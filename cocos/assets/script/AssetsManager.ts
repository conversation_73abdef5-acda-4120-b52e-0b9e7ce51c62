/*
 * @FilePath     : /cocos/assets/script/AssetsManager.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
// import { SingleBase } from "../libs/base/SingleBase";
import { SingleBase } from "../qte/core/base/SingleBase";

const {ccclass} = cc._decorator;

@ccclass
export default class AssetsManager extends SingleBase {

    public loadBundel (bundleName:string = "record") {
        return new Promise((reslove, reject) => {
            yy.loader.loadBundle(bundleName, null, (err, bundle: cc.AssetManager.Bundle) => {
                if (err) {
                    yy.error(err);
                    reject(err);
                } else {
                    reslove(bundle);
                }
            })
        });
    }

    getBundel (bundleName: string): cc.AssetManager.Bundle {
        return cc.assetManager.getBundle(bundleName);
    }

}
