/*
 * @FilePath     : /cocos/assets/script/extends/yy.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
/**
 * 对yy进行赋值
 * 拓展
 */

import { SingleFactory } from "../../qte/core/base/SingleBase";
import ActionUtiil from "../../qte/core/extension/action/ActionUtiil";
import ResLoader from "../../qte/core/loader/ResLoader";
import ValueUtils from "../../qte/core/utils/ValueUtils";
import LogUtil from "../../qte/util/LogUtil";
// import LogUtil from "./LogUtil";

(window as any).yy || ((window as any).yy = {});

// 接口导出
(window as any).yy.single = {};
(window as any).yy.single.instance = SingleFactory.getInstance;
(window as any).yy.single.destory = SingleFactory.destory;
(window as any).yy.single.destoryAll = SingleFactory.destoryAll;
// 新的简短接口导出
(window as any).yy.instance = SingleFactory.getInstance;
(window as any).yy.destory = SingleFactory.destory;
(window as any).yy.destoryAll = SingleFactory.destoryAll;

// LogUtil
(window as any).yy.log = LogUtil.getInstance().log;
(window as any).yy.debug = LogUtil.getInstance().debug;
(window as any).yy.warn = LogUtil.getInstance().warn;
(window as any).yy.error = LogUtil.getInstance().error;
(window as any).yy.saveLog = LogUtil.save;
(window as any).yy.setLogStatus = LogUtil.setLogStatus;
(window as any).yy.logTrace = LogUtil.logTrace;

// ValueUtils
(window as any).yy.checkValue = ValueUtils.check;
(window as any).yy.cloneValues = ValueUtils.clone;

// loader
(window as any).yy.loader || ((window as any).yy.loader = {});
(window as any).yy.loader.assetsMap = ResLoader.assetsMap;
(window as any).yy.loader.cmptAssets = ResLoader.cmptAssets;
(window as any).yy.loader.loadRes = ResLoader.loadRes;
(window as any).yy.loader.loadDir = ResLoader.loadDir;
(window as any).yy.loader.loadBundle = ResLoader.loadBundle;
(window as any).yy.loader.addObserver = ResLoader.addObserver;
(window as any).yy.loader.removeObserver = ResLoader.removeObserver;

// action,自定义action
(window as any).yy.Action || ((window as any).yy.Action = {});
(window as any).yy.Action.getBezierPoints = ActionUtiil.getBezierPoints;
(window as any).yy.Action.getBezierByArray = ActionUtiil.getBezierByArray;
(window as any).yy.Action.getHermitePoints = ActionUtiil.getHermitePoints;

export default yy;
