declare namespace yy {
  export class SingleBase {
    public initInstance();
  }

  // eslint-disable-next-line @typescript-eslint/class-name-casing
  export class single {
    static instance<T extends SingleBase>(type: new () => T): T;
    static destory<T extends SingleBase>(type: new () => T): void;
    static destoryAll(): void;
  }

  // 单例相关接口导出
  export function instance<T extends SingleBase>(type: new () => T): T;
  export function destory<T extends SingleBase>(type: new () => T): void;
  export function destoryAll<T extends SingleBase>(except?: new () => T): void;

  // LogUtil 日志相关
  export function log(msg: any, ...subst: any[]): void;
  export function warn(msg: any, ...subst: any[]): void;
  export function debug(msg: any, ...subst: any[]): void;
  export function error(msg: any, ...subst: any[]): void;
  export function logTrace(message?: any): void;

  // --------------   utils   ----------------
  /**
   * 检查数值有效性
   * @param val 数值
   * @param d 无效时的默认值
   */
  export function checkValue<T>(val: T, d?: T): T;
  /** clone 数值 */
  export function cloneValues(obj: any);

  /**
   * 资源加载部分
   * 所有资源加载部分接口都统一在loader下
   */
  export interface LoaderObserver {
    /**
     * 资源开始加载之前
     * @param {string} url 资源地址
     * @param {cc.Asset} type 资源类型
     */
    beforeLoadRes(param: LoaderObserverParam): void;

    /**
     * 资源开始加载之前
     * @param {string} url 资源地址
     * @param {cc.Asset} type 资源类型
     * @param {number} time 资源加载时间
     */
    afterLoadRes(param: LoaderObserverParam): void;

    /**
     * bundle开始加载之前
     * @param {string} url 资源地址
     * @param {cc.Asset} type 资源类型
     */
    beforeLoadBundle(param: LoaderObserverParam): void;

    /**
     * bundle开始加载之前
     * @param {string} url 资源地址
     * @param {cc.Asset} type 资源类型
     * @param {number} time 资源加载时间
     */
    afterLoadBundle(param: LoaderObserverParam): void;
  }
  // eslint-disable-next-line @typescript-eslint/class-name-casing
  export class loader {
    static assetsMap: Map<string, cc.Asset>;
    static cmptAssets: Map<string, string[]> = new Map();
    /**
     * 通用资源加载接口（包括本地资源、网络资源和远程资源）
     * @param {string} path 资源路径，可以是本地资源、网络资源和远程资源
     * @param {cc.Asset | Record<string, any>} options 资源类型 | 远程资源可选参数
     * @param {(err, res) => void} onComplete 加载完成回调
     * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。
     * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度
     */
    static loadRes(
      path: string,
      type: typeof cc.Asset | Record<string, any>,
      onComplete: (err, res) => void,
      bundle?: cc.AssetManager.Bundle | string,
      cmptId?: string,
      onProgress?: (finish: number, total: number, item: cc.AssetManager.RequestItem) => void,
    ): void;

    /**
     * 加载目录
     * @param {string} dir 资源目录
     * @param {cc.Asset} type 资源类型
     * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度回调
     * @param {(error: Error, assets: Array<T>) => void} onComplete 加载完成回调
     * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。
     */
    static loadDir<T extends cc.Asset>(
      dir: string,
      type: typeof cc.Asset,
      onProgress: (finish: number, total: number, item: cc.AssetManager.RequestItem) => void,
      onComplete: (error: Error, assets: Array<T>) => void,
      bundle?: cc.AssetManager.Bundle | string,
    ): void;

    /**
     * 加载bundle
     * @param {string} nameOrUrl bundle名称或地址
     * @param {Record<string, any>} options 下载bundle的可选参数
     * @param {(err: Error, bundle: cc.AssetManager.Bundle) => void} onComplete 加载完成回调
     */
    static loadBundle(nameOrUrl: string, options: Record<string, any>, onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void): void;

    /**
     * 注册观察者
     * @param {LoaderObserver} observer 自定义观察者
     */
    static addObserver(observer: LoaderObserver): void;
    /**
     * 移除观察者
     * @param {LoaderObserver} observer 自定义观察者
     * @returns {boolean} 是否移除成功
     */
    static removeObserver(observer: LoaderObserver): boolean;
  }
}
