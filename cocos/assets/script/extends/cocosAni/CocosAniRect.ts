/* eslint-disable no-bitwise */
/* eslint-disable no-unused-expressions */
/* eslint-disable sort-vars */
/* eslint-disable init-declarations */
/* eslint-disable dot-notation */


const { ccclass} = cc._decorator;

@ccclass
export default class CocosAniRect extends cc.Component {
    public spineW: number = -1;
    public spineH: number = 0;
    public centerPoint: cc.Vec2 = new cc.Vec2(0, 0);
    public anchorX = 0.5;
    public anchorY = 0.5;
    // eslint-disable-next-line max-lines-per-function
    calcOffset (callBack: any) {
        // TODO 处理动画的尺寸问题
        const animationData = this.node.getComponent(cc.Animation);
        if (callBack) {
            callBack(); 
        }
    }

    getAnchor (): cc.Vec2 {
        return cc.v2(this.anchorX, this.anchorY);
    }

}

