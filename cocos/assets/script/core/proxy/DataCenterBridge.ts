import { SingleBase } from "../../../qte/core/base/SingleBase";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import CommandManager from "../../../qte/core/extension/command/CommandManager";
import { UpdateLevelType } from "../../common/EditorEnum";
import StageController from "../../stage/StageController";
import AnimDisplayControl from "../animation/display/AnimDisplayControl";
import AnimDisplayManager, { OptionData } from "../animation/display/AnimDisplayManager";
import {
  AddCmptCommand,
  AssembleGroupCommand,
  RemoveActionCommand,
  RemoveCmptCommand,
  RemoveFragmentCommand,
  ReplaceCmptCommand,
  SetActiveActionCommand,
  SetFragmentCommand,
  UnassembleGroupCommand,
  UpdateAnimPropertiesCommand,
  UpdateBgCommand,
  UpdateCmptLevelCommand,
  UpdateComponentTagCommand,
  UpdateModeCommand,
  UpdatePropertiesCommand,
  UpdateEditingComponentAnimations,
  UpdateDragableCommand,
} from "../command/commond";
import { AddChildComponent } from "../command/operate/base/AddChildComponent";
import { ChangeAutoSubmitCommand } from "../command/operate/base/ChangeAutoSubmitCommand";
import UpdateComponentEditable from "../command/operate/base/UpdateComponentEditable";
import CommandFactory from "../command/operate/CommandFactory";
import { CreatePointCommand } from "../command/operate/cutShape/CreatePointCommand";
import { RemovePointCommand } from "../command/operate/cutShape/RemovePointCommand";
import { UpdatePointPosCommand } from "../command/operate/cutShape/UpdatePointPosCommand";
import UpdateProp2VueCmd from "../command/simple/UpdateProp2VueCmd";
import DisplayEditBox from "../display/base/DisplayEditBox";
import DisplayObjectGroup from "../display/base/DisplayObjectGroup";
import DisplayObjectManager from "../display/DisplayObjectManager";
import { LINT_DISPLAY_TYPE, LINT_POS_TYPE } from "../hintline/HintLineDisplay";
import HintLineManager from "../hintline/HintLineManager";
import ComptData, { StageInfo } from "./ComptData";
import DataProxyImp from "./DataProxyImp";
import MockData from "./MockData";
import { UpdateComponentOrderCommand } from "../command/operate/base/UpdateComponentOrderCommand";

/**
 * 数据中心交互类
 * 负责与vuex数据中心进行交互
 */
export default class DataCenterBridge extends SingleBase implements DataProxyImp {
  public initInstance() {
    this.initData();
  }

  /** 初始化数据 */
  // eslint-disable-next-line max-lines-per-function
  public initData(): void {
    // 测试数据
    MockData.init();
    // 监听mutation
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (window as any).storeUnSubscribe = (window as any)._$store.subscribe((mutationData, state) => {
      const mutation = yy.cloneValues(mutationData);
      yy.log("subscribe mutation :", mutation);
      if (mutation.type === "updateComponentProperties") {
        // 更新组件属性
        this.updateDisplayObjectProperties(mutation.payload.id, mutation.payload.newProperties);
      } else if (mutation.type === "updateStage") {
        // 更新舞台背景属性
        yy.single.instance(CommandFactory).execute(UpdateBgCommand, mutation.payload);
        yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, {
          selectIds: [mutation.payload.id],
          isUpdateGroupData: false,
          notUpdate2Vue: false,
          notRunCmd: true,
        });
      } else if (mutation.type === "updateComponentLevel") {
        // 更新组件层级
        this.updateComponentLevel(mutation.payload.id, mutation.payload.type);
      } else if (mutation.type === "addComponent") {
        // 添加组件
        this.addComponet(mutation.payload);
      } else if(mutation.type === "updateComponentOrder" || mutation.type === "updateSubComponentOrder"){
        // 批量 更新组件顺序
        this.updateComponentOrder(mutation.payload);
      } else if (mutation.type === "removeComponent") {
        // 移除组件
        this.removeComponet(mutation.payload);
      } else if (mutation.type === "updateComponentIdsWhenSeparateGroupComponent") {
        // 取消组
        this.unassembleGroup(mutation.payload);
      } else if (mutation.type === "updateComponentIdsWhenCombineComponents") {
        // 合并组
        this.assembleGroup(mutation.payload.id, mutation.payload.ids);
      } else if (mutation.type === "timeTravel/push") {
        // 一个操作行为单元
        yy.single.instance(CommandFactory).step();
      } else if (mutation.type === "timeTravel/redo") {
        // 前进
        yy.single.instance(CommandFactory).forward();
      } else if (mutation.type === "timeTravel/undo") {
        // 后退
        yy.single.instance(CommandFactory).backward();
      } else if (mutation.type === "replaceCurrentComponentIds") {
        // 当前选中组件
        yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids: mutation.payload, isVue: true });
      } else if (mutation.type === "moduleAnimations/playFragment") {
        // 预览单个动画片段
        let actions = yy.cloneValues(mutation.payload);
        yy.single.instance(StageController).playFragmentAnimation(actions);
      } else if (mutation.type === "moduleAnimations/playAnimation") {
        // 预览整个舞台动画
        let actions = yy.cloneValues(mutation.payload);
        yy.single.instance(StageController).playGroupAnim(actions);
      } else if (mutation.type === "rulerTick/addVerticalLine") {
        yy.single.instance(HintLineManager).addHintLine((window as any)._$store.state.rulerTick.verticalLines.length - 1, true, mutation.payload, LINT_DISPLAY_TYPE.VERTICAL);
      } else if (mutation.type === "rulerTick/removeVerticalLine") {
        yy.single.instance(HintLineManager).removeHintLine(mutation.payload, true, LINT_DISPLAY_TYPE.VERTICAL);
      } else if (mutation.type === "rulerTick/addHorizontalLine") {
        yy.single.instance(HintLineManager).addHintLine((window as any)._$store.state.rulerTick.horizontalLines.length - 1, true, mutation.payload, LINT_DISPLAY_TYPE.HORIZONTAL);
      } else if (mutation.type === "rulerTick/removeHorizontalLine") {
        yy.single.instance(HintLineManager).removeHintLine(mutation.payload, true, LINT_DISPLAY_TYPE.HORIZONTAL);
      } else if (mutation.type === "rulerTick/updateVerticalLine") {
        yy.single.instance(HintLineManager).updateLinePosAndCollide(mutation.payload.index, true, mutation.payload.value, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.HORIZONTAL, false);
      } else if (mutation.type === "rulerTick/updateHorizontalLine") {
        yy.single.instance(HintLineManager).updateLinePosAndCollide(mutation.payload.index, true, mutation.payload.value, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL, false);
      } else if (mutation.type === "updateMode") {
        // 模式切换 0 普通模式 1 动画模式
        yy.single.instance(CommandFactory).execute(UpdateModeCommand, mutation.payload);
      } else if (mutation.type === "moduleAnimations/setActiveActionId" || mutation.type === "setComponentActiveActionId") {
        // 选中动画
        this.setActiveActionId(mutation.payload);
      } else if (mutation.type === "moduleAnimations/updateActionConfigData" || mutation.type === "updateComponentActionConfigData") {
        // 选中动画改变
        let acitonData = yy.single.instance(AnimDisplayControl).getActiveAction();
        this.setActiveActionId(acitonData.id);
      } else if (mutation.type === "moduleAnimations/setFragmentId" || mutation.type === "setComponentFragmentId") {
        // 切换动画片段
        yy.single.instance(CommandFactory).execute(SetFragmentCommand, { fragmentId: mutation.payload });
      } else if (mutation.type === "moduleAnimations/setActionAnimRelative" || mutation.type === "setComponentActionAnimRelative") {
        // 更改动画Relative
        let acitonData = yy.single.instance(AnimDisplayControl).getActiveAction();
        this.setActiveActionId(acitonData.id);
      } else if (mutation.type === "moduleAnimations/setActionAnimOption" || mutation.type === "setComponentActionAnimOption") {
        // 更改动画option
        this.updateActiveAction(mutation.payload);
      } else if (mutation.type === "moduleAnimations/removeFragment" || mutation.type === "removeComponentAnimationFragment") {
        // 删除动画片段
        yy.single.instance(CommandFactory).execute(RemoveFragmentCommand, { fragment: mutation.payload });
      } else if (mutation.type === "moduleAnimations/addFragment" || mutation.type === "addComponentAnimationFragment") {
        // 增加动画片段
        yy.single.instance(CommandFactory).execute(SetFragmentCommand, { fragmentId: mutation.payload });
      } else if (mutation.type === "moduleAnimations/removeAction" || mutation.type === "removeComponentAnimationAction") {
        // 删除单个动画
        yy.single.instance(CommandFactory).execute(RemoveActionCommand, mutation.payload);
      } else if (mutation.type === "updateComponentTag") {
        // 更新组件业务属性
        this.updateComponentTag(mutation.payload);

      } else if (mutation.type === "moduleAnimations/stopFragment" || mutation.type === "moduleAnimations/stopAnimation") {
        // 暂停动画预览
        yy.single.instance(StageController).stopAnimation();
      } else if (mutation.type === "setComponentsDragable") {
        // 更改组件锁定
        yy.single.instance(CommandFactory).execute(UpdateDragableCommand, {
          cmptIds: mutation.payload.ids,
          dragable: mutation.payload.dragable,
        });
      } else if (mutation.type === "addCutShapePoint") {
        // 切割图形类消息  添加切割图形组件坐标点
        yy.single.instance(CommandFactory).execute(CreatePointCommand, {
          cmptIds: mutation.payload.componentId,
          pointDatas: yy.cloneValues(mutation.payload.point),
        });
      } else if (mutation.type === "removeCutShapePoint") {
        // 切割图形类消息  删除切割图形组件坐标点
        yy.single.instance(CommandFactory).execute(RemovePointCommand, {
          cmptIds: mutation.payload.componentId,
          pointId: mutation.payload.pointId,
        });
      } else if (mutation.type === "updateCutShapePointPosition") {
        // 切割图形类消息  更新切割图形组件坐标点位置
        yy.single.instance(CommandFactory).execute(UpdatePointPosCommand, {
          cmptIds: mutation.payload.componentId,
          pointId: mutation.payload.pointId,
          pointPos: mutation.payload.position,
          isVue: true,
        });
      } else if (mutation.type === "setIsEditingComponentAnimations") {
        yy.single.instance(CommandFactory).execute(UpdateEditingComponentAnimations, {
          editType: mutation.payload,
        });
      } else if (mutation.type === "updateExtraStageData") {
        if (mutation.payload.hasOwnProperty("isAutoSubmit") || mutation.payload.hasOwnProperty("hasRecover")  ) {
          const _isAutoSubmit: boolean = mutation.payload.isAutoSubmit;
          yy.single.instance(CommandFactory).execute(ChangeAutoSubmitCommand, { submitType: mutation.payload });
        }
      } else if (mutation.type === "updateComponentEditable") {
        yy.single.instance(CommandFactory).execute(UpdateComponentEditable, {
          componentIds: mutation.payload.componentIds,
          newEditable: mutation.payload.newEditable.properties,
        });
      } else if (mutation.type === "addChildComponent") {
        console.log("########---->addChildComponent--->", mutation.payload);
        this.addChildComponent(mutation.payload);
      } else if (mutation.type == "startEditLabel") {
        let displayManager = yy.single.instance(DisplayObjectManager);
        let id = mutation.payload[0];
        let displayObject = displayManager.getDisplayObjectById(id);
        let displayEditBox = displayObject.getComponent(DisplayEditBox);
        if (displayEditBox) {
          displayEditBox.setStartEdit();
        }
      }
      else if (mutation.type == "timeTravel/resetCommandFactory") {
        yy.destoryAll();
      }


    });
  }

  /**
   * GetStageSize获取舞台宽高参数
   */
  public getStageInfo(): StageInfo {
    return (window as any)._$store.state.stageData;
  }

  /**
   * GetStageSize获取舞台宽高参数
   */
  public getExtraStageData(): any {
    return (window as any)._$store.state.extraStageData;
  }

  /**
   * 获取组件结构列表
   */
  public getComponentIds(): any[] {
    return (window as any)._$store.state.componentIds;
  }

  /**
  * 获取template
  */
  public getTemplate(): any {
    return (window as any)._$store.state.template;
  }

  /**
   * 获取初始组件数据
   */
  public getComponentMap(): Map<string, ComptData> {
    return (window as any)._$store.state.componentMap;
  }

  /**
   * 根据组件id获取组件tagName
   * @param id 组件id
   */
  public getComponetTagName(id: string): string {
    let tag = (window as any)._$store.state.componentMap[id].tag;
    if (!tag) {
      return "";
    }
    let tagName = (window as any)._$store.state.template.tags.find(item => item.name === tag);
    return tagName.label;
  }
  /**
   * 根据组件id获取组件tagName 针对连线题，获取父级组件格式 答题区id:1的连接点id:2
   * @param id 组件id
   */
  public getSubComponetTagName(id: string): string {
    let tagName = "";
    let subTagName = this.getComponetTagName(id);
    let componentMap = (window as any)._$store.state.componentMap;
    componentMap.forEach((component: any) => {
      const { extra = {} } = component;
      console.error("extra.point", extra);
      if (extra.point) {
        if (extra.point.id === id) {
          tagName = this.getComponetTagName(component.id);
        }
      }
    });
    return subTagName + "-" + tagName;
  }

  /** 显示选择菜单 */
  public showContextMenu(data: any): void {
    (window as any)._$store.commit("showContextMenu", data);
  }

  public doubleClick(data: any): void {
    (window as any)._$store.commit("doubleClick", data);
  }

  /**
   * 更新cocos舞台中显示对象属性
   * @param id 组件id
   * @param newProperties 组件属性
   */
  public updateDisplayObjectProperties(id: string, newProperties: any) {
    let displaymanager = yy.single.instance(DisplayObjectManager);
    let displayObject = displaymanager.getDisplayObjectById(id);
    let tempProperty = ValueUtils.clone(newProperties);
    let newPos = displaymanager.getNodePos(id, newProperties);
    newProperties.x = newPos.x;
    newProperties.y = newPos.y;
    let _groupId = displaymanager.getDisplayGroupID(id);
    if (_groupId !== "-1") {
      if (newProperties.x || newProperties.y || newProperties.width || newProperties.height || newProperties.angle) {
        yy.single.instance(CommandFactory).execute(UpdatePropertiesCommand, { id, newProperties });
        displaymanager.updateDisplayObject(id, newProperties);
        let _displayGroup = displaymanager.getDisplayObjectById(_groupId);
        (_displayGroup as DisplayObjectGroup).resetLayoutSize();
        let ids = (_displayGroup as DisplayObjectGroup).groupIds;
        ids.push(_groupId);
        yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, {
          selectIds: ids,
          isUpdateGroupData: true,
          notUpdate2Vue: false,
          notRunCmd: false,
        });
      } else {
        yy.single.instance(CommandFactory).execute(UpdatePropertiesCommand, { id, newProperties });
        yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, {
          selectIds: [id],
          isUpdateGroupData: false,
          notUpdate2Vue: false,
          notRunCmd: true,
        });
      }
    } else {
      if (!displayObject || !displayObject.node) {
        console.log("组件不存在了，无法更新了", displayObject);
        return;
      }
      if (displayObject.node.parent.name !== "object_layer") {
        newProperties = tempProperty;
        console.log("############");
      }
      yy.single.instance(CommandFactory).execute(UpdatePropertiesCommand, { id, newProperties });
      // 非组合元素,update属性之后,不在上报
      yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, {
        selectIds: [id],
        isUpdateGroupData: false,
        notUpdate2Vue: false,
        notRunCmd: true,
      });
    }
  }

  /**
   * 选中组件
   * @param {string[]} ids
   */
  public replaceComponet(ids: string[]) {
    (window as any)._$store.commit("cocos/replaceCurrentComponentIds", ids);
  }

  /**
   * 更新vuex数据中心对象属性
   * @param id 组件id
   * @param newProperties 组件属性
   */
  public updateComponetProperties(id: string, newProperties: any): void {
    (window as any)._$store.commit("cocos/updateComponentProperties", {
      id,
      newProperties,
    });
  }

  /**
   * 更新全局动画编辑数据
   * @param data tween action data
   */
  public setActionAnimOption(actionId: string, data: OptionData) {
    (window as any)._$store.commit("moduleAnimations/cocos/setActionAnimOption", { id: actionId, option: data });
  }

  /** 更新组件动画数据 */
  public setCmptActionOption(actionId: string, data: OptionData) {
    (window as any)._$store.commit("cocos/setComponentActionAnimOption", {
      id: actionId,
      option: data,
    });
  }

  /** 选中全局动画 */
  public setAnimModelActiveActionId(actionId: string) {
    (window as any)._$store.commit("moduleAnimations/cocos/setActiveActionId", actionId);
  }

  /** 选中组件动画编辑 动画 */
  public setCmptModelActiveActionId(actionId: string) {
    (window as any)._$store.commit("cocos/setComponentActiveActionId", actionId);
  }

  /** 暂停动画播放 */
  public stopAnimatioinCommit(): void {
    (window as any)._$store.commit("moduleAnimations/stopAnimation");
  }
  /** 暂停动画片段播放 */
  public stopFragmentCommit(): void {
    (window as any)._$store.commit("moduleAnimations/stopFragment");
  }
  /** 初始化完成 */
  public cocosInitFinished(): void {
    (window as any)._$store.commit("cocosInitFinished");
  }

    /** 加载完成题型bundle */
  public cocosLoadQuestionBundleFinished(): void {
    (window as any)._$store.commit("cocosLoadQuestionBundleFinished");
  }
  
  
 /** 加载完成游戏场景 */
  public cocosLoadGameSceneFinished(): void {
    (window as any)._$store.commit("cocosLoadGameSceneFinished");
  }
  
   /** 加载完成舞台根节点 */
  public cocosLoadStageRootNodeFinished(): void {
    (window as any)._$store.commit("cocosLoadStageRootNodeFinished");
  }
  

   /** 开始加载组件 */
  public cocosLoadCompListStart(): void {
    (window as any)._$store.commit("cocosLoadCompListStart");
  }

     /** 完成加载组件 */
  public cocosLoadCompListFinished(): void {
    (window as any)._$store.commit("cocosLoadCompListFinished");
  }

  /** 获取动画模式下的所有动画数据 */
  public getAnimModelFlatActions(): any {
    let flatActions = (window as any)._$store.getters["moduleAnimations/flatActions"];
    return flatActions;
  }

  /** 获取组件模式下的所有动画数据 */
  public getCmptModelFlatActions(): any {
    // eslint-disable-next-line dot-notation
    let flatActions = (window as any)._$store.getters["flatComponentActions"];
    return flatActions;
  }

  /**
   * 获取动画模式选中动画的数据
   */
  public getAnimModelActiveAction(): any {
    let action = (window as any)._$store.getters["moduleAnimations/activeAction"];
    return action;
  }

  /**
   * 获取组件动画编辑模式选中动画的数据
   */
  public getCmptModelActiveAction(): any {
    // eslint-disable-next-line dot-notation
    let action = (window as any)._$store.getters["activeComponentAction"];
    return action;
  }

  /**
   * @description 设置切割图形组件坐标点数据
   */
  public setCutShapePointsData(actionId: string, pointData: any): void {
    (window as any)._$store.commit("cocos/setCutShapePointsData", {
      componentId: actionId,
      pointsData: yy.cloneValues(pointData),
    });
  }

  /**
   * @description 设置切割图形组件线段数据
   */
  public setCutShapeLinesData(actionId: string, lineData: any): void {
    (window as any)._$store.commit("cocos/setCutShapeLinesData", {
      componentId: actionId,
      linesData: yy.cloneValues(lineData),
    });
  }

  /**
   * @description 更新切割图形组件坐标点位置
   */
  public updateCutShapePointPosition(actionId: string, pointID: string, pointPos: {}): void {
    (window as any)._$store.commit("cocos/updateCutShapePointPosition", {
      componentId: actionId,
      pointId: pointID,
      position: yy.cloneValues(pointPos),
    });
  }
   /** 
   * 更新组件顺序
   * @param ids 组件id
   * @param newIndex 新索引
   * @param oldIndex 旧索引
   */
   public updateComponentOrder(payload: {id: string, newIndex: number, oldIndex: number}[]): void {
    yy.single.instance(CommandFactory).execute(UpdateComponentOrderCommand, payload);
  }

  /**
   * 更新指定显示对象层级
   * @param id 显示对象id
   * @param type 层级修改类型
   */
  public updateComponentLevel(id: string, type: UpdateLevelType): void {
    yy.single.instance(CommandFactory).execute(UpdateCmptLevelCommand, { id, type });
  }

  /**
   * 添加组件
   * @param id 组件id
   * @param type 组件类型
   * @param properties 组件属性
   */
  public addComponet(payload): void {
    yy.single.instance(CommandFactory).execute(AddCmptCommand, { payload });
  }

  /** 移除组件 */
  public removeComponet(id: string): void {
    yy.single.instance(CommandFactory).execute(RemoveCmptCommand, id);
  }

  public addChildComponent(payload): void {
    yy.single.instance(CommandFactory).execute(AddChildComponent, { payload });
  }
  /**
   * 将多个组件组合成一个group
   * @param id 组合后的group id
   * @param ids 多选的几个id
   */
  public assembleGroup(id: string, ids: string[]): void {
    yy.single.instance(CommandFactory).execute(AssembleGroupCommand, { id, ids });
  }

  /**
   * 取消组合
   * @param id 组合id
   */
  public unassembleGroup(id: string): void {
    yy.single.instance(CommandFactory).execute(UnassembleGroupCommand, id);
  }

  /**
   * 设置选中动画
   */
  public setActiveActionId(payload) {
    if (!payload || payload === "") {
      return;
    }
    yy.single.instance(CommandFactory).execute(SetActiveActionCommand, { actionId: payload });
  }

  /** 更新动画数据 */
  public updateActiveAction(payload) {
    let activeAction = yy.single.instance(AnimDisplayControl).getActiveAction();
    if (!activeAction) {
      return;
    }
    let actionId = activeAction.id;

    let cmptdIds = yy.single.instance(AnimDisplayManager).getAnimDisplayObjects(actionId);
    if (!actionId || actionId === "" || !cmptdIds) {
      return;
    }
    if (!payload.option || !payload.option.points) {
      return;
    }
    let index = payload.option.currIndex;
    yy.single.instance(CommandFactory).execute(UpdateAnimPropertiesCommand, {
      id: cmptdIds[index],
      newProperties: payload.option.points[index],
      actionId,
    });
  }

  /**
   * 更新业务属性标签
   * @param payload
   */
  public updateComponentTag(payload) {
    yy.single.instance(CommandFactory).execute(UpdateComponentTagCommand, {
      id: payload.id,
      label: payload.tag.label,
    });
  }

  public destory(): void {
    yy.log("====destory====");
  }

  /**
   * 更新
   * @param payload
   */
  public updateCocosAniClips(payload) {
    (window as any)._$store.commit("updateCocosAniClips", {
      id: payload.id,
      clips: payload.clips,
    });
  }

  /**
   * 文本转为图片保存
   * @param payload
   */
  public textToImg(payload) {
    console.warn("textToImg", payload.texture);
    (window as any)._$store.commit("textToImg", {
      selectId: payload.selectId,
      texture: payload.texture,
    });
  }

  /** 获取组件是否可删除属性 */
  public getComponentEditable(id: string) {
    let editable = (window as any)._$store.state.componentMap[Number(id)];
    if (!editable) {
      return true;
    }
    if (typeof editable.editable === "boolean") {
      return editable.editable;
    }

    return editable.editable.properties;
  }
}
