/*
 * @FilePath     : /cocos/assets/script/core/proxy/ExtraModule.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import { SingleBase } from "../../../qte/core/base/SingleBase";


export interface BootParameter {
    resourceMap: {
        
    },
}

export default class ExtraModule extends SingleBase {
    public bootData:BootParameter;    
    init(data:BootParameter){
        console.log('Extramodunle init',data);
        this.bootData = data;
    }
    getBundleName(){
        
    }
    initInstance(){

    }
    onDestoryInstance(){
        
    }





}