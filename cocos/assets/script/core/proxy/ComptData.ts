import { CmptType, FlipType } from "../../common/EditorEnum";

/** 模板中组件数据结构 */
export default class ComptData {
  public id: string;
  public type: CmptType;
  public editable: boolean | EditableProperties;
  // 组件属性，K-V
  public properties: BaseProperties;
  public subComponents?: number[];
  public spineData?: SpineData;
  public cocosAniData?: CocosAniData;
  // 是否可移动
  public dragable?: boolean;
  // 切割图形的类型 如：等腰三角形
  public subType?: any;
  // 是否可删除
  public deletable?: boolean;
  public cName?: string;
  public tag?: string;
}

/** spine数据结构 */
export class SpineData {
  public atlas: string;
  public skeleton: string;
  public images: string[];
  // 图片名称
  public get imageNames(): string[] {
    let results = [];
    for (let url of this.images) {
      let arry = url.split("/");
      results.push(arry[arry.length - 1]);
    }
    return results;
  }
}

export class CocosAniData {
  public url: string;
}

/** 舞台属性 */
export class StageInfo {
  public texture: string;
  public backgroundColor: string;
  public width: number;
  public height: number;
  public safeHeight: number;
  public safeWidth: number;
  public bgColor: string; // 后续不再使用backgroundColor字段 背景色改用bgColor
}

/**
 * 组件基础属性
 */
interface BaseProperties {
  active: boolean;
  width: number;
  height: number;
  x: number;
  y: number;
  color?: string;
  opacity?: number;
  zIndex?: number;
  angle?: number;
  scaleX?: number;
  scaleY?: number;
  flipType?: FlipType;
}

export interface SpriteProperties extends BaseProperties {
  texture: string;
}

export interface Shape2Properties extends BaseProperties {
  texture: string;
}

export interface FormulaProperties extends BaseProperties {
  url: string;
  latex: string;
}

/**
 * svg 所属数据格式
 */
export interface SvgCompData extends BaseProperties {
  id: string;
  width: number;
  height: number;
  fillColor: string;
  strokeColor: string;
  url?: string;
  svgText?: string;
  lineWidth: number;
  origin: boolean;
}

// 切割图形的所有属性
export interface CutShapeCompData extends BaseProperties {
  pointsData: any[];
  linesData: any[];
  fillColor: string;
  strokeColor: string;
  lineWidth: number;
  isHidePointLine: boolean;
}

/** 文本属性 */
export interface LabelProperties extends BaseProperties {
  string: string;
  str: string;
  fontSize?: number;
  lineHeight?: number;
  overflow?: cc.Label.Overflow;
  enableUnderline?: boolean;
  enableItalic?: boolean;
  enableBold?: boolean;
  cusorIndex?: number;
  isLabelRight?: true;
  selectArr?: [];
  horizontalAlign?: number;
  textureArray?: [];
}
/** 录音组件属性 */
export interface RecordCmptProperties extends BaseProperties {
  accuracy: []; // 正确率
  answerDuration: number; // 答题时长
  autoBegin: boolean; // 自动开始
  autoEnd: boolean; //  自动结束
  autoReset: number; // 自动重放分数
  evaluatingText: string; // 测评关键字
  loseAward: number; // 失败奖励
  repetition: number; // 二次重读 分数
  voiceStyle: number; // 话筒样式 0 自动 1 手动
  wordType: 0; // 评测类型
}

/**
 * 是否口编辑属性
 */
export class EditableProperties {
  public properties: {
    angle?: boolean;
    height?: boolean;
    width?: boolean;
    x?: boolean;
    y?: boolean;
    cutShapePoints?: boolean; // 图形组件编辑点
    str?: boolean;
  };

  constructor() {
    this.properties = {};
  }
}
