/*
 * @FilePath: /interactive-question-editor/cocos/assets/script/core/proxy/MockData.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

import Config from "../../Config";
import ExtraModule from "./ExtraModule";

/**
 * 测试数据
 */
export default class MockData {

    public static init () {
        if (!(window as any)._$store) {
            Config.DEBUG = true;
            (window as any)._$store = {};
            yy.single.instance(ExtraModule).bootData = {
                "resourceMap": {
                    "quickResponseQuestion": "http://jiaoyanbos.cdnjtzy.com/cocos/dir/2dccc26fb389dec8a1b81f3e9c6f6956/quickResponseQuestion"
                }
            },
            // (window as any)._$store.isUpdateCmdIng=false;
            (window as any)._$store.state = {
                "userInfo": {
                    "mobile": "15611194527",
                    "uid": 2509023235,
                    "username": "wanghuan13",
                    "uname": "wanghuan13",
                    "zhname": "王欢13",
                    "dinguid": 0,
                    "emplId": "Z076447",
                    "create": 1705459462,
                    "userType": 1,
                    "email": "<EMAIL>",
                    "expire": 2549283
                },
                "mode": 0,
                "initialDataLoaded": true,
                "cocosInitFinished": true,
                "name": "快速连答",
                "templateId": 192,
                "template": {
                    "name": "快速连答",
                    "tempType": 85,
                    "category": 1169,
                    "bundleUrl": "",
                    "questionType": 2,
                    "bundleName": "quickResponseQuestion",
                    "tags": [],
                    "stage": {
                        "width": 1280,
                        "height": 720,
                        "safeWidth": 1280,
                        "safeHeight": 960,
                        "backgroundColor": "#ffffff",
                        "texture": "",
                        "textureType": 0
                    },
                    "extraConfig": [],
                    "animationConfig": [
                        {
                            "label": "读题后",
                            "value": "afterReadingQuestion"
                        },
                        {
                            "label": "正确后",
                            "value": "afterSubmitCorrect"
                        },
                        {
                            "label": "答错后",
                            "value": "afterSubmitWrong"
                        }
                    ],
                    "features": {
                        "isQuestion": 1,
                        "newCocos": 1,
                        "hasMys": 0,
                        "canInteract": 1,
                        "isOral": 0,
                        "isGroup": 0,
                        "demoPage": 0,
                        "hasVideo": 0,
                        "liveResources": 1,
                        "groupData": {
                            "questionLength": 0
                        }
                    },
                    "categoryName": "快速连答"
                },
                "extData": {
                    "formConfig": {
                        "quickResponse": [
                            {
                                "formItemType": "collapse",
                                "collapseName": "题干配置",
                                "formList": [
                                    {
                                        "formItemType": "BaseRadioGroup",
                                        "key": "stuStemModeLeft",
                                        "label": "题干类型",
                                        "value": "0",
                                        "options": [
                                            {
                                                "label": "文本",
                                                "value": "0",
                                                "span": 8,
                                                "associatedForm": [
                                                    {
                                                        "formItemType": "BaseInput",
                                                        "key": "stuStemTextLeft",
                                                        "label": "左题干",
                                                        "value": "",
                                                        "span": 16,
                                                        "maxLength": 29,
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置左题干文本",
                                                                "trigger": "blur"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "formItemType": "SpecialDynamicList",
                                                        "key": "stuOptionListLeft",
                                                        "label": "选项列表",
                                                        "labelPosition": "top",
                                                        "orderConfig": {
                                                            "show": true,
                                                            "labelPosition": "left",
                                                            "decorate": "左选项{{$}}:"
                                                        },
                                                        "max": 29,
                                                        "min": 1,
                                                        "value": [
                                                            {
                                                                "stuOptionType": "0",
                                                                "text": "",
                                                                "imgUrl": ""
                                                            }
                                                        ],
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置选项",
                                                                "trigger": "change"
                                                            }
                                                        ],
                                                        "subFormConfigs": [
                                                            {
                                                                "formItemType": "BaseRadioGroup",
                                                                "key": "stuOptionType",
                                                                "value": "0",
                                                                "options": [
                                                                    {
                                                                        "label": "文本",
                                                                        "value": "0",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseInput",
                                                                                "key": "text",
                                                                                "label": "设置选项文本",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    },
                                                                    {
                                                                        "label": "图片",
                                                                        "value": "1",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseImageSelect",
                                                                                "key": "imgUrl",
                                                                                "label": "设置选项图片（图片推荐147*52）",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    }
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                ]
                                            },
                                            {
                                                "label": "图片",
                                                "value": "1",
                                                "span": 8,
                                                "associatedForm": [
                                                    {
                                                        "formItemType": "BaseImageSelect",
                                                        "key": "stuStemImgLeft",
                                                        "label": "左题干图片（图片推荐00*00）",
                                                        "labelPosition": "top",
                                                        "value": "",
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置左题干图片",
                                                                "trigger": "change"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "formItemType": "SpecialDynamicList",
                                                        "key": "stuOptionListLeft",
                                                        "label": "选项列表",
                                                        "labelPosition": "top",
                                                        "orderConfig": {
                                                            "show": true,
                                                            "labelPosition": "left",
                                                            "decorate": "左选项{{$}}:"
                                                        },
                                                        "max": 29,
                                                        "min": 1,
                                                        "value": [
                                                            {
                                                                "stuOptionType": "0",
                                                                "text": "",
                                                                "imgUrl": ""
                                                            }
                                                        ],
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置选项",
                                                                "trigger": "change"
                                                            }
                                                        ],
                                                        "subFormConfigs": [
                                                            {
                                                                "formItemType": "BaseRadioGroup",
                                                                "key": "stuOptionType",
                                                                "value": "0",
                                                                "options": [
                                                                    {
                                                                        "label": "文本",
                                                                        "value": "0",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseInput",
                                                                                "key": "text",
                                                                                "label": "设置选项文本",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    },
                                                                    {
                                                                        "label": "图片",
                                                                        "value": "1",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseImageSelect",
                                                                                "key": "imgUrl",
                                                                                "label": "设置选项图片（图片推荐00*00）",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    }
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                ]
                                            }
                                        ],
                                        "actions": {
                                            "change": true
                                        },
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请选择题干类型",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseInput",
                                        "key": "stuStemTextLeft",
                                        "label": "左题干",
                                        "value": "",
                                        "span": 16,
                                        "maxLength": 29,
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置左题干文本",
                                                "trigger": "blur"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "SpecialDynamicList",
                                        "key": "stuOptionListLeft",
                                        "label": "选项列表",
                                        "labelPosition": "top",
                                        "orderConfig": {
                                            "show": true,
                                            "labelPosition": "left",
                                            "decorate": "左选项{{$}}:"
                                        },
                                        "max": 29,
                                        "min": 1,
                                        "value": [
                                            {
                                                "stuOptionType": "0",
                                                "text": "",
                                                "imgUrl": ""
                                            }
                                        ],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置选项",
                                                "trigger": "change"
                                            }
                                        ],
                                        "subFormConfigs": [
                                            {
                                                "formItemType": "BaseRadioGroup",
                                                "key": "stuOptionType",
                                                "value": "0",
                                                "options": [
                                                    {
                                                        "label": "文本",
                                                        "value": "0",
                                                        "span": 8,
                                                        "associatedForm": [
                                                            {
                                                                "formItemType": "BaseInput",
                                                                "key": "text",
                                                                "label": "设置选项文本",
                                                                "labelPosition": "top",
                                                                "value": ""
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "label": "图片",
                                                        "value": "1",
                                                        "span": 8,
                                                        "associatedForm": [
                                                            {
                                                                "formItemType": "BaseImageSelect",
                                                                "key": "imgUrl",
                                                                "label": "设置选项图片（图片推荐147*52）",
                                                                "labelPosition": "top",
                                                                "value": ""
                                                            }
                                                        ]
                                                    }
                                                ]
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseRadioGroup",
                                        "key": "stuStemModeRight",
                                        "label": "题干类型",
                                        "value": "0",
                                        "options": [
                                            {
                                                "label": "文本",
                                                "value": "0",
                                                "span": 8,
                                                "associatedForm": [
                                                    {
                                                        "formItemType": "BaseInput",
                                                        "key": "stuStemTextRight",
                                                        "label": "右题干",
                                                        "value": "",
                                                        "span": 16,
                                                        "maxLength": 29,
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置右题干文本",
                                                                "trigger": "blur"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "formItemType": "SpecialDynamicList",
                                                        "key": "stuOptionListRight",
                                                        "label": "选项列表",
                                                        "labelPosition": "top",
                                                        "orderConfig": {
                                                            "show": true,
                                                            "labelPosition": "left",
                                                            "decorate": "右选项{{$}}:"
                                                        },
                                                        "max": 29,
                                                        "min": 1,
                                                        "value": [
                                                            {
                                                                "stuOptionType": "0",
                                                                "text": "",
                                                                "imgUrl": ""
                                                            }
                                                        ],
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置选项",
                                                                "trigger": "change"
                                                            }
                                                        ],
                                                        "subFormConfigs": [
                                                            {
                                                                "formItemType": "BaseRadioGroup",
                                                                "key": "stuOptionType",
                                                                "value": "0",
                                                                "options": [
                                                                    {
                                                                        "label": "文本",
                                                                        "value": "0",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseInput",
                                                                                "key": "text",
                                                                                "label": "设置选项文本",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    },
                                                                    {
                                                                        "label": "图片",
                                                                        "value": "1",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseImageSelect",
                                                                                "key": "imgUrl",
                                                                                "label": "设置选项图片（图片推荐00*00）",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    }
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                ]
                                            },
                                            {
                                                "label": "图片",
                                                "value": "1",
                                                "span": 8,
                                                "associatedForm": [
                                                    {
                                                        "formItemType": "BaseImageSelect",
                                                        "key": "stuStemImgRight",
                                                        "label": "右题干图片（图片推荐00*00）",
                                                        "labelPosition": "top",
                                                        "value": "",
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置右题干图片",
                                                                "trigger": "change"
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "formItemType": "SpecialDynamicList",
                                                        "key": "stuOptionListRight",
                                                        "label": "选项列表",
                                                        "labelPosition": "top",
                                                        "orderConfig": {
                                                            "show": true,
                                                            "labelPosition": "left",
                                                            "decorate": "右选项{{$}}:"
                                                        },
                                                        "max": 29,
                                                        "min": 1,
                                                        "value": [
                                                            {
                                                                "stuOptionType": "0",
                                                                "text": "",
                                                                "imgUrl": ""
                                                            }
                                                        ],
                                                        "rule": [
                                                            {
                                                                "required": true,
                                                                "message": "请设置选项",
                                                                "trigger": "change"
                                                            }
                                                        ],
                                                        "subFormConfigs": [
                                                            {
                                                                "formItemType": "BaseRadioGroup",
                                                                "key": "stuOptionType",
                                                                "value": "0",
                                                                "options": [
                                                                    {
                                                                        "label": "文本",
                                                                        "value": "0",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseInput",
                                                                                "key": "text",
                                                                                "label": "设置选项文本",
                                                                                "labelPosition": "top",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    },
                                                                    {
                                                                        "label": "图片",
                                                                        "value": "1",
                                                                        "span": 8,
                                                                        "associatedForm": [
                                                                            {
                                                                                "formItemType": "BaseImageSelect",
                                                                                "key": "imgUrl",
                                                                                "label": "设置选项图片（图片推荐147*52）",
                                                                                "value": ""
                                                                            }
                                                                        ]
                                                                    }
                                                                ]
                                                            }
                                                        ]
                                                    }
                                                ]
                                            }
                                        ],
                                        "actions": {
                                            "change": true
                                        },
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请选择题干类型",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseImageSelect",
                                        "key": "stuStemImgRight",
                                        "label": "右题干图片（图片推荐00*00）",
                                        "labelPosition": "top",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置右题干图片",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "SpecialDynamicList",
                                        "key": "stuOptionListRight",
                                        "label": "选项列表",
                                        "labelPosition": "top",
                                        "orderConfig": {
                                            "show": true,
                                            "labelPosition": "left",
                                            "decorate": "右选项{{$}}:"
                                        },
                                        "max": 29,
                                        "min": 1,
                                        "value": [
                                            {
                                                "stuOptionType": "0",
                                                "text": "",
                                                "imgUrl": ""
                                            }
                                        ],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置选项",
                                                "trigger": "change"
                                            }
                                        ],
                                        "subFormConfigs": [
                                            {
                                                "formItemType": "BaseRadioGroup",
                                                "key": "stuOptionType",
                                                "value": "0",
                                                "options": [
                                                    {
                                                        "label": "文本",
                                                        "value": "0",
                                                        "span": 8,
                                                        "associatedForm": [
                                                            {
                                                                "formItemType": "BaseInput",
                                                                "key": "text",
                                                                "label": "设置选项文本",
                                                                "labelPosition": "top",
                                                                "value": ""
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "label": "图片",
                                                        "value": "1",
                                                        "span": 8,
                                                        "associatedForm": [
                                                            {
                                                                "formItemType": "BaseImageSelect",
                                                                "key": "imgUrl",
                                                                "label": "设置选项图片（图片推荐147*52）",
                                                                "value": ""
                                                            }
                                                        ]
                                                    }
                                                ]
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "formItemType": "collapse",
                                "collapseName": "题目属性",
                                "formList": [
                                    {
                                        "formItemType": "BaseSingleSelect",
                                        "key": "stuGameTime",
                                        "label": "设置游戏时长",
                                        "value": "1",
                                        "options": [
                                            {
                                                "label": "30秒",
                                                "value": "0"
                                            },
                                            {
                                                "label": "60秒",
                                                "value": "1"
                                            },
                                            {
                                                "label": "90秒",
                                                "value": "2"
                                            },
                                            {
                                                "label": "120秒",
                                                "value": "3"
                                            },
                                            {
                                                "label": "150秒",
                                                "value": "4"
                                            },
                                            {
                                                "label": "180秒",
                                                "value": "5"
                                            }
                                        ],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置游戏时长",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "key": "stuLevelConfigs",
                                        "labelPosition": "top",
                                        "formItemType": "SpecialDynamicList",
                                        "label": "等级配置",
                                        "isShowOrder": true,
                                        "orderConfig": {
                                            "show": true,
                                            "labelPosition": "top",
                                            "type": "function",
                                            "args": {
                                                "optionLen": 3
                                            },
                                            "decorate": "const strs = ['三', '二', '一']; \n  const arr = [...Array(optionLen)].map((item, index) => `【${strs[index]}颗星】等级设置`); \nreturn arr"
                                        },
                                        "min": 3,
                                        "max": 3,
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请合理设置等级配置",
                                                "trigger": "blur"
                                            }
                                        ],
                                        "subFormConfigs": [
                                            {
                                                "formItemType": "BaseInputNumber",
                                                "key": "rightCount",
                                                "label": "答对正确个数",
                                                "value": 1,
                                                "span": 16,
                                                "stepStrictly": true,
                                                "step": 1,
                                                "min": 1,
                                                "rule": [
                                                    {
                                                        "required": true,
                                                        "trigger": "blur",
                                                        "message": "”答对正确个数“表单为必填项"
                                                    }
                                                ]
                                            },
                                            {
                                                "formItemType": "BaseInput",
                                                "key": "evaluation",
                                                "label": "评价",
                                                "value": "",
                                                "span": 16,
                                                "maxLength": 30
                                            }
                                        ],
                                        "value": [
                                            {
                                                "rightCount": 3,
                                                "evaluation": ""
                                            },
                                            {
                                                "rightCount": 2,
                                                "evaluation": ""
                                            },
                                            {
                                                "rightCount": 1,
                                                "evaluation": ""
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                "formItemType": "collapse",
                                "collapseName": "业务属性",
                                "formList": [
                                    {
                                        "formItemType": "BaseSpineSelect",
                                        "key": "stuSpineSettle",
                                        "label": "结果反馈动画",
                                        "hasDefaultOption": false,
                                        "value": {
                                            "atlas": "",
                                            "images": [],
                                            "skeleton": "",
                                            "cover": ""
                                        },
                                        "options": [
                                            {
                                                "label": "〇星动画",
                                                "value": "star0"
                                            },
                                            {
                                                "label": "一星动画",
                                                "value": "star1"
                                            },
                                            {
                                                "label": "二星动画",
                                                "value": "star2"
                                            },
                                            {
                                                "label": "三星动画",
                                                "value": "star3"
                                            }
                                        ],
                                        "validation": [],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置结果反馈动画",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundStar0",
                                        "label": "结果〇星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置结果〇星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundStar1",
                                        "label": "结果一星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置结果一星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundStar2",
                                        "label": "结果二星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置结果二星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundStar3",
                                        "label": "结果三星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置结果三星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseSpineSelect",
                                        "key": "stuSpineDirector",
                                        "label": "雪球动画",
                                        "hasDefaultOption": false,
                                        "value": {
                                            "atlas": "",
                                            "images": [],
                                            "skeleton": "",
                                            "cover": ""
                                        },
                                        "options": [
                                            {
                                                "label": "雪球背面待机动画",
                                                "value": "idle"
                                            },
                                            {
                                                "label": "雪球背面右手举旗动画",
                                                "value": "right"
                                            },
                                            {
                                                "label": "雪球背面左手举旗动画",
                                                "value": "left"
                                            },
                                            {
                                                "label": "雪球背面摇头动画",
                                                "value": "shakeHead"
                                            },
                                            {
                                                "label": "雪球正面开心动画",
                                                "value": "happy"
                                            }
                                        ],
                                        "validation": [],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置雪球动画",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseSpineSelect",
                                        "key": "stuSpineIP",
                                        "label": "移动元素动画",
                                        "hasDefaultOption": false,
                                        "value": {
                                            "atlas": "",
                                            "images": [],
                                            "skeleton": "",
                                            "cover": ""
                                        },
                                        "options": [
                                            {
                                                "label": "正面待机",
                                                "value": "idleFront"
                                            },
                                            {
                                                "label": "正面移动",
                                                "value": "moveFront"
                                            },
                                            {
                                                "label": "正面高兴移动",
                                                "value": "happyFront"
                                            },
                                            {
                                                "label": "正面伤心移动",
                                                "value": "sadFront"
                                            },
                                            {
                                                "label": "右侧待机",
                                                "value": "idleRight"
                                            },
                                            {
                                                "label": "右侧走进",
                                                "value": "enterRight"
                                            },
                                            {
                                                "label": "右侧高兴走出",
                                                "value": "exitHappyRight"
                                            },
                                            {
                                                "label": "右侧伤心走出",
                                                "value": "exitSadRight"
                                            }
                                        ],
                                        "validation": [],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置移动元素动画",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseSpineSelect",
                                        "key": "stuSpineAnswerCorrect",
                                        "label": "答对动画",
                                        "hasDefaultOption": false,
                                        "value": {
                                            "atlas": "",
                                            "images": [],
                                            "skeleton": "",
                                            "cover": ""
                                        },
                                        "options": [
                                            {
                                                "label": "播放列表",
                                                "value": "animation"
                                            }
                                        ],
                                        "validation": [],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置答对动画",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseSpineSelect",
                                        "key": "stuSpineStarProgress",
                                        "label": "星星进度条动画",
                                        "hasDefaultOption": false,
                                        "value": {
                                            "atlas": "",
                                            "images": [],
                                            "skeleton": "",
                                            "cover": ""
                                        },
                                        "options": [
                                            {
                                                "label": "默认状态",
                                                "value": "normal"
                                            },
                                            {
                                                "label": "达成一星",
                                                "value": "star_1"
                                            },
                                            {
                                                "label": "达成二星",
                                                "value": "star_2"
                                            },
                                            {
                                                "label": "达成三星",
                                                "value": "star_3"
                                            }
                                        ],
                                        "validation": [],
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置星星进度条动画",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuAudioAnswerCorrect",
                                        "label": "答对音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置答对音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuAudioAnswerWrong",
                                        "label": "答错音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置答错音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundArchieve1",
                                        "label": "达成一星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置达成一星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundArchieve2",
                                        "label": "达成二星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置达成二星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuSoundArchieve3",
                                        "label": "达成三星音效",
                                        "value": "",
                                        "rule": [
                                            {
                                                "required": true,
                                                "message": "请设置达成三星音效",
                                                "trigger": "change"
                                            }
                                        ]
                                    },
                                    {
                                        "formItemType": "BaseAudioSelect",
                                        "key": "stuAudioGameOver",
                                        "label": "游戏结束音效",
                                        "value": ""
                                    }
                                ]
                            }
                        ]
                    }
                },
                "stageData": {
                    "width": 1280,
                    "height": 720,
                    "safeWidth": 1280,
                    "safeHeight": 960,
                    "backgroundColor": "#ffffff",
                    "texture": "https://jiaoyanbos.cdnjtzy.com/cw_13fc06689795679ebc4e21c3f5fed0bb.png",
                    "textureType": 0,
                    "bgColor": ""
                },
                "extraStageData": {
                    "isAutoSubmit": true,
                    "hasRecover": false,
                    "analysis": {
                        "text": "",
                        "audio": {
                            "url": "",
                            "fileName": "",
                            "duration": 0
                        },
                        "imageUrl": ""
                    }
                },
                "componentMap": {
                    "1": {
                        "type": "optionComponent",
                        "subType": "quickResponse",
                        "componentLabel": "快速连答",
                        "tag": "",
                        "canCombine": false,
                        "deletable": false,
                        "dragable": false,
                        "editable": {
                            "properties": {
                                "width": false,
                                "height": false,
                                "x": false,
                                "y": false,
                                "angle": false
                            }
                        },
                        "properties": {
                            "active": true,
                            "width": 1280,
                            "height": 720,
                            "opacity": 255,
                            "angle": 0,
                            "x": 0,
                            "y": 0,
                            "stuStemModeLeft": "0",
                            "stuStemModeRight": "1",
                            "stuStemTextLeft": "这是左题干左题干",
                            "stuOptionListLeft": [
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_27af93650859d1810564ca7317007907.png",
                                    "_id": 1705045343176
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_8236d984721f90eac88ba342bdc5c752.png",
                                    "_id": 1706001943615
                                },
                                {
                                    "stuOptionType": "0",
                                    "text": "对的对的",
                                    "imgUrl": "",
                                    "_id": 1706001942335
                                },
                                {
                                    "stuOptionType": "0",
                                    "text": "正确正确",
                                    "imgUrl": "",
                                    "_id": 1706001940024
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_b0d9b008d4f52bf1ec01a325255a1564.png",
                                    "_id": 1706001938659
                                }
                            ],
                            "stuStemTextRight": "",
                            "stuOptionListRight": [
                                {
                                    "_id": "1706002023651-0",
                                    "stuOptionType": "0",
                                    "text": "这是宝箱",
                                    "imgUrl": ""
                                },
                                {
                                    "stuOptionType": "0",
                                    "text": "这是怪兽",
                                    "imgUrl": "",
                                    "_id": 1706002041783
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_3f8ea459a6925b8587d425484e177361.png",
                                    "_id": 1706002041287
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_3b27c7271ce97121ecfb5386121eef4b.png",
                                    "_id": 1706002038943
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_ead8d2ee911159c3a7cbe6e9c6d1fe52.png",
                                    "_id": 1706002037399
                                },
                                {
                                    "stuOptionType": "0",
                                    "text": "这是快速连答",
                                    "imgUrl": "",
                                    "_id": 1706002036215
                                },
                                {
                                    "stuOptionType": "1",
                                    "text": "",
                                    "imgUrl": "https://jiaoyanbos.cdnjtzy.com/cw_609662d596045cbb0de4479cf5a18b6f.png",
                                    "_id": 1706002035144
                                }
                            ],
                            "stuStemImgLeft": "",
                            "stuStemImgRight": "https://jiaoyanbos.cdnjtzy.com/cw_b362605f46b322caf938d73abca2b9c6.png",
                            "stuGameTime": "5",
                            "stuLevelConfigs": [
                                {
                                    "rightCount": 300,
                                    "evaluation": "A",
                                    "_id": 1705045511020
                                },
                                {
                                    "rightCount": 6,
                                    "evaluation": "B",
                                    "_id": 1705045515338
                                },
                                {
                                    "rightCount": 4,
                                    "evaluation": "C",
                                    "_id": 1705045516668
                                }
                            ],
                            "stuSpineSettle": {
                                "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.atlas",
                                "images": [
                                    "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.png"
                                ],
                                "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.json",
                                "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-e90b511bfca5fdd6875fce577c03d4c2.png",
                                "star0": "jieguo_0",
                                "star1": "jieguo_1",
                                "star2": "jieguo_2",
                                "star3": "jieguo_3"
                            },
                            "stuSoundStar0": "https://yaya.cdnjtzy.com/cchd_dev/tihu-97fed0719ff34082ca08824ea1b14eba.mp3",
                            "stuSoundStar1": "https://yaya.cdnjtzy.com/cchd_dev/tihu-3bfba1f5cf6ba34ef8e8610b7e23aa80.mp3",
                            "stuSoundStar2": "https://yaya.cdnjtzy.com/cchd_dev/tihu-7ebde8d2e9627caa1116331ea7e7f926.mp3",
                            "stuSoundStar3": "https://yaya.cdnjtzy.com/cchd_dev/tihu-99c869442c72b5613a6f18b43e1b6716.mp3",
                            "stuSpineDirector": {
                                "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.atlas",
                                "images": [
                                    "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.png"
                                ],
                                "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.json",
                                "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-790e341e821d40d47d6f9b6405206447.png",
                                "idle": "idle",
                                "right": "right",
                                "left": "left",
                                "shakeHead": "error",
                                "happy": "happy"
                            },
                            "stuSpineIP": {
                                "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.atlas",
                                "images": [
                                    "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.png"
                                ],
                                "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.json",
                                "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-ad1c78dbe263b2351f2def2e53542d54.png",
                                "idleFront": "zheng_idle",
                                "moveFront": "zheng_walk",
                                "happyFront": "zheng_happy",
                                "sadFront": "zheng_sad",
                                "idleRight": "idle_right",
                                "enterRight": "in_right",
                                "exitHappyRight": "out_happy_right",
                                "exitSadRight": "out_sad_right"
                            },
                            "stuSpineAnswerCorrect": {
                                "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.atlas",
                                "images": [
                                    "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.png"
                                ],
                                "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.json",
                                "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-3ce80d7de31408ebb0b10760e63e3b65.png",
                                "animation": "sahua"
                            },
                            "stuSpineStarProgress": {
                                "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.atlas",
                                "images": [
                                    "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.png"
                                ],
                                "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.json",
                                "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-1fff7447849baa33c150fe80d39843fd.png",
                                "normal": "normal",
                                "star_1": "xing_1",
                                "star_2": "xing_2",
                                "star_3": "xing_3"
                            },
                            "stuAudioAnswerCorrect": "https://yaya.cdnjtzy.com/cchd_dev/tihu-0eba914481d030442811b667de0c1de0.mp3",
                            "stuAudioAnswerWrong": "https://yaya.cdnjtzy.com/cchd_dev/tihu-a471fd6d061932a59f4bd0c6f6d68976.mp3",
                            "stuSoundArchieve1": "https://yaya.cdnjtzy.com/cchd_dev/tihu-3bfba1f5cf6ba34ef8e8610b7e23aa80.mp3",
                            "stuSoundArchieve2": "https://yaya.cdnjtzy.com/cchd_dev/tihu-7ebde8d2e9627caa1116331ea7e7f926.mp3",
                            "stuSoundArchieve3": "https://yaya.cdnjtzy.com/cchd_dev/tihu-36cc275ba7a55c19348c83ec9e57bdb7.mp3",
                            "stuAudioGameOver": "https://yaya.cdnjtzy.com/cchd_dev/tihu-84d52be14d33c63b51c3ef1559ad51fd.mp3"
                        },
                        "id": "1",
                        "extra": {
                            "tag": ""
                        }
                    },
                    "2": {
                        "tag": "",
                        "type": "spine",
                        "properties": {
                            "timeScale": 1,
                            "animationList": [
                                ""
                            ],
                            "loop": true,
                            "active": true,
                            "scaleX": 1,
                            "scaleY": 1,
                            "x": 0,
                            "y": 0,
                            "width": 1305.5,
                            "height": 734.5,
                            "angle": 0,
                            "offsetX": -52.73199999999997,
                            "offsetY": 37.080000000000034
                        },
                        "spineData": {
                            "atlas": "http://10.250.10.91:8000/yindao.atlas",
                            "images": [
                                "http://10.250.10.91:8000/yindao.png"
                            ],
                            "skeleton": "http://10.250.10.91:8000/yindao.json",
                            "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-2a457c606f55b0b7df978b910c6489fe.png"
                        },
                        "id": "2",
                        "extra": {
                            "tag": ""
                        }
                    }
                },
                "componentIds": [
                    "1",
                    "2"
                ],
                "currentComponentIds": [],
                "animations": {
                    "afterReadingQuestion": {
                        "audio": {
                            "url": "",
                            "delay": 0
                        },
                        "fragments": {},
                        "points": {}
                    },
                    "afterSubmitCorrect": {
                        "audio": {
                            "url": "",
                            "delay": 0
                        },
                        "fragments": {},
                        "points": {}
                    },
                    "afterSubmitWrong": {
                        "audio": {
                            "url": "",
                            "delay": 0
                        },
                        "fragments": {},
                        "points": {}
                    }
                },
                "contextMenu": {
                    "top": 400,
                    "left": 200,
                    "visible": false
                },
                "clipboard": {
                    "type": 0,
                    "components": []
                },
                "isEditingComponentAnimations": false,
                "editingAnimationsComponentId": "",
                "componentAnimationConfig": [],
                "componentAnimationVal": "",
                "componentFragmentId": "",
                "componentActiveActionId": "",
                "tagsData": "{\"ability\":[],\"difficulty\":{\"id\":0,\"name\":null},\"pointList\":[{\"id\":1,\"name\":\"质量及质量的常用单位\"}]}",
                "editFocus": false,
                "parentVersion": 1,
                "renderFormula": false,
                "containerWidth": 468,
                "containerHeight": 351,
                "componentManagerCard": {
                    "visible": true,
                    "activeName": "LIBRARY"
                },
                "moduleAnimations": {
                    "animationVal": "afterReadingQuestion",
                    "fragmentId": "",
                    "activeActionId": "",
                    "animations": {
                        "afterReadingQuestion": {
                            "audio": {
                                "url": "",
                                "delay": 0
                            },
                            "fragments": {},
                            "points": {}
                        },
                        "afterSubmitCorrect": {
                            "audio": {
                                "url": "",
                                "delay": 0
                            },
                            "fragments": {},
                            "points": {}
                        },
                        "afterSubmitWrong": {
                            "audio": {
                                "url": "",
                                "delay": 0
                            },
                            "fragments": {},
                            "points": {}
                        }
                    },
                    "isPlayingAnimation": false,
                    "isPlayingFragment": false
                },
                "timeTravel": {
                    "currentIndex": 0,
                    "total": 0
                },
                "rulerTick": {
                    "verticalLines": [],
                    "horizontalLines": []
                },
                "gradeId": 0,
                "subjectId": 0,
                "status": 1
            },
            (window as any)._$store.commit = (commit: string, opts: any) => {
                yy.log(commit, opts);
            };
            (window as any)._$store.subscribe = (mutation, state) => {
                yy.log(mutation, state);
            };
            (window as any)._$store.subscribeAction = (obj) => {
                yy.log(obj);
            };
            (window as any)._$store.getters = {
                "newId": "3",
                "currentComponents": [],
                "componentIdsAllObjectType": [
                    {
                        "id": "1",
                        "subIds": []
                    },
                    {
                        "id": "2",
                        "subIds": []
                    }
                ],
                "isNormalMode": true,
                "isAnimationMode": false,
                "flatComponentActions": [],
                "moduleAnimations/flatActions": [],
                "moduleAnimations/currentComponentsActions": [],
                "timeTravel/canRedo": false,
                "timeTravel/canUndo": true
            }
        }
    }
}
