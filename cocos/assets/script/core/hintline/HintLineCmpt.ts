import HintLineManager from "./HintLineManager";
import DisplayObjectManager from "../display/DisplayObjectManager";
import { LINEDATA, LINT_POS_TYPE, LINT_DISPLAY_TYPE } from "./HintLineDisplay";

export class LineNode extends cc.Node {
    findLineData: LINEDATA = null;
    lineData: LINEDATA = null;
    posType: LINT_POS_TYPE = null;
}
/**
 * 辅助线显示对象类
 */
const { ccclass } = cc._decorator;
@ccclass
export default class HintLineCmpt extends cc.Component {
    // 对象管理器
    private _displayManager: DisplayObjectManager;
    private _instanceManager:HintLineManager = null;
    private _arrNode: Array<LineNode> = []
    onLoad () {
        cc.director.on("REMOVE_ALL_LINE", this.removeAllLine, this);
        this._instanceManager = yy.single.instance((HintLineManager));
        this._displayManager = yy.single.instance(DisplayObjectManager);
        this._instanceManager.callAdd = (hintLineData: LINEDATA, findLineData: LINEDATA, posType: LINT_POS_TYPE) => {
            // 如果ID 不是 现在绘制的id, 则清空之前的
            this.drawLine(hintLineData, findLineData, posType);
        };

        this._instanceManager._callDebugDraw = (sprite: cc.Node, pos: cc.Vec2) => {
            this.node.addChild(sprite);
            sprite.x = pos.x;
            sprite.y = pos.y;
            this.node.runAction(
                cc.sequence(
                    cc.delayTime(1),
                    cc.callFunc(() => {
                        sprite.removeFromParent();
                    })
                )
            );
        };
    }

    /**
     * 画一条辅助线
     * @param hintLineData 碰撞的线段
     * @param findLineData 查找的线段
     */
    private drawLine (hintLineData: LINEDATA, findLineData: LINEDATA, posType: LINT_POS_TYPE): void {
        for (let i = 0; i < this._arrNode.length; i++) {
            let data = this._arrNode[i];
            if (data.findLineData.id === findLineData.id && data.lineData.id === hintLineData.id && data.posType === posType) {
                return;
            }
        }
        let edgeNode = new LineNode();
        edgeNode.findLineData = findLineData;
        edgeNode.lineData = hintLineData;
        edgeNode.posType = posType;
        edgeNode.addComponent(cc.Graphics);
        this.node.addChild(edgeNode);
        this._arrNode.push(edgeNode);
        let graphics = edgeNode.getComponent(cc.Graphics);
        graphics.strokeColor = cc.Color.RED;
        if(hintLineData.id === -66){
            if(hintLineData.typeDisplay === LINT_DISPLAY_TYPE.VERTICAL){
                graphics.lineWidth = 8;
            }else{
                graphics.lineWidth = 4;
            }
        }
        graphics.clear();
        if (hintLineData.typeDisplay === LINT_DISPLAY_TYPE.HORIZONTAL) {
            let y = hintLineData.pos;
            graphics.moveTo(-cc.winSize.width / 2, y);
            graphics.lineTo(cc.winSize.width / 2, y);
        } else {
            let x = hintLineData.pos;
            graphics.moveTo(x, -cc.winSize.height / 2);
            graphics.lineTo(x, cc.winSize.height / 2);
        }
        graphics.stroke();

        let display = this._displayManager.getDisplayObjectById(hintLineData.id.toString());
        if (display) {
            display.showPreSelectRect();
        }
    }

    /**
     * 清除所有辅助线
     */
    private removeAllLine () {
        // 清除旧的线段
        let temp = [];
        for (let i = this._arrNode.length - 1; i >= 0; i--) {
            temp.push(i);
        }
        // 清除旧的线段
        for (let i = 0; i < temp.length; i++) {
            let node = this._arrNode.splice(temp[i], 1);
            this.node.removeChild(node[0]);
            let display = this._displayManager.getDisplayObjectById(node[0].lineData.id.toString());
            if (display) {
                display.clearPreSelectRect();
            }
        }
    }

    onDestroy () {
        cc.director.off("REMOVE_ALL_LINE", this.removeAllLine, this);
    }

    /**
     * 检测没有碰撞的辅助线就被清除
     * @param dt 
     */
    update (): void {
        let temp = [];
        for (let i = this._arrNode.length - 1; i >= 0; i--) {
            let data = this._arrNode[i];
            let bCheck = this._instanceManager.checkLineCollider(data.lineData, data.findLineData);
            if (!bCheck) {
                temp.push(i);
            }
        }
        // 清除旧的线段
        for (let i = 0; i < temp.length; i++) {
            let node = this._arrNode.splice(temp[i], 1);
            this.node.removeChild(node[0]);
            let display = this._displayManager.getDisplayObjectById(node[0].lineData.id.toString());
            if (display) {
                display.clearPreSelectRect();
            }
        }
    }
}