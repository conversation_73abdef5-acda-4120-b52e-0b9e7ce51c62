import { SingleBase } from "../../../qte/core/base/SingleBase";
import { LINEDATA, LINT_POS_TYPE, HINT_LINE_TYPE, LINT_DISPLAY_TYPE } from "./HintLineDisplay";
import DisplayObjectManager from "../display/DisplayObjectManager";
import MathUtil from "../../../qte/core/utils/MathUtil";
import { SEL_POINT_ENUM } from "../display/base/cmpt/DisplaySelect";
// 碰撞的检测
export enum LINE_COLLIDE_TYPE {
    NODE = 0,
    HOR = 1,
    VER = 2,
    HORandVER = 3
}
/**
 * 辅助线管理类
 */
export default class HintLineManager extends SingleBase {
    // 编辑器辅助线数组
    private _arrHintLine: Array<LINEDATA> = [];
    // 对象管理器
    private _displayManager: DisplayObjectManager;
    // 添加线段回调
    private _callAdd: Function = null;
    // 辅助绘制
    _callDebugDraw: Function = null;
    get callAdd() {
        return this._callAdd;
    }
    set callAdd(call: Function) {
        this._callAdd = call;
    }
    // 删除线段断掉
    private _callDel: Function = null;
    get callDel() {
        return this._callDel;
    }
    set callDel(call: Function) {
        this._callDel = call;
    }
    // 两条线直接碰撞的阈值
    private nColliderChar = 5;
    // 线段吸附方向
    private nAdsorbHor = 0;
    private nOldPosY = 0;
    private nOldHorLine: LINEDATA = null;
    private nOldFindHorLine: LINEDATA = null;
    private nOldHorType: LINT_POS_TYPE = LINT_POS_TYPE.HORIZONTAL;

    private nAdsorbVer = 0;
    private nOldPosX = 0;
    private nOldVerLine: LINEDATA = null;
    private nOldFindVerLine: LINEDATA = null;
    private nOldVerType: LINT_POS_TYPE = LINT_POS_TYPE.VERTICAL;

    initInstance(): void {
        this._displayManager = yy.single.instance(DisplayObjectManager);
        this._arrHintLine = [

        ];

        let lineData0 = new LINEDATA();
        lineData0.id = -66;
        lineData0.type = HINT_LINE_TYPE.DISPLAY;
        lineData0.pos = 360;
        lineData0.typeDisplay = LINT_DISPLAY_TYPE.HORIZONTAL;
        lineData0.posType = LINT_POS_TYPE.TOP;
        this._arrHintLine.push(lineData0);

        let lineData1 = new LINEDATA();
        lineData1.id = -66;
        lineData1.type = HINT_LINE_TYPE.DISPLAY;
        lineData1.pos = -360;
        lineData1.typeDisplay = LINT_DISPLAY_TYPE.HORIZONTAL;
        lineData1.posType = LINT_POS_TYPE.BOTTOM;
        this._arrHintLine.push(lineData1);

        let lineData2 = new LINEDATA();
        lineData2.id = -66;
        lineData2.type = HINT_LINE_TYPE.DISPLAY;
        lineData2.pos = -640;
        lineData2.typeDisplay = LINT_DISPLAY_TYPE.VERTICAL;
        lineData2.posType = LINT_POS_TYPE.LEFT;
        this._arrHintLine.push(lineData2);

        let lineData3 = new LINEDATA();
        lineData3.id = -66;
        lineData3.type = HINT_LINE_TYPE.DISPLAY;
        lineData3.pos = 640;
        lineData3.typeDisplay = LINT_DISPLAY_TYPE.VERTICAL;
        lineData3.posType = LINT_POS_TYPE.RIGHT;
        this._arrHintLine.push(lineData3);
    }


    removeHintLineByDisplay(lineId: string) {
        // 横 中线
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL);
        // 横 Top
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP);
        // 横 Bottom
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM);
        // 竖 中线
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL);
        // 竖 Left
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT);
        // 竖 Right
        this.removeHintLine(Number(lineId), false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT);
    }

    /**
     * 添加辅助线对象，依赖displayId
     * @param lineId 
     */
    addHintLinebyDisplay(lineId: string) {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return;
        }
        let nLindId = Number(lineId);
        let newPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosY = newPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });
        const topY = newPosY[0].y;
        const bottomY = newPosY[3].y;
        // 横 中线
        this.addHintLine(nLindId, false, display.node.y, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL);
        // 横 Top
        this.addHintLine(nLindId, false, topY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP);
        // 横 Bottom
        this.addHintLine(nLindId, false, bottomY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM);

        // // // 竖 中线
        let newXPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosX = newXPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        const leftX = newPosX[0].x;
        const rightX = newPosX[3].x;
        // 竖 中线
        this.addHintLine(nLindId, false, display.node.x, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL);
        // 竖 Left
        this.addHintLine(nLindId, false, leftX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT);
        // 竖 Right
        this.addHintLine(nLindId, false, rightX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT);
    }

    /**
     * 添加一条线
     * @param id 
     * @param isEditor 
     */
    addHintLine(id: number, isEditor: boolean, pos: number, typeDisplay: LINT_DISPLAY_TYPE, posType: LINT_POS_TYPE = LINT_POS_TYPE.HORIZONTAL): void {
        let lineData = new LINEDATA();
        lineData.id = id;
        let type = HINT_LINE_TYPE.DISPLAY;
        if (isEditor) {
            type = HINT_LINE_TYPE.EDITOR;
        }
        lineData.type = type;
        lineData.pos = pos;
        lineData.typeDisplay = typeDisplay;
        if (!isEditor) {
            lineData.posType = posType;
        }
        this._arrHintLine.push(lineData);
    }

    /**
     * 删除一条线
     * @param id 
     * @param isEditor 
     */
    removeHintLine(id: number, isEditor: boolean, typeDisplay: LINT_DISPLAY_TYPE, posType: LINT_POS_TYPE = LINT_POS_TYPE.HORIZONTAL): void {
        let nFindIndex = this.findLineData(id, isEditor, typeDisplay, posType);
        if (nFindIndex !== -1) {
            this._arrHintLine.splice(nFindIndex, 1);
            if (!isEditor) {
                return;
            }
            for (let i = 0; i < this._arrHintLine.length; i++) {
                let lineData = this._arrHintLine[i];
                if (lineData.type === HINT_LINE_TYPE.EDITOR &&
                    lineData.typeDisplay === typeDisplay &&
                    lineData.id > id) {
                    lineData.id -= 1;
                }
            }
        }
    }

    /**
     * 查找一条线段的数据实例
     * @param id 
     * @param isEditor 
     * @param typeDisplay 
     * @param posType 
     */
    private findLineData(id: number, isEditor: boolean, typeDisplay: LINT_DISPLAY_TYPE, posType: LINT_POS_TYPE = LINT_POS_TYPE.HORIZONTAL): number {
        let nFindIndex = -1;
        let type = HINT_LINE_TYPE.DISPLAY;
        if (isEditor) {
            type = HINT_LINE_TYPE.EDITOR;
        }
        for (let i = 0; i < this._arrHintLine.length; i++) {
            let lineData = this._arrHintLine[i];
            if (lineData.id === id &&
                lineData.typeDisplay === typeDisplay &&
                lineData.posType === posType &&
                lineData.type === type) {
                nFindIndex = i;
                break;
            }
        }
        return nFindIndex;
    }

    /**
     * 检查当前线段是否和当时绘制的碰撞线段仍在碰撞
     * @param hintLineData 
     * @param findLineData 
     */
    checkLineCollider(hintLineData: LINEDATA, findLineData: LINEDATA): boolean {
        let nFindLine = this.findLineData(findLineData.id, findLineData.type === HINT_LINE_TYPE.EDITOR, findLineData.typeDisplay, findLineData.posType);
        if (nFindLine === -1) {
            return false;
        }
        let nLineIndex = this.findLineData(hintLineData.id, hintLineData.type === HINT_LINE_TYPE.EDITOR, hintLineData.typeDisplay, hintLineData.posType);
        if (nLineIndex === -1) {
            return false;
        }
        let findData = this._arrHintLine[nFindLine];
        let lineData = this._arrHintLine[nLineIndex];
        // findData.pos > (lineData.pos - this.nColliderChar) && findData.pos < (lineData.pos + this.nColliderChar) &&
        if (findData.findId == lineData.id + "_" + lineData.posType) {
            return true;
        }
        return false;
    }

    /**
     * 缩放过程,更新线段
     * @param lineId 
     */
    updateLineByDisplayScale(lineId: string): void {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return;
        }
        let nLindId = Number(lineId);
        let newPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosY = newPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });

        const topY = newPosY[0].y;
        const bottomY = newPosY[3].y;
        // // // 横 中线
        this.updateLinePosAndCollide(nLindId, false, Number(display.node.y), LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL, false);
        // 横 Top
        this.updateLinePosAndCollide(nLindId, false, topY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP, false);
        // 横 Bottom
        this.updateLinePosAndCollide(nLindId, false, bottomY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM, false);
        // // // 竖 中线
        let newXPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosX = newXPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        const leftX = newPosX[0].x;
        const rightX = newPosX[3].x;
        this.updateLinePosAndCollide(nLindId, false, Number(display.node.x), LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL, false);
        // // // 竖 Left
        this.updateLinePosAndCollide(nLindId, false, leftX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT, false);
        // // // 竖 Right
        this.updateLinePosAndCollide(nLindId, false, rightX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT, false);
    }

    updateVer(lineId: string, pos: cc.Vec2, deltaY: number): boolean {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return false;
        }
        let nLindId = Number(lineId);
        let newPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosY = newPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });
        const topY = newPosY[0].y;
        const bottomY = newPosY[3].y;
        const nHorCheck = this.updateLinePosAndCollide(nLindId, false, Number(display.node.y), LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL, true);
        const nTopCheck = this.updateLinePosAndCollide(nLindId, false, topY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP, true);
        const nBottomCheck = this.updateLinePosAndCollide(nLindId, false, bottomY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM, true);
        let bHorCollide = false;
        if (nHorCheck !== -1 || nTopCheck !== -1 || nBottomCheck !== -1) {
            let lineData = null;
            let charY = null;
            let findLine = null;
            let type = null;
            if (nHorCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nHorCheck];
                charY = lineData.pos;
                type = LINT_POS_TYPE.HORIZONTAL;
            } else if (nTopCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nTopCheck];
                charY = lineData.pos - topY + display.node.y;
                type = LINT_POS_TYPE.TOP;
            } else if (nBottomCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nBottomCheck];
                charY = lineData.pos - bottomY + display.node.y;
                type = LINT_POS_TYPE.BOTTOM;
            }
            if (this.nAdsorbHor === 0) {
                this.nAdsorbHor = pos.y - this.nOldPosY;
                if (this.nAdsorbHor === 0) {
                    this.nAdsorbHor = deltaY;
                }
                this.adsorbLineY(lineId, charY);
                this.nOldHorLine = lineData;
                this.nOldFindHorLine = findLine;
                this.nOldHorType = type;
                bHorCollide = true;
            } else if (this.nAdsorbHor > 0) {
                if (pos.y < this.nOldPosY) {
                    this.nAdsorbHor = pos.y - this.nOldPosY;
                    this.adsorbLineY(lineId, charY);
                    this.nOldHorLine = lineData;
                    this.nOldFindHorLine = findLine;
                    this.nOldHorType = type;
                    bHorCollide = true;
                }
            } else if (pos.y > this.nOldPosY) {
                this.nAdsorbHor = pos.y - this.nOldPosY;
                this.adsorbLineY(lineId, charY);
                this.nOldHorLine = lineData;
                this.nOldFindHorLine = findLine;
                this.nOldHorType = type;
                bHorCollide = true;
            }
        }
        this.nOldPosY = pos.y;
        return bHorCollide;
    }

    updateHor(lineId: string, pos: cc.Vec2, deltaX: number): boolean {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return false;
        }
        let nLindId = Number(lineId);
        let newXPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosX = newXPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        let bVerCollide = false;
        const leftX = newPosX[0].x;
        const rightX = newPosX[3].x;
        const nVerCheck = this.updateLinePosAndCollide(nLindId, false, Number(display.node.x), LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL, true);
        const nLeftCheck = this.updateLinePosAndCollide(nLindId, false, leftX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT, true);
        const nRightCheck = this.updateLinePosAndCollide(nLindId, false, rightX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT, true);
        if (nVerCheck !== -1 || nLeftCheck !== -1 || nRightCheck !== -1) {
            let lineData = null;
            let charX = null;
            let findLine = null;
            let type = null;
            if (nVerCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nVerCheck];
                charX = lineData.pos;
                type = LINT_POS_TYPE.VERTICAL;
            } else if (nLeftCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nLeftCheck];
                charX = lineData.pos - leftX + display.node.x;
                type = LINT_POS_TYPE.LEFT;
            } else if (nRightCheck !== -1) {
                let findLineIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT);
                findLine = this._arrHintLine[findLineIndex];
                lineData = this._arrHintLine[nRightCheck];
                charX = lineData.pos - rightX + display.node.x;
                type = LINT_POS_TYPE.RIGHT;
            }
            if (this.nAdsorbVer === 0) {
                this.nAdsorbVer = pos.x - this.nOldPosX;
                if (this.nAdsorbVer === 0) {
                    this.nAdsorbVer = deltaX;
                }
                this.adsorbLineX(lineId, charX);
                this.nOldVerLine = lineData;
                this.nOldFindVerLine = findLine;
                this.nOldVerType = type;
                bVerCollide = true;
            } else if (this.nAdsorbVer > 0) {
                if (pos.x < this.nOldPosX) {
                    this.nAdsorbVer = pos.x - this.nOldPosX;
                    this.adsorbLineX(lineId, charX);
                    this.nOldVerLine = lineData;
                    this.nOldFindVerLine = findLine;
                    this.nOldVerType = type;
                    bVerCollide = true;
                }
            } else if (pos.x > this.nOldPosX) {
                this.nAdsorbVer = pos.x - this.nOldPosX;
                this.adsorbLineX(lineId, charX);
                this.nOldVerLine = lineData;
                this.nOldFindVerLine = findLine;
                this.nOldVerType = type;
                bVerCollide = true;
            }
        }
        this.nOldPosX = pos.x;
        return bVerCollide;
    }
    /**
     * 移动过程，更新辅助线并做检测，返回碰撞类型
     * @param lineId 
     * @param pos 
     */
    updateLineByDisplayMove(lineId: string, pos: cc.Vec2, deltaX: number, deltaY: number): LINE_COLLIDE_TYPE {
        let bHorCollide = this.updateVer(lineId, pos, deltaY);
        let bVerCollide = this.updateHor(lineId, pos, deltaX);
        if (bHorCollide && !bVerCollide) {
            return LINE_COLLIDE_TYPE.HOR;
        }
        if (bVerCollide && !bHorCollide) {
            return LINE_COLLIDE_TYPE.VER;
        }
        if (bHorCollide && bVerCollide) {
            return LINE_COLLIDE_TYPE.NODE;
        }
        return LINE_COLLIDE_TYPE.NODE;
    }


    updateMagLineByMove(lineId: string, targetPos: cc.Vec2, pos: cc.Vec2): { isColl: boolean, culPps: cc.Vec2 } {
        let isColl = false;
        let culPps = cc.v2(0, 0);
        let display = this._displayManager.getDisplayObjectById(lineId);

        if (!display) {
            return { isColl: false, culPps };
        }

        if (!display) {
            return;
        }
        let nLindId = Number(lineId);
        let newPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosY = newPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });

        const topY = newPosY[0].y;
        const bottomY = newPosY[3].y;
        const middleY = Number(display.node.y);


        let newXPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosX = newXPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        const leftX = newPosX[0].x;
        const rightX = newPosX[3].x;
        const middleX = Number(display.node.x);

        let posXType = LINT_POS_TYPE.LEFT;
        let magX = Math.abs(pos.x - leftX);
        if (magX > Math.abs(pos.x - rightX)) {
            posXType = LINT_POS_TYPE.RIGHT;
            magX = Math.abs(pos.x - rightX);
        }
        if (magX > Math.abs(pos.x - middleX)) {
            posXType = LINT_POS_TYPE.VERTICAL;
            magX = Math.abs(pos.x - middleX);
        }

        let posYType = LINT_POS_TYPE.TOP;
        let magY = Math.abs(pos.y - topY);
        if (magY > Math.abs(pos.y - bottomY)) {
            posYType = LINT_POS_TYPE.BOTTOM;
            magY = Math.abs(pos.y - bottomY);
        }
        if (magY > Math.abs(pos.y - middleY)) {
            posYType = LINT_POS_TYPE.HORIZONTAL;
            magY = Math.abs(pos.y - middleY);
        }

        let posType: any = posXType;
        let nReturnIndex = -1;
        let type = null;
        const nVerCheck: { nReturnIndex: number, mag: number } = this.preCheckLineMagCollide(false, targetPos.x, LINT_DISPLAY_TYPE.VERTICAL);
        const nHorCheck: { nReturnIndex: number, mag: number } = this.preCheckLineMagCollide(false, targetPos.y, LINT_DISPLAY_TYPE.HORIZONTAL);
        if (nVerCheck.nReturnIndex !== -1 && nHorCheck.nReturnIndex !== -1) {
            if (nVerCheck.mag < nHorCheck.mag) {
                nReturnIndex = nVerCheck.nReturnIndex;
                type = LINT_DISPLAY_TYPE.HORIZONTAL;
                posType = posXType;
            }
            else {
                nReturnIndex = nHorCheck.nReturnIndex;
                type = LINT_DISPLAY_TYPE.HORIZONTAL;
                posType = posYType;
            }
        }
        else if (nHorCheck.nReturnIndex !== -1) {
            nReturnIndex = nHorCheck.nReturnIndex;
            type = LINT_DISPLAY_TYPE.HORIZONTAL;
            posType = posYType;

        }
        else if (nVerCheck.nReturnIndex !== -1) {
            nReturnIndex = nVerCheck.nReturnIndex;
            type = LINT_DISPLAY_TYPE.VERTICAL;
            posType = posXType;
        }

        if (nReturnIndex != -1) {
            let nFindIndex = this.findLineData(nLindId, false, type, posType);
            let findLineData = null;
            if (nFindIndex !== -1) {
                findLineData = this._arrHintLine[nFindIndex];
            }

            for (let i = 0; i < this._arrHintLine.length; i++) {
                if (i === nFindIndex) {
                    continue;
                }
                let lineData = this._arrHintLine[i];
                if (lineData.findId) {
                    lineData.findId = null;
                }
            }
            let lineData = this._arrHintLine[nReturnIndex];

            if (lineData && findLineData) {
                if (findLineData && findLineData.findId !== lineData.id + "_" + lineData.posType) {
                    findLineData.mousePos = pos;
                    findLineData.findId = lineData.id + "_" + lineData.posType;
                    this._callAdd(lineData, findLineData, posType);
                }
                let magMousePos = pos.sub(findLineData.mousePos).mag();
                if (lineData.typeDisplay === LINT_DISPLAY_TYPE.HORIZONTAL) {
                    if (magMousePos < this.nColliderChar) {
                        isColl = true;
                        culPps = cc.v2(0, lineData.pos);
                    } else if (findLineData.findId == lineData.id + "_" + lineData.posType) {
                        findLineData.findId = null;
                    }
                } else if (lineData.typeDisplay === LINT_DISPLAY_TYPE.VERTICAL) {
                    if (magMousePos < this.nColliderChar) {
                        isColl = true;
                        culPps = cc.v2(lineData.pos, 0);
                    } else if (findLineData.findId == lineData.id + "_" + lineData.posType) {
                        findLineData.findId = null;
                    }
                }
            }
        } else {
            for (let i = 0; i < this._arrHintLine.length; i++) {
                let lineData = this._arrHintLine[i];
                if (lineData.findId) {
                    lineData.findId = null;
                }
            }
        }

        return { isColl, culPps };

    }



    /**
   * chenk线段属性
   * @param id 
   * @param isEditor 
   */
    preCheckLineMagCollide(isEditor: boolean, pos: number, typeDisplay: LINT_DISPLAY_TYPE): { nReturnIndex: number, mag: number } {
        let nReturnIndex = -1;
        let minMag = null;
        // 碰撞检测
        for (let i = 0; i < this._arrHintLine.length; i++) {

            let lineData = this._arrHintLine[i];
            if (lineData.id !== -66) {
                continue;
            }
            if (isEditor && lineData.type === HINT_LINE_TYPE.EDITOR) {
                continue;
            }
            if (typeDisplay !== lineData.typeDisplay) {
                continue;
            }
            let bSelect = this._displayManager.selectedIds.find((value) => value === lineData.id.toString());
            if (bSelect) {
                continue;
            }


            if ((pos > (lineData.pos - this.nColliderChar) && pos < (lineData.pos + this.nColliderChar))) {
                if (minMag == null) {
                    minMag = Math.abs(lineData.pos - pos);
                    nReturnIndex = i;
                }
                else {
                    if (Math.abs(lineData.pos - pos) < minMag) {
                        minMag = Math.abs(lineData.pos - pos);
                        nReturnIndex = i;
                    }
                }
            }
        }
        return { nReturnIndex, mag: minMag };
    }



    /**
    * chenk线段属性
    * @param id 
    * @param isEditor 
    */
    preCheckLinePosAndCollide(id: number, isEditor: boolean, pos: number, typeDisplay: LINT_DISPLAY_TYPE, posType: LINT_POS_TYPE): { nReturnIndex: number, mag: number } {
        if (posType === null) {
            posType = LINT_POS_TYPE.HORIZONTAL;
        }

        let minMag = null;
        let nReturnIndex = -1;
        // 碰撞检测
        for (let i = 0; i < this._arrHintLine.length; i++) {
            let lineData = this._arrHintLine[i];
            if (isEditor && lineData.type === HINT_LINE_TYPE.EDITOR) {
                continue;
            }
            if (typeDisplay !== lineData.typeDisplay) {
                continue;
            }
            let bSelect = this._displayManager.selectedIds.find((value) => value === lineData.id.toString());
            if (bSelect) {
                continue;
            }
            if ((pos > (lineData.pos - this.nColliderChar) && pos < (lineData.pos + this.nColliderChar))) {
                if (minMag == null) {
                    minMag = Math.abs(lineData.pos - pos);
                    nReturnIndex = i;
                }
                else {
                    if (Math.abs(lineData.pos - pos) < minMag) {
                        minMag = Math.abs(lineData.pos - pos);
                        nReturnIndex = i;
                    }
                }
            }

        }
        return { nReturnIndex, mag: minMag };
    }






    updateLineByMove(lineId: string, pos: cc.Vec2, deltaX: number, deltaY: number): { type: LINE_COLLIDE_TYPE, deltaX: number, deltaY: number } {
        let bx: { isColl: boolean, deltaY: number } = this.updateVerNextMove(lineId, pos, deltaY);
        let by: { isColl: boolean, deltaX: number } = this.updateHorNextMove(lineId, pos, deltaX);
        if (bx.isColl && !by.isColl) {
            return { type: LINE_COLLIDE_TYPE.HOR, deltaX: by.deltaX, deltaY: bx.deltaY };
        }
        if (by.isColl && !bx.isColl) {
            return { type: LINE_COLLIDE_TYPE.VER, deltaX: by.deltaX, deltaY: bx.deltaY };
        }
        if (bx.isColl && by.isColl) {
            return { type: LINE_COLLIDE_TYPE.NODE, deltaX: by.deltaX, deltaY: bx.deltaY };
        }
        return { type: LINE_COLLIDE_TYPE.NODE, deltaX: by.deltaX, deltaY: bx.deltaY };
    }

    resetHintLineHandler(): void {
        this._arrHintLine.forEach(line => {
            line.findId = null;
        });
    }





    updateVerNextMove(lineId: string, pos: cc.Vec2, deltaY: number): { isColl: boolean, deltaY: number } {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return { isColl: false, deltaY: deltaY };
        }
        let nLindId = Number(lineId);
        let newPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosY = newPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });
        const topY = newPosY[0].y;
        const bottomY = newPosY[3].y;
        const middleY = Number(display.node.y);

        const nHorCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, middleY + deltaY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL);
        const nTopCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, topY + deltaY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP);
        const nBottomCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, bottomY + deltaY, LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM);
        let minMag = null;
        let nReturnIndex = -1;
        let posType = null;
        if (nHorCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nHorCheck.mag;
                nReturnIndex = nHorCheck.nReturnIndex;
                posType = LINT_POS_TYPE.HORIZONTAL;
            } else {
                if (nHorCheck.mag < minMag) {
                    minMag = nHorCheck.mag;
                    nReturnIndex = nHorCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.HORIZONTAL;
                }
            }
        }
        if (nTopCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nTopCheck.mag;
                nReturnIndex = nTopCheck.nReturnIndex;
                posType = LINT_POS_TYPE.TOP;
            } else {
                if (nTopCheck.mag < minMag) {
                    minMag = nTopCheck.mag;
                    nReturnIndex = nTopCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.TOP;
                }
            }
        }
        if (nBottomCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nBottomCheck.mag;
                nReturnIndex = nBottomCheck.nReturnIndex;
                posType = LINT_POS_TYPE.BOTTOM;
            } else {
                if (nBottomCheck.mag < minMag) {
                    minMag = nBottomCheck.mag;
                    nReturnIndex = nBottomCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.BOTTOM;
                }
            }
        }



        let bHorCollide = false;
        let offset = 0;
        let charY = deltaY;
        if (nReturnIndex != -1) {
            let nFindIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.HORIZONTAL, posType);
            let findLineData = null;
            if (nFindIndex !== -1) {
                findLineData = this._arrHintLine[nFindIndex];
            }

            for (let i = 0; i < this._arrHintLine.length; i++) {
                if (i === nFindIndex || i === nReturnIndex) {
                    continue;
                }
                let lineData = this._arrHintLine[i];
                if (lineData.findId && lineData.typeDisplay === LINT_DISPLAY_TYPE.HORIZONTAL) {
                    lineData.findId = null;
                }
            }
            let lineData = this._arrHintLine[nReturnIndex];

            if (lineData && findLineData) {
                if (findLineData.findId !== lineData.id + "_" + lineData.posType) {

                    findLineData.mousePos = pos;
                    findLineData.findId = lineData.id + "_" + lineData.posType;
                    this._callAdd(lineData, findLineData, posType);
                }
                let magMousePos = Math.abs(pos.y - findLineData.mousePos.y);
                if (magMousePos < this.nColliderChar) {
                    bHorCollide = true;
                    charY = lineData.pos - findLineData.pos;
                } else if (findLineData.findId == lineData.id + "_" + lineData.posType) {
                    findLineData.findId = null;
                    offset = pos.y - findLineData.mousePos.y;
                }

            }
        } else {
            for (let i = 0; i < this._arrHintLine.length; i++) {
                let lineData = this._arrHintLine[i];
                if (lineData.findId && lineData.typeDisplay === LINT_DISPLAY_TYPE.HORIZONTAL) {
                    lineData.findId = null;
                }
            }
        }

        return { isColl: bHorCollide, deltaY: charY + offset };
    }



    updateHorNextMove(lineId: string, pos: cc.Vec2, deltaX: number): { isColl: boolean, deltaX: number } {
        let display = this._displayManager.getDisplayObjectById(lineId);
        if (!display) {
            return { isColl: false, deltaX: deltaX }
        }
        let nLindId = Number(lineId);
        let newXPointArr = MathUtil.getRotaRectNew(display.node);
        let newPosX = newXPointArr.sort((pos1: cc.Vec2, pos2: cc.Vec2) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });

        const leftX = newPosX[0].x;
        const rightX = newPosX[3].x;
        const middleX = Number(display.node.x);
        const nVerCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, middleX + deltaX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL);
        const nLeftCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, leftX + deltaX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT);
        const nRightCheck: { nReturnIndex: number, mag: number } = this.preCheckLinePosAndCollide(nLindId, false, rightX + deltaX, LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT);


        let minMag = null;
        let nReturnIndex = -1;
        let posType = null;

        if (nVerCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nVerCheck.mag;
                nReturnIndex = nVerCheck.nReturnIndex;
                posType = LINT_POS_TYPE.VERTICAL;
            }
            else {
                if (nVerCheck.mag < minMag) {
                    minMag = nVerCheck.mag;
                    nReturnIndex = nVerCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.VERTICAL;
                }
            }
        }
        if (nLeftCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nLeftCheck.mag;
                nReturnIndex = nLeftCheck.nReturnIndex;
                posType = LINT_POS_TYPE.LEFT;
            }
            else {
                if (nLeftCheck.mag < minMag) {
                    minMag = nLeftCheck.mag;
                    nReturnIndex = nLeftCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.LEFT;
                }
            }
        }

        if (nRightCheck.nReturnIndex !== -1) {
            if (minMag == null) {
                minMag = nRightCheck.mag;
                nReturnIndex = nRightCheck.nReturnIndex;
                posType = LINT_POS_TYPE.RIGHT;
            }
            else {
                if (nRightCheck.mag < minMag) {
                    minMag = nRightCheck.mag;
                    nReturnIndex = nRightCheck.nReturnIndex;
                    posType = LINT_POS_TYPE.RIGHT;
                }
            }
        }
        let charX = deltaX;
        let offset = 0;
        let bVerCollide = false;
        if (nReturnIndex !== -1) {
            let nFindIndex = this.findLineData(nLindId, false, LINT_DISPLAY_TYPE.VERTICAL, posType);
            let findLineData = null;
            if (nFindIndex !== -1) {
                findLineData = this._arrHintLine[nFindIndex];
            }
            for (let i = 0; i < this._arrHintLine.length; i++) {
                if (i === nFindIndex || i === nReturnIndex) {
                    continue;
                }
                let lineData = this._arrHintLine[i];
                if (lineData.findId && lineData.typeDisplay === LINT_DISPLAY_TYPE.VERTICAL) {
                    lineData.findId = null;
                }
            }
            let lineData = this._arrHintLine[nReturnIndex];

            if (lineData && findLineData) {
                if (findLineData && findLineData.findId !== lineData.id + "_" + lineData.posType) {
                    findLineData.mousePos = pos;
                    findLineData.findId = lineData.id + "_" + lineData.posType;
                    this._callAdd(lineData, findLineData, posType);
                }
                let magMousePos = Math.abs(pos.x - findLineData.mousePos.x);
                if (magMousePos < this.nColliderChar) {
                    bVerCollide = true;
                    charX = lineData.pos - findLineData.pos;
                } else if (findLineData.findId == lineData.id + "_" + lineData.posType) {
                    findLineData.findId = null;
                    offset = pos.x - findLineData.mousePos.x;
                }
            }
        } else {
            for (let i = 0; i < this._arrHintLine.length; i++) {
                let lineData = this._arrHintLine[i];
                if (lineData.findId && lineData.typeDisplay === LINT_DISPLAY_TYPE.VERTICAL) {
                    lineData.findId = null;
                }
            }
        }




        return { isColl: bVerCollide, deltaX: charX + offset };
    }




    /**
     * 线段X轴吸附
     * @param lineId 
     * @param destX 
     */
    private adsorbLineX(lineId: string, destX: number): void {
        let display = this._displayManager.getDisplayObjectById(lineId);
        let oldX = display.node.x;
        let charX = destX - oldX;
        this._displayManager.updateDisplayObject(lineId, { "x": destX });
        let selectedIds = this._displayManager.selectedIds;
        if (selectedIds.length > 1) {
            for (let i = 0; i < selectedIds.length; i++) {
                let id = selectedIds[i];
                if (id === lineId) {
                    continue;
                }
                let displaySel = this._displayManager.getDisplayObjectById(id);
                let displayDestY = displaySel.node.x + charX;
                this._displayManager.updateDisplayObject(id, { "x": displayDestY });
            }
        }
    }

    /**
     * 线段Y轴吸附
     * @param lineId 
     * @param destX 
     */
    private adsorbLineY(lineId: string, destY: number): void {
        let display = this._displayManager.getDisplayObjectById(lineId);
        let oldY = display.node.y;
        let charY = destY - oldY;
        this._displayManager.updateDisplayObject(lineId, { "y": destY });
        let selectedIds = this._displayManager.selectedIds;
        if (selectedIds.length > 1) {
            for (let i = 0; i < selectedIds.length; i++) {
                let id = selectedIds[i];
                if (id === lineId) {
                    continue;
                }
                let displaySel = this._displayManager.getDisplayObjectById(id);
                let displayDestY = displaySel.node.y + charY;
                this._displayManager.updateDisplayObject(id, { "y": displayDestY });
            }
        }
    }

    /**
     * 更新线段属性
     * @param id 
     * @param isEditor 
     */
    updateLinePosAndCollide(id: number, isEditor: boolean, pos: number, typeDisplay: LINT_DISPLAY_TYPE, posType: LINT_POS_TYPE, check: boolean): number {
        if (posType === null) {
            posType = LINT_POS_TYPE.HORIZONTAL;
        }
        let nFindIndex = this.findLineData(id, isEditor, typeDisplay, posType);
        if (nFindIndex === -1) {
            // 没找到
            return -1;
        }
        let findLineData = this._arrHintLine[nFindIndex];
        findLineData.pos = pos;
        if (!check) {
            return;
        }
        let nReturnIndex = -1;
        // 碰撞检测
        for (let i = 0; i < this._arrHintLine.length; i++) {
            if (i === nFindIndex) {
                continue;
            }
            let lineData = this._arrHintLine[i];
            if (isEditor && lineData.type === HINT_LINE_TYPE.EDITOR) {
                continue;
            }
            if (findLineData.typeDisplay !== lineData.typeDisplay) {
                continue;
            }
            let bSelect = this._displayManager.selectedIds.find((value) => value === lineData.id.toString());
            if (bSelect) {
                continue;
            }
            if (findLineData.pos > (lineData.pos - this.nColliderChar) && findLineData.pos < (lineData.pos + this.nColliderChar)) {
                if (this._callAdd) {
                    this._callAdd(lineData, findLineData, posType);
                }
                // 吸过去?
                if (nReturnIndex === -1) {
                    nReturnIndex = i;
                }
            }
        }
        return nReturnIndex;
    }
}
