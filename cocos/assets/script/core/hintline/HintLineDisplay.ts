// 线段类型枚举
export enum HINT_LINE_TYPE {
    EDITOR = 0,
    DISPLAY
}

/**
 * 线段类型枚举
 */
export enum LINT_POS_TYPE {
    HORIZONTAL = 0,
    VERTICAL,
    LEFT,
    RIGHT,
    TOP,
    BOTTOM
}

/**
 * 线段显示类型枚举，横向还是纵向
 */
export enum LINT_DISPLAY_TYPE {
    HORIZONTAL = 0,
    VERTICAL,
}

/**
 * 线段数据类
 */
export class LINEDATA {
    private _pos: number = -1;
    public mousePos: any = null;
    public findId: any = null;
    get pos(): number {
        return this._pos;
    }
    set pos(value: number) {
        this._pos = value;
    }
    // 线段类型
    private _type: HINT_LINE_TYPE = HINT_LINE_TYPE.EDITOR;
    get type(): HINT_LINE_TYPE {
        return this._type;
    }
    set type(value: HINT_LINE_TYPE) {
        this._type = value;
    }

    // 线段ID
    private _id: number = -1;
    get id(): number {
        return this._id;
    }
    set id(value: number) {
        this._id = value;
    }

    // 位置ID 
    private _posType: LINT_POS_TYPE = LINT_POS_TYPE.HORIZONTAL;
    get posType(): LINT_POS_TYPE {
        return this._posType;
    }
    set posType(value: LINT_POS_TYPE) {
        this._posType = value;
    }

    // 线段显示类型
    private _typeDisplay: LINT_DISPLAY_TYPE = LINT_DISPLAY_TYPE.HORIZONTAL;
    get typeDisplay(): LINT_DISPLAY_TYPE {
        return this._typeDisplay;
    }
    set typeDisplay(value: LINT_DISPLAY_TYPE) {
        this._typeDisplay = value;
    }

    isEqaul(value: LINEDATA): boolean {
        if (this._id !== value.id) {
            return false;
        }
        if (this._pos !== value.pos) {
            return false;
        }
        if (this._posType !== value.posType) {
            return false;
        }
        if (this._type !== value.type) {
            return false;
        }
        if (this._typeDisplay !== value.typeDisplay) {
            return false;
        }
        return true;
    }
}