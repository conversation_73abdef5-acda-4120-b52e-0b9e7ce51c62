import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import StageController from "../../../../stage/StageController";

/**
 * 更新舞台背景属性命令
 */
export class UpdateBgCommand extends SimpleCommand {
  public execute(newProperties: any): void {
    this.oldBody = yy.single
      .instance(StageController)
      .updateBackground(newProperties);
    console.warn("bg--execute", newProperties, this.oldBody);
  }

  public undo(oldProperties: any): void {
    console.warn("bg--undo", oldProperties);
    yy.single.instance(StageController).updateBackground(oldProperties);
  }
}
