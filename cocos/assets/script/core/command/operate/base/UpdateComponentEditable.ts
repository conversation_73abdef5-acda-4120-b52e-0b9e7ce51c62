import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import { EditableProperties } from "../../../proxy/ComptData";
import DataCenterBridge from "../../../proxy/DataCenterBridge";

const { ccclass } = cc._decorator;

/**
 * 修改属性是否可编辑
 */
@ccclass
export default class UpdateComponentEditable extends SimpleCommand {
  public execute(body: { componentIds: []; newEditable: any }): void {
    let cmptIds = body.componentIds;
    let displayManager = yy.single.instance(DisplayObjectManager);
    this.oldBody = { componentIds: body.componentIds };
    for (let i = 0; i < cmptIds.length; i++) {
      let display = displayManager.getDisplayObjectById(cmptIds[i]);
      let tempEditable = new EditableProperties();
      if (typeof display.editable === "object") {
        tempEditable = display.editable;
      }
      for (let key in body.newEditable) {
        tempEditable.properties[key] = body.newEditable[key];
      }
      display.editable = tempEditable;
    }
  }

  public undo(body: { componentIds: [] }): void {
    let cmptIds = body.componentIds;
    let displayManager = yy.single.instance(DisplayObjectManager);

    for (let i = 0; i < cmptIds.length; i++) {
      let display = displayManager.getDisplayObjectById(cmptIds[i]);
      display.editable = yy.single
        .instance(DataCenterBridge)
        .getComponentEditable(cmptIds[i]);
    }
  }
}
