import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

export class UpdateComponentTagCommand extends SimpleCommand {
  public execute(body?: { id: string; label: string }): void {
    let manager = yy.single.instance(DisplayObjectManager);
    let display = manager.getDisplayObjectById(body.id);
    let oldStr = display.showDisplayTag(body.label);
    this.oldBody = { id: body.id, label: oldStr };
    console.log("showDisplayTag2");
  }
  public undo(body?: any): void {
    let manager = yy.single.instance(DisplayObjectManager);
    let display = manager.getDisplayObjectById(body.id);
    display.showDisplayTag(body.label);
    console.log("showDisplayTag3");
  }
}
