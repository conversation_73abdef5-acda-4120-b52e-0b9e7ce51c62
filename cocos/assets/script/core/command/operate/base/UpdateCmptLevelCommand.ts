/*
 * @FilePath     : /cocos/assets/script/core/command/operate/base/UpdateCmptLevelCommand.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { UpdateLevelType } from "../../../../common/EditorEnum";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";

/**
 * 更新组件层级命令
 */
export class UpdateCmptLevelCommand extends SimpleCommand {

    public execute (body?: {id: string, type: UpdateLevelType}): void {
        let id = body.id;
        let type = body.type;
        let dpm = yy.single.instance(DisplayObjectManager);
        let displayObject = dpm.getDisplayObjectById(id);
        // 保存老数据
        this.oldBody = {id, type, sIdx: displayObject.node.getSiblingIndex()};
        // 更新层级
        if (!displayObject) {
            yy.warn(`id = ${id}的组件不存在!`);
            return;
        }
        let _index = 0;
        let isSubNode = dpm.isSubNode(id);
        let oldIndex1 = dpm.getOriginSiblingIndexById(id);
        let contextIndexRange = this.getContextIndexRange(isSubNode || 'root');
        // 调整层级
        if (type === UpdateLevelType.TOP) {
            // 判断是否是组合的子节点，如果是子节点，则计算同组子节点的最高值
            _index = contextIndexRange.maxIndex;
                
        } else if (type === UpdateLevelType.BOTTOM) {
            _index = contextIndexRange.minIndex;
        } else if (type === UpdateLevelType.FORWARD) {
            _index = oldIndex1 + 1 > contextIndexRange.maxIndex ? contextIndexRange.maxIndex : oldIndex1 + 1;
        }  else if (type === UpdateLevelType.BACKWARD) {
            _index = Math.max(oldIndex1 - 1, 0);
        }
        console.log("%c Line:46 🌮 _index", "color:#f5ce50", _index);
        let affectedNodes = this.calculateAffectedNodes(id, type, _index);
        console.log("%c Line:47 🍪 affectedNodes", "color:#ffdd4d", affectedNodes);
        // 调整层级之后，更新最新的层级
        if (affectedNodes.updates.length > 0) {
            dpm.updateOriginSiblingIndexById(affectedNodes.updates, true);
        }
    }
    // 获取同上下文节点的索引范围
    getContextIndexRange(contextId) {
        const contextNodes = new Map();
        let dpm = yy.single.instance(DisplayObjectManager);
        for (const [id, index] of Array.from(dpm.dpIndexMap.entries())) {
            const nodeContextId = dpm.isSubNode(id) || 'root';
            if (nodeContextId === contextId) {
                contextNodes.set(id, index);
            }
        }

        const indexes = Array.from(contextNodes.values()).sort((a, b) => a - b);
        return {
            nodes: contextNodes,
            minIndex: indexes.length > 0 ? indexes[0] : 0,
            maxIndex: indexes.length > 0 ? indexes[indexes.length - 1] : 0,
            count: indexes.length
        };
    }
    // 计算受影响的其他节点
    calculateAffectedNodes(id, type, targetIndex: number) {
        let dpm = yy.single.instance(DisplayObjectManager);
        const currentIndex = dpm.dpIndexMap.get(id);
        
        if (currentIndex === targetIndex) {
          console.warn(`⚡ 位置未变化，无需更新`);
          return { updates: [], reason: "位置未变化" };
        }
        
        const contextId = dpm.isSubNode(id) || 'root';
        const contextInfo = this.getContextIndexRange(contextId);
        const updates = [];
        
        // 主节点的更新
        updates.push({
          id: id,
          newIndex: targetIndex,
          oldIndex: currentIndex
        });
        
        // 计算受影响的其他节点
        const isMovingUp = targetIndex > currentIndex; // 索引增大 = 向上移动
        
        for (const [otherId, otherIndex] of Array.from(contextInfo.nodes.entries())) {
          if (otherId === id) continue; // 跳过主节点
          
          let newIndex = otherIndex; // 默认不变
          
          if (isMovingUp) {
            // 向上移动：原位置+1到新位置之间的节点向下移动一位
            if (otherIndex > currentIndex && otherIndex <= targetIndex) {
              newIndex = otherIndex - 1;
            //   console.log(`  📉 ${otherId}: ${otherIndex} → ${newIndex} (向下让位)`);
            }
          } else {
            // 向下移动：新位置到原位置-1之间的节点向上移动一位  
            if (otherIndex >= targetIndex && otherIndex < currentIndex) {
              newIndex = otherIndex + 1;
            //   console.log(`  📈 ${otherId}: ${otherIndex} → ${newIndex} (向上让位)`);
            }
          }
          
          if (newIndex !== otherIndex) {
            updates.push({
              id: otherId,
              newIndex: newIndex,
              oldIndex: otherIndex
            });
          }
        }
        
        // console.log(`📋 总共影响 ${updates.length} 个节点`);
        return { updates, currentIndex, targetIndex };
      }



    public undo (body?: {id: string, type: UpdateLevelType, sIdx: number}): void {
        let dpm = yy.single.instance(DisplayObjectManager);
        let displayObject = dpm.getDisplayObjectById(body.id);
        displayObject.node.setSiblingIndex(body.sIdx);
    }

}