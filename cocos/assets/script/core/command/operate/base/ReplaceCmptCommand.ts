import ValueUtils from "../../../../../qte/core/utils/ValueUtils";
import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimTouchHandler from "../../../../cmpt/AnimTouchHandler";
import { CmptType } from "../../../../common/EditorEnum";
import DisplayEditBox from "../../../display/base/DisplayEditBox";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import DataCenterBridge from "../../../proxy/DataCenterBridge";

/**
 * 设置组件选中状态命令
 */
export class ReplaceCmptCommand extends SimpleCommand {
  /**
   * @param ids 选中的目标id
   * @param isVue 是否是vue主动调用
   * @param isMulti 是否是多选
   * @param option 是否阻止发送commit到vue
   */
  public execute(body: { ids: string[]; isVue: boolean; isMulti: boolean; option?: boolean }) {
    let dispManager = yy.single.instance(DisplayObjectManager);
    this.oldBody = dispManager.selectedIds;
    console.warn("ReplaceCmptCommand this.oldBody", JSON.stringify(this.oldBody));

    // vue组件管理中选中组件
    if (body.isVue) {
      this.setSelectIds(["-1"], body.isMulti, body.isVue);
    }
    if (body.ids.length <= 0) {
      // 设置选中的接口逻辑中，id为-1时，是取消所有选中
      this.setSelectIds(["-1"], body.isMulti, body.isVue);
    } else {
      this.setSelectIds(body.ids, body.isMulti, body.isVue);
    }
    if (!body.isVue && !body.option) {
      let temp = yy.cloneValues(dispManager.selectedIds);
      let result = this.getSubIdsExceptGroupId(temp);
      console.warn("ReplaceCmptCommand temp", temp);
      console.warn("ReplaceCmptCommand result", result);
      yy.single.instance(DataCenterBridge).replaceComponet(result);
    }
    console.warn("ReplaceCmptCommand", body);

    this.newBody.option = true;

    // 判断如果是 ids = ["-1"] ，判断数组是 ['-1'] ，则兜底计算下是否与组件管理中的层级有差异
    if (body.ids.length == 0 || (body.ids.length === 1 && body.ids[0] === "-1")) {
      yy.single.instance(DisplayObjectManager).compareAndRefreshDisplayObjectIndex();
    }
        
    // yy.log('----ReplaceCmptCommand---execute--->',this.oldBody)
  }

  public undo(ids?: string[]) {
    yy.log("----ReplaceCmptCommand---undo--->", ids);
    let dispManager = yy.single.instance(DisplayObjectManager);
    dispManager.setSelected("-1");
    this.setSelectIds(ids, true, false);
  }

  /**
   * 设置组件选中状态
   * @param {string[]} ids 组件id数组
   */
  private setSelectIds(ids: string[], isMulti: boolean, isVue: boolean): void {
    let dispManager = yy.single.instance(DisplayObjectManager);
    console.warn("setSelectIds=======>", ids, isMulti, isVue);
    for (let cid of ids) {
      dispManager.setSelected(cid, isMulti || ids.length > 1);
    }
    if (ids.length === 1 && isVue) {
      let groupID = dispManager.getDisplayGroupID(ids[0]);
      if (groupID !== "-1") {
        // 直接选中组内元素
        dispManager.selectId = groupID;
        dispManager.selectSubId = ids[0];
        dispManager.setSelected(groupID, false);
        dispManager.setSelected(ids[0], true);
        let _display = dispManager.getDisplayObjectById(ids[0]);
        if (_display && _display.type === CmptType.LABEL) {
          (_display as DisplayEditBox).setpikerviewStatue(2);
        }
      } else {
        dispManager.selectId = ids[0];
        dispManager.selectSubId = "-1";
        let _display = dispManager.getDisplayObjectById(ids[0]);
        if (_display && _display.type === CmptType.LABEL) {
          (_display as DisplayEditBox).setpikerviewStatue(2);
        }
      }
    }
    // 告诉动画选中组件
    yy.single.instance(AnimTouchHandler).selectedDisplayObject(dispManager.selectedIds);
  }

  /**
   * 获取组内子id时，过滤掉组id
   * @param arrIds
   */
  private getSubIdsExceptGroupId(arrIds: string[]): string[] {
    let dispManager = yy.single.instance(DisplayObjectManager);
    let arrGroup = [];
    let arrSubId = [];
    let bFindOther = false;
    for (let i = 0; i < arrIds.length; i++) {
      let id = arrIds[i];
      let display = dispManager.getDisplayObjectById(id);
      console.warn("getSubIdsExceptGroupId", display);
      if (display.type === CmptType.GROUP) {
        let subIds = (display as DisplayObjectGroup).groupIds;
        let midexArr = ValueUtils.isMixedBy2Arr(arrIds, subIds);
        if (midexArr.length > 0) {
          /*
           * 选中组&组内子节点.
           * 这个组的节点就不要传递了.
           */
          arrGroup.push(id);
          midexArr.forEach(_mixId => {
            arrSubId.push(_mixId);
          });
        } else {
          // 选中了组，没选中子节点
        }
      } else {
        // 如果这个ID 不是其他groupID的SubId,那么他是其他ID
        let hasOwne = dispManager.hasOwnedGroup(id, arrIds);
        if (!hasOwne) {
          bFindOther = true;
        }
      }
    }
    let tempIds = [];
    if (bFindOther) {
      // 剔除子节点ID
      for (let i = 0; i < arrIds.length; i++) {
        let _id = arrIds[i];
        let bFind = arrSubId.find(value => value === _id);
        if (!bFind) {
          tempIds.push(_id);
        }
      }
    } else {
      // 剔除组ID
      for (let i = 0; i < arrIds.length; i++) {
        let _id = arrIds[i];
        let bFind = arrGroup.find(value => value === _id);
        if (!bFind) {
          tempIds.push(_id);
        }
      }
    }
    return tempIds;
  }
}
