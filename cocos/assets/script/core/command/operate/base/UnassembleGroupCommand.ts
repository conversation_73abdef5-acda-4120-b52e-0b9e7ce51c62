import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import HintLineManager from "../../../hintline/HintLineManager";
import DataCenterBridge from "../../../proxy/DataCenterBridge";

/**
 * 将对象组装成组的命令
 */
export class UnassembleGroupCommand extends SimpleCommand {

    public execute (id: string): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let removeDisplayGroup = dpMgr.getDisplayObjectById(id);
        let groups = (removeDisplayGroup as DisplayObjectGroup).groudSubs;
        let groupWidth = removeDisplayGroup.node.width;
        let groupHeight = removeDisplayGroup.node.height;
        let groupAngle = removeDisplayGroup.getNodeAngle();
        let tempGroupPropertes = [];
        let tempID = [];
        for (let i = 0; i < groups.length; i++) {
            let sub = groups[i];
            tempGroupPropertes.push({subId: sub.cid, x: sub.node.x, y: sub.node.y, angle: sub.getNodeAngle()});
            tempID.push(sub.cid);
        }
        let subList = yy.single.instance(DisplayObjectManager).removeGroupDisplayObject(id);
        this.oldBody = {
            id, 
            ids: subList, 
            pos: removeDisplayGroup.node.position, 
            groupWidth, 
            groupHeight,
            groupAngle,
            tempGroupPropertes
        };     
        
        for (let i = 0; i < tempID.length; i++) {
            let _id = tempID[i]
            let displayObj = yy.single.instance(DisplayObjectManager).getDisplayObjectById(_id);
            if (displayObj) {
                // 准备传给command的数据 暂时先这样
                let newData = displayObj.getNewProperties();
                yy.single.instance(DataCenterBridge).updateComponetProperties(_id, newData.newProperties)
            }
        }

        yy.log("----UnassembleGroupCommand---execute--->", this.oldBody);
    }

    public undo (body?: {id: string, ids: string[], pos: cc.Vec3, 
        groupWidth: number,
        groupHeight: number,
        groupAngle: number,
        tempGroupPropertes: {subId: string, x: number, y: number, angle: number}[]}): void {
        
        yy.log("----UnassembleGroupCommand---undo--->", body);
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let groupCmpt = dpMgr.getDisplayObjectById(body.id) as DisplayObjectGroup;
        let groupNode = groupCmpt.node;
        groupNode.position = body.pos;
        groupNode.angle = body.groupAngle;
        groupNode['cAngle'] = body.groupAngle;
        groupNode.width = body.groupWidth;
        groupNode.height = body.groupHeight;
        if (groupCmpt) {
            for (let subId of body.ids) {
                let subNode = dpMgr.getDisplayObjectById(subId);
                subNode.node.parent = groupNode;
                // 删除线段
                yy.single.instance((HintLineManager)).removeHintLineByDisplay(subId);
                groupCmpt.addSubObject(subId, subNode);
                let findData = body.tempGroupPropertes.find((value) => value.subId === subId);
                if (findData) {
                    subNode.node.x = findData.x;
                    subNode.node.y = findData.y;
                    subNode.node.angle = findData.angle;
                    subNode.node['cAngle'] = findData.angle;
                }
            }
        }
        this.oldBody = body.id;    
    }
}