import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

const { ccclass } = cc._decorator;
/** 修改组件是否可删除 */
@ccclass
export default class UpdateComponentDeletable extends SimpleCommand {
  public execute(body: { cid: string; deletable: boolean }): void {
    let display = yy.single
      .instance(DisplayObjectManager)
      .getDisplayObjectById(body.cid);
    this.oldBody = { cid: body.cid, deletable: display.deletable };
    display.deletable = body.deletable;
  }

  public undo(body: { cid: string; deletable: boolean }): void {
    let display = yy.single
      .instance(DisplayObjectManager)
      .getDisplayObjectById(body.cid);
    display.deletable = body.deletable;
  }
}
