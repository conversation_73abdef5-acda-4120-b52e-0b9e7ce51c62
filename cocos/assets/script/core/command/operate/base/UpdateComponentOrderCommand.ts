/*
 * @FilePath     : /cocos/assets/script/core/command/operate/base/UpdateComponentOrderCommand.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { UpdateLevelType } from "../../../../common/EditorEnum";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";
import ValueUtils from "../../../../../qte/core/utils/ValueUtils";

/**
 * 更新组件顺序命令
 */
export class UpdateComponentOrderCommand extends SimpleCommand {

    public execute(body?: { id: string, newIndex: number, oldIndex: number }[]): void {
        const dpm = yy.single.instance(DisplayObjectManager);
        this.oldBody = ValueUtils.clone(body);
        console.log("Array.from(dpm.dpIndexMap.entries())==",Array.from(dpm.dpIndexMap.entries()));

        // 判断是子节点还是父节点， 如果是子节点，所有的 newIndex, oldIndex 都加 2
        // 根据传入节点，判断是子节点还是父节点
        let isSubNode = dpm.isSubNode(body[0].id) || 'root';

        // if(isSubNode != 'root'){
        //     body.forEach(item => {
        //         item.newIndex += 2;
        //         item.oldIndex += 2;
        //     });
        // }
        console.log("body,,",JSON.stringify(body));
        
        // 计算 body 是向上移动还是向下移动；
        // 移动方向
        let moveDirection = body[0].newIndex - body[0].oldIndex;
        
        let maxNewIndex = body.reduce((max, item) => Math.max(max, moveDirection >= 0 ? item.newIndex : item.oldIndex), 0);
        console.log("%c Line:39 🥒 maxNewIndex", "color:#4fff4B", maxNewIndex);
        let minNewIndex = body.reduce((min, item) => Math.max(min, moveDirection >= 0 ? item.oldIndex : item.newIndex), 0);
        console.log("%c Line:41 🍣 minNewIndex", "color:#3f7cff", minNewIndex);
        let length = moveDirection >= 0 ? maxNewIndex - minNewIndex : minNewIndex - maxNewIndex;

        if(isSubNode != 'root'){
            maxNewIndex = Math.max(maxNewIndex + 2, 0);
            minNewIndex = Math.max(minNewIndex + 2, 0);
        }
        
        let sameContextNodes = new Map<string, number>();
        for (const [id, index] of Array.from(dpm.dpIndexMap.entries())) {
            const nodeContextId = dpm.isSubNode(id) || 'root';
            if (nodeContextId === isSubNode) {
                sameContextNodes.set(id, index);
            }
        }
        

        // 把 _dpIndexMap 转成数组
        let dpIndexMapArray = Array.from(sameContextNodes.entries());
        // 按照节点中的 index 排序
        dpIndexMapArray.sort((a, b) => a[1] - b[1]);
        
        // 修复：采用收集-处理-应用三阶段模式，避免遍历时修改数组导致的解构null错误
        // 阶段1：收集需要移除的元素索引位置
        const indicesToRemove: number[] = [];
        const bodyIds = new Set(body.map(item => item.id)); // 使用Set优化查找性能
        
        for (let i = 0; i < dpIndexMapArray.length; i++) {
            const element = dpIndexMapArray[i];
            // 安全检查：确保元素存在且格式正确
            if (element && Array.isArray(element) && element.length >= 2) {
                const [id] = element;
                if (id && bodyIds.has(id)) {
                    indicesToRemove.push(i);
                }
            }
        }
        
        // 阶段2：批量处理，将收集到的索引位置设置为null
        for (const index of indicesToRemove) {
            dpIndexMapArray[index] = null;
        }
        console.log("第一次删除 body ,",JSON.stringify(dpIndexMapArray));

        // 受影响的节点数据
        let affectedNodes = [];

        // 计算 minNewIndex 到 maxNewIndex 之间的元素向下移动 length 个位置
        // 收集受影响的节点，然后批量从 dpIndexMapArray 中移除
        const affectedNodeIds = new Set<string>();
        
        for (const [id, index] of Array.from(sameContextNodes.entries())) {
            if (index >= minNewIndex && index <= maxNewIndex) {
                // 不能包含 body 中的 id
                if (!body.some(item => item.id === id)) {
                    affectedNodes.push({
                        id,
                        oldIndex: index
                    });
                    affectedNodeIds.add(id);
                }
            }
        }
        console.log("%c Line:96 🍢 affectedNodes", "color:#f5ce50", affectedNodes);
        // 批量从 dpIndexMapArray 中移除受影响的节点
        for (let i = 0; i < dpIndexMapArray.length; i++) {
            const element = dpIndexMapArray[i];
            if (element && Array.isArray(element) && element.length >= 2) {
                const [id] = element;
                if (id && affectedNodeIds.has(id)) {
                    dpIndexMapArray[i] = null;
                }
            }
        }
        console.log('第二次 删除',JSON.stringify(dpIndexMapArray));
        // 把要移动的按新的位置填入 dpIndexMapArray
        for (const item of body) {
            dpIndexMapArray[item.newIndex] = [item.id, item.newIndex];
        }
        console.log('第一次填入  ',JSON.stringify(dpIndexMapArray));
        // affectedNodes 按 oldIndex 排序
        affectedNodes.sort((a, b) => a.oldIndex - b.oldIndex);
        // 循环查找 dpIndexMapArray 中为 null 的元素 ， 把 affectedNodes 中的 id 填入 dpIndexMapArray
        for (const item of affectedNodes) {
            // 循环 dpIndexMapArray ， 如果 index 为 null ， 把 item.id 填入 dpIndexMapArray
            for (let j = 0; j < dpIndexMapArray.length; j++) {
                if (dpIndexMapArray[j] === null) {
                    dpIndexMapArray[j] = [item.id, item.oldIndex];
                    if(isSubNode != 'root'){
                        item.newIndex = j + 2;
                    }else{
                        item.newIndex = j;
                    }
                    break;
                }
            }
        }
        console.log('第二次填入  ',JSON.stringify(dpIndexMapArray));

        
        // affectedNodes  按照 oldIndex 排序
        affectedNodes.sort((a, b) => a.oldIndex - b.oldIndex);
        // 合并 2 个数组 affectedNodes 和 body 
        // 处理 body 
        if(isSubNode != 'root'){
            body.forEach(item => {
                item.newIndex += 2;
                item.oldIndex += 2;
            });
        }
        let allNodes = [...affectedNodes, ...body];
        // 输出
        console.log("affectedNodes", allNodes);
        dpm.updateOriginSiblingIndexById(allNodes, true);
    }

    public undo(): void {
        const dpm = yy.single.instance(DisplayObjectManager);
        const undoUpdates = this.oldBody.map(item => ({
            id: item.id,
            newIndex: item.oldIndex,
            oldIndex: item.newIndex
        }));
        dpm.updateOriginSiblingIndexById(undoUpdates, true);
    }

}