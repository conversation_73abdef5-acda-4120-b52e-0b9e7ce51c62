import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { CmptLayer, CmptType } from "../../../../common/EditorEnum";
import DisplayObject from "../../../display/base/DisplayObject";
import DisplayObjectFactory from "../../../display/DisplayObjectFactory";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import TemplateInterpreter from "../../../display/TemplateInterpreter";
import ComptData, { EditableProperties } from "../../../proxy/ComptData";

/**
 * 添加组件命令
 */
export class AddCmptCommand extends SimpleCommand {
  public execute(body: { payload: any }) {
    let id = body.payload.id;
    let type = body.payload.type;
    let properties = body.payload.properties;
    let spineData = body.payload.spineData;
    let cocosAniData = body.payload.cocosAniData;
    let data = new ComptData();
    data.id = id;
    data.type = type;
    data.properties = properties;
    data.spineData = spineData;
    data.cocosAniData = cocosAniData;
    data.dragable = body.payload.dragable;
    data.deletable = body.payload.deletable;
    if (typeof body.payload.editable === "object" && body.payload.editable.properties) {
      data.editable= new EditableProperties();
      data.editable.properties = body.payload.editable.properties;
    } else {
      data.editable = body.payload.editable;
    }
    if (typeof body.payload.subType !== "undefined") {
      data.subType = body.payload.subType;
    }

    this.createNode(data, id);
    this.oldBody = id;
    yy.log("----AddCmptCommand---execute--->", this.oldBody);
  }

  private async createNode(data, id) {
    let factory = yy.single.instance(DisplayObjectFactory);
    let manager = yy.single.instance(DisplayObjectManager);
    let node = null;
    // if (data.type === CmptType.SPECIALCOMPONENT && data.subType != "speaker") {
    //   console.log("#====createNode==1=>", data)
    //   node = await factory.getDisplayNode(data, true);
    // } else {
    //   console.log("#====createNode==2=>", data)
      node = factory.getDisplayNodeAsync(data, true);
    // }
    let temp = yy.single.instance(TemplateInterpreter);
    temp.addObjFunc(node, CmptLayer.OBJECT_LAYER);
    let groupCmpt = node.getComponent(DisplayObject);
    manager.addDisplayObject(id, groupCmpt);
  }

  public undo(id: string): void {
    yy.log("----AddCmptCommand---undo--->", id);
    let dspManager = yy.single.instance(DisplayObjectManager);
    /*
     * 如果撤销删除的是组，将组解散
     * if (display.type === CmptType.GROUP) {
     *     dspManager.removeGroupDisplayObject(id);
     * }
     */
    dspManager.removeDisplayObject(id);
  }
}
