import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 批量设置组件是否可以拖动
 */
export class UpdateDragableCommand extends SimpleCommand {
    public execute (body: {cmptIds: string[], dragable: boolean}): void {
        let ids = body.cmptIds;
        let dragable = body.dragable;
        this.oldBody = {cmptIds: ids, dragable: !dragable};

        this.updateProperty(ids, dragable);
    }

    public undo (body: {cmptIds: string[], dragable: boolean}): void {
        this.updateProperty(body.cmptIds, body.dragable);
    }

    private updateProperty (ids: string[], dragable: boolean) {
        let displayManager = yy.single.instance(DisplayObjectManager);

        for (let id of ids) {
            // eslint-disable-next-line object-shorthand
            displayManager.updateDisplayObject(id, {"dragable": dragable});
        }
    }
}
