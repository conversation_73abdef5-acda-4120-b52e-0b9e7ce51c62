/*
 * @FilePath     : /cocos/assets/script/core/command/operate/base/AssembleGroupCommand.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import CommandManager from "../../../../../qte/core/extension/command/CommandManager";
import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import HintLineManager from "../../../hintline/HintLineManager";
import UpdateProp2VueCmd from "../../simple/UpdateProp2VueCmd";

/**
 * 将对象组装成组的命令
 */
export class AssembleGroupCommand extends SimpleCommand {

    public execute(body?: { id: string, ids: string[] }, isNew?: boolean): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let groupCmpt = dpMgr.getDisplayObjectById(body.id) as DisplayObjectGroup;
        let groupNode = groupCmpt.node;
        let groupWidth = groupCmpt.node.width;
        let groupHeight = groupCmpt.node.height;
        let groupAngle = groupCmpt.getNodeAngle();
        let tempGroupPropertes = [];
        if (groupCmpt) {
            for (let subId of body.ids) {
                let subNode = dpMgr.getDisplayObjectById(subId);
                tempGroupPropertes.push({
                    subId: subNode.cid,
                    x: subNode.node.x,
                    y: subNode.node.y,
                    angle: subNode.getNodeAngle(),
                    width: subNode.node.width,
                    height: subNode.node.height
                });
                let pos = subNode.node.position.subtract(groupNode.position);
                subNode.node.parent = groupNode;
                subNode.node.position = pos;
                let _index = subNode.node.getSiblingIndex();
                // 子节点的 index 是根据现有的所有层级上开始算起的；
                yy.single.instance(DisplayObjectManager).updateDisplayObjectIndex(subId, _index);
                yy.single.instance(HintLineManager).removeHintLineByDisplay(subId);
                groupCmpt.addSubObject(subId, subNode);
                // groupCmpt.showAllNodes(4);
            }
        }
        // console.log("groupNode==inde1111=", JSON.stringify(Array.from(yy.single.instance(DisplayObjectManager).dpIndexMap.entries())));
        // groupCmpt.showAllNodes(3);
        // 组合后，所有层级按照实际的重新排序。
        let _mgr = yy.single.instance(DisplayObjectManager);
        groupCmpt.scheduleOnce(()=>{
            _mgr.dpIndexMap.forEach((value, key) => {
                // 父结点重新设置 index
                if (_mgr.isSubNode(key)) {
                    return;
                }
                // console.log("dp index", key, value);
                _mgr.updateDisplayObjectIndex(key, _mgr.getDisplayObjectById(key).node.getSiblingIndex());
                // groupCmpt.showAllNodes(2);
                // console.log("groupNode==index222=", JSON.stringify(Array.from(yy.single.instance(DisplayObjectManager).dpIndexMap.entries())));
            });
            _mgr.sortDisplayObjectByIndex();
        }, 0);
        
        

        this.oldBody = body.id;
        this.oldBody = {
            id: body.id,
            pos: cc.v2(groupCmpt.node.x, groupCmpt.node.y),
            groupWidth,
            groupHeight,
            groupAngle,
            tempGroupPropertes
        };
        groupCmpt.resetLayoutSize();

        let tempID = [];
        tempID.push(body.id);
        yy.single.instance((CommandManager)).executeCommand(UpdateProp2VueCmd, { selectIds: tempID, isUpdateGroupData: true, notUpdate2Vue: !isNew, notRunCmd: true });
        yy.log("----AssembleGroupCommand---execute--->", body);
        yy.single.instance(HintLineManager).updateLineByDisplayScale(body.id);
    }

    public undo(body?: {
        id: string, pos: cc.Vec2,
        groupWidth: number,
        groupHeight: number,
        groupAngle: number,
        tempGroupPropertes: { subId: string, x: number, y: number, angle: number, width: number, height: number }[]
    }): void {
        yy.log("----AssembleGroupCommand---undo--->", body);
        yy.single.instance(DisplayObjectManager).removeGroupDisplayObject(body.id);
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let groupCmpt = dpMgr.getDisplayObjectById(body.id) as DisplayObjectGroup;
        groupCmpt.node.x = body.pos.x;
        groupCmpt.node.y = body.pos.y;
        groupCmpt.node.width = body.groupWidth;
        groupCmpt.node.height = body.groupHeight;
        groupCmpt.node.angle = body.groupAngle;
        groupCmpt.node['cAngle'] = body.groupAngle;
        for (let i = 0; i < body.tempGroupPropertes.length; i++) {
            let _data = body.tempGroupPropertes[i];
            let _subNode = dpMgr.getDisplayObjectById(_data.subId);
            if (_subNode) {
                _subNode.node.x = _data.x;
                _subNode.node.y = _data.y;
                _subNode.node.angle = _data.angle;
                _subNode.node['cAngle'] = _data.angle;
                _subNode.node.width = _data.width;
                _subNode.node.height = _data.height;
            }
        }
    }

}