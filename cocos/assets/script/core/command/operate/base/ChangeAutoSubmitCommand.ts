import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import StageController from "../../../../stage/StageController";

/**
 * 添加组件命令
 */
export class ChangeAutoSubmitCommand extends SimpleCommand {

  public execute(body: { submitType: any }) {

    if (typeof (body.submitType.hasRecover) != "undefined") {
      const hasRecover = body.submitType.hasRecover;
      this.oldBody = { submitType: { hasRecover: !hasRecover } };
      yy.instance(StageController).changeAutoRest(hasRecover);
      return;
    }

    const isAutoSubmit = body.submitType.isAutoSubmit;
    this.oldBody = { submitType: { isAutoSubmit: !isAutoSubmit } };
    // oldIsAutoReset
    yy.instance(StageController).changeAutoSubmit(isAutoSubmit);

  }

  public undo(body: { submitType: any }): void {
    if (typeof (body.submitType.hasRecover) != "undefined") {
      const hasRecover = body.submitType.hasRecover;
      yy.instance(StageController).changeAutoRest(hasRecover);
      return;
    }

    const isAutoSubmit = body.submitType.isAutoSubmit;
    yy.instance(StageController).changeAutoSubmit(isAutoSubmit);

  }
}