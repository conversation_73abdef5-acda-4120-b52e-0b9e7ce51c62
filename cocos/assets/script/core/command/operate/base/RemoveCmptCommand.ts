import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { CmptLayer, CmptType } from "../../../../common/EditorEnum";
import DisplayObject from "../../../display/base/DisplayObject";
import DisplayObjectGroup from "../../../display/base/DisplayObjectGroup";
import DisplayObjectFactory from "../../../display/DisplayObjectFactory";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import TemplateInterpreter from "../../../display/TemplateInterpreter";
import HintLineManager from "../../../hintline/HintLineManager";
import ComptData, { CocosAniData, SpineData } from "../../../proxy/ComptData";

/**
 * 移除组件命令
 */
export class RemoveCmptCommand extends SimpleCommand {
  public execute(id: string): void {
    let dpMgr = yy.single.instance(DisplayObjectManager);
    let display = dpMgr.getDisplayObjectById(id);

    // 缓存老数据用户回退操作
    let type = yy.single.instance(DisplayObjectFactory).getCmptTypeStr(display.type);
    let properties = display.getNewProperties();
    // 如果在vue组建管理点击删除组  删除子节点 并且加入oldbody
    let subIds = [];
    let groupid = dpMgr.getDisplayGroupID(id);

    // 组内子节点属性
    let subDisplayPropertes = [];
    // 组的属性
    let groupPropertes = null;
    if (groupid !== "-1") {
      let displayGroup = dpMgr.getDisplayObjectById(groupid) as DisplayObjectGroup;
      let tempGroupPropertes = displayGroup.getNewProperties();
      // 组内子displayObject
      let subPropertes = tempGroupPropertes.extra.subProperties;
      groupPropertes = tempGroupPropertes.newProperties;
      for (let i = 0; i < subPropertes.length; i++) {
        let sub = subPropertes[i];
        subDisplayPropertes.push({
          subId: sub.id,
          x: sub.newProperties.x,
          y: sub.newProperties.y,
          angle: sub.newProperties.angle,
        });
      }
      // 删除组内节点 重置选中
      dpMgr.selectSubId = "-1";
    }

    let subComponent = [];
    if (type === CmptType.GROUP) {
      let displayGroup = display as DisplayObjectGroup;
      subIds = displayGroup.groupIds;
      for (let key of subIds) {
        let subDisplay = dpMgr.getDisplayObjectById(key);
        // 如果子节点没有被删除
        if (subDisplay) {
          subComponent.push(subDisplay.getNewProperties());
          dpMgr.removeDisplayObject(key);
        }
      }
    }
    // 如果删除的组内id
    if (groupid !== "-1") {
      let displayGroup = dpMgr.getDisplayObjectById(groupid) as DisplayObjectGroup;
      displayGroup.removeSubObject(id);
      displayGroup.resetLayoutSize();
    }

    let parentId = dpMgr.getDisplayParentID(id);
    if (parentId !== '-1') {
      let displayParent = dpMgr.getDisplayObjectById(parentId);
      displayParent.removeChildObject(id);
    }
    let childComponent = [];
    let childIds = display.childIds;
    for (let key of childIds) {
      let subDisplay = dpMgr.getDisplayObjectById(key);
      // 如果子节点没有被删除
      if (subDisplay) {
        childComponent.push(subDisplay.getNewProperties());
        display.removeChildObject(key);
        dpMgr.removeDisplayObject(key);
      }
    }

    this.oldBody = {
      id,
      type,
      properties,
      subComponent,
      groupid,
      subDisplayPropertes,
      groupPropertes,
      parentId,
      childComponent,
    };

    // 删除该组件
    dpMgr.removeDisplayObject(id);
    yy.log("----RemoveCmptCommand---execute--->", this.oldBody);
  }

  public undo(body: {
    id: string;
    type: CmptType;
    properties: any;
    subComponent: any[];
    chs
    groupid: string;
    subDisplayPropertes: {
      subId: string;
      x: number;
      y: number;
      angle: number;
    }[];
    groupPropertes: any;
    parentId,
    childComponent
  }): void {
    yy.log("----RemoveCmptCommand---undo--->", body);
    let cmptProp = this.getCmptProp(body.properties);
    this.addDisplay(body.id, body.type, cmptProp, body.properties.extra.spineData, body.properties.extra.cocosAniData, body.properties.extra.dragable, body.properties.extra.subType);
    let manager = yy.single.instance(DisplayObjectManager);
    let display = manager.getDisplayObjectById(body.id);
    // 添加组内节点
    for (let subProp of body.subComponent) {
      let comptData = this.getCmptProp(subProp);
      let type = subProp.extra.type;
      this.addDisplay(subProp.id, type, comptData, subProp.extra.spineData, body.properties.extra.cocosAniData, subProp.extra.dragable, subProp.extra.subType);
    }

    // 合并组
    if (body.type === CmptType.GROUP) {
      this.assenmleGroup({
        id: body.id,
        subComponent: body.properties.extra.groupIds,
      });
    }

    // 撤销的组内子节点
    if (body.groupid !== "-1") {
      let displayGroup = manager.getDisplayObjectById(body.groupid) as DisplayObjectGroup;
      display.node.parent = displayGroup.node;
      displayGroup.addSubObject(body.id, display);
      displayGroup.resetLayoutSize(true);
      let groudSubs = displayGroup.groudSubs;
      for (let i = 0; i < groudSubs.length; i++) {
        let subDisplay = groudSubs[i];
        let data = body.subDisplayPropertes.find(value => value.subId === subDisplay.cid);
        if (data) {
          subDisplay.node.x = data.x;
          subDisplay.node.y = data.y;
          subDisplay.node.angle = data.angle;
          subDisplay.node['cAngle'] = data.angle;
        }
      }
      for (let key in body.groupPropertes) {
        displayGroup.node[key] = body.groupPropertes[key];
        if (key == "angle") {
          displayGroup.node['cAngle'] = body.groupPropertes[key];
        }
      }
      displayGroup.refreshRectPoint();
    }

    if (body.parentId !== "-1") {
      let manager = yy.single.instance(DisplayObjectManager);
      let displayParent = manager.getDisplayObjectById(body.parentId);
      displayParent.addChildObject(body.id, display);
      display.node.parent = displayParent.node;
    }

    for (let childProp of body.childComponent) {
      let comptData = this.getCmptProp(childProp);
      let type = childProp.extra.type;
      this.addDisplay(childProp.id, type, comptData, childProp.extra.spineData, childProp.extra.cocosAniData, childProp.extra.dragable, childProp.extra.subType);
      let displayChild = manager.getDisplayObjectById(childProp.id);
      display.addChildObject(childProp.id, displayChild);
      displayChild.node.parent = display.node;
    }
  }

  private async addDisplay(id: string, type: CmptType, properties: any, spineData: SpineData, cocosAniData: CocosAniData, dragable: boolean, subType: any) {
    let factory = yy.single.instance(DisplayObjectFactory);
    let manager = yy.single.instance(DisplayObjectManager);
    let data = new ComptData();
    // 如果已经加过了就不加了
    if (manager.getDisplayObjectById(id)) {
      return;
    }
    data.id = id;
    data.type = type;
    data.properties = properties;
    data.spineData = spineData;
    data.cocosAniData = cocosAniData;
    data.dragable = dragable;
    data.subType = subType;
    let node = null;
    if (type === CmptType.SPECIALCOMPONENT) {
      node = await factory.getDisplayNode(data, true);
    } else {
      node = factory.getDisplayNodeAsync(data, true);
    }
    let temp = yy.single.instance(TemplateInterpreter);
    temp.addObjFunc(node, CmptLayer.OBJECT_LAYER);
    let groupCmpt = node.getComponent(DisplayObject);
    // 设置层级
    node.setSiblingIndex(properties.zIndex);
    manager.addDisplayObject(id, groupCmpt);
  }

  // 合并组
  private assenmleGroup(body: { id: string; subComponent: string[] }) {
    let dpMgr = yy.single.instance(DisplayObjectManager);
    let groupCmpt = dpMgr.getDisplayObjectById(body.id) as DisplayObjectGroup;
    let groupNode = groupCmpt.node;
    if (groupCmpt) {
      // 再创建group的子节点
      for (let subId of body.subComponent) {
        let subNode = dpMgr.getDisplayObjectById(subId);
        subNode.node.parent = groupNode;
        groupCmpt.addSubObject(subId, subNode);
        yy.single.instance(HintLineManager).removeHintLineByDisplay(subId);
      }
    }
  }

  private getCmptProp(properties: any): any {
    let result = {};
    if (properties.newProperties) {
      for (let key in properties.newProperties) {
        result[key] = properties.newProperties[key];
      }
    }

    if (properties.extra) {
      for (let key in properties.extra) {
        result[key] = properties.extra[key];
      }
    }
    return result;
  }
}
