import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { EditMode } from "../../../../common/EditorEnum";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 模式切换命令
 */
export class UpdateModeCommand extends SimpleCommand {

    public execute (modelId: EditMode): void {
        let control = yy.single.instance(AnimDisplayControl);
        this.oldBody = control.editMode;
        control.editMode = modelId;
        this.updateEditMode(modelId);
    }

    public undo (modelId: EditMode): void {
        this.updateEditMode(modelId);
    }

    private updateEditMode (modelId: EditMode) {
        let control = yy.single.instance(AnimDisplayControl);
        control.editMode = modelId;

        if (!control.idAnimationEdit) { // 普通模式
            // 清除动画编辑
            yy.single.instance(AnimDisplayControl).resetMode();
        } else { // 动画模式
            let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
            control.updateFragmentsMap();
            control.showAllPah(selectId);
        }
    }
}
