import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { CmptLayer } from "../../../../common/EditorEnum";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
import TemplateInterpreter from "../../../display/TemplateInterpreter";

/**
 * 添加组件命令
 */
export class AddChildComponent extends SimpleCommand {
  public execute(body: { payload: any }) {
    //   {
    //     "id": "1",
    //     "childId": "2"
    // }
    console.log("###-AddChildComponent-body--->", body)
    let dspManager = yy.single.instance(DisplayObjectManager);
    let parentDisplay = dspManager.getDisplayObjectById(body.payload.id)
    let childParent = dspManager.getDisplayObjectById(body.payload.childId)
    let oldParent = childParent.node.parent;
    console.log("###-AddChildComponent-oldParent--->", oldParent)
    body.payload.oldParent = oldParent;
    childParent.node.parent = parentDisplay.node;
    parentDisplay.addChildObject(body.payload.childId, childParent);
    this.oldBody = body;

  }

  public undo(body: { payload: any }): void {
    console.log("###--AddChildComponent---undo-body--->", body)
    let dspManager = yy.single.instance(DisplayObjectManager);
    // dspManager.removeDisplayObject(body.payload.childId);
    let childParent = dspManager.getDisplayObjectById(body.payload.childId)
    childParent.node.parent = body.payload.oldParent;
    let parentDisplay = dspManager.getDisplayObjectById(body.payload.id);
    parentDisplay.removeChildObject(body.payload.childId);
    // let temp = yy.single.instance(TemplateInterpreter);
    // temp.addObjFunc(childParent.node, CmptLayer.OBJECT_LAYER);
  }
}
