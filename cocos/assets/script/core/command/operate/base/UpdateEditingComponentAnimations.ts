import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 组件动画编辑模式修改
 */
export class UpdateEditingComponentAnimations extends SimpleCommand {
    public execute (editType: boolean): void {
        let animCtrol = yy.single.instance(AnimDisplayControl);
        this.oldBody = animCtrol.isEditingComponentAnimations;
        animCtrol.isEditingComponentAnimations = editType;
        this.updateEditMode(editType);
    }
    public undo (editType: boolean): void {
        let animCtrol = yy.single.instance(AnimDisplayControl);
        animCtrol.isEditingComponentAnimations = editType;
        this.updateEditMode(editType);
    }

    private updateEditMode (editType: boolean) {
        if (!editType) { // 普通模式
            // 清除动画编辑
            yy.single.instance(AnimDisplayControl).resetMode();
        } else { // 组件动画模式
            let control = yy.single.instance(AnimDisplayControl);
            let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
            control.updateFragmentsMap();
            control.showAllPah(selectId);
        }
    }
}
