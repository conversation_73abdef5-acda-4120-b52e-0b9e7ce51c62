import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import { CmptType } from "../../../../common/EditorEnum";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 更新属性命令
 */
export class UpdatePropertiesCommand extends SimpleCommand {
  /**
   * @param body.id 修改的组件id
   * @param body.newProperties 修改的属性
   */
  public execute(body: { id: string; newProperties: any }): void {
    let oldProperties = yy.single
      .instance(DisplayObjectManager)
      .updateDisplayObject(body.id, body.newProperties);
    this.oldBody.id = body.id;
    this.oldBody.newProperties = oldProperties;
    yy.single.instance(AnimDisplayControl).updateActionOption(body.id);
    console.log(body);
    yy.log("----UpdatePropertiesCommand---execute--->", this.oldBody);
    // 更新动画逻辑
    let delta = {
      x: this.newBody.newProperties.x - this.oldBody.newProperties.x,
      y: this.newBody.newProperties.y - this.oldBody.newProperties.y,
    };
    yy.single.instance(AnimDisplayControl).updateDispObject(body.id, delta);
  }

  public undo(body?: { id: string; newProperties: any }, option?: any): void {
    yy.log("----UpdatePropertiesCommand---undo--->", body, option);
    this.updateUndo(body)
  }
  public updateUndo(body, time?) {
    if (!body) {
      return;
    }
    let manager = yy.single.instance(DisplayObjectManager);
    let display = manager.getDisplayObjectById(body.id);
    if (!display || !display.isInitFinished) {
      yy.log("display", body.id, "组件不存在还没出初始化成功，稍后更新");
      let culBody = null;
      if (body && typeof (time) == "undefined") {
        culBody = JSON.parse(JSON.stringify(body));
        time = 0;
      } else {
        culBody = body;
        time++;
      }
      if (time <= 10) {
        setTimeout(() => {
          this.updateUndo(culBody, time);
        }, 100);
        return;
      }
      yy.log("display", body.id, "组件不存在还没出初始化成功，不在更新");
      return;
    }
    if (display.type === CmptType.LABEL) {
      this.newBody = display.getNewProperties();
    }
    yy.single
      .instance(DisplayObjectManager)
      .updateDisplayObject(body.id, body.newProperties, true);
    // yy.single.instance(AnimDisplayControl).updateActionOption(body.id);

    // 更新动画逻辑
    let delta = {
      x: body.newProperties.x - this.newBody.newProperties.x,
      y: body.newProperties.y - this.newBody.newProperties.y,
    };
    yy.single.instance(AnimDisplayControl).updateDispObject(body.id, delta);
  }
}

