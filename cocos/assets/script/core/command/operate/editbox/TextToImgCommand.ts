import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DataCenterBridge from "../../../proxy/DataCenterBridge";

export default class TextToImgCommand extends SimpleCommand {
  /**
   * 更新属性命令
   * @param {string[]} body.selectIds 更新的组件id数组
   */
  public execute(body?: { selectId: string; texture: cc.RenderTexture }): void {
    let selectId = body.selectId;
    let texture = body.texture;
    let dataCenterBridge = yy.single.instance(DataCenterBridge);
    dataCenterBridge.textToImg({ selectId, texture });
  }

  public undo(body?: any): void {
    yy.log(body);
  }
}
