import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayCutShape from "../../../display/base/edit/DisplayCutShape";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 添加切割图形组件坐标点
 */
export class CreatePointCommand extends SimpleCommand {
    public execute (body: {cmptIds: string, pointDatas: any }): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.cmptIds) as DisplayCutShape;
        pointObj.createEditPoint(body.pointDatas);
        this.oldBody = {id: body.cmptIds, pointId: body.pointDatas.id, editable: body.pointDatas.editable};
    }

    public undo (body: {id: string, pointId: string, editable: boolean}): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.id) as DisplayCutShape;
        pointObj.removeEditPoint(body.pointId, body.editable);
    }
}
