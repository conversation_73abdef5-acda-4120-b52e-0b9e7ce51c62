import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayCutShape from "../../../display/base/edit/DisplayCutShape";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 添加切割图形组件坐标点
 */
export class RemovePointCommand extends SimpleCommand {
    public execute (body: {cmptIds: string, pointId: string }): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.cmptIds) as DisplayCutShape;
        let pointData: cc.Vec2 = pointObj.removeEditPoint(body.pointId, true);
        this.oldBody = {id: body.cmptIds, pointData};
    }

    public undo (body: {id: string,  pointData: any}): void {     
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.id) as DisplayCutShape;
        pointObj.createEditPoint(body.pointData); 
    }
}
