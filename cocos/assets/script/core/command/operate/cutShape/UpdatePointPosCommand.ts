import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import DisplayCutShape from "../../../display/base/edit/DisplayCutShape";
import DisplayObjectManager from "../../../display/DisplayObjectManager";
/**
 * 更新切割图形组件坐标点位置
 */
export class UpdatePointPosCommand extends SimpleCommand {
    public execute (body: {cmptIds: string, pointId: string, pointPos: any, isVue? :boolean}): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.cmptIds) as DisplayCutShape;    
        let newPos = body.pointPos;
        if (body.isVue) {
            newPos = dpMgr.getEditPointPos(body.cmptIds, body.pointId, body.pointPos)
        }
        let oldPos = pointObj.updatePointPos(body.pointId, newPos);
        this.oldBody = {cmptIds: body.cmptIds, pointId: body.pointId, pointPos: oldPos};
    }
    
    public undo (body: {cmptIds: string, pointId: string, pointPos: cc.Vec2}): void {
        let dpMgr = yy.single.instance(DisplayObjectManager);
        let pointObj = dpMgr.getDisplayObjectById(body.cmptIds) as DisplayCutShape;
        pointObj.updatePointPos(body.pointId, body.pointPos);
    }
}
