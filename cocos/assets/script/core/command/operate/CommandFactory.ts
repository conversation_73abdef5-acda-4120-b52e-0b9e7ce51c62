import { SingleBase } from "../../../../qte/core/base/SingleBase";
import CommandManager from "../../../../qte/core/extension/command/CommandManager";
import SimpleCommand from "../../../../qte/core/extension/command/SimpleCommand";
import MergeCommand from "./MergeCommand";
/**
 * 命令封装工厂
 * <AUTHOR>
 * 命令封装工厂用于封装独立的命令、组合的action命令、处理命令回退、以及处理组合命令的事务。
 */
export default class CommandFactory extends SingleBase {
  // 缓存操作队列最大长度
  public static MAX_LENGTH: number = 100;
  // 组合命令
  private _mergeCommond: MergeCommand;
  // 撤销命令栈
  private _backwardCommandList: MergeCommand[];
  // 前进命令栈
  private _forwordCommandList: MergeCommand[];

  public initInstance(): void {
    this._backwardCommandList = [];
    this._forwordCommandList = [];
    this._mergeCommond = new MergeCommand();
  }

  /**
   * 执行一个命令
   * @param {new () => SimpleCommand} cmd 需要执行的命令
   * @param {any} data 自定义参数
   */
  public execute(cmd: new () => SimpleCommand, data: any): void {
    let c = yy.single.instance(CommandManager).executeCommand(cmd, data, true);
    this._mergeCommond.addCmdInstance(c);
  }

  /**
   * 向回退栈中压入一个命令
   * @param {SimpleCommand} cmd 需要执行的命令
   * @param {any} data 自定义参数
   * @param {boolean} isFront
   */
  public pushCommand(
    cmd: new () => SimpleCommand,
    newData: any,
    oldData: any,
    isFront?: boolean,
  ): void {
    // eslint-disable-next-line new-cap
    let instance = new cmd();
    instance.newBody = newData;
    instance.oldBody = oldData;
    this._mergeCommond.addCmdInstance(instance, null, isFront);
  }

  /** 一个可回退步骤 */
  public step(): void {
    let temp = this._mergeCommond.cloneReverse();
    console.warn("前进+++++++++", temp);
    this._backwardCommandList.push(temp);
    this._forwordCommandList = [];
    this._mergeCommond = new MergeCommand();
  }

  /** 回退 */
  public backward(): void {
    let back = this._backwardCommandList.pop();
    if (!back || !back.oldBody || back.oldBody === {}) {
      return;
    }
    console.warn("回退+++++++++", back);
    back.undo(back.oldBody, false);
    if (this._forwordCommandList.length > CommandFactory.MAX_LENGTH) {
      this._forwordCommandList.shift();
    }
    this._forwordCommandList.push(back.cloneReverse());
  }

  /** 前进 */
  public forward(): void {
    let forward = this._forwordCommandList.pop();
    if (!forward) {
      return;
    }
    console.warn("前进+++++++++", forward);
    forward.execute(forward.newBody, false);
    if (this._backwardCommandList.length > CommandFactory.MAX_LENGTH) {
      this._backwardCommandList.shift();
    }
    this._backwardCommandList.push(forward.cloneReverse());
  }
}
