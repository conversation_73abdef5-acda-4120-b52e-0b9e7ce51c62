import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import AnimDisplayManager from "../../../animation/display/AnimDisplayManager";

/**
 * 更新动画操作节点属性
 */
export class UpdateAnimPropertiesCommand extends SimpleCommand {

    public execute (body: {id: string, newProperties: {x: number, y: number}, actionId: string}): void {
        let animDisplayManager = yy.single.instance(AnimDisplayManager);
        let animDisplayControl = yy.single.instance(AnimDisplayControl);
        let obj = animDisplayManager.getAnimDisplayObject(body.id);
        if (!obj || !cc.isValid(obj.node)) {
            return;
        }
        obj.setSelect(true);
        // This.oldBody = body;
        this.oldBody = {id: body.id, newProperties: obj.oldProperties, actionId: body.actionId};
        // 更新位置
        animDisplayManager.updateDisplayObjectDeltaPos(body.actionId, body.id, cc.v3(body.newProperties.x - obj.node.x, body.newProperties.y - obj.node.y));
        // 设置朝向
        let cmptids = animDisplayManager.getAnimDisplayObjects(body.actionId);
        animDisplayManager.setDisObjectDir(cmptids);
        animDisplayControl.updateFragmentsMap();
        // 通知vue修改option 保证offset修改
        let newData = animDisplayControl.getActionOption(body.id);
        animDisplayControl.saveOptionData(animDisplayManager.getActionById(body.id), newData)
          
    }

    public undo (body: {id: string, newProperties: {x: number, y: number}, actionId: string}): void {
        let animDisplayManager = yy.single.instance(AnimDisplayManager);
        let animDisplayControl = yy.single.instance(AnimDisplayControl);
        let obj = animDisplayManager.getAnimDisplayObject(body.id);
        if (!obj || !cc.isValid(obj.node)) {
            return;
        }
        // 更新位置
        animDisplayManager.updateDisplayObjectDeltaPos(body.actionId, body.id, cc.v3(body.newProperties.x - obj.node.x, body.newProperties.y - obj.node.y));
        // 设置朝向
        let cmptids = animDisplayManager.getAnimDisplayObjects(body.actionId);
        animDisplayManager.setDisObjectDir(cmptids);
        // Obj.setSelect(true);
        animDisplayControl.updateFragmentsMap();
    }

}
