import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import AnimDisplayManager from "../../../animation/display/AnimDisplayManager";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 删除动画片段
 */
export class RemoveFragmentCommand extends SimpleCommand {
    public execute (body: {fragment: string}): void {
        let control = yy.single.instance(AnimDisplayControl);
        this.oldBody = body;
        control.setFragmentId("");
        control.updateFragmentsMap();
        yy.single.instance(AnimDisplayManager).clearAnimDisplayObject();
    }

    public undo (body: {fragment: string}): void {
        let control = yy.single.instance(AnimDisplayControl);
        let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
        control.updateFragmentsMap();
        control.setFragmentId(body.fragment);
        control.showAllPah(selectId);
    }

}
