import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 选中一个动作
 */
export class SetActiveActionCommand extends SimpleCommand {

    public execute (body: {actionId: string}): void {
        if (!body.actionId || body.actionId === "") {
            return;
        }
        let displayManager = yy.single.instance(DisplayObjectManager);
        let animDisplayControl = yy.single.instance(AnimDisplayControl);
        this.oldBody = {actionId: animDisplayControl.customLineData._editActionId};

        animDisplayControl.updateFragmentsMap();
        animDisplayControl.showAllPah(displayManager.selectedIds, body.actionId);
        // animDisplayControl.setActiveAction(body.actionId);
    }

    public undo (body: {actionId: string}): void {
        let displayManager = yy.single.instance(DisplayObjectManager);
        let animDisplayControl = yy.single.instance(AnimDisplayControl);

        animDisplayControl.updateFragmentsMap();
        animDisplayControl.showAllPah(displayManager.selectedIds, body.actionId);
    }
}

