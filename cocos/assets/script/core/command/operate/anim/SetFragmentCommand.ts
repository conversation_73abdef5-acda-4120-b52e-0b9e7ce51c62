
import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 切换和添加动画片段
 */
export class SetFragmentCommand extends SimpleCommand {
    public execute (body: {fragmentId: string}): void {
        let control = yy.single.instance(AnimDisplayControl);
        let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
        this.oldBody = {fragmentId: control._editFragment};
        control.updateFragmentsMap();
        control.setFragmentId(body.fragmentId);
        control.showAllPah(selectId);
    }

    public undo (body: {fragmentId: string}): void {
        let control = yy.single.instance(AnimDisplayControl);
        let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
        control.updateFragmentsMap();
        control.setFragmentId(body.fragmentId);
        control.showAllPah(selectId);
    }
}
