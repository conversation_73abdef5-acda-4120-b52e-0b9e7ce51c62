import SimpleCommand from "../../../../../qte/core/extension/command/SimpleCommand";
import AnimDisplayControl from "../../../animation/display/AnimDisplayControl";
import DisplayObjectManager from "../../../display/DisplayObjectManager";

/**
 * 删除单个动画
 */
export class RemoveActionCommand extends SimpleCommand {
    public execute (): void {
        let selectId = yy.single.instance(DisplayObjectManager).selectedIds;
        let animDisplayCtrl = yy.single.instance(AnimDisplayControl);
        animDisplayCtrl.updateFragmentsMap();
        animDisplayCtrl.showAllPah(selectId);
    }
    public undo (): void {
        this.execute();
    }
}
