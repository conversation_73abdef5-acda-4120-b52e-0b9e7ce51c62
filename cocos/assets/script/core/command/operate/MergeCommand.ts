import MacroCommand from "../../../../qte/core/extension/command/MacroCommand";
import SimpleCommand from "../../../../qte/core/extension/command/SimpleCommand";


export default class MergeCommand extends MacroCommand {

    /**
     * 向组合宏中添加子命令
     * @param {{new (): SimpleCommand}} command 子命令
     * @param {Object} body 命令参数
     * @param {boolean} isFront 是否向前加命令
     */
    public addCmdInstance (command: SimpleCommand, body?: any, isFront?: boolean): void {
        if (isFront) {
            this.commandList.splice(0, 0, { cmd: command, body });
            return;
        }
        this.commandList.push({ cmd: command, body });
    }

    public cloneReverse (): MergeCommand {
        let cmd = new MergeCommand();
        for (let i = this.commandList.length - 1; i >= 0; i--) {
            let c = this.commandList[i];
            cmd.commandList.push(c);
        }
        return cmd;
    }

    public clone (): MergeCommand {
        let cmd = new MergeCommand();
        for (let i = 0; i < this.commandList.length; i++) {
            let c = this.commandList[i];
            cmd.commandList.push(c);
        }
        return cmd;
    }
}