import SimpleCommand from "../../../../qte/core/extension/command/SimpleCommand";
import { CmptType } from "../../../common/EditorEnum";
import AnimDisplayControl from "../../animation/display/AnimDisplayControl";
import DisplayObjectGroup from "../../display/base/DisplayObjectGroup";
import DisplayObjectManager from "../../display/DisplayObjectManager";
import DataCenterBridge from "../../proxy/DataCenterBridge";
import { UpdatePropertiesCommand } from "../operate/base/UpdatePropertiesCommand";
import CommandFactory from "../operate/CommandFactory";

export default class UpdateProp2VueCmd extends SimpleCommand {
  /**
   * 更新属性命令
   * @param {string[]} body.selectIds 更新的组件id数组
   * @param {boolean} body.isUpdateGroupData 是否更新组内数据
   * @param {boolean} body.notUpdate2Vue 是否【不】更新数据到vue，默认值false
   * @param {boolean} body.notRunCmd 是否【不】运行UpdatePropertiesCommand命令，默认值false
   */
  public execute(body?: {
    selectIds: string[];
    isUpdateGroupData?: boolean;
    notUpdate2Vue?: boolean;
    notRunCmd?: boolean;
  }): void {
    console.log("UpdateProp2VueCmd", body);
    let selectIds = body.selectIds;
    let notRunCmd = body.notRunCmd;
    let notUpdate2Vue = body.notUpdate2Vue;
    let isUpdateGroupData = body.isUpdateGroupData;

    let displayManager = yy.single.instance(DisplayObjectManager);
    let dataCenterBridge = yy.single.instance(DataCenterBridge);

    let updateIds = yy.cloneValues(selectIds);
    if (isUpdateGroupData) {
      let tempIds = [];
      for (let id of updateIds) {
        let displayObj = displayManager.getDisplayObjectById(id);
        if (displayObj.type === CmptType.GROUP) {
          let gourpIds = (displayObj as DisplayObjectGroup).groupIds;
          for (let groupId of gourpIds) {
            // eslint-disable-next-line max-depth
            if (!tempIds.find(value => value === groupId)) {
              tempIds.push(groupId);
            }
          }
        }
      }
      for (let id of tempIds) {
        if (!updateIds.find(value => value === id)) {
          updateIds.push(id);
        }
      }
    }
    let cmdFactory = yy.single.instance(CommandFactory);
    for (let id of updateIds) {
      let displayObj = displayManager.getDisplayObjectById(id);
       let idData= dataCenterBridge.getComponentMap()[id];
      if (idData &&displayObj) {
        // 获取原始状态，将操作封装成命令添加到命令队列中，用于回退操作
        let oldData = displayObj.getOldProperties();
        // 准备传给command的数据
        let newData = displayObj.getNewProperties();
        //if (this.isChangePropertie(oldData, newData) && !notRunCmd) {
        cmdFactory.pushCommand(UpdatePropertiesCommand, newData, oldData);
        //}
        /*
         * 只有命令新创建时是正向操作，需要更新数据到vue数据层
         * 后续前进和后退逻辑时是执行的缓存命令，不应该再次commit数据
         */
        if (!notUpdate2Vue) {
          // 通知编辑器修改属性状态
          dataCenterBridge.updateComponetProperties(id, newData.newProperties);
        }
        // 更新动画逻辑
        let delta = {
          x: newData.newProperties.x - oldData.newProperties.x,
          y: newData.newProperties.y - oldData.newProperties.y,
        };
        yy.single.instance(AnimDisplayControl).updateDispObject(id, delta);
      }
    }
  }

  public undo(body?: any): void {
    yy.log(body);
  }

  // 检查选中的组件属性是否改变
  private isChangePropertie(oldData, newData): boolean {
    for (let key in oldData.newProperties) {
      if (
        yy.checkValue(newData.newProperties[key]) !==
        yy.checkValue(oldData.newProperties[key])
      ) {
        return true;
      }
    }
    return false;
  }
}
