import { SingleBase } from "../../../qte/core/base/SingleBase";
import MathUtil from "../../../qte/core/utils/MathUtil";
import DisplayObject from "../display/base/DisplayObject";

/**
 * 碰撞相关逻辑
 */
export default class CollisionManager extends SingleBase {

    /**
     * 检测对象碰撞
     * @param pos 当前点击位置
     * @param comptIds 当前组件层级关系数组
     * @param objectMap 当前显示对象map
     */
    public testDisplayObject (pos: cc.Vec2, comptIds: any[], objectMap: Map<string, DisplayObject>): string {
        for (let i = comptIds.length - 1; i >= 0; i--) {
            let id = comptIds[i];
            let displayObject: DisplayObject = null;
            if (typeof (id) === "string") {
                displayObject = objectMap.get(id);
            } else {
                displayObject = objectMap.get(id.id);
            }
            if (!displayObject) {
                continue;
            }
            // 如果对象的可编辑属性是false,则不检测碰撞
            if (!displayObject.editable) {
                continue;
            }

            let isInRect = MathUtil.isPosInRotationRect(pos, displayObject.rect, yy.checkValue(displayObject.node.angle, 0));
            if (isInRect) {
                id = displayObject.cid;
                if (id && id.length > 0) {
                    return id;
                }
            }
        }
        return "-1";
    }
    /**
     **
     * 检测鼠标选中框是否覆盖对象中心点
     * @param rect 选中框矩形
     * @param comptIds 组件id数组
     * @param objectMap 现实对象map
     */
    public testDisplaySelect (rect: cc.Rect, comptIds: any[], objectMap: Map<string, DisplayObject>): string[] {
        let temp = [];
        for (let i = comptIds.length - 1; i >= 0; i--) {
            let id = comptIds[i];
            let displayObject: DisplayObject = null;
            if (typeof (id) === "string") {
                displayObject = objectMap.get(id);
            } else {
                displayObject = objectMap.get(id.id);
            }
            if (!displayObject) {
                continue;
            }
            // 如果对象的可编辑属性是false,则不检测碰撞
            if (!displayObject.editable) {
                continue;
            }
            let pos = cc.v2(displayObject.node.x, displayObject.node.y);
            let isInRect = MathUtil.isPosInRotationRect(pos, rect, yy.checkValue(displayObject.node.angle, 0));
            if (isInRect) {
                id = displayObject.cid;
                if (id > 0) {
                    temp.push(id);
                }
            }
        }
        return temp;
    }
}
