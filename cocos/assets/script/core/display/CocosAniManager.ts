import CocosAniRect from "../../extends/cocosAni/CocosAniRect";
import SpineRect from "../../../qte/core/component/spine/SpineRect";
import { SingleBase } from "../../../qte/core/base/SingleBase";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import CommandManager from "../../../qte/core/extension/command/CommandManager";
import { CmptLayer, CmptType } from "../../common/EditorEnum";
import UpdateProp2VueCmd from "../command/simple/UpdateProp2VueCmd";
import { CocosAniData, SpineData } from "../proxy/ComptData";
import DisplayCocosAni from "./base/DisplayCocosAni";
import DisplaySpine from "./base/DisplaySpine";
import DisplayObjectManager from "./DisplayObjectManager";

export default class CocosAniManager extends SingleBase {

    /**
     * 设置spine初始数据， 通过缩放设置宽高 加上偏移坐标
     * @param id 
     * @param properties 
     */
    // eslint-disable-next-line require-await
    public async initCocosAniData (id: string, properties: any, cocosAniData: CocosAniData) {
        return new Promise<void>((resolve) => {
            let manager = yy.single.instance(DisplayObjectManager);
            let displayObj: DisplayCocosAni = null;
            displayObj = manager.getDisplayObjectById(id) as DisplayCocosAni;
            // TODO
            if(!displayObj){
                return;
            }

            let cocosAniNode = displayObj.node.getChildByName("cocosAniNode");
            // cocosAni的Rect
            let cocosAniRect = cocosAniNode.getComponent(CocosAniRect);
            
            let animationCmpt = cocosAniNode.getComponent(cc.Animation);

            displayObj.cocosAniData = cocosAniData;
            const tempSize:cc.Size = cocosAniNode.getContentSize();
            // size为0时给定默认值
            tempSize.width = tempSize.width===0? 200 : tempSize.width;
            tempSize.height = tempSize.height===0? 200 : tempSize.height;
            displayObj.size = tempSize;
            
            displayObj.node.width = displayObj.size.width;
            displayObj.node.height = displayObj.size.height;

            displayObj.addListener(animationCmpt);

            cocosAniRect.calcOffset(() => {
                setTimeout(() => {
                    displayObj.anchor = ValueUtils.setOneDecimal(cocosAniRect.getAnchor(), 2);
                    displayObj.resetCocosAniPos();
                    this.setProperty(displayObj, properties);
                    displayObj.refreshRectPoint();
                    yy.single.instance((CommandManager)).executeCommand(UpdateProp2VueCmd, { selectIds: [displayObj.cid], isUpdateGroupData: false, notUpdate2Vue: false, notRunCmd: true });
                    resolve()
                }, 20)
            });
        })
    }

    /**
     * 计算取消偏移的位置 缩放 剔除属性
     * @param properties 
     */
    private setProperty (displayObj: DisplayCocosAni, properties: any) {
        let prop = {} as any;
        for (let key in properties) {
            if (key === "x" || key === "y") {
                continue;
            }
            if (key === "scaleX") {
                displayObj.node.width *= properties.scaleX;
                continue;
            }
            if (key === "scaleY") {
                displayObj.node.height *= properties.scaleY;
                continue;
            }
            // 如果初始状态loop 是true 不设置， 防止动画播放覆盖
            if (key === "loop" && properties[key] === true) {
                continue;
            }
            prop[key] = properties[key];
        }
        yy.single.instance(DisplayObjectManager).updateDisplayObject(displayObj.cid, prop);
    }

    /**
     * 用object_layer 里的数据去播放其他层的spine组件
     * @param layer 
     */
    public playActionByCmptLayer (layerNode: cc.Node) {
        let manager = yy.single.instance(DisplayObjectManager);
        manager.dpMap.forEach((displayObj) => {
            let cmptCocosAni = displayObj as DisplayCocosAni;
            if (cmptCocosAni.type === CmptType.COCOSANI) {
                let nodePath = this.getNodePath(cmptCocosAni.node, CmptLayer.OBJECT_LAYER);
                let tempNode = cc.find(nodePath, layerNode);
                if (tempNode) {
                    let displayCocosAni = tempNode.getComponent(DisplayCocosAni);
                    displayCocosAni.addListener(displayCocosAni.getComponentInChildren(cc.Animation));    
                    // 设置属性
                    displayCocosAni.setLoop(cmptCocosAni.loop);       
                    displayCocosAni.setTimeScale(cmptCocosAni.timeScale); 
                    displayCocosAni.setAnimation(cmptCocosAni.actionList);
                }
            }
        });

    }

    /**
     * vue 调用
     * @param cmptId 
     */
    public getCocosAniProperties (cmptId: string): any {
        let displayObj: DisplayCocosAni = null;
        displayObj = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cmptId) as DisplayCocosAni;
        let cocosAniNode = displayObj.node.getChildByName("cocosAniNode");
        if (!displayObj || !cocosAniNode) {
            return null;
        }
        let prop = {offsetX: 0, offsetY: 0, scaleX: 1, scaleY: 1};
        prop.scaleX = cocosAniNode.scaleX;
        prop.scaleY = cocosAniNode.scaleY;
        prop.offsetX = displayObj.offset.x;
        prop.offsetY = displayObj.offset.y;
        return prop;
    
    }

    /**
     * 获得节点在跟节点下的路径
     * @param node 子节点
     * @param root 根节点
     */
    private getNodePath (node: cc.Node, root: CmptLayer): string {
        let path = "";
        while (node && !(node.name === root)) {
            if (path) {
                path = `${node.name}/${path}`;
            } else {
                path = node.name;
            }
            node = node.parent;
        }
        return path;
    }

}