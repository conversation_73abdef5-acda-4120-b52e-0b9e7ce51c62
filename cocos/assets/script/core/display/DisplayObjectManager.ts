/*
 * @FilePath     : /cocos/assets/script/core/display/DisplayObjectManager.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
/* eslint-disable arrow-body-style */
import { SingleBase } from "../../../qte/core/base/SingleBase";
import MathUtil from "../../../qte/core/utils/MathUtil";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import { CmptType } from "../../common/EditorEnum";
import Properties from "../../utils/Properties";
import HintLineManager from "../hintline/HintLineManager";
import DataCenterBridge from "../proxy/DataCenterBridge";
import DisplayObject from "./base/DisplayObject";
import DisplayObjectGroup from "./base/DisplayObjectGroup";
import DisplayCutShape from "./base/edit/DisplayCutShape";
import { SignalData } from "../../stage/StageController";
import QTEUtils from "../../../qte/util/QTEUtils";
import DisplaySpecialComponent from "./base/DisplaySpecialComponent";
import ComptData from "../proxy/ComptData";

/**
 * 显示对象管理类
 */
export default class DisplayObjectManager extends SingleBase {
  // 显示对象集合
  private _dpMap: Map<string, DisplayObject> = null;
  public get dpMap(): Map<string, DisplayObject> {
    return this._dpMap;
  }

  // 显示对象原始index
  private _dpIndexMap: Map<string, number> = null;
  public get dpIndexMap(): Map<string, number> {
    return this._dpIndexMap;
  }

  // 当前选中拖动的的组件i
  private _selectId: string = "-1";
  public set selectId(value: string) {
    this._selectId = value;
  }
  public get selectId(): string {
    return this._selectId;
  }
  // 当前选中组中的子节点ID
  private _selectSubId: string = "-1";
  public set selectSubId(value: string) {
    this._selectSubId = value;
  }
  public get selectSubId(): string {
    return this._selectSubId;
  }

  // 选中对象列表
  private _selectedIds: string[];
  public get selectedIds(): string[] {
    return this._selectedIds;
  }

  public initInstance(): void {
    this._dpMap = new Map();
    this._selectedIds = [];
    this._dpIndexMap = new Map();
  }

  /**
   * 这个id是否是选中id组中id的subId
   * @param id 该ID
   * @param arrIds 选中的id组
   */
  public hasOwnedGroup(id: string, arrIds: string[]): boolean {
    for (let i = 0; i < arrIds.length; i++) {
      let _id = arrIds[i];
      let display = this.getDisplayObjectById(_id);
      if (display.type === CmptType.GROUP) {
        let subIds = (display as DisplayObjectGroup).groupIds;
        for (let j = 0; j < subIds.length; j++) {
          if (subIds[j] === id) {
            return true;
          }
        }
      }
    }
    return false;
  }

  /** 获取所有根节点id，不包括组内id，即subId */
  public getRootIds(): string[] {
    let ids = yy.cloneValues(this.selectedIds);
    let result = [];
    for (let id of ids) {
      let display = this.getDisplayObjectById(id);
      if (display.type === CmptType.GROUP) {
        result.push(id);
      } else {
        let boo = this.hasOwnedGroup(id, ids);
        if (!boo) {
          result.push(id);
        }
      }
    }
    return result;
  }

  /** 稍选选择子组件 */
  public getSubIds(): string[] {
    let ids = yy.cloneValues(this.selectedIds);
    let result = [];
    if (ids.length == 2) {
      let display = this.getDisplayObjectById(ids[0]);
      if (display.type === CmptType.GROUP) {
        let subIds = (display as DisplayObjectGroup).groupIds;
        for (let j = 0; j < subIds.length; j++) {
          if (subIds[j] === ids[1]) {
            result.push(ids[1]);
            return result;
          }
        }
      }
    }
    return result;
  }

  /**
   * 添加显示对象
   * @param id
   * @param object
   */
  public addDisplayObject(id: string, object: DisplayObject): void {
    this._dpMap.set(id, object);
    console.log("%c Line:133 🍔 object.node.getSiblingIndex()", "color:#fca650", object.node.getSiblingIndex(), id);
    this._dpIndexMap.set(id, object.node.getSiblingIndex());
  }
  /**
   * 更新显示对象的 index
   * @param id 
   * @param index 
   */
  public updateDisplayObjectIndex(id: string, index: number): void {
    this._dpIndexMap.set(id, index);
  }

  // 拿到编辑器中组件管理的数据，和当前 的 _dpIndexMap 进行对比，如果发现有差异，则按照组件管理的数据进行排序刷新
  public compareAndRefreshDisplayObjectIndex(): void {
    let editorData = yy.single.instance(DataCenterBridge).getComponentIds();
    console.log("%c Line:148 🌶 editorData", "color:#33a5ff", editorData);
    // editorData 数据结构为 
    //      [
    //     "29",
    //     {
    //         "id": "39",
    //         "subIds": [
    //             "25",
    //             "38"
    //         ]
    //     }
    // ]

    // 把 ——dpIndexMap 按照父节点分成 2 个数组；
    let _dpIndex = [];
    let _dpSubIndex = [];

    //  循环 _dpIndexMap ， 把 父节点和子节点分开
    for (let [id, index] of Array.from(this._dpIndexMap.entries())) {
      let parentId = this.isSubNode(id);
      if (parentId) {
        // 把 _dpIndexMap 的值 赋值给 _dpSubIndex
        console.log("%c Line:170 🎂 index", "color:#7f2b82", index);
        _dpSubIndex.push([id, index]);
      } else {
        _dpIndex.push([id, index]);
      }
    }
    // 是否有差异
    let isDiff = false;
    console.log("%c Line:177 🥤 _dpIndex", "color:#f5ce50", _dpIndex);
    console.log("%c Line:178 🥤 _dpSubIndex", "color:#f5ce50", _dpSubIndex);

    // 循环 editorData, 判断是字符串还是对象。
    // for (let item of editorData) {
    for (let i = 0; i < editorData.length; i++) {
      let item = editorData[i];
      if (typeof item === 'string') {
        // 说明是父节点 判断 所在下标 是否等于 _dpIndex 的值；
        let index = _dpIndex.findIndex(dpItem => dpItem[0] === item);
        if (index !== -1 && _dpIndex[index][1] !== i) {
          isDiff = true;
          break;
        }
      } else {
        // 先判断父节点 是否匹配，如果匹配，则判断子节点是否匹配
        let index = _dpIndex.findIndex(dpItem => dpItem[0] === item.id);
        if (index !== -1 && _dpIndex[index][1] !== i) {
          isDiff = true;
          break;
        }
        // 再判断子节点是否匹配
        for (let j = 0; j < item.subIds.length; j++) {
          let subId = item.subIds[j];
          let subIndex = _dpSubIndex.findIndex(dpItem => dpItem[0] === subId);
          // 修复：子节点索引应该考虑辅助节点偏移量
          if (subIndex !== -1 && _dpSubIndex[subIndex][1] !== (j + 2)) { // +2 for tag and signs nodes
            isDiff = true;
            break;
          }
        }
        if (isDiff) break;
      }
    }
    console.log("%c Line:177 🥤 isDiff", "color:#f5ce50", isDiff);
    if (isDiff) {
      // 按照 editorData 的顺序，重新排序 _dpIndexMap
      let newDpIndexMap = new Map<string, number>();
      for (let i = 0; i < editorData.length; i++) {
        let item = editorData[i];
        if (typeof item === 'string') {
          newDpIndexMap.set(item, i);
        } else {
          newDpIndexMap.set(item.id, i);
          for (let j = 0; j < item.subIds.length; j++) {
            // 保持硬编码 +2，因为这是基于已知的 tag 和 signs 节点
            newDpIndexMap.set(item.subIds[j], j + 2);
          }
        }
      }
      console.log("%c Line:223 🍯 newDpIndexMap", "color:#4fff4B", newDpIndexMap);
      this._dpIndexMap = newDpIndexMap;
      this.sortDisplayObjectByIndex();
    }
  }


  /**
   * 按照原始 index , 给所有节点重新设置层级
   */
  public sortDisplayObjectByIndex(): void {
    console.log('调用 sortDisplayObjectByIndex, _dpIndexMap:', JSON.stringify(Array.from(this._dpIndexMap.entries())));
    console.log('调用 sortDisplayObjectByIndex, _dpMap:', Array.from(this._dpMap.keys()));

    if (this._dpMap.size === 0) {
      return;
    }

    // 1. 按父节点对所有待排序节点进行分组
    const nodesByParent = new Map<cc.Node, { node: any; targetIndex: number; id: string }[]>();

    for (const [id, displayObject] of Array.from(this._dpMap.entries())) {
      const targetIndex = this._dpIndexMap.get(id);
      // console.log("displayObject.node.parent", displayObject.node.parent);
      if (targetIndex !== undefined && displayObject.node && displayObject.node.parent) {
        const parent = displayObject.node.parent;
        if (!nodesByParent.has(parent)) {
          nodesByParent.set(parent, []);
        }
        nodesByParent.get(parent)!.push({ node: displayObject.node, targetIndex: targetIndex, id: id });
      } else {
        console.warn(`DisplayObjectManager: Node, parent, or targetIndex not found for id ${id}`);
      }
    }
    // console.log("%c Line:172 🍫 nodesByParent", "color:#42b983", nodesByParent);
    // 2. 遍历每个父节点分组，独立进行排序和层级设置
    for (const [parent, nodesToSort] of Array.from(nodesByParent.entries())) {
      // 2.1 对当前分组内的节点列表进行排序
      nodesToSort.sort((a, b) => {
        if (a.targetIndex !== b.targetIndex) {
          return a.targetIndex - b.targetIndex;
        }
        return a.id.localeCompare(b.id); // 稳定性排序
      });

      console.log(`Sorted nodes for parent ${parent.name}:`, nodesToSort.map(item => ({ id: item.id, targetIndex: item.targetIndex })));

      // 2.2 按排序结果设置兄弟节点索引
      nodesToSort.forEach((item, index) => {
        // 在这里，可以确信同一个分组内的所有节点都有相同的父节点
        if (item.node) {
          item.node.setSiblingIndex(index);
        }
      });
    }
  }
  /**
   * 获取原始 index
   * @param id 
   */
  public getOriginSiblingIndexById(id: string): number {
    return this._dpIndexMap.get(id) as number;
  }

  // 判断是否是子节点
  public isSubNode(id: string): string | false {
    // 循环 _dpMap
    for (const [key, value] of Array.from(this._dpMap.entries())) {
      if (value.type === CmptType.GROUP) {
        let subIds = (value as DisplayObjectGroup).groupIds;
        if (subIds.includes(id)) {
          return key;
        }
      }
    }
    return false;
  }


  // 更新原始层级,循环 map, 把 id 对应的 index 更新为新的 index, 同时调整其他受影响元素的 index
  public updateOriginSiblingIndexById(updates: {id: string, newIndex: number, oldIndex: number}[], isRefresh?: boolean): void {
    console.log("%c Line:219 🍫 updates", "color:#42b983", updates);
    if (!updates || updates.length === 0) {
      return;
    }
    
    // 验证所有ID都存在
    for (const update of updates) {
      if (!this._dpIndexMap.has(update.id)) {
        console.warn(`[updateOriginSiblingIndexById] ID ${update.id} not found in _dpIndexMap.`);
        return;
      }
    }

    // 1. 确定所有更新节点的上下文并验证一致性
    const firstContextId = this.isSubNode(updates[0].id) || 'root';
    for (const update of updates) {
      const contextId = this.isSubNode(update.id) || 'root';
      if (contextId !== firstContextId) {
        console.warn(`[updateOriginSiblingIndexById] 所有更新节点必须属于同一上下文. 节点 ${update.id} 的上下文 ${contextId} 与预期 ${firstContextId} 不匹配`);
        return;
      }
    }

    // console.log("%c Line:323 🥥 contextId", "color:#e41a6a", firstContextId);

    // 2. 分离同上下文节点和其他节点
    const sameContextNodes = new Map<string, number>();
    const otherContextNodes = new Map<string, number>();

    for (const [id, index] of Array.from(this._dpIndexMap.entries())) {
      const nodeContextId = this.isSubNode(id) || 'root';
      if (nodeContextId === firstContextId) {
        sameContextNodes.set(id, index);
      } else {
        otherContextNodes.set(id, index);
      }
    }

    console.log("%c Line:333 🍌 sameContextNodes", "color:#6ec1c2", Array.from(sameContextNodes.entries()));
    console.log("%c Line:337 🥥 otherContextNodes", "color:#42b983", Array.from(otherContextNodes.entries()));

    // 3. 策略2：分阶段处理 - 确保精确分配
    const updatedIds = new Set(updates.map(u => u.id));
    const targetIndexes = new Set(updates.map(u => u.newIndex));
    
    // 阶段1: 清空目标位置 - 将占用目标位置的非更新节点移到临时位置
    const temporaryAssignments = new Map<string, number>();
    const conflictNodes: {id: string, originalIndex: number}[] = [];
    
    for (const [id, currentIndex] of Array.from(sameContextNodes.entries())) {
      if (!updatedIds.has(id)) {
        // 非更新节点
        if (targetIndexes.has(currentIndex)) {
          // 该节点占用了目标位置，需要移出
          conflictNodes.push({id, originalIndex: currentIndex});
          // console.log(`   📦 节点 ${id} 从位置 ${currentIndex} 暂时移出 (目标位置被需要)`);
        } else {
          // 该节点不冲突，保持原位置
          temporaryAssignments.set(id, currentIndex);
        }
      }
    }
    
    // 阶段2: 精确分配目标位置 - 将更新节点分配到期望位置
    for (const update of updates) {
      temporaryAssignments.set(update.id, update.newIndex);
      // console.log(`   ✅ 节点 ${update.id}: ${update.oldIndex} → ${update.newIndex} (精确分配)`);
    }
    
    // 阶段3: 重新安置被移出的节点 - 为冲突节点找到新位置
    for (const conflictNode of conflictNodes) {
      const availableIndex = this.findNextAvailableIndex(temporaryAssignments, conflictNode.originalIndex);
      temporaryAssignments.set(conflictNode.id, availableIndex);
      // console.log(`   🏠 节点 ${conflictNode.id}: ${conflictNode.originalIndex} → ${availableIndex} (重新安置)`);
    }

    console.log("%c 🏆 分阶段处理完成，最终分配方案", "color:#e41a6a", Array.from(temporaryAssignments.entries()));

    // 4. 重建 _dpIndexMap
    this._dpIndexMap.clear();
    
    // 恢复其他上下文的节点
    for (const [id, index] of Array.from(otherContextNodes.entries())) {
      this._dpIndexMap.set(id, index);
    }
    
    // 应用最终的索引分配方案
    for (const [id, index] of Array.from(temporaryAssignments.entries())) {
      this._dpIndexMap.set(id, index);
    }

    // console.log("%c Line:395 🥥 final _dpIndexMap", "color:#42b983", JSON.stringify(Array.from(this._dpIndexMap.entries())));
    
    if (isRefresh) {
      this.sortDisplayObjectByIndex();
    }
    
    console.log(`[updateOriginSiblingIndexById] 批量更新完成 (策略2-分阶段处理):`, JSON.stringify(Array.from(this._dpIndexMap.entries())));
  }

  /**
   * 寻找下一个可用的索引位置
   */
  private findNextAvailableIndex(indexMap: Map<string, number>, startFrom: number): number {
    const usedIndexes = new Set(Array.from(indexMap.values()));
    let candidate = startFrom;
    
    // 先尝试向后寻找
    while (usedIndexes.has(candidate)) {
      candidate++;
    }
    
    return candidate;
  }

  /**
   * 移除显示对象组件
   * @param id 组件id
   */
  public removeDisplayObject(id: string): void {
    let dp = this.getDisplayObjectById(id);
    if (!dp || !cc.isValid(dp.node)) {
      return;
    }
    this._dpMap.delete(id);
    this._dpIndexMap.delete(id);
    if (dp.node) {
      console.warn(yy.loader.cmptAssets.get(id), yy.loader.assetsMap, "dengchao");
      if (yy.loader.cmptAssets.get(id)) {
        yy.loader.cmptAssets.get(id).forEach((path: string) => {
          if (yy.loader.assetsMap.get(path).refCount > 0) {
            yy.loader.assetsMap.get(path).decRef();
          }
        });
        yy.loader.cmptAssets.delete(id);
      }

      dp.node.destroy();
    }
    // 删除线段
    yy.single.instance(HintLineManager).removeHintLineByDisplay(id);
    // 选中框的数据 从数组中干掉~
    let nIndex = -1;
    for (let i = 0; i < this.selectedIds.length; i++) {
      if (this.selectedIds[i] === id) {
        nIndex = i;
        break;
      }
    }
    if (nIndex !== -1) {
      this.selectedIds.splice(nIndex, 1);
    }
  }

  /**
   * 根据id获取显示对象
   * @param {string} id
   */
  public getDisplayObjectById(id: string): DisplayObject {
    return this._dpMap.get(id);
  }

  /**
   * 根据id获取显示对象的原始index
   * @param {string} id
   */
  public getDisplayObjectIndexById(id: string): number {
    return this._dpIndexMap.get(id) as number;
  }
  /**
   * 根据id获取显示对象
   * @param {string} id
   */
  public getDisplayObjectByTag(tag: string): DisplayObject[] {
    let disObjList: DisplayObject[] = [];
    this._dpMap.forEach((value, key) => {

      let dataCenterBridge = yy.single.instance(DataCenterBridge);
      let cmpt: ComptData = dataCenterBridge.getComponentMap()[key];
      if (cmpt.tag == tag) {
        disObjList.push(value);
      }
    });
    return disObjList;
  }

  /**
   * 更新显示对象属性
   * @param id 组件id
   * @param newProperties 属性
   */
  public updateDisplayObject(id: string, newProperties: any, isUndo?: boolean): any {
    let curObj = this.getDisplayObjectById(id);

    if (!curObj) {
      yy.error(`id = ${id}的组件不存在`);
      return;
    }
    let oldProperties = {};
    const { properties } = (curObj.editable as any) || {};
    // console.error("111newProperties", newProperties);
    for (let key in newProperties) {
      if (properties) {
        if (typeof properties[key] === "boolean" && !properties[key]) {
          continue;
        }
      }
      let oldValue = Properties.setProperty(curObj, curObj.type, key, newProperties[key], isUndo);
      if (typeof oldValue.value != "undefined") {
        oldProperties[key] = oldValue.value;
      }
    }

    // console.error("1111oldProperties", oldProperties);
    // 更新线段配置
    yy.single.instance(HintLineManager).updateLineByDisplayScale(id);
    if (curObj.type === CmptType.GROUP) {
      let gourpIds = (curObj as DisplayObjectGroup).groupIds;
      for (let i = 0; i < gourpIds.length; i++) {
        yy.single.instance(HintLineManager).updateLineByDisplayScale(gourpIds[i]);
      }
    }
    curObj.refreshRectPoint();
    return oldProperties;
  }

  /**
   * 更新显示对象坐标属性的增量值
   * @param id 组件id
   * @param pos 坐标的增量
   */
  public updateDisplayObjectDeltaPos(id: string, pos: cc.Vec3): void {
    let curObj = this.getDisplayObjectById(id);
    if (!curObj) {
      yy.error(`id = ${id}的组件不存在`);
      return;
    }
    // console.error("id", id);
    // 如果组件是锁住状态 不可拖动
    if (!curObj.dragable) {
      return;
    }
    let newPos = cc.v3(pos.x, pos.y);
    const { properties } = (curObj.editable as any) || {};

    // 判断属性是否可编辑
    if (typeof curObj.editable === "object" && properties) {
      if (typeof properties.x === "boolean" && !properties.x) {
        newPos.x = 0;
      }
      if (typeof properties.y === "boolean" && !properties.y) {
        newPos.y = 0;
      }
    }
    // 操作的是组内控件
    let displayGroupID = this.getDisplayGroupID(id);
    if (displayGroupID !== "-1") {
      let displayGroup = this.getDisplayObjectById(displayGroupID);
      if (displayGroup) {
        MathUtil.rotatePosSelf(newPos, displayGroup.node.angle);
      }
    }
    curObj.node.position = curObj.node.position.add(newPos);
    // 刷新组合辅助线
    if (curObj.type === CmptType.GROUP) {
      (curObj as DisplayObjectGroup).refreshRectPoint();
    }
    yy.single.instance(HintLineManager).updateLineByDisplayScale(id);
    let groupId = this.getDisplayGroupID(id);
    if (groupId !== "-1") {
      let groupDisplay = this.getDisplayObjectById(groupId) as DisplayObjectGroup;
      groupDisplay.resetLayoutSize();
      let gourpIds = groupDisplay.groupIds;
      for (let i = 0; i < gourpIds.length; i++) {
        yy.single.instance(HintLineManager).updateLineByDisplayScale(gourpIds[i]);
      }
      yy.single.instance(HintLineManager).updateLineByDisplayScale(groupId);
    }
  }

  /**
   * 设置选中状态
   * @param id 选中的id
   * @param multi 是否是多选，默认单选false
   */
  public setSelected(id: string, multi?: boolean): void {
    if (!this.checkIsValidNode(this._selectedIds)) {
      this._selectedIds = [];
      return;
    }
    if (id === "-1") {
      for (let sid of this._selectedIds) {
        this.getDisplayObjectById(sid).setSelected(false);
      }
      this._selectedIds = [];
      return;
    }
    let displayID = this.getDisplayObjectById(id);
    if (!displayID || !cc.isValid(displayID.node)) {
      return;
    }
    if (multi) {
      // 多选
      let bSelectd = displayID.getSelected();
      if (bSelectd) {
        let nIndex = this._selectedIds.indexOf(id);
        if (nIndex >= 0) {
          this._selectedIds.splice(nIndex, 1);
        }
      } else {
        this._selectedIds.push(id);
      }
      displayID.setSelected(!bSelectd);
      // 再检查一下，是否有其他节点选中，如果有，则需要恢复选中状态
      let selectedIds = yy.single.instance(DisplayObjectManager).selectedIds;
      // console.log("%c Line:505 🍷 selectedIds", "color:#b03734", selectedIds);
      if (selectedIds.length > 0) {
        for (let id of selectedIds) {
          let display = yy.single.instance(DisplayObjectManager).getDisplayObjectById(id);
          display.moveToTop();
        }
      }
    } else {
      if (this._selectedIds.indexOf(id) >= 0) {
        return;
      }
      for (let sid of this._selectedIds) {
        this.getDisplayObjectById(sid).setSelected(false);
      }
      this._selectedIds = [];
      this._selectedIds.push(id);
      displayID.setSelected(!displayID.getSelected());
    }
  }

  /**
   * 解除group
   * @param id
   */
  public removeGroupDisplayObject(id: string): string[] {
    let displayObject = this.getDisplayObjectById(id);
    if (!displayObject) {
      yy.warn(`id = ${id}的组件不存在`);
      return;
    }
    if (displayObject.type !== CmptType.GROUP) {
      yy.warn(`id = ${id}的组件不是Group`);
      return;
    }
    displayObject.setSelected(false);
    // 记录下组合父节点index
    let parentIndex = this._dpIndexMap.get(id);
    let parent = displayObject.node.parent;
    let subList = (displayObject as DisplayObjectGroup).groupIds;
    let rectGroup = displayObject;
    let sortArr: { id: string; _index: number }[] = [];
    for (let subId of subList) {
      let tempObj = this.getDisplayObjectById(subId);
      if (tempObj) {
        let _siblingIndex = tempObj.node.getSiblingIndex();
        sortArr.push({ id: subId, _index: _siblingIndex });
      }
    }
    sortArr = MathUtil.getSortDisplayArr(sortArr);
    console.log("%c Line:545 🥥 sortArr", "color:#ffdd4d", sortArr);
    for (let comId of sortArr) {
      // Display层级需要和数据对应
      for (let subId of subList) {
        let tempObj = this.getDisplayObjectById(subId);
        if (tempObj && comId.id === subId) {
          tempObj.node.parent = parent;
          console.log("tempObj==index222=", subId, "tempObj.node.getSiblingIndex()", tempObj.node.getSiblingIndex());
          
          let posChar = MathUtil.getRotationPoint(tempObj.node.x + rectGroup.node.x, tempObj.node.y + rectGroup.node.y, rectGroup.node.x, rectGroup.node.y, yy.checkValue(rectGroup.node.angle, 0));
          tempObj.node.x = posChar.x;
          tempObj.node.y = posChar.y;
          // 旋转之后的相对坐标,
          let newAngle1 = yy.checkValue(tempObj.node.angle, 0) + yy.checkValue(rectGroup.node.angle, 0);
          if (tempObj.node.angle > 360) {
            const newAgle = tempObj.node.angle % 360;
            newAngle1 = newAgle;
          }
          tempObj.node['cAngle'] = newAngle1;
          tempObj.node.angle = newAngle1;
          tempObj.node.opacity = tempObj.node.opacity;
          // 添加线段
          yy.single.instance(HintLineManager).addHintLinebyDisplay(subId);
          break;
        }
      }
    }
    (displayObject as DisplayObjectGroup).clearGroupIds();
    // 取消后的子组件节点列表
    let subListAfterCancel: { id: string; newIndex: number; oldIndex: number }[] = [];

    const contextId = this.isSubNode(id) || 'root';
    const peerElements: { id: string, originalIndex: number }[] = [];
    // console.log("%c Line:579 🍺 Array.from(this._dpIndexMap.entries())", "color:#ea7e5c", Array.from(this._dpIndexMap.entries()));
    for (const [nodeId, index] of Array.from(this._dpIndexMap.entries())) {
      const currentContextId = this.isSubNode(nodeId) || 'root';
      // console.log("%c Line:581 🍺 currentContextId", "color:#ea7e5c", contextId, currentContextId, nodeId, index);
      if (currentContextId === contextId) {
        peerElements.push({ id: nodeId, originalIndex: index });
      }
    }
    // console.log("%c Line:587 🍺 peerElements", "color:#ea7e5c", JSON.stringify(peerElements));
    peerElements.sort((a, b) => a.originalIndex - b.originalIndex);
    
    const shiftOffset = subList.length > 0 ? subList.length - 1 : 0;
    const sortArrIds = new Set(sortArr.map(item => item.id));
    const nodesToShift = peerElements.filter(p => p.originalIndex > parentIndex && !sortArrIds.has(p.id));

    let insertIndex = 0;
    for (const comId of sortArr) {
      const oldIndex = this._dpIndexMap.get(comId.id); 
      subListAfterCancel.push({ id: comId.id, newIndex: parentIndex + insertIndex, oldIndex: oldIndex });
      
      insertIndex++;
    }
    // console.log("%c Line:595 🍰 subListAfterCancel", "color:#33a5ff", JSON.stringify(subListAfterCancel) );
    // console.log("nodesToShift111", JSON.stringify(nodesToShift));
    for (const node of nodesToShift) {
      if (node.id !== id) { 
        subListAfterCancel.push({ id: node.id, newIndex: node.originalIndex + shiftOffset, oldIndex: node.originalIndex });
      }
    }
    // console.log("%c Line:578 🥥 subListAfterCancel", "color:#ffdd4d", subListAfterCancel);
    // 需要计算子组件新的 index, 
    this.updateOriginSiblingIndexById(subListAfterCancel, true);
    return subList;
  }

  /** 检查是否是有效节点 */
  private checkIsValidNode(cids: string[]): boolean {
    for (let sid of cids) {
      let obj = this.getDisplayObjectById(sid);
      if (!obj) {
        yy.warn(`id = ${sid}的组件不存在`);
        return false;
      }
    }
    return true;
  }

  /**
   * 获取当前选中ID的ParentId
   * @param selectId
   */
  public getDisplayParentID(selectId: string): string {
    let comIds = yy.single.instance(DataCenterBridge).getComponentIds();
    for (let i = 0; i < comIds.length; i++) {
      let id = comIds[i];
      if (!id.id) {
        continue;
      }
      let display = this.getDisplayObjectById(id.id);
      if (display) {
        let bFind = (display as DisplayObject).childIds.find(value => value === selectId);
        if (bFind) {
          return id.id;
        }
      }
    }
    return "-1";
  }

  /**
   * 获取当前选中ID的组ID
   * @param selectId
   */
  public getDisplayGroupID(selectId: string): string {
    let comIds = yy.single.instance(DataCenterBridge).getComponentIds();
    for (let i = 0; i < comIds.length; i++) {
      let id = comIds[i];
      if (!id.id) {
        continue;
      }
      let display = this.getDisplayObjectById(id.id);
      if (display && display.type === CmptType.GROUP) {
        let bFind = (display as DisplayObjectGroup).groupIds.find(value => value === selectId);
        if (bFind) {
          return id.id;
        }
      }
    }
    return "-1";
  }

  /**
   * 获取组件的设计坐标
   * @param id
   */
  public getWorldPos(id: string): { x: number; y: number } {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return { x: 0, y: 0 };
    }
    let rect = display.rect;
    let pos = display.node.convertToWorldSpaceAR(cc.v2(-rect.width / 2, rect.height / 2));
    // console.log('xu-pos-convertToWorldSpaceAR', pos);
    let posY = pos.y;
    let winsize = cc.winSize;
    let worldPos = cc.v2(winsize.width / 2 + pos.x, -posY + winsize.height / 2);
    // console.log('xu-pos-worldPos', worldPos);
    // console.log('xu-pos-res', {
    //   x: ValueUtils.setOneDecimal(worldPos.x, 1),
    //   y: ValueUtils.setOneDecimal(worldPos.y),
    // });
    // this.setNodePos(id, cc.v2(worldPos.x - 100, worldPos.y))
    return {
      x: ValueUtils.setOneDecimal(worldPos.x, 1),
      y: ValueUtils.setOneDecimal(worldPos.y),
    };
  }
  /**
   * 获得组件的设计rect
   * @param id 
   */
  public getWorldRect(id: string): { x: number; y: number; width: number; height: number } {
    let pos = this.getWorldPos(id);
    let display = this.getDisplayObjectById(id);
    let rect = display.node.getBoundingBoxToWorld();
    if (!display) {
      rect = cc.rect(0, 0, 0, 0);
    }
    return cc.rect(pos.x, pos.y, rect.width, rect.height);
  }

  private cWorldPosToEWorldPos(cWorld: cc.Vec2) {
    return cc.v2(cc.winSize.width / 2 + cWorld.x, -cWorld.y + cc.winSize.height / 2);
  }

  public getRotateEWordRect(id: string): cc.Rect {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return cc.rect(0, 0, 0, 0)
    }
    let rect = display.rect;
    // console.log("%c Line:436 🌶 rect", "color:#93c0a4", JSON.stringify(rect));
    let nNode = new cc.Node();
    nNode.parent = display.node;
    nNode.setContentSize(cc.size(rect.width, rect.height));
    let nRect = nNode.getBoundingBoxToWorld();
    // console.log("%c Line:436 🍇 rect", "color:#42b983", JSON.stringify(nRect));
    let centerPos = this.cWorldPosToEWorldPos(cc.v2(nRect.xMin, nRect.yMax));
    // console.log("%c Line:438 🥑 cc.rect(centerPos.x, centerPos.y, rect.width, rect.height)", "color:#ffdd4d", cc.rect(centerPos.x, centerPos.y, nRect.width, nRect.height));
    return cc.rect(centerPos.x, centerPos.y, nRect.width, nRect.height);
  }
  // 循环对象，查找对象的 key 是否等于传入的参数
  private traverseObject(obj, needKey) {
    let keys = Object.keys(obj);
    // 循环 keys 数组，判断数组值 == needKey
    for (let key in keys) {
      if (keys[key] === needKey) {
        return true;
      }
    }
    return false;
  }


  // 获取小题更新完成
  public async getSubUpdateFinish(id: string, newProperties?): Promise<void> {
    return new Promise(async (resolve, reject) => {
      let curObj = this.getDisplayObjectById(id) as DisplaySpecialComponent;
      // 确定对应的 key 是哪个
      let _oldData = curObj.getNewProperties();
      let _key = null;
      for (let key in newProperties) {
        if (this.traverseObject(_oldData.newProperties, key)) {
          _key = key;
          break;
        }
      }
      // console.log("%c Line:505 🍏 _key", "color:#b03734", _key);
      await curObj.assembleComponent.changeProperties(_key, newProperties[_key]);
      // console.log("%c Line:485 🥟 _proData222", "color:#3f7cff", newProperties);
      resolve();
    });
  }
  /**
   * 获得在编辑器中，给定坐标后，计算出的组件的设计坐标
   * @param id      
   * @param worldPos  
   */
  public convertEditorWorldPos(id: string, worldPos: cc.Vec2) {
    worldPos = cc.v2(ValueUtils.setOneDecimal(worldPos.x, 1), ValueUtils.setOneDecimal(worldPos.y));
    let display = this.getDisplayObjectById(id);
    let newEWpos = this.getWorldPos(id);
    if (!display) {
      return;
    }
    let rect = display.rect;
    let winsize = cc.winSize;

    // 计算旋转前后的坐标差
    let pos = display.node.convertToWorldSpaceAR(cc.v2(-rect.width / 2, rect.height / 2));


    let oNode = cc.instantiate(display.node);
    oNode.angle = 0;
    oNode.position = display.node.position;
    oNode.parent = display.node.parent;
    oNode.active = false;
    let originPos = oNode.convertToWorldSpaceAR(cc.v2(-rect.width / 2, rect.height / 2));


    let char = this.cWorldPosToEWorldPos(pos).sub(this.cWorldPosToEWorldPos(originPos));
    let oRect = oNode.getBoundingBoxToWorld();
    let nRect = display.node.getBoundingBoxToWorld();

    let originCenterChar = cc.v2(nRect.x - oRect.x, nRect.y - oRect.y);
    let finallyPos = worldPos.add(char).add(cc.v2(-originCenterChar.x, -originCenterChar.y));
    oNode.destroy();

    if (worldPos.x === newEWpos.x) {
      finallyPos.x = worldPos.x;
    }
    if (worldPos.y === newEWpos.y) {
      finallyPos.y = worldPos.y;
    }

    return {
      x: ValueUtils.setOneDecimal(finallyPos.x, 1),
      y: ValueUtils.setOneDecimal(finallyPos.y),
    };
  }
  public getCaptureByNode(id: string) {
    let display = this.getDisplayObjectById(id);
    display.setTagActive(false);
    if (!display) {
      console.error('getCaptureByNode error: display is null');
    }
    let texture = QTEUtils.captureNode(display);
    display.setTagActive(true);
    return texture;
  }



  public localToWorld(yaw: number, localOffset: cc.Vec2): cc.Vec2 {
    return MathUtil.localToWorld(undefined, yaw, localOffset);
  }

  private resetNodePos(id: string, worldPos: cc.Vec2): { x: number; y: number } {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return;
    }

    let rect = display.rect;
    let winsize = cc.winSize;

    let pos = display.node.convertToWorldSpaceAR(cc.v2(-rect.width / 2, rect.height / 2));
    let posY = pos.y;
    let worldPosOld = cc.v2(winsize.width / 2 + pos.x, -posY + winsize.height / 2);
    let newWorldPos = cc.v2(worldPosOld.x - winsize.width / 2, -worldPosOld.y + winsize.height / 2);
    let nodePos = display.node.convertToNodeSpaceAR(newWorldPos);

    let newWorldPos1 = cc.v2(worldPos.x - winsize.width / 2, -worldPos.y + winsize.height / 2);
    let nodePos1 = display.node.convertToNodeSpaceAR(newWorldPos1);

    let char = nodePos1.sub(nodePos);
    // eslint-disable-next-line init-declarations
    let newPos: cc.Vec3;
    if (display.node.parent.name !== "object_layer") {
      let leftPoint = MathUtil.getRotationPoint(char.x, char.y, 0, 0, display.node.angle);
      // display.node.position = display.node.position.add(cc.v3(leftPoint.x, leftPoint.y))
      newPos = display.node.position.add(cc.v3(leftPoint.x, leftPoint.y));
      return { x: newPos.x, y: newPos.y };
    }
    char = newWorldPos1.sub(newWorldPos);
    // display.node.position = display.node.position.add(cc.v3(char.x, char.y))
    newPos = display.node.position.add(cc.v3(char.x, char.y));
    return { x: newPos.x, y: newPos.y };
  }

  /**
   * 获取操作点的设计坐标
   * @param id
   * @param pointId
   */
  public getEditPointWorldPos(id: string, pointId: string): cc.Vec2 {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return cc.v2(0, 0);
    }
    let cutShape = display as DisplayCutShape;
    let pointList = cutShape.getPointList();
    let nFindIndex = pointList.findIndex(value => value.pointData.id === pointId);
    if (nFindIndex !== -1) {
      let point = pointList[nFindIndex];
      let pos = display.node.convertToWorldSpaceAR(cc.v2(point.node.x, point.node.y));
      let posY = pos.y;
      let winsize = cc.winSize;
      let worldPos = cc.v2(winsize.width / 2 + pos.x, -posY + winsize.height / 2);
      // this.setNodePos(id, cc.v2(worldPos.x - 100, worldPos.y))
      return cc.v2(ValueUtils.setOneDecimal(worldPos.x, 1), ValueUtils.setOneDecimal(worldPos.y, 1));
    }
    return cc.v2(0, 0);
  }

  public getNodePos(id: string, newProperties: any): { x: number; y: number } {
    let oldWorldPos = this.getWorldPos(id);
    let posX = oldWorldPos.x;
    // eslint-disable-next-line no-undefined
    if (newProperties.x !== undefined) {
      posX = newProperties.x;
    }
    let posY = oldWorldPos.y;
    // eslint-disable-next-line no-undefined
    if (newProperties.y !== undefined) {
      posY = newProperties.y;
    }
    let worldPos = cc.v2(posX, posY);
    let newPos = this.resetNodePos(id, worldPos);
    if (!newPos) {
      return { x: 0, y: 0 };
    }
    return { x: newPos.x, y: newPos.y };
  }

  private resetEditPointPos(id: string, pointId: string, worldPos: cc.Vec2): cc.Vec3 {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return cc.v3(0, 0);
    }
    let cutShape = display as DisplayCutShape;
    let pointList = cutShape.getPointList();
    let nFindIndex = pointList.findIndex(value => value.pointData.id === pointId);
    if (nFindIndex !== -1) {
      let point = pointList[nFindIndex];
      let winsize = cc.winSize;
      let pos = display.node.convertToWorldSpaceAR(cc.v2(point.node.x, point.node.y));
      let posY = pos.y;
      let worldPosOld = cc.v2(winsize.width / 2 + pos.x, -posY + winsize.height / 2);
      let newWorldPos = cc.v2(worldPosOld.x - winsize.width / 2, -worldPosOld.y + winsize.height / 2);
      let nodePos = display.node.convertToNodeSpaceAR(newWorldPos);

      let newWorldPos1 = cc.v2(worldPos.x - winsize.width / 2, -worldPos.y + winsize.height / 2);
      let nodePos1 = display.node.convertToNodeSpaceAR(newWorldPos1);
      let char = nodePos1.sub(nodePos);
      let newPos = point.node.position.add(cc.v3(char.x, char.y));
      return newPos;
    }
    return cc.v3(0, 0);
  }

  public getEditPointPos(id: string, pId: string, newProperties: any): { x: number; y: number } {
    let oldWorldPos = this.getEditPointWorldPos(id, pId);
    let posX = oldWorldPos.x;
    // eslint-disable-next-line no-undefined
    if (newProperties.x !== undefined) {
      posX = newProperties.x;
    }
    let posY = oldWorldPos.y;
    // eslint-disable-next-line no-undefined
    if (newProperties.y !== undefined) {
      posY = newProperties.y;
    }
    let worldPos = cc.v2(posX, posY);
    let newPos = this.resetEditPointPos(id, pId, worldPos);
    return { x: newPos.x, y: newPos.y };
  }

  /**
   * 获取节点的真是坐标
   * @param id
   */
  public getNodeRealPos(id: string): { x: number; y: number } {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return { x: 0, y: 0 };
    }
    return {
      x: ValueUtils.setOneDecimal(display.node.x, 1),
      y: ValueUtils.setOneDecimal(display.node.y, 1)
    };
  }

  /**
  * 获取节点的真是坐标
  * @param id
  */
  public getNodeCentEdtiorPos(id: string): { x: number; y: number } {
    let display = this.getDisplayObjectById(id);
    if (!display) {
      return { x: 0, y: 0 };
    }
    let pos = display.node.convertToWorldSpaceAR(cc.v2(0, 0));
    // console.log('xu-pos-convertToWorldSpaceAR', pos);
    let posY = pos.y;
    let winsize = cc.winSize;
    let worldPos = cc.v2(winsize.width / 2 + pos.x, -posY + winsize.height / 2);
    return {
      x: worldPos.x,
      y: worldPos.y
    };
  }
  /**
   * @msg     : 根据所给数据显示标号节点
   * @param    {boolean} isView
   * @return   {*}
   */
  public setAllObjectSignalView(isView: boolean, data: SignalData[]) {
    console.log("%c Line:551 🍤 data", "color:#3f7cff", data);
    console.log("%c Line:551 🍋 isView", "color:#3f7cff", isView);

    for (let i = 0; i < data.length; i++) {
      let ob = this.getDisplayObjectById(data[i].id);
      // 判断组内则隐藏
      if (ob && data[i].newExtra && data[i].newExtra.signalId != undefined) {
        if (yy.single.instance(DisplayObjectManager).getDisplayGroupID(data[i].id) !== "-1") {
          ob.setSignalActive(false);
        } else {
          isView && ob.showDisplaySignal(data[i].newExtra.signalId);
          ob.setSignalActive(isView);
          console.log("isCorrect--", data[i].newExtra.isCorrect);
          if (data[i].newExtra.isCorrect) {
            ob.addSignalSelectView(isView);
          }
        }
      }
    }
  }
  onDestoryInstance() {
    super.onDestoryInstance();
    this._dpIndexMap.clear();
  }

}
