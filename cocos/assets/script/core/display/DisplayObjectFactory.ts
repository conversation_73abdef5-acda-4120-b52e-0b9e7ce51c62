import SpineRect from "../../../qte/core/component/spine/SpineRect";
import { SingleBase } from "../../../qte/core/base/SingleBase";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import { CmptType, SpecialComponentSubTypes } from "../../common/EditorEnum";
import Config from "../../Config";
import { LINT_DISPLAY_TYPE, LINT_POS_TYPE } from "../hintline/HintLineDisplay";
import HintLineManager from "../hintline/HintLineManager";
import ComptData, { CutShapeCompData, FormulaProperties, LabelProperties, Shape2Properties, SpineData, SpriteProperties, SvgCompData } from "../proxy/ComptData";
import DataCenterBridge from "../proxy/DataCenterBridge";
import DisplayEditBox from "./base/DisplayEditBox";
import DisplayObject from "./base/DisplayObject";
import DisplayObjectGroup from "./base/DisplayObjectGroup";
import DisplaySpine from "./base/DisplaySpine";
import DisplaySvg from "./base/DisplaySvg";
import SpineManager from "./SpineManager";
import DisplayCutShape from "./base/edit/DisplayCutShape";
import { ComUtil } from "../../common/ComUtils";
import AssetsManager from "../../AssetsManager";
import DisplayCocosAni from "./base/DisplayCocosAni";
import CocosAniManager from "./CocosAniManager";
import CocosAniRect from "../../extends/cocosAni/CocosAniRect";
import DisplaySpecialComponent from "./base/DisplaySpecialComponent";
import ExtraModule from "../proxy/ExtraModule";
import OptSubObjectFactory from "./OptSubObjectFactory";

/**
 * 显示对象创建工厂
 */
export default class DisplayObjectFactory extends SingleBase {
  /** 初始化接口，第一次实例化时会被调用 */
  // eslint-disable-next-line no-empty-function
  public initInstance() { }
  /**
   * 异步增加节点
   * @param objectData
   * @param isAsync
   */
  public getDisplayNodeAsync(objectData: ComptData, isAsync?: boolean): cc.Node {
    let type = objectData.type;
    let node = new cc.Node();
    console.log("%c Line:37 🍩 objectData", "color:#33a5ff", objectData);
    switch (type) {
      case CmptType.SPRITE:
        let _spr = async () => {
          await this.createSprite(objectData, isAsync, node);
        };
        _spr();
        break;
      case CmptType.SHAPE:
        let _shp2 = async () => {
          await this.createShape2(objectData, isAsync, node);
        };
        _shp2();
        break;
      case CmptType.FORMULA:
        let _formula = async () => {
          await this.createFormula(objectData, isAsync, node);
        };
        _formula();
        break;
      case CmptType.LABEL:
        node = this.createEditBox(objectData);
        break;
      case CmptType.SPINE:
        let _spine = async () => {
          await this.createSpine(objectData, isAsync, node);
        };
        _spine();
        break;
      case CmptType.GROUP:
        node = this.createGroup(objectData);
        break;
      case CmptType.SVGSHAPE:
        node = this.createSvgShape(objectData);
        break;
      case CmptType.CUTSHAPE:
        node = this.createShape(objectData);
        break;
      case CmptType.SPECIALCOMPONENT:
        let _speicial = async () => {
          await this.createChildComponent(objectData, node);
        };
        _speicial();
        break;
      case CmptType.OPTIONCOMPONENT:
        let _game = async () => {
          await this.createOptionComp(objectData, node);
        }
        _game();
        break;
      case CmptType.RICHTEXTSPRITE:
        let _rich = async () => {
          await this.createRichTextSprite(objectData, isAsync, node);
        };
        _rich();
        break;
      default:
        break;
    }
    node.name = `cid_${objectData.id}`;
    this.displayInit(node, objectData, isAsync);
    return node;
  }

  /**
   * 根据组件数据获取显示对象
   * @param {ComptData} objectData
   */
  public async getDisplayNode(objectData: ComptData, isAsync?: boolean): Promise<cc.Node> {
    console.log("%c Line:89 🍊 objectData", "color:#e41a6a", objectData);
    let type = objectData.type;
    let node = new cc.Node();
    switch (type) {
      case CmptType.SPRITE:
        await this.createSprite(objectData, isAsync, node);
        break;
      case CmptType.RICHTEXTSPRITE:
        await this.createRichTextSprite(objectData,isAsync,node);
        break;
      case CmptType.SHAPE:
        await this.createShape2(objectData, isAsync, node);
        break;
      case CmptType.FORMULA:
        await this.createFormula(objectData, isAsync, node);
        break;
      case CmptType.LABEL:
        node = this.createEditBox(objectData);
        break;
      case CmptType.SPINE:
        await this.createSpine(objectData, isAsync, node);
        break;
      case CmptType.GROUP:
        node = this.createGroup(objectData);
        break;
      case CmptType.SVGSHAPE:
        node = this.createSvgShape(objectData);
        break;
      case CmptType.CUTSHAPE:
        node = this.createShape(objectData);
        break;
      case CmptType.SPECIALCOMPONENT:
        node = await this.createSpecialLibrary(objectData);
        break;
      case CmptType.OPTIONCOMPONENT:
        node = await this.createOptionComp(objectData);
        break;
      default:
        break;
    }
    node.name = `cid_${objectData.id}`;
    this.displayInit(node, objectData, isAsync);
    return node;
  }
  /**
   * 同步操作下 检查spine 执行数据刷新
   * @param objectData
   */
  // eslint-disable-next-line require-await
  public async spineCheckData(objectData: ComptData) {
    if (objectData.type === CmptType.SPINE) {
      // 等待一帧
      return yy.single.instance(SpineManager).initSpineData(objectData.id, objectData.properties, objectData.spineData);
    }
  }

  /**
   * 增加辅助线
   * @param node
   * @param objectData
   * @param isAsync
   */
  private async displayInit(node: cc.Node, objectData: ComptData, isAsync) {
    let displayObject = node.getComponent(DisplayObject);

    if (displayObject) {
      displayObject.init();
      if (typeof objectData.editable === "undefined") {
        displayObject.editable = true;
      } else {
        displayObject.editable = objectData.editable;
      }
      displayObject.cid = objectData.id;
      if (objectData.cName) {
        displayObject.cName = objectData.cName;
      }
      const { dragable } = objectData;
      displayObject.dragable = dragable === undefined ? true : dragable;
      //@ts-ignore
      displayObject.type = this.getCmptTypeStr(objectData.type);
      let prop = objectData.properties;
      displayObject.node.angle = 0;
      displayObject.node['cAngle'] = 0;
      if (typeof objectData.properties.active !== "undefined" && displayObject.type !== CmptType.SPINE && displayObject.type !== CmptType.SVGSHAPE) {
        displayObject.node.active = objectData.properties.active;
      }
      if (typeof prop.angle !== "undefined") {
        displayObject.node.angle = Number(prop.angle);
        displayObject.node['cAngle'] = Number(prop.angle);
      }
      node.color = new cc.Color().fromHEX(yy.checkValue(prop.color, "#ffffff"));
      node.opacity = yy.checkValue(prop.opacity, 255);
      this.addHint(objectData);
    } else {
      yy.error(`组件id=${objectData.id}，type=${objectData.type}的组件创建失败。`);
    }
    // 初始化时创建 标号,此时隐藏，使用时显示。
    if (isAsync) {
      this.addTageNode(displayObject, isAsync);
      this.addSignalNode(displayObject, isAsync);
    } else {
      await this.addTageNode(displayObject, isAsync);
      await this.addSignalNode(displayObject, isAsync);
    }
    displayObject.initFinish();
  }

  // 添加辅助线
  private addHint(objectData: ComptData) {
    yy.log("====objectData= ==>", objectData);
    yy.single.instance(HintLineManager).addHintLine(Number(objectData.id), false, Number(objectData.properties.y), LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.HORIZONTAL);
    // 横 Top
    yy.single
      .instance(HintLineManager)
      .addHintLine(Number(objectData.id), false, Number(objectData.properties.y) + Number(objectData.properties.height / 2), LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.TOP);
    // 横 Bottom
    yy.single
      .instance(HintLineManager)
      .addHintLine(Number(objectData.id), false, Number(objectData.properties.y) - Number(objectData.properties.height / 2), LINT_DISPLAY_TYPE.HORIZONTAL, LINT_POS_TYPE.BOTTOM);

    // 竖 中线
    yy.single.instance(HintLineManager).addHintLine(Number(objectData.id), false, Number(objectData.properties.x), LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.VERTICAL);
    // 竖 Left
    yy.single
      .instance(HintLineManager)
      .addHintLine(Number(objectData.id), false, Number(objectData.properties.x) - Number(objectData.properties.width / 2), LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.LEFT);
    // 竖 Right
    yy.single
      .instance(HintLineManager)
      .addHintLine(Number(objectData.id), false, Number(objectData.properties.x) + Number(objectData.properties.width / 2), LINT_DISPLAY_TYPE.VERTICAL, LINT_POS_TYPE.RIGHT);
  }

  // 创建sprite
  private async createSprite(objectData: ComptData, isAsync?: boolean, _node?: cc.Node) {
    let prop = objectData.properties as SpriteProperties;
    let node = _node;
    if (!node) {
      node = new cc.Node();
    }
    let disp = node.addComponent(DisplayObject);
    let sp = node.addComponent(cc.Sprite);
    sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    let setProperty = async () => {
      // 属性进行赋值
      node.x = prop.x;
      node.y = prop.y;
      node.scaleX = yy.checkValue(prop.scaleX, 1);
      node.scaleY = yy.checkValue(prop.scaleY, 1);
      node.width = Number(prop.width);
      node.height = Number(prop.height);
      await ComUtil.setMateria(sp, prop.flipType);
    };
    // 待修改为真正的远程资源路径
    let _load = null;

    _load = () =>
      new Promise(resolve => {
        yy.loader.loadRes(prop.texture, cc.Texture2D, (err, texture: cc.Texture2D) => {
          if (err) {
            yy.warn(err);
            return;
          }
          // sp.spriteFrame = new cc.SpriteFrame(texture);
          texture.packable = false;
          ComUtil.addPremultiplyAlpha(texture, sp);
          // @ts-ignore
          sp.___textureUrl = prop.texture;
          disp.initFinished();
          resolve(node);
        });
      });

    if (isAsync) {
      _load();
    } else {
      await _load();
    }
    await setProperty();

    return node;
  }

  // TODO:增加新文本组件创建
  private async createRichTextSprite(objectData: ComptData, isAsync?: boolean, _node?: cc.Node) {
    let prop = objectData.properties as SpriteProperties;
    let node = _node;
    if (!node) {
      node = new cc.Node();
    }
    let disp = node.addComponent(DisplayObject);
    let sp = node.addComponent(cc.Sprite);
    sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    let setProperty = async () => {
      // 属性进行赋值
      node.x = prop.x;
      node.y = prop.y;
      node.scaleX = yy.checkValue(prop.scaleX, 1);
      node.scaleY = yy.checkValue(prop.scaleY, 1);
      node.width = Number(prop.width);
      node.height = Number(prop.height);
      await ComUtil.setMateria(sp, prop.flipType);
    };
    // 待修改为真正的远程资源路径
    let _load = null;

    _load = () =>
      new Promise(resolve => {
        if (prop.texture && prop.texture != "") {
          yy.loader.loadRes(prop.texture, cc.Texture2D, (err, texture: cc.Texture2D) => {
            if (err) {
              yy.warn(err);
              return;
            }
            // sp.spriteFrame = new cc.SpriteFrame(texture);
            texture.packable = false;
            ComUtil.addPremultiplyAlpha(texture, sp);
            // @ts-ignore
            sp.___textureUrl = prop.texture;
            disp.initFinished();
            resolve(node);
          });
        } else {
          // @ts-ignore
          sp.___textureUrl = prop.texture;
          disp.initFinished();
          resolve(node);
        }

      });

    if (isAsync) {
      _load();
    } else {
      await _load();
    }
    await setProperty();

    return node;
  }

  // 创建shape
  private async createShape2(objectData: ComptData, isAsync?: boolean, _node?: cc.Node) {
    let prop = objectData.properties as Shape2Properties;
    let node = _node;
    if (!node) {
      node = new cc.Node();
    }
    let disp = node.addComponent(DisplayObject);
    let sp = node.addComponent(cc.Sprite);
    sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    let setProperty = async () => {
      // 属性进行赋值
      node.x = prop.x;
      node.y = prop.y;
      node.scaleX = yy.checkValue(prop.scaleX, 1);
      node.scaleY = yy.checkValue(prop.scaleY, 1);
      node.width = Number(prop.width);
      node.height = Number(prop.height);
    };
    // 待修改为真正的远程资源路径
    let _load = null;

    _load = () =>
      new Promise(resolve => {
        if (!prop.texture) {
          disp.initFinished();
          resolve(node);
        } else {
          yy.loader.loadRes(prop.texture, cc.Texture2D, (err, texture: cc.Texture2D) => {
            if (err) {
              yy.warn(err);
              return;
            }
            sp.spriteFrame = new cc.SpriteFrame(texture);
            texture.packable = false;

            ComUtil.addPremultiplyAlpha(texture, sp);

            // @ts-ignore
            sp.___textureUrl = prop.texture;
            disp.initFinished();
            resolve(node);
          });
        }
      });

    if (isAsync) {
      _load();
    } else {
      await _load();
    }
    await setProperty();

    return node;
  }

  // 创建Formula
  private async createFormula(objectData: ComptData, isAsync?: boolean, _node?: cc.Node) {
    let prop = objectData.properties as FormulaProperties;
    let node = _node;
    if (!node) {
      node = new cc.Node();
    }
    console.warn(JSON.stringify(prop));
    let displayObject = node.addComponent(DisplayObject);
    let sp = node.addComponent(cc.Sprite);
    sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    let setProperty = () => {
      // 属性进行赋值
      node.x = prop.x;
      node.y = prop.y;
      node.scaleX = yy.checkValue(prop.scaleX, 1);
      node.scaleY = yy.checkValue(prop.scaleY, 1);
      node.width = Number(prop.width);
      node.height = Number(prop.height);
    };
    // 待修改为真正的远程资源路径
    let _load = null;

    _load = () =>
      new Promise(resolve => {
        yy.loader.loadRes(prop.url, cc.Texture2D, (err, texture: cc.Texture2D) => {
          if (err) {
            yy.warn(err);
            return;
          }
          // sp.spriteFrame = new cc.SpriteFrame(texture);

          ComUtil.addPremultiplyAlpha(texture, sp);
          // @ts-ignore
          sp.___textureUrl = prop.url;
          displayObject.initFinished();
          resolve(node);
        });
      });

    if (isAsync) {
      _load();
    } else {
      await _load();
    }
    setProperty();

    return node;
  }

  // 创建label
  private createEditBox(objectData: ComptData): cc.Node {
    let prop = objectData.properties as LabelProperties;
    let node = new cc.Node();
    let editBoxComponent = node.addComponent(DisplayEditBox);
    editBoxComponent.initData(prop);
    yy.log("======>", prop);
    // enditBox initFinished 在 initNode 里面
    return node;
  }

  // 创建spine
  private async createSpine(objectData: ComptData, isAsync?: boolean, _node?: cc.Node): Promise<cc.Node> {
    let prop = objectData.properties;
    let cloneProperties = ValueUtils.clone(prop);
    let spineData = new SpineData();
    let node = _node;
    if (!node) {
      node = new cc.Node();
    }
    let spineNode = new cc.Node("spineNode");
    let spinCmpt = spineNode.addComponent(sp.Skeleton);
    let pngList = [];
    let pngLoadCount = 0;
    let skeletonData = new sp.SkeletonData();
    const displaySpine = node.addComponent(DisplaySpine);
    spineNode.addComponent(SpineRect);
    spineData.atlas = objectData.spineData.atlas;
    spineData.images = objectData.spineData.images;
    spineData.skeleton = objectData.spineData.skeleton;
    node.addChild(spineNode);
    node.x = cloneProperties.x;
    node.y = cloneProperties.y;
    node.width = Number(cloneProperties.width);
    node.height = Number(cloneProperties.height);
    let _atlas = new Promise((resolve, reject) => {
      yy.loader.loadRes(spineData.atlas, cc.TextAsset, (error, atlas: cc.TextAsset) => {
        if (error) {
          yy.error(error);
          reject(error);
        }
        resolve(atlas);
      });
    }) as Promise<cc.TextAsset>;
    let _json = new Promise((resolve, reject) => {
      yy.loader.loadRes(spineData.skeleton, cc.JsonAsset, (jsonError, json: cc.JsonAsset) => {
        if (jsonError) {
          yy.error(jsonError);
          reject(jsonError);
        }
        resolve(json);
      });
    }) as Promise<cc.JsonAsset>;
    let _image = new Promise((resolve, reject) => {
      let imageObj = new Map();
      const loadImage = (url: string, index: number) => {
        yy.loader.loadRes(url, cc.Texture2D, (pngErr: any, png: cc.Texture2D) => {
          if (!pngErr) {
            png.setPremultiplyAlpha(true);
            imageObj.set(index, png);
            png.setPremultiplyAlpha && png.setPremultiplyAlpha(true);
            pngLoadCount++;
            if (pngLoadCount >= spineData.images.length) {
              for (let i = 0; i < spineData.images.length; i++) {
                pngList.push(imageObj.get(i));
              }
              resolve(pngList);
            }
          } else {
            yy.error(pngErr);
            reject(pngErr);
          }
        });
      };
      for (let im = 0; im < spineData.images.length; im++) {
        // eslint-disable-next-line no-loop-func
        loadImage(spineData.images[im], im);
      }
    }) as Promise<cc.Texture2D[]>;
    if (isAsync) {
      Promise.all([_atlas, _json, _image]).then(value => {
        skeletonData.atlasText = value[0].text;
        skeletonData.skeletonJson = value[1].json;
        skeletonData.textures = value[2];
        skeletonData.textureNames = spineData.imageNames;
        spinCmpt.skeletonData = skeletonData;
        yy.single.instance(SpineManager).initSpineData(objectData.id, cloneProperties, spineData);
      });
    } else {
      await Promise.all([_atlas, _json, _image]).then(value => {
        skeletonData.atlasText = value[0].text;
        skeletonData.skeletonJson = value[1].json;
        skeletonData.textures = value[2];
        skeletonData.textureNames = spineData.imageNames;
        spinCmpt.skeletonData = skeletonData;
      });
    }
    // spine initFinished 在 initSpineData 里面
    return node;
  }
  /**
   * 创建svg
   * @param objectData
   */
  private createSvgShape(objectData: ComptData): cc.Node {
    let _prop = objectData.properties as SvgCompData;
    let _svgNode = new cc.Node();
    let _compNode: DisplaySvg = _svgNode.addComponent(DisplaySvg);

    if (CC_DEBUG) {
      cc.resources.load("svg/test4", cc.TextAsset, (error, text: cc.TextAsset) => {
        if (error) {
          return;
        }
        // _prop.textAsset = text;
        _compNode.onInit(_prop);
        _compNode.scheduleOnce(() => {
          let _rect = _compNode.getShapeRect();
          _svgNode.setPosition(_prop.x, _prop.y);
          let _svgSize = cc.size(_rect.width, _rect.height);
          _svgNode.setContentSize(_svgSize);
          _compNode.setSvgNodeSize(_svgSize);
          _compNode.createVertexBtn();
          _compNode.initFinished();
        }, 0);
      });
    } else {
      _compNode.onInit(_prop);
      _compNode.scheduleOnce(() => {
        let _rect = _compNode.getShapeRect();
        _svgNode.setPosition(_prop.x, _prop.y);
        let _svgSize = cc.size(_prop.width, _prop.height);
        _svgNode.setContentSize(_prop.width, _prop.height);
        _compNode.setSvgNodeSize(_svgSize);
        _compNode.createVertexBtn();
        _compNode.initFinished();
      }, 0);
    }
    return _svgNode;
  }

  // 创建图形
  private createShape(objectData: ComptData): cc.Node {
    let node = new cc.Node();
    let editComponent = node.addComponent(DisplayCutShape);
    let data = yy.cloneValues(objectData.properties) as CutShapeCompData;
    node.x = objectData.properties.x;
    node.y = objectData.properties.y;
    node.width = objectData.properties.width;
    node.height = objectData.properties.height;
    editComponent.initData(data, objectData.subType);
    editComponent.initFinished();
    return node;
  }

  // 创建组合
  private createGroup(objectData: ComptData): cc.Node {
    let prop = objectData.properties;
    let node = new cc.Node();
    let displayObjectGroup = node.addComponent(DisplayObjectGroup);
    node.x = prop.x;
    node.y = prop.y;
    node.width = prop.width;
    node.height = prop.height;
    displayObjectGroup.initFinished();
    return node;
  }

  private async createChildComponent(objectData: ComptData, node: cc.Node) {
    let subType = objectData.subType.toString() as any;
    let cmpt = node.addComponent(DisplaySpecialComponent);
    cmpt.dragable = objectData.dragable;
    cmpt.cid = objectData.id;
    cmpt.editable = objectData.editable;
    node.x = objectData.properties.x;
    node.y = objectData.properties.y;
    node.width = objectData.properties.width;
    node.height = objectData.properties.height;
    let bundel = yy.single.instance(AssetsManager).getBundel("qte");
    let baseComponent = await assemble.factory.createObject(subType, objectData, bundel);
    node.addChild(baseComponent.node);
    cmpt.assembleComponent = baseComponent;
    console.log(objectData);
    cmpt.initComponent(objectData);
    cmpt.initFinished();
  }

  /** 创建通用组件 */
  private async createSpecialLibrary(objectData: ComptData, _node?: cc.Node): Promise<cc.Node> {
    let node = _node ? _node : new cc.Node();
    let subType = objectData.subType.toString() as any;
    let bundel = yy.single.instance(AssetsManager).getBundel("qte");
    console.log("%c Line:505 🍫 bundel", "color:#3f7cff", bundel);
    let baseComponent = await assemble.factory.createObject(subType, objectData, bundel);
    node.addChild(baseComponent.node);
    node.x = objectData.properties.x;
    node.y = objectData.properties.y;
    node.width = objectData.properties.width;
    node.height = objectData.properties.height;
    let cmpt = node.addComponent(DisplaySpecialComponent);
    cmpt.dragable = objectData.dragable;
    cmpt.cid = objectData.id;
    cmpt.editable = objectData.editable;
    cmpt.assembleComponent = baseComponent;
    console.log(objectData);
    cmpt.initComponent(objectData);
    cmpt.initFinished();
    return node;
  }

  private async createOptionComp(objectData: ComptData, _node?: cc.Node): Promise<cc.Node> {
    console.log("%c Line:525 🍖 objectData", "color:#f5ce50", objectData);
    // 根据加载的 bundle 去加载 bundle 内部组件。
    let node = _node ? _node : new cc.Node();
    let subType = objectData.subType.toString() as any;
    let bundel = yy.single.instance(AssetsManager).getBundel((window as any)._$store.state.template.bundleName);
    let baseComponent = await OptSubObjectFactory.getInstance().createObject(subType, objectData, bundel);
    console.log("%c Line:528 🌰 baseComponent", "color:#e41a6a", baseComponent);
    node.addChild(baseComponent.node);
    node.x = objectData.properties.x;
    node.y = objectData.properties.y;
    node.width = objectData.properties.width;
    node.height = objectData.properties.height;
    let cmpt = node.addComponent(DisplaySpecialComponent);
    cmpt.dragable = objectData.dragable;
    cmpt.cid = objectData.id;
    cmpt.editable = objectData.editable;
    cmpt.assembleComponent = baseComponent;
    console.log(objectData);
    cmpt.initComponent(objectData);
    cmpt.initFinished();
    return node;
  }

  // 获取一个组合对象
  public getDisplayGroupNode(id: string = "1"): cc.Node {
    let data = new ComptData();
    data.id = id;
    data.type = CmptType.GROUP;
    return this.createGroup(data);
  }

  /** 根据枚举获取类型字符串 */
  public getCmptTypeStr(type: CmptType): string {
    switch (type) {
      case CmptType.COCOSANI:
        return "cocosAni";
      case CmptType.SPRITE:
        return "sprite";
      case CmptType.RICHTEXTSPRITE:
        return "richTextSprite";
      case CmptType.FORMULA:
        return "formula";
      case CmptType.SPINE:
        return "spine";
      case CmptType.LABEL:
        return "label";
      case CmptType.GROUP:
        return "group";
      case CmptType.SVGSHAPE:
        return "svgShape";
      case CmptType.CUTSHAPE:
        return "cutShape";
      case CmptType.SPECIALCOMPONENT:
        return "specialComponent";
      case CmptType.OPTIONCOMPONENT:
        return "optionComponent";
      case CmptType.SHAPE:
        return "shape";
      default:
        break;
    }
  }

  specialComponentPath(subType: string) {
    let path = "";
    switch (subType) {
      case SpecialComponentSubTypes.CmptRecord:
        path = "assemble/record/res/prefabs/entry";
        break;
      case SpecialComponentSubTypes.KeyBord:
        path = "assemble/keyboard/prefabs/keyboard";
        break;
      case SpecialComponentSubTypes.KeyBordEnglish:
        path = "assemble/keyboardEnglish/prefabs/keyboardEnglish";
        break;
      case SpecialComponentSubTypes.Write:
        path = "assemble/write/prefabs/write";
        break;
      case SpecialComponentSubTypes.MatchBoard:
        path = "assemble/matchBoard/prefab/matchBoard";

        break;
      case SpecialComponentSubTypes.Counter:
        path = "assemble/countCompoent/prefabs/counter";

        break;
      case SpecialComponentSubTypes.Clock:
        path = "assemble/clock/prefabs/clockRoot";

        break;
      case SpecialComponentSubTypes.Tangram:
        path = "assemble/tangram/prefab/tangram";

        break;
      case SpecialComponentSubTypes.Speaker:
        path = "assemble/speaker/prefabs/speaker";

        break;
      case SpecialComponentSubTypes.Brush:
        path = "assemble/drawControl/prefab/drawControl";
        break;
      default:
        break;
    }
    return path;
  }

  /**
   * 添加节点标签
   * @param display
   */
  private async addTageNode(display: DisplayObject, isAsync?: boolean) {

    let tagStr = yy.single.instance(DataCenterBridge).getComponetTagName(display.cid);
    let extraData = yy.single.instance(DataCenterBridge).getComponentMap()[display.cid].extra;

    let root = new cc.Node();
    root.height = 30;
    root.anchorX = 0;
    root.anchorY = 0.5;
    root.name = "tag_Node";
    // 创建layout
    let layout = root.addComponent(cc.Layout);
    layout.type = cc.Layout.Type.HORIZONTAL;
    layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
    // 添加widget
    let widget = root.addComponent(cc.Widget);
    widget.isAlignLeft = true;
    widget.isAlignTop = true;
    widget.left = 5;
    widget.top = -35;
    widget.alignMode = cc.Widget.AlignMode.ALWAYS;
    // 添加背景
    let sprite = root.addComponent(cc.Sprite);

    sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;

    let _bgLoad = () =>
      new Promise<void>((resolve, reject) => {
        cc.loader.loadRes("img/bg", cc.Texture2D, (err, texture: cc.Texture2D) => {
          if (err) {
            yy.warn(err);
            reject(err);
            return;
          }
          // sprite.spriteFrame = res;
          ComUtil.addPremultiplyAlpha(texture, sprite);
          resolve();
        });
      });
    if (isAsync) {
      _bgLoad();
    } else {
      await _bgLoad();
    }

    // 创建label
    /* eslint-disable @typescript-eslint/camelcase */
    /* eslint-disable camelcase */
    let label_node = new cc.Node();
    let lb = label_node.addComponent(cc.Label);
    label_node.anchorX = 0;
    label_node.anchorY = 0.5;
    label_node.color = new cc.Color().fromHEX("#63390E");
    lb.useSystemFont = true;
    lb.fontFamily = "Arial";
    lb.fontSize = 20;
    lb.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
    ComUtil.setLabelBlend(lb);
    // 添加到跟节点
    display.node.addChild(root);
    root.addChild(label_node);
    label_node.position = cc.v3(0, -14);
    root.group = "edge";
    display.showDisplayTag(tagStr, extraData);
    console.log("showDisplayTag1");
  }
  /**
   * @msg     : 设置标号
   * @param    {DisplayObject} display
   * @param    {boolean} isAsync
   * @return   {*}
   */
  private async addSignalNode(display: DisplayObject, isAsync?: boolean) {
    let tagStr = yy.single.instance(DataCenterBridge).getComponetTagName(display.cid);
    console.log("%c Line:674 🥕 tagStr", "color:#7f2b82", tagStr);
    let root = new cc.Node();
    root.height = 30;
    root.anchorX = 0;
    root.anchorY = 0.5;
    root.name = "signs_Node";
    // 创建layout
    let layout = root.addComponent(cc.Layout);
    layout.type = cc.Layout.Type.HORIZONTAL;
    layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
    // 添加widget
    let widget = root.addComponent(cc.Widget);
    widget.isAlignLeft = true;
    widget.isAlignTop = true;
    widget.left = 5;
    widget.top = -35;
    widget.alignMode = cc.Widget.AlignMode.ALWAYS;
    // 添加背景
    let sprite = root.addComponent(cc.Sprite);

    sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;

    let _bgLoad = () =>
      new Promise<void>((resolve, reject) => {
        cc.loader.loadRes("img/bg", cc.Texture2D, (err, texture: cc.Texture2D) => {
          if (err) {
            yy.warn(err);
            reject(err);
            return;
          }
          // sprite.spriteFrame = res;
          ComUtil.addPremultiplyAlpha(texture, sprite);
          resolve();
        });
      });
    if (isAsync) {
      _bgLoad();
    } else {
      await _bgLoad();
    }

    // 创建label
    /* eslint-disable @typescript-eslint/camelcase */
    /* eslint-disable camelcase */
    let label_node = new cc.Node();
    let lb = label_node.addComponent(cc.Label);
    label_node.anchorX = 0;
    label_node.anchorY = 0.5;
    label_node.color = new cc.Color().fromHEX("#63390E");
    lb.useSystemFont = true;
    lb.fontFamily = "Arial";
    lb.fontSize = 20;
    lb.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
    ComUtil.setLabelBlend(lb);
    // 添加到跟节点
    display.node.addChild(root);
    root.addChild(label_node);
    label_node.position = cc.v3(0, -14);
    root.group = "default";
    display.showDisplaySignal(0);
  }

}
