/*
 * @FilePath     : /cocos/assets/script/core/display/GameSubObjectFactory.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import {OptionComponentSubTypes } from "../../common/EditorEnum";
import BaseComponent from "../../../qte/assemble/core/BaseComponent";

export default class OptSubObjectFactory {
    /** 单例对象 */
    private static _instance: OptSubObjectFactory = null;

    public bundle: cc.AssetManager.Bundle;

    /** 获取单例对象 */
    public static getInstance(): OptSubObjectFactory {
        if (!this._instance) {
            this._instance = new OptSubObjectFactory();
        }
        return this._instance;
    }

    public static destroyInstance(): void {
        this._instance = null;
    }

    /**
     * 创建组件
     * @param type
     * @param data
     * @param bundle
     * @param rootPath 
     */
    public async createObject(
        type: OptionComponentSubTypes,
        data: any,
        bundle: cc.AssetManager.Bundle,
    ): Promise<cc.Component> {
        this.bundle = bundle;
        console.log("%c Line:41 🍺 bundle", "color:#6ec1c2", bundle);
        let cmpt: any = null;
        let node: cc.Node = null;
        node = await this.createOptionComp(bundle);
        cmpt = node.getComponent(BaseComponent);
        return cmpt;
    }
    createOptionComp(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
        console.log("%c Line:62 🍇 bundle", "color:#6ec1c2", bundle);
        return new Promise<cc.Node>((reslove, reject) => {
            let path = "prefabs/optComp";
            bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
                if (err) {
                    console.log("%c Line:65 🍏 err", "color:#2eafb0", err);
                    return;
                }
                let node = cc.instantiate(assets);
                node.active = true;
                reslove(node);
            });
        }).catch(err => {
            console.log("%c Line:75 🍷 err", "color:#3f7cff", err);
            return null;
        });
    }
}