import { SingleBase } from "../../../qte/core/base/SingleBase";
import { CmptLayer } from "../../common/EditorEnum";
import ComptData from "../proxy/ComptData";
import DisplayObject from "./base/DisplayObject";
import DisplayObjectGroup from "./base/DisplayObjectGroup";
import DisplayObjectFactory from "./DisplayObjectFactory";
import DisplayObjectManager from "./DisplayObjectManager";

/**
 * 模板解释器
 * 负责初始化时
 */
export default class TemplateInterpreter extends SingleBase {
  // 添加显示对象回调函数
  private _addObjFunc: (obj: cc.Node, layer: CmptLayer) => void;
  public get addObjFunc(): (obj: cc.Node, layer: CmptLayer) => void {
    return this._addObjFunc;
  }

  /** 注册添加背景和显示对象接口 */
  public registerLayer(addObjFunc: (obj: cc.Node, layer: CmptLayer) => void): void {
    this._addObjFunc = addObjFunc;
  }

  /**
   * 根据模板数据创建显示对象
   * @param {Map<number, ComptData>} comptsData 组件属性数据
   * @param {number[]} comptIds 组件id数组，保存层级关系
   */
  public async generate(comptsData: Map<string, ComptData>, comptIds: any[]) {
    let factory = yy.single.instance(DisplayObjectFactory);
    let manager = yy.single.instance(DisplayObjectManager);
    qte.displayObjectManager = manager;
    console.log("#------comptsData->", comptsData);
    console.log("#------comptIds->", comptIds);
    // 遍历组件树构建场景
    for (let i = 0; i < comptIds.length; i++) {
      let obj = comptIds[i];
      if (typeof obj === "string") {
        let com = comptsData[obj];
        // eslint-disable-next-line no-await-in-loop
        let node = await factory.getDisplayNode(com, true);
        console.log("#------com->", com);
        this._addObjFunc(node, CmptLayer.OBJECT_LAYER);
        manager.addDisplayObject(com.id, node.getComponent(DisplayObject));
        // eslint-disable-next-line no-await-in-loop
        await factory.spineCheckData(com);
      } else {

        let com = comptsData[obj.id];
        let node: cc.Node = null;
        if (com.type == "group") {
          node = await factory.getDisplayNode(com, true);
          this._addObjFunc(node, CmptLayer.OBJECT_LAYER);
          let groupCmpt = node.getComponent(DisplayObjectGroup);
          let nodeGroup = groupCmpt.node;
          manager.addDisplayObject(com.id, groupCmpt);
          for (let id of obj.subIds) {
            let subCom: ComptData = comptsData[id];
            if (!subCom.cName) {
              let subNode = await factory.getDisplayNode(subCom, true);
              nodeGroup.addChild(subNode);
              groupCmpt.addSubObject(id, subNode.getComponent(DisplayObject));
              manager.addDisplayObject(id, subNode.getComponent(DisplayObject));
              // eslint-disable-next-line no-await-in-loop
              await factory.spineCheckData(subCom);
            }
          }

        } else {
          node = await factory.getDisplayNode(com, true);
          console.log("#--222----com->", com);
          this._addObjFunc(node, CmptLayer.OBJECT_LAYER);
          manager.addDisplayObject(com.id, node.getComponent(DisplayObject));
          // eslint-disable-next-line no-await-in-loop
          await factory.spineCheckData(com);
        }
        if (com.childComponents && com.childComponents[0]) {
          if (!node) {
            console.error("插槽父节点为空");
          }
          let disCmpt = node.getComponent(DisplayObject);
          let nodeSub = await factory.getDisplayNode(com.childComponents[0], true);
          nodeSub.parent = node;
          disCmpt.addChildObject(com.childComponents[0].id, nodeSub.getComponent(DisplayObject));
          manager.addDisplayObject(com.childComponents[0].id, nodeSub.getComponent(DisplayObject));
        }

      }
    }
  }

  // check IoadFinish
  checkCompLoadFinish(comptsData: Map<string, ComptData>, comptIds: any[]) {
    let manager = yy.single.instance(DisplayObjectManager);
    let loadFinshedNum = 0;
    for (let i = 0; i < comptIds.length; i++) {
      let obj = comptIds[i];
      if (typeof obj === "string") {
        let idObj = manager.getDisplayObjectById(obj);
        if (idObj && idObj.isInitFinished) {
          loadFinshedNum++;
        }
      } else {
        let com = comptsData[obj.id];
        if (com.type == "group") {
          let isLoadSubComFinish = 0;
          let idGObj = manager.getDisplayObjectById(obj.id);
          if (idGObj && idGObj.isInitFinished) {
            isLoadSubComFinish++;
          }
          for (let id of obj.subIds) {
            let subCom: ComptData = comptsData[id];
            if (!subCom.cName) {
              let idSObj = manager.getDisplayObjectById(id);
              if (idSObj && idSObj.isInitFinished) {
                isLoadSubComFinish++;
              }
            }
          }
          if (isLoadSubComFinish >= obj.subIds.length + 1) {
            loadFinshedNum++;
          }
        } else {
          let idObj = manager.getDisplayObjectById(obj);
          if (idObj && idObj.isInitFinished) {
            loadFinshedNum++;
          }
        }
      }
    }
    if (loadFinshedNum >= comptIds.length) {
      return true
    }
    return false;
  }

  // check IoadFinish
  initAllComponentsFinish(comptsData: Map<string, ComptData>, comptIds: any[]) {
    let manager = yy.single.instance(DisplayObjectManager);
    for (let i = 0; i < comptIds.length; i++) {
      let obj = comptIds[i];
      if (typeof obj === "string") {
        let idObj = manager.getDisplayObjectById(obj);
        idObj.initAllComponentsFinish();
      } else {
        let com = comptsData[obj.id];
        if (com.type == "group") {
          let idGObj = manager.getDisplayObjectById(obj);
          idGObj.initAllComponentsFinish();
        } else {
          let idObj = manager.getDisplayObjectById(obj);
          idObj.initAllComponentsFinish();
        }
      }
    }
  }

}
