/*
 * @FilePath     : /cocos/assets/script/core/display/base/DisplayGameBaseComponent.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

const { ccclass, property } = cc._decorator;

@ccclass
export default abstract class DisplayGameBaseComponent extends cc.Component {
    /**
     * 初始化
     * @param {data: {data: any, engine: anys}} 
     */
    public abstract initComponent(data?: any);
    /**
     * 属性更新
     * @param key 属性key
     * @param data 参数 
     */
    public abstract changeProperties(key: string, data: any);


    /** 宽高变化后触发的函数 */
    public onSizeChange(rect: cc.Size): void {

    }
}
