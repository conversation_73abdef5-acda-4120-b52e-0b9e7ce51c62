import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { CocosAniData, SpineData } from "../../proxy/ComptData";
import DisplayObject from "./DisplayObject";

/**
 * 显示对象基类
 */
const { ccclass } = cc._decorator;

@ccclass
export default class DisplayCocosAni extends DisplayObject {
    // 锚点位置
    private _anchor: cc.Vec2 = null;

    public set anchor (anchor: cc.Vec2) {
        this._anchor = anchor;
    }
    public get anchor (): cc.Vec2 {
        return this._anchor;
    }
    /** size作为初始size */
    private _size: cc.Size = new cc.Size(200,200);
    public set size (size: cc.Size) {
        this._size = size;
    }
    public get size (): cc.Size {
        return this._size;
    }
    // animationNode
    private _animationCmpt: cc.Animation = null;
    public get animationCmpt (): cc.Animation {
        return this._animationCmpt;
    }
    // 播放动画队列
    private _actionList = [];
    public get actionList (): string[] {
        return this._actionList;
    }

    /** 偏移坐标 */
    private _offet: cc.Vec2 = cc.v2(0, 0);
    public get offset (): cc.Vec2 {
        return this._offet;
    }
    // 播放索引
    private _playIndex = 0;
    
    /*
     * 循环
     */
    private _loop: boolean = true;
    public get loop (): boolean {
        return this._loop;
    }
    // 时间缩放率
    private _timeScale: number = 1;
    public get timeScale (): number {
        return this._timeScale;
    }
    
    // cocosAni数据
    public cocosAniData: CocosAniData = null;
    /**
     * 添加事件监听
     * @param cmpt 
     */
    public addListener (cmpt: cc.Animation) {
        this._animationCmpt = cmpt;
        // TODO
        // 监听播放完成
        //this._animationCmpt.setCompleteListener(this.completeListener.bind(this));
    }

    /**
     *重置位置
     */
     resetCocosAniPos () {
        let offestX = (this._anchor.x - 0.5) * this._size.width * this._animationCmpt.node.scaleX;
        let offestY = (this._anchor.y - 0.5) * this._size.height * this._animationCmpt.node.scaleY;
        this._animationCmpt.node.setPosition(cc.v3(offestX, offestY));
        this._offet = cc.v2(offestX, offestY);
    }

    refreshRectPoint () {
        super.refreshRectPoint();
        let size = this.node.getContentSize();
        let percentW = size.width / this._size.width;
        let percentH = size.height / this._size.height;
        let spineNode = this._animationCmpt.node;
        
        spineNode.scaleX = percentW;
        spineNode.scaleY = percentH;
        this.resetCocosAniPos();
    }

    /** 设置播放列表 */
    public setAnimation (actionList: string[]) {
        this._actionList = actionList;
        this.step();
    }
    /** 设置循环 */
    public setLoop (loop: boolean) {
        if (this._loop === loop) {
            return;
        }
        this._loop = loop;
        if (this._loop) {
            this.step();
        }
    }

    /** 设置TimeScale */
    public setTimeScale (value: number) {
        this._timeScale = Math.max(value, 0);
        // this._animationCmpt.timeScale = this._timeScale;
    }

    // 播放下一个
    private step () {
        if (this.actionList.length <= 0 || this._playIndex >= this.actionList.length) {
            return;
        }
        let animName = this.actionList[this._playIndex];
        if (animName === "") {
            this.completeListener();
            return;
        }

        //this._animationCmpt.setAnimation(0, animName, false);
    }

    /**
     * 播放完事件
     */
    private endListener () {
        cc.log("");
    }

    /**
     * 循环一次播放完成事件setCompleteListener
     */
    private completeListener () {
        // 播放加一
        this._playIndex++;
        if (!this.checkActionList()) {
            this._playIndex = 0;
           // this._animationCmpt.animation = "";
            return;
        }
        // 播放完了 并且不是循环播放
        if (this._playIndex > this.actionList.length - 1) {
            this._playIndex = 0;
            if (!this.loop) {
                return;
            }  
        }
        // 播放下一个
        this.step();
    }

    /**
     * 检测动画列表是否合法
     */
    private checkActionList (): boolean {
        for (let value of this.actionList) {
            if (value !== "") {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取spine某个动作的持续时间,
     * @param skeleton spine
     * @param animaitonName 动作名称
     */
    public getSpineAnimationTime (animaitonName: string) : number {
        let duration = 0;
        // @ts-ignore
        let attachUtil = this._animationCmpt.attachUtil;
        if (!attachUtil) {
            return duration;
        }
        let animations = attachUtil._skeleton.data.animations;
        for (let i = 0; i < animations.length; i++) {
            let animation = animations[i]
            if (animation.name === animaitonName) {
                duration = yy.checkValue(animation.duration, 0);
                break;
            }
        }
        return ValueUtils.setOneDecimal(duration, 2);
    }

    /**
     * 重写获取属性方法
     */
    public getNewProperties (): any {
        let superData = super.getNewProperties();
        // eslint-disable-next-line dot-notation
        superData.extra["cocosAniData"] = this.cocosAniData;
        return superData;
    }
}