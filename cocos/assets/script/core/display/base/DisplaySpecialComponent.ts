// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { cloneDeep } from "lodash";
import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { SpecialComponentSubTypes } from "../../../common/EditorEnum";
import { UpdatePropertiesCommand } from "../../command/commond";
import CommandFactory from "../../command/operate/CommandFactory";
import ComptData from "../../proxy/ComptData";
import DisplayObject from "./DisplayObject";

const { ccclass, property } = cc._decorator;

@ccclass
export default class DisplaySpecialComponent extends DisplayObject {
  // LIFE-CYCLE CALLBACKS:
  public assembleComponent: any;
  // onLoad () {}
  /** 初始size */
  private _size: cc.Size = null;
  public set size(size: cc.Size) {
    this._size = size;
  }
  public get size(): cc.Size {
    return this._size;
  }

  private _subType: SpecialComponentSubTypes;

  get subType(): SpecialComponentSubTypes {
    return this._subType;
  }
  set subType(subType: SpecialComponentSubTypes) {
    this._subType = subType;
  }

  private customDataProperties: any = {};

  /** 缓存组件缩放 */
  public componentScale: { x: number; y: number } = { x: 0, y: 0 };

  public customDataPropertiesForkey(key: string) {
    return this.customDataProperties[key];
  }
  public getNewProperties(): any {
    let superData = super.getNewProperties();
    for (let key in this.customDataProperties) {
      if (typeof (superData.newProperties[key]) == "undefined") {
        superData.newProperties[key] = this.customDataProperties[key];
      }
    }
    superData.extra['subType'] = this.subType;
    return superData;
  }
  public initAllComponentsFinish(): void {
    if (this.assembleComponent && this.assembleComponent) {
      this.assembleComponent.initAllComponentsFinish();
    }
  }

  /** 初始化组件 */
  public initComponent(cmptData: ComptData) {
    console.log(cmptData, "dengchao initComponent");
    if (typeof cmptData.properties == "object") {
      this.customDataProperties = JSON.parse(
        JSON.stringify(cmptData.properties),
      );
    }

    const newData = cloneDeep(cmptData);
    (newData as any).isEditor = true;
    (newData as any).updateComponentProperties = this.updateComponentProperties.bind(
      this,
    );
    (this.assembleComponent as any).initComponent(newData);
    (this.assembleComponent as any).onSizeChange = (data: cc.Size) => {
      // 组件位置，大小需要更新
      console.log("设置display node属性", data, this.node);
      this.node.width = data.width;
      this.node.height = data.height;
      this.size = (this.assembleComponent as any).node.getContentSize();
    };
    this.size = (this.assembleComponent as any).node.getContentSize();

    if (cmptData.subType) {
      //@ts-ignore
      this.subType = cmptData.subType;
    }
    this.refreshRectPoint();
  }

  public changeProperties(key: string, value: any): any {
    let oldVaue: any = null;
    oldVaue = this[key];
    if (typeof value == "object") {
      this.customDataProperties[key] = JSON.parse(JSON.stringify(value));
    } else {
      this.customDataProperties[key] = value;
    }

    (this.assembleComponent as any).changeProperties(key, value);
    return oldVaue;
  }

  updateComponentProperties(properties: any) {
    console.warn({
      id: this.cid,
      newProperties: properties,
    });
    yy.single.instance(CommandFactory).execute(UpdatePropertiesCommand, {
      id: this.cid,
      newProperties: properties,
    });
    (window as any)._$store.commit("updateComponentProperties", {
      id: this.cid,
      newProperties: properties,
    });
  }

  refreshRectPoint() {
    super.refreshRectPoint();
    let rootSize = this.node.getContentSize();
    if (this._subType == SpecialComponentSubTypes.H5Label) { // 文本组件不需要设置scale
      return;
    }
    if (this.assembleComponent as any) {
      (this.assembleComponent as any).node.scaleX = ValueUtils.setOneDecimal(
        rootSize.width / this.size.width,
        2,
      );
      (this.assembleComponent as any).node.scaleY = ValueUtils.setOneDecimal(
        rootSize.height / this.size.height,
        2,
      );

      this.componentScale.x = (this.assembleComponent as any).node.scaleX;
      this.componentScale.y = (this.assembleComponent as any).node.scaleY;
    }
  }

  start() { }
}
