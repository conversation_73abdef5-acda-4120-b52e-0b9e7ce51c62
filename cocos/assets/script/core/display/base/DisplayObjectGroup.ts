import MathUtil from "../../../../qte/core/utils/MathUtil";
import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import DisplayObject from "./DisplayObject";

/**
 * 显示对象Group
 */
const { ccclass } = cc._decorator;
@ccclass
export default class DisplayObjectGroup extends DisplayObject {

    /** 分组id数组 */
    private _groupIds: string[] = [];
    public get groupIds (): string[] {
        // 不允许外部修改
        return ValueUtils.clone(this._groupIds);
    }

    private _groupSubs: DisplayObject[] = [];
    public get groudSubs (): DisplayObject[] {
        return this._groupSubs;
    }

    // 组件宽高
    public get rect (): cc.Rect {
        return new cc.Rect(this.node.x,
            this.node.y,
            this.node.width,
            this.node.height);
    }

    // 缓存子节点id
    private _oldSubDisplay = [];

    /**
     * Group显示对象显示边框
     */
    public showEdge (): void {
        super.showEdge();
    }

    public showCenterEdge (pos: cc.Vec3, color: cc.Color): void {
        let edgeNode = new cc.Node();
        edgeNode.addComponent(cc.Graphics);
        edgeNode.name = "edge_node_node";
        this.node.parent.addChild(edgeNode);
        let graphics = edgeNode.getComponent(cc.Graphics);
        graphics.strokeColor = color;
        graphics.clear();
        let centerPos = pos;
        graphics.moveTo(centerPos.x - 10, centerPos.y - 10);
        graphics.lineTo(centerPos.x - 10, centerPos.y + 10);
        graphics.lineTo(centerPos.x + 10, centerPos.y + 10);
        graphics.lineTo(centerPos.x + 10, centerPos.y - 10);
        graphics.lineTo(centerPos.x - 10, centerPos.y - 10);
        graphics.stroke();
    }

    /**
     * 添加子组件
     * @param {number} id 组件id
     */
    public addSubObject (id: string, subDisplay: DisplayObject): void {
        if (this._groupIds.indexOf(id) >= 0) {
            yy.warn(`id = ${id}的子组件以及添加过了.`);
            return;
        }
        this._groupIds.push(id);
        this._groupSubs.push(subDisplay);
        this.showSubCmptTag(id, false);
        // TODO：添加子组件时，隐藏。
        this.showSubCmptSignal(id,false);
        
    }

    /**
     * 删除子组件
     * @param {number} id 组件id
     */
    public removeSubObject (id: string) {
        let index = this._groupIds.indexOf(id);
        let subNodeIds = -1;
        if (index < 0) {
            yy.warn(`id = ${id}的子组件不存在.`);
            return;
        }
        for (let i = 0; i < this._groupSubs.length - 1; i++) {
            if (this._groupSubs[i].cid === id) {
                subNodeIds = i;
            }
        }
        
        this._groupIds.splice(index, 1);
        this._groupSubs.splice(subNodeIds, 1);
        this.showSubCmptTag(id, true);
    }
    /**
     * 撤销过程。坐标点已缓存。不再进行坐标修改。
     * @param bCheck 
     */
    public resetLayoutSize (bCheck: boolean = false) {
        let tempPos = [];
        if (this._groupSubs.length === 0) {
            return;
        }
        let oldPos = cc.v3(this.node.position.x, this.node.position.y);
        let angle = this.getNodeAngle();
        for (let subDisplay of this._groupSubs) {
            let newRect: cc.Vec2[] = MathUtil.getRotaRectNew(subDisplay.node);
            for (let i = 0; i < newRect.length; i++) {
                let item = newRect[i];
                let data = cc.v3(item.x, item.y).add(oldPos);
                tempPos.push(cc.v3(data.x, data.y));
            }
        }
        if (tempPos.length === 0) {
            return;
        }
        let rectData = MathUtil.getXY(tempPos);
        let centerPos = MathUtil.getCenterPosByPosArr(tempPos);
        let nMinX = rectData.minX;
        let nMaxX = rectData.maxX;
        let nMinY = rectData.minY;
        let nMaxY = rectData.maxY;
        this.node.width = (nMaxX - nMinX);
        this.node.height = (nMaxY - nMinY);

        if (!bCheck) {
            // Undo操作不计算坐标
            let movePos = centerPos.sub(this.node.position);
            this.node.position = centerPos;
            for (let subDisplay of this._groupSubs) {
                subDisplay.node.position = subDisplay.node.position.sub(movePos);
            }
            if (angle !== 0) {
                let newPos = cc.v2(centerPos.x, centerPos.y);
                let oldPos1 = cc.v2(oldPos.x, oldPos.y);
                let changePos = MathUtil.getAngleAfterPos(angle, oldPos1, newPos);
                this.node.setPosition(changePos);
            }
        }
        this.refreshRectPoint();
    }

    public clearGroupIds () {
        for (let sub of this._groupSubs) {
            sub.setTagActive(true);
        }
        this._oldSubDisplay = this._groupSubs;
        this._groupIds = [];
        this._groupSubs = [];
    }
    /**
     * @msg     : 设置子节点 标号
     * @param    {string} id
     * @param    {boolean} active
     * @return   {*}
     */    
    private showSubCmptSignal(id: string, active: boolean) {
        for (let sub of this._groupSubs) {
            if (sub.cid === id) {
                sub.setSignalActive(active);
            }
        }
    }
    /**
     * 设置子节点标签
     * @param id 
     * @param active 
     */
    private showSubCmptTag (id: string, active: boolean) {
        for (let sub of this._groupSubs) {
            if (sub.cid === id) {
                sub.setTagActive(active);
            }
        }
    }

    /**
     * 重写获取属性方法
     */
    public getNewProperties (): any {
        let superData = super.getNewProperties();
        superData.extra.subProperties = [];
        for (let sub of this._groupSubs) {
            superData.extra.subProperties.push(sub.getNewProperties());
        }
        superData.extra.groupIds = this._groupIds;
        return superData;
    }
}
