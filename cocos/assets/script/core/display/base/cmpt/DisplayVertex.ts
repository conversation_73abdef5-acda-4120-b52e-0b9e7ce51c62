/*
 * @FilePath     : /assets/script/core/display/base/cmpt/DisplayVertex.ts
 * <AUTHOR> wanghuan
 * @description  : svg 顶点脚本
 * @warn         : 
 */
import { EditorGroup } from "../../../../common/EditorEnum";
import DisplayObject from "../DisplayObject";
import DisplaySvg from "../DisplaySvg";
import { PointData, SEL_POINT_ENUM } from "./DisplaySelect";
import DisplaySelectPoint from "./DisplaySelectPoint";


const {ccclass} = cc._decorator;


@ccclass
export default class DisplayVertex extends cc.Component {

    _display : DisplayObject;
    _vertexPointComp : DisplaySelectPoint[] = [];

    _nodeVertex : cc.Node = null;

    _vertexData:cc.Vec2[] = [];

    chooseIndex:any = null;
    

    // _vertexNodes:cc.Node[] = [];
    public onLoad (): void {
        this._display = this.node.getComponent(DisplayObject);
        this._vertexPointComp = [];
        this.chooseIndex = null;
    }

    init (vertexData:cc.Vec2[]): void {
        if (!this._nodeVertex) {
            this._nodeVertex = new cc.Node();
            this._nodeVertex.name = "_nodeVertex";
            this.node.group = EditorGroup.EDGE;
            this.node.addChild(this._nodeVertex);
        }
        
        this.changeWordPos(vertexData);
        
        for (let i = 0; i < this._vertexData.length; i++) {
            let _data = this._vertexData[i];
            let _itemNode = new cc.Node();
            
            _itemNode.setPosition(_data);
            _itemNode.name = "DisplaySelectPoint";
            this._nodeVertex.addChild(_itemNode);

            let itemComp = _itemNode.addComponent(DisplaySelectPoint);
            let _d = new PointData();
            _d._value = i;
            _d.pos = cc.v3(_data.x, _data.y, 0);
            itemComp.init(_d);
            this._vertexPointComp.push(itemComp);
        }
    }
    /**
     * 刷新顶点的世界坐标
     * @param vertexData 
     */
    changeWordPos (vertexData:cc.Vec2[]) {
        
        // 世界坐标转成本地坐
        this._vertexData = [];
        for (let i = 0; i < vertexData.length; i++) {
            let _p = this._nodeVertex.convertToNodeSpaceAR(yy.cloneValues(vertexData[i]));
            this._vertexData.push(_p);
        }
    }
    /**
     * 移动顶点时，改变vertex位置
     */
    changePosByMove (vertexIndex:number, wordPos):void {
        let _newPos = this._nodeVertex.convertToNodeSpaceAR(wordPos);
        this._vertexPointComp[vertexIndex].node.position = _newPos;
        this._vertexData[vertexIndex] = _newPos;
    }
    /**
     * 刷新节点坐标
     */
    public refreshPointPos (): void {
        let _display = this._display as DisplaySvg;
        _display.refreshVertexWordPos();
        for (let i = 0; i < this._vertexPointComp.length; i++) {
            this._vertexPointComp[i].node.position = cc.v3(this._vertexData[i].x, this._vertexData[i].y, 0);
        }
    }

    public setShowStatus (bIsShow): void {
        if (!bIsShow) {
            if (!this._nodeVertex) {
                return;
            }
            for (let i = 0; i < this._vertexPointComp.length; i++) {
                let itemCom = this._vertexPointComp[i];
                itemCom.node.removeFromParent();
            }
            this._nodeVertex.removeFromParent();
            this._nodeVertex = null;
            this._vertexPointComp = [];
            this.chooseIndex = null;
            this.destroy();
        }
    }

    /**
     * 检测是否点击在操作点上
     * @param touchPos 点击坐标
     */
    public inRectSelPoint (pos: cc.Vec2, getType = false): any {
        for (let i = 0; i < this._vertexData.length; i++) {
            let point = this._vertexPointComp[i];
            if (getType && this.chooseIndex !== null) {
                return this.chooseIndex;
            }
            if (point.inRect(pos)) {
                if (getType) {
                    this.chooseIndex = point._data._value;
                    return point._data._value;
                }
            }
        }
        return SEL_POINT_ENUM.NONE;
    }

    /**
     * 隐藏操作节点
     */
    public hidePoint () {
        this.setPointEnable(false);
    }

    /**
     * 恢复操作节点
     */
    public resumePoint () {
        this.setPointEnable(true);
    }

    private setPointEnable (enable: boolean) {
        for (let i = 0; i < this._vertexPointComp.length; i++) {
            this._vertexPointComp[i].node.active = enable;
        }
    }
}
