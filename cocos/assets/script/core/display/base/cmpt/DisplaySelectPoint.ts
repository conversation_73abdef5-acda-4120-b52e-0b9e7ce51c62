/* eslint-disable dot-notation */
/* eslint-disable max-depth */
/*
 * @FilePath     : /cocos/assets/script/core/display/base/cmpt/DisplaySelectPoint.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import { values } from "lodash-es";
import MathUtil from "../../../../../qte/core/utils/MathUtil";
import { CmptType, EditorGroup } from "../../../../common/EditorEnum";
import HintLineManager from "../../../hintline/HintLineManager";
import DataCenterBridge from "../../../proxy/DataCenterBridge";
import DisplayObjectManager from "../../DisplayObjectManager";
import DisplayObject from "../DisplayObject";
import DisplayObjectGroup from "../DisplayObjectGroup";
import { PointData, SEL_POINT_ENUM } from "./DisplaySelect";

/**
 * 显示对象选中显示逻辑的点
 */
const { ccclass } = cc._decorator;

@ccclass
export default class DisplaySelectPoint extends cc.Component {

    _data: PointData;
    public get data(): PointData {
        return this._data;
    }
    _nPointWH = 10;
    public init(_data: PointData): void {
        this._data = _data;
        let edgeNode = this.node.getChildByName("edge_node");
        if (!edgeNode) {
            edgeNode = new cc.Node();
            edgeNode.addComponent(cc.Graphics);
            edgeNode.name = "edge_node";
            edgeNode.group = EditorGroup.EDGE;
            this.node.addChild(edgeNode);
        }
        let graphics = edgeNode.getComponent(cc.Graphics);
        graphics.strokeColor = cc.Color.BLUE;
        graphics.clear();
        graphics.circle(0, 0, this._nPointWH);
        graphics.fillColor = cc.Color.WHITE;
        graphics.fill();
        graphics.stroke();
    }

    public get rect(): cc.Rect {
        return new cc.Rect(this.node.x - this._nPointWH,
            this.node.y - this._nPointWH,
            this._nPointWH * 2,
            this._nPointWH * 2);
    }

    /**
     * 是否被选中
     * @param pos 
     */
    public inRect(pos: cc.Vec2): boolean {
        if (this.rect.contains(pos)) {
            return true;
        }
        return false;
    }

    /**
     * 获取组内操作点的坐标
     * @param _displayManager 
     * @param _selectId 
     * @param absPos 
     */
    private getGroupAbsPos(_displayManager: DisplayObjectManager, _selectId: string, absPos: cc.Vec3): cc.Vec3 {
        let displayGroupID = _displayManager.getDisplayGroupID(_selectId);
        if (displayGroupID !== "-1") {
            // 操作的是组内控件
            let displayGroup = _displayManager.getDisplayObjectById(displayGroupID);
            if (displayGroup) {
                if (this.data.type !== SEL_POINT_ENUM.ROTATE) {
                    absPos = displayGroup.node.convertToNodeSpaceAR(absPos);
                } else {
                    absPos = absPos.sub(displayGroup.node.position);
                }
            }
        }
        return absPos;
    }




    getHintOffsetY(cPos: cc.Vec2, bPos: cc.Vec2, x: number, absPos: cc.Vec2): number | null {

        let diPos = bPos.sub(cPos);
        // 处理垂直线情况（x轴无变化）
        if (diPos.x === 0) {
            return cPos.x === x ? absPos.y : null; // 0时返回a的y，否则无解
        }

        // 计算参数t：t = (目标x - a.x) / 方向向量x分量
        const t = (x - cPos.x) / diPos.x;
        // 通过参数方程计算y值
        const y = cPos.y + diPos.y * t;
        return y;
    }


    getHintOffsetX(cPos: cc.Vec2, bPos: cc.Vec2, y: number, absPos: cc.Vec2): number | null {
        let diPos = bPos.sub(cPos);
        // 处理水平线情况（y轴无变化）
        if (diPos.y === 0) {
            return cPos.y === y ? absPos.x : null; // 直线为y=targetY时返回a的x，否则无解
        }

        // 计算参数t：t = (目标y - a.y) / 方向向量y分量
        const t = (y - cPos.y) / diPos.y;

        // 通过参数方程计算x值
        const x = cPos.x + diPos.x * t;
        return x;
    }


    getMag(node: cc.Node, cPos: cc.Vec2, bPos: cc.Vec2, wPos: cc.Vec2): number {
        let tCPos = qte.QTEUtils.convertToStageSpaceAR(node, cPos);
        let pPos = qte.QTEUtils.convertToStageSpaceAR(node, bPos);
        let diPos = pPos.sub(tCPos);
        let dotPos = diPos.normalize().dot(wPos.sub(tCPos).normalize());
        let mag = 0;
        if (dotPos > 0) {
            let orScale = diPos.mag();
            let tyOffset = wPos.sub(tCPos).project(diPos);
            let tyPos = tCPos.add(tyOffset);
            let scale = tyOffset.mag() / orScale;
            mag = tyPos.sub(pPos).mag()
            if (scale < 1) {
                mag = -mag;
            }
        }
        return mag;
    }


    getScaleOffset(node: cc.Node, cPos: cc.Vec2, bPos: cc.Vec2, len: number): { scale: number, offsetPos: cc.Vec2 } {
        let tCPos = qte.QTEUtils.convertToStageSpaceAR(node, cPos);
        let pPos = qte.QTEUtils.convertToStageSpaceAR(node, bPos);
        let diPos = pPos.sub(tCPos);
        let cluData = {
            scale: 1,
            offsetPos: cc.v2(0, 0),
        };
        let offset = diPos.normalize().multiplyScalar(len);
        let tyPos = pPos.add(offset);
        let dotPos = diPos.normalize().dot(tyPos.sub(tCPos).normalize());
        if (dotPos > 0) {
            let orScale = diPos.mag();
            let mag = tyPos.sub(tCPos).mag();
            cluData.scale = mag / orScale;
            cluData.offsetPos = offset;
        }
        return cluData;
    }







    /**
     * 操作旋转操作点
     * @param _displayManager 
     * @param _selectId 
     * @param display 
     * @param absPos 
     */
    private getRotateProperty(_displayManager: DisplayObjectManager, _selectId: string, display: DisplayObject, absPos: cc.Vec3): void {
        let displayGroupID = _displayManager.getDisplayGroupID(_selectId);
        if (displayGroupID !== "-1") {
            let displayGroup = _displayManager.getDisplayObjectById(displayGroupID);
            if (displayGroup) {
                MathUtil.rotatePosSelf(absPos, displayGroup.node.angle);
            }
        }
        let centerDisplay = _displayManager.getDisplayObjectById(_selectId);
        let centerPos = cc.v2(centerDisplay.node.x, centerDisplay.node.y);
        let localPos = cc.v2(absPos.x, absPos.y);

        let angle = MathUtil.getAngle4Rotate(centerPos, localPos);
        angle = MathUtil.getAbsorAngle(angle);
        let _data = this.updateRotateProperty(_displayManager, display, _selectId);
        let _groupParent = _data._groupParent;
        let bFindInGroup = _data.bFindInGroup;
        if (bFindInGroup) {
            // 在组里是子对象
            _displayManager.updateDisplayObject(_selectId, { angle });
            _groupParent.refreshRectPoint();
        } else {

            for (let id of _displayManager.selectedIds) {
                let groupId = _displayManager.getDisplayGroupID(id);
                if (groupId !== "-1") {
                    continue;
                }
                let _display = _displayManager.getDisplayObjectById(id);
                if (_display.type == CmptType.GROUP) {
                    continue;
                }
                if (typeof _display.node.angle !== "number") {
                    _display.node.angle = 0;
                }
                _displayManager.updateDisplayObject(id, { angle });
            }
        }

    }

    /**
     * 更新物体属性数据
     * @param _displayManager 
     * @param display 
     * @param _selectId 
     */
    private updateRotateProperty(_displayManager: DisplayObjectManager, display: DisplayObject, _selectId: string): { bFindInGroup: boolean, _groupParent: any } {
        let _dataCenterBridge = yy.single.instance(DataCenterBridge);
        let compIds = _dataCenterBridge.getComponentIds();
        let bFindInGroup = false;
        let _groupParent = null;
        for (let i = 0; i < compIds.length; i++) {
            let _display = _displayManager.getDisplayObjectById(compIds[i].id);
            if (_display && _display.type === CmptType.GROUP) {
                let group = _display as DisplayObjectGroup;
                for (let j = 0; j < group.groupIds.length; j++) {
                    if (group.groupIds[j] === _selectId) {
                        bFindInGroup = true;
                        _groupParent = _display;
                    }
                }
            }
        }
        return { bFindInGroup, _groupParent };
    }


    getPosFromType(node: cc.Node,): { centPos: cc.Vec2, targetPos: cc.Vec2 } {
        let centPos = cc.v2(0, 0);
        let targetPos = cc.v2(0, 0);
        let _type = this.data.type;
        switch (_type) {
            case SEL_POINT_ENUM.UP: {
                centPos = cc.v2(0, -node.height / 2);
                targetPos = cc.v2(0, node.height / 2);
                break;
            }
            case SEL_POINT_ENUM.DOWN: {
                centPos = cc.v2(0, node.height / 2);
                targetPos = cc.v2(0, -node.height / 2);
                break;
            }
            case SEL_POINT_ENUM.LEFT: {
                centPos = cc.v2(node.width / 2, 0);
                targetPos = cc.v2(-node.width / 2, 0);
                break;
            }

            case SEL_POINT_ENUM.RIGHT: {
                centPos = cc.v2(-node.width / 2, 0);
                targetPos = cc.v2(node.width / 2, 0);
                break;
            }
            case SEL_POINT_ENUM.LEFT_UP: {
                centPos = cc.v2(node.width / 2, -node.height / 2);
                targetPos = cc.v2(-node.width / 2, node.height / 2);
                break;
            }
            case SEL_POINT_ENUM.LEFT_DOWN: {
                centPos = cc.v2(node.width / 2, node.height / 2);
                targetPos = cc.v2(-node.width / 2, -node.height / 2);
                break;
            }
            case SEL_POINT_ENUM.RIGHT_UP: {
                centPos = cc.v2(-node.width / 2, -node.height / 2);
                targetPos = cc.v2(node.width / 2, node.height / 2);
                break;
            }
            case SEL_POINT_ENUM.RIGHT_DOWN: {
                centPos = cc.v2(-node.width / 2, node.height / 2);
                targetPos = cc.v2(node.width / 2, -node.height / 2)
                break;
            }
            default:
                break;
        }
        return { centPos, targetPos }

    }

    /**
     * 操作点之后的操作项
     * @param _selectId 操作ID
     * @param absPos 点击的世界坐标
     */
    public updateDisplayObjectByHandle(_selectId: string, absPos: cc.Vec3): void {
        let _displayManager = yy.single.instance(DisplayObjectManager);
        let _type = this.data.type;
        let display = _displayManager.getDisplayObjectById(_selectId);
        let groupId = _displayManager.getDisplayGroupID(_selectId);
        for (let id of _displayManager.selectedIds) {
            let _display = _displayManager.getDisplayObjectById(id);

            if (id == _selectId && _display.dragable == false) {//自己锁情况下
                return;
            }
            if (id !== _selectId) { // 其他组件
                let _groupId = _displayManager.getDisplayGroupID(id);
                if (_groupId == "-1" && id != groupId && _display.dragable == false) {
                    return;
                }
            }

            if (_type == SEL_POINT_ENUM.UP || _type == SEL_POINT_ENUM.DOWN) {
                if (_display.editable && _display.editable['properties'] && _display.editable['properties'].height === false) {
                    return;
                }
            }
            if (_type == SEL_POINT_ENUM.LEFT || _type == SEL_POINT_ENUM.RIGHT) {
                if (_display.editable && _display.editable['properties'] && _display.editable['properties'].width === false) {
                    return;
                }
            }
            if (_type == SEL_POINT_ENUM.LEFT_UP || _type == SEL_POINT_ENUM.LEFT_DOWN || _type == SEL_POINT_ENUM.RIGHT_UP || _type == SEL_POINT_ENUM.RIGHT_DOWN) {
                if (_display.editable && _display.editable['properties'] && (_display.editable['properties'].width === false || _display.editable['properties'].height === false)) {
                    return;
                }
            }

        }

        let mag = 0;
        let centPos = cc.v2(0, 0);
        let targetPos = cc.v2(0, 0);
        switch (_type) {
            case SEL_POINT_ENUM.UP:
            case SEL_POINT_ENUM.DOWN:
            case SEL_POINT_ENUM.LEFT:
            case SEL_POINT_ENUM.RIGHT:
            case SEL_POINT_ENUM.LEFT_UP:
            case SEL_POINT_ENUM.LEFT_DOWN:
            case SEL_POINT_ENUM.RIGHT_UP:
            case SEL_POINT_ENUM.RIGHT_DOWN: {
                let posData = this.getPosFromType(display.node);
                centPos = posData.centPos;
                targetPos = posData.targetPos;
                break;
            }

            case SEL_POINT_ENUM.ROTATE: {
                absPos = this.getGroupAbsPos(_displayManager, _selectId, absPos)
                this.getRotateProperty(_displayManager, _selectId, display, absPos);
                break;
            }

            default:
                break;
        }

        if (!centPos.equals(cc.v2(0, 0)) && !targetPos.equals(cc.v2(0, 0))) {
            if (_type !== SEL_POINT_ENUM.ROTATE && _displayManager.selectedIds.length == 1) {
                let hintLineManager = yy.single.instance(HintLineManager);
                let tCPos = qte.QTEUtils.convertToStageSpaceAR(display.node, centPos);
                let pPos = qte.QTEUtils.convertToStageSpaceAR(display.node, targetPos);
                let hintLine = hintLineManager.updateMagLineByMove(_selectId, pPos, cc.v2(absPos.x, absPos.y));
                if (hintLine.isColl) {
                    if (hintLine.culPps.x != 0) {
                        let y = this.getHintOffsetY(tCPos, pPos, hintLine.culPps.x, cc.v2(absPos.x, absPos.y));
                        if (y != null) {
                            absPos.x = hintLine.culPps.x;
                            absPos.y = y;
                        }

                    } else if (hintLine.culPps.y != 0) {
                        let x = this.getHintOffsetX(tCPos, pPos, hintLine.culPps.y, cc.v2(absPos.x, absPos.y));
                        if (x != null) {
                            absPos.x = x;
                            absPos.y = hintLine.culPps.y;
                        }
                    }
                }
            }
            mag = this.getMag(display.node, centPos, targetPos, cc.v2(absPos.x, absPos.y));
        }
        if (mag != 0) {
            this.updateDisplayProperty(_displayManager, _selectId, mag);
        }


        // 更新选中框和节点位置;
        for (let id of _displayManager.selectedIds) {
            let _display = _displayManager.getDisplayObjectById(id);
            _display.refreshRectPoint();
            let groupId = _displayManager.getDisplayGroupID(id);
            if (groupId !== "-1") {
                let groupDisplay = _displayManager.getDisplayObjectById(groupId) as DisplayObjectGroup;
                groupDisplay.resetLayoutSize();
                yy.single.instance(HintLineManager).updateLineByDisplayScale(groupId);
            }
        }
    }

    /**
     * 更新组的属性数据
     * @param _displayManager 
     * @param _selectId 
     * @param property 
     */
    private updateDisplayProperty(_displayManager: DisplayObjectManager, _selectId, mag: number): void {
        let selectedIdsList = JSON.parse(JSON.stringify(_displayManager.selectedIds));
        let display = _displayManager.getDisplayObjectById(_selectId);
        let groupIdList = {};
        let groupId = _displayManager.getDisplayGroupID(_selectId) // 组内元素拉升 组不动
        if (groupId !== "-1") {
            groupIdList[groupId] = true;
            for (let i = 0; i < selectedIdsList.length; i++) {
                if (groupIdList[selectedIdsList[i]]) {
                    selectedIdsList.splice(i, 1);
                    i--;
                }
            }
        } else if (display.type === CmptType.GROUP) {
            let cId = display.cid;
            for (let i = 0; i < selectedIdsList.length; i++) {
                let groupId = _displayManager.getDisplayGroupID(selectedIdsList[i])
                if (groupId !== "-1" && groupId == cId) {
                    selectedIdsList.splice(i, 1);
                    i--;
                }
            }
        }

        for (let id of selectedIdsList) {
            let _display = _displayManager.getDisplayObjectById(id);
            let posData = this.getPosFromType(_display.node);
            let centPos = posData.centPos;
            let targetPos = posData.targetPos;
            let cluData = this.getScaleOffset(_display.node, centPos, targetPos, mag);
            if (cluData.scale != 1 && !cluData.offsetPos.equals(cc.v2(0, 0))) {
                this.setDisplayProperty(_displayManager, _display, cluData.scale, cluData.offsetPos);
            }
        }
    }
    getAngleDisplayNode(node: cc.Node) {
        let angle = node.angle;
        if (typeof node['cAngle'] != "undefined") {
            angle = node['cAngle'];
        }
        return Math.round(angle % 360) == 0;
    }

    private setDisplayProperty(_displayManager: DisplayObjectManager, _display: DisplayObject, scale: number, offestPos: cc.Vec2): void {
        let _type = this.data.type;
        let h0 = Math.abs(_display.node.height * scale);
        let w0 = Math.abs(_display.node.width * scale);
        let pos0 = _display.node.getPosition().add(offestPos.div(2));
        if (_display.type === CmptType.GROUP) {
            let isOneAmplify = true;
            let group = _display as DisplayObjectGroup;
            if (_type == SEL_POINT_ENUM.UP || _type == SEL_POINT_ENUM.DOWN || _type == SEL_POINT_ENUM.LEFT || _type == SEL_POINT_ENUM.RIGHT) {
                for (let subId of group.groupIds) {
                    let subDisplay = _displayManager.getDisplayObjectById(subId);
                    if (!this.getAngleDisplayNode(subDisplay.node)) {
                        isOneAmplify = false;
                        break;
                    }
                }
            } else {
                isOneAmplify = false;
            }
            for (let subId of group.groupIds) {
                let subDisplay = _displayManager.getDisplayObjectById(subId);
                let h = Math.abs(subDisplay.rect.height * scale);
                let w = Math.abs(subDisplay.rect.width * scale);
                let pos = subDisplay.node.getPosition().multiplyScalar(scale);
                let obj = {
                    height: h,
                    width: w,
                    x: pos.x,
                    y: pos.y
                };
                if (isOneAmplify) {
                    if (_type == SEL_POINT_ENUM.UP || _type == SEL_POINT_ENUM.DOWN) {
                        obj.width = Math.abs(subDisplay.rect.width);
                        obj.x = subDisplay.rect.x;
                    } else {
                        obj.height = Math.abs(subDisplay.rect.height);
                        obj.y = subDisplay.rect.y;
                    }
                }
                _displayManager.updateDisplayObject(subId, obj);
            }
            if (isOneAmplify) {
                if (_type == SEL_POINT_ENUM.UP || _type == SEL_POINT_ENUM.DOWN) {
                    w0 = Math.abs(_display.node.width);
                } else {
                    h0 = Math.abs(_display.node.height);
                }
            }
            let obj0 = {
                height: h0,
                width: w0,
                x: pos0.x,
                y: pos0.y
            };
            _displayManager.updateDisplayObject(_display.cid, obj0);
        } else {
            if (_type == SEL_POINT_ENUM.UP || _type == SEL_POINT_ENUM.DOWN) {
                w0 = Math.abs(_display.node.width);
            } else if (_type == SEL_POINT_ENUM.LEFT || _type == SEL_POINT_ENUM.RIGHT) {
                h0 = Math.abs(_display.node.height);
            }
            let obj0 = {
                height: h0,
                width: w0,
                x: pos0.x,
                y: pos0.y
            };
            _displayManager.updateDisplayObject(_display.cid, obj0);

        }
    }

}