import { CmptType } from "../../../../common/EditorEnum";
import DisplayObject from "../DisplayObject";
import DisplaySelectPoint from "./DisplaySelectPoint";
export enum SEL_POINT_ENUM {
    UP = 0,
    RIGHT_UP = 1,
    RIGHT = 2,
    RIGHT_DOWN = 3,
    DOWN = 4,
    LEFT_DOWN = 5,
    LEFT = 6,
    LEFT_UP = 7,
    ROTATE = "ROTATE",
    NONE = "NONE",
}
// Svg 编辑外框对应 现在编辑 SEL_POINT_ENUM
export let SVG_INDEX = {
    "5": 0,
    "6": 1,
    "7": 2,
    "0": 3,
    "1": 4,
    "2": 5,
    "3": 6,
    "4": 7
};

export class PointData {
    pos: cc.Vec3;
    _type: SEL_POINT_ENUM;
    public get type(): SEL_POINT_ENUM {
        return this._type;
    }
    _value: number;
}

/**
 * 显示对象选中显示逻辑
 */
const { ccclass } = cc._decorator;
const ROTATE_HIGH = 20;
@ccclass
export default class DisplaySelect extends cc.Component {
    _display: DisplayObject;
    _arrSelPoint: DisplaySelectPoint[];

    _nodeSelect: cc.Node;


    // TODO active false  不会走脚本函数
    private initSelectData() {
        this._display = this.node.getComponent(DisplayObject);
        this._arrSelPoint = [];
    }

    public init(): void {
        if (!this._nodeSelect) {
            this._nodeSelect = new cc.Node();
            this._nodeSelect.name = "_nodeSelect";
            this.node.addChild(this._nodeSelect);
            // 设置操作节点层级
            this._nodeSelect.zIndex = 1;
        }
        this.refreshSelectNode();
        let points = this.getPoints();
        this._arrSelPoint = [];
        for (let i = 0; i < points.length; i++) {
            let data = points[i];
            let itemNode = new cc.Node();
            itemNode.position = data.pos;
            this._nodeSelect.addChild(itemNode);
            let itemCom = itemNode.addComponent(DisplaySelectPoint);
            itemCom.init(data);
            this._arrSelPoint.push(itemCom);
        }
        this.refreshPointPos();
    }

    private refreshSelectNode(): void {
        if (!this._display) {
            this.initSelectData();
        }
        let rect = this._display.rect;
        this._nodeSelect.width = rect.width;
        this._nodeSelect.height = rect.height;
    }

    public refreshPointPos(): void {
        let points = this.getPoints();
        for (let i = 0; i < this._arrSelPoint.length; i++) {
            let data = points[i];
            this._arrSelPoint[i].node.position = data.pos;
            if (this._display.dragable === false) {
                this._arrSelPoint[i].node.active = false;
            } else {
                this._arrSelPoint[i].node.active = true;
            }
        }
    }

    public setShowStatus(bIsShow): void {
        if (!bIsShow) {
            if (!this._nodeSelect) {
                return;
            }
            for (let i = 0; i < this._arrSelPoint.length; i++) {
                let itemCom = this._arrSelPoint[i];
                itemCom.node.removeFromParent();
            }
            this._nodeSelect.removeFromParent();
            this._nodeSelect = null;
            this._arrSelPoint = [];
        }
    }

    private getPoints(): PointData[] {
        let _arrPos = [];
        let rect: cc.Rect = this._display.getSelectRect();

        let pData2 = new PointData();
        pData2.pos = new cc.Vec3(rect.x, rect.y + rect.height / 2);
        pData2._type = SEL_POINT_ENUM.LEFT;
        _arrPos.push(pData2);

        let pData6 = new PointData();
        pData6.pos = new cc.Vec3(rect.x + rect.width, rect.y + rect.height / 2);
        pData6._type = SEL_POINT_ENUM.RIGHT;
        _arrPos.push(pData6);


        let pData9 = new PointData();
        pData9.pos = new cc.Vec3(rect.x + rect.width / 2, rect.y + rect.height + ROTATE_HIGH);
        pData9._type = SEL_POINT_ENUM.ROTATE;
        if (this._display.type != CmptType.GROUP) {
            _arrPos.push(pData9);
        }
        if (this.node.getComponent(DisplayObject).type === CmptType.LABEL) {
            return _arrPos;
        }

        let pData1 = new PointData();
        pData1.pos = new cc.Vec3(rect.x, rect.y);
        pData1._type = SEL_POINT_ENUM.LEFT_DOWN;
        _arrPos.push(pData1);

        let pData3 = new PointData();
        pData3.pos = new cc.Vec3(rect.x, rect.y + rect.height);
        pData3._type = SEL_POINT_ENUM.LEFT_UP;
        _arrPos.push(pData3);

        let pData5 = new PointData();
        pData5.pos = new cc.Vec3(rect.x + rect.width, rect.y + rect.height);
        pData5._type = SEL_POINT_ENUM.RIGHT_UP;
        _arrPos.push(pData5);

        let pData7 = new PointData();
        pData7.pos = new cc.Vec3(rect.x + rect.width, rect.y);
        pData7._type = SEL_POINT_ENUM.RIGHT_DOWN;
        _arrPos.push(pData7);
        let pData4 = new PointData();
        pData4.pos = new cc.Vec3(rect.x + rect.width / 2, rect.y + rect.height);
        pData4._type = SEL_POINT_ENUM.UP;
        _arrPos.push(pData4);

        let pData8 = new PointData();
        pData8.pos = new cc.Vec3(rect.x + rect.width / 2, rect.y);
        pData8._type = SEL_POINT_ENUM.DOWN;
        _arrPos.push(pData8);

        // 点坐标,枚举,
        return _arrPos;
    }

    /**
     * 检测是否点击在操作点上
     * @param touchPos 点击坐标
     */
    public inRectSelPoint(pos: cc.Vec2): DisplaySelectPoint {
        for (let i = 0; i < this._arrSelPoint.length; i++) {
            let point = this._arrSelPoint[i];
            if (point.inRect(pos)) {
                return point;
            }
        }
        return null;
    }

    /**
     * 隐藏操作节点
     */
    public hidePoint() {
        this.setPointEnable(false);
    }

    /**
     * 恢复操作节点
     */
    public resumePoint() {
        this.setPointEnable(true);
    }

    private setPointEnable(enable: boolean) {
        //   let  this._
        for (let i = 0; i < this._arrSelPoint.length; i++) {
            this._arrSelPoint[i].node.active = enable;
            // if()
        }
    }
}
