/*
 * @FilePath     : /assets/script/core/display/base/DisplaySvg.ts
 * <AUTHOR> wanghuan
 * @description  : svg 组件脚本
 * @warn         :
 */

import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { ComUtil } from "../../../common/ComUtils";
import { SvgCompData } from "../../proxy/ComptData";
import DataCenterBridge from "../../proxy/DataCenterBridge";
import DisplayObjectManager from "../DisplayObjectManager";
import DisplayVertex from "./cmpt/DisplayVertex";
import DisplayObject from "./DisplayObject";

const { ccclass, property } = cc._decorator;

export enum EditorType {
  rect,
  vertex,
}

@ccclass
export default class DisplaySvg extends DisplayObject {
  isShowVertex: boolean = false;
  private svgComp: svg.RaphaelComponent = null;
  private _displayVertex: DisplayVertex = null;
  centerNode: cc.Node;
  private _displayObjectMgr: DisplayObjectManager;
  public get displayVertex(): DisplayVertex {
    return this._displayVertex;
  }
  public set displayVertex(value: DisplayVertex) {
    this._displayVertex = value;
  }
  /** 图形的数据 */
  private _shapeDatas: SvgCompData = null;
  public get shapeDatas(): SvgCompData {
    return this._shapeDatas;
  }

  // 编辑状态
  private _editorType: EditorType = EditorType.rect;
  public set editorType(v: EditorType) {
    this._editorType = v;
  }

  public get editorType(): EditorType {
    return this._editorType;
  }
  private _originSize: cc.Size;
  private _changeVertexBtn: cc.Node;
  onInit(data?: SvgCompData) {
    data && (this._shapeDatas = data);
    this._displayObjectMgr = yy.single.instance(DisplayObjectManager);
    let svgNode = new cc.Node();
    svgNode.addComponent(svg.RaphaelComponent);
    svgNode.name = "display_svg";
    this.node.addChild(svgNode);
    console.warn(data, "svgOnInit");
    this.svgComp = svgNode.getComponent(svg.RaphaelComponent);
    // 设置自己的属性
    if (data.fillColor) {
      this.svgComp.fillColor = new cc.Color().fromHEX(data.fillColor);
    }
    if (data.strokeColor) {
      this.svgComp.strokeColor = new cc.Color().fromHEX(data.strokeColor);
    }
    this.node.on(cc.Node.EventType.COLOR_CHANGED, () => {
      const graphicNode = svgNode.getChildByName("svg_group");
      graphicNode && ComUtil.setSvgShapeOpacity(graphicNode, svgNode.parent.opacity / 255);
    });

    this.svgComp.lineWidth = data.lineWidth || 1;
    const textAsset = new cc.TextAsset();
    textAsset.text = data.svgText;
    this.setPath(textAsset, data);
  }
  /**
   * 顶点编辑按钮显示
   */
  public vertexBtnView() {
    this._changeVertexBtn.active = true;
  }
  /**
   * 创建顶点编辑按钮
   */
  public createVertexBtn(): void {
    // 在外框出添加修改顶点按钮
    if (this._changeVertexBtn) {
      this._changeVertexBtn.active = true;
      return;
    }
    let _btnNode = new cc.Node();
    this._changeVertexBtn = _btnNode;
    _btnNode.active = false;
    this.node.addChild(_btnNode);
    _btnNode.position = cc.v3(this.node.width / 2 + 40, this.node.height / 2 + 40);
    _btnNode.setContentSize(cc.size(100, 100));
    let _spr = _btnNode.addComponent(cc.Sprite);
    cc.resources.load("img/bg", cc.SpriteFrame, (error, spr: cc.SpriteFrame) => {
      if (!error) {
        _spr.spriteFrame = spr;
      }
    });
  }
  /**
   * 判断是否点击了顶点编辑按钮
   * @param event
   */
  public clickVertexBtn(pos): boolean {
    let _isContain = this._changeVertexBtn.getBoundingBoxToWorld().contains(pos);
    if (_isContain) {
      let _isVisible = true;
      if (this.editorType === EditorType.vertex) {
        _isVisible = false;
      }
      // this._isClickVertex = _isVisible;
      this.showVertexPoint(_isVisible);
    }
    return _isContain;
  }

  /**
   * 初始化 路径
   * @param textAsset
   */
  public setPath(textAsset: cc.TextAsset, data?: SvgCompData) {
    this.scheduleOnce(() => {
      this.svgComp.svgText = textAsset;
      this.svgComp.onInit();
      this.changeFillColor(data.fillColor);
      this.changeStrokeColor(data.strokeColor);
      this.changeStrokeWidth(data.lineWidth);
      !this.cid && (this.node.active = data.active);
      this.cid && yy.single.instance(DisplayObjectManager).updateDisplayObject(this.cid, { active: data.active });
      this.svgComp.getVertexPoss();
      this.scheduleOnce(() => {
        const svgWidth = this.getShapeRect().width;
        const svgHeight = this.getShapeRect().height;
        const scaleX = data.width / svgWidth;
        const scaleY = data.height / svgHeight;
        const scale = Math.min(scaleX, scaleY);
        if (!data.origin) {
          this.svgComp.setSvgScaleX(scaleX);
          this.svgComp.setSvgScaleY(scaleY);
        } else {
          this.svgComp.setSvgScaleX(scale);
          this.svgComp.setSvgScaleY(scale);
        }
        if (this.cid) {
          console.warn(this.cid, "this.cid");

          data.origin && yy.single.instance(DisplayObjectManager).updateDisplayObject(this.cid, { width: this.getShapeRect().width * scale, height: this.getShapeRect().height * scale }),
            (window as any)._$store.commit("updateComponentProperties", {
              id: this.cid,
              newProperties: { origin: false },
            });
        }

        this.shapeDatas.origin = false;
        this.refreshRectPoint();
      });
    }, 0);
  }

  public setSvgNodeSize(size: cc.Size): void {
    this.svgComp.node.setContentSize(size);
    this._originSize = size;
  }

  /**
   * 是否显示顶点编辑
   * @param isVisible
   */
  public showVertexPoint(isVisible: boolean): void {
    let vertexComp = this.node.getComponent(DisplayVertex);
    if (!vertexComp) {
      vertexComp = this.node.addComponent(DisplayVertex);
      vertexComp.init(this.getVertexPoss());
    }
    this._displayVertex = vertexComp;
    if (isVisible === true) {
      this.editorType = EditorType.vertex;
      super.setSelected(false);
      this.setUpdateAnchor();
    } else {
      this.editorType = EditorType.rect;
      this.setUpdateAnchor(false);
    }
    vertexComp.setShowStatus(isVisible);
  }
  /**
   * 编辑模式改成缩放
   */
  public editorToRect(): void {
    if (this._displayVertex) {
      this._displayVertex.setShowStatus(false);
    }
    this.editorType = EditorType.rect;
  }

  public changeShapeSvg(index: number, wordPos: cc.Vec3): void {
    this.setUpdateAnchor(true);
    // 当前的中心点位置
    let _oldRect = this.getShapeRect();
    let _centerPos = cc.v2(_oldRect.x + _oldRect.width / 2, _oldRect.y + _oldRect.height / 2);

    // 直接赋值给世界坐标
    this.svgComp.changeShape(index, cc.v2(wordPos.x, wordPos.y));

    // 刷新编辑顶点位置 下一帧刷新 减少抖动
    this.scheduleOnce(() => {
      this._displayVertex.refreshPointPos();
    }, 0);

    // VertexNode 自己移动
    this._displayVertex.changePosByMove(index, wordPos);

    let _newShapeRect = this.getShapeRect();
    // 保持原始svg大小
    this._originSize = cc.size(_newShapeRect.width / this.svgComp.node.scaleX, _newShapeRect.height / this.svgComp.node.scaleY);

    //  改变node节点rect 更新外边框
    this.node.setContentSize(cc.size(_newShapeRect.width, _newShapeRect.height));
    this.svgComp.node.setContentSize(cc.size(_newShapeRect.width, _newShapeRect.height));

    // 新的当前的中心点位置
    let _newCenterPos = cc.v2(_newShapeRect.x + _newShapeRect.width / 2, _newShapeRect.y + _newShapeRect.height / 2);

    let _newCenterDiff = cc.v2(_newCenterPos.x - _centerPos.x, _newCenterPos.y - _centerPos.y);

    // 改变外框位置和父节点位置
    let _nodePos = this.node.getPosition();
    this.node.position = cc.v3(_nodePos.x + _newCenterDiff.x, _nodePos.y + _newCenterDiff.y, 0);
  }
  /**
   * 更新顶点世界坐标
   */
  public refreshVertexWordPos() {
    this._displayVertex.changeWordPos(this.getVertexPoss());
  }
  /**
   * 获取矩形rect
   */
  public getShapeRect(): cc.Rect {
    return this.svgComp.getShapeRect();
  }
  public getVertexPoss(): Array<cc.Vec2> {
    return this.svgComp.getVertexPoss();
  }
  public setUpdateAnchor(isTrue = false) {
    this.svgComp.setUpdateAnchor(isTrue);
  }
  // 修改填充颜色
  public changeFillColor(color: string): void {
    const _color = new cc.Color().fromHEX(color);
    this.svgComp.fillColor = _color;
    this._shapeDatas.fillColor = color;
    this.svgComp.changeFillColor(_color);
  }
  // 修改画线颜色
  public changeStrokeColor(color: string): void {
    const _color = new cc.Color().fromHEX(color);
    this._shapeDatas.strokeColor = color;

    this.svgComp.changeStrokeColor(_color);
    this.svgComp.strokeColor = _color;
  }
  // 修改画线宽度
  public changeStrokeWidth(width: number): void {
    this.svgComp.changeStrokeWidth(width);
    this.svgComp.lineWidth = width;
    this._shapeDatas.lineWidth = width;
  }
  /**
   * 设置宽度
   * @param width
   */
  public setNodeWidth(width: number): void {
    // this.svgComp.node.width = width;
    if (this.getShapeRect().width) {
      const svgWidth = this.getShapeRect().width;
      this._shapeDatas.width = width;
      this.svgComp.setSvgScaleX(width / svgWidth);
    }

    // this.svgComp.refreshSvg();
  }

  /**
   * 设置高度
   * @param height
   */
  public setNodeHeight(height: number): void {
    if (this.getShapeRect().height) {
      const svgHeight = this.getShapeRect().height;
      this._shapeDatas.height = height;
      this.svgComp.setSvgScaleY(height / svgHeight);
    }
  }
  /**
   *
   * @param scaleX
   */
  public setNodeScaleX(scaleX: number): void {
    // this.svgComp.node.scaleX = scaleX;
  }
  /**
   *
   * @param scaleY
   */
  public setNodeScaleY(scaleY: number): void {
    // this.svgComp.node.scaleY = scaleY;
  }

  /**
   * 检测是否是修改顶点状态
   */
  public checkVertexType(): boolean {
    if (this.editorType === EditorType.vertex) {
      return true;
    }
    return false;
  }
  /**
   * 获得顶点的index;
   * @param pos
   */
  public getVertexIndex(pos: cc.Vec2): any {
    let comSel = this.node.getComponent(DisplayVertex);
    if (comSel) {
      let posNode = this.node.convertToNodeSpaceAR(pos);
      return comSel.inRectSelPoint(posNode, true);
    }
    return null;
  }

  /**
   * 修改缩放
   */
  public refreshRectPoint(): void {
    super.refreshRectPoint();
    // const nowSize: cc.Size = this.node.getContentSize();
    // let _originSize = this._originSize;
    // if (_originSize) {
    //   let _delta = cc.v2(nowSize.width / _originSize.width, nowSize.height / _originSize.height);
    //   if (this.editorType === EditorType.rect) {
    //     this.svgComp.node.scaleX = _delta.x;
    //     this.svgComp.node.scaleY = _delta.y;
    //   }
    // }
  }
  public clearEdge(): void {
    super.clearEdge();
    // this._changeVertexBtn.active = false;
  }

  /**
   * getOld
   */
  public getOldProperties() {
    // 获取原始状态，将操作封装成命令添加到命令队列中，用于回退操作
    let dataCenterBridge = yy.single.instance(DataCenterBridge);
    let cmpt = dataCenterBridge.getComponentMap()[this.cid] as {
      id: number;
      properties: SvgCompData;
    };
    // 原始数据
    let oldData = {
      id: this.cid,
      newProperties: {
        x: cmpt.properties.x,
        y: cmpt.properties.y,
        width: cmpt.properties.width,
        height: cmpt.properties.height,
        angle: cmpt.properties.angle,
        svgText: cmpt.properties.svgText,
        strokeColor: cmpt.properties.strokeColor,
        fillColor: cmpt.properties.fillColor,
        lineWidth: cmpt.properties.lineWidth,
      },
    };
    return oldData;
  }

  /** 获取当前最新数据 */
  public getNewProperties() {
    let superData = super.getNewProperties();
    // 当前要更新的数据
    let data = {
      strokeColor: "#" + this.svgComp.strokeColor.toHEX("#rrggbb"),
      fillColor: "#" + this.svgComp.fillColor.toHEX("#rrggbb"),
      lineWidth: this.svgComp.lineWidth,
      svgText: this._shapeDatas.svgText,
    };
    for (let key in data) {
      if (typeof superData.newProperties[key] == "undefined") {
        superData.newProperties[key] = data[key];
      }
    }
    console.log("superData== svg==>", superData);
    data = ValueUtils.setOneDecimal(data);
    // 准备传给command的数据

    return superData;
  }

  /**
   * 导出文件格式
   */
  public exportPath() {
    // 获取绝对坐标数组
    //@ts-ignore
    let _vertexs = this.svgComp._path.vertexPoints;
    // 组成字符串  字符串格式    <path d="M10 10v290L400 10z" stroke="#8b572a" fill="#8b572a" stroke-width="1.5"/>
    let _path = "<path ";

    for (let i = 0; i < _vertexs.length; i++) {
      if (i === 0) {
        _path += `d="M${_vertexs[0][0]} ${_vertexs[0][1]}`;
      } else {
        _path += `L${_vertexs[i][0]} ${_vertexs[1][1]}`;
      }
    }
    // 其他属性
    console.log("%c 🍱 _path: ", "font-size:20px;background-color: #4b4b4b;color:#fff;", _path);

    // 样式格式
    let _strokeColor = `stroke="${this.svgComp.strokeColor.toHEX()}`;

    let _fillColor = `fill="${this.svgComp.fillColor.toHEX()}`;

    let _lineWidth = `stroke-width=${this.svgComp.lineWidth}`;

    return `${_path + _strokeColor + _fillColor}/>`;
  }
}
