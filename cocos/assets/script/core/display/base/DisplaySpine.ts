import { join } from "lodash";
import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { SpineData } from "../../proxy/ComptData";
import DisplayObject from "./DisplayObject";

/**
 * 显示对象基类
 */
const { ccclass } = cc._decorator;

@ccclass
export default class DisplaySpine extends DisplayObject {
  // 锚点位置
  private _anchor: cc.Vec2 = null;

  public set anchor(anchor: cc.Vec2) {
    this._anchor = anchor;
  }
  public get anchor(): cc.Vec2 {
    return this._anchor;
  }
  /** Spine的size作为初始size */
  private _size: cc.Size = null;
  public set size(size: cc.Size) {
    this._size = size;
  }
  public get size(): cc.Size {
    return this._size;
  }
  // spineNode
  private _spineCmpt: sp.Skeleton = null;
  public get spineCmpt(): sp.Skeleton {
    return this._spineCmpt;
  }
  // 播放动画队列
  private _actionList = [""];
  public get actionList(): string[] {
    return this._actionList;
  }

  /** 偏移坐标 */
  private _offet: cc.Vec2 = cc.v2(0, 0);
  public get offset(): cc.Vec2 {
    return this._offet;
  }
  // 播放索引
  private _playIndex = 0;

  /*
   * 循环
   */
  private _loop: boolean = true;
  public get loop(): boolean {
    return this._loop;
  }
  // 时间缩放率
  private _timeScale: number = 1;
  public get timeScale(): number {
    return this._timeScale;
  }

  // spine数据
  public spineData: SpineData = null;
  /**
   * 添加事件监听
   * @param cmpt
   */
  public addListener(cmpt: sp.Skeleton) {
    this._spineCmpt = cmpt;
    // 监听播放完成
    this._spineCmpt.setCompleteListener(this.completeListener.bind(this));
  }

  /**
   *重置位置
   */
  resetSpinePos() {
    let scaleX = this._spineCmpt.node.scaleX || 1;
    let scaleY = this._spineCmpt.node.scaleY || 1;
    let offestX = (this._anchor.x - 0.5) * this._size.width * scaleX;
    let offestY = (this._anchor.y - 0.5) * this._size.height * scaleY;
    this._spineCmpt.node.setPosition(cc.v3(offestX, offestY));
    this._offet = cc.v2(offestX, offestY);
  }

  refreshRectPoint() {
    super.refreshRectPoint();
    let size = this.node.getContentSize();
    let percentW = size.width / this._size.width;
    let percentH = size.height / this._size.height;
    let spineNode = this._spineCmpt.node;
    if (this._size.height == 0) { //0/0 有问题
      percentH = 1;
    }

    if (this._size.width == 0) {
      percentW = 1;
    }
    spineNode.scaleX = percentW;
    spineNode.scaleY = percentH;
    this.resetSpinePos();
  }

  /** 设置播放列表 */
  public setAnimation(actionList: string[]) {
    this._actionList = actionList;
    this.step();
  }
  /** 设置循环 */
  public setLoop(loop: boolean) {
    if (this._loop === loop) {
      return;
    }
    this._loop = loop;
    if (this._loop) {
      this.step();
    }
  }

  /** 设置TimeScale */
  public setTimeScale(value: number) {
    this._timeScale = Math.max(value, 0);
    this._spineCmpt.timeScale = this._timeScale;
  }

  // 播放下一个
  private step() {
    if (
      this.actionList.length <= 0 ||
      this._playIndex >= this.actionList.length
    ) {
      return;
    }
    let animName = this.actionList[this._playIndex];
    if (animName === "") {
      this.completeListener();
      return;
    }
    this._spineCmpt.setAnimation(0, animName, false);
  }

  /**
   * 播放完事件
   */
  private endListener() {
    cc.log("");
  }

  /**
   * 循环一次播放完成事件setCompleteListener
   */
  private completeListener() {
    // 播放加一
    this._playIndex++;
    if (!this.checkActionList()) {
      this._playIndex = 0;
      this._spineCmpt.animation = "";
      return;
    }
    // 播放完了 并且不是循环播放
    if (this._playIndex > this.actionList.length - 1) {
      this._playIndex = 0;
      if (!this.loop) {
        return;
      }
    }
    // 播放下一个
    this.step();
  }

  /**
   * 检测动画列表是否合法
   */
  private checkActionList(): boolean {
    for (let value of this.actionList) {
      if (value !== "") {
        return true;
      }
    }
    return false;
  }

  /**
   * 获取spine某个动作的持续时间,
   * @param skeleton spine
   * @param animaitonName 动作名称
   */
  public getSpineAnimationTime(animaitonName: string): number {
    let duration = 0;
    // @ts-ignore
    let attachUtil = this._spineCmpt.attachUtil;
    if (!attachUtil) {
      return duration;
    }
    let animations = attachUtil._skeleton.data.animations;
    for (let i = 0; i < animations.length; i++) {
      let animation = animations[i];
      if (animation.name === animaitonName) {
        duration = yy.checkValue(animation.duration, 0);
        break;
      }
    }
    return ValueUtils.setOneDecimal(duration, 2);
  }

  /**
   * 重写获取属性方法
   */
  public getNewProperties(): any {
    let superData = super.getNewProperties();

    // eslint-disable-next-line dot-notation
    let actionList = JSON.parse(JSON.stringify(this.actionList));
    if (actionList.length <= 0) {
      actionList = ['']; // 解决数组为空创建回退再前进报错
    }
    superData.extra["spineData"] = this.spineData;
    superData.newProperties.animationList = actionList;
    superData.newProperties.loop = this.loop;
    superData.newProperties.timeScale = this.timeScale;
    if (superData.extra.color) { // spine 没有color属性，回退报错
      delete superData.extra.color;
    }
    return superData;
  }
}
