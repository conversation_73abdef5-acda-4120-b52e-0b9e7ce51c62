import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import CommandManager from "../../../../qte/core/extension/command/CommandManager";
import TextToImgCommand from "../../command/operate/editbox/TextToImgCommand";
import UpdateProp2VueCmd from "../../command/simple/UpdateProp2VueCmd";
import ComptData, { LabelProperties } from "../../proxy/ComptData";
import DataCenterBridge from "../../proxy/DataCenterBridge";
import DisplayObject from "./DisplayObject";
import {writeText} from 'clipboard-polyfill'

const { ccclass } = cc._decorator;
@ccclass
export default class DisplayEditBox extends DisplayObject {
  private editBoxData: any = null;
  public editProperties = {
    enableBold: false,
    enableItalic: false,
    enableUnderline: false,
    fontSize: 30,
    lineHeight: 40,
    color: "#000000",
    string: "请输入文本",
    str: "",
    cusorIndex: 0,
    isLabelRight: true,
    selectArr: [],
    horizontalAlign: 0,
    isFixed: false,
  };

  // new
  private _editBoxCom: editbox.EditBoxTool = null;
  public getEditBoxCom(): editbox.EditBoxTool {
    return this._editBoxCom;
  }
  _editBoxNode: cc.Node = null;
  // 是否点在 输入框上
  _clickInStatue: boolean = true;
  // 是否选中
  _isSelected: boolean = false;

  initFinish() {
    this.initNode();
  }

  //
  private initNode() {
    yy.log("=======initNode=====");
    if (this._editBoxNode) {
      return;
    }
    this._editBoxNode = new cc.Node();

    this._editBoxNode.setPosition(cc.v2(-100, 0));
    this._editBoxNode.parent = this.node;
    this._editBoxNode.setContentSize(this.node.getContentSize());
    this._editBoxCom = this._editBoxNode.addComponent(editbox.EditBoxTool);
    this._editBoxCom.textToImg = this.textToImg.bind(this);
    this._editBoxCom.writeToText = this.writeToClipBoard.bind(this);
    this._editBoxCom.widthHeightFixed = this.editBoxData.isFixed;
    this._editBoxCom.editable = this.editable ||null;
    this._editBoxCom.fontSize = this.editBoxData.fontSize;
    this._editBoxCom.scheduleOnce(() => {
      if (this.editBoxData && this.editBoxData.str !== "") {
        this._editBoxCom.pikerviewStatue = 1;
        yy.log("initNode_editBoxData======>", this.editBoxData);
        this._editBoxCom.setEditBoxHorizontalStatue(this.editBoxData.horizontalAlign);
        this._editBoxCom.setNodeSize(this.node.getContentSize().width, this.node.getContentSize().height);
        if (this.editBoxData.lineHeight) {
          this._editBoxCom.lineHeight = this.editBoxData.lineHeight;
        }
        if (this.editBoxData.rowSpacing) {
          this._editBoxCom.rowSpace = this.editBoxData.rowSpacing;
        }
        this._editBoxCom.createEditBoxWithString(this.editBoxData.str);
      } else {
        this._editBoxCom.pikerviewStatue = 2;
        this._editBoxCom.createEditBox(); 
      }
      this.initFinished();
    }, 0);
    this._editBoxCom.updateEditProperties = this.updateEditProperties.bind(this);
    this._editBoxCom.updateNodeSize = this.refreshNodeSize.bind(this);
    this._editBoxCom.updateEditPropertiesWithStr = this.updateEditPropertiesWithStr.bind(this);
  }
  private mouseBegin(event: cc.Event.EventMouse) {
    let local = event.getLocation();
    let touchPos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    this._clickInStatue = this._editBoxCom.rect().contains(touchPos);
    if (this._editBoxCom.pikerviewStatue === 0) {
      this._editBoxCom.pikerviewStatue = 1;
    } else {
      this._editBoxCom.mouseBeginLayout(event, touchPos);
    }
  }

  private mouseMove(event: cc.Event.EventMouse) {
    let local = event.getLocation();
    let touchPos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    this._editBoxCom.mouseMoveLayout(event, touchPos);
  }

  private mouseUp() {
    this._editBoxCom.mouseUpLayout();
  }

  // 初始化数据
  public initData(data: LabelProperties): void {
    let data1 = ValueUtils.clone(data);
    yy.log("=======initData=====", JSON.stringify(data), JSON.stringify(data1));
    this.node.color = new cc.Color().fromHEX(yy.checkValue(data1.color, "#ffffff"));
    this.node.x = data1.x;
    this.node.y = data1.y;
    this.node.width = data1.width;
    this.node.height = data1.height;
    this.node.scaleX = yy.checkValue(data1.scaleX, 1);
    this.node.scaleY = yy.checkValue(data1.scaleY, 1);
    this.editBoxData = data1;
    for (let key in data1) {
      if (typeof this.editProperties[key] !== "undefined") {
        this.editProperties[key] = data1[key];
      }
    }

    // let testRichtTest = "<b><size=30><color=#F9F8F8>yangha&lt;/b&lt;,&lt b&gt;c</c><color=#EB0808>uanhu</c></s></b><i><u><size=30><color=#EB0808>anhuan 啊叫啊叫啊叫就开始大家阿莱克斯的的<br/>撒开多久啊叫大叔了； 大叔看见的撒觉</c><color=#F9F8F8>得撒娇的考拉；监督<br/>卡； 的撒健康的健身卡就打开手机</c></s></u></i>";
    // this._editBoxNode = new cc.Node();
    // this._editBoxCom = this._editBoxNode.addComponent(editbox.EditBoxTool);
    // this._editBoxCom.setNodeSize(300, 403);
    // this._editBoxCom.setEditBoxHorizontalStatue(0);
    // this._editBoxCom.createEditBoxWithString(testRichtTest);
    // this._editBoxNode.parent = this.node;
  }
  /* **********************画线**************************** */
  ////////////////////////////////

  public setBoldEnabled(data: boolean): boolean {
    return this._editBoxCom.setBoldEnabled(data);
  }

  public setItalicEnabled(data: boolean): boolean {
    return this._editBoxCom.setItalicEnabled(data);
  }

  public setUnderLine(data: boolean): boolean {
    return this._editBoxCom.setUnderLine(data);
  }

  public setIsFixed(data: boolean): boolean {
    return this._editBoxCom.setIsFixed(data);
  }

  public setRowSpace(data: number): number {
    return this._editBoxCom.setRowSpace(data);
  }

  public setLineHeight(data: number): number {
    return this._editBoxCom.setLineHeight(data);
  }

  public setFontSize(data: number): number {
    return this._editBoxCom.setFontSize(data);
  }

  public setFontColor(data: string): string {
    return this._editBoxCom.setFontColor(data);
  }

  public setNodeWidth(data: number, isUndo: boolean): void {
    this._editBoxCom.setNodeWidth(data, !isUndo);
  }

  public setNodeHeight(data: number): void {
    this._editBoxCom.setNodeHeight(data);
  }

  public setNodeAngle(): void {
    this._editBoxCom.setNodeAngle();
  }

  public setHorizontalAlign(data: number): number {
    return this._editBoxCom.setHorizontalAlign(data);
  }

  public clickDirectionKey(keyNum: number, shift?: boolean): void {
    this._editBoxCom.clickDirectionKey(keyNum, shift);
  }

  public clickCtrlA(): void {
    this._editBoxCom.clickCtrlA();
  }

  public clickCtrlC(): void {
    this._editBoxCom.clickCtrlC();
  }

  public clickCtrlX(): void {
    this._editBoxCom.clickCtrlX();
  }

  public clickDeleteKey(): void {
    this._editBoxCom.clickDeleteKey();
  }

  public setCursorIsLabelRight(data: boolean): boolean {
    return this._editBoxCom.setCursorIsLabelRight(data);
  }

  public setCursorLabelIndex(data: number): number {
    return this._editBoxCom.setCursorLabelIndex(data);
  }

  public setRichTextToEditBox(data: string): string {
    return this._editBoxCom.convertToEditBox(data);
  }

  // /**
  //  * 刷新选中框框和操作节点坐标的位置
  //  */
  private refreshNodeSize(): void {
    this.node.setContentSize(this._editBoxNode.getContentSize());
    super.refreshRectPoint();
  }

  public getNodeEditStatue(): boolean {
    return this._editBoxCom.isEditing;
  }

  public setpikerviewStatue(data: number): void {
    this._editBoxCom.pikerviewStatue = data;
  }

  /** 选中状态 */
  public setSelected(selected: boolean): void {
    super.setSelected(selected);
    if (selected) {
      this._editBoxCom.setSelected();
    } else {
      this._editBoxCom.cancelSelected();
    }
  }

  public cancelSelected() {
    this._editBoxCom.cancelSelected();
  }

  public mouseBeginLayout(event: cc.Event.EventMouse) {
    this.mouseBegin(event);
  }

  public mouseMoveLayout(event: cc.Event.EventMouse) {
    this.mouseMove(event);
  }

  public mouseUpLayout() {
    this.mouseUp();
  }

  public mouseWheel(event: cc.Event.EventMouse) {
    this._editBoxCom.mouseWheelLayout(event);
  }

  public setStartEdit() {
    this._editBoxCom.createWithEdit = true;
  }

  private updateEditPropertiesWithStr(properties, notUpdate: boolean = true): void {
    this.updateEditProperties(properties);
    yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, {
      selectIds: [this.cid],
      isUpdateGroupData: false,
      notUpdate2Vue: notUpdate,
      notRunCmd: true,
    });
  }

  private updateEditProperties(properties): void {
    console.warn("displayEditor updateEditProperties", properties);
    for (let key in properties) {
      if (typeof this.editProperties[key] !== "undefined") {
        this.editProperties[key] = properties[key];
      }
    }
  }

  private textToImg(texture: cc.RenderTexture): void {
    yy.single.instance(CommandManager).executeCommand(TextToImgCommand, {
      selectId: this.cid,
      texture: texture,
    });
  }

  /** 获取旧数据 */
  public getOldProperties(): any {
    // 获取原始状态，将操作封装成命令添加到命令队列中，用于回退操作
    let dataCenterBridge = yy.single.instance(DataCenterBridge);
    let cmpt: ComptData = dataCenterBridge.getComponentMap()[this.cid];
    let labeProperties = cmpt.properties as LabelProperties;
    // 原始数据
    let oldData = {
      id: this.cid,
      newProperties: {
        x: labeProperties.x,
        y: labeProperties.y,
        width: labeProperties.width,
        height: labeProperties.height,
        angle: labeProperties.angle || 0,
        enableBold: Boolean(labeProperties.enableBold),
        enableItalic: Boolean(labeProperties.enableItalic),
        enableUnderline: Boolean(labeProperties.enableUnderline),
        fontSize: labeProperties.fontSize,
        lineHeight: labeProperties.lineHeight,
        color: labeProperties.color || "#FFFFFF",
        str: labeProperties.str,
        cusorIndex: labeProperties.cusorIndex,
        isLabelRight: labeProperties.isLabelRight,
        selectArr: labeProperties.selectArr,
        horizontalAlign: labeProperties.horizontalAlign,
        isFixed: this.editProperties.isFixed
      },
    };
    return oldData;
  }

  //  editBoxStr: string 导出字符串 cusorIndex: number 光标位置 selectArr:[] 选中节点数组
  /** 获取当前最新数据 */
  public getNewProperties() {
    let superData = super.getNewProperties();
    // 当前要更新的数据
    let data = {
      x: this.node.x,
      y: this.node.y,
      width: this.node.width,
      height: this.node.height,
      angle:superData.newProperties.angle || 0,
      enableBold: Boolean(this.editProperties.enableBold),
      enableItalic: Boolean(this.editProperties.enableItalic),
      enableUnderline: Boolean(this.editProperties.enableUnderline),
      fontSize: this.editProperties.fontSize,
      lineHeight: this.editProperties.lineHeight,
      color: this.editProperties.color || "#FFFFFF",
      str: this.editProperties.str,
      cusorIndex: this.editProperties.cusorIndex,
      isLabelRight: this.editProperties.isLabelRight,
      selectArr: this.editProperties.selectArr,
      horizontalAlign: this.editProperties.horizontalAlign,
      isFixed: this.editProperties.isFixed
    };
    let datas = {};
    for (let key in data) {
      datas[key] = ValueUtils.setOneDecimal(data[key]);
    }
    // console.error("data.angle",data.angle);
    // let newData = {
    //     id: this.cid,
    //     newProperties: data
    // };
    superData.newProperties = data;
    return superData;
  }

  private writeToClipBoard(s) {
    writeText(s);
  }
}
