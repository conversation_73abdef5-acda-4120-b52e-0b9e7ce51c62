import { SingleBase } from "../../../../qte/core/base/SingleBase";
import { CmptLayer, CmptType } from "../../../common/EditorEnum";
import DisplayObjectManager from "../DisplayObjectManager";
import DisplaySvg from "./DisplaySvg";

export default class SvgShapeManager extends SingleBase {
  /** 动画预览 重新构建图形 */
  reloadSvgShap(layerNode: cc.Node) {
    let manager = yy.single.instance(DisplayObjectManager);
    manager.dpMap.forEach(displayObj => {
      if (displayObj.type === CmptType.SVGSHAPE) {
        let cmptSvgShape = displayObj as DisplaySvg;

        let nodePath = this.getNodePath(cmptSvgShape.node, CmptLayer.OBJECT_LAYER);
        let tempNode = cc.find(nodePath, layerNode);
        if (tempNode.active) {
          let displaySvgShape = tempNode.getComponent(DisplaySvg);
          displaySvgShape.onInit(cmptSvgShape.shapeDatas);
        }
      }
    });
  }

  private setProperty(displayObj: DisplaySvg, properties: any) {
    let prop = {} as any;
    yy.single.instance(DisplayObjectManager).updateDisplayObject(displayObj.cid, prop);
  }

  /**
   * 获得节点在跟节点下的路径
   * @param node 子节点
   * @param root 根节点
   */
  private getNodePath(node: cc.Node, root: CmptLayer): string {
    let path = "";
    while (node && !(node.name === root)) {
      if (path) {
        path = `${node.name}/${path}`;
      } else {
        path = node.name;
      }
      node = node.parent;
    }
    return path;
  }
}
