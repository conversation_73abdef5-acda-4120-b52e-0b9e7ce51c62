import ValueUtils from "../../../../../qte/core/utils/ValueUtils";
import { ComUtil } from "../../../../common/ComUtils";
import { EditorGroup } from "../../../../common/EditorEnum";
import { CutShapeCompData } from "../../../proxy/ComptData";
import DataCenterBridge from "../../../proxy/DataCenterBridge";
import DisplayEditObject from "./DisplayEditObject";

const {ccclass} = cc._decorator;

enum  cutShapeType {
    TRIAGLE_1      = 0,        // 等腰三角形
    TRIAGLE_2      = 1,        // 等边三角形
    TRIAGLE_3      = 2,        // 直角三角形
    TRIAGLE_3_1    = 3,        // 镜像直角三角形2
    TRAPEZOID_1    = 4,        // 梯形
    TRAPEZOID_2    = 5,        // 直角梯形
    TRAPEZOID_2_1  = 6,        // 镜像直角梯形1
    TRAPEZOID_3    = 7,        // 等腰梯形
    RHOMBOID       = 8,        // 平行四边形1
    RHOMBOID_1     = 9,        // 镜像平行四边形2
    SQUARE         = 10,       // 正方形
    PENTAGON       = 11,       // 五边形
    LIUBIANXING    = 12,       // 六边形
}

@ccclass
export default class DisplayCutShape extends DisplayEditObject {

    /** 图形类型 */
    private _shapeType: number = null;
    public get subType (): number {
        return this._shapeType;
    }
     /** 图形的数据 */
    private _shapeDatas: CutShapeCompData = null;
    public get shapeDatas (): CutShapeCompData {
        return this._shapeDatas;
    }
    /** 初始化图形 */
    private _initShape: boolean = true;
    /** 图形是否缩放 */
    private _isScaleChange: boolean = false;
    /** 所有的点数据 */
    private _pointDatas: any[] = null;
    /** 所有的线数据 */
    private _lineDatas: any[] = null;

    /** 初始化完成 */
    initFinish () {
        super.initFinish()
        this.isHidePoint = this._shapeDatas.isHidePointLine;
        // 创建图形
        this._createShape();
        // 刷新创建好的编辑点
        for (let point of this._pointDatas) {
            if (point.editable) {
                this.createPoint(point);
            }
        }
    }
    
    /** 初始化数据 */
    public initData (data: CutShapeCompData, subType: number): void {
        this._shapeDatas = data;
        this._shapeType = subType;
        this._pointDatas = data.pointsData;
        if (this._pointDatas.length > 0) {
            this._initShape = false;
        }
    }

    /** 更新图形大小 */
    public refreshRectPoint () {
        this._initShape = false;
        this._isScaleChange = true;
        super.refreshRectPoint()
        this._createShape();
    }

     /** 刷新 点线是否隐藏 */
    public updateLinePointActive (hideStatue: boolean): boolean {   
        let oldHideStatue = this.isHidePoint;
        this.isHidePoint = hideStatue;  
        // 刷新操作点
        this.updatePointActive(hideStatue);
        for (let i = 0; i < this._pointDatas.length; i++) {
            let oneData = this._pointDatas[i];
            if (oneData.editable) {
                return
            }
    
            this._createPointHint(oneData.x, oneData.y, i, hideStatue);
          
            let nextPointIndex = 0;
            if (this._pointDatas[i + 1] && !this._pointDatas[i + 1].editable) {
                nextPointIndex = i + 1;
            }
            let x = (this._pointDatas[i].x + this._pointDatas[nextPointIndex].x) / 2;
            let y = (this._pointDatas[i].y + this._pointDatas[nextPointIndex].y) / 2;
            this._createSidetHint(x, y, i, hideStatue);
        }
        return oldHideStatue
    }
    
    /** 创建图形 */
    private _createShape (): void {
        switch (this._shapeType) {
        case cutShapeType.TRIAGLE_1:
            this._createTriagle1();
            break;
        case cutShapeType.TRIAGLE_2: 
            this._createTriagle2();
            break;
        case cutShapeType.TRIAGLE_3:
            this._createTriagle3();
            break;
        case cutShapeType.TRIAGLE_3_1:
            this._createTriagle31();
            break;
        case cutShapeType.TRAPEZOID_1: 
            this._createTrapezoid1();
            break;    
        case cutShapeType.TRAPEZOID_2:
            this._createTrapezoid2(); 
            break;    
        case cutShapeType.TRAPEZOID_2_1:
            this._createTrapezoid21(); 
            break;      
        case cutShapeType.TRAPEZOID_3: 
            this._createTrapezoid3(); 
            break;    
        case cutShapeType.RHOMBOID: 
            this._createSibianxing(); 
            break;  
        case cutShapeType.RHOMBOID_1: 
            this._createSibianxing1(); 
            break; 
        case cutShapeType.SQUARE: 
            this._createSquare(); 
            break;    
        case cutShapeType.PENTAGON: 
            this._createPentagon(); 
            break;    
        case cutShapeType.LIUBIANXING: 
            this._createLiubianxing(); 
            break;    
        default:
            break
        }
    }

    /** 创建等腰三角形 */
    private _createTriagle1 (): void  {
        let pos1 = {left: 0.2, top: 0.2};
        let pos2 = {left: 0.5, top: 0.8};
        let pos3 = {left: 0.8, top: 0.2};
        let posArr = [pos1, pos2, pos3]
        this._createGraphics(posArr); 
    }

    /** 创建等边三角形 */
    private _createTriagle2 (): void  {
        let pos1 = {left: 0.1, top: 0.2};
        let pos2 = {left: 0.5, top: 0.8};
        let pos3 = {left: 0.9, top: 0.2};
        let posArr = [pos1, pos2, pos3]
        this._createGraphics(posArr); 
    }

    /** 创建直角三角形 */
    private _createTriagle3 (): void  {
        let pos1 = {left: 0.1, top: 0.2};
        let pos2 = {left: 0.1, top: 0.8};
        let pos3 = {left: 0.9, top: 0.2};
        let posArr = [pos1, pos2, pos3]
        this._createGraphics(posArr); 
    }
    
    /** 创建镜像直角三角形 */
    private _createTriagle31 (): void  {
        let pos1 = {left: 0.1, top: 0.2};
        let pos2 = {left: 0.9, top: 0.8};
        let pos3 = {left: 0.9, top: 0.2};
        let posArr = [pos1, pos2, pos3]
        this._createGraphics(posArr); 
    }

    /** 创建梯形 */
    private _createTrapezoid1 (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.2, top: 0.9};
        let pos3 = {left: 0.6, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }

    /** 创建直角梯形 */
    private _createTrapezoid2 (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.1, top: 0.9};
        let pos3 = {left: 0.5, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }

    /** 创建镜像直角梯形 */
    private _createTrapezoid21 (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.5, top: 0.9};
        let pos3 = {left: 0.9, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }
    
    /** 创建等腰梯形 */
    private _createTrapezoid3 (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.3, top: 0.9};
        let pos3 = {left: 0.7, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }
   
    /** 创建平行四边形 */
    private _createSibianxing (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.3, top: 0.9};
        let pos3 = {left: 0.9, top: 0.9};
        let pos4 = {left: 0.7, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }

    /** 创建镜像平行四边形 */
    private _createSibianxing1 (): void  {
        let pos1 = {left: 0.3, top: 0.1};
        let pos2 = {left: 0.1, top: 0.9};
        let pos3 = {left: 0.7, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }

    /** 创建正方形 */
    private _createSquare (): void  {
        let pos1 = {left: 0.1, top: 0.1};
        let pos2 = {left: 0.1, top: 0.9};
        let pos3 = {left: 0.9, top: 0.9};
        let pos4 = {left: 0.9, top: 0.1};
        let posArr = [pos1, pos2, pos3, pos4]
        this._createGraphics(posArr); 
    }

    /** 创建五边形 */
    private _createPentagon (): void  {
        let pos1 = {top: 0.09, left: 0.21};
        let pos2 = {top: 0.65, left: 0.03};
        let pos3 = {top: 1, left: 0.5};
        let pos4 = {top: 0.65, left: 0.97};
        let pos5 = {top: 0.09, left: 0.79};
        let posArr = [pos1, pos2, pos3, pos4, pos5]
        this._createGraphics(posArr); 
    }

    /** 创建六边形 */
    private _createLiubianxing (): void  {
        let pos1 = {left: 0.25, top: 0.07};
        let pos2 = {left: 0, top: 0.5};
        let pos3 = {left: 0.25, top: 0.93};
        let pos4 = {left: 0.75, top: 0.93};
        let pos5 = {left: 1, top: 0.5};
        let pos6 = {left: 0.75, top: 0.07};
        let posArr = [pos1, pos2, pos3, pos4, pos5, pos6]
        this._createGraphics(posArr); 
    }

    /**
     *  绘制图形
     * @param posArr 所有顶点数据
     */
    private _createGraphics (posArr:any) : void {
        // 初始化数据
        let pointArr = [];
        this._lineDatas = [];
        for (let i = 0; i < posArr.length; i++) {
            let pos = posArr[i];
            let posX = ValueUtils.setOneDecimal(-this.node.width / 2 + this.node.width  * pos.left);
            let posY = ValueUtils.setOneDecimal(-this.node.height / 2 + this.node.height * pos.top);
            // 显示点label 
            pointArr.push(cc.v2(posX, posY));           
        }
        this.drawShape(pointArr);
        this.setPointData(pointArr); 
        this.setLindeData(pointArr);

        this.updateLinePointActive(this.isHidePoint);
    }
    
    /**
     * 处理定点数据
     * @param posArr 所有点
     */
    private setPointData (pointArr: cc.Vec2[]): void {
        for (let i = 0; i < pointArr.length; i++) {
            let onePointData = {
                id: `P${i}`,
                label: `点${i + 1}`,
                x: pointArr[i].x,
                y: pointArr[i].y,
                editable: false
            };
            let nFindIndex = this._pointDatas.findIndex((value) => value.id === onePointData.id)
            if (nFindIndex === -1) {
                this._pointDatas.push(onePointData);
            } else {
                this._pointDatas[nFindIndex] = onePointData;
            }
        }
        if (this._initShape) {          
            yy.single.instance(DataCenterBridge).setCutShapePointsData(this.cid, this._pointDatas);
        }
    }

    /**
     * 处理边线数据
     * @param pointArr 组成一条边的两个端点 
     */
    private setLindeData (pointArr: cc.Vec2[]): void {
        this._lineDatas = [];
        for (let i = 0; i < pointArr.length; i++) {
            let nextPointIndex = 0;
            if (pointArr[i + 1]) {  
                nextPointIndex = i + 1;
            }
            let oneLineData = {
                id: `L${i}`,
                label: `边${i + 1}`,
                points: [this._pointDatas[i].id, this._pointDatas[nextPointIndex].id]
            };
            this._lineDatas.push(oneLineData);
        }
        if (this._initShape) {
            yy.single.instance(DataCenterBridge).setCutShapeLinesData(this.cid,  this._lineDatas);
        }
    }
    
    /**
     * 画图形逻辑
     * @param pointArr 顶点数组
     */
    private drawShape (pointArr: cc.Vec2[]): void {
        let edgeNode = this.node.getChildByName("shape_node");
        if (!edgeNode) {
            edgeNode = new cc.Node();
            edgeNode.addComponent(cc.Graphics);
            this.node.addChild(edgeNode, 0, "shape_node");
        }
        let graphics = edgeNode.getComponent(cc.Graphics);
        // 线段和填充颜色的透明度用父节点的
        let fillColor = new cc.Color().fromHEX(this._shapeDatas.fillColor);
        fillColor.a = this.node.opacity;
        let strokeColor = new cc.Color().fromHEX(this._shapeDatas.strokeColor);
        strokeColor.a = this.node.opacity
        
        graphics.strokeColor = strokeColor;
        graphics.fillColor = fillColor;
        
        graphics.lineWidth = this._shapeDatas.lineWidth;
        graphics.clear();

        for (let i = 0; i < pointArr.length; i++) {
            if (i === 0) {
                graphics.moveTo(pointArr[i].x, pointArr[i].y);
            } else {
                graphics.lineTo(pointArr[i].x, pointArr[i].y);
            }
        }
        graphics.close()
        graphics.fill()
        graphics.stroke();
    }

    /** 图形缩放 抬起鼠标时更新数据给 VUE */
    public updateShapeDatasToVue (): void {
        if (!this._isScaleChange) {
            return 
        }
        if (this._pointDatas) {
            for (let onePoint of this._pointDatas) {
                yy.single.instance(DataCenterBridge).updateCutShapePointPosition(this.cid, onePoint.id, {x: onePoint.x, y: onePoint.y}); 
            }
        }
        if (this._lineDatas) {
            yy.single.instance(DataCenterBridge).setCutShapeLinesData(this.cid,  this._lineDatas);
        }
        this._isScaleChange = false;
    }

    /**
     * 点的文字提示
     * @param posX 坐标x
     * @param posY 坐标y
     * @param index 点的下角标
     * @param statue 显示状态
     */
    private _createPointHint (posX: number, posY: number, index: number, hideStatue: boolean): void  {
        // 画圆圈
        let circleNode = this.node.getChildByName(`circle${index}`);
        if (!circleNode) {
            circleNode = new cc.Node(`circle${index}`);
            circleNode.group = EditorGroup.EDGE;
            circleNode.parent = this.node;
            circleNode.addComponent(cc.Graphics);
        }
        let graphics = circleNode.getComponent(cc.Graphics)
        graphics.clear();
        if (!hideStatue) {
            graphics.strokeColor = cc.Color.RED;
            graphics.lineWidth = 5;
            graphics.arc(posX, posY, 20, 0, 180);
            graphics.stroke();
        }
        
        // 点
        let hintLabNode = this.node.getChildByName(`pointLabel_${index}`);
        if (!hintLabNode) {
            hintLabNode = new cc.Node(`pointLabel_${index}`);
            hintLabNode.color = cc.Color.BLACK;
            hintLabNode.group = EditorGroup.EDGE;
            hintLabNode.parent = this.node;
            let labOutLine = hintLabNode.addComponent(cc.LabelOutline);
            labOutLine.color = cc.Color.WHITE;
            labOutLine.width = 2;
            let label = hintLabNode.addComponent(cc.Label);
            label.fontSize = 25;
            label.string = `点${index + 1}`;
            ComUtil.setLabelBlend(label);
        }
        hintLabNode.active = !hideStatue;
        if (posX >= 0) {
            posX += 20; 
        } else {
            posX -= 20;   
        }
        if (posY >= 0) {
            posY += 20; 
        } else {
            posY -= 20;   
        }
        hintLabNode.setPosition(cc.v2(posX, posY));
    }

    /**
     * 边的文字提示
     * @param posX 坐标x
     * @param posY 坐标y
     * @param index 点的下角标
     * @param statue 显示状态
     */
    private _createSidetHint (posX: number, posY: number, index: number, hideStatue: boolean): void  {
        let hintLabNode = this.node.getChildByName(`sideLabel_${index}`);
        if (!hintLabNode) {
            hintLabNode = new cc.Node(`sideLabel_${index}`);
            hintLabNode.group = EditorGroup.EDGE;
            hintLabNode.color =  cc.Color.WHITE;
            hintLabNode.parent = this.node;

            let labOutLine = hintLabNode.addComponent(cc.LabelOutline);
            labOutLine.color = cc.Color.BLACK;
            labOutLine.width = 2;
            let label = hintLabNode.addComponent(cc.Label);
            label.fontSize = 30;
            label.string = `边${index + 1}`;
            ComUtil.setLabelBlend(label);
        }
        hintLabNode.active = !hideStatue;
        hintLabNode.setPosition(cc.v2(posX, posY));
    }

    /** 设置描边的宽度 */
    public setLineWidht (data: number) {
        let oldLineWidth = this._shapeDatas.lineWidth;
        this._shapeDatas.lineWidth = data;
        this._createShape()
        return oldLineWidth;
    }

    /** 设置 描边的颜色 */
    public setStrockColor (data: string) {
        let oldStrokeColor = this._shapeDatas.strokeColor;
        this._shapeDatas.strokeColor = data;
        this._createShape()
        return oldStrokeColor;
    }

    /** 设置图形填充的颜色 */
    public setFillColor (data: string) {
        let oldFillColor = this._shapeDatas.fillColor;
        this._shapeDatas.fillColor = data;
        this._createShape()
        return oldFillColor;
    }

    /** 重写获取属性方法 */
    public getNewProperties (): any {
        let superData = super.getNewProperties();
       
        let allPointData = this._pointDatas;
        superData.extra.isHidePointLine = this._shapeDatas.isHidePointLine;
        superData.extra.strokeColor = this._shapeDatas.strokeColor
        superData.extra.fillColor = this._shapeDatas.fillColor
        superData.extra.lineWidth = this._shapeDatas.lineWidth;
        superData.extra.pointsData = allPointData;
        superData.extra.linesData = this._lineDatas;
        superData.extra.subType = this.subType;
        return superData;
    }

    /** 添加操作点的时候 保存操作点的数据 */
    public createEditPoint (data: any): void {
        let onePointData = {
            id: data.id,
            label: data.label,
            x: data.x,
            y: data.y,
            editable: data.editable
        };
        this._pointDatas.push(onePointData);
        super.createPoint(data);
    }
    
    /**
     * 删除操作点的时候 删除操作点的数据
     * @param pointId  操作点 id
     * @param editable 操作点是否可编辑
     */
    public removeEditPoint (pointId:string, editable:boolean): cc.Vec2 {
        let oldData = super.removePoint(pointId, editable);
        let nFindIndex = this._pointDatas.findIndex((value) => value.id === pointId)
        if (nFindIndex !== -1) {
            this._pointDatas.splice(nFindIndex, 1);
        }
        return oldData;
    }
    
    /**
     *  更新操作点的坐标 
     * @param pointId  操作点 id
     * @param pointPos 新的坐标
     */
    public updateEditPointPos (pointId:string, pointPos:any): void {
        let nFindIndex = this._pointDatas.findIndex((value) => value.id === pointId)
        if (nFindIndex !== -1) {
            this._pointDatas[nFindIndex].x = pointPos.x;
            this._pointDatas[nFindIndex].y = pointPos.y;
        }
    }
}
