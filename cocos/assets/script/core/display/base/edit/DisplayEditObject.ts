import ValueUtils from "../../../../../qte/core/utils/ValueUtils";
import { ComUtil } from "../../../../common/ComUtils";
import CommandFactory from "../../../command/operate/CommandFactory";
import { UpdatePointPosCommand } from "../../../command/operate/cutShape/UpdatePointPosCommand";
import DataCenterBridge from "../../../proxy/DataCenterBridge";
import DisplayObjectManager from "../../DisplayObjectManager";
import DisplayObject from "../DisplayObject";
import CutPoint from "./CutPoint";
import DisplayCutShape from "./DisplayCutShape";
/**
 * 显示对象基类
 */
const { ccclass } = cc._decorator;
@ccclass
export default class DisplayEditObject extends DisplayObject {
  /** 是否处于编辑状态 */
  private _bIsEdting: boolean = false;
  get isEditing(): boolean {
    return this._bIsEdting;
  }

  /** 是否显示点线 */
  private _bIsHidePoint: boolean = false;
  public get isHidePoint(): boolean {
    return this._bIsHidePoint;
  }
  public set isHidePoint(value: boolean) {
    this._bIsHidePoint = value;
  }

  /** 当前选中的可编辑的点 */
  public pitchPoint: cc.Node = null;
  /** 点移动前的旧坐标 */
  private _pitchOldPos: cc.Vec2 = null;
  /** 编辑按钮 */
  private _editButton: cc.Node = null;
  /** 编辑按钮文字 */
  private _editLabel: cc.Label = null;
  /** 可编辑的点数组 */
  private _editPointArr: CutPoint[] = [];

  /**
   *  创建可编辑的点
   * @param data
   */
  public createPoint(data: any): void {
    let pointNode = this.node.getChildByName(`editablePoint${data.id}`);
    if (!pointNode) {
      pointNode = new cc.Node(`editablePoint${data.id}`);
      pointNode.addComponent(cc.Graphics);
      this.node.addChild(pointNode);
    }
    let point = pointNode.addComponent(CutPoint);
    point.createPoint(data);
    point.updatePointActive(this._bIsHidePoint);
    this._editPointArr.push(point);
    this.refreshEditBtn();
  }

  /** 刷新编辑按钮状态 */
  private refreshEditBtn(): void {
    this._editButton.active = false;
    if (this.getPointList().length > 0) {
      if (this.selected && !this._editButton.active) {
        this._editButton.active = true;
      }
    }
  }

  /**
   * 刷新操作点是否可见
   * @param statue 可见状态
   */
  public updatePointActive(statue: boolean): void {
    for (let cutPoint of this._editPointArr) {
      cutPoint.updatePointActive(statue);
    }
  }

  /**
   * 删除指定操作点
   * @param id  操作点id
   * @param editable  点是否可编辑
   */
  public removePoint(id: string, editable: boolean): any {
    if (!editable) {
      return;
    }
    let nFindIndex = this._editPointArr.findIndex(
      value => value.pointData.id === id,
    );
    let pointData = null;
    if (nFindIndex !== -1) {
      let pointCom = this._editPointArr.splice(nFindIndex, 1)[0];
      pointData = pointCom.pointData;
      pointCom.node.destroy();
    }
    return pointData;
  }

  /** 设置可操作点数组 */
  public pointList(value: CutPoint[]): void {
    this._editPointArr = value;
  }

  /** 获取可操作点数组 */
  public getPointList(): CutPoint[] {
    return this._editPointArr;
  }

  /** 清除所有可操作点数组 */
  public clearPointList(): void {
    this._editPointArr = [];
  }

  /**
   * 更新 对应下角标可操作点
   * @param point 点数据
   * @param index 点的下角标
   */
  public updatePoint(point: CutPoint, index: number): void {
    if (!point) {
      return;
    }
    if (index > 0 && index < this._editPointArr.length) {
      this._editPointArr[index] = point;
    }
  }

  /** 初始化 */
  init() {
    super.init();
    // 添加可编辑按钮,控制组件是否可编辑状态
    this.initEditButton();
  }

  /** 开始编辑 */
  private beginEdit(): void {
    this._bIsEdting = true;
    this._editLabel.string = " 编辑中 ";
  }

  /** 结束编辑 */
  private stopEdit(): void {
    this._bIsEdting = false;
    this._editLabel.string = " 去编辑 ";
  }

  /**
   * 传递编辑事件
   * @param pos
   */
  public touchBegin(pos: cc.Vec2) {
    // 节点内坐标
    let realPos = this.node.convertToNodeSpaceAR(pos);
    let rect = new cc.Rect(
      this._editButton.x,
      this._editButton.y - this._editButton.height / 2,
      this._editButton.width,
      this._editButton.height,
    );
    if (rect.contains(realPos)) {
      if (typeof this.editable === "object" && this.editable.properties) {
        let statue = this.editable.properties.cutShapePoints;
        if (!statue) {
          return true;
        }
      }
      if (this._bIsEdting) {
        this.stopEdit();
      } else {
        this.beginEdit();
      }
      return true;
    }
    if (!this._bIsEdting) {
      return false;
    }
    for (let point of this._editPointArr) {
      if (!point.node.active) {
        return false;
      }
      let dis = cc.Vec2.distance(
        realPos,
        cc.v2(point.pointData.x, point.pointData.y),
      );
      if (dis <= point.circleRadius) {
        this.pitchPoint = point.node;
        this._pitchOldPos = this.pitchPoint.getPosition();
        return true;
      }
    }
    return false;
  }

  /**
   * 点击移动
   * @param pos
   */
  public touchMove(pos: cc.Vec2) {
    // 节点内坐标
    let realPos = this.node.convertToNodeSpaceAR(pos);
    if (this.pitchPoint) {
      this.pitchPoint.getComponent(CutPoint).updatePos(realPos);
      return true;
    }
    return false;
  }

  /**
   * 点击抬起
   * @param pos
   */
  public touchUp() {
    if (this.pitchPoint) {
      if (this.pitchPoint.getPosition() !== this._pitchOldPos) {
        let pointId = this.pitchPoint.getComponent(CutPoint).pointData.id;
        let pointPosition = {
          x: ValueUtils.setOneDecimal(
            this.pitchPoint.getComponent(CutPoint).pointData.x,
            2,
          ),
          y: ValueUtils.setOneDecimal(
            this.pitchPoint.getComponent(CutPoint).pointData.y,
            2,
          ),
        };
        yy.single
          .instance(DataCenterBridge)
          .updateCutShapePointPosition(this.cid, pointId, pointPosition);
        this._updateEditPointPos(pointId, pointPosition);
        yy.single.instance(CommandFactory).pushCommand(
          UpdatePointPosCommand,
          { cmptIds: this.cid, pointId, pointPos: pointPosition },
          {
            cmptIds: this.cid,
            pointId,
            pointPos: ValueUtils.clone(this._pitchOldPos),
          },
        );
      }
      this._pitchOldPos = null;
      this.pitchPoint = null;
    }
    return false;
  }

  /**
   * 更新指定操作点的位置
   * @param pointId 点id
   * @param pos     点位置
   */
  updatePointPos(pointId: string, pos: any) {
    let nFindIndex = this._editPointArr.findIndex(
      value => value.pointData.id === pointId,
    );
    if (nFindIndex === -1) {
      return;
    }
    let oldPos = cc.v2(0, 0);
    let x = this._editPointArr[nFindIndex].node.x;
    let y = this._editPointArr[nFindIndex].node.y;
    oldPos.x = x;
    oldPos.y = y;
    if (typeof pos.x !== "undefined") {
      x = pos.x;
    }
    if (typeof pos.y !== "undefined") {
      y = pos.y;
    }
    x = ValueUtils.setOneDecimal(x, 2);
    y = ValueUtils.setOneDecimal(y, 2);
    this._editPointArr[nFindIndex].updatePos(cc.v2(x, y));
    yy.single
      .instance(DataCenterBridge)
      .updateCutShapePointPosition(this.cid, pointId, cc.v2(x, y));
    this._updateEditPointPos(pointId, cc.v2(x, y));
    return oldPos;
  }

  /** 刷新宽高之后,更新按钮位置 */
  public refreshRectPoint(): void {
    super.refreshRectPoint();
    if (this.selected) {
      this._editButton.x = this.rect.width / 2;
      this._editButton.y = this.rect.height / 2 - this._editButton.height;
    }
  }

  /**
   * 选中状态
   * @param selected
   */
  public setSelected(selected: boolean): void {
    super.setSelected(selected);
    if (this._bIsEdting && !selected) {
      this.stopEdit();
    }
    this.refreshEditBtn();
  }

  /**
   * 初始化可编辑按钮
   */
  private async initEditButton() {
    let root = new cc.Node();
    root.height = 30;
    root.anchorX = 0;
    root.anchorY = 0.5;
    root.name = "EditBtn_Node";
    this._editButton = root;
    // 创建layout
    let layout = root.addComponent(cc.Layout);
    layout.type = cc.Layout.Type.HORIZONTAL;
    layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
    // 添加widget
    let widget = root.addComponent(cc.Widget);
    widget.isAlignRight = true;
    widget.isAlignTop = true;
    widget.right = 0;
    widget.top = 0;
    widget.alignMode = cc.Widget.AlignMode.ALWAYS;
    // 添加背景
    let sprite = root.addComponent(cc.Sprite);
    sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;

    let _bgLoad = () =>
      new Promise<void>((resolve, reject) => {
        cc.loader.loadRes(
          "img/bg",
          cc.Texture2D,
          (err, texture: cc.Texture2D) => {
            if (err) {
              yy.warn(err);
              reject(err);
              return;
            }
            // sprite.spriteFrame = res;
            ComUtil.addPremultiplyAlpha(texture, sprite);
            resolve();
          },
        );
      });
    await _bgLoad();
    // 创建label
    /* eslint-disable @typescript-eslint/camelcase */
    /* eslint-disable camelcase */
    let label_node = new cc.Node();
    let lb = label_node.addComponent(cc.Label);
    ComUtil.setLabelBlend(lb);
    label_node.anchorX = 0;
    label_node.anchorY = 0.5;
    label_node.color = new cc.Color().fromHEX("#63390E");
    lb.useSystemFont = true;
    lb.fontFamily = "Arial";
    lb.fontSize = 20;
    lb.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
    // 添加到跟节点
    this.node.addChild(root);
    root.addChild(label_node);
    label_node.position = cc.v3(0, -14);
    root.group = "edge";
    lb.string = " 去编辑 ";
    this._editLabel = lb;
    this._editButton.active = false;
  }

  /**
   * 删除 操作点 的时候 删除操作点的数据
   * @param pointId  操作点id
   * @param pointPos 点位置
   */
  private _updateEditPointPos(pointId: string, pointPos: any): void {
    let displayCutShape = yy.single
      .instance(DisplayObjectManager)
      .getDisplayObjectById(this.cid) as DisplayCutShape;
    displayCutShape.updateEditPointPos(pointId, pointPos);
  }
}
