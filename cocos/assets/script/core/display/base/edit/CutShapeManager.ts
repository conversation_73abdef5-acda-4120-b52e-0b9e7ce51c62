import { SingleBase } from "../../../../../qte/core/base/SingleBase";
import { CmptLayer, CmptType } from "../../../../common/EditorEnum";
import DisplayObjectManager from "../../DisplayObjectManager";
import DisplayCutShape from "./DisplayCutShape";

export default class CutShapeManager extends SingleBase {
  /**
   * 检查选中id是否是DisplayCutShape并返回
   * @param selectId
   */
  private checkSelectedType(selectId: string): DisplayCutShape {
    if (selectId === "-1") {
      return null;
    }
    let displayManager = yy.single.instance(DisplayObjectManager);
    let nodeCom = displayManager.getDisplayObjectById(selectId) as DisplayCutShape;
    if (!nodeCom) {
      return null;
    }
    if (nodeCom.type !== CmptType.CUTSHAPE) {
      return null;
    }
    return nodeCom;
  }

  /** 鼠标按下 */
  public mouseBegin(pos: cc.Vec2, selectId: string): boolean {
    let nodeCom = this.checkSelectedType(selectId);
    if (nodeCom && nodeCom.touchBegin(pos)) {
      return true;
    }
    return false;
  }

  /** 鼠标移动 */
  public mouseMove(pos: cc.Vec2, selectId: string): boolean {
    let nodeCom = this.checkSelectedType(selectId);
    if (nodeCom && nodeCom.isEditing && nodeCom.touchMove(pos)) {
      return true;
    }
    return false;
  }

  /** 鼠标抬起 */
  public mouseUp(selectId: string): boolean {
    let nodeCom = this.checkSelectedType(selectId);
    if (nodeCom && nodeCom.updateShapeDatasToVue) {
      nodeCom.updateShapeDatasToVue();
    }
    if (nodeCom && nodeCom.isEditing && nodeCom.touchUp()) {
      return true;
    }
    return false;
  }

  /** 动画预览 重新构建图形 */
  reloadShap(layerNode: cc.Node) {
    let manager = yy.single.instance(DisplayObjectManager);
    manager.dpMap.forEach(displayObj => {
      if (displayObj.type === CmptType.CUTSHAPE) {
        let cmptSpine = displayObj as DisplayCutShape;

        let nodePath = this.getNodePath(cmptSpine.node, CmptLayer.OBJECT_LAYER);
        let tempNode = cc.find(nodePath, layerNode);
        if (tempNode) {
          let displayCutShape = tempNode.getComponent(DisplayCutShape);
          displayCutShape.initData(cmptSpine.shapeDatas, cmptSpine.subType);
          displayCutShape.init();
          displayCutShape.initFinish();
        }
      }
    });
  }

  /**
   * 获得节点在跟节点下的路径
   * @param node 子节点
   * @param root 根节点
   */
  private getNodePath(node: cc.Node, root: CmptLayer): string {
    let path = "";
    while (node && !(node.name === root)) {
      if (path) {
        path = `${node.name}/${path}`;
      } else {
        path = node.name;
      }
      node = node.parent;
    }
    return path;
  }
}
