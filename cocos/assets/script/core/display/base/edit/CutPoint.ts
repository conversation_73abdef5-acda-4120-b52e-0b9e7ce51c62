import ValueUtils from "../../../../../qte/core/utils/ValueUtils";
import { ComUtil } from "../../../../common/ComUtils";
import { EditorGroup } from "../../../../common/EditorEnum";

class PointData {
    id: string;
    label: string;
    x: number;
    y: number;
    editable: boolean;
}
const {ccclass} = cc._decorator;
@ccclass
export default class CutPoint extends cc.Component {
    /** 点数据*/  
    private _pointData : PointData=null;
    public get pointData () {
        return this._pointData
    }

     /** 点的半径*/  
    public circleRadius :number =20;

    /** 组件宽高*/  
    public getRect ():cc.Rect {
        return  new cc.Rect(this.node.x - this.node.width / 2, this.node.y - this.node.height / 2, this.node.width, this.node.height)
    }

    /** 创建可操作点 */
    public createPoint (data: PointData) {
        this._pointData = data;
        this.node.setPosition(cc.v2(data.x, data.y));
        this.node.setContentSize(cc.size(40, 40));
        this.node.group = EditorGroup.EDGE;
        
        // 画点
        let graphics = this.node.getComponent(cc.Graphics)
        graphics.clear();
        graphics.strokeColor = cc.Color.BLUE;
        graphics.lineWidth = 5;
        graphics.arc(0, 0, this.circleRadius, 0, 180);
        graphics.stroke();
        graphics.moveTo(0, -15);
        graphics.lineTo(0, 15);
        graphics.stroke();
        graphics.moveTo(-15, 0);
        graphics.lineTo(15, 0);
        graphics.stroke();
         
        let labNode = new cc.Node();
        labNode.setAnchorPoint(0, 0.5);
        labNode.setPosition(cc.v2(30, 0));
        labNode.color = cc.Color.BLACK;
        labNode.parent = this.node;

        let label = labNode.addComponent(cc.Label);
        label.string = data.label
        label.fontSize = 25;
        label.lineHeight = 25;
        ComUtil.setLabelBlend(label);
        
        let labOutLine = labNode.addComponent(cc.LabelOutline);
        labOutLine.color = cc.Color.WHITE;
        labOutLine.width = 2;
    }

    /** 是否显示操作点*/
    public updatePointActive (hideStatue: boolean) {
        this.node.active = !hideStatue
    }

    /** 移动操作点 更新位置 */
    public updatePos (pos:cc.Vec2) {
        let posX = ValueUtils.setOneDecimal(pos.x);
        let posY = ValueUtils.setOneDecimal(pos.y);
        this.node.setPosition(cc.v2(posX, posY));
        this._pointData.x = posX;
        this._pointData.y = posY;
    }

}
