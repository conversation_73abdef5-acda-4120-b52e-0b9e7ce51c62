import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { CmptType, EditorGroup } from "../../../common/EditorEnum";
import ComptData, { EditableProperties } from "../../proxy/ComptData";
import DataCenterBridge from "../../proxy/DataCenterBridge";
import DisplayObjectManager from "../DisplayObjectManager";
import DisplaySelect from "./cmpt/DisplaySelect";
import DisplaySelectPoint from "./cmpt/DisplaySelectPoint";
import { ComUtil } from "../../../common/ComUtils";

/**
 * 显示对象基类
 */
const { ccclass } = cc._decorator;

@ccclass
export default class DisplayObject extends cc.Component {

  /** 分组id数组 */
  private _childIds: string[] = [];
  public get childIds(): string[] {
    // 不允许外部修改
    return ValueUtils.clone(this._childIds);
  }
  private _childs: DisplayObject[] = [];
  public get childs(): DisplayObject[] {
    return this._childs;
  }

  private _cName: string = "";
  public get cName(): string {
    return this._cName;
  }
  public set cName(_cName: string) {
    this._cName = _cName;
  }
  // 组件id
  private _cid: string;
  public get cid(): string {
    return this._cid;
  }
  public set cid(id: string) {
    this._cid = id;
  }
  // 组件类型
  private _type: CmptType;
  public get type(): CmptType {
    return this._type;
  }
  public set type(t: CmptType) {
    this._type = t;
  }

  // 是否可以编辑
  private _editable: boolean | EditableProperties;
  public set editable(e: boolean | EditableProperties) {
    this._editable = e;
  }
  public get editable() {
    // 设置了隐藏不可编辑
    if (!this.node.active) {
      return false;
    }
    // 透明度
    if (this.node.opacity <= 0) {
      return false;
    }
    return this._editable;
  }

  /** 是否可以拖动 */
  private _dragable: boolean;
  public get dragable(): boolean {
    return this._dragable;
  }
  public set dragable(v: boolean) {
    this._dragable = v;
    this.refreshRectPointLine();
  }
  /** 是否可删除 true 不可删除 */
  private _deletable: boolean;
  public get deletable(): boolean {
    return this._deletable;
  }
  public set deletable(v: boolean) {
    this._deletable = v;
    if (typeof v === "undefined") {
      this._deletable = true;
    }
    this.changeObjectDeletable();
  }

  /** 是否展示tagName名称为 组件 - extra里面的组件 */
  private _tagName: string = "";
  public get tagName(): string {
    return this._tagName;
  }

  private _bSeleted: boolean = false;
  public get selected(): boolean {
    return this._bSeleted;
  }

  private _originalZIndex: number | null = null;

  public init() {
    // 开始初始化
  }
  public isInitFinished = false; // 可以更新属性了

  public initFinish() {
    // 初始化完成
  }


  public initFinished() {
    this.isInitFinished = true;
  }


  public initAllComponentsFinish(){
  }



  // 组件宽高
  public get rect(): cc.Rect {
    return new cc.Rect(this.node.x, this.node.y, this.node.width, this.node.height);
  }
  /**
   * select 点需要的rect;
   */
  public getSelectRect(): cc.Rect {
    return new cc.Rect(-Math.abs(this.rect.width) / 2, -Math.abs(this.rect.height) / 2, Math.abs(this.rect.width), Math.abs(this.rect.height));
  }

  public onLoad(): void {
    // 初始化边框和标签
  }

  /**
   * 添加子组件
   * @param {number} id 组件id
   */
  public addChildObject(id: string, subDisplay: DisplayObject): void {
    if (this._childIds.indexOf(id) >= 0) {
      yy.warn(`id = ${id}的子组件以及添加过了.`);
      return;
    }
    this._childIds.push(id);
    this.childs.push(subDisplay);
  }

  /**
   * 删除子组件
   * @param {number} id 组件id
   */
  public removeChildObject(id: string) {
    let index = this._childIds.indexOf(id);
    let subNodeIds = -1;
    if (index < 0) {
      yy.warn(`id = ${id}的子组件不存在.`);
      return;
    }
    for (let i = 0; i < this._childIds.length - 1; i++) {
      if (this._childs[i].cid === id) {
        subNodeIds = i;
      }
    }

    this._childIds.splice(index, 1);
    this._childs.splice(subNodeIds, 1);
  }



  /**
   * 左上角标签
   * @param str
   */
  public showDisplayTag(str: string, extra?): string {

    let oldStr = "";
    let tagNode: cc.Node = this.node.getChildByName("tag_Node");
    if (tagNode) {
      let labCmpt = tagNode.getComponentInChildren(cc.Label);
      oldStr = labCmpt.string;
      if (str !== "") {
        console.log("%c Line:120 🍢 extra", "color:#42b983", extra);
        if (extra && extra.tagPrefix != "") {
          let st = this.cid;
          console.log("%c Line:130 🥝 this.cid", "color:#4fff4B", this.cid);
          if (extra.tagPrefix == "tagIndex") {
            st = extra.tagIndex;
          }
          labCmpt.string = `${str} ID: ${st}`;
        } else {
          console.log(`${str}id:${this.cid}`);
          if (str.indexOf(this.cid) !== -1) {
            labCmpt.string = `${str}`;
          } else {
            labCmpt.string = `${str}ID:${this.cid}`;
          }
        }

      } else {
        labCmpt.string = "";
      }
      // @ts-ignore
      labCmpt._forceUpdateRenderData();
      // 是组内子节点
      if (yy.single.instance(DisplayObjectManager).getDisplayGroupID(this.cid) !== "-1" || str === "") {
        tagNode.active = false;
      } else {
        tagNode.active = true;
      }
    }
    return oldStr;
  }
  public async addSignalSelectView(isView) {
    console.log("%c Line:147 🍅 isView", "color:#93c0a4", isView);
    let _root = this.node.getChildByName("signal_Frame");
    if (_root) {
      if (isView) {
        _root.active = true;
      } else {
        _root.active = false;
      }
      return;
    }
    let root = new cc.Node();
    this.node.addChild(root, -1);
    root.name = "signal_Frame";
    // root.setContentSize(cc.size(this.node.width,this.node.height));
    let sprite = root.addComponent(cc.Sprite);
    sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;

    let _bgLoad = () =>
      new Promise<void>((resolve, reject) => {
        cc.loader.loadRes("img/answer_border_new", cc.Texture2D, (err, texture: cc.Texture2D) => {
          if (err) {
            yy.warn(err);
            reject(err);
            return;
          }
          // sprite.spriteFrame = res;
          ComUtil.addPremultiplyAlpha(texture, sprite);
          resolve();
        });
      });
    await _bgLoad();
    let sp = sprite.spriteFrame;
    sp.insetTop = sp.insetBottom = sp.insetLeft = sp.insetRight = 35;
    sprite.type = cc.Sprite.Type.SLICED;
    // sprite.sizeMode = cc.Sprite.SizeMode.TRIMMED;
    root.width = this.rect.width + 30;
    root.height = this.rect.height + 30;
    root.angle = this.getNodeAngle();
    // 添加到跟节点
    root.group = "default";
  }
  /**
   * 右上角标签
   * @param str
   */
  public showDisplaySignal(str): string {
    console.log("%c Line:150 🥒 showDisplaySignal str", "color:#465975", str);
    let oldStr = "";
    let signsNode: cc.Node = this.node.getChildByName("signs_Node");
    if (signsNode) {
      let labCmpt = signsNode.getComponentInChildren(cc.Label);
      oldStr = labCmpt.string;
      if (str !== "") {
        labCmpt.string = `  ${str}  `;
      }
      // @ts-ignore
      labCmpt._forceUpdateRenderData();
      // 是组内子节点
      signsNode.active = false;

    }
    return oldStr;
  }
  /**
   * 设置标签显示和隐藏
   * @param active
   */
  public setSignalActive(active: boolean) {
    let signsNode: cc.Node = this.node.getChildByName("signs_Node");
    if (signsNode) {
      signsNode.active = active;
    }
  }

  /**
   * 设置tag 显示和隐藏
   * @param active
   * 
   */
  public setTagActive(active: boolean) {
    let tagNode: cc.Node = this.node.getChildByName("tag_Node");
    if (tagNode) {
      tagNode.active = active;
    }
  }

  // 显示所有节点
  public showAllNodes(click): void {
    console.log("%c Line:308 🎂 click", "color:#e41a6a", click);

    let allNodes = yy.single.instance(DisplayObjectManager).dpMap;
    // 循环 map
    for (const node of Array.from(allNodes.values())) {
      console.log(`层级 node ,${node.node.name},id= ${node.cid}, index:${node.node.getSiblingIndex()} zIndex:${node.node.zIndex}`);
    }
  }

  /**
   * 将节点移动到最上层并记录原始层级
   */
  public moveToTop(): void {
    
    this.node.parent.insertChild(this.node, this.node.parent!.children.length + 1);
    // console.log("%c Line:322 🥐 this.node.parent!.children.length", "color:#4fff4B", this.node.parent!.children.length);
    // this.showAllNodes(1);
  }

  /**
   * 恢复节点到原始层级
   */
  public restoreZIndex(): void {
      yy.single.instance(DisplayObjectManager).sortDisplayObjectByIndex();
      // console.log("层级  restoreZIndex  restoreZIndex", `this._originalZIndex + ${this._originalZIndex} id==${this._cid}`);
      // console.log("层级 restoreZIndex 结束");
      // this.showAllNodes(0);
  }

  /** 选中状态 */
  public setSelected(selected: boolean): void {
    if (selected) {
      this.showEdge();
      this.moveToTop();
    } else {
      this.clearEdge();
      this.restoreZIndex();
    }
    let comSel = this.node.getComponent(DisplaySelect);
    if (!comSel) {
      comSel = this.node.addComponent(DisplaySelect);
    }

    if (selected) {
      comSel.init();
    }

    comSel.setShowStatus(selected);
    this._bSeleted = selected;
  }

  public getSelected(): boolean {
    return this._bSeleted;
  }

  /**
   * 是否点击在操作点上
   * @param pos 世界坐标
   */
  public inSelRect(pos: cc.Vec2): DisplaySelectPoint {
    // 增加判断  是外框修改还是顶点修改
    let comSel = this.node.getComponent(DisplaySelect);
    if (comSel) {
      let posNode = this.node.convertToNodeSpaceAR(pos, cc.v2(this.rect.x, this.rect.y));
      return comSel.inRectSelPoint(posNode);
    }
    return null;
  }

  public clearEdge(): void {
    let edgeNode = this.node.getChildByName("edge_node");
    if (edgeNode) {
      let graphics = edgeNode.getComponent(cc.Graphics);
      graphics.clear();
    }
  }

  /** 显示边框 */
  public showEdge(): void {
    let edgeNode = this.node.getChildByName("edge_node");
    if (!edgeNode) {
      edgeNode = new cc.Node();
      edgeNode.addComponent(cc.Graphics);
      edgeNode.name = "edge_node";
      edgeNode.group = EditorGroup.EDGE;
      this.node.addChild(edgeNode);
    }
    let graphics = edgeNode.getComponent(cc.Graphics);
    graphics.strokeColor = cc.Color.WHITE;
    if (this._dragable === false) {
      graphics.strokeColor = cc.Color.RED;
    }
    graphics.clear();
    graphics.rect(-this.node.width / 2, -this.node.height / 2, this.node.width, this.node.height);
    graphics.stroke();
  }
  /**
   * 刷新选中框框和操作节点坐标的位置
   */
  public refreshRectPoint(): void {
    this.refreshRectPointLine();
  }

  /**
 * 刷新选中框框和操作节点坐标的位置
 */
  public refreshRectPointLine(): void {
    if (this._bSeleted) {
      this.showEdge();
    } else {
      this.clearEdge();
    }
    let comSel = this.node.getComponent(DisplaySelect);
    if (comSel) {
      comSel.refreshPointPos();
    }
  }

  /**
   * 隐藏操作节点
   */
  public hidePoint() {
    let comSel = this.node.getComponent(DisplaySelect);

    if (comSel) {
      comSel.hidePoint();
    }
  }

  /**
   * 恢复操作节点
   */
  public resumePoint() {
    let comSel = this.node.getComponent(DisplaySelect);
    if (comSel) {
      comSel.resumePoint();
    }
  }

  /**
   * 通过缩放设置显隐
   * @param active
   */
  public setActive(active: boolean) {
    if (active) {
      this.node.setScale(1);
    } else {
      this.node.setScale(0);
    }
  }

  /** 显示预选中框 */
  public showPreSelectRect(): void {
    let edgeNode = this.node.getChildByName("preSelectNode");
    if (!edgeNode) {
      edgeNode = new cc.Node();
      edgeNode.addComponent(cc.Graphics);
      edgeNode.name = "preSelectNode";
      this.node.addChild(edgeNode);
    }
    edgeNode.active = true;
    let graphics = edgeNode.getComponent(cc.Graphics);
    graphics.fillColor.set(cc.color(0, 0, 0, 78)); // = cc.Color.MAGENTA;

    graphics.clear();
    graphics.moveTo(-this.node.width / 2, -this.node.height / 2);
    graphics.lineTo(-this.node.width / 2, this.node.height / 2);
    graphics.lineTo(this.node.width / 2, this.node.height / 2);
    graphics.lineTo(this.node.width / 2, -this.node.height / 2);
    graphics.lineTo(-this.node.width / 2, -this.node.height / 2);
    graphics.fill();
  }

  /**
   * 清除显示碰撞的预选中框
   */
  public clearPreSelectRect(): void {
    let edgeNode = this.node.getChildByName("preSelectNode");
    if (edgeNode) {
      edgeNode.active = false;
    }
  }

  /** 获取旧数据 */
  public getOldProperties() {
    // 获取原始状态，将操作封装成命令添加到命令队列中，用于回退操作
    let dataCenterBridge = yy.single.instance(DataCenterBridge);
    let cmpt: ComptData = dataCenterBridge.getComponentMap()[this._cid];
    // 原始数据
    let oldData = {
      id: this._cid,
      newProperties: {
        x: cmpt.properties.x,
        y: cmpt.properties.y,
        width: cmpt.properties.width,
        height: cmpt.properties.height,
        angle: cmpt.properties.angle,
      },
    };
    return oldData;
  }




  /** 获取当前最新数据 */
  public getNewProperties(): any {
    this.fixProperties();
    // 当前要更新的数据
    let data = {
      x: this.node.x,
      y: this.node.y,
      width: this.node.width,
      height: this.node.height,
      angle: this.getNodeAngle(),
    };
    // 判断是否是选中状态
    let isSelected = this.getSelected();
    // 如果是选中状态，则当前的 zIndex 是从_dpIndexMap中获取的；
    let zIndex = isSelected ? yy.single.instance(DisplayObjectManager).dpIndexMap.get(this._cid) : this.node.getSiblingIndex();
    console.log("%c Line:528 🥔 zIndex", "color:#42b983", zIndex);
    let extraData = {
      type: this.type,
      active: this.node.active,
      angle: yy.checkValue(this.getNodeAngle(), 0),
      color: yy.checkValue("#" + this.node.color.toHEX(), "#ffffff"),
      dragable: this.dragable,
      zIndex: zIndex,
    };
    if ((this.type === CmptType.SPRITE || this.type === CmptType.RICHTEXTSPRITE) && this.node["_renderComponent"]) {
      // @ts-ignore
      extraData.texture = yy.checkValue(
        // @ts-ignore
        this.node._renderComponent.___textureUrl,
        "",
      );
    } else if (this.type === CmptType.FORMULA && this.node["_renderComponent"]) {
      // @ts-ignore
      extraData.url = yy.checkValue(
        // @ts-ignore
        this.node._renderComponent.___textureUrl,
        "",
      );
    } else if (this.type === CmptType.SHAPE && this.node["_renderComponent"]) {
      // @ts-ignore
      extraData.texture = yy.checkValue(
        // @ts-ignore
        this.node._renderComponent.___textureUrl,
        "",
      );
    }

    data = ValueUtils.setOneDecimal(data);
    // 准备传给command的数据
    let newData = {
      id: this._cid,
      newProperties: data,
      extra: extraData,
    };
    return newData;
  }
  getNodeAngle() {
    let angle = this.node.angle;
    if (typeof this.node['cAngle'] != "undefined") {
      angle = this.node['cAngle'];
    }
    if (angle < 0) {
      angle += 360;
    }
    angle = Math.round(angle % 360);
    return angle;
  }

  /**
   * 规范化结点属性
   */
  public fixProperties() {
    let angle = this.getFixedNum(this.getNodeAngle(), 1)
    // console.error("fixProperties",angle);
    this.node.width = this.getFixedNum(this.node.width, 1);
    this.node.height = this.getFixedNum(this.node.height, 1);
    this.node.angle = angle;
    this.node.x = this.getFixedNum(this.node.x, 1);
    this.node.y = this.getFixedNum(this.node.y, 1);
  }

  /**
   * 保留几位小数
   * @param value
   * @param fix 小数位数
   */
  public getFixedNum(value: number, fix: number): number {
    return Math.round(value * Math.pow(10, fix)) / Math.pow(10, fix);
  }

  /** 快速双击 */
  public onDoubleClick(): void {
    yy.log("快速双击");
  }

  /** 连续点击两次，也就是选中后再次点击逻辑 */
  public onTwiceClick(): void {
    yy.log("连续点击两次，也就是选中后再次点击逻辑");
  }

  /** 节点可删除属性修改 */
  public changeObjectDeletable() {
    let deletableTag = this.node.getChildByName("deletableTag");
    if (!deletableTag) {
      return;
    }
    deletableTag.active = !this._deletable;
    let deletableToggle = this.node.getChildByName("toggle");
    if (!deletableToggle) {
      return;
    }
    let toggleCmpt = deletableToggle.getComponent(cc.Toggle);
    toggleCmpt.isChecked = !this._deletable;
  }
}
