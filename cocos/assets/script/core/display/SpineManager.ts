import SpineRect from "../../../qte/core/component/spine/SpineRect";
import { SingleBase } from "../../../qte/core/base/SingleBase";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import CommandManager from "../../../qte/core/extension/command/CommandManager";
import { CmptLayer, CmptType } from "../../common/EditorEnum";
import UpdateProp2VueCmd from "../command/simple/UpdateProp2VueCmd";
import { SpineData } from "../proxy/ComptData";
import DisplaySpine from "./base/DisplaySpine";
import DisplayObjectManager from "./DisplayObjectManager";

export default class SpineManager extends SingleBase {

    /**
     * 设置spine初始数据， 通过缩放设置宽高 加上偏移坐标
     * @param id 
     * @param properties 
     */
    // eslint-disable-next-line require-await
    public async initSpineData(id: string, properties: any, spineData: SpineData) {
        return new Promise<void>((resolve) => {
            let manager = yy.single.instance(DisplayObjectManager);
            let displayObj: DisplaySpine = null;
            displayObj = manager.getDisplayObjectById(id) as DisplaySpine;
            let spineNode = displayObj.node.getChildByName("spineNode");
            let spineRect = spineNode.getComponent(SpineRect);
            let spineCmpt = spineNode.getComponent(sp.Skeleton);
            if(spineCmpt && !spineCmpt.skeletonData){
                resolve();
                return;
            }
            displayObj.spineData = spineData;
            displayObj.size = spineNode.getContentSize();
            displayObj.node.width = displayObj.size.width;
            displayObj.node.height = displayObj.size.height;
            displayObj.addListener(spineCmpt);
            spineRect.calcOffset(() => {
                setTimeout(() => {
                    displayObj.anchor = ValueUtils.setOneDecimal(spineRect.getAnchor(), 2);
                    // 直接判断宽高，如果宽高超过 1280 * 720 的，直接设置为 0.5,0.5;
                    if (displayObj.size.width > 1280 || displayObj.size.height > 720) {
                        displayObj.anchor = cc.v2(0.5, 0.5);
                    }
                    displayObj.resetSpinePos();
                    this.setProperty(displayObj, properties);
                    displayObj.refreshRectPoint();
                    yy.single.instance((CommandManager)).executeCommand(UpdateProp2VueCmd, { selectIds: [displayObj.cid], isUpdateGroupData: false, notUpdate2Vue: false, notRunCmd: true });
                    displayObj.initFinished();
                    resolve();
                }, 20)
            });
        })
    }

    /**
     * 计算取消偏移的位置 缩放 剔除属性
     * @param properties 
     */
    private setProperty(displayObj: DisplaySpine, properties: any) {
        let prop = {} as any;
        for (let key in properties) {
            if (key === "x" || key === "y") {
                continue;
            }
            if (key === "scaleX") {
                displayObj.node.width *= properties.scaleX;
                continue;
            }
            if (key === "scaleY") {
                displayObj.node.height *= properties.scaleY;
                continue;
            }
            // 如果初始状态loop 是true 不设置， 防止动画播放覆盖
            if (key === "loop" && properties[key] === true) {
                continue;
            }
            prop[key] = properties[key];
        }
        yy.single.instance(DisplayObjectManager).updateDisplayObject(displayObj.cid, prop);
    }

    /**
     * 用object_layer 里的数据去播放其他层的spine组件
     * @param layer 
     */
    public playActionByCmptLayer(layerNode: cc.Node) {
        let manager = yy.single.instance(DisplayObjectManager);
        manager.dpMap.forEach((displayObj) => {
            let cmptSpine = displayObj as DisplaySpine;
            if (cmptSpine.type === CmptType.SPINE) {
                let nodePath = this.getNodePath(cmptSpine.node, CmptLayer.OBJECT_LAYER);
                let tempNode = cc.find(nodePath, layerNode);
                if (tempNode) {
                    let displaySpine = tempNode.getComponent(DisplaySpine);
                    displaySpine.addListener(displaySpine.getComponentInChildren(sp.Skeleton));
                    // 设置属性
                    displaySpine.setLoop(cmptSpine.loop);
                    displaySpine.setTimeScale(cmptSpine.timeScale);
                    displaySpine.setAnimation(cmptSpine.actionList);
                }
            }
        });

    }

    /**
     * vue 调用
     * @param cmptId 
     */
    public getSpineProperties(cmptId: string): any {
        let displayObj: DisplaySpine = null;
        displayObj = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cmptId) as DisplaySpine;
        let spineNode = displayObj.node.getChildByName("spineNode");
        if (!displayObj || !spineNode) {
            return null;
        }
        let prop = { offsetX: 0, offsetY: 0, scaleX: 1, scaleY: 1 };
        prop.scaleX = spineNode.scaleX;
        prop.scaleY = spineNode.scaleY;
        prop.offsetX = displayObj.offset.x;
        prop.offsetY = displayObj.offset.y;
        return prop;

    }

    /**
     * 获得节点在跟节点下的路径
     * @param node 子节点
     * @param root 根节点
     */
    private getNodePath(node: cc.Node, root: CmptLayer): string {
        let path = "";
        while (node && !(node.name === root)) {
            if (path) {
                path = `${node.name}/${path}`;
            } else {
                path = node.name;
            }
            node = node.parent;
        }
        return path;
    }

}