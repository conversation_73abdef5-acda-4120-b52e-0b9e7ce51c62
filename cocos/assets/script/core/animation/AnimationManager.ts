import { SingleBase } from "../../../qte/core/base/SingleBase";
import AudioUtil from "../../utils/AudioUtil";
import { AnimPreviewType } from "../../common/EditorEnum";
import ActionManager from "./ActionManager";
import { EAction, EAnimation, TweenData, TweenType } from "./AnimationData";
import AnimationStep from "./AnimationStep";

/**
 * 动画管理器, 处理动画主逻辑
 * <AUTHOR>
 * @date 2020-09-14
 */
export default class AnimationManager extends SingleBase {
  // 动画播放数量
  private _actionCount = 0;
  // 动画片段完成数量
  private _actionFinishCount = 0;
  private _rootNode: cc.Node = null;
  private _data: EAnimation;
  // 播放完成回调
  private _cb: (type: AnimPreviewType) => void;
  /**
   * 播放动画
   * @param {cc.Node} root 动画根节点
   * @param {EAction} data 动画数据
   * @param {() => void} cb 动画播放完成回调
   */
  public play(root: cc.Node, data: EAnimation, cb: (type: AnimPreviewType) => void): void {
    this._cb = cb;
    this._data = data;
    this._rootNode = root;
    // 重置数据
    this._actionCount = 0;
    this._actionFinishCount = 0;
    // 处理所有进入动画对象，使其默认隐藏，通过设置scale来实现。
    this.initAppearDisplayObject();
    // 预处理所有动画数据
    let allActionList = [];
    for (let key in data.points) {
      let fragmentId = data.points[key].fragmentId;
      allActionList.push(...this.getFragmentData(fragmentId));
    }
    yy.single.instance(ActionManager).reloadTweenData(allActionList);

    // 预加载音效
    let audoiList = [];
    audoiList.push(data.audio.url);
    AudioUtil.preLoadEffectList(audoiList, null, () => {
      // 播放音效
      setTimeout(() => {
        AudioUtil.playEffect(this._data.audio.url);
      }, this._data.audio.delay * 1000);
      // 播放动画
      this.next();
    });
  }

  // 播放单个动画片段
  public playStep(root: cc.Node, data: EAction[], cb: () => void) {
    this._rootNode = root;
    this._cb = cb;
    let tempAction = new AnimationStep();
    yy.single.instance(ActionManager).reloadTweenData(data);
    tempAction.play(this._rootNode, data, () => {
      if (this._cb) {
        this._cb(AnimPreviewType.FRAGMENT);
      }
    });
  }

  /** 进行一步 */
  private next(): void {
    let actions = this._data.points;
    for (let key in actions) {
      this._actionCount++;
      let action = actions[key];
      let tempAction = new AnimationStep();
      setTimeout(() => {
        tempAction.play(this._rootNode, this.getFragmentData(action.fragmentId), () => {
          this._actionFinishCount++;
          this.checkFinish();
        });
      }, action.startTime * 1000);
    }

    this.checkFinish();
  }

  /** 是否隐藏判断接口 */
  public isDisappear(type: TweenType): boolean {
    if (type === TweenType.FLY_INTO || type === TweenType.APPEAR || type === TweenType.APPEAR_OPACITY || type === TweenType.SIWEI_APPEAR || type === TweenType.SIWEI_AFTER_APPEAR) {
      return true;
    }
    return false;
  }

  /** 处理所有进入动画对象，使其默认隐藏，通过设置scale来实现 */
  private initAppearDisplayObject() {
    for (let key in this._data.points) {
      let tempData = this._data.points[key];
      let tempAction = this.getFragmentData(tempData.fragmentId);
      if (!tempAction) {
        continue;
      }
      for (let data of tempAction) {
        let cid = data.componentId;
        let node = this._rootNode.getChildByName(`cid_${cid}`);
        if (this.isDisappear((data.value.anim as TweenData).type)) {
          console.warn("缩放为0");
          node.setScale(0);
        }
      }
    }
  }

  /**
   * 检测是否播放完成
   */
  private checkFinish() {
    if (this._actionFinishCount >= this._actionCount) {
      if (this._cb) {
        this._cb(AnimPreviewType.ALL);
      }
    }
  }
  /**
   * 获得单个片段动画数据
   * @param uuid
   */
  private getFragmentData(uuid: string): EAction[] {
    return this._data.fragments[uuid];
  }
}
