import { EAction, SpineActionData, SpineAfter } from "./AnimationData";
import BaseAction from "./BaseAction";
import DisplaySpine from "./../display/base/DisplaySpine";
/**
 * spine动作
 */
export default class SpineAction extends BaseAction {
    /**
     * 播放动作
     * @param {cc.Node} node 所作用的节点
     * @param {any} data 动画数据
     * @param {() => void} cb 动作播放完成回调
     */
    public play (node: cc.Node, data: EAction, cb?: () => void): void {
        let value = data.value;
        let after = data.after as SpineAfter;
        let delay = data.delay;
        node.active = true;
        let spineData = value.anim as SpineActionData;
        let spineObject: sp.Skeleton = null;
        let displaySpine = node.getComponent(DisplaySpine);
        spineObject = node.getComponentInChildren(sp.Skeleton);
    
        setTimeout(() => {
            // spineObject.clearTracks();
            if (spineData.animName === "") {
                this.complete(displaySpine, after, spineObject, cb);
                return;
            }
            spineObject.setCompleteListener(() => {
                this.complete(displaySpine, after, spineObject, cb);
            });
            spineObject.timeScale = spineData.timeScale;
            spineObject.setAnimation(0, spineData.animName, false)
        }, delay * 1000);
    }

    private complete (displaySpine: DisplaySpine, after: SpineAfter, skeleton: sp.Skeleton, cb: () => void) {
        // 重置结束监听
        displaySpine.addListener(skeleton); 
        // 使用displayobject去播放after动画
        displaySpine.setTimeScale(after.endTimeScale);
        displaySpine.setLoop(after.loop); 
        displaySpine.setAnimation(after.animList); 
        if (cb) {
            cb();
        }
    }
}