import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { TweenData, TweenType } from "../AnimationData";
import { OptionData } from "../display/AnimDisplayManager";

export default abstract class BaseTween {

    // 动画类型
    public tweenType: TweenType = TweenType.DEFAULT;
    //
    public relative: boolean;
    /**
     * 获取数据
     * @param node
     * @param speed
     */
    public abstract actionData(node: cc.Node, speed: number, tweenData?: TweenData): any[]

    /**
     * 获取自定义点
     * @param tween
     */
    public getCustomPoints (tween: TweenData): any[] {
        let result = [];
        let option: OptionData = null;
        if (!tween.option) {
            return result;
        }
        option = tween.option;

        for (let i = 0; i < option.points.length; i++) {
            result.push(ValueUtils.setOneDecimal({x: option.points[i].x, y: option.points[i].y}));
        }
        return result;
    }

    /**
     * 获取自定义路径动画数据
     */
    public getCustomData (actionData: any[], speed: number): any[] {
        let posData = [];
        if (actionData.length <= 0) {
            return posData;
        }

        posData.push({props: {x: actionData[0].x, y: actionData[0].y }, duration: 0});
        for (let index = 0; index < actionData.length - 1; index++) {
            let nextPos = actionData[index + 1];
            let temp1 = new cc.Vec2(nextPos.x, nextPos.y);

            let time = 0;
            if (index !== actionData.length - 1) {
                time = this.duration(index, actionData, speed);
            }

            posData.push({props: ValueUtils.setOneDecimal({x: temp1.x, y: temp1.y}), duration: time});
        }

        return posData;
    }

    private duration (index: number, posArray: any[], time: number): number {
        let curDistance = 0;
        let pathLength = 0;
        posArray.forEach((pos, i) => {
            if (i > posArray.length - 2) {
                return 0;
            }
            let tempDistance = cc.Vec2.distance(pos, posArray[i + 1]);
            if (i === index) {
                curDistance = tempDistance;
            }
            pathLength += tempDistance;
        });
        return (curDistance / pathLength) * time;
    }
}
