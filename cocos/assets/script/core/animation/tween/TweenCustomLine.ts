import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 自定义路径
 */
export default class TweenCustomLine extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.CUSTOM_LINE;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        let pointsData = this.getCustomPoints(tweenData);
        resultData = this.getCustomData(pointsData, speed);
        return resultData;
    }
}