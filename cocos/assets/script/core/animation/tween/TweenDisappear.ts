import { TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 消失（缩放）
 */
export default class TweenDisappear extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.DISAPPEAR;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number): any[] {
        let resultData = [];
        resultData.push({props: {scale: 1}, duration: 0});
        resultData.push({props: {scale: 0}, duration: speed});
        return resultData;
    }
}