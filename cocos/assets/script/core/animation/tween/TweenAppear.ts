import { TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";
/**
 * 出现（放大）
 */
export default class TweenAppear extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.APPEAR;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number): any[] {
        let resultData = [];
        resultData.push({props: { scale: 0 }, duration: 0});
        resultData.push({props: { scale: 1 }, duration: speed});
        return resultData;
    }
}
