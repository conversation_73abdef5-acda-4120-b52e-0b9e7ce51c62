import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 旋转
 */
export default class TweenRotation extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.ROTATION;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        this.relative = tweenData.relative;
        let resultData = [];
        let option = {start: 0, end: 0};
        if (tweenData.option && typeof (tweenData.option) !== "number") {
            option = tweenData.option;
        }
        
        resultData = [
            {props: {angle: option.start}, duration: 0},
            {props: {angle: option.end}, duration: speed}
        ];
        return resultData;
    }
}