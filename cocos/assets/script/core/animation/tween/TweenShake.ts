import { ShakeDirections, TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 抖动
 */
export default class TweenShake extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.SHAKE;
        this.relative = true;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        resultData = this.getShakeData(tweenData, speed);
        return resultData;
    }

    /**
     * 获取抖动动画数据
     */
    private getShakeData (tweenData: TweenData, speed: number) {
        let arry = [];
        if (tweenData.option === ShakeDirections.HORIZONTAL) {
            let duration = speed / 4;
            arry.push({props: {angle: -10}, duration});
            arry.push({props: {angle: 27},  duration});
            arry.push({props: {angle: -28}, duration});
            arry.push({props: {angle: 11}, duration});
        } else if (tweenData.option === ShakeDirections.VERTICAL) {
            let duration = speed / 5;
            arry.push({props: {y: 11.5},  duration});
            arry.push({props: {y: -18.2}, duration});
            arry.push({props: {y: 14.3},  duration});
            arry.push({props: {y: -11.7}, duration});
            arry.push({props: {y: 4.1},   duration});
        }
        return arry;
    }
}