import ActionUtiil from "../../../../qte/core/extension/action/ActionUtiil";
import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 自定义曲线
 */
export default class TweenCustomCurve extends BaseTween {
  constructor() {
    super();
    this.tweenType = TweenType.CUSTON_CURVE;
    this.relative = false;
  }

  public actionData(node: cc.Node, speed: number, tweenData: TweenData): any[] {
    let resultData = [];
    // Vue缓存的点数据
    let pointsData = this.getCustomPoints(tweenData);
    // 曲线路径
    let curveData = ActionUtiil.getHermitePoints(pointsData);
    resultData = this.getCustomData(curveData, speed);
    return resultData;
  }
}
