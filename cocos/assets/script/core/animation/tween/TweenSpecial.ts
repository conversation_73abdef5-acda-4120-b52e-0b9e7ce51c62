import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 特殊动画：业务需求
 */
export default class TweenSpecial extends BaseTween {

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        switch (tweenData.type) {
        case TweenType.SIWEI_AFTER_APPEAR:
            resultData = this.afterAppearAni(node);
            break;
        case TweenType.SIWEI_APPEAR:
            resultData = this.appearAni();
            break;
        case TweenType.SIWEI_DISAPPEAR:
            resultData = this.disappearAni();
            break;
        case TweenType.SIWEI_REPEATEDLY:
            resultData = this.repeatedlyAni();
            break;
        case TweenType.SIWEI_SHAKE_X:
            resultData = this.shakeXAni();
            break;
        case TweenType.SIWEI_SHAKE_Y:
            resultData = this.shakeYAni();
            break;
        case TweenType.SIWEI_SIZE_CHANGE:
            resultData = this.sizeChangeAni();
            break;
        case TweenType.SIWEI_STAGNATION:
            resultData = this.stagnationAni();
            break;
        case TweenType.SIWEI_MOVE_LINE:
            this.relative = false;
            let linePoints = this.getCustomPoints(tweenData);
            if (linePoints.length <= 0) {
                return resultData;
            }
            resultData.push({props: { x: linePoints[1].x, y: linePoints[1].y}, duration: speed});
            break;
        default:
            break;
        }
        return resultData;
    }
    // 1. 元素大小变化 sizeChangeAni  --- 275 / 1000
    private sizeChangeAni (): any[] {
        this.relative = false;
        const aniData = [
            {
                duration: (16.67 * 0) / 1000,
                props: {
                    scale: 1
                }
            }, {
                duration: (16.67 * 10) / 1000,
                props: {
                    scale: 1.08
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    scale: 1
                }
            }
        ];
        return aniData;
    }
    // 2. 元素左右抖动 shakeXAni --- 403 /1000
    private shakeXAni (): any[] {
        this.relative = true;
        const aniData = [
            {
                duration: (16.67 * 0) / 1000,
                props: {
                    angle: 0
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: -5
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: 8
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: -4
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: 1
                }
            }
        ];
        return aniData;
    }
    // 3. 元素出现又消失 repeatedlyAni --- 800 /1000
    private repeatedlyAni (): any[] {
        this.relative = false;
        const aniData = [
            {
                duration: 0,
                props: {
                    scale: 1
                }
            },
            {
                duration: (16.67 * 0) / 1000,
                props: {
                    opacity: 255
                }
            }, {
                duration: (16.67 * 20) / 1000,
                props: {
                    opacity: 0
                }
            }, {
                duration: (16.67 * 10) / 1000,
                props: {
                    opacity: 255
                }
            }, {
                duration: (16.67 * 10) / 1000,
                props: {
                    opacity: 255
                }
            }, {
                duration: (16.67 * 10) / 1000,
                props: {
                    opacity: 0
                }
            }, {
                duration: 0,
                props: {
                    scale: 0
                }
            }
        ];
        return aniData;
    }
    // 4. 元素出现后左右抖动 afterAppearAni   876/1000
    private afterAppearAni (node: cc.Node): any[] {
        this.relative = false;
        const aniData = [
            {
                duration: 0,
                props: {
                    scale: 1,
                    opacity: 0
                }
            }, {
                duration: (16.67 * 20) / 1000,
                props: {
                    opacity: 255
                }
            }, {
                duration: (16.67 * 12) / 1000,
                props: {
                    angle: node.angle
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: node.angle - 10
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: node.angle + 7
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: node.angle - 4
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: node.angle + 2
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    angle: node.angle
                }
            }
        ];
        return aniData;
    }
    // 5. 元素上下抖动 shakeYAni --- 497
    private shakeYAni (): any[] {
        this.relative = true;
        const aniData = [
            {
                duration: (16.67 * 6) / 1000,
                props: {
                    y: 11.5
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    y: -18.5
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    y: 14.375
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    y: -11.75
                }
            }, {
                duration: (16.67 * 6) / 1000,
                props: {
                    y: 4.125
                }
            }
        ];
        return aniData;
    }
    // 6. 元素停滞 stagnationAni  335 / 1000
    private stagnationAni () {
        this.relative = false;
        const aniData = [
            {
                duration: (16.67 * 0) / 1000,
                props: {
                    opacity: 0
                }
            }, {
                duration: (16.67 * 20) / 1000,
                props: {
                    opacity: 255
                }
            }
        ];
        return aniData;
    }
    //   // 7. 元素出现（非主动配置，默认添加在每一个动效元素上）appearAni 335 / 1000
    private appearAni (): any[] {
        this.relative = false;
        const aniData = [
            {
                duration: 0,
                props: {
                    scale: 1
                }
            }, {
                duration: (16.67 * 0) / 1000,
                props: {
                    opacity: 0
                }
            }, {
                duration: (16.67 * 20) / 1000,
                props: {
                    opacity: 255
                }
            }
        ];
        return aniData;
    }
    // 8. 元素消失（非主动配置，默认添加在每一个动效元素上）disappearAni 335 / 1000
    private disappearAni (): any[] {
        this.relative = false;
        const aniData = [
            {
                duration: (16.67 * 0) / 1000,
                props: {
                    opacity: 255
                }
            }, {
                duration: (16.67 * 20) / 1000,
                props: {
                    opacity: 0
                }
            }, {
                duration: 0,
                props: {
                    scale: 0
                }
            }
        ];
        return aniData;
    }
}


/*
 *   // 动画对应时间
 *   const aniTime = {
 *     sizeChangeAni: 0.28,
 *     shakeXAni: 0.4,
 *     repeatedlyAni: 0.8,
 *     afterAppearAni: 0.9,
 *     shakeYAni: 0.5,
 *     stagnationAni: 0.33,
 *     appearAni: 0.33,
 *     disappearAni: 0.33
 *   };
 */
