import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 缩放
 */
export default class TweenScale extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.SCALE;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        let option = {start: 0, end: 0};
        if (typeof (tweenData.option) !== "number") {
            option = tweenData.option;
        }
        resultData.push({props: {scale: option.start}, duration: 0});
        resultData.push({props: {scale: option.end}, duration: speed});
        return resultData;
    }
}