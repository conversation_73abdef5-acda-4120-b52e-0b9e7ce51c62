import DataCenterBridge from "../../proxy/DataCenterBridge";
import { DirectionType, TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 飞出
 */
export default class TweenFlyOut extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.FLY_OUT;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        let flyOutData = this.getFlyOutData(node, tweenData.option);
        resultData.push({ props: { x: flyOutData[0].x, y: flyOutData[0].y }, duration: 0});
        resultData.push({ props: { x: flyOutData[1].x, y: flyOutData[1].y }, duration: speed });
        return resultData;
    }
    /**
     * 获取飞出数据
     * @param node 
     * @param option 
     */
    private getFlyOutData (node: cc.Node, option: DirectionType): any {
        let posArry = this.getFlyIntoData(node, option);
        let data = [];
        data.push(posArry[1]);
        data.push(posArry[0]);
        return data;
    }

    /**
     * 获取飞入数据
     * @param cc.Node node 节点cc.Node
     * @param option 
     */
    private getFlyIntoData (node: cc.Node, option: DirectionType): any {
        let data = [];
        let targetPos = node.position;
        let stageInfo = yy.single.instance(DataCenterBridge).getStageInfo();
        let startPos = cc.v2(0, 0);
        switch (option) {
        case DirectionType.DOWN:
            startPos = cc.v2(targetPos.x, stageInfo.safeHeight / 2 + node.height);
            break;
        case DirectionType.LEFT_DOWN:
            startPos = cc.v2(stageInfo.width / 2 + node.width, stageInfo.safeHeight / 2 + node.height);
            break;
        case DirectionType.RIGHT:
            startPos = cc.v2(-stageInfo.width / 2 - node.width, targetPos.y);
            break;
        case DirectionType.LEFT_UP:
            startPos = cc.v2(stageInfo.width / 2 + node.width, -stageInfo.safeHeight / 2 - node.height);
            break;
        case DirectionType.UP:
            startPos = cc.v2(targetPos.x, -stageInfo.safeHeight / 2 - node.height);
            break;
        case DirectionType.RIGHT_UP:
            startPos = cc.v2(-stageInfo.width / 2 - node.width, -stageInfo.safeHeight / 2 - node.height);
            break;
        case DirectionType.LEFT:
            startPos = cc.v2(stageInfo.width / 2 + node.width, targetPos.y);
            break;
        case DirectionType.RIGHT_DOWN:
            startPos = cc.v2(-stageInfo.width / 2 - node.width, stageInfo.safeHeight / 2 + node.height);
            break;
        default:
            startPos = cc.v2(targetPos.x, stageInfo.safeHeight / 2 + node.height);
        }
        data.push(startPos);
        data.push(targetPos);
        return data;
    }
}