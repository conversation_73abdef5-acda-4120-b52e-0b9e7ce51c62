import { TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 消失（透明度）
 */
export default class TweenDisappearOpacity extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.DISAPPEAR_OPACITY;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number): any[] {
        let resultData = [];
        resultData.push({props: {opacity: 255}, duration: 0});
        resultData.push({props: {opacity: 0}, duration: speed});
        resultData.push({props: {scale: 0}, duration: 0});
        return resultData;
    }
}