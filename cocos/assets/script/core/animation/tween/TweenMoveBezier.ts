import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 贝塞尔曲线（3阶）
 */
export default class TweenMoveBezier extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.MOVE_BEZIRE;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        this.relative = false;
        let resultData = [];
        let points = this.getCustomPoints(tweenData);
        resultData.push({props: { x: points[0].x, y: points[0].y}, duration: 0});
        resultData.push(
            {
                props: {
                    c1: 
                        {
                            x: points[1].x, y: points[1].y
                        },
                    c2: 
                        {
                            x: points[2].x, y: points[2].y
                        },
                    p2: 
                        {
                            x: points[3].x, y: points[3].y
                        }
                },
                duration: speed
            });
        return resultData;
    }
}