
import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 闪烁
 */
export default class TweenBink extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.BLINK;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        let option = {start: 0, end: 0};
        if (tweenData.option && typeof (tweenData.option) !== "number") {
            option = tweenData.option;
        }
        resultData.push({props: {opacity: option.start}, duration: 0});
        resultData.push({props: {opacity: option.end}, duration: speed});
        return resultData;
    }
}
