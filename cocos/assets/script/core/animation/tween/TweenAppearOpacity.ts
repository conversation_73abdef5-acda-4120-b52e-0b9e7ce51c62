import { TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";
/**
 * 出现（透明度）
 */
export default class TweenAppearOpacity extends BaseTween {
    constructor () {
        super();
        this.tweenType = TweenType.APPEAR_OPACITY;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number): any[] {
        let resultData = [];
        resultData.push({props: {opacity: 0, scale: 1},  duration: 0});
        resultData.push({props: {opacity: 255}, duration: speed });
        return resultData;
    }
}
