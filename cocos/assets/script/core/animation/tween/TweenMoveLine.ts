import { TweenData, TweenType } from "../AnimationData";
import BaseTween from "./BaseTween";

/**
 * 直线运动
 */
export default class TweenMoveLine extends BaseTween {

    constructor () {
        super();
        this.tweenType = TweenType.MOVE_LINE;
        this.relative = false;
    }

    public actionData (node: cc.Node, speed: number, tweenData: TweenData): any[] {
        let resultData = [];
        let linePoints = this.getCustomPoints(tweenData);
        if (linePoints.length <= 0) {
            return resultData;
        }
        resultData.push({props: { x: linePoints[0].x, y: linePoints[0].y}, duration: 0});
        resultData.push({props: { x: linePoints[1].x, y: linePoints[1].y}, duration: speed});

        return resultData;
    }
}