import { EditorGroup } from "../../../common/EditorEnum";
import DisplayObject from "../../display/base/DisplayObject";
import { TweenType } from "../AnimationData";

/**
 * 动画编辑节点
 */
export default class AnimDisplayObject extends DisplayObject {

    private _displayType: TweenType;
    public get displayType (): TweenType {
        return this._displayType;
    }
    public set displayType (type: TweenType) {
        this._displayType = type;
        if (type === TweenType.SIWEI_MOVE_LINE) {
            let cmptId = this.cid.split("_")[3];
            if (cmptId === "0") {
                this.editable = false;
            }
        } else {
            this.editable = true; 
        }
    }

    // 旧属性
    private _oldProperties: any;
    public get oldProperties (): any {
        return this._oldProperties;
    }

    // 新属性
    private _newProperties: {};
    public get newProperties () {
        return this._newProperties;
    }

    private _graphics: cc.Graphics;
   
    // 设置选中状态
    public setSelect (selected: boolean) {
        if (selected) {
            this.showEdgeColor(cc.Color.RED);
        } else {
            this.showEdgeColor(cc.Color.BLACK);
        }
        this._oldProperties = {x: this.node.position.x, y: this.node.position.y};
    }
    /** 显示边框 */
    public showEdgeColor (color: cc.Color): void {
        let ctrlSpNode = this.node.getChildByName("ctrl_sp");
        let edgeNode: cc.Node = null;
        if (ctrlSpNode) {
            edgeNode = ctrlSpNode.getChildByName("edge_node");
        }
        if (!edgeNode) {
            edgeNode = new cc.Node();
            edgeNode.addComponent(cc.Graphics);
            edgeNode.name = "edge_node";
            edgeNode.group = EditorGroup.EDGE;
            ctrlSpNode.addChild(edgeNode);
        }
        let graphics = edgeNode.getComponent(cc.Graphics);
        graphics.strokeColor = color;
        graphics.lineWidth = 3;
        graphics.clear(true);
        graphics.moveTo(-this.node.width / 2, -this.node.height / 2);
        graphics.lineTo(-this.node.width / 2, this.node.height / 2);
        graphics.lineTo(this.node.width / 2, this.node.height / 2);
        graphics.lineTo(this.node.width / 2, -this.node.height / 2);
        graphics.lineTo(-this.node.width / 2, -this.node.height / 2);
        graphics.stroke();
    }

    public clearEdgeline () {
        if (this._graphics) {
            this._graphics.clear();
        }
    }

    /**
     *
     * @param pos 结束点
     * @param reset 是否移动到起始位置
     */
    public drawLine (pos: any[], reset: boolean = true) {
        if (!this._graphics) {
            let edgeNode = this.node.getChildByName("line_node");
            edgeNode = new cc.Node();
            this._graphics = edgeNode.addComponent(cc.Graphics);
            edgeNode.name = "line_node";
            this.node.addChild(edgeNode);
            this._graphics.strokeColor = cc.Color.BLUE;
            this._graphics.lineWidth = 3;
        }
        this._graphics.clear();
        this._graphics.moveTo(0, 0);
        let i = 1;
        if (reset) {
            i = 0;
        }
        for (i; i < pos.length; i++) {
            let temp1 = new cc.Vec3(pos[i].x, pos[i].y, 0);
            let drawPos = this.node.convertToNodeSpaceAR(temp1);
            this._graphics.lineTo(drawPos.x, drawPos.y);
        }
        this._graphics.stroke();
    }

    public drawLastPos (posAarray: any[]) {
        if (posAarray.length < 2) {
            return;
        }
        if (!this._graphics) {
            let edgeNode = this.node.getChildByName("line_node");
            edgeNode = new cc.Node();
            this._graphics = edgeNode.addComponent(cc.Graphics);
            edgeNode.name = "line_node";
            this.node.addChild(edgeNode);
            this._graphics.lineWidth = 3;
        }
        this._graphics.strokeColor = cc.Color.BLUE;
        let posToPos = posAarray[posAarray.length - 1];
        let posFromPos = posAarray[posAarray.length - 2];
        this._graphics.moveTo(this.convertToNodeSpaceAR(this.node, posFromPos).x, this.convertToNodeSpaceAR(this.node, posFromPos).y);

        this._graphics.lineTo(this.convertToNodeSpaceAR(this.node, posToPos).x, this.convertToNodeSpaceAR(this.node, posToPos).y);
        /*
         * Let drawToPos = this.node.convertToNodeSpaceAR(tempTo);
         * let drawFromPos = this.node.convertToNodeSpaceAR
         */
        this._graphics.stroke();
    }

    private convertToNodeSpaceAR (node: cc.Node, pos: any): cc.Vec3 {
        let tempPos = new cc.Vec3(pos.x, pos.y, 0);
        return node.convertToNodeSpaceAR(tempPos);
    }

    /**
     * 画线段
     * startList: [[x,y]]
     */
    public drawBezierVertex (startList: any[], endList: any[]) {
        if (startList.length !== endList.length) {
            return;
        }
        startList.forEach((item, index) => {
            let startPos = this.convertToNodeSpaceAR(this.node, cc.v2(item[0], item[1]));
            let endPos = this.convertToNodeSpaceAR(this.node, cc.v2(endList[index][0], endList[index][1]));
            this._graphics.moveTo(startPos.x, startPos.y);
            this._graphics.lineTo(endPos.x, endPos.y);
        });
        this._graphics.stroke();
    }

    /**
     * 将显示图片更改为结束点
     */
    public changeEndSprite () {
        let spNode = this.node.getChildByName("ctrl_sp");
        let spCmpt = spNode.getComponent(cc.Sprite);
        if (!spCmpt) {
            return;
        }
        yy.loader.loadRes("img/arrow", cc.SpriteFrame, (err, texture: cc.SpriteFrame) => {
            if (err) {
                yy.warn(err);
                return;
            }
            if (!cc.isValid(this.node)) {
                return;
            }
            spCmpt.spriteFrame = texture;
            spNode.width = 30;
            spNode.height = 20;
            spNode.color = cc.Color.RED;
        });
    }
}
