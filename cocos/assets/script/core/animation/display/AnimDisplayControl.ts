/* eslint-disable max-lines */
import { SingleBase } from "../../../../qte/core/base/SingleBase";
import MathUtil from "../../../../qte/core/utils/MathUtil";
import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { CmptLayer, EditMode } from "../../../common/EditorEnum";
import TemplateInterpreter from "../../display/TemplateInterpreter";
import DataCenterBridge from "../../proxy/DataCenterBridge";
import { EAction, EValue, TweenData, TweenEditType, TweenType } from "../AnimationData";
import AnimDisplayManager, { OptionData } from "./AnimDisplayManager";
import AnimDisplayObject from "./AnimDisplayObject";
// 一个动画数据
class ActionData {
    // 动作id
    public actionId = "";
    // 单个动画数据
    public value: EValue;
    // 处于那个动画模式  0 - 组件动画 1 - 全局动画
    public actionType? = -1; 
    // 动画类型
    public get tweenType (): TweenType {
        let tweenData = this.value.anim as TweenData;
        return tweenData.type;
    }
    constructor (actionid: string, value: EValue) {
        this.actionId = actionid;
        this.value = value;
    }
}
// 单个动画片段数据
class FragmentsData {
    // 片段id
    private _fragmentId = "-1";
    public get fragmentId (): string {
        return this._fragmentId;
    }
    // 组件动画归类 《cmptid, actionData》
    public _actionMap: Map<string, ActionData[]>;

    constructor (fragmentId: string) {
        this._actionMap = new Map<string, ActionData[]>();
        this._fragmentId = fragmentId;
    }
    /**
     * 添加一个动画
     * @param {string} cmptId 组件id
     * @param {EValue} value 动画数据
     * @param {string} actionId
     */
    public addAction (cmptId: string, value: EValue, actionId: string) {
        let tempAc = new ActionData(actionId, value);
        if (this._actionMap.has(cmptId)) {
            let exitAction = this._actionMap.get(cmptId);
            exitAction.push(tempAc);
        } else {
            this._actionMap.set(cmptId, [tempAc]);
        }
    }
    /**
     * 获得一个组件点动画数据
     * @param {string} componentId 组件id
     */
    public getAction (componentId: string): ActionData[] {
        if (!this._actionMap.has(componentId)) {
            return [];
        }
        return this._actionMap.get(componentId);
    }
}

/**
 * 自由路线数据
 */
class CustomDaata {
    // 动画类型
    public _editTweenType: TweenType;
    // 编辑的组件id
    public _editComponentId: string;
    // 当前编辑的动作id
    public _editActionId = "";
    // Option数据长度
    public _optionLength = 0;
    // 锁定和不锁定
    public _relative: boolean;
}
export default class AnimDisplayControl extends SingleBase {
    private _animdisplayManager: AnimDisplayManager;

    // 全局模式编辑的动画片段id
    public _editFragment = "";
    // 组件动画编辑下的动画片段id
    public _cmptEditFragment = "";

    // ///// 当前编辑的数据
    public customLineData: CustomDaata;
    // 当前编辑模式
    public editType: TweenEditType;
    // 动画模式还是普通模式
    private _editMode: EditMode = EditMode.NORMAL; // 默认普通模式
    public get editMode (): EditMode {
        return this._editMode;
    }
    public set editMode (e: EditMode) {
        this._editMode = e;
    }

    // 组件动画编辑模式
    private _isEditingComponentAnimations: boolean;
    public get isEditingComponentAnimations (): boolean {
        return this._isEditingComponentAnimations;
    }
    public set isEditingComponentAnimations (value) {
        this._isEditingComponentAnimations = value;
    }

    // 是不是动画编辑 或者组件动画编辑
    public get idAnimationEdit (): boolean {
        return this._isEditingComponentAnimations || this.editMode === EditMode.ANIM;
    }
    /** 所有动画列表: [fragmentid: [cmptid: [action]]]*/
    private _fragments: Map<string, FragmentsData>;

    public initInstance () {
        this._fragments = new Map();
        this.customLineData = new CustomDaata();
        this._animdisplayManager = yy.single.instance(AnimDisplayManager);
    }

    // 结束自定义划线
    public endCustomLine (): void {
        // This._animdisplayManager.clearAnimDisplayObject();
        this.editType = TweenEditType.VERTEX;
        this.setActiveAction(this.customLineData._editActionId);
    }

    /**
     * 模式切换 清空数据
     */
    public resetMode () {
        this._animdisplayManager.clearAnimDisplayObject();
        this._fragments = new Map();
        this.customLineData = new CustomDaata();
        this.editType = TweenEditType.DEFAULT;
    }
    // 获取当前编辑模式
    public getEditType (): TweenEditType {
        return this.editType;
    }

    // 画出最后一个节点到鼠标的线
    public drawMouseLine (position): void {
        let cmptids = this._animdisplayManager.getAnimDisplayObjects(this.customLineData._editActionId);
        if (!cmptids || cmptids.length <= 0) {
            return;
        }

        if (this.customLineData._editTweenType === TweenType.CUSTOM_LINE) {
            this._animdisplayManager.drawCustomLine(false, cmptids, position);
        } else if (this.customLineData._editTweenType === TweenType.CUSTON_CURVE) {
            this._animdisplayManager.drawCustomCurve(cmptids, position);
        }
    }

    // 画一个点
    public drawDisplayObj (position): void {
        if (this.isDuplicateNode(position)) {
            return;
        }
        let editActionId = this.customLineData._editActionId;
        let editTweenType = this.customLineData._editTweenType;
        let displayIds = this._animdisplayManager.getAnimDisplayObjects(editActionId);
        // 自定义曲线画到40个点强制结束
        if (editTweenType === TweenType.CUSTON_CURVE && displayIds.length > 30) {
            let optionData = this.getActionOption("-1");
            this.saveOptionData(editActionId, optionData);
            this.endCustomLine();
            return;
        }
        let oldComponentIds = this._animdisplayManager.getAnimDisplayObjects(editActionId);
        let componentId = this.customLineData._editComponentId.toString();
        // 创建一个控制点
        let cmptid = `cid_${componentId}_${editActionId}_${oldComponentIds.length + 1}`;
        let tempNode = null;
        if (oldComponentIds.length === 0) {
            tempNode = this._animdisplayManager.createAnimObject(cmptid, { x: position.x, y: position.y }, 0, 2);
        } else {
            tempNode = this._animdisplayManager.createAnimObject(cmptid, { x: position.x, y: position.y });
        }
        tempNode.name = cmptid;
        let displayObj = tempNode.getComponent(AnimDisplayObject);
        displayObj.displayType = editTweenType;

        this._animdisplayManager.addAnimDisplayObject(cmptid, displayObj, editActionId);
        yy.single.instance(TemplateInterpreter).addObjFunc(tempNode, CmptLayer.ANIM_LAYER);

        // 画线
        this.drawCustomPath(editActionId, editTweenType);
    }

    /**
     * Displayobject 组件属性更新
     */
    public updateDispObject (cmptId: string, property: any) {
        let actions = this.getActionsByCmptID(cmptId);
        for (let action of actions) {
            let tweenData = action.value.anim as TweenData;
            // 思维动画移动
            if (tweenData.type === TweenType.SIWEI_MOVE_LINE) {
                let cmpts = this._animdisplayManager.getAnimDisplayObjects(action.actionId);
                let pos = new cc.Vec3(property.x, property.y);
                this.updateDisplayObj(cmpts[0], pos, action.actionId);
                continue;
            }
            if (!tweenData.relative) {
                continue;
            }
            let cmpts = this._animdisplayManager.getAnimDisplayObjects(action.actionId);
            for (let id of cmpts) {
                // Let displayObj = this._animDisplayManager.getAnimDisplayObject(id);
                let pos = new cc.Vec3(property.x, property.y);
                // 坐标移动增量,给该显示对象做累加
                this.updateDisplayObj(id, pos, action.actionId);
            }
        }
        this.updateActionOption(cmptId);
    }

    // 节点移动
    public updateDisplayObj (cmptId, pos, actionId: string): void {
        this._animdisplayManager.updateDisplayObjectDeltaPos(actionId, cmptId, pos);
        let cmptids = this._animdisplayManager.getAnimDisplayObjects(actionId);
        this._animdisplayManager.setDisObjectDir(cmptids);
    }

    /**
     * 获得当前编辑action的option数据
     */
    public getActionOption (selectObjectId: string): OptionData {
        if (this.customLineData._editActionId === "-1") {
            return;
        }
        let actionData = this.getActiveAction() as EAction;
        let actionId = actionData.id;
        let optionData = new OptionData();
        let cmptIds = this._animdisplayManager.getAnimDisplayObjects(actionId);

        for (let i = 0; i < cmptIds.length; i++) {
            let tempNode = this._animdisplayManager.getAnimDisplayObject(cmptIds[i]);
            optionData.points.push({ x: tempNode.node.x, y: tempNode.node.y });
        }

        optionData.currIndex = this._animdisplayManager.getIndex(selectObjectId);

        optionData.offset = this._animdisplayManager.getOffestPosition(cmptIds[0], this.customLineData._editComponentId);
        return optionData;
    }

    /**
     * 设置当前划线模式
     */
    private reSetEditType () {
        let resultType = TweenEditType.DEFAULT;
        switch (this.customLineData._editTweenType) {
        case TweenType.MOVE_LINE:
        case TweenType.MOVE_BEZIRE:
        case TweenType.SIWEI_MOVE_LINE:
            resultType = TweenEditType.VERTEX;
            break;
        case TweenType.CUSTOM_LINE:
        case TweenType.CUSTON_CURVE:
            if (this.customLineData._optionLength > 1) {
                resultType = TweenEditType.VERTEX;
            } else {
                resultType = TweenEditType.DRAW;
            }
            break;
        default:
            break;
        }
        this.editType = resultType;
    }

    /**
     * 检测新增点和上一个点是不是重复的
     * @param pos
     */
    private isDuplicateNode (pos: cc.Vec2): boolean {
        let componentIds = this._animdisplayManager.getAnimDisplayObjects(this.customLineData._editActionId);
        if (!componentIds || componentIds.length < 1) {
            return false;
        }
        let cmptId = componentIds[componentIds.length - 1];
        let dspObj = this._animdisplayManager.getAnimDisplayObject(cmptId);
        let offset = MathUtil.pointSubtractionForPoint(pos, dspObj.node.position);
        if (Math.abs(offset.x) < 30 && Math.abs(offset.y) < 30) {
            return true;
        }
        return false;
    }

    /**
     * Displayobject 位置改变 更新对应相对动画的数据
     * @param cmptId 现实对象id
     */
    public updateActionOption (cmptId: string, isVue: boolean = true) {
        let actionDatas: ActionData[] = this.getAllActionsByCmptID(cmptId);

        if (!actionDatas || actionDatas.length <= 0) {
            return;
        }
        for (let i = 0; i < actionDatas.length; i++) {
            let tweenData = actionDatas[i].value.anim as TweenData;
            if (!tweenData.option || tweenData.option.length <= 0) {
                continue;
            }
            tweenData.option = this._animdisplayManager.resetOption(cmptId, actionDatas[i].actionId, tweenData);
            // 如果修改修改动画数据已经被展示出来  需要改变offset
            let cmpts = this._animdisplayManager.getAnimDisplayObjects(actionDatas[i].actionId);
            if (cmpts.length > 0) {
                tweenData.option.offset = this._animdisplayManager.getOffestPosition(cmpts[0], cmptId);
            }
            // 通知修改属性
            if (isVue) {
                this.saveOptionData(actionDatas[i].actionId, tweenData.option, actionDatas[i].actionType);
            }
        }
    }

    /**
     * 展示所有路径
     * @param componentId 组件id
     */
    public showAllPah (componentId: string | string[], selectId?: string) {
        if (!componentId) {
            return;
        }
        this.editType = TweenEditType.DEFAULT;
        this._animdisplayManager.clearAnimDisplayObject();
        
        if (typeof (componentId) === "object") { 
            for (let id of componentId) {
                let actionDatas: ActionData[] = this.getActionsByCmptID(id);
                if (!actionDatas || actionDatas.length <= 0) {
                    continue;
                }
                
                this.showActionPath(actionDatas, id);
            }
        } else {
            // 拿到对应组件的所有动画
            let actionData: ActionData[] = this.getActionsByCmptID(componentId.toString());
            if (!actionData || actionData.length <= 0) {
                return;
            }
            this.showActionPath(actionData, componentId.toString());
        }
        if (selectId) {
            // 清空已有选中
            this._animdisplayManager.clearSelect();
            // 设置actionId路径选中状态
            this._animdisplayManager.setSelectAction(selectId, true);
            let activeAction = this.getActiveAction() as EAction;
            if (!activeAction) {
                return;
            }
            this.setCustomData(activeAction);
            this.reSetEditType();
        }
    }

    /**
     * 展示组件对应的动画
     * @param actionDatas 动画数据
     * @param componentId 组件id
     */
    private showActionPath (actionDatas: ActionData[], componentId: string) {
        // 显示路径
        for (let data of actionDatas) {
            let tweenData = data.value.anim as TweenData;
            // 选中对动画数据只有一个点 开启画线模式 同时展示其他路径
            if (!tweenData.option || !tweenData.option.points || tweenData.option.points.length <= 1) {
                tweenData = this._animdisplayManager.structPointsData(tweenData, componentId);
                // 保存数据
                this.saveOptionData(data.actionId, tweenData.option);
               
            }
            this._animdisplayManager.addDisObjectByDatas(tweenData.option, componentId, tweenData.type, data.actionId);
            // 画线
            this.drawCustomPath(data.actionId, tweenData.type);
            // 设置相对位置
            this._animdisplayManager.setOffsetPos(componentId, data.actionId, tweenData);
            tweenData.option = this._animdisplayManager.resetOption(componentId, data.actionId, tweenData);
            if (tweenData.type !== TweenType.SIWEI_MOVE_LINE) {
                tweenData.option.currIndex = -1;
            }

            // 设置选中状态
            this._animdisplayManager.setSelectAction(data.actionId, true)
        }
    }

    /**
     * 选中一个动画点
     * @param animCmptId 节点id
     */
    public selecAnimObj (animCmptId: string): void {
        if (animCmptId === "-1") {
            return;
        }
        let selectActionId = this._animdisplayManager.getActionById(animCmptId);
        /*
         * If (this.customLineData._editActionId == selectActionId) return;
         * 取消已有选中路径选中状态
         */
        this._animdisplayManager.clearSelect();
        // 设置选中点为选中状态
        this._animdisplayManager.setSelectId(animCmptId);

        // 设置选中动画
        let dataCenter = yy.single.instance(DataCenterBridge);
        if (this.editMode === EditMode.ANIM) { // 优先动画模式
            dataCenter.setAnimModelActiveActionId(selectActionId);
        } else if (this.isEditingComponentAnimations) { // 组件动画编辑模式
            dataCenter.setCmptModelActiveActionId(selectActionId);
        }
        let activeAction = this.getActiveAction() as EAction;
        if (!activeAction) {
            return;
        }
        this.setCustomData(activeAction);
    }

    /**
     * 选中动画
     * @param actionId 动作id
     */
    public setActiveAction (actionId: string) {
        // 从列表里获取对应动画数据
        let activeAction = this.getActiveAction() as EAction;
        if (!activeAction) {
            // 清空已有选中
            this._animdisplayManager.clearSelect();
            return;
        }
        // 更改最后一个点sprite
        let ids = this._animdisplayManager.getAnimDisplayObjects(actionId);
        let object = this._animdisplayManager.getAnimDisplayObject(ids[ids.length - 1]);
        object.changeEndSprite();
        // 清空已有选中
        this._animdisplayManager.clearSelect();
        // 设置actionId路径选中状态
        this._animdisplayManager.setSelectAction(actionId, true);
    }

    /**
     * 画线
     * @param actionid 动画id
     */
    public drawCustomPath (actionid: string, type: TweenType) {
        let cmptids = this._animdisplayManager.getAnimDisplayObjects(actionid);
        if (!cmptids || cmptids.length <= 0) {
            return;
        }
        // 划直线
        if (type === TweenType.MOVE_LINE) {
            this._animdisplayManager.drawCustomLine(true, cmptids);
        } else if (type === TweenType.MOVE_BEZIRE) {
            this._animdisplayManager.drawBezier(cmptids);
        } else if (type === TweenType.CUSTOM_LINE || type === TweenType.SIWEI_MOVE_LINE) {
            this._animdisplayManager.drawCustomLine(true, cmptids);
        } else if (type === TweenType.CUSTON_CURVE) {
            this._animdisplayManager.drawCustomCurve(cmptids);
        }
        // 画设置起始点lookat
        this._animdisplayManager.setDisObjectDir(cmptids);
    }

    /**
     * 设置选中动画片段id
     */
    public setFragmentId (fragmentId: string) {
        if (this._editMode === EditMode.ANIM) {
            this._editFragment = fragmentId;
        } else if (this.isEditingComponentAnimations) {
            this._cmptEditFragment = fragmentId;
        }
    }

    /**
     * 获取所有动画数据
     */
    public getFlatActions () {
        let allActions = [];
        let dataCenter = yy.single.instance(DataCenterBridge);
        if (this.editMode === EditMode.ANIM) { // 优先获取动画模式下所有动画数据
            allActions = dataCenter.getAnimModelFlatActions();
        } else if (this.isEditingComponentAnimations) { // 组件动画编辑
            allActions = dataCenter.getCmptModelFlatActions();
        }
        
        return yy.cloneValues(allActions);
    }

    /**
     * 获取选中动画的数据
     */
    public getActiveAction () {
        let action = [];
        let dataCenter = yy.single.instance(DataCenterBridge);
        if (this.editMode === EditMode.ANIM) { // 优先动画模式
            action = dataCenter.getAnimModelActiveAction();
        } else if (this.isEditingComponentAnimations) { // 组件动画编辑模式
            action = dataCenter.getCmptModelActiveAction();
        }

        return ValueUtils.clone(action);
    }

    /**
     * 获取动画在哪个片段中
     * @param actionId 动作id
     * @param cmptId 组件id
     */
    public getFragmentByActionId (actionId: string, cmptId: string): string {
        let fragmentId = "";
        this._fragments.forEach((data) => {
            let actionDatas = data.getAction(cmptId);
            for (let actionData of actionDatas) {
                if (actionData.actionId === actionId) {
                    fragmentId = data.fragmentId;
                }
            }
        });
        return fragmentId;
    }

    /**
     * 更新本地动画数据
     */
    public updateFragmentsMap () {
        let allActions = this.getFlatActions();
        if (!allActions) {
            yy.log("======allActions null===> ", this.editMode)
            return;
        }
        this.setActionMap(allActions);
    }

    /**
     * 设置动画map
     * @param actionDatas
     */
    private setActionMap (actionDatas: any[]) {
        this._fragments = new Map();
        for (let ac of actionDatas) {
            let tweenData = ac.value.anim as TweenData;
            let fragmentId = ac.fragmentId;
            // 只保存自定义动画
            if (!this.isCustomAnimType(tweenData)) {
                continue;
            }
            let tempAc: FragmentsData = null;

            if (this._fragments.has(fragmentId)) {
                tempAc = this._fragments.get(fragmentId);
            } else {
                tempAc = new FragmentsData(fragmentId);
                this._fragments.set(fragmentId, tempAc);
            }
            tempAc.addAction(ac.componentId, ac.value, ac.id);
        }
    }

    /**
     * 设置当前编辑的动画 数据
     * @param editAction
     */
    private setCustomData (editAction: EAction) {
        let activeAction = editAction as EAction;
        let activeTween = activeAction.value.anim as TweenData;

        this.customLineData._editActionId = activeAction.id;
        this.customLineData._editComponentId = activeAction.componentId;
        this.customLineData._editTweenType = activeTween.type;
        if (!activeTween.option || !activeTween.option.points) {
            return;
        }
        this.customLineData._optionLength = activeTween.option.points.length;
        this.customLineData._relative = activeTween.relative;
    }

    /**
     * 从当前选中片段中 获取组件身上的自由编辑动画数据
     * @param componentId displayobject id
     */
    public getActionsByCmptID (componentId: string): ActionData[] {
        let resultAcData = [];
        let fragmentId = "";
        if (this._editMode === EditMode.ANIM) {
            fragmentId = this._editFragment;
        } else if (this.isEditingComponentAnimations) {
            fragmentId = this._cmptEditFragment;
        }
        let fragmentData = this._fragments.get(fragmentId);
        if (!fragmentData) {
            return resultAcData;
        }
        return fragmentData.getAction(componentId);
    }
    /**
     * 获取所有片段中组件的动画
     * @param componentId displayobject id
     */
    public getAllActionsByCmptID (componentId: string): ActionData[] {
        let resultAcData = [];
        let dataCenter = yy.single.instance(DataCenterBridge);
        let cmptModeAction = yy.cloneValues(dataCenter.getCmptModelFlatActions());
        let animModeAction = yy.cloneValues(dataCenter.getAnimModelFlatActions());
        cmptModeAction.forEach((element) => {
            if (element.componentId === componentId) {
                let actionData = new ActionData(element.id, element.value);
                actionData.actionType = 0;
                resultAcData.push(actionData);
            }
        });

        animModeAction.forEach((element) => {
            if (element.componentId === componentId) {
                let actionData = new ActionData(element.id, element.value);
                actionData.actionType = 1;
                resultAcData.push(actionData);
            }
        });
        return resultAcData;
    }
    // 判断是不是自由编辑的动画
    public isCustomAnimType (animData: TweenData): boolean {
        if (animData.type === TweenType.MOVE_LINE || animData.type === TweenType.MOVE_BEZIRE || animData.type === TweenType.CUSTOM_LINE || animData.type === TweenType.CUSTON_CURVE || animData.type === TweenType.SIWEI_MOVE_LINE) {
            return true;
        }
        return false;
    }

    // 保存option data
    public saveOptionData (actionId: string, option: OptionData, actionType: number = -1) {
        if (actionType !== -1) {
            if (actionType === 0) {
                yy.single.instance(DataCenterBridge).setCmptActionOption(actionId, option);
            } else {
                yy.single.instance(DataCenterBridge).setActionAnimOption(actionId, option);
            }
            return;
        }

        if (this.editMode === EditMode.ANIM) {
            yy.single.instance(DataCenterBridge).setActionAnimOption(actionId, option);
        } else if (this.isEditingComponentAnimations) {
            yy.single.instance(DataCenterBridge).setCmptActionOption(actionId, option);
        }
    }
}
