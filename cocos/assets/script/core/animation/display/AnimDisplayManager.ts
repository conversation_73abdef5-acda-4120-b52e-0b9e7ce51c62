
import { SingleBase } from "../../../../qte/core/base/SingleBase";
import ActionUtiil from "../../../../qte/core/extension/action/ActionUtiil";
import MathUtil from "../../../../qte/core/utils/MathUtil";
import ValueUtils from "../../../../qte/core/utils/ValueUtils";
import { CmptLayer, EditorGroup } from "../../../common/EditorEnum";
import DisplayObjectManager from "../../display/DisplayObjectManager";
import TemplateInterpreter from "../../display/TemplateInterpreter";
import { TweenData, TweenType } from "../AnimationData";
import AnimDisplayObject from "./AnimDisplayObject";

export class OptionData {
    public points: {x: number, y: number}[];
    public offset: {x: number, y: number};
    // 当前选中point
    currIndex: number;

    constructor () {
        this.currIndex = -1;
        this.points = [];
    }
}
export default class AnimDisplayManager extends SingleBase {

    // 当前编辑的动画id
    private _curActiveAnimationId: string = "";
    public get curActiveAnimationId (): string {
        return this._curActiveAnimationId;
    }
    public set curActiveAnimationId (value: string) {
        this._curActiveAnimationId = value;
    }

    // 动画节点集合
    private _dpMap: Map<string, AnimDisplayObject> = null;
    public get dpMap (): Map<string, AnimDisplayObject> {
        return this._dpMap;
    }

    /** 一个action 对应的动画节点点数据 《actionid， pointsid》*/
    private _componentIds: Map<string, string[]>;
    public get componentIds (): Map<string, string[]> {
        return this._componentIds;
    }

    public initInstance (): void {
        this._dpMap = new Map();
        this._componentIds = new Map();
    }

    /**
     * 创建多个点
     * @param optionData 节点数据
     * @param componentId display组件id
     * @param type 动画类型
     * @param actionId 动作id
     */
    public addDisObjectByDatas (optionData: OptionData, componentId: string, type: TweenType, actionId: string) {
        if (!optionData || optionData.points.length <= 0) {
            return;
        }
        // 有点数据
        for (let i = 0; i < optionData.points.length; i++) {
            let cmptid = `cid_${componentId}_${actionId}_${i}`;
            let tempNode = this.createAnimObject(cmptid, optionData.points[i], i, optionData.points.length);
            let displayObj = tempNode.getComponent(AnimDisplayObject);
            tempNode.name = cmptid;
            displayObj.displayType = type;
            this.addAnimDisplayObject(cmptid, displayObj, actionId);
            yy.single.instance(TemplateInterpreter).addObjFunc(tempNode, CmptLayer.ANIM_LAYER);
        }
    }

    /**
     * 组件移动设置跟随偏移
     * @param componentId 组件id
     * @param actionId
     * @param tweenData
     */
    public setOffsetPos (componentId: string, actionId: string, tweenData: TweenData) {
        let option = tweenData.option;
        if (!option || !option.offset) {
            return option;
        }
        if (!tweenData.relative) {
            return;
        }
        let displayObject = yy.single.instance(DisplayObjectManager).getDisplayObjectById(componentId);
        let cmpts = this.getAnimDisplayObjects(actionId);
        if (!cmpts || cmpts.length <= 0) {
            return;
        }
        let distanceX = 0, distanceY = 0;
        let firstNode = this.getAnimDisplayObject(cmpts[0]);
        distanceX = firstNode.node.x - displayObject.node.x;
        distanceY = firstNode.node.y - displayObject.node.y;
        for (let id of cmpts) {
            const tempNode = this.getAnimDisplayObject(id);
            // 设置相对位置
            tempNode.node.x -= (distanceX - option.offset.x);
            tempNode.node.y -= (distanceY - option.offset.y);
        }
    }

    /**
     * 位置发生改变，重新计算option
     */
    public resetOption (componentId: string, actionId: string, tweenData: TweenData): OptionData {
        let displayObject = yy.single.instance(DisplayObjectManager).getDisplayObjectById(componentId);
        let resOption = new OptionData();
        if (!tweenData.option || !tweenData.option.points || tweenData.option.points.length < 2) {
            return tweenData.option;
        }
        if (tweenData.relative) {
            let points = tweenData.option.points;
            let distanceX = 0, distanceY = 0;
            distanceX = points[0].x - displayObject.node.x;
            distanceY = points[0].y - displayObject.node.y;
            for (let i = 0; i < points.length; i++) {
                let tempX = points[i].x - (distanceX - tweenData.option.offset.x);
                let tempY = points[i].y - (distanceY - tweenData.option.offset.y);
                resOption.points.push((ValueUtils.setOneDecimal({x: tempX, y: tempY})));
            }
        } else {
            resOption.points = tweenData.option.points;
        }
        if (tweenData.type === TweenType.SIWEI_MOVE_LINE) {
            resOption.points[0] = ValueUtils.setOneDecimal({x: displayObject.node.x, y: displayObject.node.y});
        }
        resOption.offset = tweenData.option.offset;
        resOption.currIndex = tweenData.option.currIndex;

        return resOption;
    }

    /**
     * 设置节点转向
     * @param componentIds 路径节点id数组
     * @param onlyStart 是不是只设置startNode
     */
    public setDisObjectDir (componentIds: string[], onlyStart = false) {
        if (componentIds.length < 2) {
            return;
        }
        for (let i = 0; i < componentIds.length; i++) {
            let endDsp : AnimDisplayObject = null, startDsp: AnimDisplayObject = null;
            let spNode: cc.Node = null;
            if (i === 0) { // 起始点
                startDsp = this.getAnimDisplayObject(componentIds[i]);
                spNode = startDsp.node.getChildByName("ctrl_sp");
                this.setAngle(componentIds[i], componentIds[i + 1], spNode);
            }
            if (onlyStart) {
                return;
            }
            if (i === componentIds.length - 1) { // 最后一个点
                endDsp = this.getAnimDisplayObject(componentIds[i]);
                spNode = endDsp.node.getChildByName("ctrl_sp");
                this.setAngle(componentIds[i - 1], componentIds[i], spNode);
            }
        }

    }

    /**
     * 设置角度
     * @param startIndex
     * @param endIndex 
     * @param spNode 
     */
    private setAngle (startIndex, endIndex, spNode) {
        let startDsp = this.getAnimDisplayObject(startIndex);
        let endDsp = this.getAnimDisplayObject(endIndex);
        let angle = this.getAngle(cc.v2(startDsp.node.x, startDsp.node.y), cc.v2(endDsp.node.x, endDsp.node.y));
        if (spNode) {
            spNode.angle = angle;
        }
    }

    /**
     * 设置相对位置
     * @param isRelative
     * @param componentId
     */
    public getOffestPosition (animDispId: string, componentId: string): {x: number, y: number} {
        let tempNode = this.getAnimDisplayObject(animDispId);
        let offset = {x: 0, y: 0};
        let cmpt = yy.single.instance(DisplayObjectManager).getDisplayObjectById(componentId);
        let pos = tempNode.node.position;

        offset.x = cmpt.node.parent.convertToNodeSpaceAR(cc.v3(pos.x, pos.y)).x - cmpt.node.x;
        offset.y = cmpt.node.parent.convertToNodeSpaceAR(cc.v3(pos.x, pos.y)).y - cmpt.node.y;

        return offset;
    }
    /**
     * 添加动画编辑节点
     * @param componetId
     * @param object
     */
    public addAnimDisplayObject (componetId: string, object: AnimDisplayObject, actionId: string) {
        this._dpMap.set(componetId, object);
        if (this._componentIds.has(actionId)) {
            let tempIdMap = this._componentIds.get(actionId);
            tempIdMap.push(componetId);
        } else {
            this._componentIds.set(actionId, [componetId]);
        }
    }

    // 获取节点在路径中的index
    public getIndex (componetId: string) {
        let index = -1;
        this._componentIds.forEach((ids) => {
            if (ids.indexOf(componetId) > -1) {
                index = ids.indexOf(componetId);
            }
        });
        return index;
    }

    /**
     * 获取点在哪个动画片段里
     * @param componetId
     */
    public getActionById (componetId: string) {
        let actionId = "";

        this._componentIds.forEach((value, key) => {
            if (value.indexOf(componetId) > -1) {
                actionId = key;
            }
        });
        return actionId;
    }

    /**
     * 获取单个object
     * @param componetId
     */
    public getAnimDisplayObject (componetId: string): AnimDisplayObject {
        return this._dpMap.get(componetId);
    }

    /**
     * 获取action 对应的所有节点id
     * @param actionId
     */
    public getAnimDisplayObjects (actionId: string): string[] {
        if (!this._componentIds.has(actionId)) {
            return [];
        }
        return this._componentIds.get(actionId);
    }

    /**
     * 获取所有点点数组
     */
    public getComponetIdArray (): string[] {
        let result = [];
        this._componentIds.forEach((ids) => {
            result.push(...ids);
        });
        return result;
    }

    /** 根据类型获取节点type */
    public getObjByType (id: string, animType: TweenType): AnimDisplayObject[] {
        let reslut = [];
        this._dpMap.forEach((value) => {
            if (value.displayType === animType && value.cid !== id) {
                reslut.push(value);
            }
        });
        return reslut;
    }


    // 选中组件
    public setSelectId (componetId: string) {
        let displayObj = this.getAnimDisplayObject(componetId);
        if (!displayObj) {
            return;
        }

        displayObj.setSelect(true);
    }


    /**
     * 设置一条路径为选中
     * @param actionId 动作id
     * @param select 选中状态
     */
    public setSelectAction (actionId: string, select: boolean) {
        let cmptIds = this.getAnimDisplayObjects(actionId);
        if (!cmptIds) {
            return;
        }
        for (let i = 0; i < cmptIds.length; i++) {
            let tempNode = this.getAnimDisplayObject(cmptIds[i]);
            tempNode.setSelect(select);
        }
    }

    /**
     * 清空所有选中状态
     */
    public clearSelect () {
        this._dpMap.forEach((value) => {
            value.setSelect(false);
        });
    }

    /**
     * 构造默认点数据
     * @param e_anim
     * @param componentId
     */
    public structPointsData (tweenData: TweenData, componentId: string): TweenData {
        let displayObj = yy.single.instance(DisplayObjectManager).getDisplayObjectById(componentId);
        let optionData = new OptionData();
        let posY = 0;
        // 直线 构造两个点
        if (tweenData.type === TweenType.MOVE_LINE || tweenData.type === TweenType.SIWEI_MOVE_LINE) {
            optionData.points.push({x: displayObj.node.position.x, y: displayObj.node.position.y });
            if (displayObj.node.position.y > 0) { // 向下
                posY = displayObj.node.position.y - 150;
            } else { // 向上
                posY = displayObj.node.position.y + 150;
            }
            optionData.points.push({x: displayObj.node.position.x, y: posY });
        } else if (tweenData.type === TweenType.MOVE_BEZIRE) {  // Bezire 构造三个点
            optionData.points.push({ x: displayObj.node.position.x, y: displayObj.node.position.y });
            if (displayObj.node.position.y > 0) { // 向下
                posY = -1;
            } else { // 向上
                posY = 1;
            }

            optionData.points.push({ x: displayObj.node.position.x, y: displayObj.node.position.y + 150 * posY });
            optionData.points.push({ x: displayObj.node.position.x, y: displayObj.node.position.y + 200 * posY});
            optionData.points.push({ x: displayObj.node.position.x, y: displayObj.node.position.y + 250 * posY});
        }
        optionData.offset = {x: 0, y: 0};
        optionData.currIndex = -1;
        if (tweenData.type === TweenType.SIWEI_MOVE_LINE) {
            optionData.currIndex = 1;
        }
        tweenData.option = optionData;
        return tweenData;
    }

    /**
     * 移除所有已存在的操作节点
     */
    public clearAnimDisplayObject () {
        this._dpMap.forEach((value) => {
            value.clearEdgeline();
            value.node.destroy();
        });
        this._dpMap.clear();
        this._dpMap = new Map();
        this._componentIds = new Map();
    }


    /**
     * 更新属性增量
     * @param editActionId  当前编辑的动画id
     * @param cmptId 动画节点id
     * @param pos 坐标
     */
    public updateDisplayObjectDeltaPos (editActionId: string, cmptId: string, pos: cc.Vec3): void {
        let curObj = this.getAnimDisplayObject(cmptId);
        if (!curObj) {
            yy.error(`id = ${cmptId}的组件不存在`);
            return;
        }
        curObj.node.position = curObj.node.position.add(pos);
        let cmptids = this.getAnimDisplayObjects(editActionId);
        // 划直线
        if (curObj.displayType === TweenType.MOVE_LINE) {
            this.drawCustomLine(true, cmptids);
        } else if (curObj.displayType === TweenType.MOVE_BEZIRE) {
            this.drawBezier(cmptids);
        } else if (curObj.displayType === TweenType.CUSTOM_LINE || curObj.displayType === TweenType.SIWEI_MOVE_LINE) {
            this.drawCustomLine(true, cmptids);
        } else if (curObj.displayType === TweenType.CUSTON_CURVE) {
            this.drawCustomCurve(cmptids);
        }
    }

    /**
     * 创建动画操作节点 - 暂时处理逻辑
     * @param componentId 节点id
     * @param prop 节点属性
     * @param index 第几个点
     */
    public createAnimObject (componentId: string, prop: { x: number, y: number }, index?: number, allCount?: number): cc.Node {
        let node = new cc.Node();
        let displayObj = node.addComponent(AnimDisplayObject);
        // 将图片修改到子节点
        let spNode = new cc.Node("ctrl_sp");
        let sp = spNode.addComponent(cc.Sprite);
        sp.sizeMode = cc.Sprite.SizeMode.CUSTOM;
        let path = "img/arrow";
        let color = new cc.Color().fromHEX("#ffffff"); ;
        let size = cc.v2(30, 20);
        let setProperty = () => {
            // 属性进行赋值
            node.x = prop.x;
            node.y = prop.y;
            node.width = size.x;
            node.height = size.y;
            node.scale = 1;
            // 图片现实大小
            spNode.width = size.x;
            spNode.height = size.y;
            spNode.color = color;
        };

        if (index === 0) {
            color = cc.Color.GREEN;
        } else if (index === allCount - 1) {
            color = cc.Color.RED;
        } else {
            size.x = 20;
            path = "singleColor";
        }
        yy.loader.loadRes(path, cc.SpriteFrame, (err, texture: cc.SpriteFrame) => {
            if (err) {
                yy.warn(err);
                return;
            }
            if (!sp || !cc.isValid(node)) {
                return;
            }
            sp.spriteFrame = texture;
        });
        setProperty();
        displayObj.cid = componentId;
        node.addChild(spNode);
        node.group = EditorGroup.EDGE;
        return node;
    }

    /**
     * 画曲线
     */
    public drawBezier (componentId: string[]) {
        let pos: Array<number[]> = [];
        let dspObj = this.getAnimDisplayObject(componentId[0]);
        let nodePos = [];
        for (let i = 0; i < componentId.length; i++) {
            let tempNode = this.getAnimDisplayObject(componentId[i]).node;
            nodePos.push([tempNode.position.x, tempNode.position.y]);
        }
        if (nodePos.length < 4) {
            yy.error("drawBezier points length 小于 4");
            return;
        }
        pos = ActionUtiil.getBezierPoints(30, nodePos[0], nodePos[1], nodePos[2], nodePos[3]);
        let resultPos = [];
        for (let i = 1; i < pos.length; i++) {
            let temp1 = new cc.Vec3(pos[i][0], pos[i][1], 0);
            resultPos.push(temp1);
        }
        dspObj.clearEdgeline();
        dspObj.drawLine(resultPos);
        // 画贝塞尔控制点连线
        dspObj.drawBezierVertex([nodePos[0], nodePos[2]], [nodePos[1], nodePos[3]]);
    }

    /**
     *  画自定义线
     * @param isNew 是否从新划线
     * @param componentIds 动画点ids
     * @param mousPosition 鼠标移动的点 最后一个点作为开始点
     */
    public drawCustomLine (isNew: boolean, componentIds: string[], mousPosition?: cc.Vec3) {
        let startNode = this.getAnimDisplayObject(componentIds[0]);
        if (!startNode) {
            return;
        }
        // 清除最后一个点的划线
        let clearIndex = componentIds.length - 1;
        if (componentIds.length >= 2) {
            clearIndex = componentIds.length - 2;
        }
        let endNode = this.getAnimDisplayObject(componentIds[componentIds.length - 1]);
        let clearNode = this.getAnimDisplayObject(componentIds[clearIndex]);
        endNode.clearEdgeline();
        // 重新划线
        if (isNew) {
            clearNode.clearEdgeline();
            let posAry = this.getNodePos(componentIds);
            startNode.clearEdgeline();
            startNode.drawLine(posAry, false);
            return;
        }
        // 画最后一个点到鼠标的线
        if (mousPosition) {
            let pos = [mousPosition];
            endNode.drawLine(pos);
            if (clearIndex !== 0) {
                clearNode.clearEdgeline();
            }
        }
        // 画倒数第二个点到最后一个点的连线
        if (mousPosition) {
            return;
        }
        clearNode.clearEdgeline();
        let posAry = this.getNodePos(componentIds);
        startNode.drawLastPos(posAry);
    }

    /**
     * 画自由曲线
     */
    public drawCustomCurve (componentIds: string[], mousePos?: cc.Vec3) {
        let startNode = this.getAnimDisplayObject(componentIds[0]);
        if (!startNode) {
            return;
        }
        let posAry = this.getNodePos(componentIds);
        if (mousePos) {
            posAry.push(mousePos);
        }
        let customCurveArray = posAry;
        if (posAry.length > 2) {
            customCurveArray = ActionUtiil.getHermitePoints(posAry);
        } else {
            customCurveArray = posAry;
        }
        
        startNode.drawLine(customCurveArray, false);
    }

    private getNodePos (componentIds: string[]): any[] {
        let result = [];
        for (let i = 0; i < componentIds.length; i++) {
            let tempNode = this.getAnimDisplayObject(componentIds[i]);
            if (!tempNode) {
                continue;
            }
            let tempPos = { x: tempNode.node.x, y: tempNode.node.y };
            result.push(tempPos);
        }
        return result;
    }

    // 获取第一个点到第二点的方向向量的角度
    private getAngle (startPos: cc.Vec2, endPos: cc.Vec2):number {
        let dir = endPos.sub(startPos);
        let angle = MathUtil.calcYaw(cc.v2(dir.x, dir.y));
        return 90 - angle;
    }
}
