import AudioUtil from "../../utils/AudioUtil";
import { CmptLayer } from "../../common/EditorEnum";
import DisplayObjectManager from "../display/DisplayObjectManager";
import { AfterType, AnimType, EAction, EAudio, MomentType, TweenData } from "./AnimationData";
import AnimationManager from "./AnimationManager";
import BaseAction from "./BaseAction";
import ClipAction from "./ClipAction";
import SpineAction from "./SpineAction";
import TweenAction from "./TweenAction";

/**
 * 动画片段管理, 处理动画片段播放
 * <AUTHOR>
 * @date 2020-09-14
 */
export default class AnimationStep {
  // 当前动画数据
  private _data: EAction[];
  // 动画所作用的根节点
  private _rootNode: cc.Node;
  // 当前动画中action引用计数
  private _actionCount: number;
  public get actionCount(): number {
    return this._actionCount;
  }
  public set actionCount(value: number) {
    this._actionCount = value;
  }
  // 动画播放完成回调
  private _cb: () => void;

  /**
   * 播放动画
   * @param {cc.Node} root 动画根节点
   * @param {EAction[]} data 动画数据
   * @param {() => void} cb 动画播放完成回调
   */
  public play(root: cc.Node, data: EAction[], cb: () => void): void {
    this.actionCount = 0;
    this._cb = cb;
    this._data = data;
    this._rootNode = root;
    // 处理所有进入动画对象，使其默认隐藏，通过设置scale来实现。
    this.initAppearDisplayObject();

    // 预加载音效
    let audoiList = [];
    for (let tempData of this._data) {
      audoiList.push(tempData.audio.url);
    }
    AudioUtil.preLoadEffectList(audoiList, null, () => {
      // 执行一步
      this.step();
    });
  }

  /** 处理所有进入动画对象，使其默认隐藏，通过设置scale来实现 */
  private initAppearDisplayObject() {
    for (let tempData of this._data) {
      let cid = tempData.componentId;
      let displayObject = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cid);
      let nodePath = this.getNodePath(displayObject.node, CmptLayer.OBJECT_LAYER);
      let node = cc.find(nodePath, this._rootNode);

      if (yy.single.instance(AnimationManager).isDisappear((tempData.value.anim as TweenData).type)) {
        console.warn("1111111111");
        node.setScale(0);
      }
    }
  }

  /** 进行一步 */
  private step(): void {
    let actions = this._data;
    if (actions.length <= 0) {
      if (this._cb) {
        this._cb();
      }
      return;
    }
    let action = actions.shift();
    this.actionHandler(action);
    // 检查下一个action是否是与当前action同时触发的
    if (actions.length > 0) {
      let head = actions[0];
      if (head.moment === MomentType.BEFORE) {
        this.step();
      }
    }
  }

  /** 处理动作逻辑 */
  private actionHandler(action: EAction): void {
    let cid = action.componentId;
    let displayObject = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cid);
    let nodePath = this.getNodePath(displayObject.node, CmptLayer.OBJECT_LAYER);
    let node = cc.find(nodePath, this._rootNode);
    // 播放当前动作
    let value = action.value;
    let ac = this.createAction(value.animType);
    this.actionCount++;
    ac.play(node, action, () => {
      this.playAudioEffect(action.audio, false);
      this.actionCount--;
      // 处理动作after
      if (action.after.type === AfterType.HIDE) {
        node.scale = 0;
      }
      this.checkNext();
    });
    // 播放音效
    this.playAudioEffect(action.audio, true);
  }

  /** 检查是否进行下一步 */
  private checkNext(): void {
    if (this.actionCount <= 0) {
      this.step();
    }
  }

  /**
   * 根据action类型创建action对象
   * @param {AnimType} type action类型
   */
  private createAction(type: AnimType): BaseAction {
    let ac: BaseAction = null;
    if (type === AnimType.ANIM) {
      ac = new ClipAction();
    } else if (type === AnimType.TWEEN) {
      ac = new TweenAction();
    } else if (type === AnimType.SPINE) {
      ac = new SpineAction();
    }
    return ac;
  }

  /** 播放音效 */
  private playAudioEffect(audioData: EAudio, isStart) {
    if ((audioData.moment === 0 && isStart) || (audioData.moment === 1 && !isStart)) {
      setTimeout(() => {
        AudioUtil.playEffect(audioData.url);
      }, audioData.delay * 1000);
    }
  }

  /**
   * 获得节点在跟节点下的路径
   * @param node 子节点
   * @param root 根节点
   */
  private getNodePath(node: cc.Node, root: CmptLayer): string {
    let path = "";
    while (node && !(node.name === root)) {
      if (path) {
        path = `${node.name}/${path}`;
      } else {
        path = node.name;
      }
      node = node.parent;
    }
    return path;
  }
}
