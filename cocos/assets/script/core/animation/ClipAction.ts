import BaseAction from "./BaseAction";
import { EAction, SpineActionData, SpineAfter } from "./AnimationData";
import DisplayCocosAni from "../display/base/DisplayCocosAni";

/**
 *
 */
export default class ClipAction extends BaseAction {
  public play(node: cc.Node, data: EAction, cb?: () => void): void {
    let value = data.value;
    let after = data.after as SpineAfter;
    let delay = data.delay;
    node.active = true;
    let spineData = value.anim as SpineActionData;
    let displayCocosAni = node.getComponent(DisplayCocosAni);
    let animationObject = node.getComponentInChildren(cc.Animation);

    // 没有配置动画名称
    if (spineData.animName === "") {
      this.complete(displayCocosAni, after, animationObject, cb);
      return;
    }

    // 监听播放完成事件
    animationObject.on(
      cc.Animation.EventType.FINISHED,
      () => {
        this.complete(displayCocosAni, after, animationObject, cb);
      },
      this
    );

    const clipName: string = value.anim["animName"];
    let crtClip: cc.AnimationClip = null;
    // 速度设置
    setTimeout(() => {
      // 获取clip
      const clips = animationObject.getClips();
      for (let i = 0; i < clips.length; i++) {
        if (clips[i].name == clipName) {
          crtClip = clips[i];
          break;
        }
      }
      // 设置速度
      crtClip.speed = spineData.timeScale;
      animationObject.play(clipName);
    }, delay * 1000);
  }

  private complete(
    displayCocosAni: DisplayCocosAni,
    after: SpineAfter,
    animation: cc.Animation,
    cb: () => void
  ) {
    // 重置结束监听
    displayCocosAni.addListener(animation);
    // 使用displayobject去播放after动画
    displayCocosAni.setTimeScale(after.endTimeScale);
    displayCocosAni.setLoop(after.loop);
    displayCocosAni.setAnimation(after.animList);
    if (cb) {
      cb();
    }
  }
}
