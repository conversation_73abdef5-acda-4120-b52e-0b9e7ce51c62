import { result } from "lodash";
import { SingleBase } from "../../../qte/core/base/SingleBase";
import ValueUtils from "../../../qte/core/utils/ValueUtils";
import DisplayCocosAni from "../display/base/DisplayCocosAni";
import DisplaySpine from "../display/base/DisplaySpine";
import DisplayObjectManager from "../display/DisplayObjectManager";
import { CocosAniActionData, AnimType, EAction, EValue, SpineActionData, SpineAfter, TweenData, TweenType } from "./AnimationData";
import BaseTween from "./tween/BaseTween";
import TweenAppear from "./tween/TweenAppear";
import TweenAppearOpacity from "./tween/TweenAppearOpacity";
import TweenBink from "./tween/TweenBlink";
import TweenCustomCurve from "./tween/TweenCustomCurve";
import TweenCustomLine from "./tween/TweenCustomLine";
import TweenDisappear from "./tween/TweenDisappear";
import TweenDisappearOpacity from "./tween/TweenDisappearOpacity";
import TweenFlyInto from "./tween/TweenFlyInto";
import TweenFlyOut from "./tween/TweenFlyOut";
import TweenMoveBezier from "./tween/TweenMoveBezier";
import TweenMoveLine from "./tween/TweenMoveLine";
import TweenRotation from "./tween/TweenRotation";
import TweenScale from "./tween/TweenScale";
import TweenShake from "./tween/TweenShake";
import TweenSpecial from "./tween/TweenSpecial";

/**
 * 动作管理器：用于处理动画片段中的单独动作播放，使用tween实现。
 * <AUTHOR>
 */
export default class ActionManager extends SingleBase {
  /** 缓存 */
  private _tweenMap: Map<number, BaseTween> = new Map();

  /** 所有动作数据  <fragmentId, data[] >*/
  private _tweenDataMap: Map<string, any[]> = new Map();

  public initInstance() {
    for (let key in TweenType) {
      let cls = this.getImpClsByType(Number(key));
      if (cls) {
        this._tweenMap.set(Number(key), cls);
      }
    }
  }

  /**
   * 预处理所有的数据并且缓存 动画数据
   * @param {EAction[]} actionData 动画数据列表
   */
  public reloadTweenData(actionData: EAction[]) {
    this._tweenDataMap.clear();
    for (let i = 0, len = actionData.length; i < len; i++) {
      let eValue = actionData[i].value;
      if (eValue.animType !== AnimType.TWEEN) {
        continue;
      }
      let actionId = actionData[i].id;
      let tweenData = this.getTweenActionDatas(actionData[i].componentId, eValue);
      this._tweenDataMap.set(actionId, tweenData);
    }
  }

  /**
   * 获取缓存tween data
   * @param {string} actionId
   */
  public getTweenData(actionId: string): any {
    return this._tweenDataMap.get(actionId);
  }
  // ////////////////
  /**
   * 获取构造好的tween data
   * @param {cc.Node | string} componentId 节点id或者cc.Node
   * @param value
   */
  public getTweenActionDatas(componentId: cc.Node | string, value: EValue): any {
    let node: cc.Node = null;
    let resultData: any = null;
    let tweenData = value.anim as TweenData;
    let tweenCls = this._tweenMap.get(tweenData.type);
    if (!tweenCls) {
      yy.error(`类型${tweenData.type}的实例不存在`);
      return [];
    }
    if (typeof componentId === "string") {
      console.log("getTweenActionDatas", componentId);
      let display = yy.single.instance(DisplayObjectManager).getDisplayObjectById(componentId);
      node = display.node;
    } else {
      node = componentId;
    }

    let animData = tweenCls.actionData(node, value.speed, tweenData);
    resultData = this.structDAta(tweenCls.relative, value.repeat, value.easing, animData);
    // 贝塞尔曲线特殊处理
    if (tweenData.type === TweenType.MOVE_BEZIRE) {
      resultData[1].type = "bezier";
    }
    return resultData;
  }

  private structDAta(relative, repeat, easing, option: { props: any; duration: number }[]) {
    let result = [];
    option.forEach(item => {
      let prop = { type: "", time: 0, props: null, easing: null };
      if (relative) {
        prop.type = "by";
      } else {
        prop.type = "to";
      }
      prop.time = ValueUtils.setOneDecimal(item.duration, 3);
      prop.props = item.props;
      prop.easing = easing;
      result.push(prop);
    });
    return result;
  }

  // ////////////////////////////////////////////////////////
  /**
   * 获取clip curvedata
   * @param value
   */
  public getClipCurveData(value: EValue): any {
    let crdata = {
      props: {
        scale: [
          {
            frame: 0,
            value: {
              __type__: "cc.Vec2",
              x: 0.5,
              y: 1,
            },
          },
          {
            frame: 0.5833333333333334,
            value: {
              __type__: "cc.Vec2",
              x: 1,
              y: 1,
            },
          },
        ],
      },
    };
    return crdata;
  }

  /**
   * 获取spine导出数据
   * @param cmptId 组件id
   * @param value
   */
  public getSpineData(cmptId: string | cc.Node, actionData: EAction): any {
    let display: DisplaySpine = null;
    if (typeof cmptId === "string") {
      display = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cmptId) as DisplaySpine;
    } else {
      display = cmptId.getComponent(DisplaySpine);
    }

    let value = actionData.value;
    let afterData = actionData.after as SpineAfter;
    let resultData = {} as any;
    let after = {} as any; // 结束事件
    let spineData = value.anim as SpineActionData;
    resultData.animation = spineData.animName;
    resultData.timeScale = spineData.timeScale;
    resultData.loop = false;
    resultData.duration = display.getSpineAnimationTime(spineData.animName) / resultData.timeScale;
    after.loop = afterData.loop;
    after.endTimeScale = afterData.endTimeScale;
    after.animList = afterData.animList;
    resultData.after = after;
    return resultData;
  }

  /**
   * 获取animation导出数据
   * @param cmptId 组件id
   * @param value
   */
  public getCocosAniData(cmptId: string | cc.Node, actionData: EAction): any {
    let display: DisplayCocosAni = null;
    if (typeof cmptId === "string") {
      display = yy.single.instance(DisplayObjectManager).getDisplayObjectById(cmptId) as DisplayCocosAni;
    } else {
      display = cmptId.getComponent(DisplayCocosAni);
    }
    // cocosAni节点
    const cocosAniNode = display.node.getChildByName("cocosAniNode");
    const amimationCmpt = cocosAniNode.getComponent(cc.Animation);
    let clips = amimationCmpt.getClips();

    let value = actionData.value;
    let afterData = actionData.after as SpineAfter;
    let resultData = {} as any;
    let after = {} as any; // 结束事件

    let animData = value.anim as CocosAniActionData;
    let clipData = null;
    // 获取到clip的数据
    for (let i = 0; i < clips.length; i++) {
      let tempClip = clips[i];
      if (tempClip.name === actionData.value.anim["animName"]) {
        clipData = tempClip;
        break;
      }
    }
    resultData.animation = animData.animName;
    resultData.timeScale = animData.timeScale;
    resultData.loop = false;
    resultData.duration = clipData._duration;
    // 动画数据
    resultData.curveData = clipData.curveData;
    resultData.events = clipData.events;
    resultData.wrapMode = clipData.wrapMode;
    resultData.sample = clipData.sample;
    resultData.speed = clipData.speed;
    resultData._name = clipData.name;
    resultData._duration = clipData._duration;
    // resultData.animData = animData;

    after.loop = afterData.loop;
    after.endTimeScale = afterData.endTimeScale;
    after.animList = afterData.animList;
    resultData.after = after;
    return resultData;
  }

  /**
   * 获取动画数据(外部vue调用)
   * @param componentId 组件id
   * @param type
   * @param option
   */
  public getActionByType(componentId: string | cc.Node, eAction: EAction): any {
    let actionData: any = null;
    console.log("getActionByType", componentId, eAction.value);
    if (eAction.value.animType === AnimType.SPINE) {
      actionData = this.getSpineData(componentId, eAction);
    } else if (eAction.value.animType === AnimType.ANIM) {
      actionData = this.getCocosAniData(componentId, eAction);
    } else {
      actionData = this.getTweenActionDatas(componentId, eAction.value);
    }
    return actionData;
  }

  /** 动画实例 */
  private getImpClsByType(type: TweenType): BaseTween {
    let baseTween: BaseTween = null;
    if (type >= TweenType.SIWEI_SIZE_CHANGE && type <= TweenType.SIWEI_MOVE_LINE) {
      baseTween = new TweenSpecial();
      return baseTween;
    }
    switch (type) {
      case TweenType.APPEAR:
        baseTween = new TweenAppear();
        break;
      case TweenType.APPEAR_OPACITY:
        baseTween = new TweenAppearOpacity();
        break;
      case TweenType.BLINK:
        baseTween = new TweenBink();
        break;
      case TweenType.CUSTOM_LINE:
        baseTween = new TweenCustomLine();
        break;
      case TweenType.CUSTON_CURVE:
        baseTween = new TweenCustomCurve();
        break;
      case TweenType.DISAPPEAR:
        baseTween = new TweenDisappear();
        break;
      case TweenType.DISAPPEAR_OPACITY:
        baseTween = new TweenDisappearOpacity();
        break;
      case TweenType.FLY_INTO:
        baseTween = new TweenFlyInto();
        break;
      case TweenType.FLY_OUT:
        baseTween = new TweenFlyOut();
        break;
      case TweenType.MOVE_BEZIRE:
        baseTween = new TweenMoveBezier();
        break;
      case TweenType.MOVE_LINE:
        baseTween = new TweenMoveLine();
        break;
      case TweenType.ROTATION:
        baseTween = new TweenRotation();
        break;
      case TweenType.SCALE:
        baseTween = new TweenScale();
        break;
      case TweenType.SHAKE:
        baseTween = new TweenShake();
        break;
      default:
        break;
    }
    return baseTween;
  }
}
