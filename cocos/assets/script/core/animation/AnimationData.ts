
/** 动画播放时机 */
export enum MomentType {
    BEFORE          = 0,    // 之前
    AFTER           = 1,    // 之后
}

/** 动画类型： animation、tween等 */
export enum AnimType {
    TWEEN           = "tween",          // Tween缓动动画
    ANIM            = "cocosAni",      // Cocos格式的动画配置Animation
    SPINE           = "spine"           // spine动画
}

/** 动作结束后操作类型：无操作、渐隐消失等 */
export enum AfterType {
    NONE            = 0,    // 无操作
    HIDE            = 1,    // 隐藏
}

/** 抖动方向枚举 */
export enum ShakeDirections {
    VERTICAL = 0,
    HORIZONTAL = 1
}

/** 一个动画片段数据 */
export class EAnimation {
    // Audio
    public audio: EAudio;
    // 动作数组
    public fragments: EAction[];
    // 开始时间
    public points: {fragmentId: string, startTime: number}[];
}

/** 一个动画动作 */
export class EAction {
    // 动作id
    public id: string;
    // 组件id
    public componentId: string;
    // 动作播放时机
    public moment: MomentType;
    // 动作播放的延迟时间，默认为0
    public delay: number;
    // 动作数据配置
    public value: EValue;
    // 动作音效
    public audio: EAudio;
    // 动作结束后紧跟的动作配置
    public after: BaseAfter;
}

/** 动作数据配置 */
export class EValue {
    // 动画类型
    public animType: AnimType;
    // 动画数据
    public anim: ActionDataBase;
    // 速度
    public speed: number;
    // 重复次数: -1为无限循环
    public repeat: number;
    // 缓动模式
    public easing: string;
}

/** 动作音效配置 */
export class EAudio {
    // 音效资源地址
    public url: string;
    // 音效播放时机
    public moment: MomentType;
    // 延迟时间
    public delay: number;
}

export class BaseAfter {
    public type: AfterType;
}

/** tween动作结束后紧跟的动作配置 */
export class EAfter extends BaseAfter {

}

/** spine动作结束后紧跟的动作配置, 目前cocosAni也使用这个类，但是这三个字段好像没有被使用 */
export class SpineAfter extends BaseAfter {
    public loop: boolean;      
    public animList: string[];
    public endTimeScale: number;
}

// 动画片段对应的开始时间
export class EPoint {
    // Uuid
    public fragmentId: string;
    // 开始时间
    public startTime: string;
}

/** 方向 */
export enum DirectionType {
    UP = 0,
    RIGHT_UP = 1,
    RIGHT = 2,
    RIGHT_DOWN = 3,
    DOWN = 4,
    LEFT_DOWN = 5,
    LEFT = 6,
    LEFT_UP = 7,
}

/** Tween动画类型 */
export enum TweenType {
    DEFAULT         = 0,        // 默认
    FLY_INTO        = 1,        // 飞入
    FLY_OUT         = 2,        // 飞出
    APPEAR          = 3,        // 出现
    DISAPPEAR       = 4,        // 消失
    SCALE           = 5,        // 放大缩小
    ROTATION        = 6,        // 旋转
    MOVE_LINE       = 7,        // 直线移动
    MOVE_BEZIRE     = 8,        // 贝塞尔曲线移动
    CUSTOM_LINE     = 9,        // 自定义路径
    CUSTON_CURVE    = 10,       // 自定义曲线
    APPEAR_OPACITY  = 11,       // 出现透明度
    DISAPPEAR_OPACITY = 12,     // 消失透明度
    SHAKE           = 13,       // 抖动
    BLINK           = 14,        // 闪烁
    // 思维
    SIWEI_SIZE_CHANGE = 100,    // 思维 元素大小变化
    SIWEI_SHAKE_X = 101,        // 思维 左右抖动
    SIWEI_SHAKE_Y = 102,        // 思维 上下抖动
    SIWEI_REPEATEDLY = 103,     // 思维 出现又消失
    SIWEI_AFTER_APPEAR = 104,   // 思维 出现后左右抖动
    SIWEI_STAGNATION = 105,     // 思维 停滞
    SIWEI_APPEAR = 106,         // 思维 出现
    SIWEI_DISAPPEAR = 107,      // 思维 消失
    SIWEI_MOVE_LINE = 108       // 思维 直线移动
}
export const SpineType = 15;        // spine 动作枚举

/** 动画编辑模式 */
export enum TweenEditType {
    DEFAULT         = 0,        // 普通模式
    DRAW            = 1,        // 画线模式
    VERTEX          = 2,        // 编辑顶点模式
}

/** Action动作基类 */
export class ActionDataBase {

}

/** Tween动画数据 */
export class TweenData extends ActionDataBase {
    // 缓动类型
    public type: TweenType;
    // 可选参数
    public option: any;
    // 是否是相对
    public relative: boolean;
    // 缓动数据
    public data: any;
}

/** Anim动画数据 */
export class CocosAniActionData extends ActionDataBase {
    public timeScale: number;  // 速率
    public animName: string;   // 动画名称即clip名称
    public name: string;       //
    public type: number;       // 动画类型


    // 动画数据
    public curveData: any;
    // 循环模式
    public wrapMode: number;
    // 帧速率
    public sample: number;
    // 播放速度
    public speed: number;
    // 事件列表
    public events: any;
    // 持续时间
    public _duration:number
}

/** SpineData CococAni 动画数据 */
export class SpineActionData extends ActionDataBase {
    public timeScale: number;  // 速率
    public animName: string;   // 动画名称
    public name: string;       //
    public type: number;       // 动画类型
}