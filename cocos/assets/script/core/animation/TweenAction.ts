import ValueUtils from "../../../qte/core/utils/ValueUtils";
import ActionManager from "./ActionManager";
import { EAction } from "./AnimationData";
import BaseAction from "./BaseAction";

export default class TweenAction extends BaseAction {

    /**
     * 播放动作
     * @param {cc.Node} node 所作用的节点
     * @param {any} data 动画数据
     * @param {() => void} cb 动作播放完成回调
     */
    public play (node: cc.Node, data: EAction,  cb?: () => void): void {
        let value = data.value;
        let delay = data.delay;
        let actionData = yy.single.instance(ActionManager).getTweenData(data.id);
        if (actionData.length <= 0) {
            if (cb) {
                cb();
            }
            return;
        }
        let tween = cc.tween(node);        
        for (let tempData of actionData) {
            let easing = {};
            if (tempData.easing) {
                // @ts-ignore
                easing.easing = tempData.easing;
            }
            if (tempData.type === "bezier") {
                let pos1 = cc.v2(tempData.props.c1.x, tempData.props.c1.y);
                let pos2 = cc.v2(tempData.props.c2.x, tempData.props.c2.y);
                let pos3 = cc.v2(tempData.props.p1.x, tempData.props.p1.y);
                // tween.to(0, {x: actionData[0].x, y: actionData[0].y});
                tween.bezierTo(value.speed, pos1, pos2, pos3);
            } else {
                tween[tempData.type](tempData.time, tempData.props, easing);
            }
        }
        tween.repeat(value.repeat, tween);    

        setTimeout(() => {
            tween.call(() => {
                if (cb) {
                    cb();
                }
            }).start();
        }, ValueUtils.check(yy.checkValue(delay * 1000, 0), 0));
    }
}