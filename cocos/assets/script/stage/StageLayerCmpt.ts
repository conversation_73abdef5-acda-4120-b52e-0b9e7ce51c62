import { CmptLayer } from "../common/EditorEnum";
import DisplayObjectManager from "../core/display/DisplayObjectManager";
import TemplateInterpreter from "../core/display/TemplateInterpreter";
import { StageInfo } from "../core/proxy/ComptData";
import DataCenterBridge from "../core/proxy/DataCenterBridge";
import StageController from "./StageController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class StageLayerCmpt extends cc.Component {
  @property({
    type: cc.Node,
    tooltip: "截图摄像机",
  })
  public shootCameraNode: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "背景层",
  })
  public backgroundLayer: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "显示对象层",
  })
  public displayObjectLayer: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "动画层",
  })
  public animDisplayLayer: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "事件层",
  })
  public touchLayer: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "最高对象层",
  })
  public topLayer: cc.Node = null;

  @property({
    type: cc.Node,
    tooltip: "调试层",
  })
  public debugLayer: cc.Node = null;

  onLoad() {
    // 注册舞台控制器
    yy.single.instance(StageController).register(this);
  }

  async start() {
    console.log("开始cocos加载组件建", new Date().getTime());
    // 数据中心
    let dataCenter = yy.single.instance(DataCenterBridge);
    // 注册模板解释器
    let template = yy.single.instance(TemplateInterpreter);
    template.registerLayer(this.addDisplayObject.bind(this));


    yy.single.instance(DataCenterBridge).cocosLoadCompListStart();
    // 设置背景
    let stageInfo = dataCenter.getStageInfo();
    this.addBackground(stageInfo.texture, stageInfo.bgColor, stageInfo);
    console.log((window as any)._$store.state.template);
    this.shootCameraNode.active = false;
    // 通过模板解释器生成初始场景
    await template.generate(
      dataCenter.getComponentMap(),
      dataCenter.getComponentIds(),
    );
    // 场景初始化完成
    yy.single.instance(StageController).onInitFinished();
    console.log("cocos组件加载完成", new Date().getTime());
    this.schedule(this.checkAllCompLoadFinish, 0.1);

  }

  checkAllCompLoadFinish() {
    let dataCenter = yy.single.instance(DataCenterBridge);
    let template = yy.single.instance(TemplateInterpreter);
    if (!cc.isValid(template, true)) {
      this.unschedule(this.checkAllCompLoadFinish);
      yy.single.instance(DataCenterBridge)?.cocosLoadCompListFinished();
      return;
    }
    let isFinishEd = template.checkCompLoadFinish(
      dataCenter.getComponentMap(),
      dataCenter.getComponentIds(),
    );
    if (isFinishEd) {
      this.unschedule(this.checkAllCompLoadFinish);
      template.initAllComponentsFinish(
        dataCenter.getComponentMap(),
        dataCenter.getComponentIds());
      console.log("全部组件加载完成回调了");
      yy.single.instance(DataCenterBridge).cocosLoadCompListFinished();
      cc.game.setFrameRate(30);
    }
  }



  /**
   * 添加背景
   * @param url 背景资源地址
   * @param color 背景颜色
   */
  public addBackground(url: string, color: string, stageInfo: StageInfo): void {
    this.backgroundLayer.width = stageInfo.width;
    this.backgroundLayer.height = stageInfo.height;
    // let img = this.backgroundLayer.getChildByName("img");
    /*
     * img.width = stageInfo.safeWidth;
     * img.height = stageInfo.safeHeight;
     */
    this.updateBackground(color, url, cc.Sprite.Type.SIMPLE);
  }

  /**
   * 更新背景属性
   * @param url 背景资源地址
   * @param color 背景颜色
   */
  public updateBackground(
    color: string,
    url: string,
    textureType: cc.Sprite.Type,
  ): any {
    let properites: any = {};
    let img = this.backgroundLayer.getChildByName("img");
    if (color && color !== "") {
      properites.bgColor = color;
      this.backgroundLayer.color = new cc.Color().fromHEX(
        yy.checkValue(color, "#ffffff"),
      );
      img.active = false;
      return properites;
    } else {
      this.backgroundLayer.color = new cc.Color().fromHEX("#ffffff");
    }
    img.active = true;
    properites.texture = (img as any).__textureUrl__;
    if (url && url !== "") {
      cc.assetManager.loadRemote(url, (err, texture) => {
        if (err) {
          yy.warn(err);
          return;
        }
        (img as any).__textureUrl__ = url;
        img.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture);
      });
    } else {
      img.getComponent(cc.Sprite).spriteFrame = null;
      (img as any).__textureUrl__ = "";
    }
    // 设置纹理平铺类型
    // eslint-disable-next-line no-undefined
    // img.getComponent(cc.Sprite).type = textureType;
    img.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.RAW;
    return properites;
  }

  /**
   * 添加显示对象接口
   * @param objNode
   */
  public addDisplayObject(objNode: cc.Node, layer: CmptLayer): void {
    let displayerLayer = this.getDisplayLayer(layer);

    let temp = this.topLayer.getChildByName(objNode.name);
    if (temp) {
      temp.destroy();
    }
    displayerLayer.addChild(objNode);
  }

  /**
   * 移除节点
   * @param {string} name 节点名称
   * @param {CmptLayer} layer 层级
   */
  public removeChildFromLayer(name: string, layer: CmptLayer): void {
    let displayLayer = this.getDisplayLayer(layer);
    let temp = displayLayer.getChildByName(name);
    if (temp) {
      temp.destroy();
    }
  }

  // 显示调试边框
  public showDebug(): void {
    let gra = this.debugLayer.getComponent(cc.Graphics);
    let map = yy.single.instance(DisplayObjectManager).dpMap;
    let list = Array.from(map.keys());
    for (let k of list) {
      let val = map.get(k);
      gra.moveTo(val.rect.x, val.rect.y);
      gra.lineTo(val.rect.x, val.rect.y + val.rect.height);
      gra.lineTo(val.rect.x + val.rect.width, val.rect.y + val.rect.height);
      gra.lineTo(val.rect.x + val.rect.width, val.rect.y);
      gra.lineTo(val.rect.x, val.rect.y);
      gra.stroke();
    }
  }

  public onDestroy(): void {
    yy.single.destory(StageController);
  }

  /**
   * 获取添加对象节点
   */
  private getDisplayLayer(layer: CmptLayer): cc.Node {
    switch (layer) {
      case CmptLayer.OBJECT_LAYER:
        return this.displayObjectLayer;
      case CmptLayer.ANIM_LAYER:
        return this.animDisplayLayer;
      case CmptLayer.TOUCH_LAYER:
        return this.touchLayer;
      case CmptLayer.TOP_LAYER:
        return this.topLayer;
      default:
        return this.displayObjectLayer;
    }
  }

  screenshootHide() {
    this.shootCameraNode.active = false;
  }
  screenshootShow() {
    this.shootCameraNode.active = true;
  }

  public screenshoot(): any {
    this.unschedule(this.screenshootHide);
    let camera = this.shootCameraNode.getComponent(cc.Camera);
    // 新建一个 RenderTexture，并且设置 camera 的 targetTexture 为新建的 RenderTexture，这样 camera 的内容将会渲染到新建的 RenderTexture 中。
    let texture = new cc.RenderTexture();
    let gl = (cc.game as any)._renderContext;
    // 如果截图内容中不包含 Mask 组件，可以不用传递第三个参数
    texture.initWithSize(
      cc.visibleRect.width,
      cc.visibleRect.height,
      gl.DEPTH_STENCIL,
    );
    camera.targetTexture = texture;
    // 渲染一次摄像机，即更新一次内容到 RenderTexture 中
    camera.render();
    // readPixels()
    this.scheduleOnce(this.screenshootHide, 1);
    return texture;
  }
}
