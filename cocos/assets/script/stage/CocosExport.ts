/* eslint-disable max-lines-per-function */
import ActionManager from "../core/animation/ActionManager";
import { TweenType, AnimType, SpineType } from "../core/animation/AnimationData";
import DisplayEditBox from "../core/display/base/DisplayEditBox";
import DisplaySpecialComponent from "../core/display/base/DisplaySpecialComponent";
import CocosAniManager from "../core/display/CocosAniManager";
import DisplayObjectManager from "../core/display/DisplayObjectManager";
import SpineManager from "../core/display/SpineManager";
import StageController, { SignalData } from "./StageController";

/** window导出逻辑初始位置 */
export class CocosExport {
  public static initExport() {
    // eslint-disable-next-line no-unused-expressions
    (window as any).cocos || ((window as any).cocos = {});
    /** 截屏 */
    // eslint-disable-next-line arrow-body-style
    (window as any).cocos.screenshot = async () => {
      return await yy.single.instance(StageController).screenshot();
    };


    // 带标号截图
    (window as any).cocos.screenSignalShot = async (signalData: [SignalData]) => {
      return await yy.single.instance(StageController).screenshotSignal(signalData);
    };
    // 隐藏部分组件的截图
    (window as any).cocos.hideComponentShot = async (hideIds: number[]) => {
      return await yy.single.instance(StageController).hideComponentShot(hideIds);
    };
    // 隐藏部分组件的带标号截图
    (window as any).cocos.hideComponentShotWithSignal = async (signalData: [SignalData], hideIds: number[]) => {
      return await yy.single.instance(StageController).hideComponentShotWithSignal(signalData, hideIds);
    };


    (window as any).cocos.getSpineProperties = spineComponent => {
      let prop = yy.single
        .instance(SpineManager)
        .getSpineProperties(spineComponent.id);
      return prop;
    };

    (window as any).cocos.getCocosAniProperties = cocosAniComponent => {
      let prop = yy.single
        .instance(CocosAniManager)
        .getCocosAniProperties(cocosAniComponent.id);
      return prop;
    };


    (window as any).cocos.cancleStageMouse = () => {
      let sc = yy.single.instance(StageController)
      if (sc) {
        sc.cancleStageMouse();
      }
    }


    /**
     * 获取动画数据
     * @param {number} componetId 组件id
     * @param {E_Value} actionData 动画数据
     */
    (window as any).cocos.getActionData = (componetId, actionData) => {
      let data = yy.single
        .instance(ActionManager)
        .getActionByType(componetId, actionData);
      return data;
    };
    /** 获取动画类型对应的动画属性Map */
    (window as any).cocos.getAnimaPropMap = () => {
      let map = new Map<number, string[]>();
      // 通用
      map.set(TweenType.DEFAULT, []);
      map.set(TweenType.FLY_INTO, ["move"]);
      map.set(TweenType.FLY_OUT, ["move"]);
      map.set(TweenType.APPEAR, ["scale"]);
      map.set(TweenType.DISAPPEAR, ["scale"]);
      map.set(TweenType.SCALE, ["scale"]);
      map.set(TweenType.ROTATION, ["angle"]);
      map.set(TweenType.MOVE_LINE, ["move"]);
      map.set(TweenType.MOVE_BEZIRE, ["move"]);
      map.set(TweenType.CUSTOM_LINE, ["move"]);
      map.set(TweenType.CUSTON_CURVE, ["move"]);
      map.set(TweenType.APPEAR_OPACITY, ["opcity"]);
      map.set(TweenType.DISAPPEAR_OPACITY, ["opcity"]);
      map.set(TweenType.SHAKE, ["angle"]);
      map.set(TweenType.BLINK, ["opcity"]);
      // 思维
      map.set(TweenType.SIWEI_SIZE_CHANGE, ["scale"]);
      map.set(TweenType.SIWEI_SHAKE_X, ["scale"]);
      map.set(TweenType.SIWEI_SHAKE_Y, ["move"]);
      map.set(TweenType.SIWEI_REPEATEDLY, ["opacity"]);
      map.set(TweenType.SIWEI_AFTER_APPEAR, ["scale", "angle"]);
      map.set(TweenType.SIWEI_STAGNATION, ["opacity"]);
      map.set(TweenType.SIWEI_APPEAR, ["opacity"]);
      map.set(TweenType.SIWEI_DISAPPEAR, ["opacity"]);
      map.set(TweenType.SIWEI_MOVE_LINE, ["move"]);
      // 增加 spine 动画
      map.set(SpineType, ["spine"]);
      return map;
    };

    // eslint-disable-next-line no-unused-expressions
    (window as any).cocos || ((window as any).cocos = {});
    (window as any).cocos.getWorldPos = id =>
      yy.single.instance(DisplayObjectManager).getWorldPos(id);
    (window as any).cocos.getWorldRect = id =>
      yy.single.instance(DisplayObjectManager).getWorldRect(id);
    (window as any).cocos.convertEditorWorldPos = (id, pos) =>
      yy.single.instance(DisplayObjectManager).convertEditorWorldPos(id, pos);
    (window as any).cocos.getRotateEWordRect = (id) =>
      yy.single.instance(DisplayObjectManager).getRotateEWordRect(id);
    (window as any).cocos.getEditPointWorldPos = (id, pId) => {
      let pos = yy.single
        .instance(DisplayObjectManager)
        .getEditPointWorldPos(id, pId);
      return pos;
    };
    (window as any).cocos.getNodeRealPos = id => {
      console.log('call getNodeRealPos');
      yy.single.instance(DisplayObjectManager).getNodeRealPos(id);
    }
    // 获取节点截图
    (window as any).cocos.getCaptureByNode = id => {
      console.log("getCaptureByNode", id);
      let texture = yy.single.instance(DisplayObjectManager).getCaptureByNode(id);
      return texture;
      // ----------------test--------------
      // let data1 = texture.readPixels();
      // let texture2D = new cc.Texture2D();
      // texture2D.initWithData(data1, cc.Texture2D.PixelFormat.RGBA8888, texture.width, texture.height);
      // texture2D.setPremultiplyAlpha(true);
      // let spriteFrame = new cc.SpriteFrame();
      // spriteFrame.setTexture(texture2D);
      // spriteFrame.setFlipY(true);  
      // let _tNode = new cc.Node();
      // _tNode.addComponent(cc.Sprite);
      // _tNode.getComponent(cc.Sprite).spriteFrame = spriteFrame;
      // // _tNode.width = texture.width;
      // // _tNode.height = texture.height;
      // cc.find('Canvas').addChild(_tNode);
      // _tNode.zIndex = 10000;
    }

    // 设置小题截图
    (window as any).cocos.getSmallCaptureById = async (id, obj: {}) => {

      await yy.single.instance(DisplayObjectManager).getSubUpdateFinish(id, obj);


      let texture = yy.single.instance(DisplayObjectManager).getCaptureByNode(id);

      return texture;


      let data1 = texture.readPixels();
      let texture2D = new cc.Texture2D();
      texture2D.initWithData(data1, cc.Texture2D.PixelFormat.RGBA8888, texture.width, texture.height);
      texture2D.setPremultiplyAlpha(true);
      let spriteFrame = new cc.SpriteFrame();
      spriteFrame.setTexture(texture2D);
      spriteFrame.setFlipY(true);
      let _tNode = new cc.Node();
      _tNode.addComponent(cc.Sprite);
      _tNode.getComponent(cc.Sprite).spriteFrame = spriteFrame;
      // _tNode.width = texture.width;
      // _tNode.height = texture.height;
      cc.find('Canvas').addChild(_tNode);
      _tNode.zIndex = 10000;
      return texture;
    }

    (window as any).cocos.getNodeCentEdtiorPos = (id) => {
      let pos = yy.single
        .instance(DisplayObjectManager)
        .getNodeCentEdtiorPos(id);
      return pos;
    };

    (window as any).cocos.showSocpedComponent = false;

    // 文本框获取富文本字符串
    (window as any).cocos.getLableString = id => {
      let labDisplay = yy.single
        .instance(DisplayObjectManager)
        .getDisplayObjectById(id);
      if (!labDisplay) {
        return "";
      }
      let editBoxCom = (labDisplay as DisplayEditBox).getEditBoxCom();
      if (!editBoxCom) {
        return "";
      }
      yy.log("=====getLableString===>3===>", editBoxCom.convertToRichText());
      return editBoxCom.convertToRichText();
    };

    // 获取文本框不带样式的纯文本字符串
    (window as any).cocos.getLableSimplyString = id => {
      let labDisplay = yy.single
        .instance(DisplayObjectManager)
        .getDisplayObjectById(id);
      if (!labDisplay) {
        return "";
      }
      let editBoxCom = (labDisplay as DisplayEditBox).getEditBoxCom();
      if (!editBoxCom) {
        return "";
      }
      yy.log("###str------>", editBoxCom.getEditBoxLabel());
      return editBoxCom.getEditBoxLabel();
    };

    // 特殊组件属性
    (window as any).cocos.getSpecialComponentProperties = component => {
      let labDisplay = yy.single
        .instance(DisplayObjectManager)
        .getDisplayObjectById(component.id) as DisplaySpecialComponent;
      if (!labDisplay) {
        return "";
      }
      let prop = {
        width: labDisplay.size.width,
        height: labDisplay.size.height,
        scaleX: labDisplay.componentScale.x,
        scaleY: labDisplay.componentScale.y,
      };
      return prop;
    };

    (window as any).cocos.setResetAndSubmitShow = (show) => {
      const Canvas: cc.Node = cc.find("Canvas");
      if (Canvas) {
        const stage_prefab = Canvas.getChildByName('stage_prefab');
        if (stage_prefab) {
          const com_BtnNextAndSubmit = stage_prefab.getChildByName('com_BtnNextAndSubmit');
          if (com_BtnNextAndSubmit) {
            com_BtnNextAndSubmit.active = show;
          }
        }
      }
    };
    (window as any).cocos.createQuestionContent = (data: any[]) => {
       console.log('qte createQuestionContent', data);
       qte.createQuestionContent(data);

    }
  }
}
