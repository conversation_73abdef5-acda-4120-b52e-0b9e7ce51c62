import { SingleBase } from "../../qte/core/base/SingleBase";
import TouchHandlerCmpt from "../cmpt/TouchHandlerCmpt";
import { AnimPreviewType, CmptLayer } from "../common/EditorEnum";
import { EAction, EAnimation } from "../core/animation/AnimationData";
import AnimationManager from "../core/animation/AnimationManager";
import CutShapeManager from "../core/display/base/edit/CutShapeManager";
import SvgShapeManager from "../core/display/base/SvgShapeManager";
import CocosAniManager from "../core/display/CocosAniManager";
import SpineManager from "../core/display/SpineManager";
import DataCenterBridge from "../core/proxy/DataCenterBridge";
import StageLayerCmpt from "./StageLayerCmpt";
import DisplayObjectManager from "../core/display/DisplayObjectManager";

// 标号数据
export interface SignalData {
  id: string,
  newExtra: { signalId: number, isCorrect?: boolean }   // 选择题有 isCorrect 字段，填空不需要
}

/**
 * 舞台控制器
 */
export default class StageController extends SingleBase {
  // 注册组件
  private _cmpt: StageLayerCmpt;
  // 预览动画播放进行中
  private _actionPlaying = false;
  // 动画播放延时实例
  private _settimeoutId: any;

  /** 注册接口 */
  public register(cmpt: StageLayerCmpt) {
    this._cmpt = cmpt;
    this.onInitStart();
  }
  /**
   *
   * @param isAuto
   */
  public changeAutoSubmit(isAuto: boolean) {
    const Canvas: cc.Node = cc.find("Canvas");
    if (Canvas) {
      const stage_prefab = Canvas.getChildByName('stage_prefab');
      if (stage_prefab) {
        const com_BtnNextAndSubmit = stage_prefab.getChildByName('com_BtnNextAndSubmit');
        if (com_BtnNextAndSubmit) {
          com_BtnNextAndSubmit.getChildByName("com_btnSubmit").active = !isAuto;
        }
      }
    }
  }


  public changeAutoRest(isAuto: boolean) {
    const Canvas: cc.Node = cc.find("Canvas");
    if (Canvas) {
      const stage_prefab = Canvas.getChildByName('stage_prefab');
      if (stage_prefab) {
        const com_BtnNextAndSubmit = stage_prefab.getChildByName('com_BtnNextAndSubmit');
        if (com_BtnNextAndSubmit) {
          com_BtnNextAndSubmit.getChildByName("com_btnReset").active = isAuto;
        }
      }
    }
  }

  /** 更新背景属性 */
  public updateBackground(properties: any): any {
    let textureType = properties.textureType;
    // eslint-disable-next-line no-undefined
    if (textureType !== undefined) {
      if (textureType === 0) {
        textureType = cc.Sprite.Type.SIMPLE;
      } else if (textureType === 2) {
        textureType = cc.Sprite.Type.TILED;
      }
    }
    // 后续不再使用backgroundColor字段 背景色改用bgColor
    return this._cmpt.updateBackground(properties.bgColor, properties.texture, textureType);
  }

  /**
   * 播放动画片段
   * @param animData 编辑器传递过来的动画数据
   */
  public playFragmentAnimation(animData: EAction[]): void {
    this.onEnterAnimEdit();
    yy.log("==============playStageAnimationStep==============");
    if (this._actionPlaying) {
      return;
    }

    this._actionPlaying = true;
    let objClone = this.initTopLayer();
    // 使用对象层和动画数据播放动画;
    yy.single.instance(AnimationManager).playStep(objClone, animData, this.playFinished.bind(this));
  }

  /**
   * 多个片段一起播放
   * @param animData
   */
  public playGroupAnim(animData: EAnimation) {
    this.onEnterAnimEdit();
    if (this._actionPlaying || !animData) {
      return;
    }
    yy.log("==============playStageAnimation==============");
    this._actionPlaying = true;
    let objClone = this.initTopLayer();
    // 使用对象层和动画数据播放动画;
    yy.single.instance(AnimationManager).play(objClone, animData, this.playFinished.bind(this));
  }

  /** 停止播放动画 */
  public stopAnimation(): void {
    clearTimeout(this._settimeoutId);
    this._settimeoutId = null;
    this.clearTopLayer();
  }

  cancleStageMouse() {
    if (this._cmpt && this._cmpt.touchLayer) {
      this._cmpt.touchLayer.getComponent(TouchHandlerCmpt).cancleStageMouse();
    }
  }

  /** 初始化toplayer预览背景 */
  private initTopLayer(): cc.Node {
    let sc = this._cmpt.touchLayer.getComponent(TouchHandlerCmpt);
    sc.unregistEvent();
    // 1. 克隆背景层 添加到 top 层上；
    let bgClone = cc.instantiate(this._cmpt.backgroundLayer);
    bgClone.name = "clone_bg";
    this._cmpt.addDisplayObject(bgClone, CmptLayer.TOP_LAYER);
    // 2. 克隆对象层 添加到 top 层上;
    let objClone = cc.instantiate(this._cmpt.displayObjectLayer);
    objClone.name = "clone_obj";
    this._cmpt.addDisplayObject(objClone, CmptLayer.TOP_LAYER);
    // 播放toplayer 里的spine
    yy.single.instance(SpineManager).playActionByCmptLayer(objClone);
    yy.single.instance(CocosAniManager).playActionByCmptLayer(objClone);
    yy.single.instance(CutShapeManager).reloadShap(objClone);
    yy.single.instance(SvgShapeManager).reloadSvgShap(objClone);

    return objClone;
  }

  // 开启主循环处理 结束延迟清楚toplayer
  private playFinished(type: AnimPreviewType) {
    this._settimeoutId = setTimeout(() => {
      this.clearTopLayer();
      if (type === AnimPreviewType.ALL) {
        yy.single.instance(DataCenterBridge).stopAnimatioinCommit();
      } else {
        yy.single.instance(DataCenterBridge).stopFragmentCommit();
      }
      this.onExitAnimEdit();
    }, 1500);
  }

  /** 清空toplayer的克隆对象 还原事件*/
  private clearTopLayer() {
    // 动画预览完成后移除top层临时加的clone对象
    this._cmpt.removeChildFromLayer("clone_bg", CmptLayer.TOP_LAYER);
    this._cmpt.removeChildFromLayer("clone_obj", CmptLayer.TOP_LAYER);
    this._cmpt.touchLayer.getComponent(TouchHandlerCmpt).registEvent();
    this._actionPlaying = false;
  }

  /** 截屏接口 */
  public async screenshot() {
    return new Promise((resolve, reject) => {
      this._cmpt.screenshootShow();
      setTimeout(() => {
        resolve(this._cmpt.screenshoot());
      }, 100);
    });
  }
  /**
   * @msg     : 标号截图
   * @return   {*}
   */
  public screenshotSignal(signalData: SignalData[]) {
    return new Promise((resolve, reject) => {
      // 获取所有 signal 数据

      // let signalData = [{ id: "1", newExtra: { signalId: 1 , isCorrect:true} }]
      // console.log("screenshotSigns",signalData);
      yy.single.instance(DisplayObjectManager).setAllObjectSignalView(true, signalData);
      let self = this;
      setTimeout(() => {
        let p = self._cmpt.screenshoot();
        setTimeout(() => {
          yy.single.instance(DisplayObjectManager).setAllObjectSignalView(false, signalData);
          resolve(p);
        }, 100);
      }, 100);
      self._cmpt.screenshootShow();
    })
  }
  /**
   *  隐藏部分组件的截图
   * @param hideIds  需要隐藏的组件id
   */
  public hideComponentShot(hideIds: number[]) {
    return new Promise((resolve, reject) => {
      // 循环隐藏组件
      for (let i = 0; i < hideIds.length; i++) {
        yy.single.instance(DisplayObjectManager).getDisplayObjectById(hideIds[i] + "").setActive(false);
      }
      this._cmpt.screenshootShow();
      setTimeout(() => {
        resolve(this._cmpt.screenshoot());
        // 截完图后，恢复组件显示
        for (let i = 0; i < hideIds.length; i++) {
          yy.single.instance(DisplayObjectManager).getDisplayObjectById(hideIds[i] + "").setActive(true);
        }
      }, 100);
    })
  }
  // 隐藏部分组件，带标号截图
  public hideComponentShotWithSignal(signalData: SignalData[], hideIds: number[]) {
    return new Promise((resolve, reject) => {
      yy.single.instance(DisplayObjectManager).setAllObjectSignalView(true, signalData);
      // 循环隐藏组件
      for (let i = 0; i < hideIds.length; i++) {
        yy.single.instance(DisplayObjectManager).getDisplayObjectById(hideIds[i] + "").setActive(false);
      }
      let self = this;
      setTimeout(() => {
        let p = self._cmpt.screenshoot();
        setTimeout(() => {
          yy.single.instance(DisplayObjectManager).setAllObjectSignalView(false, signalData);
          // 截完图后，恢复组件显示
          for (let i = 0; i < hideIds.length; i++) {
            yy.single.instance(DisplayObjectManager).getDisplayObjectById(hideIds[i] + "").setActive(true);
          }
          resolve(p);
        }, 100);
      }, 100);
      self._cmpt.screenshootShow();
    })
  }

  // ************************ 生命周期相关 ************************* //
  public onInitStart(): void {
    yy.log("----- onInitStart -----");
  }

  public onInitFinished(): void {
    yy.log("----- onInitFinished -----");
    yy.single.instance(DataCenterBridge).cocosInitFinished();
  }

  public onEnterAnimEdit(): void {
    yy.log("----- onEnterAnimEdit -----");
  }

  public onExitAnimEdit(): void {
    yy.log("----- onExitAnimEdit -----");
  }
  // ************************ 生命周期相关 ************************* //

  /** 销毁接口 */
  public onDestoryInstance(): void {
    this._cmpt = null;
  }
}
