import { QuestionType } from "../common/EditorEnum";
import DataCenterBridge from "../core/proxy/DataCenterBridge";

const { ccclass } = cc._decorator;

@ccclass
export default class StageSceneCmpt extends cc.Component {
  start() {
    // 加载舞台预制体
    cc.resources.load(
      "prefabs/stage_prefab",
      cc.Prefab,
      (_err, prefab: cc.Prefab) => {
        // 获取设计分辨率
        let stageSize = yy.single.instance(DataCenterBridge).getStageInfo();
        let dSize = cc.size(stageSize.safeWidth, stageSize.safeHeight);
        let stageNode = cc.instantiate(prefab);
        stageNode.width = dSize.width;
        stageNode.height = dSize.height;
        this.node.addChild(stageNode);
        const _isAutoSubmit = (window as any)._$store.state.extraStageData
          .isAutoSubmit;
        const _hasRecover = (window as any)._$store.state.extraStageData
          .hasRecover;
        console.log("_isAutoSubmit", _isAutoSubmit, "_hasRecover", _hasRecover);

     
        yy.single.instance(DataCenterBridge).cocosLoadStageRootNodeFinished();
        // cc.resources.load(
        //   "prefabs/com_BtnNextAndSubmit",
        //   cc.Prefab,
        //   (error: any, prefab: cc.Prefab) => {
        //     if (error) {
        //       console.error("提交按钮加载失败");
        //     } else {
        const btn = stageNode.getChildByName("com_BtnNextAndSubmit");
        console.log("-----stageNode--->", stageNode);
        console.log("-----btn--->", btn);
        btn.getChildByName("com_btnSubmit").active = !_isAutoSubmit;
        btn.getChildByName("com_btnReset").active = _hasRecover;
        //     }
        //   },
        // );
      },
    );
  }
}
