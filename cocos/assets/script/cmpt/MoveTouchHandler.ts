import { SingleBase } from "../../qte/core/base/SingleBase";
import CommandManager from "../../qte/core/extension/command/CommandManager";
import { CmptType } from "../common/EditorEnum";
import CollisionManager from "../core/collision/CollisionManager";
import { ReplaceCmptCommand } from "../core/command/commond";
import CommandFactory from "../core/command/operate/CommandFactory";
import UpdateProp2VueCmd from "../core/command/simple/UpdateProp2VueCmd";
import DisplaySelectPoint from "../core/display/base/cmpt/DisplaySelectPoint";
import DisplayObjectGroup from "../core/display/base/DisplayObjectGroup";
import DisplayObjectManager from "../core/display/DisplayObjectManager";
import HintLineManager, { LINE_COLLIDE_TYPE } from "../core/hintline/HintLineManager";
import EditBoxTouchHandler from "./EditBoxTouchHandler";

// eslint-disable-next-line @typescript-eslint/class-name-casing
class LINE_COLLIDE_DATA {
    pos: cc.Vec2;
    type: LINE_COLLIDE_TYPE;
    constructor(pos: cc.Vec2, type: LINE_COLLIDE_TYPE) {
        this.pos = pos;
        this.type = type;
    }
}
/**
 * 移动选中逻辑
 */
export default class MoveTouchHandler extends SingleBase {
    // 辅助线碰撞检测map
    private _collideMap = new Map<string, LINE_COLLIDE_DATA>();
    // 辅助线管理器
    private _instanceManager: HintLineManager = null;
    // 对象管理器
    private _displayManager: DisplayObjectManager;
    // 碰撞管理器
    private _collisionManager: CollisionManager;
    // 自定义文本框
    private _editBoxTouchHandler: EditBoxTouchHandler;
    // 当前选中操作节点类型
    private _typeHandlePoint: DisplaySelectPoint = null;
    // 是否更新的组内数据
    private bUpdateGroupData = false;

    //记录鼠标事件
    private mouseSate: number = -1;

    public initInstance(): void {
        this._displayManager = yy.single.instance(DisplayObjectManager);
        this._collisionManager = yy.single.instance(CollisionManager);
        this._editBoxTouchHandler = yy.single.instance(EditBoxTouchHandler);
        this._instanceManager = yy.single.instance(HintLineManager);
    }



    /**
     * 鼠标点下
     * @param pos 
     * @param id 
     * @param multiSelect 
     */
    public mouseBegin(pos: cc.Vec2, id: string, multiSelect: boolean): void {
        this.mouseSate = 0;
        // 如果点击的是选中的是操作节点,选中Id不清零
        if (this._displayManager.selectId !== "-1") {
            this._typeHandlePoint = null;
            let zIndex = -1;
            for (let i = 0; i < this._displayManager.selectedIds.length; i++) {
                let _selectId = this._displayManager.selectedIds[i];

                let _selDisplay = this._displayManager.getDisplayObjectById(_selectId);
                let typeHandle = _selDisplay.inSelRect(pos);
                if (typeHandle !== null) {
                    let tmpZIndex = _selDisplay.node.getSiblingIndex();
                    if (_selDisplay.dragable != false && tmpZIndex > zIndex) {
                        this._typeHandlePoint = typeHandle;
                        zIndex = tmpZIndex;
                        this._displayManager.selectId = _selectId;
                    }
                }
            }
            // 点到操作点上，缓存操作类型~
            if (this._typeHandlePoint !== null) {
                return;
            }
            this._displayManager.selectId = "-1";
        } else {
            // 子组件不可本次选中
            this.mouseSate = 2;
        }

        let _selectSubId = "-1"
        // 点中的是组的话
        let _display = this._displayManager.getDisplayObjectById(id);
        if (_display && _display.type === CmptType.GROUP) {
            let bSelectGroup = this._displayManager.selectedIds.find((value) => value === id);
            // 如果选中的是组内的子节点
            if (bSelectGroup) {
                // 点和矩形碰撞。。如果要碰撞子节点。先转换成父节点的相对坐标
                pos = _display.node.convertToNodeSpaceAR(pos);
                let _groupsIds = _display.getComponent(DisplayObjectGroup).groupIds;
                _groupsIds.sort(this.compareByZIndex.bind(this));
                let touchSubId = this._collisionManager.testDisplayObject(pos, _groupsIds, this._displayManager.dpMap);
                // 如果选中的是组内的子节点
                if (touchSubId !== "-1") {
                    // 判断选中组内是否存在非组内元素
                    let _bUnSelect = false;
                    _bUnSelect = this.isUnSelect(_groupsIds, id);
                    if (!_bUnSelect && !multiSelect) {
                        // 塞到选中的表里
                        _selectSubId = touchSubId;
                    }
                }
            }
        }
        if (this._displayManager.selectedIds.length > 0 && this._displayManager.selectedIds[0] != id) {
            // 子组件不可本次选中
            this.mouseSate = 2;
        }

        if (this._displayManager.selectSubId != "-1") {
            if (_selectSubId !== this._displayManager.selectSubId) {
                this._displayManager.selectSubId = "-1";
                yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids: [], isVue: false, isMulti: true });
            }

        }

        //  啥也不是,单纯点击了一下组件，塞到多选或者选中表里 如果选中id 是当前动画选中的id， 不发送选中事件
        if (id !== "-1") {
            this._displayManager.selectId = id;
            yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids: [id], isVue: false, isMulti: multiSelect });
        } else {
            yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids: [], isVue: false, isMulti: multiSelect });
        }
    }

    /**
     * 鼠标移动
     * @param pos 
     * @param delta 
     */
    public mouseMove(event: cc.Event.EventMouse): void {
        this.mouseSate = 1;
        if (this._displayManager.selectId === "-1") {
            return;
        }
        let delta = event.getDelta();
        let local = event.getLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
        // 如果选中组内子节点组件
        if (this._displayManager.selectSubId !== "-1") {
            let groupDisplay = this._displayManager.getDisplayObjectById(this._displayManager.selectId);
            // 如果有操作类型
            if (this._typeHandlePoint) {
                if (groupDisplay.type === CmptType.GROUP) {
                    // 组的话走组的逻辑
                    this._typeHandlePoint.updateDisplayObjectByHandle(this._displayManager.selectId, new cc.Vec3(pos.x, pos.y));
                } else {
                    // 操作点操作
                    this._typeHandlePoint.updateDisplayObjectByHandle(this._displayManager.selectId, new cc.Vec3(pos.x, pos.y));
                }
            } else {
                // 没有操作类型,单纯移动~~~
                let display = this._displayManager.getDisplayObjectById(this._displayManager.selectSubId);
                if (display) {
                    this._editBoxTouchHandler.stopEdit(this._displayManager.selectSubId, this._displayManager);
                    this._displayManager.updateDisplayObjectDeltaPos(this._displayManager.selectSubId, new cc.Vec3(delta.x, delta.y));
                }
            }
            // 刷新组的显示框框和操作点
            if (groupDisplay) {
                if (groupDisplay.type === CmptType.GROUP) {
                    this.bUpdateGroupData = true;
                } else {
                    let nFindId = this._displayManager.getDisplayGroupID(this._displayManager.selectSubId);
                    if (nFindId !== "-1") {
                        let groupParent = this._displayManager.getDisplayObjectById(nFindId);
                        // eslint-disable-next-line max-depth
                        if (groupParent) {
                            this.bUpdateGroupData = true;
                        }
                    }
                }
            }
            return;
        }
        // 如果没有选中组内的操作节点或者组的操作节点，但是又有操作节点
        if (this._typeHandlePoint) {
            // 操作
            this._typeHandlePoint.updateDisplayObjectByHandle(this._displayManager.selectId, new cc.Vec3(pos.x, pos.y));
            return;
        }
        // 处理辅助线逻辑
        this.hintLineHandler(pos, delta);
    }

    /**
     * 鼠标抬起
     */
    public mouseUp(pos: cc.Vec2, multiSelect: boolean): void {
        // 更新节点属性
        if (this._displayManager.selectId !== "-1") {
            this._typeHandlePoint = null;
            this.updateEditor(this._displayManager.selectedIds);
            if (this.mouseSate == 0) {
                this.groupSubIdsChoose(pos, multiSelect);
            }

        }
        this.resetHintLineHandler();
    }


    cancleStageMouse(pos: cc.Vec2, multiSelect: boolean) {
        if (pos) {
            this.mouseUp(pos, multiSelect);
        }
        if (this._typeHandlePoint) {
            this._typeHandlePoint = null;
        }
    }


    compareByZIndex(a: string, b: string) {
        let displayObjectA = this._displayManager.getDisplayObjectById(a);
        let displayObjectB = this._displayManager.getDisplayObjectById(b);
        let tmpZIndexA = displayObjectA.node.getSiblingIndex();
        let tmpZIndexB = displayObjectB.node.getSiblingIndex();
        return tmpZIndexA - tmpZIndexB;
    }
    // 计算子组件是否选中
    public groupSubIdsChoose(pos: cc.Vec2, multiSelect: boolean) {
        let id = this._displayManager.selectId;
        let _selectSubId = "-1"
        // 点中的是组的话
        let _display = this._displayManager.getDisplayObjectById(id);
        if (_display && _display.type === CmptType.GROUP) {
            let bSelectGroup = this._displayManager.selectedIds.find((value) => value === id);
            // 如果选中的是组内的子节点
            if (bSelectGroup) {
                // 点和矩形碰撞。。如果要碰撞子节点。先转换成父节点的相对坐标
                pos = _display.node.convertToNodeSpaceAR(pos);
                let _groupsIds = _display.getComponent(DisplayObjectGroup).groupIds;
                _groupsIds.sort(this.compareByZIndex.bind(this));
                let touchSubId = this._collisionManager.testDisplayObject(pos, _groupsIds, this._displayManager.dpMap);
                // 如果选中的是组内的子节点
                if (touchSubId !== "-1") {
                    // 判断选中组内是否存在非组内元素
                    let _bUnSelect = false;
                    _bUnSelect = this.isUnSelect(_groupsIds, id);
                    if (!_bUnSelect && !multiSelect) {
                        // 塞到选中的表里
                        _selectSubId = touchSubId;
                    }
                }
            }
        }

        if (_selectSubId !== "-1" && _selectSubId !== this._displayManager.selectSubId) {
            yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids: [_selectSubId], isVue: false, isMulti: true });
        }
        this._displayManager.selectSubId = _selectSubId;

    }


    /**
     * 监听按住方向键
     */
    public onPressDirection(delta: cc.Vec3): void {

        let editorSelectIds = this._displayManager.getRootIds();
        let checkMoveSubId = this._displayManager.getSubIds();
        let checkSubIdMove = false;
        if (checkMoveSubId.length == 1) {
            editorSelectIds = checkMoveSubId;
            checkSubIdMove = true;
        }

        for (let id of editorSelectIds) {
            let _display = this._displayManager.getDisplayObjectById(id);
            if (_display.dragable == false) {
                return;
            }
        }

        for (let id of editorSelectIds) {
            let display = this._displayManager.getDisplayObjectById(id);
            if (display) {
                if (display.editable && display.editable['properties'] && display.editable['properties'].x === false) {
                    delta.x = 0;
                }
                if (display.editable && display.editable['properties'] && display.editable['properties'].y === false) {
                    delta.y = 0;
                }
                if (display.dragable == false) {
                    delta.x = 0;
                    delta.y = 0;
                }
                display.node.position = display.node.position.add(delta);
            }
        }
        this.updateEditor(editorSelectIds);

        if (checkSubIdMove) { // 刷新组 框位置
            let groupId = this._displayManager.getDisplayGroupID(editorSelectIds[0]);
            if (groupId !== "-1") {
                let groupDisplay = this._displayManager.getDisplayObjectById(groupId) as DisplayObjectGroup;
                groupDisplay.resetLayoutSize();
            }
        }

    }

    resetHintLineHandler(): void {
        this._instanceManager.resetHintLineHandler();
    }

    /** 辅助线逻辑 */
    private hintLineHandler(pos: cc.Vec2, delta: cc.Vec2): void {
        for (let id of this._displayManager.selectedIds) {
            let _display = this._displayManager.getDisplayObjectById(id);
            if (_display.dragable == false) {
                return;
            }
        }

        this._editBoxTouchHandler.stopEdit(this._displayManager.selectId, this._displayManager);
        // 判断辅助线逻辑,再给显示对象做累加 坐标移动增量,给该显示对象做累加

        let deltaX = delta.x;
        let deltaY = delta.y;
        if (this._displayManager.selectedIds.length == 1 && this._displayManager.selectedIds[0] && this._displayManager.selectedIds[0] !== "-1") {
            const id = this._displayManager.selectedIds[0];
            let typeLineCollide = this._instanceManager.updateLineByMove(id, pos, deltaX, deltaY);
            deltaX = typeLineCollide.deltaX;
            deltaY = typeLineCollide.deltaY;


        }
        for (let id of this._displayManager.selectedIds) {
            this._displayManager.updateDisplayObjectDeltaPos(id, new cc.Vec3(deltaX, deltaY));
        }
    }

    /**
     * 判断选中列表是是否有非组内元素~
     * @param _groupsIds
     */
    private isUnSelect(_groupsIds: string[], groupId: string): boolean {
        let _bUnSelect = false;
        let selectedIds = this._displayManager.selectedIds;
        for (let _id of selectedIds) {
            let _bFind = _groupsIds.find((value) => value === _id);
            if (!_bFind && _id !== groupId) {
                _bUnSelect = true;
                break;
            }
        }
        return _bUnSelect;
    }

    /**
     * 更新数据给编辑器
     * @param updateIds
     */
    private updateEditor(selectIds: string[]): void {
        yy.single.instance(CommandManager).executeCommand(UpdateProp2VueCmd, { selectIds, isUpdateGroupData: true });
    }


}