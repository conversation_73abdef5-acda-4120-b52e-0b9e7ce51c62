/* eslint-disable max-lines-per-function */
import MathUtil from "../../qte/core/utils/MathUtil";
import Config from "../Config";
import CollisionManager from "../core/collision/CollisionManager";
import { ReplaceCmptCommand } from "../core/command/operate/base/ReplaceCmptCommand";
import CommandFactory from "../core/command/operate/CommandFactory";
import { SEL_POINT_ENUM } from "../core/display/base/cmpt/DisplaySelect";
import CutShapeManager from "../core/display/base/edit/CutShapeManager";
import DisplayObjectManager from "../core/display/DisplayObjectManager";
import DataCenterBridge from "../core/proxy/DataCenterBridge";
import CursorUtil from "../utils/CusorStyle";
import AnimTouchHandler from "./AnimTouchHandler";
import EditBoxTouchHandler from "./EditBoxTouchHandler";
import MoveTouchHandler from "./MoveTouchHandler";
import SvgTouchHandler from "./SvgTouchHandler";
import { CmptType } from "../common/EditorEnum";

const { ccclass } = cc._decorator;
@ccclass
export default class TouchHandlerCmpt extends cc.Component {
  // 动画
  private _animTouchHandler: AnimTouchHandler;
  // 自定义文本框
  private _editBoxTouchHandler: EditBoxTouchHandler;

  // 选中移动逻辑
  private _moveHandler: MoveTouchHandler;
  // 对象管理器
  private _displayManager: DisplayObjectManager;
  // 数据中心接口
  private _dataCenterBridge: DataCenterBridge;
  // 碰撞管理器
  private _collisionManager: CollisionManager;
  // svg 顶点操作
  private _svgTouchHandler: SvgTouchHandler;
  // 多选状态
  private _multiSelect: boolean = false;
  // 框选矩形相关,鼠标移动的开始坐标
  private _mouseBeginPos: cc.Vec2 = null;
  // 鼠标移动的结束坐标
  private _mouseEndPos: cc.Vec2 = null;
  // 鼠标按住
  private _isHoldOnMouseLeft = false;
  private _lastSelectTime = 0;
  _keydownEvent: any = null;
  _keyupEvent: any = null;
  _mousedownEvent: any = null;
  _mouseLastPos: cc.Vec2 = null;
  public onLoad(): void {
    this._displayManager = yy.single.instance(DisplayObjectManager);
    this._dataCenterBridge = yy.single.instance(DataCenterBridge);
    this._collisionManager = yy.single.instance(CollisionManager);
    this._animTouchHandler = yy.single.instance(AnimTouchHandler);
    this._editBoxTouchHandler = yy.single.instance(EditBoxTouchHandler);
    this._svgTouchHandler = yy.single.instance(SvgTouchHandler);
    this._moveHandler = yy.single.instance(MoveTouchHandler);
  }

  public start(): void {
    // 注册事件
    this.registEvent();
    let preKeyCode;

    this._keydownEvent = e => {
      if ((window as any).cocos.vueHandeKeyDown) {
        (window as any).cocos.vueHandeKeyDown(e);
      }
      if ((window as any).cocos.showSocpedComponent) {
        return;
      }
      // 37：左， 38：上， 39：右， 40：下
      console.warn("cocos keyboard", e.key);
      if (e.keyCode === 16 /** Shift */ || e.keyCode === 17 /** Ctrl */ || e.keyCode === 91 /** Cmd */) {
        this._multiSelect = true;
        preKeyCode = e.keyCode;
      } else if (this._multiSelect) {
        this.onPressMultiKey(preKeyCode, e.keyCode);
      } else {
        this.onPressKey(e.keyCode);
      }
    };

    this._keyupEvent = e => {
      if ((window as any).cocos.vuehandleKeyUp) {
        (window as any).cocos.vuehandleKeyUp(e);
      }
      if ((window as any).cocos.showSocpedComponent) {
        return;
      }
      if (e.keyCode === 16 /** Shift */ || e.keyCode === 17 /** Ctrl */ || e.keyCode === 91 /** Cmd */) {
        this._multiSelect = false;
      }
    },

      this._mousedownEvent = event => {
        if ((window as any).cocos.showSocpedComponent) {
          return;
        }
        if (!event.metaKey && !event.ctrlKey && !event.shiftKey) {
          this._multiSelect = false;
        }
      },
      // 监听键盘按下
      window.document.addEventListener(
        "keydown",
        this._keydownEvent,
        true
      );
    // 监听键盘抬起
    window.document.addEventListener(
      "keyup",
      this._keyupEvent,
      true,
    );
    // 监听网页上鼠标点击事件，来处理失去焦点时的多选bug
    window.document.addEventListener(
      "mousedown",
      this._mousedownEvent,
      true,
    );
  }

  /** 注册事件 */
  public registEvent(): void {
    this.node.on(cc.Node.EventType.MOUSE_DOWN, this.mouseBegin, this);
    this.node.on(cc.Node.EventType.MOUSE_MOVE, this.mouseMove, this);
    this.node.on(cc.Node.EventType.MOUSE_UP, this.mouseUp, this);
    this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.mouseUp, this);
    this.node.on(cc.Node.EventType.MOUSE_WHEEL, this.mouseWheel, this);
  }

  /** 移除事件 */
  public unregistEvent(): void {
    this.node.off(cc.Node.EventType.MOUSE_DOWN, this.mouseBegin, this);
    this.node.off(cc.Node.EventType.MOUSE_MOVE, this.mouseMove, this);
    this.node.off(cc.Node.EventType.MOUSE_UP, this.mouseUp, this);
    this.node.off(cc.Node.EventType.TOUCH_CANCEL, this.mouseUp, this);
    this.node.off(cc.Node.EventType.MOUSE_WHEEL, this.mouseWheel, this);
    window.document.removeEventListener(
      "keydown",
      this._keydownEvent,
      true
    );
    window.document.removeEventListener(
      "keyup",
      this._keyupEvent,
      true,
    );
    window.document.removeEventListener(
      "mousedown",
      this._mousedownEvent,
      true,
    );
  }

  /**
   * 判断 id 是否与 editId 相同，如果不同，在验证下 editid 是否触摸
   * @param id 
   * @param editId 
   * @param pos 点击位置
   */
  private isTouchEditId(id: string, editId: string, pos: cc.Vec2, isMulti?: boolean) {
    if (editId !== "-1" && id !== editId) {
      let _curobj = this._displayManager.getDisplayObjectById(editId);
      if (_curobj) {
        // 如果是组合或者是组合中的子节点，则返回组合节点id
        if (yy.instance(DisplayObjectManager).isSubNode(editId) != false) {
          editId = yy.instance(DisplayObjectManager).isSubNode(editId) as string;
          console.log("%c Line:163 🍷 editId", "color:#33a5ff", editId);
          _curobj = this._displayManager.getDisplayObjectById(editId);
        }
        let isInRect = MathUtil.isPosInRotationRect(pos, _curobj.rect, yy.checkValue(_curobj.node.angle, 0));
        if (isInRect) {
          let _id = _curobj.cid;
          console.log("%c Line:169 🥓 _id", "color:#3f7cff", _id);
          if (_id && editId == _id && _id.length > 0) {
            return _id;
          }
        }
      }
    }
    // 当已经选择多个组件时，如果点击的组件在已选组件中，则返回已选组件中对应的id
    if (isMulti) {
      for(let i = 0; i < this._displayManager.selectedIds.length; i++){
        let _curobj = this._displayManager.getDisplayObjectById(this._displayManager.selectedIds[i]);
        let isInRect = MathUtil.isPosInRotationRect(pos, _curobj.rect, yy.checkValue(_curobj.node.angle, 0));
        if(isInRect){
          return this._displayManager.selectedIds[i];
        }
      }
    }
    return id;
  }

  /** 鼠标按下逻辑 */
  private mouseBegin(event: cc.Event.EventMouse) {
    this._animTouchHandler.mouseDown(event);
    // 如果选中了动画操作节点
    if (this._animTouchHandler.onTouchStart(event)) {
      this._displayManager.selectId = "-1";
      return;
    }
    this._mouseBeginPos = null;
    this._isHoldOnMouseLeft = true;
    let local = event.getLocation();
    let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    this._mouseLastPos = pos;
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }
    if (yy.single.instance(CutShapeManager).mouseBegin(pos, editId)) {
      return;
    }
    const curobj = this._displayManager.getDisplayObjectById(editId);

    curobj && curobj.node.children[0].emit(event.type + "_proxy", event);

    let id = this._collisionManager.testDisplayObject(pos, this._dataCenterBridge.getComponentIds(), this._displayManager.dpMap);

    // 如果 id 与 editId 不同，在验证下 editid 是否触摸
    if(this._displayManager.selectedIds.length > 0){
      id = this.isTouchEditId(id, editId, pos, true);
    }else {
      id = this.isTouchEditId(id, editId, pos);
    }
    
    // console.log("%c Line:204 🥑 id", "color:#ffdd4d", id);
    if (this._svgTouchHandler.onTouchStart(id, event)) {
      return;
    }
    if (id === "-1") {
      this._mouseBeginPos = pos;
      this._mouseEndPos = null;
    }

    this._moveHandler.mouseBegin(pos, id, this._multiSelect);
    // 通知选中输入框
    this._editBoxTouchHandler.mouseBegin(event, editId, this._displayManager);
  }

  /** 鼠标抬起 */
  private mouseUp(event: cc.Event.EventMouse): void {
    if (this._animTouchHandler.mouseUp(event)) {
      return;
    }
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }
    if (yy.single.instance(CutShapeManager).mouseUp(editId)) {
      return;
    }
    const curobj = this._displayManager.getDisplayObjectById(editId);

    curobj && curobj.node.children[0].emit(event.type + "_proxy", event);
    this._editBoxTouchHandler.mouseUp(editId, this._displayManager);
    this._isHoldOnMouseLeft = false;
    this._svgTouchHandler.onTouchEnd(event);
    let local = event.getLocation();
    let destX = local.x;
    let destY = cc.winSize.height - local.y;
    let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    this._mouseLastPos = pos;
    if (event.getButton && event.getButton() === cc.Event.EventMouse.BUTTON_RIGHT) {
      let id = this._collisionManager.testDisplayObject(pos, this._dataCenterBridge.getComponentIds(), this._displayManager.dpMap);
      if (id !== "-1") {
        if(this._displayManager.selectedIds.length > 0){
          id = this.isTouchEditId(id, editId, pos, true);
        }else {
          id = this.isTouchEditId(id, editId, pos);
        }
        this._displayManager.selectId = id;
        yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, {
          ids: [id],
          isVue: false,
          isMulti: this._multiSelect,
        });
        this._dataCenterBridge.showContextMenu({ id, left: destX, top: destY });
      } else {
        this._dataCenterBridge.showContextMenu({ left: destX, top: destY });
      }
    }
    let id = this._collisionManager.testDisplayObject(pos, this._dataCenterBridge.getComponentIds(), this._displayManager.dpMap);

    // 如果 id 与 editId 不同，在验证下 editid 是否触摸
    // console.log("%c Line:202 🥑 id", "color:#ffdd4d", id);
    if(this._displayManager.selectedIds.length > 0){
      id = this.isTouchEditId(id, editId, pos, true);
    }else {
      id = this.isTouchEditId(id, editId, pos);
    }

    if (this._lastSelectTime === 0) {
      this._lastSelectTime = new Date().getTime() / 1000;
    } else {
      const deltaTime = new Date().getTime() / 1000 - this._lastSelectTime;
      if (deltaTime < 0.3) {
        console.warn("你双击啊！！");
        this._dataCenterBridge.doubleClick({ id });
      }
      this._lastSelectTime = new Date().getTime() / 1000;
    }
    this.clearDrawRect();
    this._moveHandler.mouseUp(pos, this._multiSelect);
    // 清除所有辅助线
    cc.director.emit("REMOVE_ALL_LINE");
  }

  cancleStageMouse() {
    if (this._isHoldOnMouseLeft) {
      this._isHoldOnMouseLeft = false;
      this.clearDrawRect();
      this._moveHandler.cancleStageMouse(this._mouseLastPos, this._multiSelect);
      cc.director.emit("REMOVE_ALL_LINE");
    }
  }


  /** 鼠标移动的监听函数 */
  public mouseMove(event: cc.Event.EventMouse): void {
    if (this._animTouchHandler.mouseMove(event)) {
      return;
    }

    let local = event.getLocation();
    let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    let id = this._collisionManager.testDisplayObject(pos, this._dataCenterBridge.getComponentIds(), this._displayManager.dpMap);
    this._mouseLastPos = pos;
    // 当前选中的节点正在编辑中
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }

    if (yy.single.instance(CutShapeManager).mouseMove(pos, editId)) {
      return;
    }
    const curobj = this._displayManager.getDisplayObjectById(editId);
    curobj && curobj.node.children[0].emit(event.type + "_proxy", event);
    if (this._editBoxTouchHandler.mouseMove(event, editId, this._displayManager)) {
      return;
    }

    // 判断是否svg编辑顶点
    if (this._isHoldOnMouseLeft && this._svgTouchHandler.onTouchMove(this._displayManager.selectId, event)) {
      return;
    }

    if (this._isHoldOnMouseLeft) {
      this._moveHandler.mouseMove(event);
    }

    let styleCursor = "default";
    if (id !== "-1") {
      styleCursor = "move"; // 移动样式
    }
    // 如果点击的是选中的 操作节点,选中Id不清零
    if (this._displayManager.selectId !== "-1") {
      let _typeHandlePoint = null;
      let _displayAngle = 0;
      let zIndex = -1;
      for (let i = 0; i < this._displayManager.selectedIds.length; i++) {
        let _selectId = this._displayManager.selectedIds[i];
        let _selDisplay = this._displayManager.getDisplayObjectById(_selectId);
        let typeHandle = _selDisplay.inSelRect(pos);
        if (typeHandle !== null) {
          let tmpZIndex = _selDisplay.node.getSiblingIndex();
          if (_selDisplay.dragable != false && tmpZIndex > zIndex) {
            _typeHandlePoint = typeHandle;
            zIndex = tmpZIndex;
            _displayAngle = _selDisplay.getNodeAngle();
          }
        }
      }
      // 点到操作点上
      if (_typeHandlePoint) {
        // 操作按钮
        if (_typeHandlePoint.data.type === SEL_POINT_ENUM.ROTATE) {
          styleCursor = Config.CUSTOM_CURSOR;
        } else {
          styleCursor = CursorUtil._getCursorTyle(_typeHandlePoint.data.type, _displayAngle);
        }
        // 点到操作节点 取消拖选
        this._mouseBeginPos = null;
        this.clearDrawRect();
      }
    }
    // 选中框
    this._mouseEndPos = pos;
    this.drawSelectRect();
    /*
     * 防止重复 -- vue 有设置鼠标样式 有需要再加
     * if (this._lastCursorStykle != styleCursor) {
     */
    cc.game.canvas.style.cursor = styleCursor;
  }

  /**
   * 鼠标滚动
   * @param event
   */
  public mouseWheel(event: cc.Event.EventMouse): void {
    this._editBoxTouchHandler.mouseWheel(event, this._displayManager.selectId, this._displayManager);
  }

  /**
   * 清除鼠标拖拽的选中框
   */
  private drawSelectRect() {
    if (!this._mouseBeginPos || !this._mouseEndPos) {
      return;
    }
    let { minX, maxX, minY, maxY } = MathUtil.getMinMaxXY(this._mouseBeginPos, this._mouseEndPos);
    let edgeNode = this.node.getChildByName("draw_rect");
    if (!edgeNode) {
      edgeNode = new cc.Node();
      edgeNode.addComponent(cc.Graphics);
      edgeNode.name = "draw_rect";
      this.node.addChild(edgeNode);
    }
    let graphics = edgeNode.getComponent(cc.Graphics);
    graphics.lineWidth = 3;
    graphics.strokeColor = cc.Color.CYAN;
    graphics.clear();
    graphics.moveTo(minX, minY);
    graphics.lineTo(minX, maxY);
    graphics.lineTo(maxX, maxY);
    graphics.lineTo(maxX, minY);
    graphics.lineTo(minX, minY);
    graphics.stroke();
  }

  /**
   * 清除鼠标选中框
   */
  private clearDrawRect() {
    let edgeNode = this.node.getChildByName("draw_rect");
    if (edgeNode) {
      edgeNode.removeFromParent();
    }
    let clearDataFun = () => {
      this._mouseBeginPos = null;
      this._mouseEndPos = null;
    };
    if (!this._mouseBeginPos || !this._mouseEndPos) {
      clearDataFun();
      return;
    }
    // 修复点击创建文本框自动获取焦点后 多选问题
    let { minX, maxX, minY, maxY } = MathUtil.getMinMaxXY(this._mouseBeginPos, this._mouseEndPos);
    let width = maxX - minX;
    let height = maxY - minY;
    let rect = new cc.Rect(minX + width / 2, minY + height / 2, width, height);
    let ids = this._collisionManager.testDisplaySelect(rect, this._dataCenterBridge.getComponentIds(), this._displayManager.dpMap);
    if (ids.length > 0) {
      yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, {
        ids,
        isVue: false,
        isMulti: this._multiSelect,
      });
    }
    clearDataFun();
  }

  /**
   * 更新选中
   * @param selectId
   * @param isMulti
   */
  private selectEditor(ids: string[], isMulti: boolean) {
    let selectedIds = this._displayManager.selectedIds;
    if (ids.length > 0 && !isMulti) {
      if (selectedIds.indexOf(ids[ids.length - 1]) >= 0) {
        return;
      }
    }
    // 没有选中组件，再次点中也没选中组件
    if (selectedIds.length <= 0 && ids.length <= 0) {
      return;
    }
    yy.single.instance(CommandFactory).execute(ReplaceCmptCommand, { ids, isVue: false, isMulti });
  }

  /**
   * 监听按住方向键
   * @param dir 方向 37：左， 38：上， 39：右， 40：下
   */
  private onPressDirection(dir: number): void {
    let delta: cc.Vec3 = new cc.Vec3(0, 0, 0);
    switch (dir) {
      case 37: // 左
        delta.x -= 1;

        break;
      case 38: // 上
        delta.y += 1;
        break;
      case 39: // 右
        delta.x += 1;
        break;
      case 40: // 下
        delta.y -= 1;
        break;
      default:
        delta.x += 1;
    }
    if (this._multiSelect) {
      delta.x *= 10;
      delta.y *= 10;
    }
    // 当前选中的节点为editbox，并且正在编辑中
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }
    if (this._editBoxTouchHandler.keyDown(editId, this._displayManager, dir, this._multiSelect)) {
      return;
    } else {
      // 非编辑文本情况下，不要修改x，y值。x，y坐标的修改是其他路径进行修改的
      if ((window as any).vueFocus) {
        delta.x = 0;
        delta.y = 0;
      }
      // delta.x = 0;
      // delta.y = 0;
    }
    this._moveHandler.onPressDirection(delta);
  }

  /**
   * 处理组合建
   * @param preKeyCode 上一次按键
   * @param keyCode 当前按键
   */
  private onPressMultiKey(preKeyCode, keyCode) {
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }
    if (preKeyCode == 17 || preKeyCode == 91) {
      // Ctrl、Command
      this._editBoxTouchHandler.ctrlKeyDown(editId, this._displayManager, keyCode);
    } else if (preKeyCode == 16) {
      // Shift
      if (keyCode >= 37 && keyCode <= 40) {
        // 上下左右方向键
        this.onPressDirection(keyCode);
      }
    }
  }

  private onPressKey(keyCode) {
    let editId = this._displayManager.selectId;
    if (this._displayManager.selectSubId !== "-1") {
      editId = this._displayManager.selectSubId;
    }
    if (keyCode >= 37 && keyCode <= 40) {
      // 方向键调整元素移动
      this.onPressDirection(keyCode);
    } else {
      this._editBoxTouchHandler.keyDown(editId, this._displayManager, keyCode);
    }
  }
  onDestroy() {
    this.unregistEvent();
  }
}
