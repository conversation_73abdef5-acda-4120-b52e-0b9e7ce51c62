import { SingleBase } from "../../qte/core/base/SingleBase";
import ValueUtils from "../../qte/core/utils/ValueUtils";
import { TweenEditType, TweenType } from "../core/animation/AnimationData";
import AnimDisplayControl from "../core/animation/display/AnimDisplayControl";
import AnimDisplayManager from "../core/animation/display/AnimDisplayManager";
import CollisionManager from "../core/collision/CollisionManager";
import { UpdateAnimPropertiesCommand } from "../core/command/operate/anim/UpdateAnimPropertiesCommand";
import CommandFactory from "../core/command/operate/CommandFactory";

export default class AnimTouchHandler extends SingleBase {
    // 当前选中拖动的的组件id
    private _selectId: string = "-1";
    // 碰撞管理器
    private _collisionManager: CollisionManager;

    // 是不是按住
    private _isHoldOn: boolean = false;

    private _animDisplayControl: AnimDisplayControl;

    private _animDisplayManager: AnimDisplayManager;

    // 坚持双击
    private _timearray: number[];
    // 上一次画点的时间戳
    private _lastTimeStamp: number = -1;
    // 划线间隔 秒
    private _drawPointStamp: number = 0.07;
    public initInstance (): void {
        this._animDisplayManager = yy.single.instance(AnimDisplayManager);
        this._collisionManager = yy.single.instance(CollisionManager);
        this._animDisplayControl = yy.single.instance(AnimDisplayControl);
        this._timearray = [];
    }

    public onTouchStart (event: cc.Event.EventMouse): boolean {
        if (!this._animDisplayControl.idAnimationEdit) {
            this._selectId = "-1";
            return false;
        }
        // 添加双击检测
        let timeDate = new Date();
        let time = timeDate.getTime();// 获取当前时间的毫秒数
        this._lastTimeStamp = time; // 记录第一次点击的时间
        if (this._timearray.length > 0 && (time - this._timearray[0]) / 1000 > 0.2) {
            this._timearray = [];
        }
        this._timearray.push(time);

        let local = event.getLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);

        if (this._animDisplayControl.getEditType() === TweenEditType.DRAW) {
            this._selectId = "-1";
            if (this._timearray.length >= 2) {
                return true;
            } // 如果双击结束了 就不画了
            this._animDisplayControl.drawDisplayObj(pos);
            return true;
        }

        let id = this._collisionManager.testDisplayObject(
            pos,
            this._animDisplayManager.getComponetIdArray(),
            this._animDisplayManager.dpMap
        );
        if (id !== "-1") {
            this._selectId = id;
        } else {
            this._selectId = "-1";
        }
        this._animDisplayControl.selecAnimObj(this._selectId);
        return this._selectId !== "-1";
    }

    public onTouchMove (event: cc.Event.EventMouse) {
        if (!this._isHoldOn) {
            return false;
        } // 不是按住
        let local = event.getLocation();
        let mousePos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
        if (this._animDisplayControl.editType === TweenEditType.DRAW && this._animDisplayControl.customLineData._editTweenType === TweenType.CUSTOM_LINE) {
            let timeDate = new Date();
            let time = timeDate.getTime();// 获取当前时间的毫秒数
            if ((time - this._lastTimeStamp) / 1000 > this._drawPointStamp) {
                this._animDisplayControl.drawDisplayObj(mousePos);
                this._lastTimeStamp = time;
            }
            return true;
        }
        if (this._selectId === "-1") {
            return false;
        }
        let delta = event.getDelta();
        let pos = new cc.Vec3(ValueUtils.setOneDecimal(delta.x),  ValueUtils.setOneDecimal(delta.y));
        // 坐标移动增量,给该显示对象做累加
        this._animDisplayControl.updateDisplayObj(this._selectId, pos, this._animDisplayControl.customLineData._editActionId);

        return true;
    }

    public onTouchEnd (event: cc.Event.EventMouse): boolean {
        if (this._timearray.length === 2 && this._animDisplayControl.getEditType() === TweenEditType.DRAW) { // 检测双击
            // 保存数据
            let optionData = this._animDisplayControl.getActionOption("-1");
            if (optionData.points.length > 0) {
                this._animDisplayControl.saveOptionData(this._animDisplayControl.customLineData._editActionId, optionData);
                this._animDisplayControl.endCustomLine();
            }
        }
        if (this._animDisplayControl.getEditType() === TweenEditType.DRAW) {
            return true;
        }
        this.updateVue(this._selectId);
        this._selectId = "-1";
        return false;
    }

    public mouseMove (event: cc.Event.EventMouse): boolean {
        let local = event.getLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
        this.onTouchMove(event);
        if (this._animDisplayControl.getEditType() === TweenEditType.DRAW) {
            cc.game.canvas.style.cursor = "move";
            this._animDisplayControl.drawMouseLine(pos);
            return true;
        }

        if (this._selectId !== "-1") {
            return true;
        }

        let id = this._collisionManager.testDisplayObject(
            pos,
            this._animDisplayManager.getComponetIdArray(),
            this._animDisplayManager.dpMap
        );
        let styleCursor = "default";
        if (id !== "-1") {
            // 移动样式
            styleCursor = "move";
        }
        cc.game.canvas.style.cursor = styleCursor;
        return id !== "-1";
    }

    public mouseDown (event: cc.Event.EventMouse) {
        this._isHoldOn = true;
    }

    public mouseUp (event: cc.Event.EventMouse): boolean {
        this._isHoldOn = false;
        return this.onTouchEnd(event);
    }

    /**
     * 选中显示组件
     * @param cmptId 组件id
     */
    public selectedDisplayObject (cmptId) {
        if (!this._animDisplayControl.idAnimationEdit) {
            return;
        }
        this._animDisplayControl.updateFragmentsMap();
        this._animDisplayControl.showAllPah(cmptId);
    }

    /**
     * 更改显示中的 操作节点属性
     * @param animIds
     */
    private updateVue (animId: string) {
        let displayObj = this._animDisplayManager.getAnimDisplayObject(animId);
        if (!displayObj) {
            return;
        }
        let oldData = {
            id: animId,
            newProperties: displayObj.oldProperties,
            actionId: this._animDisplayManager.getActionById(animId)
        };

        let newData = {
            id: animId,
            newProperties: {
                x: displayObj.node.x,
                y: displayObj.node.y
            },
            actionId: this._animDisplayManager.getActionById(animId)
        };
        // 添加cmd 做回退
        yy.single.instance(CommandFactory).pushCommand(UpdateAnimPropertiesCommand, newData, oldData);
        // 通知编辑器修改属性
        let optionData = this._animDisplayControl.getActionOption(animId);
        this._animDisplayControl.saveOptionData(this._animDisplayControl.customLineData._editActionId, optionData);
    }
}
