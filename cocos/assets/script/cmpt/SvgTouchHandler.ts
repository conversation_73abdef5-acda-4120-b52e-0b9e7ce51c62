/*
 * @FilePath     : /assets/script/cmpt/SvgTouchHandler.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import { SingleBase } from "../../qte/core/base/SingleBase";
import { CmptType } from "../common/EditorEnum";
import { SEL_POINT_ENUM } from "../core/display/base/cmpt/DisplaySelect";
import DisplaySvg, { EditorType } from "../core/display/base/DisplaySvg";
import DisplayObjectManager from "../core/display/DisplayObjectManager";

const { ccclass } = cc._decorator;

@ccclass
export default class SvgTouchHandler extends SingleBase {
    private _displayObjectMgr: DisplayObjectManager;
    private _selectId;
    public initInstance (): void {
        this._displayObjectMgr = yy.single.instance(DisplayObjectManager);
    }
    public onTouchStart (id, event:cc.Event.EventMouse) {
        let _disObject = this._displayObjectMgr.getDisplayObjectById(id) as DisplaySvg;
        let local = event.getLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
        // 点击空白处
        if (id === "-1") {
            // 增加顶点修改按钮
            for (let i = 0; i < this._displayObjectMgr.selectedIds.length; i++) {
                let _selectId = this._displayObjectMgr.selectedIds[i];
                let _selDisplay = this._displayObjectMgr.getDisplayObjectById(_selectId) as DisplaySvg;
                if (_selDisplay.type === CmptType.SVG) {
                    let _isClick = _selDisplay.clickVertexBtn(pos);
                    if (_isClick) {
                        return _isClick;
                    }
                    if (_selDisplay.checkVertexType()) {
                        _selDisplay.editorToRect();
                    }
                }
            }
            return false;
        }
        // 判断是否是svg 并且点击的点是顶点才返回true;    
        if (_disObject && id !== "-1" && _disObject.type === CmptType.SVG) {
            let vertexIndex = _disObject.getVertexIndex(cc.v2(pos.x, pos.y));
            if (_disObject.checkVertexType()) {
                // 是顶点编辑模式
                if (vertexIndex !== null && vertexIndex !== SEL_POINT_ENUM.NONE) {
                    return true;
                }
                _disObject.setSelected(true);
            } else {
                _disObject.vertexBtnView();
            }
            _disObject.editorToRect();
        }
        
        return false;
    }

    public onTouchMove (id, event: cc.Event.EventMouse): boolean {
        let _disObject = this._displayObjectMgr.getDisplayObjectById(id) as DisplaySvg;
        // 判断是否
        if (_disObject && id !== "-1" && _disObject.type === CmptType.SVG && _disObject.checkVertexType()) {
            let local = event.getLocation();
            let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);

            let vertexIndex = _disObject.getVertexIndex(cc.v2(pos.x, pos.y));
            if (vertexIndex === null || vertexIndex === SEL_POINT_ENUM.NONE) {
                // yy.error("vertexIndex 出错了");
                return;
            }
            _disObject.changeShapeSvg(vertexIndex, cc.v3(pos.x, pos.y));
            // _disObject.refreshRectPoint();
            this._selectId = id;
            return id;
        }

        return false;
    }
    
    public onTouchEnd (event: cc.Event.EventMouse): boolean {
        /*
         * 如果有选中顶点，清除顶点操作记录
         */
        let _display = this._displayObjectMgr.getDisplayObjectById(this._selectId);
        if (_display && _display.type === CmptType.SVG) {
            let _obj = _display as DisplaySvg;
            if (_obj.editorType === EditorType.vertex) {
                _obj.displayVertex.chooseIndex = null;
            }
        }

        return false;
    }
    // VUE会自动调用
    /*
     * public step ():void {
     *     yy.single.instance(CommandFactory).step();
     * }
     */
}
