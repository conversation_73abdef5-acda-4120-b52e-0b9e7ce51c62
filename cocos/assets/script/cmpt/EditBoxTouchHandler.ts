import { SingleBase } from "../../qte/core/base/SingleBase";
import { CmptType } from "../common/EditorEnum";
import DisplayEditBox from "../core/display/base/DisplayEditBox";
import DisplayObjectManager from "../core/display/DisplayObjectManager";

export default class EditBoxTouchHandler extends SingleBase {
  private checkSelectedType(selectId: string, displayManager: DisplayObjectManager) {
    if (selectId === "-1") {
      return false;
    }
    let node = displayManager.getDisplayObjectById(selectId);
    if (!node) {
      return false;
    }
    if (node.type !== CmptType.LABEL) {
      return false;
    }
    let nodeCom = node.getComponent(DisplayEditBox);
    if (nodeCom.editable) {
      const focus = (nodeCom.editable as any).properties ? (nodeCom.editable as any).properties.focus : (nodeCom.editable as any).focus;
      if (focus === false) {
        return false;
      }
    }
    if (nodeCom) {
      return nodeCom;
    }
    return false;
  }

  private checkSelectedTypeH5Label(selectId: string, displayManager: DisplayObjectManager) {
    if (selectId === "-1") {
      return false;
    }
    let node = displayManager.getDisplayObjectById(selectId);
    if (!node) {
      return false;
    }
    if (node.type !== CmptType.SPECIALCOMPONENT && (node as any).subType != "h5Label") {
      return false;
    }
    if ((node as any).assembleComponent && (node as any).assembleComponent.node) {
      let nodeCom = (node as any).assembleComponent.node.getComponent("h5LabelComponent");
      if (nodeCom) {
        return nodeCom;
      }
    }
    return false;
  }

  public mouseBegin(event: cc.Event.EventMouse, selectId: string, displayManager: DisplayObjectManager) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom) {
      nodeCom.mouseBeginLayout(event);
    }
  }

  public mouseMove(event: cc.Event.EventMouse, selectId: string, displayManager: DisplayObjectManager): boolean {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom && nodeCom.getNodeEditStatue()) {
      nodeCom.mouseMoveLayout(event);
      return true;
    }
    return false;
  }

  public mouseUp(selectId: string, displayManager: DisplayObjectManager) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom) {
      nodeCom.mouseUpLayout();
    }
  }

  public mouseWheel(event: cc.Event.EventMouse, selectId: string, displayManager: DisplayObjectManager) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom) {
      nodeCom.mouseWheel(event);
    }
    let nodeCom1 = this.checkSelectedTypeH5Label(selectId, displayManager);
    if (nodeCom1) {
      nodeCom1.mouseWheel(event);
    }
  }

  public stopEdit(selectId: string, displayManager: DisplayObjectManager) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom && !nodeCom.getNodeEditStatue()) {
      nodeCom.setpikerviewStatue(1);
    }
  }

  public isEditState(selectId: string, displayManager: DisplayObjectManager) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    return nodeCom && nodeCom.getNodeEditStatue();
  }

  public keyDown(selectId: string, displayManager: DisplayObjectManager, keyNum: number, shift?: boolean) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom && nodeCom.getNodeEditStatue()) {
      if (keyNum >= 37 && keyNum <= 40) {
        // 上下左右方向键
        nodeCom.clickDirectionKey(keyNum, shift);
      } else if (keyNum === 13) {
        // 回车按钮
        // nodeCom.clickEnterKey();
      } else if (keyNum == 8) {
        nodeCom.clickDeleteKey();
      }
      return true;
    }
    return false;
  }

  public ctrlKeyDown(selectId: string, displayManager: DisplayObjectManager, keyNum: number) {
    let nodeCom = this.checkSelectedType(selectId, displayManager);
    if (nodeCom && nodeCom.getNodeEditStatue()) {
      switch (keyNum) {
        case 65:
          nodeCom.clickCtrlA();
          break;
        case 67:
          nodeCom.clickCtrlC();
          break;
        case 88:
          nodeCom.clickCtrlX();
          break;
      }
      return true;
    }
    return false;
  }

}
