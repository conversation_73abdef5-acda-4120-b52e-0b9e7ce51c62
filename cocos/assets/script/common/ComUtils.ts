import { FlipType } from "./EditorEnum";

export class ComUtil {
  /**
   * label 加预乘
   * @param label
   */
  static setLabelBlend(label: cc.Label) {
    // eslint-disable-next-line dot-notation
    label["_srcBlendFactor"] = cc.macro.BlendFactor.ONE;
    // eslint-disable-next-line dot-notation
    label["_dstBlendFactor"] = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
  }

  /**
   * 添加预乘
   * @param texture
   * @param spr
   */
  static addPremultiplyAlpha(texture: cc.Texture2D, spr: cc.Sprite) {
    texture.setPremultiplyAlpha && texture.setPremultiplyAlpha(true);
    spr.spriteFrame = new cc.SpriteFrame(texture);
    spr.sizeMode = cc.Sprite.SizeMode.CUSTOM;
    spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
    spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
  }

  static setGray(node: cc.Node, bGray: boolean) {
    let childrens = ComUtil.getNodeList(node);
    for (let i = 0; i < childrens.length; i++) {
      let sprite = childrens[i].getComponent(cc.Sprite);
      let spine = childrens[i].getComponent(sp.Skeleton);
      if (sprite) {
        ComUtil.setSpriteGray(sprite, bGray);
      }
      if (spine) {
        ComUtil.setSpineGray(spine, bGray);
      }
    }
  }

  static setSpineGray(skeleton: sp.Skeleton, bGray: boolean) {
    if (bGray) {
      yy.loader.loadRes("material/gray-spine", cc.Material, (err, data) => {
        if (err) {
          yy.error("加载gray-spine失败");
          return;
        }
        skeleton.setMaterial(0, data);
        (skeleton as any).markForRender(true);
      });
    } else {
      skeleton.setMaterial(0, cc.Material.getBuiltinMaterial("2d-spine"));
      (skeleton as any).markForRender(true);
    }
  }
  static setSpriteGray(sprite: cc.Sprite, bGray: boolean) {
    let matral: cc.Material = null;
    if (bGray) {
      matral = cc.Material.getBuiltinMaterial("2d-gray-sprite");
    } else {
      matral = cc.Material.getBuiltinMaterial("2d-sprite");
    }

    sprite.setMaterial(0, matral);
  }
  static setSvgShapeOpacity(node: cc.Node, opacity: number) {
    return new Promise((resolve, reject) => {
      cc.resources.load("effects/svg_graph", cc.EffectAsset, (err, eff: cc.EffectAsset) => {
        if (err) {
          yy.warn(err);
          reject(err);
          return;
        }
        let material = cc.Material.create(eff);
        material.setProperty("alphaU", opacity);
        node && node.getComponent(cc.Graphics) && node.getComponent(cc.Graphics).setMaterial(0, material);
        resolve(node);
      });
    });
  }

  static getNodeList(node): cc.Node[] {
    let list = [node];
    let children = node.getChildren();
    for (let i = 0; i < children.length; i++) {
      let rtn = this.getNodeList(children[i]);
      list.push(...rtn);
    }
    return list;
  }
  static async setMateria(sprite: cc.Sprite, flip: FlipType) {
    return new Promise((resolve, reject) => {
      cc.resources.load("effects/sprite-flip-effect", cc.EffectAsset, (err, eff: cc.EffectAsset) => {
        if (err) {
          yy.warn(err);
          reject(err);
          return;
        }
        let material = cc.Material.create(eff);
        // material.setProperty("flipX", !!(flip & 1));
        // material.setProperty("flipY", !!(flip & 2));
        if (flip) {
          material.setProperty("filpType", Number(flip.toFixed(1)));
        }
        sprite && sprite.setMaterial(0, material);
        resolve(sprite);
      });
    });
  }
}
