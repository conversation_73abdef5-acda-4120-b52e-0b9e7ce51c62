/*
 * @FilePath     : /cocos/assets/script/common/EditorEnum.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
/*
 * @Date: 2021-09-18 13:11:50
 * @LastEditors: chxu
 * @LastEditTime: 2021-09-18 16:25:44
 * @FilePath: /interactive-question-editor/cocos/assets/script/common/EditorEnum.ts
 * @Author: chxu
 */
/** 更新节点层级枚举 */
export enum UpdateLevelType {
  TOP = 0, // 顶层
  BOTTOM = 1, // 底层
  FORWARD = 3, // 上一层
  BACKWARD = 4, // 下一层
}

/** 题目类型 */
export enum QuestionType {
  /** 题组  */
  GROUP = 1, //
  /** 无对错题目 */
  NORIGHTWRONG = 2,
  /** 有对错题目 */
  HASRIGHTWRONG = 3,
  /** 选择题 */
  CHOOSE = 4,
  /** 填空题 */
  BLANK = 5,
  /** 口述题 */
  ORAL = 44,
  /** 神秘提示 */
  GodTips = 14,
}

/** 预览动画类型 */
export enum AnimPreviewType {
  ALL = 1, // 全部动画
  FRAGMENT = 2, // 预览动画片段
}

/** 编辑模式 */
export enum EditMode {
  NORMAL = 0, // 普通模式
  ANIM = 1, // 动画模式
}

/** 组件类型枚举 */
export enum CmptType {
  COCOSANI = "cocosAni",
  SPRITE = "sprite",
  LABEL = "label",
  SPINE = "spine",
  GROUP = "group",
  FORMULA = "formula",
  SVGSHAPE = "svgShape",
  CUTSHAPE = "cutShape",
  SPECIALCOMPONENT = "specialComponent",
  OPTIONCOMPONENT = 'optionComponent',
  SHAPE = "shape",
  SVG='SVG', // 防止报错
  // 增加新文本组件
  RICHTEXTSPRITE = "richTextSprite"
  
}

export enum SpecialComponentSubTypes {
  CmptRecord = "voice", // 录音组件
  KeyBord = "keyboard", // 键盘组件
  KeyBordEnglish = "keyboardEnglish",
  MatchBoard = "matchboard", // 火柴组件
  Counter = "counter", // 计数组件
  Write = "write", // 手写组件
  Clock = "clock", // 钟表组件
  Tangram = "tangram", // 七巧板
  Speaker = "speaker", // 音频组件
  Brush = "brush",
  H5Label = "h5Label" // h5文本组件
}
export enum OptionComponentSubTypes {
  huarongdao = 'huatongdao'
}

/** 组件层级 */
export enum CmptLayer {
  OBJECT_LAYER = "object_layer",
  ANIM_LAYER = "anim_layer",
  TOUCH_LAYER = "touch_layer",
  TOP_LAYER = "top_layer",
}

/** 编辑器组 */
export enum EditorGroup {
  DEFAULT = "default", // 默认group
  EDGE = "edge", // 边框
}

/** 翻转类型 */
export enum FlipType {
  FNORMAL = 0, // 水平（不翻转）｜垂直（不翻转）
  FLIPX = 1, // 水平（翻转）｜垂直（不翻转）
  FLIPY = 2, // 水平（不翻转）｜垂直（翻转）
  FLIPALL = 3, // 水平（翻转）｜垂直（翻转）
}
