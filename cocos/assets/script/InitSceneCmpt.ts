/*
 * @FilePath     : /assets/script/InitSceneCmpt.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import yy from "./extends/yy";
import AssetsManager from "./AssetsManager";
import Conifg from "./Config";
import DataCenterBridge from "./core/proxy/DataCenterBridge";
import { CocosExport } from "./stage/CocosExport";
import StageSceneCmpt from "./stage/StageSceneCmpt";
import ExtraModule from "./core/proxy/ExtraModule";

yy.log('initSceneCmpts');
const { ccclass } = cc._decorator;

@ccclass
export default class InitSceneCmpt extends cc.Component {
  // LIFE-CYCLE CALLBACKS:
  initData(data?) {
    yy.single.instance(ExtraModule).init(data);
  }
  start() {
    CocosExport.initExport();
    this.initCustomCursor();
    this.init();
  }

  async init() {
    console.log("(window as any)._$store.state.template==", (window as any)._$store.state.template);
    console.log("test");
    console.log("%c Line:33 🥔 y.single.instance(ExtraModule).bootData", "color:#3f7cff", yy.single.instance(ExtraModule).bootData);
    if (yy.single.instance(ExtraModule).bootData) {
      await yy.single.instance(AssetsManager).loadBundel(Object.values(yy.single.instance(ExtraModule).bootData.resourceMap)[0] as string);
      yy.single.instance(DataCenterBridge).cocosLoadQuestionBundleFinished();
    }
    this.runToStageScene();
  }

  runToStageScene() {

    // 获取安全分辨率
    let stageSize = yy.single.instance(DataCenterBridge).getStageInfo();
    let templateData = yy.single.instance(DataCenterBridge).getTemplate();
    qte.getTemplateByKey = (key: string) => {
      return templateData[key];
    }
    qte.openMode = qte.QTE_OPENMODE.SHOW_NORMAL;
    let dSize = cc.size(stageSize.safeWidth, stageSize.safeHeight);

    // 初始化场景
    let ccs = new cc.Scene();
    ccs.autoReleaseAssets = true;
    ccs.name = "Scene";
    let canvasNode = new cc.Node();
    canvasNode.name = "Canvas";
    canvasNode.width = dSize.width;
    canvasNode.height = dSize.height;
    let canvas = canvasNode.addComponent(cc.Canvas);
    canvas.designResolution = dSize;
    canvas.fitHeight = false;
    canvas.fitWidth = false;
    ccs.addChild(canvasNode);
    canvas.addComponent(StageSceneCmpt);
    // 这里延迟1ms是为了让场景在下一帧加载
    setTimeout(() => {
      console.log("舞台创建完成InitSceneCmptFinish", new Date().getTime());
      cc.director.runSceneImmediate(ccs);
      yy.single.instance(DataCenterBridge).cocosLoadGameSceneFinished();
    }, 1);
  }

  /**
   * 获取鼠标自定义样式资源路径
   */
  initCustomCursor() {
    cc.loader.loadRes("img/refresh", cc.SpriteFrame, (err, res) => {
      if (err) {
        yy.error(err);
        return;
      }
      // eslint-disable-next-line @typescript-eslint/camelcase
      Conifg.CUSTOM_CURSOR = `url(${res._texture.nativeUrl}) 8 8,auto`;
    });
  }
}
