/*
 * @Author: your name
 * @Date: 2021-02-20 14:09:54
 * @LastEditTime: 2021-02-25 20:04:31
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /packages/Users/<USER>/Desktop/cheng/zuoyebangWorkSpace/yaya-question-editor/cocos/assets/script/TestScript.ts
 */
import MathUtil from "../qte/core/utils/MathUtil";
import AssetsManager from "./AssetsManager";
import DataCenterBridge from "./core/proxy/DataCenterBridge";
import ExtraModule from "./core/proxy/ExtraModule";
import MockData from "./core/proxy/MockData";
import { CocosExport } from "./stage/CocosExport";
import StageSceneCmpt from "./stage/StageSceneCmpt";


const {ccclass, property} = cc._decorator;

@ccclass
export default class TestScript extends cc.Component {

    @property(cc.Node)
    testNode: cc.Node = null;

    public cccc = "cccc";

    start () {
        CocosExport.initExport();
        MockData.init();
        this.init();
    }
    async init() {
        // (window as any)._$store = {};
        // (window as any)._$store.state = a;
        console.log("(window as any)._$store.state.template==", (window as any)._$store.state.template);
        console.log("%c Line:33 🥔 y.single.instance(ExtraModule).bootData", "color:#3f7cff", yy.single.instance(ExtraModule).bootData);
        if (yy.single.instance(ExtraModule).bootData) {
    
          await yy.single.instance(AssetsManager).loadBundel(Object.values(yy.single.instance(ExtraModule).bootData.resourceMap)[0] as string);
        }
        this.runToStageScene();
      }
    
      runToStageScene() {
        // 获取安全分辨率
        let stageSize = yy.single.instance(DataCenterBridge).getStageInfo();
        let templateData = yy.single.instance(DataCenterBridge).getTemplate();
        qte.getTemplateByKey = (key: string) => {
          return templateData[key];
        }
        qte.openMode = qte.QTE_OPENMODE.SHOW_NORMAL;
        let dSize = cc.size(stageSize.safeWidth, stageSize.safeHeight);
        // 初始化场景
        let ccs = new cc.Scene();
        ccs.autoReleaseAssets = true;
        ccs.name = "Scene";
        let canvasNode = new cc.Node();
        canvasNode.name = "Canvas";
        canvasNode.width = dSize.width;
        canvasNode.height = dSize.height;
        let canvas = canvasNode.addComponent(cc.Canvas);
        canvas.designResolution = dSize;
        canvas.fitHeight = false;
        canvas.fitWidth = false;
        ccs.addChild(canvasNode);
        canvas.addComponent(StageSceneCmpt);
        // 这里延迟1ms是为了让场景在下一帧加载
        setTimeout(() => {
          console.log("舞台创建完成InitSceneCmptFinish", new Date().getTime());
          cc.director.runSceneImmediate(ccs);
        }, 1);
        cc.game.setFrameRate(30);
    }

}