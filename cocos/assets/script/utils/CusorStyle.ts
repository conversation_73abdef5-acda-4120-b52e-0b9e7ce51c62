/*
 * @FilePath     : /assets/script/utils/CusorStyle.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import { SEL_POINT_ENUM } from "../core/display/base/cmpt/DisplaySelect";

export default class CursorUtil {
    static _getCursorTyle (type: SEL_POINT_ENUM, angle: number): string {
        // 从北到东北顺时针一圈圈
        let curStyle = [
            "n-resize",
            "ne-resize",
            "e-resize",
            "se-resize",
            "s-resize",
            "sw-resize",
            "w-resize",
            "nw-resize",
            "pointer"
        ];
        let numType = Number(type);
        if (type === SEL_POINT_ENUM.VERTEX) {
            return curStyle[numType - 1];
        }
        if (angle > -45 && angle < 0) {
            numType += 1;
            if (numType > 7) {
                numType -= 8;
            }
        } else if (angle >= -90 && angle <= -45) {
            numType += 2;
            if (numType > 7) {
                numType -= 8;
            }
        } else if (angle >= -180 && angle <= -90) {
            numType += 3;
            if (numType > 7) {
                numType -= 8;
            }
        } else if (angle < -180) {
            numType += 4;
            if (numType > 7) {
                numType -= 8;
            }
        }
        return curStyle[numType];
    }
}