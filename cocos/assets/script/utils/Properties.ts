import ValueUtils from "../../qte/core/utils/ValueUtils";
import { ComUtil } from "../common/ComUtils";
import { CmptType } from "../common/EditorEnum";
import DisplayEditBox from "../core/display/base/DisplayEditBox";
import DisplayObject from "../core/display/base/DisplayObject";
import DisplaySpecialComponent from "../core/display/base/DisplaySpecialComponent";
import DisplaySpine from "../core/display/base/DisplaySpine";
import DisplaySvg from "../core/display/base/DisplaySvg";
import DisplayCutShape from "../core/display/base/edit/DisplayCutShape";

export class EValue {
  // 值类型
  public type: "string" | "number" | "boolean" | "color" | "textAsset";
  // 值
  public value: any;
}

export default class Properties {
  /**
   * 设置属性
   * @param curObj 当前显示对象
   * @param type 当前组件类型
   * @param key 属性名称key值
   * @param val 属性数值
   */
  // eslint-disable-next-line max-lines-per-function
  public static colorHex(newCo: string) {
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/;
    if (/^(rgba|RGBA)/.test(newCo)) {
      const aColor = newCo.replace(/(?:\(|\)|rgba|RGBA)*/g, "").split(",");
      let strHex = "#";
      for (let i = 0; i < aColor.length; i++) {
        let hex = Number(aColor[i]).toString(16);
        if (hex === "0") {
          hex += hex;
        }
        strHex += hex;
      }
      if (strHex.length !== 7) {
        strHex = newCo;
      }
      return strHex;
    } else if (/^(rgb|RGB)/.test(newCo)) {
      const aColor = newCo.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
      let strHex = "#";
      for (let i = 0; i < aColor.length; i++) {
        let hex = Number(aColor[i]).toString(16);
        if (hex === "0") {
          hex += hex;
        }
        strHex += hex;
      }
      if (strHex.length !== 7) {
        strHex = newCo;
      }
      return strHex;
    } else if (reg.test(newCo)) {
      const aNum = newCo.replace(/#/, "").split("");
      if (aNum.length === 8) {
        let newC1 = "#";
        for (var i = 0; i < 6; i += 1) {
          newC1 += aNum[i];
        }
        return newC1;
      } else if (aNum.length === 6) {
        return newCo;
      } else if (aNum.length === 3) {
        let newC1 = "#";
        for (let i = 0; i < aNum.length; i += 1) {
          newC1 += aNum[i] + aNum[i];
        }
        return newC1;
      }
    } else {
      return newCo;
    }
  }

  public static setDefaultColor(t: string) {
    var source = t.toUpperCase();
    source = (source = source.replace(/\s*/g, "")).replace(/，/g, ",");
    var e = {
      hex1: /^#[0-9A-F]{3}$/,
      hex2: /^#[0-9A-F]{6}$/,
      rgb: /^(RGB\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\)?$/,
      argb: /^#[0-9A-F]{8}$/,
      rgba: /^(RGBA\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),([01]|0?\.\d+|1\.0+)\)?$/,
    },
      r = "";
    for (var o in e) {
      if (e[o].test(source)) {
        r = o;
        break;
      }
    }
    if (r) {
      var n = "";
      switch (r) {
        case "hex1":
        case "hex2":
          n = Properties.hexToRgba(source);
          break;
        case "rgb":
          n = Properties.rgbToRgba(source);
          break;
        case "argb":
          n = Properties.argbToRgba(source);
          break;
        case "rgba":
          n = source.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),((0?\.\d+)|(1\.0+)|[01])/)[0];
      }
      console.log(n);

      return Properties.rgbaToHex(n);
    }
  }

  public static hexToRgba(t) {
    if (/^#[0-9A-F]{3}$/.test(t)) {
      var e = t.match(/[0-9A-F]/g),
        r = e.slice(0, 3),
        n = r[0],
        g = r[1],
        b = r[2];
      return ""
        .concat(parseInt(n + n, 16).toString(), ",")
        .concat(parseInt(g + g, 16).toString(), ",")
        .concat(parseInt(b + b, 16).toString(), ",1");
    }
    if (/^#[0-9A-F]{6}$/.test(t)) {
      var c = t.match(/[0-9A-F]{2}/g),
        l = c.slice(0, 3),
        f = l[0],
        v = l[1],
        d = l[2];
      return ""
        .concat(parseInt(f, 16).toString(), ",")
        .concat(parseInt(v, 16).toString(), ",")
        .concat(parseInt(d, 16).toString(), ",1");
    }
    return "";
  }

  public static rgbToRgba(t) {
    return (t = t.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])/)[0]) + ",1";
  }
  public static argbToRgba(t) {
    var e = t.match(/[0-9A-F]{2}/g),
      r = (r = e.slice(0, 4)),
      a = r[0],
      n = r[1],
      g = r[2],
      b = r[3];
    return (
      (a = (parseInt(a, 16) / 255).toFixed(3)),
      ""
        .concat(parseInt(n, 16).toString(), ",")
        .concat(parseInt(g, 16).toString(), ",")
        .concat(parseInt(b, 16).toString(), ",")
        .concat(a)
    );
  }

  public static rgbaToHex(t) {
    var e = t.split(","),
      r = e.slice(0, 4),
      n = r[0],
      g = r[1],
      b = r[2],
      a = r[3];
    return (
      (a = parseFloat(a)),
      (n = Math.floor(a * parseInt(n) + 255 * (1 - a))),
      (g = Math.floor(a * parseInt(g) + 255 * (1 - a))),
      (b = Math.floor(a * parseInt(b) + 255 * (1 - a))),
      "#"
        .concat(("0" + n.toString(16).toUpperCase()).slice(-2))
        .concat(("0" + g.toString(16).toUpperCase()).slice(-2))
        .concat(("0" + b.toString(16).toUpperCase()).slice(-2))
    );
  }

  public static setProperty(curObj: DisplayObject, type: CmptType, key: string, val: any, isUndo?: boolean): EValue {
    let oldVal = new EValue();
    let _svgComp: DisplaySvg = null;
    if (curObj.type === CmptType.SVGSHAPE) {
      _svgComp = curObj as DisplaySvg;
    }

    let _cutShapeComp: DisplayCutShape = null;
    if (curObj.type === CmptType.CUTSHAPE) {
      _cutShapeComp = curObj as DisplayCutShape;
    }

    let _EditBoxComp: DisplayEditBox = null;
    if (curObj.type === CmptType.LABEL) {
      _EditBoxComp = curObj as DisplayEditBox;
    }

    /** 组件库 */
    if (curObj.type === CmptType.SPECIALCOMPONENT || curObj.type === CmptType.OPTIONCOMPONENT) {
      let DisplaySpecialCmpt = curObj as DisplaySpecialComponent;
      oldVal.value = DisplaySpecialCmpt[key];
      if (typeof oldVal.value == "undefined") {
        oldVal.value = DisplaySpecialCmpt.customDataPropertiesForkey(key);
      }
      DisplaySpecialCmpt.changeProperties(key, val);
    }
    switch (key) {
      // Node
      case "anchorY":
        oldVal.type = "number";
        oldVal.value = curObj.node.anchorY;
        curObj.node.anchorY = Number(val);
        break;
      case "anchorX":
        oldVal.type = "number";
        oldVal.value = curObj.node.anchorX;
        curObj.node.anchorX = Number(val);
        break;
      case "angle":
        oldVal.type = "number";
        oldVal.value = curObj.getNodeAngle(); //.node.angle;
        let angle = yy.checkValue(val);
        curObj.node['cAngle']=angle;
        curObj.node.angle = angle;
        // console.error("angle", angle)
        break;
      case "rotation":
        oldVal.type = "number";
        oldVal.value = curObj.node.rotation;
        curObj.node.rotation = Number(val);
        break;
      case "opacity":
        oldVal.type = "number";
        oldVal.value = curObj.node.opacity;
        curObj.node.opacity = Number(val);
        console.warn("opacity change==>", curObj);
        if (curObj.type === CmptType.SVGSHAPE) {
          console.warn("svgNode====>", curObj.node, val);
          const graphicNode = curObj.node.getChildByName("display_svg").getChildByName("svg_group");
          graphicNode && ComUtil.setSvgShapeOpacity(graphicNode, val / 255);
          _svgComp.shapeDatas.opacity = val;
        }
        break;
      case "scaleX":
        oldVal.type = "number";
        oldVal.value = curObj.node.scaleX;
        curObj.node.scaleX = Number(val);
        break;
      case "scaleY":
        oldVal.type = "number";
        oldVal.value = curObj.node.scaleY;
        curObj.node.scaleY = Number(val);

        break;
      case "active":
        oldVal.type = "boolean";
        oldVal.value = curObj.node.active;
        curObj.node.active = Boolean(val);
        break;
      case "width":
        oldVal.type = "number";
        oldVal.value = curObj.node.width;
        curObj.node.width = Number(val);
        if (curObj.type === CmptType.LABEL) {
          _EditBoxComp.setNodeWidth(Number(val), isUndo);
        } else if (curObj.type === CmptType.SVGSHAPE) {
          _svgComp.setNodeWidth(ValueUtils.setOneDecimal(val));
        }
        break;
      case "height":
        oldVal.type = "number";
        oldVal.value = curObj.node.height;
        curObj.node.height = Number(val);
        if (curObj.type === CmptType.LABEL) {
          _EditBoxComp.setNodeHeight(Number(val));
        } else if (curObj.type === CmptType.SVGSHAPE) {
          _svgComp.setNodeHeight(ValueUtils.setOneDecimal(val));
        }
        break;
      case "x":
        oldVal.type = "number";
        oldVal.value = curObj.node.x;
        curObj.node.x = Number(val);
        break;
      case "y":
        oldVal.type = "number";
        oldVal.value = curObj.node.y;
        curObj.node.y = Number(val);
        break;
      case "color":
        oldVal.type = "color";
        oldVal.value = "#" + curObj.node.color.toHEX();
        const clo = Properties.setDefaultColor(val);
        console.log("color:", clo);
        curObj.node.color = new cc.Color().fromHEX(clo);
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setFontColor(clo);
        }
        break;
      // Label
      case "string":
        let lbs = curObj.node.getComponent(cc.Label);
        oldVal.type = "string";
        oldVal.value = lbs.string;
        lbs.string = val;
        break;
      case "enableBold":
        oldVal.type = "number";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setBoldEnabled(Boolean(val));
        }
        break;
      case "enableItalic":
        oldVal.type = "boolean";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setItalicEnabled(Boolean(val));
        }
        break;
      case "enableUnderline":
        oldVal.type = "boolean";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setUnderLine(Boolean(val));
        }
        break;
      case "isFixed":
        oldVal.type = "boolean";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setIsFixed(Boolean(val));
        }
        break;
      case "rowSpacing":
        oldVal.type = "number";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setRowSpace(Number(val));
        }
        break;
      case "fontSize":
        oldVal.type = "number";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setFontSize(Number(val));
        }
        break;
      case "lineHeight":
        oldVal.type = "number";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setLineHeight(Number(val));
        }
        break;
      case "spacingX":
        let lbspacex = curObj.node.getComponent(cc.Label);
        oldVal.type = "number";
        oldVal.value = lbspacex.spacingX;
        lbspacex.spacingX = Number(val);
        break;
      case "horizontalAlign":
        oldVal.type = "number";
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setHorizontalAlign(Number(val));
        }
        break;
      case "verticalAlign":
        let verticalAlign = curObj.node.getComponent(cc.Label);
        oldVal.type = "number";
        oldVal.value = verticalAlign.verticalAlign;
        verticalAlign.verticalAlign = Number(val);
        break;
      case "overflow":
        let lbv = curObj.node.getComponent(cc.Label);
        oldVal.type = "number";
        oldVal.value = lbv.overflow;
        lbv.overflow = Number(val);
        break;
      case "texture":
      case "url":
        let sprite = curObj.node.getComponent(cc.Sprite);
        yy.loader.cmptAssets.get(curObj.cid) && yy.loader.cmptAssets.get(curObj.cid).forEach((path: string) => {
          if (yy.loader.assetsMap.get(path).refCount > 0) {
            yy.loader.assetsMap.get(path).decRef();
          }
        });

        yy.loader.cmptAssets.set(curObj.cid, []);
        console.log("yy.loader.cmptAssets.set==", yy.loader.cmptAssets.get(curObj.cid));
        if(!val){
          break;
        }
        yy.loader.loadRes(
          String(val),
          cc.Texture2D,
          (err, texture: cc.Texture2D) => {
            if (err) {
              yy.warn(err);
              return;
            }

            texture.packable = false;
            ComUtil.addPremultiplyAlpha(texture, sprite);
            // @ts-ignore
            sprite.___textureUrl = val;
          },
          null,
          curObj.cid,
        );
        oldVal.type = "string";
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        oldVal.value = sprite.___textureUrl;
        break;
      case "flipType":
        oldVal.type = "number";
        oldVal.value = Number(val);
        let spr = curObj.node.getComponent(cc.Sprite);
        spr && ComUtil.setMateria(spr, val);
        break;
      // spine
      case "animationList":
        let displaySpine = curObj as DisplaySpine;
        oldVal.value = displaySpine.actionList;
        displaySpine.setAnimation(val);
        break;
      case "loop":
        oldVal.value = (curObj as DisplaySpine).loop;
        (curObj as DisplaySpine).setLoop(val);
        break;
      case "timeScale":
        oldVal.value = (curObj as DisplaySpine).timeScale;
        (curObj as DisplaySpine).setTimeScale(val);
        break;
      case "textAsset":
        oldVal.type = "textAsset";
        oldVal.value = val;
        _svgComp.setPath(val);
        break;
      case "lineWidth":
        oldVal.type = "color";
        oldVal.value = val;
        if (curObj.type === CmptType.SVGSHAPE) {
          _svgComp.changeStrokeWidth(val);
        } else if (curObj.type === CmptType.CUTSHAPE) {
          oldVal.value = _cutShapeComp.setLineWidht(val);
        }
        break;
      case "strokeColor":
        oldVal.type = "color";
        oldVal.value = val;
        if (curObj.type === CmptType.SVGSHAPE) {
          _svgComp.changeStrokeColor(val);
        } else if (curObj.type === CmptType.CUTSHAPE) {
          oldVal.value = _cutShapeComp.setStrockColor(val);
        }
        break;
      case "fillColor":
        oldVal.type = "color";
        oldVal.value = val;
        if (curObj.type === CmptType.SVGSHAPE) {
          _svgComp.changeFillColor(val);
        } else if (curObj.type === CmptType.CUTSHAPE) {
          oldVal.value = _cutShapeComp.setFillColor(val);
        }
        break;
      case "dragable":
        oldVal.type = "boolean";
        oldVal.value = curObj.dragable;
        curObj.dragable = val;
        break;
      // eslint-disable-next-line no-fallthrough

      case "str":
        oldVal.type = "string";
        oldVal.value = val;
        if (curObj.type === CmptType.LABEL) {
          console.log("修改富文本1", val);
          oldVal.value = _EditBoxComp.setRichTextToEditBox(val);
        }
        break;
      case "cusorIndex":
        oldVal.type = "number";
        oldVal.value = val;
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setCursorLabelIndex(val);
        }
        break;
      case "isLabelRight":
        oldVal.type = "boolean";
        oldVal.value = val;
        if (curObj.type === CmptType.LABEL) {
          oldVal.value = _EditBoxComp.setCursorIsLabelRight(val);
        }
        break;
      case "selectArr":
        oldVal.type = "number";
        oldVal.value = val;
        if (curObj.type === CmptType.LABEL) {
          // oldVal.value = _EditBoxComp.setFillColor(val);
        }
        break;
      case "isHidePointLine":
        oldVal.type = "boolean";
        if (curObj.type === CmptType.CUTSHAPE) {
          oldVal.value = _cutShapeComp.updateLinePointActive(val);
        }
        break;
      case "displayType":
        oldVal.type = "number";
        oldVal.value = val;
        break;
      case "boardType":
        oldVal.type = "number";
        oldVal.value = val;
        break;
      case "background":
        oldVal.type = "string";
        oldVal.value = val;
        break;
      case "lineColor":
        oldVal.type = "string";
        oldVal.value = val;
        break;
      case "keyboardType":
        oldVal.type = "number";
        oldVal.value = val;
        break;
      case "offsetX":
        oldVal.type = "number";
        oldVal.value = val;
        break;
      case "offsetY":
        oldVal.type = "number";
        oldVal.value = val;
        break;
      default:
        yy.log("properties error",);
        break;
    }
    return oldVal;
  }
}
