{"ver": "1.0.27", "uuid": "89c17163-1fc5-4417-8187-b6f56a7fc83a", "importer": "effect", "compiledShaders": [{"glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec4 a_color;\nvarying vec4 v_color;\nvarying vec3 v_position;\nattribute float a_dist;\nvarying float v_dist;\nuniform bool isFlipX;\nuniform bool isFlipY;\nuniform vec2 oPosition;\nuniform float alphaU;\nvarying float v_alpha;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  if(isFlipX){\n    pos.x = oPosition.x - pos.x + oPosition.x;\n  }\n  if(isFlipY){\n    pos.y = oPosition.y - pos.y + oPosition.y;\n  }\n  v_position = a_position;\n  v_alpha = alphaU;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform float alphaThreshold;\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nvarying vec4 v_color;\nvarying float v_dist;\nvarying vec3 v_position;\nvarying float v_alpha;\nuniform sampler2D texture;\nvoid main () {\n  vec4 o = v_color;\n  ALPHA_TEST(o);\n  #if CC_SUPPORT_standard_derivatives\n  float aa = fwidth(v_dist);\n  #else\n  float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  o *= v_alpha;\n  gl_FragColor = o;\n}"}, "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec4 a_color;\nout vec4 v_color;\nout vec3 v_position;\nin float a_dist;\nout float v_dist;\nuniform FragConstants {\n  bool isFlipX;\n  bool isFlipY;\n  vec2 oPosition;\n  float alphaU;\n};\nout float v_alpha;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  if(isFlipX){\n    pos.x = oPosition.x - pos.x + oPosition.x;\n  }\n  if(isFlipY){\n    pos.y = oPosition.y - pos.y + oPosition.y;\n  }\n  v_position = a_position;\n  v_alpha = alphaU;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nvoid ALPHA_TEST (in vec4 color) {\n  #if USE_ALPHA_TEST\n      if (color.a < alphaThreshold) discard;\n  #endif\n}\nvoid ALPHA_TEST (in float alpha) {\n  #if USE_ALPHA_TEST\n      if (alpha < alphaThreshold) discard;\n  #endif\n}\nin vec4 v_color;\nin float v_dist;\nin vec3 v_position;\nin float v_alpha;\nuniform sampler2D texture;\nvoid main () {\n  vec4 o = v_color;\n  ALPHA_TEST(o);\n  #if CC_SUPPORT_standard_derivatives\n  float aa = fwidth(v_dist);\n  #else\n  float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  o *= v_alpha;\n  gl_FragColor = o;\n}"}}], "subMetas": {}}