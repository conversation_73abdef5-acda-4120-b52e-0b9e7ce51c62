/**
*  svg 图形shader
*  实现翻转
*  修正透明度
*  by dd
**/
CCEffect %{
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
          blendSrc: one
          blendDst: one_minus_src_alpha
          blendSrcAlpha: one
          blendDstAlpha: one_minus_src_alpha
      rasterizerState:
        cullMode: none
      properties:
        alphaThreshold: { value: 0.5 }
}%

CCProgram vs %{
	precision highp float;
	
	#include <cc-global>
	#include <cc-local>
	
	in vec3 a_position;
	
	in vec4 a_color;
	out vec4 v_color;
	out vec3 v_position;

	in float a_dist;
	out float v_dist;
	uniform FragConstants {
		bool isFlipX; // 是否水平翻转
		bool isFlipY; // 是否垂直翻转
		vec2 oPosition; // 初始位置
		float alphaU; // alpha 透明度设置
	};
	out float v_alpha;
		
	void main () {
		vec4 pos = vec4(a_position, 1);
		pos = cc_matViewProj * cc_matWorld * pos;
	
		v_color = a_color;
		v_dist = a_dist;
		
	    // 翻转操作
		if(isFlipX){
			pos.x = oPosition.x - pos.x + oPosition.x;
		}
		if(isFlipY){
			pos.y = oPosition.y - pos.y + oPosition.y;
		}
		v_position = a_position;
		v_alpha = alphaU;
		gl_Position = pos;
	}
}%
 
 
CCProgram fs %{
	#if CC_SUPPORT_standard_derivatives
		#extension GL_OES_standard_derivatives : enable
	#endif
	
	precision highp float;
	
	#include <alpha-test>
	
	in vec4 v_color;
	in float v_dist;
	in vec3 v_position;
	in float v_alpha;
	uniform sampler2D texture;
	void main () {
		vec4 o = v_color;
	
		ALPHA_TEST(o);
	
		#if CC_SUPPORT_standard_derivatives
		float aa = fwidth(v_dist);
		#else
		float aa = 0.05;
		#endif

		float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);
		o.rgb *= o.a;
		o *= alpha;
		o *= v_alpha;
		gl_FragColor = o;
	}
}%
