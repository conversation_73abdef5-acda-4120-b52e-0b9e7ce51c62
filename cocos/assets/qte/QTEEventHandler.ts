/**
 * 统一触摸事件派发器
 * <AUTHOR>
 * @date 2020-12-16
 */
import QTENotificationManager from "./manager/QTENotificationManager";
import { NotifyType } from "./qte-core-export";

const { ccclass } = cc._decorator;

@ccclass
export default class QTEEventHandler_V2 extends cc.Component {
    // 全局触摸事件
    private _onBegin: (touch: cc.Touch) => void;   // 拖拽开始
    private _onMove: (touch: cc.Touch) => void;    // 移动
    private _onEnd: (touch: cc.Touch) => void;     // 拖拽结束
    private _onCancel: (touch: cc.Touch) => void;  // 拖拽取消

    public onLoad () {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
    }

    // 初始化TouchHandler参数配置
    public initTouchHandler () {}

    public onTouchStart (touch: cc.Touch): void {
        this._onBegin && this._onBegin(touch);
        // 发送全局点击事件，用于处理打断动画、引导等操作
        qte.instance(QTENotificationManager).sendNotification(NotifyType.COMMON_TOUCH_EVENT);
    }

    public onTouchMove (touch: cc.Touch): void {
        this._onMove && this._onMove(touch);
    }

    public onTouchCancel (touch: cc.Touch): void {
        this._onCancel && this._onCancel(touch);
    }

    public onTouchEnd (touch: cc.Touch): void {
        this._onEnd && this._onEnd(touch);
    }

    public registerGlobalEvent (onBegin: (touch: cc.Touch) => void, onMove: (touch: cc.Touch) => void, onEnd: (touch: cc.Touch) => void, onCancel: (touch: cc.Touch) => void): void {
        this._onBegin = onBegin;
        this._onMove = onMove;
        this._onEnd = onEnd;
        this._onCancel = onCancel;
    }

    public onDestroy () {}
}