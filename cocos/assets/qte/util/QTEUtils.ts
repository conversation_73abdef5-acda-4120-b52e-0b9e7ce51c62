import { addListenQuestionSorce } from '../assemble/listenRetell/scripts/ListenQuestionAddSorce';
import { SingleBase } from '../core/base/SingleBase';
import ResLoader from '../core/loader/ResLoader';
import QTEAssetsManager from '../manager/QTEAssetsManager';
import QTETemplateManager from '../manager/QTETemplateManager';
import { QTE_LOGCAT_TYPE } from '../qte-core-export';
import QTEFaceda from '../QTEFaceda';
import QTEAnswerResultUI from '../ui/QTEAnswerResultUI';
import QTEQuestionNumberUI from '../ui/QTEQuestionNumberUI';

export default class QTEUtils {
    private static _blockStatus: boolean = false;
    public static get blockStatus(): boolean {
        return QTEUtils._blockStatus;
    }

    public static getPromise<T>(
        executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void,
    ): Promise<T> {
        return new Promise(executor);
    }

    public static getQTECanvas(): cc.Canvas {
        let canvasNode = cc.director.getScene().getChildByName('Canvas');
        if (!canvasNode) {
            throw new Error('场景上不存在 Canvas 节点！');
        }
        let canvas = canvasNode.getComponent(cc.Canvas);
        if (!canvas) {
            throw new Error('Canvas节点上不存在 cc.Canvas 组件！');
        }
        return canvas;
    }

    public static getQTERoot(): cc.Node {
        let canvas = this.getQTECanvas();
        let rootNode = canvas.node.getChildByName('root');
        if (!rootNode) {
            throw new Error('Canvas节点上不存在 root 节点！');
        }
        return rootNode;
    }

    public static getQTECamera(): cc.Camera {
        let canvas = this.getQTECanvas();
        let cameraNode = canvas.node.getChildByName('Main Camera');
        if (!cameraNode) {
            throw new Error('Canvas节点上不存在 Main Camera 节点！');
        }
        let camera = cameraNode.getComponent(cc.Camera);
        if (!camera) {
            throw new Error('Main Camera节点上不存在 cc.Camera 组件！');
        }
        return camera;
    }

    public static getQTEScreenshootCamera(): cc.Camera {
        let canvas = this.getQTECanvas();
        let cameraNode = canvas.node.getChildByName('qteShootCamera');
        if (!cameraNode) {
            cameraNode = new cc.Node();
            cameraNode.name = 'qteShootCamera';
            canvas.node.addChild(cameraNode);
        }
        let camera = cameraNode.getComponent(cc.Camera);
        if (!camera) {
            camera = cameraNode.addComponent(cc.Camera);
            camera.depth = -1;
        }
        return camera;
    }

    public static isInApp() {
        return (
            /siwei/i.test(navigator.userAgent) ||
            /yuwen/i.test(navigator.userAgent) ||
            /homework/i.test(navigator.userAgent) ||
            /airclass/i.test(navigator.userAgent) ||
            /kid/i.test(navigator.userAgent)
        );
    }

    public static isIOS() {
        return /(iPhone|iPad|iPod|iOS)/i.test(navigator.userAgent);
    }

    public static isIpad() {
        return /(iPad)/i.test(navigator.userAgent);
    }

    public static bolckInput() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_BLOCK, '========== qte global bolckInput ===========');
        let canvas = cc.director.getScene().getChildByName('Canvas');
        let block = canvas.getChildByName('g-BolckInput');
        if (!block) {
            block = new cc.Node('g-BolckInput');
            block.width = cc.winSize.width;
            block.height = cc.winSize.height;
            block.addComponent(cc.BlockInputEvents);
            block.zIndex = cc.macro.MAX_ZINDEX;
            block.parent = canvas;
            block.on(cc.Node.EventType.TOUCH_START, () => {
                qte.log('touch the bolck');
            });
        }
        block.active = true;
        QTEUtils._blockStatus = true;

        // 锁屏60s超时
        cc.tween(block)
            .delay(60)
            .call(() => {
                QTEUtils.unbolckInput();
            })
            .start();
    }

    static unbolckInput() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_BLOCK, '========== qte global unbolckInput ===========');
        let canvas = cc.director.getScene().getChildByName('Canvas');
        let block = canvas.getChildByName('g-BolckInput');
        if (block) {
            cc.Tween.stopAllByTarget(block);
            block.active = false;
            QTEUtils._blockStatus = false;
        }
    }

    static convertPosition(
        p: { endX: string; endY: string },
        stage: { width: number; height: number },
        box: { width: number; height: number },
    ) {
        return cc.v2(
            parseFloat(p.endX) - stage.width / 2 + box.width / 2,
            -parseFloat(p.endY) + stage.height / 2 - box.height / 2,
        );
    }

    /** 截图功能 */
    public static screenshoot(): any {
        let camera = QTEUtils.getQTEScreenshootCamera();
        // 新建一个 RenderTexture，并且设置 camera 的 targetTexture 为新建的 RenderTexture，这样 camera 的内容将会渲染到新建的 RenderTexture 中。
        let texture = new cc.RenderTexture();
        let gl = (cc.game as any)._renderContext;
        // 如果截图内容中不包含 Mask 组件，可以不用传递第三个参数
        texture.initWithSize(cc.visibleRect.width, cc.visibleRect.height, gl.STENCIL_INDEX8);
        camera.targetTexture = texture;
        // 渲染一次摄像机，即更新一次内容到 RenderTexture 中
        camera.render();
        // readPixels()
        return texture;
    }

    public static captureNode(nodeTarget: any): cc.RenderTexture {
        let nodeP = nodeTarget.node;
        let cameraNode = new cc.Node();
        cameraNode.parent = nodeP;
        cameraNode.setPosition(0, 0);
        let camera = nodeTarget.addComponent(cc.Camera);
        let width = Math.ceil(nodeP.getBoundingBoxToWorld().width);
        let height = Math.ceil(nodeP.getBoundingBoxToWorld().height);

        let texture = new cc.RenderTexture();
        let gl = (cc.game as any)._renderContext;
        // 如果截图内容中不包含 Mask 组件，可以不用传递第三个参数
        texture.initWithSize(width, height, gl.STENCIL_INDEX8);
        camera.alignWithScreen = false;
        camera.clearFlags = 0;
        camera.ortho = true;
        camera.orthoSize = height / 2;
        camera.targetTexture = texture;

        width = texture.width;
        height = texture.height;
        camera.render(nodeP);
        camera.scheduleOnce(() => {
            cameraNode.removeFromParent();
        }, 1);
        return texture;
    }

    public static getCurrentData(uuid: string) {
        let templateManager = QTEUtils.instance(QTETemplateManager, uuid);
        return templateManager.data;
    }

    public static getBundleName(uuid: string) {
        let bundleName;
        let bundleUrl;

        bundleName = QTEUtils.getCurrentData(uuid).template.bundleName;
        bundleUrl = QTEUtils.getCurrentData(uuid).template.bundleUrl;
        // 兼容老数据，没有bundleName时，读取template中的bundleUrl来解析bundleName
        if (!bundleName) {
            let tempName = bundleUrl.match(/[v|n]\/[^\n\r]*.zip/)[0];
            bundleName = tempName.replace('.zip', '').replace('v/', '').replace('n/', '');
        }
        return bundleName;
    }

    public static getResourceList(uuid: string): string[] {
        return QTEUtils.getCurrentData(uuid).resourceList;
    }
    public static getComponents(uuid: string): { id: string, subType: string, properties: any }[] {
        return QTEUtils.getCurrentData(uuid).components;
    }

    public static instance<T extends SingleBase>(type: new () => T, uuid: string): T {
        return qte.instance(type, uuid);
    }

    public static desctroyByUUID(uuid: string) {
        qte.destoryBySymbol(uuid);
    }

    public static checkValue<T>(val: T, d?: T): T {
        if (typeof val === 'string') {
            if (val === '' && d) {
                return d;
            }
            return val;
        } else if (typeof val === 'number' && !isNaN(val) && isFinite(val)) {
            return val;
        } else if (typeof val === 'boolean') {
            return val;
        }

        if (!val) {
            return d;
        }
        return val;
    }

    public static showToast(str: string) {
        if (QTEFaceda.adapter && QTEFaceda.adapter.toast) {
            QTEFaceda.adapter.toast(str);
            return;
        }
        ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
            if (!err) {
                const toastPath = 'res/prefab/toast';
                qte.instance(QTEAssetsManager).loadRes(
                    toastPath,
                    cc.Prefab,
                    (err, prefab) => {
                        let toastNode: cc.Node = cc.instantiate(prefab);
                        toastNode.getChildByName('lab').getComponent(cc.Label).string = str;
                        toastNode.parent = cc.find('Canvas');
                        cc.tween(toastNode)
                            .delay(3)
                            .call(() => {
                                toastNode.removeFromParent();
                                toastNode.destroy();
                            })
                            .start();
                    },
                    bundle,
                );
            }
        });
    }

    /**
     * 将字节数组进行base64编码
     * @param bytes
     * @returns
     */
    public static base64encode(bytes) {
        let base64 = '';
        let encodings = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
        let byteLength = bytes.byteLength;
        let byteRemainder = byteLength % 3;
        let mainLength = byteLength - byteRemainder;
        let a, b, c, d;
        let chunk;
        for (let i = 0; i < mainLength; i += 3) {
            chunk = (bytes[i] << 16) | (bytes[i + 1] << 8) | bytes[i + 2];
            // Use bitmasks to extract 6-bit segments from the triplet
            a = (chunk & 16515072) >> 18; // 16515072 = (2^6 - 1) << 18
            b = (chunk & 258048) >> 12; // 258048 = (2^6 - 1) << 12
            c = (chunk & 4032) >> 6; // 4032 = (2^6 - 1) << 6
            d = chunk & 63; // 63 = 2^6 - 1
            // Convert the raw binary segments to the appropriate ASCII encoding
            base64 += encodings[a] + encodings[b] + encodings[c] + encodings[d];
        }
        // Deal with the remaining bytes and padding
        if (byteRemainder == 1) {
            chunk = bytes[mainLength];
            a = (chunk & 252) >> 2; // 252 = (2^6 - 1) << 2;
            // Set the 4 least significant bits to zero
            b = (chunk & 3) << 4; // 3 = 2^2 - 1;
            base64 += encodings[a] + encodings[b] + '==';
        } else if (byteRemainder == 2) {
            chunk = (bytes[mainLength] << 8) | bytes[mainLength + 1];
            a = (chunk & 16128) >> 8; // 16128 = (2^6 - 1) << 8;
            b = (chunk & 1008) >> 4; // 1008 = (2^6 - 1) << 4;
            // Set the 2 least significant bits to zero
            c = (chunk & 15) << 2; // 15 = 2^4 - 1;
            base64 += encodings[a] + encodings[b] + encodings[c] + '=';
        }
        // return "data:image/jpeg;base64," + base64;
        return base64;
    }

    public static playSpineArr(spine: sp.Skeleton, array: string[], loop: boolean, index: number = 0) {
        spine.setCompleteListener((trackEntry, loopCount) => {
            if (spine['index'] === array.length - 1 && loop) {
                spine['index'] = 0;
            } else {
                spine['index']++;
            }
            spine['set-ani-list'](array, loop, spine['index'], spine);
            if (spine['index'] >= 0 && spine['index'] < array.length) {
                spine.setAnimation(0, array[spine['index']], false);
            } else {
                spine.animation = null;
                spine.setCompleteListener(null);
                spine['set-ani-list']([], false, 0, spine);
            }
        });
        spine['index'] = index;
        spine.animation = array[index];
        spine.loop = false;
        spine['set-ani-list'](array, loop, spine['index'], spine);
    }

    /**
     * 选择框
     * @param str
     * @param okcall
     * @param failcall
     * @returns
     */
    public static showNoticeChoice(str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) {
        if (QTEFaceda.adapter && QTEFaceda.adapter.showNoticeChoice) {
            QTEFaceda.adapter.showNoticeChoice(str, okcall, failcall, isChangeFocus);
            return;
        }
        //已经展示了选择框
        if (cc.find('Canvas').getChildByName('noticeChoice')) {
            return;
        }
        //预览端模拟
        ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
            if (!err) {
                const toastPath = 'res/prefab/noticeChoice';
                qte.instance(QTEAssetsManager).loadRes(
                    toastPath,
                    cc.Prefab,
                    (err, prefab) => {
                        let toastNode: cc.Node = cc.instantiate(prefab);
                        toastNode.name = 'noticeChoice';
                        toastNode.parent = cc.find('Canvas');
                        toastNode.getChildByName('label').getComponent(cc.Label).string = str;
                        toastNode.getChildByName('ok').on(
                            cc.Node.EventType.TOUCH_START,
                            () => {
                                okcall();
                                toastNode.removeFromParent();
                                toastNode.destroy();
                            },
                            this,
                        );
                        toastNode.getChildByName('fail').on(
                            cc.Node.EventType.TOUCH_START,
                            () => {
                                failcall();
                                toastNode.removeFromParent();
                                toastNode.destroy();
                            },
                            this,
                        );
                    },
                    bundle,
                );
            }
        });
    }

    /**
     * 关闭选择框
     */
    public static hideNoticeChoice() {
        if (QTEFaceda.adapter && QTEFaceda.adapter.hideNoticeChoice) {
            QTEFaceda.adapter.hideNoticeChoice();
            return;
        }
        const node = cc.find('Canvas')?.getChildByName('noticeChoice');
        if (node) {
            node.removeFromParent();
            node.destroy();
        }
    }
    public static getAngelEulerAngles(node: cc.Node): number {
        let angel = 0;
        let eulerAngles = node.eulerAngles;
        if (eulerAngles.x == 0) {
            angel = eulerAngles.z;
        }
        else if (eulerAngles.x < 0) {
            angel = -(eulerAngles.x + eulerAngles.z);

        } else if (eulerAngles.x > 0) {
            angel = eulerAngles.x - eulerAngles.z;
        }
        return angel;
    }

    public static culOffestStageCent(node: cc.Node, _offsetList: any[]): any[] {
        if (node.parent && node.name != 'root' && node.name != 'object_layer') {
            let pos = node.getPosition();
            let scaleX = node.parent.scaleX || 1;
            let scaleY = node.parent.scaleY || 1;
            let preData = { pos: cc.v2(pos.x, pos.y), scaleX, scaleY, angle: this.getAngelEulerAngles(node.parent) };
            _offsetList.unshift(preData);
            if (node.parent.name != 'root' && node.name != 'object_layer' && node.parent.name != 'QTETemplate') {
                return this.culOffestStageCent(node.parent, _offsetList);
            }
        }
        return _offsetList;
    }

    public static centStageBase(node: cc.Node): any {
        let _offsetList = this.culOffestStageCent(node, []);
        //console.error("_offsetList:", JSON.stringify(_offsetList))
        let _offsetV2 = cc.v2(0, 0);
        let angel = 0;
        let offScaleXLen = 1;
        let offScaleYLen = 1;
        for (let i = 0; i < _offsetList.length; i++) {
            offScaleXLen *= _offsetList[i].scaleX;
            offScaleYLen *= _offsetList[i].scaleY;
            let _offsetParent = cc.v2(_offsetList[i].pos.x * offScaleXLen, _offsetList[i].pos.y * offScaleYLen);
            angel += _offsetList[i].angle;
            _offsetParent = _offsetParent.rotateSelf((angel / 180) * Math.PI);
            _offsetV2 = _offsetV2.add(_offsetParent);
        }
        return { angel, offScaleXLen, offScaleYLen, _offsetV2 };
    }

    public static convertStageToNodeSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2 {
        let { angel, offScaleXLen, offScaleYLen, _offsetV2 } = this.centStageBase(node);
        let _offset = cc.v2(0 * offScaleXLen, 0 * offScaleYLen);
        _offset = _offset.rotateSelf((angel / 180) * Math.PI);
        _offsetV2.add(_offset);
        angel += this.getAngelEulerAngles(node);
        offScaleXLen *= node.scaleX;
        offScaleYLen *= node.scaleY;
        var p = pos;
        let touchPos = cc.v2(p.x - _offsetV2.x, p.y - _offsetV2.y);
        touchPos = touchPos.rotateSelf((-angel / 180) * Math.PI);
        touchPos.x = touchPos.x / offScaleXLen;
        touchPos.y = touchPos.y / offScaleYLen;
        return touchPos;
    }


    public static convertToStageSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2 {
        let { angel, offScaleXLen, offScaleYLen, _offsetV2 } = this.centStageBase(node);
        offScaleXLen *= node.scaleX;
        offScaleYLen *= node.scaleY;
        let _offsetParent = cc.v2(pos.x * offScaleXLen, pos.y * offScaleYLen);
        angel += this.getAngelEulerAngles(node);
        _offsetParent = _offsetParent.rotateSelf((angel / 180) * Math.PI);
        return _offsetV2.add(_offsetParent);
    }

    /**
     * 加载预制答案框
     *
     * @return {*}
     * @memberof QTEUtils
     */
    public static async loadAnswerBorder(): Promise<cc.Prefab> {
        return new Promise<cc.Prefab>((resolve, reject) => {
            ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        'res/prefab/answerBorder',
                        cc.Prefab,
                        (errRes, pb) => {
                            if (!errRes) {
                                resolve(pb);
                            } else {
                                qte.logCatBoth(
                                    QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                                    `QTEUtils loadAnswerBorder error ${errRes}`,
                                );
                                reject(errRes);
                            }
                        },
                        bundle,
                    );
                } else {
                    qte.logCatBoth(
                        QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                        `QTEUtils loadAnswerBorder load [qte bundle] error ${err}`,
                    );
                    reject(err);
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    /**
     * 妙笔生花action接口
     * @param param backgroundSoundPath:背景音乐路径，type:类型，1: 录音开始 2：停止录音 3：开始播放4：暂停播放5：重新录制6：点击提交 
     *  callBack:回调
     * @returns 
     */
    public static flowerAudioRecord(param: { backgroundSoundPath?: string, type: number, timeMax: number, callBack?: Function }) {
        console.log(" QTEUtils flowerAudioRecord");
        if (QTEFaceda.adapter && QTEFaceda.adapter.flowerAudioRecord) {
            QTEFaceda.adapter.flowerAudioRecord(param);
            return;
        }
        else {
            param.callBack && param.callBack({ isPreview: true, code: 202 });
        }
        console.log("not have QTEFaceda.adapter.flowerAudioRecord");
    }
    //添加题号组件
    public static async loadQuestionNumberUI(num_str: string): Promise<QTEQuestionNumberUI> {
        return new Promise<QTEQuestionNumberUI>((resolve, reject) => {
            ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        'res/prefab/questionNumberUI',
                        cc.Prefab,
                        (errRes, pb) => {
                            if (!errRes) {
                                let gNode = cc.instantiate(pb);
                                let qTEQuestionNumberUI = gNode.getComponent(QTEQuestionNumberUI);
                                qTEQuestionNumberUI.setQuestionNumber(num_str);
                                resolve(qTEQuestionNumberUI);
                            } else {
                                qte.logCatBoth(
                                    QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                                    `QTEUtils loadQuestionNumberUI error ${errRes}`,
                                );
                                reject(errRes);
                            }
                        },
                        bundle,
                    );
                } else {
                    qte.logCatBoth(
                        QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                        `QTEUtils loadQuestionNumberUI load [qte bundle] error ${err}`,
                    );
                    reject(err);
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    /** 增加语音题分数  */
    public static addListenQuestionSorce(num: number): number {
        return addListenQuestionSorce(num);
    }

    static _answerResultUICb: Function = null;
    static _answerResultUiTimer: number = -1;
    /**
     * @description: 增加作答结果 UI
     * @param {Object: {}} objResult  .arrResult作答数据数组类型  .dt回调时间<可选>  .cb回调函数<可选>
     * @return {*}
     */
    public static async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node> {
        if (this._answerResultUiTimer != -1) {
            qte.TimerUtils.clearTimer(this._answerResultUiTimer);
            this._answerResultUiTimer = -1;
        }
        return new Promise<cc.Node>((resolve, reject) => {
            ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        'res/prefab/answerResultUI',
                        cc.Prefab,
                        (errRes, pb) => {
                            if (!errRes) {
                                let ndNode: cc.Node = cc.instantiate(pb);
                                if (objResult && objResult.arrResult) {
                                    ndNode.getComponent(QTEAnswerResultUI).show(objResult.arrResult);
                                }

                                resolve(ndNode);
                                this._answerResultUICb = () => {
                                    objResult.cb && objResult.cb();
                                    this._answerResultUICb = null;
                                };

                                if (objResult && objResult.delayTime && objResult.cb) {
                                    if (this._answerResultUiTimer != -1) {
                                        qte.TimerUtils.clearTimer(this._answerResultUiTimer);
                                        this._answerResultUiTimer = -1;
                                    }
                                    this._answerResultUiTimer = qte.TimerUtils.addTimer(() => {
                                        this._answerResultUiTimer = -1;
                                        this._answerResultUICb()
                                    }, objResult.delayTime);
                                } else {
                                    this._answerResultUICb();
                                }
                            } else {
                                qte.logCatBoth(
                                    QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                                    ` addAnswerResultUI error ${errRes}`,
                                );
                                reject(errRes);
                            }
                        },
                        bundle,
                    );
                } else {
                    qte.logCatBoth(
                        QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                        ` addAnswerResultUI load [qte bundle] error ${err}`,
                    );
                    reject(err);
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });;
    }
    /**
     * 安全等待异步操作完成
     * @param promise 需要等待的Promise对象
     * @param target 目标对象(通常是组件或节点)
     * @returns Promise结果,如果目标对象已销毁则返回null
     */
    public static async safeAwait<T>(promise: Promise<T>, target: any): Promise<T> {
        const result = await promise;
        if (!cc.isValid(target, true)) {
            qte.warn('Target object was destroyed before async operation completed.');
            throw new Error('Target object was destroyed before async operation completed.');
        }
        return result;
    }


    // 深度克隆对象
    public static deepClone(obj: any, hash = new Map()): any {
        if (obj == null) return null;
        if (typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj);
        if (obj instanceof RegExp) return new RegExp(obj);
        
        // 检查是否已经克隆过该对象，避免循环引用
        if (hash.has(obj)) {
            return hash.get(obj);
        }
        
        let newObj: any = new obj.constructor();
        // 将新对象存入 hash 表，以便处理循环引用
        hash.set(obj, newObj);
        
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                newObj[key] = this.deepClone(obj[key], hash);
            }
        }
        return newObj;
    }



}

window['_shark_cocos_bridge'] || (window['_shark_cocos_bridge'] = {});
window['_shark_cocos_bridge'].screenshoot = QTEUtils.screenshoot;
