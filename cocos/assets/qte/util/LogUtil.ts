/*
 * @Author: your name
 * @Date: 2021-08-19 16:29:47
 * @LastEditTime : 2023-05-12 19:33:13
 * @LastEditors  : Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath     : /sdk/assets/qte/util/LogUtil.ts
 */
/* eslint-disable no-console */

import QTEFaceda from "../QTEFaceda";
import { QTE_LOGCAT_TYPE } from "../vo/QTEEnum";

/**
 * LogUtil:日志相关接口
 * <AUTHOR>
 * @description 日志分级、日志打印、日志保存
 */
export default class LogUtil {
    /** Log标签 */
    public static GLOBAL_TAG: string = "QTE-SDK";
    /** Log 日志信息 */
    private static _logStr: string = "";
    /** Log状态 */
    private static _logStatus: boolean = true;
    private static _instance = null;

    constructor() { }

    initLog() {
        if (!LogUtil._logStatus) {
            return;
        }
        this.log = console.log.bind(this, "[log][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG);
        this.debug = console.debug.bind(this, "[debug][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG);
        this.warn = console.warn.bind(this, "[warn][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG);
        this.error = console.error.bind(this, "[error][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG);
    }

    static getInstance() {
        if (this._instance === null) {
            this._instance = new LogUtil();
        } else {
            return this._instance;
        }
        this._instance.initLog();
        return this._instance;
    }

    log(...data: any[]): void { }

    debug(...data: any[]): void { }

    warn(...data: any[]): void { };

    error(...data: any[]): void { };

    /* public static log(message?: any, ...optionalParams: any[]): void {
        if (!LogUtil._logStatus) {
            return;
        }
        console.log("[log][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG, ":", message, ...optionalParams);
    }

    public static debug(message?: any, ...optionalParams: any[]): void {
        if (!LogUtil._logStatus) {
            return;
        }
        console.debug("[debug][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG, ":", message, ...optionalParams);
    }

    public static warn(message?: any, ...optionalParams: any[]): void {
        if (!LogUtil._logStatus) {
            return;
        }
        console.warn("[warn][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG, ":", message, ...optionalParams);
    }

    public static error(message?: any, ...optionalParams: any[]): void {
        if (!LogUtil._logStatus) {
            return;
        }
        console.error("[error][", LogUtil.getFullTime(), "]", LogUtil.GLOBAL_TAG, ":", message, ...optionalParams);
    } */

    /** 写入文件保存 */
    public static save(cb?: () => void): void {
        if (cc.sys.isNative) {
            // TODO 写入文件保存
            if (cb) {
                cb();
            }
        }
    }

    /** 获取完全时间 */
    public static getFullTime(): string {
        let date = new Date();
        return `${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}-${date.getMilliseconds()}`;
    }

    /** 获取文件名 */
    public static getFileName(): string {
        let date = new Date();
        return `${date.getMonth()}-${date.getDay()}_${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}.log`;
    }

    /** 设置日志状态 */
    public static setLogStatus(flag: boolean): void {
        LogUtil._logStatus = flag;
        if (flag) {
            /*
             * 定时保存日志
             * setInterval(() => {
             *     LogUtil.save();
             * }, 1000 * 15);
             */
        }
    }

    /**
     * 输出调用堆栈
     */
    public static logTrace(message) {
        console.trace(message);
    }

    /**
     * @msg     : 输出 log 到 apm
     * @param    {*} type
     * @param    {*} args
     * @return   {*}
     */
    public static logCatToAPM(type: QTE_LOGCAT_TYPE, args) {
        LogUtil.logCat(1, type, args);
    }

    /**
     * @msg     : 输出 log 到 端本地 logcat
     * @param    {*} type
     * @param    {*} args
     * @return   {*}
     */
    public static logCatToNative(type: QTE_LOGCAT_TYPE, args) {
        LogUtil.logCat(0, type, args);
    }

    /**
     * @msg     : 输出 log 到 2 个渠道
     * @param    {*} type
     * @param    {*} args
     * @return   {*}
     */
    public static logCatBoth(type, args) {
        LogUtil.logCat(2, type, args);
    }

    public static logCat(logType, type, args): void {
        if (QTEFaceda.adapter && QTEFaceda.adapter.logCat) {
            QTEFaceda.adapter.logCat(logType, type, args);
        } else {
            qte.log("logCatToNative=", args);
        }
    }
    /**
     * 打点到蓝鲸
     * @param type 名字
     * @param args 参数
     */
    public static tracePm(type, args): void {
        if (QTEFaceda.adapter && QTEFaceda.adapter.tracePm) {
            QTEFaceda.adapter.tracePm(type, args);
        } else {
            qte.log("tracePm=", args);
        }
    }
}

// 将接口导出
// eslint-disable-next-line no-unused-expressions
(window as any).qte || ((window as any).qte = {});
(window as any).qte.log = LogUtil.getInstance().log;
(window as any).qte.debug = LogUtil.getInstance().debug;
(window as any).qte.warn = LogUtil.getInstance().warn;
(window as any).qte.error = LogUtil.getInstance().error;
(window as any).qte.saveLog = LogUtil.save;
(window as any).qte.setLogStatus = LogUtil.setLogStatus;
(window as any).qte.logTrace = LogUtil.logTrace;
(window as any).qte.logCatToNative = LogUtil.logCatToNative;
(window as any).qte.logCatToAMP = LogUtil.logCatToAPM;
(window as any).qte.logCatBoth = LogUtil.logCatBoth;
(window as any).qte.tracePm = LogUtil.tracePm;


