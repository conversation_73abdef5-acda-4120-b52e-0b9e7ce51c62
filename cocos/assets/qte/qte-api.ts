/*
 * @FilePath     : /sdk/assets/qte/qte-api.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
/*
 * @Author: your name
 * @Date: 2021-09-02 11:31:41
 * @LastEditTime: 2021-09-02 16:16:43
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /cocos-framework/assets/qte/qte-api.ts
 */
import BaseComponent from "./assemble/core/BaseComponent";
import QTEBaseMeta from "./base/QTEBaseMeta";
import QTEBaseState from "./base/QTEBaseState";
import QTEComponent from "./base/QTEComponent";
import QTEEntityNode from "./base/QTEEntityNode";
import QTETemplate from "./base/QTETemplate";
import ClickCmpt from "./components/ClickCmpt";
import QTEFaceda, { QTE_OPENMODE } from "./QTEFaceda";
import TimerUtils from "./core/utils/TimerUtil";
import QTESystem from "./QTESystem";
import QTEUtils from "./util/QTEUtils";
import { AutoSubmitMode, QTE_STATE, RecordState } from "./vo/QTEEnum";
import QTETemplateData from "./vo/QTETemplateData";
import QTEQuestionNumberUI from "./ui/QTEQuestionNumberUI";
import DrawControl from "./assemble/drawControl/DrawControl";
import { ExecutionType } from "./core/timer/TimerManager";



// eslint-disable-next-line no-unused-expressions
(window as any).qte || ((window as any).qte = {});

// adapter 相关接口导出
// QTEAdapter = module.exports = QTEAdapter;
// (window as any).qte.QTEAdapter = QTEAdapter;
// (window as any).qte.registerAdapter = QTEAdapterManager.registerAdapter;

// API
/** 框架初始化接口,需要先执行此接口才可以执行qte.create创建题版 */
(window as any).qte.initialize = QTEFaceda.initialize;
/** 创建题版接口 */
(window as any).qte.create = QTEFaceda.create;
/** qte框架完全销毁接口; */
(window as any).qte.destroy = QTEFaceda.destroy;
/** 获取当前题版的显示数据,用来发送题目给另一个端恢复该题目; */
(window as any).qte.getStates = QTEFaceda.getStates;
/** 状态恢复 */
(window as any).qte.recover = QTEFaceda.recover;
/** 页面回到前台展示 */
(window as any).qte.page_show = QTEFaceda.page_show;
/** 页面退到后台 */
(window as any).qte.page_hide = QTEFaceda.page_hide;
/** 页面最小化 */
(window as any).qte.fold = QTEFaceda.fold;
/** 页面最大化 */
(window as any).qte.unfold = QTEFaceda.unfold;
/** 单独添加全局组件 */
(window as any).qte.fWAddComponent = QTEFaceda.fWAddComponent;
/** 停用全部全局组件 */
(window as any).qte.stopfWComponents = QTEFaceda.stopfWComponents;
/** 停止播放全部全局组件 */
(window as any).qte.stopVoicefWComponents = QTEFaceda.stopVoicefWComponents;
/** 获取全部全局组件播放时长 */
(window as any).qte.getfWComponentsTemData = QTEFaceda.getfWComponentsTemData;
/** 单独播放全局组件 */
(window as any).qte.playfWComponents = QTEFaceda.playfWComponents;
/** 重制播放全局组件 */
(window as any).qte.resetPlayfWComponents = QTEFaceda.resetPlayfWComponents;
/** 打开解析 */
(window as any).qte.createAnalysis = QTEFaceda.createAnalysis;
/** 删除解析 */
(window as any).qte.deletAnalysis = QTEFaceda.deletAnalysis;
// 添加题干内容
(window as any).qte.createQuestionContent = QTEFaceda.createQuestionContent;

(window as any).qte.QTE_OPENMODE = QTE_OPENMODE;
/** 重制播放全局组件 */
(window as any).qte.QTETemplateData = QTETemplateData;
(window as any).qte.QTETemplate = QTETemplate;
(window as any).qte.QTEComponent = QTEComponent;
(window as any).qte.BaseComponent = BaseComponent;
(window as any).qte.QTEBaseMeta = QTEBaseMeta;
(window as any).qte.QTESystem = QTESystem;
(window as any).qte.QTEEntityNode = QTEEntityNode;
(window as any).qte.QTEBaseState = QTEBaseState;
(window as any).qte.QTE_STATE = QTE_STATE;
(window as any).qte.ClickCmpt = ClickCmpt;
(window as any).qte.QTEUtils = QTEUtils;
(window as any).qte.QTEQuestionNumberUI = QTEQuestionNumberUI;
(window as any).qte.TimerUtils = TimerUtils;
(window as any).qte.ExecutionType = ExecutionType;
(window as any).qte.AutoSubmitMode = AutoSubmitMode;
(window as any).qte.RecordState = RecordState;
(window as any).qte.DrawControl = DrawControl;

