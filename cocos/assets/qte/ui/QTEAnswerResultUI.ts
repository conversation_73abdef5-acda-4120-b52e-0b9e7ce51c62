/*
 * @Author: your name
 * @Date: 2023-04-23 15:07:27
 * @LastEditTime: 2023-04-27 16:08:28
 * @LastEditors: your name
 * @Description: 通用作答结果弹框
 * @FilePath: /qte_sdk/sdk/assets/qte/ui/QTEAnswerResultUI.ts
 */

const { ccclass, property } = cc._decorator;

@ccclass
export default class QTEAnswerResultUI extends cc.Component {
    @property(cc.Node) private ndBgRight: cc.Node = null;
    @property(cc.Node) private ndBgWrong: cc.Node = null;
    @property(cc.SpriteFrame) private large_bg_R: cc.SpriteFrame = null;
    @property(cc.SpriteFrame) private large_bg_W: cc.SpriteFrame = null;
    @property(cc.Node) private nd15: cc.Node = null;
    @property(cc.Node) private nd610: cc.Node = null;
    @property(cc.Node) private large_nd15: cc.Node = null;
    @property(cc.Node) private large_nd610: cc.Node = null;
    @property(cc.Node) private large_nd1015: cc.Node = null;

    /**
     * @description: 用作答数据初始化 UI 并显示
     * @param {Array<boolean>} data 作答结果数组
     * @return {*}
     */
    show(data: Array<boolean>) {
        let l = data.length;
        if (l < 0) return;
        //全对的情况
        let bAllRight = true;
        for (let i = 0; i < l; i++) {
            if (!data[i]) {
                bAllRight = false;
                break;
            }
        }

        if (bAllRight) {
            this.ndBgRight.active = true;
            this.ndBgWrong.active = false;
        } else {
            this.ndBgRight.active = false;
            this.ndBgWrong.active = true;
        }
        if(l<=10)
        {
            this.nd15.active = true;
            l > 5 && (this.nd610.active = true);
            for (let i = 0; i < l; i++) {
                const bRight = data[i];
                let item = null;
                if (i < 5) {
                    item = this.nd15.children[i];
                } else {
                    item = this.nd610.children[i - 5];
                }
                item && (item.active = true);
                item && (item.getChildByName('bgRight').active = bAllRight);
                item && (item.getChildByName('bgWrong').active = !bAllRight);
                item && (item.getChildByName('flagRight').active = bRight);
                item && (item.getChildByName('flagWrong').active = !bRight);
            }
        }
        else
        {
            this.ndBgWrong.getComponent(cc.Sprite).spriteFrame = this.large_bg_W;
            this.ndBgRight.getComponent(cc.Sprite).spriteFrame = this.large_bg_R;
            this.large_nd15.active = true;
            this.large_nd610.active = true;
            this.large_nd1015.active = true;
            for (let i = 0; i < l; i++) {
              const bRight = data[i];
              let item = null;
              if (i < 5) {
                  item = this.large_nd15.children[i];
              } else if(i>=5 && i < 10) {
                  item = this.large_nd610.children[i - 5];
              }
              else{
                  item = this.large_nd1015.children[i - 10];
              }
              item && (item.active = true);
              item && (item.getChildByName('bgRight').active = bAllRight);
              item && (item.getChildByName('bgWrong').active = !bAllRight);
              item && (item.getChildByName('flagRight').active = bRight);
              item && (item.getChildByName('flagWrong').active = !bRight);
          }
        }
    }
}
