// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const {ccclass, property} = cc._decorator;

@ccclass
export default class QTEQuestionNumberUI extends cc.Component {

    @property(cc.Label)
    questionNumber: cc.Label = null;

    public setQuestionNumber(num_str: string) {
        console.log("QTEQuestionNumberUI",num_str)
        this.questionNumber.string = num_str;
    }

    // update (dt) {}
}
