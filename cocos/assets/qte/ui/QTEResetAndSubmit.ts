/*
 * @FilePath     : /assets/qte/ui/QTEResetAndSubmit.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import QTEComponent from "../base/QTEComponent";
import QTETemplateManager from "../manager/QTETemplateManager";
import QTEFaceda, { QTEParams, QTE_RESET_TYPE, QTE_SUBMIT_TYPE } from "../QTEFaceda";
import QTEUtils from "../util/QTEUtils";
import { QTE_LOGCAT_TYPE } from "../vo/QTEEnum";

const { ccclass, property } = cc._decorator;
@ccclass
export default class QTEResetAndSubmit extends QTEComponent {
    @property({
        type: cc.Sprite,
        displayName: "重置按钮",
    })
    sprReset: cc.Sprite = null;

    @property({
        type: cc.Sprite,
        displayName: "提交按钮",
    })
    sprSubmit: cc.Sprite = null;

    @property([cc.SpriteFrame])
    resetSprList: cc.SpriteFrame[] = [];

    @property([cc.SpriteFrame])
    submitSprList: cc.SpriteFrame[] = [];

    _isResetIng: boolean = false;

    onLoad() {
        this.sprReset.node.on(cc.Node.EventType.TOUCH_START, this.onClickResetStartUI, this);
        this.sprSubmit.node.on(cc.Node.EventType.TOUCH_START, this.onClickSubmitStart, this);
        this.sprReset.node.on(cc.Node.EventType.TOUCH_END, this.onClickReset, this);
        this.sprSubmit.node.on(cc.Node.EventType.TOUCH_END, this.onClickSubmit, this);

        this.sprReset.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onClickResetEndUI, this);
        this.sprSubmit.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onClickSubmitEndUI, this);
        this.refreshUI();
    }
    onClickResetStartUI() {
        this.sprReset.spriteFrame = this.resetSprList[1];
    }
    onClickSubmitStart() {
        this.sprSubmit.spriteFrame = this.submitSprList[1];
    }
    onClickResetEndUI() {
        this.sprReset.spriteFrame = this.resetSprList[0];
    }
    onClickSubmitEndUI() {
        this.sprSubmit.spriteFrame = this.submitSprList[0];
    }



    public refreshUI() {
        let data = QTEUtils.getCurrentData(this.qteBaseMeta.UUID);
        this.refreshResetBtn(data);
        this.refreshSubmitBtn(data);
    }

    /** 刷新提交按钮数据 */
    private refreshSubmitBtn(data) {
        let settings = QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).settings;
        if (settings) {
            if (settings.submit) {
                this.sprSubmit.node.active = settings.submit != QTE_SUBMIT_TYPE.HIDE;
                return;
            }
        }
        this.sprSubmit.node.active = !data.extraStageData.isAutoSubmit;
    }

    /** 刷新重置按钮数据 */
    private refreshResetBtn(data) {
        let settings = QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).settings;
        if (settings) {
            if (settings.reset) {
                this.sprReset.node.active = settings.reset != QTE_RESET_TYPE.HIDE;
                return;
            }
        }
        this.sprReset.node.active = data.extraStageData.hasRecover;
    }

    public submitEnd() {
        console.log("####--RESET_SUBMIT--submitEnd");
        this.sprSubmit.node.active = false;
        let settings = QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).settings;
        if (settings) {
            if (settings.reset) {
                if (settings.reset == QTE_RESET_TYPE.SHOW) {
                    this.sprReset.node.active = true;
                    return;
                }
            }
        }
        this.sprReset.node.active = false;
    }

    private onClickReset() {
        if (this._isResetIng) {
            return;
        }
        this._isResetIng = true;
        this.scheduleOnce(() => {
            this._isResetIng = false;
            this.onClickResetEndUI();
        }, 3);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickReset');
        let qNode = QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).quoteNode
        if (qNode && qNode.onResetBeforeCheck()) {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickReset begin');
            QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).quoteNode.reset();
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickReset end');
        }
 
    }

    private onClickSubmit() {
        this.onClickSubmitEndUI();
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickSubmit');
        let qNode = QTEUtils.instance(QTETemplateManager, this.qteBaseMeta.UUID).quoteNode
        if (qNode && qNode.onSubmitBeforeCheck()) {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickSubmit begin');
            qNode.submit();
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'onClickSubmit end');
        }
    }

    onDestroy() {
    }
}