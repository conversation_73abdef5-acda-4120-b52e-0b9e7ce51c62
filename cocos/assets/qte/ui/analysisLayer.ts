import QTEFaceda from "../QTEFaceda";
import ResLoader from "../core/loader/ResLoader";
import QTEAudioManager from "../manager/QTEAudioManager";


class AudioItem {
    // 声音路径，用作key
    public url: string = "";
    public nId: number = -1;
    public bIsLoop: boolean = false;
}

const { ccclass, property } = cc._decorator;

@ccclass
export default class analysisLayer extends cc.Component {
    @property(cc.Node)
    playNode: cc.Node = null; // qte父节点

    @property(cc.Node)
    textNode: cc.Node = null;

    @property(cc.Label)
    textLabel: cc.Label = null;

    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.Label)
    sumTimeLabel: cc.Label = null;

    @property(cc.ProgressBar)
    pb: cc.ProgressBar = null;

    @property(cc.Node)
    pbPointNode: cc.Node = null;
    _data: qte.Analysis = null;

    @property(cc.SpriteFrame)
    audioIcon: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    audioSp: cc.Sprite = null;
    private _curAudioId: number = null;
    private _audio: cc.AudioClip = null;
    _audioItem: AudioItem = null;
    currPlayTime: number = 0;
    skinSp: number = 0;
    @property(cc.Sprite)
    imgSp: cc.Sprite = null;

    UUID: string = "";
    onLoad() {
        this.node.zIndex = 1;
    }

    playAudio() {
        if (!this._audio) {
            console.error("音频没有加载完成请稍后");
        }

        if (this._curAudioId != null) {
            this.stopAudio();
            this._curAudioId = null;
            return;
        }

        let item = new AudioItem()
        item.url = this._audio.nativeUrl;
        item.bIsLoop = false;


        let qteAudioManager = qte.QTEUtils.instance(QTEAudioManager, this.UUID);
        let callBack = () => {
            this.stopAudio();
            this._curAudioId = null;
        }


        if (this.UUID && qteAudioManager) {
            this._curAudioId = qteAudioManager.playAudio(this._audio, false, () => {
                if (callBack) {
                    callBack();
                }
            });

        } else {
            this._curAudioId = cc.audioEngine.play(this._audio, false, 1);
            cc.audioEngine.setFinishCallback(this._curAudioId, () => {
                if (callBack) {
                    callBack();
                }
            });

        }

        item.nId = this._curAudioId;
        this._audioItem = item;
        this.unschedule(this.updateUi);
        this.currPlayTime = 0;
        this.schedule(this.updateUi, 0.1);

    }
    updateUi(dt: number) {
        this.currPlayTime += dt;
        if (this.currPlayTime > this._data.audio.duration) {
            this.currPlayTime = this._data.audio.duration;
        }
        if (this._curAudioId != null) {
            let muit0 = parseInt(this.currPlayTime / 60 + "") + "";
            let sec0 = parseInt(this.currPlayTime % 60 + "") + "";
            if (muit0.length == 1) {
                muit0 = "0" + muit0;
            }
            if (sec0.length == 1) {
                sec0 = "0" + sec0;
            }
            if (this.timeLabel) {
                this.timeLabel.string = muit0 + ":" + sec0;
            }
            if (this.pb) {
                this.pb.progress = Number(parseFloat(this.currPlayTime / this._data.audio.duration + "").toFixed(2));
                this.pbPointNode.x = this.pbPointNode.parent.width * this.pb.progress;
            }

            if (this.skinSp != 1) {
                this.skinSp = 1;
                this.audioSp.spriteFrame = this.audioIcon[this.skinSp];
            }

        } else {
            this.currPlayTime = 0;
            if (this.pb) {
                this.pb.progress = 0;
            }
            if (this.skinSp != 0) {
                this.skinSp = 0;
                this.audioSp.spriteFrame = this.audioIcon[this.skinSp];
            }
            if (this.timeLabel) {
                this.timeLabel.string = "00:00";
            }
            this.pbPointNode.x = 0;
            this.unschedule(this.updateUi);
        }

    }

    public stopAudio() {
        console.log("stopAudio");
        let qteAudioManager = qte.QTEUtils.instance(QTEAudioManager, this.UUID);
        if (this.UUID && qteAudioManager) {
            qte.QTEUtils.instance(QTEAudioManager, this.UUID).stopAudio(this._audioItem.nId);
        } else {
            cc.audioEngine.stop(this._audioItem.nId);
        }
    }


    public updateAnalysisLayer(data: qte.Analysis): void {
        this._data = data;
        if (QTEFaceda.uuidArr.length > 0) {
            this.UUID = QTEFaceda.uuidArr[QTEFaceda.uuidArr.length - 1];
        } else {
            this.UUID = '';  // 用cocos自己播放吧
        }

        if (this._data && this._data.audio) {
            this.playNode.active = true;
            this.sumTimeLabel.string = "/  " + this.getTimeStr(this._data.audio.duration);
            let url = this._data.audio.url;
            ResLoader.loadRes(url, cc.AudioClip, (err, res: cc.AudioClip) => {
                if (err) {
                    console.log("检查:audioUrl", url);
                    return;
                }
                if (!cc.isValid(this, true)) {
                    return;
                }
                this._audio = res;
            }, null, null);
        } else {
            this.playNode.active = false;
        }
        this.textNode.active = false;
        this.imgSp.node.active = false;
        if (this._data && this._data.text) {
            if (!this._data.audio) {
                this.textNode.height = 544;
            } else {
                this.textNode.height = 416;
            }
            this.textLabel.string = this._data.text;
            this.textNode.active = true;
        } else if (this._data && this._data.imageUrl) {
            this.imgSp.node.active = true;
            this.imgSp.spriteFrame = null;
            this.textNode.active = false;
            ResLoader.loadRes(this._data.imageUrl, cc.Texture2D, (err, res: cc.Texture2D) => {
                if (err) {
                    console.log("检查:this._data.imageUrl", this._data.imageUrl);
                    return;
                }
                if (!cc.isValid(this.node, true)) {
                    return;
                }
                this.imgSp.node.scale = 1;
                if (!this._data.audio) {
                    this.imgSp.node.y = 0;
                } else {
                    this.imgSp.node.y = -64;
                    if (res.height > 416) {
                        this.imgSp.node.scale = 416 / res.height;
                    }
                }
                this.imgSp.spriteFrame = new cc.SpriteFrame(res);
            }, null, null);
        }
        else if(this._data && this._data.audio){
            console.log("只有音频")
        }else {
            this.textNode.active = true;
            this.textLabel.string = '暂无解析';
        }
    }


    getTimeStr(time: number) {
        let muit1 = parseInt(time / 60 + "") + "";


        let sec1 = parseInt(Math.round((time % 60) * 100 / 100) + "") + "";
        if (muit1.length == 1) {
            muit1 = "0" + muit1;
        }
        if (sec1.length == 1) {
            sec1 = "0" + sec1;
        }
        return muit1 + ":" + sec1;
    }

    /**
     * 销毁当前层
     */
    public async dispose() {
        this.node.destroy();
    }

    public onDestroy() {
        this.unscheduleAllCallbacks();
        if (this._curAudioId != null) {
            this.stopAudio();
            this._curAudioId = null;
        }
    }
}
