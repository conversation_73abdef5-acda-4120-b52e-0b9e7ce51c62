/*
 * @FilePath     : /sdk/assets/qte/ui/QTEQuestionContent.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

import ResLoader from "../core/loader/ResLoader";
import { TraceName } from "../vo/QTEEnum";

const {ccclass, property} = cc._decorator;



const CONTENT_STATE = {
    OPEN: 1,
    CLOSE: 2
}
const BTN_STATE = {
    OPEN: 1,
    CLOSE: 2
}
const MASK_WIDTH = {
    OPEN: 172,
    CLOSE: 48
}
const MASK_X = {
    OPEN: 0,
    CLOSE: 127
}

@ccclass
export default class  QTEQuestionContent extends cc.Component {

    // 按钮节点
    @property(cc.Node)
    private maskNode: cc.Node = null;
    // scroll 节点
    @property(cc.Node)
    private contentNode: cc.Node = null;
    // 关闭按钮
    @property(cc.Node)
    private btnClose: cc.Node = null;
    @property(cc.Node)
    // 缩回按钮
    private btnBack: cc.Node = null;
    // 展开按钮
    @property(cc.Node)
    private btnExpand: cc.Node = null;
    // 展开按钮文字
    @property(cc.Node)
    private btnExpandText: cc.Node = null;
    // 按钮显示图标
    @property(cc.Node)
    private btnExpandIcon: cc.Node = null;
    @property(cc.Node)
    private scrollViewContent: cc.Node = null;
    
    

    // 设置状态，分开打开和关闭， 默认显示按钮。
    private _state = CONTENT_STATE.OPEN;
    private _btnStatus = BTN_STATE.OPEN;


    public show(data: any []){
        this.btnInit();
        this.showQuestionContent(data);
        qte.tracePm(TraceName.updateShowQuestionContent);
    }
    public btnInit(){
        this._btnStatus = BTN_STATE.OPEN;
        this.maskNode.active = true;
        this.maskNode.width = MASK_WIDTH.OPEN;
        this.contentNode.active = false;
        this.contentNode.parent = cc.find('Canvas');
        this.contentNode.zIndex = 1000;
    }

    public btnStatus(status: number){
        console.log("%c Line:84 🥥 status", "color:#f5ce50", status);
        switch (status) {
            case BTN_STATE.OPEN:
                this.maskNode.getComponent(cc.Animation).play("zhankai");
                break;
            case BTN_STATE.CLOSE:
                this.maskNode.getComponent(cc.Animation).play("guanbi");
                break;
        }
        this._btnStatus = status;
    }
    // 点击关闭按钮
    public btnCloseClick(){
        this.contentNode.active = false;
    }
    // 点击缩回按钮
    public btnBackClick(){
        if(this._btnStatus == BTN_STATE.OPEN){
            this.btnStatus(BTN_STATE.CLOSE);
        }else{
            this.btnStatus(BTN_STATE.OPEN);
        }
    }
    // 查看原文
    public btnOriginalClick(){
        console.log("查看原文");
        this.contentNode.active = true;
        qte.tracePm(TraceName.updateClickQuestionContent);
    }

    // 图片内容显示
    public async showQuestionContent(data: any[]){
        
        // 先加载完所有图片
        let promiseArr = [];
        // 包装ResLoader.loadRes为Promise
        const loadResPromise = (url: string): Promise<cc.Texture2D> => {
            return new Promise((resolve, reject) => {
                ResLoader.loadRes(url, cc.Texture2D, (err, asset) => {
                    if (err) {
                        console.error(err);
                        reject(err);
                        return;
                    }
                    console.log(`加载完成 ${url}`);
                    resolve(asset);
                });
            });
        };

        try {
            // 创建所有加载Promise
            const loadPromises = data.map((url, i) => {
                console.log(`开始加载 data[${i}] == `, url);
                return loadResPromise(url);
            });
            
            // 并发加载所有资源
            promiseArr = await Promise.all(loadPromises);
            
        } catch (error) {
            console.error("资源加载失败:", error);
            return;
        }

        console.log("所有资源加载完成 promiseArr.length == ", promiseArr.length);
        let currentHeight = 105;
        for(let i = 0; i < promiseArr.length; i++){
            let tempNode = new cc.Node();
            tempNode.addComponent(cc.Sprite);
            tempNode.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(promiseArr[i]);
            tempNode.height = promiseArr[i].height;
            tempNode.y = -(tempNode.height/2 + currentHeight);
            currentHeight += tempNode.height;
            this.scrollViewContent.addChild(tempNode);
            this.scrollViewContent.height = (-tempNode.y + tempNode.height/2) > 656 ? (-tempNode.y + tempNode.height/2) : 656;
        }
        
    }


}
