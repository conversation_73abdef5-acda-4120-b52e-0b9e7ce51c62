import { QTE_OPENMODE } from "../QTEFaceda";

const { ccclass, property } = cc._decorator;
@ccclass
export default class QTESerialChangeUI extends cc.Component {
    @property(cc.Node)
    leftNode: cc.Node = null;

    @property(cc.Label)
    numLabel: cc.Label = null;

    @property(cc.Node)
    rightNode: cc.Node = null;

    @property(cc.Node)
    zhankaiNode: cc.Node = null;

    @property(cc.Node)
    suoxiaoNode: cc.Node = null;

    @property(cc.Node)
    navNode: cc.Node = null;

    @property(cc.Prefab)
    clickPre: cc.Prefab = null;

    @property(cc.SpriteFrame)
    errorSf0: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    errorSf1: cc.SpriteFrame = null;

    @property(cc.SpriteFrame,)
    rightSf0: cc.SpriteFrame = null;

    @property(cc.SpriteFrame)
    rightSf1: cc.SpriteFrame = null;
    // 数据
    data: qte.QTESerialUIParams = null;
    // 节点列表
    nodeList: cc.Node[] = [];
    // 是否可以点击
    isCanTouch: boolean = true;
    // 初始化数据
    openOp: number = 1;
    mode: QTE_OPENMODE = 0;

    initData(data: qte.QTESerialUIParams, mode: QTE_OPENMODE) {
        this.mode = mode;
        this.data = data;
        if (this.nodeList.length > 0) {
            for (let i = 0; i < this.nodeList.length; i++) {
                this.nodeList[i].destroy();
            }
        }
        this.nodeList = [];
        for (let i = 0; i < this.data.questionCount; i++) {
            let node = cc.instantiate(this.clickPre);
            node.parent = this.navNode;
            let button = node.getComponent(cc.Button);
            let event = new cc.Component.EventHandler();
            event.target = this.node;
            event.component = "QTESerialChangeUI";
            event.handler = "onClick";
            event.customEventData = i + ""
            button.clickEvents.push(event);
            this.nodeList.push(node);
        }
        this.updateUI();
        this.onChangeQuestinIndex(this.data.questionIndex);
        this.updateOpenOp();
    }

    zhanKai() {
        this.openOp = 1;
        this.updateOpenOp();
    }

    suoxiao() {
        this.openOp = 0;
        this.updateOpenOp();
    }
    updateOpenOp() {
        if (this.openOp == 1) {
            this.zhankaiNode.active = true;
            this.suoxiaoNode.active = false;

        } else {
            this.zhankaiNode.active = false;
            this.suoxiaoNode.active = true;
        }


    }

    // 点击事件
    onClick(event: cc.Event.EventTouch, custom: string) {
        if (!this.isCanTouch) {
            return;
        }

        let num = Number(custom);
        this.onChangeQuestinIndex(num);
    }
    // 切换题目
    async onChangeQuestinIndex(index: number) {
        this.isCanTouch = false;
        this.data.questionIndex = index;
        await this.data.changeQuestion(index);
        this.isCanTouch = true;
        this.updateUI();
    }
    // 刷新UI
    updateUI() {
        for (let i = 0; i < this.nodeList.length; i++) {
            let label = this.nodeList[i].getChildByName("label").getComponent(cc.Label);
            label.string = (i + 1) + "";
            if (this.mode == QTE_OPENMODE.SHOW_CORRECT) {
                if (i == this.data.questionIndex) {
                    this.nodeList[i].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = this.rightSf1;

                } else {
                    this.nodeList[i].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = this.rightSf0;
                }

            } else {
                let index = 0;
                if (i == this.data.questionIndex) {
                    index = 1;
                } else {
                    index = 0;
                }
                if (this.data.arrResult && this.data.arrResult[i]) {
                    this.nodeList[i].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = this['rightSf' + index]
                } else {
                    this.nodeList[i].getChildByName("bg").getComponent(cc.Sprite).spriteFrame = this['errorSf' + index]
                }
            }
        }
        if (this.data.questionIndex == 0) {
            this.leftNode.active = false;
        } else {
            this.leftNode.active = true;
        }
        if (this.data.questionIndex == this.data.questionCount - 1) {
            this.rightNode.active = false;
        } else {
            this.rightNode.active = true;
        }
        this.numLabel.string = (this.data.questionIndex + 1) + "/" + this.data.questionCount;
    }
    // 向前切换
    preChange() {
        if (!this.isCanTouch) {
            return;
        }
        if (this.data.questionIndex - 1 >= 0) {
            this.onChangeQuestinIndex(this.data.questionIndex - 1)
        }

    }
    // 向后切换
    nextChange() {
        if (!this.isCanTouch) {
            return;
        }
        if (this.data.questionIndex + 1 <= this.data.questionCount - 1) {
            this.onChangeQuestinIndex(this.data.questionIndex + 1)
        }
    }
}