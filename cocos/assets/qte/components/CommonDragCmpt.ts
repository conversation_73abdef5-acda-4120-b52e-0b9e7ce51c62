import QTEComponent from "../base/QTEComponent";
import { relatedTouch } from "../decorators";

const { ccclass } = cc._decorator;
@relatedTouch
@ccclass
export default class CommonDragCmpt extends QTEComponent {
    private _offsetPos: cc.Vec3;
    private _touched: boolean;

    public onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onBegin, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onCancel, this);
    }

    public onBegin(e: cc.Touch): void {
        e["_propagationStopped"] = true;
        let pos = this.convertPos(e);
        this._offsetPos = pos.sub(this.node.position);
        this._touched = true;
    }

    public onMove(e: cc.Touch): void {
        if (!this._touched) {
            return;
        }
        e["_propagationStopped"] = true;
        let pos = this.convertPos(e);
        this.node.position = pos;
    }

    public onEnd(e: cc.Touch): void {
        e["_propagationStopped"] = true;
        this._touched = false;
    }

    public onCancel(e: cc.Touch): void {
        this.onEnd(e);
    }

    private convertPos(e: cc.Touch) {
        let local = e.getLocation();
        return cc.v3(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    }

}