// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import QTEComponent from "../base/QTEComponent";
import QTEEntityNode from "../base/QTEEntityNode";
import QTEAssetsManager from "../manager/QTEAssetsManager";
import QTEEntityManager from "../manager/QTEEntityManager";
import QTEUtils from "../util/QTEUtils";

const { ccclass, property } = cc._decorator;
/**
 * 填空题选项的图片组件
 */
@ccclass
export default class BlankCmpt extends QTEComponent {
    eventNode: cc.Node = null
    qid: any;
    normalTex2D: any;
    touchTex2D: any;
    touch() {
        this.eventNode.emit("blankTouch", this.qid);
    }
    async init(obj: any) {
        let {
            eventNode, qid, UUID, touchUrl, isCurrent
        } = obj
        if (touchUrl) {
            this.touchTex2D = await QTEUtils.instance(QTEAssetsManager, UUID).getAsset(touchUrl);
        }
        //@ts-ignore
        let normalUrl = this.node.getComponent(cc.Sprite) ? this.node.properties.texture : null;
        this.normalTex2D = await QTEUtils.instance(QTEAssetsManager, UUID).getAsset(normalUrl);
        this.eventNode = eventNode;
        this.qid = qid;
        this.node.on(cc.Node.EventType.TOUCH_START, this.touch, this)
        this.eventNode.on("blankTouch", this.getBlankTouch, this)
        if (isCurrent&&this.touchTex2D) {
            let touchSP = new cc.SpriteFrame(this.touchTex2D) || null;
            this.node.getComponent(cc.Sprite).spriteFrame = touchSP
        }

    }






    getBlankTouch(qid) {
        if (this.touchTex2D) {
            //单纯变ui
            if (this.qid == qid) {
                //他自己
                let touchSP = new cc.SpriteFrame(this.touchTex2D) || null;
                this.node.getComponent(cc.Sprite).spriteFrame = touchSP
            } else {
                let nroma = new cc.SpriteFrame(this.normalTex2D) || null;

                this.node.getComponent(cc.Sprite).spriteFrame = nroma
            }
        }
    }



}
