import QTEComponent from "../base/QTEComponent";
const { ccclass } = cc._decorator;

@ccclass
export default class ClickCmpt extends QTEComponent {
    
    public onLoad () {
        this.setCanSkip(true);
        this.node.on(cc.Node.EventType.TOUCH_START, this.onClick, this);
    }

    public onClick (e: cc.Touch) {
        this.options && this.options.onClick && this.options.onClick(this.entityNode);
    }
}