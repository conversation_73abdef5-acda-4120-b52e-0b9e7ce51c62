import QTEComponent from "../base/QTEComponent";
import QTEAnimationManager from "../manager/QTEAnimationManager";
import QTENotificationManager from "../manager/QTENotificationManager";
import QTEUtils from "../util/QTEUtils";
import { AnimationConfig, AnimationData } from "../vo/QTEDataVo";
import { NotifyType } from "../vo/QTEEnum";
const { ccclass } = cc._decorator;

@ccclass
export default class AnimationCmpt extends QTEComponent {

    public playAnimation (name: string, callback: () => void, data: AnimationData[], config?:AnimationConfig) {
        QTEUtils.instance(QTEAnimationManager, this.qteBaseMeta.UUID).playAnimation(data, () => {
            callback && callback();
        }, name, config);
    }

    public skipAnimation (key: string) {
        if (key === "") {
            return;
        }
        QTEUtils.instance(QTEAnimationManager, this.qteBaseMeta.UUID).skipAnimation(key);
    }

    public skipAllAnimation() {
        QTEUtils.instance(QTENotificationManager, this.qteBaseMeta.UUID).sendNotification(NotifyType.SIMPLE_SKIP_ALL_ANIMATION)
        QTEUtils.instance(QTEAnimationManager, this.qteBaseMeta.UUID).skipAllAnimation();
    }
    /**
     * 跳过动画,不回调
     * @param key 
     */
    public skipAnimationWithoutCB (key: string) {
        if (key === "") {
            return;
        }
        // 跳过动画,不回调，要在animationManager里新增接口
        QTEUtils.instance(QTEAnimationManager, this.qteBaseMeta.UUID).skipAnimationWithoutCB(key);
    }
}