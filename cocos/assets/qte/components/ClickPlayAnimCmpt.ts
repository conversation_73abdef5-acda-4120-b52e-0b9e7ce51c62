import QTEComponent from "../base/QTEComponent";
const { ccclass } = cc._decorator;

/**
 * 点击播放组件动画脚本
 * <AUTHOR>
 * @date 2021-02-20
 */
@ccclass
export default class ClickPlayAnimCmpt extends QTEComponent {

    public onLoad () {
        this.setCanSkip(true)
        this.node.on(cc.Node.EventType.TOUCH_END, this.onClick, this);
    }

    public onClick (e: cc.Touch) {
        // TODO 点击元素默认可以打断
        this.entityNode.playAnimation("afterClick", null, true);
    }

}