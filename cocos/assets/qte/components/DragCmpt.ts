import QTEComponent from "../base/QTEComponent";
import { relatedTouch } from "../decorators";

const { ccclass } = cc._decorator;
@relatedTouch
@ccclass
export default class DragCmpt extends QTEComponent {
    // 组件原始坐标
    public originLocal: cc.Vec3;
    private _tween: cc.Tween;
    private _offsetPos: cc.Vec2;

    private _lastStayPos: cc.Vec3;
    private _block: boolean;

    public onLoad() {
        this.setCanSkip(true);
        this.node.on(cc.Node.EventType.TOUCH_START, this.onBegin, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onCancel, this);
        this.originLocal = this.node.position;
        this._lastStayPos = this.originLocal;
        this._block = true;
    }

    public onBegin(e: cc.Touch): void {
        // if (!this.enabled || !this._canDrag) return;
        if (!this.enabled) return;
        e["_propagationStopped"] = true;
        this._block = false;
        let startPos = this._lastStayPos ? this._lastStayPos : this.originLocal
        let targetPos = cc.v3(0, 0, 0).add(startPos)
        if (this._tween) this._tween.stop()
        this.node.position = startPos
        this._tween = cc.tween(this.node).to(0.1, { position: targetPos }).start();

        this.options && this.options.onBegin && this.options.onBegin(this.entityNode);
    }
    public changeLastStayPos(pos: cc.Vec3) {
        this._lastStayPos = pos
    }

    public onMove(e: cc.Touch): void {
        if (!this.enabled || this._block) return;
        e["_propagationStopped"] = true;
        if (this._tween) {
            this._tween.stop();
            let local = e.getLocation();
            let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
            this._offsetPos = cc.v2(pos.x - this.node.x, pos.y - this.node.y);
            this._tween = null;
        }
        this.node.setPosition(this.convertPos(e));

        this.options && this.options.onMove && this.options.onMove(this.entityNode);
    }

    public onEnd(e: cc.Touch): void {
        if (!this.enabled || this._block) return;
        e["_propagationStopped"] = true;
        // this.node.setSiblingIndex(this.node['oldSiblingIndex']);
        this.options && this.options.onEnd && this.options.onEnd(this.entityNode);
    }

    public onCancel(e: cc.Touch): void {
        if (!this.enabled || this._block) return;
        // this.node.setSiblingIndex(this.node['oldSiblingIndex']);
        this.options && this.options.onCancel && this.options.onCancel(this.entityNode);
    }

    private convertPos(e: cc.Touch) {
        let local = e.getLocation();
        return cc.v3(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
    }
}