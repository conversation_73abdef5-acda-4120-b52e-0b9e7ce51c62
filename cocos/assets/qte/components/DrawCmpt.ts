/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2022-02-11 15:50:36
 * @modify date 2022-02-11 15:50:36
 * @desc [description]
 */

import QTEComponent, { QTEComponentType } from "../base/QTEComponent";
import ValueUtils from "../core/utils/ValueUtils";
import QTEUtils from "../util/QTEUtils";

const { ccclass, property } = cc._decorator;

class DrawModel {
    public path: number[][]
    public paths: number[][][];

    constructor() {
        this.path = [];
        this.paths = [];
    }
}

@ccclass
export default class DrawCmpt extends QTEComponent {
    // 所有笔迹的画板
    private _graphicNode: cc.Node;
    private _graphics: cc.Graphics = null;
    // 当前笔迹的画板
    private _curGraphicNode: cc.Node;
    private _curGraphics: cc.Graphics = null;
    // 是否激活画笔
    private _canDraw: boolean;
    private _parentScroll: cc.ScrollView;
    private _lastUpdateTime: number = 0;
    private _lastDrawTime: number = 0;
    private _drawStartFunc: Function;
    private _isInit: boolean = false;
    private _isTouching: boolean = false;
    private _offsetV2: cc.Vec2 = cc.v2(0, 0);
    public onLoad() {
        this.init();
        this.node.on(cc.Node.EventType.SIZE_CHANGED, () => {
            this._graphicNode = this.getGraphicNode('graphic_node', cc.macro.MAX_ZINDEX - 2);
            this._curGraphicNode = this.getGraphicNode('cur_graphic_node', cc.macro.MAX_ZINDEX - 1);
        }, this);


    }

    checkParentOffestV2(node: cc.Node) {
        if (node.parent) {
            this._offsetV2 = this._offsetV2.add(node.parent.getPosition());
            if (node.parent.name != "root") {
                this.checkParentOffestV2(node.parent);
            }
        }

    }

    private init() {
        if (!this.node) { return; }
        let size = this.node.getContentSize();
        // 设置当前组件的类型
        this.type = QTEComponentType.DrawCmpt;
        this.state = new DrawModel();
        this._graphicNode = this.getGraphicNode('graphic_node', cc.macro.MAX_ZINDEX - 2);
        this._graphics = this._graphicNode.getComponent(cc.Graphics);

        this._curGraphicNode = this.getGraphicNode('cur_graphic_node', cc.macro.MAX_ZINDEX - 1);
        this._curGraphics = this._curGraphicNode.getComponent(cc.Graphics);
        this._isInit = true;
    }

    getGraphicNode(name: string, zIndex: number) {
        let node = this.node.getChildByName(name);
        if (!node) {
            node = new cc.Node();
            node.name = name;
            node.parent = this.node;
            node.setAnchorPoint(this.node.getAnchorPoint());
            node.setContentSize(this.node.getContentSize());
            node.zIndex = zIndex;

            let comp = node.addComponent(cc.Graphics);
            comp.strokeColor = cc.Color.RED;
            comp.lineWidth = 3;
            comp.lineJoin = cc.Graphics.LineJoin.ROUND;
        }
        node.setContentSize(this.node.getContentSize());
        return node;
    }

    setParentScroll(comp: cc.ScrollView) {
        this._parentScroll = comp;
    }

    private clear() {
        this._graphics.clear();
        this.state.path = [];
        this.state.paths = [];
        this.stateChanged();
        this.drawAllPath();
    }

    private isInNode(pos: cc.Vec2) {
        // let size = this._curGraphicNode.getContentSize();
        // let x = this._curGraphicNode.x - size.width * this._curGraphicNode.anchorX;
        // let y = this._curGraphicNode.y - size.height * this._curGraphicNode.anchorY;
        // let rect = cc.rect(x, y, size.width, size.height);
        // return rect.contains(pos);
        let temp = this._curGraphicNode.getBoundingBox();
        return temp.contains(pos);

    }

    private onTouchStart(touch: cc.Touch): void {
        if (!this._canDraw) {
            return;
        }
        touch["_propagationStopped"] = true;
        this.state.path = [];
        // let pos = this._curGraphicNode.convertToNodeSpaceAR(touch.getLocation());
        let local = touch.getLocation();
        //let pos = cc.v2(local.x - cc.winSize.width / 2 - this._offsetV2.x, local.y - cc.winSize.height / 2 - this._offsetV2.y);
        let pos = qte.QTEUtils.convertStageToNodeSpaceAR(this.node,  cc.v2(local.x - cc.winSize.width / 2 , local.y - cc.winSize.height / 2 ));

        if (!this.isInNode(pos)) {
            return;
        }
        this._isTouching = true;
        this.state.paths.push([]);
        this.addToPath(pos);
        this._drawStartFunc && this._drawStartFunc();
    }

    private onTouchMove(touch: cc.Touch): void {
        if (!this._canDraw || !this._isTouching) {
            console.error("onTouchMove1111")
            return;
        }
        touch["_propagationStopped"] = true;
        let curTime = Date.now();
        if (curTime - this._lastDrawTime < 30) {
            return;
        }
        this._lastDrawTime = curTime;

        // let pos = this._curGraphicNode.convertToNodeSpaceAR(touch.getLocation());
        let local = touch.getLocation();
        //let pos = cc.v2(local.x - cc.winSize.width / 2 - this._offsetV2.x, local.y - cc.winSize.height / 2 - this._offsetV2.y);
        let pos = qte.QTEUtils.convertStageToNodeSpaceAR(this.node,  cc.v2(local.x - cc.winSize.width / 2 , local.y - cc.winSize.height / 2 ));

        if (!this.isInNode(pos)) {
            return;
        }
        pos.x = Math.round(pos.x);
        pos.y = Math.round(pos.y);
        // 单笔点数过多的场景，到达200个点之后，就禁止继续绘图，toast提示一下吧，文案：“绘制已达单笔极限”
        if (this.isPointFull()) {
            QTEUtils.showToast("绘制已达单笔极限");
            this._isTouching = false;
            return;
        }
        this.addToPath(pos);

        curTime = Date.now();
        if (curTime - this._lastUpdateTime > 50) {
            this.stateChanged();
            this._lastUpdateTime = curTime;
        }

        this.drawCurPath();
    }

    private onTouchEnd(touch: cc.Touch): void {
        if (!this._canDraw) {
            return;
        }
        touch["_propagationStopped"] = true;
        this.drawAllPath();
        this.state.path = [];
        this.stateChanged();
        this._isTouching = false;
    }

    private onTouchCancel(touch: cc.Touch): void {
        this.onTouchEnd(touch);
    }

    private isPointFull() {
        let pathLength = this.state.paths.length;
        return this.state.paths[pathLength - 1].length > 200;
    }

    private addToPath(p: cc.Vec2) {
        let pathLength = this.state.paths.length;
        if (this.state.path.length >= 2) {
            let len = this.state.path.length;
            let p1 = cc.v2(this.state.path[len - 1][0], this.state.path[len - 1][1]);
            let p2 = cc.v2(this.state.path[len - 2][0], this.state.path[len - 2][1]);
            let vec1 = p.sub(p1);
            let vec2 = p1.sub(p2);
            let radian = 0;
            if (vec1.mag() > 0 && vec2.mag() > 0) {
                radian = vec1.angle(vec2);
            }
            // 两个线段的夹角小于0.15弧度，或者线段长度小于2的，舍掉中间点
            if (radian < 0.15 || vec1.mag() < 2) {
                this.state.path.pop();
                this.state.paths[pathLength - 1].pop();
            }
        }
        let point = [p.x, p.y];
        this.state.path.push(point);
        this.state.paths[pathLength - 1].push(point);
    }

    /**
     * 绘制当前笔迹
     */
    private drawCurPath() {
        this._curGraphics.clear();
        this.draw(this._curGraphics, this.state.path);
    }

    /**
     * 绘制所有笔迹
     */
    private drawAllPath() {
        this._graphics.clear();
        for (let j = 0; j < this.state.paths.length; j++) {
            let path = this.state.paths[j];
            this.draw(this._graphics, path);
        }
        this._curGraphics.clear();
    }

    private draw(graphics, path) {
        let len = path.length;
        if (len < 2) {
            return;
        }
        let start = cc.v2(path[0][0], path[0][1]);
        let end = cc.v2(path[len - 1][0], path[len - 1][1]);

        graphics.moveTo(start.x, start.y);
        for (let i = 0; i < len - 1; i++) {
            let cur = cc.v2(path[i][0], path[i][1]);
            let next = cc.v2(path[i + 1][0], path[i + 1][1]);
            let xc = cur.x + (next.x - cur.x) * 0.5;
            let yc = cur.y + (next.y - cur.y) * 0.5;
            graphics.quadraticCurveTo(cur.x, cur.y, xc, yc);
        }
        graphics.lineTo(end.x, end.y);
        graphics.stroke();
    }

    /**
     * 回退一步
     */
    back() {
        !this._isInit && this.init();
        this.state.paths.pop();
        this.stateChanged();
        this.drawAllPath();
    }

    setDrawStartFunc(func: Function) {
        this._drawStartFunc = func;
    }

    getDrawStartFunc() {
        return this._drawStartFunc;
    }

    public get canDraw(): boolean {
        return this._canDraw;
    }

    public set canDraw(value: boolean) {
        if (this._canDraw == value) {
            return;
        }
        !this._isInit && this.init();
        this._canDraw = value;
        if (this._canDraw) {
            this._offsetV2 = cc.v2(this._curGraphicNode.x, this._curGraphicNode.y);
            this.checkParentOffestV2(this._curGraphicNode);
        }
        this._parentScroll && (this._parentScroll.enabled = !value);
        if (!this._curGraphicNode) {
            return;
        }
        if (value) {
            this._curGraphicNode.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
            this._curGraphicNode.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this._curGraphicNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this._curGraphicNode.on(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        } else {
            this._curGraphicNode.off(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
            this._curGraphicNode.off(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this._curGraphicNode.off(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            this._curGraphicNode.off(cc.Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
        }
    }

    public reset() {
        !this._isInit && this.init();
        this.clear();
    }

    watcher(key, val) {
        !this._isInit && this.init();
        if (this._isInit) {
            switch (key) {
                case "path":
                    this.state.path = ValueUtils.clone(val);
                    this.drawCurPath();
                    break;
                case "paths":
                    this.state.paths = ValueUtils.clone(val);
                    this.drawAllPath();
                    break;
            }
        }
    }
    //设置画笔颜色
    public setGraphicColor(color: cc.Color) {
        this._graphics.strokeColor = color;
        this._curGraphics.strokeColor = color;
    }
    //获取当前绘制了多少段线
    public getStatePathsLength() {
        return this.state.paths.length || 0;
    }
    //获取当前是否正在绘制
    public getIsTouching() {
        return this._isTouching;
    }

}