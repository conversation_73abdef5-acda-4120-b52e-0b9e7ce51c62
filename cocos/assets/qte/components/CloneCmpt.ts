import QTEComponent from "../base/QTEComponent";
import QTEEntityNode from "../base/QTEEntityNode";
import QTEEntityManager from "../manager/QTEEntityManager"
import QTEUtils from "../util/QTEUtils";
const { ccclass } = cc._decorator;

@ccclass
export default class CloneCmpt extends QTEComponent {

    public async start () {
        let entityManager = QTEUtils.instance(QTEEntityManager, this.qteBaseMeta.UUID);
        for (let i = this.options.num - 1; i >= 1; i-- ) {
            let newId = this.entityNode.qid + "_" + i
            let entity = await entityManager.cloneEntity(this.entityNode, newId);
            entity.qid = newId;
            entity.name = newId;
            this.entityNode.parent.insertChild(entity, this.entityNode.getSiblingIndex() + 1);
            // this.entityNode.parent.addChild(entity);
            // entity.setSiblingIndex(this.entityNode.getSiblingIndex()+i);
            entityManager.addEntityNode(entity.qid, entity.qtag, entity);
            entity.width = this.entityNode.width;
            entity.height = this.entityNode.height;
            entity.active = false;
        }
    }
}