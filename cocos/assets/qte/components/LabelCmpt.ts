// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import QTEComponent from "../base/QTEComponent";


const { ccclass, property } = cc._decorator;
/**
 * 填空题文字容器
 * 作者：武建凯
 * 说明：填空题每个选项的文字容器
 *      暴露方法1：接收全字符串做独立处理
 *      暴露方法2：获取当前全字符串
 *      报露方法3:激活/关闭
 *      给什么瑱什么，不区别筛选任何字符，这些工作由题板做处理过滤
 *      完全由内部字符串独立控制光标位置
 */
@ccclass
export default class LabelCmpt extends QTEComponent {
    private group: cc.Node = null;//容器
    public _index = 0;//层级
    private font: number = 55//默认字体
    private color: cc.Color = null;//默认颜色
    private padding: number = 0.8//padding左右系数
    private lastValues = ""//上次字符串
    private lightNode: cc.Node = null;//光标
    private tw: cc.Tween<cc.Node> = null//光标动画
    public options: any = null;
    public strLabelList: cc.Label[] = [];
    onLoad(): void {
        this._initGroup();
        this.setValues("");


    }
    setFont(font: cc.Font) {
        this.options.fontFamily = font;
        this.setValues(this.lastValues);
    }
    //初始化容器
    private _initGroup() {
        if (this.group == null) {
            this.group = new cc.Node("group");
            this.group.width = this.node.width * this.padding;
            this.group.height = this.node.height;
            this.node.addChild(this.group, this._index);
            let layout: cc.Layout = this.group.addComponent(cc.Layout);
            layout.type = cc.Layout.Type.HORIZONTAL;
            layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
            //@ts-ignore
            this.font = this.node.extra.fontSize;
            let color: cc.Color = cc.color();
            //@ts-ignore
            cc.Color.fromHEX(color, this.node.extra.labelColor);
            this.color = color;
            this.lightNode = new cc.Node("lightNode");
            let lightLabel: cc.Label = this.lightNode.addComponent(cc.Label);
            lightLabel.string = "█";
            this.node.addChild(this.lightNode);
            this.lightNode.color = color;
            this.lightNode.width = 5;
            let lightFont = 30
            if (this.font > lightFont) {
                lightFont = this.font;
            }
            lightLabel.fontSize = lightFont;
            this.lightNode.height = lightFont;
            this.lightNode.y = 0;
            lightLabel.overflow = cc.Label.Overflow.SHRINK;
            this.runForever(this.lightNode);
        }

    }

    //激活、待机
    public changeActive(boo: boolean) {
        if (!this.lightNode) {
            this._initGroup()

        }
        this.lightNode.active = boo;
    }
    //获取当前值
    public getValues() {
        return this.lastValues;

    }
    //设置文本字符串
    public setValues(str: string, font = this.font, activeLight = false) {
        if (!this.group) {
            this._initGroup();
        }
        if (activeLight) {
            this.changeActive(activeLight)
        }
        this.lastValues = str;
        for (let i = 0; i < str.length; i++) {
            if (!this.strLabelList[i]) {
                let cellNode: cc.Node = null;
                let cellLabel: cc.Label = null;
                if (this.options && this.options.cellNodePrefab) {
                    cellNode = cc.instantiate(this.options.cellNodePrefab);
                    cellLabel = cellNode.getComponent(cc.Label);
                } else {
                    cellNode = new cc.Node("cellNode");
                    cellLabel = cellNode.addComponent(cc.Label);
                    cellLabel.overflow = cc.Label.Overflow.NONE;
                }

                cellNode.color = this.color;
                cellLabel.fontSize = font;
                cellLabel.lineHeight = font;
                cellNode.y = -font * 0.1;
                cellLabel.string = str[i];
                this.group.addChild(cellNode);
                this.strLabelList.push(cellLabel);

            } else {
                let cellLabel = this.strLabelList[i];
                cellLabel.string = str[i];
            }

        }
        for (let j = str.length; j < this.strLabelList.length; j++) {
            this.strLabelList[j].node.destroy();
            this.strLabelList.splice(j, 1);
            j--;
        }

        this.scheduleOnce(() => {
            let x = 0;
            if (str != "") {
                let lastCell = this.group.children[this.group.childrenCount - 1];
                if (cc.isValid(lastCell, true))
                    x = lastCell.x + lastCell.width / 2;
            }
            this.lightNode.x = x;
        }, 0);

    }






    private runForever(node: cc.Node) {
        if (this.tw) {
            this.tw.stop()
        }
        this.tw = cc.tween(node).to(0.5, { opacity: 0 }).to(0.5, { opacity: 255 }).call(() => {
            this.runForever(node);
        })
            .start()
    }
}
