import { SingleBase } from "./core/base/SingleBase";
import QTEComponent from "./base/QTEComponent";
import QTEEntityManager from "./manager/QTEEntityManager";
import QTEEntityNode from "./base/QTEEntityNode";

import { ComponentData, QuestionConfigData, StageConfig, QuestionMetaData } from "./vo/QTEDataVo";
import LogUtil from "./util/LogUtil";
import QTEAssetsManager from "./manager/QTEAssetsManager";
import QTEUtils from "./util/QTEUtils";
import QTEBaseMeta from "./base/QTEBaseMeta";
import AnimationCmpt from "./components/AnimationCmpt";
import ClickPlayAnimCmpt from "./components/ClickPlayAnimCmpt";
import QTETemplate from "./base/QTETemplate";
import ComponentFactory from "./assemble/core/ComponentFactory";
import { SpecialComponentType } from "./assemble/core/ComponentData";
import QTEAudioManager from "./manager/QTEAudioManager";
import ValueUtils from "./core/utils/ValueUtils";
import DragCmpt from "./components/DragCmpt";
import CommonDragCmpt from "./components/CommonDragCmpt";
import QTETemplateManager from "./manager/QTETemplateManager";
import BaseComponent from "./assemble/core/BaseComponent";
import ResLoader from "../qte/core/loader/ResLoader";
import { QTE_LOGCAT_TYPE } from "./vo/QTEEnum";

enum ComponentType {
    Sprite = "sprite",
    Spine = "spine",
    Group = "group",
    CutShape = "cutShape",
    Label = "label",
    CocosAni = "cocosAni",
    SpecialComponent = 'specialComponent',
    OptionComponent = 'optionComponent',
    Formula = 'formula',
    Svg = 'svgShape', // svg 图形
    Shape = 'shape', // 图形组件
    RichTextSprite = 'richTextSprite'   // 新富文本组件
}
export default class QTEStageFactory extends SingleBase {
    public static ROOT_NAME = "root";
    public initInstance() {
        console.log("初始化接口，子类可以重写实现");
    }
    // 创建舞台场景
    public async createStage(
        uuid: string,
        meta: QTEBaseMeta,
        template: QTETemplate,
        data: QuestionConfigData,
        config: {
            [key: string]: { cmpt: new () => cc.Component; options?: any }[];
        },
        onComplete?: () => void
    ): Promise<void> {
        let root = this.createRoot(template.node);
        root["templateUUID"] = template.UUID;
        let entityManager = QTEUtils.instance(QTEEntityManager, uuid);
        return new Promise<void>(async (resole, reject) => {
            // 设置背景
            await this.setBackground(root, data.stageData, uuid);
            // 创建节点树
            let createList: QTEEntityNode[] = [];
            for (let component of data.components) {
                let extraProp = data.extraDataMap[component.id];
                let entityNode = await this.createEntityNode(component, extraProp, uuid, root);
                entityNode.parent = root;
                createList.push(entityNode);
                QTEUtils.instance(QTEEntityManager, uuid).addEntityNode(component.id, component.tag, entityNode);
            }
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEStageFactory   createStage  createList ");
            // TODO：测试 是否需要 await          
            Promise.all(createList).then(() => {
                for (let component of data.components) {
                    let entity = QTEUtils.instance(QTEEntityManager, uuid).getEntityById(component.id);
                    if (entity) {
                        let extraProp = data.extraDataMap[component.id];
                        if (!extraProp) {
                            continue;
                        }
                        // 获取业务属性并添加对应组件
                        let compts = config[extraProp.tag];
                        if (compts) {
                            this.addComponents(entity, compts, meta);
                        }
                        // 添加动画播放组件
                        let animationCmpt = entity.addComponent(AnimationCmpt);
                        animationCmpt.qteBaseMeta = meta;
                        // 普通元素增加动画触发时机
                        if (extraProp.tag === "clickAction") {
                            let clickAn = entity.addComponent(ClickPlayAnimCmpt);
                            clickAn.qteBaseMeta = meta;
                        }
                        if (extraProp.tag == "oneDragableObject") {
                            let dragCmpt = entity.addComponent(CommonDragCmpt);
                            dragCmpt.qteBaseMeta = meta;
                        }
                    }
                }

                resole();
                // 加载完成
                onComplete && onComplete();
            }).catch(err => {
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTEStageFactory  Promise.all err ${err}`);
            });
        }).catch(err => {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTEStageFactory  Promise err ${err}`);
        });
    }

    public async createEntityNode(component: ComponentData, extraProp: QuestionMetaData, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        let node: QTEEntityNode;
        switch (component.type) {
            case ComponentType.Sprite:
                node = await this.createSprite(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.RichTextSprite:
                qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, 'createEntityNode  RichTextSprite');
                node = await this.createSprite(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.Spine:
                node = await this.createSpine(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.Label:
                node = await this.createLabel(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.Group:
                node = await this.createGroup(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.CutShape:
                node = await this.createShape(component, extraProp, component.extra, uuid, parent);
                break;
            // TODO 测试 恢复。
            case ComponentType.OptionComponent:
                node = await this.createOptComp(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.SpecialComponent:
                node = await this.createObject(component, extraProp, component.extra, uuid, parent);
                break;

            case ComponentType.Formula:
                node = await this.createFormula(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.Svg:
                node = await this.createSvg(component, extraProp, component.extra, uuid, parent);
                break;
            case ComponentType.Shape:
                node = await this.createShape2(component, extraProp, component.extra, uuid, parent);
                break;
            default:
                node = await this.createNode(component, extraProp, component.extra, uuid);
                if (cc.isValid(node, true) && cc.isValid(parent, true))
                    node.parent = parent;
                qte.error("this ComponentType not support--component.type-->", component.type);
                qte.logCatBoth("this ComponentType not support--component.type-->", component.type);
                break;
        }
        if (component.childComponents && component.childComponents.length > 0) {
            // 插槽
            for (const iterator of component.childComponents) {
                let entityNode = await this.createEntityNode(iterator, null, uuid, node);
                // entityNode.parent = node;
                QTEUtils.instance(QTEEntityManager, uuid).addEntityNode(iterator.id, "", entityNode);
            }
        }
        if (component.hideBeforeAnimation) {
            node.x = component.properties.x;
            node.y = component.properties.y;
            node.scale = 0;
        }
        node.initEntity(uuid);
        if (cc.isValid(node, true) && cc.isValid(parent, true))
            return node;
    }

    /**
     * 添加组件
     * @param entity
     * @param cmpts
     */
    public addComponents(entity: QTEEntityNode, cmpts: { cmpt: new () => cc.Component; options?: any }[], meta: QTEBaseMeta): void {
        entity.customCmpts = cmpts;
        for (let cmpt of cmpts) {
            let c = entity.addComponent(cmpt.cmpt) as QTEComponent;
            c.options = cmpt.options;
            c.qteBaseMeta = meta;
        }
    }

    /**
     * 添加 root
     */
    public createRoot(parentNode: cc.Node) {
        let node = new cc.Node(QTEStageFactory.ROOT_NAME);
        node.parent = parentNode;
        node.width = cc.winSize.width;
        node.height = cc.winSize.height;
        return node;
    }

    public async setBackground(root: cc.Node, stageData: StageConfig, uuid: string): Promise<cc.Node> {
        return new Promise<cc.Node>(async (resolve, reject) => {
            const backgroundNode = new cc.Node();
            backgroundNode.addComponent(cc.Sprite);
            if (stageData.bgColor) {
                ResLoader.loadBundle("qte", null, (err, bundle: cc.AssetManager.Bundle) => {
                    if (!err) {
                        ResLoader.loadRes(
                            "res/texture/default_sprite",
                            cc.Texture2D,
                            (errRes, text) => {
                                if (!errRes) {
                                    backgroundNode.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(text);
                                    backgroundNode.color = new cc.Color().fromHEX(stageData.bgColor);
                                    backgroundNode.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.CUSTOM;
                                    backgroundNode.width = stageData.width;
                                    backgroundNode.height = stageData.safeHeight;
                                    backgroundNode.name = "background";
                                    root.addChild(backgroundNode, -1);
                                    resolve(backgroundNode);
                                }
                            },
                            bundle
                        );
                    }
                })
            } else if (stageData.texture) {
                const texture = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(stageData.texture);
                backgroundNode.getComponent(cc.Sprite).spriteFrame = new cc.SpriteFrame(texture as cc.Texture2D);
                backgroundNode.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.CUSTOM;
                backgroundNode.name = "background";
                root.addChild(backgroundNode, -1);
                resolve(backgroundNode);
            } else {
                root.addChild(backgroundNode, -1);
                resolve(backgroundNode);
            }


        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    public createNode(component: ComponentData, extraProp: any, extra: Object, uuid: string): QTEEntityNode {
        const node = new QTEEntityNode();
        node.componentData = component;
        node.qid = component.id;
        node.qtag = component.tag;
        node.extra = extra;
        node.properties = component.properties;
        node.extraDataMap = extraProp;
        if (component.properties.color) {
            const color = new cc.Color();
            cc.Color.fromHEX(color, component.properties.color);
            node.color = color;
        }
        node.width = component.properties.width;
        node.height = component.properties.height;
        node.opacity = QTEUtils.checkValue(component.properties.opacity, 255);
        node.angle = component.properties.angle || 0;
        node.x = component.properties.x;
        node.y = component.properties.y;
        node.active = component.properties.active;
        node.name = component.id + "";
        node.scaleX = component.properties.scaleX || 1;
        node.scaleY = component.properties.scaleY || 1;
        if (component.childComponents) {
            node.childComponents = component.childComponents;
        }
        if (component.hideBeforeAnimation) {
            // node.active = false;
            // node.scale = 1;
            node.setPosition(cc.v2(9999, 9999));
        }

        return node;
    }

    public createShape(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>((resole, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            let g = node.addComponent(cc.Graphics);

            g.lineWidth = component.properties.lineWidth;
            let fillColor = cc.color(0, 0, 0, 0);
            fillColor = cc.Color.fromHEX(fillColor, component.properties.fillColor);
            fillColor.a = QTEUtils.checkValue(component.properties.opacity, 255);
            g.fillColor = fillColor;
            let strokeColor = cc.color(0, 0, 0, 0);
            strokeColor = cc.Color.fromHEX(strokeColor, component.properties.strokeColor);
            strokeColor.a = QTEUtils.checkValue(component.properties.opacity, 255);
            g.strokeColor = strokeColor;
            let pointsData = component.properties.pointsData;
            for (let i = 0; i < pointsData.length; i++) {
                const { x, y } = pointsData[i];
                if (i == 0) {
                    g.moveTo(x, y);
                    continue;
                }
                g.lineTo(x, y);
                if (i === pointsData.length - 1) g.close();
            }
            g.stroke();
            g.fill();
            resole(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    /**
     * 创建额外组件
     * @param component 
     * @param extraProp 
     * @param extra 
     * @param uuid 
     * @param parent 
     * @returns 
     */
    public async createOptComp(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        console.log("%c Line:285 🥃 component", "color:#ffdd4d", component);
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            let node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            let bundleName = QTEUtils.instance(QTETemplateManager, uuid).data.template.bundleName;
            qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `createOptComp  bundleName ${bundleName}`);
            QTEUtils.instance(QTEAssetsManager, this.symbol).loadBundle(bundleName, null, async (err, bundle) => {
                if (err) {
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `createOptComp error ${err}`);
                    reject(null);
                    return;
                }
                let recordNode = null;
                try {
                    recordNode = (await this.createOptNode(bundle));
                    node.addChild(recordNode);
                    node.name = component.subType;
                } catch (error) {
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `createOptComp  QTE 283 = ${error}`);
                    reject(null);
                    return;
                };


                // 设置外部能力
                let cmpt = recordNode.getComponent(BaseComponent);
                let assetManager = QTEUtils.instance(QTEAssetsManager, uuid);
                let audioManager = QTEUtils.instance(QTEAudioManager, uuid);
                cmpt.setPlayFunc((audio: cc.AudioClip, loop: boolean, cb: () => void): number => {
                    return audioManager.playAudio(audio, loop, cb);
                });
                cmpt.setStopAudioFunc((audioId: number) => {
                    audioManager.stopAudio(audioId);
                });
                cmpt.setGetRemoteFunc((url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => {
                    assetManager.loadRemote(url, type, onComplete)
                });
                cmpt.setClearRemoteFunc((url: string[]) => {
                    assetManager.clearAssetByList(url);
                });
                cmpt.setShowToastFun((content: string) => {
                    QTEUtils.showToast(content);
                });
                cmpt.setShowNoticeChoice((str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) => {
                    QTEUtils.showNoticeChoice(str, okcall, failcall, isChangeFocus);
                })
                // 组件初始化
                await cmpt.initComponent(component);
                console.log("%c Line:328 🍒 cmpt.initComponent(component);  createOptComp  111", "color:#4fff4B");

                // if (component.subType === SpecialComponentType.Counter
                //     || component.subType === SpecialComponentType.MatchBoard
                //     || component.subType === SpecialComponentType.Clock
                //     || component.subType === SpecialComponentType.Speaker
                //     //component.subType === SpecialComponentType.H5Label
                // ) {
                //     let rootSize = node.getContentSize();
                //     if (cc.isValid(recordNode, true)) {
                //         recordNode.scaleX = ValueUtils.setOneDecimal(rootSize.width / recordNode.width, 2);
                //         recordNode.scaleY = ValueUtils.setOneDecimal(rootSize.height / recordNode.height, 2);
                //         console.log("组件大小更改scaleX:", recordNode.scaleX, "scaleY:", recordNode.scaleY)
                //     }
                // }
                resolve(node);
            })
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
        return;
    }
    /**
     * 创建额外组件
     * @param bundle 
     * @returns 
     */
    public async createOptNode(
        bundle: cc.AssetManager.Bundle,
    ): Promise<cc.Node> {
        return new Promise<cc.Node>(reslove => {
            let path = "prefabs/optComp";
            bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
                if (err) {
                    console.error(err);
                    return;
                }
                let node = cc.instantiate(assets);
                reslove(node);
            });
        }).catch(err => {
            window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
            return null;
        });
    }
    public async createObject(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            let node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            QTEUtils.instance(QTEAssetsManager, this.symbol).loadBundle('qte', null, async (err, bundle) => {
                if (err) {
                    console.log("#----load qte bundle ERROR----")
                    reject(null);
                    return;
                }
                let recordNode = null;
                try {
                    recordNode = (await ComponentFactory.getInstance().createObject(component.subType as SpecialComponentType, component, bundle)).node;
                    node.addChild(recordNode);
                    node.name = component.subType;
                } catch (error) {
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `createObject  QTE 283 = ${error}`);
                    //TODO:先记录 后续处理
                    return null;
                };


                // 设置外部能力
                let cmpt = recordNode.getComponent(BaseComponent);
                let assetManager = QTEUtils.instance(QTEAssetsManager, uuid);
                let audioManager = QTEUtils.instance(QTEAudioManager, uuid);
                cmpt.setPlayFunc((audio: cc.AudioClip, loop: boolean, cb: () => void): number => {
                    return audioManager.playAudio(audio, loop, cb);
                });
                cmpt.setStopAudioFunc((audioId: number) => {
                    audioManager.stopAudio(audioId);
                });
                cmpt.setGetRemoteFunc((url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => {
                    assetManager.loadRemote(url, type, onComplete)
                });
                cmpt.setClearRemoteFunc((url: string[]) => {
                    assetManager.clearAssetByList(url);
                });
                cmpt.setShowToastFun((content: string) => {
                    QTEUtils.showToast(content);
                });
                cmpt.setShowNoticeChoice((str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) => {
                    QTEUtils.showNoticeChoice(str, okcall, failcall, isChangeFocus);
                })


                if (component.subType === SpecialComponentType.H5Label) {
                    let rootSize = node.getContentSize();
                    recordNode.width = rootSize.width;
                    recordNode.height = rootSize.height;
                    let content = cc.find("view/content", recordNode);
                    content.width = rootSize.width;
                    content.height = rootSize.height;
                }
                // 组件初始化
                await cmpt.initComponent(component);
                if (component.subType === SpecialComponentType.Counter
                    || component.subType === SpecialComponentType.MatchBoard
                    || component.subType === SpecialComponentType.Clock
                    || component.subType === SpecialComponentType.Speaker
                    //component.subType === SpecialComponentType.H5Label
                ) {
                    let rootSize = node.getContentSize();
                    if (cc.isValid(recordNode, true)) {
                        recordNode.scaleX = ValueUtils.setOneDecimal(rootSize.width / recordNode.width, 2);
                        recordNode.scaleY = ValueUtils.setOneDecimal(rootSize.height / recordNode.height, 2);
                        console.log("组件大小更改scaleX:", recordNode.scaleX, "scaleY:", recordNode.scaleY)
                    }
                }
                if(component.subType === SpecialComponentType.RTRVoice){   
                    let rootSize = node.parent.getContentSize();
                    node.y = -rootSize.height/2 - 300;
                }

                resolve(node);
            })
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    public async createSprite(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            let assetManager = QTEUtils.instance(QTEAssetsManager, uuid);
            let texture = await assetManager.getAsset(component.properties.texture) as cc.Texture2D;
            let sprite = node.addComponent(cc.Sprite);
            let flipType = component.properties["flipType"];
            if (flipType) {
                texture.packable = false;
                let material = await this.getMaterialByPath('qte', "res/effects/sprite-flip-effect", uuid);
                if (material) {
                    material.setProperty("filpType", Number(flipType.toFixed(1)));
                    sprite.setMaterial(0, material);
                }
            }
            const sp = new cc.SpriteFrame(texture);
            sprite.spriteFrame = sp;
            sprite.type = cc.Sprite.Type.SIMPLE;
            sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            node.width = component.properties.width;
            node.height = component.properties.height;
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    public async createShape2(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            let assetManager = QTEUtils.instance(QTEAssetsManager, uuid);
            let texture = await assetManager.getAsset(component.properties.texture) as cc.Texture2D;
            let sprite = node.addComponent(cc.Sprite);

            const sp = new cc.SpriteFrame(texture);
            sprite.spriteFrame = sp;
            sprite.type = cc.Sprite.Type.SIMPLE;
            sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            sprite.srcBlendFactor = cc.macro.BlendFactor.ONE;
            sprite.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.width = component.properties.width;
            node.height = component.properties.height;
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /**
     * 添加文本显示
     * @param component
     * @param extraProp
     * @param extra
     * @param parent
     */
    private async createLabel(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            // 先把 node 添加到节点树上，然后进行拍照截图，然后把 node 返回出去
            node.parent = parent;
            let editBoxNode = new cc.Node();
            editBoxNode.parent = node;
            editBoxNode.setContentSize(node.getContentSize());

            let textureArray = component.properties["textureArray"];
            if (textureArray && textureArray instanceof Array) {
                let layout = editBoxNode.addComponent(cc.Layout);
                layout.type = cc.Layout.Type.VERTICAL;
                layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
                for (let i = 0; i < textureArray.length; i++) {
                    let node = new cc.Node();
                    let spr = node.addComponent(cc.Sprite);
                    let texture = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(textureArray[i]);
                    spr.spriteFrame = new cc.SpriteFrame(texture as cc.Texture2D);
                    spr.trim = false;
                    spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
                    spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
                    editBoxNode.addChild(node);
                }
            } else if (!component.properties.texture && !textureArray) {
                let rt = editBoxNode.addComponent(cc.RichText);
                rt.string = component.properties.str;
                rt.maxWidth = component.properties.width;
                rt.lineHeight = component.properties.lineHeight;
                if (component.properties.horizontalAlign && component.properties.horizontalAlign == 1) {
                    rt.horizontalAlign = cc.macro.TextAlignment.CENTER;
                }
            }
            else {
                editBoxNode.parent = node;
                let spr = editBoxNode.addComponent(cc.Sprite);
                let texture = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(component.properties.texture);
                spr.spriteFrame = new cc.SpriteFrame(texture as cc.Texture2D);
                spr.trim = false;
                spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
                spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            }

            if (component.properties["isFixed"]) {
                node.addComponent(cc.Mask);
                editBoxNode.name = "content";
                editBoxNode.setAnchorPoint(0.5, 1);
                editBoxNode.position = cc.v3(0, node.height / 2);

                let scrollBarNode = new cc.Node();
                scrollBarNode.name = 'scrollBar';
                scrollBarNode.setContentSize(8, node.height);
                scrollBarNode.parent = node;
                scrollBarNode.position = cc.v3(node.width / 2 - scrollBarNode.width / 2, 0);

                let barNode = new cc.Node();
                barNode.name = 'bar';
                barNode.setContentSize(8, node.height);
                barNode.parent = scrollBarNode;

                let barSprite = barNode.addComponent(cc.Sprite);
                barSprite.spriteFrame = this.buildSpriteFrame(cc.color(170, 170, 170, 200), cc.rect(0, 0, 8, node.height));

                let widget = scrollBarNode.addComponent(cc.Widget);
                widget.right = 0;
                widget.top = 0;
                widget.bottom = 0;

                let scrollBar = scrollBarNode.addComponent(cc.Scrollbar);
                scrollBar.handle = barSprite;
                scrollBar.direction = cc.Scrollbar.Direction.VERTICAL;
                scrollBar.enableAutoHide = true;
                scrollBar.autoHideTime = 1;

                let scrollView = node.addComponent(cc.ScrollView);
                scrollView.vertical = true;
                scrollView.horizontal = false;
                scrollView.inertia = false;
                scrollView.elastic = false;
                scrollView.cancelInnerEvents = true;
                scrollView.content = editBoxNode;
                scrollView.verticalScrollBar = scrollBar;
            }
            resolve(node)
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    // private async createLabelByEditBoxTool(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
    //     return new Promise(async (resolve, reject) => {
    //         const node = this.createNode(component, extraProp, extra, uuid);
    //         // 先把 node 添加到节点树上，然后进行拍照截图，然后把 node 返回出去
    //         node.parent = parent;

    //         let editBoxNode = new cc.Node();
    //         editBoxNode.parent = node;
    //         editBoxNode.setContentSize(node.getContentSize());
    //         let editCom = editBoxNode.addComponent(editbox.EditBoxTool);
    //         component.properties.lineHeight && (editCom.lineHeight = component.properties.lineHeight);
    //         editCom.setNodeSize(component.properties.width, component.properties.height);
    //         if (component.properties.horizontalAlign) {
    //             editCom.setEditBoxHorizontalStatue(component.properties.horizontalAlign);
    //         }
    //         /** 将编辑器导出文本中的\n 替换成█。因为题版label对于换行的解析使用的是█。 */
    //         var str = component.properties.str;  
    //         var re = /\n/gi;  
    //         var newstr = str.replace(re, "█"); 
    //         console.log("###----newstr--->", newstr);

    //         if (component.properties["isFixed"]) {
    //             node.addComponent(cc.Mask);
    //             editBoxNode.name = "content";

    //             let scrollBarNode = new cc.Node();
    //             scrollBarNode.name = 'scrollBar';
    //             scrollBarNode.setContentSize(8, node.height);
    //             scrollBarNode.parent = node;
    //             scrollBarNode.position = cc.v3(node.width / 2 - scrollBarNode.width / 2, 0);

    //             let barNode = new cc.Node();
    //             barNode.name = 'bar';
    //             barNode.setContentSize(8, node.height);
    //             barNode.parent = scrollBarNode;

    //             let barSprite = barNode.addComponent(cc.Sprite);
    //             barSprite.spriteFrame = this.buildSpriteFrame(cc.color(170, 170, 170, 200), cc.rect(0, 0, 8, node.height));

    //             let widget = scrollBarNode.addComponent(cc.Widget);
    //             widget.right = 0;
    //             widget.top = 0;
    //             widget.bottom = 0;

    //             let scrollBar = scrollBarNode.addComponent(cc.Scrollbar);
    //             scrollBar.handle = barSprite;
    //             scrollBar.direction = cc.Scrollbar.Direction.VERTICAL;
    //             scrollBar.enableAutoHide = true;
    //             scrollBar.autoHideTime = 1;

    //             let scrollView = node.addComponent(cc.ScrollView);
    //             scrollView.vertical = true;
    //             scrollView.horizontal = false;
    //             scrollView.inertia = false;
    //             // scrollView.brake = 1;
    //             scrollView.elastic = false;
    //             // scrollView.bounceDuration = 0;
    //             scrollView.cancelInnerEvents = true;
    //             scrollView.content = editBoxNode;
    //             scrollView.verticalScrollBar = scrollBar;
    //         }
    //         await editCom.createEditBoxWithString(newstr, false, true);
    //         component.properties["rowSpacing"] && (editCom.setRowSpace(component.properties["rowSpacing"]));
    //         if (component.properties["isFixed"]) {
    //             node.getComponent(cc.ScrollView).scrollToTop();
    //         }
    //         editBoxNode.opacity = node.opacity;
    //         node.opacity = 255;
    //         resolve(node)
    //     });
    // }

    private async createFormula(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            // 先把 node 添加到节点树上，然后进行拍照截图，然后把 node 返回出去
            node.parent = parent;
            let texture = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(component.properties["url"]);
            node.addComponent(cc.Sprite);
            const sp = new cc.SpriteFrame(texture as cc.Texture2D);
            node.getComponent(cc.Sprite).spriteFrame = sp;
            node.getComponent(cc.Sprite).type = cc.Sprite.Type.SIMPLE;
            node.getComponent(cc.Sprite).sizeMode = cc.Sprite.SizeMode.CUSTOM;
            node.width = component.properties.width;
            node.height = component.properties.height;
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /**
     * 添加图形
     * @param component 
     * @param extraProp 
     * @param extra 
     * @param uuid 
     * @param parent 
     * @returns 
     */
    private async createSvg(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            node.active = true;
            node.parent = parent;
            node.addComponent(svg.RaphaelComponent);
            let com = node.getComponent(svg.RaphaelComponent);
            com.svgText = new cc.TextAsset();
            com.svgText.text = component.properties.svgText;
            com.onInit();
            let color = component.properties.strokeColor;
            let fillColor = component.properties.fillColor;
            let lineWidth = component.properties.lineWidth || 1;
            // 设置填充颜色
            com.changeFillColor(new cc.Color().fromHEX(fillColor));
            // 设置线的颜色
            com.changeStrokeColor(new cc.Color().fromHEX(color));
            // 设置线宽
            com.changeStrokeWidth(lineWidth);
            node.width = component.properties.width;
            node.height = component.properties.height;
            com.scheduleOnce(() => {
                let scaleX = component.properties.width / com.getShapeRect().width;
                let scaleY = component.properties.height / com.getShapeRect().height;
                com.setSvgScaleX(scaleX);
                com.setSvgScaleY(scaleY);
                node.active = component.properties.active;
            });
            let material = await this.getMaterialByPath("qte", "res/effects/svg_graph", uuid);
            node.on(cc.Node.EventType.COLOR_CHANGED, () => {
                let graphics = node.getChildByName("svg_group").getComponent(cc.Graphics);
                graphics.getMaterial(0).setProperty("alphaU", node.opacity / 255.0);
            }, null);
            node.on(cc.Node.EventType.POSITION_CHANGED, () => {
                let designSize = cc.view.getDesignResolutionSize();
                let v = cc.v2(2 * node.x / designSize.width, 2 * node.y / designSize.height);
                let graphics = node.getChildByName("svg_group").getComponent(cc.Graphics);
                graphics.getMaterial(0).setProperty("oPosition", v);
            }, null);

            let graphics = node.getChildByName("svg_group").getComponent(cc.Graphics);
            graphics.setMaterial(0, material);
            graphics.getMaterial(0).setProperty("alphaU", node.opacity / 255.0);

            // 翻转修订位置
            let designSize = cc.view.getDesignResolutionSize();
            let v = cc.v2(2 * node.x / designSize.width, 2 * node.y / designSize.height);
            graphics.getMaterial(0).setProperty("oPosition", v);
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /**
     * 根据路径获取材质
     * @param budleName 
     * @param path 
     * @returns 
     */
    private async getMaterialByPath(budleName: string, path: string, uuid: string): Promise<cc.Material> {
        return new Promise<cc.Material>((resolve, reject) => {
            let assetManager = QTEUtils.instance(QTEAssetsManager, this.symbol);
            assetManager.loadBundle(budleName, null, async (err, bundle) => {
                assetManager.loadRes(path, cc.EffectAsset, (err, res) => {
                    if (err) {
                        qte && qte.logCatBoth('load effect fail:', path);
                        let templateManager = QTEUtils.instance(QTETemplateManager, uuid);
                        let qteUrl = "";
                        if (templateManager && templateManager.resourceMaps && templateManager.resourceMaps['qte']) {
                            qteUrl = templateManager.resourceMaps['qte'];
                        }
                        if (qteUrl && qte.adapter && qte.adapter.assetsCheck) {
                            qte.adapter.assetsCheck({ path: qteUrl });
                        }
                        return;
                    }
                    resolve(cc.Material.create(res));
                },
                    bundle);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    private buildSpriteFrame(color: cc.Color, rect: cc.Rect) {
        let texture = new cc.Texture2D();
        let spriteFrame = new cc.SpriteFrame();
        texture.initWithData(new Uint8Array([color.r, color.g, color.b]), cc.Texture2D.PixelFormat.RGB888, 1, 1);
        spriteFrame.setTexture(texture);
        spriteFrame.setRect(rect);
        return spriteFrame;
    }

    public async createSpine(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            const node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            if (node.scale != 0) {
                node.scale = 1;
            }
            let texture = [];
            let textureName = [];
            let haveLoadTextureErr = false;
            for (const iterator of component.spineData.images) {
                let asset = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(iterator)
                if (asset instanceof cc.Texture2D) {
                    asset.setPremultiplyAlpha(true)
                } else {
                    haveLoadTextureErr = true;
                }
                texture.push(asset);
                textureName.push(iterator.substring(iterator.lastIndexOf("/") + 1));
            }

            let spineNode = new cc.Node("spine-node");
            spineNode.x = component.properties.offsetX;
            spineNode.y = component.properties.offsetY;
            spineNode.scaleX = component.properties.scaleX || 1;
            spineNode.scaleY = component.properties.scaleY || 1;
            spineNode.parent = node;
            let spine = spineNode.addComponent(sp.Skeleton);

            let atlasText = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(component.spineData.atlas);
            let skeletonJson = await QTEUtils.instance(QTEAssetsManager, uuid).getAsset(component.spineData.skeleton);
            if (haveLoadTextureErr || !atlasText || !skeletonJson) { // 加载资源出错
                if(qte && qte.logCatBoth){
                    qte.logCatBoth('createSpine ', "loadError");
                }
                return;
            }

            let asset = new sp.SkeletonData();
            asset.skeletonJson = (skeletonJson as cc.JsonAsset).json;
            asset.atlasText = (atlasText as cc.TextAsset).text;
            asset.textures = texture;
            // @ts-ignore
            asset.textureNames = textureName;
            spine.skeletonData = asset;

            spine.timeScale = component.properties.timeScale;
            let array: Array<string> = component.properties.animationList || component.properties.animList;
            QTEUtils.playSpineArr(spine, array, component.properties.loop)
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    public async createGroup(component: ComponentData, extraProp: any, extra: Object, uuid: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            let node = this.createNode(component, extraProp, extra, uuid);
            node.parent = parent;
            // 组合中的子元素不能添加动画和业务属性，所以第二个参数传 null
            const createList: QTEEntityNode[] = [];
            for (const iterator of component.subComponents) {
                let entityNode = await this.createEntityNode(iterator, null, uuid, node);
                // entityNode.parent = node;
                createList.push(entityNode);
                QTEUtils.instance(QTEEntityManager, uuid).addEntityNode(iterator.id, "", entityNode);
            }
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
}
