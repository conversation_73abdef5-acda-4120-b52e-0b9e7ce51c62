
if (CC_DEV || !window['_shark_cocos_bridge']) {
    window['_shark_cocos_bridge'] = {};
    window['_shark_cocos_bridge'].___assets = new Map<string, any>();
    window['_shark_cocos_bridge'].emit = () => {};
    window['_shark_cocos_bridge'].on = () => {};
    window['_shark_cocos_bridge'].once = () => {};
    window['_shark_cocos_bridge'].off = () => {};
    window['_shark_cocos_bridge'].once = () => {};
    window['_shark_cocos_bridge'].getCachedAsset = (url) => {
        return window['_shark_cocos_bridge'].___assets.get(url)
    };
    window['_shark_cocos_bridge'].setCachedAsset = (url, asset) => {
        window['_shark_cocos_bridge'].___assets.set(url, asset);
    };
    window['_shark_cocos_bridge'].noviceGuide = false;// 是否需要新手引导;
}

export const CocosBridge = window['_shark_cocos_bridge'];
