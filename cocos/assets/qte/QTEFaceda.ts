/*
 * @Author: your name
 * @Date: 2021-09-02 17:25:41
 * @LastEditTime: 2025-01-02 13:38:15
 * @LastEditors: gedengyang_v <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath     : /sdk/assets/qte/QTEFaceda.ts
 */
import ComponentUtils from "./assemble/componentUtils/ComponentUtils";
import ComponentFactory from "./assemble/core/ComponentFactory";
import { SpeakerComponent } from "./assemble/speaker/scripts/SpeakerComponent";
import QTEEntityNode from "./base/QTEEntityNode";
import QTETemplate from "./base/QTETemplate";
import QuoteNode, { ExportTemData } from "./base/QuoteNode";
import { UrlType } from "./core/loader/LoaderUtil";
import ResLoader from "./core/loader/ResLoader";
import ValueUtils from "./core/utils/ValueUtils";
import EngineExtension from "./EngineExtension";
import QTEAssetsManager from "./manager/QTEAssetsManager";
import QTEStateManager from "./manager/QTEStateManager";
import QTETemplateManager from "./manager/QTETemplateManager";
import { ComponentData } from "./qte-core-export";
import QTEStageFactory from "./QTEStageFactory";
import analysisLayer from "./ui/analysisLayer";
import QTEUtils from "./util/QTEUtils";
import { QTEHooks, QTE_LOGCAT_TYPE, TraceFinishType, TraceName } from "./vo/QTEEnum";
/**
 * 适配器,如需使用外部能力,则需要接入时传入
 */
class Adapter {
    /**
     * 播放音频接口
     * @clip cc.AudioClip对象
     * @loop 是否循环播放
     * @volume 音量
     * @callback 播放完成回调
     * @return number 返回当前播放音频的ID,用于停止/暂停播放音频
     */
    playAudio?: (clip: cc.AudioClip, loop: boolean, volume: number, callback: () => void) => number;
    /**
     * 停止音频播放接口
     * @audioItem:
     * {
     *  url: 音频链接地址
     *  nId: 音频播放时返回的ID
     *  nIsLoop: 循环
     * }
     */
    stopAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
    /**
     * 暂停音频播放接口
     * @audioItem:
     * {
     *  url: 音频链接地址
     *  nId: 音频播放时返回的ID
     *  nIsLoop: 循环
     * }
     */
    pauseAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
    /**
     * 恢复音频播放接口
     * @audioItem:
     * {
     *  url: 音频链接地址
     *  nId: 音频播放时返回的ID
     *  nIsLoop: 循环
     * }
     */
    resumeAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
    /**
     * 开始收音接口
     * @recordData  
     * {     ak: any; // 加密内容
     *       st: number; // 评分类型 1-单词 2-句子 3-段落 4-问题回答 5-复述 6-音标
     *       rt: string; // 传入的内容
     * }
     * @startRecordCb 开始收音的回调
     * @removeRecordCb 取消收音的回调
     * @startEndCb 收音完成的回调
     */
    startRecordAction?: (recordData: any, startRecordCb: any, removeRecordCb: any, startEndCb: any) => void;
    /**
     * 停止收音接口
     * @stopEndCb 停止收音的回调
     */
    stopRecordAction?: (stopEndCb?: any) => void;
    /**
     * 检查是否有录音权限的接口
     * @callback 回调函数
     */
    checkRecordAuthorityAction?: (callBack) => void;
    /**
     * toast提示的接口
     * @content 提示文本的字符串
     */
    toast: (content: string) => void;
    /** 上报日志 */
    logCat?: (logType, type, args) => void;
    /**
     * 写写卡--是否卡
     * @str 显示内容
     * @okCb 确认回调
     * @failCb 取消回调
     */
    showNoticeChoice?: (str: string, okCb: any, failCb: any, isChangeFocus?: boolean) => void;
    /**
     * 隐藏选项卡
     */
    hideNoticeChoice?: () => void;
    /**
     * 写写卡--检测摄像头权限 
     * 检测是否有权限 permission 0 有 1 没有 2 没申请
     */
    core_checkCameraPermission?: (cb: any) => void;
    /**
     * 写写卡--获取摄像头权限 
     */
    core_applyCameraPermission?: (cb: any) => void;
    /** 单选确定框 */
    showConfirm?: (content: string, okCb: any, cancelCb: any) => void;
    /** 取消 */
    hideConfirm?: () => void;
    /**
     * 写写卡--拍照上传
     */
    liveCameraUpload: (cb: any) => void;
    /**
     * 写写卡--隐藏键盘
     */
    mvpHideInputBar: (cb: any) => void;
    /**
     * 写写卡--显示键盘
     */
    mvpShowInputBar: (maxWordNum: number, placeholder: string, content: string, cb: any) => void;

    /**
     * @msg     : 调用fw的远程加载
     * @return   {*}
     */
    cocosLoadRemote: (path, type?, callback?) => void;
    // 获取小组学生信息
    getGroupUserInfo?: (callBack: Function) => void;

    // 切小题
    substart?: (data: number) => void;
    // 抢上台
    stuplatform?: (status: number) => void;

    // 加学分通用反馈
    corebusInteractResult?: (param: any) => void;
    // 点赞
    liveShowThumb?: (param: any) => void;

    // 请求互动题信令恢复
    interRecover?: () => void;
    // 缩小放大屏幕
    screenChange?: (bo: boolean) => void;
    // 获取当前用用户信息
    getUserInfo?: (callBack: Function) => void;
    // 上台
    hdAvatarLocate?(param: { num: number, positions: any[], type?: string }): void
    // 下台
    hdRemoveAvatar?(param: { uid: any[], type?: string }): void;

    // 蓝鲸业务打点
    tracePm?(name, param?): void;
    //妙笔生花action接口
    flowerAudioRecord?(param: { backgroundSoundPath?: string, type: number, timeMax: number, callBack?: Function }): void;
    //小鹿练字获取作答结果
    xlPracticeRecover?(callback: Function): void;
    //小鹿练字--拍照上传
    uploadImageAutoMark?(data: { type: 1 | 2, characters: string, tID?: string }, callback: Function): void;
    //小鹿练字--跳转学习报告页面
    jumpStudyReport?(): void;
    // 个性化纠音跳转到报告页并关闭容器
    closeGXHJYContainer?();
    /** 设置本地缓存 */
    setLocalData?: (key: string, value: any) => void;
    /** 获取本地缓存 */
    getLocalData?: (key: string) => any;
}


/**
 * QTE初始化公参
 */
export class QTEParams {
    /** 用户ID,暂未用到 */
    uid?: string;
    /** 操作系统类型,暂未用到 */
    os?: string;
}


/**
 * 题版创建的扩展参数
 */
class QTEOption {
    /** 
     * qte主键，用于区分多个qte对象同时存在的值，如果不传入则会默认生产一个uuid作为key值
     *  例如：
     *      直播业务中，可以传入pageId作为key，这样主讲端状态同步时就以该key值进行状态同步。
     *      如果不自定义key值会导致状态同步时，无法找到对应同步的qte实例。
     */
    qteKey?: string;
    /**
     * 资源读取路径。
     * 1. runtime下都统一放在assets下，资源路径统一加上assets
     * 2. spine动画资源[*.atlas、*.json、*.png]runtime下只需要assets目录下的相对路径，其余资源需要完整路径;
     */
    resourceMap?: any;
    // 恢复数据,暂未用到
    recoverData?: any;
    /** 配置信息 */
    settings?: QTE_SETTINGS;
    /** 操作模式 */
    openMode?: QTE_OPENMODE
}

export enum QTE_OPENMODE {
    SHOW_NORMAL = 0,
    SHOW_CORRECT = 1,
    SHOW_USER_ANSWER = 2,
    SHOW_TEACHER = 3            // 主讲端模式
}

export class QTE_SETTINGS {
    /** 重置按钮显示逻辑 */
    reset?: QTE_RESET_TYPE;
    submit?: QTE_SUBMIT_TYPE;
    playSubmitAni?: boolean;
    isGroupLoad?: boolean;
    isAnswerResultUI?: boolean;     // 是否展示小鳄鱼答案结果UI
}

/** 重置按钮显示逻辑枚举 */
export enum QTE_RESET_TYPE {
    /** 默认逻辑,提交后隐藏重置按钮 */
    DEFAULT = 0,
    /** 一直显示重置按钮 */
    SHOW = 1,
    /** 一直隐藏重置按钮 */
    HIDE = 2
}

/** 重置按钮显示逻辑枚举 */
export enum QTE_SUBMIT_TYPE {
    /** 默认逻辑*/
    DEFAULT = 0,
    /** 一直显示 */
    SHOW = 1,
    /** 一直隐藏 */
    HIDE = 2
}
export default class QTEFaceda {
    // 容器能力适配器
    public static adapter: Adapter;
    // QTE初始化公参
    public static qteParams: QTEParams;
    // 钩子函数
    public static qteHooks: QTEHooks;
    // 创建题版主键的数组
    public static uuidArr: string[] = [];

    /**
     * 框架初始化接口,需要先执行此接口才可以执行qte.create创建题版
     * @param adapter 传入外部能力的功能集
     * @param qteHooks  传入qte声明周期的钩子函数。
     * @param params qte配置信息,目前只支持配置重置按钮显示逻辑
     */

    // 创建全局组件数组
    public static fWComponentList: QTEEntityNode[] = [];
    public static _isInitPlay: boolean = false;
    public static analysisLayerNode: cc.Node = null;
    // 记录题干内容节点
    public static questionContentNode: cc.Node = null;

    public static initialize(adapter: Adapter, qteHooks?: QTEHooks, params?: QTEParams) {
        // 引擎代码注入修改
        EngineExtension.init();
        QTEFaceda.uuidArr = [];
        QTEFaceda.adapter = adapter;
        (window as any).qte.adapter = adapter;
        if (qteHooks) {
            QTEFaceda.qteHooks = qteHooks;
        }
        QTEFaceda.qteParams = params;
    }

    public static playfWComponents() {
        if (QTEFaceda._isInitPlay) {
            console.log("playfWComponents")
            return;
        }
        QTEFaceda._isInitPlay = true;
        console.log("playfWComponents", QTEFaceda.fWComponentList)
        for (let i = 0; i < QTEFaceda.fWComponentList.length; i++) {
            let data = QTEFaceda.fWComponentList[i].componentData;
            if (data.type == "specialComponent" && data.subType == "speaker") {
                if (QTEFaceda.fWComponentList[i] && QTEFaceda.fWComponentList[i].getChildByName("audio")) {
                    QTEFaceda.fWComponentList[i].getChildByName("audio").getComponent(SpeakerComponent).autoPlayVoice();
                }
            }
        }
    }
    public static resetPlayfWComponents() {
        QTEFaceda._isInitPlay = true;
        console.log("resetPlayfWComponents", QTEFaceda.fWComponentList)
        for (let i = 0; i < QTEFaceda.fWComponentList.length; i++) {
            let data = QTEFaceda.fWComponentList[i].componentData;
            if (data.type == "specialComponent" && data.subType == "speaker") {
                if (QTEFaceda.fWComponentList[i] && QTEFaceda.fWComponentList[i].getChildByName("audio")) {
                    let spc = QTEFaceda.fWComponentList[i].getChildByName("audio").getComponent(SpeakerComponent);
                    spc.resetVoice();
                    spc.autoPlayVoice();
                }
            }
        }
    }

    // 停用全局组件
    public static stopfWComponents() {
        for (let i = 0; i < QTEFaceda.fWComponentList.length; i++) {
            let data = QTEFaceda.fWComponentList[i].componentData
            if (data.type == "specialComponent" && data.subType == "speaker") {
                if (QTEFaceda.fWComponentList[i] && QTEFaceda.fWComponentList[i].getChildByName("audio")) {
                    QTEFaceda.fWComponentList[i].getChildByName("audio").getComponent(SpeakerComponent).stopPlayEnd();
                }
            }
        }
    }

    // 停止播放全局组件
    public static stopVoicefWComponents() {
        if (!QTEFaceda.fWComponentList) {
            return;
        }
        console.log("stopVoicefWComponents")
        for (let i = 0; i < QTEFaceda.fWComponentList.length; i++) {
            let data = QTEFaceda.fWComponentList[i].componentData
            if (data.type == "specialComponent" && data.subType == "speaker") {
                if (QTEFaceda.fWComponentList[i] && QTEFaceda.fWComponentList[i].getChildByName("audio")) {
                    QTEFaceda.fWComponentList[i].getChildByName("audio").getComponent(SpeakerComponent).stopVoice();
                }

            }
        }
        QTEFaceda.fWComponentList = [];
    }

    public static async fWAddComponent(data: ComponentData, parent: cc.Node): Promise<QTEEntityNode> {
        QTEFaceda._isInitPlay = false;
        console.log("fWAddComponent:", data)
        QTEFaceda.fWComponentList = [];
        return new Promise<QTEEntityNode>(async (resolve, reject) => {
            let node = await QTEUtils.instance(QTEStageFactory, "root").createEntityNode(data, null, qte.genUUID("qte"), parent);
            if (data.type == "specialComponent" && data.subType == "speaker") {
                if (node.getChildByName("audio")) {
                    node.getChildByName("audio").getComponent(SpeakerComponent).setCountParent(parent);
                    // node.getChildByName("audio").getComponent(SpeakerComponent).autoPlayVoice();
                }
            }
            QTEFaceda.fWComponentList.push(node);
            resolve(node);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });;
    }



    /** 获取题干音频时长 */
    public static async getfWComponentsTemData(data: ComponentData[]): Promise<ExportTemData> {
        return new Promise<ExportTemData>(async reslove => {
            let exportData: ExportTemData = {
                guideDuration: 0
            }
            let duration = 0;
            for (let i = 0; i < data.length; i++) {
                let component = data[i];
                if (component.subType == 'speaker') {
                    if (component.properties['autoPlay']) {
                        let audioUrl = component.properties['audioUrl'];
                        let audioRes = await QTEUtils.instance(QTEAssetsManager, qte.genUUID("qte")).loadResourceByUrl(audioUrl);
                        if (audioRes) {
                            let dura = (audioRes as cc.AudioClip)?.duration;
                            if (component.properties['countdown']) { // 3秒倒计时
                                dura += 3;
                            }
                            if (dura > duration) {
                                duration = dura;
                            }
                        }
                    }
                }

            }
            exportData.guideDuration = duration;
            console.log("exportData:", exportData);
            reslove(exportData);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /**
     * 创建题版接口
     * @param data 课件平台产出的题版数据
     * @param options 题版创建的参数，包括题版主键和资源索引map
     * @returns 返回题版创建好的显示节点
     */
    public static async create(data: any, options?: QTEOption): Promise<QuoteNode> {
        let artInfo = data;
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTEFaceda artInfo.template.bundleName = ${artInfo.template.bundleName}`);
        if (!artInfo.template.bundleName) {
            artInfo.template.bundleName = "sharkDragQuestion";
        }
        let url = artInfo.template.bundleName; //artInfo.template.bundleName; //
        let uuid = "";
        if (options && options.qteKey) {
            uuid = options.qteKey;
        }
        // 如果用户没有传自定义key，则生成默认uuid作为key值
        if (!uuid || uuid == "") {
            uuid = qte.genUUID("qte");
        }
        let templateManager = QTEUtils.instance(QTETemplateManager, uuid);
        if (options && options.settings) {
            for (let value in options.settings) {
                templateManager.settings[value] = options.settings[value];
            }
        }
        templateManager.data = artInfo;

        qte.getTemplateByKey = (key: string) => {
            return artInfo.template[key];
        }
        let td = Date.now()
        qte.tracePm(TraceName.updateBundleStart);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "#####---QTEFaceda---create--qteKey-->" + uuid);
        if (options && options.resourceMap) {
            // TODO:    优化下，一份
            qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE + "QTEFaceda 资源映射长度 ", Object.keys(options.resourceMap).length);
            qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE + "QTEFaceda 设置的资源映射表key ", JSON.stringify(options.resourceMap));
            templateManager.resourceMaps = ValueUtils.clone(options.resourceMap);
        }
        let assetManager = QTEUtils.instance(QTEAssetsManager, uuid);
        return new Promise<QuoteNode>(async (resolve, reject) => {
            assetManager.loadBundle(url, null, (err, bundle: cc.AssetManager.Bundle) => {
                if (err) {
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEFaceda ###--log---loadBundle  ERROE--->" + err);
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEFaceda ###--log---loadBundle  url==" + url);
                    qte.tracePm(TraceName.updateBundleEnd, { type: TraceFinishType.fail, error: err });
                    let checkBundleUrl = url;
                    if (templateManager.resourceMaps && templateManager.resourceMaps[checkBundleUrl]) {
                        checkBundleUrl = templateManager.resourceMaps[checkBundleUrl];
                    }
                    qte.adapter.assetsCheck && qte.adapter.assetsCheck({ path: checkBundleUrl });
                    reject(null);
                    return;
                }
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEFaceda ###-----loadBundel Succ--->" + url);
                assetManager.loadRes("prefabs/template", cc.Prefab, (errPrefab, prefab: cc.Prefab) => {
                    if (errPrefab) {
                        console.error("###--console---loadTemplatePrefab  ERROE--->", errPrefab);
                        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEFaceda ###--log---loadTemplatePrefab  ERROE--->" + errPrefab);
                        reject(null);
                        qte.tracePm(TraceName.updateBundleEnd, { type: TraceFinishType.fail, error: errPrefab });
                        let checkBundleUrl = url;
                        if (templateManager.resourceMaps && templateManager.resourceMaps[checkBundleUrl]) {
                            checkBundleUrl = templateManager.resourceMaps[checkBundleUrl];
                        }
                        qte.adapter.assetsCheck && qte.adapter.assetsCheck({ path: checkBundleUrl });
                        return;
                    }
                    qte.tracePm(TraceName.updateBundleEnd, { type: TraceFinishType.success, duration: Date.now() - td });
                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTEFaceda ###-----load prefabs/template Succ--->");
                    qte.openMode = options.openMode || qte.QTE_OPENMODE.SHOW_NORMAL;
                    let prefabNode: cc.Node = cc.instantiate(prefab);
                    let templateCom = prefabNode.getComponent(QTETemplate);
                    templateCom.UUID = uuid;
                    /** 设置当前作答模式 */
                    templateCom.openMode = qte.openMode;
                    /** 封装一层QTE题版的node给调用者 */
                    let node = new QuoteNode();
                    node.UUID = uuid;
                    prefabNode.name = "QTETemplate"
                    prefabNode.parent = node;
                    templateManager.quoteNode = node;
                    QTEFaceda.uuidArr.push(uuid);
                    resolve(node);
                }, bundle);
            });
        }).catch(err => {
            qte && qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `cocos promise error: ${err}`);
            return null;
        });
    }

    /**
     * qte框架完全销毁接口;
     */
    public static destroy() {
        // 销毁除QTESystem以外所有单例对象
        ComponentUtils.destroyInstance();
        ComponentFactory.destroyInstance();
        qte.destoryAll();
        QTEFaceda.uuidArr = [];
    }

    /**
     * 获取当前题版的显示数据,用来发送题目给另一个端恢复该题目;
     */
    public static getStates() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_GETSTATE, qte.instance(QTEStateManager).getStates());
        return qte.instance(QTEStateManager).getStates();
    }


    /**
     * 状态恢复
     * @param dataStates 状态恢复数据
     */
    public static recover(dataStates: any[]) {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_RECOVER, 'start');
        qte.instance(QTEStateManager).recover(dataStates);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_RECOVER_END, "end");
    }

    /**
     * 暂停
     */
    public static pause() { }

    /**
     * 恢复
     */
    public static resume() { }

    /**
     * 回到前台
     */
    public static page_show() {
        for (let i = 0; i < QTEFaceda.uuidArr.length; i++) {
            let templateManager = QTEUtils.instance(QTETemplateManager, QTEFaceda.uuidArr[i]);
            if (templateManager) {
                let templateNode = templateManager.quoteNode.getTemplate();
                if (templateNode) {
                    let templateCmpt = templateNode.getComponent(QTETemplate);
                    if (templateCmpt) {
                        templateCmpt.page_show();
                    }
                }
            }
        }
    }


    /**
     * 推到后台
     */
    public static page_hide() {
        for (let i = 0; i < QTEFaceda.uuidArr.length; i++) {
            let templateManager = QTEUtils.instance(QTETemplateManager, QTEFaceda.uuidArr[i]);
            if (templateManager) {
                let templateNode = templateManager.quoteNode.getTemplate();
                if (templateNode) {
                    let templateCmpt = templateNode.getComponent(QTETemplate);
                    if (templateCmpt) {
                        templateCmpt.page_hide();
                    }
                }
            }
        }
    }

    /**
     * 页面最小化
     */
    public static fold() {
        qte.log('qte.received.fold');
        for (let i = 0; i < QTEFaceda.uuidArr.length; i++) {
            let templateManager = QTEUtils.instance(QTETemplateManager, QTEFaceda.uuidArr[i]);
            if (templateManager) {
                qte.log('fold, templateManager ', templateManager ? '1' : '0');
                qte.log('fold, templateManager?.quoteNode ', templateManager?.quoteNode ? '1' : '0');
                let templateNode = templateManager?.quoteNode?.getTemplate();
                if (templateNode) {
                    let templateCmpt = templateNode.getComponent(QTETemplate);
                    if (templateCmpt) {
                        templateCmpt.fold();
                    }
                }
            }
        }
    }

    /**
     * 页面最大化
     */
    public static unfold() {
        qte.log('qte.received.unfold');
        for (let i = 0; i < QTEFaceda.uuidArr.length; i++) {
            let templateManager = QTEUtils.instance(QTETemplateManager, QTEFaceda.uuidArr[i]);
            if (templateManager) {
                qte.log('unfold, templateManager ', templateManager ? '1' : '0');
                qte.log('unfold, templateManager?.quoteNode ', templateManager?.quoteNode ? '1' : '0');
                let templateNode = templateManager?.quoteNode?.getTemplate();
                if (templateNode) {
                    let templateCmpt = templateNode.getComponent(QTETemplate);
                    if (templateCmpt) {
                        templateCmpt.unfold();
                    }
                }
            }
        }
    }
    static deletAnalysis() {
        qte.logCatBoth(
            QTE_LOGCAT_TYPE.QTE_LOG_COMMON,
            ` deletAnalysis, analysisLayer`,
        );
        if (this.analysisLayerNode) {
            this.analysisLayerNode.destroy();
            this.analysisLayerNode = null;
        }
    }

    public static async createAnalysis(data: qte.Analysis, node: cc.Node): Promise<void> {
        return new Promise<void>(async (resolve, reject) => {
            this.deletAnalysis();
            ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        'res/prefab/analysisLayer',
                        cc.Prefab,
                        (errRes, pb) => {
                            if (!errRes) {
                                let gNode = cc.instantiate(pb);
                                let alysisLayer = gNode.getComponent(analysisLayer);
                                alysisLayer.updateAnalysisLayer(data);
                                node.addChild(gNode);
                                this.analysisLayerNode = gNode;
                                resolve();
                            } else {
                                qte.logCatBoth(
                                    QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                                    ` analysisLayer error ${errRes}`,
                                );
                                reject(errRes);
                            }
                        },
                        bundle,
                    );
                } else {
                    qte.logCatBoth(
                        QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                        ` analysisLayer load [qte bundle] error ${err}`,
                    );
                    reject(err);
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    /**
     * 创建题干内容
     * @param data 题干内容数据
     * @param node 题干内容节点
     * @returns 返回题干内容节点
     */
    public static async createQuestionContent(data: any []): Promise<void> {
        
        return new Promise<void>(async (resolve, reject) => {
            let templateNode = null;
            for (let i = 0; i < QTEFaceda.uuidArr.length; i++) {
                let templateManager = QTEUtils.instance(QTETemplateManager, QTEFaceda.uuidArr[i]);
                if (templateManager) {

                    templateNode = templateManager?.quoteNode?.getTemplate();
                }
            }


            if (templateNode && templateNode.parent.getChildByName('questionContent')) {
                templateNode.parent.getChildByName('questionContent').destroy();
            }
            ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        'res/prefab/questionContent',
                        cc.Prefab,
                        (errRes, pb) => {
                            if (!errRes) {
                                let gNode = cc.instantiate(pb);
                                let questionContent = gNode.getComponent('QTEQuestionContent');
                                templateNode.parent.addChild(gNode, 2);
                                gNode.name = "questionContent";
                                questionContent.show(data);
                                this.questionContentNode = gNode;
                                resolve();
                            } else {
                                qte.logCatBoth(
                                    QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                                    ` questionContent error ${errRes}`,
                                );
                                reject(errRes);
                            }
                        },
                        bundle,
                    );
                } else {
                    qte.logCatBoth(
                        QTE_LOGCAT_TYPE.QTE_LOAD_ERROR,
                        ` questionContent load [qte bundle] error ${err}`,
                    );
                    reject(err);
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth('createQuestionContent cocos promise error:', err);
            return null;
        });
    }

}
