/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2022-02-15 17:27:26
 * @modify date 2022-02-15 17:27:26
 * @desc [description]
 */

import QTEComponent, { QTEComponentType } from "../base/QTEComponent";
import { SingleBase } from "../core/base/SingleBase";
import ValueUtils from "../core/utils/ValueUtils";
import QTEStateManager from "./QTEStateManager";

export default class QTECompManager extends SingleBase {

    /** 管理所有组件 */
    private _compArray: QTEComponent[];
    /** 管理所有组件的状态同步 */
    private _compStates: any = {};

    public initInstance(): void {
        this._compArray = [];
    }

    public addComp(comp: QTEComponent, type?: QTEComponentType) {
        comp.id = this._compArray.length;
        if (type) {
            comp.type = type;
        }
        this._compArray.push(comp);
    }

    public getCompById(id: number) {
        return this._compArray[id];
    }

    public getAllComp() {
        return this._compArray;
    }

    public getCompByType(type: QTEComponentType): QTEComponent[] {
        let tmp = [];
        this._compArray.forEach(comp => {
            if (type == comp.type) {
                tmp.push(comp);
            }
        });
        return tmp;
    }

    /**
     * 将状态同步到某个组件
     * @param key 
     * @param obj 
     * @returns 
     */
    public dispatch(key: string, obj) {
        let sep = key.split("_");
        if (!sep || sep.length < 2) {
            return;
        }
        let comp = this._compArray[sep[0]];
        comp && comp.watcher(sep[1], obj);
    }

    /**
     * 更新组件状态变化
     * @param qteId 
     * @param id 
     */
    public updateState(qteId: string, id: number) {
        let comp = this._compArray[id];
        for (const k in comp.state) {
            if (Object.prototype.hasOwnProperty.call(comp.state, k)) {
                let key = `${id}_${k}`;
                let tmp = ValueUtils.clone(comp.state[k]);
                if (JSON.stringify(this._compStates[key]) == JSON.stringify(tmp)) {
                    continue;
                }
                this._compStates[key] = tmp;
                let obj = {};
                obj[key] = tmp;
                qte.instance(QTEStateManager).updateState(qteId, "component", obj);
            }
        }
    }

}