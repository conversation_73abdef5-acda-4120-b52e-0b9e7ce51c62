/*
 * @Author: your name
 * @Date: 2021-06-18 16:30:39
 * @LastEditTime: 2021-07-20 22:34:55
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 */
/**
 * 实体管理类
 * <AUTHOR>
 * @date 2020-12-16
 */
import QTEBaseMeta from "../base/QTEBaseMeta";
import QTEComponent from "../base/QTEComponent";
import QTEEntityNode from "../base/QTEEntityNode";
import AnimationCmpt from "../components/AnimationCmpt";
import { SingleBase } from "../core/base/SingleBase";
import QTEStageFactory from "../QTEStageFactory";
import QTEUtils from "../util/QTEUtils";

export default class QTEEntityManager extends SingleBase {
    // 节点实体数组
    private _entitiesList: QTEEntityNode[];
    public get entitiesList(): QTEEntityNode[] {
        return this._entitiesList;
    }
    // 节点实体map， 以id为key
    private _entitiesIdMap: Map<string, QTEEntityNode>;
    public get entitiesIdMap(): Map<string, QTEEntityNode> {
        return this._entitiesIdMap;
    }
    // 节点实体map， 以tag为key
    private _entitiesTagMap: Map<string, QTEEntityNode[]>;
    public get entitiesTagMap(): Map<string, QTEEntityNode[]> {
        return this._entitiesTagMap;
    }

    public initInstance() {
        this._entitiesIdMap = new Map();
        this._entitiesTagMap = new Map();
        this._entitiesList = [];
    }

    public addEntityNode(id: string, tag: string, node: QTEEntityNode): void {
        this._entitiesIdMap.set(id, node);
        this._entitiesList.push(node);
        let list = this._entitiesTagMap.get(tag);
        if (list) {
            list.push(node);
        } else {
            list = [];
            list.push(node);
            this._entitiesTagMap.set(tag, list);
        }
        // TODO 状态同步给StateManager
        // qte.instance(QTEStateManager).updateState(node.qteUUID, id, {destroy: true});
    }

    public deleteEntityNode(id: string, tag: string) {
        this._entitiesIdMap.delete(id);
        let nIndex = this._entitiesList.findIndex((value)=>value.qid == id);
        if(nIndex >= 0){
            this._entitiesList.splice(nIndex, 1);
        }
        let list = this._entitiesTagMap.get(tag);
        if (list) {
            nIndex = list.findIndex((value)=>value.qid == id);
            if(nIndex >= 0){
                list.splice(nIndex, 1);
            } 
        }
    }

    public getEntityBySubType(subType: string): QTEEntityNode[] {
        let arr: QTEEntityNode[] = [];
        for (let i = 0; i < this._entitiesList.length; i++) {
            let node = this._entitiesList[i];
            if (node.componentData.subType && node.componentData.subType == subType) {
                arr.push(node);
            }
        }
        return arr;
    }

    public getEntityById(id: string): QTEEntityNode {
        return this.entitiesIdMap.get(id);
    }

    public getEntitiesByTag(tag: string): QTEEntityNode[] {
        return this.entitiesTagMap.get(tag);
    }

    public async cloneEntity(entity: QTEEntityNode, newId: string): Promise<QTEEntityNode> {
        // 复制节点,如果配置了自身动画数据,需要洗一下新复制出来的动画数据对应到自己身上.
        let componentData = qte.cloneValues(entity.componentData);
        if (componentData.extra["animationsForClient"]) {
            let animations = componentData.extra["animationsForClient"];
            for (let key in animations) {
                for(let fragmentKey in animations[key]) {
                    let fragment = animations[key][fragmentKey];
                    if (fragment.componentId === entity.qid) {
                        fragment.componentId =  newId;
                    }
                }
            }
        }

        // 添加全局动画 复制节点的动画数据数据
        let currentData = QTEUtils.getCurrentData(this.symbol)
        if (currentData["animationsForClient"]) {
            let animations = currentData["animationsForClient"];
            for (let key in animations) {
                let temp = [];
                for(let fragmentKey in animations[key]) {
                    let fragment = animations[key][fragmentKey];
                    if (fragment.componentId === entity.qid) {
                        let newData = qte.cloneValues(fragment);
                        newData.componentId = newId;
                        temp.push(newData);
                    }
                }
                animations[key] = animations[key].concat(temp);
            }
        }
        let node = await QTEUtils.instance(QTEStageFactory, entity.qteUUID).createEntityNode(componentData, entity.extraDataMap, entity.qteUUID, entity.parent);

        // 添加动画组件
        let animCmpt = entity.getComponent(AnimationCmpt);
        if (animCmpt) {
            let cloneAnim = node.addComponent(AnimationCmpt);
            cloneAnim.qteBaseMeta = animCmpt.qteBaseMeta;
        }
        // 添加自定义组件
        this.addComponents(node, entity.customCmpts, animCmpt.qteBaseMeta);
        return node;
    }

    private addComponents(
        entity: QTEEntityNode,
        cmpts: { cmpt: new () => cc.Component; options?: any }[],
        qteBaseMeta: QTEBaseMeta
    ): void {
        entity.customCmpts = cmpts;
        for (let cmpt of cmpts) {
            let c = entity.addComponent(cmpt.cmpt) as QTEComponent;
            c.options = cmpt.options;
            c.qteBaseMeta = qteBaseMeta;
        }
    }
}
