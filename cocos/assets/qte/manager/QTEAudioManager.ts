
import { SingleBase } from "../core/base/SingleBase";
import QTEAssetsManager from "./QTEAssetsManager";
import QTEUtils from "../util/QTEUtils";
import QTEAdapter from "../adapter/QTEAdapter";
export class AudioItem {
    // 声音路径，用作key
    public url: string = "";
    public nId: number = -1;
    public bIsLoop: boolean = false;
}

export default class QTEAudioManager extends SingleBase {

    private _adapter: QTEAdapter;

    public initInstance() {
        this._adapter = new QTEAdapter();
    }

    /**
     * 播放音效, 只提供给Animation调用
     * @param url 
     * @param loop 
     * @param callback 
     */
    playAudio(url: string | cc.AudioClip, loop: boolean = false, callback?: () => void): number {
        if (!url) {
            return -1;
        }
        let audio;
        if (url instanceof cc.AudioClip) {
            audio = url;
        } else {
            audio = this.getResource(url);
        }
        let ID = this.playAudioClip(audio, loop, 1, callback);
        console.log("@@@@@@----playAudio---->ID====>", ID);
        return ID;
    }

    /**
     * 停止音效, 只提供给Animation调用
     * @param ID 
     */
    stopAudio(ID: number) {
        console.log("@@@@@@----stopAudio---->ID====>", ID);
        this.stopAudioClipById(ID);
    }

    getResource(url: string) {
        return this.getRemoteResource(url)
    }

    getRemoteResource(url: string) {
        let asset = QTEUtils.instance(QTEAssetsManager, this.symbol).getAsset(url);
        if (!asset) {
            throw (`${url} 远程资源没有预加载`);
        }
        return asset
    }


    /*******************分割线 ***************************/
    /** 正在播放的音频 */
    private _arrAudioItem: AudioItem[] = [];

    /**
     * 播放cc.AudioClip
     * @param clip 
     * @param loop 
     * @param callback 
     * @return 
     */
    private playAudioClip(clip: cc.AudioClip, loop: boolean, volume: number = 1, callback?: () => void): number {
        let item = new AudioItem()
        item.url = clip.nativeUrl;
        item.bIsLoop = loop;
        item.nId = this._adapter.playAudio(clip, loop, volume, () => {
            if (callback) {
                callback();
            }
            if (!loop) {
                this.clearAudioItem(item);
            }
        });
        console.log("#####----playAudioClip---->",item);
        this._arrAudioItem.push(item);
        return item.nId;
    }
    /**
     *  暂停
     * @param audioId 
     */
    private stopAudioClip(audioItem: AudioItem) {
        console.log("#####----playAudioClip---1->",audioItem);
        console.log("#####----_arrAudioItem---1->",this._arrAudioItem);
        if (!this.findAudioItem(audioItem)) {
            return;
        }
        console.log("#####----playAudioClip--2-->",audioItem);
        this._adapter.stopAudio(audioItem);
    }

    private stopAudioClipById(id: number){
        let nIndex = this._arrAudioItem.findIndex(value=>value.nId == id);
        if (nIndex !== undefined){
            if (nIndex < this._arrAudioItem.length && nIndex >= 0) {
                this.stopAudioClip(this._arrAudioItem[nIndex]);
            }
        }
    }

    /**
     * 暂停
     * @param audioItem 
     */
    public pauseAudio(audioItem: AudioItem) {
        this._adapter.pauseAudio(audioItem);
    }

    public resumeAudio(audioItem: AudioItem) {
        this._adapter.resumeAudio(audioItem);
    }

    /**
     * 停止播放
     * @param soundURL 
     */
    public closeAudio(audioItem: AudioItem) {
        this.stopAudioClip(audioItem);
    }

    /** 暂停所有音频 */
    public pauseAllAudio() {
        for (let i = 0; i < this._arrAudioItem.length; i++) {
            let item = this._arrAudioItem[i];
            this.pauseAudio(item);
        }
    }
    /** 恢复所有音频 */
    public resumeAllAudio() {
        for (let i = 0; i < this._arrAudioItem.length; i++) {
            let item = this._arrAudioItem[i];
            this.resumeAudio(item);
        }
    }
    /** 停止所有音频 */
    public stopAllAudios() {
        for (let i = 0; i < this._arrAudioItem.length; i++) {
            let item = this._arrAudioItem[i];
            this.stopAudioClip(item);
        }
        this._arrAudioItem = [];
    }

    /**
     * 查找音频是
     * @param audioItem 
     * @returns 
     */
    private findAudioItem(audioItem: AudioItem): AudioItem | undefined {
        if (!this._arrAudioItem) {
            return undefined;
        }
        if (this._arrAudioItem.length == 0) {
            return undefined;
        }
        if (!audioItem) {
            return undefined;
        }
        let nIndex = this._arrAudioItem.find((val) => val.nId === audioItem.nId && val.url === audioItem.url);
        return nIndex;
    }

    /**
     * 清理指定audioItem
     * @param audioItem 
     */
    private clearAudioItem(audioItem: AudioItem) {
        let nIndex = this._arrAudioItem.findIndex((val) => val.nId === audioItem.nId && val.url === audioItem.url);
        if (nIndex) {
            this._arrAudioItem.splice(nIndex, 1);
        }
    }
}