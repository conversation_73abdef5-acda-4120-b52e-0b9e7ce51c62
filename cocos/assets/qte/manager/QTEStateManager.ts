import QTETemplate from "../base/QTETemplate";
import { SingleBase } from "../core/base/SingleBase";
import TimerUtils from "../core/utils/TimerUtil";
import ValueUtils from "../core/utils/ValueUtils";
import QTEFaceda from "../QTEFaceda";
import QTEUtils from "../util/QTEUtils";
import { QTE_LOGCAT_TYPE } from "../vo/QTEEnum";
import QTECompManager from "./QTECompManager";
import QTEEntityManager from "./QTEEntityManager";
import QTETemplateManager from "./QTETemplateManager";

/**
 * 状态同步管理类
 * <AUTHOR>
 * @date 2021-09-26
 * @desc 该类主要用作状态同步，目前状态同步类型分为三类：
 *      1. default : 默认的基础属性同步, key值一般为qid（组件id）；
 *      2. custom : 业务数据，自定义的同步数据；
 *      3. event : 特殊需要同步的属性，属于事件性型的，不需要持久化到数据中；
 */
export default class QTEStateManager extends SingleBase {

    // 当前的全量状态数据 Map<string, Map<string, any>>
    private _states: any;
    // 当前flow中的状态数据
    private _flowStates: any;
    // 当前flow定时器
    private _timerId: number;

    public initInstance() {
        this._states = {};
        this._flowStates = {};
    }

    /**
     * 更新状态数据
     * @param states 要更新的目标状态 
     * @param qteId 当前qte id
     * @param key 组件|属性 id
     * @param properties 属性数据
     */
    public updateState(qteId: string, key: string, properties: any) {
        this.addToStates(this._states, qteId, key, properties, true);
    }

    private addToStates(states: any, qteId: string, key: string, properties: any, flow: boolean) {
        // console.error("getStates",this._states)
        let qteState = states[qteId];
        if (qteState) {
            let templateState = qteState[key];
            if (templateState) {
                let flag = this.updateData(templateState, properties);
                flag && flow && this.addFlow(qteId, key, properties);
            } else {
                templateState = {};
                this.updateData(templateState, properties);
                flow && this.addFlow(qteId, key, properties);
                qteState[key] = templateState;
            }
        } else {
            qteState = {};
            let templateState = {};
            this.updateData(templateState, properties);
            flow && this.addFlow(qteId, key, properties);
            qteState[key] = templateState;
            states[qteId] = qteState;
        }
    }

    private updateData(states: any, properties: any): boolean {
        let flag = false;
        for (let k in properties) {
            if (states[k] != properties[k]) {
                if (k != "destroy") {
                    states[k] = properties[k];
                }
                flag = true;
            }
        }
        return flag
    }

    // 添加到发送策略流中
    private addFlow(qteId: string, key: string, properties: any) {
        this.addToStates(this._flowStates, qteId, key, properties, false);
        // 发送状态策略: 每200毫秒发送一次，或者有custom数据时同步一次
        if (key != "custom") {
            if (this._timerId != -1) {
                return;
            }
            this._timerId = TimerUtils.addTimer(() => {
                this.sendStateChange();
                this._timerId = -1;
            }, 200);
            return;
        }
        this.sendStateChange();
    }

    private sendStateChange() {
        let option = QTEFaceda.qteHooks;
        if (option && option.onStateChange) {
            option.onStateChange(null, ValueUtils.clone(this._flowStates));
        }
        this._flowStates = {};
        TimerUtils.clearTimer(this._timerId);
        this._timerId = -1;
    }

    /**
     * 清理状态
     * @param {string} qteId （可选）qte实例id，不传则删除所有状态数据
     * @returns {boolean} 是否删除成功
     */
    public clearState(qteId?: string): boolean {
        if (!qteId) {
            this._states = {};
            return true;
        }
        let qteState = this._states[qteId];
        if (qteState) {
            delete this._states[qteId];
            return true;
        }
        return false;
    }

    /**
     * 获取状态数据
     * @param {string} qteId （可选）qte实例id，不传则删除所有状态数据 
     * @returns {any} 状态数据
     */
    public getStates(qteId?: string) {
        if (qteId) {
            return ValueUtils.clone(this._states[qteId]);
        }
        return ValueUtils.clone(this._states);
    }

    /**
     * 增量更新
     */
    public syncStates(dataStates: any) {
        // TODO 
    }

    /**
     * 合并数据
     * @param {any[]} dataStates 多条状态数据，状态数组需要有顺序，后面的为最新
     * @returns {any} 返回一个最新
     */
    private mergeStates(dataStates: any[]): any {
        console.log("#--------dataStates---->", dataStates);
        // 第一条全量数据
        let tempQteKey: { qteId: string, data: any }[] = [];
        for (let i = 0; i < dataStates.length; i++) {
            let data = dataStates[i];
            for (let qteId in data) {
                let firstData = tempQteKey.find((val) => qteId === val.qteId);
                if (!firstData) {
                    tempQteKey.push({ qteId: qteId, data: data });
                } else {
                    // merge 
                    let state = firstData.data[qteId];
                    for (let key in data) {
                        let properties = data[key];
                        for (let k in properties) {
                            if (state[k]) {
                                if (state[k] != properties[k]) {
                                    for (let k1 in properties[k]) {
                                        if (state[k][k1]) {
                                            if (state[k][k1] !== properties[k][k1]) {
                                                state[k][k1] = properties[k][k1];
                                            }
                                        } else {
                                            state[k][k1] = properties[k][k1];
                                        }
                                    }
                                }
                            } else {
                                // 取不到此项,合并对象
                                state[k] != properties[k];
                                state = Object.assign(state, properties)
                            }
                        }
                    }
                }
            }
        }
        return tempQteKey;
    }

    ////////////////////// 恢复逻辑 ///////////////////////
    /**
     * 恢复数据
     * @param dataStates 
     * @param isAll 
     */
    public recover(dataStates: any[]) {
        this._states = {}
        qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_RECOVER, '#--AlldataStates---> start');
        let AlldataStates = this.mergeStates(dataStates);
        qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_RECOVER, '#--AlldataStates---> end');
        console.log("#--AlldataStates--->", AlldataStates)
        for (let i = 0; i < AlldataStates.length; i++) {
            let dataState = AlldataStates[i].data;
            for (let qteId in dataState) {
                let templateData = dataState[qteId];
                for (let key in templateData) {
                    let properties = templateData[key];
                    let qteState = this._states[qteId];
                    if (qteState) {
                        let templateState = qteState[key];
                        if (templateState) {
                            this.render(qteId, key, templateState, properties);
                        } else {
                            templateState = {};
                            this.render(qteId, key, templateState, properties);
                            qteState[key] = templateState;
                        }
                    } else {
                        qteState = {};
                        let templateState = {};
                        this.render(qteId, key, templateState, properties);
                        qteState[key] = templateState;
                        this._states[qteId] = qteState;
                    }
                }
            }
        }
        qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_RECOVER, 'QTESTartManager  recover ---> end');
    }

    private render(qteId: string, key: string, state: any, properties: any) {
        for (let k in properties) {
            if (state[k] != properties[k]) {
                if (k != "destroy") {
                    state[k] = properties[k];
                }
                if (key == "custom") {
                    this.renderCustom(qteId, k, properties[k]);
                } else if (key == "component") {
                    this.renderComponent(qteId, k, properties[k]);      
                } else {
                    this.renderBase(qteId, key, k, properties[k]);
                }
            }
        }
    }

    // 根据状态进行渲染逻辑
    private renderCustom(qteId: string, propertyKey: string, newproperties: any) {
        let qteTemp = QTEUtils.instance(QTETemplateManager, qteId);
        if (qteTemp.quoteNode) {
            let templateNode = qteTemp.quoteNode.getTemplate()
            if (templateNode) {
                let templateCmpt = templateNode.getComponent(QTETemplate);
                templateCmpt.state[propertyKey] = newproperties;
            }
        }
    }

    private renderBase(qteId: string, key: string, propertyKey: string, newproperties: any) {
        let entityManager = QTEUtils.instance(QTEEntityManager, qteId);
        let entity = entityManager.getEntityById(key);
        if (entity) {
            if (propertyKey == "destroy") {
                entity.destroy();
                return;
            }
            entity.render(propertyKey, newproperties);
        } else {
            // TODO: 创建新节点
        }
    }

    // 同步组件状态
    private renderComponent(qteId: string, propertyKey: string, newproperties: any) {
        let comManager = QTEUtils.instance(QTECompManager, qteId);
        comManager.dispatch(propertyKey, newproperties);
    }
}