
import TimerUtils from "../core/utils/TimerUtil";
import QTEAudioManager_V2 from "./QTEAudioManager";
import QTEEntityManager_V2 from "./QTEEntityManager";
import { AnimationData, SpineAnimationDetailData, TweenAnimationDetailData, AudioDetailData, TweenKeyFrameData } from "../qte-core-export";
import QTEUtils from "../util/QTEUtils";
import LogUtil from "../util/LogUtil";

/**
 * Animation
 * 解析并执行配置数据中的动画
 */
export default class QTEAnimation {
    // 存放正在播放的音频ID
    private _arrayAudio: Array<number> = [];
    // 存放正在播放的tween实例
    private _arrayTween: Array<cc.Tween> = [];
    // 存放播放tween的node的最终属性值
    private _mapTween: Map<cc.Node, any> = new Map();
    // 存放spine的播放结束后的动画序列
    private _mapSpine: Map<sp.Skeleton, any> = new Map();
    // 存放spine对象和对应回调函数的map
    // componentId, callback;
    private _mapSpCb: Map<string, () => void> = new Map();

    // 动画结束后的回调
    private _callback: () => void = () => { };
    // 动画的名字
    private _key: string;
    public get key(): string {
        return this._key;
    }

    private _uuid: string;

    /**
     * 需要播放的动画总数,用来判断动画是否播放完毕
     * 以防止动画间延时播放时,实例数组中未添加其实例
     * 感觉动画片段应该增加一个唯一id,方便控制
     */
    private _animCount: number = 0;

    /** 定时器表 */
    private _arrTimer = [];

    /** 能否被打断 */
    private _bIsAllowBreak = false;
    public set AllBreak(value: boolean) {
        this._bIsAllowBreak = value;
    }
    public get AllBreak(): boolean {
        return this._bIsAllowBreak;
    }
    /** 是否加速播放 */
    isAddSpeedPlay: boolean = false;

    constructor(uuid: string) {
        this._uuid = uuid;
        // 初始化
        this._arrayAudio = [];
        this._arrayTween = [];
        this._mapTween = new Map();
        this._mapSpine = new Map();
        this._mapSpCb = new Map();
        this._callback = () => { };
        this._key = '';
        this._animCount = 0;
        this._arrTimer = [];
    }

    private addTimer(timerId: number) {
        this._arrTimer.push(timerId);
    }

    private clearTimer(timerId: number) {
        let index = this._arrTimer.indexOf(timerId);
        if (index !== -1) {
            this._arrTimer.splice(index, 1);
            TimerUtils.clearTimer(timerId);
        }
    }

    private clearAllTimer() {
        for (const iterator of this._arrTimer) {
            TimerUtils.clearTimer(iterator);
        }
        this._arrTimer = [];
    }

    /**
     * 动作初始化
     * @param data 动作数据
     * @param key 动作名称
     * @param cb 回调函数
     */
    init(data: AnimationData[], key: string, cb: () => void, uuid: string) {
        this._key = key;
        this._callback = cb;

        for (const iterator of data) {
            this._animCount++;
            if (iterator.type === "audio") {
                this.playAudio(iterator, uuid)
            } else if (iterator.type === "tween") {
                this.playTween(iterator, uuid);
            } else if (iterator.type === "spine") {
                this.playSpine(iterator, uuid)
            }
        }
    }

    private playAudio(data: AudioDetailData, uuid: string) {
        let deTime = data.time;
        if (this.isAddSpeedPlay) {
            deTime = 0.01 * deTime;
        }
        let node = null;
        let entitiesList = QTEUtils.instance(QTEEntityManager_V2, uuid).entitiesList;
        if (entitiesList) {
            node = entitiesList[0];
        }
        if (!cc.isValid(node, true)) {
            return;
        }
        
        let timerId = TimerUtils.addTimer(() => {
            this.clearTimer(timerId);
            if (!cc.isValid(node, true)) {
                return;
            }
            let audioID = QTEUtils.instance(QTEAudioManager_V2, uuid).playAudio(data.audioUrl, false, () => {
                if (!this._arrayAudio) {
                    return;
                }
                this.stopAudio(audioID);
            });
            this._arrayAudio.push(audioID);
            if (this.isAddSpeedPlay) {
                this.stopAudio(audioID);
            }
        }, deTime)

        this.addTimer(timerId);
    }

    /**
     * 停止单个音频
     * @param audioID 
     */
    private stopAudio(audioID?: number) {
        if (audioID != undefined) {
            this._animCount--;
            let index = this._arrayAudio.indexOf(audioID);
            QTEUtils.instance(QTEAudioManager_V2, this._uuid).stopAudio(audioID);
            if (index !== -1) {
                this._arrayAudio.splice(index, 1);
            }
            this.checkOver();
        }
    }

    /**
     * 停止所有音频
     */
    private stopAllAudio() {
        for (const iterator of this._arrayAudio) {
            QTEUtils.instance(QTEAudioManager_V2, this._uuid).stopAudio(iterator);
        }
        this._arrayAudio = [];
    }

    /**
     * 播放tween动画
     * @param data 
     */
    private playTween(data: TweenAnimationDetailData, uuid: string) {
        let node = QTEUtils.instance(QTEEntityManager_V2, uuid).getEntityById(data.componentId);
        if (!cc.isValid(node, true)) {
            return;
        }
        let sequence = data.data;
        data.repeat = data.repeat || 0;

        let property = this._mapTween.get(node);
        if (!property) {
            property = {};
        }
        for (let i = data.repeat; i >= 0; i--) {
            for (const iterator of sequence) {
                property = this.finalProperty(property, iterator, node);
            }
        }
        this._mapTween.set(node, property);
        let deTime = data.time;
        if (this.isAddSpeedPlay) {
            deTime = 0.01 * deTime;
        }
        let timerId = TimerUtils.addTimer(() => {
            if (!this._arrayTween || !cc.isValid(node, true)) {
                return;
            }
            this.clearTimer(timerId);
            let tween = cc.tween(node);
            this._arrayTween.push(tween);
            if (sequence.length <= 0) {
                this.stopTween(tween);
                return;
            }

            for (const iterator of sequence) {
                if (this.isAddSpeedPlay) {
                    iterator.time = 0.02;
                }
                if (iterator.type == "to") {
                    if (iterator.opts) {
                        tween.to(iterator.time, iterator.props, {
                            easing: iterator.opts
                        });
                    } else {
                        tween.to(iterator.time, iterator.props);
                    }
                } else if (iterator.opts) {
                    tween.by(iterator.time, iterator.props, {
                        easing: iterator.opts
                    });
                } else {
                    tween.by(iterator.time, iterator.props);
                }
            }
            tween.repeat(data.repeat, tween);
            tween.call(() => {
                this.stopTween(tween);
            });
            tween.start();
        }, deTime);
        // 存储timer句柄
        this.addTimer(timerId);
    }

    private finalProperty(property: Object, animation: TweenKeyFrameData, node: cc.Node) {
        if (animation.type === "by") {
            for (const key in animation.props) {
                if (!property.hasOwnProperty(key)) {
                    property[key] = node[key];
                }
                property[key] += animation.props[key];
            }
            return property;
        }
        if (animation.type === "to") {
            for (const key in animation.props) {
                if (!property.hasOwnProperty(key)) {
                    property[key] = node[key];
                }
                property[key] = animation.props[key];
            }
            return property;
        }
    }

    private stopTween(tween?: cc.Tween) {
        if (tween) {
            this._animCount--;
            // 同一个动画两个tween ,第二个有延迟时间,stopTween 不会设置为结果位置
            // let node = tween['_target'];
            // if (this._mapTween.has(node)) {
            //     this._mapTween.delete(node);
            // }
            let node = tween['_target'];
            this.fixNodeOpacity(node);
            let index = this._arrayTween.indexOf(tween);
            this._arrayTween.splice(index, 1);
            this.checkOver();
        }
    }

    fixNodeOpacity(node: cc.Node) {
        if (!cc.isValid(node, true)) {
            return;
        }
        let sprite = node.getComponent(cc.Sprite);
        if (sprite && sprite.srcBlendFactor == cc.macro.BlendFactor.ONE) {
            this.updateColor(node);
        }
        for (let i = 0; i < node.children.length; i++) {
            const childNode = node.children[i];
            this.fixNodeOpacity(childNode);
        }
    }
    updateColor(node: cc.Node) {
        let color = new cc.Color(node.color.r, node.color.g, node.color.b, node.color.a);
        let newB = node.color.b + 1;
        if (newB > 255) {
            newB = node.color.b - 1;
        }
        node.color = new cc.Color(node.color.r, node.color.g, newB, node.color.a);
        qte.TimerUtils.addTimer(() => {
            if (!cc.isValid(node, true)) {
                return;
            }
            node.color = color;
        }, 0);
    }

    private stopAllTween() {
        for (const iterator of this._arrayTween) {
            iterator.stop();
        }
        this._arrayTween = [];

        this._mapTween.forEach((property, node) => {
            // cc.Tween.stopAllByTarget(node);
            for (const key in property) {
                node[key] = property[key];
            }
        });
        this._mapTween.clear();
    }

    private playSpine(data: SpineAnimationDetailData, uuid: string) {
        let node = QTEUtils.instance(QTEEntityManager_V2, uuid).getEntityById(data.componentId);
        if (!cc.isValid(node, true)) {
            return;
        }

        let animation = data.data;
        let deTime = data.time;
        if (this.isAddSpeedPlay) {
            deTime = 0.01 * deTime;
        }
        let timerId = TimerUtils.addTimer(() => {
            this.clearTimer(timerId);
            if (!cc.isValid(node, true)) {
                return;
            }
            let spine = node.getChildByName("spine-node").getComponent(sp.Skeleton);
            spine.timeScale = animation.timeScale;
            if (this.isAddSpeedPlay) {
                spine.timeScale = 10;
            }
            // FIXME: 用来处理有读题后动画时，主讲端播放完后同步到学生端状态，学生无法显示读题后动画
            spine.node.parent.scale = 1;
            if (animation.animation != "") {
                // 删除该node身上sp的回调
                if (!animation.loop) {
                    // @ts-ignore
                    if (spine._listener && spine._listener.complete) {
                        // @ts-ignore
                        spine._listener.complete();
                        // @ts-ignore
                    } else if (spine && spine._completeListener) {
                        // @ts-ignore
                        spine._completeListener();
                    }
                }
                spine.setAnimation(0, animation.animation, animation.loop);
                if (!animation.loop) {
                    // 添加新回调
                    this._mapSpCb.set(data.componentId, () => {
                        this.stopSpine(spine);
                    })
                    this._mapSpine.set(spine, animation.after);
                    spine.setCompleteListener((trackEntry, loopCount) => {
                        // 播放完成删除该node身上回调
                        this.delSpineCb(data.componentId);
                    });
                }
            } else {
                this.spineStop(spine)
                this._mapSpine.set(spine, animation.after);
                this.stopSpine(spine);
            }
        }, deTime);
        this.addTimer(timerId);
    }

    /**
     * 删除组件身上的sp回调函数
     * @param componentId 
     */
    private delSpineCb(componentId: string): void {
        const _cb = this._mapSpCb.get(componentId);
        if (_cb) {
            _cb();
            this._mapSpCb.delete(componentId);
        }
    }

    /**
     * stop当前spine,播放spine的after
     * @param spine 
     */
    private stopSpine(spine: sp.Skeleton) {
        this._animCount--;
        this.spineCompleteListener(spine, this._mapSpine.get(spine));
        // 为啥要把spine从表里删除？
        // this._mapSpine.delete(spine);
        this.checkOver();
    }

    /**
     * spine停止
     */
    private stopAllSpine(): void {
        this._mapSpine.forEach((value, key) => {
            this.spineStop(key);
        })
        this._mapSpine.clear();
        this._mapSpCb.clear();
    }

    /**
     * spine清除
     * @param spine 
     * @param data 
     */
    private spineStop(spine: sp.Skeleton) {
        // @ts-ignore
        if (spine._listener && spine._listener.complete) {
            // @ts-ignore
            spine._listener.complete()
        }
        spine["index"] = 0;
        spine.animation = null;
        spine.loop = false;
    }

    private spineCompleteListener(spine: sp.Skeleton, data) {
        spine.timeScale = data.timeScale || data.endTimeScale;
        let array: Array<string> = data.animationList || data.animList;
        if (array.length == 0) {
            return;
        }
        QTEUtils.playSpineArr(spine, array, data.loop);
    }

    private checkOver() {
        if (this._animCount == 0 &&
            this._arrayAudio.length == 0 &&
            this._arrayTween.length == 0) {
            this.over();
        }
    }

    private stopAll() {
        this.stopAllAudio();
        this.stopAllTween();
        this.stopAllSpine();
        this.clearAllTimer();
    }

    stop() {
        this.stopAll();
        this.over();
    }

    over() {
        if (this._callback) {
            this._callback();
        }
        this.destroy();
    }

    private destroy() {
        this._arrayAudio = [];
        this._arrayTween = [];
        this._mapTween.clear()
        this._mapSpine.clear()
        this._mapSpCb.clear()
        this._callback = null;
        this._key = '';
        this._animCount = 0;
        this._arrTimer = [];
    }
}