import { SingleBase } from "../core/base/SingleBase";
import ResLoader from "../core/loader/ResLoader";
import ValueUtils from "../core/utils/ValueUtils";
import QTEUtils from "../util/QTEUtils";
import { QTE_LOGCAT_TYPE } from "../vo/QTEEnum";
import { QTECacheAssetManager } from "./QTECacheAssetManager";
import QTETemplateManager from "./QTETemplateManager";

export default class QTEAssetsManager extends SingleBase {
    private assetsReloadMap: Map<string, number> = new Map<string, number>();
    /**
     * 加载过的资源地址,远端 & 本地路径
     */
    private _loadUrls: string[] = [];
    private _transloadUrls: string[] = [];
    private _loadResListCb: () => void = () => { };

    // spine的纹理url数组
    private _arraySpineTextures: Array<string> = [];

    /*********** *********************************************/
    //资源引用数组
    // private _assetsRefArray: cc.Asset[] = [];

    oninitInstance() {
        this.assetsReloadMap = new Map<string, number>();
    }

    onDestoryInstance() {
        this.clearAssets();
    }

    /**
     * 通用资源加载接口（包括本地资源、网络资源和远程资源）
     * @param {string} path 资源路径，可以是本地资源、网络资源和远程资源
     * @param {cc.Asset | Record<string, any>} options 资源类型 | 远程资源可选参数
     * @param {(err, res) => void} onComplete 加载完成回调
     * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。
     * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度
     */
    loadRes(
        path: string,
        options: typeof cc.Asset | Record<string, any>,
        onComplete: (err, res: any) => void,
        bundle?: cc.AssetManager.Bundle | string,
        onProgress?: (
            finish: number,
            total: number,
            item: cc.AssetManager.RequestItem
        ) => void
    ) {
        let requestUrl = this.transRequestUrl(path);
        let asset = this.getAsset(path)
        if (asset) {
            onComplete(null, asset);
            this._loadUrls.push(path);
            console.log("#====_loadUrls===1===>", this._loadUrls)
            this._transloadUrls.push(requestUrl);
            QTECacheAssetManager.getInstance().setAsset(requestUrl, asset);
            return;
        }
        let complete = (err, res: cc.Asset) => {
            if (err) {
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, {
                    data: JSON.stringify({
                        err: err,
                        path: path,
                        'options': options,
                        bundle: bundle
                    })
                })
            }
            onComplete(err, res);
            // path ---> nativeUrl
            // this.addLocalRes2ResourceMaps(requestUrl, res.nativeUrl);
            if (!(res instanceof cc.Prefab)) {
                this._loadUrls.push(path);
                console.log("#====_loadUrls===2===>", this._loadUrls)
                this._transloadUrls.push(requestUrl);
                QTECacheAssetManager.getInstance().setAsset(requestUrl, res);
            }
        };
        ResLoader.loadRes(requestUrl, options, complete, bundle, null, onProgress);
    }

    /**
     * 
     * @param nameOrUrl 
     * @param options 
     * @param onComplete 
     * @returns 
     */
    loadBundle(
        nameOrUrl: string,
        options: Record<string, any>,
        onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void
    ): void {
        console.log('QTEAssetsManager loadBundle1', nameOrUrl);
        nameOrUrl = QTEUtils.instance(QTEAssetsManager, this.symbol).transRequestUrl(nameOrUrl);
        console.log('QTEAssetsManager loadBundle2', nameOrUrl);
        ResLoader.loadBundle(nameOrUrl, options, onComplete);
    }
    /****************************************************** */

    clearAssets() {
        QTECacheAssetManager.getInstance().releaseingResourceList(this._transloadUrls);
    }
    clearAssetByList(list: string[]) {
        QTECacheAssetManager.getInstance().releaseingResourceList(list);
    }

    /**
     * 增加资源加载接口
     * @param url 资源地址
     * @param type 资源类型
     * @param onComplete 加载成功回调
     */
    public loadRemote(url: string, type: typeof cc.Asset, onComplete: (err, res) => void) {
        let asset = this.getAsset(url);
        qte.log("单独加载", asset)
        if (asset && asset.nativeUrl) {
            onComplete(null, asset);
            return;
        }
        let requestUrl = this.transRequestUrl(url);
        qte.log("单独加载", url)
        ResLoader.loadRes(requestUrl, null, (e, a) => {
            if (e) {
                console.error(url, e);
                onComplete(e, null);
            } else {
                if (a instanceof cc.Texture2D) {
                    if (this._arraySpineTextures.indexOf(requestUrl) == -1) {
                        // asset.setPremultiplyAlpha(true);
                    }
                }
                this._loadUrls.push(url);
                this._transloadUrls.push(requestUrl);
                qte.log("单独加载完成", a)
                QTECacheAssetManager.getInstance().setAsset(requestUrl, a);
                onComplete(e, a);
            }
        });
    }

    getAsset(url: string) {
        let requestUrl = this.transRequestUrl(url);
        let asset = QTECacheAssetManager.getInstance().getAsset(requestUrl)
        if (!asset) {
            qte.warn(`${url} 资源不存在`);
            console.warn(QTECacheAssetManager.getInstance().getAssetsCache());
        }
        // 图片资源packable 如果是true,更改图片uv坐标的的材质不生效
        if (asset instanceof cc.Texture2D) {
            asset.packable = false;
        }
        return asset;
    }

    private getSpineTextures(uuid: string) {
        let components = QTEUtils.getCurrentData(uuid).components;
        for (const iterator of components) {
            if (iterator.type === "spine") {
                // this._arraySpineTextures.push.apply(this._arraySpineTextures, iterator.spineData.images);
                for (const iteratorImg of iterator.spineData.images) {
                    if (this._arraySpineTextures.indexOf(iteratorImg) == -1) {
                        this._arraySpineTextures.push(iteratorImg);
                    }
                }
            }
        }
    }

    async loadResourceList(urls: string[], callBack: () => void, uuid: string) {
        this._loadUrls = ValueUtils.clone(urls);
        this._transloadUrls = [];
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--loadResourceList---:" + JSON.stringify(this._loadUrls))
        console.log("#====_loadUrls===0===>", this._loadUrls)
        this.getSpineTextures(uuid);
        this._loadResListCb = callBack;
        qte.warn("V2===", urls.length);
        if (urls.length > 0) {
            urls.map((url) => {
                this.assetsReloadMap.set(url, 0);
                this.loadResource(url);
            });
        } else {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "loadResEnd调用")
            this.loadResEnd();
        }
    }

    /**
     * 资源下载回调,当下载表和资源表长度相同时,回调QteSystem
     */
    private loadResEnd(): void {
        for (let i = 0; i < this._loadUrls.length; i++) {
            let requestUrl = this.transRequestUrl(this._loadUrls[i]);
            if (!QTECacheAssetManager.getInstance().getAsset(requestUrl)) {
                // console.log("loadResEnd 没加载完需要等会",this._loadUrls[i] )
                // console.log("loadResEnd 没加载完需要等会 ,getLoadObj",QTECacheAssetManager.getInstance().getLoadObj(this._loadUrls[i]) )
                return;
            }
        }
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--loadResourceEnd---")
        if (this._loadResListCb) {
            this._loadResListCb();
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "loadResEnd 加载完了 回调了");
            this._loadResListCb = null;
        }
    }

    getNewUrl(requestUrl) {
        //file:///storage/emulated/0/Android/data/com.zuoyebang.airclass/files/data/runtime/app/ffbbdc8bb98e02d10e6ceeb44ffd616c/0/assets/cocosversion/jianghuabox.atlas
        // jianghuabox.atlas
        let templateManager = QTEUtils.instance(QTETemplateManager, this.symbol);
        let obj = templateManager.resourceMaps;
        let name1 = this.getName(requestUrl);
        for (let key in obj) {
            let name = this.getName(obj[key]);
            if (name == name1 && obj[key] != requestUrl) {
                return true;
            }
        }
        return false;
    }


    getName(requestUrl) {
        let names = requestUrl.split("/");
        let name = names[names.length - 1].split(".")
        // jianghuabox
        return name[0];
    }

    async loadResByUrl(url: string) {
        return new Promise<cc.Asset>(reslove => {
            ResLoader.loadRes(
                url,
                null,
                (err, asset) => {
                    if (err) {
                        reslove(null);
                    } else {
                        reslove(asset);
                    }
                }
            );
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    async loadResourceByUrl(url): Promise<cc.Asset> {
        return new Promise<cc.Asset>(reslove => {
            let requestUrl = this.transRequestUrl(url);
            let asset: cc.Asset = QTECacheAssetManager.getInstance().getAsset(requestUrl);
            if (asset) {
                reslove(asset);
            }

            ResLoader.loadRes(
                requestUrl,
                null,
                (err, asset) => {
                    if (err) {
                        reslove(null);
                    } else {
                        reslove(asset);
                    }
                }
            );
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /** 
     * 兜底线上 
     **/
    loadResourceByOnLine(url) {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--loadResourceFail--loadResourceByOnLine-:" + url);
        let requestUrl = this.transRequestUrl(url);
        const cachedAsset = QTECacheAssetManager.getInstance().getAsset(requestUrl);
        if (cachedAsset) {
            this._transloadUrls.push(requestUrl);
            QTECacheAssetManager.getInstance().setAsset(requestUrl, cachedAsset);
            console.log("loadResourceByOnLine加载完成了11", url);
            this.loadResEnd();
            return;
        }
        console.warn("loadResourceByOnLine-ResLoad From Cocos ONLINE: " + url, "进入兜底");
        ResLoader.loadRes(
            url,
            null,
            (err, asset) => {
                if (err) {
                    let dataReloadCount = this.assetsReloadMap.get(url);
                    qte.error("## loadResourceByOnLine loadResource error =====>", { url, dataReloadCount, err });
                    qte.logCatBoth("## loadResourceByOnLine loadResource error =====>", { url, dataReloadCount });
                    if (dataReloadCount < 2) {
                        this.assetsReloadMap.set(url, dataReloadCount + 1);
                        qte.error("### loadResourceByOnLine loadResource retry =====>", { url, dataReloadCount, err });
                        qte.logCatBoth("### loadResourceByOnLine loadResource retry =====>", { url, dataReloadCount });
                        return this.loadResourceByOnLine(url);
                    } else {
                        qte.error("### loadResourceByOnLine loadResource fail =====>", { url, dataReloadCount });
                        qte.logCatBoth("### loadResourceByOnLine loadResource fail =====>", { url, dataReloadCount });
                        this._transloadUrls.push(requestUrl);
                        QTECacheAssetManager.getInstance().setAsset(requestUrl, {});
                        this.loadResEnd();
                    }
                } else {
                    if (asset instanceof cc.Texture2D) {
                        if (this._arraySpineTextures.indexOf(url) == -1) {
                            if (asset.width != 0 && asset.height != 0) {
                                // asset.setPremultiplyAlpha(true);
                            }
                        }
                    }
                    this._transloadUrls.push(requestUrl);
                    QTECacheAssetManager.getInstance().setAsset(requestUrl, asset);
                    this.loadResEnd();
                }
            }
        );
    }

    loadResource(url) {
        let requestUrl = this.transRequestUrl(url);
        const cachedAsset = QTECacheAssetManager.getInstance().getAsset(requestUrl);
        if (cachedAsset) {
            QTECacheAssetManager.getInstance().setAsset(requestUrl, cachedAsset);
            console.log("loadResource cached", url);
            this._transloadUrls.push(requestUrl);
            this.loadResEnd();
            return;
        }

        // if (this.getNewUrl(requestUrl)) {
        //     // console.log("#######################-->", requestUrl)
        //     // file:///storage/emulated/0/Android/data/com.zuoyebang.airclass/files/data/runtime/app/ffbbdc8bb98e02d10e6ceeb44ffd616c/0/assets/cocosversion/BEE.atlas
        //     let name = this.getName(requestUrl);
        //     let names = requestUrl.split(".")
        //     let str = "assets/cocosversion/" + name + "." + names[names.length - 1];
        //     console.log("####----str---->", str);
        //     requestUrl = str;
        // }

        // // 模拟第一次资源失败
        // let dataReloadCount1 = this.assetsReloadMap.get(url);
        // if (dataReloadCount1 >= 0){
        //     requestUrl = "https://yayaa.cdnjtzy.com/%E7%86%8A%E7%8C%AB%E7%AC%91%E5%A3%B0-%E9%95%BF%2B%E9%BC%93%E6%8E%8C-a51b7d908dbb5187be25588c02fbacb6.mp3"
        // }

        qte.warn("ResLoad From Cocos: " + requestUrl);
        ResLoader.loadRes(
            requestUrl,
            null,
            (err, asset) => {
                if (err) {
                    let dataReloadCount = this.assetsReloadMap.get(url);
                    qte.error("## loadResource error =====>", { url, dataReloadCount });
                    qte.logCatBoth("## loadResource error =====>", { url, dataReloadCount });
                    if (qte.adapter && qte.adapter.assetsCheck) {
                        qte.adapter.assetsCheck({ path: requestUrl,isAssest:true})
                    }
                    if (dataReloadCount < 2) {
                        this.assetsReloadMap.set(url, dataReloadCount + 1);
                        qte.error("### loadResource loadResourceByOnLine =====>", { url, dataReloadCount })
                        qte.logCatBoth("### loadResource loadResourceByOnLine =====>", { url, dataReloadCount })
                        return this.loadResourceByOnLine(url);
                    } else {
                        qte.error("### loadResource fail =====>", { url, dataReloadCount })
                        qte.logCatBoth("### loadResource fail =====>", { url, dataReloadCount })
                        this._transloadUrls.push(requestUrl);
                        QTECacheAssetManager.getInstance().setAsset(requestUrl, {});
                        this.loadResEnd();
                    }
                } else {
                    qte.log("## loadResource success url=" + url)
                    if (asset instanceof cc.Texture2D) {
                        if (this._arraySpineTextures.indexOf(url) == -1) {
                            if (asset.width != 0 && asset.height != 0) {
                                // asset.setPremultiplyAlpha(true);
                            }
                        }
                    }
                    this._transloadUrls.push(requestUrl);
                    QTECacheAssetManager.getInstance().setAsset(requestUrl, asset);
                    this.loadResEnd();
                }
            }
        );
    }

    transRequestUrl(url): string {
        let templateManager = QTEUtils.instance(QTETemplateManager, this.symbol);
        if (!templateManager || !templateManager.resourceMaps) {
            return url;
        }
        if (templateManager.resourceMaps[url]) {
            return templateManager.resourceMaps[url];
        }

        return url;
    }

    addLocalRes2ResourceMaps(path: string, nativeUrl: string) {
        let templateManager = QTEUtils.instance(QTETemplateManager, this.symbol);
        if (templateManager.resourceMaps) {
            // templateManager.resourceMaps = Object.assign(templateManager.resourceMaps, obj)
            templateManager.resourceMaps[path] = nativeUrl;
        }
    }
}
