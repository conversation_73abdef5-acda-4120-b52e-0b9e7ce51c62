/**
 * description:题目生命周期观察者
 * author:dengchao
 * */
import { SingleBase } from "../core/base/SingleBase";
import LogUtil from "../util/LogUtil";
import { observeenum } from "../decorators";
import QTESystem from "../QTESystem";
import { NotifyType, QTE_STATE } from "../vo/QTEEnum";
import { QTEObserve } from "../base/QTEObserve";
import QTENotificationManager from "./QTENotificationManager";
import QTEUtils from "../util/QTEUtils";

export default class ObserveManager extends SingleBase {
    public gotoState(state: any, observe: QTEObserve) {

        if (!observe) {
            return;
        }
        observe.curState = state;
        let func = this["_stateMap"].get(state);
        if (func && func !== "") {
            this[func](observe);
        }
    }

    printLog(str: string) {
        qte.log("QTE ObserveManager_V2:: " + str);
    }


    @observeenum(QTE_STATE.GUIDE)
    onStartGuide(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onStartGuide",
            "start onStartGuide"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onStartGuide 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.playGuide();
    }
    @observeenum(QTE_STATE.GUIDE_AFTER)
    onEndGuide(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onEndGuide",
            "start onEndGuide"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onEndGuide 节点销毁无法执行了`);
            return;
        }
        this.gotoState(QTE_STATE.GUIDE_AFTER_ANMI_BEGIN, observe);
    }

    @observeenum(QTE_STATE.GUIDE_AFTER_ANMI_BEGIN)
    onGuideAnimBegin(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onGuideAnimBegin",
            "start onGuideAnimBegin"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onGuideAnimBegin 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.afterReadingQuestion(observe);
    }

    @observeenum(QTE_STATE.GUIDE_AFTER_ANMI_END)
    onGuideAnimEnd(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onGuideAnimEnd",
            "start onGuideAnimEnd"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onGuideAnimEnd 节点销毁无法执行了`);
            return;
        }
        this.gotoState(QTE_STATE.ANSWERING, observe);
    }

    @observeenum(QTE_STATE.ANSWERING)
    onStartAnswering(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onStartAnswering",
            "start onStartAnswering"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onStartAnswering 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onStartAnswering();
        this.gotoState(QTE_STATE.ANSWERING_AFTER, observe);
    }
    @observeenum(QTE_STATE.ANSWERING_AFTER)
    onEndAnswering(observe: QTEObserve): void {

    }

    /** 回答正确逻辑 */
    @observeenum(QTE_STATE.ANSWER_CORRECT)
    onAnswerCorrect(observe: QTEObserve): void {

        qte.log(
            "ObserveManager",
            "onAnswerCorrect",
            "start onAnswerCorrect"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onAnswerCorrect 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onAnswerCorrect();
    }
    @observeenum(QTE_STATE.ANSWER_CORRECT_END)
    onAnswerCorrectEnd(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onAnswerCorrectEnd",
            "start onAnswerCorrectEnd"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onAnswerCorrectEnd 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onAnswerCorrectEnd();
    }
    /** 回答错误逻辑 */
    @observeenum(QTE_STATE.ANSWER_WRONG)
    onAnswerWrong(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onAnswerWrong",
            "start onAnswerWrong"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onAnswerWrong 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onAnswerWrong();
    }
    @observeenum(QTE_STATE.ANSWER_WRONG_END)
    onAnswerWrongEnd(observe: QTEObserve): void {
        qte.log(
            "ObserveManager",
            "onAnswerWrongEnd",
            "start onAnswerWrongEnd"
        );
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onAnswerWrongEnd 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onAnswerWrongEnd();
    }

    @observeenum(QTE_STATE.RESET_QUESTION)
    onResetQuestion(observe: QTEObserve): void {
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onResetQuestion 节点销毁无法执行了`);
            return;
        }
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onResetQuestion 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onResetQuestion();
    }

    @observeenum(QTE_STATE.RESET_QUESTION_END)
    onResetQuestionEnd(observe: QTEObserve): void {
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onResetQuestionEnd 节点销毁无法执行了`);
            return;
        }
        if (!cc.isValid(observe.template.node, true)) {
            qte && qte.logCatBoth('ObserveManager', `onResetQuestionEnd 节点销毁无法执行了`);
            return;
        }
        observe.template.qteSys.onResetQuestionEnd();
    }
}
