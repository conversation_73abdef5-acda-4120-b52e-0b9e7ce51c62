/*
 * @FilePath     : /sdk/assets/qte/manager/QTETemplateManager.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

import QuoteNode from "../base/QuoteNode";
import { SingleBase } from "../core/base/SingleBase";
import { QTE_RESET_TYPE, QTE_SETTINGS, QTE_SUBMIT_TYPE } from "../QTEFaceda";
export default class QTETemplateManager extends SingleBase {
    /** 题版显示节点引用 */
    quoteNode: QuoteNode = null;
    /** 题版数据 */
    data: any = null;
    /** 题版对应的资源映射表 */
    resourceMaps: any = null;
    /** 题版配置信息 */
    settings: QTE_SETTINGS = {
        reset: QTE_RESET_TYPE.DEFAULT,
        submit: QTE_SUBMIT_TYPE.DEFAULT,
        playSubmitAni : true,
        isGroupLoad:false,
        isAnswerResultUI:true,
    };
    
    onDestoryInstance(){
        console.log("####-----onDestoryInstance-uuid-->", this.symbol);
        this.quoteNode = null;
    }
}