/**
 * cocos缓存数据
 */
interface cacheAsset {
    count: number,
    asset: cc.Asset
}

/**
 * 缓存管理类
 */
export class QTECacheAssetManager {
    static _instance: QTECacheAssetManager = null;
    static getInstance (): QTECacheAssetManager {
        if (!this._instance) {
            this._instance = new QTECacheAssetManager();
        }
        return this._instance;
    }
    /**
     * 缓存列表
     */
    private cachedAssets: Map<string, cacheAsset> = new Map<string, cacheAsset>();
    /**
     * load之后,
     * @param url 
     * @param asset 
     */
    setAsset(url: string, asset: any) {
        const cachedAsset = this.cachedAssets.get(url)
        if (cachedAsset) {
            this.cachedAssets.set(url, {count: cachedAsset.count + 1, asset: cachedAsset.asset || asset}) // 加载失败情况 重新设置
        } else {
            this.cachedAssets.set(url, { count: 1, asset: asset})
        }
    }

    /**
     * 
     * @param url 
     * @returns 
     */
    getAsset(url: string): cc.Asset | undefined {
        const cachedAsset = this.cachedAssets.get(url)
        return cachedAsset ? cachedAsset.asset : undefined
    }

    getLoadObj(url: string): any {
        const cachedAsset = this.cachedAssets.get(url)
        return cachedAsset ;
    }

    getAssetsCache() {
        return this.cachedAssets;
    }

    /**
     * 析构
     * @param url 
     * @returns 
     */
    releasingAsset(url: string) {
        const cachedAsset = this.cachedAssets.get(url)
        if (!cachedAsset) {
            return;
        }
        if (cachedAsset.count > 1) {
            this.cachedAssets.set(url, {count: cachedAsset.count - 1, asset: cachedAsset.asset});
        } else {
            this.cachedAssets.delete(url)
            console.log("删除的url",url)
            cc.assetManager.releaseAsset(cachedAsset.asset);
        }
        console.log("####QTECacheAssetManager===>", this.cachedAssets);
    }

    /**
     * 释放这一批urls的资源
     * @param urls 
     */
    releaseingResourceList(urls: string[]) {
        console.log("#====releaseingResourceList===0===>", urls)
        urls.forEach(url => {
            console.log("#====releaseingResourceList===URL===>", url)
            this.releasingAsset(url);
        })
    }
}
  