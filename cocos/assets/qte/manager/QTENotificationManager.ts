import { SingleBase } from "../core/base/SingleBase";

export default class QTENotificationManager extends SingleBase {
    // 全局事件列表
    private _globalEventMap: Map<string, ({cb: (... args: any)=>void, target?: any})[]>;

    /**
     * @constructor
     * @private
     */
    public initInstance() {
        this._globalEventMap = new Map();
    }

    /**
     * 注册全局事件，注意：该接口仅用于框架内部使用，外部业务请勿调用。
     * @param {string} key 事件名称
     * @param {(...args) => void} callback 回调函数
     */
    public registNotification(key: string, callback: (...args) => void, target: any): void {
        let cbList = this._globalEventMap.get(key);
        if (cbList) {
            cbList.push({cb: callback, target: target});
        } else {
            cbList = [];
            cbList.push({cb: callback, target: target});
            this._globalEventMap.set(key, cbList);
        }
    }

    /**
     * 发送全局事件，注意：该接口仅用于框架内部使用，外部业务请勿调用。
     * @param {string} key 事件名称 
     */
    public sendNotification(key: string, ...args: any): void {
        let cbList = this._globalEventMap.get(key);
        if (cbList) {
            for (let cb of cbList) {
                if (cb) { 
                    if (cb.target) {
                        cb.cb.call(cb.target, ... args);
                    } else {
                        cb.cb(... args); 
                    }
                }
            }
        }
    }

    /**
     * 移除消息监听
     * @param {string | number} noti 通知key值
     */
    public unregistNotification(key: string, callback: (...args) => void, target: any): void {
        let cbList = this._globalEventMap.get(key);
        if(cbList){
            for (var i = cbList.length - 1; i >= 0; i--){
                let cb = cbList[i];
                if (target === cb.target){
                    cbList.splice(i, 1);
                }
            }
            if (cbList.length === 0){
                this._globalEventMap.delete(key)
            }
        }
    }

    public unregistNotificationByType(key: string): void {
        let cbList = this._globalEventMap.get(key);
        if(cbList){
            this._globalEventMap.delete(key)
        }
    }

    /** 移除所有框架内部注册的事件 */
    public __clearAllEvent__(): void {
        this._globalEventMap.clear();
    }
}