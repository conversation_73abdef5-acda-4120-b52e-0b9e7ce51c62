import QTEFaceda, { QTE_OPENMODE } from "../QTEFaceda";
import { SingleBase } from "../core/base/SingleBase";
import { AnimationData, AnimationConfig } from "../qte-core-export";
import QTEAnimation from "./QTEAnimation";
/**
 * 动画播放管理类
 */
export default class QTEAnimationManager extends SingleBase {
    private _map: Map<string, QTEAnimation> = new Map();
    // 回调函数的map
    private _aniCBMap: Map<string, () => void>;
    // 锁屏函数
    private _funLock: () => void;
    private _funUnLock: () => void;

    public initInstance() {
        this._aniCBMap = new Map();
    }


    public setLockFun(fun: () => void) {
        this._funLock = fun;
    }

    public setUnLockFun(fun: () => void) {
        this._funUnLock = fun;
    }

    public playAnimation(data: AnimationData[], callback: () => void, key: string, config?: AnimationConfig) {
        if (!data) {
            callback && callback();
            return;
        }
        this._aniCBMap.set(key, callback);
        let animation = new QTEAnimation(this.symbol);
        let isAddSpeed = false;
        if (qte && qte.openMode != QTE_OPENMODE.SHOW_NORMAL) {
            isAddSpeed = true;
        }
        if (isAddSpeed) {
            animation.isAddSpeedPlay = isAddSpeed;
        }
        // 是否允许打断;
        if (config) {
            animation.AllBreak = config.animationAllowBreak;
        }
        if (this.isExceptAnimation(key)) {
            animation.AllBreak = true;
        }
        // 陈疆说:鸭鸭帮动画都不允许打断。
        if (key === "helpers") {
            animation.AllBreak = false;
        }
        if (!animation.AllBreak) {
            this._funLock && this._funLock();
        }
        // 不让操作
        animation.init(data, key, () => {
            if (!animation.AllBreak) {
                this._funUnLock && this._funUnLock();
            }
            this.deleteAnimation(key);
            let callback = this._aniCBMap.get(key)
            if (callback) {
                this._aniCBMap.delete(key);
                callback();
            }
        }, this.symbol);
        this._map.set(key, animation);
    }

    /**
     * 跳过所有可以打断的动画;
     */
    public skipAllAnimation() {
        let temp = []
        let arr = Array.from(this._map.values());
        for (let i = 0; i < arr.length; i++) {
            let animationItem = arr[i];
            if (animationItem.AllBreak) {
                temp.push(animationItem.key);
            }
        }
        // 跳过动画
        for (let i = 0; i < temp.length; i++) {
            if (this.isExceptAnimation(temp[i])) {
                continue;
            }
            this.skipAnimation(temp[i]);
        }
    }

    /**
     * 全局动画打断豁免列表
     * @param key 
     * @returns 
     */
    private isExceptAnimation(key): boolean {
        var re = /playGuide/gi;
        if (key.search(re) == -1) {
            return false;
        } else {
            return true;
        }
    }

    public skipAnimation(key: string) {
        let animation = this._map.get(key);
        if (animation) {
            animation.stop();
        }
    }

    public skipAnimationWithoutCB(key: string) {
        let callBack = this._aniCBMap.get(key);
        if (callBack) {
            this._aniCBMap.delete(key);
        }
        this.skipAnimation(key);
    }

    public deleteAnimation(key: string) {
        this._map.delete(key);
    }
}