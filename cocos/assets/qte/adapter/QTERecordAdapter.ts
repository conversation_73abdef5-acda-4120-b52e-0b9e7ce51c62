import QTEFaceda from "../QTEFaceda";

/*
 * @FilePath     : /sdk/assets/qte/adapter/QTERecordAdapter.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */
// const MockData: any = {
//     status: 0,
//     segment: 0,
//     result: {
//         hypotheses: [
//             {
//                 transcript:
//                     "{\"status\": 0, \"wordList\": [{\"stress\": [{\"stress_char\": \"w_er_d\", \"score\": 0.0, \"ref\": \"0\"}], \"end\": 3040, \"fall_tone\": 0, \"syllable\": [{\"start\": 2740, \"score\": 29.740471, \"end\": 3040, \"syb_char\": \"w_er_d\"}], \"word\": \"word\", \"phoneNum\": 3, \"start\": 2740, \"score\": 56.892559, \"phoneList\": [{\"phid\": \"1_1\", \"end\": 2820, \"phone\": \"W_B\", \"pherr\": 0, \"start\": 2740, \"pitch\": 207.531097, \"prob\": 0.809657, \"best\": \"W\"}, {\"phid\": \"2_3\", \"end\": 2920, \"phone\": \"ER1_I\", \"pherr\": 1, \"start\": 2820, \"pitch\": 229.892288, \"prob\": 0.049418, \"best\": \"W\"}, {\"phid\": \"4_4\", \"end\": 3040, \"phone\": \"D_E\", \"pherr\": 1, \"start\": 2920, \"pitch\": 235.133423, \"prob\": 0.033139, \"best\": \"Z\"}], \"rise_tone\": 1}], \"accurate_score\": 28.44628, \"grammar_score\": 7.925722, \"fluency_score\": 0.0,\"online_audio_url\": \"https://yaya.cdnjtzy.com/preview-3e7fd6.mp3\", \"score\": 28.44628, \"length_ms\": 9920, \"integrity_score\": 50.0, \"msg\": \"OK\", \"token_id\": \"06F410A0-5FC2-426A-A1E3-F1ABDC442404\", \"id\": \"06F410A0-5FC2-426A-A1E3-F1ABDC442404\"}"
//             }
//         ],
//         final: true
//     },
//     id: "96df4ace-6864-4567-bdec-18100a0159be"
// };

const MockData:any = {
    "status": 0,
    "online_audio_url": "https://yaya.cdnjtzy.com/preview-3e7fd6.mp3",
    "result": {
        "hypotheses": [
            {
                "transcript":"{\"status\": 0, \"wordList\": [{\"stress\": [{\"stress_char\": \"h_a\ʊ\", \"score\": 0, \"ref\": 0}], \"end\": 1630, \"fall_tone\": 0, \"syllable\": [{\"start\": 1380, \"score\": 26.748997, \"end\": 1630, \"syb_char\": \"h_a\ʊ\"}], \"word\": \"how\", \"phoneNum\": 2, \"start\": 1380, \"score\": 77.332428, \"phoneList\": [{\"phid\": \"1_1\", \"end\": 1460, \"phone\": \"h\", \"pherr\": 1, \"start\": 1380, \"pitch\": 197.782394, \"prob\": 0.327465, \"best\": \"sil\"}, {\"phid\": \"2_3\", \"end\": 1630, \"phone\": \"a\ʊ\", \"pherr\": 1, \"start\": 1460, \"pitch\": 229.31311, \"prob\": 0.207515, \"best\": \"\ə\ʊ\"}], \"rise_tone\": 0}, {\"stress\": [{\"stress_char\": \"\ə\ʊ_l_d\", \"score\": 0, \"ref\": 0}], \"end\": 1920, \"fall_tone\": 0, \"syllable\": [{\"start\": 1710, \"score\": 12.592117, \"end\": 1920, \"syb_char\": \"\ə\ʊ_l_d\"}], \"word\": \"old\", \"phoneNum\": 3, \"start\": 1710, \"score\": 63.78233, \"phoneList\": [{\"phid\": \"1_1\", \"end\": 1850, \"phone\": \"\ə\ʊ\", \"pherr\": 1, \"start\": 1710, \"pitch\": 217.095596, \"prob\": 0.122003, \"best\": \"w\"}, {\"phid\": \"2_2\", \"end\": 1890, \"phone\": \"l\", \"pherr\": 1, \"start\": 1850, \"pitch\": 181.697662, \"prob\": 0.144925, \"best\": \"\ʊ\"}, {\"phid\": \"3_3\", \"end\": 1920, \"phone\": \"d\", \"pherr\": 1, \"start\": 1890, \"pitch\": 177.711304, \"prob\": 0.110835, \"best\": \"l\"}], \"rise_tone\": 0}, {\"stress\": [{\"stress_char\": \"\ə_r\", \"score\": 0, \"ref\": 0}], \"end\": 2120, \"fall_tone\": 0, \"syllable\": [{\"start\": 1920, \"score\": 27.410938, \"end\": 2120, \"syb_char\": \"\ə_r\"}], \"word\": \"are\", \"phoneNum\": 2, \"start\": 1920, \"score\": 77.349976, \"phoneList\": [{\"phid\": \"1_1\", \"end\": 1980, \"phone\": \"\ə\", \"pherr\": 0, \"start\": 1920, \"pitch\": 167.119858, \"prob\": 0.309234, \"best\": \"\ə\"}, {\"phid\": \"2_3\", \"end\": 2120, \"phone\": \"r\", \"pherr\": 1, \"start\": 1980, \"pitch\": 162.947952, \"prob\": 0.238985, \"best\": \"\ə\"}], \"rise_tone\": 0}, {\"stress\": [{\"stress_char\": \"j_\ʊ\", \"score\": 0, \"ref\": 0}], \"end\": 2490, \"fall_tone\": 0, \"syllable\": [{\"start\": 2120, \"score\": 27.204151, \"end\": 2490, \"syb_char\": \"j_\ʊ\"}], \"word\": \"you\", \"phoneNum\": 2, \"start\": 2120, \"score\": 76.973473, \"phoneList\": [{\"phid\": \"1_1\", \"end\": 2190, \"phone\": \"j\", \"pherr\": 1, \"start\": 2120, \"pitch\": 177.942734, \"prob\": 0.172561, \"best\": \"sil\"}, {\"phid\": \"2_3\", \"end\": 2490, \"phone\": \"\ʊ\", \"pherr\": 1, \"start\": 2190, \"pitch\": 154.723892, \"prob\": 0.371522, \"best\": \"sil\"}], \"rise_tone\": 0}], \"accurate_score\": 73.85955, \"fluency_score\": 100.0, \"online_audio_url\": \"https://yaya.cdnjtzy.com/preview-3e7fd6.mp3\", \"audio_url\": \"https://yaya.cdnjtzy.com/preview-3e7fd6.mp3\", \"score\": 73.85955, \"length_ms\": 3852, \"integrity_score\": 100.0, \"msg\": \"OK\", \"token_id\": \"d976a34ded83407ca0d376fb5f817197\", \"id\": \"d976a34ded83407ca0d376fb5f817197\"}"
            }
        ],
        "final": true
    },
    "msg": "OK",
    "id": "d976a34ded83407ca0d376fb5f817197",
    "is_final": 1
}



// TODO: 区分默认数据，根据类型进行设置
export default class QTERecordAdapter {
    private recordCallBack: any = null;
    startRecordAction(recordData, startRecordCb, removeRecordCb, startEndCb) {
        if (QTEFaceda.adapter && QTEFaceda.adapter.startRecordAction) {
            qte.log("startRecordAction");
            QTEFaceda.adapter.startRecordAction(recordData, startRecordCb, removeRecordCb, startEndCb);
        } else {
            this.recordCallBack = startRecordCb;
        }
    }
    stopRecordAction() {
        if (QTEFaceda.adapter && QTEFaceda.adapter.stopRecordAction) {
            qte.log("stopRecordAction");
            QTEFaceda.adapter.stopRecordAction();
        } else {
            this.recordCallBack && this.recordCallBack(MockData);
        }
    }

    checkRecordAuthorityAction(callBack) {
        if (QTEFaceda.adapter && QTEFaceda.adapter.checkRecordAuthorityAction) {
            qte.log("check Record ");
            QTEFaceda.adapter.checkRecordAuthorityAction(callBack);
        } else {
            callBack({
                status: 1,
                isMock:true
            });
        }
    }
}
