const MockDataIde: any = {
    "status": 0,
    "online_audio_url": "https://yaya.cdnjtzy.com/preview-3e7fd6.mp3",
    "lattice_depth": -1,
    "hypotheses": [
        {
            "utterance": "十点五十分"
        },
        {
            "word-level": "演示文案"
        }
    ],
    "is_final": 1,
    "msg": "OK",
    "id": "450756c1-5faa-4a8a-b859-4767b45d9097",
    "version": "",
    "uid": ""
}



// TODO: 区分默认数据，根据类型进行设置
export default class QTEIdentifyAdapter {
    private recordCallBack: any = null;

    private startIdeTime: number = 0;
    private offTime: number = 0;
    startIdentifyAction(recordData, startRecordCb) {
        if (qte.adapter && qte.adapter.startRecognitionAction) {
            qte.log("startIdentifyAction");
            this.startIdeTime = new Date().getTime() / 1000;
            this.offTime = 0;
            qte.adapter.startRecognitionAction(recordData, startRecordCb);
        } else {
            this.recordCallBack = startRecordCb;
        }
    }

    stopIdentifyAction() {
        if (qte.adapter && qte.adapter.stopRecognitionAction) {
            qte.log("stopIdentifyAction");
            this.offTime = new Date().getTime() / 1000 - this.startIdeTime;
            qte.adapter.stopRecognitionAction();
        } else {
            this.recordCallBack(MockDataIde);
        }
    }

    checkRecordAuthorityAction(callBack) {
        if (qte.adapter && qte.adapter.checkRecordAuthorityAction) {
            qte.log("check Record ");
            qte.adapter.checkRecordAuthorityAction(callBack);
        } else {
            callBack({
                status: 1,
                isMock: true
            });
        }
    }
    culScore(recordResult: any, data: { fulNum: number, wordList: string[] }): { evaluatingText: string, score: number, time: number } {
        let currFulNum = data.fulNum ||5;
        let score = 0;
        let evaluatingText = "";
        if (recordResult.hypotheses) {
            for (let i = 0; i < recordResult.hypotheses.length; i++) {
                if (recordResult.hypotheses[i]['word-level']) {
                    evaluatingText = recordResult.hypotheses[i]['word-level'];
                    break;
                }
            }

            if (evaluatingText) {
                let wordRate = 0;
                for (let j = 0; j < data.wordList.length; j++) {
                    if (evaluatingText.indexOf(data.wordList[j]) !== -1) {
                        wordRate++;
                    }
                }
                if (data.wordList.length > 0) {
                    score += (wordRate / data.wordList.length * 60);
                } else {
                    score += 60;
                }

                if (this.offTime > 0) {
                    let fulNum = (evaluatingText.length / this.offTime);
                    if (fulNum >= currFulNum) {
                        score += 40;
                    } else {
                        let rate = fulNum / currFulNum;
                        score += (40 * rate);
                    }
                }

            }
        }
        score = Math.round(score);
        if (score > 100) {
            score = 100;
        }
        return { evaluatingText, score, time: this.offTime }
    }
}
