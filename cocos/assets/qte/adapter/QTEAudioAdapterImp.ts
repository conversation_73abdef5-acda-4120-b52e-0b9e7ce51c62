
export interface QTEAudioAdapterImp {
    /**
     * 播放音频
     * @param clip cc.AudioClip 对象
     * @param loop 是否循环
     * @param volume 音量 0~1
     * @param callback 播放完成回调
     * @return 音频播放ID
     */
    playAudio(clip: cc.AudioClip, loop: boolean, volume: number, callback: ()=>void): number;
    /**
     * 停止音频
     * @param audioItem 
     */
    stopAudio(audioItem: {url: string, nId: number, bIsLoop: boolean});
    /**
     * 暂停音频
     * @param audioItem 
     */
    pauseAudio(audioItem: {url: string, nId: number, bIsLoop: boolean});
    /**
     * 恢复播放音频
     * @param audioItem 
     */
    resumeAudio(audioItem: {url: string, nId: number, bIsLoop: boolean});
}