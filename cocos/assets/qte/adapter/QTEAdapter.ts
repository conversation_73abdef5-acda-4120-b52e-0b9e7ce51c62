/*
 * @FilePath     : /assets/qte/adapter/QTEAdapter.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */



import QTEFacada from "../QTEFaceda";
import QTEUtils from "../util/QTEUtils";
import { QTEAudioAdapterImp } from "./QTEAudioAdapterImp";

export default class QTEAdapter implements QTEAudioAdapterImp{

    private recordCallBack:any = null;
    playAudio(clip: cc.AudioClip, loop: boolean, volume: number, callback: () => void): number {

        if (QTEFacada.adapter && QTEFacada.adapter.playAudio) {
            let ID = QTEFacada.adapter.playAudio(clip, loop, volume, callback);
            console.log("#------Framework---return--->", ID);
            return ID;
        } 
        const audioId = cc.audioEngine.play(clip, loop, volume);
        cc.audioEngine.setFinishCallback(audioId, () => {
            if (callback){
                callback();
            }
        });
        return audioId;
    }

    stopAudio(audioItem: { url: string; nId: number; bIsLoop: boolean; }) {
        if (QTEFacada.adapter && QTEFacada.adapter.stopAudio) {
            QTEFacada.adapter.stopAudio(audioItem);
            return;
        } 
        cc.audioEngine.stop(audioItem.nId);
    }

    pauseAudio(audioItem: { url: string; nId: number; bIsLoop: boolean; }) {
        if (QTEFacada.adapter && QTEFacada.adapter.pauseAudio) {
            QTEFacada.adapter.pauseAudio(audioItem);
            return;
        } 
        cc.audioEngine.pause(audioItem.nId)
    }

    resumeAudio(audioItem: { url: string; nId: number; bIsLoop: boolean; }) {
        if (QTEFacada.adapter && QTEFacada.adapter.resumeAudio) {
            QTEFacada.adapter.resumeAudio(audioItem);
            return;
        } 
        cc.audioEngine.resume(audioItem.nId)
    }

    toast (content: string) {
        if(QTEFacada.adapter && QTEFacada.adapter.toast){
            QTEFacada.adapter.toast(content);
            return;
        }
        // QTEUtils.showToast(content);
        let node = new cc.Node();
        let label = node.addComponent(cc.Label);
        node.color = cc.Color.BLACK;
        label.string = content;
        let labOutLine = node.addComponent(cc.LabelOutline);
        labOutLine.color = cc.Color.WHITE;
        labOutLine.width = 2;
        node.parent = cc.find("Canvas");
        cc.tween(node)
            .to(3, {x: 0, y: 100, opacity: 128})
            .call(() => {
                node.removeFromParent();
                node.destroy();
            })
            .start();
    }
}

