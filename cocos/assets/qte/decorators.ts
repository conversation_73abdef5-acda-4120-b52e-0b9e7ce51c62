/**
 * 观察者枚举装饰器，用于映射枚举与函数关系
 * @param name 枚举
 */
export function observeenum (name: number) {
    return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
        if (!target._stateMap) {
            target._stateMap = new Map<number, string>();
        }
        target._stateMap.set(name, propertyKey);
    };
}



export let relatedTouchCmpt = new Set<string>();
/**
 * 定义那些组件需要关联touch功能, 方便筛选
 */
export function relatedTouch(constructor: Function){
    relatedTouchCmpt.add(constructor.prototype.__classname__);
}

/** 当前题板下标：题组为-2，单题为-2 */
export let QTE_CURR_TEMPLATE_INDEX: number = -1;
/** 设置当前题板下标：题组为-2，单题为-2 */
export function setCurrTemplateIndex (idx: number) {
    QTE_CURR_TEMPLATE_INDEX = idx;
}