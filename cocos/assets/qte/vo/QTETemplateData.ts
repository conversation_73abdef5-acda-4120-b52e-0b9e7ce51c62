import { StageConfigData } from "./QTEDataVo";

export default class QTETemplateData {
    components: any[];
    extraDataMap: {};
    resourceList: string[];
    template: {
        questionType: number;
        isSpecialQuestion: boolean;
        name: string;
        category: number;
        tempType: number;
        bundleUrl: string;
        bundleName: string;
        tags: [];
        stage: {
            width: number;
            height: number;
            safeWidth: number;
            safeHeight: number;
        };
        extraConfig: [];
        animationConfig: [];
        video: number;
        smts: number;
        features: {
            isQuestion?: number,//是否组卷、是否可发题
            isOral?: number,//是否含有口述逻辑
            isGroup?: number,//是否是题组
            hasVideo?: number,//是否包含视频
            hasMys?: number,//是否包含神秘提示
            demoPage?: number,//讲解页
            canInteract?: number,//是否可交互
        }
    };
    animations: {};
    animationsForClient: {};
    stageData: StageConfigData;
    extraStageData: {
        isAutoSubmit?: boolean;
        answerWrongCount?: number;
        errorType?: number;
        defaultAction?: boolean;
        dragRelation?: any;
        closeDefaultFeedback?: boolean;
        hasRecover?: boolean;
    };
    thumbnail: string;
    questionType: number;

    [key: string]: unknown;

    constructor(data: any) {
        this.parse(data);
    }

    /**
     * 解析ArtInfo，用于渲染每个page的bundle
     * @param data
     */
    parse(data: any) {
        if (!data) {
            return;
        }
        this.components = data.components;
        this.extraDataMap = data.extraDataMap;
        this.resourceList = data.resourceList;
        this.template = data.template;
        this.animations = data.animations;
        this.animationsForClient = data.animationsForClient;
        this.stageData = data.stageData;
        this.extraStageData = data.extraStageData;
        this.thumbnail = data.thumbnail;
        this.questionType = data.questionType;
    }

    clear() {
        this.components = null;
        this.extraDataMap = null;
        this.resourceList = null;
        this.template = null;
        this.animations = null;
        this.animationsForClient = null;
        this.stageData = null;
        this.extraStageData = null;
        this.thumbnail = null;
        this.questionType = null;
    }
}
