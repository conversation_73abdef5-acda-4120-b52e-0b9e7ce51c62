
/** 全局通知事件 */
export enum NotifyType {
    COMMON_TOUCH_EVENT = "common_touch_event", //全局触摸事件, 用于监听打断动画等操作
    // 跳过题组动画
    SIMPLE_SKIP_ALL_ANIMATION = "simple_skip_all_animation",
    // 题目准备完成，可以开始运行了，仅适用于题组逻辑
    QUESTION_READY_FOR_LIST = "question_ready_for_list",
    // 题目加载完成
    QUESTION_READY = "question_ready",
}

/** 题板生命周期枚举 */
export enum QTE_STATE {
    /** 语音读题 */
    GUIDE,
    GUIDE_AFTER,
    /** 语音读题后动画开始 */
    GUIDE_AFTER_ANMI_BEGIN,
    /** 语音读题后动画结束 */
    GUIDE_AFTER_ANMI_END,
    /** 作答阶段 */
    ANSWERING,

    ANSWERING_AFTER,
    ANSWER_CORRECT,
    ANSWER_CORRECT_END,
    ANSWER_WRONG,
    ANSWER_WRONG_END,
    /** 重置 */
    RESET_QUESTION,
    RESET_QUESTION_END,
}
/**
 * @msg     : 上传报错类型定义
 * @param    {*}
 * @return   {*}
 */
export enum QTE_LOGCAT_TYPE {
    QTE_LOAD_ERROR = 'qte_load_error',
    QTE_LOAD_RES_ERROR = 'qte_load_resource_error',
    QTE_LOAD_ONLINE_ERROR = 'qte_load_online_error',

    /** qte常规流程日志 */
    QTE_LOG_COMMON = 'qte_log_common',

    // 按生命周期细分一下
    QTE_LOG_UPDATE = 'updateFunc',
    QTE_LOG_RESET = 'resetFunc',
    QTE_LOG_ANSWER = 'answerFunc',
    QTE_LOG_SUBMIT = 'submitFunc',


    /** qte拦截操作日志 */
    QTE_LOG_BLOCK = 'qte_log_block',
    /** qteFecade option */
    QTE_LOG_OPTION = 'qte_log_option',
    /** qteFecade artinfo */
    QTE_LOG_ARTINFO = 'qte_log_artinfo',
    /** qteFecade getState */
    QTE_LOG_GETSTATE = 'qte_log_getState',
    /** qteFecade recover */
    QTE_LOG_RECOVER = 'qte_log_recover',
    /** qteFecade recover end */
    QTE_LOG_RECOVER_END = 'qte_log_recover_end',
    QTE_LOG_BLANKQUESTION = "qte_log_blank_question",

}

export enum TraceName {
    // 加载bundle
    updateBundleStart = 'FMG_019',
    updateBundleEnd = 'FMG_020',

    // 预加载资源
    updatePreloadStart = 'FMG_021',
    updatePreloadEnd = 'FMG_022',

    // 创建节点
    updateCreateNodeStart = 'FMG_023',
    updateCreateNodeEnd = 'FMG_024',

    // 点击查看原文按钮
    updateClickQuestionContent = 'FMG_047',
    // 原文查看页面显示
    updateShowQuestionContent = 'FMG_048',
}
export enum TraceFinishType {
    fail = 0,
    success = 1,
}

//录音组件枚举
export enum RecordState {
    Idle = "idle",
    Ready = 'ready',
    Recording = "recording",
    Loading = "loading",
    Stop = "stop",
    Error = "error",
}

/** 音频默认ID */
export const DEFAULT_AUDIO_ID = -1;
/** 默认题目索引 */
export const DEFAULT_TEMPLATE_INDEX = -1;
/** 默认题组索引 */
export const DEFAULT_TEMPLATE_LIST_INDEX = -2;

/**
 * 鲨鱼题版公参
 */
export class QTETemplateCommonParams {
    type: number;
    tempType: number;
    /** 题目类型 */
    questionType: number;
    name: string;
    /** 资源列表, 需要在初始化时加载 */
    resourceList: string[];
    /** 封面截图资源地址 */
    thumbnail: string;
    /** 题干音频资源地址 */
    guide: string;
    width: number;
    height: number;
    safeHeight: number;
    safeWidth: number;
    /** 背景颜色 弃用 */
    backgroundColor: string;
    /** 背景颜色 bgColor */
    bgColor: string;
    /** 背景图片资源 */
    texture: string;
    bundleUrl: string;
    /** 选择题 1000 拖拽题 1001 填空题 1002 连线题 1003 口述题 1004 画图题 1005 拼图题 1006 特殊题 1028 讲解页 1029 */
    category: number;
    /** 题干 */
    information?: {
        /** 描述 */
        desc: string
    };
    /** 作答信息 */
    answerBase?: {};
    /** 题版key 创建时传入的 */
    qteKey: string;
}

export interface SubmitAnswer {
    answerContent?: AnswerContentItem[] | any[];           // 作答内容
    pContent?: PContentItem[] | any[],                      // 作答产生数据
    [propName: string]: any
}
export interface AnswerContentItem {
    signalId: number,                // 标号
    type?: string,                    // 类型
    isCorrect?: boolean,                 // 单个空的对错
    content?: string                  // 内容
}
export interface PContentItem {
    type: string,                    // 类型
    content: string                  // 内容
    expendType?: string               // 指定类型 
}


/**
 * shark题版生命周期回传参数
 */
export class QTETemplateParams extends QTETemplateCommonParams {
    /** 是否有作答痕迹 */
    answerMark: boolean;
    /** 题目是否正确 */
    correct: boolean;
}

/**
 * shark题版提交周期使用的参数
 */
export class QTETemplateSubmitParams extends QTETemplateParams {
    submitAnswer: SubmitAnswer;
}
/**
 * 每次触发判断正误逻辑回传参数
 */
export class QTETemplateJudgeParams extends QTETemplateParams {
    /** 本次操作是否正确 */
    singleCorrect: boolean;
}

/**
 * shark题版生命周期函数
 */
export interface QTEHook {
    /** 题版创建完成后调用 */
    onCreate: (vo: QTETemplateCommonParams) => Promise<void> | void;
    /** 创建完成onCreate钩子函数执行完,下一帧调用 */
    onStart: (vo: QTETemplateCommonParams) => Promise<void> | void;
    /** 题版重置时调用 */
    onReset: (vo: QTETemplateParams) => Promise<void> | void;
    /** 题版提交时调用 */
    onSubmit: (vo: QTETemplateSubmitParams) => Promise<void> | void;
    /** 题版暂停时调用 */
    onPause: (vo: QTETemplateParams) => Promise<void> | void;
    /** 题版回复时调用 */
    onResume: (vo: QTETemplateParams) => Promise<void> | void;
    /** 题版销毁时调用 */
    onDestroy: (vo: QTETemplateParams) => Promise<void> | void;
    /** 题版触发判断时调用 */
    onJudge: (vo: QTETemplateJudgeParams) => Promise<void> | void;
    /** 题版状态数据更改时调用 */
    onStateChange: (vo: QTETemplateJudgeParams, states: any) => Promise<void> | void;
    /**普通题结束 调用通用反馈弹窗 */
    onShowResult: (vo: any) => Promise<void> | void;
}
/**
 * 对应的wiki地址: https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=300011273
 */
export interface QTEHooks extends Partial<QTEHook> { }

export enum AutoSubmitMode {
    RIGHT_SUBMIT = 0, // 答对自动提交
    OPERATE_SUBMIT = 1, //作答即提交
}