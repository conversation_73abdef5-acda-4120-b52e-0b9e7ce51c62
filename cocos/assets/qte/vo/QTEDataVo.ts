// 题目数据
export interface QuestionConfigData {
    components: ComponentData[];
    stageData: StageConfig;
    extraStageData: ExtraStageConfig;
    extraDataMap: QuestionMetaData;
    resourceList: ResourceConfig;
    audios: { [key: string]: string };
    animationsForClient: {
        [key: string]: AnimationData[];
    };
    tid: number;
}

export interface StageConfig {
    [key: string]: any;
}

export interface ResourceConfig {
    [key: string]: { type: typeof cc.Asset; sourceDir: string };
}

export interface ExtraStageConfig {
    [key: string]: any;
}

export interface QuestionMetaData {
    [key: string]: any;
}

export interface ComponentData {
    id: string;
    type: "sprite" | "label" | "group" | "spine" | "cutShape" | "cocosAni" | "specialComponent" | "formula" | "svgShape" | "optionComponent" | "shape" | "richTextSprite";
    tag: string;
    properties: ComponentProperty;
    hideBeforeAnimation?: boolean;
    extra: Object;
    spineData?: any;
    cocosAniData?: any;
    subComponents?: ComponentData[];
    childComponents?: ComponentData[];
    subType?: string;
}

export interface ComponentProperty {
    answerDuration?: number;  // 看图说话倒计时
    x: number;
    y: number;
    width: number;
    height: number;
    active: boolean;
    texture?: string;
    zIndex?: number;
    color?: string;
    angle?: number;
    opacity?: number;
    scaleX?: number;
    scaleY?: number;
    offsetX?: number;
    offsetY?: number;
    timeScale?: number;
    animList?: string[];
    animationList?: string[];
    loop?: boolean;
    fillColor?: string;
    lineWidth?: number;
    strokeColor?: string;
    linesData?: LineData[];
    pointsData?: PointData[];
    lineHeight?: any;
    str?: string;
    horizontalAlign?: any;
    svgText?: string;  // svg文本描述

    speakerType?: number,
    audioUrl?:string
    count?: number,
    autoPlay?: boolean,
    notStopPlaying?: boolean,
    countdown?: boolean,
    autoRepeatCount?: number,
    countdownSkin?: number,
    duration?:number
    isNotTouch?:boolean
}
export interface LineData {
    id: string;
    label: string;
    points: number[];
}
export interface PointData {
    id: string;
    label: string;
    x: number;
    y: number;
}

// 动画数据
export type AnimationData =
        | AudioDetailData
        | TweenAnimationDetailData
        | SpineAnimationDetailData
        | NormalAnimationDetailData
        | ClipAnimationDetailData;

export interface AudioDetailData {
    time: number;
    audioUrl: string;
    type: "audio";
}

export interface AnimationConfig {
    animationAllowBreak: boolean;
}

export interface TweenAnimationDetailData {
    time: number;
    componentId: string;
    component: cc.Node;
    type: "tween";
    data: TweenKeyFrameData[];
    repeat?: number;
}

export interface TweenKeyFrameData {
    type: "to" | "by";
    time: number;
    props: any;
    opts?: string;
}

export interface SpineAnimationDetailData {
    time: number;
    componentId: string;
    type: "spine";
    data: SpineKeyFrameData;
}

export interface ClipAnimationDetailData {
    time: number;
    componentId: string;
    type: "cocosAni";
    data: SpineKeyFrameData;
}

export interface SpineKeyFrameData {
    animation: string;
    duration: number;
    loop: boolean;
    timeScale: number;
    endTimeScale: number;
    after: any;
}

export interface NormalAnimationDetailData {
    time: number;
    componentId: string;
    component: cc.Node;
    type: "animation";
    data: any;
}

// 其他
export interface AnswerContent {
    isAnswered: boolean;
    isRight: boolean;
    isPassive: boolean;
    tryTimes: number;
    ext?: string;
    answer: string;
}

export interface StageConfigData {
    width: number;
    height: number;
    safeWidth: number;
    safeHeight: number;
    extraStageData: { [key: string]: any };
    // 提示错误次数
    answerWrongCount: number;
    // 错误提示时机
    // 1==首次错误起 2==二次错误起
    errorType: number;
    // 是否自动提交
    isAutoSubmit: boolean;
    // 是否播放元素的默认反馈
    defaultAction: boolean;
    dragRelation: any;
    // 是否使用默认反馈
    closeDefaultFeedback: boolean;
}
