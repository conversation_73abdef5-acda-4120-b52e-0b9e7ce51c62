import QTECompManager from "../manager/QTECompManager";
import QTEUtils from "../util/QTEUtils";
import QTEBaseMeta from "./QTEBaseMeta";
import QTEEntityNode from "./QTEEntityNode";

/**
 * 组件类型，在 QTECompManager 可以用来获取组件
 */
export enum QTEComponentType {
    AnimationCmpt = "AnimationCmpt",
    BlankCmpt = "BlankCmpt",
    ClickCmpt = "ClickCmpt",
    ClickPlayAnimCmpt = "ClickPlayAnimCmpt",
    CloneCmpt = "CloneCmpt",
    DragCmpt = "DragCmpt",
    LabelCmpt = "LabelCmpt",
    DrawCmpt = "DrawCmpt",
}

const { ccclass } = cc._decorator;
@ccclass
export default class QTEComponent extends cc.Component {

    // qte元数据
    public qteBaseMeta: QTEBaseMeta;

    /** 包装需要状态同步的成员 */
    public state: any;
    public id: number;
    public type: QTEComponentType;

    public get entityNode(): QTEEntityNode {
        return this.node as QTEEntityNode;
    }
    // 组件可选属性，用于不同组件对业务的拓展逻辑
    public options: any;

    private _bIsCanSkip: boolean = false;
    public setCanSkip(value: boolean) {
        // set多次
        this.node.off(cc.Node.EventType.TOUCH_START, this._onClick, this);
        this.node.on(cc.Node.EventType.TOUCH_START, this._onClick, this);
        this._bIsCanSkip = value;
    }

    public onDestroy() {
        this.node.off(cc.Node.EventType.TOUCH_START, this._onClick, this);
    }

    private _onClick (e: cc.Touch) {
        this.entityNode.skipAllAnimation()
    }

    /** 
     * 同步组件状态
     * 如果需要该功能，组件状态变化后需要调用该方法
     */
    stateChanged() {
        QTEUtils.instance(QTECompManager, this.qteBaseMeta.UUID).updateState(this.qteBaseMeta.UUID, this.id);
    }

    /** 
     * 监听同步过来的状态
     * 如果需要该功能，子组件需要重写该方法 
     * */
    watcher(key, val) {
        return null;
    }

    /** 
     * 重置组件内容，题版重置会调用到
     * 如果需要该功能，子组件需要重写该方法 
     * */
    reset() {

    }
}