import ResLoader from "../core/loader/ResLoader";
import ValueUtils from "../core/utils/ValueUtils";
import ObserveManager from "../manager/ObserveManager";
import QTEAnimationManager from "../manager/QTEAnimationManager";
import QTEAssetsManager from "../manager/QTEAssetsManager";
import QTEAudioManager from "../manager/QTEAudioManager";
import QTEEntityManager from "../manager/QTEEntityManager";
import QTEStateManager from "../manager/QTEStateManager";
import QTEFaceda, { QTE_OPENMODE } from "../QTEFaceda";
import QTESystem from "../QTESystem";
import QTEResetAndSubmit from "../ui/QTEResetAndSubmit";
import QTEUtils from "../util/QTEUtils";
import { AnimationData, ComponentData, QuestionMetaData, StageConfigData } from "../vo/QTEDataVo";
import { QTETemplateCommonParams, QTETemplateJudgeParams, QTETemplateParams, QTETemplateSubmitParams, QTE_LOGCAT_TYPE, QTE_STATE } from "../vo/QTEEnum";
import { reactive } from "./../core/reactivity/reactivity.cjs";
import { watch } from "./../core/reactivity/watch";
import QTEEntityNode from "./QTEEntityNode";
import QTEStageFactory from "../QTEStageFactory";
import { SpeakerComponent } from "../assemble/speaker/scripts/SpeakerComponent";
import { SpecialComponentType } from "../assemble/core/ComponentData";
import DrawCmpt from "../components/DrawCmpt";
import DrawControl from "../assemble/drawControl/DrawControl";
import QTECompManager from "../manager/QTECompManager";
import { QTEComponentType } from "./QTEComponent";
import ReadcomComponent from "../assemble/readcom/scripts/ReadcomComponent";
import QTETemplateManager from "../manager/QTETemplateManager";
import ListenRetellComponent from "../assemble/listenRetell/scripts/ListenRetellComponent";
import QTESerialChangeUI from "../ui/QTESerialChangeUI";
import QTEBaseState from "./QTEBaseState";


export default abstract class QTETemplate extends cc.Component {
    // 当前题板唯一id
    public UUID: string = "invalid template uuid";
    // 当前同步数据
    state: QTEBaseState;
    // qte
    private _qte: QTESystem;
    public get qteSys(): QTESystem {
        return this._qte;
    }
    // 题板配置属性
    protected _stageConfig: StageConfigData = null;
    // 锁屏引用计数
    private _nLockCount: number = 0;
    // 题干音频&音频组件默认时长
    private _nGuideDuration = -1;

    // 13 类型切换小题调用
    public switchQuestionData: qte.QTESerialUIParams = null;

    /** 是否操作过 */
    protected get _bIsOperated() {
        return this.state.isOperated;
    }
    protected set _bIsOperated(operate: boolean) {
        this.state.isOperated = operate;
    }

    /** 题版是否正确 */
    protected get _bCorrect() {
        return this.state.isCorrect;
    }
    protected set _bCorrect(correct: boolean) {
        this.state.isCorrect = correct;
    }


    /** 答案信息 */
    protected _answerBase = {};
    public setAnswerBase() {
        console.log('you can modify the fun to set answer base data')
    }

    /** 题版bundle名称 */
    protected _strBundleName: string = "";
    /** 本地资源列表 */
    protected _arrLocalResList: { path: string, type: typeof cc.Asset }[] = [];
    /** 本地资源缓存,方便题版内部获取 */
    private _arrLocalRes = {}
    // 是否已经进入答题阶段
    public get Answering(): boolean {
        return this._qte.observe.curState >= QTE_STATE.ANSWERING;
    }

    private _drawControl: DrawControl;

    private _drawControlList: DrawControl[] = [];

    private feedBackData: any = null;

    private _openMode: QTE_OPENMODE = QTE_OPENMODE.SHOW_NORMAL;

    public set openMode(mode: QTE_OPENMODE) {
        this._openMode = mode;
    }
    public get openMode(): QTE_OPENMODE {
        return this._openMode;
    }

    // 是否强制隐藏题号,切换小题时，可设置
    public isForcedHideSerialUI: boolean = false;


    onLoad() {
        this._nGuideDuration = -1;
    }

    start() { //  这里start 执行有时候比较晚， 状态恢复完成与 与其加载可能出现渲染错误
    }

    loadResetAndSubmit() {
        return new Promise<void>((resolve, reject) => {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate start load resetAndSubmit begin`);
            let templateManager = QTEUtils.instance(QTETemplateManager, this.UUID);
            let qteUrl = "";
            if (templateManager && templateManager.resourceMaps && templateManager.resourceMaps['qte']) {
                qteUrl = templateManager.resourceMaps['qte'];
            }
            ResLoader.loadBundle("qte", null, (err, bundle: cc.AssetManager.Bundle) => {
                if (!err) {
                    ResLoader.loadRes(
                        "res/prefab/resetAndSubmit",
                        cc.Prefab,
                        (errRes, res) => {
                            if (!errRes) {
                                if (!cc.isValid(this.node,true)) {
                                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `QTETemplate  load resetAndSubmit fail`);
                                    if (qteUrl && qte.adapter.assetsCheck) {
                                        qte.adapter.assetsCheck({ path: qteUrl });
                                    }
                                    reject();
                                    return;
                                }
                                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate  load resetAndSubmit success`);
                                let node = cc.instantiate(res);
                                let resetAndSubmitCom = node.getComponent(QTEResetAndSubmit);
                                if (resetAndSubmitCom) {
                                    resetAndSubmitCom.qteBaseMeta = this.qteSys;
                                }
                                node.name = "resetAndSubmit";
                                this.node.addChild(node, cc.macro.MAX_ZINDEX - 1);
                                let widget = node.getComponent(cc.Widget);
                                widget.target = this.node;
                                widget.right = 30;
                                widget.bottom = 20;
                                widget.updateAlignment();
                                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate  load resetAndSubmit view`);
                                resolve();
                            } else {
                                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate  load resetAndSubmit fail ${errRes}`);
                                if (qteUrl && qte.adapter.assetsCheck) {
                                    qte.adapter.assetsCheck({ path: qteUrl });
                                }
                                reject();
                            }
                        },
                        bundle
                    );
                } else {
                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate start error ${err}`);
                    if (qteUrl && qte.adapter.assetsCheck) {
                        qte.adapter.assetsCheck({ path: qteUrl });
                    }
                    reject();
                }
            });
        }).catch(err => {
            qte && qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `QTETemplate  load resetAndSubmit fail  ==== ${err}`);
        });
    }


    /** 题版内私有资源加载函数 */
    async loadPrivateResource(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            console.log("%c Line:125 🍿 _arrLocalResList", "color:#ffdd4d", this._arrLocalResList);
            if (this._arrLocalResList.length === 0) {
                resolve();
                return;
            }
            let assetManager = QTEUtils.instance(QTEAssetsManager, this.UUID);
            assetManager.loadBundle(this._strBundleName, null, (err, bundle: cc.AssetManager.Bundle) => {
                if (err) {
                    reject();
                    return;
                }
                let resCount = this._arrLocalResList.length;
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `loadPrivateResource  _arrLocalResList ==== ${this._arrLocalResList}`);
                let lTime = new Date().getTime();
                let loadend = () => {
                    resCount--;
                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `loadPrivateResource resCount  ==== ${resCount}`,);
                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `loadPrivateResource lTime  ==== ${lTime - new Date().getTime()}`,);
                    lTime = new Date().getTime();
                    if (resCount == 0) {
                        resolve();
                    }
                }
                for (let i = 0; i < this._arrLocalResList.length; i++) {
                    let data = this._arrLocalResList[i];
                    let path = data.path;
                    let type = data.type;
                    assetManager.loadRes(path, type, (errRes, res) => {
                        if (errRes) {
                            reject();
                            return;
                        }
                        this._arrLocalRes[path] = res;
                        loadend();
                    }, bundle)
                }
            })
        }).catch(err => {
            qte && qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `loadPrivateResource Promise error  ==== ${err}`);
        });
    }

    getLocalAsset(path: string): cc.Asset {
        return this._arrLocalRes[path];
    }

    /**
     * 题版初始化完成后,由QTESystem调用
     */
    onCreated() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onCreated---")
        this.bindDrawComponent();
        let option = QTEFaceda.qteHooks;
        if (option && option.onCreate) {
            this.bolckInput();
            let vo = this.getBaseVo();
            option.onCreate.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onCreated--- end ");
                this.unbolckInput();
            });
        }
    }

    onStart() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onStart--- start");
        // 去掉定时器延迟，直接执行
        // this.scheduleOnce(() => {
        let option = QTEFaceda.qteHooks;
        if (option && option.onStart) {
            this.bolckInput();
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onStart--- 1");
            let vo = this.getBaseVo();
            option.onStart.apply(this, [vo]).then(() => {
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onStart--- 2");
                if (!this.node) {
                    return;
                }
                this.unbolckInput();
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onStart--- 3");
                // 题目开始
                // this.questionStart();
            });
        } else {
            // 题目开始
            // this.questionStart();
        }
        // });
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTETemplate--onStart--- end");
    }

    /**
     * 触发判断正误逻辑时,有题版调用
     * @param vo
     */
    onJudge(vo: QTETemplateJudgeParams) {
        console.log("####----QTETemplate--onJudge--->", vo);
        let option = QTEFaceda.qteHooks;
        if (option && option.onJudge) {
            this.bolckInput();
            option.onJudge.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                // TEST
                this.unbolckInput();
            });
        }
    }

    /**
     * 重置生命周期,reset触发,和题版自身reset触发,都有调用
     * @param vo
     */
    onReset(vo: QTETemplateParams) {
        // console.log("####----QTETemplate--onReset--->", vo);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--onReset---")
        let option = QTEFaceda.qteHooks;
        if (option && option.onReset) {
            this.bolckInput();
            option.onReset.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                // TEST
                this.unbolckInput();
            });
        }
    }

    /**
     * 题版暂停时触发
     * @param vo 
     */
    onPause(vo: QTETemplateParams) {
        // console.log("####----QTETemplate--onPause--->", vo);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--onPause---")
        let option = QTEFaceda.qteHooks;
        if (option && option.onPause) {
            this.bolckInput();
            option.onPause.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                // TEST
                this.unbolckInput();
            });
        }
    }

    /**
     * 题版恢复时触发
     * @param vo 
     */
    onResume(vo: QTETemplateParams) {
        // console.log("####----QTETemplate--onResume--->", vo);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTETemplate--onResume---")
        let option = QTEFaceda.qteHooks;
        if (option && option.onResume) {
            this.bolckInput();
            option.onResume.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                // TEST
                this.unbolckInput();
            });
        }
    }

    /**
     * 作答完成生命周期,submit触发,题版自身submit,都有调用
     * @param vo
     */
    onSubmit(vo: QTETemplateParams) {
        console.log("#--###------>", vo);
        if (vo.answerMark) {
            // 有作答痕迹,才能触发动画
            // let settings = QTEUtils.instance(QTETemplateManager, this.UUID).settings;
            // if (settings) {
            //     if (settings.playSubmitAni) {
            //         if (vo.correct) {
            //             this.answerCorrect();
            //         } else {
            //             this.answerWrong();
            //         }
            //     }
            // }
            let resetAndSubmitNode = this.node.getChildByName('resetAndSubmit');
            if (resetAndSubmitNode) {
                resetAndSubmitNode.getComponent(QTEResetAndSubmit).submitEnd()
            }
        }
        // console.log("####----QTETemplate--onSubmit--->", vo);
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, "####----QTETemplate--onSubmit---");
        let option = QTEFaceda.qteHooks;
        if (option && option.onSubmit) {
            this.bolckInput();
            option.onSubmit.apply(this, [vo]).then(() => {
                if (!this.node) {
                    return;
                }
                // TEST
                this.unbolckInput();
            });
        }
        this.endPlayVoice();
    }

    /** play接口 */
    public play() {
        this.questionStart();
    }

    // 题目开始
    questionStart() {
        console.log("questionStart")
        this.addDrawToCloneNode();
        this.gotoState(QTE_STATE.GUIDE);
        this.autoPlayVoice();
        qte.playfWComponents();
        // this.listenRetellPlayReady();
    }

    /** 听后转述倒计时开始 */
    private listenRetellPlayReady() {
        let listenRetell = this.getEntitiesBySubType('listenRetell');
        for (let i = 0; i < listenRetell.length; i++) {
            let lr = listenRetell[i];
            if (lr.children && lr.children[0]) {
                let lrcom = lr.children[0].getComponent(ListenRetellComponent);
                if (lrcom) {
                    lrcom.startReady();
                }
            }
        }
    }

    /** 结束音频播放*/
    private endPlayVoice() {
        console.log("endPlayVoice 小题结束音频播放")
        let audios = this.getEntitiesBySubType('speaker');
        for (let i = 0; i < audios.length; i++) {
            let audio = audios[i];
            if (audio.children && audio.children[0]) {
                let audioCom = audio.children[0].getComponent(SpeakerComponent);
                if (audioCom) {
                    audioCom.stopVoice();
                }
            }
        }
    }

    /** 自动播放音频 */
    private autoPlayVoice() {
        let audios = this.getEntitiesBySubType('speaker');
        for (let i = 0; i < audios.length; i++) {
            let audio = audios[i];
            if (audio.children && audio.children[0]) {
                let audioCom = audio.children[0].getComponent(SpeakerComponent);
                if (audioCom) {
                    audioCom.resetVoice();
                    audioCom.autoPlayVoice();
                }
            }
        }
    }

    SYNC<T extends object>(target: T): T {
        if (!(this.state instanceof QTEBaseState)) {
            console.error("state don't instance of QTEBaseState！");
            return null;
        }
        // 类型检查，是否空值等检查
        for (let k in target) {
            if (target[k] == undefined || target[k] == null) {
                console.error("can not null");
            }
            // 校验是否是map，提示用object代替
            if (target[k] instanceof Map) {
                console.error("同步数据不能使用Map，请使用Object来代替。")
            }
        }
        return reactive(target) as T;
    }

    __init() {
        if (!this.state) {
            this.state = new QTEBaseState();
        }
        this.state = this.SYNC(this.state);
        for (let k in this.state.baseWatcher()) {
            watch(
                () => this.state[k],
                (newVal, oldVal) => {
                    let obj = {};
                    obj[k] = ValueUtils.clone(this.state[k]);
                    qte.instance(QTEStateManager).updateState(this.qteSys.UUID, "custom", obj);
                },
                { deep: true }
            )
        }
        for (let k in this.watcher()) {
            watch(
                () => this.state[k],
                (newVal, oldVal) => {
                    let obj = {};
                    obj[k] = ValueUtils.clone(this.state[k]);
                    // 此处来收集状态变化
                    qte.instance(QTEStateManager).updateState(this.qteSys.UUID, "custom", obj);
                    if (this.watcher()[k]) {
                        this.watcher()[k](newVal, oldVal);
                    }
                },
                { deep: true }
            );
        }
    }

    /**
     * 此方法需要重写，用于watch所需要的数据
     * @returns {[key: string]: (val: any, oldVal: any) => void}
     */
    watcher(): { [key: string]: (val: any, oldVal: any) => void } {
        return null;
    }

    /**
     * 切换状态
     * @param {QTE_STATE} state 状态
     */
    public gotoState(state: QTE_STATE): void {
        if (this.qteSys.observe) {
            QTEUtils.instance(ObserveManager, this.UUID).gotoState(state, this.qteSys.observe);
        }
    }

    /**
     * 根据业务组件id获取实体节点
     * @param {string} id 业务组件id
     */
    public getEntityById(id: string): QTEEntityNode {
        return QTEUtils.instance(QTEEntityManager, this.UUID).getEntityById(id);
    }

    /**
     * 根据业务组件tag获取实体节点
     * @param {string} tag 业务组件tag
     */
    public getEntitiesByTag(tag: string): QTEEntityNode[] {
        return QTEUtils.instance(QTEEntityManager, this.UUID).getEntitiesByTag(tag);
    }

    public getEntitiesMap(): Map<string, QTEEntityNode[]> {
        return QTEUtils.instance(QTEEntityManager, this.UUID).entitiesTagMap;
    }

    public getEntitiesList(): QTEEntityNode[] {
        return QTEUtils.instance(QTEEntityManager, this.UUID).entitiesList;
    }

    public getEntitiesBySubType(subType: string): QTEEntityNode[] {
        return QTEUtils.instance(QTEEntityManager, this.UUID).getEntityBySubType(subType);
    }

    /**
     * 获取动画配置
     * @param name
     * @returns
     */
    public getAnimationConfig(name: string) {
        let animations = this.getCurrentData().animationsConfigForClient;
        if (animations && animations.hasOwnProperty(name)) {
            return animations[name];
        }
        return null;
    }
    /**
     * 播放动画
     * @param name 动画名称
     * @param callback 动画回调
     * @param data 动画数据
     */
    public playAnimation(name: string, callback: () => void): void {
        if (this.hasAnimation(name)) {
            let animations = this.getCurrentData().animationsForClient[name];
            QTEUtils.instance(QTEAnimationManager, this.UUID).playAnimation(animations, callback, name, this.getAnimationConfig(name));
        } else if (callback) {
            callback();
        }
    }

    /**
     * 使用动画数据播放动画,适用于题组逻辑
     * @param name
     * @param callback
     * @param data
     */
    public playAnimationWithData(name: string, callback: () => void, data: AnimationData[], aniConfig: any): void {
        QTEUtils.instance(QTEAnimationManager, this.UUID).playAnimation(data, callback, name, aniConfig);
    }

    /**
     * 跳过动画，直接展示完成状态
     * @param name
     */
    public skipAnimation(name: string): void {
        QTEUtils.instance(QTEAnimationManager, this.UUID).skipAnimation(name);
    }
    /**
     * 判断是否存在动画
     */
    public hasAnimation(name: string) {
        let currentData = QTEUtils.getCurrentData(this.UUID);
        let animations = currentData.animationsForClient[name];
        if (animations) {
            return true;
        }
        return false;
    }

    /** 获取全局配置 */
    public getStageConfig(): StageConfigData {
        if (this._stageConfig == null) {
            let stageData = QTEUtils.getCurrentData(this.UUID).stageData;
            let extraStageData = QTEUtils.getCurrentData(this.UUID).extraStageData;
            this._stageConfig = {
                width: stageData.width,
                height: stageData.height,
                safeWidth: stageData.safeWidth,
                safeHeight: stageData.safeHeight,
                // 这里将舞台全局属性全部导出，后续不用再次添加其它属性了❗️❗️❗️
                extraStageData: extraStageData,
                answerWrongCount: extraStageData.answerWrongCount,
                errorType: extraStageData.errorType,
                isAutoSubmit: extraStageData.isAutoSubmit,
                defaultAction: extraStageData.defaultAction,
                dragRelation: extraStageData.dragRelation,
                closeDefaultFeedback: extraStageData.closeDefaultFeedback,
            };
        }

        return this._stageConfig;
    }
    public getAutoSubmitMode(): qte.AutoSubmitMode {
        let extraStageData = this.getStageConfig().extraStageData;
        let category = this.getCurrentData().template.category;
        let autoSubmitMode: qte.AutoSubmitMode = qte.AutoSubmitMode.OPERATE_SUBMIT; // 作答即提交
        if (category == 1010 || category == 1011) {
            autoSubmitMode = qte.AutoSubmitMode.RIGHT_SUBMIT;  // 答对提交
        }
        if (typeof (extraStageData.autoSubmitMode) != 'undefined') {
            autoSubmitMode = extraStageData.autoSubmitMode;
        }
        return autoSubmitMode;
    }

    public getCurrentData(): any {
        return QTEUtils.getCurrentData(this.UUID);
    }

    /**
     * 回答正确
     * @param {AnswerContent} data 答题数据
     */
    public answerCorrect() {
        this.gotoState(QTE_STATE.ANSWER_CORRECT);
    }

    /**
     * 回答错误
     * @param {AnswerContent} data 答题数据
     */
    public answerWrong() {
        this.gotoState(QTE_STATE.ANSWER_WRONG);
    }

    /** 禁止操作 */
    public bolckInput() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_BLOCK, "========== qte template bolckInput ===========")
        let block = this.node?.getChildByName("Template-BolckInput");
        if (!block) {
            block = new cc.Node("Template-BolckInput");
            block.width = cc.winSize.width;
            block.height = cc.winSize.height;
            block.addComponent(cc.BlockInputEvents);
            block.zIndex = cc.macro.MAX_ZINDEX;
            block.parent = this.node;
            block.on(cc.Node.EventType.TOUCH_START, () => {
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_BLOCK, this.name)
            });
        }
        block.x = 0;
        block.y = 0;
        this._nLockCount++;
        // 锁屏60s超时
        cc.tween(block)
            .delay(60)
            .call(() => {
                this._nLockCount = 0;
                this.unbolckInput();
            })
            .start();
    }

    /** 解锁禁止操作 */
    public unbolckInput() {
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_BLOCK, "========== qte template unbolckInput ===========")
        this._nLockCount--;
        if (this._nLockCount <= 0) {
            let block = this.node?.getChildByName("Template-BolckInput");
            if (block) {
                cc.Tween.stopAllByTarget(block);
                block.y = cc.winSize.height * 3;
            }
            this._nLockCount = 0;
        }
    }

    /** 硬性解锁,不建议使用,除非你真的清楚自己在做什么 */
    public UNBLOCKINPUT() {
        qte.log("==========###UNBLOCKINPUT###===========");
        let block = this.node?.getChildByName("Template-BolckInput");
        if (block) {
            cc.Tween.stopAllByTarget(block);
            block.y = cc.winSize.height * 2;
        }
        this._nLockCount = 0;
    }


    page_hide() {
        console.log("------page_hide---")
    }

    page_show() {
        console.log("------page_show---")
    }

    /** 
     * 点击右上方最小化
     */
    fold() {
        console.log("###--qteTemplate fold")
    }

    /** 
     * 点击左下角继续作答
     */
    unfold() {
        console.log("###--qteTemplate unfold")
    }


    judge(answerMack: boolean, singleCorrect: boolean) {
        this.onJudge(this.getJudgeVo(answerMack, this._bCorrect, singleCorrect))
    }

    protected setSubmitAnswer(): any {
        console.log('you can modify the fun to set submit answer data');
        return {};
    }
    /**
     * framework 同步特殊消息
     * @param key 
     * @param data 
     */
    public upateCustomData(key: string, data: any) {
        console.log("key:", key, data);
    }
    /**
     * 读题后调用
     */
    public onStartAnswering() {

    }

    /** 调用编辑器配置动画之前 业务自定义 */
    onAnswerActionFeedBackBefore(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            resolve();
        }).catch(err => {
            qte && qte.logCatBoth('cocos playFeedBackCustomActionBefore promise error:', err);
        });
    }
    /**
    * framework 播放反馈动画
    * @param key 
    * @param data 
    */
    public async playFeedBackAction(data: any) {
        this.feedBackData = data;
        let _bIsOperated = this._bIsOperated;
        let correct = this._bCorrect;
        this.bolckInput();
        qte && qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_SUBMIT, 'cocos playFeedBackAction');
        // 未操作，不播放动画
        if (_bIsOperated && cc.isValid(this.node, true)) {
            await this.onAnswerActionFeedBackBefore();
        }
        if (!cc.isValid(this.node, true)) { // 节点销毁了快速返回
            qte && qte.logCatBoth('qte题版销毁了', '来不及播放反馈动画了快速返回');
            return;
        }
        let settings = QTEUtils.instance(QTETemplateManager, this.UUID).settings;
        if (_bIsOperated && settings && settings.playSubmitAni) {
            if (correct) {
                this.answerCorrect();
            } else {
                this.answerWrong();
            }
        } else {
            this.onAnswerActionEnd()
        }
    }

    /** 调用编辑器配置动画之后 业务自定义 */
    onAnswerActionFeedBackEnd(): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            resolve();
        }).catch(err => {
            qte && qte.logCatBoth('cocos playFeedBackCustomActionEnd promise error:', err);
        });
    }

    /**
     * 结束播放反馈动画后调用端上缩小屏幕 通用弹窗
     */
    public async onAnswerActionEnd() {
        if (this._bIsOperated && cc.isValid(this.node, true)) {
            await this.onAnswerActionFeedBackEnd();
        }
        let option = QTEFaceda.qteHooks;
        if (option && option.onShowResult) {
            option.onShowResult(this.feedBackData);
        }
        this.unbolckInput();
        qte && qte.logCatBoth('cocos playFeedBackActionEnd', '关闭遮罩');
    }


    /**
     * 提交
     */
    submit(): QTETemplateSubmitParams {
        let submitAnswer = this.setSubmitAnswer();
        let vo = this.getSubmitVo(this._bIsOperated, this._bCorrect, submitAnswer)
        this.onSubmit(vo);
        this.onSubmitEnd();
        console.log("%c Line:668 🥒 vo submit ", "color:#7f2b82", vo);
        return ValueUtils.clone(vo);
    }

    pause() {
        this.onPause(this.getTemVo(this._bIsOperated, this._bCorrect));
    }

    resume() {
        this.onResume(this.getTemVo(this._bIsOperated, this._bCorrect));
    }

    public onSubmitBeforeCheck(): boolean {
        return true;
    }

    /**
     * 提交完成给题版内部使用的
     */
    protected onSubmitEnd() {
        console.log("###--qteTemplate onSubmitEnd");
    }

    public onResetBeforeCheck(): boolean {
        return true;
    }
    /**
     * 重置后给题版内部使用
     */
    protected onResetEnd() {
        console.log("###--qteTemplate onResetEnd");
    }


    reset() {
        // 根据初始显示状态，还原entityList
        let entityList = this.getEntitiesList();
        for (let i = 0; i < entityList.length; i++) {
            let entity = entityList[i];
            entity.resetData();
        }
        // 停止所有音频
        QTEUtils.instance(QTEAudioManager, this.UUID).stopAllAudios();
        // 重置音频组件
        this.autoPlayVoice();
        qte.resetPlayfWComponents();
        this.resetComps();
        this.onReset(this.getTemVo(this._bIsOperated, this._bCorrect))
        let resetAndSubmitNode = this.node.getChildByName('resetAndSubmit');
        if (resetAndSubmitNode) {
            resetAndSubmitNode.getComponent(QTEResetAndSubmit).refreshUI()
        }
        this._bIsOperated = false;
        this._bCorrect = false;
        this.UNBLOCKINPUT();
        this.onResetEnd();
        this.gotoState(QTE_STATE.GUIDE);
    }


    getBaseVo(): QTETemplateCommonParams {
        let currentData = QTEUtils.getCurrentData(this.UUID);
        console.log('%c 🍆 currentData: ', 'font-size:20px;background-color: #FCA650;color:#fff;', currentData);
        if (currentData && currentData.template) {
            return {
                type: currentData.template.type,
                tempType: currentData.template.tempType,
                questionType: currentData.questionType,
                name: currentData.template.name,
                resourceList: currentData.resourceList,
                thumbnail: currentData.thumbnail,
                guide: currentData.extraStageData.guide,
                width: currentData.stageData.width,
                height: currentData.stageData.height,
                safeHeight: currentData.stageData.safeHeight,
                safeWidth: currentData.stageData.safeWidth,
                backgroundColor: currentData.stageData.backgroundColor,
                bgColor: currentData.stageData.bgColor,
                texture: currentData.stageData.texture,
                bundleUrl: currentData.template.bundleUrl,
                category: currentData.template.category,
                information: {
                    desc: currentData.template.name
                },
                answerBase: this._answerBase,
                qteKey: this.UUID
            };
        }

    }

    /**
     * 获取判断题型
     * @param answerMark //是否作答
     * @param correct //题目是否正确
     * @param singleCorrect //本次是否正确
     * @returns
     */
    getJudgeVo(answerMark: boolean, correct: boolean, singleCorrect: boolean): QTETemplateJudgeParams {
        // answerMark: boolean; //是否作答
        // correct: boolean;//题目是否正确
        // singleCorrect: boolean; //本次是否正确
        let vo = new QTETemplateJudgeParams();
        vo = Object.assign(vo, this.getBaseVo());
        vo.answerMark = answerMark;
        vo.correct = correct;
        vo.singleCorrect = singleCorrect;
        return vo;
    }

    /**
     * 获取声明周期使用Vo
     * @param answerMark
     * @param correct
     * @returns
     */
    getTemVo(answerMark: boolean, correct: boolean): QTETemplateParams {
        let vo = new QTETemplateParams();
        vo = Object.assign(vo, this.getBaseVo());
        vo.answerMark = answerMark;
        vo.correct = correct;
        return vo;
    }

    /**
     * 获取提交数据使用的Vo
     * @param answerMark 
     * @param correct 
     * @returns 
     */
    private getSubmitVo(answerMark: boolean, correct: boolean, submitAnswer: any): QTETemplateSubmitParams {
        let vo = new QTETemplateSubmitParams();
        vo = Object.assign(vo, this.getBaseVo());
        vo.answerMark = answerMark;
        vo.correct = correct;
        vo.submitAnswer = submitAnswer;
        return vo;
    }

    public playAudio(url: string | cc.AudioClip, loop: boolean = false, callback?: () => void): number {
        return QTEUtils.instance(QTEAudioManager, this.uuid).playAudio(url, loop, callback);
    }

    /** 获取localSprite data */
    getLocalSpriteData(id: string, tag: string, localUrl: string): ComponentData {
        return {
            "tag": tag,
            "type": "sprite",
            "properties": {
                width: 0,
                height: 0,
                angle: 0,
                active: true,
                x: 0,
                y: 0,
                opacity: 255,
                zIndex: 0,
                str: "",
                horizontalAlign: 1,
                texture: localUrl
            },
            "id": id,
            "extra": {
                "tag": tag
            },
            hideBeforeAnimation: false
        }
    }

    /** 跟进题版内置资源地址, 创建entity Sprite
     * @id entityNode Id
     * @tag entityNode tag
     * @localUrl 题版bundle内路径
     */
    async createLocalSprie(id: string, tag: string, localUrl: string, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resole, reject) => {
            let componetData: ComponentData = this.getLocalSpriteData(id, tag, localUrl)
            let entityNode = await this.createEntityNode(componetData, {}, parent);
            resole(entityNode);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }


    /** 根据组件结构数据创建组件
     * createEntityNode
     * @param componetData 
     * @param extraProp 
     * @param parent 
     * @returns 
     */
    public async createEntityNode(componetData: ComponentData, extraProp: QuestionMetaData, parent: cc.Node): Promise<QTEEntityNode> {
        return new Promise<QTEEntityNode>(async (resole, reject) => {
            let entityNode = await QTEUtils.instance(QTEStageFactory, this.UUID).createEntityNode(componetData, extraProp, this.UUID, parent);
            resole(entityNode);
        }).catch(err => {
            qte && qte.logCatBoth('QTETemplate cocos promise error:', err);
            return null;
        });
    }

    // destroy(): boolean{
    //     this.onDestory();
    //     return super.destroy();
    // }

    onDestroy() {
        let option = QTEFaceda.qteHooks;
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_RESET, "####----QTETemplate--onDestroy---")
        if (option && option.onDestroy) {
            let vo = this.getBaseVo();
            vo && option.onDestroy.apply(this, [vo]).then(() => { });
        }
        // console.log("##############QTETemplate -- onDestory############")
        // console.log("##############QTETemplate -- onDestory############--->", this.UUID);
        // 题版销毁时，停掉所有正在播放的音频
        QTEUtils.instance(QTEAudioManager, this.UUID).stopAllAudios();
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_RESET, "##############QTETemplate -- onDestory############---UUID--->" + this.UUID)
        // 清空本地缓存
        // QTEUtils.instance(QTEAssetsManager, this.UUID).clearAssets();
        QTEUtils.desctroyByUUID(this.UUID);
        qte.instance(QTEStateManager).clearState(this.UUID);
        this.UNBLOCKINPUT();
    }

    // 画笔相关
    bindDrawComponent() {
        let entities = this.getEntitiesBySubType(SpecialComponentType.Brush);
        if (entities.length == 0) {
            return;
        }
        console.log("bindDrawComponent entities", entities);
        let controlNode = entities[0];
        this._drawControl = controlNode.getChildByName('drawControl').getComponent(DrawControl);
        let compManager = QTEUtils.instance(QTECompManager, this.UUID);
        this._drawControl.setGetCompByIdFunc(compManager.getCompById.bind(compManager));
        this._drawControl.setGetCompByTypeFunc(compManager.getCompByType.bind(compManager));
        let compData = controlNode.componentData;
        let mountList = compData.properties.mountList;
        if (mountList.length == 0) { // 全局绘图
            let root = cc.find(QTEStageFactory.ROOT_NAME, this.node);
            let node = new cc.Node();
            node.width = this.node.width;
            node.height = this.node.height;
            node.parent = root;
            node.zIndex = cc.macro.MAX_ZINDEX - 2;
            this.addDrawComp(node);
        } else {
            for (let i = 0; i < mountList.length; i++) {
                const id = mountList[i];
                let entity = this.getEntityById(id);
                this.addDrawComp(entity);
            }
        }
        controlNode.zIndex = cc.macro.MAX_ZINDEX;

    }

    // 重置所有组件
    public resetComps() {
        let comps = QTEUtils.instance(QTECompManager, this.UUID).getAllComp();
        comps.forEach(comp => {
            comp.reset();
        });
        this._drawControl && this._drawControl.reset();
        //如果存在多个画笔组件，重置所有画笔组件
        if (this._drawControlList) {
            for (let i = 0; i < this._drawControlList.length; i++) {
                this._drawControlList[i].reset();
            }
        }
    }
    /**
     * 
     * @param entity 挂载画笔组件的节点
     * @param index 画笔组件index，如果有多个画笔组件，需要传入对应的index(当前仅有物理作图题使用)
     * @returns 
     */
    private addDrawComp(entity, index?: number) {
        if (!entity) {
            return;
        }
        let scrollView = entity.getComponent(cc.ScrollView);
        let comp: DrawCmpt;
        if (scrollView && scrollView.content) {
            scrollView.content.removeComponent(cc.Layout);
            comp = scrollView.content.addComponent(DrawCmpt);
            comp.setParentScroll(scrollView);
        } else if (entity.name == SpecialComponentType.H5Label) {
            let scrollView = cc.find("h5Label", entity).getComponent(cc.ScrollView);
            if (scrollView) {
                comp = scrollView.content.addComponent(DrawCmpt);
                comp.setParentScroll(scrollView);
            }
        }
        else if (entity.name == SpecialComponentType.Readcom) { // 阅读理解特殊处理
            let len = cc.find("readcom", entity).getComponent(ReadcomComponent).questionData['optionsList'].length;
            let scrollView0 = cc.find("readcom/questionRoot/questionNode", entity).getComponent(cc.ScrollView);
            let comp0 = cc.find("content", scrollView0.content.parent).addComponent(DrawCmpt);
            comp0.setParentScroll(scrollView0);
            comp0.qteBaseMeta = this.qteSys;
            QTEUtils.instance(QTECompManager, this.UUID).addComp(comp0, QTEComponentType.DrawCmpt);
            for (let i = 0; i < len; i++) {
                let scrollView1 = cc.find("readcom/questionRoot/anwersNode", entity).getComponent(cc.ScrollView);
                let comp1 = cc.find("content" + i, scrollView1.content.parent).addComponent(DrawCmpt);
                comp1.setParentScroll(scrollView1);
                comp1.qteBaseMeta = this.qteSys;
                QTEUtils.instance(QTECompManager, this.UUID).addComp(comp1, QTEComponentType.DrawCmpt);
            }
            return;
        }
        else if (entity.name == SpecialComponentType.SKETCH) {
            for (let i = 0; i < 3; i++) {
                let drawNode = null;
                if (i == 0)
                    drawNode = cc.find("optComp/mask/drawArea", entity);
                else if (i == 1)
                    drawNode = cc.find("optComp/mask/drawArea1", entity);
                else if (i == 2)
                    drawNode = cc.find("optComp/mask/drawArea2", entity);
                if (drawNode) {
                    comp = drawNode.addComponent(DrawCmpt);
                }
                else {
                    qte && qte.logCatBoth("QTETemplate addDrawToCloneNode error:", "sketch/optComp/mask/drawArea not found")
                    return
                }
                comp.qteBaseMeta = this.qteSys;
                QTEUtils.instance(QTECompManager, this.UUID).addComp(comp, QTEComponentType.DrawCmpt);
            }
            return;
        }
        else {
            comp = entity.addComponent(DrawCmpt);
        }
        comp.qteBaseMeta = this.qteSys;
        QTEUtils.instance(QTECompManager, this.UUID).addComp(comp, QTEComponentType.DrawCmpt);
    }

    private addDrawToCloneNode() {
        let comps = QTEUtils.instance(QTECompManager, this.UUID).getCompByType(QTEComponentType.DrawCmpt);
        for (let i = 0; i < comps.length; i++) {
            let entity = comps[i].node as QTEEntityNode;
            if (entity && entity.extraDataMap && entity.extraDataMap.num > 0) {
                let num = entity.extraDataMap.num;
                for (let j = 0; j < num; j++) {
                    let node = this.getEntityById(`${entity.qid}_${j + 1}`);
                    this.addDrawComp(node);
                }
            }
        }
    }

    public showCorrect() {

    }
    /**
     *  自定义作答子方法
     */
    public customizeSwitchQuestion(index) {

    }

    public addSerialUI(data: qte.QTESerialUIParams) {
        this.switchQuestionData = data;
        if (!cc.isValid(this.node, true)) {
            qte.logCatBoth("addSerialUI", "节点已销毁无法添加了")
            return;
        }

        let cusNode = this.node.getChildByName("serialChangeUI");
        if (cusNode) {
            let customChangeCom = cusNode.getComponent(QTESerialChangeUI);
            customChangeCom.initData(data, this.openMode);
            if (this.isForcedHideSerialUI) {
                cusNode.active = false;
            }
            if (!this.isForcedHideSerialUI && cusNode.active == false) {
                cusNode.active = true;
            }
            return;
        }
        ResLoader.loadBundle("qte", null, (err, bundle: cc.AssetManager.Bundle) => {
            if (!err) {
                ResLoader.loadRes(
                    "res/prefab/serialChangeUI",
                    cc.Prefab,
                    (errRes, res) => {
                        if (!errRes) {
                            if (!cc.isValid(this.node, true)) {
                                qte.logCatBoth("addSerialUI", "parent节点已销毁无法添加了")
                                return;
                            }
                            if (!this.isForcedHideSerialUI) {
                                let node: cc.Node = cc.instantiate(res);
                                if (!node) {
                                    qte.logCatBoth("addSerialUI", "addChild node节点已销毁无法添加了")
                                    return;
                                }
                                let customChangeCom = node.getComponent(QTESerialChangeUI);
                                customChangeCom.initData(data, this.openMode);
                                node.active = true;
                                this.node.addChild(node, cc.macro.MAX_ZINDEX - 5);
                            }
                        }
                    },
                    bundle
                );
            }
        });
    }

    public showUserAnswerState(data: any) {

    }

    /**
     * 增加透明触摸屏蔽层
     *
     * @return {*} 
     * @memberof QTETemplate
     */
    public addTouchMask() {
        let touchMaskNode = this.node.getChildByName('touchMask');
        if (touchMaskNode) {
            return;
        }
        ResLoader.loadBundle('qte', null, (err, bundle: cc.AssetManager.Bundle) => {
            if (!err) {
                ResLoader.loadRes(
                    'res/prefab/touchMask',
                    cc.Prefab,
                    (errRes, res) => {
                        if (!errRes) {
                            if (!this.node) {
                                return;
                            }
                            let node: cc.Node = cc.instantiate(res);
                            this.node.addChild(node, cc.macro.MAX_ZINDEX - 6);
                        }
                    },
                    bundle,
                );
            }
        });
    }

    /**
     * @description: 增加作答结果 UI
     * @param {Object: {}} objResult  .arrResult作答数据数组类型  .dt回调时间<可选>  .cb回调函数<可选>
     * @return {*}
     */
    public async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node> {
        return new Promise<cc.Node>(async (resolve, reject) => {
            // * 增加外层限制，通过配置来判断是否加载
            let _isAnswerResultUI = QTEUtils.instance(QTETemplateManager, this.UUID).settings.isAnswerResultUI;
            if (!_isAnswerResultUI) {
                objResult.cb && objResult.cb();
                resolve(null);
                return;
            }
            let answerResultNode = this.node.getChildByName('answerResultUI');
            if (answerResultNode) {
                answerResultNode.destroy();
                answerResultNode = null;
            }
            let node = await QTEUtils.addAnswerResultUI(objResult);
            if (cc.isValid(node, true) && cc.isValid(this.node, true)) {
                this.node.addChild(node);
                resolve(node);
            }
            else {
                resolve(null);
                return;
            }
        }).catch(err => {
            qte && qte.logCatBoth('QTETemplate cocos promise error:', err);
            return null;
        });
    }


    /** 编辑器预览 提交正确后 
    * 调用次接口提供参考答案 
    * 获取参考答案 
    * 保存在referenceAnswer字段中
    * */
    public setReferenceAnswer(): any {
        return null;
    }
    /**  获取参考答案*/
    public getReferenceAnswer(): any[] | null {
        let currData = QTEUtils.getCurrentData(this.UUID);
        let referenceAnswer = null;
        if (currData.template && currData.template.referenceAnswer) {
            referenceAnswer = currData.template.referenceAnswer;
        }
        return referenceAnswer;
    }
}
