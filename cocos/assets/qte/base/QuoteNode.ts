
import { QTETemplateCommonParams, QTETemplateSubmitParams, } from "../vo/QTEEnum";
import QTEAssetsManager from "../manager/QTEAssetsManager";
import QTETemplateManager from "../manager/QTETemplateManager";
import QTEUtils from "../util/QTEUtils";
import QTETemplate from "./QTETemplate";

export interface ExportTemData {
    guideDuration: number;
    /** 答案 */
    answer?: string;
    /** 答案列表 */
    optionList?: string;
}
/**
 * 题版创建后,返回的显示对象应用
 */
export default class QuoteNode extends cc.Node {
    /**  */
    UUID: string = "";
    /**
     * 题版提交接口;
     * 调用此接口后,会回调初始化时注入的onSubmit钩子函数
     */
    submit(): QTETemplateSubmitParams | undefined {
        let template = this.getTemplate();
        if (cc.isValid(template, true)) {
            return template.getComponent(QTETemplate).submit();
        }
        return undefined;
    }

    /** 题版play接口，开始播放题干/自动播放的音频 */
    play(): void {
        let template = this.getTemplate()
        console.log("quoteNode Play", template)
        if (template) {
            return template.getComponent(QTETemplate).play();
        }
    }
    /** 题版  主动提交前提示 */
    onSubmitBeforeCheck() {
        let template = this.getTemplate()
        if (template) {
            return template.getComponent(QTETemplate).onSubmitBeforeCheck();
        }
        return true;
    }

    /** 题版 重置前提示 */
    onResetBeforeCheck() {
        let template = this.getTemplate()
        if (template) {
            return template.getComponent(QTETemplate).onResetBeforeCheck();
        }
        return true;
    }



    /**
     * 题版重置接口;
     * 重置题版回复到初始化状态,会调用初始化注入的onReset钩子函数
     */
    reset() {
        let template = this.getTemplate()
        if (template) {
            template.getComponent(QTETemplate).reset();
        }
    }

    /**
     * 获取题版配置数据接口
     * @returns 题版配置数据 
     */
    getBaseVo(): QTETemplateCommonParams | undefined {
        let template = this.getTemplate();
        if (template) {
            return template.getComponent(QTETemplate).getBaseVo()
        }
        return undefined;
    }

    /**
     * 题版暂停接口,会调用cc.director.pause();并回调初始化注入的onPause钩子函数
     */
    pause() {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).pause()
        }
        cc.director.pause();
    }

    /**
     * 题版恢复接口,会调用cc.director.resume();并回调初始化注入的onResume钩子函数
     */
    resume() {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).resume()
        }
        cc.director.resume();
    }

    /**
     * 展示正确答案
     */
    showCorrect() {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).showCorrect()
        }
    }

    /**
    * 展示用户作答
    */
    showUserAnswerState(data: any) {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).showUserAnswerState(data)
        }

    }

    /** 获取题版的引用 */
    getTemplate(): cc.Node {
        let child = this.getChildByName("QTETemplate");
        return child;
    }

    /**调用通用反馈动画 */
    playFeedBackAction(data: any): void {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).playFeedBackAction(data);
        }
    }

    /** upateCustomData */
    upateCustomData(key: string, data) {
        let template = this.getTemplate();
        if (template) {
            template.getComponent(QTETemplate).upateCustomData(key, data);
        }
    }

    /** 获取题干音频时长 */
    async getTemData(): Promise<ExportTemData> {
        return new Promise<ExportTemData>(async reslove => {
            let exportData: ExportTemData = {
                guideDuration: 0
            }
            // 题干音频
            let duration = 0;
            let currentData = QTEUtils.getCurrentData(this.UUID);
            // 题干音频
            if (currentData.extraStageData.guide) {
                let audioRes = await QTEUtils.instance(QTEAssetsManager, this.UUID).loadResourceByUrl(currentData.extraStageData.guide);
                if (audioRes) {
                    duration = (audioRes as cc.AudioClip).duration;
                    console.log("#-------1-duration---->", duration);
                }
            }
            // 自动播放音频
            for (let component of currentData.components) {
                if (component.type == 'specialComponent') {
                    if (component.subType == 'speaker') {
                        if (component.properties.autoPlay) {
                            let audioUrl = component.properties.audioUrl;
                            let audioRes = await QTEUtils.instance(QTEAssetsManager, this.UUID).loadResourceByUrl(audioUrl);
                            if (audioRes) {
                                let dura = (audioRes as cc.AudioClip).duration;
                                if (component.properties['countdown']) { // 3秒倒计时
                                    dura += 3;
                                }
                                if (dura > duration) {
                                    duration = dura;
                                    console.log("#-------2-duration---->", duration);
                                }
                            }
                        }
                    }
                }
            }

            // 如果有语音组件,上报语音时长
            for (let component of currentData.components) {
                if (component.type == 'specialComponent') {
                    if (component.subType == 'voice') {
                        if (component.properties.answerDuration > duration) {
                            duration = component.properties.answerDuration;
                            console.log("#-------3-duration---->", duration);
                        }
                    }
                }
            }

            exportData.guideDuration = duration;
            if (currentData.template.bundleName == "sharkSelectQuestion") {
                let answer = "";
                let optionList = "";
                for (let component of currentData.components) {
                    if (component.tag == "answer") {
                        if (optionList != "") {
                            optionList += ","
                        }
                        optionList += component.id;
                        if (currentData.extraDataMap[component.id].isCorrect) {
                            if (answer != "") {
                                answer += ","
                            }
                            answer += component.id;
                        }
                    }
                }
                exportData.answer = answer;
                exportData.optionList = optionList;
            }
            reslove(exportData);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    /**设置编辑器预览参考答案/*/
    setReferenceAnswer(): any {
        let template = this.getTemplate();
        if (template) {
            return template.getComponent(QTETemplate).setReferenceAnswer();
        }
        return null;
    };
    /**
     *  切换小题，单题直接返回
     */
    async switchQuestion(index?, isForcedHideSerialUI?): Promise<any> {
        let template = this.getTemplate();
        // index 为空为单题
        if (index == null) {
            return;
        }
        if (template) {
            let _currentComp = template.getComponent(QTETemplate);
            _currentComp.isForcedHideSerialUI = isForcedHideSerialUI;
            if (_currentComp?.switchQuestionData?.changeQuestion) {
                await _currentComp.switchQuestionData.changeQuestion(index);
            } else if (_currentComp?.customizeSwitchQuestion) {
                await _currentComp.customizeSwitchQuestion(index);
            }
        }
    }
}