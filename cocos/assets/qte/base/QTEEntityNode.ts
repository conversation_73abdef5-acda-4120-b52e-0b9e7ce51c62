import AnimationCmpt from "../components/AnimationCmpt";
import ValueUtils from "../core/utils/ValueUtils";
import QTEAssetsManager from "../manager/QTEAssetsManager";
import QTEEntityManager from "../manager/QTEEntityManager";
import QTEStateManager from "../manager/QTEStateManager";
import QTETemplateManager from "../manager/QTETemplateManager";
import { ComponentData, QTE_LOGCAT_TYPE } from "../qte-core-export";
import QTEUtils from "../util/QTEUtils";

export default class QTEEntityNode extends cc.Node {
    // component
    public componentData: any;
    // qte uuid
    public qteUUID: string;
    // id
    public qid: string;
    // tag
    public qtag: string;
    // 节点属性
    public properties: {
        [key: string]: any;
    };
    // 业务属性
    public extraDataMap: {
        [key: string]: any;
    };
    // 插槽属性
    public childComponents: ComponentData[];
    // 额外数据
    public extra: any = null;
    // 当前节点自定义组件配置
    public customCmpts: { cmpt: new () => cc.Component; options?: any }[] = null;

    /** 实体初始化接口 */
    public initEntity(qteId: string) {
        this.qteUUID = qteId;

        this.on(cc.Node.EventType.POSITION_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { x: Number(this.x.toFixed(2)), y: Number(this.y.toFixed(2)) });
        }, this);

        this.on(cc.Node.EventType.ROTATION_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { angle: this.angle });
        }, this);

        this.on(cc.Node.EventType.SCALE_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { scale: this.scale });
        }, this);

        this.on(cc.Node.EventType.SIZE_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { width: this.width, height: this.height });
        }, this);

        this.on(cc.Node.EventType.COLOR_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { color: this.color.toHEX() });
        }, this);

        this.on(cc.Node.EventType.SIBLING_ORDER_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { siblingIndex: this.getSiblingIndex() });
        }, this);

        this.on(cc.Node.EventType.OPACITY_CHANGED, () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { opacity: this.opacity });
        }, this);

        this.on("spriteframe-changed", () => {
            let spr = this.getComponent(cc.Sprite);
            if (spr) {
                let nativeUrl = spr.spriteFrame.getTexture()?.nativeUrl;
                // console.log("#----nativeUrl--->", nativeUrl);
                let key = "";
                if(nativeUrl)
                    key = this.getNativeResKey(nativeUrl);
                if (key !== "") {
                    nativeUrl = key;
                }
                qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { spriteframe: nativeUrl });
            }
        }, this);

        this.on("active-in-hierarchy-changed", () => {
            qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { active: this.active });
        }, this);

        let spineNode = this.getChildByName("spine-node");
        if (spineNode) {
            let spine = spineNode.getComponent(sp.Skeleton);
            spineNode.on("spine-animation-change", () => {
                qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { spineAnimation: spine.animation });
            });
            spineNode.on("spine-timeScale-change", () => {
                qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { spineTimeScale: spine.timeScale });
            });
            spineNode.on("spine-loop-change", () => {
                qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { spineLoop: spine.loop });
            })
            spineNode.on("spine-ani-list", () => {
                let spineAniListData = {
                    list: spine['ani-list'],
                    listLoop: spine['ani-list-loop'],
                    listIndex: spine['ani-list-index']
                }
                qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { spineList: spineAniListData });
            })
        }
    }

    public getNativeResKey(nativeUrl: string) {
        let isFind = false;
        // TODO: 修改为在题版对应的 resourceList 中查找
        let templateManager = QTEUtils.instance(QTETemplateManager, this.qteUUID);
        let obj = templateManager.resourceMaps || {};
        let objList = QTEUtils.getResourceList(this.qteUUID);

        // 检测 key 是否在 resourceList 中
        for (let key1 in objList) {
            if (obj[objList[key1]] == nativeUrl || (obj[objList[key1]] && obj[objList[key1]].indexOf(nativeUrl) > -1)) {
                isFind = true;
                qte.log("找到资源", nativeUrl, "key=", key1, "value=")
                return objList[key1];
            }
        }
        if (!isFind) {
            for (let key in obj) {
                if (key == nativeUrl || (obj[key] && obj[key].indexOf(nativeUrl) > -1)) {
                    qte.log("找不到资源", nativeUrl);
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `有问题资源 nativeUr=${nativeUrl} cdn=${key}`);
                    return key;
                }
            }
        }
        return "";
    }

    /**
     * 返回 node 的指定动画数据，如果没有该动画，返回 null
     */
    public hasAnimation(name: string) {
        let animations = this.extra["animationsForClient"];
        if (animations && animations.hasOwnProperty(name)) {
            return animations[name];
        }
        return null;
    }

    /**
     * 获取动画配置
     * @param name 
     * @returns 
     */
    public getAnimationConfig(name: string, canSkip?: boolean) {
        let animations = this.extra["animationsConfigForClient"];
        if (animations && animations.hasOwnProperty(name)) {
            return animations[name];
        }
        let defaultSkip = false;
        if (canSkip) {
            defaultSkip = true;
        }
        let oldData = {
            animationAllowBreak: defaultSkip
        }
        return oldData;
    }

    // 播放动画
    public playAnimation(name: string, callback: () => void, canSkip?: boolean): void {
        let data = this.hasAnimation(name);
        if (data) {
            let animCmpt = this.getComponent(AnimationCmpt);
            if (animCmpt) {
                animCmpt.playAnimation(this.qid + "_" + name, callback, data, this.getAnimationConfig(name, canSkip));
            } else {
                callback && callback();
            }
        } else {
            callback && callback();
        }
    }

    public skipAnimation(name: string): void {
        let animCmpt = this.getComponent(AnimationCmpt);
        if (animCmpt) {
            animCmpt.skipAnimation(this.qid + "_" + name);
        }
    }

    public skipAllAnimation(): void {
        let animCmpt = this.getComponent(AnimationCmpt);
        if (animCmpt) {
            animCmpt.skipAllAnimation()
        }
    }

    /**
     * 跳过动画,并且不调用动画的回调
     * @param name 
     */
    public skipAnimationWithoutCB(name: string): void {
        let animCmpt = this.getComponent(AnimationCmpt);
        if (animCmpt) {
            animCmpt.skipAnimationWithoutCB(this.qid + "_" + name);
        }
    }

    public render(key: string, properties: any) {
        // console.log("=====key--->", key,"----properties-->", properties)
        switch (key) {
            case "x":
                this.x = ValueUtils.check(properties, 0);
                break;
            case "y":
                this.y = ValueUtils.check(properties, 0);
                break;
            case "angle":
                this.angle = ValueUtils.check(properties, 0);
                break;
            case "scale":
                this.scale = ValueUtils.check(properties, 1);
                break;
            case "width":
                this.width = ValueUtils.check(properties, 0);
                break;
            case "height":
                this.height = ValueUtils.check(properties, 0);
                break;
            case "active":
                this.active = ValueUtils.check(properties, true);
                break;
            case "siblingIndex":
                this.setSiblingIndex(ValueUtils.check(properties, 0));
                break;
            case "color":
                let color = cc.Color.BLACK;
                cc.Color.fromHEX(color, ValueUtils.check(properties, "0x000000"));
                this.color = color;
                break;
            case "spriteframe":
                let url = properties + "";
                let spr = this.getComponent(cc.Sprite);
                // native  --->   url
                // native  --->   other native
                let assetManager = QTEUtils.instance(QTEAssetsManager, this.qteUUID)
                // let assetUrl = assetManager.transRequestUrl(url);
                console.log("====spriteframe==>", url);
                let ass: any = assetManager.getAsset(url);
                if (!ass) {
                    // TODO： 如何资源不存在，判断 url 是否为 cdn 地址，如果是走兜底逻辑
                    qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "spriteframe asset not found: " + url);
                    ass = this.loadResByUrl(url, (res) => {
                        spr.spriteFrame = new cc.SpriteFrame(res);
                        this.width = this.properties.width;
                        this.height = this.properties.height;
                    });
                } else {
                    spr.spriteFrame = new cc.SpriteFrame(ass);
                    this.width = this.properties.width;
                    this.height = this.properties.height;
                }

                break;
            case "spineAnimation":
                let spineAniNode = this.getChildByName("spine-node");
                if (spineAniNode) {
                    let spine = spineAniNode.getComponent(sp.Skeleton);
                    if (spine) {
                        if (spine['ani-list']) {
                            if (spine['ani-list'].length == 0) {
                                spine.setCompleteListener(null);
                            }
                        } else {
                            spine.setCompleteListener(null);
                        }
                        spine.animation = properties;
                    }
                }
                break;
            case "spineTimeScale":
                let spineTimeNode = this.getChildByName("spine-node");
                if (spineTimeNode) {
                    let spine = spineTimeNode.getComponent(sp.Skeleton);
                    if (spine) {
                        spine.timeScale = Number(properties);
                    }
                }
                break;
            case "spineLoop":
                let spineLoopNode = this.getChildByName("spine-node");
                if (spineLoopNode) {
                    let spine = spineLoopNode.getComponent(sp.Skeleton);
                    if (spine) {
                        spine.loop = properties;
                        spine.animation = spine.animation;
                    }
                }
            case "spineList":
                let spineListNode = this.getChildByName("spine-node");
                if (spineListNode) {
                    let spine = spineListNode.getComponent(sp.Skeleton);
                    if (spine) {
                        if (properties['list'] && properties['list'].length > 0) {
                            QTEUtils.playSpineArr(spine, properties['list'], properties['listLoop'], properties['listIndex'])
                        }
                    }
                }

                break;
        }
    }
    async loadResByUrl(url: string, callBack: Function) {
        if (url.indexOf('http') === 0 || url.indexOf('https') === 0) {
            let assetManager = QTEUtils.instance(QTEAssetsManager, this.qteUUID);
            let ass = await assetManager.loadResByUrl(url);
            callBack(ass);
            return ass;

        }
    }

    /** 根据自身属性,刷新节点至初始状态 */
    resetData() {
        if (this.properties) {
            this.active = this.properties.active;
            this.width = this.properties.width;
            this.height = this.properties.height;
            this.opacity = QTEUtils.checkValue(this.properties.opacity, 255);
            this.angle = this.properties.angle || 0;
            this.x = this.properties.x;
            this.y = this.properties.y;
            this.active = this.properties.active;
            this.scaleX = this.properties.scaleX || 1;
            this.scaleY = this.properties.scaleY || 1;
            let spineNode = this.getChildByName("spine-node");
            if (spineNode) {
                if (this.scale != 0) {
                    this.scale = 1;
                }
                let spine = spineNode.getComponent(sp.Skeleton);
                spineNode.x = this.properties.offsetX;
                spineNode.y = this.properties.offsetY;
                spineNode.scaleX = this.properties.scaleX || 1;
                spineNode.scaleY = this.properties.scaleY || 1;
                spine.timeScale = this.properties.timeScale;
                let array: Array<string> = this.properties.animationList || this.properties.animList;
                QTEUtils.playSpineArr(spine, array, this.properties.loop);
            }
        }
        if (this.componentData) {
            if (this.componentData.hideBeforeAnimation) {
                this.scale = 0;
            }
        }
    }

    destroy() {
        qte.instance(QTEStateManager).updateState(this.qteUUID, this.qid, { destroy: true });
        qte.instance(QTEEntityManager, this.qteUUID).deleteEntityNode(this.qid, this.qtag);
        return super.destroy();
    }

}
