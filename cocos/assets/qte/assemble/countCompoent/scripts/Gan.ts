
/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @desc [计数器杆子]
 */
const { ccclass, property } = cc._decorator;
let Unit = cc.Enum({
    0: "个",
    1: "十",
    2: "百",
    3: "千",
    4: "万",
    5: "十万",
    6: "百万",
    7: "千万",
    8: "亿",
    9: "十亿",
});
export interface updateGanData {
    unit: number;
    currNum: number,
    // addbuttonState: number;
    // actionState: number
}

export interface CountEvent {
    addEvent: Function;
    subEvent: Function;
}

@ccclass
export default class Gan extends cc.Component {

    @property(cc.Label)
    unitLabel: cc.Label = null;

    @property(cc.SpriteFrame)
    addSp0: cc.SpriteFrame = null;
    @property(cc.SpriteFrame)
    addSp1: cc.SpriteFrame = null;
    @property(cc.Sprite)
    btAddSp: cc.Sprite = null;
    @property(cc.Prefab)
    zhuPre: cc.Prefab = null;
    @property(cc.Sprite)
    unitTagetSp: cc.Sprite = null;
    @property(cc.SpriteFrame)
    unitSp: cc.SpriteFrame[] = [];
    private _MaxZhuNum: number = 9;
    private _currData: updateGanData = null;
    private _CountEvent: CountEvent = null;
    private _speed: number = 500;
    private _suanZhuArray: cc.Node[] = [];

    onLoad() {
        let touchNode = cc.find("touchNode", this.node);
        touchNode.on(cc.Node.EventType.TOUCH_END, this.onTouchEndAddZhu, this);
    }


    public initComponentEvent(data: CountEvent): void {
        this._CountEvent = data;
    }

    public recoverData(num: number) {
        if (!cc.isValid(this.node, true)) {
            return;
        }

        for (let i = 0; i < num; i++) {
            let node = cc.instantiate(this.zhuPre);
            node.name = "suanzhu_" + i;
            node.parent = this.node;
            node.setPosition(this.btAddSp.node.getPosition());
            this._suanZhuArray.push(node);
            node.on(cc.Node.EventType.TOUCH_END, this.onTouchEndSubZhu, this);
            let pos = cc.v2(-17.2, -187 + i * 37);
            node.setPosition(pos);
        }
    }

    // touch 加算珠
    public onTouchEndAddZhu(e: cc.Touch): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        if (this._currData['currNum'] >= this._MaxZhuNum) {
            return;
        }
        console.log(this._currData);
        console.log("onTouchEndAddZhu")
        if (this._CountEvent && this._CountEvent.addEvent) {
            this._CountEvent.addEvent(this._currData, this.addZhuAction.bind(this));
        }
    }


    // touch 减算珠
    public onTouchEndSubZhu(e: cc.Touch): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        let targetNode = e['target'];
        let index = -1;
        for (let i = 0; i < this._suanZhuArray.length; i++) {
            if (this._suanZhuArray[i] === targetNode) {
                index = i;
                break;
            }
        }
        console.log("index:", index)
        if (this._currData['currNum'] <= 0 || index === -1) {
            return;
        }
        console.log("onTouchEndSubZhu")
        if (this._CountEvent && this._CountEvent.subEvent) {
            this._CountEvent.subEvent(this._currData, index, this.subZhuAction.bind(this));
        }
    }

    // 减算珠action
    public subZhuAction(index: number, finishCallBack: Function): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        if (!cc.isValid(this.btAddSp.node, true)) {
            return;
        }
        let tempArray: cc.Node[] = [];
        for (let i = 0; i < this._suanZhuArray.length; i++) {
            if (i >= index) {
                tempArray.push(this._suanZhuArray[i]);
            }

        }
        tempArray.forEach(ele => {
            ele.off(cc.Node.EventType.TOUCH_END, this.onTouchEndSubZhu, this);
            this.subNextAction(ele, index, finishCallBack)
        })

    }
    private subNextAction(node: cc.Node, index: number, finishCallBack: Function): void {
        let pos = this.btAddSp.node.getPosition();
        let distance = pos.sub(node.getPosition()).mag();
        let time = Number(parseFloat(distance / this._speed + "").toFixed(2));
        cc.tween(node).to(time, { position: cc.v3(pos) }).call(
            () => {
                this.deletShuZhu(node);
                node.destroy();
                if (this._suanZhuArray.length <= index) {
                    finishCallBack && finishCallBack();
                }
            }
        ).start();
    }
    deletShuZhu(node: cc.Node) {
        let index = -1;
        for (let i = 0; i < this._suanZhuArray.length; i++) {
            if (this._suanZhuArray[i] === node) {
                index = i;
                break;
            }
        }
        if (index != -1) {
            this._suanZhuArray.splice(index, 1);
        }

    }

    // 加算珠action
    public addZhuAction(index: number, finishCallBack: Function): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        if (!cc.isValid(this.btAddSp.node, true)) {
            return;
        }

        let node = cc.instantiate(this.zhuPre);
        node.name = "suanzhu_" + index;
        node.parent = this.node;
        node.setPosition(this.btAddSp.node.getPosition());
        this._suanZhuArray.push(node);
        node.on(cc.Node.EventType.TOUCH_END, this.onTouchEndSubZhu, this);
        let pos = cc.v2(-17.2, -187 + index * 37);
        let distance = pos.sub(node.getPosition()).mag();
        let time = Number(parseFloat(distance / this._speed + "").toFixed(2));
        cc.tween(node).to(time, { position: cc.v3(pos) }).call(
            () => {
                finishCallBack && finishCallBack();
            }
        ).start();

    }
    // 刷新ui
    public updateUi(data: updateGanData): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        this._currData = data;
        // let unitValue = Unit[this._currData.unit]
        // this.unitLabel.string = unitValue;
        let unitValue = this.unitSp[this._currData.unit];
        if (unitValue) {
            this.unitTagetSp.spriteFrame = unitValue;
        }
        if (this._currData['currNum'] >= this._MaxZhuNum) {
            this.btAddSp.spriteFrame = this.addSp0;
        } else {
            this.btAddSp.spriteFrame = this.addSp1;
        }


    }


    // update (dt) {}
}
