/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @desc [计数器组件]
 */



import BaseComponent from "../../core/BaseComponent";
import Gan, { CountEvent, updateGanData } from "./Gan";
const { ccclass, property } = cc._decorator;
@ccclass
export default class CounterComponent extends BaseComponent {

    private _floorNode: cc.Node;
    private _data: any;
    private _w: number;
    private _playCurrNum: number = 0;
    // 杆子
    @property(cc.Prefab)
    ganPre: cc.Prefab = null;


    private _ganNodeArray: cc.Node[] = [];
    private _targetData: string = "";
    private _dataMap: updateGanData[] = [];
    private _isCanPlay: boolean = true;


    private _setStateFunc: Function;
    private _setSubmitFunc: Function;
    public changeSkine(path: string, param?: any) {
    }
    public changeProperties(key: string, data: any) {
        if (key == "countNum") {
            this._data.properties[key] = data;
            this.reset();
        }
    }

    public setWDIsOperator(val: boolean): void {
        this._setSubmitFunc && this._setSubmitFunc();

    }
    public setWDCurrPlayNum(val: number): void {
        if (this._playCurrNum == val) {
            return;
        }
        console.log("setWDCurrPlayNum", val);
        for (let i = 0; i < this._ganNodeArray.length; i++) {
            this._ganNodeArray[i].destroy();
        }
        this._isCanPlay = false;
        this._ganNodeArray = [];
        this.initComponent(this._data);
        this._playCurrNum = val;
        this.setRecoverDataMap();
        this.updateUi();
        this._isCanPlay = true;
        this._setSubmitFunc && this._setSubmitFunc();

    }
    private setRecoverDataMap(): void {
        console.log(this._dataMap);
        let tempStr = this._playCurrNum + "";
        let unitStrNum = tempStr.split('').reverse().join(''); // 反转字符串
        for (let i = 0; i < this._dataMap.length; i++) {
            if (typeof unitStrNum[i] != "undefined") {
                this._dataMap[i].currNum = Number(unitStrNum[i]);
                if (cc.isValid(this._dataMap[i], true)) {
                    this._ganNodeArray[i] && this._ganNodeArray[i].getComponent(Gan) && this._ganNodeArray[i].getComponent(Gan).recoverData(this._dataMap[i].currNum);
                }
            }

        }
    }

    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
    }
    public setSubFunc(fun: Function) {
        this._setSubmitFunc = fun;
    }

    public reset(): void {
        for (let i = 0; i < this._ganNodeArray.length; i++) {
            this._ganNodeArray[i].destroy();
        }
        if (!this._isCanPlay) {
            this._isCanPlay = true;
        }
        this._ganNodeArray = [];
        this.initComponent(this._data);
    }
    public get PlayNum(): number {
        let num1 = 0;
        if (this._targetData != "") {
            num1 = Number(this._targetData);

        }
        return this._playCurrNum;
    }

    public get targetData(): string {
        return this._targetData;
    }


    public initComponent(data?: any) {
        console.log('data = ', data);
        if (!data) {
            return;
        }
        this._data = data;
        this._targetData = this._data['properties']['countNum'] + "";
        if (this._targetData.length > 10) {
            console.log("发送数据越界了:", this._targetData);
            return;
        }

        this._floorNode = cc.find("floor", this.node);
        this._dataMap = [];
        console.log(this._targetData.length);
        // this._w = 140 + 70 * (this._targetData.length - 1);
        this._w = 245 + 93 * (this._targetData.length - 1);
        this._floorNode.width = this._w;
        this.node.width = this._floorNode.width;
        // let rect = this.node.getBoundingBoxToWorld();
        // console.log("全局rect",rect);
        this.onSizeChange(this.node.getContentSize());
        this._ganNodeArray = [];
        for (let i = 0; i < this._targetData.length; i++) {
            let ganNode = cc.instantiate(this.ganPre);
            ganNode.parent = this._floorNode;
            // ganNode.x = this._w / 2 - (60 + 70 * i);
            ganNode.x = this._w / 2 - (105.5 + 93 * i);
            // ganNode.y = 62;
            ganNode.y = 38;
            ganNode.name = "gan_" + i;
            this._ganNodeArray.push(ganNode);
            let ganCom = ganNode.getComponent(Gan);
            let ent: CountEvent = {
                addEvent: this.addZhuzi.bind(this),
                subEvent: this.subZhuzi.bind(this)
            }
            ganCom.initComponentEvent(ent);
            let ganData: updateGanData = {
                unit: i,
                currNum: 0
            }
            this._dataMap.push(ganData);
        }
        this.updateUi();
        this.culNowNum();
    }
    private culNowNum(): void {
        let haveNotZero = false;
        let tempStr = "";
        for (let i = this._dataMap.length - 1; i >= 0; i--) {
            if (haveNotZero == false && this._dataMap[i].currNum != 0) {
                haveNotZero = true;
            }
            if (haveNotZero) {
                tempStr += this._dataMap[i].currNum;
            }
        }
        if (tempStr === "") {
            tempStr = "0";
        }
        this._playCurrNum = Number(tempStr);
        console.log("_playCurrNum :" + this._playCurrNum);
    }
    // 添加珠子
    public addZhuzi(data: updateGanData, actionFunction: Function): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        let openData = this._dataMap[data.unit];
        if (!this._isCanPlay || !openData || openData.currNum >= 9) {
            return;
        }
        this._setStateFunc && this._setStateFunc("isOperator", true);
        this._isCanPlay = false;
        actionFunction && actionFunction(openData.currNum, () => {
            this._isCanPlay = true;
        });
        openData.currNum += 1;
        this.updateUi();
        this.culNowNum();
        this._setStateFunc && this._setStateFunc("currPlayNum", this._playCurrNum);
        this._setSubmitFunc && this._setSubmitFunc();
    }


    // 减少珠子
    public subZhuzi(data: updateGanData, subIndex: number, actionFunction: Function): void {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        let openData = this._dataMap[data.unit];
        if (!this._isCanPlay || !openData || openData.currNum <= 0) {
            return;
        }

        this._isCanPlay = false;
        actionFunction && actionFunction(subIndex, () => {
            this._isCanPlay = true;
        });
        openData.currNum = subIndex;
        this.updateUi();
        this.culNowNum();
        this._setStateFunc && this._setStateFunc("currPlayNum", this._playCurrNum);
        this._setSubmitFunc && this._setSubmitFunc();
    }

    private updateUi(): void {
        for (let i = 0; i < this._ganNodeArray.length; i++) {
            let ganCom = this._ganNodeArray[i].getComponent(Gan);
            if (ganCom) {
                ganCom.updateUi(this._dataMap[i]);
            }


        }

    }

}


