import BaseComponent from "../../core/BaseComponent";
const { ccclass, property } = cc._decorator;
@ccclass
export class countdownComponent extends BaseComponent {

    private _title: string = "";
    private _time: number = 0;
    private _style: number = 0     // 0 小学，1 高中
    private _callBack: Function = null;
    @property(cc.SpriteFrame)
    skinSpList0: cc.SpriteFrame[] = [];

    numLabel: cc.Label = null;
    numSprite: cc.Sprite = null;
    titleLabel: cc.Label = null;


    @property(cc.Prefab)
    skinPre: cc.Prefab[] = [];

    public changeSkine(path: string, param?: any) {
        throw new Error("Method not implemented.");
    }

    public async initComponent(data?: any) {

        const property = data.properties;

        this._title = property.title;
        this._style = property.style;
        this._time = property.time;
        this._callBack = property.callBack;
        let node = cc.instantiate(this.skinPre[this._style])
        this.node.addChild(node);
        if (this._style == 1) {
            this.numLabel = node.getChildByName("time").getComponent(cc.Label);
        } else if (this._style == 0) {
            this.numSprite = node.getChildByName("time").getComponent(cc.Sprite);
        }
        this.titleLabel = node.getChildByName("title").getComponent(cc.Label);
        this.updateUI(true);
        this.schedule(() => { this.updateUI(false) }, 1);
    };
    updateUI(isNotAdd?: boolean) {
        if (!isNotAdd) {
            this._time--;
        }
        if (this.numLabel) {
            this.numLabel.string = this._time + "";
        }
        if (this.numSprite && this.skinSpList0[this._time-1]) {
            this.numSprite.spriteFrame = this.skinSpList0[this._time-1];
        }

        this.titleLabel.string = this._title + "";
        if (this._time <= 0) {
            this._callBack && this._callBack();
            this._callBack = null;
            this.node.destroy();
        }
    }
    /**
     * 属性更新
     * @param key 属性key
     * @param data 参数 
     */
    public async changeProperties(key: string, data: any) {

    };

}