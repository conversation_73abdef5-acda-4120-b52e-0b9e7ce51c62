/**
 * 创建钟表组件
 *
 */

/**
 * 回调keys
 */
export enum CallBackKeys {
    C_HAVE_DONE = "c_have_done",            // 操作回调key
    C_JUDGE = "c_judge_question",  // 判断对错
    C_TIME = "c_time"              // 当前时间
}

/**
 * 当前选中哪个指针
 */
enum SelectPointerState {
    SPS_Hour = "SPS_Hour", // 选中时针
    SPS_Minute = "SPS_Minute", // 选中分针
    SPS_None = "SPS_None", // 未选中
}

import BaseComponent from "../../core/BaseComponent";
const { ccclass, property } = cc._decorator;
@ccclass
export class ClockComponent extends BaseComponent {
    @property({
        type: cc.Node,
        displayName: "时针指针",
    })
    nodHourPointer: cc.Node = null;
    @property({
        type: cc.Node,
        displayName: "分针指针",
    })
    nodMinutePointer = null;

    @property({
        type: cc.Node,
        displayName: "钟表盘",
    })
    nodClockBg = null;
    // 是否进行时针分针联动
    private _isBindHourAndMu = true;
    // 第一次加载题版的时间，用来恢复到最初表盘状态,默认3点
    private _firstHour = 3;
    private _firstMinute = 0;

    // 初始时间，老师发题时的时间
    private _oHour = 0;
    private _oMinute = 0;

    // 正确答案
    private _correctHour = 0;
    private _correctMinute = 0;

    // 当前小时
    private _hour = 0;
    // 当前分钟
    private _minute = 0;
    // 当前指针状态
    private _selectPointerState = SelectPointerState.SPS_None;
    // 记录在旋转之前时针角度
    private _oHourAngle = 0;
    // 总共转了多少角度了，自从本次开始旋转以来
    private _totalAngle = 0;

    // 回调key-function
    private _callbackObj = {};

    private _offsetV2: cc.Vec2 = cc.v2(0, 0);

    onLoad() {
        console.log("clockComponent load ");
        // this._firstHour = this.getHourNow();
        // this._firstMinute = this.getMinutesNow();
        // // 初始化触摸事件
        // this.initTouchEvents();
        // // 默认隐藏提示时间
        // this.setTipsVisible(false);
    }

    /**
     * 还原到最初的加载时间状态
     */
    public resetToOriginTime() {
        this.setTime(this._firstHour, this._firstMinute);
        this.refreshTimeNow();
    }

    /**
     * 重置时间,重置到老师发题的时间
     */
    public resetTime() {
        this.setTime(this._oHour, this._oMinute);
    }

    /**
     * 初始化触摸事件
     */
    private initTouchEvents() {
        this.nodClockBg.on(
            cc.Node.EventType.TOUCH_START,
            this.touchStartOnClockBg,
            this
        );

        this.nodClockBg.on(
            cc.Node.EventType.TOUCH_MOVE,
            this.touchMoveOnClockBg,
            this
        );

        this.nodClockBg.on(
            cc.Node.EventType.TOUCH_END,
            this.touchEndOnClockBg,
            this
        );

        this.nodClockBg.on(
            cc.Node.EventType.TOUCH_CANCEL,
            this.touchEndOnClockBg,
            this
        );
        this.checkParentOffestV2(this.nodClockBg);
    }

    update(dt) {
        this.updateTime();
    }

    /**
     * 是否显示提示时间的文本
     * @param isVisible
     */
    public setTipsVisible(isVisible: boolean) {
        this.node.getChildByName("labelTIme").active = isVisible;
    }

    /**
     * 设置时间
     */
    public setTime(hour: number, minute: number) {
        this.setHour(hour);
        this.setMinutes(minute);
    }

    /**
     * 设置时针分针角度
     * @param hourAngle 
     * @param minuteAngle 
     */
    public setTimeAngle(hourAngle: number, minuteAngle: number) {
        if (isNaN(hourAngle) || isNaN(minuteAngle)) {
            console.error("setTimeAngle error ", hourAngle, minuteAngle);
            return;
        }
        this.nodMinutePointer.angle = minuteAngle;
        this.nodHourPointer.angle = hourAngle;
        console.log("setTimeAngle ", hourAngle, minuteAngle);
        this._hour = this.getHourNow();
        this._minute = this.getMinutesNow();
    }

    /**
     * 设置小时 1--12
     * @param hour 小时
     */
    public setHour(hour: number) {
        this._hour = hour;
        var angle = hour * 30;
        var f = this.getPlusAngle(90 - angle);
        this.nodHourPointer.angle = f;
        // this.refreshTimeNow();
    }

    /**
     * 设置分钟
     * @param minute 小时12小时制
     */
    public setMinutes(minute: number) {
        this._minute = minute;
        var angle = minute * 6;
        var mPlusAngle = this.getPlusAngle(90 - angle);
        this.nodMinutePointer.angle = mPlusAngle;

        var angleH = this._hour * 30;
        var f = this.getPlusAngle(90 - angleH);
        this.turnHourPointer(f, -angle);
        // this.refreshTimeNow();
    }

    /**
     * 是否绑定时针分针
     * @param isBind 
     */
    public setBindHourAndMu(isBind: boolean) {
        this._isBindHourAndMu = isBind;
    }
    private checkParentOffestV2(node: cc.Node) {
        if (node.parent) {
            this._offsetV2 = this._offsetV2.add(node.parent.getPosition());
            if (node.parent.name != "root") {
                this.checkParentOffestV2(node.parent);
            }
        }
    }

    /**
     * 根据触摸点判断当前选中哪个指针
     * @param pos
     */
    private checkeckSelPointers(pos: cc.Vec2) {
        // var p1 = this.nodHourPointer.convertToNodeSpace(pos);
        let p = cc.v2(pos.x - cc.winSize.width / 2 - this._offsetV2.x, pos.y - cc.winSize.height / 2 - this._offsetV2.y);
        // var p1 = this.nodHourPointer.convertToNodeSpaceAR(pos);
        // var p2 = this.nodMinutePointer.convertToNodeSpaceAR(pos);
        var size1 = this.nodHourPointer.getBoundingBox();
        var size2 = this.nodMinutePointer.getBoundingBox();
        // var rect1 = cc.rect(0, 0, size1.width, size1.height);
        // var rect2 = cc.rect(0, 0, size2.width, size2.height);
        this._oHourAngle = this.nodHourPointer.angle;

        if (size1.contains(p)) {
            this._selectPointerState = SelectPointerState.SPS_Hour;
            console.log("checkeckSelPointers 选中时针 ");
        }
        else if (size2.contains(p)) {
            this._selectPointerState = SelectPointerState.SPS_Minute;
            console.log("checkeckSelPointers 选中分针 ", this._oHourAngle);
        } else {
            this._selectPointerState = SelectPointerState.SPS_None;
            console.log("checkeckSelPointers 没选中 ");
        }
    }

    /**
     * 触摸按下
     * @param event
     */
    private touchStartOnClockBg(event: cc.Event.EventTouch) {
        var touchLoc = event.getLocation();
        this._totalAngle = 0;
        this.checkeckSelPointers(touchLoc);
    }

    /**
     * 触摸抬起
     * @param touch
     */
    private touchEndOnClockBg(touch: cc.Touch) {
        this._selectPointerState = SelectPointerState.SPS_None;
        this.refreshTimeNow();
        this.checkIsCorrect();
    }

    /**
     * 指针旋转
     * @param touch
     */
    private touchMoveOnClockBg(touch: cc.Touch) {
        if (this._selectPointerState == SelectPointerState.SPS_None) {
            return;
        }
        var p = touch.getLocation();
        // var temp = this.nodClockBg.convertToNodeSpace(p);
        let temp = cc.v2(p.x - cc.winSize.width / 2 - this._offsetV2.x, p.y - cc.winSize.height / 2 - this._offsetV2.y);
        // if (this._selectPointerState == SelectPointerState.SPS_Hour) {
        //     temp = temp.sub(this.nodHourPointer.getPosition());
        // }else{
        //     temp = temp.sub(this.nodMinutePointer.getPosition());
        // }
        // temp.x = temp.x - this.nodClockBg.getContentSize().width / 2;
        // temp.y = temp.y - this.nodClockBg.getContentSize().height / 2;
        var PI = 3.14159;
        // console.log("moveMinutePointer 转换", temp.x, temp.y);
        var angle = 0;
        // 在y轴附近直接设置成 直角
        if (Math.abs(temp.x) < 0.1) {
            if (temp.y > 0) {
                angle = 90;
            } else {
                angle = -90;
            }
        } else {
            // 第一四象限用atan
            if (temp.x > 0) {
                angle = (Math.atan(temp.y / temp.x) * 180) / PI;
            } else if (temp.x < 0 && temp.y >= 0) {
                // 第二象限
                angle = 180 - (Math.atan(Math.abs(temp.y / temp.x)) * 180) / PI;
            } else {
                // 第三象限
                angle = 180 + (Math.atan(temp.y / temp.x) * 180) / PI;
            }
        }
        angle = this.getPlusAngle(angle);

        // console.log("moveMinutePointer angle = ", angle, temp.x, temp.y);
        if (this._selectPointerState == SelectPointerState.SPS_Hour) {
            this.nodHourPointer.angle = angle;
            if (this._isBindHourAndMu) {
                this.turnMinutePointerByHour(angle);
            }
        } else {
            var oAngle = this.getPlusAngle(this.nodMinutePointer.angle);
            var delt = angle - oAngle;
            if (delt > 180) {
                // 大于180，一定是正角度转负角度（负角度会转换成+360）导致
                delt = delt - 360;
            }
            if (delt < -180) {
                delt = delt + 360;
            }
            this._totalAngle += delt;
            this.nodMinutePointer.angle = angle;
            if (this._isBindHourAndMu) {
                this.turnHourPointer(this._oHourAngle, this._totalAngle);
            }
        }
        // 移动过即操作过
        this.setHaveDone(true);
        this.checkIsCorrect();
    }

    /**
     * oHourAngle 初始时针角度
     * totalMinuteAngle 分针转了多少角度，导致时针需要旋转多少度
     */
    private turnHourPointer(oHourAngle: number, totalMinuteAngle: number) {
        var delt = totalMinuteAngle / 12;
        this.nodHourPointer.angle = this.getPlusAngle(oHourAngle + delt);
    }

    /**
     * 旋转时针引导分针转动
     */
    private turnMinutePointerByHour(angle: number) {
        var angleT = this.getPlusAngle(angle);
        angleT = (30 - (angleT % 30)) % 30; // 距离整点走了多少度，顺时针

        // 因为初始是横着的图片，所以需要校正90度
        var mA = angleT * 12; // 分针应该在回退这么多角度，顺时针
        var nMA = 360 - mA; // 逆时针角度
        var f = nMA + 90; // 
        this.nodMinutePointer.angle = this.getPlusAngle(f);
        // console.log("turnMinutePointerByHour ",angleT, angle, f)
    }

    /**
     * 获取正角度数
     * @param angle
     * @returns
     */
    private getPlusAngle(angle: number): number {
        var result = angle;
        while (result < 0) {
            result += 360;
        }
        return result % 360;
    }

    /**
     * 获取当前时间--小时数
     * @returns
     */
    public getHourNow(): number {
        var result = 0;
        var angle = this.nodHourPointer.angle - 90;
        angle = this.getPlusAngle(angle); // 需要校正90度，因为初始是横着的图片
        angle = 360 - angle; // 因为结点旋转角度是逆时针算正
        result = Math.floor(angle / 30);

        // 因为分针计数有个舍弃的操作，所以需要校准
        var m = this.getMinutesNow();
        if (m == 0) {
            // 如果很靠近下一个小时数，则按下一个算。非绑定情况下会出现时分钟不按比例旋转
            if (Math.abs((result + 1) * 30 - angle) < 1) {
                result += 1;
            }
        }

        // 0点一般说12点
        if (result == 0) {
            result = 12;
        }
        return result;
    }

    /**
     * 获取当前时间分钟数
     * @returns
     */
    public getMinutesNow(): number {
        var temp = 0;
        var angle = this.nodMinutePointer.angle;
        angle = this.getPlusAngle(angle - 90); // 需要校正90度，因为初始是横着的图片

        angle = 360 - angle;
        temp = angle / 6;
        var result = Math.floor(temp);
        result = result % 60;
        return result;
    }

    /**
     * 获取当前时间 12:00,格式化
     * @returns
     */
    public getTimeNow(): string {
        var result = "";
        var hour = this.getHourNow();
        var m = this.getMinutesNow();
        if (hour < 10) {
            result = "0" + hour.toString();
        } else {
            result = hour.toString();
        }
        if (m < 10) {
            result += ":0" + m.toString();
        } else {
            result += ":" + m.toString();
        }
        // console.log("hour = " + hour + ";minute " + m);
        return result;
    }

    /**
     * 刷新事件提示文本
     */
    private updateTime() {
        this.node.getChildByName("labelTIme").getComponent(cc.Label).string =
            this.getTimeNow();
    }

    /**
     * 判断是否拨的时间准确
     * 通过角度验证对错，时分针都是正负6度的误差
     */
    private checkIsCorrect(): boolean {
        let h = Math.floor(this.nodHourPointer.angle);
        // 时针正确角度
        var corHourAngle = Math.floor(this._correctHour * 30 + this._correctMinute / 2);
        // 转换成逆时针
        corHourAngle = 360 - corHourAngle;
        // 校正
        corHourAngle = this.getPlusAngle(corHourAngle + 90);
        var isCorrect = Math.abs(corHourAngle - h) < 6
            || Math.abs(h + 360 - corHourAngle) < 6
            || Math.abs(corHourAngle + 360 - h) < 6; // 验证时针,0度角附近需要考虑-1度被换算成了365度
        // console.log("checkIsCorrect  " + isCorrect, this._correctHour + ":" + this._correctMinute
        // + "判断时针 = " + corHourAngle + ";" + h) ;
        // 同等比较,转换成逆时针角度
        var angle = this._correctMinute * 6;
        var mPlusAngle = this.getPlusAngle(90 - angle);

        // 分针6度误差内算正确
        var curAngle = this.nodMinutePointer.angle;
        var isCorrect2 = Math.abs(mPlusAngle - curAngle) < 6
            || Math.abs(curAngle + 360 - mPlusAngle) < 6
            || Math.abs(mPlusAngle + 360 - curAngle) < 6;
        isCorrect = isCorrect && isCorrect2;
        // console.log("checkIsCorrect 最后判断 = " + isCorrect);
        var f = this._callbackObj[CallBackKeys.C_JUDGE];
        f && f(isCorrect);
        return isCorrect;
    }

    /**
     * 是否做过题目
     * @returns 
     */
    private setHaveDone(is: boolean) {
        var f = this._callbackObj[CallBackKeys.C_HAVE_DONE];
        f && f(is);
    }

    /**
     * 通知clockQuestion 时间已经更新
     * @returns 
     */
    private refreshTimeNow() {
        // console.log("refreshTimeNow " + this.nodHourPointer.angle, ";" + this.nodMinutePointer.angle)
        var f = this._callbackObj[CallBackKeys.C_TIME];
        f && f(this.nodHourPointer.angle, this.nodMinutePointer.angle);
    }

    /**
     * 注册一些回调方法
     * @param key 
     * @param func 
     */
    public registerCallback(key: string, func: Function) {
        this._callbackObj[key] = func;
    }

    /**
     * 组件初始化
     * @param data
     */
    public initComponent(data?: any) {
        console.log("initComponent ", data);
        this.onSizeChange(this.node.getContentSize());
        var properties = data.properties;

        this._oHour = properties.hour;
        this._oMinute = properties.minute;
        this._correctHour = properties.correctHour;
        this._correctMinute = properties.correctMinute;
        this.setTime(this._oHour, this._oMinute);
        this.setBindHourAndMu(properties.linkage);
        if (properties.showTips) {
            this.setTipsVisible(!!properties.showTips);
        }
        this._firstHour = properties.hour;
        this._firstMinute =this._oMinute;
        // 初始化触摸事件
        this.initTouchEvents();
        // 默认隐藏提示时间
        this.setTipsVisible(false);
    }

    /**
     * 编辑侧传值
     * @param key
     * @param data
     */
    public changeProperties(key: string, data: any) {
        console.log("changeProperties key = ", key, ";value = ", data);
        if (key == "hour") {
            this.setHour(data);
        }
        if (key == "minute") {
            this.setMinutes(data);
        }
        // 修改联动与否
        if (key == "linkage") {
            this.setBindHourAndMu(data);
        }
        if (key == "correctHour" || key == "correctMinute" || key == "linkage" || key == "minute" || key == "hour") {
            this.onSizeChange(this.node.getContentSize());
        }
    }

    /**
     * 换肤
     * @param path
     * @param param
     */
    public changeSkine(path: string, param?: any) { }
}
