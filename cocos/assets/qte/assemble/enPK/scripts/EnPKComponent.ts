import ComponentUtils from "../../componentUtils/ComponentUtils";
import BaseComponent from "../../core/BaseComponent";
const { ccclass, property } = cc._decorator;

let posConfig = {
    1: [cc.v2(0, 79.934)],
    2: [cc.v2(-33, 79.934), cc.v2(33, 79.934)],
    3: [cc.v2(-63, 79.934), cc.v2(0, 79.934), cc.v2(63, 79.934)],
    4: [cc.v2(-66, 79.934), cc.v2(-22, 79.934), cc.v2(22, 79.934), cc.v2(66, 79.934)],
    5: [cc.v2(-78, 79.934), cc.v2(-39, 79.934), cc.v2(0, 79.934), cc.v2(39, 79.934), cc.v2(78, 79.934)],
}
const enum LOOK_TYPE {
    start,
    inProgress,
    end
}
@ccclass
export default class EnPKComponent extends BaseComponent {

    pkData: any = null;
    @property(cc.Sprite)
    bg: cc.Sprite = null;
    @property(cc.Label)
    quetionName: cc.Label = null;

    @property(cc.Node)
    starLeft: cc.Node = null;

    @property(cc.Node)
    starRight: cc.Node = null;

    @property(cc.Node)
    succLeft: cc.Node = null;

    @property(cc.Node)
    succRight: cc.Node = null;

    @property(cc.Label)
    quetionLabel0: cc.Label = null;

    @property(cc.Label)
    quetionLabel1: cc.Label = null;

    @property(cc.Sprite)
    quetionPic: cc.Sprite = null;

    @property(cc.Node)
    content: cc.Node = null;

    @property(cc.Node)
    jianju: cc.Node = null;

    _QIndex: number = 0;

    @property(cc.Sprite)
    studentHead: cc.Sprite[] = [];

    @property(cc.Label)
    studentName: cc.Label[] = [];

    @property(cc.Label)
    studentSorce: cc.Label[] = [];

    @property(cc.SpriteFrame)
    defaultHead: cc.SpriteFrame[] = [];

    @property(cc.Node)
    studentBan: cc.Node[] = [];


    @property(cc.Sprite)
    pkStudentHead: cc.Sprite[] = [];

    @property(cc.Label)
    pkStudentName: cc.Label[] = [];

    @property(cc.Label)
    pkStudentSorce: cc.Label[] = [];

    @property(cc.Node)
    pkStudentBan: cc.Node[] = [];



    @property(cc.Node)
    fightNode: cc.Node = null;

    @property(cc.Node)
    anwerNode: cc.Node = null;


    @property(cc.Node)
    cutDownNode: cc.Node = null;
    @property(cc.Node)
    cutDownEndNode: cc.Node = null;

    @property(cc.Node)
    randomNameNode: cc.Node[] = [];

    @property(cc.Node)
    noOneNode: cc.Node = null;

    _countDownUrlSpr: any = {
        0: null,
        1: null,
        2: null
    };
    _countDownSound: cc.AudioClip = null;
    _countDownEndSound: cc.AudioClip = null;
    stageInfo: any = {
        "groupUserList": [],
        "electedUidList": [],
        "uidList": []
    };
    @property(cc.Sprite)
    pk: cc.Sprite = null;

    @property(cc.Animation)
    pkAnima: cc.Animation = null;

    @property(cc.Sprite)
    pkL: cc.Sprite = null;
    @property(cc.Sprite)
    pkR: cc.Sprite = null;

    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;

    @property(cc.Prefab)
    pf: cc.Prefab = null;


    @property(cc.ProgressBar)
    countProgressList: cc.ProgressBar[] = [];

    @property(sp.Skeleton)
    soundWaveSpines: sp.Skeleton[] = [];

    @property(sp.Skeleton)
    pingFenSpines: sp.Skeleton[] = [];

    @property(cc.Label)
    pingFenLabel: cc.Label[] = [];

    @property(cc.Label)
    timeOutFenLabel: cc.Label[] = [];

    _starUrl0: cc.SpriteFrame = null;
    _starUrl1: cc.SpriteFrame = null;
    _pkSound: cc.AudioClip = null;
    _addStarSound: cc.AudioClip = null;
    _succSound: cc.AudioClip = null;
    _equalSound: cc.AudioClip = null;
    _randomSound: cc.AudioClip = null;
    leftNodeList: cc.Sprite[] = [];
    rightNodeList: cc.Sprite[] = [];
    _mode: number = 2;
    _mode1CutDownIndex: number = 0;
    _countDownEndUrl: cc.SpriteFrame = null;
    _randomNameNodeL: cc.Node[] = [];
    _randomNameNodeR: cc.Node[] = [];
    _moveY0: number = 0;
    _moveY1: number = 0;
    _isTeacher: boolean = true;
    _gotoIndex: number = -1;
    uidList: any = []
    _setStateFunc: Function = null;
    counterIndex: number = 0;
    countDownNum: number = 0;
    playIngAuidoList: any = {};

    succSpine: sp.Skeleton = null;
    equalSpine0: sp.Skeleton = null;
    equalSpine1: sp.Skeleton = null;
    _isMock: boolean = false;
    public changeProperties(key: string, data: any) {
        if (key == "pkData") {
            this.pkData = data;
            this.initComponent(null);
        } else if (key == "questionIndex") {
            this.pkData.questionIndex = data;
            this.initComponent(null);
        }

    }

    public changeSkine(path: string, param?: any) {
    }


    public initComponent(data?: any) {
        console.log(data);
        if (data) {
            this.pkData = data.properties.pkData;
            this.cutDownEndNode.on(cc.Node.EventType.TOUCH_START, this.endPlayMode, this);
        }
        this._QIndex = this.pkData.questionIndex;
        this.initUi();

    }

    initFirst() {
        this._QIndex = 0;
        this.updateSmartUi();
    }

    openNoOneNode() {
        this.noOneNode.active = true;
    }
    closeNoOneNode() {
        this.noOneNode.active =false;
    }

    public setInitData(data: any) {
        this._mode = data.mode;
        this._isTeacher = data.isTeacher;
        this._isMock = data.isMock;
        this._QIndex = 0;
        this.updateSmartUi();

    }
    setGroupUserList(data: any) {
        this.stageInfo.groupUserList = data;
    }
    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
    }

    changAnwerMode(index: number) {
        if (this._isMock) {
            qte && qte.logCatBoth("xiaoyin", "开始发小题了");
            this._setStateFunc("startAnwerQuestion", null);
        }
    }

    addSocre(data) {
        this.uidList = data.uidList;
        this.updateStarNodeUI();
        this.playAudioEffer(this._addStarSound);
        this.showScore();
    }
    addSocreByFinalLabel(uidList) {
        let electedUidList = this.stageInfo.electedUidList;
        for (let i = 0; i < electedUidList.length; i++) {
            if (uidList[i] && uidList[i].hasScore) {
                let uInfo = this.getInfoFromGroup(uidList[i].uid);
                let score = electedUidList[i].score || 0;
                if (!score && uInfo) {
                    score = uInfo.score;
                }
                score += uidList[i].score;
                electedUidList[i].score = score;
            }
        }
        this.updateStudentUi();
    }


    goToQuestion(index) {
        if (this._mode == 1) {
            this.cutDownNode.active = false;
            this.cutDownEndNode.active = false;
            this.fightNode.active = false;
            this.unschedule(this.updateMode1Ui);
        }
        if (this._gotoIndex != index && this._gotoIndex < this.pkData.question.length) {
            this.unschedule(this.updateAnwer);
            this.stopAudioEffer();
            this._gotoIndex = index;
            this._QIndex = this._gotoIndex;
            this.updateSmartUi();
            this.counterIndex = 0;
            let smartData = this.pkData.question[this._QIndex];
            this.countDownNum = smartData.time;
            this.countProgressList[0].progress = 1;
            this.countProgressList[1].progress = 1;
            this.countProgressList[0].node.active = true;
            this.countProgressList[1].node.active = true;
            this.countProgressList[0].node.parent.active = true;
            this.countProgressList[1].node.parent.active = true;
            this.pingFenLabel[0].node.parent.active = false;
            this.pingFenLabel[1].node.parent.active = false;
            for (let i = 0; i < this.pingFenSpines.length; i++) {
                let loadingSp = this.pingFenSpines[i];
                loadingSp.node.parent.active = false;
            }
            this.anwerNode.active = true;
            this.schedule(this.updateAnwer, 0.1);
            this.setSoundWaveSpines(LOOK_TYPE.inProgress);
        }
    }

    private setSoundWaveSpines(status: LOOK_TYPE | string) {
        let name = null;
        if (status === LOOK_TYPE.inProgress) {
            name = "animation";
        } else {
            name = null;
        }
        for (let i = 0; i < this.soundWaveSpines.length; i++) {
            let spine = this.soundWaveSpines[i];
            this.soundWaveSpines[i].animation = name;
            this.soundWaveSpines[i].loop = true;
            this.soundWaveSpines[i].timeScale = 1;
            this.soundWaveSpines[i].node.active = true;
            spine.setCompleteListener((trackEntry, loopCount) => {
                spine.animation = name;
            });
        }
    }



    updateAnwer() {
        if (this.counterIndex >= this.countDownNum * 10) {
            this.countProgressList[0].node.active = false;
            this.countProgressList[1].node.active = false;
            this.setSoundWaveSpines(LOOK_TYPE.end);
            this.unschedule(this.updateAnwer);
            this.pingFenAnima();
        }
        let pb = 1 - this.counterIndex / (this.countDownNum * 10);
        this.countProgressList[0].progress = pb;
        this.countProgressList[1].progress = pb;
        this.counterIndex++;
    }

    pingFenAnima() {
        this.countProgressList[0].node.parent.active =
            false;
        this.countProgressList[1].node.parent.active =
            false;

        for (let i = 0; i < this.pingFenSpines.length; i++) {
            let loadingSp = this.pingFenSpines[i];
            loadingSp.node.parent.active = true;
            loadingSp.animation = "animation";
            loadingSp.loop = true;
            loadingSp.timeScale = 1;
            loadingSp.setCompleteListener(() => {
                loadingSp.animation = "animation";
            });

        }

        this._setStateFunc("endAnwerQuestion", null);
    }
    hidePinfeng() {
        for (let i = 0; i < this.pingFenSpines.length; i++) {
            let loadingSp = this.pingFenSpines[i];
            loadingSp.node.parent.active = false;
        }
        this.pingFenLabel[0].node.active = false;
        this.pingFenLabel[1].node.active = false;
        this.timeOutFenLabel[0].node.active = false;
        this.timeOutFenLabel[1].node.active = false;
        this.pingFenLabel[0].node.parent.active = false;
        this.pingFenLabel[1].node.parent.active = false;
    }

    showTimeOutScore() {
        this.updateStarNodeUI();
        this.playAudioEffer(this._addStarSound);
        this.showScoreBase(false);
        this.pingFenLabel[0].node.active = false;
        this.pingFenLabel[1].node.active = false;
        this.timeOutFenLabel[0].node.active = true;
        this.timeOutFenLabel[1].node.active = true;
    }

    showScoreBase(isShow: boolean) {
        for (let i = 0; i < this.pingFenSpines.length; i++) {
            let loadingSp = this.pingFenSpines[i];
            loadingSp.node.parent.active = false;
        }
        this.pingFenLabel[0].string = this.getTotalPoint(0) + "";
        this.pingFenLabel[1].string = this.getTotalPoint(1) + "";
        this.pingFenLabel[0].node.parent.active = true;
        this.pingFenLabel[1].node.parent.active = true;

        if (this._QIndex >= this.pkData.question.length - 1 && this.uidList) {
            this.scheduleOnce(() => {
                this.pingFenLabel[0].node.parent.active = false;
                this.pingFenLabel[1].node.parent.active = false;

                if (this.uidList[0] && this.uidList[1] && this.uidList[0].finalWin == true && this.uidList[1].finalWin == true) {
                    if (this.equalSpine0) {
                        this.equalSpine0.node.parent = this.succLeft;
                        this.equalSpine0.node.active = true;
                        this.equalSpine0.loop = false;
                        this.equalSpine0.animation = this.pkData.equalSpine.animation;
                        this.equalSpine0.setAnimation(0, this.pkData.equalSpine.animation, false);
                    }
                    if (this.equalSpine1) {
                        this.equalSpine1.node.parent = this.succRight;
                        this.equalSpine1.node.active = true;
                        this.equalSpine1.loop = false;
                        this.equalSpine1.animation = this.pkData.equalSpine.animation;
                        this.equalSpine1.setAnimation(0, this.pkData.equalSpine.animation, false);

                    }
                    this.playAudioEffer(this._equalSound);

                } else if (this.uidList[0]) {
                    this.playAudioEffer(this._succSound);
                    let node = this.succLeft;
                    if (this.uidList[1] && this.uidList[1].finalWin == true) {
                        node = this.succRight;
                    }
                    this.succSpine.node.parent = node;
                    this.succSpine.node.active = true;
                    this.succSpine.loop = false;
                    this.succSpine.animation = this.pkData.succSpine.animation;
                    this.succSpine.setAnimation(0, this.pkData.succSpine.animation, false);
                }
            }, 1.5);
            isShow && this.uidList && this.addSocreByFinalLabel(this.uidList)
        }
    }


    showScore() {
        this.showScoreBase(true);
        if (this.uidList[0]) {
            this.pingFenLabel[0].node.active = true;
            this.timeOutFenLabel[0].node.active = false;
        } else {
            this.pingFenLabel[0].node.active = false;
            this.timeOutFenLabel[0].node.active = true;
        }

        if (this.uidList[1]) {
            this.pingFenLabel[1].node.active = true;
            this.timeOutFenLabel[1].node.active = false;
        } else {
            this.pingFenLabel[1].node.active = false;
            this.timeOutFenLabel[1].node.active = true;
        }

    }

    getTotalPoint(index: number) {
        let num = 0;
        if (this.uidList[index] && this.uidList[index].points) {
            num = this.uidList[index].points;
        }
        return num;
    }




    showPK() {
        this.randomNameNode[0].active = false;
        this.randomNameNode[1].active = false;
        this.updateStudentUi();
        this.pkAnima.node.active = true;
        this.pkAnima.play("pk");
        this.playAudioEffer(this._pkSound);
        this.updatePkStudentUi();
        this.scheduleOnce(() => {
            this.pkAnima.node.active = false;
            this.changAnwerMode(0);
        }, 1.5);


    }
    updateRandomName() {
        let moveY0 = 10;
        if (this._moveY0 - moveY0 < 0) {
            if (this._moveY0 > 0) {
                moveY0 = this._moveY0;
            } else {
                moveY0 = 0;
            }

        }
        this._moveY0 -= moveY0;
        if (moveY0 > 0) {
            for (let i = 0; i < this._randomNameNodeL.length; i++) {
                this._randomNameNodeL[i].y -= moveY0;
                if (this._randomNameNodeL[i].y >= -20 && this._randomNameNodeL[i].y <= 20) {
                    this._randomNameNodeL[i].opacity = 255;
                    if (this._moveY0 == 0) {
                        this._randomNameNodeL[i].color = cc.color(255, 252, 107);
                    }
                } else {
                    this._randomNameNodeL[i].opacity = 102;
                }

            }
        }

        let moveY1 = 10;
        if (this._moveY1 - moveY1 < 0) {
            moveY1 = 0;
            if (this._moveY1 > 0) {
                moveY1 = this._moveY1;
            } else {
                moveY1 = 0;
            }
        }
        this._moveY1 -= moveY1;
        if (moveY1 > 0) {
            for (let i = 0; i < this._randomNameNodeR.length; i++) {
                this._randomNameNodeR[i].y -= moveY1;
                if (this._randomNameNodeR[i].y >= -20 && this._randomNameNodeR[i].y <= 20) {
                    this._randomNameNodeR[i].opacity = 255;
                    if (this._moveY1 == 0) {
                        this._randomNameNodeR[i].color = cc.color(255, 252, 107);
                    }
                } else {
                    this._randomNameNodeR[i].opacity = 102;
                }

            }
        }


        if (this._moveY0 <= 0 && this._moveY1 <= 0) {
            this.unschedule(this.updateRandomName);
            this.scheduleOnce(() => {
                this.showPK();
            }, 1);

        }
    }

    recoverOnStage(data) {
        this.stageInfo.electedUidList = data.electedUidList;
        this.updateStudentUi();
    }

    onStage(data: any) {
        this.stageInfo.electedUidList = data.electedUidList;
        if (this._mode == 1) {
            this.updateStudentUi();
            this.showPK();
        } else if (this._mode == 2) {
            this.playAudioEffer(this._randomSound);
            if (this._isTeacher) {
                this.scheduleOnce(this.showPK, 1.6);
            } else {
                let dataList0 = this.getRandomOnStageData(0);
                let dataList1 = this.getRandomOnStageData(1);
                let uid0 = (this.stageInfo.electedUidList && this.stageInfo.electedUidList[0] && this.stageInfo.electedUidList[0].uid) || "";
                if (uid0) {
                    for (let i = 0; i < dataList0.length; i++) {
                        let node = cc.instantiate(this.pf);
                        node.opacity = 102;
                        let name = dataList0[i].name || "";
                        if (name.length > 11) {
                            name = name.substring(0, 11);
                        }
                        node.getComponent(cc.Label).string = name;
                        node.y = 0 + 20 + (40 * i);
                        node.parent = this.randomNameNode[0];
                        this._randomNameNodeL.push(node);
                        node.x = 0;
                        if (uid0 && dataList0[i].uid == uid0) {
                            this._moveY0 = node.y;
                        }

                    }
                }

                let uid1 = (this.stageInfo.electedUidList && this.stageInfo.electedUidList[1] && this.stageInfo.electedUidList[1].uid) || "";
                if (uid1) {
                    for (let i = 0; i < dataList1.length; i++) {
                        let node = cc.instantiate(this.pf);
                        node.opacity = 102;
                        let name = dataList1[i].name || "";
                        if (name.length > 11) {
                            name = name.substring(0, 11);
                        }
                        node.getComponent(cc.Label).string = name;
                        node.y = 0 + 20 + (40 * i);
                        node.parent = this.randomNameNode[1];
                        this._randomNameNodeR.push(node);
                        node.x = 0;
                        if (uid1 && dataList1[i].uid == uid1) {
                            this._moveY1 = node.y;
                        }
                    }
                }
                this.schedule(this.updateRandomName, 0.02);
            }

            this.randomNameNode[0].active = true;
            this.randomNameNode[1].active = true;

        }
    }
    getRandomOnStageData(index) {
        let list = [];
        let uid = this.stageInfo.electedUidList[index] && this.stageInfo.electedUidList[index].uid;
        if (!uid) {
            return list;
        }
        let data = JSON.parse(JSON.stringify(this.stageInfo.groupUserList));
        let selfData = null;
        while (data.length > 0) {
            let index = Math.floor(Math.random() * data.length);
            if (data[index].uid == uid) {
                selfData = data[index];
            } else {
                list.push(data[index]);
            }
            data.splice(index, 1);
        }
        let list1 = JSON.parse(JSON.stringify(list));
        let list2 = JSON.parse(JSON.stringify(list));
        if (selfData) {
            list1.push(selfData);
        }
        if (list1.length > 0) {
            list = list.concat(list1);
        }
        if (list2.length > 0) {
            list = list.concat(list2);
        }
        return list;
    }


    getRandomPostionMode(sp: cc.SpriteFrame): cc.Vec2 {
        let pos = cc.v2(0, 0);
        if (sp) {
            let minX = -this.cutDownNode.parent.width / 2 + 120 + sp.getTexture().width / 2;
            let maxX = this.cutDownNode.parent.width / 2 - 120 - sp.getTexture().width / 2;
            let minH = -this.cutDownNode.parent.height / 2 + 120 + sp.getTexture().height / 2;
            let maxH = this.cutDownNode.parent.height / 2 - 120 - sp.getTexture().height / 2;
            pos.x = Math.floor(Math.random() * (maxX - minX + 1) + minX);
            pos.y = Math.floor(Math.random() * (maxH - minH + 1) + minH);
        }
        return pos;

    }
    updateMode1Ui() {
        this._mode1CutDownIndex++;
        if (this._mode1CutDownIndex < 3) {
            this.cutDownNode.active = true;
            this.cutDownEndNode.active = false;
            let pos = this.getRandomPostionMode(this._countDownUrlSpr[this._mode1CutDownIndex]);
            this.cutDownNode.getComponent(cc.Sprite).spriteFrame = this._countDownUrlSpr[this._mode1CutDownIndex];
            this.cutDownNode.setPosition(pos);
            this.playAudioEffer(this._countDownSound);
            this.cutDownNode.scale = 1;
            cc.tween(this.cutDownNode)
                .to(0.2, { scale: 1.2 })
                .to(0.2, { scale: 1 })
                .delay(0.3)
                .call(() => {
                    this.cutDownNode.active = false;
                })
                .start();
        } else if (this._mode1CutDownIndex == 3) {
            this.cutDownNode.active = false;
            this.cutDownEndNode.active = true;
            let pos = this.getRandomPostionMode(this._countDownEndUrl);
            this.cutDownEndNode.getComponent(cc.Sprite).spriteFrame = this._countDownEndUrl;
            this.cutDownEndNode.setPosition(pos);
            this.playAudioEffer(this._countDownEndSound);
            this.cutDownEndNode.scale = 1;
            cc.tween(this.cutDownEndNode)
                .to(0.1, { scale: 1.2 })
                .to(0.1, { scale: 1 })
                .start();
        } else if (this._mode1CutDownIndex > 5) {
            this.cutDownNode.active = false;
            this.cutDownEndNode.active = false;
            this.fightNode.active = false;
            this.unschedule(this.updateMode1Ui);
        }

    }
    starPlayMode() {
        if (this._mode == 1) {
            this.fightNode.active = true;
            this._mode1CutDownIndex = -1;
            this.updateMode1Ui();
            this.schedule(this.updateMode1Ui, 1);
        }

    }
    endPlayMode() {
        qte && qte.logCatBoth("xiaoyin", "11执行抢上台：" + this._mode);
        if (this._isTeacher) {
            qte && qte.logCatBoth("xiaoyin", "抢上台:" + this._isTeacher);
            return;
        }
        if (this._mode == 1) {
            this.cutDownNode.active = false;
            this.cutDownEndNode.active = false;
            this.fightNode.active = false;
            this.unschedule(this.updateMode1Ui);
        }
        this._setStateFunc("stuplatform", null);
    }

    playAudioEffer(ad: cc.AudioClip) {
        if (!ad) {
            return;
        }
        let index = this.playAudio(ad, false, () => {
            this.playIngAuidoList[index] = null;
        });
        this.playIngAuidoList[index] = index;
    }
    stopAudioEffer() {
        for (let key in this.playIngAuidoList) {
            if (this.playIngAuidoList[key] != null) {
                this.stopAudio(this.playIngAuidoList[key]);
                this.playIngAuidoList[key] = null;
            }
        }

    }

    async initUi() {
        if (this.leftNodeList.length > 0) {
            for (let i = 0; i < this.leftNodeList.length; i++) {
                this.leftNodeList[i].node.destroy();
            }
        }
        this.leftNodeList = [];
        if (this.rightNodeList.length > 0) {
            for (let i = 0; i < this.rightNodeList.length; i++) {
                this.rightNodeList[i].node.destroy();
            }
        }
        this.rightNodeList = [];
        for (let i = 0; i < this.pkData.question.length; i++) {
            let pos = posConfig[this.pkData.question.length][i];
            let node = new cc.Node();
            let spr = node.addComponent(cc.Sprite)
            spr.trim = false;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.parent = this.starLeft;
            node.setPosition(pos);
            this.leftNodeList.push(spr);

            let node1 = new cc.Node();
            let spr1 = node1.addComponent(cc.Sprite)
            spr1.trim = false;
            spr1.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr1.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node1.parent = this.starRight;
            node1.setPosition(pos);
            this.rightNodeList.push(spr1);
        }


        // if (this.pkData.bgUrl) {
        //     this.loadPicUpdate(this.pkData.bgUrl, this.bg, 1);
        // }
        for (let i = 0; i < this.pkData.countDownUrl.length; i++) {
            await this.loadCountDownUrl(this.pkData.countDownUrl[i], i);
        }
        if (this.pkData.countDownSound) {
            await this.loadMp3Update(this.pkData.countDownSound, "_countDownSound");
        }
        if (this.pkData.countDownEndSound) {
            await this.loadMp3Update(this.pkData.countDownEndSound, "_countDownEndSound");
        }
        if (this.pkData.pkUrl) {
            await this.loadPicUpdate(this.pkData.pkUrl, this.pk, 1);
        }
        if (this.pkData.pkBgUrl0) {
            await this.loadPicUpdate(this.pkData.pkBgUrl0, this.pkL, 1);
        }
        if (this.pkData.pkBgUrl1) {
            await this.loadPicUpdate(this.pkData.pkBgUrl1, this.pkR, 1);
        }
        if (this.pkData.pkSound) {
            await this.loadMp3Update(this.pkData.pkSound, "_pkSound");
        }
        if (this.pkData.addStarSound) {
            await this.loadMp3Update(this.pkData.addStarSound, "_addStarSound");
        }
        if (this.pkData.succSound) {
            await this.loadMp3Update(this.pkData.succSound, "_succSound");
        }
        if (this.pkData.equalSound) {
            await this.loadMp3Update(this.pkData.equalSound, "_equalSound");
        }
        if (this.pkData.randomSound) {
            this.loadMp3Update(this.pkData.randomSound, "_randomSound");
        }
        if (this.pkData.starUrl1) {
            await this.loadPicUpdate(this.pkData.starUrl1, "_starUrl1", 2);
        }
        if (this.pkData.starUrl0) {
            await this.loadPicUpdate(this.pkData.starUrl0, '_starUrl0', 2);
        }
        if (this.pkData.countDownEndUrl) {
            await this.loadPicUpdate(this.pkData.countDownEndUrl, '_countDownEndUrl', 2);
        }
        await this.loadSpine(this.pkData.succSpine, "succSpine");
        await this.loadSpine(this.pkData.equalSpine, "equalSpine0");
        await this.loadSpine(this.pkData.equalSpine, "equalSpine1");
        await this.updateSmartUi();
    }
    async loadSpine(spineData: any, key: string) {
        let spineNode = new cc.Node();
        spineNode.x = 0;
        spineNode.y = 0;
        spineNode.scaleX = 1;
        spineNode.scaleY = 1;
        spineNode.parent = this.node;
        let spine = spineNode.addComponent(sp.Skeleton);
        spineNode.active = false;
        let texture = [];
        let textureName = [];
        for (const iterator of spineData.images) {
            let asset = await this.loadRes(iterator)
            if (asset instanceof cc.Texture2D) {
                asset.setPremultiplyAlpha(true)
            }
            texture.push(asset);
            textureName.push(iterator.substring(iterator.lastIndexOf("/") + 1));
        }
        let atlasText = await this.loadRes(spineData.atlas);
        let skeletonJson = await this.loadRes(spineData.skeleton);

        let asset = new sp.SkeletonData();
        asset.skeletonJson = (skeletonJson as cc.JsonAsset).json;
        asset.atlasText = (atlasText as cc.TextAsset).text;
        asset.textures = texture;
        // @ts-ignore
        asset.textureNames = textureName;
        spine.skeletonData = asset;
        spine.timeScale = 1;
        this[key] = spine;


    }
    loadRes(url): Promise<any> {
        return new Promise<any>((resolve, reject) => {
            this.getRemoteRes(url, null, (error, res: cc.Asset) => {
                if (error) {
                    return;
                }
                resolve(res);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }


    loadPicUpdate(url, sp: cc.Sprite | any, type): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.Texture2D, (error, texture: cc.Texture2D) => {
                if (error) {
                    return;
                }
                if (texture instanceof cc.Texture2D) {
                    texture.setPremultiplyAlpha(true);
                    texture.packable = false;
                }
                let spr = new cc.SpriteFrame(texture);
                if (type == 1) {
                    sp.spriteFrame = spr;
                } else {
                    this[sp] = spr;
                    if (sp == "_starUrl0") {
                        this.updateStarNodeUI();
                    }

                }
                resolve();
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    updateStarNodeUI() {
        let leftWinNum = this.getTotalWinNum(0);
        for (let i = 0; i < this.leftNodeList.length; i++) {
            if (i < leftWinNum) {
                this.leftNodeList[i].spriteFrame = this._starUrl1;
            } else {
                this.leftNodeList[i].spriteFrame = this._starUrl0;
            }
        }
        let rightWinNum = this.getTotalWinNum(1);
        for (let i = 0; i < this.rightNodeList.length; i++) {
            if (i < rightWinNum) {
                this.rightNodeList[i].spriteFrame = this._starUrl1;
            } else {
                this.rightNodeList[i].spriteFrame = this._starUrl0;
            }
        }
    }
    getTotalWinNum(index: number) {
        let num = 0;
        if (this.uidList[index] && this.uidList[index].totalWinNum) {
            num = this.uidList[index].totalWinNum;
        }
        return num;
    }

    loadMp3Update(url, sp: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.AudioClip, (err, audio) => {
                if (err) {
                    console.log("检查::audioUrl", url);
                    return;
                }
                this[sp] = audio;
                resolve();
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

    loadCountDownUrl(url, id): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.Texture2D, (error, texture: cc.Texture2D) => {
                if (error) {
                    return;
                }
                if (texture instanceof cc.Texture2D) {
                    texture.setPremultiplyAlpha(true);
                    texture.packable = false;
                }
                let spr = new cc.SpriteFrame(texture);
                this._countDownUrlSpr[id + ""] = spr;
                resolve();
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });

    }

    async updatePkStudentUi() {
        if (this.stageInfo.electedUidList) {
            for (let i = 0; i < 2; i++) {
                let info = this.stageInfo.electedUidList[i];
                if (info) {
                    this.pkStudentHead[i].spriteFrame = this.defaultHead[1];
                    let uInfo = this.getInfoFromGroup(info.uid)
                    let avatar = info.avatar || ""
                    if (!avatar && uInfo) {
                        avatar = uInfo.avatar;
                    }
                    let score = info.score || 0;
                    if (!score && uInfo) {
                        score = uInfo.score;
                    }

                    this.pkStudentSorce[i].string = score + "";
                    let name = info.name || "";
                    if (!name && uInfo) {
                        name = uInfo.name;
                    }

                    if (name.length > 11) {
                        name = name.substring(0, 11);
                    }
                    this.pkStudentName[i].string = name + "";
                    this.pkStudentBan[i].active = true;
                    if (avatar) {
                        this.loadHead(avatar, this.pkStudentHead[i]);
                    }
                }
            }
        } else {
            for (let i = 0; i < 2; i++) {
                this.pkStudentHead[i].spriteFrame = this.defaultHead[0];
                this.pkStudentBan[i].active = false;
            }
        }

    }
    getInfoFromGroup(uid): any {
        let tager = null;
        for (let i = 0; i < this.stageInfo.groupUserList.length; i++) {
            if (this.stageInfo.groupUserList[i].uid == uid) {
                tager = this.stageInfo.groupUserList[i];
                break;
            }
        }
        return tager;
    }
    async updateStudentUi() {
        if (this.stageInfo.electedUidList) {
            for (let i = 0; i < 2; i++) {
                let info = this.stageInfo.electedUidList[i];
                if (info) {
                    this.studentHead[i].spriteFrame = this.defaultHead[1];
                    let uInfo = this.getInfoFromGroup(info.uid)
                    let avatar = info.avatar || ""
                    if (!avatar && uInfo) {
                        avatar = uInfo.avatar;
                    }
                    let score = info.score || 0;
                    if (!score && uInfo) {
                        score = uInfo.score;
                    }
                    this.studentSorce[i].string = score + "";
                    let name = info.name || "";
                    if (!name && uInfo) {
                        name = uInfo.name;
                    }
                    if (name.length > 11) {
                        name = name.substring(0, 11);
                    }
                    this.studentName[i].string = name + "";
                    // debugger显示uid
                    //this.studentName[i].string = info.uid + "";
                    this.studentBan[i].active = true;
                    if (avatar) {
                        this.loadHead(avatar, this.studentHead[i]);
                    }
                }
            }
        } else {
            for (let i = 0; i < 2; i++) {
                this.studentHead[i].spriteFrame = this.defaultHead[0];
                this.studentBan[i].active = false;
            }
        }

    }
    loadHead(url, sp: cc.Sprite): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.Texture2D, (error, texture: cc.Texture2D) => {
                if (error) {
                    return;
                }
                texture.setPremultiplyAlpha(true);
                let spr = new cc.SpriteFrame(texture);
                sp.spriteFrame = spr;
                let scale = 1;
                if (sp.node.height > 109) {
                    let scale1 = 109 / sp.node.height;
                    if (scale > scale1) {
                        scale = scale1;
                    }
                }
                sp.node.scale = scale;
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });

    }


    loadPicInQuestionUrl(url): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.Texture2D, (error, texture: cc.Texture2D) => {
                if (error||!cc.isValid(this.node,true)) {
                    return;
                }
                // texture.setPremultiplyAlpha(true);
                let spr = new cc.SpriteFrame(texture);
                this.quetionPic.spriteFrame = spr;
                let scale = 1;
                if (this.quetionPic.node.height > 140) {
                    let scale1 = 140 / this.quetionPic.node.height;
                    if (scale > scale1) {
                        scale = scale1;
                    }
                }
                if (this.quetionPic.node.width > 140) {
                    let scale1 = 140 / this.quetionPic.node.width;
                    if (scale > scale1) {
                        scale = scale1;
                    }
                }
                this.quetionPic.node.scale = scale;
                this.quetionPic.node.parent.active = true;
                resolve();
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });

    }

    async updateSmartUi() {
        let smartData = this.pkData.question[this._QIndex]
        if (smartData) {
            let name = smartData.name;
            this.quetionName.string = name + "（" + (this._QIndex + 1) + "/" + this.pkData.question.length + "）";
            this.quetionLabel0.string = smartData.text0;
            this.quetionLabel1.string = smartData.text1;
            if (smartData.picUrl) {
                this.jianju.active = false;
                await this.loadPicInQuestionUrl(smartData.picUrl);
            } else {
                this.quetionPic.node.parent.active = false;
                this.scheduleOnce(() => {
                    let culH = this.quetionLabel0.node.height + this.quetionLabel1.node.height + 8;
                    if (culH + 21 > this.content.parent.height) {
                        this.jianju.active = false;
                    } else {
                        let clh = (this.content.parent.height - (culH + 21)) / 2 - 21;
                        if (clh < 0) {
                            clh = 0;
                        }
                        this.jianju.height = clh;
                        this.jianju.active = true;
                    }
                }, 0.05);
            }
        }
        this.scrollView.scrollToTop(0);
    }

    public reset() {
    }

}
