
const { ccclass, property } = cc._decorator;

@ccclass
export default class KeyBoardUI extends cc.Component {

    @property(cc.SpriteFrame)
    azList: cc.SpriteFrame[] = [];

    @property(cc.SpriteFrame)
    AZList: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    azSpr: cc.Sprite = null;

    @property(cc.Sprite)
    AZSpr: cc.Sprite = null;

    @property(cc.Node)
    xiaoxieRootNode: cc.Node = null;

    @property(cc.Node)
    daxieRootNode: cc.Node = null;
    xiaoxieList: any = [
            {
                "name": "q",
                "x": -572,
                "y": 109
            },
            {
                "name": "w",
                "x": -460,
                "y": 109
            },
            {
                "name": "e",
                "x": -348,
                "y": 109
            },
            {
                "name": "r",
                "x": -236,
                "y": 109
            },
            {
                "name": "t",
                "x": -124,
                "y": 109
            },
            {
                "name": "y",
                "x": -12,
                "y": 109
            },
            {
                "name": "u",
                "x": 100,
                "y": 109
            },
            {
                "name": "i",
                "x": 212,
                "y": 109
            },
            {
                "name": "o",
                "x": 324,
                "y": 109
            },
            {
                "name": "p",
                "x": 436,
                "y": 109
            },
            {
                "name": "a",
                "x": -508,
                "y": 31
            },
            {
                "name": "s",
                "x": -396,
                "y": 31
            },
            {
                "name": "d",
                "x": -284,
                "y": 31
            },
            {
                "name": "f",
                "x": -172,
                "y": 31
            },
            {
                "name": "g",
                "x": -60,
                "y": 31
            },
            {
                "name": "h",
                "x": 52,
                "y": 31
            },
            {
                "name": "j",
                "x": 164,
                "y": 31
            },
            {
                "name": "k",
                "x": 276,
                "y": 31
            },
            {
                "name": "l",
                "x": 388,
                "y": 31
            },
            {
                "name": "z",
                "x": -396,
                "y": -46
            },
            {
                "name": "x",
                "x": -284,
                "y": -46
            },
            {
                "name": "c",
                "x": -172,
                "y": -46
            },
            {
                "name": "v",
                "x": -60,
                "y": -46
            },
            {
                "name": "b",
                "x": 52,
                "y": -46
            },
            {
                "name": "n",
                "x": 164,
                "y": -46
            },
            {
                "name": "m",
                "x": 276,
                "y": -46
            }
        ];
    daxieList: any = [
            {
                "name": "Q",
                "x": -572,
                "y": 109
            },
            {
                "name": "W",
                "x": -460,
                "y": 109
            },
            {
                "name": "E",
                "x": -348,
                "y": 109
            },
            {
                "name": "R",
                "x": -236,
                "y": 109
            },
            {
                "name": "T",
                "x": -124,
                "y": 109
            },
            {
                "name": "Y",
                "x": -12,
                "y": 109
            },
            {
                "name": "U",
                "x": 100,
                "y": 109
            },
            {
                "name": "I",
                "x": 212,
                "y": 109
            },
            {
                "name": "O",
                "x": 324,
                "y": 109
            },
            {
                "name": "P",
                "x": 436,
                "y": 109
            },
            {
                "name": "A",
                "x": -508,
                "y": 31
            },
            {
                "name": "S",
                "x": -396,
                "y": 31
            },
            {
                "name": "D",
                "x": -284,
                "y": 31
            },
            {
                "name": "F",
                "x": -172,
                "y": 31
            },
            {
                "name": "G",
                "x": -60,
                "y": 31
            },
            {
                "name": "H",
                "x": 52,
                "y": 31
            },
            {
                "name": "J",
                "x": 164,
                "y": 31
            },
            {
                "name": "K",
                "x": 276,
                "y": 31
            },
            {
                "name": "L",
                "x": 388,
                "y": 31
            },
            {
                "name": "Z",
                "x": -396,
                "y": -46
            },
            {
                "name": "X",
                "x": -284,
                "y": -46
            },
            {
                "name": "C",
                "x": -172,
                "y": -46
            },
            {
                "name": "V",
                "x": -60,
                "y": -46
            },
            {
                "name": "B",
                "x": 52,
                "y": -46
            },
            {
                "name": "N",
                "x": 164,
                "y": -46
            },
            {
                "name": "M",
                "x": 276,
                "y": -46
            }
        ];
    xiaoxieNodeList: cc.Node[] = [];
    daxieNodeList: cc.Node[] = [];
    onLoad(): void {
        // this.daxieRootNode.children.forEach((child, index) => {
        //     if (child.name.length == 1) {
        //         this.xiaoxieList.push({ name: child.name.toLocaleLowerCase(), x: child.x, y: child.y });
        //     }
        // });
        // console.error(this.xiaoxieList)
    }
    changePosList(showType: number) {
        if (showType == 0) {
            this.azSpr.spriteFrame = this.azList[0];
            this.AZSpr.spriteFrame = this.AZList[0];
            for (let i = 0; i < this.xiaoxieList.length; i++) {
                let name = this.xiaoxieList[i].name;
                let cNode = this.xiaoxieRootNode.getChildByName(name);
                if (cNode) {
                    cNode.setPosition(this.xiaoxieList[i].x, this.xiaoxieList[i].y);
                }

            }
            for (let i = 0; i < this.daxieList.length; i++) {
                let name = this.daxieList[i].name;
                let cNode = this.daxieRootNode.getChildByName(name);
                if (cNode) {
                    cNode.setPosition(this.daxieList[i].x, this.daxieList[i].y);
                }
            }


        } else if (showType == 1) {
            this.azSpr.spriteFrame = this.azList[1];
            this.AZSpr.spriteFrame = this.AZList[1];
            let sortXiaoList = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z'];
            for (let i = 0; i < sortXiaoList.length; i++) {
                let name = sortXiaoList[i];
                let cNode = this.xiaoxieRootNode.getChildByName(name);
                if (cNode) {
                    cNode.setPosition(this.xiaoxieList[i].x, this.xiaoxieList[i].y);
                }
            }

            let sortDaxieList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
            for (let i = 0; i < sortDaxieList.length; i++) {
                let name = sortDaxieList[i];
                let cNode = this.daxieRootNode.getChildByName(name);
                if (cNode) {
                    cNode.setPosition(this.daxieList[i].x, this.daxieList[i].y);
                }
            }
        }

    }


}
