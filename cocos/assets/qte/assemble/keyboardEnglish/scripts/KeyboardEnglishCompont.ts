

import BaseComponent from "../../core/BaseComponent";
import ComponentFactory from "../../core/ComponentFactory";
import { keyboardEnglishEnum, keyboardEnglishModelEnum } from "./KeyboardEnglishConfig";




/**
 * 组件名称：键盘组件
 * 作者：武建凯
 * 
 *
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class KeyboardEnglishComponent extends BaseComponent {
    private currentNode: cc.Node = null//当前键盘实体
    public eventNode: cc.Node = null//事件接收节点
    private xiaoxieModelNode: cc.Node = null;//小写字母模式实体
    private daxieModelNode: cc.Node = null;//大写字母模式实体
    private biaodianshuziModelNode: cc.Node = null;//标点数字模式实体
    private ctril: cc.Node = null;// 收起弹出按钮
    private group: cc.Node = null;//键盘model父级
    private isOpen = true;//默认展开
    private isPackUp: any;//是否有控制按钮

    private keyBoardUI: any = null;
    private azMode: number = 0;// az模式
    pcGuideNode: cc.Node = null;

    /**
     * 换肤
     * @param key 
     * @param data 
     */
    changeProperties(key: string, data: any) {
        if (key == "keyboardType") {
            //键盘类型切换
            this.initComponent({
                properties: {
                    keyboardType: data || keyboardEnglishEnum.COCOS_NORMAL
                }
            })
        }
        if (key == "isPackUp") {
            //键盘类型切换
            this.changeIsPackUp(data)
        }

    }


    //变更isPackUp属性自适应
    changeIsPackUp(data) {
        this.isPackUp = data;
        this.ctril.active = this.isPackUp;
        this.node.height = data ? 386 : 326;
        this.node.parent.y = data ? -167 : -197;
        this.node.children[0].height = data ? 386 : 326;
        this.onSizeChange(this.node.getContentSize());
        console.log("english", this.node.height, this.node.y)

    }


    /**
     * 获取键盘样式实体
     * @param bundle 
     * @param typeName 
     */
    public createCore(bundle: cc.AssetManager.Bundle, typeName: string): Promise<cc.Node> {
        return new Promise<cc.Node>((reslove) => {
            let path = `assemble/keyboardEnglish/prefabs/keyboardUi/${typeName}/prefab/entry`;
            bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
                if (err) {
                    console.error(err);
                    return;
                }
                let node = cc.instantiate(assets);
                console.log(assets);
                this.keyBoardUI = node.getComponent("KeyBoardUI");
                node.active = true;
                reslove(node);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err)
            return null;
        });
    }

    //键盘模式切换
    private changeModel(currentModel: keyboardEnglishModelEnum) {
        this.biaodianshuziModelNode.active = currentModel == keyboardEnglishModelEnum.SHUZI;
        this.xiaoxieModelNode.active = currentModel == keyboardEnglishModelEnum.ZIMU_SMALL;
        this.daxieModelNode.active = currentModel == keyboardEnglishModelEnum.ZIMU_BIG;
    }
    //为键盘按钮添加点击监听
    private addListen(node: cc.Node) {
        node.children.forEach((node_item: cc.Node) => {
            node_item.on(cc.Node.EventType.TOUCH_START, this.cellTouchStart, this);
            node_item.on(cc.Node.EventType.TOUCH_CANCEL, this.cellTouchEnd, this)
            node_item.on(cc.Node.EventType.TOUCH_END, this.cellTouchEnd, this)
        })
    }
    //点击收起、展开点击
    private touchCtril() {
        this.touchCtrilCore(this.isOpen)
    }
    updatePcGuideNodeY() {
        if (!cc.isValid(this.pcGuideNode, true) || !cc.isValid(this.currentNode, true)) {
            return;
        }
        if (this.isOpen) {
            let groupy = this.currentNode.y - this.currentNode.height / 2 + this.group.height / 2;
            this.pcGuideNode.y = (groupy + this.group.height / 2 + 10 + this.pcGuideNode.height / 2);
        } else {
            this.pcGuideNode.y = (this.currentNode.y - this.currentNode.height / 2 + 10 + this.pcGuideNode.height / 2);
        }
    }

    //点击收起、展开方法
    touchCtrilCore(isOpen: boolean) {
        this.isOpen = !isOpen;
        this.group.active = this.isOpen;
        this.ctril.children[0].active = !this.isOpen;
        this.updatePcGuideNodeY();
        if (this.isOpen) {
            this.ctril.getComponent(cc.Widget).isAlignTop = true;
            this.ctril.getComponent(cc.Widget).isAlignBottom = false;
            this.ctril.getComponent(cc.Widget).top = 0;
            if (!this.node.getComponent(cc.BlockInputEvents)) {
                this.node.addComponent(cc.BlockInputEvents)
            }

        } else {
            this.ctril.getComponent(cc.Widget).isAlignTop = false;
            this.ctril.getComponent(cc.Widget).isAlignBottom = true;
            this.ctril.getComponent(cc.Widget).bottom = 0;
            if (this.node.getComponent(cc.BlockInputEvents)) {
                this.node.removeComponent(cc.BlockInputEvents)
            }
        }

    }





    //初始化键盘
    public init() {
        this.isOpen = true;
        this.group.active = true;
        this.ctril.children[0].active = !this.isOpen;
        this.touchCtrilCore(false)
        this.changeModel(keyboardEnglishModelEnum.ZIMU_SMALL);
        this.changeIsPackUp(this.isPackUp)
        this.changeAz(0);
    }

    changeAz(type: number) {
        this.azMode = type;
        this.keyBoardUI && this.keyBoardUI.changePosList(type);
    }


    //初始化键盘组件
    async initComponent(data?: any) {
        let type = keyboardEnglishEnum.COCOS_NORMAL + ""
        this.isPackUp = data ? data.properties.isPackUp : false;
        this.node.height = this.isPackUp ? 386 : 326;
        this.currentNode = await this.createCore(ComponentFactory.getInstance().bundle, type);
        if (!cc.isValid(this.node, true)) {
            return;
        }
        this.node.removeAllChildren();
        this.node.addChild(this.currentNode);
        this.group = this.currentNode.getChildByName("group");
        this.pcGuideNode = this.currentNode.getChildByName("pcGuide");
        this.biaodianshuziModelNode = this.group.getChildByName(keyboardEnglishModelEnum.SHUZI);
        this.xiaoxieModelNode = this.group.getChildByName(keyboardEnglishModelEnum.ZIMU_SMALL);
        this.daxieModelNode = this.group.getChildByName(keyboardEnglishModelEnum.ZIMU_BIG);
        this.ctril = this.currentNode.getChildByName("ctril");
        this.ctril.active = this.isPackUp;
        this.isOpen = true;
        this.ctril.children[0].active = !this.isOpen;

        this.changeModel(keyboardEnglishModelEnum.ZIMU_SMALL)
        this.ctril.on(cc.Node.EventType.TOUCH_START, this.touchCtril, this)
        this.addListen(this.biaodianshuziModelNode)
        this.addListen(this.xiaoxieModelNode)
        this.addListen(this.daxieModelNode)
        this.ctril.getComponent(cc.Widget).isAlignTop = true;
        this.ctril.getComponent(cc.Widget).isAlignBottom = false;
        this.ctril.getComponent(cc.Widget).top = 0;
        this.changeIsPackUp(this.isPackUp);
        if (!this.node.getComponent(cc.BlockInputEvents)) {
            this.node.addComponent(cc.BlockInputEvents)
        }
        this.changeAz(0);
        this.updatePcGuideNodeY();


    }

    //点击单个键盘按键
    private cellTouchStart(e: cc.Event.EventTouch) {
        let node: cc.Node = e.target;
        node['isPingTouch'] = true;
        this.updateTouchEndUI(node);
    }
    public cellTouchEnd(e: cc.Event.EventTouch) {
        console.log("WKK点击了")
        let node: cc.Node = e.target;
        node['isPingTouch'] = false;
    }

    updateTouchEndUI(node: cc.Node) {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        let value: string = node.name;
        switch (value) {
            case keyboardEnglishModelEnum.az: {
                this.azMode = this.azMode == 0 ? 1 : 0;
                this.changeAz(this.azMode);
                break;
            }
            case keyboardEnglishModelEnum.SHUZI:
                this.changeModel(value)
                break;
            case keyboardEnglishModelEnum.ZIMU_BIG:
                this.changeModel(value)
                break;
            case keyboardEnglishModelEnum.ZIMU_SMALL:
                this.changeModel(value)
                break;
            case keyboardEnglishModelEnum.KONGGE:
                this.eventNode.emit("keyboardTouch", " ")
                //    this.changeProperties("isPackUp", !this.isPackUp)
                break;
            default:
                this.eventNode.emit("keyboardTouch", value)
                break;
        }
    }


    pcKeyboardTouch(str: string) {
        let pNode = this.biaodianshuziModelNode;
        if (this.biaodianshuziModelNode.active) {
            pNode = this.biaodianshuziModelNode;
        } else if (this.xiaoxieModelNode.active) {
            pNode = this.xiaoxieModelNode;
        } else if (this.daxieModelNode.active) {
            pNode = this.daxieModelNode;
        }
        let childeName = str;
        if (childeName == " ") {
            childeName = "kongge";
        }

        let cNode = pNode.getChildByName(childeName);
        if (cNode) {
            let btn = cNode.getComponent(cc.Button);
            let sp = cNode.getComponent(cc.Sprite);
            if (sp && btn && btn.pressedSprite) {
                sp.spriteFrame = btn.pressedSprite;
            }
            this.updateTouchEndUI(cNode);
            cNode.stopAllActions();
            cc.tween(cNode).delay(0.2).call(() => {
                if (!cNode['isPingTouch']) {
                    if (cc.isValid(this.node, true) && sp && btn && btn.normalSprite) {
                        sp.spriteFrame = btn.normalSprite;
                    }
                }
            }).start();
        } else if (str.length == 1) {
            this.eventNode.emit("keyboardTouch", str)
        }

    }


    public changeSkine(path: string, param?: any) {
        throw new Error("Method not implemented.");
    }
    playPCGuide() {
        this.updatePcGuideNodeY();
        if (this.pcGuideNode) {
            this.pcGuideNode.active = true;
            let orX = this.pcGuideNode.x;
            cc.tween(this.pcGuideNode).to(0.1, { x: orX+20 }).to(0.2, { x: orX-20 }).to(0.125, { x: orX+10 }) .to(0.1, { x: orX-10 }).to(0.05,{x:orX}).delay(2.5).call(() => { this.pcGuideNode.active = false }).start();
        }
    }


}