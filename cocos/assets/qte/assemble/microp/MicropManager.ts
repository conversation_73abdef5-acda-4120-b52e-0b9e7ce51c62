/*
 * @FilePath     : /sdk/assets/qte/assemble/microp/MicropManager.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import RecordEngine from "../record/RecordEngine";
import MicropComponent from "./MicropComponent";
import { EngineCallBack, MicropState, ResultData } from "./MicropData";
import MicropEngineCallBack from "./MicropEngineCallBack";

export default class MicropManager {

    // 回调监听
    private micropEngineCallBack: MicropEngineCallBack;

    private micropComp: MicropComponent;

    private micropEngine:any = null;

    private updateTimeFunc = null;

    private overTimeFunc = null;

    private isOverStop = false;

    private isFinish = false;

    private _isRecordAuthority = null; // 请求权限结果。
    public get isRecordAuthority() {
        return this._isRecordAuthority;
    }
    public set isRecordAuthority(value) {
        this._isRecordAuthority = value;
    }

    private _isAuthorityIng = false; // 是否在请求权限中。
    public get isAuthorityIng() {
        return this._isAuthorityIng;
    }
    public set isAuthorityIng(value) {
        this._isAuthorityIng = value;
    }

    public resultData = null;      // 评测结果
    private currentRemoveCb = null;

    // 初始化RecordManager
    public initEngine(engineCB: MicropEngineCallBack, engine, target: MicropComponent,isCheck?) {
        console.log("%c Line:51 🍺 isCheck=", "color:#93c0a4", isCheck);
        this.isRecordAuthority = null;
        this.isAuthorityIng = false;
        console.log("engine==", engine);
        if (!engine || CC_PREVIEW) {
            this.micropEngine = new RecordEngine();
        } else {
            this.micropEngine = engine;
        }
        this.micropEngineCallBack = engineCB;
        this.micropComp = target;
        // 优先检测是否有麦克风权限
        if(isCheck){
            this.initAuthorityAction();
        }
    }
    
    initAuthorityAction() {
        console.log("%c Line:68 🥒 initAuthorityAction", "color:#42b983");
        if(this.micropComp.micropState === MicropState.Idle){
            this.isAuthorityIng = true;
            this.checkRecordAuthorityAction((isAuthority) => {
                console.log("%c Line:72 🍿 initAuthorityAction isAuthority", "color:#e41a6a", isAuthority);
            });
        }
    }

    checkRecordAuthorityAction(back){
        this.micropEngine.checkRecordAuthorityAction((isAuthority)=>{
            this.isRecordAuthority = isAuthority.status;
            qte && qte.logCatBoth('checkRecordAuthorityAction', isAuthority);
            isAuthority.status && this.micropComp.sendCallBack(EngineCallBack.CheckAuthority, true);
            console.log("isAuthority,",isAuthority);
            this.micropComp.showAuthority(!isAuthority.status);
            back(isAuthority);
        })
    }


    /**
     * @msg     : 请求语音权限
     * @param    {*}
     * @return   {*}
     */

    reqRecordAuthority(isTouch?) {
        // 开始没有检测权限，在开始录音前去检测下
        console.log("%c Line:95 🍭 reqRecordAuthority this.isRecordAuthority", "color:#e41a6a", this.isRecordAuthority);
        if((this.isRecordAuthority == null || isTouch == true) && this.isAuthorityIng === false ){
            this.isAuthorityIng = true;
            this.checkRecordAuthorityAction((isAuthority)=>{
                this.handleBeginAuthor(isAuthority,isTouch);
            });
        }else{
            // 开始检测了权限，设置二次弹窗提示
            this.handleBeginAuthor(this.isRecordAuthority);
        }
    }
    /**
     * @msg     : 开始后 处理权限结果
     * @param    {*} isAuthority
     * @return   {*}
     */    
    handleBeginAuthor(isAuthority,isTouch?) {
        this.isAuthorityIng = false;
        if (this.isRecordAuthority) {
            // 判断是否自动开始。
            this.autoBegin(isTouch);
        } else if (isAuthority == null || isAuthority.status == null) {
            // 主讲端回调，无处理
            return;
        } else {
            this.micropComp.showNoticeChoice('未允许使用麦克风，无法作答题目，请点击确定并允许打开麦克风权限', () => {
                console.log("%c Line:113 🥖 handleBeginAuthor showNoticeChoice sure", "color:#fca650" );
                this.isRecordAuthority = null;
                this.reqRecordAuthority(false);
            }, () => {
                console.log("showNoticeChoice close");
            }, true);

        }
    }

    autoBegin(isTouch?) {
        if ((this.micropComp.recordData.voiceEngineProp.autoBegin && this.isRecordAuthority) || (this.isRecordAuthority && isTouch)) {
            this.startRecord();
        }
    }
    public reset() {
        this.isOverStop = false;
        this.isFinish = false;
        this.updateTimeFunc = null;
        this.overTimeFunc = null;
    }

    /**
     * 开始录音
     * 说明： 调用 stopRecord后会 返回resultCb
     * @param parm
     */
    public startRecord() {
        // 判断是否是第二次之后的点击
        if(this.isFinish || this.isOverStop){
            // console.log("remove")
            // this.currentRemoveCb && delete window[this.currentRemoveCb];
            // this.micropComp.recordData.reqRecordData['callbackKey'] = null;
            this.isFinish = false;
            this.isOverStop = false;
        }
        this.micropComp.changeState(MicropState.Start);
        this.updateTimeFunc = this.update.bind(this);
        this.micropComp.schedule(this.updateTimeFunc);
        this.micropEngine.startRecordAction(
            this.micropComp.recordData.reqRecordData,
            res => {
                // 不管结果如何，执行评测完成回调
                this.micropComp.sendCallBack(EngineCallBack.AnswerCorrectCb, "");
                if (res && res.status === 0) {
                    let is_finish = res.result.final;
                    if (!this.isOverStop && is_finish) {
                        console.log(" res === finish =",res);
                        this.stopOverTime();
                        this.isFinish = is_finish;
                        let _data = JSON.parse(res.result.hypotheses[0].transcript);
                        console.log("_data====",_data);
                        console.log("_data.score,",_data.score);
                        console.log("_data.online_audio_url,",_data.online_audio_url);
                        console.log("this.resultData===",this.resultData);
                        if(((_data.score || _data.score == 0) && _data.online_audio_url) || this.resultData == null){
                            this.resultData = _data;
                        }
                        console.log("this.resultData===",this.resultData);

                        this.micropEngineCallBack.startRecordingCb(this.resultData);
                        this.micropComp.changeState(MicropState.Stop);
                    }
                } else {
                    console.log("返回参数 error");
                }
            },
            (removeCb?) => {
                console.log("removeCb this.isFinish: ", this.isFinish);
                this.currentRemoveCb = removeCb;
                if (this.isFinish) {
                    removeCb && delete window[removeCb];
                    this.currentRemoveCb = null;
                }
            },
            () => {
                // 可以设置通知开始结束了。
                
            },
        );
        this.micropEngineCallBack.startReadyEndCb();
    }
    update(dt) {
        if (this.micropComp.countDownNum >= 0) {
            this.micropComp.countdown(dt);
        } else {
            // 倒计时结束，执行评分。
            if (!this.isOverStop) {
                this.recording();                
            }
        }
    }
    /**
     * @msg     : 计算分数
     * @param    {*}
     * @return   {*}
     */
    recording() {
        console.log("recording=====");
        this.micropComp.unschedule(this.updateTimeFunc);
        this.micropComp.changeState(MicropState.Loading);
        this.overTimeFunc = this.overTimeHandle.bind(this);
        console.log('%c 🥨 this.overTimeFunc: ', 'font-size:20px;background-color: #2EAFB0;color:#fff;', this.overTimeFunc);
        // 开始超时计时。
        this.micropComp.schedule(this.overTimeFunc, 10, 0);
        this.stopRecord();
    }
    /**
     * @msg     : 停止超时
     * @return   {*}
     */    
    stopOverTime(){
        console.log("this.stopOverTime overTimeFunc===",this.overTimeFunc);
        if (this.overTimeFunc) {
            this.micropComp.unschedule(this.overTimeFunc);
            this.overTimeFunc = null;
        }
    }

    // 超时，直接处理
    overTimeHandle() {
        this.stopOverTime();
        this.micropComp.sendCallBack(EngineCallBack.UploadStop,null);
        console.log("overTimeHandle: 超时，直接处理");
        this.isOverStop = true;
        let _data = {
            "result":{
                'hypotheses':[
                    {
                        transcript:{}
                    }
                ]
            }
        }
        if(this.resultData){
           _data.result.hypotheses[0].transcript = this.resultData; 
        }
        console.log("overTimeHandle",_data);
        this.resultData = _data.result.hypotheses[0].transcript;
        this.micropEngineCallBack.startRecordingCb(_data.result.hypotheses[0].transcript);
        this.micropComp.changeState(MicropState.Stop);
    }

    /** 停止录音 */
    stopRecord() {
        this.micropEngine.stopRecordAction();
        this.micropComp.sendCallBack(EngineCallBack.UploadStop,null);
    }
    /**
     * @msg     : 题组时，切题恢复数据
     * @param    {*} url
     * @param    {*} score
     * @return   {*}
     */    
    recordGroupData(url,score){
        let _data = {
            "result":{
                'hypotheses':[
                    {
                        transcript:{
                            online_audio_url:url,
                            score:score,
                        }
                    }
                ]
            }
        }
        this.resultData = _data.result.hypotheses[0].transcript;
    }
    /**
     * @msg     : 清理
     * @return   {*}
     */    
    destroy() {
        console.log("%c Line:275 🥤 this.micropComp", "color:#ed9ec7", this.micropComp);
        if (this.micropComp) {
            this.micropComp.unschedule(this.updateTimeFunc);
            this.micropComp.unschedule(this.overTimeFunc);
            this.micropEngineCallBack.reset();
        }
        this.currentRemoveCb && delete window[this.currentRemoveCb];
        this.currentRemoveCb = null;
    }
}
