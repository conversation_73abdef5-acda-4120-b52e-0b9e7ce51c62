/*
 * @FilePath     : /sdk/assets/qte/assemble/microp/MicropEngineCallBack.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import MicropComponent from "./MicropComponent";
import { EngineCallBack, MicropState, ResultData } from "./MicropData";

export default class MicropEngineCallBack {
    private micropComp: MicropComponent;
    // 是否在后台
    private isGamePause = false;
    private stopRecordEndCb = null;


    // 处于后台保存事件处理
    private gamePaseCb: {
        parm: any;
        cb: (data) => void;
    } = null;

    public reset() {
        this.stopRecordEndCb = null;
        this.isGamePause = false;
        this.unregisterEvent();
    }


    public regiestComponent(cmpt: MicropComponent) {
        this.micropComp = cmpt;
        this.registerEvent();
    }

    public startReadyEndCb() {
        this.micropComp.sendCallBack(EngineCallBack.StartRecordingCb, null);
    }

    // #region  回调事件处理
    /**
     * 开始录音的回调
     * @param v
     */
    public startRecordingCb(data: any) {
        console.log("startRecordingCb  data", data);
        
        if (this.isGamePause) {
            this.gamePaseCb = {
                parm: data,
                cb: this.startRecordingCb.bind(this),
            };
            return;
        }
        // 判断是语音结果
        // let result = JSON.parse(data.result.hypotheses[0].transcript);
        let result = data;
        let callData:ResultData = {
            'score': this.checkDataNull(result.score) ? Math.floor(result.score):-1,
            'status':result.status,
            'wordList':this.checkDataNull(result.wordList) ? result.wordList:[],
            'audioTime':this.checkDataNull(result.length_ms) ? result.length_ms : 0,
            'audioUrl':this.checkDataNull(result.online_audio_url) ? result.online_audio_url.split(".mp3")[0] + ".mp3" : ""
        }
        console.log("callData==",callData);
        if (result.status === 0) {
            // 成功
            this.micropComp.sendCallBack(EngineCallBack.RecordSuccess, callData);
        } else {
            // 失败
            this.micropComp.sendCallBack(EngineCallBack.RecordError, callData);
        }
    }
    checkDataNull(a){
        if(!a && a != 0){
            return false;
        }else{
            return true;
        }
    }
    public stopRecordCb(cb?) {
        // this.stopRecordEndCb = cb;
    }

    public onGamePause() {
        this.isGamePause = true;
    }

    public onGameResume() {
        this.isGamePause = false;
        if (this.gamePaseCb) {
            this.gamePaseCb.cb(this.gamePaseCb.parm);
            this.gamePaseCb = null;
        }
        this.micropComp.micropManager.initAuthorityAction();


    }
    registerEvent() {
        //     // this.recorDCmpt.cocosBridge.on('innerPause', this.onGamePause, this);
        cc.game.on(cc.game.EVENT_HIDE, this.onGamePause, this);
        cc.game.on(cc.game.EVENT_SHOW, this.onGameResume, this);
        // this.recorDCmpt.cocosBridge.on('innerResume', this.onGameResume, this);
    }

    unregisterEvent() {
        cc.game.off(cc.game.EVENT_HIDE, this.onGamePause, this);
        cc.game.off(cc.game.EVENT_SHOW, this.onGameResume, this);
        // this.recorDCmpt.cocosBridge.off('innerPause', this.onGamePause, this);
        // this.recorDCmpt.cocosBridge.off('innerResume', this.onGameResume, this);
    }
}
