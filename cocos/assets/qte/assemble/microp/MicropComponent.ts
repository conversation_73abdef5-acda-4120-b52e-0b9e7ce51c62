import ComponentUtils from "../componentUtils/ComponentUtils";
import BaseComponent from "../core/BaseComponent";
import RecordData from "../record/RecordData";
import { EngineCallBack, MicropState } from "./MicropData";
import MicropEngine<PERSON>allBack from "./MicropEngineCallBack";
import MicropManager from "./MicropManager";

/*
 * @FilePath     : /sdk/assets/qte/assemble/microp/MicropComponent.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
const {ccclass, property} = cc._decorator;

@ccclass
export default class MicropComponent extends BaseComponent {

    @property(cc.Node)
    private soundByteNodeLeft:cc.Node = null;
    @property(cc.Node)
    private soundByteNodeRight:cc.Node = null;
    @property(cc.SpriteFrame)
    private soundByteImages:cc.SpriteFrame [] = [];
    private soundIndex = 0;
    public micropState = MicropState.Idle;
    @property(cc.Node)
    private idleNode:cc.Node = null;
    @property(cc.Node)
    private labaNode:cc.Node = null;
    @property(cc.Node)
    private micNode:cc.Node = null;
    @property(cc.Node)
    private tipsWordNode:cc.Node = null;        // 文字提示
    
    private listenerMap;        // 题版注册周期函数

    public  recordData:RecordData = null;

    public countDownNum:number = 15;

    public micropCallBack = null;

    private countTimeProg:cc.ProgressBar = null;           // 倒计时组件
    public micropManager:MicropManager = null;

    @property(cc.Node)
    private loadNode:cc.Node = null;

    private loadProgNode:cc.Node = null;            // load prog
    private loadBarNode:cc.Node = null;             // load bar

    @property(cc.Node)
    private labaView:cc.Node = null;        // 喇叭播放动画显示
    private audioIsPlay:boolean = false;    // 喇叭是否正在播放；
    private audioId = null;                 // 播放音频 id

    private labaBtn:cc.Button = null;
    private idleBtn:cc.Button = null;

    @property(cc.Node)
    private authorityNode:cc.Node = null;       // 权限提示

    @property(cc.Node)
    private wordAudioNode:cc.Node = null;   // 单词音频
    @property(cc.Node)
    private wordAudioView:cc.Node = null;   // 单词音频动画
    private wordAudioId = null;
    private wordAudioAuto = false;          // 单词结束是否自定开始
    
    onLoad(){
        
    }

    public templateInit(engine,isCheck?) {
        this.changeState(MicropState.Idle);
        this.labaViewAnimation(false);
        this.stopSoundAnimation();
        this.micropManager.destroy();
        this.micropManager.initEngine(this.micropCallBack, engine,this, isCheck);
        this.micropCallBack.regiestComponent(this);
    }
    public micBegin(){
        console.log("%c Line:85 🥐 micBegin", "color:#4fff4B");
        this.changeState(MicropState.Ready);
        // 检测是否自动播放
        this.micropManager.reqRecordAuthority(false);
    }
    public micWordAudio(){
        this.wordAudioAuto = true;
        this.playWordAudio();
    }
    /**
     * @msg     : 是否显示权限提示
     * @param    {*} isView
     * @return   {*}
     */    
    showAuthority(isView){
        if(cc.isValid(this.authorityNode,true))
            this.authorityNode.active = isView;
    }

    public initComponent(data?: any) {
        
        this.wordAudioNode.active = false;
        this.wordAudioView.active = false;

        this.countTimeProg = cc.find('timeNode',this.micNode).getComponent(cc.ProgressBar);
        this.listenerMap = new Map();
        this.loadProgNode = cc.find("prog",this.loadNode);
        this.loadBarNode = cc.find("bar",this.loadNode);

        this.micropState = MicropState.Idle;
        this.changeState();

        this.micropManager = new MicropManager();
        this.recordData = new RecordData();
        this.micropCallBack = new MicropEngineCallBack();
        this.recordData.initData(data.properties);


        this.recordData.lastRecordTime = new Date().getTime();
        this.countDownNum = this.recordData.voiceEngineProp.answerDuration;

        this.idleBtn = this.idleNode.getComponents(cc.Button)[0];
        this.labaBtn = this.labaNode.getComponents(cc.Button)[0];
        

        this.showAuthority(false);
    
        // 显示单词音频
        this.wordAudioNodeView(true);
    }
    private playSoundAnimation() {
        this.soundView(true);
        this.schedule(this.animationLeftFunc, 0.05);
    }
    private stopSoundAnimation(){
        this.unschedule(this.animationLeftFunc);
    }
    private soundView(isVisible){
        this.soundByteNodeLeft.active = isVisible;
        this.soundByteNodeRight.active = isVisible;
    }
    private animationLeftFunc() {
        this.soundByteNodeRight.getComponent(cc.Sprite).spriteFrame = this.soundByteImages[this.soundIndex];
        this.soundByteNodeLeft.getComponent(cc.Sprite).spriteFrame = this.soundByteImages[this.soundIndex];
        this.soundIndex++;
        if(this.soundIndex > 33){
            this.soundIndex = 0;
        }
    }
    private hideAll(){
        this.idleNode.active = false;
        this.labaNode.active = false;
        this.micNode.active = false;
        this.loadNode.active = false;
        this.tipsWordNode.active = false;
        this.labaView.active = false;
        this.loadBarNode.getComponent(cc.Animation).stop();
        this.stopSoundAnimation();
    }
    private loadView(){
        this.loadNode.active = true;
        this.micNode.active = false;
        this.stopSoundAnimation();
        this.loadAnimation();
        this.tipsWordNode.active = true;
        this.tipsWordNode.getComponent(cc.Label).string = '录音生成中';
    }
    private loadAnimation(){
        let an = this.loadBarNode.getComponent(cc.Animation);
        let prog = this.loadProgNode.getComponent(cc.ProgressBar);
        an.on('finished',()=>{
            console.log('%c 🥤 ang: ', 'font-size:20px;background-color: #93C0A4;color:#fff;', prog.progress);
            an.getAnimationState('prog').delay = 0.2;
            an.play();
        });
        an.play();
    }

    public idleView(){
        this.idleNode.active = true;
        this.labaNode.active = true;
        this.idleNode.getComponent(cc.Button).interactable = false;
        this.labaNode.getComponent(cc.Button).interactable = false;
        
    }
    private readyView(){
        this.idleNode.active = true;
        this.labaNode.active = true;
        this.idleNode.getComponent(cc.Button).interactable = true;
        this.labaNode.getComponent(cc.Button).interactable = false;
    }
    private startView(){
        
        // 左边音频隐藏
        this.wordAudioNode.active = false;
        this.wordAudioView.active = false;

        this.countTimeProg.progress = 1;
        this.countDownNum = this.recordData.voiceEngineProp.answerDuration;
        this.micNode.active = true;
        this.playSoundAnimation(); 
    }

    private stopView(){
        this.idleNode.active = true;
        this.tipsWordNode.active = true;
        this.tipsWordNode.getComponent(cc.Label).string = '点击话筒，可重新录音';
    
        this.wordAudioNodeView(true);
        console.log("%c Line:218 🍌 this.micropManager.resultData", "color:#ffdd4d", this.micropManager.resultData);
        // 判断音频是否存在，
        if (this.micropManager.resultData && this.micropManager.resultData.online_audio_url) {
            this.labaNode.active = true;
            this.labaNode.getComponent(cc.Button).interactable = true;
            this.labaView.active = false;
            this.labaViewAnimation(false);
        } else {
            this.tipsWordNode.getComponent(cc.Label).string = '遇到点问题，请再试一次';
            this.labaNode.active = true;
            this.labaNode.getComponent(cc.Button).interactable = false;
            this.labaView.active = false;
        }
    }
    private onDisIdleBtn(){
        if(!this.idleBtn.interactable){
            this.showToast('目前不是答题期，请稍后再试');
        }
    }
    private onDisLalbBtn(){
        if(!this.labaBtn.interactable){
            this.showToast('暂无录音');
        }
    }
    private onIdleBtn(event?) {
        console.log("%c Line:238 🥝 event", "color:#93c0a4", event);
        this.wordAudioAuto = false;
        if (ComponentUtils.Instance().CheckDelay("button")) {
            return;
        }
        this.stopAudioLaba();
        this.stopWordAudio();

        console.log('%c 🍯 this.micropManager.isAuthorityIng: ', 'font-size:20px;background-color: #7F2B82;color:#fff;', this.micropManager.isAuthorityIng);
        // 增加 权限判断。
        if (this.micropManager.isAuthorityIng) {
            console.log("onClick this.micropManager.isAuthorityIng ", this.micropManager.isAuthorityIng);
            return;
        }
        console.log('%c 🍻 this.micropManager.isRecordAuthority: ', 'font-size:20px;background-color: #F5CE50;color:#fff;', this.micropManager.isRecordAuthority);
        if (!this.micropManager.isRecordAuthority) {
            console.log("onClick isRecordAuthority  ", this.micropManager.isRecordAuthority);
            this.micropManager.reqRecordAuthority(true);
            return;
        }
        this.micropManager.startRecord();
    }
    private onLabaBtn(){
        this.labaNode.active = false;
        this.labaView.active = true;
        this.playAudioLaba(this.micropManager.resultData.online_audio_url && this.micropManager.resultData.online_audio_url.split(".mp3")[0] + ".mp3");
    }
    public setLaBaData(url,score){
        console.log("%c Line:267 🥟 setLaBaData", "color:#b03734");
        this.micropManager.recordGroupData(url,score);
        this.changeState(MicropState.Stop);
    }

    private labaViewAnimation(isPlay: boolean) {
        if (cc.isValid(this.labaView,true)) {
            if (isPlay) {
                this.labaView.getComponent(cc.Animation).play();
            } else {
                this.labaView.getComponent(cc.Animation).stop();
            }
        }
    }


    private playAudioLaba(url){
        if(!this.audioIsPlay){
            this.audioIsPlay = true;
            console.log("playAudioLaba==url",url);
            this.getRemoteRes(url,cc.AudioClip,(err,audio)=>{
                this.labaViewAnimation(true);
                console.log("playAudioLaba down ",audio);
                this.audioId = this.playAudio(audio,false,()=>{
                    this.stopAudioLaba();
                    console.log("playAudioLaba end");
                });
                console.log("this.audioId===",this.audioId);
            });
        }else{
            this.stopAudioLaba();
        }
    }
    public stopAudioLaba(){
        if(this.audioIsPlay && this.audioId!=null){
            this.labaViewAnimation(false);
            this.stopAudio(this.audioId);
            this.audioId = null;
            this.audioIsPlay = false;
            this.labaNode.active = true
            this.labaView.active = false;
        }   
    }

    private manualStop() {
        const now = new Date().getTime();
        // toast提示 只有不是自动结束才触发
        if (now - this.recordData.lastRecordTime < RecordData.MIN_RECORD_TIME) {
            if (ComponentUtils.Instance().CheckDelay("toast")) {
                return;
            }
            return;
        }
        this.changeState(MicropState.Loading);
        this.micropManager.recording();
    }
    /**
     * @msg     : 播放文字音频
     * @return   {*}
     */    
    private playWordAudio(){
        this.stopWordAudio();
        this.playWordAnimation();
        this.getRemoteRes(this.recordData.voiceEngineProp.audioUrl,cc.AudioClip,(err,audio)=>{
            console.log("playWordAudio down ",audio);
            this.wordAudioId = this.playAudio(audio,false,()=>{
                this.stopWordAudio();
                console.log("playWordAudio end");
                
            });
            console.log("playWordAudio this.audioId===",this.audioId);
        });
    }
    private stopWordAudio() {
        // console.log("%c Line:340 🥔 this.wordAudioNode", "color:#2eafb0", this.wordAudioNode.active);
        // console.log("%c Line:341 🍣 this.wordAudioView.active", "color:#465975", this.wordAudioView.active);
        if (cc.isValid(this.wordAudioNode,true) && (this.wordAudioView.active || this.wordAudioNode.active)) {
            this.wordAudioNodeView(true);
            console.log("stopWordAudio  begin 1111");
            this.wordAudioView.getComponent(cc.Animation).stop();
            if (this.wordAudioId != null) {
                console.log("stopWordAudio  begin222");
                this.stopAudio(this.wordAudioId);
                this.wordAudioId = null;
                let _call = this.wordAudioAuto == true ? () => {
                    console.log("playWordAudio  跟读单词自动开");
                    if (this.wordAudioAuto == true) {
                        this.onIdleBtn();
                    }
                } : null;
                this.sendCallBack(EngineCallBack.WordAudioStop, _call);
            }
        }
    }
    private playWordAnimation(){
        this.wordAudioNodeView(false);
        this.wordAudioView.getComponent(cc.Animation).play();
    }
    private wordAudioNodeView(isView:boolean){
        // 判断音频是否存在，存在则为跟读单词
        if(this.recordData.voiceEngineProp.audioUrl){
            this.wordAudioNode.active = isView;
            this.wordAudioView.active = !isView;
        }
    }
    /**
     * @msg     : 播放文字音频
     * @return   {*}
     */    
    private onClickWordAudio(){
        this.wordAudioAuto = false;
        this.playWordAudio();
    }

    public changeState(state?:MicropState){
        if (cc.isValid(this.node, true)) {
            let _state = state ? state : this.micropState;
            this.micropState = _state;
            this.hideAll();
            console.log("_state", _state);
            switch (_state) {
                case MicropState.Idle:
                    this.idleView();
                    break;
                case MicropState.Ready:
                    this.readyView();
                    break;
                case MicropState.Start:
                    this.startView();
                    break;
                case MicropState.Loading:
                    this.loadView();
                    break;
                case MicropState.Stop:
                    this.stopView();
                    break;
            }
        }
        else{
            qte&&qte.logCatBoth("MicropComponent", "changeState 组件已被销毁");
        }

    }
    public countdown(dt){
        this.countDownNum -= dt;
        let progNum = this.countDownNum / this.recordData.voiceEngineProp.answerDuration;
        this.countTimeProg.progress = progNum;
    }
    public changeProperties(key: string, data: any) {
        
    }
    public changeSkine(path: string, param?: any) {
        
    }
    /**
  * 注册回调事件
  * @param callBack
  * @param cb
  */
    public addCallBackListener(option: { key: EngineCallBack; callBack: (data) => void }[]) {
        for (let temp of option) {
            this.listenerMap.set(temp.key, temp.callBack);
        }
    }

    /**
     * 触发sdk回调事件
     */
    public sendCallBack(callBack: EngineCallBack, data: any) {
        console.log("EngineCallBack==",EngineCallBack,data);
        if(cc.isValid(this,true)){
            let cb = this.listenerMap.get(callBack);
            if (cb) {
                cb(data);
            }
        }
    }
    protected onDestroy(): void {
        // 删除时，停止所有动画、定时器
        this.labaViewAnimation(false);
        this.stopSoundAnimation();
        this.stopAudioLaba();
        this.stopWordAudio()
        this.micropManager.destroy();
    }
}
