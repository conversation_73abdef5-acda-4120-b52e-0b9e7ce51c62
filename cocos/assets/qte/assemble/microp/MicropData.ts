/*
 * @FilePath     : /assets/qte/assemble/microp/MicropData.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */



export enum MicropState {
    Idle = "idle",      // 初始化
    Ready = "ready",    // 准备开始
    Start = "start",    // 开始录音
    Loading = "loading",    // 等待结果返回
    Stop = "stop"       // 结束录音
}

export enum EngineCallBack {
    // CreateReadyCb       = 1,    // 创建成功回调
    // RecordReadyCb       = 2,    // 准备完成回调
    StartRecordingCb = 3, // 开始录音回调
    // VolumeCb            = 4,    // 音量返回
    // RecordingResultCb   = 5,    // 测评中回调
    // ResultCb            = 6,    // 测评结果回调
    RecordSuccess = 6,
    // FailCb              = 7,    //
    // RecordFailCb        = 8,    // 录音失败回调
    // StopCb              = 9,    //
    // ReadGuideAudio      = 10,   // 阅读提干音频
    AnswerCorrectCb = 11, // 答题完成回调
    UploadStop          = 12,   // 开始停止录音
    // NoSpeakeCb          = 13,   // 无声状态
    WordAudioStop = 14,     // 跟读单词音频停止
    RecordError = 15, // 答题失败
    CheckAuthority = 16     // 检测麦克风是否有权限
}
export interface ResultData {
    status:number,
    wordList:[],
    score:number,
    audioUrl?:string,
    audioTime?:number,
}
