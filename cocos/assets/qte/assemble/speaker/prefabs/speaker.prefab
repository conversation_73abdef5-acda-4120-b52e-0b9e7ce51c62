[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "audio", "_objFlags": 512, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 258, "height": 88}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "b80153C1/ZC8bcppE3gHtVW", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "skFourFive": [{"__uuid__": "ddba134b-0f98-4d49-9f5b-521de85e8359"}, {"__uuid__": "1d9dfdda-58ae-4546-b4d3-ae6044c8514d"}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "649643b2-8efa-4dd4-ac29-bf394b992d10"}, "fileId": "", "sync": false}]