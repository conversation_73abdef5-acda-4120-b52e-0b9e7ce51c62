import BaseComponent from "../../core/BaseComponent";
import ComponentFactory from "../../core/ComponentFactory";
const { ccclass, property } = cc._decorator;
@ccclass
export class SpeakerComponent extends BaseComponent {
    /** 音频组件ID */
    private _nComId: string;
    /**
     * spine动画引用
     */
    private _spine: sp.Skeleton = null;
    /**
     * 音频
     */
    private _audio: cc.AudioClip;
    /** 
     * 当前播放音频ID
     */
    private _curAudioId: number = undefined;
    /**
     * 当前播放音频次数
     */
    private _curPlayCount: number;
    /**
     * 音频播放最大次数
     */
    private _maxPlayCount: number;

    /** 是否自动播放 */
    private _bAutoPlay: boolean = false;
    /**
     * 初始化
     * @param {data: {data: any, engine: anys}} 
     */

    private _speakerType: number = 0;

    private _haveCountdown: boolean = false;

    private _showPlayCountdown: boolean = true;

    private _maxDuration: number = 0;

    private durationLabel: cc.Label = null;

    private curDurationLabel: cc.Label = null;


    private parogress: cc.ProgressBar = null;
    private skinSp: cc.Sprite = null;

    private _notStopPlaying: boolean = false;

    private _autoRepeatCount: number = 1;

    private _isCanStarRepeat: boolean = false;

    private _playEnd: boolean = false;

    private _property: any = null;

    currPlayTime: number = 0;

    @property(cc.SpriteFrame)
    skFourFive: cc.SpriteFrame[] = [];

    private _getDuratinTime: number = 0;
    private _countdownSkin: number = 0;

    private _playingNode: cc.Node = null;
    private _countdownNode: cc.Node = null;
    private _countdownNodeParent: cc.Node = null;
    private _playReplateCountCallBack: Function = null;
    private _isNotTouch: boolean = false;

    public async initComponent(data?: any) {

        console.log("###---SPEAKER---initComponent-->", data);
        this._nComId = data.id;
        const property = data.properties;
        this._property = property;
        this._curAudioId = undefined;
        this._curPlayCount = 0;
        this._maxPlayCount = property.count;//Number.MAX_VALUE;
        this._haveCountdown = property.countdown || false;
        this._notStopPlaying = property.notStopPlaying || false;
        this._autoRepeatCount = property.autoRepeatCount || 1;
        this._isNotTouch = property.isNotTouch || false;
        if (this._maxPlayCount < 0) {
            this._maxPlayCount = Number.MAX_VALUE;
        }
        if (this._autoRepeatCount > this._maxPlayCount) {
            this._autoRepeatCount = this._maxPlayCount;
        }
        this._bAutoPlay = property.autoPlay;
        this._speakerType = property.speakerType;
        this._isCanStarRepeat = property.autoPlay;
        if (this._speakerType < 4) {
            this.node.width = 72;
            this.node.height = 72;
        } else { // 带进度条的宽不一样
            this.node.width = 258;
            this.node.height = 88;
        }

        let countdownSkin = 0;
        if (this._speakerType < 5) {
            if (typeof property.countdownSkin !== "undefined") {
                countdownSkin = property.countdownSkin;
            }
        } else {
            countdownSkin = 1;
            if (typeof property.countdownSkin !== "undefined") {
                countdownSkin = property.countdownSkin;
            }
        }
        this._countdownSkin = countdownSkin;

        let voice = await this.createVoice(ComponentFactory.getInstance().bundle, property.speakerType);
        this.node.addChild(voice);
        this._spine = null;
        if (this._speakerType < 4) {
            this._spine = voice.getComponent(sp.Skeleton);
        } else { // 4 小学带进度条, 5是初高带进度
            this.curDurationLabel = voice.getChildByName("time").getComponent(cc.Label);
            this.durationLabel = voice.getChildByName("maxTime").getComponent(cc.Label);
            this.parogress = voice.getChildByName("pbBar").getComponent(cc.ProgressBar);
            this.skinSp = voice.getChildByName("sp").getComponent(cc.Sprite);
            this._playingNode = voice.getChildByName("playing");
            this.updateUi();
        }

        if (property.audioUrl) {
            this._maxDuration = parseInt(property.duration + "") || 0;
            console.log("property.duration ", property.duration);
            await  this.loadRes(property.audioUrl);
        } else {
            console.log("检查:audioUrl", property.audioUrl);
            return;
        }
        if (this._speakerType < 4) {
            this.node.on(cc.Node.EventType.TOUCH_START, this.clickSpeaker, this);
        } else {
            if (cc.isValid(this.parogress.node,true)) {
                this.parogress.node.on(cc.Node.EventType.TOUCH_START, this.clickSpeaker, this);
            }
        }
    };

    async loadRes(url: string): Promise<void> {
        if (!url) {
            return;
        }
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.AudioClip, (err, res: cc.AudioClip) => {
                if (err) {
                    console.log("检查:audioUrl", url);
                    reject();
                    return;
                }
            
                console.log("audioUrl检查组件状态:", res);
                if (!cc.isValid(this, true)) {
                    return;
                }
                this._audio = res;
                this.updateUi();
                resolve();
            });
        }).catch(err => {
            qte && qte.logCatBoth('h5Label Load  cocos promise error:', err);
            return null;
        });
    }



    public getDurationTime(audioId: any) {
        if (!cc.isValid(this, true)) {
            return;
        }
        this._getDuratinTime++;
        let durationTime = cc.audioEngine.getDuration(audioId);
        this._maxDuration = parseInt(durationTime + "") || 0;
        console.log("audioId", audioId, "@@@@@@@@@@@@@@@@@@@@@@@--durationTime", durationTime);
        if (this._getDuratinTime < 20 && this._maxDuration == 0) {
            this.scheduleOnce(() => {
                this.getDurationTime(audioId);
            }, 0.1);
        } else {
            cc.audioEngine.stop(audioId);
            this.updateUi();
        }
    }
    /**
     * 属性更新
     * @param key 属性key
     * @param data 参数 
     */
    public async changeProperties(key: string, data: any) {
        console.log("###---SPEAKER---changeProperties---key-->", key, "--data-->", data);
        if (key == "audioUrl") {
            this._maxDuration = 0;
            this._audio = null;
            this.updateUi();
            if (data) {
                this.getRemoteRes(data, cc.AudioClip, (err, res: cc.AudioClip) => {
                    if (err) {
                        console.error(err);
                        return;
                    }
                    this._audio = res;
                    this._maxDuration = parseInt(this._audio.duration + "");
                    this.updateUi();
                });
            } else {
                console.log("检查:audioUrl", data);
            }
        } else if (key == "speakerType") {
            this.durationLabel = null;
            this.curDurationLabel = null;
            this.parogress = null;
            this.skinSp = null;
            this._speakerType = data;
            this.node.removeAllChildren();
            if (this._speakerType < 4) {
                this.node.width = 72;
                this.node.height = 72;
                this.onSizeChange(this.node.getContentSize());
            } else { // 带进度条的宽不一样
                this.node.width = 258;
                this.node.height = 88;
                this.onSizeChange(this.node.getContentSize());
            }

            let voice = await this.createVoice(ComponentFactory.getInstance().bundle, data);
            this.node.addChild(voice);
            this._spine = null;
            if (this._speakerType < 4) {
                this._spine = voice.getComponent(sp.Skeleton);
            } else { // 4 小学带进度条, 5是初高带进度
                this.curDurationLabel = voice.getChildByName("time").getComponent(cc.Label);
                this.durationLabel = voice.getChildByName("maxTime").getComponent(cc.Label);
                this.parogress = voice.getChildByName("pbBar").getComponent(cc.ProgressBar);
                this.skinSp = voice.getChildByName("sp").getComponent(cc.Sprite);
                this.updateUi();

            }
        };
    }

    /**
     * @deprecated 弃用
     * 更换skin
     * @param path 资源路径 
     */
    public changeSkine(path: string, param?: any) {

    };

    public onDestroy() {
        this.node.off(cc.Node.EventType.TOUCH_START, this.playVoice, this);
        this.unscheduleAllCallbacks();
        if (this._curAudioId != undefined) {
            this.stopAudio(this._curAudioId);
            this._curAudioId = undefined;
        }
    }

    /** 重置音频组件 */
    public resetVoice() {
        if (this._countdownNode) {
            this._countdownNode.destroy();
            this._countdownNode = null;
        }
        this.unscheduleAllCallbacks();
        this.stopVoice();
        this.updateUi();
        this._curPlayCount = 0;
        this._isCanStarRepeat = this._property.autoPlay;
        this._showPlayCountdown = true;
    }

    public setPlayReplateCountCallBack(cb: Function) {
        this._playReplateCountCallBack = cb;
    }
    /** 自动播放 */
    public autoPlayVoice() {
        console.log("进入autoPlayVoice", this._audio)
        let callBack = () => {
            if (!cc.isValid(this, true)) {
                return;
            }
            this._countdownNode = null;
            if (!this._audio) {
                this.scheduleOnce(() => {
                    callBack();
                }, 0.5);
                return;
            }
            if (!this._bAutoPlay) {
                return;
            }
            this.playVoice();
        }
        if (this._haveCountdown && this._showPlayCountdown) {
            this._showPlayCountdown = false;
            this.loadCountdown(callBack);
        } else {
            callBack();
        }
    }
    loadCountdown(callBack: Function) {
        let path = `assemble/countdown/prefabs/countdown`;
        console.log("########===path=====>", path);
        let data = {
            properties: {
                title: "音频马上开始播放哦",
                style: this._countdownSkin,
                time: 3,
                callBack: callBack
            }
        };
        if (!cc.isValid(this, true)) {
            return;
        }
        let bundle = cc.assetManager.getBundle('qte');
        if (!bundle) {
            this.scheduleOnce(() => {
                this.loadCountdown(callBack);
            }, 0.4);
            return;
        }
        bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
            if (err) {
                console.error(err);
                return;
            }
            let node = cc.instantiate(assets);
            this._countdownNode = node;
            node.x = 0;
            node.y = 0;
            let parentNode = this.node.parent.parent;
            if (this._countdownNodeParent) {
                parentNode = this._countdownNodeParent;
            } else if (parentNode.parent) {
                parentNode = parentNode.parent;
            }
            parentNode.addChild(node);
            node.zIndex = cc.macro.MAX_ZINDEX - 1;
            node.getComponent(BaseComponent).initComponent(data);
        });

    }
    public setCountParent(node: cc.Node) {
        this._countdownNodeParent = node;
    }
    /** 播放音频 */
    public playVoice() {
        if (this._playEnd) {
            console.log("不允许再触发播放了")
            return;
        }
        console.log("#####------SpeakerComponent---playVoice--->1,--->", Date.now())
        if (this._curPlayCount >= this._maxPlayCount) {
            this.showToast('播放次数已经用尽啦~');
            this.stopVoice();
            return;
        }
        this._curPlayCount++;
        if (this._curPlayCount >= this._autoRepeatCount) {
            this._isCanStarRepeat = false;
        }

        console.log("#####------SpeakerComponent---playVoice--->2,--->", Date.now())
        this._curAudioId = this.playAudio(this._audio, false, () => {
            console.log("#####------SpeakerComponent---playVoice--->end,--->", Date.now())
            this.stopVoice();
            if (this._isCanStarRepeat) {
                this.scheduleOnce(() => {
                    this.autoPlayVoice();
                }, 0.05);

            } else {
                // TODO:题板被删除时需要删除注册的所有回调
                if (this._playReplateCountCallBack&&cc.isValid(this.node, true)) {
                    this._playReplateCountCallBack();
                    this._playReplateCountCallBack = null;
                }

            }
        });
        if (this._spine) {
            this._spine.animation = 'start';
            this._spine.loop = true;
            this._spine.timeScale = 1;
            this._spine.setCompleteListener((trackEntry, loopCount) => {
                this._spine.animation = 'start';
            });
        }
        // this.unschedule(this.updateUi);
        // this.schedule(this.updateUi, 0.5);
        this.unschedule(this.updateUiManger);
        this.currPlayTime = 0;
        this.schedule(this.updateUiManger, 0.1);

    }
    updateUiManger(dt: number) {
        this.currPlayTime += dt;
        if (this.currPlayTime > this._maxDuration) {
            this.currPlayTime = this._maxDuration;
        }
        this.updateUi();

    }
    updateUi() {
        let muit1 = parseInt(this._maxDuration / 60 + "") + "";
        let sec1 = parseInt(this._maxDuration % 60 + "") + "";
        if (muit1.length == 1) {
            muit1 = "0" + muit1;
        }
        if (sec1.length == 1) {
            sec1 = "0" + sec1;
        }
        if (this._curAudioId != undefined) {
            // let time = parseInt(this.currPlayTime+"");
            let muit0 = parseInt(this.currPlayTime / 60 + "") + "";
            let sec0 = parseInt(this.currPlayTime % 60 + "") + "";
            if (muit0.length == 1) {
                muit0 = "0" + muit0;
            }
            if (sec0.length == 1) {
                sec0 = "0" + sec0;
            }

            if (this.durationLabel) {
                this.durationLabel.string = muit1 + ":" + sec1;
            }
            if (this.curDurationLabel) {
                this.curDurationLabel.string = muit0 + ":" + sec0;
            }
            if (this.parogress) {
                this.parogress.progress = Number(parseFloat(this.currPlayTime / this._maxDuration + "").toFixed(2));
            }

            if (this.skinSp && this.skinSp['skinId'] != 1) {
                if (this._notStopPlaying) {
                    this._playingNode.active = true;
                    this.skinSp.node.active = false;
                } else {
                    this._playingNode.active = false;
                    this.skinSp.node.active = true;
                }
                this.skinSp['skinId'] = 1
                //this.skinSp  设置皮肤
                this.skinSp.spriteFrame = this.skFourFive[1];
            }

        } else {
            this.currPlayTime = 0;
            if (this.durationLabel) {
                this.durationLabel.string = muit1 + ":" + sec1;
            }
            if (this.curDurationLabel) {
                this.curDurationLabel.string = "00:00";
            }
            if (this.parogress) {
                this.parogress.progress = 0;
            }
            if (this.skinSp && this.skinSp['skinId'] != 0) {
                this.skinSp['skinId'] = 0;
                //this.skinSp  设置皮肤
                this.skinSp.spriteFrame = this.skFourFive[0];
                this.skinSp.node.active = true;
                if (this._notStopPlaying) {
                    this._playingNode.active = false;
                }
            }
            // this.unschedule(this.updateUi);
            this.unschedule(this.updateUiManger);

        }

    }

    /** 停止音频 */
    public stopVoice() {
        console.log("调用了stopVoice")
        if (this._curAudioId != undefined) {
            this.stopAudio(this._curAudioId);
            this._curAudioId = undefined;
        }
        if (cc.isValid(this.node, true)) {
            if (this._spine) {
                this._spine.setCompleteListener(null);
                this._spine.animation = 'end';
            }
            if (this._countdownNode) {
                this._countdownNode.destroy();
                this._countdownNode = null;
            }

        }

    }
    /**不允许再播放了 */
    public stopPlayEnd() {
        this._playEnd = true;
        this.stopVoice()
    }
    // 点击喇叭
    private clickSpeaker() {
        qte && qte.logCatBoth('SpeakerComponent', 'clickSpeaker: this._isNotTouch '+this._isNotTouch+' this._curAudioId '+this._curAudioId);
        if (this._isNotTouch) {
            return;
        }
        if (!this._audio) {
            return;
        }
        this.playSpeaker();
    }

    // 处理喇叭点击事件
    private playSpeaker() {
        if (this._notStopPlaying && this._curAudioId != undefined) {
            return;
        }

        if (this._isCanStarRepeat) {
            this._isCanStarRepeat = false;
        }

        if (this._curAudioId == undefined) {
            this._curAudioId = undefined;
            this.playVoice();
        } else {
            this.stopVoice();
        }
        return;
    }

    /**
     * 创建spine动画
     * @param bundle 
     * @param typeName 
     * @returns 
     */
    public createVoice(bundle: cc.AssetManager.Bundle, typeName: string): Promise<cc.Node> {
        return new Promise<cc.Node>((reslove) => {
            let path = `assemble/speaker/prefabs/voice_${typeName}`;
            console.log("########===path=====>", path);
            bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
                if (err) {
                    console.error(err);
                    return;
                }
                let node = cc.instantiate(assets);
                reslove(node);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }

}