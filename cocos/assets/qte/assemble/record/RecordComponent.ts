/* eslint-disable max-lines */

import ComponentUtils from "../componentUtils/ComponentUtils";
import BaseComponent from "../core/BaseComponent";
import { RecordType } from "../core/ComponentData";
import { EngineCallBack } from "../microp/MicropData";
import RecordData, {  RecordState, SubjectEnum, SubjectType, WordType } from "./RecordData";
import RecordEngineCallBack from "./RecordEngineCallBack";
import RecordManager from "./RecordManager";

/**
 * 录音组件
 *
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class RecordComponent extends BaseComponent {
  /**  */
  private recordManager: RecordManager;
  /** callback */
  public recordCallBack: RecordEngineCallBack;
  /** 数据 */
  private _recordData: RecordData;

  public get recordData(): RecordData {
    return this._recordData;
  }

  // 录音状态
  protected _status: RecordState;
  get status(): RecordState {
    return this._status;
  }

  set status(v: RecordState) {
    this._status = v;
  }

  // 科目
  private _subject: SubjectEnum;
  public get subject(): SubjectEnum {
    return this._subject;
  }
  public set subject(value) {
    this._subject = value;
  }

  // 题目类型
  public get subjectType(): SubjectType {
    return this._recordData.subjectType;
  }
  public set subjectType(val: SubjectType) {
    this._recordData.subjectType = val;
  }

  /** 事件回调 */
  private listenerMap: Map<number, (data) => void>;

  // 是否初始化过
  private _isInit = false;
  public get isInt(): boolean {
    return this._isInit;
  }
  // 是否开始过
  private isStart = false;

  // 是否结束过。
  private isStop = false;

  public get isPlaying(): boolean {
    return ComponentUtils.Instance().isPlaying;
  }
  // 初始scale
  private onLoadStatus = {
    scale: 1,
    opacity: 255,
  };

  private countDown: cc.Node = null;
  private recordIng: cc.Node = null;
  private score: cc.Node = null;
  private countProgress: cc.ProgressBar = null;
  private scoreLab: cc.Label = null;
  // 倒计时计树
  public countDownNum: number = 15;

  private microphoneBack: cc.Node = null;
  private readyLab: cc.Node = null;
  private microphoneRes: cc.Node = null;
  private soundWaveSpines: sp.Skeleton[] = [];
  private readyTishi: cc.Node = null;

  //是否跳过准备阶段
  private isSkipReady = false;

  //星星spine节点
  private starSpineNode: cc.Node = null;
  //星星音效
  private starAudioList: cc.AudioClip[] = [];
  //设置是否可以跳过准备阶段
  public setIsSkipReady(bool:boolean)
  {
      this.isSkipReady = bool;
  }
  //设置回调是否结束，多次调用组件时使用
  public setIsFinish(bool:boolean)
  {
    this.recordManager.setIsFinish(bool)
  }

  onLoad() {
    this.countDown = this.node.getChildByName("daojishi");
    this.countDown.on("click", this.onClick, this);

    this.countProgress = this.countDown.getChildByName("progress").getComponent(cc.ProgressBar);

    // idle
    this.microphoneBack = this.countDown.getChildByName("back");
    this.microphoneRes = this.countDown.getChildByName("res");
    this.readyLab = this.countDown.getChildByName("idleLab");
    this.readyTishi = this.countDown.getChildByName("tishi");
    this.readyTishi.active = false;

    this.soundWaveSpines.push(this.countDown.getChildByName("spine1").getComponent(sp.Skeleton));
    this.soundWaveSpines.push(this.countDown.getChildByName("spine2").getComponent(sp.Skeleton));

    this.recordIng = this.node.getChildByName("pBack");
    this.score = this.node.getChildByName("score");
    this.scoreLab = this.score.getChildByName("lab").getComponent(cc.Label);
  }
  /**
   * @msg     : 设置 spine 动画
   * @param    {RecordState} status
   * @return   {*}
   */

  setSoundWaveSpines(status: RecordState) {
    let name = null;
    if (status === RecordState.Idle) {
      name = null;
    } else {
      name = "animation";
    }
    for (let i = 0; i < this.soundWaveSpines.length; i++) {
      let spine = this.soundWaveSpines[i];
      this.soundWaveSpines[i].animation = name;
      this.soundWaveSpines[i].loop = true;
      this.soundWaveSpines[i].timeScale = 1;
      this.soundWaveSpines[i].node.active = true;
      if (status === RecordState.Loading) {
        this.soundWaveSpines[i].node.active = false;
      }
      spine.setCompleteListener((trackEntry, loopCount) => {
        spine.animation = name;
      });
    }
  }
  /**
   * @msg     : idle 显示。
   * @param    {*}
   * @return   {*}
   */

  idleShow() {
    if(!this.node.active){
      return;
    }
    this.readyLab.active = false;
    this.microphoneBack.active = true;
    this.score.active = false;
    this.countProgress.progress = 1;
    // this.micBack.color = cc.color().fromHEX('#ABAAF1');
    this.microphoneRes.active = true;
    this.countProgress.node.active = false;
    this.readyTishi.active = false;
    this.setSoundWaveSpines(RecordState.Idle);
  }
  /**
   * @msg     : 准备阶段显示
   * @param    {*}
   * @return   {*}
   */

  readyShow() {
    if(!this.isSkipReady)
    {
      this.readyLab.active = true;
      this.microphoneRes.active = false;
      this.readyTishi.active = true;
      // 开启个倒计时。
      let num = 2;
      this.schedule(
        () => {
          if(!cc.isValid(this.node,true))
            return;
          // console.log('%c 🥟 num: ', 'font-size:20px;background-color: #93C0A4;color:#fff;', num);
          this.readyLab.getComponent(cc.Label).string = num + "";
          num--;
          if (num < 0) {
            this.readyTishi.active = false;
            // console.log('%c 🍔 num1111=: ', 'font-size:20px;background-color: #33A5FF;color:#fff;', num);
            this.recordManager.startRecord();
          }
        },
        1,
        2,
      );
    }
    else
    {
      this.recordManager.startRecord();
    }
  }

  setInitNode() {
    this.countDown.active = true;
    this.recordIng.active = false;
    this.score.active = false;
    this.countProgress.progress = 1;
    this.countDownNum = this._recordData.voiceEngineProp.answerDuration;
    this.readyLab.getComponent(cc.Label).string = "3";
    this.isStart = false;
    this.isStop = false;
    this.recordCallBack.reset();
    this.recordManager.reset();
  }

  /** 初始化组件 */
  public async  initComponent(data: any) {
    // 还原初始状态

    cc.log("initComponent", data);
    this.listenerMap = new Map();
    // this._historyStatus = [];
    this.recordManager = new RecordManager();
    this._recordData = new RecordData();
    this._recordData.initData(data.properties);
    this.recordCallBack = new RecordEngineCallBack();

    this.status = RecordState.Idle;
    this.recordData.lastRecordTime = new Date().getTime();
    this.countDownNum = this._recordData.voiceEngineProp.answerDuration;
    this.recordCallBack.regiestComponent(this);

    this.node.scaleX = data.properties.width / this.node.width;
    this.node.scaleY = data.properties.height / this.node.height;
    //使用星星特效
    if (this._recordData.voiceEngineProp.isUseStarSpine && typeof this._recordData.voiceEngineProp.isUseStarSpine == "boolean")
      await this.loadStarRes();

  }
  templateInit(engine) {
    this.setInitNode();
    this.recordManager.initEngine(this.recordCallBack, engine, this);
  }
  reset() {
    this.status = RecordState.Idle;
    this.recordData.lastRecordTime = new Date().getTime();
    this.changeState(this.status);
    this.unscheduleAllCallbacks();
  }
  onClick() {
    if(this.node.parent.opacity == 0) return;
    // 防止连续点击
    if (ComponentUtils.Instance().CheckDelay("button")) {
      console.log("RecordComponent onClick CheckDelay button ");
      return;
    }
    qte.logCatBoth('recordComp','click');

    // 增加 权限判断。
    if (this.recordManager.isAuthorityIng) {
      console.log("onClick this.recordManager.isAuthorityIng ", this.recordManager.isAuthorityIng);
      qte.logCatBoth('recordComp',`this.recordManager.isAuthorityIng ${this.recordManager.isAuthorityIng}`);
      return;
    }
    if (!this.recordManager.isRecordAuthority) {
      console.log("onClick isRecordAuthority  ", this.recordManager.isRecordAuthority);
      this.recordManager.reqRecordAuthority(true);
      qte.logCatBoth('recordComp',`this.recordManager.isRecordAuthority ${this.recordManager.isRecordAuthority}`);
      return;
    }
    qte.logCatBoth('recordComp this.status',this.status);
    // 判断开始还是结束
    if (this.status === RecordState.Idle) {
      this.startRecord();
    } else {
      this.stopRecord();
    }
  }
  /** 开始录音 */
  public startRecord() {
    qte.logCatBoth('recordComp this.isStart',this.isStart);
    if (this.isStart) {
      return;
    }
    qte.logCatBoth('recordComp ',`${this.recordData.voiceEngineProp.autoBegin} ${this.status}`,);
    // 自动时，不能点击开始。
    if (this.recordData.voiceEngineProp.autoBegin && this.status === RecordState.Idle) {
      return;
    }

    if (this.status !== RecordState.Idle) {
      return;
    }
    this.changeState(RecordState.Ready);
  }

  /** 停止录音 */

  public stopRecord(reason: number = 1) {
    if (this.status !== RecordState.Recording) {
      return;
    }

    const now = new Date().getTime();
    // toast提示 只有不是自动结束才触发
    if (now - this.recordData.lastRecordTime < RecordData.MIN_RECORD_TIME && reason !== 2) {
      if (ComponentUtils.Instance().CheckDelay("toast")) {
        return;
      }
      // this.showToast("答题时间太短啦，再多答一会儿吧~", 1000);
      // console.log("录音太短啦！", 1000);
      return;
    }
    // 停止过了
    if (this.isStop) {
      return;
    }
    // this.isStart = false;
    this.isStop = true;
    this.recordManager.recording();
  }

  /**
   * 注册回调事件
   * @param callBack
   * @param cb
   */
  public addCallBackListener(option: { key: EngineCallBack; callBack: (data) => void }[]) {
    for (let temp of option) {
      this.listenerMap.set(temp.key, temp.callBack);
    }
  }

  /**
   * 触发sdk回调事件
   */
  public sendCallBack(callBack: EngineCallBack, data: any) {
    let cb = this.listenerMap.get(callBack);
    if (cb) {
      cb(data);
    }
  }

  /** 更换资源 */
  public changeSkine(path: string, style: RecordType) {
    // -- TODO
    // this.voiceStyle = style;
  }

  changeState(recordState: RecordState, num?) {
    this.status = recordState;
    switch (recordState) {
      case RecordState.Idle:
        this.idleShow();
        break;
      case RecordState.Ready:
        this.readyShow();
        break;
      case RecordState.Loading:
        this.scoreLoading();
        break;
      case RecordState.Recording:
        this.recordingShow();
        break;
      case RecordState.Stop:
          this.stopShow(num);
        break;
    }
  }
  recordingShow() {
    this.microphoneRes.active = true;
    this.isStart = true;
    this.readyLab.active = false;
    this.microphoneBack.active = true;
    this.countProgress.node.active = true;
    this.setSoundWaveSpines(RecordState.Recording);
  }

  scoreLoading() {
    this.setSoundWaveSpines(RecordState.Loading);
    this.recordIng.active = true;
    let loadingSp = this.recordIng.getChildByName("spine").getComponent(sp.Skeleton);
    loadingSp.animation = "animation";
    loadingSp.loop = true;
    loadingSp.timeScale = 1;
    loadingSp.setCompleteListener(() => {
      loadingSp.animation = "animation";
    });
  }
  stopShow(num) {
    if(!cc.isValid(this.node,true))
      return;
    this.score.active = true;
    this.scoreLab.string = num;
    if (this._recordData.voiceEngineProp.isUseStarSpine && typeof this._recordData.voiceEngineProp.isUseStarSpine == "boolean") {
      let index = this.getStarIndex(Number(num));
      let starAudioId = this.playAudio(this.starAudioList[index], false);
      this.starSpineNode.active = true;
      let animation = this._recordData.voiceEngineProp.scoreLevels[index].animation
      let starSpine = this.starSpineNode.getComponent(sp.Skeleton);
      starSpine.setAnimation(0, animation, false);
      starSpine.setCompleteListener(() => {
          this.stopAudio(starAudioId);
      });
  }
  }
  countdown(dt) {
    this.countDownNum -= dt;
    let progNum = this.countDownNum / this.recordData.voiceEngineProp.answerDuration;
    this.countProgress.progress = progNum;
  }

  /** 重置所有动画 */
  resetAction() {
    this.changeState(RecordState.Idle);
  }

  private isNetRes(path: string): boolean {
    if (path.startsWith("http://") || path.startsWith("https://") || path.startsWith("zybhost://")) {
      return true;
    }
    return false;
  }

  changeProperties() {}
  getStarIndex(score) {
    let index = null;
    for (let i = 0; i < this._recordData.voiceEngineProp.scoreLevels.length; i++) {
        if(i == 0)
        {
            if (score <= this._recordData.voiceEngineProp.scoreLevels[i].score)
                index = 0;
        }
        else{
            if (score <= this._recordData.voiceEngineProp.scoreLevels[i].score && score > this._recordData.voiceEngineProp.scoreLevels[i - 1].score)
                index = i;
        }
        if (index !== null)
            break; 
    }
    if(index === null)
        index = this._recordData.voiceEngineProp.scoreLevels.length - 1;
    return index;
}
  async loadStarRes() {
    let skeleton = await this.loadSpine(this._recordData.voiceEngineProp.starSpine);
    this.starSpineNode = new cc.Node();
    let starSpine = this.starSpineNode.addComponent(sp.Skeleton);
    starSpine.skeletonData = skeleton;
    starSpine.timeScale = 1;
    this.starSpineNode.parent = this.score;
    this.starSpineNode.active = false;
    for (let i = 0; i < this.recordData.voiceEngineProp.scoreLevels.length; i++) {
        if (this.recordData.voiceEngineProp.scoreLevels[i].audio) {
            let audio: cc.AudioClip = await this.loadRes(this.recordData.voiceEngineProp.scoreLevels[i].audio);
            this.starAudioList[i] = audio;
        }
    }
}
   /**
    * 加载spine资源
    * @param component spine数据atlas、images、skeleton、cover、动作
    * @returns
    */
   public async loadSpine(component: any): Promise<sp.SkeletonData> {
    return new Promise<sp.SkeletonData>(async (resolve, reject) => {
        let texture = [];
        let textureName = [];
        for (const iterator of component.images) {
            let asset: any = await this.loadRes(iterator)
            if (asset instanceof cc.Texture2D) {
                asset.setPremultiplyAlpha(true)
            }
            texture.push(asset);
            textureName.push(iterator.substring(iterator.lastIndexOf("/") + 1));
        }

        let atlasText: any = await this.loadRes(component.atlas);
        let skeletonJson: any = await this.loadRes(component.skeleton);

        let asset = new sp.SkeletonData();
        asset.skeletonJson = (skeletonJson as cc.JsonAsset).json;
        asset.atlasText = (atlasText as cc.TextAsset).text;
        asset.textures = texture;
        // @ts-ignore
        asset.textureNames = textureName;
        // @ts-ignore
        resolve(asset);
    }).catch(err => {
        qte && qte.logCatBoth('cocos promise error:', err);
        return null;
    });
}
/**
* 加载远程资源
* @param url 资源路径
* @returns
*/
public loadRes(url): Promise<any> {
    return new Promise<any>((resolve, reject) => {
        this.getRemoteRes(url, null, (error, res: cc.Asset) => {
            if (error) {
                return;
            }
            resolve(res);
        });
    }).catch(err => {
        qte && qte.logCatBoth('cocos promise error:', err);
        return null;
    });
}
onDestroy() {
  if (this.status == RecordState.Recording) {
    if (!this.isStop) {
      this.isStop = true;
      this.recordManager &&this.recordManager.stopRecord();
    }
  }
}

}
