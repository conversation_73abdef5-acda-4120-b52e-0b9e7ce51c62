import { QTE_LOGCAT_TYPE } from "../../qte-core-export";
import CryptoJS = require("./crypto-js/indexCrypto");

/** 科目 */
export enum SubjectEnum {
    English = "1",
}

export interface ResultData {
    status:number,
    wordList:[],
    score:number,
}

/** 题目类型 */
export enum SubjectType {
    Default = -1,
    FollowAudioQuestions = 1, // 跟读题
    FollowWordQuestions = 2,    // 跟读单题
    // OralAudioQuestions    = 2,             // 口述题
    // MaskAudioQuestions  // 蒙层语音
}



/** 评测类型 */
export enum WordType {
    null = 0,
    word = 1, // 单词
    sentence = 2, // 句子
    paragraph = 3, // 段落
    question = 4, // 问题回答
    repeat = 5, // 复述
    symbols = 6, // 音标
}
/** 录音sdk错误码 */
export enum RecordErrorCode {
    // 录音
    ERROR_VOICE_CREATE_ALREADY = 1000, // 重复初始化
    ERROR_VOICE_CREATE_PARAMS = 1001, // 参数错误
    ERROR_VOICE_NO_PERMISSION = 1002, // 未开启录音权限
    ERROR_VOICE_ENGINE_NO_READY = 1003, // SDK未准备好或引擎对象为null
    ERROR_VOICE_CREATE_RECORD_FILE = 1004, // 创建录音文件失败错误码
    ERROR_VOICE_READY_RECORD_PARAMS = 1005, // 准备录音参数错误
    ERROR_VOICE_INIT_AUDIO_RECORDER = 1006, // 准备录音是AudioRecorder错误
    ERROR_VOICE_STAR_RECORD = 1007, //
    ERROR_VOICE_ENCODE = 1008,
    ERROR_VOICE_RECORDING_OTHER = 1009,
    ERROR_VOICE_DUPLICATE_START = 1010,
    ERROR_VOICE_DUPLICATE_READY = 1011,
    // 评测
    ERROR_CODE_PARAM_ERROR = 2001,
    ERROR_CODE_CREATEENGINE_FAILED = 2002,
    STATUS_WS_CONNECT_ERROR = 2003,
    STATUS_WS_CONNECT_CLOSED = 2004,
    ERROR_CS_EVALUATE_CODE = 2005,
}


export enum RecordState {
    Idle = "idle",
    Ready = 'ready',
    Recording = "recording",
    Loading = "loading",
    Stop = "stop",
    Error = "error",
}

export interface ResultStruct {
    accurate_score: number;
    audio_url: string;
}

interface ERecordParams {
    ak: any; // 加密内容
    st: number; // 评分类型 1-单词 2-句子 3-段落 4-问题回答 5-复述 6-音标
    rt: string; // 传入的内容
    kp?:[];     // 评测关键字
    // syllable?: number; // 1-获取重读,音节对错
}

export class VoiceEngineProp {
    // 评测类型：1 - 单词；2 - 句子； 3 - 段落； 4 - 问题回答； 5 - 复述;  6 - 音标
    wordType: WordType;
    evaluatingText: string; // 评测内容
    // scoringMode: boolean; // 评分方式且或
    answerDuration: number = 15; // 答题时长
    // 正确率
    accuracy:string; 
    autoBegin: boolean; // 0 手动; 1 自动
    overTimeNum: number;
    overScore:Array<number>;        // 保底分数
    isStart?:boolean;                   // 是否开始
    answerkeyword?:[];              // 评测关键字
    randomNum?:number;              // 随机分数，算法返回的分数低于此分数时，取兜底评分的分数作为学生分数
    audioUrl?:string;                  // 跟读单题，播放的音频
    isUsePhonetics?:boolean;        // 是否使用音标
    evaluatePhonetics?:string;    // 音标评测内容
    isUseStarSpine?:boolean;      // 是否使用星星特效
    starSpine?:{};                  // 星星特效参数
    scoreLevels?:Array<{score:number,animation:string,audio:string}>;     // 分数播放队列配置 score animation audio
}
/**
 * 存放所有录音sdk数据
 */
export default class RecordData {
    // 最短录音时间 毫秒
    public static readonly MIN_RECORD_TIME = 2000;
    // 题目类型
    public subjectType = SubjectType.Default;
    public curIndex = -1;

    public tryTimes = 2;

    public lastRecordTime = 0;

    // public isRepeate = false; // 是否重读过
    public voiceEngineProp: VoiceEngineProp;
    // 调用 端的语音 action 需要传入。
    public reqRecordData: ERecordParams = {
        ak: "",
        rt: "",
        st: 1,
    };
    constructor() {}

    public initData(data) {

        this.voiceEngineProp = new VoiceEngineProp();
        for (let key in data) {
            this.voiceEngineProp[key] = data[key];
        }
        //判断是否开启音标判定，如果开启则将音标内容添加到评测文本中
        if(this.voiceEngineProp.isUsePhonetics && typeof this.voiceEngineProp.isUsePhonetics == "boolean")
        {
            const regex = /^\/.*\/$/;
            const regexfirst = /^\//;
            const regexLast = /\/$/;
            // 检查 evaluatePhonetics 是否包含 g 和 ǀ,如果有则替换为 ɡ 和 l
            const regexG = /g/;
            const regexL = /ǀ/;
            
            if(regexG.test(this.voiceEngineProp.evaluatePhonetics)){
                this.voiceEngineProp.evaluatePhonetics = this.voiceEngineProp.evaluatePhonetics.replace(regexG, "ɡ");
            }
            if(regexL.test(this.voiceEngineProp.evaluatePhonetics)){
                this.voiceEngineProp.evaluatePhonetics = this.voiceEngineProp.evaluatePhonetics.replace(regexL, "l");
            }
            if (CC_DEBUG) {
                // this.voiceEngineProp.evaluatePhonetics
                // 循环字符串，输出 unicode 编码    
                for (let i = 0; i < this.voiceEngineProp.evaluatePhonetics.length; i++) {
                    // g：103 - > 609;  l： 448 - > 108;
                    console.log("this.voiceEngineProp.evaluatePhonetics[i] code=", this.voiceEngineProp.evaluatePhonetics[i].charCodeAt(0));
                }
            }
            
            if(regex.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"("+this.voiceEngineProp.evaluatePhonetics+")";
            else if(regexfirst.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"("+this.voiceEngineProp.evaluatePhonetics+"/)";
            else if(regexLast.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"(/"+this.voiceEngineProp.evaluatePhonetics+")";
            else
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"(/"+this.voiceEngineProp.evaluatePhonetics+"/)";
        }
        
        this.reqRecordData.st = this.voiceEngineProp.wordType;
        this.reqRecordData.ak = this.handleAesEecryption(
            this.voiceEngineProp.evaluatingText
        );
        if(this.voiceEngineProp.evaluatingText.indexOf('[') != -1){
            this.voiceEngineProp.evaluatingText = JSON.parse(this.voiceEngineProp.evaluatingText);
        };
        this.reqRecordData.rt = this.voiceEngineProp.evaluatingText;
        if(this.voiceEngineProp.answerkeyword && this.voiceEngineProp.answerkeyword.length > 0){
            this.reqRecordData.kp = this.voiceEngineProp.answerkeyword;
        }
    }
    
    private handleAesEecryption(text: string) {
        const key = CryptoJS.enc.Latin1.parse("zyb.2015ZYB.2018");
        const iv = CryptoJS.enc.Latin1.parse("zyb.2015ZYB.2018");
        const serverTime = new Date().getTime();
        const pid = parseInt(serverTime / 1000 + "", 10);
        const contentText = text + "@" + pid;
        const encoded = CryptoJS.AES.encrypt(contentText, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            adding: CryptoJS.pad.ZeroPadding,
        }).toString();
        cc.log("handleAesEecryption", text, encoded);
        return encoded;
    }

    /**
     * 计算语音评分的兜底策略函数。
     * 当评分低于或等于20时，此函数将生成一个介于最小值60和最大值80之间的随机评分。
     * @param score - 原始语音评分
     * @returns 返回调整后的评分
     */
    static fallbackScoreStrategy(score: number) {
        // 修改语音评分 2024 年 06 月 13 日 11:42:43
        // score = this.handleScore(score);
        // result.score = score;
        // 2024-07-18 15:38:30 还原评分策略
        // 2024-07-18 15:38:30 修改兜底评分 90 - 80
        // * FIX: 2024-07-18 15:38:30 修复兜底评分策略  60 - 70
        const maxScore = 70;
        const minScore = 60;
        
        if (score <= 20) {
            let _d = parseInt(Math.random() * (maxScore - minScore + 1) + minScore + "", 10);
            qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `跟读题 走兜底评分策略 ${_d}`);
            return _d;
        }else {
            return score;
        }
    }

    /**
     * 计算超时评估策略的评分。
     * 最终设定的分数范围为60到80。
     * @returns 超时后的随机评分
     */
    static timeoutEvaluationStrategy() {
        // 修改语音评分 2024 年 06 月 13 日 11:42:43 
        // 当超时，随机分数在 40 - 60 之间
        // let score = Math.floor(Math.random() * (overScore[1] - overScore[0]) + overScore[0]);
        // let score = Math.floor(Math.random() * (60 - 40) + 40);
        // FIX: 修改超时评分 2024-07-11 18:31:54 范围为70 - 90
        // FIX: 修改超时评分 2024-11.6，  范围 60 - 80
        // * FIX: 修改超时评分 2024-11.10，  范围 60 - 70
        const maxScore = 70;
        const minScore = 60;
        let score = Math.floor(Math.random() * (maxScore - minScore) + minScore);
        qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `跟读题 走超时评分策略 ${score}`);
        return score;

    }
}
