/*
 * @FilePath     : /sdk/assets/qte/assemble/record/RecordEngineCallBack.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */
import { EngineCallBack } from "../microp/MicropData";
import RecordComponent from "./RecordComponent";
import RecordData, { RecordState, ResultData } from "./RecordData";

export default class RecordEngineCallBack {
  private recorDCmpt: RecordComponent;
  // 是否在后台
  private isGamePause = false;
  private stopRecordEndCb = null;

  // 是否二次重读过
  // private isReStart = false;

  // 处于后台保存事件处理
  private gamePaseCb: {
    parm: any;
    cb: (data) => void;
  } = null;

  public reset() {
    this.stopRecordEndCb = null;
    this.isGamePause = false;
  }

  public regiestComponent(cmpt: RecordComponent) {
    this.recorDCmpt = cmpt;
    // this.registerEvent();
  }

  public startReadyEndCb() {
    this.recorDCmpt.sendCallBack(EngineCallBack.StartRecordingCb, null);
  }

  // #region  回调事件处理
  /**
   * 开始录音的回调
   * @param v
   */
  public startRecordingCb(data: any) {
    console.log("startRecordingCb data", data);

    if (this.isGamePause) {
      this.gamePaseCb = {
        parm: data,
        cb: this.startRecordingCb.bind(this),
      };
      return;
    }
    // 判断是语音结果
    let result: ResultData = JSON.parse(data.result.hypotheses[0].transcript);



    let score = Math.floor(result.score);

    // qte.adapter.toast("跟读题真时返回分数:" + result.score);

    // 修改语音评分 2024 年 06 月 13 日 11:42:43
    // score = this.handleScore(score);
    // result.score = score;
    // 2024-07-18 15:38:30 还原评分策略
    // 2024-07-18 15:38:30 修改兜底评分 90 - 80
    score = RecordData.fallbackScoreStrategy(score);
    result.score = score;
    this.recorDCmpt.changeState(RecordState.Stop, score);

    if (result.status === 0) {
      // 成功
      if (score >= parseInt(this.recorDCmpt.recordData.voiceEngineProp.accuracy)) {
        this.recorDCmpt.sendCallBack(EngineCallBack.RecordSuccess, result);
      } else {
        this.recorDCmpt.sendCallBack(EngineCallBack.RecordError, result);
      }
    } else {
      // 失败
      this.recorDCmpt.sendCallBack(EngineCallBack.RecordError, result);
    }
  }

  // /**
  //  *  结束录音的回调
  //  * @param score  分数
  //  * @returns 
  //  */
  // public handleScore(score) {
  //   // 如果小于 20，则在分数基础上 * 1.5
  //   if (score < 20) {
  //     score = Math.floor(score * 1.5);
  //   } else if (score >= 20 && score < 60) {
  //     // 如果分数在 20 - 60 之间，则在 70 80 之间随机
  //     score = parseInt(Math.random() * (80 - 70 + 1) + 70 + "", 10);
  //   } else if (score >= 60) {
  //     // 如果分数超过 60，则按照返回分数
  //     score = Math.floor(score);
  //   }
  //   return score;
  // }


  public stopRecordCb(cb?) {
    // this.stopRecordEndCb = cb;
  }

  destory() {
    // this.unregisterEvent();
  }

  // registerEvent () {
  //     // this.recorDCmpt.cocosBridge.on('innerPause', this.onGamePause, this);
  //     cc.game.on(cc.game.EVENT_HIDE, this.onGamePause,this);
  //     cc.game.on(cc.game.EVENT_SHOW, this.onGameResume,this);
  //     // this.recorDCmpt.cocosBridge.on('innerResume', this.onGameResume, this);
  // }

  // unregisterEvent () {
  //     cc.game.off(cc.game.EVENT_HIDE, this.onGamePause,this);
  //     cc.game.off(cc.game.EVENT_SHOW, this.onGameResume,this);
  //     // this.recorDCmpt.cocosBridge.off('innerPause', this.onGamePause, this);
  //     // this.recorDCmpt.cocosBridge.off('innerResume', this.onGameResume, this);
  // }

  public onGamePause() {
    this.isGamePause = true;
  }

  public onGameResume() {
    this.isGamePause = false;
    if (this.gamePaseCb) {
      this.gamePaseCb.cb(this.gamePaseCb.parm);
      this.gamePaseCb = null;
    }
  }
}
