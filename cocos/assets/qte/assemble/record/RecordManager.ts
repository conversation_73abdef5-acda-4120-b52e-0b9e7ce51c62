/*
 * @FilePath     : /sdk/assets/qte/assemble/record/RecordManager.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import { EngineCallBack } from "../microp/MicropData";
import RecordComponent from "./RecordComponent";
import RecordData, { RecordState, ResultData } from "./RecordData";
import RecordEngine from "./RecordEngine";
import RecordEngineCallBack from "./RecordEngineCallBack";

export default class RecordManager {
  /** sdk */
  private recordEngine: RecordEngine;
  // 回调监听
  private recordCallBack: RecordEngineCallBack;

  private recordCmpt: RecordComponent;

  private removeCb: any = null;

  private updateTimeFunc = null;

  private overTimeFunc = null;

  private isOverStop = false;

  private isFinish = false;

  private _isRecordAuthority = false; // 请求权限结果。
  public get isRecordAuthority() {
    return this._isRecordAuthority;
  }
  public set isRecordAuthority(value) {
    this._isRecordAuthority = value;
  }

  private _isAuthorityIng = false; // 是否在请求权限中。
  public get isAuthorityIng() {
    return this._isAuthorityIng;
  }
  public set isAuthorityIng(value) {
    this._isAuthorityIng = value;
  }

  // 初始化RecordManager
  public initEngine(engineCB: RecordEngineCallBack, engine: RecordEngine, target: RecordComponent) {
    this.isRecordAuthority = false;
    this.isAuthorityIng = false;
    console.log("engine==", engine);
    if (!engine || CC_PREVIEW) {
      this.recordEngine = new RecordEngine();
    } else {
      this.recordEngine = engine;
    }
    this.recordCallBack = engineCB;
    this.recordCmpt = target;

    if (this.recordCmpt.recordData.voiceEngineProp.autoBegin) {
      this.reqRecordAuthority(false);
    }
  }
  /**
   * @msg     : 请求语音权限
   * @param    {*}
   * @return   {*}
   */

  reqRecordAuthority(isTouch?) {
    this.isAuthorityIng = true;
    console.log("reqRecordAuthority====1111");
    this.recordEngine.checkRecordAuthorityAction(isAuthority => {
      console.log("isAuthority: ", isAuthority);
      this.isRecordAuthority = isAuthority.status;
      this.isAuthorityIng = false;
      if (this.isRecordAuthority) {
        // 判断是否自动开始。
        this.autoBegin(isTouch);
      }
    });
  }
  autoBegin(isTouch?) {
    if ((this.recordCmpt?.recordData?.voiceEngineProp?.autoBegin && this.isRecordAuthority) || (this.isRecordAuthority && isTouch)) {
      this.recordCmpt.changeState(RecordState.Ready);
    }
  }
  public reset() {
    this.isOverStop = false;
    this.updateTimeFunc = null;
    this.overTimeFunc = null;
  }

  /**
   * 开始录音
   * 说明： 调用 stopRecord后会 返回resultCb
   * @param parm
   */
  public startRecord() {
    if (!cc.isValid(this.recordCmpt.node, true)) {
      qte && qte.logCatBoth("recordCmpt", " startRecord,节点已销毁音频回调无法继续执行了");
      return;
    }
    this.recordCmpt.changeState(RecordState.Recording);
    this.updateTimeFunc = this.update.bind(this);
    this.recordCmpt.schedule(this.updateTimeFunc);
    console.log("startRecord=init=");
    this.recordEngine.startRecordAction(
      this.recordCmpt.recordData.reqRecordData,
      res => {
        if (!cc.isValid(this.recordCmpt.node, true)) {
          qte && qte.logCatBoth("recordCmpt", "startRecord节点已销毁,识别回调无法继续执行了");
          return;
        }
        console.log("res: ", res);
        if (res && res.status === 0) {
          let is_finish = res.result.final;
          console.log("is_finish: ", is_finish);
          if (!this.isOverStop && is_finish) {
            this.isFinish = is_finish;
            if (this.overTimeFunc) {
              this.recordCmpt.unschedule(this.overTimeFunc);
            }
            this.recordCallBack.startRecordingCb(res);
          }
        } else {
          console.log("返回参数 error");
        }
      },
      (removeCb?) => {
        console.log("removeCb this.isFinish: ", this.isFinish);
        if (this.isFinish) {
          removeCb && delete window[removeCb];
        }
      },
      () => {
        this.recordCallBack.startReadyEndCb();
      },
    );
  }
  update(dt) {
    if (this.recordCmpt.countDownNum >= 0) {
      this.recordCmpt.countdown(dt);
    } else {
      // 倒计时结束，执行评分。
      if (!this.isOverStop) {
        this.recording();
      }
    }
  }
  /**
   * @msg     : 计算分数
   * @param    {*}
   * @return   {*}
   */
  recording() {
    this.recordCmpt.unschedule(this.updateTimeFunc);
    this.recordCmpt.changeState(RecordState.Loading);
    this.overTimeFunc = this.overTimeHandle.bind(this);
    this.recordCmpt.sendCallBack(EngineCallBack.AnswerCorrectCb, "");
    // this.recordCmpt.schedule(this.overTimeFunc, this.recordCmpt.recordData.voiceEngineProp.overTimeNum,0);
    this.recordCmpt.schedule(this.overTimeFunc, 4, 0);
    this.stopRecord();
    // this.recordCmpt.unschedule(this.overTimeFunc);
    // 开始超时计时。
  }

  // 超时，直接处理
  overTimeHandle() {
    console.log("overTimeHandle: 超时，直接处理");
    if (this.overTimeFunc) {
      this.recordCmpt.unschedule(this.overTimeFunc);
    }

    //  1.随机出分数
    // 2.改变状态、显示分数、
    // 3，结束
    this.isOverStop = true;
    this.overTimeFunc = null;
    let overScore = this.recordCmpt.recordData.voiceEngineProp.overScore;
    // 修改语音评分 2024 年 06 月 13 日 11:42:43 
    // 当超时，随机分数在 40 - 60 之间
    // let score = Math.floor(Math.random() * (overScore[1] - overScore[0]) + overScore[0]);
    // let score = Math.floor(Math.random() * (60 - 40) + 40);
    // FIX: 修改超时评分 2024-07-11 18:31:54 范围为70 - 90
    // FIX: 修改超时评分 2024-11.6，  范围 60 - 80
    let score = RecordData.timeoutEvaluationStrategy();
    this.endShow(score);
  }
  endShow(score) {
    this.recordCmpt.changeState(RecordState.Stop, score);
    let result: ResultData = {
      score: score,
      status: score >= parseInt(this.recordCmpt.recordData.voiceEngineProp.accuracy) ? 0 : 1, // TODO: 增加判断
      wordList: [],
    };
    if (result.status === 0) {
      this.recordCmpt.sendCallBack(EngineCallBack.RecordSuccess, result);
    } else {
      this.recordCmpt.sendCallBack(EngineCallBack.RecordError, result);
    }
    // 分
  }

  /** 停止录音 */
  stopRecord() {
    this.recordEngine.stopRecordAction();
  }
  //多次调用组件时，需要重置isFinish为false
  setIsFinish(isFinish) {
    this.isFinish = isFinish;
  }
}
