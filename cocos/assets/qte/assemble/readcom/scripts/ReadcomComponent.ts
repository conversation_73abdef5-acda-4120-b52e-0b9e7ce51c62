import BaseComponent from "../../core/BaseComponent";


const { ccclass, property } = cc._decorator;
enum ModeType {
    anwer = "anwer",        // 显示作答详情，包含正确答案和用户作答，学生用
    look = "look",
    showCorrect = "showCorrect",    // 只显示正确答案，老师用
    showUserAnswer = "showUserAnswer"    // 只显示用户作答，老师用

}

@ccclass
export default class ReadcomComponent extends BaseComponent {
    _questionNode: cc.Node = null;

    _answerNode: cc.Node = null;

    _setStateFunc: Function;
    private _currQuestionIndex: number = 0;
    public questionData: any = [];

    @property(cc.Prefab)
    answerPrefab: cc.Prefab = null;

    _anwerList = [];
    _anwerPicMaxWidth: number = 438;
    _nextPosAnwer: cc.Vec2 = cc.v2(24, -24);
    _optionItem: cc.Node[] = [];

    @property(cc.SpriteFrame)
    topBtn: cc.SpriteFrame[] = [];

    @property(cc.Node)
    previousBtn: cc.Node = null;

    @property(cc.Label)
    nextLabel: cc.Label = null;

    @property(cc.Prefab)
    questionNum: cc.Prefab = null;
    @property(cc.Node)
    topItemNode: cc.Node = null;
    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.SpriteFrame)
    optionBtnSp: cc.SpriteFrame[] = [];
    _modeType: ModeType = ModeType.anwer;

    @property(cc.Prefab)
    itemBigPrefab: cc.Prefab = null;
    @property(cc.SpriteFrame)
    itemBigSp: cc.SpriteFrame[] = [];
    @property(cc.Node)
    itemBigNode: cc.Node = null;
    _itemBigNode: cc.Node[] = [];
    _questionPicArray: cc.Node[] = [];
    _isUpdateUi: boolean = false;
    @property(cc.ScrollView)
    qScrollView: cc.ScrollView = null;
    @property(cc.ScrollView)
    aScrollView: cc.ScrollView = null;
    @property(cc.Node)
    qViewNode: cc.Node = null;
    @property(cc.Node)
    aViewNode: cc.Node = null;
    @property(cc.Prefab)
    contentPrefab: cc.Prefab = null;
    _topItemNodeList: cc.Node[] = [];
    @property(cc.Node)
    resultNode: cc.Node = null;
    @property(cc.Node)
    questionRootNode: cc.Node = null;
    _questionCorrectList: any[] = [];
    @property(cc.SpriteFrame)
    optionABSp: cc.SpriteFrame[] = [];
    _time: number = 0;
    _needloadTime: number = 0;
    @property(cc.Node)
    toastNode: cc.Node = null;
    _optionQuestionPicArray: cc.Node[] = [];
    _maxQuesctionW = 678;
    _maxOptionQuesctionW = 438;
    _maxOptionPic = 372 + 34;
    _anwerTime: string = "";
    resMap: any = {};
    answerStatus = [];
    public changeProperties(key: string, data: any) {

    }
    public changeSkine(path: string, param?: any) {
    }
    start() {

    }
    updateUI() {
        if (this._modeType === ModeType.anwer) {
            this.timeLabel.node.parent.active = false;
        } else if (this._modeType == ModeType.look) {
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = this._anwerTime;
        } else if (this._modeType == ModeType.showUserAnswer) {
            this.timeLabel.node.parent.active = false;
        }
        else {
            this.timeLabel.node.parent.active = false;
        }

        if (this._currQuestionIndex == 0) {
            this.previousBtn.active = false;
        } else {
            this.previousBtn.active = true;
        }
        if (this._currQuestionIndex == this.questionData['optionsList'].length - 1) {
            if (this._modeType === ModeType.look) {
                this.nextLabel.node.parent.active = true;
                this.nextLabel.string = "查看结果";
            } else if (this._modeType == ModeType.anwer) {
                this.nextLabel.node.parent.active = true;
                this.nextLabel.string = "提交";
            }
            else {
                this.nextLabel.node.parent.active = false;
            }
        } else {
            this.nextLabel.node.parent.active = true;
            this.nextLabel.string = "下一题";
        }

        for (let i = 0; i < this._topItemNodeList.length; i++) {
            let label = cc.find("num", this._topItemNodeList[i]);

            if (this._modeType === ModeType.anwer) {
                if (this._currQuestionIndex == i) {
                    label.getComponent(cc.Label).fontSize = 30;
                    if (this._anwerList[i] != -1) {
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[3];
                        label.color = new cc.Color().fromHEX("#FFFFFF");
                    } else {
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[1];
                        label.color = new cc.Color().fromHEX("#141414");
                    }

                } else {
                    label.getComponent(cc.Label).fontSize = 22;
                    if (this._anwerList[i] != -1) {
                        label.color = new cc.Color().fromHEX("#FFFFFF");
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[2];
                    } else {
                        label.color = new cc.Color().fromHEX("#898989");
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[0];
                    }
                }
            }
            else if (this._modeType === ModeType.look) {
                label.color = new cc.Color().fromHEX("#FFFFFF");
                if (this._currQuestionIndex == i) {
                    label.getComponent(cc.Label).fontSize = 30;
                    if (this._anwerList[i] == this._questionCorrectList[i]) { //选中答对了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[3];

                    } else {  //选中中答错了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[5];
                    }

                } else {
                    label.getComponent(cc.Label).fontSize = 22;
                    if (this._anwerList[i] == this._questionCorrectList[i]) { //没选中答对了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[2];
                    } else { //没选中答错了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[4];
                    }
                }

            }
            else if (this._modeType === ModeType.showUserAnswer) {
                label.color = new cc.Color().fromHEX("#FFFFFF");
                if (this._currQuestionIndex == i) {
                    label.getComponent(cc.Label).fontSize = 30;
                    if (this._anwerList[i] == this._questionCorrectList[i]) { //选中答对了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[3];

                    } else {  //选中中答错了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[5];
                    }

                } else {
                    label.getComponent(cc.Label).fontSize = 22;
                    if (this._anwerList[i] == this._questionCorrectList[i]) { //没选中答对了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[2];
                    } else { //没选中答错了
                        this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[4];
                    }
                }
            }
            else {
                label.color = new cc.Color().fromHEX("#FFFFFF");
                if (this._currQuestionIndex == i) {
                    label.getComponent(cc.Label).fontSize = 30;
                    this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[3];
                } else {
                    label.getComponent(cc.Label).fontSize = 22;
                    this._topItemNodeList[i].getComponent(cc.Sprite).spriteFrame = this.topBtn[2];
                }
            }

        }

    }
    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
        this._setStateFunc("setAnswerBase", null);
    }



    public reset() {
        if (!this._isUpdateUi) {
            this._currQuestionIndex = 0;
            this._anwerList = [];
            for (let i = 0; i < this.questionData['optionsList'].length; i++) {
                this._anwerList.push(-1);
            }
            this._modeType = ModeType.anwer;
            this.nextRender();
        }
        else {
            this.scheduleOnce(() => {
                this.reset();
            }, 0.5);
        }

    }
    public getAnswerTime(): number {
        return this._time;
    }
    public getAnwerIsCorrect(): boolean {
        let isCorrect = true;
        for (let i = 0; i < this._anwerList.length; i++) {
            if (this._anwerList[i] != this._questionCorrectList[i]) {
                isCorrect = false;
                break;
            }

        }
        return isCorrect;
    }

    public getChooseAnswer(): any {
        let list = [];
        for (let i = 0; i < this._anwerList.length; i++) {
            let value = 2;
            if (this._anwerList[i] == this._questionCorrectList[i] && this._questionCorrectList[i] != -1) {
                value = 1;
            }
            list.push(value);
        }
        console.log("被动提交了");
        if (this._modeType != ModeType.look) {
            this._modeType = ModeType.look;
            this._time = parseInt((new Date().getTime() - this._time) / 1000 + "");
            let muit = parseInt(this._time / 60 + "") + "";
            let sec = parseInt(this._time % 60 + "") + "";
            if (muit.length == 1) {
                muit = "0" + muit;
            }
            if (sec.length == 1) {
                sec = "0" + sec;
            }
            this._anwerTime = muit + ":" + sec
            this.timeLabel.string = this._anwerTime;
            this.updateResult();
        }
        return list;
    }
    public getCorrectAnswer() {
        return JSON.parse(JSON.stringify(this._questionCorrectList));
    }
    public async initComponent(data?: any) {
        this.questionData = data.properties.question;
        this._time = new Date().getTime();
        this._isUpdateUi = true;
        for (let i = 0; i < this.questionData['optionsList'].length; i++) {
            let options = this.questionData['optionsList'][i].options;
            for (let j = 0; j < options.length; j++) {
                if (options[j].isCorrect) {
                    this._questionCorrectList[i] = j;
                    break;
                }
            }
            if (typeof this._questionCorrectList[i] == "undefined") {
                this._questionCorrectList[i] = -1;
                console.log("第" + i + "题没找到正确答案")
            }
            let node4 = cc.instantiate(this.contentPrefab);
            node4.name = "content" + i;
            node4.parent = this.aViewNode;

            let node = cc.instantiate(this.questionNum);
            this._topItemNodeList.push(node);
            node.parent = this.topItemNode;
            cc.find("num", node).getComponent(cc.Label).string = "" + (i + 1);
            let butC = node.getComponent(cc.Button);
            const clickEventHandler = new ReadcomComponent.EventHandler();
            clickEventHandler.target = this.node; // 这个 node 节点是你的事件处理代码组件所属的节点
            clickEventHandler.component = 'ReadcomComponent';// 这个是脚本类名
            clickEventHandler.handler = 'topButton';
            clickEventHandler.customEventData = i + "";
            butC.clickEvents.push(clickEventHandler);
            this._anwerList.push(-1);

            let node1 = cc.instantiate(this.itemBigPrefab);
            node1.parent = this.itemBigNode;
            cc.find("num", node1).getComponent(cc.Label).string = "" + (i + 1);
            this._itemBigNode.push(node1);
        }
        for (let i = 0; i < this.questionData.questionList.length; i++) {
            let url = this.questionData.questionList[i];
            await this.loadPicUpdate(url, null, 0);
        }

        let optionsList = this.questionData['optionsList'];
        for (let j = 0; j < optionsList.length; j++) {
            let optionsQuestion = optionsList[j].optionsQuestion;
            for (let h = 0; h < optionsQuestion.length; h++) {
                await this.loadPicUpdate(optionsQuestion[h], null, 0);
            }

            let options = optionsList[j].options;
            for (let k = 0; k < options.length; k++) {
                let picList = options[k].picList;
                for (let l = 0; l < picList.length; l++) {
                    await this.loadPicUpdate(picList[l], null, 0);
                }
            }
        }

        this._questionNode = cc.find("content", this.qViewNode);
        this.loadTextureArray(this.questionData.questionList, this._questionNode, cc.v2(24, 0), 0);
        this.nextRender();
    }

    loadPicUpdate(url, sp: cc.Sprite | any, type): Promise<any> {
        return new Promise<void>((resolve, reject) => {
            this.getRemoteRes(url, cc.Texture2D, (error, texture: cc.Texture2D) => {
                if (error) {
                    return;
                }
                if (texture instanceof cc.Texture2D) {
                    texture.setPremultiplyAlpha(true);
                    texture.packable = false;
                }
                let spr = new cc.SpriteFrame(texture);
                if (type == 1) {
                    sp.spriteFrame = spr;
                } else {
                    this.resMap[url] = spr;
                }
                resolve(sp);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }


    nextRender() {
        if (this.questionData['optionsList'].length <= 0) {
            return;
        }
        this._isUpdateUi = true;
        let pos = cc.v3(0, 0, 0);
        for (let i = 0; i < this.questionData['optionsList'].length; i++) {
            let aNode = cc.find("content" + i, this.aViewNode);
            aNode.position = pos;
            if (i == this._currQuestionIndex) {
                this._answerNode = aNode;
                this._answerNode.active = true;
                this.aScrollView.content = this._answerNode;
            } else {
                aNode.active = false;
            }
        }
        for (let i = 0; i < this._optionItem.length; i++) {
            this._optionItem[i].destroy();
        }
        for (let i = 0; i < this._optionQuestionPicArray.length; i++) {
            this._optionQuestionPicArray[i].destroy();
        }
        this._optionQuestionPicArray = [];
        this._optionItem = [];
        this._answerNode.y = 0;
        this._nextPosAnwer = cc.v2(24, -24);
        this._needloadTime = 1;
        if (this.questionData['optionsList'][this._currQuestionIndex].optionsQuestion.length > 0) {
            this.loadTextureArray(this.questionData['optionsList'][this._currQuestionIndex].optionsQuestion, this._answerNode, cc.v2(24, -24), 0, "optionsQuestion");

        } else { //没有回答区答干
            this.loadOptionsArray(this.questionData['optionsList'][this._currQuestionIndex].options, this._answerNode, 0, "choose");
        }

        this.updateUI();
    }

    updateOptinUI() {
        for (let i = 0; i < this._optionItem.length; i++) {
            if (this._modeType === ModeType.anwer) {
                if (i == this._anwerList[this._currQuestionIndex]) {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[1];
                } else {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[0];
                }
            }
            if (this._modeType === ModeType.look) {
                cc.find("noAnswer", this._optionItem[i]).active = false;
                if (i == this._questionCorrectList[this._currQuestionIndex]) {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[2];
                    if (this._anwerList[this._currQuestionIndex] == -1) {
                        cc.find("noAnswer", this._optionItem[i]).active = true;
                    }
                }
                else if (i == this._anwerList[this._currQuestionIndex] && this._anwerList[this._currQuestionIndex] != this._questionCorrectList[this._currQuestionIndex]) {

                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[3];
                } else {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[0];
                }
            }
            if (this._modeType === ModeType.showCorrect) {
                cc.find("noAnswer", this._optionItem[i]).active = false;
                if (i == this._questionCorrectList[this._currQuestionIndex]) {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[2];
                }
            }
            if (this._modeType === ModeType.showUserAnswer) {
                cc.find("noAnswer", this._optionItem[i]).active = false;
                let answerIndex = this.answerStatus[this._currQuestionIndex].content - 1;
                if (answerIndex == -1) {
                    cc.find("noAnswer", this._optionItem[i]).active = true;
                }

                if (i == answerIndex) {
                    if (answerIndex != this._questionCorrectList[this._currQuestionIndex]) {
                        this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[3];
                    }
                    else {
                        this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[2];
                    }
                }
                else {
                    this._optionItem[i].getComponent(cc.Sprite).spriteFrame = this.optionBtnSp[0];
                }

            }
        }
    }
    loadFinish() {
        this._needloadTime++;
        if (this._needloadTime >= 2) {
            this._isUpdateUi = false;
        }

    }


    private loadOptionsArray(array: [], parent: cc.Node, optionInex: number, type: string) {
        if (!cc.isValid(this.node, true) || !array[optionInex]) {
            return;
        }
        let callBack = (pos1: cc.Vec2, picParent: cc.Node) => {

            let itemH = Math.abs(pos1.y) + 24;
            picParent.height = itemH;
            let mangerNode = picParent.parent;
            mangerNode.height = itemH;
            let maxW = 0;
            for (let child of picParent.children) {
                if (maxW < child.width) {
                    maxW = child.width;
                }
            }
            maxW += 34;
            let scale = Number(parseFloat(this._maxOptionPic / maxW + "").toFixed(2));
            if (maxW > this._maxOptionPic) {
                mangerNode.scale = scale;
            }
            let h1 = mangerNode.height * mangerNode.scale;
            let chooseItem = mangerNode.parent;
            chooseItem.height = h1;
            chooseItem.width = this._anwerPicMaxWidth;
            this._optionItem.push(chooseItem);
            if (type == "choose") {
                this.addButton(chooseItem, optionInex);
            }
            if (this.optionABSp[optionInex]) {
                let aNode = cc.find("mangerNode/A", chooseItem);
                aNode.getComponent(cc.Sprite).spriteFrame = this.optionABSp[optionInex];
                aNode.y = -(itemH / 2) + aNode.height / 2;
            }
            optionInex++;
            this._nextPosAnwer.y = - (h1 + Math.abs(this._nextPosAnwer.y) + 12)
            if (array[optionInex]) {
                let _answerNode = cc.instantiate(this.answerPrefab);
                _answerNode.setPosition(this._nextPosAnwer);
                _answerNode.parent = parent;
                let picParent = cc.find("mangerNode/itemPic", _answerNode);
                // this._anwerPicMaxWidth = 0;
                this.loadOptionItem(array[optionInex]['picList'], picParent, cc.v2(0, 0), 0, type, optionInex, callBack)
            } else {
                let h2 = Math.abs(this._nextPosAnwer.y);
                h2 += 80;
                if (h2 > parent.parent.height) {
                    parent.height = h2;
                } else {
                    parent.height = parent.parent.height;
                }
                this.updateOptinUI();
                this.loadFinish();
            }
        }
        let _answerNode = cc.instantiate(this.answerPrefab);
        _answerNode.setPosition(this._nextPosAnwer);
        _answerNode.parent = parent;
        let picParent = cc.find("mangerNode/itemPic", _answerNode);
        this.loadOptionItem(array[optionInex]['picList'], picParent, cc.v2(0, 0), 0, type, optionInex, callBack)

    }

    private loadOptionItem(array: [], parent: cc.Node, pos: cc.Vec2, index: number, type: string, optionInex: number, callBack: Function) {
        if (!cc.isValid(this.node, true) || !array[index]) {
            return;
        }
        if (this.resMap[array[index]]) {
            let node = new cc.Node();
            let spr = node.addComponent(cc.Sprite)
            spr.spriteFrame = this.resMap[array[index]];
            spr.trim = false;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.parent = parent;
            node.anchorX = 0;
            node.anchorY = 1;
            node.x = 0;
            node.y = pos.y;
            pos.x = 0;
            pos.y = pos.y - node.height;
            if (index < array.length - 1) {
                index++;
                this.loadOptionItem(array, parent, pos, index, type, optionInex, callBack);
            } else {
                if (callBack) {
                    callBack(pos, parent);
                }
            }
        }
    }



    private loadTextureArray(array: [], parent: cc.Node, pos: cc.Vec2, index: number, type?: string) {
        if (!cc.isValid(this.node, true) || !array[index]) {
            return;
        }
        if (this.resMap[array[index]]) {
            let node = new cc.Node();
            node.anchorX = 0;
            node.anchorY = 1;
            let spr = node.addComponent(cc.Sprite)
            spr.spriteFrame = this.resMap[array[index]];
            spr.trim = false;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.parent = parent;
            node.x = 24;
            node.y = pos.y;
            pos.y = pos.y - node.height;

            if (type == "optionsQuestion") {
                this._nextPosAnwer.y = pos.y;
                this._optionQuestionPicArray.push(node);
            }
            else {
                this._questionPicArray.push(node);
            }
            if (index < array.length - 1) {
                index++;
                this.loadTextureArray(array, parent, pos, index, type);
            } else {
                let h1 = Math.abs(pos.y);
                if (h1 < parent.parent.height) {
                    parent.height = parent.parent.height;
                } else {
                    parent.height = h1;
                }
                if (type != "optionsQuestion") {
                    // questionList
                    this.setScaleFormPicList(this._questionPicArray, "question");
                } else {
                    this._nextPosAnwer.y -= 18;
                    // 加一个间距
                    this.setScaleFormPicList(this._optionQuestionPicArray, "optionsQuestion");
                    this.loadOptionsArray(this.questionData['optionsList'][this._currQuestionIndex].options, this._answerNode, 0, "choose");
                }
            }

        }


    }
    setScaleFormPicList(nodeList: cc.Node[], type?: string) {
        let maxW = 0;
        for (let i = 0; i < nodeList.length; i++) {
            if (maxW < nodeList[i].width) {
                maxW = nodeList[i].width;
            }

        }
        switch (type) {
            case "question": // 适配答题区
                {
                    let scale = Number(parseFloat(this._maxQuesctionW / maxW + "").toFixed(2));
                    if (maxW > this._maxQuesctionW) {
                        let culH = 0;
                        for (let i = 0; i < nodeList.length; i++) {
                            nodeList[i].scale = scale;
                            nodeList[i].y = culH;
                            culH -= (scale * nodeList[i].height)
                        }
                        let h1 = Math.abs(culH);
                        if (h1 < this.qViewNode.height) {
                            this._questionNode.height = this.qViewNode.height;
                        } else {
                            this._questionNode.height = h1;
                        }

                    }

                }
                break;
            case "optionsQuestion":
                {
                    let scale = Number(parseFloat(this._anwerPicMaxWidth / maxW + "").toFixed(2));
                    if (maxW > this._anwerPicMaxWidth) {

                        let culH = -24;
                        for (let i = 0; i < nodeList.length; i++) {
                            nodeList[i].scale = scale;
                            nodeList[i].y = culH;
                            culH -= (scale * nodeList[i].height)
                        }
                        this._nextPosAnwer.y = culH - 8;
                    }
                }
                break;
            default:
                break;
        }




    }
    setHistoryData() {
        let tempData = {
            currQuestionIndex: this._currQuestionIndex,
            anwerList: this._anwerList,
            modeType: this._modeType,
            anwerTime: this._anwerTime
        }
        this._setStateFunc("redacomData", tempData);
    }
    public recoverData(tempData: any): void {
        if (this._currQuestionIndex == tempData.currQuestionIndex && this._modeType == tempData.modeType) {
            return;
        }

        if (!this._isUpdateUi) {
            this._currQuestionIndex = tempData.currQuestionIndex;
            this._anwerList = tempData.anwerList;
            this._modeType = tempData.modeType;
            this._anwerTime = tempData.anwerTime;
            this.nextRender();
        }
        else {
            this.scheduleOnce(() => {
                this.recoverData(tempData);
            }, 0.5);
        }

    }
    clickButton(event: Event, customData: string) {
        let customEventData = Number(customData);
        if (this._anwerList[this._currQuestionIndex] == customEventData) {
            return;
        }
        console.log("customEventData", customEventData);
        if (this._isUpdateUi) {
            console.log("正在更新")
            return;
        }
        this._setStateFunc("isOperator", true);
        this._anwerList[this._currQuestionIndex] = customEventData;
        this.setHistoryData();
        this.updateOptinUI();
        this.updateUI();
        // this._setStateFunc && this._setStateFunc("isOperator", true);
        // this._setStateFunc && this._setStateFunc("chooseIndex", Number(customEventData))
    }
    private addButton(node: cc.Node, index: number) {
        const clickEventHandler = new ReadcomComponent.EventHandler();
        clickEventHandler.target = this.node; // 这个 node 节点是你的事件处理代码组件所属的节点
        clickEventHandler.component = 'ReadcomComponent';// 这个是脚本类名
        clickEventHandler.handler = 'clickButton';
        clickEventHandler.customEventData = index + "";
        const button = node.addComponent(cc.Button)
        if (this._modeType === ModeType.look) {
            button.interactable = false;
        }
        button.clickEvents.push(clickEventHandler);
    }
    updateResult() {
        this.resultNode.active = true;
        this.questionRootNode.active = false;
        this.timeLabel.node.parent.active = true;
        for (let i = 0; i < this._itemBigNode.length; i++) {
            let node = this._itemBigNode[i];
            cc.find("num", node).getComponent(cc.Label).string = "" + (i + 1);
            if (this._anwerList[i] == this._questionCorrectList[i]) {
                node.getComponent(cc.Sprite).spriteFrame = this.itemBigSp[0];
            } else {
                node.getComponent(cc.Sprite).spriteFrame = this.itemBigSp[1];
            }
        }
        this._currQuestionIndex = 0;
    }
    closeDetail() {
        this.resultNode.active = false;
        this.questionRootNode.active = true;
        this.nextRender();
    }

    public setChooseIndex(index: number) {
        if (this._isUpdateUi) {
            console.log("正在更新")
            return;
        }
        this._currQuestionIndex = index;
        this.updateUI();
    }
    hideToast() {
        this.toastNode.active = false;
    }
    nextButton() {
        if (this._currQuestionIndex == this.questionData['optionsList'].length - 1) {
            let index = -1;
            for (let i = 0; i < this._anwerList.length; i++) {
                if (this._anwerList[i] == -1) {
                    index = i;
                    break;
                }
            }
            if (index != -1) {
                console.log(" 第" + (index + 1) + "题未作答");
                this.toastNode.active = true;
                this.unschedule(this.hideToast);
                this.scheduleOnce(this.hideToast, 2);

            } else {
                if (this._modeType != ModeType.look) {
                    this._setStateFunc("submit", null);
                } else {
                    this._modeType = ModeType.look;
                    this.updateResult();
                }

            }

        } else {
            if (this._isUpdateUi) {
                console.log("正在更新")
                return;
            }
            this._currQuestionIndex++;
            this.nextRender();

        }

        this.setHistoryData();
    }

    previousButton(e, index: number) {
        if (this._currQuestionIndex == index || this._currQuestionIndex == 0 || this._isUpdateUi) {
            return;
        }
        this._currQuestionIndex--;
        this.nextRender();
        this.setHistoryData();
    }

    topButton(e, index: number) {
        if (this._currQuestionIndex == index || this._isUpdateUi) {
            return;
        }
        this._currQuestionIndex = index;
        this.nextRender();
        this.setHistoryData();
    }

    showCorrect() {
        this._modeType = ModeType.showCorrect;
        this.resultNode.active = false;
        this.questionRootNode.active = true;
        this.onCheckNextReander()
    }

    showUserAnswerState(answerStatus) {
        for (let i = 0; i < answerStatus.length; i++) {
            const element = answerStatus[i];
            this._anwerList[i] = Number(element.content) - 1
        }
        this.answerStatus = answerStatus;
        this._modeType = ModeType.showUserAnswer;
        this.resultNode.active = false;
        this.questionRootNode.active = true;
        this.onCheckNextReander()
    }

    onCheckNextReander() {
        if (this._isUpdateUi == false) {
            this.nextRender();
        } else {
            this.scheduleOnce(() => {
                this.onCheckNextReander()
            }, 0.2)
        }
    }
    // update (dt) {}
}
