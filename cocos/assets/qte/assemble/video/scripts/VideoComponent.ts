
import BaseComponent from "../../core/BaseComponent";
const { ccclass, property } = cc._decorator;


@ccclass
export default class VideoComponent extends BaseComponent {

    isInit: boolean = false;
    isStartRecord = false;
    @property(cc.Texture2D)
    videoTextureList: cc.Texture2D[] = [];

    @property(cc.Node)
    loadingNode: cc.Node = null;

    @property(sp.Skeleton)
    loadSpines: sp.Skeleton = null;

    isMock: boolean = false;

    crearorId: number = 1;

    public onLoad() {

    }

    public async initComponent(data?: any) {
        let prop = data.properties;
        this.node.width = prop.width;
        this.node.height = prop.height;
        this.loadingNode.active = true;
        this.loadSpines.timeScale = 1;
    }

    public changeSkine(path: string, param?: any) {
    }

    public changeProperties(key: string, data: any) {

    }

    startPreviewAndUp(params: { borderRadius: number; isShowUserInfo: boolean; videoWidth: number; videoHeight: number, isMock: boolean }, callBack: Function) {

        if (!cc.isValid(this.node, true)) {
            return;
        }
        this.loadingNode.active = true;
        this.loadSpines.timeScale = 1;
        this.loadSpines.setAnimation(0, "Loading_prefab", true);
        this.crearorId++;
        this.isMock = params.isMock;
        this.unscheduleAllCallbacks();
        if (this.isMock) {
            this.isInit = true;
            this.scheduleOnce(() => {
                this.loadingNode.active = false;
                callBack && callBack({ status: 1 });
            }, 5);
            return;
        }
        let pos = qte.QTEUtils.convertToStageSpaceAR(this.node, cc.v2(0, 0));
        if (qte.adapter && qte.adapter.startPreviewAndUp) {

            let width = this.node.width;
            if (this.node.parent && this.node.parent.parent) {
                width = this.node.width * this.node.parent.parent.scale;
            }
            let height = this.node.height;
            if (this.node.parent && this.node.parent.parent) {
                height = this.node.height * this.node.parent.parent.scale;
            }

            qte.adapter.startPreviewAndUp(
                {
                    micBgImage: this.videoTextureList[0].nativeUrl,
                    defaultAvImage: this.videoTextureList[1].nativeUrl,
                },
                {
                    width: width,
                    height: height,
                    x: pos.x,
                    y: pos.y,
                    borderRadius: params.borderRadius,
                    isShowUserInfo: params.isShowUserInfo,
                    videoWidth: params.videoWidth,
                    videoHeight: params.videoHeight,

                }, (data) => {
                    if (!cc.isValid(this.node, true)) {
                        return;
                    }
                    this.loadingNode.active = false;
                    this.isInit = true;
                    this.loadingNode.active = false;
                    callBack && callBack(data);
                });
        }
    }




    //filetype  录制视屏格式
    startVideoRecord(params: { filetype: string, maxTime: number, minTime: number }) {
        if (!this.isInit) {
            return;
        }

        if (this.isStartRecord) {
            return;
        }
        if (!this.isMock && qte.adapter && qte.adapter.startVideoRecord) {
            qte.adapter.startVideoRecord(params);
        }
        this.isStartRecord = true;
    }


    //这个action 虽然qte注册了  但实际不调用但是由于依赖后端提交后返回是否作答过 因此QA验证时比较费题
    //题版本异常状态会调用到
    //needUpload  是否需要上传
    stopVideoRecordManger(params: { needUpload: number, uniqId: string, pcNotRelease?: boolean }) {
        if (!this.isInit) {
            return;
        }
        if (!this.isStartRecord) {
            params.needUpload = 0;
        }

        if (!this.isMock && qte.adapter && qte.adapter.stopVideoRecordManger) {
            qte.adapter.stopVideoRecordManger(params);
        }

        this.isStartRecord = false;
    }

    updatePreviewFrame(params: { borderRadius: number; isShowUserInfo: boolean; isShowVideo:boolean}) {

        if (!this.isInit || this.isMock) {
            return;
        }

        let pos = qte.QTEUtils.convertToStageSpaceAR(this.node, cc.v2(0, 0));
        let width = this.node.width;
        if (this.node.parent && this.node.parent.parent) {
            width = this.node.width * this.node.parent.parent.scale;
        }
        let height = this.node.height;
        if (this.node.parent && this.node.parent.parent) {
            height = this.node.height * this.node.parent.parent.scale;
        }

        if (qte.adapter && qte.adapter.updatePreviewFrame) {
            qte.adapter.updatePreviewFrame({
                width: width,
                height: height,
                x: pos.x,
                y: pos.y,
                borderRadius: params.borderRadius,
                isShowUserInfo: params.isShowUserInfo,
                isShowVideo: params.isShowVideo
            });
        }
    }

    checkVolumeLevel(params: any, callBack: Function) {
        if (!this.isInit) {
            return;
        }
        if (this.isMock) {
            callBack && callBack({ status: 30 });
            return
        }

        if (qte.adapter && qte.adapter.checkVolumeLevel) {
            qte.adapter.checkVolumeLevel(params, (data: { status: number }) => {
                if (!cc.isValid(this.node, true)) {
                    return
                }
                callBack && callBack(data);
            });
        }
    }

}