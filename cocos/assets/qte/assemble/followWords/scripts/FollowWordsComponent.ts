import BaseComponent from "../../core/BaseComponent";
const { ccclass, property } = cc._decorator;
@ccclass
export default class FollowWordsComponent extends BaseComponent {

    questionData: any = null;

    @property(cc.Label)
    sorceLable: cc.Label = null;

    @property(cc.Label)
    gradeLabel: cc.Label = null;

    @property(cc.Node)
    lostNode: cc.Node = null;

    @property(cc.SpriteFrame)
    sorceBgIcon: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    sorceBg: cc.Sprite = null;

    soundRank: number = -1;

    hideScore: boolean = true;
    @property(cc.AudioClip)
    dingAudio: cc.AudioClip = null;

    @property(cc.AudioClip)
    succAudio: cc.AudioClip[] = [];

    public changeProperties(key: string, data: any) {
        if (key == "question") {
            this.questionData = data;
            this.initComponent(null);
        }
    }

    public startUpdateAnswerUi() {
        this.hideScore = true;
        this.updateUi();
    }
    public changeSkine(path: string, param?: any) {
    }

    public initComponent(data?: any) {
        if (data) {
            this.questionData = data.properties.question;
        }
        console.log(data);
        this.updateUi();
    }
    playDingAudio() {
        this.playAudio(this.dingAudio, false, () => {
        });
    }
    playSuccAudio() {
        let audio = this.succAudio[2];
        if (this.soundRank >= 80) {
            audio = this.succAudio[0];
        } else if (this.soundRank >= 60 && this.soundRank < 80) {
            audio = this.succAudio[1];
        }
        this.playAudio(audio, false, () => {
        });
    }
    updateUi() {
        if ( this.hideScore) {
            this.sorceBg.node.active = false;
        } else {
            this.sorceBg.node.active = true;
            this.lostNode.active = false;
            this.gradeLabel.node.active = true;
            this.sorceLable.node.active = true;
            this.sorceLable.string = this.soundRank + "分";
            let color = new cc.Color();
            if (this.soundRank >= 80) {
                this.gradeLabel.string = "Perfect!";
                this.sorceBg.spriteFrame = this.sorceBgIcon[2];
                color.fromHEX("#2ED1A0");

            } else if (this.soundRank >= 60 && this.soundRank < 80) {
                this.gradeLabel.string = "Good";
                this.sorceBg.spriteFrame = this.sorceBgIcon[1];
                color.fromHEX("#FFBB00");
            } else if (this.soundRank >= 0 && this.soundRank < 60) {
                this.gradeLabel.string = "Come on !";
                this.sorceBg.spriteFrame = this.sorceBgIcon[0];
                color.fromHEX("#FF6647");
            } else if (this.soundRank < 0) {
                this.lostNode.active = true;
                this.gradeLabel.node.active = false;
                this.sorceLable.node.active = false;
                this.sorceBg.spriteFrame = this.sorceBgIcon[0];
                color.fromHEX("#2ED1A0");
            }
            this.sorceLable.node.color = color;
        }
    }

    public reset() {
    }
    updateResult() {
    }

}
