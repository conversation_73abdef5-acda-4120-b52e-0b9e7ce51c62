[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "template", "_objFlags": 512, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}], "_active": true, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "questionNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1230, "height": 440}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-613, 262, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "eeb20762-c6c8-4e51-af25-c6614efab51b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "558fcRXRFAjaH0LiJKOa1y", "sync": false}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 6}, {"__id__": 8}, {"__id__": 11}, {"__id__": 14}, {"__id__": 16}, {"__id__": 19}], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}], "_prefab": {"__id__": 23}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 299, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -116, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 96, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-101.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "d3PFMXMg9CL5JhZUJu9yAM", "sync": false}, {"__type__": "cc.Node", "_name": "sorceStr", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 20, "g": 20, "b": 20, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 69, "height": 36}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-19, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Good", "_N$string": "Good", "_fontSize": 28, "_lineHeight": 28, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 2, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "713FEmFZ9MbpEJn1GeV/LU", "sync": false}, {"__type__": "cc.Node", "_name": "lost", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": false, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 20, "g": 20, "b": 20, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 157, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [15.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "再试一次吧~", "_N$string": "再试一次吧~", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "c9nzwYlApAGKWMXqNd1xIS", "sync": false}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [27.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "2d2o/Wg8tJ0Li4qsPU6aO0", "sync": false}, {"__type__": "cc.Node", "_name": "sorce<PERSON><PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [{"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 32, "b": 32, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 86, "height": 46}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [82.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 16}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "100分", "_N$string": "100分", "_fontSize": 32, "_lineHeight": 36, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "30iPUARqNBPrUvNywa0yAS", "sync": false}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 5}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 20}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 24, "height": 68}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [137.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "1fCQWHyWBDPbgu/KDOQXqS", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2041f3fb-82bb-44ee-8df0-72ec916c8228"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 299, "height": 68}, "_resize": 1, "_N$layoutType": 1, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "20R6s95OtIs4V0UhMh3YL4", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "839a4238-0f77-4d56-a8da-1542ea7c5d19"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "da6a0lT6ClL3a8/XF4hOJKM", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "sorceLable": {"__id__": 17}, "gradeLabel": {"__id__": 9}, "lostNode": {"__id__": 11}, "sorceBgIcon": [{"__uuid__": "b64bf335-0c2a-482d-a3ff-45d607ff46f0"}, {"__uuid__": "55fdc4b6-2720-4838-b6b1-9e8911d58b0a"}, {"__uuid__": "2041f3fb-82bb-44ee-8df0-72ec916c8228"}], "sorceBg": {"__id__": 21}, "dingAudio": {"__uuid__": "58435d31-c1ef-4795-a293-2617c659d927"}, "succAudio": [{"__uuid__": "c21d040a-1670-4df0-b993-9b981a3e31de"}, {"__uuid__": "46e1f7f1-6c52-43ea-91f5-8f074b31e211"}, {"__uuid__": "15c1cc25-28af-4939-9958-60ffca069516"}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "4d848c25-ec75-4e78-a223-26b5c2771707"}, "fileId": "", "sync": false}]