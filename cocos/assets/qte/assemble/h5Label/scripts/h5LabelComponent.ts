import BaseComponent from "../../core/BaseComponent";


const { ccclass, property } = cc._decorator;

@ccclass
export default class h5LabelComponent extends BaseComponent {
    @property(cc.Node)
    content: cc.Node = null;
    questionData: any = null;
    _labelPicList: [] = [];
    labelPicList: cc.Node[] = [];
    @property(cc.ScrollView)
    scrollView: cc.ScrollView = null;
    public changeProperties(key: string, data: any) {
        if (key == "labelPicList") {
            this._labelPicList = data;
            this.initComponent(null);
        }
        else if (key == "width") {
            this.node.width = data;
            this.content.parent.width = data;
            this.content.width = data;
            this.setScaleFormPicList(this.labelPicList, "h5List");
            this.content.y = 0;
            this.scrollView.scrollToTop(0);
        }
        else if (key == "height") {
            this.node.height = data;
            this.content.parent.height = data;
            this.setScaleFormPicList(this.labelPicList, "h5List");
            this.content.y = 0;
            this.scrollView.scrollToTop(0);
        }
    }
    public mouseWheel(event: cc.Event.EventMouse) {
        let h = event.getScrollY();
        let d = -h * 0.12;
        let offSet = this.scrollView.getScrollOffset();
        let maxoffSetY = this.content.height - this.content.parent.height;

        if (maxoffSetY > 0) {
            let nextOffestY = offSet.y + d;
            if (nextOffestY > maxoffSetY) {
                nextOffestY = maxoffSetY;
            }
            if (nextOffestY < 0) {
                nextOffestY = 0;
            }
            this.content.y = nextOffestY;
        }
    }

    public changeSkine(path: string, param?: any) {
    }

    public async initComponent(data?: any) {
        if (data) {
            this.node.width = data.properties.width;
            this.node.height = data.properties.height;
            this.content.parent.width = data.properties.width;
            this.content.parent.height = data.properties.height;
            this.content.width = data.properties.width;
            this.questionData = data.properties.customH5label;
            this._labelPicList = data.properties.labelPicList;
        }

        for (let i = 0; i < this.labelPicList.length; i++) {
            this.labelPicList[i].destroy();
        }

        this.labelPicList = [];
        let pos = cc.v2(0, 0)
        for (let i = 0; i < this._labelPicList.length; i++) {
            pos = await this.loadTextureArray(this._labelPicList, this.content, pos, i);
        }
        let h1 = Math.abs(pos.y);
        this.content.height = h1;
        this.setScaleFormPicList(this.labelPicList, "h5List");
        this.scrollView.scrollToTop(0);
    }

    /**
      * 加载远程资源,返回对应asset对象
      */
    async loadRes(url: string): Promise<cc.Texture2D> {
        if (!url) {
            return;
        }
        return new Promise<any>((resolve, reject) => {
            this.getRemoteRes(url, null, (error, res: cc.Asset) => {
                if (error) {
                    reject();
                    return;
                }
                resolve(res);
            });
        }).catch(err => {
            qte && qte.logCatBoth('h5Label Load  cocos promise error:', err);
            return null;
        });
    }


    private async loadTextureArray(array: [], parent: cc.Node, pos: cc.Vec2, index: number, type?: string) {

        if (!cc.isValid(this.node, true) || !array[index]) {
            return;
        }
        let texture = await this.loadRes(array[index]);
        return new Promise<any>((resolve, reject) => {
            if (!cc.isValid(texture, true)) {
                qte.logCatBoth("H5LabelComponent", "loadTextureArray texture is null");
                return;
            }
            if (texture instanceof cc.Texture2D) {
                texture.setPremultiplyAlpha(true);
                texture.packable = false;
            }
            let sp = new cc.SpriteFrame(texture);
            let node = new cc.Node();
            node.anchorX = 0;
            node.anchorY = 1;
            let spr = node.addComponent(cc.Sprite)
            spr.spriteFrame = sp;
            spr.trim = false;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.parent = parent;
            node.x = 0;
            node.y = pos.y;
            pos.y = pos.y - node.height;
            this.labelPicList.push(node);
            resolve(pos);
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });




        // if (index < array.length - 1) {
        //     index++;
        //   await this.loadTextureArray(array, parent, pos, index, type);
        // } else {
        //     let h1 = Math.abs(pos.y);
        //     parent.height = h1;
        //     this.setScaleFormPicList(this.labelPicList, "h5List");
        //     this.scrollView.scrollToTop(0);
        // }
    }
    setScaleFormPicList(nodeList: cc.Node[], type?: string) {
        let maxW = 0;
        for (let i = 0; i < nodeList.length; i++) {
            if (maxW < nodeList[i].width) {
                maxW = nodeList[i].width;
            }

        }
        switch (type) {
            case "h5List": // 适配答题区
                {
                    console.log("this.content.width ", this.content.width);
                    let scale = Number(parseFloat(this.content.width / maxW + "").toFixed(2));
                    if (maxW > this.content.width) {
                        let culH = 0;
                        for (let i = 0; i < nodeList.length; i++) {
                            nodeList[i].scale = scale;
                            nodeList[i].y = culH;
                            culH -= (scale * nodeList[i].height)
                        }
                        let h1 = Math.abs(culH);
                        if (h1 < this.content.parent.height) {
                            this.content.height = this.content.parent.height;
                        } else {
                            this.content.height = h1;
                        }
                    }
                }
                break;

            default:
                break;
        }
    }
}
