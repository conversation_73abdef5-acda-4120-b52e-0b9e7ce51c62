[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "h5LabelComponent", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 14}, {"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 6}, {"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-500, 320, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1000, "height": 250}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 40, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1000, "_originalHeight": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 3}, "asset": {"__uuid__": "5baab98c-fc74-4e43-9891-6215f8ff4858"}, "fileId": "0b1dZ5CuhBdanIvFbADEg6", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1000, "_originalHeight": 640, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8e13f325-6913-4e51-a5ad-2d506ac96f2e"}, "fileId": "53+Wl6sRNCIYyUxujpAJYA", "sync": false}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}], "_active": true, "_components": [{"__id__": 13}, {"__id__": 15}], "_prefab": {"__id__": 16}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [500, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 4, "height": 172}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 1, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "203532b3-cc16-47d3-b028-3eb988a80eb1"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8e13f325-6913-4e51-a5ad-2d506ac96f2e"}, "fileId": "2eyg4jIrdEJrry9FzXUaZ2", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_scrollView": {"__id__": 14}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 11}, "_N$direction": 1, "_id": ""}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": false, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 3}, "content": {"__id__": 3}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 13}, "_id": ""}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "alignMode": 2, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8e13f325-6913-4e51-a5ad-2d506ac96f2e"}, "fileId": "ccjp9cBvlOCaa3z4FcHM2l", "sync": false}, {"__type__": "ce10csPzaNFipKQWXU3AtXs", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "content": {"__id__": 3}, "scrollView": {"__id__": 14}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "8e13f325-6913-4e51-a5ad-2d506ac96f2e"}, "fileId": "", "sync": false}]