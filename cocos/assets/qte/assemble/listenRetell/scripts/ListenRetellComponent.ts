import BaseComponent from "../../core/BaseComponent";


const { ccclass, property } = cc._decorator;
export enum ModeType {
    onStart = -1,
    ready = 0,
    listen = 1,
    answerReady = 2,
    answer = 3,
    result = 4
}
enum TimeName {
    ready = "读题时间：",
    listen = "听音时间：",
    answerReady = "准备时间：",
    answer = "答题时间：",
    result = "-"
}


@ccclass
export default class ListenRetellComponent extends BaseComponent {
    _setStateFunc: Function = null;
    public questionData: any = null;
    _questionPicArray: cc.Node[] = [];

    @property(cc.Label)
    titleLabel: cc.Label = null;

    @property(cc.Node)
    questionLabelNode: cc.Node = null;

    @property(cc.Node)
    qViewNode: cc.Node = null;

    @property(cc.Label)
    tipsStartLabel: cc.Label = null;

    @property(cc.Label)
    tipsDecLabel: cc.Label = null;

    @property(cc.Label)
    timeTitleLabel: cc.Label = null;

    @property(cc.Label)
    resultTipsLabel: cc.Label = null;

    @property(cc.Label)
    resultAnswerTipsLabel: cc.Label = null;

    @property(cc.Node)
    questionNode: cc.Node = null;

    @property(cc.Node)
    anwersNode: cc.Node = null;

    @property(cc.Node)
    bottom: cc.Node = null;

    @property(cc.Node)
    result: cc.Node = null;

    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.Label)
    soundLostLabel: cc.Label = null;


    @property(cc.SpriteFrame)
    subMSp: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    subMBt: cc.Sprite = null;

    @property(cc.Node)
    orNode: cc.Node = null;

    @property(cc.AudioClip)
    dingAudio: cc.AudioClip = null;

    @property(cc.Label)
    gradeLabel: cc.Label = null;
    @property(cc.Node)
    lostNode: cc.Node = null;

    @property(cc.Node)
    resultAnswer: cc.Node = null;

    _maxQuesctionW: number = 886;
    _mode: ModeType = ModeType.onStart;
    _countNum: number = 0;
    _isCanPlay: boolean = false;
    soundRank: number = -2;
    answerAudioUrl: string = "";
    answerDuration: number = 0;
    _isOperator: boolean = false;
    public changeProperties(key: string, data: any) {
        if (key == "question") {
            this.questionData = data;
            this.initComponent(null);
        }
    }
    public changeSkine(path: string, param?: any) {
    }
    start() {

    }

    public setOpenator() {
        this._isOperator = true;
    }

    public initComponent(data?: any) {
        if (data) {
            this.questionData = data.properties.question;
        }

        for (let i = 0; i < this._questionPicArray.length; i++) {
            this._questionPicArray[i].destroy();
        }

        this._questionPicArray = [];
        this.loadTextureArray(this.questionData.questionTitlePicList, this.questionLabelNode, cc.v2(24, 0), 0);
        this.initUi()
    }
    initUi() {
        this.mode = ModeType.onStart;
        this.tipsStartLabel.string = this.questionData.tipsStart;
        if (this.questionData.title) {
            this.titleLabel.string = this.questionData.title;
        }
        this._countNum = this.questionData.lookTime;
        this._isCanPlay = false;
        this._isOperator = false;
        this.resultTipsLabel.string = this.questionData.tipsStart;
        this.resultAnswerTipsLabel.string = this.questionData.answerSetting;
        this.lostNode.active = false;
        this.soundLostLabel.node.active = false;
        this.gradeLabel.node.active = false;
        this.soundRank = -2;
        this.answerAudioUrl = "";
        this.answerDuration = 0;
        this.updateUI();

    }
    get mode(): ModeType {
        return this._mode
    }
    set mode(val: ModeType) {
        this._mode = val;
        this._setStateFunc && this._setStateFunc('changeMode', this._mode);
        if (this._mode == ModeType.result) {
            this.questionNode.active = false;
            this.anwersNode.active = false;
            this.bottom.active = false;
            this.result.active = true;
            this.result.parent = this.node.parent.parent;
            this.result.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        else {
            this.questionNode.active = true;
            this.anwersNode.active = true;
            this.bottom.active = true;
            this.result.active = false;
        }

    }
    public startReady() {
        this.mode = ModeType.onStart;
    }
    startRead() {
        this.mode = ModeType.ready;
        this._countNum = this.questionData.lookTime;
        this._isCanPlay = false;
        this.updateUI();
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }

    startPlaySound() {
        if (!this.questionData.audioUrl) {
            this.starAnswerReady();
            return;
        }
        this.playDingAudio();
        this.mode = ModeType.listen;
        this.updateUI();
    }
    starAnswerReady() {
        this.playDingAudio();
        this.mode = ModeType.answerReady;
        this._isCanPlay = false;
        this._countNum = this.questionData.readyTime;
        this.updateUI();
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);

    }
    playDingAudio() {
        this.playAudio(this.dingAudio, false, () => {
        });
    }
    startAnswer() {
        this.playDingAudio();
        this.scheduleOnce(() => {
            this.mode = ModeType.answer;
            this._isCanPlay = false;
            this._countNum = this.questionData.answerTime;
            this.updateUI();
        }, 1);
    }
    startTouchAnswer() {
        this.unschedule(this.updateUI);
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this.updateUI();
    }

    startUpdateAnswerUi() {
        this.unschedule(this.updateUI);
        // this.mode = ModeType.answer;
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this._isOperator = false;
        this.updateUI();
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }
    startPauseAnwer() {
        this.unschedule(this.updateUI);
    }
    startEndAnwer() {
        this.unschedule(this.updateUI);
        this._isCanPlay = false;
        this._countNum = this.questionData.answerTime;
        this.updateUI();
    }

    startResult() {
        if (this._isOperator && this.answerAudioUrl) {
            this.mode = ModeType.result;
            this._setStateFunc("submit", null);
            this.updateUI();
            this.scheduleOnce(() => {
                this.resultTipsLabel.node.parent.height = this.resultTipsLabel.node.height + 15;
                this.resultAnswerTipsLabel.node.parent.height = this.resultAnswerTipsLabel.node.height + 52;
            }, 0.1)
        } else {
            this.showToast(`暂未生成录音，请答题后再提交`);
        }
    }
    starResultEnd(isPassive: boolean = false) {
        if ((this._isOperator && this.answerAudioUrl) || (this._isOperator && isPassive)) {
            this.mode = ModeType.result;
            this.unschedule(this.unschedule);
            this.updateUI();
            this.scheduleOnce(() => {
                this.resultTipsLabel.node.parent.height = this.resultTipsLabel.node.height + 15;
                this.resultAnswerTipsLabel.node.parent.height = this.resultAnswerTipsLabel.node.height + 52;
            }, 0.1)
        }
    }


    updateUI() {
        if (this._isCanPlay) {
            this._countNum--;
            if (this._countNum < 0) {
                this._countNum = 0
            }
        }
        let muit = parseInt(this._countNum / 60 + "") + "";;
        let sec = parseInt(this._countNum % 60 + "") + "";
        if (muit.length == 1) {
            muit = "0" + muit;
        }
        if (sec.length == 1) {
            sec = "0" + sec;
        }

        let timeStr = muit + ":" + sec;
        if (this.mode == ModeType.ready || this.mode == ModeType.onStart) {
            this.timeTitleLabel.string = TimeName.ready;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            this.tipsDecLabel.string = "现在，你有" + this.questionData.lookTime + "秒钟的时间阅读这道小题。"
            if (this._countNum == 0) {
                this.unschedule(this.updateUI);
                this.timeLabel.string = '- -';
                this.startPlaySound();
            }
            this.subMBt.spriteFrame = this.subMSp[1];

        } else if (this.mode == ModeType.listen) {
            this.timeTitleLabel.string = TimeName.listen;
            this.tipsDecLabel.string = "现在，请听音频。"
            // this.timeLabel.node.parent.active = false;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = '- -';
            this.subMBt.spriteFrame = this.subMSp[1];
        }
        else if (this.mode == ModeType.answerReady) {
            this.timeTitleLabel.string = TimeName.answerReady;
            this.tipsDecLabel.string = "现在,你有" + this.questionData.readyTime + "秒钟的时间做转述准备，转述的开头已给出。";
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            this.subMBt.spriteFrame = this.subMSp[1];
            if (this._countNum == 0) {
                this.unschedule(this.updateUI);
                this.startAnswer()
            }
        }
        else if (this.mode == ModeType.answer) {
            this.timeTitleLabel.string = TimeName.answer;
            this.tipsDecLabel.string = "现在,请在" + this.questionData.answerTime + "秒钟内完成作答";
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            // this.timeLabel.string = '- -';
            if (this._isOperator && this.answerAudioUrl) {
                this.subMBt.spriteFrame = this.subMSp[0];
            } else {
                this.subMBt.spriteFrame = this.subMSp[1];
            }

        }
        else if (this.mode == ModeType.result) {
            this.timeTitleLabel.string = TimeName.result;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = '- -';
            this.subMBt.spriteFrame = this.subMSp[0];
            this.getRankSp()
        }
    }

    getRankSp() {

        if (typeof this.soundRank == 'number') {
            if (this.soundRank >= 0 && this.soundRank <= 100) {
                this.gradeLabel.string = this.soundRank + "";
                this.gradeLabel.node.active = true;
                this.lostNode.active = false;
            }
            else {
                this.soundRank = -1;
                this.lostNode.active = true;
                this.gradeLabel.node.active = false;
            }

        } else {
            this.soundRank = -1;
            this.lostNode.active = true;
            this.gradeLabel.node.active = false;
        }

        if (this.answerAudioUrl) {
            this.soundLostLabel.node.active = false;
        } else {
            this.soundLostLabel.node.active = true;
        }

    }

    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
        this._setStateFunc("setAnswerBase", null);
    }




    public reset() {
        this.unschedule(this.updateUI);
        this.initUi();
    }

    public getAnwerIsCorrect(): boolean {
        return true
    }


    private loadTextureArray(array: [], parent: cc.Node, pos: cc.Vec2, index: number, type?: string) {
        if (!cc.isValid(this.node, true) || !array[index]) {
            return;
        }

        this.getRemoteRes(array[index], cc.Texture2D, (error, texture: cc.Texture2D) => {
            if (error) {
                return;
            }
            // texture.setPremultiplyAlpha(true);
            let sp = new cc.SpriteFrame(texture);
            let node = new cc.Node();
            node.anchorX = 0;
            node.anchorY = 1;
            let spr = node.addComponent(cc.Sprite)
            spr.spriteFrame = sp;
            spr.trim = false;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            node.parent = parent;
            node.x = 0;
            node.y = pos.y;
            pos.y = pos.y - node.height;
            this._questionPicArray.push(node);
            if (index < array.length - 1) {
                index++;
                this.loadTextureArray(array, parent, pos, index, type);
            } else {
                let h1 = Math.abs(pos.y);
                if (h1 < this.qViewNode.height) {
                    parent.height = this.qViewNode.height;
                } else {
                    parent.height = h1;
                }
                this.setScaleFormPicList(this._questionPicArray, "question");

            }
        });
    }
    setScaleFormPicList(nodeList: cc.Node[], type?: string) {
        let maxW = 0;
        for (let i = 0; i < nodeList.length; i++) {
            if (maxW < nodeList[i].width) {
                maxW = nodeList[i].width;
            }

        }
        switch (type) {
            case "question": // 适配答题区
                {
                    let scale = Number(parseFloat(this._maxQuesctionW / maxW + "").toFixed(2));
                    if (maxW > this._maxQuesctionW) {
                        let culH = 0;
                        for (let i = 0; i < nodeList.length; i++) {
                            nodeList[i].scale = scale;
                            nodeList[i].y = culH;
                            culH -= (scale * nodeList[i].height)
                        }
                        let h1 = Math.abs(culH);
                        if (h1 < this.qViewNode.height) {
                            this.questionLabelNode.height = this.qViewNode.height;
                        } else {
                            this.questionLabelNode.height = h1;
                        }

                    }

                }
                break;

            default:
                break;
        }
    }

    public showCorrect(): void {
        this.resultAnswer.active = true;
        this.resultAnswer.parent = this.node.parent.parent;
        this.resultAnswer.zIndex = cc.macro.MAX_ZINDEX - 1;

        let mySound: cc.Node = this.resultAnswer.getChildByName("mySound");
        mySound.active = false;
        let lostRank: cc.Node = this.resultAnswer.getChildByName("lostRank");
        lostRank.active = false;
        let grade: cc.Node = this.resultAnswer.getChildByName("grade");
        grade.active = false;

        let list: cc.ScrollView = this.resultAnswer.getChildByName("list").getComponent(cc.ScrollView);

        // 题干
        let questionContent: cc.Node = cc.find("questionStem/questionContent", list.content);
        this.loadTextureArray(this.questionData.questionTitlePicList, questionContent, cc.v2(24, 0), 0);

        // 转述开头
        let startNewtLabel: cc.Label = cc.find("ortopNode/startNewtLabel", list.content)?.getComponent(cc.Label);
        startNewtLabel.string = this.questionData?.tipsStart + "";

        // 参考答案
        let answertLabel: cc.Label = cc.find("orBottomNode/answertLabel", list.content)?.getComponent(cc.Label);
        answertLabel.string = this.questionData?.answerSetting + "";
    }

    public showUserAnswer(data: any): void {
        this.resultAnswer.active = true;
        this.resultAnswer.parent = this.node.parent.parent;
        this.resultAnswer.zIndex = cc.macro.MAX_ZINDEX - 1;
        let mySound: cc.Node = this.resultAnswer.getChildByName("mySound");
        mySound.active = true;
        let mySoundLost: cc.Node = this.resultAnswer.getChildByName("mySoundLost");
        mySoundLost.active = !data || !data.audioUrl || !data.duration;
        let lostRank: cc.Node = this.resultAnswer.getChildByName("lostRank");
        let grade: cc.Label = this.resultAnswer.getChildByName("grade").getComponent(cc.Label);

        if (data) {
            if (typeof data.soundRank == 'number') {
                if (data.soundRank >= 0 && data.soundRank <= 100) {
                    grade.string = data.soundRank + "";
                    grade.node.active = true;
                    lostRank.active = false;
                } else {
                    this.soundRank = -1;
                    lostRank.active = true;
                    grade.node.active = false;
                }
            } else {
                lostRank.active = true;
                grade.node.active = false;
            }
        }

        let list: cc.ScrollView = this.resultAnswer.getChildByName("list").getComponent(cc.ScrollView);

        // 题干
        let questionContent: cc.Node = cc.find("questionStem/questionContent", list.content);
        this.loadTextureArray(this.questionData.questionTitlePicList, questionContent, cc.v2(24, 0), 0);

        // 转述开头
        let startNewtLabel: cc.Label = cc.find("ortopNode/startNewtLabel", list.content)?.getComponent(cc.Label);
        startNewtLabel.string = this.questionData?.tipsStart + "";

        // 参考答案
        let answertLabel: cc.Label = cc.find("orBottomNode/answertLabel", list.content)?.getComponent(cc.Label);
        answertLabel.string = this.questionData?.answerSetting + "";
    }

    // update (dt) {}
}
