/*
 * @FilePath     : /sdk/assets/qte/assemble/listenRetell/scripts/ListenQuestionAddSorce.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
export const addListenQuestionSorce = (data: number) => {
    let temp = data;
    if (data >= 0 && data <= 20) {
        temp =parseInt(temp * 1.8+"");
    } else if (data >= 21 && data <= 50) {
        temp = parseInt( temp * 1.5+"");
    } else if (data >= 51 && data <= 70) {
        temp = parseInt(temp * 1.2+"");
    } else if (data >= 71 && data <= 90) {
        temp = parseInt(temp * 1.1+"");
    }
    else if (data >= 91 && data <= 100) {
        temp = parseInt(temp * 1+"");
    }
    if(temp == 0){
        temp = 20;
    }
    return temp;
}