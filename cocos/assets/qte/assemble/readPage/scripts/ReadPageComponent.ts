import BaseComponent from "../../core/BaseComponent";
import { addListenQuestionSorce } from "../../listenRetell/scripts/ListenQuestionAddSorce";


const { ccclass, property } = cc._decorator;
export enum ModeType {
    onStart = -1,
    ready = 0,
    answer = 2,
    result = 3
}
enum TimeName {
    ready = "准备时间：",
    answer = "答题时间：",
    result = "-"
}


@ccclass
export default class ReadPageComponent extends BaseComponent {
    _setStateFunc: Function = null;
    public questionData: any = null;

    @property(cc.Label)
    questionLabel: cc.Label = null;

    @property(cc.Node)
    qViewNode: cc.Node = null;



    @property(cc.Label)
    tipsDecLabel: cc.Label = null;

    @property(cc.Label)
    timeTitleLabel: cc.Label = null;

    @property(cc.RichText)
    resultTipsLabel: cc.RichText = null;

    @property(cc.Node)
    questionNode: cc.Node = null;


    @property(cc.Node)
    bottom: cc.Node = null;

    @property(cc.Node)
    result: cc.Node = null;

    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.Label)
    soundLostLabel: cc.Label = null;


    @property(cc.SpriteFrame)
    subMSp: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    subMBt: cc.Sprite = null;



    @property(cc.AudioClip)
    dingAudio: cc.AudioClip = null;

    @property(cc.Label)
    gradeLabel: cc.Label = null;
    @property(cc.Node)
    lostNode: cc.Node = null;

    _maxQuesctionW: number = 886;
    _mode: ModeType = ModeType.ready;
    _countNum: number = 0;
    _isCanPlay: boolean = false;
    soundRank: number = -2;
    answerAudioUrl: string = "";
    answerDuration: number = 0;
    _isOperator: boolean = false;
    _answerList: cc.Node[] = [];
    resultWorldList: any[] = [];
    wordList = [];
    public changeProperties(key: string, data: any) {
        if (key == "question") {
            this.questionData = data;
            this.initComponent(null);
        }
    }
    public changeSkine(path: string, param?: any) {
    }

    public setOpenator() {
        this._isOperator = true;
    }

    public initComponent(data?: any) {
        if (data) {
            this.questionData = data.properties.question;
        }
        console.log(data);
        this.initUi();
    }
    get mode(): ModeType {
        return this._mode
    }
    set mode(val: ModeType) {
        this._mode = val;
        this._setStateFunc && this._setStateFunc('changeMode', this._mode);
        if (this._mode == ModeType.result) {
            this.questionNode.active = false;
            this.bottom.active = false;
            this.result.active = true;
            this.result.parent = this.node.parent.parent;
            this.result.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        else {
            this.questionNode.active = true;
            this.bottom.active = true;
            this.result.active = false;
        }

    }
    public startReady() {
        this.mode = ModeType.onStart;
    }
    startRead() {
        this.mode = ModeType.ready;
        this._countNum = this.questionData.readyTime;
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }



    startAnswer() {
        this.playDingAudio();
        this.scheduleOnce(() => {
            this.mode = ModeType.answer;
            this._isCanPlay = false;
            this._countNum = this.questionData.answerTime;
            this.updateUI();
        }, 1);
    }
    playDingAudio() {
        this.playAudio(this.dingAudio, false, () => {
        });
    }
    startTouchAnswer() {
        this.unschedule(this.updateUI);
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this.updateUI();
    }

    startUpdateAnswerUi() {
        this.unschedule(this.updateUI);
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this._isOperator = false;
        this.updateUI();
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }
    startPauseAnwer() {
        this.unschedule(this.updateUI);
    }
    startEndAnwer() {
        this.unschedule(this.updateUI);
        this._isCanPlay = false;
        this._countNum = this.questionData.answerTime;
        this.updateUI();
    }

    startResult() {
        if (this._isOperator && this.answerAudioUrl) {
            this.mode = ModeType.result;
            this._setStateFunc("submit", null);
            this.updateUI();

        } else {
            this.showToast(`暂未生成录音，请答题后再提交`);
        }
    }
    starResultEnd(isPassive: boolean = false) {
        if ((this._isOperator && this.answerAudioUrl) || (this._isOperator && isPassive)) {
            this.mode = ModeType.result;
            this.unschedule(this.unschedule);
            this.updateUI();
        }
    }


    updateUI() {
        if (this._isCanPlay) {
            this._countNum--;
            if (this._countNum < 0) {
                this._countNum = 0
            }
        }
        let muit = parseInt(this._countNum / 60 + "") + "";;
        let sec = parseInt(this._countNum % 60 + "") + "";
        if (muit.length == 1) {
            muit = "0" + muit;
        }
        if (sec.length == 1) {
            sec = "0" + sec;
        }

        let timeStr = muit + ":" + sec;
        if (this.mode == ModeType.ready || this.mode == ModeType.onStart) {
            this.timeTitleLabel.string = TimeName.ready;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            if (this._countNum == 0) {
                this.unschedule(this.updateUI);
                this.timeLabel.string = '- -';
                this.startAnswer();
            }
            this.subMBt.spriteFrame = this.subMSp[1];
        }
        else if (this.mode == ModeType.answer) {
            this.timeTitleLabel.string = TimeName.answer;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            // this.timeLabel.string = '- -';
            if (this._isOperator && this.answerAudioUrl) {
                this.subMBt.spriteFrame = this.subMSp[0];
            } else {
                this.subMBt.spriteFrame = this.subMSp[1];
            }

        }
        else if (this.mode == ModeType.result) {
            this.timeTitleLabel.string = TimeName.result;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = '- -';
            this.subMBt.spriteFrame = this.subMSp[0];
            this.getRankSp()
            this.resultTipsLabel.string = this.creatorRichText(this.questionData.questionTitle, this.resultWorldList);
        }
    }

    public showUserAnswer(answerStatus: any) {
        this.timeTitleLabel.string = TimeName.result;
        this.timeLabel.node.parent.active = true;
        this.timeLabel.string = '- -';
        this.subMBt.spriteFrame = this.subMSp[0];
        this.resultTipsLabel.string = this.creatorRichText(this.questionData.questionTitle, this.resultWorldList);

        if (answerStatus && typeof answerStatus.soundRank == 'number') {
            if (answerStatus.soundRank >= 0 && answerStatus.soundRank <= 100) {
                this.gradeLabel.string = answerStatus.soundRank + "";
                this.gradeLabel.node.active = true;
                this.lostNode.active = false;
            }
            else {
                this.lostNode.active = true;
                this.gradeLabel.node.active = false;
            }

        } else {
            this.lostNode.active = true;
            this.gradeLabel.node.active = false;
        }
        if (answerStatus && answerStatus.answerAudioUrl) {
            this.soundLostLabel.node.active = false;
        } else {
            this.soundLostLabel.node.active = true;
        }

    }

    getRankSp() {
        if (typeof this.soundRank == 'number') {
            if (this.soundRank >= 0 && this.soundRank <= 100) {
                this.gradeLabel.string = this.soundRank + "";
                this.gradeLabel.node.active = true;
                this.lostNode.active = false;
            }
            else {
                this.soundRank = -1;
                this.lostNode.active = true;
                this.gradeLabel.node.active = false;
            }

        } else {
            this.soundRank = -1;
            this.lostNode.active = true;
            this.gradeLabel.node.active = false;
        }
        if (this.answerAudioUrl) {
            this.soundLostLabel.node.active = false;
        } else {
            this.soundLostLabel.node.active = true;
        }
    }

    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
        this._setStateFunc("setAnswerBase", null);
    }

    initUi() {
        this.mode = ModeType.onStart;
        this._countNum = this.questionData.readyTime;
        this._isCanPlay = false;
        this.tipsDecLabel.string = "你有" + this.questionData.readyTime + "秒的时间浏览短文并做准备，听到“叮”之后，有" + this.questionData.answerTime + "秒的时间进行录音"

        // for (let i = 0; i < this._answerList.length; i++) {
        //     this._answerList[i].destroy();
        // }
        // this._answerList = [];
        // for (let i = 0; i < this.questionData.answerSetting.length; i++) {
        //     let node = new cc.Node();
        //     let la = node.addComponent(cc.Label);
        //     la.string = this.questionData.answerSetting[i];
        //     la.fontSize = 32;
        //     la.lineHeight = 46;
        //     la.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
        //     la.cacheMode = cc.Label.CacheMode.CHAR;
        //     la.verticalAlign=cc.Label.VerticalAlign.TOP;
        //     node.width = this.resultAnswerNode.width;
        //     node.color = cc.color(20, 20, 20, 255);
        //     node.anchorX = 0;
        //     node.anchorY = 1;
        //     node.x = 0;
        //     node.y = 0;
        //     node.parent = this.resultAnswerNode;
        //     this._answerList.push(node);
        // }
        this.questionLabel.string = this.questionData.questionTitle;

        this._isCanPlay = false;
        this._isOperator = false;
        this.lostNode.active = false;
        this.soundLostLabel.node.active = false;
        this.gradeLabel.node.active = false;
        this.soundRank = -2;
        this.answerAudioUrl = "";
        this.answerDuration = 0;
        this.updateUI();
    }
    cbCheckRichText(str: string, worldList: any[]) {
        let colorStr = str;
        let wordData = null;
        for (let i = 0; i < worldList.length; i++) {
            if (str.toLowerCase().indexOf(worldList[i].word) != -1 || str.indexOf(worldList[i].word) != -1) {
                wordData = worldList.splice(i, 1)[0];
                break;
            }

        }
        if (wordData) {
            if (wordData.score <= 30) {
                colorStr = "<color=#FF6647>" + colorStr + "</color>";
            }
            else if (wordData.score > 30 && wordData.score <= 80) {
                colorStr = "<color=#FFBB00>" + colorStr + "</color>";
            }
            else if (wordData.score > 80) {
                colorStr = "<color=#2DCCA2>" + colorStr + "</color>";
            }

        }
        return colorStr;
    }

    creatorRichText(str: string, resultArray: any[]) {
        let cluStr = "";
        let arr = str.split(" ");
        let tempWordList = JSON.parse(JSON.stringify(this.wordList));
        for (let j = 0; j < tempWordList.length; j++) {
            tempWordList[j].score = addListenQuestionSorce(tempWordList[j].score)

        }
        console.log("显示分数:",JSON.stringify(tempWordList));
        for (let i = 0; i < arr.length; i++) {
            if (arr[i] != "") {
                cluStr += this.cbCheckRichText(arr[i], tempWordList) + " ";
            } else {
                cluStr += " "
            }
        }
        let defaultStr = "<color=#141414>" + cluStr + " </color>"
        return defaultStr;
    }



    public reset() {
        this.unschedule(this.updateUI);
        this.initUi();
    }

    public getAnwerIsCorrect(): boolean {
        return true
    }





    updateResult() {

    }
    // update (dt) {}
}
