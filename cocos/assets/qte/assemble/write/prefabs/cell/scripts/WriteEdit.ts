// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { WRITE_TYPE } from "../../../scripts/WriteEnum";
import WriteHP from "./cell/WriteHP";

const { ccclass, property } = cc._decorator;

@ccclass
export default class WriteEdit extends cc.Component {
    @property({ displayName: "汉字模版", type: cc.Prefab })
    H_prefab: cc.Prefab = null
    @property({ displayName: "拼音模版", type: cc.Prefab })
    P_prefab: cc.Prefab = null
    @property({ displayName: "展示区", type: cc.Node })
    content: cc.Node = null

    @property({ displayName: "标题", type: cc.Label })
    label: cc.Label = null

    init(arr: Array<any>, type: WRITE_TYPE) {
        this.label.string = type == WRITE_TYPE.HTOP ? "请写出下列汉字的拼音" : "请写出下列拼音的汉字";
        arr.forEach((item) => {
            let group: cc.Node = new cc.Node("group");
            let layout: cc.Layout = group.addComponent(cc.Layout);
            layout.type = cc.Layout.Type.VERTICAL;
            layout.resizeMode = cc.Layout.ResizeMode.CONTAINER;
            group.parent = this.content;
            //汉字
            let H: cc.Node = cc.instantiate(this.H_prefab);
            H.getComponent(WriteHP).setLabel(item.hanzi)
            H.parent = group;
            H.scale = 1.5

            //拼音
            let P: cc.Node = cc.instantiate(this.P_prefab);
            P.getComponent(WriteHP).setLabel(item.pinyin)
            P.parent = group;
            P.scale = 0.24

        })

    }

    // update (dt) {}
}
