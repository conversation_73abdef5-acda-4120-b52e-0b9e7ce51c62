import Draw from "./cell/Draw";



const { ccclass, property } = cc._decorator;
/**
 * 拼音写汉字的答题页
 */
@ccclass
export default class WriteCell_PtoH extends cc.Component {
    @property({ displayName: "题目0", type: cc.Label })
    timu0: cc.Label = null;
    @property({ displayName: "题目1", type: cc.Label })
    timu1: cc.Label = null;
    @property({ displayName: "汉字模版", type: cc.Prefab })
    H_prefab: cc.Prefab = null


    @property({ displayName: "垃圾桶1", type: cc.Node })
    rebbage0: cc.Node = null;
    @property({ displayName: "垃圾桶2", type: cc.Node })
    rebbage1: cc.Node = null;
    @property(cc.Node)
    draw0Node: cc.Node = null;
    @property(cc.Node)
    draw1Node: cc.Node = null;
    draw0: Draw = null
    draw1: Draw = null


    start() {
        this.rebbage0.on(cc.Node.EventType.TOUCH_START, this.touchRabbage0, this)
        this.rebbage1.on(cc.Node.EventType.TOUCH_START, this.touchRabbage1, this)


    }
    touchRabbage0() {
        this.draw0.clear()

    }
    touchRabbage1() {
        this.draw1.clear()

    }



    /**
     * 初始化拼音写汉字页面
     */
    initPtoH(arrIndex, all, evevtNode) {
        this.draw0 = this.draw0Node.getComponent(Draw);
        this.draw1 = this.draw1Node.getComponent(Draw);
        arrIndex.forEach((index, i) => {
            let shuju = all[index];
            if (shuju) {
                this["draw" + i].node.parent.parent.active = true
                this["timu" + i].string = shuju.pinyin
                if (shuju.hasAnswer) {
                    //示范
                    this["draw" + i].initOnlyShow(shuju)
                } else {
                    //可以手写
                    this["draw" + i].init(index, evevtNode)
                }

                this["rebbage" + i].active = !shuju.hasAnswer

            }
        });

        return [this.draw0, this.draw1]

    }

}
