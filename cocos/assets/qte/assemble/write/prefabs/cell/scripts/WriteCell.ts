import Draw from "./cell/Draw";

import WriteHP from "./cell/WriteHP";

const { ccclass, property } = cc._decorator;
/**
 * 汉字写拼音答题页
 */
@ccclass
export default class WriteCell extends cc.Component {
    @property({ displayName: "题目展示区", type: cc.Node })
    timu_group: cc.Node = null;

    @property({ displayName: "汉字模版", type: cc.Prefab })
    H_prefab: cc.Prefab = null
    @property({ displayName: "拼音模版", type: cc.Prefab })
    P_prefab: cc.Prefab = null

    @property({ displayName: "垃圾桶", type: cc.Node })
    rebbage: cc.Node = null;

    @property({ displayName: "画板", type: Draw })
    draw: Draw = null;




    start() {
        this.rebbage.on(cc.Node.EventType.TOUCH_START, this.touchRabbage, this)

    }
    touchRabbage() {
        this.draw.clear()

    }

    /**
     * 初始化汉字写拼音页面
     * @param currentIndex 当前下标
     * @param all //全部待展示汉字
     */
    initHtoP(currentIndex, all, evevtNode) {
        this.timu_group.removeAllChildren()
        all.forEach((item, i) => {
            let node: cc.Node = cc.instantiate(this.H_prefab);
            node.getComponent(WriteHP).setLabel(item.hanzi, i == currentIndex)
            this.timu_group.addChild(node)
        });

        this.draw.init(currentIndex, evevtNode)
        return this.draw
    }

 

}
