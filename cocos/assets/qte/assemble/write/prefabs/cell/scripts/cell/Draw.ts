
import QTEUtils from "../../../../../../util/QTEUtils";
import { DRAWTYPE, LISTENNAME } from "../../../../scripts/WriteEnum";

const { ccclass, property } = cc._decorator;
/**
 * 名称：单画板组件
 * 步骤：触摸操作传输=》state=》接收解析全量=》重绘
 * LISTENNAME.SETDRAW 传输数据
 * LISTENNAME.DRAW${num} 接收重绘数据
 * 对外:提供垃圾桶清屏功能，在点垃圾桶和重置按钮时调用
 */
@ccclass
export default class Draw extends cc.Component {
    static touchId = null;
    graphics: cc.Graphics = null//画笔
    eventNode: cc.Node = null;//事件承载
    setFun: any;//更改状态的函数
    obj: any = {};//此画板全量数据
    dr = 0.1//斜率阀值
    lineWidth = 14//画笔粗细
    hasAnswer = false;
    lastTime = 0;
    shuju = {}

    _offsetV2: cc.Vec2 = cc.v2(0, 0);
    //同步恢复用
    private clearCore() {
        this.graphics.clear()
    }
    //同步恢复用
    private touchStartCore(pos) {
        this.graphics.moveTo(pos.x, pos.y)
    }
    //同步恢复用
    private touchMoveCore(pos) {
        this.graphics.lineTo(pos.x, pos.y)

        this.graphics.stroke()
        this.graphics.moveTo(pos.x, pos.y)
    }
    // 解析state并绘制
    public doDrawObj(draws: Array<any>) {
        // console.error(draws)
        this.graphics.clear(true)
        draws.forEach((obj) => {
            switch (obj.name) {
                case DRAWTYPE.START:
                    this.touchStartCore(obj.value)
                    break;
                case DRAWTYPE.MOVE:
                    this.touchMoveCore(obj.value)
                    break;
                case DRAWTYPE.CLEAR:
                    this.clearCore()
                    break;
                default:
                    break;
            }
        })
        this.obj.draws = draws;
    }







    //传送状态精简数组 V3
    formatV3(arr: Array<any>): Array<any> {
        if (arr.length >= 3
            && arr[arr.length - 1].name == DRAWTYPE.MOVE
            && arr[arr.length - 2].name == DRAWTYPE.MOVE
            && arr[arr.length - 3].name == DRAWTYPE.MOVE) {
            if (Math.abs(Math.atan2((arr[arr.length - 1].value.y - arr[arr.length - 2].value.y), arr[arr.length - 1].value.x - arr[arr.length - 2].value.x) / 2 * Math.PI
                - Math.atan2((arr[arr.length - 2].value.y - arr[arr.length - 3].value.y), arr[arr.length - 2].value.x - arr[arr.length - 3].value.x) / 2 * Math.PI)
                < this.dr) {
                arr[arr.length - 2] = arr[arr.length - 1];
                arr.pop()
            }
        }
        return arr
    }
    // 推送state的当前画板全量
    private pushDrawObj(draw) {
        switch (draw.name) {
            case DRAWTYPE.CLEAR:
                this.obj.draws = []
                break;
            case DRAWTYPE.START:
                this.obj.draws.push(draw)
                break;
            case DRAWTYPE.MOVE:
                this.obj.draws.push(draw)
                break;
            default:
                break;
        }
        //数据精简
        //   this.obj.draws = this.formatV3(this.obj.draws);
        //获取到当前画板全量，推送到state
        this.setFun(this.obj)


    }
    onLoad(): void {
        this._offsetV2 = cc.v2(this.node.x, this.node.y);
        this.checkParentOffestV2(this.node);
    }
    checkParentOffestV2(node: cc.Node) {
        if (node.parent) {
            this._offsetV2 = this._offsetV2.add(node.parent.getPosition());
            if (node.parent.name != "write") {
                this.checkParentOffestV2(node.parent);
            }
        }

    }
    //初始化，由上级调用，可以开发给玩家手写
    init(index, eventNode) {
        this.node.on(cc.Node.EventType.TOUCH_START, this.touchStart, this)
        this.node.on(cc.Node.EventType.TOUCH_END, this.touchEnd, this)
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.touchEnd, this)
        this.obj.draws = []
        this.obj.id = index;
        this.eventNode = eventNode
        this.graphics = this.node.addComponent(cc.Graphics);
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.lineCap = cc.Graphics.LineCap.ROUND




    }

    //初始化，由上级调用示范，无法手写
    initOnlyShow(shuju) {
        this.shuju = shuju;
        let node: cc.Node = new cc.Node("show");
        let label: cc.Label = node.addComponent(cc.Label);
        label.string = shuju.hanzi
        node.color = cc.color(0, 0, 0, 255)
        label.fontSize = 260;
        label.lineHeight = 260
        node.parent = this.node;
        node.y = -20
        this.hasAnswer = true
    }

    onDestroy() {
        Draw.touchId = null;
    }

    //简易初始化，只提供绘制功能,为结果页服务
    initForEnd(draws) {
        this.obj.draws = []
        this.graphics = this.node.addComponent(cc.Graphics);
        this.graphics.lineWidth = this.lineWidth;
        this.graphics.lineCap = cc.Graphics.LineCap.ROUND
        this.doDrawObj(draws)
    }

    //垃圾桶的持有者调用　
    clear() {
        if (!this.hasAnswer) {
            //传输
            this.pushDrawObj({ name: DRAWTYPE.CLEAR })
        } else {
            this.initOnlyShow(this.shuju)
        }

    }


    //获取本地坐标
    private getPos(e: cc.Event.EventTouch) {
        // let pos: cc.Vec2 = e.getPreviousLocation();
        // pos = this.node.parent.convertToNodeSpaceAR(pos)
        let local = e.getPreviousLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2-this._offsetV2.x, local.y - cc.winSize.height / 2-this._offsetV2.y);
        return { x: Number(pos.x.toFixed(2)), y: Number(pos.y.toFixed(2)) };
    }

    //判断是否越界
    private check(pos) {
        let re = true
        if (Math.abs(pos.x) > this.node.width / 2 || Math.abs(pos.y) > this.node.height / 2) {
            re = false
        }
        return re;
    }

    //自用
    private touchEnd(e: cc.Event.EventTouch) {
        Draw.touchId = null;
        //松手、界外则取消move监听
        this.node.off(cc.Node.EventType.TOUCH_MOVE, this.touchMove, this)
    }
    //自用
    private touchStart(e: cc.Event.EventTouch) {
        if (Draw.touchId != e.getID()) {
            //点击后开始 监听move
            this.node.on(cc.Node.EventType.TOUCH_MOVE, this.touchMove, this)
            //传输

            this.pushDrawObj({ name: DRAWTYPE.START, value: this.getPos(e) })
            Draw.touchId = e.getID()
        }

    }
    //自用
    private touchMove(e: cc.Event.EventTouch) {
        let timeNow = Date.now();
        if (timeNow - this.lastTime > 50) {
            if (Draw.touchId == e.getID()) {
                let pos = this.getPos(e);
                if (!this.check(pos)) {
                    //划出则取消move监听
                    Draw.touchId = null;
                    this.node.off(cc.Node.EventType.TOUCH_MOVE, this.touchMove, this)
                    return
                }
                this.pushDrawObj({ name: DRAWTYPE.MOVE, value: pos })
                this.lastTime = timeNow;
            }

        }

    }
}
