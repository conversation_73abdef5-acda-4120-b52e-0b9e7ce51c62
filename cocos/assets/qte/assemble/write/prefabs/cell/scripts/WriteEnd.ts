// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { WRITE_TYPE } from "../../../scripts/WriteEnum";
import WriteHP from "./cell/WriteHP";



const { ccclass, property } = cc._decorator;
/**
 * 结果页（拼音汉字共用）
 */
@ccclass
export default class WriteEnd extends cc.Component {
    @property({ displayName: "题目区", type: cc.Node })
    timuContent: cc.Node = null;
    @property({ displayName: "玩家答案区", type: cc.Node })
    answerContent: cc.Node = null;
    @property({ displayName: "正确答案区", type: cc.Node })
    rightAnswerContent: cc.Node = null;
    @property({ displayName: "汉字模版", type: cc.Prefab })
    H_prefab: cc.Prefab = null
    @property({ displayName: "大汉字模版", type: cc.Prefab })
    H_prefab_big: cc.Prefab = null
    @property({ displayName: "拼音模版", type: cc.Prefab })
    P_prefab: cc.Prefab = null

    init(writeArray: Array<any>, type: WRITE_TYPE, state) {
        this.timuContent.removeAllChildren()
        this.rightAnswerContent.removeAllChildren()
        this.answerContent.removeAllChildren()
        //题目文字和正确答案录入--汉字写拼音
        if (type == WRITE_TYPE.HTOP) {
            writeArray.forEach((item, i) => {
                //题目
                let node = cc.instantiate(this.H_prefab);
                let HP: WriteHP = node.getComponent(WriteHP);
                HP.setLabel(item.hanzi)
                node.parent = this.timuContent;
                //正确答案
                let node2 = cc.instantiate(this.P_prefab);
                let HP2: WriteHP = node2.getComponent(WriteHP);
                HP2.setLabel(item.pinyin);
                node2.scale = 0.2;
                node2.parent = this.rightAnswerContent;
                this.rightAnswerContent.getComponent(cc.Layout).spacingX = 60
                //玩家答案
                let node3 = cc.instantiate(this.P_prefab);
                node3.parent = this.answerContent;
                let HP3: WriteHP = node3.getComponent(WriteHP);
                HP3.doDraw(state["draw" + i]);
                node3.scale = 0.2;
                this.answerContent.getComponent(cc.Layout).spacingX = 60

            })
        } else if (type == WRITE_TYPE.PTOH) {
            writeArray.forEach((item, i) => {
               
                //正确答案
                let node2 = cc.instantiate(this.H_prefab_big);
                let HP2: WriteHP = node2.getComponent(WriteHP);
                HP2.setLabel(item.hanzi);
                node2.parent = this.rightAnswerContent;
                node2.scale = 0.3
                this.rightAnswerContent.getComponent(cc.Layout).spacingX = 200
             
                if (!item.hasAnswer) {
                    //玩家答案
                    let node3 = cc.instantiate(this.H_prefab_big);
                    node3.parent = this.answerContent;
                    let HP3: WriteHP = node3.getComponent(WriteHP);
                    HP3.doDraw(state["draw" + i]);
                    node3.scale = 0.3
                    //拼音题目
                    HP3.setPinyin(item.pinyin)
                    this.answerContent.getComponent(cc.Layout).spacingX = 200;
                } else {
                  
                    //玩家答案
                    let node3 = cc.instantiate(this.H_prefab_big);
                    node3.parent = this.answerContent;
                    let HP3: WriteHP = node3.getComponent(WriteHP);
                    HP3.setLabel(item.hanzi)
                    node3.scale = 0.3
                    //拼音题目
                    HP3.setPinyin(item.pinyin)
                    this.answerContent.getComponent(cc.Layout).spacingX = 200;
                }




            })
        }
    }
}
