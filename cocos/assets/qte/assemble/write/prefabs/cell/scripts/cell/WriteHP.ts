import Draw from "./Draw";

const { ccclass, property } = cc._decorator;
/**
 * 简易版汉字和拼音展示组件
 * 题干展示和结果页重绘
 */
@ccclass
export default class WriteHP extends cc.Component {
    label: cc.Label = null;//文字
    bg: cc.Sprite = null;//背景
    @property(cc.SpriteFrame)
    touchSp: cc.SpriteFrame = null;
    /**
     * 设置拼音文字(如果布局中存在)
     */
    setPinyin(str) {
        let pinyinNode=this.node.getChildByName("pinyin");
        if(pinyinNode){
            let pinyin:cc.Label=pinyinNode.getComponent(cc.Label);
            pinyin.string=str
        }

    }
    //更改label展示
    setLabel(str, iscurr = false) {
        this.label = this.node.getChildByName("label").getComponent(cc.Label)
        this.bg = this.node.getChildByName("bg").getComponent(cc.Sprite)
        this.label.string = str;
        if (iscurr) {
            this.label.node.color = cc.color(255, 255, 255, 255)
            this.bg.spriteFrame = this.touchSp;

        }
    }

    //简易重绘
    doDraw(draws) {
        let resultDraw = new cc.Node("resultDraw");
        let draw: Draw = resultDraw.addComponent(Draw)
        resultDraw.parent = this.node;
        draw.initForEnd(draws)


    }


}
