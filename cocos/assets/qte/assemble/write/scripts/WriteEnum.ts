//同步恢复数据格式
export interface DRAW {
    name: string,
    value: any
}
//传递对象格式
export interface DRAW_OBJ {
    id: any,
    draws: Array<DRAW>
}
//手写组件命名枚举
export enum DRAWNAME {
    WRITE = "write",
}
//监听派发枚举
export enum LISTENNAME {
    DRAW_0 = "draw0",
    DRAW_1 = "draw1",
    DRAW_2 = "draw2",
    DRAW_3 = "draw3",
    DRAW = "draw",
    SETDRAW = "setDraw",
    PAGEINDEXPUSH="pageIndexPush",
    PAGEINDEXDO="pageIndexDo"

}
//同步恢复数据画笔类型
export enum DRAWTYPE {
    START = "1",
    MOVE = "2",
    CLEAR = "3"
}
export enum WRITE_TYPE {
    "HTOP" = 1,//汉字写拼音
    "PTOH" = 0//拼音写汉字
}