import BaseComponent from "../../core/BaseComponent";
import Draw from "../prefabs/cell/scripts/cell/Draw";
import WriteCell from "../prefabs/cell/scripts/WriteCell";
import WriteCell_PtoH from "../prefabs/cell/scripts/WriteCell_PtoH";
import WriteEdit from "../prefabs/cell/scripts/WriteEdit";
import WriteEnd from "../prefabs/cell/scripts/WriteEnd";
import { LISTENNAME, WRITE_TYPE } from "./WriteEnum";
const { ccclass, property } = cc._decorator;
/**
 * 手写题组件
 */
@ccclass
export default class WriteCompont extends BaseComponent {
    //--------------ui区
    @property({ displayName: "编辑侧布局prefab", type: cc.Prefab })
    Edit: cc.Prefab = null
    @property({ displayName: "汉字写拼音布局prefab", type: cc.Prefab })
    HtoP: cc.Prefab = null
    @property({ displayName: "拼音写汉字布局prefab", type: cc.Prefab })
    PtoH: cc.Prefab = null
    @property({ displayName: "结果页布局prefab", type: cc.Prefab })
    End: cc.Prefab = null
    @property({ displayName: "答题区", type: cc.Node })
    content: cc.Node = null
    @property({ displayName: "结果区", type: cc.Node })
    result: cc.Node = null
    @property({ displayName: "编辑侧展示区", type: cc.Node })
    editContent: cc.Node = null
    @property({ displayName: "上一页", type: cc.Node })
    pre: cc.Node = null
    @property({ displayName: "下一页", type: cc.Node })
    next: cc.Node = null
    //-------------数据区
    public writeArray: any = []//答题区数据
    public type: WRITE_TYPE;//整体类型
    public currentIdex = 0//当前在第几页答题页
    writeEnd: WriteEnd = null//结束页
    draw0: Draw = null
    draw1: Draw = null
    draw2: Draw = null
    draw3: Draw = null

    public changeProperties(key: string, data: any) {
        if (key == "writeType") {
            //类型变更
            this.type = data == 0 ? WRITE_TYPE.PTOH : WRITE_TYPE.HTOP;
        }
        else if (key == "writeArray") {
            //内容变更
            this.writeArray = data;
        }
        this.initChangeEdit()

    }
    public changeSkine(path: string, param?: any) {

    }

    setFunction(fun: any) {
        this.content.children.forEach((item) => {
            if (this.type == WRITE_TYPE.HTOP) {
                item.getComponent(WriteCell).draw.setFun = fun;
            } else if (this.type == WRITE_TYPE.PTOH) {
                item.getComponent(WriteCell_PtoH).draw0.setFun = fun;
                if (item.getComponent(WriteCell_PtoH).draw1.graphics) {
                    item.getComponent(WriteCell_PtoH).draw1.setFun = fun;
                }

            }
        })
    }


    public initComponent(data?: any) {
        //  data.isEditor=true
        this.type = data.properties.writeType == 0 ? WRITE_TYPE.PTOH : WRITE_TYPE.HTOP;
        this.writeArray = data.properties.writeArray;
        this.result.active = false
        if (data.isEditor) {
            this.initChangeEdit()
        } else {
            this.initChangeCore()
        }



    }

    /**
     * 编辑侧初始化数据
     */
    private initChangeEdit() {
        this.editContent.removeAllChildren();
        this.editContent.active = true;
        let editNode = cc.instantiate(this.Edit);
        this.editContent.addChild(editNode);
        let writeEdit: WriteEdit = editNode.getComponent(WriteEdit);
        writeEdit.init(this.writeArray, this.type);

    }
    /**
     *预览侧初始化数据
     */
    private initChangeCore() {
        this.editContent.active = false;
        this.result.active = false;
        let currentprefab: cc.Prefab = this.type == WRITE_TYPE.PTOH ? this.PtoH : this.HtoP;
        this.content.removeAllChildren();
        if (this.type == WRITE_TYPE.HTOP) {
            //汉字写拼音
            this.writeArray.forEach((item, i) => {
                let node: cc.Node = cc.instantiate(currentprefab)
                this["draw" + i] = node.getComponent(WriteCell).initHtoP(i, this.writeArray, this.node)
                this.content.addChild(node);
            });
        } else {
            //拼音写汉字
            if (this.writeArray.length <= 2) {
                //需要一页
                let node_page: cc.Node = cc.instantiate(currentprefab)
                let arr = node_page.getComponent(WriteCell_PtoH).initPtoH([0, 1], this.writeArray, this.node);
                this.draw0 = arr[0];
                this.draw1 = arr[1];
                this.content.addChild(node_page);
            } else if (this.writeArray.length <= 4) {
                //需要两页
                let node_page: cc.Node = cc.instantiate(currentprefab)
                let arr01 = node_page.getComponent(WriteCell_PtoH).initPtoH([0, 1], this.writeArray, this.node)
                this.content.addChild(node_page);
                this.draw0 = arr01[0];
                this.draw1 = arr01[1];
                let node_page1: cc.Node = cc.instantiate(currentprefab)
                let arr23 = node_page1.getComponent(WriteCell_PtoH).initPtoH([2, 3], this.writeArray, this.node)
                this.content.addChild(node_page1);
                this.draw2 = arr23[0];
                this.draw3 = arr23[1];
            }

        }
    }



    //对外---提供当前真实答题页面数量
    public getCountViews() {
        return this.content.childrenCount;

    }
    //对外---初始化当前答题页面指向下标
    public initPageIndex() {
        this.result.active = false
        this.currentIdex = 0
        this.setPageIndex(this.currentIdex);
    }
    //对外---初始化当前结果页是否展示
    public showEnd(boo: boolean, state: any) {

        if (!boo) {
            //关闭
            this.result.removeAllChildren();
            this.result.active = false
        } else {
            //打开
            this.writeEnd = cc.instantiate(this.End).getComponent(WriteEnd)
            this.writeEnd.init(this.writeArray, this.type, state)
            this.writeEnd.node.parent = this.result;
            this.result.active = true
        }

    }
    //对外---初始化当前所有画板
    public clear() {
        this.content.children.forEach((item) => {
            if (this.type == WRITE_TYPE.HTOP) {
                item.getComponent(WriteCell).draw.clear()
            } else if (this.type == WRITE_TYPE.PTOH) {

                item.getComponent(WriteCell_PtoH).draw0.clear()
                if (item.getComponent(WriteCell_PtoH).draw1.graphics) {
                    item.getComponent(WriteCell_PtoH).draw1.clear()
                }

            }
        })
    }











    onLoad() {
        this.next.on(cc.Node.EventType.TOUCH_START, this.touchBtn, this)
        this.pre.on(cc.Node.EventType.TOUCH_START, this.touchBtn, this)
        this.node.on(LISTENNAME.PAGEINDEXDO, this.doPageIndex, this)

    }
    private touchBtn(e: cc.Event.EventTouch) {
        let name = e.target.name
        console.log("WKK点击左右按钮", name)
        this.currentIdex += name == "pre" ? -1 : 1;
        if (this.currentIdex < 0) {
            this.currentIdex = 0
        } else if (this.currentIdex >= this.content.childrenCount) {
            this.currentIdex = this.content.childrenCount - 1;
        }
        this.setPageIndex(this.currentIdex)

    }
    private setPageIndex(currentIdex) {
        this.node.emit(LISTENNAME.PAGEINDEXPUSH, currentIdex)

    }
    private doPageIndex(currentIdex) {
        this.currentIdex = currentIdex
        if (currentIdex == 0) {
            //最前
            this.noVisNextPre(this.pre, this.getCountViews() == 1 ? this.next : null)
        } else if (currentIdex == this.content.childrenCount - 1) {
            //最后
            this.noVisNextPre(this.next)
        } else {
            this.noVisNextPre(null)
        }
        this.content.children.forEach((item, i) => {
            if (i == currentIdex) {
                item.active = true
            } else {
                item.active = false
            }
        })
    }
    private noVisNextPre(current, another = null) {
        this.pre.active = true
        this.next.active = true
        if (current) {
            current.active = false
        }
        if (another) {
            another.active = false
        }
    }

}

