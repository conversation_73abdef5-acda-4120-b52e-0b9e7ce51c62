/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2022-02-11 14:31:08
 * @modify date 2022-02-11 14:31:08
 * @desc [description]
 */

import BaseComponent from "../core/BaseComponent";
import ComponentFactory from "../core/ComponentFactory";

const { ccclass, property } = cc._decorator;

enum OperateType {
    NULL = 0, PEN, CANCEL
}

@ccclass
export default class DrawControl extends BaseComponent {

    @property(cc.Node)
    selectedNode: cc.Node = null;
    @property(cc.Button)
    cancelBtn: cc.Button = null;

    private _indexStack: number[];
    private _operateType: OperateType;
    private _getCompByIdFunc: Function;
    private _getCompByTypeFunc: Function;

    private _style: number;

    onLoad() {

    }

    start() {

    }

    public initComponent(data?: any) {
        this._style = data.properties.style;
        this.init();
    }

    public changeProperties(key: string, data: any) {
        if (key == "style" && this._style != data) {
            this._style = data;
            this.updateSkin();
        }
    }

    private async updateSkin() {
        let names = ["bg", "cancel", "pen", "selected"];
        for (let i = 0; i < names.length; i++) {
            let sprite = this.node.getChildByName(names[i]).getComponent(cc.Sprite);
            let path = `assemble/drawControl/resources/${this._style}/${names[i]}`;
            let texture = await this.loadTexture(ComponentFactory.getInstance().bundle, path);
            if (!cc.isValid(texture,true)) {
                continue;
            }
            // if (texture.setPremultiplyAlpha) {
            //     texture.setPremultiplyAlpha(true);
            // }
            sprite.trim = false;
            sprite.srcBlendFactor = cc.macro.BlendFactor.ONE;
            sprite.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
            sprite.spriteFrame = new cc.SpriteFrame(texture);
        }
    }  
    /**
     * 加载本地图片资源
     * @param path 图片路径
     * @returns 
     */
    private loadTexture(bundle, path: string): Promise<cc.Texture2D> {
        return new Promise<cc.Texture2D>((resolve, reject) => {
            bundle.load(path, cc.Texture2D, (err, res) => {
                resolve(res);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err)
            return null;
        });
    }


    public changeSkine(path: string, param?: any) {

    }

    public init() {
        this._operateType = OperateType.NULL;
        this.cancelBtn.interactable = false;
        this._indexStack = [];
        this.updateSkin();
        this.updateSelected();
    }

    public reset() {
        this.init();
    }

    updateSelected(index: number = -1) {
        this.selectedNode.active = this._operateType != OperateType.NULL;
        switch (this._operateType) {
            case OperateType.PEN:
                this.selectedNode.y = 45;
                break;
            case OperateType.CANCEL:
                this.selectedNode.y = -45;
                break;
        }
        if (this._getCompByTypeFunc) {
            let comps = this._getCompByTypeFunc("DrawCmpt");
            console.log("comps", comps)
              //如果未传入index，则认为是画笔组件的点击事件，否则认为是物理作图题组件的点击事件。
            if (index != -1) {
                let curComp = comps[index];
                curComp.canDraw = this._operateType == OperateType.PEN;
                if (!curComp.getDrawStartFunc()) {
                    curComp.setDrawStartFunc(this.drawStart.bind(this, curComp.id));
                }
            }
            else {
                comps.forEach(comp => {
                    comp.canDraw = this._operateType == OperateType.PEN;
                    if (!comp.getDrawStartFunc()) {
                        comp.setDrawStartFunc(this.drawStart.bind(this, comp.id));
                    }
                });
            }
        }
    }

    private drawStart(id: number) {
        this._indexStack.push(id);
        if (this._indexStack.length > 500) {
            this._indexStack.shift();
        }
        if (!this.cancelBtn.interactable) {
            this.cancelBtn.interactable = true;
        }
    }

    public setGetCompByIdFunc(func: Function) {
        this._getCompByIdFunc = func;
    }

    public setGetCompByTypeFunc(func: Function) {
        this._getCompByTypeFunc = func;
    }
    //仅供物理作图题与画笔组件使用
    public onPenClick(index: number = -1) {
        switch (this._operateType) {
            case OperateType.NULL:
            case OperateType.CANCEL:
                this._operateType = OperateType.PEN;
                break;
            case OperateType.PEN:
                this._operateType = OperateType.NULL;
                break;
        }
        //区分物理作图题与画笔组件的点击事件，传入index表示是物理作图题的点击事件。
        if(typeof index == "number")
            this.updateSelected(index);
        else
            this.updateSelected();
    }
    //仅供物理作图题与画笔组件使用
    public onCancelClick() {
        this._operateType = OperateType.CANCEL;
        this.updateSelected();
        let id = this._indexStack.pop();
        if (this._indexStack.length == 0) {
            this.cancelBtn.interactable = false;
            this.selectedNode.active = false;
        }
        if (id == undefined) {
            return;
        }
        if (this._getCompByIdFunc) {
            let comp = this._getCompByIdFunc(id);
            comp.back();
        }
    }
    // 修改画笔颜色
    public setDrawColor(color: string[]) {
        if (this._getCompByTypeFunc) {
            let comps = this._getCompByTypeFunc("DrawCmpt");
            comps.forEach((comp,i) => {
                comp.setGraphicColor(color[i]);
            });
            
        }
    }
    //物理作图题onStartAnswering之后调用 清空画板 防止状态恢复
    public resetCmpt() {
        if (this._getCompByTypeFunc) {
            let comps = this._getCompByTypeFunc("DrawCmpt");
            comps.forEach(comp => {
                comp.reset();
            });
        }
    }

    //获取所有绘图组件绘制线段的总段数
    public getDrawLineLength() {
        let allDrawValue = 0;
        if (this._getCompByTypeFunc) {
            let comps = this._getCompByTypeFunc("DrawCmpt");
            comps.forEach(comp => {
                allDrawValue += comp.getStatePathsLength();
            });
        }
        return allDrawValue;
    }
    //获取绘图组件是否正在绘制线段
    public getDrawCmptIsTouching() {
        if (this._getCompByTypeFunc) {
            let comps = this._getCompByTypeFunc("DrawCmpt");
            for (let i = 0; i < comps.length; i++) {
                if (comps[i].getIsTouching()) {
                    return true;
                }
            }
        }
        return false;
    }
     //关闭画笔功能
     public closePen() {
        this._operateType = OperateType.NULL;
        this.updateSelected();
    }
}
