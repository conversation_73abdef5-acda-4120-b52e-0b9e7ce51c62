import BaseComponent from "../../core/BaseComponent";


const { ccclass, property } = cc._decorator;
export enum ModeType {
    onStart = -1,
    ready = 0,
    listen = 1,
    answer = 2,
    result = 3
}
enum TimeName {
    ready = "读题时间：",
    listen = "听音时间：",
    answer = "答题时间：",
    result = "-"
}


@ccclass
export default class ListenAnswerComponent extends BaseComponent {
    _setStateFunc: Function = null;
    public questionData: any = null;

    @property(cc.Label)
    questionLabel: cc.Label = null;

    @property(cc.Node)
    qViewNode: cc.Node = null;



    @property(cc.Label)
    tipsDecLabel: cc.Label = null;

    @property(cc.Label)
    timeTitleLabel: cc.Label = null;

    @property(cc.Label)
    resultTipsLabel: cc.Label = null;

    @property(cc.Node)
    resultAnswerNode: cc.Node = null;


    @property(cc.Node)
    questionNode: cc.Node = null;


    @property(cc.Node)
    bottom: cc.Node = null;

    @property(cc.Node)
    result: cc.Node = null;

    @property(cc.Label)
    timeLabel: cc.Label = null;

    @property(cc.Label)
    soundLostLabel: cc.Label = null;


    @property(cc.SpriteFrame)
    subMSp: cc.SpriteFrame[] = [];

    @property(cc.Sprite)
    subMBt: cc.Sprite = null;

    @property(cc.Node)
    orNode: cc.Node = null;

    @property(cc.AudioClip)
    dingAudio: cc.AudioClip = null;

    @property(cc.Label)
    gradeLabel: cc.Label = null;
    @property(cc.Node)
    lostNode: cc.Node = null;

    _maxQuesctionW: number = 886;
    _mode: ModeType = ModeType.ready;
    _countNum: number = 0;
    _isCanPlay: boolean = false;
    soundRank: number = -2;
    answerAudioUrl: string = "";
    answerDuration: number = 0;
    _isOperator: boolean = false;
    _answerList: cc.Node[] = [];
    public changeProperties(key: string, data: any) {
        if (key == "question") {
            this.questionData = data;
            this.initComponent(null);
        }
    }
    public changeSkine(path: string, param?: any) {
    }
    start() {

    }

    public setOpenator() {
        this._isOperator = true;
    }

    public initComponent(data?: any) {
        if (data) {
            this.questionData = data.properties.question;
        }
        console.log(data);
        this.initUi();
    }
    get mode(): ModeType {
        return this._mode
    }
    set mode(val: ModeType) {
        this._mode = val;
        this._setStateFunc && this._setStateFunc('changeMode', this._mode);
        if (this._mode == ModeType.result) {
            this.questionNode.active = false;
            this.bottom.active = false;
            this.result.active = true;
            this.result.parent = this.node.parent.parent;
            this.result.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        else {
            this.questionNode.active = true;
            this.bottom.active = true;
            this.result.active = false;
        }

    }
    public startReady() {
        this.mode = ModeType.onStart;
    }
    startRead() {
        this.mode = ModeType.ready;
        this._countNum = this.questionData.lookTime;
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }

    startPlaySound() {
        this.playDingAudio();
        this.mode = ModeType.listen;
        this.updateUI();
    }

    startAnswer() {
        this.playDingAudio();
        this.scheduleOnce(() => {
            this.mode = ModeType.answer;
            this._isCanPlay = false;
            this._countNum = this.questionData.answerTime;
            this.updateUI();
        }, 1);
    }
    playDingAudio() {
        this.playAudio(this.dingAudio, false, () => {
        });
    }
    startTouchAnswer() {
        this.unschedule(this.updateUI);
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this.updateUI();
    }

    startUpdateAnswerUi() {
        this.unschedule(this.updateUI);
        this._countNum = this.questionData.answerTime;
        this._isCanPlay = false;
        this._isOperator = false;
        this.updateUI();
        this._isCanPlay = true;
        this.schedule(this.updateUI, 1);
    }
    startPauseAnwer() {
        this.unschedule(this.updateUI);
    }
    startEndAnwer() {
        this.unschedule(this.updateUI);
        this._isCanPlay = false;
        this._countNum = this.questionData.answerTime;
        this.updateUI();
    }

    startResult() {
        if (this._isOperator && this.answerAudioUrl ) {
            this.mode = ModeType.result;
            this._setStateFunc("submit", null);
            this.updateUI();
            this.scheduleOnce(() => {
                this.resultTipsLabel.node.parent.height = this.resultTipsLabel.node.height + 15;
                this.resultAnswerNode.parent.height = this.resultAnswerNode.height + 52;
            }, 0.1);
        } else {
            this.showToast(`暂未生成录音，请答题后再提交`);
        }
    }
    starResultEnd(isPassive:boolean=false) {
        if ((this._isOperator && this.answerAudioUrl )||( this._isOperator&&isPassive)) {
            this.mode = ModeType.result;
            this.unschedule(this.unschedule);
            this.updateUI();
            this.scheduleOnce(() => {
                this.resultTipsLabel.node.parent.height = this.resultTipsLabel.node.height + 15;
                this.resultAnswerNode.parent.height = this.resultAnswerNode.height + 52;
            }, 0.1)
        }
    }


    updateUI() {
        if (this._isCanPlay) {
            this._countNum--;
            if (this._countNum < 0) {
                this._countNum = 0
            }
        }
        let muit = parseInt(this._countNum / 60 + "") + "";;
        let sec = parseInt(this._countNum % 60 + "") + "";
        if (muit.length == 1) {
            muit = "0" + muit;
        }
        if (sec.length == 1) {
            sec = "0" + sec;
        }

        let timeStr = muit + ":" + sec;
        if (this.mode == ModeType.ready || this.mode == ModeType.onStart) {
            this.timeTitleLabel.string = TimeName.ready;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            this.tipsDecLabel.string = "现在，你有" + this.questionData.lookTime + "秒钟的时间阅读这道小题。"
            if (this._countNum == 0) {
                this.unschedule(this.updateUI);
                this.timeLabel.string = '- -';
                this.startPlaySound();
            }
            this.subMBt.spriteFrame = this.subMSp[1];

        } else if (this.mode == ModeType.listen) {
            this.timeTitleLabel.string = TimeName.listen;
            this.tipsDecLabel.string = "现在，请听音频。"
            // this.timeLabel.node.parent.active = false;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = '- -';
            this.subMBt.spriteFrame = this.subMSp[1];
        }

        else if (this.mode == ModeType.answer) {
            this.timeTitleLabel.string = TimeName.answer;
            this.tipsDecLabel.string = "现在,请在" + this.questionData.answerTime + "秒钟内完成作答";
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = timeStr;
            // this.timeLabel.string = '- -';
            if (this._isOperator && this.answerAudioUrl ) {
                this.subMBt.spriteFrame = this.subMSp[0];
            } else {
                this.subMBt.spriteFrame = this.subMSp[1];
            }

        }
        else if (this.mode == ModeType.result) {
            this.timeTitleLabel.string = TimeName.result;
            this.timeLabel.node.parent.active = true;
            this.timeLabel.string = '- -';
            this.subMBt.spriteFrame = this.subMSp[0];
            this.getRankSp()
        }
    }

    getRankSp() {

        if (typeof this.soundRank == 'number') {
            if (this.soundRank >= 0 && this.soundRank <= 100) {
                this.gradeLabel.string = this.soundRank + "";
                this.gradeLabel.node.active = true;
                this.lostNode.active = false;
            }
            else {
                this.soundRank = -1;
                this.lostNode.active = true;
                this.gradeLabel.node.active = false;
            }

        } else {
            this.soundRank = -1;
            this.lostNode.active = true;
            this.gradeLabel.node.active = false;
        }

        if (this.answerAudioUrl) {
            this.soundLostLabel.node.active = false;
        } else {
            this.soundLostLabel.node.active = true;
        }

    }

    public setStateFunc(fun: Function) {
        this._setStateFunc = fun;
        this._setStateFunc("setAnswerBase", null);
    }

    initUi() {
        this.mode = ModeType.onStart;
        this._countNum = this.questionData.lookTime;
        this._isCanPlay = false;
        this.resultTipsLabel.string = this.questionData.questionTitle;
        for (let i = 0; i < this._answerList.length; i++) {
            this._answerList[i].destroy();
        }
        this._answerList = [];
        for (let i = 0; i < this.questionData.answerSetting.length; i++) {
            let node = new cc.Node();
            let la = node.addComponent(cc.Label);
            la.string = this.questionData.answerSetting[i];
            la.fontSize = 32;
            la.lineHeight = 46;
            la.overflow = cc.Label.Overflow.RESIZE_HEIGHT;
            la.cacheMode = cc.Label.CacheMode.CHAR;
            la.verticalAlign=cc.Label.VerticalAlign.TOP;
            node.width = this.resultAnswerNode.width;
            node.color = cc.color(20, 20, 20, 255);
            node.anchorX = 0;
            node.anchorY = 1;
            node.x = 0;
            node.y = 0;
            node.parent = this.resultAnswerNode;
            this._answerList.push(node);
        }
        this.questionLabel.string = this.questionData.questionTitle;

        this._isCanPlay = false;
        this._isOperator = false;
        this.lostNode.active = false;
        this.soundLostLabel.node.active = false;
        this.gradeLabel.node.active = false;
        this.soundRank = -2;
        this.answerAudioUrl = "";
        this.answerDuration = 0;
        this.updateUI();
    }


    public reset() {
        this.unschedule(this.updateUI);
        this.initUi();
    }

    public getAnwerIsCorrect(): boolean {
        return true
    }





    updateResult() {

    }
    // update (dt) {}
}
