declare namespace assemble {

    export class SpecialComponentType {
        static CmptRecord = 'voice'; // 录音组件
        static KeyBord = 'keyboard'; // 键盘组件
        static MatchBoard = 'matchboard'; // 火柴组件
        static Counter = 'counter'; // 计数组件 
        static Write = 'write'; // 手写组件 
        static Clock = "clock" // 钟表组件
        static Tangram = "tangram" // 七巧板组件
        static Brush = "brush"; // 画笔组件
    }
    export class factory {
        static async createObject(type: SpecialComponentType, data: any, bundle: cc.AssetManager.Bundle): Promise<qte.BaseComponent>;
    }
    
}