/*
 * @FilePath     : /sdk/assets/qte/assemble/realTimeRecord/RTREngine.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

const successMock: any = {
    status: 0,
    segment: 0,
    result: {
        hypotheses: [
            {
                transcript:
                    '{"status": 0, "wordList": [{"start": 1350, "score": 0, "word": "\\u997a", "phoneList": [{"end": 1440, "start": 1350, "phone": "j_B", "pitch": 400.0, "prob": 0.99911, "best": "j"}, {"end": 1530, "start": 1440, "phone": "y_I", "pitch": 400.0, "prob": 0.999689, "best": "y"}, {"end": 1620, "start": 1530, "phone": "a3_I", "pitch": 400.0, "prob": 0.912876, "best": "a3"}, {"end": 1710, "start": 1620, "phone": "ww_E", "pitch": 400.0, "prob": 0.996446, "best": "ww"}], "oovFlag": false, "end": 1710, "phoneNum": 4, "prob": 0.97703025}, {"start": 1710, "score": 90, "word": "\\u5b50", "phoneList": [{"end": 1830, "start": 1710, "phone": "z_B", "pitch": 400.0, "prob": 0.582297, "best": "z"}, {"end": 1890, "start": 1830, "phone": "ii1_E", "pitch": 400.0, "prob": 0.575717, "best": "ii1"}], "oovFlag": false, "end": 1890, "phoneNum": 2, "prob": 0.579007}, {"start": 1890, "score": 86.29530000000001, "word": "\\u7684", "phoneList": [{"end": 1950, "start": 1890, "phone": "d_B", "pitch": 400.0, "prob": 0.997657, "best": "d"}, {"end": 2040, "start": 1950, "phone": "e1_E", "pitch": 400.0, "prob": 0.728249, "best": "e1"}], "oovFlag": false, "end": 2040, "phoneNum": 2, "prob": 0.8629530000000001}, {"start": 2040, "score": 100, "word": "\\u76ae", "phoneList": [{"end": 2160, "start": 2040, "phone": "p_B", "pitch": 400.0, "prob": 0.997583, "best": "p"}, {"end": 2310, "start": 2160, "phone": "i2_E", "pitch": 400.0, "prob": 0.998049, "best": "i2"}], "oovFlag": false, "end": 2310, "phoneNum": 2, "prob": 0.997816}, {"start": 2310, "score": 99.91319999999999, "word": "\\u80a4", "phoneList": [{"end": 2430, "start": 2310, "phone": "f_B", "pitch": 400.0, "prob": 0.99986, "best": "f"}, {"end": 2700, "start": 2430, "phone": "u1_E", "pitch": 400.0, "prob": 0.998404, "best": "u1"}], "oovFlag": false, "end": 2700, "phoneNum": 2, "prob": 0.9991319999999999}, {"start": 2790, "score": 99.26336666666667, "word": "\\u6ed1", "phoneList": [{"end": 2910, "start": 2790, "phone": "h_B", "pitch": 400.0, "prob": 0.998008, "best": "h"}, {"end": 3000, "start": 2910, "phone": "w_I", "pitch": 400.0, "prob": 0.989766, "best": "w"}, {"end": 3150, "start": 3000, "phone": "a2_E", "pitch": 400.0, "prob": 0.990127, "best": "a2"}], "oovFlag": false, "end": 3150, "phoneNum": 3, "prob": 0.9926336666666667}, {"start": 3150, "score": 99.8169, "word": "\\u6ed1", "phoneList": [{"end": 3270, "start": 3150, "phone": "h_B", "pitch": 400.0, "prob": 0.999524, "best": "h"}, {"end": 3360, "start": 3270, "phone": "w_I", "pitch": 400.0, "prob": 0.997683, "best": "w"}, {"end": 3540, "start": 3360, "phone": "a2_E", "pitch": 400.0, "prob": 0.9973, "best": "a2"}], "oovFlag": false, "end": 3540, "phoneNum": 3, "prob": 0.998169}, {"start": 3540, "score": 49.972619, "word": "\\u7684", "phoneList": [{"end": 3660, "start": 3540, "phone": "d_B", "pitch": 400.0, "prob": 0.999113, "best": "d"}, {"end": 3870, "start": 3660, "phone": "e4_E", "pitch": 400.0, "prob": 0.00033938, "best": "nn"}], "oovFlag": false, "end": 3870, "phoneNum": 2, "prob": 0.49972619}], "accurate_score": 100.0, "bos_id": "96df4ace-6864-4567-bdec-18100a0159be.mp3", "audio_url": "http://172.26.67.156:8888/audio/20210309/zou_peng/96/96df4ace-6864-4567-bdec-18100a0159be", "source_score": 86.33083883333333, "bos_bucket": "zyb-speech", "online_audio_url": "http://bj.bcebos.com/zyb-speech/96df4ace-6864-4567-bdec-18100a0159be.mp3?authorization=bce-auth-v1%2Ff2d16318b2914fd1b6ff3ca5d4cd5ebd%2F2021-03-09T08%3A41%3A29Z%2F31536000%2F%2F7a0b1c2c2b5366079c5c38221782b04978dc963afa0c85cb2aefdbbdc9d81c57", "id": "96df4ace-6864-4567-bdec-18100a0159be", "fluency_score": 93.4066, "score": 100.0, "length_ms": 4560, "token_id": "96df4ace-6864-4567-bdec-18100a0159be", "msg": "OK", "integrity_score": 100.0, "online_audio_url_mp3": "http://bj.bcebos.com/zyb-speech/96df4ace-6864-4567-bdec-18100a0159be.mp3", "prob": 0.8633083883333333}',
            },
        ],
        final: true,
    },
    id: "96df4ace-6864-4567-bdec-18100a0159be",
};
const failMock = {

    "status": 0,
    "id": "127e266ea7b040b4a4443030446cd773",
    "type": "HEARTBEAT",
    "result": {
        "hypotheses": [
            {
                "transcript": '{"status":0,"intensity":27.529816,"wordScoreList":[{"flag":1,"score":0,"word":"HOW"},{"flag":1,"score":0,"word":"ARE"},{"flag":1,"score":0,"word":"YOU"}]}'
            }
        ],
        "final": false
    },
    "is_final": 0
};
export default class RTREngine {

    private resultCb: (any) => void;
    /**
     * @name startRecordAction
     * @description 开始录音action
     * @action startRecordAction
     * @param { Object } params 相关参数
     */
    public startRecordAction(recordData: any, startRecordCb: any, removeRecordCb: any, startEndCb: any) {
        setTimeout(()=>{
            startRecordCb(successMock);
        },2000);
    }
    /**
     * @msg     : 请求权限。
     * @param    {*} callBack
     * @return   {*}
     */    
    public checkRecordAuthorityAction(callBack){
        callBack({status:1});
    }

    /**
     * @name stopRecordAction
     * @description 停止录音action
     * @action stopRecordAction
     * @param { Object } params 相关参数
     */
    public stopRecordAction(stopEndCB?) {
        // setTimeout(()=>{
        //     this.resultCb(MockData);    
        // },2000);
    }
}
