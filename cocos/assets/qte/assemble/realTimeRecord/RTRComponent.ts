/* eslint-disable max-lines */

import ComponentUtils from "../componentUtils/ComponentUtils";
import BaseComponent from "../core/BaseComponent";
import { RecordType } from "../core/ComponentData";
import { EngineCallBack } from "../microp//MicropData";

// import RecordEngineCallBack from "../record/RecordEngineCallBack";
import RTRManager from "./RTRManager";
import RTRData, { recordResultData, RecordState, VoiceEngineProp } from "./RTRData";

/**
 * 录音组件
 *
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class RTRComponent extends BaseComponent {

  // 进度条
  @property(cc.ProgressBar)
  private countProgress: cc.ProgressBar = null;

  @property(cc.Node)
  private tipsImg: cc.Node = null;

  @property(sp.Skeleton)
  private microphone: sp.Skeleton = null;

  // 蒙层节点
  @property(cc.Node)
  private maskNode: cc.Node = null;



  private recordManager: RTRManager;

  // 评测数据
  public recordData: RTRData;

  // 录音状态
  private recordStatus: RecordState;

  /** 事件回调 */
  private listenerMap: Map<number, (data) => void>;

  public countDownNum: number = 0;

  // 原始位置坐标
  private originPos: cc.Vec3 = null;

  onLoad() {

  }

  /**
   * @msg     : idle 显示。
   * @param    {*}
   * @return   {*}
   */
  idleShow() {
    if (!this.node.active) {
      return;
    }
  }

  /**
   * 更新评测词
   * @param data 语音引擎属性数据
   */
  public async updateWord(data: VoiceEngineProp) {
    console.log("%c Line:73 🍖 data", "color:#7f2b82", data);
    this.countDownNum = data.answerDuration;
    console.log("%c Line:72 🥓 this.countDownNum", "color:#ea7e5c", this.countDownNum);
    this.recordData.initData(data);

    this.countProgress.progress = 0;

    await this.changeState(RecordState.Recording);

    return true;

  }


  /** 初始化组件 */
  public async initComponent(data: any) {
    // 还原初始状态

    cc.log("initComponent", data);
    this.listenerMap = new Map();
    // this._historyStatus = [];
    this.recordManager = new RTRManager();
    this.recordData = new RTRData();

    this.recordData.lastRecordTime = new Date().getTime();

    this.node.scaleX = data.properties.width / this.node.width;
    this.node.scaleY = data.properties.height / this.node.height;

    this.countProgress.progress = 0;

    this.originPos = this.node.parent.position;

    this.maskNode.active = false;

    this.changeState(RecordState.Idle);

  }

  /**
   * 初始化模板
   * @param engine 引擎实例
   * @returns 返回是否有录音权限
   */
  public templateInit(engine): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      // this.setInitNode();
      // 检测是否有权限
      let isRecordAuthority = true;
      isRecordAuthority = await this.recordManager.initEngine(engine, this);
      resolve(isRecordAuthority);
    });
  }

  reset() {
    this.recordStatus = RecordState.Idle;
    this.recordData.lastRecordTime = new Date().getTime();
    this.changeState(this.recordStatus);
    this.unscheduleAllCallbacks();
  }

  /**
   * 注册回调事件
   * @param callBack
   * @param cb
   */
  public addCallBackListener(option: { key: EngineCallBack; callBack: (data) => void }[]) {
    for (let temp of option) {
      this.listenerMap.set(temp.key, temp.callBack);
    }
  }

  /**
   * 触发sdk回调事件
   */
  public sendCallBack(callBack: EngineCallBack, data: recordResultData) {
    let cb = this.listenerMap.get(callBack);
    if (cb) {
      cb(data);
    }
  }

  /** 更换资源 */
  public changeSkine(path: string, style: RecordType) {
    // -- TODO
    // this.voiceStyle = style;
  }

  async changeState(recordState: RecordState, num?) {
    this.recordStatus = recordState;
    switch (recordState) {
      case RecordState.Idle:
        this.idleShow();
        break;
      case RecordState.Recording:
        await this.recordingShow();
        break;
      case RecordState.Stop:
        this.stopShow();
        break;
    }
  }
  // 恢复原有显示
  private stopShow() {

    // 隐藏蒙层
    this.maskNode.active = false;

    if (cc.isValid(this.node.parent, true)) {
      let _nodeT = new cc.Tween(this.node.parent);
      _nodeT.by(0.5, { position: cc.v3(0, -300, 0), easing: 'sineOutIn' }).call(() => {
        this.countProgress.progress = 0;
        this.tipsImg.opacity = 255;
        // 停止麦克风动画
        this.microphone.clearTracks();
      }).start();
    }
  }

  private recordingShow(): Promise<any> {
    return new Promise(async (resolve, reject) => {

      // 显示蒙层
      this.maskNode.active = true;

      if (cc.isValid(this.node.parent, true)) {
        let _nodeT = new cc.Tween(this.node.parent);

        _nodeT.by(0.3, { position: cc.v3(0, 300, 0), easing: 'sineOutIn' }).call(() => {

          this.recordManager.startRecord();

          // 播放麦克风动画
          this.microphone.setAnimation(0, 'huatongtexiao', true);

          resolve(null);
        }).start();
      }

      if (cc.isValid(this.tipsImg, true)) {
        let _tipsT = new cc.Tween(this.tipsImg);
        _tipsT.delay(2).to(0.3, { opacity: 0, easing: "sineOutIn" }).start();
      }
    });
  }


  public countdown(dt) {
    this.countDownNum -= dt;
    let progNum = this.countDownNum / this.recordData.voiceEngineProp.answerDuration;
    this.countProgress.progress = 1 - progNum;
  }

  /** 重置所有动画 */
  resetAction() {
    this.changeState(RecordState.Idle);
  }

  private isNetRes(path: string): boolean {
    if (path.startsWith("http://") || path.startsWith("https://") || path.startsWith("zybhost://")) {
      return true;
    }
    return false;
  }

  changeProperties() { }
  /**
   * 加载spine资源
   * @param component spine数据atlas、images、skeleton、cover、动作
   * @returns
   */
  public async loadSpine(component: any): Promise<sp.SkeletonData> {
    return new Promise<sp.SkeletonData>(async (resolve, reject) => {
      let texture = [];
      let textureName = [];
      for (const iterator of component.images) {
        let asset: any = await this.loadRes(iterator)
        if (asset instanceof cc.Texture2D) {
          asset.setPremultiplyAlpha(true)
        }
        texture.push(asset);
        textureName.push(iterator.substring(iterator.lastIndexOf("/") + 1));
      }

      let atlasText: any = await this.loadRes(component.atlas);
      let skeletonJson: any = await this.loadRes(component.skeleton);

      let asset = new sp.SkeletonData();
      asset.skeletonJson = (skeletonJson as cc.JsonAsset).json;
      asset.atlasText = (atlasText as cc.TextAsset).text;
      asset.textures = texture;
      // @ts-ignore
      asset.textureNames = textureName;
      // @ts-ignore
      resolve(asset);
    }).catch(err => {
      qte && qte.logCatBoth('cocos promise error:', err);
      return null;
    });
  }
  /**
  * 加载远程资源
  * @param url 资源路径
  * @returns
  */
  public loadRes(url): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      this.getRemoteRes(url, null, (error, res: cc.Asset) => {
        if (error) {
          return;
        }
        resolve(res);
      });
    }).catch(err => {
      qte && qte.logCatBoth('cocos promise error:', err);
      return null;
    });
  }
  /**
   * 销毁组件
   */
  onDestroy() {
    console.log("onDestroy === 销毁组件");
    this.recordManager.onClose();// 删除所有回调
    this.listenerMap.clear();

    // 关闭录音管理器
    this.recordManager.onClose();

    // 还原节点初始位置
    if (this.originPos) {
      this.node.parent.setPosition(this.originPos);
    }
  }

  public onGamePause() {
    this.recordManager.onGamePause();
  }

  public onGameResume() {
    this.recordManager.onGameResume();
  }

}

// TODO：提前删除处理定时器；