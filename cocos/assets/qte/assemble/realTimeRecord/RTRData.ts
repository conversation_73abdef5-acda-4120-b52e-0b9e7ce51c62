import { QTE_LOGCAT_TYPE } from "../../qte-core-export";
import CryptoJS = require("../record/crypto-js/indexCrypto");



/** 评测类型 */
export enum WordType {
    null = 0,
    word = 1, // 单词
    sentence = 2, // 句子
    paragraph = 3, // 段落
    question = 4, // 问题回答
    repeat = 5, // 复述
    symbols = 6, // 音标
}

/**
 * 录音结果数据接口
 * @interface recordResultData
 * @property {number} score - 评测分数
 * @property {string} [audio] - 音频数据，可选参数
 */
export interface recordResultData {
    // 分数
    score?: number;
    // 音频
    audio?: string;
    // 状态 是否完成
    status?: number;
    // 题目返回值处理状态
    questionStatus?: QuestionStatus;
}
// 题型状态
export enum QuestionStatus {
    // 开始
    Start = 0,
    // 进行中
    Stop = 1,
    // 已完成
    Completed = 2,
}


export enum RecordState {
    Idle = "idle",
    Ready = 'ready',
    Recording = "recording",
    Loading = "loading",
    Stop = "stop",
    Error = "error",
}

interface ERecordParams {
    ak: any; // 加密内容
    st: number; // 评分类型 1-单词 2-句子 3-段落 4-问题回答 5-复述 6-音标
    rt: string; // 传入的内容
    kp?:[];     // 评测关键字
    // syllable?: number; // 1-获取重读,音节对错
}

export class VoiceEngineProp {
    // 评测类型：1 - 单词；2 - 句子； 3 - 段落； 4 - 问题回答； 5 - 复述;  6 - 音标
    wordType: WordType;
    evaluatingText: string; // 评测内容
    answerDuration: number = 15; // 答题时长
    isUsePhonetics?:boolean;        // 是否使用音标
    evaluatePhonetics?:string;    // 音标评测内容
    // 最低分
    minScore?: number;
}
/**
 * 存放所有录音sdk数据
 */
export default class RTRData {
    

    public lastRecordTime = 0;

    
    public voiceEngineProp: VoiceEngineProp;
    // 调用 端的语音 action 需要传入。
    public reqRecordData: ERecordParams = {
        ak: "",
        rt: "",
        st: 1,
    };
    constructor() {}

    public initData(data) {

        this.voiceEngineProp = new VoiceEngineProp();
        for (let key in data) {
            this.voiceEngineProp[key] = data[key];
        }
        //判断是否开启音标判定，如果开启则将音标内容添加到评测文本中
        if(this.voiceEngineProp.isUsePhonetics && typeof this.voiceEngineProp.isUsePhonetics == "boolean")
        {
            const regex = /^\/.*\/$/;
            const regexfirst = /^\//;
            const regexLast = /\/$/;
            if(regex.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"("+this.voiceEngineProp.evaluatePhonetics+")";
            else if(regexfirst.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"("+this.voiceEngineProp.evaluatePhonetics+"/)";
            else if(regexLast.test(this.voiceEngineProp.evaluatePhonetics))
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"(/"+this.voiceEngineProp.evaluatePhonetics+")";
            else
                this.voiceEngineProp.evaluatingText = this.voiceEngineProp.evaluatingText+"(/"+this.voiceEngineProp.evaluatePhonetics+"/)";
        }
        
        this.reqRecordData.st = this.voiceEngineProp.wordType;
        this.reqRecordData.ak = this.handleAesEecryption(
            this.voiceEngineProp.evaluatingText
        );
        if(this.voiceEngineProp.evaluatingText.indexOf('[') != -1){
            this.voiceEngineProp.evaluatingText = JSON.parse(this.voiceEngineProp.evaluatingText);
        };
        this.reqRecordData.rt = this.voiceEngineProp.evaluatingText;
    }
    
    private handleAesEecryption(text: string) {
        const key = CryptoJS.enc.Latin1.parse("zyb.2015ZYB.2018");
        const iv = CryptoJS.enc.Latin1.parse("zyb.2015ZYB.2018");
        const serverTime = new Date().getTime();
        const pid = parseInt(serverTime / 1000 + "", 10);
        const contentText = text + "@" + pid;
        const encoded = CryptoJS.AES.encrypt(contentText, key, {
            iv,
            mode: CryptoJS.mode.CBC,
            adding: CryptoJS.pad.ZeroPadding,
        }).toString();
        cc.log("handleAesEecryption", text, encoded);
        return encoded;
    }
}
