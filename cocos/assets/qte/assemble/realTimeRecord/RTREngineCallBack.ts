/*
 * @FilePath     : /sdk/assets/qte/assemble/realTimeRecord/RTREngineCallBack.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import { ResultData } from "../record/RecordData";
import { recordResultData } from "./RTRData";
import RTRManager from "./RTRManager";

export default class RTREngineCallBack {

    private rtrManager: RTRManager;
    // 是否在后台
    private isGamePause = false;
    private stopRecordEndCb = null;

    // 是否二次重读过
    // private isReStart = false;

    // 处于后台保存事件处理
    private gamePaseCb: {
        parm: any;
        cb: (data) => void;
    } = null;

    public reset() {
        this.stopRecordEndCb = null;
        this.isGamePause = false;
    }

    public regiestComponent(cmpt: RTRManager) {
        this.rtrManager = cmpt;
    }

    // #region  回调事件处理
    /**
     * 开始录音的回调
     * @param v
     */
    public startRecordingCb(data: any, minScore?) : recordResultData{

        console.log("startRecordingCb data", data);

        if (this.isGamePause) {
            this.gamePaseCb = {
                parm: data,
                cb: this.startRecordingCb.bind(this),
            };
            return;
        }

        let resultData:recordResultData = {};
        let result = JSON.parse(data.result?.hypotheses[0]?.transcript);
        // 完成了，
        if (data?.result?.final) {
            // 获取音频  this.resultData.online_audio_url.split('.mp3')[0] + ".mp3"
            resultData.audio = result.online_audio_url.split('.mp3')[0] + ".mp3";
            resultData.score = result.score;
            resultData.status = 1;
        } else {
            // 实时计算结果
            // 判断是否  wordScoreList  或者  wordList；
            if (result.wordScoreList || result.wordList) {
                let _wordScore = result.wordScoreList? result.wordScoreList : result.wordList;
                console.log("%c Line:67 🍺 _wordScore", "color:#3f7cff", _wordScore);
                // 计算每个单词分数，都需要及格；
                let _score = 0;
                // 是否完成；
                let _isFinish = 1;
                console.log("%c Line:74 🍖 minScore", "color:#2eafb0", minScore);
                for (let i = 0; i < _wordScore.length; i++) {
                    _score += _wordScore[i].score;
                    if (_wordScore[i].score < minScore) {
                        _isFinish = 0;
                    }
                };
                console.log('_isFinish', _isFinish);
                resultData.score = _score / _wordScore.length;
                resultData.status = _isFinish;
            } else {
                qte.logCatBoth("实时评测", `分数 无数据 ${result}`);
            }
        }

        // 计算分数， 先判断是否完成， is_final 为 true 表示结束
        // 如果结束，返回分数 和 音频地址，如果分数没达到要求，就只返回分数；
        return resultData;
    }
    
    public stopRecordCb(cb?) {
        // this.stopRecordEndCb = cb;
    }

    destory() {
        // this.unregisterEvent();
    }

    public onGamePause() {
        this.isGamePause = true;
    }

    public onGameResume() {
        this.isGamePause = false;
        if (this.gamePaseCb) {
            this.gamePaseCb.cb(this.gamePaseCb.parm);
            this.gamePaseCb = null;
        }
    }
}
