/*
 * @FilePath     : /sdk/assets/qte/assemble/realTimeRecord/RTRManager.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */

import RTRComponent from "./RTRComponent";
import RTREngineCallBack from "./RTREngineCallBack";
import { QuestionStatus, recordResultData, RecordState } from "./RTRData";
import { EngineCallBack } from "../microp/MicropData";
import QTEUtils from "../../util/QTEUtils";
import RTREngine from "./RTREngine";

export default class RTRManager {
    /** sdk */
    private recordEngine: RTREngine;
    // 回调监听
    private recordCallBack: RTREngineCallBack;

    private recordCmpt: RTRComponent;

    private updateTimeFunc = null;

    private isOverStop = false;


    // 是否有权限
    private isRecordAuthority: boolean;

    private currentTime = 0;

    // 每次调用时间
    private callBackTime = 1;

    // 评测回调结果数据
    private resultData:recordResultData;

    // 结果数据备份
    private resultDataBak:recordResultData;

    // 记录权限兜底定时器id
    private recordAuthorityTimerId: number;
    
        
    /**
     * 初始化录音引擎
     * @param engine - 录音引擎实例
     * @param target - 录音组件实例
     * @returns 返回是否有录音权限
     */
    public async initEngine(engine: RTREngine, target: RTRComponent): Promise<boolean> {
        // 初始化回调对象
        this.recordCallBack = new RTREngineCallBack();

        // 初始化录音权限状态
        this.isRecordAuthority = false;

        // 根据环境初始化录音引擎
        console.log("engine==", engine);
        if (!engine || CC_PREVIEW) {
            this.recordEngine = new RTREngine();
        } else {
            this.recordEngine = engine;
        }

        // 保存录音组件引用
        this.recordCmpt = target;

        // 检查并请求录音权限
        this.isRecordAuthority = await this.reqRecordAuthority();

        return this.isRecordAuthority;
    }
    /**
     * @msg     : 请求语音权限
     * @param    {*}
     * @return   {*}
     */

    private reqRecordAuthority(): Promise<boolean> {
        // this.isAuthorityIng = true;
        console.log("reqRecordAuthority====1111");
        return new Promise((resolve, reject) => {
            // 增加超时兜底
            this.recordAuthorityTimerId = qte.TimerUtils.addTimer(() => {
                qte.logCatBoth("recordCmpt", "reqRecordAuthority, 超时兜底");
                resolve(false);
            }, 8, qte.ExecutionType.once);
            this.recordEngine.checkRecordAuthorityAction(isAuthority => {
                qte.logCatBoth("recordCmpt", `reqRecordAuthority, ${isAuthority}`);
                qte.TimerUtils.clearTimer(this.recordAuthorityTimerId);
                this.recordAuthorityTimerId = null;
                resolve(isAuthority.status);
            });
        });
    }

    public reset() {
        this.isOverStop = false;
        this.updateTimeFunc = null;
        this.resultData = null; 
        this.recordAuthorityTimerId && qte.TimerUtils.clearTimer(this.recordAuthorityTimerId);
    }

    /**
     * 开始录音
     * 说明： 调用 stopRecord后会 返回resultCb
     * @param parm
     */
    public startRecord() {
        if (!cc.isValid(this.recordCmpt.node, true)) {
            qte && qte.logCatBoth("recordCmpt", " startRecord,节点已销毁音频回调无法继续执行了");
            return;
        }
        
        this.reset();
        
        this.updateTimeFunc = this.update.bind(this);

        this.recordCmpt.schedule(this.updateTimeFunc);
        
        this.recordEngine.startRecordAction(
            this.recordCmpt.recordData.reqRecordData,
            res => {
                if (!cc.isValid(this.recordCmpt.node, true)) {
                    qte && qte.logCatBoth("recordCmpt", "startRecord节点已销毁,识别回调无法继续执行了");
                    return;
                }
                qte.logCatBoth("recordCmpt", `startRecord, ${res.status}`);
                if (res && res.status === 0) {
                    qte.logCatBoth("recordCmpt", `this.isOverStop, ${this.isOverStop}`);
                    if (!this.isOverStop) {
                        qte.logCatBoth("recordCmpt", `this.resultData 1= ${JSON.stringify(this.resultData)}`);
                        // if(!this.resultData){
                            let _resultData = this.recordCallBack.startRecordingCb(res , this.recordCmpt.recordData.voiceEngineProp.minScore);
                            // 如果 resultData 为空，则说明是第一次回调，则将 resultData 赋值为 _resultData
                            if(!this.resultData){
                                this.resultData = _resultData;
                            } else {
                                // 如果 resultData 不为空，则说明是第二次回调，则单独更新每个字段数据
                                if(_resultData.score > this.resultData.score){
                                    this.resultData.score = _resultData.score;
                                }
                                if(_resultData?.audio && _resultData?.audio != this.resultData?.audio){
                                    this.resultData.audio = _resultData.audio;
                                }
                                if(_resultData?.status != this.resultData?.status){
                                    this.resultData.status = _resultData.status;
                                }
                                // 如果没状态，则设置为开始状态
                                if(!_resultData?.questionStatus){
                                    this.resultData.questionStatus = QuestionStatus.Start;
                                }
                            }
                            // 处理结果
                            qte.logCatBoth("recordCmpt", `this.resultData 2= ${JSON.stringify(this.resultData)}`);
                            this.handleResult(this.resultData);
                    } else {
                        console.log('已经结束');
                        qte.logCatBoth("recordCmpt", `结束 this.resultData 3= ${JSON.stringify(this.resultData)}`);
                    }
                } else {
                    console.log("返回参数 error");
                    qte.logCatBoth("recordCmpt", `返回参数 error`);
                }
            },
            (removeCb?) => {
                if (this.isOverStop) {
                    removeCb && delete window[removeCb];
                }
            },
            () => {
            },
        );
    }

    // 处理回调结果函数
    private handleResult(result) {
        // 判断是否完成；
        if (result && result.status === 1) {
            // 第一步完成后，判断是否有音频
            if (result.audio && result.audio != "") {
                qte.logCatBoth("RTRManager handleResult", `result  finish ${JSON.stringify(result)}`);
                this.isOverStop = true;
                this.recordCmpt.changeState(RecordState.Stop);
                this.recordCmpt.unschedule(this.updateTimeFunc);
                this.recordCmpt.sendCallBack(EngineCallBack.RecordSuccess, result);
                this.resultData.questionStatus = QuestionStatus.Completed;
                // this.resultData = null;
                
            } else {
                qte.logCatBoth("RTRManager handleResult", `result  stop 1 ${JSON.stringify(result)}  this.resultData.questionStatus=${this.resultData.questionStatus}`);
                 // 没有音频，说明分数到了，调用停止；
                if (this.resultData.questionStatus == QuestionStatus.Start) {
                    qte.logCatBoth("RTRManager handleResult", `result  stop 2 ${JSON.stringify(result)}  this.resultData.questionStatus=${this.resultData.questionStatus}`);
                    this.stopRecord();
                    // 设置题目状态为已完成
                    this.resultData.questionStatus = QuestionStatus.Stop;
                }
                // 备份结果
                this.resultDataBak = QTEUtils.deepClone(result);
                qte.logCatBoth("RTRManager handleResult", `result  stop 3 ${JSON.stringify(result)}  this.resultData.questionStatus=${this.resultData.questionStatus}`);
                // this.resultData = null;
            }
        } else {
            console.log("handleResult 结果数据为空");
            qte.logCatBoth("RTRManager handleResult", `handleResult 结果数据为空`);
        }
    }
    public update(dt) {
        if (this.recordCmpt.countDownNum >= 0) {
            this.recordCmpt.countdown(dt);
        } else {
            // 倒计时结束，执行评分
            console.log("%c Line:188 🧀 this.isOverStop", "color:#33a5ff", this.isOverStop);
            if (!this.isOverStop) {
                
                this.isOverStop = true;
                this.recordCmpt.changeState(RecordState.Stop);
                this.recordCmpt.unschedule(this.updateTimeFunc);
                console.log("%c Line:194 🍊 this.resultDataBak", "color:#f5ce50", this.resultDataBak);
                console.log("%c Line:194 🥪 this.resultData", "color:#2eafb0", this.resultData);
                if (this.resultData) {
                    this.resultData.questionStatus = QuestionStatus.Completed;
                    // * 这种情况是，学生作答正确了，但 stop 之后，没在倒计时时间内收到回调，所以 audio 是空的，但分数是对的， 可能出现 0 分；
                    let _resultData = this.resultData ? this.resultData : this.resultDataBak;
                    if (_resultData && _resultData.status == 1) {
                        this.recordCmpt.sendCallBack(EngineCallBack.RecordSuccess, this.resultData ? this.resultData : this.resultDataBak);
                    } else {
                        // 超时或者 分数低于 及格分就 随机一个分数
                        this.resultData.status = 1;
                        this.recordCmpt.sendCallBack(EngineCallBack.RecordError, {
                            // 随机一个分数 , 范围是0 - minScore
                            'score': Math.random() * this.recordCmpt.recordData.voiceEngineProp.minScore,
                            'status': 1
                        });
                        this.stopRecord();
                    }
                } else {
                    // 如果 resultData 为空，则说明评测没有回调,那就返回 0 分, 并且 设置状态为已完成 在调用 stop 
                    this.stopRecord();
                    this.resultData = {
                        'status': 1,
                        'score': 0,
                        'questionStatus': QuestionStatus.Completed
                    };
                    this.recordCmpt.sendCallBack(EngineCallBack.RecordError, {
                        'score': 0,
                        'status': 1
                    });
                }
                
                
                
                
            }
        }
    }

    /** 停止录音 */
    stopRecord() {
        this.recordEngine.stopRecordAction();
    }
    public onGamePause() {
        this.recordCallBack?.onGamePause()
    }

    public onGameResume() {
        this.recordCallBack?.onGameResume()
    }   
    

    // 关闭后处理
    public onClose() {
        this.recordEngine.stopRecordAction();
        this.recordCmpt.unschedule(this.updateTimeFunc);
        this.reset();
    }

}
