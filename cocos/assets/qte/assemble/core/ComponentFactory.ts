import RecordComponent from "../record/RecordComponent";
import BaseComponent from "./BaseComponent";
import { SpecialComponentType } from "./ComponentData";

export default class ComponentFactory {
  /** 单例对象 */
  private static _instance: ComponentFactory = null;

  public bundle: cc.AssetManager.Bundle;

  /** 获取单例对象 */
  public static getInstance(): ComponentFactory {
    if (!this._instance) {
      this._instance = new ComponentFactory();
    }
    return this._instance;
  }

  public static destroyInstance(): void {
    this._instance = null;
  }

  /**
   * 创建组件
   * @param type
   * @param data
   * @param bundle
   * @param rootPath rescore 根目录
   */
  public async createObject(
    type: SpecialComponentType,
    data: any,
    bundle: cc.AssetManager.Bundle,
  ): Promise<BaseComponent> {
    this.bundle = bundle;
    let cmpt: BaseComponent = null;
    let node: cc.Node = null;
    switch (type) {
      case SpecialComponentType.CmptRecord:
        node = await this.createRecordNode(bundle);
        break;
      case SpecialComponentType.KeyBord:
        node = await this.createKeyBordNode(bundle);

        break;
      case SpecialComponentType.KeyBordEnglish:
        node = await this.createKeyBordEnglishNode(bundle);

        break;
      case SpecialComponentType.Write:
        node = await this.createWriteNode(bundle);
        break;
      case SpecialComponentType.MatchBoard:
        node = await this.createMatchBoardNode(bundle);
        break;
      case SpecialComponentType.Counter:
        node = await this.createCounterNode(bundle);
        break;
      case SpecialComponentType.Clock:
        node = await this.createClockNode(bundle);
        break;
      case SpecialComponentType.Tangram:
        node = await this.createTangramNode(bundle);
        break;
      case SpecialComponentType.Speaker:
        node = await this.createSpeakerNode(bundle);
        break;
      case SpecialComponentType.VoiceSpeak:
        node = await this.createVoiceSpeakNode(bundle)
        break;
      case SpecialComponentType.Brush:
        node = await this.createBrushNode(bundle);
        break;
      case SpecialComponentType.Readcom:
        node = await this.createReadcomNode(bundle);
        break;

      case SpecialComponentType.ListenRetell:
        node = await this.createListenRetellNode(bundle);
        break;

      case SpecialComponentType.Microp:
        node = await this.createMicropNode(bundle);
        break;

      case SpecialComponentType.ListenAnswer:
        node = await this.createListenAnswerNode(bundle);
        break;
      case SpecialComponentType.ReadPage:
        node = await this.createReadPageNode(bundle);
        break;
      case SpecialComponentType.FollowWords:
        node = await this.createFollowWordsNode(bundle);
        break;
      case SpecialComponentType.H5Label:
        node = await this.createH5LabelNode(bundle);
        break;
      case SpecialComponentType.ENPK:
        node = await this.createENPKNode(bundle);
        break;
      case SpecialComponentType.RTRVoice:
        node = await this.createRTRVoiceNode(bundle);
        break;
      case SpecialComponentType.VIDE0:
        node = await this.createVideoNode(bundle);
        break;


      default:
        console.warn("走进来量");
        // 防止数据增量，组件创建返回null
        node = new cc.Node();
        break;
    }

    // debugger
    cmpt = node.getComponent(BaseComponent);
    return cmpt;
  }

  // --TODO 修改为根据类型加载
  public async createRecordNode(
    bundle: cc.AssetManager.Bundle,
  ): Promise<cc.Node> {
    return new Promise<cc.Node>(reslove => {
      // console.log("%c 🍕 bundle: ", "font-size:20px;background-color: #93C0A4;color:#fff;", bundle);
      let path = "assemble/record/res/prefabs/entry";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        let cmpt = node.addComponent(RecordComponent);
        node.name = "RecordNode";
        // cmpt.voiceStyle = voiceStyle;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  public createKeyBordNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>(reslove => {
      let path = "assemble/keyboard/prefabs/keyboard";
      //   debugger
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        console.log(assets);
        node.name = "keyboard";

        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  public createKeyBordEnglishNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>(reslove => {
      let path = "assemble/keyboardEnglish/prefabs/keyboardEnglish";
      //   debugger
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        console.log(assets);
        node.name = "keyboard";

        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  public createMatchBoardNode(
    bundle: cc.AssetManager.Bundle,
  ): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/matchBoard/prefab/matchBoard";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "matchBoard";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  public createWriteNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/write/prefabs/write";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "write";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  public createCounterNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/countCompoent/prefabs/counter";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "counter";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  createTangramNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    console.log("createTangramNode begin. ");
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/tangram/prefab/tangram";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        console.log("createtangramNode finish. error " + err);
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "tangram";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  /** 创建音频组件 */
  createSpeakerNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    console.log("createSpeakerNode begin ");
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/speaker/prefabs/speaker";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          console.log("createSpeakerNode finish. error " + err);
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /**
   * @msg     : 创建看图组件
   * @param    {*}
   * @return   {*}
   */
  createVoiceSpeakNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    console.log('createVoiceSpeakNode begin');
    return new Promise<cc.Node>((reslove, reject) => {
      try {
        let path = "assemble/lookSpeak/prefabs/entry";
        bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
          if (err) {
            console.log("createVoiceSpeakNode finish. error " + err);
            console.error(err);
            return;
          }
          console.log("createVoiceSpeakNode11111");
          let node = cc.instantiate(assets);
          node.active = true;
          reslove(node);
        });

      } catch (error) {
        window['qte'] && window['qte'].logCatBoth('cocos promise error:', error)
        reject(error);
      }

    });
  }
  /** 听后转述 */
  createListenRetellNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/listenRetell/prefabs/ListenRetellComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "listenRetell";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

  /** 跟读单词*/
  createFollowWordsNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/followWords/prefabs/FollowWordsComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "followWords";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /** 听后回答*/
  createListenAnswerNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/listenAnswer/prefabs/ListenAnswerComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "listenAnswer";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /** 朗读短文d*/
  createReadPageNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/readPage/prefabs/ReadPageComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "readPage";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }


  /** 阅读理解 */
  createReadcomNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/readcom/prefabs/ReadcomComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "readcom";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /** 创建画笔组件 */
  createBrushNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/drawControl/prefab/drawControl";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /**
   * 创建钟表组件
   * @param bundle
   * @returns
   */
  public createClockNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    console.log("createClockNode begin. ");
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/clock/prefabs/clockRoot";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        console.log("createClockNode finish. error " + err);
        if (err) {
          console.error(err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "clock";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }
  /**
   * @msg     : 创建麦克风组件
   * @param    {cc} bundle
   * @return   {*}
   */
  public createMicropNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((resole, reject) => {
      console.log("createMicropNode111");
      bundle.load('assemble/microp/prefabs/entry', cc.Prefab, (err, assets: cc.Prefab) => {
        console.log("createMicropNode suncc");
        if (err) {
          console.log('err==- load=', err);
          return;
        }
        let node = cc.instantiate(assets);
        node.name = 'microp';
        node.active = true;
        resole(node);
      });
    })
  }



  /** 跟读单词*/
  createH5LabelNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/h5Label/prefabs/h5LabelComponent";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "h5Label";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

    /** 小英上台pk*/
    createENPKNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
      return new Promise<cc.Node>((reslove, reject) => {
        let path = "assemble/enPK/prefabs/EnPKComponent";
        bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
          if (err) {
            return;
          }
          let node = cc.instantiate(assets);
          node.name = "enPK";
          node.active = true;
          reslove(node);
        });
      }).catch(err => {
        window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
        return null;
      });
    }
  private createRTRVoiceNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
    return new Promise<cc.Node>((reslove, reject) => {
      let path = "assemble/realTimeRecord/res/entry";
      bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
        if (err) {
          return;
        }
        let node = cc.instantiate(assets);
        node.name = "realTimeRecord";
        node.active = true;
        reslove(node);
      });
    }).catch(err => {
      window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
      return null;
    });
  }

    createVideoNode(bundle: cc.AssetManager.Bundle): Promise<cc.Node> {
      return new Promise<cc.Node>((reslove, reject) => {
        let path = "assemble/video/prefabs/video";
        bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
          if (err) {
            return;
          }
          let node = cc.instantiate(assets);
          node.name = "video";
          node.active = true;
          reslove(node);
        });
      }).catch(err => {
        window['qte'] && window['qte'].logCatBoth('cocos promise error:', err)
        return null;
      });
    }
  



}

// eslint-disable-next-line no-unused-expressions
(window as any).assemble || ((window as any).assemble = {});
(window as any).assemble.factory = ComponentFactory.getInstance();
