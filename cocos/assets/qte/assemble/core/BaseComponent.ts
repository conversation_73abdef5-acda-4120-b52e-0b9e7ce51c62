const { ccclass, property } = cc._decorator;

@ccclass
export default abstract class BaseComponent extends cc.Component {
    //#region 基础属性设置
    public setActive(active: boolean) {
        this.node.active = active;
    }
    public setWidth(width: number) {
        this.node.width = width;
    }
    public setHeight(height: number) {
        this.node.height = height;
    }
    public setPosX(x: number) {
        this.node.x = x;
    }
    public setPosY(y: number) {
        this.node.y = y;
    }
    public setColor(color: string | cc.Color) {
        if (typeof color === "string") {
            this.node.color = new cc.Color().fromHEX(color);
        } else {
            this.node.color = color;
        }
    }
    public setzIndex(val: number) {
        this.node.zIndex = val;
    }
    public setAngle(val: number) {
        this.node.angle = val;
    }
    public setScaleX(val: number) {
        this.node.scaleX = val;
    }
    public setScaleY(val: number) {
        this.node.scaleY = val;
    }

    /** 播放音频函数 */
    private _funPlayAudio: (audio: cc.AudioClip, loop: boolean, cb: () => void) => number = (audio: cc.AudioClip, loop: boolean, cb: () => void) => {
        let audioId = cc.audioEngine.play(audio, loop, 1);
        cc.audioEngine.setFinishCallback(audioId, () => {
            if (cb) {
                cb();
            }
        })
        return audioId;
    };

    /** 停止播放音频函数 */
    private _funStopAudio: (audioId: number) => void = (audioId: number) => {
        return cc.audioEngine.stop(audioId);
    }

    public setPlayFunc(func: (audio: cc.AudioClip, loop: boolean, cb: () => void) => number) {
        this._funPlayAudio = func;
    }

    public setStopAudioFunc(func: (audioId: number) => void) {
        this._funStopAudio = func;
    }

    public playAudio(audio: cc.AudioClip, loop: boolean = false, cb: () => void = null): number {
        let mangerCb = () => {
            if (cc.isValid(this.node), true) {
                cb && cb();
            } else {
                qte && qte.logCatBoth("playAudio", "节点已销毁，无法回调组件下一步了")
            }
        };
        if (!cc.isValid(this.node)) {
            qte && qte.logCatBoth("playAudio", "节点已销毁，无法播放音频");
            return -1;
        }
        return this._funPlayAudio(audio, loop, mangerCb);
    }

    public stopAudio(audioId: number) {
        if (cc.isValid(this.node), true) {
            this._funStopAudio(audioId);
        } else {
            qte && qte.logCatBoth("stopAudio", "节点已销毁，无法停止音频");
        }
    }

    private _funGetRemoteRes: (url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => void;

    public setGetRemoteFunc(func: (url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => void): void {
        this._funGetRemoteRes = func;
    }
    public getRemoteRes(url: string, type: typeof cc.Asset, onComplete: (err, res) => void): void {
        let callBack = (err, res) => {
            if (!cc.isValid(this.node, true)) {
                return;
            }
            onComplete && onComplete(err, res);
        }
        if (!cc.isValid(this.node, true)) {
            return;
        }

        if (!this._funGetRemoteRes) {
            cc.assetManager.loadRemote(url, type, callBack);
            return;
        }
        return this._funGetRemoteRes(url, type, callBack);
    }


    private _funClearRemoteRes: (url: string[]) => void;

    public setClearRemoteFunc(func: (url: string[]) => void): void {
        this._funClearRemoteRes = func;
    }
    public clearRemoteRes(url: string[]): void {
        if (url.length <= 0) {
            return;
        }
        return this._funClearRemoteRes(url);
    }

    private _showToastFun: (content: string) => void = (content: string) => {
        let node = new cc.Node();
        let label = node.addComponent(cc.Label);
        node.color = cc.Color.BLACK;
        label.string = content;
        let labOutLine = node.addComponent(cc.LabelOutline);
        labOutLine.color = cc.Color.WHITE;
        labOutLine.width = 2;
        node.parent = cc.find("Canvas");
        cc.tween(node)
            .to(2, { x: 0, y: 100, opacity: 128 })
            .call(() => {
                node.removeFromParent();
                node.destroy();
            })
            .start();
    }

    public setShowToastFun(func: (content: string) => void): void {
        this._showToastFun = func;
    }

    public showToast(content: string): void {
        this._showToastFun(content);
    }

    private _showNoticeChoice(str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) {
        console.log("_showNoticeChoice str====", str, okcall, failcall, isChangeFocus);
    }

    public setShowNoticeChoice(func: (str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) => void) {
        this._showNoticeChoice = func;
    }

    public showNoticeChoice(str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) {
        if (!cc.isValid(this.node, true))
            return;
        this._showNoticeChoice(str, okcall, failcall, isChangeFocus);
    }


    //#endregion

    /**
     * 初始化
     * @param {data: {data: any, engine: anys}} 
     */
    public abstract initComponent(data?: any);

    public initAllComponentsFinish(): void {

    };
    /**
     * 属性更新
     * @param key 属性key
     * @param data 参数 
     */
    public abstract changeProperties(key: string, data: any);

    /**
     * @deprecated 弃用
     * 更换skin
     * @param path 资源路径 
     */
    public abstract changeSkine(path: string, param?: any);

    /** 宽高变化后触发的函数 */
    public onSizeChange(rect: cc.Size): void {

    }
}
