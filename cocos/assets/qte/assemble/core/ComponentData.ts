/*
 * @FilePath     : /sdk/assets/qte/assemble/core/ComponentData.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */


/**
 * 组件类型
 */
export enum SpecialComponentType {
  CmptRecord = "voice", // 录音组件
  KeyBord = "keyboard", // 键盘组件
  KeyBordEnglish = "keyboardEnglish",
  MatchBoard = "matchboard", // 火柴组件
  Counter = "counter", // 计数组件
  Write = "write", // 手写组件
  Clock = "clock", // 钟表组件
  Tangram = "tangram", // 七巧板
  Speaker = "speaker",     // 音频组件
  Brush = "brush",
  VoiceSpeak = "voiceSpeak",   // 看图说话
  Readcom = "readcom",  //阅读理解
  Microp = "microp",      // 麦克风
  ListenRetell = "listenRetell", //听后转述
  ListenAnswer = "listenAnswer", //听后回答
  ReadPage = "readPage", // 朗读短文
  FollowWords = "followWords", // 跟读单词
  H5Label = "h5Label",// h5文本组件
  ENPK ="enPK",
  SKETCH = "sketch", // 物理作图题
  RTRVoice = "rtrVoice", // 实时录音
  VIDE0="video", // 视频组件
}

/**
 * 录音组件话筒样式
 */
export enum RecordType {
  Auto = 0,
  Manual = 1, //
}
