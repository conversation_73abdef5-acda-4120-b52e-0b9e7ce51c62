/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2021-09-02 10:58:03
 * @modify date 2021-09-02 10:58:03
 * @desc [火柴组件]
 */
import {MatchState, MatchTag, MatchType} from "./Enum";

const {ccclass, property} = cc._decorator;

@ccclass
export default class MatchComponent extends cc.Component {

    private _matchType: MatchType;
    private _state: MatchState;
    private _canMove: boolean;
    // 组件原始坐标
    public originLocal: cc.Vec3;
    private _tween: cc.Tween;
    private _offsetPos: cc.Vec2;

    private _lastStayPos: cc.Vec3;
    private _block: boolean;
    
    public options: any;
    public id: string;
    public tag: MatchTag;
    public coordinate: string;
    public originAngle: number;

    public onLoad() {
        this.node.on(cc.Node.EventType.TOUCH_START, this.onBegin, this);
        this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onMove, this);
        this.node.on(cc.Node.EventType.TOUCH_END, this.onEnd, this);
        this.node.on(cc.Node.EventType.TOUCH_CANCEL, this.onCancel, this);
        this.originLocal = this.node.position;
        this._lastStayPos = this.originLocal;
        this._block = true;
    }

    public onBegin(e: cc.Touch): void {
        if (!this.enabled) return;
        this.options && this.options.preBeginFunc && this.options.preBeginFunc(this.node);
        if (!this._canMove) {
            this.options && this.options.cantMoveFunc && this.options.cantMoveFunc();
            return;
        }
        this._block = false;
        this.node["oldSiblingIndex"] = this.node.getSiblingIndex();
        this.node.setSiblingIndex(999);
        let startPos = this._lastStayPos ? this._lastStayPos : this.originLocal
        let targetPos = cc.v3(0, 0, 0).add(startPos)
        if (this._tween) this._tween.stop()
        this.node.position = startPos
        this._tween = cc.tween(this.node).to(0.1, { position: targetPos }).start();

        this.options && this.options.onBegin && this.options.onBegin(this.node);
    }

    public changeLastStayPos(pos: cc.Vec3) {
        this._lastStayPos = pos
    }

    public onMove(e: cc.Touch): void {
        if (!this.enabled || this._block || !this._canMove) return;
        if (this._tween) {
            this._tween.stop();
            let local = e.getLocation();
            let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
            this._offsetPos = cc.v2(pos.x - this.node.x, pos.y - this.node.y);
            this._tween = null;
        }
        let local = e.getLocation();
        let pos = cc.v2(local.x - cc.winSize.width / 2, local.y - cc.winSize.height / 2);
        this.node.x = pos.x - this._offsetPos.x;
        this.node.y = pos.y - this._offsetPos.y;

        this.options && this.options.onMove && this.options.onMove(this.node);
    }

    public onEnd(e: cc.Touch): void {
        if (!this.enabled || this._block || !this._canMove) return;
        this.node.setSiblingIndex(this.node['oldSiblingIndex']);
        this.options && this.options.onEnd && this.options.onEnd(this.node);

    }

    public onCancel(e: cc.Touch): void {
        if (!this.enabled || this._block || !this._canMove) return;
        this.node.setSiblingIndex(this.node['oldSiblingIndex']);
        this.options && this.options.onCancel && this.options.onCancel(this.node);
    }

    public set matchType(value) {
        this._matchType = value;
        this.node.angle = value * 45;
    }

    public get matchType(): MatchType {
        return this._matchType;
    }

    public set state(value) {
        if (this._state == value) return;

        this._state = value;
        this.updateImg();
    }

    public get state(): MatchState {
        return this._state;
    }

    public set canMove(value) {
        this._canMove = value;
    }
    
    public get canMove(): boolean {
        return this._canMove;
    }

    /**
     * 更新火柴图片
     */
    private updateImg() {
        for (let i = 0; i < 5; i++) {
            let spriteNode = this.node.getChildByName(`${i}`);
            if (spriteNode) {
                spriteNode.active = i == this._state
            }
        }
    }

}
