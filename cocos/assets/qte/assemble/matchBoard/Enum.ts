
/**
 * 火柴题型
 */
export enum QuestionType {
    Formula = 0, // 公式
    Pattern      // 图案
}

/**
 * 火柴状态
 */
export enum MatchState {
    Static = 0,
    Basket,
    Diff,
    Active,
    Outline
}

/**
 * 火柴棍类型
 */
export enum MatchTag {
    Match = 0,    // 火柴
    Vacancy, // 空位
    TrashCan,
    Outline
}

export enum MatchType {
    Horizontal,
    Right,
    Vertical,
    Left,
}

/**
 * 火柴操作类型
 */
export enum MatchOperateType {
    //移动、添加、删除
    Move = 0,
    Add,
    Delete
}

// 火柴等式模板 火柴数据到数字的映射
export const MatchData2NumberMap = new Map([
    ['0000000', ''],
    ['1110111', '0'],
    ['0010010', '1'],
    ['0100100', '1'],
    ['1011101', '2'],
    ['1011011', '3'],
    ['0111010', '4'],
    ['1101011', '5'],
    ['1101111', '6'],
    ['1010010', '7'],
    ['1111111', '8'],
    ['1111011', '9'],
    ['0101', '+'],
    ['0100', '-'],
    ['0010', '-'],
    ['1000', '-'],
    ['1010', '=']
]);

export interface MatchData {
    position: cc.Vec3,
    id: string,
    coordinate: string,
    enabled: boolean,
    state: MatchState,
    matchType: MatchType,
    canMove: boolean,
    options?: object
}

export enum AreaType {
    OperationArea,
    TargetArea
}