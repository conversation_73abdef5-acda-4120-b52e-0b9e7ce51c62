[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "matchBoard", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}, {"__id__": 6}], "_active": true, "_components": [{"__id__": 8}], "_prefab": {"__id__": 9}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1280, "height": 720}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "vacancyNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7fcd16ed-4e98-4509-92d4-95f564533e9d"}, "fileId": "70gk49cD1O65tIyqsMiIyJ", "sync": false}, {"__type__": "cc.Node", "_name": "matchNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 756, "height": 540}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7fcd16ed-4e98-4509-92d4-95f564533e9d"}, "fileId": "3a28TUZJlIBIpNKGivSrmh", "sync": false}, {"__type__": "cc.Node", "_name": "targetNode", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 2, "height": 2}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7fcd16ed-4e98-4509-92d4-95f564533e9d"}, "fileId": "d75GxV1adCrKJBs928wtn2", "sync": false}, {"__type__": "dd5c4fmh3FKTpU01zzJZNUC", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "targetNode": {"__id__": 6}, "vacancyNode": {"__id__": 2}, "matchNode": {"__id__": 4}, "matchNodePrefab": {"__uuid__": "2cca219c-d622-486d-80f5-dd235048e537"}, "trashCanPrefab": {"__uuid__": "39903bf0-fd85-4c30-8e88-20399dadace9"}, "matchActive": {"__uuid__": "dfbea833-daf2-486b-a6b1-d2a82c19cb16"}, "matchGoBack": {"__uuid__": "c8111ab4-0685-4523-b052-7f514943c960"}, "matchClickBtn": {"__uuid__": "f6d2e2a0-fe1c-444b-9009-ddba257149bc"}, "finger": {"__uuid__": "08d4d959-1a8f-4625-ada4-f672c83db239"}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "7fcd16ed-4e98-4509-92d4-95f564533e9d"}, "fileId": "", "sync": false}]