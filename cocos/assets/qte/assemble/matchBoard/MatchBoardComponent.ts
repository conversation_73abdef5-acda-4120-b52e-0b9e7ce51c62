/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2021-09-02 19:10:04
 * @modify date 2021-09-02 19:10:04
 * @desc [火柴面板组件]
 */

import ComponentUtils from "../componentUtils/ComponentUtils";
import BaseComponent from "../core/BaseComponent";
import {
    AreaType,
    MatchData,
    MatchData2NumberMap,
    MatchOperateType,
    MatchState,
    MatchTag,
    MatchType,
    QuestionType
} from "./Enum";
import MatchComponent from "./MatchComponent";
import QTEUtils from "../../util/QTEUtils";

const { ccclass, property } = cc._decorator;

@ccclass
export default class MatchBoardComponent extends BaseComponent {
    private data: any;
    private matchList: Map<MatchTag, cc.Node[]>;
    private options;
    private nLockCount = 0;
    private moveFunc: Function;
    private moveEndFunc: Function;
    private setTargetCanTouch: Function
    private setTargetPos: Function
    private canMoveVacancy: cc.Node[];
    private curMoveTime = 0;
    private fakeNode: cc.Node = null
    private curMoveFuncTime = 0;
    private offset: cc.Vec2
    private isFirst: boolean = true;
    public canTouchTarget: number;
    @property(cc.Node)
    private targetNode: cc.Node = null
    @property(cc.Node)
    private vacancyNode: cc.Node = null
    @property(cc.Node)
    private matchNode: cc.Node = null
    @property(cc.Prefab)
    matchNodePrefab: cc.Prefab = null;
    @property(cc.Prefab)
    trashCanPrefab: cc.Prefab = null;
    @property(cc.AudioClip)
    matchActive: cc.AudioClip = null;
    @property(cc.AudioClip)
    matchGoBack: cc.AudioClip = null;
    @property(cc.AudioClip)
    matchClickBtn: cc.AudioClip = null;

    @property(cc.SpriteFrame)
    finger: cc.SpriteFrame = null

    public async initComponent(data?: any) {
        this.options = {
            onBegin: this.onDragBegin.bind(this),
            onMove: this.onDragMove.bind(this),
            onEnd: this.onDragEnd.bind(this),
            onCancel: this.onDragEnd.bind(this),
            preBeginFunc: this.preBeginFunc.bind(this),
            cantMoveFunc: this.cantMoveFunc.bind(this),
        };
        this.data = ComponentUtils.Instance().clone(data);
        await this.init();
    }

    public async changeProperties(key: string, data: any) {
        this.data.properties[key] = ComponentUtils.Instance().clone(data);
        const func = async () => {
            await this.init();
        }
        this.unschedule(func)
        this.scheduleOnce(func, 0.2)
    }

    public changeSkine(path: string, param?: any) {

    }

    /**
     * 显示正确答案
     */
    public showCorrect() {
        this.initChild()
        this.showMatch(2);
    }
    /**
     * 显示我的作答
     */
    public showUserAnswerState(answerStatus) {
        this.initChild()
        this.showMatch(3, answerStatus);
    }

    /**
     * 重置火柴面板
     */
    public async reset() {
        await this.init();
    }

    async init() {
        if (!this.data) return;
        this.node.parent.setContentSize(cc.size(1280, 720))
        this.node.parent.angle = 0;
        await this.creatFakeNode()
        this.initMatchList();
        this.initChild()
        this.showMatch(1)
    }

    private initChild() {
        const {
            isCanDrag,
            grid,
            showTargetArea,
            matchType,
            actionType
        } = this.data.properties
        const SPACE = 108
        let initMatchNodePos = cc.v2(0, 0);
        if (matchType == QuestionType.Pattern) {
            if (actionType == MatchOperateType.Add
                || actionType == MatchOperateType.Delete) {
                initMatchNodePos.y = SPACE
            }
            if (showTargetArea) {
                initMatchNodePos.x = (grid.col * SPACE - this.node.width) / 2 + 108
            }
        }
        this.vacancyNode.zIndex = 100;
        this.matchNode.zIndex = 200;
        this.targetNode.zIndex = isCanDrag ? 300 : 150;
        this.matchNode.setPosition(initMatchNodePos);
        this.vacancyNode.setPosition(initMatchNodePos);
        this.targetNode.removeAllChildren();
        this.matchNode.removeAllChildren();
        this.vacancyNode.removeAllChildren()
        const trashCan = this.node.getChildByName('trashCan');
        trashCan && trashCan.removeFromParent();
        const lockTargetArea = this.node.getChildByName('lockTargetArea');
        lockTargetArea && lockTargetArea.removeFromParent()
        const finger = this.node.getChildByName('finger');
        finger && finger.removeFromParent()
    }

    private async creatFakeNode() {
        if (this.fakeNode) {
            this.fakeNode.removeFromParent()
            this.fakeNode = null;
        }
        this.fakeNode = cc.instantiate(this.matchNodePrefab)
        this.fakeNode.name = 'fakeNode';
        this.fakeNode.setPosition(cc.v2(0, 1000))
        this.node.addChild(this.fakeNode)
        const {
            operationAreaBgOpacity,
            operationAreaMatchTexture,
            operationAreaColorfulMatchTexture,
            operationAreaMatchStatusTexture,
            operationAreaBgTexture,
            targetAreaMatchTexture,
            matchType
        } = this.data.properties;
        if (matchType == QuestionType.Formula) return
        if (typeof operationAreaBgOpacity == 'number')
            this.fakeNode.children[MatchState.Static].opacity = operationAreaBgOpacity;
        else
            this.fakeNode.children[MatchState.Static].opacity = 255;
        // 操作区火柴
        if (operationAreaMatchTexture && operationAreaMatchTexture != '') {
            await this.changeTexture(operationAreaMatchTexture, this.fakeNode.children[MatchState.Active])
        }
        // 操作区变色火柴
        if (operationAreaColorfulMatchTexture && operationAreaColorfulMatchTexture != '') {
            await this.changeTexture(operationAreaColorfulMatchTexture, this.fakeNode.children[MatchState.Diff])
        }
        // 操作区召唤态火柴
        if (operationAreaMatchStatusTexture && operationAreaMatchStatusTexture != '') {
            await this.changeTexture(operationAreaMatchStatusTexture, this.fakeNode.children[MatchState.Basket])
        }
        // 操作区底部火柴
        if (operationAreaBgTexture && operationAreaBgTexture != '') {
            await this.changeTexture(operationAreaBgTexture, this.fakeNode.children[MatchState.Static])
        }
        // 目标起火柴
        if (targetAreaMatchTexture && targetAreaMatchTexture != '') {
            await this.changeTexture(targetAreaMatchTexture, this.fakeNode.children[MatchState.Outline])
        }
    }

    private loadRes(url): Promise<any> {
        return new Promise<any>((resolve, reject) => {
           this.getRemoteRes(url, null, (error, res: cc.Asset) => {
                if (error) {
                    return;
                }
                if (res instanceof cc.Texture2D) {
                    res.setPremultiplyAlpha(true);
                    res.packable = false;
                }
                resolve(res);
            });
        }).catch(err => {
            console.log('cocos promise error:', err);
            return null;
        });
    }

    private async changeTexture(url, node) {
        if (!url || url == '' || !node) return
        const texture = await this.loadRes(url);
        const sprite = node.getComponent(cc.Sprite)
        sprite.spriteFrame = new cc.SpriteFrame(texture)
    }
    /**
     *
     * @param resultType 1 初始化 2 正确结果 3 自己作答 type为3时需传递answerStatus参数
     * @param answerStatus
     */
    private showMatch(resultType: number, answerStatus: number[][] = null) {
        const { matchType, rightAnswer, pattern, actionType, actionNumbers } = this.data.properties
        if (matchType == QuestionType.Formula) {
            this.replaceMulti(pattern);
            for (let i = 0; i < rightAnswer.length; i++) {
                this.replaceMulti(rightAnswer[i]);
            }
            switch (resultType) {
                case 1:
                    this.createFormula(pattern);
                    break;
                case 2:
                    this.createFormula(rightAnswer[0]);
                    break;
                case 3:
                    this.createFormula(answerStatus);
                    break;
                default:
                    this.createFormula(pattern);
                    break;
            }
        } else {
            switch (resultType) {
                case 1:
                    this.beforeCreatePattern()
                    this.createPattern(pattern);
                    break;
                case 2:
                    this.createPattern(rightAnswer[0]);
                    if (this.data.properties.answerDisplayCenter) {
                        this.vacancyNode.x = -this.node.parent.x
                        this.matchNode.x = -this.node.parent.x
                    }
                    break;
                case 3:
                    this.createPattern(answerStatus);
                    if (this.data.properties.answerDisplayCenter) {
                        this.vacancyNode.x = -this.node.parent.x
                        this.matchNode.x = -this.node.parent.x
                    }
                    break;
                default:
                    this.createPattern(pattern);
                    break;
            }
        }

        if (actionType == MatchOperateType.Add) {
            this.createAddMatch(actionNumbers);
        } else if (actionType == MatchOperateType.Delete) {
            this.createTrashCan();
        }

    }

    private initMatchList() {
        this.matchList = new Map();
        this.matchList.set(MatchTag.Match, []);
        this.matchList.set(MatchTag.Vacancy, []);
        this.matchList.set(MatchTag.TrashCan, []);
        this.matchList.set(MatchTag.Outline, []);
    }

    private beforeCreatePattern() {
        const { showTargetArea, isCanDrag } = this.data.properties
        if (showTargetArea) {
            this.createTargetArea()
            if (isCanDrag && !this.data.isEditor) {
                this.setLockTarget()
                if (this.isFirst) {
                    this.isFirst = false
                    this.createFinger()
                }
            }
        }
    }

    private setLockTarget() {
        this.canTouchTarget = 1;
        //重置的时候设置一次按钮状态
        if (this.setTargetCanTouch)
            this.setTargetCanTouch(this.canTouchTarget);
        const lockTarget = this.node.parent.parent.parent.getChildByName('lockTargetArea')
        lockTarget.getChildByName('lockBtn').active = this.canTouchTarget == 1
        lockTarget.getChildByName('moveBtn').active = this.canTouchTarget == 0
        lockTarget.zIndex = 100
        lockTarget.on(cc.Node.EventType.TOUCH_END, this.lockTargetArea, this)
        lockTarget.active = true
    }

    private createFinger() {
        const finger = new cc.Node('finger');
        const sprite = finger.addComponent(cc.Sprite);
        finger.setPosition(this.targetNode.getPosition())
        sprite.spriteFrame = this.finger;
        finger.anchorX = 0
        finger.anchorY = 1
        this.node.addChild(finger, 600)
        cc.tween(finger)
            .repeat(2, cc.tween()
                .to(1, { x: this.matchNode.x })
                .delay(0.5)
                .to(0, { x: this.targetNode.x }))
            .call(() => {
                finger.removeFromParent()
            })
            .start()
    }

    /**
     * 创建目标区
     */
    private createTargetArea() {
        this.targetNode.removeAllChildren();
        const { rightAnswer, obliqueMatch, isCanDrag, grid, selectTargetArea } = this.data.properties
        const targetData = ComponentUtils.Instance().clone(rightAnswer[selectTargetArea || 0]);
        if (!targetData) return
        const pattern = targetData.splice(0, grid.row * 2 + 1)
        if (obliqueMatch) {
            this.creatObliqueMatch(targetData, AreaType.TargetArea);
        }
        this.creatStraightMatch(pattern, AreaType.TargetArea)
        this.resizeTargetNode()
        if (isCanDrag) {
            this.targetNode.on(cc.Node.EventType.TOUCH_START, this.touchTargetStart, this);
            this.targetNode.on(cc.Node.EventType.TOUCH_MOVE, this.touchTargetMove, this);
            this.targetNode.on(cc.Node.EventType.TOUCH_CANCEL, this.touchTargetEnd, this);
            this.targetNode.on(cc.Node.EventType.TOUCH_END, this.touchTargetEnd, this);
        }
    }
    /**
     * 创建火柴图案
     * @param pattern 
     */
    private createPattern(pattern: number[][]) {
        const { obliqueMatch, grid } = this.data.properties
        const data = ComponentUtils.Instance().clone(pattern)
        const straightData = data.splice(0, grid.row * 2 + 1);
        if (obliqueMatch) {
            this.creatObliqueMatch(data, AreaType.OperationArea)
        }
        this.creatStraightMatch(straightData, AreaType.OperationArea)
    }

    /**
     * 创建火柴公式
     * @param formula
     */
    private createFormula(formula: number[][]) {
        for (let i = 0; i < formula.length; i++) {
            const array = formula[i];
            if (array.length == 7) {
                this.creatNumber(array, i);
            } else {
                this.creatOperator(array, i);
            }
        }
    }

    /**
     * 创建横竖向火柴
     * @param pattern
     * @param type
     */
    private creatStraightMatch(pattern: number[][], type: AreaType) {
        const SPACE = 108;
        const { row, col } = this.data.properties.grid
        const size = cc.size(col * SPACE, row * SPACE);
        for (let i = 0; i < pattern.length; i++) {
            for (let j = 0; j < pattern[i].length; j++) {
                const coordinate = `${i}_${j}`;
                const isHor = i % 2 == 0;
                const matchType = i % 2 == 0 ? MatchType.Horizontal : MatchType.Vertical
                const offsetX = isHor ? SPACE / 2 : 0;
                const offsetY = isHor ? 0 : SPACE / 2;
                const x = -size.width / 2 + j * SPACE + offsetX;
                const y = size.height / 2 - Math.floor(i / 2) * SPACE - offsetY
                const pos = cc.v3(x, y);
                let isNone = i % 2 == 0 && j == pattern[i].length - 1;
                if (isNone) {
                    continue;
                }
                // 创建火柴容器、创建火柴
                if (Number(pattern[i][j]) == 1) {
                    if (type === AreaType.OperationArea) {
                        this.creatVacancy(coordinate, matchType, pos);
                        this.creatMatch(coordinate, matchType, pos);
                    } else {
                        this.creatOutline(coordinate, matchType, pos)
                    }
                }
                else if (type === AreaType.OperationArea && this.canShowVacancy()) { // 题版里的删除模式不显示火柴空位
                    this.creatVacancy(coordinate, matchType, pos);
                }
            }
        }
    }

    /**
     * 创建横斜向火柴
     * @param data
     * @param type
     */
    private creatObliqueMatch(data, type) {
        const SPACE = 108;
        const { row, col } = this.data.properties.grid
        const size = cc.size(col * SPACE, row * SPACE);
        for (let i = 0; i < data.length; i++) {
            for (let j = 0; j < data[i].length; j++) {
                const k = Math.floor(j / 2)
                const coordinate = `${i}_${k}`;
                const matchType = j % 2 == 0 ? MatchType.Left : MatchType.Right;
                const x = -size.width / 2 + SPACE / 2 + SPACE * k;
                const y = size.height / 2 - SPACE / 2 - SPACE * i
                const pos = cc.v3(x, y);
                // 创建火柴容器、创建火柴
                if (Number(data[i][j]) == 1) {
                    if (type === AreaType.OperationArea) {
                        this.creatVacancy(coordinate, matchType, pos);
                        this.creatMatch(coordinate, matchType, pos);
                    }
                    else {
                        this.creatOutline(coordinate, matchType, pos)
                    }
                }
                else if (type === AreaType.OperationArea && this.canShowVacancy()) {
                    this.creatVacancy(coordinate, matchType, pos);
                }
            }
        }
    }

    /**
     * 创建数字
     * @param array
     * @param index
     */
    private creatNumber(array: number[], index) {
        const { pattern } = this.data.properties
        const { length } = pattern
        const SPACE = 80;
        const numberArr = this.parseArray(array);
        for (let i = 0; i < numberArr.length; i++) {
            for (let j = 0; j < numberArr[i].length; j++) {
                const coordinate = `${index}_${i + Math.floor(i / 2) + j}`;
                const id = `number_${coordinate}`;
                const isHor = i % 2 == 0;
                const matchType = isHor ? MatchType.Horizontal : MatchType.Vertical
                const offsetX = isHor ? SPACE / 2 : 0;
                const offsetY = isHor ? 0 : -SPACE / 2;
                const pos = cc.v3(-length / 2 * SPACE * 1.3 + index * SPACE * 1.3 + j * SPACE + offsetX, SPACE - Math.floor(i / 2) * SPACE + offsetY);
                const scale = SPACE / 108;
                // 创建火柴容器、创建火柴
                if (Number(numberArr[i][j]) == 1) {
                    this.creatVacancy(coordinate, matchType, pos, scale, id);
                    this.creatMatch(coordinate, matchType, pos, scale);
                } else if (this.canShowVacancy()) { // 题版里的删除模式不显示火柴空位
                    this.creatVacancy(coordinate, matchType, pos, scale, id);
                }
            }
        }
    }

    /**
     * 创建运算符
     * @param array
     * @param index
     */
    private creatOperator(array: number[], index: number) {
        const SPACE = 80;
        const scale = SPACE / 108;
        const { length } = this.data.properties.pattern;
        for (let i = 0; i < array.length; i++) {
            const item = array[i];
            if (Number(item) == 0) {
                continue;
            }
            const coordinate = `${index}_${i}`;
            const id = `operator_${coordinate}`;
            const isHor = i < 3;
            const matchType = isHor ? MatchType.Horizontal : MatchType.Vertical;
            const posX = -length / 2 * SPACE * 1.3 + index * 1.3 * SPACE + SPACE / 2;
            const posY = i < 3 ? SPACE / 4 - i * SPACE / 4 : 0;
            const pos = cc.v3(posX, posY);
            this.creatVacancy(coordinate, matchType, pos, scale, id);
            this.creatMatch(coordinate, matchType, pos, scale);
        }
        // 减号，添加一个竖杠
        if (array.join('') == '0100' && this.canShowVacancy()) {
            let posX = -length / 2 * SPACE * 1.3 + index * 1.3 * SPACE + SPACE / 2;
            let posY = 0
            let pos = cc.v3(posX, posY);
            let coordinate = `${index}_${3}`;
            this.creatVacancy(coordinate, MatchType.Vertical, pos, scale, `operator_${coordinate}`);
        }
    }

    /**
     * 创建可以添加的火柴
     * @param count 火柴数
     */
    private createAddMatch(count: number) {
        const { matchType, grid } = this.data.properties
        const scale = matchType == QuestionType.Formula ? 80 / 108 : 1;
        const posY = matchType == QuestionType.Formula ? this.matchNode.y - 80 - 150 : -((grid.row + 2) / 2 + 0.5) * 108;
        for (let i = 0; i < count; i++) {
            let pos = cc.v3(-(count - 1) / 2 * 100 + i * 100, posY);
            let id = `add_${i}`;
            this.creatVacancy(null, MatchType.Vertical, pos, scale, id);
            const node = this.creatMatch(`${i}`, MatchType.Vertical, pos, scale);
            node.getComponent(MatchComponent).enabled = true;
        }
    }

    /**
     * 创建垃圾桶火柴数
     */
    private createTrashCan() {
        const { matchType, grid } = this.data.properties
        const node = cc.instantiate(this.trashCanPrefab);
        let tag = 'trashCan';
        node.name = tag;
        const comp = node.getComponent(MatchComponent);
        comp.coordinate = tag;
        comp.id = tag;
        comp.options = this.options;
        comp.enabled = false;
        comp.state = MatchState.Static;
        let posY = matchType == QuestionType.Formula ? 80 + 150 : grid.row / 2 * 108 + 150
        node.position = cc.v3(this.matchNode.x, this.matchNode.y - posY);
        this.node.addChild(node, 400);
        this.addMatchNode(MatchTag.TrashCan, node);
    }

    /**
     * 创建火柴
     * @param data
     */
    private creatNode(data: MatchData) {
        const node = cc.instantiate(this.fakeNode);
        node.position = data.position;
        const comp = node.getComponent(MatchComponent);
        comp.id = data.id;
        comp.name = comp.id;
        comp.coordinate = data.coordinate;
        comp.enabled = data.enabled;
        comp.state = data.state;
        comp.matchType = data.matchType;
        comp.canMove = data.canMove;
        comp.options = data.options;
        return node;
    }

    /**
     * 创建火柴
     * @param id
     * @param coordinate 逻辑坐标
     * @param matchType 横竖左右
     * @param pos 位置
     * @param scale
     */
    private creatMatch(coordinate: string, matchType: MatchType, pos: cc.Vec3, scale: number = 1, id: string = null) {
        const { actionType } = this.data.properties
        const data: MatchData = {
            position: pos,
            id: id || `match${matchType}_${coordinate}`,
            coordinate,
            enabled: actionType != MatchOperateType.Add,
            state: MatchState.Active,
            matchType,
            canMove: true,
            options: this.options
        }
        const node = this.creatNode(data)
        node.scale = scale
        this.matchNode.addChild(node);
        this.addMatchNode(MatchTag.Match, node);
        return node
    }

    /**
     * 创建火柴空位
     * @param id
     * @param coordinate 逻辑坐标
     * @param matchType 横竖左右
     * @param pos 位置
     * @param scale
     */
    private creatVacancy(coordinate: string, matchType: MatchType, pos: cc.Vec3, scale: number = 1, id: string = null) {
        const data: MatchData = {
            position: pos,
            id: id || `vacancy${coordinate}`,
            coordinate,
            enabled: false,
            state: MatchState.Static,
            matchType,
            canMove: false,
        }
        const node = this.creatNode(data)
        node.scale = scale;
        this.vacancyNode.addChild(node);
        coordinate && this.addMatchNode(MatchTag.Vacancy, node);
    }

    /**
     * 创建火柴轮廓
     * @param coordinate 逻辑坐标
     * @param matchType 横竖左右
     * @param pos 位置
     */
    private creatOutline(coordinate: string, matchType: MatchType, pos: cc.Vec3) {
        const data: MatchData = {
            position: pos,
            id: `outline${coordinate}`,
            coordinate,
            enabled: false,
            state: MatchState.Outline,
            matchType,
            canMove: false,
        }
        const node = this.creatNode(data)
        this.targetNode.addChild(node);
        this.addMatchNode(MatchTag.Outline, node);
    }

    /**
     * 重设目标区
     */
    private resizeTargetNode() {
        const { targetAreaX, targetAreaY, targetAreaScale, targetAreaAngle, grid } = this.data.properties
        const SPACE = 108
        let left = grid.col * SPACE / 2, top = -grid.row * SPACE / 2, right = -grid.col * SPACE / 2, bottom = grid.row * SPACE / 2
        let matches = this.matchList.get(MatchTag.Outline);
        matches.forEach(match => {
            const comp = match.getComponent('MatchComponent')
            const offsetX = comp.matchType === MatchType.Vertical ? 0 : SPACE / 2;
            const offsetY = comp.matchType === MatchType.Horizontal ? 0 : SPACE / 2
            left = match.x - offsetX < left ? match.x - offsetX : left;
            top = match.y + offsetY > top ? match.y + offsetY : top;
            right = match.x + offsetX > right ? match.x + offsetX : right;
            bottom = match.y - offsetY < bottom ? match.y - offsetY : bottom;
        });
        const distanceX = -(right + left) / 2
        const distanceY = -(top + bottom) / 2
        matches.forEach(match => {
            match.x += distanceX;
            match.y += distanceY
        });
        this.targetNode.width = right - left;
        this.targetNode.height = top - bottom;
        let initTargetNodeX = this.matchNode.x
            + this.data.properties.grid.col * SPACE / 2
            + SPACE
            + this.targetNode.width / 2
        if (targetAreaX || targetAreaX == 0) {
            initTargetNodeX = targetAreaX
        }
        this.targetNode.x = initTargetNodeX;
        this.targetNode.y = targetAreaY || 0;
        this.targetNode.scale = targetAreaScale || 1;
        this.targetNode.angle = targetAreaAngle || 0;
    }


    /**
     * 解析编码数组
     * @param array 
     * @returns 
     */
    private parseArray(array: number[]): number[][] {
        let pattern = Array(5).fill(0).map(v => new Array());
        let index = 0;
        for (let i = 0; i < array.length; i++) {
            const element = array[i];
            pattern[index].push(element);
            if (i % 3 == 0 || i % 3 == 2) {
                index++;
            }
        }
        return pattern;
    }

    /**
     * 将 '1' 的编码调整为 '0010010'
     * @param array
     * @returns
     */
    private replaceOne(array: number[]): number[] {
        if ('0100100' == array.join('')) {
            array = [0, 0, 1, 0, 0, 1, 0];
        }
        return array;
    }

    /**
     * 将 '-' 的编码调整为 '0100'
     * @param array
     * @returns
     */
    private replaceSub(array: number[]): number[] {
        let strArray = ['0010', '1000'];
        if (strArray.includes(array.join(''))) {
            array = [0, 1, 0, 0];
        }
        return array;
    }

    /**
     * 将火柴公式中的复合编码归一化
     * @param question
     */
    private replaceMulti(question: number[][]) {
        for (let i = 0; i < question.length; i++) {
            question[i] = this.replaceOne(question[i]);
            question[i] = this.replaceSub(question[i]);
        }
    }

    /**********************************************************************************************/

    private onDragBegin(entity: cc.Node) {
        this.playAudio(this.matchActive);
        const comp = entity.getComponent(MatchComponent)
        comp.originAngle = entity.angle;
        if (comp.matchType === MatchType.Vertical || comp.matchType === MatchType.Horizontal) {
            entity.angle = 45;
        }
        else {
            entity.angle = 90;
        }
        this.buildCanMoveVacancy();
    }

    private onDragMove(entity: cc.Node) {
        // 碰撞检测抽帧
        if (Date.now() - this.curMoveTime > 50) {
            this.resetState();
            let vacancy = this.checkCollision(entity);
            if (vacancy) {
                vacancy.getComponent(MatchComponent).state = MatchState.Basket;
            }
            this.curMoveTime = Date.now();
            // 延时同步拖拽状态
            if (Date.now() - this.curMoveFuncTime > 200) {
                this.moveFunc && this.moveFunc(entity, vacancy, this.data.properties.actionType);
                this.curMoveFuncTime = Date.now();
            }
        }
    }

    private onDragEnd(entity: cc.Node) {
        this.resetState();
        this.dragEndHandler(entity).then();
    }

    private preBeginFunc(entity: cc.Node) {
        if (this.data.properties.actionType != MatchOperateType.Delete) {
            return this.checkCanMove(entity);
        }
    }

    /**
     * 检查是否可以移动
     * @param entity 
     */
    private checkCanMove(entity: cc.Node) {
        let moveCount = this.getMoveCount();
        let matchComp = entity.getComponent(MatchComponent);
        matchComp.canMove = moveCount < this.data.properties.actionNumbers || matchComp.state == MatchState.Diff;
        return matchComp.canMove;
    }


    /**
     * 检查是否可以删除
     * @param entity 
     */
    private checkCanDelete() {
        return this.getDeleteCount() < this.data.properties.actionNumbers;
    }

    /**
     * 不可移动的回调
     * @param entity 
     */
    private cantMoveFunc(entity: cc.Node) {
        this.showToast(`最多移动${this.data.properties.actionNumbers}根火柴哦`);
    }

    public setMoveEndFunc(func: Function) {
        this.moveEndFunc = func;
    }

    public setMoveFunc(func: Function) {
        this.moveFunc = func;
    }

    public setTargetCanTouchFunc(func: Function) {
        this.setTargetCanTouch = func;
    }

    public setTargetPosFunc(func: Function) {
        this.setTargetPos = func
    }

    /**
     * 检查是否操作了
     * @returns 
     */
    public checkIsOperated(): boolean {
        if (this.data.properties.actionType == MatchOperateType.Delete) {
            return this.getDeleteCount() > 0
        }
        return this.getMoveCount() > 0;
    }

    /**
     * 已经移动了火柴的数量
     * @returns
     */
    private getMoveCount() {
        let matchList = this.getMatchListByTag(MatchTag.Match);
        return matchList.filter((match) => match.getComponent(MatchComponent).state == MatchState.Diff).length;
    }

    /**
     * 已经删除了火柴的数量
     * @returns
     */
    private getDeleteCount() {
        const matchList = this.getMatchListByTag(MatchTag.Match);
        return matchList.filter((match) => !match.active).length;
    }

    private checkCollision(entity: cc.Node): cc.Node {
        if (this.data.properties.actionType == MatchOperateType.Delete) {
            return this.checkDeleteCollision(entity);
        } else {
            if (this.data.properties.obliqueMatch) {
                return this.checkObliqueMoveCollision(entity)
            }
            else {
                return this.checkMoveCollision(entity);
            }
        }
    }

    /**
     * 当斜向火柴开启时
     * 检查拖拽对象和拖拽区域的碰撞结果
     * @param entity 拖拽对象
     * @return 拖拽区域
     */
    checkObliqueMoveCollision(entity: cc.Node) {
        let tempArr = [];
        let pos = entity.getPosition();
        for (let vacancy of this.canMoveVacancy) {
            const p = vacancy.getPosition();
            let { width, height } = vacancy.getContentSize()
            width += 20;
            height += 30;
            const comp: MatchComponent = vacancy.getComponent(MatchComponent);
            const params = Math.sin(Math.PI / 4);
            const offsetH = height / 2 * params
            const offsetW = width / 2 * params
            let point1, point2, point3, point4
            switch (comp.matchType) {
                case MatchType.Horizontal:
                    point1 = cc.v2(p.x - width / 2, p.y + height / 2)
                    point2 = cc.v2(p.x + width / 2, p.y + height / 2)
                    point3 = cc.v2(p.x - width / 2, p.y - height / 2)
                    point4 = cc.v2(p.x + width / 2, p.y - height / 2)
                    break;
                case MatchType.Vertical:
                    point1 = cc.v2(p.x - height / 2, p.y + width / 2)
                    point2 = cc.v2(p.x + height / 2, p.y + width / 2)
                    point3 = cc.v2(p.x - height / 2, p.y - width / 2)
                    point4 = cc.v2(p.x + height / 2, p.y - width / 2)
                    break;
                case MatchType.Right:
                    point1 = cc.v2(p.x - offsetH + offsetW, p.y + offsetH + offsetW);
                    point2 = cc.v2(p.x + offsetH + offsetW, p.y - offsetH + offsetW);
                    point3 = cc.v2(p.x - offsetH - offsetW, p.y + offsetH - offsetW);
                    point4 = cc.v2(p.x + offsetH - offsetW, p.y - offsetH - offsetW);
                    break;
                case MatchType.Left:
                    point1 = cc.v2(p.x - offsetH - offsetW, p.y - offsetH + offsetW);
                    point2 = cc.v2(p.x + offsetH - offsetW, p.y + offsetH + offsetW);
                    point3 = cc.v2(p.x - offsetH + offsetW, p.y - offsetH - offsetW);
                    point4 = cc.v2(p.x + offsetH + offsetW, p.y + offsetH - offsetW);
                    break;
            }
            const pointArr: cc.Vec2[] = [point1, point2, point3, point4];
            const dragArr: cc.Vec2[] = [cc.v2(pos.x - 8, pos.y - 8), cc.v2(pos.x + 8, pos.y - 8), cc.v2(pos.x - 8, pos.y + 8), cc.v2(pos.x + 8, pos.y + 8)];
            //if (cc.Intersection.pointInPolygon(pos, pointArr)) {
            if (cc.Intersection.polygonPolygon(dragArr, pointArr)) {
                tempArr.push({
                    len: p.sub(pos).mag(),
                    rect: pointArr,
                    node: vacancy
                });
            }

        }
        if (tempArr.length > 0) {
            tempArr.sort((a, b) => a.len - b.len);
            //优先筛选向右倾斜的火柴
            if (tempArr.length > 1 && Math.floor(tempArr[0].len) == Math.floor(tempArr[1].len)) {
                if (tempArr[1].node.angle == 45)
                    return tempArr[1].node;
                else
                    return tempArr[0].node;
            }
            return tempArr[0].node;
        }
        return null;
    }

    /**
     * 当斜向火柴关闭时
     * 检查拖拽对象和拖拽区域的碰撞结果
     * @param entity 拖拽对象
     * @return 拖拽区域
     */
    private checkMoveCollision(entity: cc.Node): cc.Node {
        let tempArr = [];
        let pos = entity.getPosition();
        let len = entity.getContentSize().width / 3;
        let a1 = cc.v2(pos.x - len, pos.y - len)
        let a2 = cc.v2(pos.x + len, pos.y + len)
        for (let vacancy of this.canMoveVacancy) {
            let p = vacancy.getPosition();
            let l = vacancy.getContentSize().width / 2;
            let b1 = vacancy.angle == 0 ? cc.v2(p.x - l, p.y) : cc.v2(p.x, p.y - l);
            let b2 = vacancy.angle == 0 ? cc.v2(p.x + l, p.y) : cc.v2(p.x, p.y + l);
            if (cc.Intersection.lineLine(a1, a2, b1, b2)) {
                tempArr.push({
                    len: p.sub(pos).mag(),
                    node: vacancy
                });
            }
        }
        if (tempArr.length > 0) {
            tempArr.sort((a, b) => a.len - b.len);
            return tempArr[0].node;
        }
        return null;
    }

    /**
     * 生成可填充的火柴容器列表
     */
    private buildCanMoveVacancy() {
        let vacancyList = this.getMatchListByTag(MatchTag.Vacancy);
        let matchList = this.getMatchListByTag(MatchTag.Match);
        this.canMoveVacancy = vacancyList.filter((vacancy) => {
            return matchList.every((match) => {
                const matchComp = match.getComponent(MatchComponent)
                const vacancyComp = vacancy.getComponent(MatchComponent)
                if (!this.data.properties.obliqueMatch) {
                    return matchComp.coordinate != vacancyComp.coordinate
                }
                return matchComp.coordinate != vacancyComp.coordinate
                    || (matchComp.coordinate == vacancyComp.coordinate
                        && matchComp.matchType != vacancyComp.matchType)
            })
        });
    }

    /**
     * 检查火柴与垃圾桶的碰撞结果
     * @param entity 
     * @returns 
     */
    private checkDeleteCollision(entity: cc.Node): cc.Node {
        const pos1 = QTEUtils.convertToStageSpaceAR(entity, cc.v2(0, 0));
        const pos2 = QTEUtils.convertStageToNodeSpaceAR(this.node, pos1)
        let trashCanList = this.getMatchListByTag(MatchTag.TrashCan);
        for (let i = 0; i < trashCanList.length; i++) {
            const trashCan = trashCanList[i];
            let rect = trashCan.getBoundingBox();
            if (rect.contains(pos2)) {
                return trashCan;
            }
        }
        return null;
    }

    /**
     * 拖拽动作结束
     * @param entity 
     */
    private async dragEndHandler(entity: cc.Node) {
        let vacancy = this.checkCollision(entity);
        if (vacancy) {
            if (this.data.properties.actionType == MatchOperateType.Delete) {
                await this.dragDeleteEndHandler(entity, vacancy);
            } else {
                await this.dragMoveEndHandler(entity, vacancy);
            }
            this.moveEndFunc && this.moveEndFunc(entity);
        } else {
            this.resetPosition(entity);
        }
    }

    private async resetPosition(entity: cc.Node) {
        let matchComp = entity.getComponent(MatchComponent);
        matchComp.changeLastStayPos(matchComp.originLocal);
        const local = matchComp.originLocal;
        const angle = matchComp.originAngle;
        this.blockInput();
        if (entity.position.sub(matchComp.originLocal).mag() > 5) {
            this.playAudio(this.matchGoBack);
        }
        const tween = cc.tween(entity).to(0.1, { x: local.x, y: local.y, angle: angle }).call(() => {
            this.unblockInput();
        });
        await this.playTween(tween);
    }

    /**
     * 获取玩家答题数据
     * @returns 
     */
    public getAnswer(): number[][] {
        const { matchType: questionType, pattern, grid } = this.data.properties
        let answer = pattern.map(array => array.map(v => 0));
        let matches = this.matchList.get(MatchTag.Match);
        for (const match of matches) {
            const { coordinate, matchType } = match.getComponent(MatchComponent);
            if (coordinate && match.active) {
                const arr = coordinate.split('_');
                let i = Number(arr[0]);
                let j = Number(arr[1]);
                if (matchType == MatchType.Left || matchType == MatchType.Right) {
                    const offset = matchType == MatchType.Left ? 0 : 1;
                    i += grid.row * 2 + 1
                    j = j * 2 + offset
                }
                answer[i][j] = 1;
            }
        }
        if (questionType == QuestionType.Formula) {
            this.replaceMulti(answer);
            this.deleteNull(answer);
        }
        return answer;
    }

    /**
     * 算术题删掉全空位的数
     * @param answer 
     */
    private deleteNull(answer: number[][]) {
        for (let i = 0; i < answer.length; i++) {
            const array = answer[i];
            let strArr = array.join('');
            if (strArr == '0000000' || strArr == '0000') {
                answer.splice(i, 1);
            }
        }
    }

    /**
     * 检查是否答题正确
     * @returns 
     */
    public checkCorrect(): boolean {
        const { rightAnswer, grid, obliqueMatch } = this.data.properties
        let answer = this.getAnswer();
        qte.log('用户作答', answer, '正确答案', rightAnswer)
        if (!obliqueMatch) {
            for (let i = 0; i < rightAnswer.length; i++) {
                const { correct } = this.checkAnswer(answer, rightAnswer[i])
                if (correct) {
                    return correct
                }
            }
            return false
        }
        const newAnswer = ComponentUtils.Instance().clone(answer);
        const straightAnswer = newAnswer.splice(0, grid.row * 2 + 1);
        for (let i = 0; i < rightAnswer.length; i++) {
            const correctAnswer = ComponentUtils.Instance().clone(rightAnswer[i]);
            const straightRightAnswer = correctAnswer.splice(0, grid.row * 2 + 1);
            const straightResult = this.checkAnswer(straightAnswer, straightRightAnswer)
            const obliqueResult = this.checkAnswer(newAnswer, correctAnswer)
            if (straightResult.correct && obliqueResult.correct) {
                if (straightResult.offset != null && obliqueResult.offset != null) {
                    if (obliqueResult.offset.x * 2 != straightResult.offset.x
                        && straightResult.offset.y * 2 != obliqueResult.offset.y) {
                        return false
                    }
                }
                return true
            }
        }
        return false
    }

    /**
     * 检查横竖向是否正确
     * @param userAnswer
     * @param rightAnswer
     */
    private checkAnswer(userAnswer, rightAnswer): { correct: boolean, offset: any } {
        let answerPos: cc.Vec2[] = [];
        let tmpAnswerPos: cc.Vec2[] = [];
        userAnswer.forEach((arr, row) => {
            arr.forEach((v, col) => {
                v == 1 && answerPos.push(cc.v2(row, col));
            });
        });
        rightAnswer.forEach((arr, row) => {
            arr.forEach((v, col) => {
                v == 1 && tmpAnswerPos.push(cc.v2(row, col));
            });
        });
        // 答案是空的情况
        if (tmpAnswerPos.length == 0) {
            return {
                correct: answerPos.length == 0,
                offset: null
            }
        }
        if (answerPos.length != tmpAnswerPos.length) {
            return {
                correct: false,
                offset: null
            }
        }
        let isCorrect = true;
        const offsetX = tmpAnswerPos[0].x - answerPos[0].x;
        const offsetY = tmpAnswerPos[0].y - answerPos[0].y;
        for (let j = 0; j < tmpAnswerPos.length; j++) {
            const tempOffsetX = tmpAnswerPos[j].x - answerPos[j].x;
            const tempOffsetY = tmpAnswerPos[j].y - answerPos[j].y;
            if (tempOffsetX !== offsetX || tempOffsetY !== offsetY) {
                isCorrect = false;
                break;
            }
        }
        return {
            correct: isCorrect,
            offset: isCorrect ? { x: offsetX, y: offsetY } : null
        };
    }

    /**
     * 移动结束后的逻辑
     * @param entity 移动的火柴
     * @param vacancy 落位
     */
    private async dragMoveEndHandler(entity: cc.Node, vacancy: cc.Node) {
        this.playAudio(this.matchActive);
        let comp = vacancy.getComponent(MatchComponent);
        let matchComp = entity.getComponent(MatchComponent);
        let arr = comp.coordinate.split('_');
        let i = Number(arr[0]);
        let j = Number(arr[1]);
        if (comp.matchType === MatchType.Left || comp.matchType === MatchType.Right) {
            const offset = comp.matchType === MatchType.Left ? 0 : 1;
            i += this.data.properties.grid.row * 2 + 1
            j = j * 2 + offset
        }
        matchComp.state = this.data.properties.pattern[i][j] == 1 ? MatchState.Active : MatchState.Diff;
        matchComp.changeLastStayPos(vacancy.position);
        matchComp.originLocal = vacancy.position;
        matchComp.coordinate = comp.coordinate;
        this.blockInput();
        const tween = cc.tween(entity).to(0.1, { x: vacancy.position.x, y: vacancy.position.y, angle: vacancy.angle }).call(() => {
            comp.state = MatchState.Static;
            matchComp.matchType = comp.matchType;
            this.unblockInput();
        });
        await this.playTween(tween);
    }

    /**
     * 删除结束后的处理
     * @param entity 删除的火柴
     * @param trashCan 垃圾桶
     */
    private async dragDeleteEndHandler(entity: cc.Node, trashCan: cc.Node) {
        if (this.checkCanDelete()) {
            this.playAudio(this.matchActive);
            entity.active = false;
            entity.getComponent(MatchComponent).state = MatchState.Diff;
            trashCan.getComponent(MatchComponent).state = MatchState.Static;
        } else {
            await this.resetPosition(entity);
            this.showToast(`最多删除${this.data.properties.actionNumbers}根火柴哦`);
        }
    }

    private playTween(tween: cc.Tween): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            tween.call(resolve).start();
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
        });
    }

    private resetState() {
        if (this.data.properties.actionType == MatchOperateType.Delete) {
            this.resetTrashcan();
        } else {
            this.resetVacancy();
        }
    }

    /**
     * 重置空位状态
     */
    private resetVacancy() {
        let vacancyList = this.getMatchListByTag(MatchTag.Vacancy);
        for (const vacancy of vacancyList) {
            vacancy.getComponent(MatchComponent).state = MatchState.Static;
        }
    }

    /**
     * 重置垃圾桶状态
     */
    private resetTrashcan() {
        let trashCanList = this.getMatchListByTag(MatchTag.TrashCan);
        for (const trashCan of trashCanList) {
            trashCan.getComponent(MatchComponent).state = MatchState.Static;
        }
    }

    /**
     * 解析火柴公式
     * @param answer 
     * @returns 
     */
    public parseFormulaAnswer(answer): string {
        let str = '';
        for (let i = 0; i < answer.length; i++) {
            const array = answer[i];
            if (array.length == 7) {
                let num = MatchData2NumberMap.get(array.join(''));
                str = str.concat(num);
            } else {
                let operator = MatchData2NumberMap.get(array.join(''));
                str = str.concat(operator);
            }
        }
        console.log(str);
        return str;
    }

    private addMatchNode(tag: MatchTag, node: cc.Node) {
        this.matchList.get(tag).push(node);
    }

    public getMatchListByTag(tag: MatchTag) {
        return this.matchList.get(tag);
    }

    private canShowVacancy(): boolean {
        return this.data.properties.actionType != MatchOperateType.Delete || this.data.isEditor;
    }

    private blockInput() {
        let block = this.node.getChildByName("MatchBoard-BlockMask");
        if (!block) {
            block = new cc.Node("MatchBoard-BlockMask");
            block.width = cc.winSize.width;
            block.height = cc.winSize.height;
            block.addComponent(cc.BlockInputEvents);
            block.zIndex = cc.macro.MAX_ZINDEX;
            block.parent = this.node;
            block.on(cc.Node.EventType.TOUCH_START, () => {
            });
        }
        block.x = 0;
        block.y = 0;
        this.nLockCount++;
        cc.tween(block)
            .delay(60)
            .call(() => {
                this.nLockCount = 0;
                this.unblockInput();
            })
            .start();
    }

    private unblockInput() {
        this.nLockCount--;
        if (this.nLockCount <= 0) {
            if(cc.isValid(this.node,true)){
                let block = this.node.getChildByName("MatchBoard-BlockMask");
                if (block) {
                    cc.Tween.stopAllByTarget(block);
                    block.y = cc.winSize.height * 2;
                }
            }
            this.nLockCount = 0;
        }
    }
    private touchTargetStart(event) {
        if (this.canTouchTarget == 0) return
        const finger = this.node.getChildByName('finger');
        if (finger) {
            this.unscheduleAllCallbacks()
            finger.stopAllActions()
            finger.removeFromParent()
        }
        const pos1 = event.getLocation()
        const pos2 = cc.v2(pos1.x - cc.winSize.width / 2, pos1.y - cc.winSize.height / 2);
        const pos3 = QTEUtils.convertStageToNodeSpaceAR(event.currentTarget.parent, pos2)
        this.offset = cc.v2(this.targetNode.x - pos3.x, this.targetNode.y - pos3.y)
    }
    private touchTargetMove(event) {
        if (this.canTouchTarget == 0) return
        const pos1 = event.getLocation()
        const pos2 = cc.v2(pos1.x - cc.winSize.width / 2, pos1.y - cc.winSize.height / 2);
        const pos3 = QTEUtils.convertStageToNodeSpaceAR(event.currentTarget.parent, pos2)
        this.setTargetPos(cc.v2(pos3.x + this.offset.x, pos3.y + this.offset.y))
    }
    private touchTargetEnd(event) {
        if (this.canTouchTarget == 0) return
        const pos1 = event.getLocation()
        const pos2 = cc.v2(pos1.x - cc.winSize.width / 2, pos1.y - cc.winSize.height / 2);
        const pos3 = QTEUtils.convertStageToNodeSpaceAR(event.currentTarget.parent, pos2)
        this.setTargetPos(cc.v2(pos3.x + this.offset.x, pos3.y + this.offset.y))
    }

    private lockTargetArea() {
        this.playAudio(this.matchClickBtn);
        this.canTouchTarget = this.canTouchTarget == 0 ? 1 : 0
        this.setTargetCanTouch(this.canTouchTarget)
    }
}
