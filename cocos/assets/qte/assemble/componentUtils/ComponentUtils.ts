

class Delay {

    key     : string;   //key
    time    : number;   //间隔多久
    current : number;   //当前时间戳

    constructor(k, v) {

        this.key  = k;
        this.time = v;

    }
}

export default class ComponentUtils {
    private static _instance: ComponentUtils = null;
    public static Instance(): ComponentUtils {
        if (!this._instance) {
            this._instance = new ComponentUtils();
        }
        return this._instance;
    }

    public static destroyInstance(): void {
        this._instance = null;
    }
    playingSounds: any[] = [];
    // 音频id
    private audioIdList = [];
    // bridge
    private get cocosBridge (): any {
        // @ts-ignore
        return window._cocos_bridge;
    }

    private  delayMap : { [key : string] : Delay; } = {
        "button" : new Delay("button", 500),
        "toast" : new Delay("toast", 2000)
    };

    public isPlaying = false;

    /**
     * 检测是否延迟
     * @param key 
     * @returns true : 延迟 false : 不需要延迟
     */
     public CheckDelay(key : string) : boolean {

        let delay = this.delayMap[key];
        if(!delay) return true;

        let tick = (new Date()).getTime();

        let time = tick - delay.current

        if(time < delay.time) return true;

        delay.current = tick;

        return false;
    }

    /** 播放音频 */
    public playAudio (url, cb: (data) => void) {
        try {
            this.isPlaying = true;
            if (!CC_PREVIEW && this.cocosBridge && this.isInApp()) {
                const params = {
                    url,
                    playStatus: 1
                };
                if (this.isNetRes(url)) {
                    let audioId = this.cocosBridge.soundManager.playAudio(url, false, cb);
                    this.audioIdList.push(audioId);
                } else {
                    this.cocosBridge.commonSdk.postMessageHybrid("nativeAudioDisplay", params, cb);
                }
            } else {
                cb && cb("");
            }
        } catch (error) {
            console.log(error);
        }
    }

    /** 停止音频 */
    public stopAudio (url) {
        this.isPlaying = false;
        if (!CC_PREVIEW && this.cocosBridge && this.isInApp()) {
            const params = {
                url,
                playStatus: -1
            };
            if (this.isNetRes(url)) {
                for (let i = 0; i < this.audioIdList.length; i++) {
                    this.cocosBridge.soundManager.stopAudio(this.audioIdList[i]);
                }
                this.audioIdList = [];
            } else {
                this.cocosBridge.commonSdk.postMessageHybrid("nativeAudioDisplay", params, () => {});
            }
        }
    }

    private isNetRes (path: string): boolean {
        if (path.startsWith("http://") || path.startsWith("https://") || path.startsWith("zybhost://")) {
            return true;
        }
        return false;
    }


    uploadFile (params, callback) {
        if (this.isInApp()) {
            if (!params) {
                params = {};
            }
            this.cocosBridge.commonSdk.postMessageHybrid("uploadFile", params, (res) => {
                const code = res.code || res.errcode;
                const resData = res.data;
                if (code === 200) {
                    callback && callback(resData);
                } else if (code === 404) {
                    this.cocosBridge.showDialog({
                        title: "提示",
                        description: "此Action不存在！",
                        neutralText: "确定",
                        cancelOutside: 1
                    });
                } else {
                    this.cocosBridge.showDialog({
                        title: "提示",
                        description: "Action调用异常！",
                        neutralText: "确定",
                        cancelOutside: 1
                    });
                }
            });
        } else {
            console.error("uploadFile 只能在APP中使用！");
        }
    }

    uploadLocalFile (filePath: string) {
        if (!this.isInApp()) {
            return;
        }
        return new Promise((resolve, reject) => {
            this.uploadFile(
                {
                    type: 1,
                    filePath,
                    url: "https://yingyu.yayaketang.com/yaya-user-inclass/api/audioUpload",
                    fileName: "audioFile",
                    formData: {
                        filePath,
                        url: "https://yingyu.yayaketang.com/yaya-user-inclass/api/audioUpload",
                        fileName: "audioFile"
                    }
                },
                (data) => {
                    console.log("上传本地音频返回data", data);
                    if (data.data && data.data.errNo === 0) {
                        resolve(data);
                    } else {
                        reject();
                    }
                }
            );
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err)
          });
    }

    checkRecordAudioPermission (): Promise<boolean> {
        if (this.isInApp()) {
            return this.getPromise((resolve, reject) => {
                this.cocosBridge.commonSdk.postMessageHybrid("core_checkRecordAudioPermission", {}, (res) => {
                    if (res?.data?.permission === 1) {
                        resolve(true);
                    } else {
                        reject(false);
                    }
                });
            });
        } else {
            return this.getPromise((resolve, reject) => {
                resolve(true);
            });
        }
    }

    applyRecordAudioPermission () {
        if (this.isInApp()) {
            return this.getPromise((resolve, reject) => {
                this.cocosBridge.commonSdk.postMessageHybrid("core_applyRecordAudioPermission", {}, (res) => {
                    if (res?.data?.permission === 0) {
                        resolve(true);
                    } else {
                        reject(false);
                    }
                });
            }).catch(err => {
                qte && qte.logCatBoth('cocos promise error:', err)
              });
        }
    }

    public getPromise<T> (executor: (resolve: (value: T | PromiseLike<T>) => void, reject: (reason?: any) => void) => void): Promise<T> {
        return new Promise(executor);
    }


    public isInApp () {
        return (/siwei/i).test(navigator.userAgent) || (/yuwen/i).test(navigator.userAgent) || (/homework/i).test(navigator.userAgent) || (/airclass/i).test(navigator.userAgent) || (/kid/i).test(navigator.userAgent);
    }

    /** 中文逗号替换为英文 */
    public cnToen (str: string): string {
        let resultStr = "";
        if (str.indexOf('，') < 0) {
            return str;
        }

        let index = str.indexOf('，');
        resultStr = str.substring(0, index) + "," + str.substring(index + 1, str.length);
        resultStr = this.cnToen(resultStr);
        return resultStr;
    }

    public handelEvaluatingText (str: string): [] {
        const getText = function (Str) {
            const arr = Str.match(/[\u4e00-\u9fa5]{1,}/g);
            return arr;
        };
        const getEnglish = function (Str) {
            const arr = Str.match(/[a-zA-Z]{1,}/g);
            return arr;
        };

        if (this.checkChinese(str)) {
            return getText(str).toString();
        }

        return getEnglish(str).toString();
    }

    // 是否包含汉子
    public checkChinese(val): boolean{
        var reg = new RegExp("[\\u4E00-\\u9FFF]+","g");
        if(reg.test(val)) {
           return true;
        }
        return false;
    }

    public clone(obj) {
        let type = Object.prototype.toString.call(obj).slice(8, -1).toLowerCase();
        if (type === "object") {
            let json = {};
            for (let i in obj) {
                if (obj.hasOwnProperty(i)) {
                    json[i] = this.clone(obj[i]);
                }
            }
            return json;
        } else if (type === "array") {
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
                arr[i] = this.clone(obj[i]);
            }
            return arr;
        }
        return obj;
    }
}