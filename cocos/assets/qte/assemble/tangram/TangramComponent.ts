/**
 * <AUTHOR>
 * @email [<EMAIL>]
 * @create date 2021-11-09 19:29:01
 * @modify date 2021-11-09 19:29:01
 * @desc [七巧板组件]
 */

import ComponentUtils from "../componentUtils/ComponentUtils";
import BaseComponent from "../core/BaseComponent";
import { TangramEditArea, TangramLeftData } from "./InitMock";

const { ccclass, property } = cc._decorator;

/**
 * 图形类型
 */
enum PolygonType {
    Triangle,
    Square,
    Parallelogram
}

export interface SelectedData {
    position: cc.Vec3;
    active: boolean;
    size: cc.Size;
    showFlip: boolean;
}

/**
 * 图形类
 */
export class Polygon {
    public angle: number;
    public scale: number;
    public type: PolygonType;
    public color: cc.Color;
    public position: cc.Vec2;
    public flip: boolean;
    public index: number;

    constructor() {
        this.type = PolygonType.Triangle;
        this.angle = 0;
        this.scale = 1;
        this.position = cc.v2(0, 0);
        this.color = cc.Color.GRAY;
        this.flip = false;
        this.index = 0;
    }

    /**
     * 获取图形的点集合
     * @readonly
     * @type {cc.Vec2[]}
     * @memberof Polygon
     */
    public get points(): cc.Vec2[] {
        let points = [];
        let basePoints: cc.Vec2[];
        let sqrt2 = Math.SQRT2;
        switch (this.type) {
            case PolygonType.Triangle:
                basePoints = [cc.v2(0, sqrt2 / 4), cc.v2(sqrt2 / 2, -sqrt2 / 4), cc.v2(-sqrt2 / 2, -sqrt2 / 4)];
                break;
            case PolygonType.Square:
                basePoints = [cc.v2(-0.5, 0.5), cc.v2(0.5, 0.5), cc.v2(0.5, -0.5), cc.v2(-0.5, -0.5)];
                break;
            case PolygonType.Parallelogram:
                basePoints = [cc.v2(0, 0.5), cc.v2(1, 0.5), cc.v2(0, -0.5), cc.v2(-1, -0.5)];
                break;
        }
        basePoints.forEach(bp => {
            bp.rotateSelf(this.angle * Math.PI / 180);
            if (this.flip) {
                bp.x = -bp.x;
            }
            let p = bp.mul(this.scale);
            let point = cc.v2(p.x + this.position.x, p.y + this.position.y);
            points.push(point);
        });
        return points;
    }

    /**
     * 克隆一个当前图形
     * @returns 
     */
    public clone(): Polygon {
        let polygon = new Polygon();
        polygon.type = this.type;
        polygon.angle = this.angle;
        polygon.scale = this.scale;
        polygon.position = this.position.clone();
        polygon.color = this.color.clone();
        polygon.index = this.index;
        polygon.flip = this.flip;
        return polygon;
    }

}

@ccclass
export default class TangramComponent extends BaseComponent {

    @property(cc.Graphics) graphics: cc.Graphics = null;
    @property(cc.Node) selectNode: cc.Node = null;

    /** 编辑器中左边静态展示的图形集合 */
    private staticPolygons: Polygon[];
    /** 可拖动的图形集合 */
    private polygons: Polygon[];
    /** 图形答案-答题区 */
    private answer: Polygon[];
    /** 正确答案 */
    private correctAnswer: Polygon[];
    /** 提示 */
    private hint: Polygon;
    /** 当前是否选中了图形 */
    private selected: boolean;
    /** 当前选中的图形 */
    private curPolygon: Polygon;
    /** 触摸点与图形位置的偏移 */
    private offsetPos: cc.Vec2;
    /** 翻转按钮 */
    private flipNode: cc.Node;
    /** 选择按钮 */
    private rotateNode: cc.Node;
    /** 是否编辑器 */
    private isEditor: boolean = false;
    /** 是否触摸了 */
    private touched: boolean = false;
    /** 编辑区 */
    private editArea: cc.Rect;
    /** Vue 传过来的数据 */
    private data;
    /** 保存的初始数据 */
    private saveData;
    /** 图形变动回调，用以向题版同步数据 */
    private changeFunc: Function;
    /** 操作记录的回调 */
    private operatedFunc: Function;
    /** 是否答题正确的回调 */
    private correctFunc: Function;
    /** 选中框状态变化的回调 */
    private selectedFunc: Function;
    /** 数据同步时间 */
    private lastUpdateTime: number;
    /** 当前是否可以绘制 */
    private _canDraw: boolean = false;

    public get canDraw(): boolean {
        return this._canDraw;
    }
    public set canDraw(value: boolean) {
        if (this._canDraw == value) {
            return;
        }
        !value && this.drawPolygon();
        this._canDraw = value;
    }

    onLoad() {

    }

    public initComponent(data?: any) {
        this.data = ComponentUtils.Instance().clone(data);
        this.saveData = ComponentUtils.Instance().clone(data);
        this.flipNode = cc.find('flip', this.selectNode);
        this.rotateNode = cc.find('rotate', this.selectNode);
        this.isEditor = data.isEditor;

        if (this.isEditor) {
            this.node.on("mousedown_proxy", this.onTouchStart, this);
            this.node.on("mousemove_proxy", this.onTouchMove, this);
            this.node.on("mouseup_proxy", this.onTouchEnd, this);
            // 编辑区位置、大小
            this.editArea = cc.rect(...TangramEditArea);
        } else {
            this.node.on(cc.Node.EventType.TOUCH_START, this.onTouchStart, this);
            this.node.on(cc.Node.EventType.TOUCH_MOVE, this.onTouchMove, this);
            this.node.on(cc.Node.EventType.TOUCH_END, this.onTouchEnd, this);
            let size = this.node.getContentSize();
            this.editArea = cc.rect(-size.width / 2, -size.height / 2, size.width, size.height);
        }
        this.init();
    }

    public changeProperties(key: string, data: any) {
        this.data.properties[key] = ComponentUtils.Instance().clone(data);
        if (key == "rightAnswer") {
            for (let i = 0; i < data.length; i++) {
                const p = data[i];
                this.polygons[i].type = p.type;
                this.polygons[i].angle = p.angle;
                this.polygons[i].scale = p.scale;
                this.polygons[i].position = cc.v2(p.position);
                this.polygons[i].color = this.convertColor(p.color._val);
                this.polygons[i].flip = p.flip;
                this.polygons[i].index = p.index;
                if (p.index == 1) {
                    this.curPolygon = this.polygons[i];
                }
            }
            if (this.curPolygon) {
                this.selectNode.position = cc.v3(this.curPolygon.position);
            }
        }
        this.drawPolygon();
    }

    public changeSkine(path: string, param?: any) {

    }

    public setChangeFunc(func: Function) {
        this.changeFunc = func;
    }

    public setOperatedFunc(func: Function) {
        this.operatedFunc = func;
    }

    public setCorrectFunc(func: Function) {
        this.correctFunc = func;
    }

    public setSelectedFunc(func: Function) {
        this.selectedFunc = func;
    }

    /**
     * 设置 selected 节点的属性
     * @param data 
     */
    public setSelectedData(data: SelectedData) {
        this.selectNode.active = data.active;
        this.selectNode.position = data.position;
        this.selectNode.setContentSize(data.size);
        this.flipNode.position = cc.v3(-data.size.width / 2, data.size.height / 2);
        this.rotateNode.position = cc.v3(data.size.width / 2, data.size.height / 2);
        this.flipNode.active = data.showFlip;
    }

    private init() {
        this.lastUpdateTime = 0;
        this.staticPolygons = []
        this.polygons = [];
        this.answer = [];
        this.correctAnswer = [];
        this.selectNode.active = false;
        this.initLeft();
        this.initRight();
        this.drawPolygon();
    }

    /**
     * 初始化左边区域
     */
    private initLeft() {
        TangramLeftData.forEach(p => {
            let polygon = this.convertPolygon(p);
            if (this.isEditor) {
                this.staticPolygons.push(polygon);
            } else {
                this.polygons.push(polygon);
            }
        });
    }

    /**
     * 初始化右边区域
     */
    private initRight() {
        let rightAnswer = this.data.properties.rightAnswer;
        rightAnswer.forEach(p => {
            let polygon = this.convertPolygon(p);
            if (this.isEditor) {
                polygon.color = this.convertColor(p.color._val);
                this.polygons.push(polygon);
            } else {
                polygon.color = cc.color(233, 247, 253);
                this.answer.push(polygon);
            }
            let polygonCorrect = this.convertPolygon(p);
            this.correctAnswer.push(polygonCorrect);
        });
    }

    /**
     * 将对象转为类
     * @param p 
     * @returns 
     */
    private convertPolygon(p) {
        let polygon = new Polygon();
        polygon.type = p.type;
        polygon.angle = p.angle;
        polygon.scale = p.scale;
        polygon.position = cc.v2(p.position);
        polygon.color = this.convertColor(p.color._val);
        polygon.flip = p.flip;
        polygon.index = p.index;
        return polygon;
    }

    private convertColor(c: number) {
        return cc.color(c & 0x000000ff, (c & 0x0000ff00) >> 8, (c & 0x00ff0000) >> 16, (c & 0xff000000) >> 24);
    }

    public reset() {
        this.data = ComponentUtils.Instance().clone(this.saveData);
        this.init();
        this.updatePropertiesToVue();
        this.updatePropertiesToQuestion();
    }

    private getTouchPosition(touch) {
        return this.isEditor ? cc.v3(touch._x - cc.winSize.width / 2, touch._y - cc.winSize.height / 2) : touch.getLocation();
    }

    private onTouchStart(touch) {
        let touchPos = this.getTouchPosition(touch);
        if (this.isEditor && this.selected) {
            let selectPos = this.selectNode.convertToNodeSpaceAR(touchPos);
            let flipRect = this.flipNode.getBoundingBox()
            if (this.flipNode.active && flipRect.contains(selectPos)) {
                this.onFlipClick();
                return;
            }
            let rotateRect = this.rotateNode.getBoundingBox()
            if (rotateRect.contains(selectPos)) {
                this.onRotateClick();
                return;
            }
        }
        this.touched = true;
        this.selected = false;

        let pos = cc.v2(0, 0);
        if (this.isEditor) {
            pos = this.node.convertToNodeSpaceAR(touchPos);
        } else {
            pos = cc.v2(touchPos.x - cc.winSize.width / 2, touchPos.y - cc.winSize.height / 2);
        }

        for (let i = 0; i < this.polygons.length; i++) {
            const polygon = this.polygons[i];
            polygon.index = 0;
            if (cc.Intersection.pointInPolygon(pos, polygon.points)) {
                this.selected = true;
                polygon.index = 1;
                this.offsetPos = pos.sub(polygon.position);
                this.curPolygon = polygon;
                this.selectNode.position = cc.v3(polygon.position);
                let size = cc.size(polygon.scale + 80, polygon.scale + 80);
                this.selectNode.setContentSize(size);
                this.flipNode.position = cc.v3(-size.height / 2, size.width / 2);
                this.rotateNode.position = cc.v3(size.height / 2, size.width / 2);
                this.flipNode.active = polygon.type == PolygonType.Parallelogram;
                break;
            }
        }
        this.selectNode.active = this.selected;
        this.polygons.sort((a, b) => b.index - a.index);
    }

    private onTouchMove(touch) {
        if (!this.touched || !this.selected) return;
        let touchPos = this.getTouchPosition(touch);
        let pos = cc.v2(0, 0);
        if (this.isEditor) {
            pos = this.node.convertToNodeSpaceAR(touchPos);
        } else {
            pos = cc.v2(touchPos.x - cc.winSize.width / 2, touchPos.y - cc.winSize.height / 2);
        }

        let newPos = pos.sub(this.offsetPos);
        this.clampEditArea(newPos);
        if (!this.isEditor) {
            let curTime = Date.now();
            if (curTime - this.lastUpdateTime > 50) {
                this.updatePropertiesToQuestion();
                this.lastUpdateTime = curTime;
            }
        }
        this.selectNode.position = cc.v3(this.curPolygon.position);
        this.checkHint();
        this.canDraw = true;
    }

    private async onTouchEnd(touch) {
        this.touched = false;
        if (this.selected) {
            if (this.hint) {
                let vec = this.hint.position.sub(this.curPolygon.position);
                await this.moveAnimation(vec);
                this.hint = null;
            }
            this.canDraw = false;
            this.checkCorrect();
            this.updatePropertiesToVue();
        }
        this.updatePropertiesToQuestion();
    }


    /**
     * 同步数据到 Vue
     * @returns 
     */
    private updatePropertiesToVue() {
        if (!this.isEditor) {
            return;
        }
        let str = JSON.stringify(this.polygons);
        let tempArr = JSON.parse(str);
        // 生成图形数据
        this.data.updateComponentProperties && this.data.updateComponentProperties({
            "rightAnswer": tempArr
        });
    }

    /**
     * 同步数据到题版
     * @returns 
     */
    private updatePropertiesToQuestion() {
        if (this.isEditor) {
            return;
        }
        let str = JSON.stringify(this.polygons);
        let tempArr = JSON.parse(str);
        // 同步图形数据
        this.changeFunc && this.changeFunc(tempArr);
        // 同步选中框属性
        let data: SelectedData = {
            position: this.selectNode.position,
            active: this.selectNode.active,
            size: this.selectNode.getContentSize(),
            showFlip: this.flipNode.active
        }
        this.selectedFunc && this.selectedFunc(data);
    }

    /**
     * 限定图形在编辑区内
     * @param pos 
     */
    private clampEditArea(pos: cc.Vec2) {
        let polygon = this.curPolygon.clone();
        polygon.position = pos;
        let canSetX = polygon.points.every(p => p.x <= this.editArea.xMax && p.x >= this.editArea.xMin);
        if (canSetX) {
            this.curPolygon.position.x = pos.x;
        }
        let canSetY = polygon.points.every(p => p.y <= this.editArea.yMax && p.y >= this.editArea.yMin);
        if (canSetY) {
            this.curPolygon.position.y = pos.y;
        }
    }

    /**
     * 检测临近点，并提示
     */
    private checkHint() {
        let LEN = 25;
        let points = this.curPolygon.points;
        let minVec = cc.v2(LEN, LEN);
        points.forEach(point => {
            let array = this.isEditor ? this.polygons : this.answer;
            for (let j = 0; j < array.length; j++) {
                const polygon = array[j];
                if (this.curPolygon.position.equals(polygon.position)) {
                    continue;
                }
                polygon.points.forEach(p => {
                    let v = p.sub(point);
                    if (v.mag() < minVec.mag()) {
                        minVec = v.clone();
                    }
                });
            }
        });
        if (minVec.mag() < LEN) {
            let polygon = this.curPolygon.clone();
            polygon.position = this.curPolygon.position.add(minVec);
            polygon.color.a = 170;
            this.hint = polygon;
        } else {
            this.hint = null;
        }
    }

    /**
     * 绘制所有图形
     */
    private drawPolygon() {
        if(!cc.isValid(this.node,true))
            return;
        this.graphics.clear();
        let draw = (array: Polygon[]) => {
            for (let i = array.length - 1; i >= 0; i--) {
                const polygon = array[i];
                this.graphics.strokeColor = polygon.color;
                this.graphics.fillColor = polygon.color;
                let points = polygon.points;
                let length = points.length;
                this.graphics.moveTo(points[length - 1].x, points[length - 1].y);
                for (let i = 0; i < length; i++) {
                    this.graphics.lineTo(points[i].x, points[i].y);
                }
                this.graphics.stroke();
                this.graphics.fill();
            }
        }
        if (this.isEditor) {
            let color = cc.color(246, 246, 246);
            this.graphics.fillColor = color;
            this.graphics.strokeColor = color;
            this.graphics.roundRect(...TangramEditArea, 25);
            this.graphics.fill();
            draw(this.staticPolygons);
        } else {
            draw(this.answer);
        }

        draw(this.polygons);
        if (this.hint) {
            draw([this.hint]);
        }
    }

    /**
     * 检测是否正确
     */
    public checkCorrect() {
        if (this.isEditor) {
            return;
        }
        let polygonInPolygon = (arr1: Polygon[], arr2: Polygon[]) => {
            return arr1.every(polygon => polygon.points.every(point => {
                let rect = cc.rect(point.x - 2, point.y - 2, 4, 4);
                return arr2.some(p => cc.Intersection.rectPolygon(rect, p.points));
            }));
        }
        // A 在 B 中，并且，B 在 A 中， A 自己不重叠，即为重合
        let isIn1 = polygonInPolygon(this.polygons, this.answer);
        let isIn2 = polygonInPolygon(this.answer, this.polygons);
        let overlap = this.checkOverlap();
        console.log(`isIn1 = ${isIn1}, isIn2 = ${isIn2}, overlap = ${overlap}`);
        // 标记为已操作
        this.operatedFunc && this.operatedFunc(true);
        // 标记是否作对
        this.correctFunc && this.correctFunc(isIn1 && isIn2 && !overlap);
    }

    /**
     * 检测是否重叠
     */
    private checkOverlap(): boolean {
        let tempArr = [];
        this.polygons.forEach(polygon => {
            let p = polygon.clone();
            p.scale -= 2;
            tempArr.push(p);
        });
        return tempArr.some(polygon => polygon.points.some(point => {
            return this.polygons.some(p => p.position.sub(polygon.position).mag() > 10 && cc.Intersection.pointInPolygon(point, p.points));
        }));
    }

    private async moveAnimation(vec: cc.Vec2): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            let v = vec.div(10);
            cc.tween(this.curPolygon)
                .by(0.2, { position: vec })
                .by(0.05, { position: v })
                .by(0.05, { position: v.neg() })
                .by(0.05, { position: v.neg() })
                .by(0.05, { position: v })
                .by(0.05, { position: v })
                .by(0.05, { position: v.neg() })
                .call(() => {
                    if (this.curPolygon?.position) {
                        this.selectNode.position = cc.v3(this.curPolygon.position);
                    } else {
                    }
                    resolve();
                }).start();
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
        });;
    }

    update(dt) {
        this.canDraw && this.drawPolygon();
    }

    /**
     * 点击旋转按钮
     */
    private onRotateClick() {
        // TODO 选中发题,学生端恢复后点击选中元素会卡死;
        if (!this.curPolygon) {
            return;
        }
        let angle = this.curPolygon.flip ? -45 : 45;
        this.curPolygon.angle -= angle;
        this.rotateOrFlip();
    }

    /**
     * 点击翻转按钮
     */
    private onFlipClick() {
        // TODO 选中发题,学生端恢复后点击选中元素会卡死;
        if (!this.curPolygon) {
            return;
        }
        this.curPolygon.flip = !this.curPolygon.flip;
        this.rotateOrFlip();
    }

    private rotateOrFlip() {
        // this.checkHint();
        // if (this.hint) {
        //     this.curPolygon.position = this.hint.position;
        //     this.hint = null;
        //     this.selectNode.position = cc.v3(this.curPolygon.position);
        // }
        this.updatePropertiesToVue();
        this.updatePropertiesToQuestion();
        this.drawPolygon();
        this.checkCorrect();
    }

    public getAnswer() {
        return this.polygons;
    }

    public showUserAnswerState(answer) {
        let userAnswer = [];
        answer.forEach(p => {
            let polygon = this.convertPolygon(p);
            userAnswer.push(polygon);
        });

        this.graphics.clear();
        let draw = (array: Polygon[]) => {
            for (let i = array.length - 1; i >= 0; i--) {
                const polygon = array[i];
                this.graphics.strokeColor = polygon.color;
                this.graphics.fillColor = polygon.color;

                let points = polygon.points;
                let length = points.length;
                this.graphics.moveTo(points[length - 1].x, points[length - 1].y);
                for (let i = 0; i < length; i++) {
                    this.graphics.lineTo(points[i].x, points[i].y);
                }
                this.graphics.stroke();
                this.graphics.fill();
            }
        }

        draw(this.answer);
        draw(userAnswer);
    }

    public showCorrect() {
        this.graphics.clear();
        let draw = (array: Polygon[]) => {
            for (let i = array.length - 1; i >= 0; i--) {
                const polygon = array[i];
                this.graphics.strokeColor = polygon.color;
                this.graphics.fillColor = polygon.color;

                let points = polygon.points;
                let length = points.length;
                this.graphics.moveTo(points[length - 1].x, points[length - 1].y);
                for (let i = 0; i < length; i++) {
                    this.graphics.lineTo(points[i].x, points[i].y);
                }
                this.graphics.stroke();
                this.graphics.fill();
            }
        }

        draw(this.correctAnswer);
    }
}
