/* eslint-disable max-lines */




import BaseComponent from "../../core/BaseComponent";
import ComponentFactory from "../../core/ComponentFactory";
import { keyboardEnum } from "./KeyboardConfig";



/**
 * 组件名称：键盘组件
 * 作者：武建凯
 * 
 *
 */
const { ccclass, property } = cc._decorator;

@ccclass
export default class KeyboardComponent extends BaseComponent {
    public currentNode: cc.Node = null//当前键盘实体
    public eventNode: cc.Node = null//事件接收节点
    @property({ type: cc.AudioClip, displayName: "点击声音" })
    public audioClick: cc.AudioClip = null;

    @property({ type: cc.Node, displayName: "键盘节点" })
    pcGuideNode: cc.Node = null;

    childNodeIndexMap = {
        "1": 0,
        "2": 1,
        "3": 2,
        "4": 3,
        "5": 4,
        "6": 5,
        "7": 6,
        "8": 7,
        "9": 8,
        "0": 9,
        ".": 10,
        "x": 11
    }
    /**
     * 换肤
     * @param key 
     * @param data 
     */
    public changeProperties(key: string, data: any) {
        if (key == "keyboardType") {
            //键盘类型切换
            this.initComponent({
                properties: {
                    keyboardType: data || keyboardEnum.COCOS_NORMAL
                }
            })
        }

    }
    /**
     * 获取键盘样式实体
     * @param bundle 
     * @param typeName 
     */
    public createCore(bundle: cc.AssetManager.Bundle, typeName: string): Promise<cc.Node> {
        return new Promise<cc.Node>((reslove) => {
            let path = `assemble/keyboard/prefabs/keyboardUi/${typeName}/prefab/entry`;
            bundle.load(path, cc.Prefab, (err, assets: cc.Prefab) => {
                if (err) {
                    console.error(err);
                    return;
                }
                let node = cc.instantiate(assets);
                console.log(assets);
                node.active = true;
                reslove(node);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err)
            return null;
        });
    }

    //初始化键盘
    public init() {


    }
    /**
     * 初始化键盘组件
     * @param data 
     */
    async initComponent(data?: any) {

        let type = data ? data.properties.keyboardType : keyboardEnum.COCOS_NORMAL;
        if (this.currentNode) {
            this.currentNode.destroy();
        }
        this.currentNode = await this.createCore(ComponentFactory.getInstance().bundle, type);
        if (!cc.isValid(this.node, true)) {
            return;
        }
        this.node.addChild(this.currentNode)
        this.currentNode.children.forEach((btn: cc.Node) => {
            btn.on(cc.Node.EventType.TOUCH_START, this.cellTouchStart, this)
            btn.on(cc.Node.EventType.TOUCH_CANCEL, this.cellTouchEnd, this)
            btn.on(cc.Node.EventType.TOUCH_END, this.cellTouchEnd, this)

        })
        this.pcGuideNode.zIndex = 1;
    }


    /**
     * 点击单个键盘
     */
    public cellTouchStart(e: cc.Event.EventTouch) {
        let node: cc.Node = e.target;
        node['isPingTouch'] = true;
        this.updateTouchStartUI(node)

    }

    updateTouchStartUI(node: cc.Node) {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        node.children[1].active = true;
        if (this.audioClick) {
            this.playAudio(this.audioClick)
        }
    }

    updateTouchEndUI(node: cc.Node) {
        if (!cc.isValid(this.node, true)) {
            return;
        }
        node.children[1].active = false
    }

    /**
     * 松手单个键盘
     */
    public cellTouchEnd(e: cc.Event.EventTouch) {
        console.log("WKK点击了")
        let node: cc.Node = e.target;
        this.updateTouchEndUI(node);
        node['isPingTouch'] = false;
        this.eventNode.emit("keyboardTouch", node.name)
    }

    pcKeyboardTouch(str: string) {
        let index = this.childNodeIndexMap[str];
        if (this.currentNode && this.currentNode.children[index]) {
            let node = this.currentNode.children[index];
            this.updateTouchStartUI(node);
            this.eventNode.emit("keyboardTouch", node.name);
            node.stopAllActions();
            cc.tween(node).delay(0.2).call(() => {
                if (!node['isPingTouch']) {
                    this.updateTouchEndUI(node);
                }
            }).start();
        }


    }


    public changeSkine(path: string, param?: any) {
        throw new Error("Method not implemented.");
    }

    playPCGuide() {
        if (this.pcGuideNode) {
            this.pcGuideNode.active = true;
            let orX = this.pcGuideNode.x;
            cc.tween(this.pcGuideNode).to(0.1, { x: orX+20 }).to(0.2, { x: orX-20 }).to(0.125, { x: orX+10 }) .to(0.1, { x: orX-10 }).to(0.05,{x:orX}).delay(2.5).call(() => { this.pcGuideNode.active = false }).start();
        }
    }



}