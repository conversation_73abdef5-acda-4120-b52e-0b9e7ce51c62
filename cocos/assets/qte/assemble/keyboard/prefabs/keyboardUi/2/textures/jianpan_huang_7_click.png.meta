{"ver": "2.3.7", "uuid": "a5022d40-8b66-485f-9335-48ec3e6785db", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_7_click": {"ver": "1.0.6", "uuid": "73eff22c-2438-4ec4-be75-b50e73b948dc", "importer": "sprite-frame", "rawTextureUuid": "a5022d40-8b66-485f-9335-48ec3e6785db", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0.5, "offsetY": 0, "trimX": 24, "trimY": 20, "width": 33, "height": 40, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}