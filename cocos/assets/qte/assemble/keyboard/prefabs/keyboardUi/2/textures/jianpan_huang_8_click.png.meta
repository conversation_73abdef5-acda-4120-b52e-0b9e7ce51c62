{"ver": "2.3.7", "uuid": "1d4c1659-f4f1-47fc-b93a-c6a796e8ca9e", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_8_click": {"ver": "1.0.6", "uuid": "1b5ade75-c1b5-424b-83b0-625ec38034b5", "importer": "sprite-frame", "rawTextureUuid": "1d4c1659-f4f1-47fc-b93a-c6a796e8ca9e", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 23, "trimY": 20, "width": 34, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}