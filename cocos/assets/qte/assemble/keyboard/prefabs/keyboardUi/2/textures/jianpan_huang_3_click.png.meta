{"ver": "2.3.7", "uuid": "7054d947-**************-bb369702d349", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_3_click": {"ver": "1.0.6", "uuid": "e1f49b16-5df0-49dc-a08b-3feb6208cb2c", "importer": "sprite-frame", "rawTextureUuid": "7054d947-**************-bb369702d349", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 24, "trimY": 20, "width": 32, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}