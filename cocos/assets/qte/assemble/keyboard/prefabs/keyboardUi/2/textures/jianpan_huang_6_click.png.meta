{"ver": "2.3.7", "uuid": "0bedfe00-b51a-4411-90dd-fbb1889646c5", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_6_click": {"ver": "1.0.6", "uuid": "aa408ed1-5dca-470e-a044-eb277d128387", "importer": "sprite-frame", "rawTextureUuid": "0bedfe00-b51a-4411-90dd-fbb1889646c5", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 23, "trimY": 20, "width": 34, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}