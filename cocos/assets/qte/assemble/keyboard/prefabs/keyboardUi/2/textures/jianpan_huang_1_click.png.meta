{"ver": "2.3.7", "uuid": "b68c4fdd-343a-4906-965d-f14a2f89159d", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_1_click": {"ver": "1.0.6", "uuid": "cf678cc1-66a6-4aec-b3c6-6980aee5b154", "importer": "sprite-frame", "rawTextureUuid": "b68c4fdd-343a-4906-965d-f14a2f89159d", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 29, "trimY": 20, "width": 22, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}