{"ver": "2.3.7", "uuid": "75950a26-8079-4f92-9953-c928fcd79bf3", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_5_click": {"ver": "1.0.6", "uuid": "f3efc77b-04ad-49c3-8d7c-294ad33b3e9c", "importer": "sprite-frame", "rawTextureUuid": "75950a26-8079-4f92-9953-c928fcd79bf3", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 24, "trimY": 20, "width": 32, "height": 40, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}