{"ver": "2.3.7", "uuid": "e49bd573-ad0a-467e-8ad0-1376d1c1b154", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_huang_9_click": {"ver": "1.0.6", "uuid": "bfdd5c72-7db0-4553-8c93-a388468d7689", "importer": "sprite-frame", "rawTextureUuid": "e49bd573-ad0a-467e-8ad0-1376d1c1b154", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 23, "trimY": 20, "width": 34, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}