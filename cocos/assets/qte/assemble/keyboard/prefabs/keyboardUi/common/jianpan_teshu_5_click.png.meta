{"ver": "2.3.7", "uuid": "09eb75d5-75fc-435d-8334-0abb2b902baf", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_teshu_5_click": {"ver": "1.0.6", "uuid": "a78ba834-7f65-438c-89fa-3456a26f7973", "importer": "sprite-frame", "rawTextureUuid": "09eb75d5-75fc-435d-8334-0abb2b902baf", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 24, "trimY": 20, "width": 32, "height": 40, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}