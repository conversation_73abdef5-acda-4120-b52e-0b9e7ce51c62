{"ver": "2.3.7", "uuid": "ecba7a11-0591-4525-93d3-ef994b7adc59", "importer": "texture", "type": "sprite", "wrapMode": "clamp", "filterMode": "bilinear", "premultiplyAlpha": true, "genMipmaps": false, "packable": true, "width": 80, "height": 80, "platformSettings": {}, "subMetas": {"jianpan_hong_9_click": {"ver": "1.0.6", "uuid": "d898e271-cc4d-4bc6-a855-c35bfcd1672f", "importer": "sprite-frame", "rawTextureUuid": "ecba7a11-0591-4525-93d3-ef994b7adc59", "trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -0.5, "trimX": 23, "trimY": 20, "width": 34, "height": 41, "rawWidth": 80, "rawHeight": 80, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "subMetas": {}}}}