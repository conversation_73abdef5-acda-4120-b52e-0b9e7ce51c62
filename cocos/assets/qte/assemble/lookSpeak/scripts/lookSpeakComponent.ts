import BaseComponent from "../../core/BaseComponent";


const { ccclass, property } = cc._decorator;

const enum LOOK_TYPE {
    start,
    inProgress,
    end
}
const enum TIPS_STRING {
    start = '点击话筒开始录音',
    inProgress = '录音中，再次点击话筒提交',
    end = ''
}

@ccclass
export default class lookSpeakComponent extends BaseComponent {

    private countDown: cc.Node = null;
    private countProgress: cc.ProgressBar = null;
    private soundWaveSpines: sp.Skeleton[] = [];
    private labTips:cc.Label = null;
    private lookType:LOOK_TYPE = LOOK_TYPE.start;

    // 倒计时计树
    public countDownNum: number = 15;
    private counterIndex = 0;
    private subFunc = null;
      /** 更换资源 */
    public changeSkine(path: string) {
        // -- TODO
        // this.voiceStyle = style;
    }

    onLoad(): void {
        this.countDown = this.node.getChildByName("daojishi");
        this.countDown.on("click", this.onClick, this);
        this.countProgress = this.countDown.getChildByName("progress").getComponent(cc.ProgressBar);
        this.countProgress.progress = 1;

        this.soundWaveSpines.push(this.countDown.getChildByName("spine1").getComponent(sp.Skeleton));
        this.soundWaveSpines.push(this.countDown.getChildByName("spine2").getComponent(sp.Skeleton));

        this.labTips = this.countDown.getChildByName('lab_tips').getComponent(cc.Label);
        this.labTips.string = TIPS_STRING.start;

        this.lookType = LOOK_TYPE.start;

        this.counterIndex = 0;

    }

    initComponent(component){
       console.log("component",component);
       component.properties && component.properties.answerDuration && (this.countDownNum = component.properties.answerDuration);
       console.log("this.countDownNum==",this.countDownNum);
    }
    initHook(hookFunc){
        this.subFunc = hookFunc;
    }
    private onClick() {
        this.handleState();
        
    }
    private handleState(){
        switch(this.lookType){
            case LOOK_TYPE.start:
                this.lookType = LOOK_TYPE.inProgress;
                this.setSoundWaveSpines(LOOK_TYPE.inProgress);
                this.labTips.string = TIPS_STRING.inProgress;
                this.countProgress.node.active = true;
                this.countdownFunc();
                break;
            case LOOK_TYPE.inProgress:
                this.lookType = LOOK_TYPE.end;
                this.labTips.string = TIPS_STRING.end;
                this.countProgress.node.active = false;
                this.setSoundWaveSpines(LOOK_TYPE.end);
                this.unSchedule();
                this.subFunc();
                break;
            case LOOK_TYPE.end:
                break;
        }
    }
    /**
     * @msg     : 倒计时
     * @param    {*}
     * @return   {*}
     */    
    private countdownFunc(){
        this.schedule(()=>{
            if(this.counterIndex >= this.countDownNum * 10){
                this.unSchedule();
                this.handleState();
            }
            this.countProgress.progress = 1 - this.counterIndex / (this.countDownNum * 10);
            this.counterIndex++;
        },0.1);
    }
    unSchedule(){
        this.unscheduleAllCallbacks();
    }
    // data.properties
    /**
   * @msg     : 设置 spine 动画
   * @param    {LOOK_TYPE} status
   * @return   {*}
   */

    private setSoundWaveSpines(status: LOOK_TYPE) {
        let name = null;
        if (status === LOOK_TYPE.inProgress) {
            name = "animation";
        } else {
            name = null;
        }
        for (let i = 0; i < this.soundWaveSpines.length; i++) {
            let spine = this.soundWaveSpines[i];
            this.soundWaveSpines[i].animation = name;
            this.soundWaveSpines[i].loop = true;
            this.soundWaveSpines[i].timeScale = 1;
            this.soundWaveSpines[i].node.active = true;
            spine.setCompleteListener((trackEntry, loopCount) => {
                spine.animation = name;
            });
        }
    }
    reset() {
        this.lookType = LOOK_TYPE.start;
        this.setSoundWaveSpines(LOOK_TYPE.start);
        this.labTips.string = TIPS_STRING.start;
        this.countProgress.progress = 1;
        this.countProgress.node.active = true;
        this.counterIndex = 0;
        this.unSchedule();
    }

    changeProperties() {}

}
