
/**
 * MathUtil: 数学计算相关工具接口
 * <AUTHOR>
 */
export default class MathUtil {
    /**
     * 误差值
     */
    public static readonly EPSILON: number = 1.0e-6;

    /**
     * 临时点
     */
    private static readonly _TEMP_P = new cc.Vec2();

    /**
     * 获取权重值
     * @param {number[]} list 权重值列表 
     * @return {number} 返回随机到的权重list下标
     */
    public static getWeight (list: number[]): number {
        let count = 0;
        let newList = [];
        for (let w of list) {
            count += w;
            newList.push(count);
        }
        let val = Math.random() * count;
        for (let i = 0; i < newList.length; i++) {
            let cur = newList[i];
            if (val < cur) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 角度转弧度
     * @param deg 角度
     */
    public static readonly DEG_TO_RAD = Math.PI / 180;
    public static degToRad (deg: number): number {
        return deg * MathUtil.DEG_TO_RAD;
    }

    /**
     * 弧度转角度
     * @param rad 弧度
     */
    public static readonly RAD_TO_DEG = 180 / Math.PI;
    public static radToDeg (rad: number): number {
        return rad * MathUtil.RAD_TO_DEG;
    }

    /**
     * 本地坐标变换到世界坐标
     * @param worldPos 世界坐标
     * @param yaw 朝向（角度）
     * @param localPos 本地偏移
     */
    public static localToWorld (worldPos: cc.Vec2, yaw: number, localOffset: cc.Vec2): cc.Vec2 {
        let wp = new cc.Vec2();
        let rad = yaw * MathUtil.DEG_TO_RAD;
        let sin = Math.sin(rad);
        let cos = Math.cos(rad);
        wp.x = localOffset.x * cos - localOffset.y * sin;
        wp.y = localOffset.x * sin + localOffset.y * cos;
        if (worldPos) {
            wp.x += worldPos.x;
            wp.y += worldPos.y;
        }
        return wp;
    }

    /**
     * 截取变量到指定范围
     * @param v 变量
     * @param min 最小值
     * @param max 最大值
     */
    public static clamp (v: number, min: number, max: number): number {
        if (v < min) {
            v = min; 
        }
        if (v > max) {
            v = max; 
        }
        return v;
    }

    /**
     * 截取坐标
     * @param p 坐标
     * @param size 大小范围
     */
    public static clampPoint (p: cc.Vec2, size: cc.Vec2): void {
        if (p.x < 0) {
            p.x = 0; 
        }
        if (p.y < 0) {
            p.y = 0; 
        }
        if (p.x > size.x) {
            p.x = size.x; 
        }
        if (p.y > size.y) {
            p.y = size.y; 
        }
    }

    /**
     * 求反射向量
     * @param dir 方向
     * @param normal 法线
     */
    public static reflectDirSelf (dir: cc.Vec2, normal: cc.Vec2): void {
        let len = dir.dot(normal) * 2;
        dir.x -= normal.x * len;
        dir.y -= normal.y * len;
    }

    /**
     * 根据方向计算角度
     * @param dir 方向
     */
    public static calcYaw (dirVec: cc.Vec2): number {
        let radian = dirVec.signAngle(cc.v2(0, 1));    // 求方向向量与对比向量间的弧度
        let degree = radian / Math.PI * 180;    // 将弧度转换为角度
        return degree;
        /*
         * Dir.normalizeSelf();
         * let a = Math.asin(dir.x);
         * if (dir.y < 0) {
         *     a = Math.PI - a;
         * }
         * return a * 180 / Math.PI;
         */
    }

    /**
     * 归一化角度(0 ~ 360)
     * @param yaw 角度
     */
    public static normalizeYaw (yaw: number): number {
        let r = yaw % 360;
        if (r < 0) {
            r += 360;
        }
        return r;
    }

    /**
     * 计算角度差(-180 ~ 180)
     * @param srcYaw 起始角度
     * @param dstYaw 结束角度
     */
    public static calcDeltaYaw (srcYaw: number, dstYaw: number): number {
        let a1 = MathUtil.normalizeYaw(srcYaw);
        let a2 = MathUtil.normalizeYaw(dstYaw);
        let d = a2 - a1;
        if (d < -180) {
            d += 360;
        } else if (d > 180) {
            d -= 360;
        }
        return d;
    }

    /**
     * 根据角度计算方向
     * @param yaw 角度
     * @param out 输出方向
     */
    public static calcDir (yaw: number, out: cc.Vec2 = null): cc.Vec2 {
        if (!out) {
            out = new cc.Vec2();
        }
        let rad = MathUtil.degToRad(yaw);
        out.x = Math.sin(rad);
        out.y = Math.cos(rad);
        return out;
    }

    /**
     * 距离平方
     * @param p1 点1
     * @param p2 点2
     */
    public static distSq (p1: cc.Vec2, p2: cc.Vec2): number {
        let dx = p1.x - p2.x;
        let dy = p1.y - p2.y;
        return dx * dx + dy * dy;
    }

    /**
     * 距离
     * @param p1 点1
     * @param p2 点2
     */
    public static dist (p1: cc.Vec2, p2: cc.Vec2): number {
        let distSq = MathUtil.distSq(p1, p2);
        if (distSq < 1.0 - 6) {
            return 0;
        }
        return Math.sqrt(distSq);
    }

    /**
     * 插值
     * @param a a
     * @param b b
     * @param t 插值因子
     */
    public static lerp (a: number, b: number, t: number): number {
        return a * (1 - t) + b * t;
    }

    /**
     * 旋转坐标
     * @param p 点
     * @param yaw 朝向
     */
    public static rotatePosSelf (p: cc.Vec3, yaw: number): void {
        let rad = -yaw * MathUtil.DEG_TO_RAD;
        let sin = Math.sin(rad);
        let cos = Math.cos(rad);
        let x = p.x;
        let y = p.y;
        p.x = x * cos - y * sin;
        p.y = x * sin + y * cos;
    }

    /**
     * 线段相交检测(TODO 交点算法有问题)
     * @param a0 线段a起点
     * @param a1 线段a终点
     * @param b0 线段b起点
     * @param b1 线段b终点
     * @param p 交点
     */
    public static segmentIntersectSegment (a0: cc.Vec2, a1: cc.Vec2, b0: cc.Vec2, b1: cc.Vec2, p: cc.Vec2 = null): boolean {
        let ax = a1.x - a0.x;
        let ay = a1.y - a0.y;
        let bx = b1.x - b0.x;
        let by = b1.y - b0.y;

        // 平行或共线
        let cross = ax * by - ay * bx;
        if (cross > -MathUtil.EPSILON && cross < MathUtil.EPSILON) {
            return false;
        }

        let dx = b0.x - a0.x;
        let dy = b0.y - a0.y;
        let inv = 1 / cross;

        let s = (dx * ay - dy * ax) * inv;
        if (s < 0 || s > 1) {
            return false;
        }

        let t = (dx * by - dy * bx) * inv;
        if (t < 0 || t > 1) {
            return false;
        }

        if (p) {
            p.x = a0.x + s * ax;
            p.y = a0.y + t * ay;
        }
        return true;
    }

    /**
     * 是否相等
     * @param a a
     * @param b b
     */
    public static fEqual (a: number, b: number): boolean {
        let n = a - b;
        if (n > -MathUtil.EPSILON && n < MathUtil.EPSILON) {
            return true;
        }
        return false;
    }

    /**
     * 点到射线的投影
     * @param origin 射线起点
     * @param dir 射线方向（归一化后的）
     * @param p 点
     */
    public static projectRay (origin: cc.Vec2, dir: cc.Vec2, p: cc.Vec2): number {
        MathUtil._TEMP_P.x = p.x - origin.x;
        MathUtil._TEMP_P.y = p.y - origin.y;
        let dot = MathUtil._TEMP_P.dot(dir);
        return dot;
    }

    // 获取向量 角
    public static getAngle (pos1: cc.Vec2, pos2: cc.Vec2) {
        // 起点坐标
        let begin = pos1;
        // 终点坐标
        let end = pos2;
        // 起点到终点的方向向量
        let dir = end.sub(begin);
        // 计算夹角，这个夹角是带方向的
        let angle = dir.signAngle(cc.v2(0, 1));
        // 将弧度转换为欧拉角
        let degree = angle / Math.PI * 180;

        return degree;
    }

    /**
     * 获取Display欧拉角
     * @param start 
     * @param end 
     */
    static getAngle4Rotate (start, end) {
        // 计算出朝向
        let dx = end.x - start.x;
        let dy = end.y - start.y;
        let dir = cc.v2(dx, dy);
        // 根据朝向计算出夹角弧度
        let angle = dir.signAngle(cc.v2(1, 0));
        // 将弧度转换为欧拉角
        let degree = angle / Math.PI * 180;
        return -degree - 90;
    }

    /**
     * 判断点是否在旋转后的矩形中
     * @param point 触摸点的坐标
     * @param node 碰撞节点，锚点必须为(0.5,0.5)
     */
    static isPosInRotationRect (point: cc.Vec2, rect: cc.Rect, angle: number = 0) {
        rect.width=Math.abs(rect.width);
        rect.height=Math.abs(rect.height);
        let hw = rect.width / 2;
        let hh = rect.height / 2;
        let center = new cc.Vec3(rect.x, rect.y);
        let X = point.x;
        let Y = point.y;
        let r = -angle * (Math.PI / 180);
        let nTempX = center.x + (X - center.x) * Math.cos(r) - (Y - center.y) * Math.sin(r);
        let nTempY = center.y + (X - center.x) * Math.sin(r) + (Y - center.y) * Math.cos(r);
        if (nTempX > center.x - hw && nTempX < center.x + hw && nTempY > center.y - hh && nTempY < center.y + hh) {
            return true;
        }
        return false;
    }

    /**
     * 假设对图片上任意点(x,y)，绕一个坐标点(rx0,ry0)逆时针旋转a角度后的新的坐标设为(x0, y0)，有公式：
     * @param x 假设对图片上任意点(x,y)
     * @param y 假设对图片上任意点(x,y)
     * @param rx0 绕一个坐标点(rx0,ry0)
     * @param ry0 绕一个坐标点(rx0,ry0)
     * @param a 逆时针旋转a角度
     */
    static getRotationPoint (x: number, y: number, rx0: number, ry0: number, a: number): cc.Vec3 {
        let degree = MathUtil.degToRad(a);
        let x0 = (x - rx0) * Math.cos(degree) - (y - ry0) * Math.sin(degree) + rx0;
        let y0 = (x - rx0) * Math.sin(degree) + (y - ry0) * Math.cos(degree) + ry0;
        return new cc.Vec3(x0, y0);
    }

    /**
     * 获取矩形旋转后的新顶点数据
     * @param pos 
     * @param angle 
     */
    static getRotaRectNew (node: cc.Node): cc.Vec2[] {
        let displayPos = node.position;
        let angle = node.angle;
        if (!angle) {
            angle = 0;
        }
        let w = node.width;
        let h = node.height;

        let result = [];
        for (let i = 0; i < 4; i++) {
            result[i] = {};
            let v = cc.v2(0, 0);
            if (i === 0) {
                v = cc.v2(-w / 2, -h / 2);
                result[i].pos = cc.v2(-w / 2, -h / 2);
            }
            if (i === 1) {
                v = cc.v2(w / 2, -h / 2);
                result[i].pos = cc.v2(w / 2, -h / 2);
            }
            if (i === 2) {
                v = cc.v2(-w / 2, h / 2);
                result[i].pos = cc.v2(-w / 2, h / 2);
            }
            if (i === 3) {
                v = cc.v2(w / 2, h / 2);
                result[i].pos = cc.v2(w / 2, h / 2);
            }

            result[i].angle = Math.atan2(v.y, v.x) * (180 / Math.PI) + angle;
        }
        
        let arrPoint = [];
        for (let i = 0; i < result.length; i++) {
            let starPos = displayPos;
            let endPos = cc.v2(0, 0);
            let dis = result[i].pos.sub(cc.v2(0, 0)).mag();
            endPos.x = starPos.x + Math.cos(result[i].angle * (Math.PI / 180)) * dis;
            endPos.y = starPos.y + Math.sin(result[i].angle * (Math.PI / 180)) * dis;
            arrPoint.push(endPos);
        }
        return arrPoint;
    }

    /**
     * 使用已知两点。获取最大最小的X&Y
     * @param touchPos1 
     * @param touchPos2 
     */
    static getMinMaxXY (touchPos1: cc.Vec2, touchPos2: cc.Vec2): {minX: number, maxX: number, minY: number, maxY: number} {
        let nMinX = 0;
        let nMaxX = 0;
        let nMinY = 0;
        let nMaxY = 0;
        if (touchPos1.x < touchPos2.x) {
            nMinX = touchPos1.x;
            nMaxX = touchPos2.x;
        } else {
            nMinX = touchPos2.x;
            nMaxX = touchPos1.x;
        }

        if (touchPos1.y < touchPos2.y) {
            nMinY = touchPos1.y;
            nMaxY = touchPos2.y;
        } else {
            nMinY = touchPos2.y;
            nMaxY = touchPos1.y;
        }
        return {minX: nMinX, maxX: nMaxX, minY: nMinY, maxY: nMaxY};
    }

    // 一个坐标乘以一个数 得到一个新坐标
    public static pointMultiplyForNumber (p: { x: number, y: number }, dt: number): { x: number, y: number } {
        return {x: p.x * dt, y: p.y * dt};
    }

    // 一个点 + 一个点 得到一个新点
    public static pointAdditionForPoint (p: { x: number, y: number }, p2: { x: number, y: number }): { x: number, y: number } {
        return { x: (p.x + p2.x), y: (p.y + p2.y) };
    }

    // 一个坐标 减去一个坐标 得到一个新点
    public static pointSubtractionForPoint (p: { x: number, y: number }, p2: { x: number, y: number }): { x: number, y: number } {
        return { x: p.x - p2.x, y: p.y - p2.y };
    }

    /**
     * 获取点数组的中心坐标
     * @param arrPos 
     */
    static getCenterPosByPosArr (arrPos: cc.Vec3[]) {
        let newPosY = arrPos.sort((pos1: cc.Vec3, pos2: cc.Vec3) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });
        const topY = newPosY[0].y;
        const bottomY = newPosY[arrPos.length - 1].y;
        let newPosX = arrPos.sort((pos1: cc.Vec3, pos2: cc.Vec3) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        const leftX = newPosX[0].x;
        const rightX = newPosX[arrPos.length - 1].x;
        return cc.v3((rightX + leftX) / 2, (topY + bottomY) / 2);
    }

    /**
     * 获取点数组的 点最大最小值
     * @param positionArr 
     */
    static getXY (positionArr: cc.Vec3[]): {minX: number, maxX: number, minY: number, maxY: number} {
        let newPosY = positionArr.sort((pos1: cc.Vec3, pos2: cc.Vec3) => {
            if (pos1.y > pos2.y) {
                return -1;
            }
            if (pos1.y < pos2.y) {
                return 1;
            }
            return 0;
        });

        const topY = newPosY[0].y;
        const bottomY = newPosY[positionArr.length - 1].y;

        let newPosX = positionArr.sort((pos1: cc.Vec3, pos2: cc.Vec3) => {
            if (pos1.x > pos2.x) {
                return 1;
            }
            if (pos1.x < pos2.x) {
                return -1;
            }
            return 0;
        });
        const leftX = newPosX[0].x;
        const rightX = newPosX[positionArr.length - 1].x;
        return {minX: leftX, maxX: rightX, minY: bottomY, maxY: topY};
    }

    /**
     * 获取点围绕一个点 旋转angle之后的点
     * @param angle 
     * @param p1 
     * @param p2 
     */
    static getAngleAfterPos (angle: number, p1: cc.Vec2, p2: cc.Vec2):cc.Vec2 {
        let pos = cc.v2(0, 0);
        let dis = p2.sub(p1).mag();
        let angle1 =  Math.atan2(p2.y - p1.y,  p2.x - p1.x) * (180 / Math.PI);

        let angleSum = angle1 + angle;
        pos.x = p1.x + Math.cos(angleSum * (Math.PI / 180)) * dis;
        pos.y = p1.y + Math.sin(angleSum * (Math.PI / 180)) * dis;
      
        return pos;
    }

    /**
     * 获取吸附的angle值
     * @param angle 
     */
    static getAbsorAngle (angle: number): number {
        if (angle < 0) {
            angle = 360 + angle;
        }
        if (angle > 360) {
            angle %= 360;
        }
        let dis = 10;
        let destAngle = Math.floor(angle / 90);
        let yu = angle % 90;
        if (yu <= dis) {
            angle = destAngle * 90;
        } else if (yu > (90 - dis)) {
            angle = (destAngle + 1) * 90;
        }
        return angle;
    }

    /**
     * 根据_index 排序数组
     * @param sortArr 
     */
    static getSortDisplayArr (sortArr: {id:string, _index: number}[]): {id:string, _index: number}[] {
        sortArr.sort((displayData1: {id:string, _index: number}, displayData2: {id:string, _index: number}) => {
            if (displayData1._index < displayData2._index) {
                return -1;
            }
            if (displayData1._index > displayData2._index) {
                return 1;
            }
            return 0;
        });
        return sortArr;
    }
}
