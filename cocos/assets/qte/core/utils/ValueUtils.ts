/**
 * 数据操作工具类
 * 判别，clone等
 */
export default class ValueUtils {
    /** 检查数值有效性 */
    public static check<T> (val: T, d?: T): T {
        if (typeof (val) === "string") {
            if (val === "" && d) {
                return d;
            }
            return val;
        } else if (typeof (val) === "number" && !isNaN(val) && isFinite(val)) {
            return val;
        } else if (typeof (val) === "boolean") {
            return val;
        }

        if (!val) {
            return d;
        }
        return val;
    }

    /** Clone数据 */
    public static clone (obj) {
        let type = Object.prototype.toString.call(obj).slice(8, -1).
            toLowerCase();
        if (type === "object" && obj["type"] != "textToImg") {
            let json = {};
            for (let i in obj) {
                if (obj.hasOwnProperty(i)) {
                    json[i] = ValueUtils.clone(obj[i]);
                }
            }
            return json;
        } else if (type === "array") {
            let arr = [];
            for (let i = 0; i < obj.length; i++) {
                arr[i] = ValueUtils.clone(obj[i]);
            }
            return arr;
        } 
        return obj;
        
    }

    /** 两个数组中是否有相交的数 */
    static isMixedBy2Arr (arr1: number[] | string[], arr2: number[] | string[]): number[] | string[] {
        /*
         * arr1.sort();
         * arr2.sort();
         */
        let mixedIds = [];
        for (let i = 0; i < arr1.length; i++) {
            for (let j = 0; j < arr2.length; j++) {
                if (arr1[i] === arr2[j]) {
                    mixedIds.push(arr1[i]);
                }
            }
        }
        return mixedIds;
    }

    /**
     * 保留小数点后几位, 默认一位
     * @param value 
     * @param value 
     */
    static setOneDecimal (value: any, num: number = 1): any {
        if (typeof (value) === "number" && !isNaN(value) && isFinite(value)) {
            return (Number)(ValueUtils.check(value, 0).toFixed(num));
        } else if (typeof (value) === "object") {
            for (let key in value) {
                if (typeof value[key] === "number") {
                    value[key] = (Number)(ValueUtils.check((Number)(value[key]), 0).toFixed(num));
                }
            }
            return value;
        }
    }
    
    // 阶乘
    static factorial (num) {
        if (num <= 1) {
            return 1;
        } 
        return num * this.factorial(num - 1);
        
    }

    /**
     * 简易生成唯一uuid接口
     * @param key 自定义前缀
     * @returns uuid字符串
     */
    static genUUID(key?: string): string {
        let dateStr = new Date().getMilliseconds();
        let randomStr = Math.floor(Math.random()*1000);
        let resStr = dateStr + "_" + randomStr;
        return (key === null ? resStr : key + "_" + resStr);
    }
}
