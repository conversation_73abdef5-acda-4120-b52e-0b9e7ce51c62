/*
 * @FilePath     : /core/utils/TimerUtil.ts
 * <AUTHOR> wanghuan
 * @description  :
 * @warn         :
 */
import { SingleFactory } from "../base/SingleBase";
import TimerManager, { ExecutionType } from "../timer/TimerManager";

/**
 * 定时器类
 */
export default class TimerUtils {
    public static addTimer(cb: (dt) => void, time: number): number;

    public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType): number;
    public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, group: string): number;
    public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, isBack: boolean): number;
    public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, group: string, isBack: boolean): number;
    public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, isBack: boolean, group: string): number;

    public static addTimer(cb: (dt) => void, time: number, isBack: boolean): number;
    public static addTimer(cb: (dt) => void, time: number, isBack: boolean, executionType: ExecutionType): number;
    public static addTimer(cb: (dt) => void, time: number, isBack: boolean, executionType: ExecutionType, group: string): number;
    public static addTimer(cb: (dt) => void, time: number, isBack: boolean, group: string): number;
    public static addTimer(cb: (dt) => void, time: number, isBack: boolean, group: string, executionType: ExecutionType): number;

    public static addTimer(cb: (dt) => void, time: number, group: string): number;
    public static addTimer(cb: (dt) => void, time: number, group: string, executionType: ExecutionType): number;
    public static addTimer(cb: (dt) => void, time: number, group: string, executionType: ExecutionType, isBack:boolean): number;
    public static addTimer(cb: (dt) => void, time: number, group: string, isBack: boolean): number;
    public static addTimer(cb: (dt) => void, time: number, group: string, isBack: boolean, executionType:ExecutionType): number;

    /**
     * @msg     : 获取一个定时器
     * @param    {function} cb
     * @param    {number} time
     * @param    {any} executionType 定时器类型，默认一次性
     * @param    {*} group  定时器组， 默认 global
     * @param    {*} isBack 是否后台记录  默认不记录
     * @return   {*}
     */
    public static addTimer (cb: (dt) => void, time: number, executionType?: any, group?: any, isBack?: any): number {
        let timerId = 0;
        if (typeof executionType === "undefined") {
            timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, ExecutionType.once, "global", false);
        } else if (typeof executionType === "string") {
            if (group) {
                if (typeof group === "boolean") {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, typeof isBack === "undefined" ? ExecutionType.once : isBack, executionType, group);
                } else {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, group, executionType, typeof isBack === "undefined" ? false : isBack);
                }
            } else {
                timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, ExecutionType.once, executionType, false);
            }
        } else if (typeof executionType === "boolean") {
            if (group) {
                if (typeof group === "string") {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, typeof isBack === "undefined" ? ExecutionType.once : isBack, group, executionType);
                } else {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, group, typeof isBack === "undefined" ? "global" : isBack, executionType);
                }
            } else {
                timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, ExecutionType.once, "global", executionType);
            }
        } else if (typeof executionType === "number") {
            if (group) {
                if (typeof group === "boolean") {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, executionType, typeof isBack === "undefined" ? "global" : isBack, group);
                } else if (typeof group === "string") {
                    timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, executionType, group, typeof isBack === "undefined" ? false : isBack);
                }
            } else {
                timerId = SingleFactory.getInstance(TimerManager).getTimer(time, cb, executionType, "global", false);
            }
        }
        return timerId;
    }

    /**
     * 清空定时器
     * @param timerId
     */
    public static clearTimer (timerId: number, group = "global"): void {
        SingleFactory.getInstance(TimerManager).stopTimer(timerId, group);
    }
    /**
     * @msg     : 删除定时器组
     * @param    {*} group
     * @return   {*}
     */
    public static clearTimerGroup (group = "global"): void {
        SingleFactory.getInstance(TimerManager).stopGroupTimer(group);
    }
}
