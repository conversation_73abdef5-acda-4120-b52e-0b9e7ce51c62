/**
 * 单例基类，继承该类的子类就可以用单例工厂SingleFactory获取单例对象了
 * <AUTHOR>
 * @example
 *      class Test extends SingleBase {
 *          public test() {
 *              mi.log("我是单例对象");
 *          }
 *      }
 *      let t = SingleFactory.getInstance(Test);
 *      t.test();
 */
 export class SingleBase {
    /** 当前实例 */
    private static _instance: any = null;
    /** 当前symbol */
    private _symbol: string;
    public get symbol (): string {
        return this._symbol;
    }
    /** 初始化接口，子类可以重写实现 */
    public initInstance () {
        console.log("初始化接口，子类可以重写实现");
    }

    public onDestoryInstance (): void {
        console.log("销毁接口，子类可以重写实现");
    }
}

/**
 * 单例工厂
 * <AUTHOR>
 */
export class SingleFactory {

    private static __instanceMap: Map<new() => SingleBase, Map<string, SingleBase>> = new Map();

    public static getInstance<T extends SingleBase> (type: new() => T, symbol?: string): T {
        let instanceMap = SingleFactory.__instanceMap.get(type);
        if (!instanceMap) {
            // eslint-disable-next-line new-cap
            let target = new type();
            instanceMap = new Map<string, SingleBase>();
            instanceMap.set(symbol, target);
            SingleFactory.__instanceMap.set(type, instanceMap);
            target["_symbol"] = symbol;
            target.initInstance();
            return target;
        }
        let instance = instanceMap.get(symbol);
        if (!instance) {
            let target = new type();
            instanceMap.set(symbol, target);
            target["_symbol"] = symbol;
            target.initInstance();
            return target as T;
        }
        return instance as T;
    }

    public static destory<T extends SingleBase> (type: new() => T, symbol?: string): void {
        let instanceMap = SingleFactory.__instanceMap.get(type);
        if (instanceMap) {
            let instance = instanceMap.get(symbol);
            if (instance) {
                instance.onDestoryInstance();
                instanceMap.delete(symbol);
                instance = null;
            }
        }
    }

    /**
     * 消耗所有单例对象，可增加一个排除选项
     * @param {new() => T} except 排除选项
     */
    public static destoryAll<T extends SingleBase> (except?: new() => T, symbol?: string): void {
        SingleFactory.__instanceMap.forEach((value: Map<string, SingleBase>, key: new () => SingleBase) => {
            if (value) {
                value.forEach((v: SingleBase, k: string) => {
                    if (except === key) {
                        return;
                    }
                    v.onDestoryInstance();
                    SingleFactory.__instanceMap.delete(key);
                });
            }
        });
    }

    public static destoryBySymbol(symbol: string): void {
        SingleFactory.__instanceMap.forEach((value: Map<string, SingleBase>, key: new () => SingleBase) => {
            if (value) {
                value.forEach((v: SingleBase, k: string) => {
                    if (v.symbol === symbol) {
                        v.onDestoryInstance();
                        value.delete(k);
                        v = null;
                    }
                });
            }
        });
    }
}


