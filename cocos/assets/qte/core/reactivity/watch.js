"use strict";

function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }

function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }

function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }

function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }

function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }

function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }

Object.defineProperty(exports, "__esModule", {
  value: true
}); // src/errorHandling.ts

var _shared = require('./shared.cjs');

function callWithErrorHandling(fn, type, args) {
  var res;

  try {
    res = args ? fn.apply(void 0, _toConsumableArray(args)) : fn();
  } catch (err) {
    handleError(err, type);
  }

  return res;
}

function callWithAsyncErrorHandling(fn, type, args) {
  if (_shared.isFunction.call(void 0, fn)) {
    var res = callWithErrorHandling(fn, type, args);

    if (res && _shared.isPromise.call(void 0, res)) {
      res.catch(function (err) {
        handleError(err, type);
      });
    }

    return res;
  }

  var values = [];

  for (var i = 0; i < fn.length; i++) {
    values.push(callWithAsyncErrorHandling(fn[i], type, args));
  }

  return values;
}

function handleError(err, type) {
  console.error(new Error("[@vue-reactivity/watch]: ".concat(type)));
  console.error(err);
}

function warn(message) {
  console.warn(createError(message));
}

function createError(message) {
  return new Error("[reactivue]: ".concat(message));
} // src/index.ts


var _reactivity = require('./reactivity.cjs');

var invoke = function invoke(fn) {
  return fn();
};

var INITIAL_WATCHER_VALUE = {};

function watchEffect(effect2, options) {
  return doWatch(effect2, null, options);
}

function watch(source, cb, options) {
  return doWatch(source, cb, options);
}

function doWatch(source, cb) {
  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},
      immediate = _ref.immediate,
      deep = _ref.deep,
      onTrack = _ref.onTrack,
      onTrigger = _ref.onTrigger;

  var getter;

  if (_shared.isArray.call(void 0, source) && !_reactivity.isReactive.call(void 0, source)) {
    getter = function getter() {
      return source.map(function (s) {
        if (_reactivity.isRef.call(void 0, s)) return s.value;else if (_reactivity.isReactive.call(void 0, s)) return traverse(s);else if (_shared.isFunction.call(void 0, s)) return callWithErrorHandling(s, "watch getter");else warn("invalid source");
      });
    };
  } else if (_reactivity.isRef.call(void 0, source)) {
    getter = function getter() {
      return source.value;
    };
  } else if (_reactivity.isReactive.call(void 0, source)) {
    getter = function getter() {
      return source;
    };

    deep = true;
  } else if (_shared.isFunction.call(void 0, source)) {
    if (cb) {
      getter = function getter() {
        return callWithErrorHandling(source, "watch getter");
      };
    } else {
      getter = function getter() {
        if (cleanup) cleanup();
        return callWithErrorHandling(source, "watch callback", [onInvalidate]);
      };
    }
  } else {
    getter = _shared.NOOP;
  }

  if (cb && deep) {
    var baseGetter = getter;

    getter = function getter() {
      return traverse(baseGetter());
    };
  }

  var cleanup;

  var onInvalidate = function onInvalidate(fn) {
    cleanup = runner.options.onStop = function () {
      callWithErrorHandling(fn, "watch cleanup");
    };
  };

  var oldValue = _shared.isArray.call(void 0, source) ? [] : INITIAL_WATCHER_VALUE;
  var applyCb = cb ? function () {
    var newValue = runner();

    if (deep || _shared.hasChanged.call(void 0, newValue, oldValue)) {
      if (cleanup) cleanup();
      callWithAsyncErrorHandling(cb, "watch callback", [newValue, oldValue === INITIAL_WATCHER_VALUE ? void 0 : oldValue, onInvalidate]);
      oldValue = newValue;
    }
  } : void 0;
  var scheduler = invoke;

  var runner = _reactivity.effect.call(void 0, getter, {
    lazy: true,
    onTrack: onTrack,
    onTrigger: onTrigger,
    scheduler: applyCb ? function () {
      return scheduler(applyCb);
    } : scheduler
  });

  if (applyCb) {
    if (immediate) applyCb();else oldValue = runner();
  } else {
    runner();
  }

  var stopWatcher = function stopWatcher() {
    _reactivity.stop.call(void 0, runner);
  };

  stopWatcher.effect = runner;
  return stopWatcher;
}

function traverse(value) {
  var seen = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : new Set();
  if (!_shared.isObject.call(void 0, value) || seen.has(value)) return value;
  seen.add(value);

  if (_shared.isArray.call(void 0, value)) {
    for (var i = 0; i < value.length; i++) {
      traverse(value[i], seen);
    }
  } else if (value instanceof Map) {
    value.forEach(function (_, key) {
      traverse(value.get(key), seen);
    });
  } else if (value instanceof Set) {
    value.forEach(function (v) {
      traverse(v, seen);
    });
  } else {
    for (var _i = 0, _Object$keys = Object.keys(value); _i < _Object$keys.length; _i++) {
      var key = _Object$keys[_i];
      traverse(value[key], seen);
    }
  }

  return value;
}

exports.watch = watch;
exports.watchEffect = watchEffect;
