import QTEFaceda from "../../QTEFaceda";

/**
 * 资源加载util 判断当前加载资源类型
 * <AUTHOR>
 */
export enum UrlType {
    NET = "net",
    LOCAL = "local",
    FILE = "file",
}

export function getUrlType(url: string = ""): UrlType {
    if (url.startsWith("http://") || url.startsWith("https://") || url.startsWith("zybhost://") // 优先根据协议判断
    ) {
        return UrlType.NET;
    } else if (url.startsWith("file://") || url.startsWith("zybliveprotocol://")) {  // webview
        return UrlType.FILE;
    } else if (   // 则根据关键词匹配
        url.startsWith("rt://") || // 新版，根据协议头区分runtime
        url.startsWith("runtime://") || // 新版，根据协议头区分runtime
        url.startsWith("zybsdk://") || // 新版，根据协议头区分自研
        url.indexOf('assets') > -1 || // 老版，根据路径区分runtime & 自研
        url.indexOf("cocosversion") > -1  // 老版，根据路径区分runtime & 自研
    ) {
        return UrlType.FILE;
    } else {
        return UrlType.LOCAL;
    }
}

const ImageFmts = [
    ".png",
    ".jpg",
    ".bmp",
    ".jpeg",
    ".gif",
    ".ico",
    ".tiff",
    ".webp",
    ".image",
    ".pvr",
    ".pkm",
];
const AudioFmts = [".mp3", ".ogg", ".wav", ".m4a"];

function getExt(s: string) {
    const sArray = s.split(".");
    const length = sArray.length;

    return length > 1 ? `.${sArray[length - 1]}` : "";
}
//获取资源类型
export function getResourceClassType(url) {
    const ext = getExt(url);
    if (ImageFmts.includes(ext)) {
        return cc.SpriteFrame;
    }
    if (AudioFmts.includes(ext)) {
        return cc.AudioClip;
    }
}
