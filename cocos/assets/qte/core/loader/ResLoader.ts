/**
 * 资源加载类
 * <AUTHOR>
 * @date 2020-12-15
 *
 */

import { LoaderObserver, LoaderObserverParam } from "./LoaderObserver";
import { getUrlType, UrlType } from "./LoaderUtil";
import LogUtil from "../../util/LogUtil";
import { QTE_LOGCAT_TYPE } from "../../vo/QTEEnum";

/**
 * 资源加载器
 * <AUTHOR>
 * @date 2020-11-18
 * @description 提供各种资源类型加载接口，包括自定义加载器，注册加载观察者等。
 */
export default class ResLoader {
    // 加载器观察者列表
    private static _observerList: LoaderObserver[] = [];

    public static assetsMap: Map<string, cc.Asset> = new Map();

    public static cmptAssets: Map<string, string[]> = new Map();


    /**
     * 注册观察者
     * @param {LoaderObserver} observer 自定义观察者
     */
    public static addObserver(observer: LoaderObserver): void {
        ResLoader._observerList.push(observer);
    }

    /**
     * 通用资源加载接口（包括本地资源、网络资源和远程资源）
     * @param {string} path 资源路径，可以是本地资源、网络资源和远程资源
     * @param {cc.Asset | Record<string, any>} options 资源类型 | 远程资源可选参数
     * @param {(err, res) => void} onComplete 加载完成回调
     * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。
     * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度
     */
    public static async loadRes(
        path: string,
        options: typeof cc.Asset | Record<string, any>,
        onComplete: (err, res: any) => void,
        bundle?: cc.AssetManager.Bundle | string,
        cmptId?: string,
        onProgress?: (
            finish: number,
            total: number,
            item: cc.AssetManager.RequestItem
        ) => void
    ): Promise<void> {
        if(!path && typeof path !== "string")
        {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `Resloader  loadRes path ${path}`);
            return;
        }
        let curBundle: cc.AssetManager.Bundle = null;
        let tempType = null;
        if (typeof options !== "object") {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, `Resloader  loadRes options ${path}`);
            tempType = options;
            if (bundle && typeof bundle === "string" && bundle !== "") {
                curBundle = cc.assetManager.getBundle(bundle);
            } else if (bundle && typeof bundle !== "string") {
                curBundle = bundle as cc.AssetManager.Bundle;
            }
            if (curBundle) {
                let as = curBundle.get(path, tempType);
                if (as) {
                    ResLoader.__beforeLoadRes(
                        new LoaderObserverParam(
                            path,
                            tempType,
                            curBundle,
                            0,
                            null
                        )
                    );
                    onComplete(null, as);
                    ResLoader.__afterLoadRes(
                        new LoaderObserverParam(
                            path,
                            tempType,
                            curBundle,
                            0,
                            null
                        )
                    );
                    return;
                }
            }
        }
        let param = new LoaderObserverParam(path, tempType, curBundle, 0, null);
        ResLoader.__beforeLoadRes(param);
        let startTime = new Date().getTime();
        const urlType = getUrlType(path);
        path = path.replace(/^(runtime:\/\/)|(zybsdk:\/\/)/ig, "");
        switch (urlType) {
            case UrlType.LOCAL:
                ResLoader.loadLocalRes(
                    path,
                    options,
                    onProgress,
                    (_err, _res) => {
                        if (onComplete) {
                            param.time = new Date().getTime() - startTime; // 计算下载时间
                            ResLoader.__afterLoadRes(param);

                            ResLoader.assetsMap.set(path, _res);
                            console.log("%c Line:110 🍪 cmptId", "color:#465975", cmptId);
                            if (cmptId) {
                                if (!ResLoader.cmptAssets || !ResLoader.cmptAssets.get(cmptId)) {
                                    ResLoader.cmptAssets.set(cmptId, [path]);
                                } else {
                                    ResLoader.cmptAssets.get(cmptId).push(path);
                                }
                            }
                            if (_err) {
                                LogUtil.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `UrlType.LOCAL_ERROR${path}`);
                            }
                            onComplete(_err, _res);
                            LogUtil.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `urlType  UrlType.LOCAL ${path}`);
                        }
                    },
                    curBundle
                );
                break;
            case UrlType.FILE:
            case UrlType.NET:
                console.log("======NET======", path, JSON.stringify(options), typeof options);
                LogUtil.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `urlType 开始加载 UrlType.FILE ${path}`);
                ResLoader.loadRemoteRes(path, options, (_err, _res) => {
                    if (onComplete) {
                        param.time = new Date().getTime() - startTime; // 计算下载时间
                        ResLoader.__afterLoadRes(param);
                        if (_err) {
                            qte.error(`UrlType.NET == path==  ${path} err= `, _err);
                            qte.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOAD_ERROR, `UrlType.NET  ${_err} + ${path}`);
                        }
                        ResLoader.assetsMap.set(path, _res);
                        if (cmptId) {
                            if (!ResLoader.cmptAssets || !ResLoader.cmptAssets.get(cmptId)) {
                                ResLoader.cmptAssets.set(cmptId, [path]);
                            } else {
                                ResLoader.cmptAssets.get(cmptId).push(path);
                            }
                        }
                        onComplete(_err, _res);
                        LogUtil.logCatToNative(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, `urlType  UrlType.FILE ${path}`);
                    }
                });
                break;
            default:
                console.warn("no path type found");
        }
    }

    /**
     * 加载目录
     * @param {string} dir 资源目录
     * @param {cc.Asset} type 资源类型
     * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度回调
     * @param {(error: Error, assets: Array<T>) => void} onComplete 加载完成回调
     * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。
     */
    public static loadDir<T extends cc.Asset>(
        dir: string,
        type: typeof cc.Asset,
        onProgress: (
            finish: number,
            total: number,
            item: cc.AssetManager.RequestItem
        ) => void,
        onComplete: (error: Error, assets: Array<T>) => void,
        bundle?: cc.AssetManager.Bundle | string
    ): void {
        let curBundle: cc.AssetManager.Bundle = null;
        if (bundle && typeof bundle === "string" && bundle !== "") {
            curBundle = cc.assetManager.getBundle(bundle);
        } else if (bundle && typeof bundle !== "string") {
            curBundle = bundle as cc.AssetManager.Bundle;
        } else {
            curBundle = cc.resources as cc.AssetManager.Bundle;
        }
        if (!curBundle) {
            onComplete(new Error(`bundle ${bundle} is not exists!`), null);
            return;
        }
        curBundle.loadDir(dir, type, onProgress, onComplete);
    }

    /**
     * 加载bundle
     * @param {string} nameOrUrl bundle名称或地址
     * @param {Record<string, any>} options 下载bundle的可选参数
     * @param {(err: Error, bundle: cc.AssetManager.Bundle) => void} onComplete 加载完成回调
     */
    public static loadBundle(
        nameOrUrl: string,
        options: Record<string, any>,
        onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void
    ): Promise<cc.AssetManager.Bundle> {
        return new Promise<cc.AssetManager.Bundle>((resolve, reject) => {
            let param = new LoaderObserverParam(nameOrUrl, null, null, 0, null);
            let startTime = new Date().getTime();
            ResLoader.__beforeLoadBundle(param);
            const exist = cc.assetManager.bundles.find((_bundle, b) => {
                return (
                    _bundle.base.indexOf(nameOrUrl) !== -1 ||
                    _bundle.name === nameOrUrl
                );
            });
            if (exist) {
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE,`ResLoader loadBundle ${exist.name}exist`);
                param.time = new Date().getTime() - startTime; // 加载时间处理
                ResLoader.__afterLoadBundle(param);
                if (onComplete) {
                    onComplete(null, exist);
                }
                resolve(exist);
                return;
            }
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE,`ResLoader loadBundle   ${nameOrUrl} start`);
            cc.assetManager.loadBundle(nameOrUrl, options, (_err, _bundle) => {
                if (_err) {
                    // 增加判断，编辑器时，无 adapter 
                    qte.adapter && qte.adapter.assetsCheck && qte.adapter.assetsCheck({ path: nameOrUrl });
                }
                param.time = new Date().getTime() - startTime; // 加载时间处理
                ResLoader.__afterLoadBundle(param);
                if (onComplete) {
                    onComplete(_err, _bundle);
                }
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE,`ResLoader loadBundle${nameOrUrl} complete`);
                resolve(_bundle);
            });
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
            return null;
        });
    }
    /**
     * 加载远程资源
     * @param path
     * @param type
     * @param callback
     * @returns
     */
    public static loadRemoteRes(
        path: string,
        type: any,
        callback: (err: any, res: any) => void
    ): Promise<cc.Asset> {
        console.log("loadRemoteRes begin.", path, type == typeof cc.Asset)
        return new Promise<cc.Asset>((resolve, reject) => {
            // 加载网络资源
            let loadOpation = {
                timeout: 10000,
                maxRetryCount: 1
            };
            if (path.indexOf('http') == -1) {
                loadOpation.timeout = 2000;
            }
            if (type) {
                loadOpation['type'] = type;
            }
            cc.assetManager.loadRemote(path, loadOpation, (e, res) => {
                if (e) {
                    console.error(e);
                    reject(e);
                } else {
                    resolve(res);
                }
                if (callback) {
                    callback(e, res);
                }
            });

        }).catch(err => {
            qte && qte.logCatBoth('loadRemoteRes cocos promise error:', err);
            return null;
        });
    }
    /**
     * 加载resouces 或者bundle 内资源
     * @param path
     * @param type
     * @param onProgress
     * @param callback
     * @param bundle
     * @returns
     */
    public static loadLocalRes(
        path: string,
        type: any,
        onProgress: (
            finish: number,
            total: number,
            item: cc.AssetManager.RequestItem
        ) => void,
        callback: (err: any, res: any) => void,
        bundle?: cc.AssetManager.Bundle
    ) {
        return new Promise((resolve, reject) => {
            if (bundle) {
                bundle.load(path, type, onProgress, (e, res) => {
                    if (e) {
                        console.error(e);
                        reject(e);
                    } else {
                        resolve(res);
                    }
                    if (callback) {
                        callback(e, res);
                    }
                });
            } else {
                cc.resources.load(path, type, (e, res) => {
                    if (e) {
                        console.error(e);
                        reject(e);
                    } else {
                        resolve(res);
                    }
                    if (callback) {
                        callback(e, res);
                    }
                });
            }
        }).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
        });
    }

    private static __beforeLoadRes(param: LoaderObserverParam): void {
        for (let obs of ResLoader._observerList) {
            obs.beforeLoadRes(param);
        }
    }

    private static __afterLoadRes(param: LoaderObserverParam): void {
        for (let obs of ResLoader._observerList) {
            obs.afterLoadRes(param);
        }
    }

    private static __beforeLoadBundle(param: LoaderObserverParam): void {
        for (let obs of ResLoader._observerList) {
            obs.beforeLoadBundle(param);
        }
    }

    private static __afterLoadBundle(param: LoaderObserverParam): void {
        for (let obs of ResLoader._observerList) {
            obs.afterLoadBundle(param);
        }
    }

    /**
     * 移除观察者
     * @param {LoaderObserver} observer 自定义观察者
     * @returns {boolean} 是否移除成功
     */
    public static removeObserver(observer: LoaderObserver): boolean {
        for (let i = ResLoader._observerList.length - 1; i >= 0; i--) {
            let obs = ResLoader._observerList[i];
            if (obs === observer) {
                ResLoader._observerList.splice(i, 1);
                return true;
            }
        }
        return false;
    }

    /**
     * 直接通过asset释放资源（如cc.Prefab、cc.SpriteFrame）
     * @param asset 要释放的asset
     */
    public static releaseAsset(asset: cc.Asset) {
        asset.decRef();
    }
}
