/* eslint-disable no-bitwise */
/* eslint-disable no-unused-expressions */
/* eslint-disable sort-vars */
/* eslint-disable init-declarations */
/* eslint-disable dot-notation */


const { ccclass } = cc._decorator;

@ccclass
export default class SpineRect extends cc.Component {
    public spineW: number = -1;
    public spineH: number = 0;
    public centerPoint: cc.Vec2 = new cc.Vec2(0, 0);
    public anchorX = 0;
    public anchorY = 0;
    // eslint-disable-next-line max-lines-per-function
    calcOffset(callBack: any) {
        const spData = this.node.getComponent(sp.Skeleton);
        // spData.clearTracks();
        let bl = false;
        const _assembler = spData["_assembler"];
        const oldFillBuffers = _assembler["fillBuffers"];
        // eslint-disable-next-line max-lines-per-function
        _assembler["fillBuffers"] = (comp, render) => {
            if (bl) {
                spData["_assembler"]["fillBuffers"] = oldFillBuffers;
                return;
            }
            const list = [];
            const _comp = spData;
            // eslint-disable-next-line no-new
            new cc["gfx"].VertexFormat([
                { name: cc["gfx"].ATTR_POSITION, type: cc["gfx"].ATTR_TYPE_FLOAT32, num: 2 },
                { name: cc["gfx"].ATTR_UV0, type: cc["gfx"].ATTR_TYPE_FLOAT32, num: 2 },
                { name: cc["gfx"].ATTR_COLOR, type: cc["gfx"].ATTR_TYPE_UINT8, num: 4, normalize: true }
            ]);
            const VFOneColor = cc["gfx"].VertexFormat.XY_UV_Color; // vfmtPosUvColor
            // const VFTwoColor = cc['gfx'].VertexFormat.XY_UV_Two_Color; // vfmtPosUvTwoColor
            let _vertexFormat = VFOneColor;
            let _buffer = render.getBuffer("spine", _vertexFormat);

            let vbuf = null;

            let locSkeleton = _comp["_skeleton"];
            let clipper = _comp["_clipper"];
            let attachment, triangles;
            let isClip, isMesh, isRegion;
            let offsetInfo;
            let slot;
            let _slotRangeStart;
            let _slotRangeEnd;
            _slotRangeStart = _comp["_startSlotIndex"];
            _slotRangeEnd = _comp["_endSlotIndex"];
            let _inRange = false;
            if (_slotRangeStart === -1) {
                _inRange = true;
            }
            // x y u v r1 g1 b1 a1 r2 g2 b2 a2 or x y u v r g b a 
        
            let _vertexFloatCount = 0;
            let _vertexFloatOffset = 0;
            let _indexCount = 0;
            let _quadTriangles = [0, 1, 2, 2, 3, 0];
            let _perVertexSize = 5;
            // let _vertexEffect = comp._effectDelegate && comp._effectDelegate._vertexEffect;
            // ! 修改过
            bl = true;

            /*
             * if (_vertexEffect) {
             *     _vertexEffect.begin(comp._skeleton); 
             * }
             */
            for (let slotIdx = 0, slotCount = locSkeleton["drawOrder"].length; slotIdx < slotCount; slotIdx++) {
                slot = locSkeleton["drawOrder"][slotIdx];

                if (_slotRangeStart >= 0 && _slotRangeStart === slot.data.index) {
                    _inRange = true;
                }

                if (!_inRange) {
                    clipper.clipEndWithSlot(slot);
                    continue;
                }

                if (_slotRangeEnd >= 0 && _slotRangeEnd === slot.data.index) {
                    _inRange = false;
                }

                _vertexFloatCount = 0;
                _indexCount = 0;

                attachment = slot.getAttachment();
                if (!attachment) {
                    clipper.clipEndWithSlot(slot);
                    continue;
                }
                isRegion = attachment instanceof sp.spine.RegionAttachment;
                isMesh = attachment instanceof sp.spine.MeshAttachment;
                isClip = false;

                if (isClip) {
                    clipper.clipStart(slot, attachment);
                    continue;
                }

                if (!isRegion && !isMesh) {
                    clipper.clipEndWithSlot(slot);
                    continue;
                }

                if (isRegion) {
                    triangles = _quadTriangles;
                    // insure capacity
                    _vertexFloatCount = 4 * _perVertexSize;
                    _indexCount = 6;

                    offsetInfo = _buffer.request(4, 6);
                    _vertexFloatOffset = offsetInfo.byteOffset >> 2;
                    vbuf = _buffer._vData;

                    // compute vertex and fill x y
                    attachment.computeWorldVertices(slot.bone, vbuf, _vertexFloatOffset, _perVertexSize);
                    const pos = [];
                    list.push(cc.v2(vbuf[_vertexFloatOffset], vbuf[_vertexFloatOffset + 1]));
                    for (let ii = _vertexFloatOffset + _perVertexSize, nn = _vertexFloatOffset + _vertexFloatCount; ii < nn; ii += _perVertexSize) {
                        list.push(cc.v2(vbuf[ii], vbuf[ii + 1]));
                    }
                    list.push(pos);
                } else if (isMesh) {
                    triangles = attachment.triangles;

                    // insure capacity
                    _vertexFloatCount = (attachment.worldVerticesLength >> 1) * _perVertexSize;
                    _indexCount = triangles.length;

                    offsetInfo = _buffer.request(_vertexFloatCount / _perVertexSize, _indexCount);
                    _vertexFloatOffset = offsetInfo.byteOffset >> 2;
                    vbuf = _buffer._vData;

                    // compute vertex and fill x y
                    attachment.computeWorldVertices(slot, 0, attachment.worldVerticesLength, vbuf, _vertexFloatOffset, _perVertexSize);

                    for (let ii = 0, nn = triangles.length; ii < nn; ii += 3) {
                        let v1 = triangles[ii] * _perVertexSize + _vertexFloatOffset;
                        let v2 = triangles[ii + 1] * _perVertexSize + _vertexFloatOffset;
                        let v3 = triangles[ii + 2] * _perVertexSize + _vertexFloatOffset;
                        list.push(cc.v2(vbuf[v1], vbuf[v1 + 1]));
                        list.push(cc.v2(vbuf[v2], vbuf[v2 + 1]));
                        list.push(cc.v2(vbuf[v3], vbuf[v3 + 1]));
                    }
                }

                if (_vertexFloatCount === 0 || _indexCount === 0) {
                    clipper.clipEndWithSlot(slot);
                    continue;
                }
            }
            let minX = (list[0] && list[0].x) || 0;
            let maxX = (list[0] && list[0].x) || 0;
            let minY = (list[0] && list[0].y) || 0;
            let maxY = (list[0] && list[0].y) || 0;
            for (let i = 0; i < list.length; i++) {
                minX = minX > list[i].x ? list[i].x : minX;
                maxX = maxX < list[i].x ? list[i].x : maxX;
                minY = minY > list[i].y ? list[i].y : minY;
                maxY = maxY < list[i].y ? list[i].y : maxY;
            }
            let oriW = maxX - minX; // 原尺寸 宽
            let oriH = maxY - minY; // 原尺寸 高
            this.anchorX = -minX / oriW; // x坐标偏移
            this.anchorY = -minY / oriH; // y坐标偏移
            if (callBack) {
                callBack();
                // callBack = null;
            }
        };
    }

    getAnchor(): cc.Vec2 {
        return cc.v2(this.anchorX, this.anchorY);
    }

}

