import { EditUtil } from "./EditUtils";

/**
 * SimpleLabelActor.ts 单个label
 * <AUTHOR>
 * @description 自定义输入框里的每个文字是由单个label控制
 */
const { ccclass } = cc._decorator;
@ccclass
export default class SimpleLabelActor extends cc.Component {
    simpleLab: cc.Label = null; // 单个文字
    private labelPitchMask: cc.Node = null; // 文字被选中的蓝色遮照

    private _labelType : string = "label";

    private offsetX: number = 0;

    public set labelType (value: string) {
        this._labelType = value;
    }

    public get labelType () {
        return this._labelType
    }

    public simEditProperties = {
        enableBold: false,
        enableItalic: false,
        enableUnderline: false,
        fontSize: 32,
        lineHeight: 32,
        color: "#FFFFFF",
        returnStatue: false
    }

    public rect (): cc.Rect {
        return cc.rect(this.node.x - this.node.width / 2, this.node.y - this.node.height / 2, this.node.width, this.node.height)
    }

    private initLabel () {
        if (this.simpleLab) {
            return
        }
        let simpleLabelNode = new cc.Node();
        simpleLabelNode.name = "simpleLabelNode";
        simpleLabelNode.setAnchorPoint(0, 0.5);
        simpleLabelNode.parent = this.node;
        this.simpleLab = simpleLabelNode.addComponent(cc.Label);
        this.simpleLab.verticalAlign = cc.Label.VerticalAlign.CENTER;
        EditUtil.setLabelBlend(this.simpleLab);
    }

    public writeLab (data: string, simProperties: any, type = "label") {
        this.initLabel();
        if (type === "label" || type === "labelReturn") {
            this.simpleLab.string = data;
        } else if (type === "returnKey") {
            this.simpleLab.string = "";
        }
       
        this.setFontColor(simProperties.color);
        this.setFontSize(simProperties.fontSize, false)
        this.setLineHeight(simProperties.lineHeight, false);
        this.setItalicEnabled(simProperties.enableItalic, false);
        this.setBoldEnabled(simProperties.enableBold, false);
        this.setUnderLine(simProperties.enableUnderline, false);
        this._labelType = type;
        this.updateSize(true);
    }

    // 文字选中状态
    public updatePitch (statue) {
        if (this.labelPitchMask) {
            this.labelPitchMask.active = statue;
        } else if (statue) {
            this.labelPitchMask = new cc.Node();
            this.labelPitchMask.name = "labelPitchMask";
            this.labelPitchMask.opacity = 100;
            this.labelPitchMask.setPosition(cc.v2(this.offsetX, 0));
            this.labelPitchMask.setContentSize(cc.size(20, 20));
            this.labelPitchMask.parent = this.node;
            let texture = new cc.Texture2D();
            let spriteFrame = new cc.SpriteFrame();
            texture.initWithData(new Uint8Array([50, 195, 240]), cc.Texture2D.PixelFormat.RGB888, 1, 1);
            spriteFrame.setTexture(texture);
            let rect = this.simpleLab.node.getBoundingBox();
            spriteFrame.setRect(cc.rect(0, 0, rect.width, rect.height));
            this.labelPitchMask.addComponent(cc.Sprite).spriteFrame = spriteFrame;
            this.labelPitchMask.active = true;
        }
    }

    public setFontColor (data): string {
        let oldColor = `#${this.simpleLab.node.color.toHEX()}`;
        this.simEditProperties.color = data;
        let color = new cc.Color();
        color.fromHEX(data);
        this.simpleLab.node.color = color;
        return oldColor;
    }

    public setFontSize (num: number, needUpdateSize = true): void {
        this.simEditProperties.fontSize = num;
        this.simpleLab.fontSize = num;
        if (needUpdateSize) {
            this.updateSize(true);
        }
    }
    
    public setLineHeight (data: number, needUpdateSize = true): void {
        this.simEditProperties.lineHeight = data;
        this.simpleLab.lineHeight = data;
        this.simpleLab.node.height = data;
        this.node.height = data;
        if (needUpdateSize) {
            this.updateSize(true);
        }
    }

    public setBoldEnabled (statue: boolean, needUpdateSize = true): void {
        this.simEditProperties.enableBold = statue;
        this.simpleLab.enableBold = statue;
        if (needUpdateSize) {
            this.updateSize(true)
        };
    }

    public setItalicEnabled (statue: boolean, needUpdateSize = true): void {
        this.simEditProperties.enableItalic = statue;
        // 小米11、红米note 手机上 italic 不生效，所以直接对 node 倾斜
        this.simpleLab.node.skewX = statue ? 12 : 0;
        let d = this.simEditProperties.fontSize * 0.119;
        this.offsetX = statue ? d : 0;
        // this.simpleLab.enableItalic = statue;
        if (needUpdateSize) {
            this.updateSize(true)
        };
    }

    public setUnderLine (statue: boolean, needUpdateSize = true): void {
        this.simEditProperties.enableUnderline = statue;
        this.simpleLab.enableUnderline = statue;
        if (needUpdateSize) {
            this.updateSize(true)
        };
    }

    // 刷新label和node的尺寸
    private updateSize (updateNode = false): void {
        // eslint-disable-next-line dot-notation
        this.simpleLab["_forceUpdateRenderData"](true);
        // eslint-disable-next-line dot-notation
        if (this._labelType === "returnKey") {
        }
        // 强制刷新Label尺寸
        if (updateNode) {
            this.node.setContentSize(this.simpleLab.node.getContentSize());
        }
        this.simpleLab.node.setPosition(cc.v2(-this.node.width / 2 + this.offsetX, 0));
        if (this.labelPitchMask) {
            this.labelPitchMask.setContentSize(this.simpleLab.node.getBoundingBox());
            this.labelPitchMask.x = this.offsetX;
        }
    }

    // 格式   b,i,u,size,color
    public getCurFormat (): any {
        let _curLeftFormat = [];
        let _curRightFormat = [];
        if (this.simEditProperties.enableBold) {
            _curLeftFormat.push("<b>");
            _curRightFormat.push("</b>");
        }
        if (this.simEditProperties.enableItalic) {
            _curLeftFormat.push("<i>");
            _curRightFormat.push("</i>");
        }

        if (this.simEditProperties.enableUnderline) {
            _curLeftFormat.push("<u>");
            _curRightFormat.push("</u>");
        }

        if (this.simEditProperties.fontSize) {
            _curLeftFormat.push(`<size=${this.simEditProperties.fontSize}>`);
            _curRightFormat.push("</s>");
        }

        if (this.simEditProperties.color) {
            _curLeftFormat.push(`<color=${this.simEditProperties.color}>`);
            _curRightFormat.push("</c>");
        }

        let _brData = "";
        
        if (this.simEditProperties.returnStatue) {
            _brData = "<br/>";
        }

        let simpleData = {
            "curLeftFormat": _curLeftFormat,
            "curRightFormat": _curRightFormat,
            "labStr": this.simpleLab.string,
            "brData": _brData
        }
        if (this._labelType === "returnKey") {
            simpleData.labStr = "█"
        }
        return simpleData;
    }
    
    // 回收时，清除属性
    public clearProperty () {
        this.simpleLab.enableUnderline = false;
        this.simpleLab.enableBold = false;
        this.simpleLab.enableItalic = false;

        this.updatePitch(false);

        this.simEditProperties.enableUnderline = false;
        this.simEditProperties.enableBold = false;
        this.simEditProperties.enableItalic = false;
    }

    /** 获取文本 */
    public getLabelStr (): string {
        return this.simpleLab ? this.simpleLab.string : "";
    }

}
