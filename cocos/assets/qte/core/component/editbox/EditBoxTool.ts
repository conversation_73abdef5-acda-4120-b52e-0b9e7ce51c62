/**
 * EditBoxTool.ts 自定义输入框
 * <AUTHOR>
 * @description 1.createEditBox() 创建可编辑输入框，可任意选中字体编辑。
 * @description 2.createEditBoxWithString(string, isEditable)  通过富文本字符串 生成 （不可）可编辑文本框，string 为 富文本格式字符串, isEditable 是否可编辑
 */

/* eslint-disable max-lines */
import { layoutHorizontallys } from "./EditBoxData";
import { EditUtil } from "./EditUtils";
import SimpleLabelActor from "./SimpleLabelActor";
import TypeNode from "./TypeNode";
import { Md5ts } from "./md5ts";

const { ccclass } = cc._decorator;
@ccclass
export default class EditBoxTool extends cc.Component {
	// 富文本对外属性
	public enableBold: boolean = false;
	/** 斜体 */
	public enableItalic: boolean = false;
	/** 下划线 */
	public enableUnderline: boolean = false;
	/** 固定宽高 */
	public widthHeightFixed: boolean = false;
	/** 固定宽度 */
	public widthFixed: boolean = false;
	public fontSize: number = 32;
	public lineHeight: number = 32;
	/** 行间距 1、1.2、1.5、2 */
	public rowSpace: number = 1;
	public color: string = "#000000";
	public string: string = "单击添加文本";
	public horizontalAlign: number = 0; // 0 左对齐  1 中对齐，2 右对齐
	public editable: any = null;

	// 富文本自我实现逻辑
	private _labelLayout: cc.Layout = null;
	private _editBox: cc.EditBox = null;
	private _cursor: cc.Node = null;
	private _placeHolder: cc.Node = null;
	private _isCanPitch: boolean = false;

	/** 光标所在位置的label  labelIndx 光标所在label的下角标，cursor是否在该label 的右边*/
	private _cursorLabelStatue = {
		labelIndex: -1,
		isLabelRight: true,
	};
	private _shiftCursorStatue;
	// 选中 状态
	public pikerviewStatue: number = 0;
	public isSelectedFirst: boolean = true;
	public isEditing: boolean = false;
	private _blinkTimer: number = 0;
	private _oldInputString: string = "";
	private _isEditable: boolean = true;

	// 对象池
	private _labelPool: cc.NodePool = null;

	private scrollBar: cc.Node = null;
	private bar: cc.Node = null;
	/** 正在滑动滚动条 */
	private scrolling: boolean = false;
	private cacheText: string = "";
	private lastMd5: string = "";
	private readonly prefix = "::__edit__box__tool__::";
	public createWithEdit: boolean = false;
	private MAX_LENGTH = 3500;

	private _updateNodeSize: () => void;
	// eslint-disable-next-line accessor-pairs
	public set updateNodeSize(value: () => void) {
		this._updateNodeSize = value;
	}

	private _updateEditProperties: (data) => void;
	// eslint-disable-next-line
	public set updateEditProperties(value: (data) => void) {
		this._updateEditProperties = value;
	}

	private _updateEditPropertiesWithStr: (data, notUpdate) => void;
	// eslint-disable-next-line
	public set updateEditPropertiesWithStr(value: (data, notUpdate) => void) {
		this._updateEditPropertiesWithStr = value;
	}

	private _textToImg: (data) => void;
	// eslint-disable-next-line
	public set textToImg(value: (data) => void) {
		this._textToImg = value;
	}
	private _writeToText: (data) => void;
	public set writeToText(value: (data) => void) {
		this._writeToText = value;
	}

	/** 所有的添加在layout上的单个文字list */
	private _layoutLabelList: TypeNode[] = [];
	/** 每一行 单个文字list */
	private _labelLineList: any[] = [];
	/** 选中的 单个文字list */
	private _pitchLabelList: any = {};
	/** 截图node */
	private _captureNode: cc.Node = null;

	/** 点击 回车按钮 */
	private _clickReturnKey: boolean = false;
	/** 输入框文本 */
	private _editBoxStr: string = null;
	/** 输入框文本 带有 回车符站位 */
	private _editBoxStrWithReturn: string = "";
	private HOLDER: string = "\u2588";

	/**
	 * 鼠标滚动
	 * @param event 
	 */
	public mouseWheelLayout(event: cc.Event.EventMouse): void {
		if (!this.widthHeightFixed) return;
		let h = event.getScrollY();
		let d = h * 0.12;
		if (this._labelLayout.node.height > this.node.height) {
			this.wheelLabelLayout(d);
			this.wheelScrollBar(d);
		}
	}

	private wheelLabelLayout(duration: number) {
		if (duration > 0 && this._labelLayout.node.y - duration < this.node.height / 2) {
			duration = this._labelLayout.node.y - this.node.height / 2;
		}
		if (duration < 0 && this._labelLayout.node.y - duration > this._labelLayout.node.height - this.node.height / 2) {
			duration = this._labelLayout.node.y - this._labelLayout.node.height + this.node.height / 2;
		}
		this._labelLayout.node.y -= duration;
		this._cursor.y -= duration;
	}

	private wheelScrollBar(duration: number) {
		let scrollH = this.scrollBar.height - this.bar.height;
		this.bar.y += duration * scrollH / (this._labelLayout.node.height - this.node.height);
		this.bar.y = this.clamp(this.bar.y, -scrollH / 2, scrollH / 2);
	}

	/**
	 * 生成可编辑 富文本
	 */
	public createEditBox(): void {
		this.initEditBox();
		this.initPool();
		this.scheduleOnce(() => {
			if (this.createWithEdit) {
				this.startEdit();
			}
		}, 0);
	}

	/**
	 * 通过富文本字符串 生成 （不可）可编辑文本框
	 * @param {string}string 富文本格式字符串
	 * @param {isEditable} boolean 生成是否可编辑的文本
	 * @warn  调用此方法之前，要先调用 setEditBoxHorizontalStatue();设置文本的对齐方式，默认为左对齐
	 * @warn  调用此方法之前，要先调用 setNodeSize(),设置node的尺寸
	 */
	public createEditBoxWithString(string: string, isEditable: boolean = true, isSdk?: boolean): Promise<void> {
		this.widthFixed = true;
		let pattern = /::__edit__box__tool__::/ig;
		string = string.replace(pattern, "");

		return new Promise<void>((resolve, reject) => {
			this._isEditable = isEditable;
			this.initEditBox(isSdk);
			this.initPool();
			this.convertToEditBox(string);
			this.bar.active = false;

			// 给真正的 editbox赋值
			for (let i = 0; i < this._editBoxStrWithReturn.length; i++) {
				this._editBox.string += this.HOLDER;
			}
			this._oldInputString = this._editBoxStrWithReturn;
			this.scheduleOnce(() => {
				this.updateLayoutHorizontalAlign();
				this.scheduleOnce(() => {
					this._captureNode = new cc.Node();
					this._captureNode.name = "captureNode";
					this._captureNode.parent = this.node;
					this._labelLayout.node.active = true;
					this.captureNode(this._labelLayout.node);
					if (isEditable) {
						this._labelLayout.node.active = false;
					} else {
						this._editBox.node.destroy();
						this._cursor.destroy();
						this._labelLayout.node.destroy();
						this._labelPool.clear();
						this._layoutLabelList = [];
						this._labelLineList = [];
						this._pitchLabelList = {};
					}
					resolve();
				}, 0);
			}, 0);
		}).catch(err => {
            qte && qte.logCatBoth('cocos promise error:', err);
        });
	}

	/**
	 * 设置node尺寸
	 */
	public setNodeSize(width: number, height: number): void {
		this.node.setContentSize(width, height);
	}

	/** ******************************初始化逻辑************************* */
	/**
	 * 更新光标
	 * @param dt
	 */
	update(dt) {
		this._updateCursor(dt);
		if (this.cacheText) {
			let str;
			if (this.cacheText.length > 30) {
				str = this.cacheText.substring(0, 30);
				this.cacheText = this.cacheText.substring(30);
			} else {
				str = this.cacheText;
				this.cacheText = null;
			}
			this._editBox.string += str;
			this.editBoxChanged();
		}
	}

	private initLayout(isSdk?: boolean): void {
		let layoutNode = new cc.Node();
		layoutNode.name = "layoutNode";
		layoutNode.setAnchorPoint(0, 1);
		layoutNode.setContentSize(cc.size(this.node.width, this.node.height));
		layoutNode.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		layoutNode.parent = this.node;
		if (!isSdk) {
			this.addScrollView();
		}
		this._labelLayout = layoutNode.addComponent(cc.Layout);
		this._labelLayout.type = cc.Layout.Type.NONE;
		this._labelLayout.resizeMode = cc.Layout.ResizeMode.NONE;
	}

	// 初始化 自定义editBox
	private initEditBox(isSdk?: boolean): void {
		this.initLayout(isSdk);
		let editBoxNode = new cc.Node();
		editBoxNode.name = "editBoxNode";
		this._editBox = editBoxNode.addComponent(cc.EditBox);
		this._editBox.inputMode = cc.EditBox.InputMode.ANY;
		this._editBox.inputFlag = cc.EditBox.InputFlag.DEFAULT;
		this._editBox.tabIndex = 0;
		this._editBox.maxLength = 200000;
		editBoxNode.setContentSize(cc.size(1, 1));
		editBoxNode.setAnchorPoint(0, 1);
		editBoxNode.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		editBoxNode.parent = this.node;
		editBoxNode.on("text-changed", this.editBoxChanged, this);
		editBoxNode.on("editing-did-ended", this.editBoxEnd, this);

		// 初始化占位符文本
		this._placeHolder = new cc.Node();
		this._placeHolder.name = "placeHolder";
		this._placeHolder.color = cc.color(0, 0, 0, 255);
		this._placeHolder.setAnchorPoint(0, 1);
		this._placeHolder.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		this._placeHolder.parent = this.node;
		let placeHolderLabel = this._placeHolder.addComponent(cc.Label);
		EditUtil.setLabelBlend(placeHolderLabel);
		placeHolderLabel.string = this.string;
		placeHolderLabel.lineHeight = this.lineHeight;
		placeHolderLabel.fontSize = this.fontSize;

		// 初始化光标
		this._cursor = new cc.Node();
		this._cursor.name = "cursor";
		this._cursor.parent = this.node;
		let label = this._cursor.addComponent(cc.Label);
		label.string = this.HOLDER;
		label.fontSize = this.fontSize;
		label.lineHeight = this.lineHeight;
		label.overflow = cc.Label.Overflow.CLAMP; // 超出则截断，以便控制实色块宽度█
		this._cursor.color = cc.color(0, 0, 0, 255);
		this._cursor.width = 3;
		this._cursor.active = false;

		this.setLineHeight(this.lineHeight, false);
		this.setNodeWidth(this.node.width);
	}

	private addScrollView(): void {
		this.node.addComponent(cc.Mask);
		let size = this.node.getContentSize();
		let scrollBarHeight = size.height;

		let scrollBarNode = new cc.Node();
		scrollBarNode.name = 'scrollBar';
		scrollBarNode.setContentSize(8, scrollBarHeight);
		scrollBarNode.parent = this.node;
		scrollBarNode.position = cc.v3(size.width / 2 - scrollBarNode.width / 2, 0);

		let barNode = new cc.Node();
		barNode.name = 'bar';
		barNode.setContentSize(8, scrollBarHeight);
		barNode.parent = scrollBarNode;

		let barSprite = barNode.addComponent(cc.Sprite);
		barSprite.spriteFrame = this.buildSpriteFrame(cc.color(170, 170, 170, 200), cc.rect(0, 0, 8, scrollBarHeight));

		let widget = scrollBarNode.addComponent(cc.Widget);
		widget.right = 0;
		widget.top = 0;
		widget.bottom = 0;

		this.scrollBar = scrollBarNode;
		this.bar = barNode;
	}


	private buildSpriteFrame(color: cc.Color, rect: cc.Rect) {
		let texture = new cc.Texture2D();
		let spriteFrame = new cc.SpriteFrame();
		texture.initWithData(new Uint8Array([color.r, color.g, color.b]), cc.Texture2D.PixelFormat.RGB888, 1, 1);
		spriteFrame.setTexture(texture);
		spriteFrame.setRect(rect);
		return spriteFrame;
	}

	// 初始化对象池
	private initPool(): void {
		// 单个label对象池
		this._labelPool = new cc.NodePool();
		let initCount = 10;
		for (let i = 0; i < initCount; ++i) {
			let labelNode = new TypeNode();
			labelNode.addComponent(SimpleLabelActor);
			this._labelPool.put(labelNode);
		}
	}

	/* ********************文本框的输入等一系列操作********************* */

	public rect(): cc.Rect {
		return cc.rect(
			this.node.x - this.node.width / 2,
			this.node.y - this.node.height / 2,
			this.node.width,
			this.node.height,
		);
	}

	// 点击假输入框，修改光标的位置
	__startIndex: number = -1;
	__endIndex: number = -1;
	public mouseBeginLayout(event: cc.Event.EventMouse, pos = null) {
		if (!this._isEditable) {
			return;
		}
		if (this.pikerviewStatue <= 1) {
			this.pikerviewStatue += 1;
		}
		if (this.isSelectedFirst) {
			return;
		}
		this.cancelPitch();
		this.isEditing = true;
		this.vueLableEnditing();
		this._isCanPitch = true;
		this.setRowData();
		let touchPos = event.getLocation();
		if (pos) {
			touchPos = pos;
		}
		const barBox = this.bar.getBoundingBoxToWorld();
		if (this.widthHeightFixed && barBox.contains(pos)) {
			this.scrolling = true;
			return;
		}
		let realPos = this._labelLayout.node.convertToNodeSpaceAR(touchPos);
		// 先算第几行,再算第几列。
		this.__startIndex = -1;
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			const com: SimpleLabelActor = this._layoutLabelList[i].getComponent(SimpleLabelActor);
			if (com.rect().contains(realPos)) {
				this._cursorLabelStatue.labelIndex = i;
				if (realPos.x > com.node.x) {
					this.__startIndex = i;
					this._cursorLabelStatue.isLabelRight = true;
				} else {
					this.__startIndex = i - 1;
					this._cursorLabelStatue.isLabelRight = false;
				}
				break;
			}
		}
		if (this.__startIndex === -1) {
			this.__startIndex = this.getCursorIndex(cc.v3(realPos.x, realPos.y));
			this._cursorLabelStatue.labelIndex = this.__startIndex;
			this._cursorLabelStatue.isLabelRight = true;
		}
		this.updateCursor();
	}

	// 滑动选中文本
	// eslint-disable-next-line max-lines-per-function
	public mouseMoveLayout(event: cc.Event.EventMouse, pos = null) {
		if (!this._isEditable) {
			return;
		}
		if (!this.isEditing) {
			return;
		}
		if (!this._isCanPitch) {
			return;
		}
		if (this.isSelectedFirst) {
			return;
		}
		let realTouchPos = event.getLocation();
		if (pos) {
			realTouchPos = pos;
		}
		let touchPos = this._labelLayout.node.convertToNodeSpaceAR(realTouchPos);
		// 滑动滚动条
		if (this.widthHeightFixed && this.scrolling) {
			let realPos = this.scrollBar.convertToNodeSpaceAR(pos);
			let d = realPos.y - this.bar.y;
			this.wheelLabelLayout(d);
			this.wheelScrollBar(d);
			return;
		}
		// 先算第几行,再算第几列。
		this.__endIndex = -1;
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			const com: SimpleLabelActor = this._layoutLabelList[i].getComponent(SimpleLabelActor);
			if (com.rect().contains(touchPos)) {
				if (touchPos.x > com.node.x) {
					this.__endIndex = i;
				} else {
					this.__endIndex = i - 1;
				}
				break;
			}
		}
		if (this.__endIndex === -1) {
			this.__endIndex = this.getCursorIndex(cc.v3(touchPos.x, touchPos.y));
		}
		let start = null;
		let end = null;
		if (this.__startIndex < this.__endIndex) {
			start = this.__startIndex;
			end = this.__endIndex;
		} else {
			start = this.__endIndex;
			end = this.__startIndex;
		}
		if (start < 0) {
			start = -1;
		}
		if (end < 0) {
			end = this._layoutLabelList.length - 1;
		}
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			let LabelNode = this._layoutLabelList[i];
			if (i > start && i <= end) {
				LabelNode.getComponent(SimpleLabelActor).updatePitch(true);
				this._pitchLabelList[i] = LabelNode;
			} else {
				LabelNode.getComponent(SimpleLabelActor).updatePitch(false);
				this._pitchLabelList[i] = null;
				delete this._pitchLabelList[i];
			}
		}
	}

	public mouseUpLayout(): void {
		this.scrolling = false;
		if (!this._isEditable) {
			return;
		}
		if (this.pikerviewStatue <= 1) {
			return;
		}
		if (this.isSelectedFirst) {
			this._cursorLabelStatue.labelIndex = this._layoutLabelList.length - 1;
			this._cursorLabelStatue.isLabelRight = true;
			this.isSelectedFirst = false;
		}
		this.updatePropertiesWithLeftIndex();
		this.startEdit();
		this._isCanPitch = false;

		let pitchLength = Object.keys(this._pitchLabelList).length;
		if (pitchLength == 0) {
			this.__startIndex = this._cursorLabelStatue.isLabelRight ? this._cursorLabelStatue.labelIndex : this._cursorLabelStatue.labelIndex - 1;
		} else {
			this._cursorLabelStatue.labelIndex = this.__endIndex;
			this._cursorLabelStatue.isLabelRight = true;
		}
		this.updateCursor();
	}

	// 开始输入模式
	public startEdit(): void {
		this.isSelectedFirst = false;
		this.isEditing = true;
		this.vueLableEnditing();
		this._editBox.focus();
		this._placeHolder.active = false;
		this._cursor.active = true;
		this._blinkTimer = 0;
	}

	// 取消选中
	private cancelPitch(): void {
		// 清除选中状态
		for (let key in this._pitchLabelList) {
			this._pitchLabelList[key].getComponent(SimpleLabelActor).updatePitch(false);
		}
		this._pitchLabelList = {};
		// 如果因为误删，需要补齐真editbox里少的文字
		let sub = this._layoutLabelList.length - this._editBox.string.length;
		if (sub > 0) {
			for (let i = 0; i < sub; i++) {
				this._editBox.string += this.HOLDER;
				this._editBox.focus();
			}
		}
	}

	private clampEditBox() {
		let cursorStr = new Array(this._layoutLabelList.length).fill(this.HOLDER).join('');
		this._oldInputString = cursorStr;
		this._editBox.string = cursorStr;
		this._editBox.focus();
	}

	// 修改 真editBox
	// eslint-disable-next-line max-lines-per-function
	private editBoxChanged(): void {
		if (this.editable && this.editable.properties && this.editable.properties.str == false) { // 不支持输入
			return;
		}
		if (this.isSelectedFirst) {
			return;
		}
		let newLabel = this._editBox.string;
		let index = newLabel.indexOf(this.prefix);
		if (index != -1) {
			let str = newLabel.substring(index + this.prefix.length);
			let reg = /[^>]+(?=<)/ig;
			let regStr = str.match(reg).join("");
			if (index + regStr.length <= this.MAX_LENGTH) {
				this.pasteRichText(str, regStr.length);
			}
			this.clampEditBox();
			return;
		}
		if (newLabel.length > this.MAX_LENGTH) {
			this.clampEditBox();
			return;
		}
		let subNum = newLabel.length - this._oldInputString.length;
		if (subNum > 500) {
			this.cacheText = newLabel.substring(this._oldInputString.length);
			this._editBox.string = newLabel.substring(0, this._oldInputString.length);
			return;
		}
		if (subNum > 0) {
			// 如果有选中的，要直接替换掉（先删除在输入实现替换）
			let pitchLength = Object.keys(this._pitchLabelList).length;
			let pitchReplace = false;
			if (pitchLength !== 0) {
				this.deleteLabel();
				pitchReplace = true;
			}
			let subLab = "";
			let noLab = "";
			for (let i = 0; i < newLabel.length; i++) {
				if (newLabel[i] === this.HOLDER) {
					noLab += newLabel[i];
				} else {
					subLab += newLabel[i];
				}
			}
			if (pitchReplace) {
				noLab = this._oldInputString;
			}
			subLab = noLab + subLab;

			let properties = {
				enableBold: this.enableBold,
				enableItalic: this.enableItalic,
				enableUnderline: this.enableUnderline,
				fontSize: this.fontSize,
				lineHeight: this.fontSize * this.rowSpace,
				color: this.color,
				string: this.string,
			};
			for (let i = 0; i < subNum; i++) {
				let needAddLab = subLab.substr(this._oldInputString.length + i, 1);
				if (needAddLab === this.HOLDER) {
					continue;
				}
				let simpleLab = null;
				if (this._labelPool.size() > 0) {
					simpleLab = this._labelPool.get();
				} else {
					simpleLab = new TypeNode();
					simpleLab.addComponent(SimpleLabelActor);
				}
				// simpleLab.name = needAddLab;
				simpleLab.type = "simpleLab";
				this._labelLayout.node.addChild(simpleLab);
				if (this._cursorLabelStatue.isLabelRight) {
					this._cursorLabelStatue.labelIndex += 1;
				} else {
					this._cursorLabelStatue.isLabelRight = true;
				}
				if (this._cursorLabelStatue.labelIndex === -1) {
					this._cursorLabelStatue.labelIndex = 0;
				}

				simpleLab.setSiblingIndex(this._cursorLabelStatue.labelIndex);
				if (needAddLab == "\n") {
					this._cursorLabelStatue.isLabelRight = true;
					simpleLab.getComponent(SimpleLabelActor).writeLab("", properties, "returnKey");
				} else {
					simpleLab.getComponent(SimpleLabelActor).writeLab(needAddLab, properties);
				}
				this._layoutLabelList.splice(this._cursorLabelStatue.labelIndex, 0, simpleLab);
			}
			this._oldInputString = this._editBox.string;
			// 假性清空输入框的文字
			let editBoxLength = this._editBox.string.length;
			this._editBox.string = "";
			for (let i = 0; i < editBoxLength; i++) {
				this._editBox.string += this.HOLDER;
			}
			this._editBox.focus();
		} else if (subNum < 0) {
			// 不变或者变少说明按下了删除键，回收prefab
			this.deleteLabel();
		}
		// 对所有的字进行从新排版
		this.updateAllLabelCompose();
	}

	updateProperties(isUpdateToVue: boolean = false): any {
		let properties: any = {
			enableBold: this.enableBold,
			enableItalic: this.enableItalic,
			enableUnderline: this.enableUnderline,
			fontSize: this.fontSize,
			lineHeight: this.lineHeight,
			color: this.color,
			string: this.string,
			str: this.convertToRichText(),
			cusorIndex: this._cursorLabelStatue.labelIndex,
			isLabelRight: this._cursorLabelStatue.isLabelRight,
			horizontalAlign: this.horizontalAlign,
			isFixed: this.widthHeightFixed
		};
		properties.selectArr = [];
		for (let key in this._pitchLabelList) {
			properties.selectArr.push(key);
		}
		if (isUpdateToVue) {
			if (this._updateEditPropertiesWithStr) {
				this._updateEditPropertiesWithStr(properties, false);
			}
		} else if (this._updateEditProperties) {
			this._updateEditProperties(properties);
		}
	}

	private clone(obj) {
		let type = Object.prototype.toString.call(obj).slice(8, -1).toLowerCase();
		if (type === "object" && obj["type"] != "textToImg") {
			let json = {};
			for (let i in obj) {
				if (obj.hasOwnProperty(i)) {
					json[i] = this.clone(obj[i]);
				}
			}
			return json;
		} else if (type === "array") {
			let arr = [];
			for (let i = 0; i < obj.length; i++) {
				arr[i] = this.clone(obj[i]);
			}
			return arr;
		}
		return obj;
	}

	private clamp(v: number, min: number, max: number): number {
		if (v < min) {
			v = min;
		}
		if (v > max) {
			v = max;
		}
		return v;
	}

	updatePropertiesWithLeftIndex() {
		if (this._layoutLabelList.length === 0) {
			return;
		}
		let labelIndex = this._cursorLabelStatue.labelIndex;
		if (labelIndex === -1) {
			labelIndex = 0;
		}
		let properties = this.clone(this._layoutLabelList[labelIndex].getComponent(SimpleLabelActor).simEditProperties);
		for (let key in properties) {
			if (typeof this[key] !== "undefined") {
				this[key] = properties[key];
			}
		}
		properties.str = this.convertToRichText();
		properties.cusorIndex = this._cursorLabelStatue.labelIndex;
		properties.isLabelRight = this._cursorLabelStatue.isLabelRight;
		properties.selectArr = [];
		properties.horizontalAlign = this.horizontalAlign;
		for (let key in this._pitchLabelList) {
			properties.selectArr.push(key);
		}
		if (this._updateEditProperties) {
			this._updateEditProperties(properties);
		}
	}

	editBoxEnd() {
		this._cursorLabelStatue.isLabelRight = true;
		this.updatePropertiesWithLeftIndex();
	}

	// 输入框成为选中状态
	public setSelected(): void {
		if (!this._isEditable) {
			return;
		}
		this.isSelectedFirst = true;
		if (this._captureNode) {
			this._labelLayout.node.active = true;
			this._captureNode.active = false;
		}
		if (this.bar) {
			this.bar.active = true;
		}
	}

	// 输入框失去选中状态
	public cancelSelected(): number {
		if (!this._isEditable) {
			return;
		}
		if (this.bar) {
			this.bar.active = false;
		}
		this.isSelectedFirst = false;
		this.pikerviewStatue = 0;
		this.isEditing = false;
		this.vueLableEnditing();
		this.cancelPitch();
		this._cursor.active = false;
		this.convertToTexture();
		if (this._editBox.string.length === 0) {
			if (!this.widthFixed && !this.widthHeightFixed) {
				this.node.width = 200;
				this.node.setPosition(0, 0);
				this._labelLayout.node.width = 200;
				this._labelLayout.node.setPosition(-this.node.width / 2, this._labelLayout.node.height - this.node.height / 2);
				this._updateNodeSize();
			}
			this._placeHolder.active = true;
			this._placeHolder.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		}
		this.updateProperties(true);
		return this._layoutLabelList.length;
	}

	// 转换成texture
	public convertToTexture(): void {
		if (this._captureNode) {
			this._captureNode.active = true;
		} else {
			this._captureNode = new cc.Node();
			this._captureNode.name = "captureNode";
			this._captureNode.parent = this.node;
		}
		this._captureNode.opacity = 0;
		this.captureNode(this._labelLayout.node);
		this._captureNode.opacity = 255;
		this._labelLayout.node.active = false;
	}

	/**
	 * 按下删除键
	 */
	public clickDeleteKey(): void {
		// 因为删除也会触发 text-changed 所以废弃这个接口
	}

	/**
	 * 删除文本
	 * @returns 
	 */
	private deleteLabel(): void {
		// 如果有文字在被选中的状态，那么被选中的所有的文字同时全部删除
		let simpleLabNode = null;
		let pitchLength = Object.keys(this._pitchLabelList).length;
		if (pitchLength !== 0) {
			this.deletePitchLabel();
		} else {
			let leftLabelIndex = this._cursorLabelStatue.labelIndex;
			if (!this._cursorLabelStatue.isLabelRight) {
				leftLabelIndex = this._cursorLabelStatue.labelIndex - 1;
			}
			if (leftLabelIndex >= 0) {
				simpleLabNode = this._layoutLabelList.splice(leftLabelIndex, 1);
				if (simpleLabNode[0].type === "simpleLab") {
					simpleLabNode[0].getComponent(SimpleLabelActor).clearProperty();
					this._labelPool.put(simpleLabNode[0]);
				}
				this._cursorLabelStatue.labelIndex -= 1;
			}
		}
		this._editBox.string = "";
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			this._editBox.string += this.HOLDER;
		}
		this._oldInputString = this._editBox.string;
		this.startEdit();

		this.updateProperties();
	}

	/**
	 * 删除选中文本
	 */
	private deletePitchLabel() {
		let pitchLength = Object.keys(this._pitchLabelList).length;
		if (pitchLength == 0) {
			return;
		}
		let indexList = [];
		for (let key in this._pitchLabelList) {
			indexList.push(Number(key));
		}
		// eslint-disable-next-line prefer-reflect
		let minIndex = Math.min.apply(null, indexList);
		let simpleLabNode = this._layoutLabelList.splice(minIndex, pitchLength);
		for (let i = 0; i < simpleLabNode.length; i++) {
			if (simpleLabNode[i].type === "simpleLab") {
				simpleLabNode[i].getComponent(SimpleLabelActor).clearProperty();
				this._labelPool.put(simpleLabNode[i]);
			}
		}
		this._cursorLabelStatue.labelIndex = minIndex - 1;
		this._cursorLabelStatue.isLabelRight = true;
		this._pitchLabelList = {};
	}

	/**
	 * 对所有的字进行从新排版
	 * @param mustChange 强制改变宽高
	 */
	private updateAllLabelCompose(mustChange?: boolean): void {
		this._labelLineList = [];
		this._editBoxStr = "";
		let childrenList = this._layoutLabelList;
		let preMaxHeight = 0, maxHeight = 0;
		let left = 0, right = 0, top = 0, bottom = 0;
		for (let i = 0, lineIndex = 0; i < childrenList.length; i++) {
			let singleLabel = childrenList[i];
			let curComp = singleLabel.getComponent(SimpleLabelActor);
			singleLabel.labelIndex = i;
			if (i === 0) {
				singleLabel.setPosition(cc.v2(singleLabel.width / 2, -singleLabel.height / 2));
				singleLabel.lineIndex = lineIndex;
			} else {
				let x = 0;
				let y = 0;
				let singleMaxWidth = childrenList[i - 1].x + childrenList[i - 1].width / 2 + singleLabel.width;
				// 超出&固定宽度模式，换行
				if ((singleMaxWidth > this._labelLayout.node.width && (this.widthFixed || this.widthHeightFixed))
					|| curComp.labelType === "returnKey"
					|| childrenList[i - 1].getComponent(SimpleLabelActor).labelType === "labelReturn") {
					// 先将当前行按最大行高重新排列
					let preY = lineIndex == 0 ? -maxHeight / 2 : this._labelLineList[lineIndex - 1][0].y - preMaxHeight / 2 - maxHeight / 2;
					this._labelLineList[lineIndex].forEach(v => v.y = preY);
					lineIndex += 1;
					x = singleLabel.width / 2;
					y = childrenList[i - 1].y - maxHeight / 2 - singleLabel.height / 2;
					preMaxHeight = maxHeight;
					maxHeight = 0;
				} else {
					x = childrenList[i - 1].x + childrenList[i - 1].width / 2 + singleLabel.width / 2;
					y = childrenList[i - 1].y;
				}
				singleLabel.setPosition(cc.v2(x, y));
				singleLabel.lineIndex = lineIndex;
			}
			maxHeight = maxHeight < singleLabel.height ? singleLabel.height : maxHeight;
			if (!this._labelLineList[lineIndex]) {
				this._labelLineList[lineIndex] = [];
			}
			this._labelLineList[lineIndex].push(singleLabel);
			this._editBoxStr += curComp.getLabelStr();

			let r = singleLabel.x + singleLabel.width;
			let t = singleLabel.y + singleLabel.height / 2;
			let b = singleLabel.y - singleLabel.height / 2;
			if (singleLabel.x < left) left = singleLabel.x;
			if (r > right) right = r;
			if (t > top) top = t;
			if (b < bottom) bottom = b;
		}
		if (childrenList.length > 0) {
			// 动态宽度模式，改变 node 宽度
			if (!this.widthFixed && !this.widthHeightFixed) {
				let w = right - left;
				this._labelLayout.node.width = w < this.fontSize ? this.fontSize : w;
			}
			this._labelLayout.node.height = top - bottom;
		}

		const layoutWidth = this._labelLayout.node.width;
		const layoutHeight = this._labelLayout.node.height;
		if (layoutWidth > 15000) {
			this.setNodeWidth(15000, true);
			return;
		}
		if (layoutHeight > 15000) {
			this.setNodeWidth(this.node.width + this.fontSize);
			return;
		}

		if (mustChange) {
			let height = this.widthHeightFixed ? this.node.height : layoutHeight;
			this.setNodeSize(this._labelLayout.node.width, height);
		} else {
			if (!this.widthHeightFixed) {
				// 根据文本长度动态改变 node 大小
				if (this.widthFixed) {
					this.setNodeSize(this.node.width, layoutHeight);
				} else {
					this.setNodeSize(this._labelLayout.node.width, layoutHeight);
				}
			}
		}
		const nodeHeight = this.node.height;

		if (this._updateNodeSize) {
			this._updateNodeSize();
		}
		this.updateLayoutHorizontalAlign();
		if (!this.widthHeightFixed) {
			if (this.scrollBar) {
				this.scrollBar.active = false;
			}
			this._labelLayout.node.setPosition(-this.node.width / 2, nodeHeight / 2);
		}

		this.updateCursor();

		if (this.widthHeightFixed) {
			let posY = this._labelLayout.node.y;
			if (layoutHeight <= nodeHeight) {
				posY = nodeHeight / 2;
			} else {
				if (this._cursor.y + this.lineHeight / 2 > nodeHeight / 2) {
					// 将文本向下移动，使其露出光标
					posY -= (this._cursor.y - nodeHeight / 2 + this.lineHeight / 2);
					this._cursor.y = nodeHeight / 2 - this.lineHeight / 2;
				}
				if (this._cursor.y - this.lineHeight / 2 < -nodeHeight / 2) {
					// 将文本向上移动，使其露出光标
					posY += (this.lineHeight / 2 - nodeHeight / 2 - this._cursor.y);
					this._cursor.y = this.lineHeight / 2 - nodeHeight / 2;
				}
				if (posY - layoutHeight > -nodeHeight / 2) {
					this._cursor.y -= (posY - layoutHeight + nodeHeight / 2);
					posY = layoutHeight - nodeHeight / 2;
				}
			}
			this._labelLayout.node.setPosition(-this.node.width / 2, posY);
			if (this.scrollBar) {
				this.scrollBar.active = layoutHeight > nodeHeight;
				this.scrollBar.height = nodeHeight;
				this.scrollBar.position = cc.v3(this.node.width / 2 - this.scrollBar.width / 2, 0);
				this.bar.height = this.scrollBar.height * nodeHeight / layoutHeight;
				let scrollH = this.scrollBar.height - this.bar.height;
				this.bar.y = (layoutHeight / 2 - posY) * scrollH / (layoutHeight - nodeHeight);
				this.bar.y = this.clamp(this.bar.y, -scrollH / 2, scrollH / 2);
			}
		}
	}

	/*
	 * 统一处理光标位置和大小
	 * index：光标左边文字的下标
	 */
	private updateCursor(): void {
		if (this._cursorLabelStatue.labelIndex == undefined) {
			this._cursorLabelStatue.labelIndex = -1;
			this._cursorLabelStatue.isLabelRight = true;
		}
		if (this._cursorLabelStatue.labelIndex === -1 || this._labelLineList.length === 0) {
			let x = this._cursor.width / 2;
			let y = -this._cursor.height / 2;
			if (this._layoutLabelList.length > 0) {
				x = this._layoutLabelList[0].x - this._layoutLabelList[0].getContentSize().width / 2;
				y = this._layoutLabelList[0].y;
			} else if (this.horizontalAlign === layoutHorizontallys.RIGHT_TO_LEFT) {
				x = this._labelLayout.node.width - this._cursor.width / 2;
			} else if (this.horizontalAlign === layoutHorizontallys.CENTER) {
				x = this._labelLayout.node.width / 2;
			}
			let curPos = this._labelLayout.node.convertToWorldSpaceAR(cc.v2(x, y));
			let curNodePos = this.node.convertToNodeSpaceAR(curPos);
			this._cursor.setPosition(curNodePos);
			return;
		}
		if (this._layoutLabelList.length !== 0 && this._cursorLabelStatue.labelIndex > this._layoutLabelList.length - 1) {
			this._cursorLabelStatue.labelIndex = this._layoutLabelList.length - 1;
			this._cursorLabelStatue.isLabelRight = true;
		}
		let curCursorLabel = this._layoutLabelList[this._cursorLabelStatue.labelIndex];
		let curLeftLabelPos = this._labelLayout.node.convertToWorldSpaceAR(curCursorLabel.getPosition());
		let leftLabelNodeX = this.node.convertToNodeSpaceAR(curLeftLabelPos);
		let curPosX = leftLabelNodeX.x + curCursorLabel.getContentSize().width / 2 + this._cursor.width / 2;
		if (!this._cursorLabelStatue.isLabelRight) {
			curPosX = leftLabelNodeX.x - curCursorLabel.getContentSize().width / 2;
		}

		let curComp = curCursorLabel.getComponent(SimpleLabelActor);
		this._cursor.height = curCursorLabel.height;
		this._cursor.getComponent(cc.Label).lineHeight = curComp.simEditProperties.lineHeight;
		this._cursor.getComponent(cc.Label).fontSize = curComp.simEditProperties.lineHeight;
		this._cursor.setPosition(curPosX, leftLabelNodeX.y);
	}

	// 实现光标闪烁
	private _updateCursor(dt): void {
		if (!this._cursor) {
			return;
		}
		if (!this.isEditing) {
			this._cursor.active = false;
			return;
		}
		if (this._blinkTimer >= 0.5) {
			this._blinkTimer = 0;
			this._cursor.active = !this._cursor.active;
		} else {
			this._blinkTimer += dt;
		}
	}

	/**
	 * 上下键推算光标位置
	 * @param isUp  true 向上键 false 向下键
	 */
	private checkCursorLeftLabel(isUp: boolean): boolean {
		let nodeCursorPos = cc.v2(0, 0);

		let nowLabelIndex = this._cursorLabelStatue.labelIndex;
		if (nowLabelIndex === -1) {
			nowLabelIndex = 0;
		}
		if (this._layoutLabelList.length === 0) {
			return;
		}
		let nowLineIndex = this._layoutLabelList[nowLabelIndex].lineIndex;
		// 向上
		if (isUp) {
			if (this._cursor.y + this.lineHeight > this._labelLayout.node.y) {
				// 已经在第一行
				this._cursorLabelStatue.labelIndex = -1;
				this._cursorLabelStatue.isLabelRight = true;
				return;
			}
			let worldPos = this.node.convertToWorldSpaceAR(
				cc.v2(this._cursor.x, this._cursor.y + this.lineHeight),
			);
			nodeCursorPos = this._labelLayout.node.convertToNodeSpaceAR(worldPos);

			nowLineIndex -= 1;
		} else {
			if (this._cursor.y - this.lineHeight < this._labelLayout.node.y - this._labelLayout.node.height) {
				// 已经在最后一行
				this._cursorLabelStatue.labelIndex = this._layoutLabelList.length - 1;
				this._cursorLabelStatue.isLabelRight = true;
				return;
			}
			let worldPos = this.node.convertToWorldSpaceAR(
				cc.v2(this._cursor.x, this._cursor.y - this.lineHeight),
			);
			nodeCursorPos = this._labelLayout.node.convertToNodeSpaceAR(worldPos);
			nowLineIndex += 1;
		}

		let isCotainLabel = false;
		for (let simpleLab of this._labelLineList[nowLineIndex]) {
			const com: SimpleLabelActor = simpleLab.getComponent(SimpleLabelActor);
			if (com.rect().contains(nodeCursorPos)) {
				isCotainLabel = true;
				this._cursorLabelStatue.labelIndex = simpleLab.labelIndex;
				if (simpleLab.x > nodeCursorPos.x) {
					this._cursorLabelStatue.isLabelRight = false;
				} else {
					this._cursorLabelStatue.isLabelRight = true;
				}
				return;
			}
		}
		// 与某一行没有 相碰撞的文字，所以光标出现在该行 的最后一个文字后边
		if (!isCotainLabel) {
			let oneLineLabels = this._labelLineList[nowLineIndex];
			this._cursorLabelStatue.labelIndex = oneLineLabels[oneLineLabels.length - 1].labelIndex;
			this._cursorLabelStatue.isLabelRight = true;
		}
		if (this._cursorLabelStatue.labelIndex == undefined) {
			this._cursorLabelStatue.labelIndex = -1;
			this._cursorLabelStatue.isLabelRight = true;
		}
	}

	/**
	 * 编辑状态下 ，按下上下左右方向键
	 * @param keyNum 键值
	 */
	public clickDirectionKey(keyNum: number, shift?: boolean): void {
		switch (keyNum) {
			case 37: // 左
				this._cursorLabelStatue.labelIndex -= 1;
				if (this._cursorLabelStatue.labelIndex <= -1) {
					this._cursorLabelStatue.labelIndex = -1;
				}
				break;
			case 38: // 上
				this.checkCursorLeftLabel(true);
				break;
			case 39: // 右
				if (this._cursorLabelStatue.isLabelRight) {
					this._cursorLabelStatue.labelIndex += 1;
				}
				this._cursorLabelStatue.isLabelRight = true;
				break;
			case 40: // 下
				this.checkCursorLeftLabel(false);
				break;
			default:
				return;
		}
		this._cursor.active = true;
		this.updateCursor();
		if (shift && this.__startIndex) {
			this.__endIndex = this._cursorLabelStatue.isLabelRight ? this._cursorLabelStatue.labelIndex : this._cursorLabelStatue.labelIndex - 1;
			let start = Math.min(this.__startIndex, this.__endIndex)
			let end = Math.max(this.__startIndex, this.__endIndex);
			for (let i = 0; i < this._layoutLabelList.length; i++) {
				let LabelNode = this._layoutLabelList[i];
				if (i > start && i <= end) {
					LabelNode.getComponent(SimpleLabelActor).updatePitch(true);
					this._pitchLabelList[i] = LabelNode;
				} else {
					LabelNode.getComponent(SimpleLabelActor).updatePitch(false);
					this._pitchLabelList[i] = null;
					delete this._pitchLabelList[i];
				}
			}
		} else {
			this.cancelPitch();
			this.__startIndex = this._cursorLabelStatue.isLabelRight ? this._cursorLabelStatue.labelIndex : this._cursorLabelStatue.labelIndex - 1;
		}
	}

	/**
	 * 编辑状态下 ，按下Ctrl+A键
	 */
	public clickCtrlA(): void {
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			let LabelNode = this._layoutLabelList[i];
			LabelNode.getComponent(SimpleLabelActor).updatePitch(true);
			this._pitchLabelList[i] = LabelNode;
		}
	}

	/**
	 * 编辑状态下 ，按下Ctrl+C键
	 */
	public clickCtrlC(): void {
		if (this._writeToText && Object.keys(this._pitchLabelList).length > 0) {
			this._writeToText(this.prefix + this.convertToRichText(true));
		}
	}

	/**
	 * 编辑状态下 ，按下Ctrl+X键
	 */
	public clickCtrlX(): void {
		this.clickCtrlC();
		this.deletePitchLabel();
		this.updateAllLabelCompose();
	}

	/**
	 * 粘贴富文本
	 * @param str 
	 * @param length 
	 */
	private pasteRichText(str: string, length: number) {
		/**
		 * 1、如果有选中，将选中删除
		 * 2、将光标前后两部分分别转化为富文本
		 * 3、将str插入到光标处
		 * 4、将新str转化为editbox
		 */
		this.deletePitchLabel();
		let leftLabelIndex = this._cursorLabelStatue.labelIndex;
		if (this._cursorLabelStatue.isLabelRight) {
			leftLabelIndex = this._cursorLabelStatue.labelIndex + 1;
		}

		const re = /(<.+?>)|[^>]+(?=<)/gi;
		const richTextArr = str.match(re);
		let leftBBCode = [];
		let oneStr = "";
		if (richTextArr) {
			while (richTextArr.length > 0) {
				if (richTextArr[0][0] === "<" && richTextArr[0][1] === "/") {
					// 右半部分
					let onceLeftProperty = leftBBCode[leftBBCode.length - 1];
					if (onceLeftProperty === "<br/>") {
						leftBBCode.pop();
					}
					if (leftBBCode.length > 0 && richTextArr[0][2] === leftBBCode[leftBBCode.length - 1][1]) {
						leftBBCode.pop();
					}
				} else if (richTextArr[0][0] === "<") {
					// 左半部分
					let str = richTextArr[0];
					if (!leftBBCode.includes(str)) {
						leftBBCode.push(str);
					}
				} else {
					/*
					 * 文字部分
					 * 特殊字符转义（"<",">"）
					 */
					// eslint-disable-next-line
					let littleRe = /&lt;/g;
					oneStr = richTextArr[0].replace(littleRe, "<");
					// eslint-disable-next-line
					let bigRe = /&gt;/g;
					oneStr = oneStr.replace(bigRe, ">");
					this.writeWord(oneStr, leftBBCode, leftLabelIndex);
					leftLabelIndex += oneStr.length;
				}
				richTextArr.splice(0, 1);
			}
			this._cursorLabelStatue.labelIndex += length;
		}
		this.updateAllLabelCompose();
	}

	/* ***************************操作更新文字数据************************************** */
	/**
	 * 设置选中文本是否粗体
	 * @param data 
	 * @returns 
	 */
	public setBoldEnabled(data: boolean): boolean {
		let array = this.isSelectedFirst ? this._layoutLabelList : this._pitchLabelList;
		for (const key in array) {
			array[key].getComponent(SimpleLabelActor).setBoldEnabled(data);
		};
		let oldBoldEnabled = this.enableBold;
		this.enableBold = data;
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldBoldEnabled;
	}

	/**
	 * 设置选中文本是否斜体
	 * @param data 
	 * @returns 
	 */
	public setItalicEnabled(data: boolean): boolean {
		let array = this.isSelectedFirst ? this._layoutLabelList : this._pitchLabelList;
		for (const key in array) {
			array[key].getComponent(SimpleLabelActor).setItalicEnabled(data);
		};
		this._labelLayout.updateLayout();
		let oldITalicEnabled = this.enableItalic;
		this.enableItalic = data;
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldITalicEnabled;
	}

	/**
	 * 设置是否显示下划线
	 * @param data 
	 * @returns 
	 */
	public setUnderLine(data: boolean): boolean {
		let array = this.isSelectedFirst ? this._layoutLabelList : this._pitchLabelList;
		for (const key in array) {
			array[key].getComponent(SimpleLabelActor).setUnderLine(data);
		};
		let oldIUnderLineEnabled = this.enableUnderline;
		this.enableUnderline = data;
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldIUnderLineEnabled;
	}

	/**
	 * 设置固定宽高
	 * @param data 
	 * @returns 
	 */
	public setIsFixed(data: boolean): boolean {
		let oldFixedEnabled = this.widthHeightFixed;
		this.widthHeightFixed = data;
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldFixedEnabled;
	}

	/**
	 * 设置选中文本字体大小
	 * @param data 
	 * @returns 
	 */
	public setFontSize(data: number): number {
		if (data < 10) {
			return;
		}
		let array = this.isSelectedFirst ? this._layoutLabelList : this._pitchLabelList;
		for (const key in array) {
			array[key].getComponent(SimpleLabelActor).setFontSize(data);
		};
		let oldFontSize = this.fontSize;
		this.fontSize = data;
		this.updateLineHeight();
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldFontSize;
	}

	/**
	 * 设置行高
	 * @param data 
	 * @returns 
	 */
	public setLineHeight(data: number, updatePro: boolean = true): number {
		if (!data) {
			return;
		}
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			this._layoutLabelList[i].getComponent(SimpleLabelActor).setLineHeight(data, true);
		}
		let oldLineHeight = this.lineHeight;
		this.lineHeight = data;
		this.updateAllLabelCompose();
		if (updatePro) {
			this.updateProperties();
		}
		return oldLineHeight;
	}

	private updateLineHeight() {
		this.lineHeight = this.fontSize * this.rowSpace;
		for (const key in this._labelLineList) {
			let maxFontSize = 0;
			const line: TypeNode[] = this._labelLineList[key];
			line.forEach(v => {
				const simpleLabel = v.getComponent(SimpleLabelActor);
				if (simpleLabel.simEditProperties.fontSize > maxFontSize) {
					maxFontSize = simpleLabel.simEditProperties.fontSize;
				}
			});
			line.forEach(v => {
				v.getComponent(SimpleLabelActor).setLineHeight(maxFontSize * this.rowSpace);
			});
		}
	}

	/**
	 * 设置文本行间距
	 * @param data 
	 */
	public setRowSpace(data: number): number {
		if (!data) {
			return;
		}
		data = data < 1 ? 1 : data;
		let oldRowSpace = this.rowSpace;
		this.rowSpace = data;
		this.updateLineHeight();
		this.updateAllLabelCompose();
		this.updateProperties();
		return oldRowSpace;
	}

	/**
	 * 设置选中文本字体颜色
	 * @param data 
	 * @returns 
	 */
	public setFontColor(data: string): string {
		let array = this.isSelectedFirst ? this._layoutLabelList : this._pitchLabelList;
		for (const key in array) {
			array[key].getComponent(SimpleLabelActor).setFontColor(data);
		};
		let oldColor = this.color;
		this.color = data;
		this.updateProperties();
		return oldColor;
	}

	/**
	 * 设置当前节点宽度
	 * @param data 宽度
	 * @param fixed 是否固定宽度
	 */
	public setNodeWidth(data: number, fixed?: boolean): void {
		if (fixed) {
			this.widthFixed = fixed;
		}
		this.node.width = data;
		this.node.setPosition(0, 0);
		this._labelLayout.node.width = data;
		this._labelLayout.node.setPosition(-this.node.width / 2, this._labelLayout.node.height - this.node.height / 2);
		if (this._placeHolder.active) {
			this._placeHolder.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		}
		this.updateAllLabelCompose(true);
		this.pikerviewStatue = 1;
	}

	/**
	 * 设置当前节点高度
	 * @param data 
	 */
	public setNodeHeight(data: number): void {
		this.node.height = data;
		this.node.setPosition(0, 0);
		this._labelLayout.node.height = data;
		this._labelLayout.node.setPosition(-this.node.width / 2, this._labelLayout.node.height - this.node.height / 2);
		this.updateAllLabelCompose();
		this.pikerviewStatue = 1;
	}

	public setNodeAngle(): void {
		this.pikerviewStatue = 1;
	}

	public setCursorLabelIndex(data: number): number {
		if (data == undefined) {
			return;
		}
		let oldData = this._cursorLabelStatue.labelIndex;
		this._cursorLabelStatue.labelIndex = data;
		return oldData;
	}

	public setCursorIsLabelRight(data: boolean): boolean {
		let oldData = this._cursorLabelStatue.isLabelRight;
		this._cursorLabelStatue.isLabelRight = data;
		this.updateCursor();
		return oldData;
	}

	/** 
	 * 刷新每一行每一个字的对齐方式
	 */
	updateLayoutHorizontalAlign() {
		// 左对齐和中对齐化为一类，都是从左边第一个排序
		switch (this.horizontalAlign) {
			// 中对齐
			case layoutHorizontallys.CENTER:
				for (let oneLineList of this._labelLineList) {
					for (let i = 0; i < oneLineList.length; i++) {
						if (i === 0) {
							let length = 0;
							for (let oneLabel of oneLineList) {
								length += oneLabel.width;
							}
							let spaceWidth = (this._labelLayout.node.width - length) / 2;
							oneLineList[i].x = spaceWidth + oneLineList[i].width / 2;
						} else {
							oneLineList[i].x =
								oneLineList[i - 1].x +
								oneLineList[i - 1].width / 2 +
								oneLineList[i].width / 2;
						}
					}
				}
				break;
			case layoutHorizontallys.RIGHT_TO_LEFT:
				// 右对齐
				for (let oneLineList of this._labelLineList) {
					for (let i = oneLineList.length - 1; i >= 0; i--) {
						if (i === oneLineList.length - 1) {
							oneLineList[i].x =
								this._labelLayout.node.width - oneLineList[i].width / 2;
						} else {
							oneLineList[i].x =
								oneLineList[i + 1].x -
								oneLineList[i + 1].width / 2 -
								oneLineList[i].width / 2;
						}
					}
				}
				break;
			default:
				// 左对齐 或者undefined
				for (let oneLineList of this._labelLineList) {
					for (let i = 0; i < oneLineList.length; i++) {
						if (i === 0) {
							oneLineList[i].x = oneLineList[i].width / 2;
						} else {
							oneLineList[i].x =
								oneLineList[i - 1].x +
								oneLineList[i - 1].width / 2 +
								oneLineList[i].width / 2;
						}
					}
				}
				break;
		}
	}

	/**
	 * 设置文本对齐方式
	 * @param data 
	 * @returns 
	 */
	public setHorizontalAlign(data: number): number {
		let oldHorizontalAlign = this.horizontalAlign;
		if (data || data === layoutHorizontallys.LEFT_TO_RIGHT) {
			this.horizontalAlign = data;
		}
		this.updateProperties();
		this.updateAllLabelCompose();
		return oldHorizontalAlign;
	}

	/**
	 * 不可编辑富文本的 对齐方式
	 * @param {string}statue 对齐方式
	 * @warn 默认为左对齐
	 */
	public setEditBoxHorizontalStatue(statue: layoutHorizontallys): void {
		if (statue || statue === layoutHorizontallys.LEFT_TO_RIGHT) {
			this.horizontalAlign = statue;
		}
	}

	/**
	 * 提供 题板 修改指定下角标对应文字的指定颜色
	 * 返回值 返回 原来颜色
	 */
	public changeColorByIndex(colorList: {}): {} {
		this.unschedule(this.convertToTexture);
		this._labelLayout.node.active = true;
		let oldColorList = {};
		if (JSON.stringify(colorList) === "{}") {
			return oldColorList;
		}
		for (let key in colorList) {
			if (!this._layoutLabelList[key]) {
				return;
			}
			oldColorList[key] = this._layoutLabelList[key].getComponent(SimpleLabelActor).setFontColor(colorList[key]);
		}
		this.scheduleOnce(this.convertToTexture, 0);
		return oldColorList;
	}

	private _layoutDataArrRow = [];
	private setRowData(): void {
		this._layoutDataArrRow = [];
		let startNode = this._layoutLabelList[0];
		if (!startNode) {
			return;
		}
		let oldNodePos = { index: 0, pos: this.clone(startNode.position) };
		this._layoutDataArrRow.push(oldNodePos);
		this.__startIndex = -1;
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			const com: SimpleLabelActor = this._layoutLabelList[i].getComponent(SimpleLabelActor);
			if (com.node.position.y !== oldNodePos.pos.y) {
				oldNodePos = { index: i, pos: this.clone(com.node.position) };
				this._layoutDataArrRow.push(oldNodePos);
			}
		}
	}

	private getCursorIndex(pos: cc.Vec3): number {
		if (!this._layoutLabelList[0]) {
			return -1;
		}
		let rowHeight = this._layoutLabelList[0].height;
		for (let i = 0; i < this._layoutDataArrRow.length; i++) {
			let nodePos = this._layoutDataArrRow[i].pos;
			let minX = nodePos.x;
			let minY = nodePos.y - rowHeight / 2;
			let maxY = nodePos.y + rowHeight / 2;
			if (pos.y >= minY && pos.y < maxY) {
				if (pos.x < minX) {
					return this._layoutDataArrRow[i].index - 1;
				}
				if (i === this._layoutDataArrRow.length - 1) {
					return this._layoutLabelList.length - 1;
				}
				return this._layoutDataArrRow[i + 1].index - 1;
			}
			if (pos.y > this._layoutDataArrRow[0].pos.y) {
				return -1;
			} else if (pos.y < this._layoutDataArrRow[this._layoutDataArrRow.length - 1].pos.y - rowHeight / 2) {
				return this._layoutLabelList.length - 1;
			}
		}
		return -1;
	}

	// 检查特殊字符 "<"" ">"
	private checkSpecialStr(str: string): string {
		if (str === "<") {
			return "&lt;";
		} else if (str === ">") {
			return "&gt;";
		}
		return str;
	}

	/**
	 * 输入框内容转富文本格式字符串
	 *return 返回 富文本格式字符串
	 */
	private convertToRichText(selected: boolean = false): string {
		let array = selected ? this._pitchLabelList : this._layoutLabelList;
		return this.convertToRichTextByArray(array);
	}

	private convertToRichTextByArray(array): string {
		let getKey = (i: number) => {
			return Object.keys(array)[i];
		}
		// 判断回车
		let length = Object.keys(array).length;
		for (let i = 0; i < length; i++) {
			let leftLabel = array[getKey(i - 1)];
			let curLabel = array[getKey(i)];
			if (leftLabel && Math.abs(leftLabel.y - curLabel.y) > 1 && curLabel.getComponent(SimpleLabelActor).labelType === "label") {
				curLabel.getComponent(SimpleLabelActor).simEditProperties.returnStatue = true;
			} else if (curLabel.getComponent(SimpleLabelActor).simEditProperties.returnStatue) {
				curLabel.getComponent(SimpleLabelActor).simEditProperties.returnStatue = false;
			}
		}

		let richText = "";
		let richTextRightList = [];
		let lastTextLeftList = [];
		for (let i = 0; i < length; i++) {
			let curLabel = array[getKey(i)];
			let curComp: SimpleLabelActor = curLabel.getComponent(SimpleLabelActor);
			let curlabeFormat = curComp.getCurFormat();
			let leftLabelFomatList = curlabeFormat.curLeftFormat;
			let rightLabelFomatList = curlabeFormat.curRightFormat;
			// 第一个字
			if (i === 0) {
				richText += leftLabelFomatList.join("");
				richText += this.checkSpecialStr(curlabeFormat.labStr);
				lastTextLeftList = leftLabelFomatList;
				richTextRightList = rightLabelFomatList;
				continue;
			}
			// 第二个字开始,先对比
			for (let j = 0; j < lastTextLeftList.length; j++) {
				if (leftLabelFomatList[j] !== lastTextLeftList[j] || !leftLabelFomatList[j]) {
					while (richTextRightList.length > j) {
						richText += richTextRightList.pop();
					}
					break;
				}
			}
			// 再新增
			lastTextLeftList = leftLabelFomatList;
			for (let j = 0; j < rightLabelFomatList.length; j++) {
				if (!richTextRightList[j]) {
					richTextRightList = richTextRightList.concat(rightLabelFomatList.splice(j));
					let needAdd = leftLabelFomatList.slice(j);
					richText += needAdd.join("");
					break;
				}
			}
			richText += curlabeFormat.brData;
			richText += this.checkSpecialStr(curlabeFormat.labStr);
		}
		while (richTextRightList.length > 0) {
			richText += richTextRightList.pop();
		}
		return richText;
	}

	/** ************************富文本字符串转 texture逻辑代码*********************/
	/**
	 * 富文本字符串转成 自定义输入框
	 * @param richTextStr  富文本字符串
	 * @param isEditable   是否可编辑 ，默认可编辑
	 * @returns
	 */

	public convertToEditBox(richTextStr: string): string {
		let oldStr = this.convertToRichText();
		for (let i = 0; i < this._layoutLabelList.length; i++) {
			if (this._layoutLabelList[i].type === "simpleLab") {
				this._layoutLabelList[i].getComponent(SimpleLabelActor).clearProperty();
				this._labelPool.put(this._layoutLabelList[i]);
			}
		}
		this._layoutLabelList = [];
		this._placeHolder.active = false;
		// eslint-disable-next-line
		const re = /(<.+?>)|[^>]+(?=<)/gi;
		const richTextArr = richTextStr.match(re);
		let leftBBCode = [];
		this._editBoxStrWithReturn = "";
		let oneStr = "";
		if (richTextArr) {
			while (richTextArr.length > 0) {
				if (richTextArr[0][0] === "<" && richTextArr[0][1] === "/") {
					// 右半部分
					let onceLeftProperty = leftBBCode[leftBBCode.length - 1];
					if (onceLeftProperty === "<br/>") {
						leftBBCode.pop();
					}
					if (leftBBCode.length > 0 && richTextArr[0][2] === leftBBCode[leftBBCode.length - 1][1]) {
						leftBBCode.pop();
					}
				} else if (richTextArr[0][0] === "<") {
					// 左半部分
					let str = richTextArr[0];
					if (!leftBBCode.includes(str)) {
						leftBBCode.push(str);
					}
				} else {
					/*
					 * 文字部分
					 * 特殊字符转义（"<",">"）
					 */
					// eslint-disable-next-line
					let littleRe = /&lt;/g;
					oneStr = richTextArr[0].replace(littleRe, "<");
					// eslint-disable-next-line
					let bigRe = /&gt;/g;
					oneStr = oneStr.replace(bigRe, ">");
					this.writeWord(oneStr, leftBBCode);
					this._editBoxStrWithReturn += oneStr;
				}
				richTextArr.splice(0, 1);
			}
		}

		// 对所有的字进行从新排版
		this.updateAllLabelCompose();
		if (richTextStr === "") {
			this._placeHolder.active = true;
			this._placeHolder.setPosition(cc.v2(-this.node.width / 2, this.node.height / 2));
		}
		return oldStr;
	}

	/**
	 * 更新文本的时候，立即截图
	 * @param richTextStr 
	 * @returns 
	 */
	public convertToEditBoxWithCapture(richTextStr: string): string {
		this._captureNode.active = false;
		this._labelLayout.node.active = true;
		let oldStr = this.convertToEditBox(richTextStr);
		this.convertToTexture();
		return oldStr;
	}

	// eslint-disable-next-line max-lines-per-function
	private writeWord(str: string, strProperty: any[], index?: number): void {
		let editBoxProperties = {
			enableBold: false,
			enableItalic: false,
			enableUnderline: false,
			fontSize: 32,
			lineHeight: this.lineHeight,
			color: "#FFFFFF",
			string: "单击添加文本",
		};
		for (let i = 0; i < strProperty.length; i++) {
			let propertyType = strProperty[i].slice(0, 2);
			switch (propertyType) {
				case "<b":
					if (strProperty[i] !== "<br/>") {
						editBoxProperties.enableBold = true;
					}
					break;
				case "<i":
					editBoxProperties.enableItalic = true;
					break;
				case "<u":
					editBoxProperties.enableUnderline = true;
					break;
				case "<s":
					// eslint-disable-next-line
					let re = /[^<size=].*?(?=>)/;
					let fontSize = strProperty[i].match(re);
					editBoxProperties.fontSize = Number(fontSize[0]);
					editBoxProperties.lineHeight = editBoxProperties.fontSize * this.rowSpace;
					break;
				case "<c":
					// eslint-disable-next-line
					let re1 = /[^<color=].*?(?=>)/;
					let color = strProperty[i].match(re1);
					editBoxProperties.color = color[0];
					break;
				default:
			}
		}
		if (this._isEditable) {
			let array: TypeNode[] = [];
			for (let i = 0; i < str.length; i++) {
				let labelType = "label";
				if (str[i] === this.HOLDER || str[i] == "\n") {
					labelType = "returnKey";
				}
				let simpleLab = new TypeNode();
				let simpleLabCom = simpleLab.addComponent(SimpleLabelActor);
				simpleLab.type = "simpleLab";
				// simpleLab.name = str[i];
				this._labelLayout.node.addChild(simpleLab);
				array.push(simpleLab);
				simpleLabCom.writeLab(str[i], editBoxProperties, labelType);
			}
			if (index != undefined) {
				this._layoutLabelList.splice(index, 0, ...array);
			} else {
				this._layoutLabelList = this._layoutLabelList.concat(array);
			}
		} else {
			let strArr = [];
			let strIndex = 0;
			for (let i = 0; i < str.length; i++) {
				if (str[i] === this.HOLDER) {
					strArr.push(str.slice(strIndex, i + 1));
					if (str[i + 1]) {
						strIndex = i + 1;
					} else {
						break;
					}
				}
				if (i === str.length - 1) {
					strArr.push(str.slice(strIndex, str.length));
				}
			}

			for (let key of strArr) {
				let simpleLab = new TypeNode();
				let simpleLabCom = simpleLab.addComponent(SimpleLabelActor);
				let lastStr = key.slice(key.length - 1);
				simpleLab.type = "simpleLab";
				let oneStr = key;
				let labelType = "label";
				if (lastStr === this.HOLDER) {
					oneStr = key.slice(0, key.length - 1);
					labelType = "labelReturn";
				}
				// simpleLab.name = oneStr;
				this._labelLayout.node.addChild(simpleLab);
				this._layoutLabelList.push(simpleLab);
				simpleLabCom.writeLab(oneStr, editBoxProperties, labelType);
			}
		}
	}

	/** *****************************截图逻辑******************************/
	/**
	 * 截图
	 * @param {cc.Node}nodeTarget 要截图的节点，null的话则对屏幕截图
	 */
	private captureNode(nodeTarget: cc.Node = null): void {
		let properties = {
			rowSpace: this.rowSpace,
			widthHeightFixed: this.widthHeightFixed,
			horizontalAlign: this.horizontalAlign,
			opacity: this.node.parent.opacity,
		}
		let str1 = JSON.stringify(properties);
		let str2 = this.convertToRichText();
		let str3 = nodeTarget.width.toString();
		let md5 = Md5ts.hashStr(str1 + str2 + str3);
		this._captureNode.setAnchorPoint(0, 1);
		this._captureNode.position = this._labelLayout.node.position;
		// ! 修改过 if (md5 == this.lastMd5 || nodeTarget.activeInHierarchy == false) {  
		if (md5 == this.lastMd5) {
			return;
		}
		this.lastMd5 = md5;
		let node = new cc.Node();
		node.parent = nodeTarget;
		node.setPosition(nodeTarget.width / 2, -nodeTarget.height / 2);
		let camera = node.addComponent(cc.Camera);
		let width = cc.winSize.width;
		let height = cc.winSize.height;
		if (nodeTarget !== null) {
			width = nodeTarget.width;
			height = nodeTarget.height;
		}
		let texture = new cc.RenderTexture();
		texture.initWithSize(width, height);

		camera.alignWithScreen = false;
		camera.ortho = true;
		camera.orthoSize = height / 2;
		camera.targetTexture = texture;

		width = texture.width;
		height = texture.height;
		camera.render(nodeTarget);
		if (this._textToImg) {
			// 通知VUE
			this._textToImg(texture);
		}

		let data = texture.readPixels();
		node.destroy();
		this.captureShow(data, width, height);
	}

	private captureShow(img: ArrayBufferView, width: number, height: number): void {
		let texture2D = new cc.Texture2D();
		texture2D.initWithData(img, cc.Texture2D.PixelFormat.RGBA8888, width, height);
		texture2D.setPremultiplyAlpha(true);
		let spriteFrame = new cc.SpriteFrame();
		spriteFrame.setTexture(texture2D);
		spriteFrame.setFlipY(true);
		let captureSprite = this._captureNode.getComponent(cc.Sprite);
		if (!captureSprite) {
			captureSprite = this._captureNode.addComponent(cc.Sprite);
		}
		captureSprite.spriteFrame = spriteFrame;
		captureSprite.trim = false;
		captureSprite.srcBlendFactor = cc.macro.BlendFactor.ONE;
		captureSprite.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
	}

	/**
	 * 获取文本内容
	 */
	public getEditBoxLabel(): string {
		return this._editBoxStr;
	}
	/**
	 * 	通知vue同步编辑状态 输入情况下不允许回退，前进
	 */
	private vueLableEnditing() {
		(window as any).lableEnditing = this.isEditing;
	}

}
// eslint-disable-next-line no-unused-expressions
(window as any).editbox || ((window as any).editbox = {});
(window as any).editbox.EditBoxTool = EditBoxTool;
