/*
 * <AUTHOR> ya<PERSON><PERSON><PERSON><PERSON>
 * @description  : editBox配置文件
 */

declare namespace editbox {
  export class EditBoxTool extends cc.Component {
    // 文本粗体状态
    public enableBold: boolean = false;
    // 文本倾斜状态
    public enableItalic: boolean = false;
    // 文本下划线状态
    public enableUnderline: boolean = false;
    // 文本是否固定宽高
    public widthHeightFixed: boolean = false;
    // 文本字号
    public fontSize: number = 30;
    // 输入框行高
    public lineHeight: number = 40;
    // 输入框行距
    public rowSpace: number = 1;
    // 文本颜色
    public color: string = "#FFFFFF";
    // 默认文本
    public string: string = "请输入文本";
    // 选中次数
    public pikerviewStatue: number = 0;
    // 是否第一次选中
    public isSelectedFirst: boolean = true;
    // 是否在编辑状态
    public isEditing: boolean = false;
    // 创建后是否获取焦点
    public createWithEdit: boolean = false;
    public editable:any =null;
    /**
     * 更新node尺寸
     */
    // eslint-disable-next-line accessor-pairs
    public set updateNodeSize(value: () => void);

    /**
     * 更新输入框属性
     */
    // eslint-disable-next-line accessor-pairs
    public set updateEditProperties(value: (data) => void);

    /**
     * 因为修改文本更新输入框属性
     */
    // eslint-disable-next-line accessor-pairs
    public set updateEditPropertiesWithStr(value: (data) => void);

    // 文本转图片
    public set textToImg(value: (data) => void);
    public set writeToText(value: (data) => void);

    /**
     * 生成可编辑 富文本
     */
    public createEditBox(): void;

    /**
     * 通过富文本字符串 生成 （不可）可编辑文本框   题板使用
     * @param {string}string 富文本格式字符串
     * @param {isEditable} boolean 生成是否可编辑的文本
     * @warn  调用此方法之前，要先调用 setEditBoxHorizontalStatue();设置文本的对齐方式，默认为左对齐
     * @warn  调用此方法之前，要先调用 setNodeSize(),设置node的尺寸
     */
    public createEditBoxWithString(
      string: string,
      isEditable: boolean = true,
      isSdk: boolean = false
    ): Promise<void>;

    /**
     * 设置node尺寸
     */
    public setNodeSize(width: number, height: number): void;

    /**
     * 不可编辑富文本的 对齐方式
     * @param {string}statue 对齐方式
     * @warn 默认为左对齐
     */
    public setEditBoxHorizontalStatue(statue: layoutHorizontallys): void;

    /**
     *  点击按下
     */
    public mouseBeginLayout(event: cc.Event.EventMouse, pos = null): void;

    /**
     *  滑动选中文本
     */
    public mouseMoveLayout(event: cc.Event.EventMouse, pos = null): void;

    /**
     *  滚动文本
     */
    public mouseWheelLayout(event: cc.Event.EventMouse, pos = null): void;

    /**
     * 编辑状态下 ，按下上下左右方向键
     * @param keyNum 键值
     * @param shift 是否按下 Shift 键
     */
    public clickDirectionKey(keyNum: number, shift?: boolean): void;

    /**
     * 编辑状态下 ，按下 Ctrl+A 键
     */
    public clickCtrlA(): void;

    /**
     * 编辑状态下 ，按下 Ctrl+C 键
     */
    public clickCtrlC(): void;

    /**
     * 编辑状态下 ，按下 Ctrl+X 键
     */
    public clickCtrlX(): void;

    /**
     * 编辑状态下 ，按下删除键
     */
    public clickDeleteKey(): void;

    /**
     *  点击抬起
     */
    public mouseUpLayout(): void;

    /**
     * 获取输入框矩形
     */
    public rect();

    /**
     * 输入框成为选中状态
     */
    public setSelected(): void;

    /**
     * 输入框失去选中状态
     */
    public cancelSelected(): number;

    /**
     * 转换成texture
     */
    public convertToTexture(): void;

    /**
     * 设置选中文本粗体
     */
    public setBoldEnabled(data: boolean): boolean;

    /**
     * 设置选中文本倾斜
     */
    public setItalicEnabled(data: boolean): boolean;

    /**
     * 设置选中文本下划线
     */
    public setUnderLine(data: boolean): boolean;

    /**
     * 设置选中文本是否固定宽高
     */
    public setIsFixed(data: boolean): boolean;

    /**
     * 设置文本行间距
     */
    public setRowSpace(data: number): number;

    /**
     * 设置选中文本字体
     */
    public setFontSize(data: number): number;

    /**
     * 设置输入框的行高
     */
    public setLineHeight(data: number): number;

    /**
     * 设置输入框的字体颜色
     */
    public setFontColor(data: string): string;

    /**
     * 设置输入框的宽
     */
    public setNodeWidth(data: number, fixed?: boolean): void;

    /**
     * 设置输入框的高
     */
    public setNodeHeight(data: number): void;

    public setNodeAngle(): void;

    /**
     * 设置对齐方式
     * @param data 对齐方式     0:左对齐  1:中对齐  2.右对齐
     * @warn 默认为左对齐
     */
    public setHorizontalAlign(data: number): number;

    /**
     * 输入框内容转富文本格式字符串
     *return 返回 富文本格式字符串
     */
    public convertToRichText(): string;

    /**
     * 题板使用
     * @param colorList  每个字的下角标对应着 需要修改的颜色
     * @param 返回值      每个下角标对应的旧的颜色
     */
    public changeColorByIndex(colorList: {}): {};

    /**
     * 题板使用 获取文本内容
     */
    public getEditBoxLabel(): string;

    /**
     * 设计光标在文字左右状态
     * @param data 布尔值，默认 true
     */
    public setCursorIsLabelRight(data: boolean): boolean;

    /**
     * 设置光标周边相关 文字的下角标
     * @param data  index值，number
     */
    public setCursorLabelIndex(data: number): number;

    public convertToEditBox(data: string): string;

    /**
     * 开始输入模式
     */
    public startEdit(): void;
  }
}
