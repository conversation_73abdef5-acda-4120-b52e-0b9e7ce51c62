/**
 * EditUtils.ts 工具类
 * <AUTHOR>
 */
export class EditUtil {
    /**
     * label 加预乘
     * @param label 
     */
    static setLabelBlend (label: cc.Label) {
        // eslint-disable-next-line dot-notation
        label["_srcBlendFactor"] = cc.macro.BlendFactor.ONE;
        // eslint-disable-next-line dot-notation
        label["_dstBlendFactor"] = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
    }

    /**
     * 添加预乘
     * @param texture 
     * @param spr 
     */
    static addPremultiplyAlpha (texture: cc.Texture2D, spr: cc.Sprite) {
        if(texture)
        {
            texture.setPremultiplyAlpha(true);
            spr.spriteFrame = new cc.SpriteFrame(texture);
            spr.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            spr.srcBlendFactor = cc.macro.BlendFactor.ONE;
            spr.dstBlendFactor = cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA;
        }
    }
}