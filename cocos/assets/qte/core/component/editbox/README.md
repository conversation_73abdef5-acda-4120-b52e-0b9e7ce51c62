# [editbox]富文本框
### *canEdit: true*
## 功能列表

# 环境依赖
* cocosCreator 2.4版本已上版本

# 组件功能介绍

* 支持：      
    1.文本输入
    2.选中文本，进行 删除，修改字号、行高、颜色、倾斜、加粗、下划线的编辑，修改输入框的宽高，控制文本对齐方式
    3.供题板 进行文本展示，进行 对应文字染色
# 目录结构描述

├── EditBoxData .ts           // 数据配置文件

├── EditBoxTool .ts           // 主要逻辑文件

├── SimpleLabelActor.ts       // 单个文字逻辑文件

├── TypeNode.ts               // 重写node组件文件             为了满足node有type属性


# 创建方式

EditBoxTool.ts  

	/**
     * 生成可编辑 输入框
     */
    public createEditBox ():void {};

    /**
    * 根据字符串直接生成可编辑输入框
    * @param {string}string 富文本格式字符串
    * @warn  调用此方法之前，要先调用 setEditBoxHorizontalStatue();设置文本的对齐方式，默认为左对齐
    * @warn  调用此方法之前，要先调用 setNodeSize(),设置node的尺寸
    */
    public createEditBoxWithString (string: string):void {}


# 主要操作属性
* EditBoxTool.ts  
    ```
	//设置加粗
    public setBoldEnabled (data:boolean): boolean {}
    //设置倾斜
    public setItalicEnabled (data:boolean): boolean {}
	//设置下划线
    public setUnderLine (data:boolean): boolean {}
	//设置字号
    public setFontSize (data:number): number {}
	//设置行高
    public setLineHeight (data:number): number {}
	//设置色号
    public setFontColor (data:string): string {}
    ```