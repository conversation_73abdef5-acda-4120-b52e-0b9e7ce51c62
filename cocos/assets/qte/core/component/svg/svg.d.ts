/*
 * @FilePath     : /src/component/svg/svg.d.ts
 * <AUTHOR> wanghuan
 * @description  : svg 配置文件
 * @warn         :
 */

declare namespace svg {
  export class RaphaelComponent extends cc.Component {
    onLoad(): void;
    svgText: cc.TextAsset;
    fillColor: cc.Color;
    strokeColor: cc.Color;
    lineWidth: number;
    onInit(): void;
    setSvgScale(delta): void;
    setSvgScaleX(delta): void;
    setSvgScaleY(delta): void;
    refreshSvg(): void;
    changeFillColor(color: cc.Color): void;
    changeStrokeColor(color: cc.Color): void;
    changeStrokeWidth(width: number): void;
    changeFillColorByIndex(color: cc.Color, index: number): void;
    changeStrokeColorByIndex(color: cc.Color, index: number): void;
    changeStrokeWidthByIndex(width: number, index: number): void;

    /**
     * 获得编辑页面8个坐标
     *  2   3   4
     *  1       5
     *  0   7   6
     */
    getEditorPos(): void;
    /**
     * 获得当前图形Rect
     */
    getShapeRect(): cc.Rect;
    /**
     * 修改图形顶点位置
     * @param index 选择的点位
     * @param offsetX
     * @param offsetY
     */
    changeShape(index, wordPos): void;
    setUpdateAnchor(isTrue = false): void;
    /**
     * 获得图形顶点坐标
     */
    getVertexPoss(): Array<cc.Vec2>;
  }
}
