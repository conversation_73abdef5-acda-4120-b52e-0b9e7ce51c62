// 定时器类型

import { SingleBase } from "../base/SingleBase";

// eslint-disable-next-line no-shadow
export enum ExecutionType {
  once, // 默认是一次的
  cycle,
}

/**
 * timer 类
 */
/** 空的定时器组件类  */
class TimerComponent extends cc.Component {}
export class Timer {
  private static TIMER_ID = 999;
  private getNewID (): number {
      return Timer.TIMER_ID++;
  }
  /** 定时器ID */
  private _nTimerID: number = -1;
  public getTimerID (): number {
      return this._nTimerID;
  }
  /** 定时器跑最大时长 */
  private _nMaxTime: number = -1;
  /** 定时器当前计数时间 */
  private _nCountTime: number = 0;
  /** 是否结束 */
  private _bOver: boolean = false;
  public isOver (): boolean {
      return this._bOver;
  }
  private noop = () => {};
  /** 回调函数 */
  private _cb: (dt) => void = this.noop;
  // 后台时，是否参与计算
  private isBack = false;
  // 定时器类型
  private executionType: ExecutionType;
  /**
   * @msg     :
   * @param    {number} time
   * @param    {function} cb
   * @param    {*} executionType
   * @param    {*} isBack
   * @return   {*}
   */
  constructor (
      time: number,
      cb: () => void,
      executionType = ExecutionType.once,
      group = "global",
      isBack = false
  ) {
      this._nTimerID = this.getNewID();
      this._nMaxTime = time;
      this._nCountTime = 0;
      this._cb = cb;
      this._bOver = false;
      this.isBack = isBack;
      this.executionType = executionType;
  }

  updateTimer (dt, backTime = 0): void {
      if (this._bOver) {
          return;
      }

      if (this.executionType === ExecutionType.once) {
          if (this.isBack === true) {
              this._nCountTime += backTime;
          }
      } else if ((this.isBack = true)) {
          this._nCountTime += backTime;
      }
      if (this._nCountTime >= this._nMaxTime) {
          this.stopByCb(this._nCountTime);
          return;
      }
      this._nCountTime += dt;
  }
  /**
   * @msg     : 检测是否是一次性
   * @param    {*}
   * @return   {*} 一次性 ？ true : false
   */
  checkoutType (): boolean {
      if (this.executionType === ExecutionType.once) {
          return true;
      }
      return false;
  }

  stop (isStop = true): void {
      if (this._bOver) {
          return;
      }
      // 判断是否是循环类型
      if (this.executionType === ExecutionType.once) {
          this._bOver = true;
      } else if (this.executionType === ExecutionType.cycle) {
          if (isStop) {
              this._bOver = true;
          }
          this._nCountTime = 0;
      }
  }
  stopByCb (dt): void {
      this.stop(false);
      if (this._cb) {
          let _dt = dt;
          if (this.checkoutType()) {
              _dt = 0;
          }
          this._cb(_dt);
          if (this.checkoutType()) {
              this._cb = null;
          }
      }
  }
}

interface arrayTimers {
  global: Timer[];
  [key: string]: Timer[];
}
export default class TimerManager extends SingleBase {
  private _arrTimer: arrayTimers;
  private backTime = 0;
  private currentTime = 0;

  initInstance () {
      let node = cc.director.getScene().getChildByName("TIMER_NODE");
      if (!node) {
          this._arrTimer = {
              global: []
          };
          let node = new cc.Node("TIMER_NODE");
          let timerCom = node.addComponent(TimerComponent);
          cc.game.addPersistRootNode(node);
          timerCom.schedule(
              (dt) => {
                  this.updateTimer(dt);
                  this.backTime = 0;
              },
              0,
              cc.macro.REPEAT_FOREVER,
              0
          );
      }
  }
  /**
   * @msg     : 初始化 后台监听事件
   * @param    {*} cocosBridge
   * @return   {*}
   */
  setInitInner (cocosBridge?) {
      if (CC_DEV) {
          cc.game.on(cc.game.EVENT_HIDE, () => {
              this.currentTime = Date.now();
              this.backTime = 0;
          });
          cc.game.on(cc.game.EVENT_SHOW, () => {
              this.backTime = Math.floor((Date.now() - this.currentTime) / 1000);
          });
      } else {
          cocosBridge.on("innerPause", () => {
              this.currentTime = Date.now();
              this.backTime = 0;
          });
          cocosBridge.on("innerResume", () => {
              this.backTime = Math.floor((Date.now() - this.currentTime) / 1000);
          });
      }
  }

  updateTimer (dt) {
      for (const key in this._arrTimer) {
          if (Object.prototype.hasOwnProperty.call(this._arrTimer, key)) {
              const element = this._arrTimer[key];
              for (let i = 0; i < element.length; i++) {
                  let timer = element[i];
                  if(timer){
                    timer.updateTimer(dt, this.backTime);
                    if (timer.isOver()) {
                        // delArr.push(i);
                        element.splice(i, 1);
                        i--;
                    }
                  }
              }
          }
      }
  }

  /**
   * @msg     : 获取一个定时器
   * @param    {*} time 间隔时间
   * @param    {*} cb 回调函数
   * @param    {*} executionType 类型
   * @param    {*} isBack 后台是否处理
   * @return   {*} timeId
   */
  getTimer (time, cb, executionType, group, isBack): number {
      let timer = new Timer(time, cb, executionType, isBack);
      if (this._arrTimer[group]) {
          this._arrTimer[group].push(timer);
      } else {
          this._arrTimer[group] = [];
          this._arrTimer[group].push(timer);
      }
      console.log("this._arrTimer", this._arrTimer);
      return timer.getTimerID();
  }

  /** 停止计时器 */
  stopTimer (nTimerId: number, group: string): void {
      let nFindIndex = this._arrTimer[group].findIndex(
          (item) => item.getTimerID() === nTimerId && !item.isOver()
      );
      if (nFindIndex >= 0) {
          this._arrTimer[group][nFindIndex].stop();
          this._arrTimer[group].splice(nFindIndex, 1);
      }
  }
  /**
   * @msg     : 停止定时器组
   * @param    {string} group
   * @return   {*}
   */
  stopGroupTimer (group: string) {
      if (Object.prototype.hasOwnProperty.call(this._arrTimer, group)) {
          const element = this._arrTimer[group];
          for (let i = 0; i < element.length; i++) {
              let timer = element[i];
              if(timer){
                timer.stop();
              }
          }
          delete this._arrTimer[group];
      }
  }

  onDestoryInstance () {
      // 清除所有定时器;
      for (const key in this._arrTimer) {
          if (Object.prototype.hasOwnProperty.call(this._arrTimer, key)) {
              const element = this._arrTimer[key];
              for (let i = 0; i < element.length; i++) {
                  let timer = element[i];
                  if(timer){
                    timer.stop();
                  }
              }
          }
      }
      this._arrTimer = {
          global: []
      };

      // 干掉常驻节点;
      let node = cc.director.getScene().getChildByName("TIMER_NODE");
      if (node) {
          let timerCom = node.getComponent(TimerComponent);
          if (timerCom) {
              timerCom.unscheduleAllCallbacks();
          }
      }
  }
}
