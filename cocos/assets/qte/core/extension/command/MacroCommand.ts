import SimpleCommand from "./SimpleCommand";

/**
 * 组合类型命令基类
 * <AUTHOR>
 */
export default class MacroCommand extends SimpleCommand {

    /** 组合命令列表 */
    private _commandList: Array<{ cmd: SimpleCommand; body: any; }> = [];
    public get commandList (): Array<{ cmd: SimpleCommand; body: any; }> {
        return this._commandList;
    }

    /**
     * 向组合宏中添加子命令
     * @param {{new (): SimpleCommand}} command 子命令
     * @param {Object} body 命令参数
     */
    public addSubCommand (command: new () => SimpleCommand, body?: any): void {
        // eslint-disable-next-line new-cap
        this.commandList.push({cmd: new command(), body});
    }

    public execute (body?: any, option?: any): void {
        for (let obj of this.commandList) {
            // Obj.cmd.newBody = obj.body;
            obj.cmd.execute(obj.cmd.newBody, option);
        }
    }

    public undo (body?: any, option?: any): void {
        for (let obj of this.commandList) {
            obj.cmd.undo(obj.cmd.oldBody, option);
        }
    }
}