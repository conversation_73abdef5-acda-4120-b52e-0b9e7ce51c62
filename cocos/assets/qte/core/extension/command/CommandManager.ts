/**
 * 命令控制类
 * <AUTHOR>
 * @description 负责控制和维护命令。
 */

import { SingleBase } from "../../base/SingleBase";
import BaseCommand from "./BaseCommand";
import SimpleCommand from "./SimpleCommand";

export default class CommandManager extends SingleBase {

    public initInstance () {
        console.log("todo nothing");
    }

    /**
     * 执行命令
     * @param {{new (): BaseCommand}} command 命令对象
     * @param {Object} body 命令参数
     */
    public executeCommand (command: new () => BaseCommand, body?: any, option?: any): BaseCommand {
        if (cc.js.isChildClassOf(command, SimpleCommand)) {
            // eslint-disable-next-line new-cap
            let cmd: SimpleCommand = new command() as SimpleCommand;
            cmd.newBody = body;
            cmd.execute(body, option);
            return cmd;
        } 
        console.log(`${command.prototype} 不是可执行的命令！`);
        
    }

    /**
     * 撤销命令
     * @param {{new (): BaseCommand}} command 命令对象
     * @param {Object} body 命令参数
     */
    public undoCommand (command: new () => BaseCommand, body?: any, option?: any): BaseCommand {
        if (cc.js.isChildClassOf(command, SimpleCommand)) {
            // eslint-disable-next-line new-cap
            let cmd: SimpleCommand = new command() as SimpleCommand;
            cmd.undo(body, option);
            return cmd;
        } 
        console.log(`${command.prototype} 不是可执行的命令！`);
        
    }
}
