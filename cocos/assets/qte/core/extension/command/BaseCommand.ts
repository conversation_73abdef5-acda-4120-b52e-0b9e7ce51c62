/**
 * 命令基类
 * <AUTHOR>
 */

export default abstract class BaseCommand {

    public newBody: any = {};
    public oldBody: any = {};

    // 命令tag：用于自定义标签
    public cmdTag: string = "";

    /**
     * 执行命令接口
     * @param {Object} body 命令参数
     */
    public abstract execute(body?: any, option?: any): void;

    /**
     * 撤销命令接口
     * @param {Object} body 命令参数
     */
    public abstract undo(body?: any, option?: any): void;
}