import MathUtil from "../../utils/MathUtil";

export default class Hermite {

    public static createHer (controlPoints: Array<{ x: number, y: number }>): Array<{ x: number, y: number }> {
        let p0: { x: number, y: number } = null;
        let p1: { x: number, y: number } = null;
        let m0: { x: number, y: number } = null;
        let m1: { x: number, y: number } = null;

        let postions: Array<{ x: number, y: number }> = [];
        for (let j = 0; j < controlPoints.length - 1; j++) {
            p0 = controlPoints[j];
            p1 = controlPoints[j + 1];
            if (j > 0) {
                let temp = MathUtil.pointSubtractionForPoint(controlPoints[j + 1], controlPoints[j - 1]);
                m0 = MathUtil.pointMultiplyForNumber(temp, 0.5);
            } else {
                m0 = MathUtil.pointSubtractionForPoint(controlPoints[j + 1], controlPoints[j]);
            }
            if (j < controlPoints.length - 2) {
                m1 = MathUtil.pointMultiplyForNumber(MathUtil.pointSubtractionForPoint(controlPoints[j + 2], controlPoints[j]), 0.5);
            } else {
                m1 = MathUtil.pointSubtractionForPoint(controlPoints[j + 1], controlPoints[j]);
            }

            let numberOfPoints = 20;
            let t: number = 0;
            let pointStep = 1.0 / numberOfPoints;
            if (j === controlPoints.length - 2) {
                pointStep = 1.0 / (numberOfPoints - 1.0);
                // Last point of last segment should reach p1
            }

            for (let i = 0; i < numberOfPoints; i++) {
                t = i * pointStep;

                let c1 = MathUtil.pointMultiplyForNumber(p0, 2.0 * t * t * t - 3.0 * t * t + 1.0);
                let c2 = MathUtil.pointMultiplyForNumber(m0, t * t * t - 2.0 * t * t + t);
                let c3 = MathUtil.pointMultiplyForNumber(p1, -2.0 * t * t * t + 3.0 * t * t);
                let c4 = MathUtil.pointMultiplyForNumber(m1, t * t * t - t * t);


                let b1 = MathUtil.pointAdditionForPoint(c1, c2);
                let b2 = MathUtil.pointAdditionForPoint(c3, c4);
                let position = MathUtil.pointAdditionForPoint(b1, b2);
                postions.push(position);
                /*
                 * Position = (2.0 * t * t * t - 3.0 * t * t + 1.0) * p0
                 *     + (t * t * t - 2.0 * t * t + t) * m0
                 *         + (-2.0 * t * t * t + 3.0 * t * t) * p1
                 *             + (t * t * t - t * t) * m1;
                 */

                /*
                 *  If (lastPos != VEC2_IGNORE) {
                 *      _drawer -> drawLine(lastPos, position, cocos2d:: Color4F:: RED);
                 *  }
                 * lastPos = position;
                 */
            }


        }
        return  this.resolveCurvePoint(postions);
    }

    /**
     * 塞选路径点
     * @param curveData 
     */
    private static resolveCurvePoint (curveData: {x: number, y: number}[]): {x: number, y: number}[] {
        let resultData = [];
        if (curveData.length <= 0) {
            return resultData; 
        }
        // 上一个线段的角度
        let lastPointAngel = 0; 
        // 累计差值
        let offsetAngel = 0; 
        let offsetIndex = 0;
        // Push first point
        resultData.push(curveData[0]);
        let resSet = () => {
            offsetAngel = 0;
            offsetIndex = 0;
        };
        for (let i = 1; i < curveData.length - 1; i++) {
            const p0 = curveData[i];
            const p1 = curveData[i + 1];
            let angel = MathUtil.getAngle(cc.v2(p0.x, p0.y), cc.v2(p1.x, p1.y));
            offsetAngel += Math.abs((angel - lastPointAngel));
    
            if (offsetAngel > 1) {
                resultData.push(p0);
                resSet();
            }
            if (offsetIndex > 3) {
                resultData.push(p0);
                resSet();
            }
            lastPointAngel = angel;
            ++offsetIndex;
        }
        // Push last one
        resultData.push(curveData[curveData.length - 1]); 
        return resultData;
    } 
}