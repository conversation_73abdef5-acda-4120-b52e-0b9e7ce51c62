import Bezier from "./Bezier";
import Hermite from "./Hermite";

export default class ActionUtiil {

    /**
     * 获取贝塞尔曲线路径点 不经过控制点
     * @param num 返回点的数量默认50
     * @param p1 
     * @param c1 
     * @param c2 
     * @param p2 
     */
    public static getBezierPoints (num, p1, c1, c2, p2): any[] {
        return Bezier.getBezierPoints(num, p1, c1, c2, p2);
    }

    /**
     * 获取多个点的贝塞尔
     * @param num 
     * @param ctrlPosArray 
     */
    static getBezierByArray (ctrlPosArray: cc.Vec2[]): any[] {
        let num = 50 * Math.ceil(ctrlPosArray.length / 3);
        const resultPos = [];
        for (let i = 0; i < num; i++) {
            resultPos.push(Bezier.nBezier(i / num, ctrlPosArray));
        }
        return resultPos;
    }

    /**
     * 埃尔米特 曲线
     * @param controlPoints 
     */
    public static getHermitePoints (controlPoints: Array<{ x: number, y: number }>): Array<{ x: number, y: number }> {
        return Hermite.createHer(controlPoints);
    }
}
