export class Action {
    /** 获取贝塞尔路径 */
    public static getBezierPoints(num, p1, c1, c2, p2): any[];

    /**
     * 获取埃尔米特曲线路径点 经过控制点
     * @param controlPoints 
     */
    public static getHermitePoints(controlPoints: Array<{ x: number, y: number }>): Array<{ x: number, y: number }>;
    /**
     * 获取多个点的贝塞尔
     * @param num 返回点基础数量
     * @param ctrlPosArray 控制点数组
     */
    public static getBezierByArray(num, ctrlPosArray: any[]): any[]; 
}
