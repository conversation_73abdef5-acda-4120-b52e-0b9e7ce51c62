/**
 * 引擎代码注入修改
 */
 export default class EngineExtension {
    public static init () {
        EngineExtension.fixedGraphics();
    }
    /**
     * 修改graphics往返涂抹出现锯齿/毛刺的bug
     */
    public static fixedGraphics () {
        var PointFlags =  cc.Enum({
            PT_CORNER: 0x01,
            PT_LEFT: 0x02,
            PT_BEVEL: 0x04,
            PT_INNERBEVEL: 0x08,
        });
        // @ts-ignore
        cc.Graphics.__assembler__.prototype._calculateJoins = function(impl, w, lineJoin, miterLimit) {
            let iw = 0.0;
            if (w > 0.0) {
                iw = 1 / w;
            }
            // Calculate which joins needs extra vertices to append, and gather vertex count.
            let paths = impl._paths;
            for (let i = impl._pathOffset, l = impl._pathLength; i < l; i++) {
                let path = paths[i];
        
                let pts = path.points;
                let ptsLength = pts.length;
                let p0 = pts[ptsLength - 1];
                let p1 = pts[0];
                let nleft = 0;
        
                path.nbevel = 0;
        
                for (let j = 0; j < ptsLength; j++) {
                    let dmr2, cross, limit;
        
                    // perp normals
                    let dlx0 = p0.dy;
                    let dly0 = -p0.dx;
                    let dlx1 = p1.dy;
                    let dly1 = -p1.dx;
        
                    // Calculate extrusions
                    p1.dmx = (dlx0 + dlx1) * 0.5;
                    p1.dmy = (dly0 + dly1) * 0.5;
                    dmr2 = p1.dmx * p1.dmx + p1.dmy * p1.dmy;
                    if (dmr2 > 0.000001) {
                        let scale = 1 / dmr2;
                        if (scale > 600) {
                            scale = 600;
                        }
                        p1.dmx *= scale;
                        p1.dmy *= scale;
                    }
        
                    // Keep track of left turns.
                    cross = p1.dx * p0.dy - p0.dx * p1.dy;
                    if (cross > 0) {
                        nleft++;
                        p1.flags |= PointFlags.PT_LEFT;
                    }
        
                    // Calculate if we should use bevel or miter for inner join.
                    limit = Math.max(11, Math.min(p0.len, p1.len) * iw);
                    if (dmr2 * limit * limit < 1) {
                        p1.flags |= PointFlags.PT_INNERBEVEL;
                    }
    
    
                    ///////////////////////////###/////////////////////////////
                    // Check whether dm length is too long
                    let dmwx = p1.dmx * w;
                    let dmwy = p1.dmy * w;
                    let dmlen = dmwx*dmwx + dmwy*dmwy;
                    if (dmlen > (p1.len * p1.len) || dmlen > (p0.len * p0.len)) {
                        p1.flags |= PointFlags.PT_INNERBEVEL;
                    }
                    ///////////////////////////###/////////////////////////////
    
        
                    // Check to see if the corner needs to be beveled.
                    if (p1.flags & PointFlags.PT_CORNER) {
                        if (dmr2 * miterLimit * miterLimit < 1 || lineJoin === cc.Graphics.LineJoin.BEVEL || lineJoin === cc.Graphics.LineJoin.ROUND) {
                            p1.flags |= PointFlags.PT_BEVEL;
                        }
                    }
        
                    if ((p1.flags & (PointFlags.PT_BEVEL | PointFlags.PT_INNERBEVEL)) !== 0) {
                        path.nbevel++;
                    }
        
                    p0 = p1;
                    p1 = pts[j + 1];
                }
            }
        }
        
        sp.Skeleton.prototype['set-ani-list'] = function(anilist, loop, index, target) {
            target['ani-list'] = anilist;
            target['ani-list-loop'] = loop;
            target['ani-list-index'] = index;
            // console.log("=====>", anilist, "===>", loop, "---->", index);
            if(cc.isValid(target.node,true)){
                target.node && target.node.emit('spine-ani-list');
            }
        }
    }
   
}