/*
 * @Author: cheng
 * @Date: 2021-09-02 13:52:43
 * @LastEditTime: 2025-02-19 11:26:44
 * @LastEditors: gedengyang_v <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /qte_sdk/sdk/assets/qte/qte-api-export.d.ts
 */
declare namespace qte {

    /** QTE初始化公参 */

    export class QTEParams {
        uid?: string;
        os?: string;
    }

    /** 重置按钮显示逻辑枚举 */
    export enum QTE_RESET_TYPE {
        /** 默认逻辑,提交后隐藏重置按钮 */
        DEFAULT = 0,
        /** 一直显示重置按钮 */
        SHOW = 1,
        /** 一直隐藏重置按钮 */
        HIDE = 2
    }

    export enum AutoSubmitMode {
        RIGHT_SUBMIT = 0, // 答对自动提交
        OPERATE_SUBMIT = 1, //作答即提交
    }


    /** 重置按钮显示逻辑枚举 */
    export enum QTE_SUBMIT_TYPE {
        /** 默认逻辑*/
        DEFAULT = 0,
        /** 一直显示 */
        SHOW = 1,
        /** 一直隐藏 */
        HIDE = 2
    }
    /** 返回结构 */
    export interface ExportTemData {
        guideDuration: number;
        /** 答案 */
        answer?: string;
        /** 答案列表 */
        optionList?: string;
    }
    /**
     * 鲨鱼题版公参
     */
    export class QTETemplateCommonParams {
        type: number;
        tempType: number;
        /** 题目类型 */
        questionType: number;
        name: string;
        /** 资源列表, 需要在初始化时加载 */
        resourceList: string[];
        /** 封面截图资源地址 */
        thumbnail: string;
        /** 题干音频资源地址 */
        guide: string;
        width: number;
        height: number;
        safeHeight: number;
        safeWidth: number;
        /** 背景颜色 */
        backgroundColor: string;
        /** 背景图片资源 */
        texture: string;
        bundleUrl: string;
        /** 选择题 1000 拖拽题 1001 填空题 1002 连线题 1003 口述题 1004 画图题 1005 拼图题 1006 特殊题 1028 讲解页 1029 */
        category: number;
        /** 题干 */
        information?: {
            /** 描述 */
            desc: string
        };
        /** 作答信息 */
        answerBase?: {};
        /** 题版key 创建时传入的 */
        qteKey: string;
    }

    /**
     * shark题版生命周期回传参数
     */
    export class QTETemplateParams extends QTETemplateCommonParams {
        /** 是否有作答痕迹 */
        answerMark: boolean;
        /** 题目是否正确 */
        correct: boolean;
    }
    /**
     * 连续题UI参数
     */
    export class QTESerialUIParams {
        questionIndex: number; // 当前题目下标
        questionCount: number; // 题目总数
        arrResult?: boolean[] // 作答结果
        // 小题切换时的回调
        changeQuestion: (params: number) => Promise<void> | void;
    }

    /**
     * shark题版提交周期使用的参数
     */
    export class QTETemplateSubmitParams extends QTETemplateParams {
        submitAnswer: SubmitAnswer;
    }
    /**
     * 每次触发判断正误逻辑回传参数
     */
    export class QTETemplateJudgeParams extends QTETemplateParams {
        /** 本次操作是否正确 */
        singleCorrect: boolean;
    }

    /**
     * shark题版生命周期函数
     */
    export interface QTEHook {
        /** 题版创建完成后调用 */
        onCreate: (vo: QTETemplateCommonParams) => Promise<void> | void;
        /** 创建完成onCreate钩子函数执行完,下一帧调用 */
        onStart: (vo: QTETemplateCommonParams) => Promise<void> | void;
        /** 题版重置时调用 */
        onReset: (vo: QTETemplateParams) => Promise<void> | void;
        /** 题版提交时调用 */
        onSubmit: (vo: QTETemplateSubmitParams) => Promise<void> | void;
        /** 题版暂停时调用 */
        onPause: (vo: QTETemplateParams) => Promise<void> | void;
        /** 题版回复时调用 */
        onResume: (vo: QTETemplateParams) => Promise<void> | void;
        /** 题版销毁时调用 */
        onDestroy: (vo: QTETemplateParams) => Promise<void> | void;
        /** 题版触发判断时调用 */
        onJudge: (vo: QTETemplateJudgeParams) => Promise<void> | void;
        /** 题版状态数据更改时调用 */
        onStateChange: (vo: QTETemplateJudgeParams, states: any) => Promise<void> | void;

        /**普通题结束 调用通用反馈弹窗 */
        onShowResult: (vo: any) => Promise<void> | void;
    }
    /**
     * 对应的wiki地址: https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=300011273
     */
    export interface QTEHooks extends Partial<QTEHook> { }
    // --------------------- 对外暴露的class ----------------------
    // 适配器
    export class Adapter {

        playAudio?: (clip: cc.AudioClip, loop: boolean, volume: number, callback: () => void) => number;
        stopAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
        pauseAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
        resumeAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;
        startRecordAction?: (recordData: any, startRecordCb: any, removeRecordCb: any, startEndCb: any) => void;
        stopRecordAction?: (stopEndCb?: any) => void;
        checkRecordAuthorityAction?: (callBack) => void;
        toast?: (content: string) => void;
        logCat?: (logType, type, args) => void;
        getUserInfo?: (callBack: Function) => void;
        //小鹿练字获取作答结果
        xlPracticeRecover?(callback: Function): void;
        //小鹿练字--拍照上传
        uploadImageAutoMark?(data: {
            type: 1 | 2,
            characters: string,
            tImg?: string,
            tArray?: { tId: number, state: number }[],
            tID?: string
        }, callback: Function): void;
        //小鹿练字--跳转学习报告页面
        jumpStudyReport?(): void;
        // 设置页面到最高层
        setToTopLayer?(childNode: cc.Node): void;

        // bundle或者资源加载失败调用action 检查
        assetsCheck?(param: { path: string, isAssest?: boolean }): void;

        // 开始识别
        startRecognitionAction?(param: {}, cb: Function): void;
        //结束识别
        stopRecognitionAction?(): void;
        //检查麦克分权限无弹框
        checkAudioPermission?(cb: Function): void;
        // 上报学生端状态
        uploadstustatus?(param: { status: number }): void;
        // 点赞送花 互动
        zanInter?(param: { "toUid": string, "stats": any[] }): void;
        // 切座位
        switch?(param: { timeout: number, round: number, isFinial: boolean }): void;

        // 请求互动题信令恢复
        interRecover?: () => void;

        // 获取当前用用户信息
        getUserInfo?: (callBack: Function) => void;
        // 上台
        hdAvatarLocate?(param: { num: number, positions: any[], type?: string }): void
        // 下台
        hdRemoveAvatar?(param: { uid: any[], type?: string }): void;
        // 加学分通用反馈
        corebusInteractResult?: (param: any) => void;
        // 点赞
        liveShowThumb?: (param: any) => void;

        // 获取小组学生信息
        getGroupUserInfo?: (callBack: Function) => void;
        // 获取系统时间
        pingTime?(param: Function): void;
        // 个性化纠音跳转到报告页并关闭容器
        closeGXHJYContainer?(): void;
        // 本地存储
        setLocalData?: (key: string, value: any) => void;
        // 获取本地存储
        getLocalData?: (key: string) => any;
    }

    export var adapter: Adapter;
    class QuoteNode extends cc.Node {
        /** 题版play接口，开始播放题干/自动播放的音频 */
        play(): void;
        submit(): QTETemplateSubmitParams | undefined;
        reset(): void;
        pause(): void;
        resume(): void;
        /*** 展示正确答案 */
        showCorrect(): void;
        getBaseVo: QTETemplateCommonParams | undefined;
        async getTemData(): Promise<ExportTemData>;
        upateCustomData(key: string, data: any);
        playFeedBackAction(data: any): void;
        showUserAnswerState(data: any): void;
        /**编辑器预览获取参考答案/*/
        setReferenceAnswer(): any;
        // 切换小题
        switchQuestion(index?: number): void;
    }

    // 题版创建的扩展参数
    class QTEOption {
        /** 
         * qte主键，用于区分多个qte对象同时存在的值，如果不传入则会默认生产一个uuid作为key值
         *  例如：
         *      直播业务中，可以传入pageId作为key，这样主讲端状态同步时就以该key值进行状态同步。
         *      如果不自定义key值会导致状态同步时，无法找到对应同步的qte实例。
         */
        qteKey?: string;
        resourceMap?: any;
        // 恢复数据
        recoverData?: any;
        /** 配置信息 */
        settings?: QTE_SETTINGS;
        /** 操作模式 */
        openMode?: QTE_OPENMODE
    }

    export enum QTE_OPENMODE {
        SHOW_NORMAL = 0,
        SHOW_CORRECT = 1,
        SHOW_USER_ANSWER = 2,
        SHOW_TEACHER = 3            // 主讲端模式
    }

    export class QTE_SETTINGS {
        /** 重置按钮显示逻辑 */
        reset?: QTE_RESET_TYPE;
        submit?: QTE_SUBMIT_TYPE;
        playSubmitAni?: boolean;
        isAnswerResultUI?: boolean;     // 是否展示小鳄鱼答案结果UI
    }

    export interface Analysis {
        text?: string;
        audio?: {
            url: string;
            duration: number;
        }
        imageUrl?: string;
    }


    function getTemplateByKey(key: string): any;

    declare var openMode: QTE_OPENMODE;// 默认值
    // ------------------ qte作用域下的一级API -------------------
    function initialize(adapter?: Adapter, qteHooks?: QTEHooks, params?: QTEParams);

    function create(data, options?: QTEOption): Promise<QuoteNode>;

    function destroy();
    /**
     * 获取当前实体渲染数据
     */
    function getStates();

    /**
     * 状态恢复
     * @param dataStates 
     */
    function recover(dataStates: any[]);

    /**
     * 暂停
     */
    function pause();

    /**
     * 恢复
     */
    function resume();

    /**
     * 回到前台
     */
    function page_show();

    /**
     * 退到后台
     */
    function page_hide();

    /** 
     * 页面最小化
     */
    function fold();
    /** 
     * 页面最大化
     */
    function unfold();

    /**
    * 添加全局组件
    */
    function fWAddComponent(data: any, parent: cc.Node): Promise<QTEEntityNode>;
    /**
     * 停用全局组件
    */
    function stopfWComponents();
    /**
     * 停止播放全局组件
    */
    function stopVoicefWComponents();
    /**
     * 获取全局播放时长
    */
    function getfWComponentsTemData(data: ComponentData[]): Promise<ExportTemData>;

    /**
     * 播放播放全局组件
    */
    function playfWComponents();

    /**
    * 重制播放播放全局组件
   */
    function resetPlayfWComponents();
    /**
        * 打开解析
       */
    function createAnalysis(data: Analysis, node: cc.Node);
    /** 删除解析 */
    function deletAnalysis();

    export interface QuestionConfigData {
        components: ComponentData[];
        stageData: StageConfig;
        extraStageData: ExtraStageConfig;
        extraDataMap: QuestionMetaData;
        resourceList: ResourceConfig;
        audios: { [key: string]: string };
        animationsForClient: {
            [key: string]: AnimationData[];
        };
        tid: number;
    }

    export class QTETemplateData {
        components: any[];
        extraDataMap: {};
        resourceList: string[];
        template: {
            questionType: number;
            isSpecialQuestion: boolean;
            name: string;
            category: number;
            tempType: number;
            bundleUrl: string;
            bundleName: string;
            tags: [];
            stage: {
                width: number;
                height: number;
                safeWidth: number;
                safeHeight: number;
            };
            extraConfig: [];
            animationConfig: [];
            video: number;
            smts: number;
            features: {
                isQuestion?: number,//是否组卷、是否可发题
                isOral?: number,//是否含有口述逻辑
                isGroup?: number,//是否是题组
                hasVideo?: number,//是否包含视频
                hasMys?: number,//是否包含神秘提示
                demoPage?: number,//讲解页
                canInteract?: number,//是否可交互
            }
        };
        animations: {};
        animationsForClient: {};
        stageData: StageConfigData;
        extraStageData: {
            isAutoSubmit?: boolean;
            answerWrongCount?: number;
            errorType?: number;
            defaultAction?: boolean;
            dragRelation?: any;
            closeDefaultFeedback?: boolean;
            hasRecover?: boolean;
        };
        thumbnail: string;
        questionType: number;

        [key: string]: unknown;

        constructor(data: any) {
        }

        /**
         * 解析ArtInfo，用于渲染每个page的bundle
         * @param data
         */
        parse: (data: any) => void;

        clear: () => void;
    }
    export abstract class BaseComponent extends cc.Component {
        //#region 基础属性设置
        public setActive(active: boolean): void;
        public setWidth(width: number): void;
        public setHeight(height: number): void;
        public setPosX(x: number): void;
        public setPosY(y: number): void;
        public setColor(color: string | cc.Color): void;
        public setzIndex(val: number): void;
        public setAngle(val: number): void;
        public setScaleX(val: number): void;
        public setScaleY(val: number): void;
        public playAudio(audio: cc.AudioClip, loop: boolean = false, cb: () => void = null): number;
        public stopAudio(audioId: number): void;
        public getRemoteRes(url: string, type: typeof cc.Asset, onComplete: (err, res) => void): void;
        public clearRemoteRes(url: string[]): void;
        public showToast(content: string): void;
        public showNoticeChoice(str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean): void;
        /**
         * 初始化
         * @param {data: {data: any, engine: anys}} 
         */
        public abstract initComponent(data?: any): void;

        // 编辑器中使用全部组件加载完成
        public initAllComponentsFinish(): void;
        /**
         * 属性更新
         * @param key 属性key
         * @param data 参数 
         */
        public abstract changeProperties(key: string, data: any): void;

        /**
         * @deprecated 弃用
         * 更换skin
         * @param path 资源路径 
         */
        public abstract changeSkine(path: string, param?: any): void;

        /** 宽高变化后触发的函数 */
        public onSizeChange(rect: cc.Size): void;

        public setPlayFunc(func: (audio: cc.AudioClip, loop: boolean, cb: () => void) => number): void;

        public setStopAudioFunc(func: (audioId: number) => void): void;

        public setGetRemoteFunc(func: (url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => void): void;

        setShowToastFun(func: (content: string) => void): void;

        public setShowNoticeChoice(func: (str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) => void): void;
    }

    interface ResultData {
        status: number,
        wordList: [],
        score: number,
        audioUrl?: string,
        audioTime?: number,
    }


    enum EngineCallBack {
        // CreateReadyCb       = 1,    // 创建成功回调
        // RecordReadyCb       = 2,    // 准备完成回调
        StartRecordingCb = 3, // 开始录音回调
        // VolumeCb            = 4,    // 音量返回
        // RecordingResultCb   = 5,    // 测评中回调
        // ResultCb            = 6,    // 测评结果回调
        RecordSuccess = 6,
        // FailCb              = 7,    //
        // RecordFailCb        = 8,    // 录音失败回调
        // StopCb              = 9,    //
        // ReadGuideAudio      = 10,   // 阅读提干音频
        AnswerCorrectCb = 11, // 答题完成回调
        UploadStop = 12,   // 开始停止录音
        // NoSpeakeCb          = 13,   // 无声状态
        WordAudioStop = 14,     // 跟读单词音频停止
        RecordError = 15, // 答题失败
        CheckAuthority = 16     // 检测麦克风是否有权限
    }



    export class QTERecordAdapter {
    }


    export class MicropComponent extends BaseComponent {

        /** 初始化 */
        public templateInit(engine: QTERecordAdapter, isCheck?: boolean): void;
        /** 开始录音 */
        public micBegin(): void;
        /**
      * 注册回调事件
      * @param callBack
      * @param cb
      */
        public addCallBackListener(option: { key: EngineCallBack; callBack: (res?: ResultData) => void }[]): void;
        /** 停止音频播放 */
        public stopAudioLaba();
    }


    export class SpeakerComponent extends BaseComponent {
        /** 重置音频组件 */
        public resetVoice(): void
        /** 播放完成后回调 */
        public setPlayReplateCountCallBack(cb: Function): void
        /** 自动播放 */
        public autoPlayVoice(): void;
        /** 设置倒计时父级 */
        public setCountParent(node: cc.Node): void;
        /** 停止音频 */
        public stopVoice(): void;
        /**停止音频 不允许再播放了 */
        public stopPlayEnd(): void;
    }

    export enum RecordState {
        Idle = "idle",
        Ready = 'ready',
        Recording = "recording",
        Loading = "loading",
        Stop = "stop",
        Error = "error",
    }
    export class RecordComponent extends BaseComponent {
        //初始化组件
        public initComponent(data: any);
        //设置是否跳过准备阶段
        public setIsSkipReady(bool: boolean);
        //设置回调是否结束，多次调用组件时使用
        public setIsFinish(bool: boolean);
        //设置是否显示麦克风分数
        public setIsShowScore(bool: boolean);
        //开始录音
        public startRecord();
        //停止录音
        public stopRecord(reason: number = 1);
        //注册回调
        public addCallBackListener(option: { key: EngineCallBack; callBack: (data) => void }[]);
        //触发回调
        public sendCallBack(callBack: EngineCallBack, data: any)
        //修改状态
        changeState(recordState: RecordState, score?: string)
        //初始化事件
        templateInit(engine);
        //重置组件
        reset();
        //设置麦克风分数显示
        stopShow(num);
    }
    export class VoiceEngineProp {
        // 评测类型：1 - 单词；2 - 句子； 3 - 段落； 4 - 问题回答； 5 - 复述;  6 - 音标
        wordType: WordType;
        evaluatingText: string; // 评测内容
        answerDuration: number = 15; // 答题时长
        isUsePhonetics?: boolean;        // 是否使用音标
        evaluatePhonetics?: string;    // 音标评测内容
        // 最低分
        minScore?: number;
    }
    export interface recordResultData {
        // 分数
        score?: number;
        // 音频
        audio?: string;
        // 状态 是否完成
        status?: number;
    }

    export class RTRComponent extends BaseComponent {

        // 初始化组件容器
        public async templateInit(engine): Promise<boolean>;
        // 更新评测
        public async updateWord(data: VoiceEngineProp): Promise<boolean>;
        // 注册回调
        public addCallBackListener(option: { key: EngineCallBack; callBack: (data: recordResultData) => void }[]);
        // 暂停评测
        public onGamePause(): void;
        // 恢复评测
        public onGameResume(): void;
        // 重置组件
        reset();
        // 销毁组件
        onDestroy();
    }

    //画笔组件控制器
    export class DrawControl extends BaseComponent {
        //当前仅有物理作图题与画笔组件使用 触发画笔
        public onPenClick(index: number = -1);
        //当前仅有物理作图题与画笔组件使用 触发撤销
        public onCancelClick();
        //修改画笔颜色
        public setDrawColor(color: string[]);
        //重置画笔组件 清空state
        public resetCmpt();
        //获取所有绘图组件绘制线段的总段数
        public getDrawLineLength();
        //获取绘图组件是否正在绘制线段
        public getDrawCmptIsTouching();
        //关闭画笔组件
        public closePen();
    }

    export abstract class QTETemplate extends cc.Component {
        // 当前题板唯一id
        public UUID: string = "invalid template uuid";
        // 当前同步数据
        state: QTEBaseState;
        // qte
        private _qte: QTESystem;
        public get qteSys(): QTESystem;
        // 题板配置属性
        protected _stageConfig: StageConfigData = null;
        // 锁屏引用计数
        private _nLockCount: number = 0;
        // 题干音频&音频组件默认时长
        private _nGuideDuration = -1;

        /** 是否操作过 */
        protected get _bIsOperated(): boolean
        protected set _bIsOperated(operate: boolean): void
        /** 题版是否正确 */
        protected get _bCorrect(): boolean;
        protected set _bCorrect(correct: boolean): void
        private _openMode: QTE_OPENMODE;
        /** 打开模式 */
        public set openMode(mode: QTE_OPENMODE): void;
        public get openMode(): QTE_OPENMODE;

        /** 答案信息 */
        protected _answerBase = {};
        public setAnswerBase() { // 是否需要
            console.log('you can modify the fun to set answer base data')
        }

        /** 题版bundle名称 */
        protected _strBundleName: string = "";
        /** 本地资源列表 */
        protected _arrLocalResList: { path: string, type: typeof cc.Asset }[] = [];
        /** 本地资源缓存,方便题版内部获取 */
        private _arrLocalRes = {}

        onLoad(): void

        /** 题版内私有资源加载函数 */
        async loadPrivateResource(): Promise<void>;
        /** 题版创建组件API */
        async createEntityNode(componetData: ComponentData, extraProp: QuestionMetaData, parent: cc.Node): Promise<QTEEntityNode>;

        getLocalAsset(path: string): cc.Asset;
        /**
         * 此方法需要重写，用于watch所需要的数据
         * @returns {[key: string]: (val: any, oldVal: any) => void}
         */
        watcher(): { [key: string]: (val: any, oldVal: any) => void } {
            return null;
        }


        /**
         * 根据业务组件id获取实体节点
         * @param {string} id 业务组件id
         */
        public getEntityById(id: string): QTEEntityNode;
        /**
         * 根据业务组件tag获取实体节点
         * @param {string} tag 业务组件tag
         */
        public getEntitiesByTag(tag: string): QTEEntityNode[];

        public getEntitiesMap(): Map<string, QTEEntityNode[]>;

        public getEntitiesList(): QTEEntityNode[];

        public getEntitiesBySubType(subType: string): QTEEntityNode[];


        page_hide(): void;

        page_show(): void;

        /** 
         * 页面最小化点, 击右上方最小化
         */
        fold(): void;
        /** 
         * 页面最大化, 点击左下角继续作答
         */
        unfold(): void;

        /** 编辑器引导动画结束后执行 */
        public onStartAnswering() {

        }
        /** 提交前设置结构化数据 */
        protected setSubmitAnswer(): any;
        /**
         * framework 同步特殊消息
         * @param key 
         * @param data 
         */
        public upateCustomData(key: string, data: any): void;

        /** 调用编辑器配置动画之前 业务自定义 */
        onAnswerActionFeedBackBefore(): Promise<void>;
        /**
        * framework 播放反馈动画
        * @param key 
        * @param data 
        */
        public async playFeedBackAction(data: any): void;

        /** 调用编辑器配置动画之后 业务自定义 */
        onAnswerActionFeedBackEnd(): Promise<void>;

        /** 题版  主动提交前提示 */
        onSubmitBeforeCheck(): boolean;
        /**
         * 提交
         */
        submit(): QTETemplateSubmitParams;

        pause(): void;

        resume(): void;

        getBaseVo(): QTETemplateCommonParams;

        play(): void;

        getAutoSubmitMode(): AutoSubmitMode;
        /** 题版 重置前提示 */
        onResetBeforeCheck(): boolean;

        reset(): void;
        /**
         * 重置后给题版内部使用
         */
        protected onResetEnd(): void;

        /** 提交结束后给题版内部使用  */
        protected onSubmitEnd(): void;

        public playAudio(url: string | cc.AudioClip, loop: boolean = false, callback?: () => void): number;
        /** 跟进题版内置资源地址, 创建entity Sprite
         * @id entityNode Id
         * @tag entityNode tag
         * @localUrl 题版bundle内路径
         */
        async createLocalSprie(id: string, tag: string, localUrl: string, parent: cc.Node): Promise<QTEEntityNode>;


        onDestroy(): void;
        /** 显示正确答案 */
        public showCorrect(): void;
        /** 获取全局配置 */
        public getStageConfig(): StageConfigData;
        public bolckInput();
        public unbolckInput();
        /** 增加连续题UI */
        public addSerialUI(data: qte.QTESerialUIParams): void;
        /** 增加透明触摸屏蔽层 */
        public addTouchMask(): void;
        /**编辑器预览获取参考答案/*/
        public setReferenceAnswer(): any;
        /**  获取参考答案*/
        public getReferenceAnswer(): any[] | null;
        /** 增加作答详情 objResult  .arrResult作答数据数组类型  .dt回调时间<可选>  .cb回调函数<可选> */
        public async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node>;
    }

    export class QTEComponent extends cc.Component {

    }
    export class QTESystem extends QTEBaseMeta {
        public start(template: QTETemplate, config?: { [key: string]: { cmpt: new () => QTEComponent; options?: any }[] }, onComplete?: () => void): void;
    }


    export interface SubmitAnswer {
        answerContent?: AnswerContentItem[] | any[],           // 作答内容
        pContent?: PContentItem[] | any[],                      // 作答产生数据
        [propName: string]: any
    }
    export interface AnswerContentItem {
        signalId: number,                // 标号
        type?: string,                    // 类型
        isCorrect?: boolean,                 // 单个空的对错
        content?: string                  // 内容
    }
    export interface PContentItem {
        type: string,                    // 类型
        content: string                  // 内容
        expendType?: string               // 指定类型 
    }

    export class QTEEntityNode extends cc.Node {
        // component
        public componentData: any;
        // qte uuid
        public qteUUID: string;
        // id
        public qid: string;
        // tag
        public qtag: string;

        // 节点属性
        public properties: {
            [key: string]: any;
        };
        // 业务属性
        public extraDataMap: {
            [key: string]: any;
        };
        // 额外数据
        public extra: any = null;

        /**
         * 返回 node 的指定动画数据，如果没有该动画，返回 null
         * @param name 
         */
        public hasAnimation(name: string);
        public skipAnimation(name: string);
        // 播放动画
        public playAnimation(name: string, callback: () => void, canSkip?: boolean): void;

    }
    export class QTEQuestionNumberUI extends cc.Component {
        node: cc.Node;
        public setQuestionNumber(num_str: string): void;
    }
    export class QTEUtils {
        /**
         * 播放 spine 动画
         * @param spine 
         * @param array 
         * @param loop 
         * @param index 
         */

        public static playSpineArr(spine: sp.Skeleton, array: string[], loop: boolean, index: number = 0): void
        public static instance<T extends SingleBase>(type: new () => T, uuid: string): T;
        /**舞台空间坐标转换节点坐标 */
        public static convertStageToNodeSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2;
        /**节点坐标转换为舞台空间坐标  */
        public static convertToStageSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2;
        /** 加载预制答案框 */
        public static async loadAnswerBorder(): Promise<cc.Prefab>;
        public static flowerAudioRecord(param: { backgroundSoundPath?: string, type: number, timeMax: number, callBack?: Function }): void;
        /** 加载预制题号 */
        public static async loadQuestionNumberUI(num_str: string): Promise<qte.QTEQuestionNumberUI>;
        /** 增加语音题分数  */
        public static addListenQuestionSorce(num: number): number;
        /** 小鳄鱼弹框 增加作答结果 UI */
        public static async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node>;
        // 获取切换小题截图
        public static async getSwitchQuestionPicture(): Promise<cc.Texture2D[]>;
        // 获取角度
        public static getAngelEulerAngles(node: cc.Node): number;
        /** 安全等待异步操作完成 */
        public static async safeAwait<T>(promise: Promise<T>, target: cc.Object): Promise<T>
    }

    export class QTEAssetsManager extends SingleBase {
        getAsset(url: string)

    }
    export class ClickCmpt extends QTEComponent {
        public onLoad();

        public onClick(e: cc.Touch);
    }

    export class QTEBaseState {

        /** 是否操作过 */
        public isOperated: boolean = false;
        /** 题版是否正确 */
        public isCorrect: boolean = false;
    }

    export enum QTE_STATE {
        /** 语音读题 */
        GUIDE,
        GUIDE_AFTER,
        /** 语音读题后动画开始 */
        GUIDE_AFTER_ANMI_BEGIN,
        /** 语音读题后动画结束 */
        GUIDE_AFTER_ANMI_END,
        /** 作答阶段 */
        ANSWERING,

        ANSWERING_AFTER,
        ANSWER_CORRECT,
        ANSWER_CORRECT_END,
        ANSWER_WRONG,
        ANSWER_WRONG_END,
        /** 重置 */
        RESET_QUESTION,
        RESET_QUESTION_END,
    }

    export interface QTEObserve {
        // 当前题目模板
        template: QTETemplate;
        /** 当前状态 */
        curState: QTE_STATE;
    }

    export interface ComponentData {
        id: string;
        type: "sprite" | "label" | "group" | "spine" | "cutShape" | "cocosAni" | "specialComponent" | "formula" | "svgShape" | "optionComponent" | "shape";
        tag: string;
        properties: ComponentProperty;
        hideBeforeAnimation?: boolean;
        extra: Object;
        spineData?: any;
        cocosAniData?: any;
        subComponents?: ComponentData[];
        childComponents?: ComponentData[];
        subType?: string;
        dragable?: boolean;
        editable?: {
            properties: {
                [key: string]: boolean
            };
        }
    }
    export interface QuestionMetaData {
        [key: string]: any;
    }

    export interface ComponentProperty {
        answerDuration?: number;  // 看图说话倒计时
        x: number;
        y: number;
        width: number;
        height: number;
        active: boolean;
        texture?: string;
        zIndex?: number;
        color?: string;
        angle?: number;
        opacity?: number;
        scaleX?: number;
        scaleY?: number;
        offsetX?: number;
        offsetY?: number;
        timeScale?: number;
        animList?: string[];
        animationList?: string[];
        loop?: boolean;
        fillColor?: string;
        lineWidth?: number;
        strokeColor?: string;
        linesData?: LineData[];
        pointsData?: PointData[];
        lineHeight?: any;
        str?: string;
        horizontalAlign?: any;
        svgText?: string;  // svg文本描述

        speakerType?: number,
        audioUrl?: string
        count?: number,
        autoPlay?: boolean,
        notStopPlaying?: boolean,
        countdown?: boolean,
        autoRepeatCount?: number,
        countdownSkin?: number,
        duration?: number
        isNotTouch?: boolean
        [key: string]: any;
    }
    export class QTEBaseMeta {
        // 当前QTE唯一id
        public UUID: string = "invalid qte uuid";
        // 当前bundleName
        public bundleName: string;
        // 状态观察者
        public observe: QTEObserve;
        public constructor(observe: QTEObserve): void;
    }
    export function logCatBoth(type, args): void;
    export function log(...data: any[]): void;

    // 定时器 
    export class TimerUtils {
        /**
         * 添加一个默认定时器
         * @param cb 回调   
         * @param time 间隔
         */
        public static addTimer(cb: (dt) => void, time: number): number;
        /**
         * 添加一个默认定时器
         * @param cb 回调
         * @param time 间隔
         * @param executionType 选择定时器类型
         */
        public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType): number;
        /**
         * 添加一个默认定时器
         * @param cb 回调
         * @param time 间隔
         * @param executionType 选择定时器类型
         * @param isBack 后台后是否记录后台时间。
         */
        public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, isBack: boolean): number;

        public static clearTimer(timerId: number, group = "global");

    }
    export enum ExecutionType {
        once, // 默认是一次的
        cycle,
    }




    export class DisplayObjectManager extends SingleBase {
        /**
         * 这个id是否是选中id组中id的subId
         * @param id 该ID
         * @param arrIds 选中的id组
         */
        public hasOwnedGroup(id: string, arrIds: string[]): boolean;
        /** 获取所有根节点id，不包括组内id，即subId */
        public getRootIds(): string[];



        /**
         * 添加显示对象
         * @param id
         * @param object
         */
        public addDisplayObject(id: string, object: DisplayObject): void;
        /**
         * 移除显示对象组件
         * @param id 组件id
         */
        public removeDisplayObject(id: string): void;

        /**
         * 根据id获取显示对象
         * @param {string} id
         */
        public getDisplayObjectById(id: string): DisplayObject;

        /**
        * 根据tag获取显示对象数组
        * @param {string} tag
        */
        public getDisplayObjectByTag(tag: string): DisplayObject[];
        /**
         * 更新显示对象属性
         * @param id 组件id
         * @param newProperties 属性
         */
        public updateDisplayObject(id: string, newProperties: any, isUndo?: boolean): any;
        /**
         * 更新显示对象坐标属性的增量值
         * @param id 组件id
         * @param pos 坐标的增量
         */
        public updateDisplayObjectDeltaPos(id: string, pos: cc.Vec3): void;
        /**
         * 设置选中状态
         * @param id 选中的id
         * @param multi 是否是多选，默认单选false
         */
        public setSelected(id: string, multi?: boolean): void;
        /**
         * 解除group
         * @param id
         */
        public removeGroupDisplayObject(id: string): string[];

        /** 检查是否是有效节点 */
        private checkIsValidNode(cids: string[]): boolean;

        /**
         * 获取当前选中ID的ParentId
         * @param selectId
         */
        public getDisplayParentID(selectId: string): string;
        /**
         * 获取当前选中ID的组ID
         * @param selectId
         */
        public getDisplayGroupID(selectId: string): string;

        /**
         * 获取组件的设计坐标
         * @param id
         */
        public getWorldPos(id: string): { x: number; y: number };
        /**
         * 获得组件的设计rect
         * @param id 
         */
        public getWorldRect(id: string): { x: number; y: number; width: number; height: number };

        public getRotateEWordRect(id: string): cc.Rect;


        public getCaptureByNode(id: string);


        private resetNodePos(id: string, worldPos: cc.Vec2): { x: number; y: number };

        /**
         * 获取操作点的设计坐标
         * @param id
         * @param pointId
         */
        public getEditPointWorldPos(id: string, pointId: string): cc.Vec2;

        public getNodePos(id: string, newProperties: any): { x: number; y: number };

        private resetEditPointPos(id: string, pointId: string, worldPos: cc.Vec2): cc.Vec3;

        public getEditPointPos(id: string, pId: string, newProperties: any): { x: number; y: number };

        /**
         * 获取节点的真是坐标
         * @param id
         */
        public getNodeRealPos(id: string): { x: number; y: number };

        /**
        * 获取节点的真是坐标
        * @param id
        */
        public getNodeCentEdtiorPos(id: string): { x: number; y: number };
        /**
         * @msg     : 根据所给数据显示标号节点
         * @param    {boolean} isView
         * @return   {*}
         */
        public setAllObjectSignalView(isView: boolean, data: SignalData[]);
    }

    declare let displayObjectManager: DisplayObjectManager;

}
