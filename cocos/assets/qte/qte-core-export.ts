/*
 * @FilePath     : /sdk/assets/qte/qte-core-export.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */

import QTERecordAdapter from "./adapter/QTERecordAdapter";
import MicropComponent from "./assemble/microp/MicropComponent";
import { EngineCallBack } from "./assemble/microp/MicropData";
import RTRComponent from "./assemble/realTimeRecord/RTRComponent";
import { VoiceEngineProp } from "./assemble/realTimeRecord/RTRData";
import RecordComponent from "./assemble/record/RecordComponent";
import { SpeakerComponent } from "./assemble/speaker/scripts/SpeakerComponent";

import { SingleFactory } from "./core/base/SingleBase";
import ValueUtils from "./core/utils/ValueUtils";
import { QTE_RESET_TYPE, QTE_SUBMIT_TYPE } from "./QTEFaceda";

export * from "./decorators";
export * from "./vo/QTEDataVo";
export * from "./vo/QTEEnum";
export * from "./vo/QTETemplateData";
export * from "./qte-api";

// eslint-disable-next-line no-unused-expressions
(window as any).qte || ((window as any).qte = {});
// 单例接口导出
(window as any).qte.single = {};
(window as any).qte.single.instance = SingleFactory.getInstance;
(window as any).qte.single.destory = SingleFactory.destory;
(window as any).qte.single.destoryAll = SingleFactory.destoryAll;
// 新的简短接口导出
(window as any).qte.instance = SingleFactory.getInstance;
(window as any).qte.destoryAll = SingleFactory.destoryAll;
(window as any).qte.destoryBySymbol = SingleFactory.destoryBySymbol;


// 数值操作工具类
(window as any).qte.checkValue = ValueUtils.check;
(window as any).qte.cloneValues = ValueUtils.clone;
(window as any).qte.setOneDecimal = ValueUtils.setOneDecimal;
(window as any).qte.genUUID = ValueUtils.genUUID;

(window as any).qte.QTE_RESET_TYPE = QTE_RESET_TYPE;
(window as any).qte.QTE_SUBMIT_TYPE = QTE_SUBMIT_TYPE;
 
(window as any).qte.EngineCallBack=EngineCallBack;
(window as any).qte.QTERecordAdapter=QTERecordAdapter;
(window as any).qte.MicropComponent=MicropComponent;
(window as any).qte.SpeakerComponent=SpeakerComponent;
(window as any).qte.RecordComponent=RecordComponent;
(window as any).qte.RTRComponent = RTRComponent;
(window as any).qte.VoiceEngineProp = VoiceEngineProp;










