/**
 * QTE主系统逻辑类 QTESystem.ts
 * <AUTHOR>
 * @date 2020-12-16
 * QTE主系统逻辑类，封装主要业务接口，提供给题板业务使用
 */
// 逻辑解耦
import ComponentUtils from "./assemble/componentUtils/ComponentUtils";
import ComponentFactory from "./assemble/core/ComponentFactory";
import QTEBaseMeta from "./base/QTEBaseMeta";
import QTEComponent from "./base/QTEComponent";
import { QTEObserve } from "./base/QTEObserve";
import QTETemplate from "./base/QTETemplate";
import ObserveManager from "./manager/ObserveManager";
import QTEAnimationManager from "./manager/QTEAnimationManager";
import QTEAssetsManager from "./manager/QTEAssetsManager";
import QTEAudioManager from "./manager/QTEAudioManager";
import QTEStateManager from "./manager/QTEStateManager";
import QTEEventHandler_V2 from "./QTEEventHandler";
import QTEStageFactory from "./QTEStageFactory";
import QTEUtils from "./util/QTEUtils";
import { ExtraStageConfig } from "./vo/QTEDataVo";
import { QTE_LOGCAT_TYPE, QTE_STATE, TraceFinishType, TraceName } from "./vo/QTEEnum";

export const QTE_VERSION = "____1.0.0____";

export default class QTESystem extends QTEBaseMeta {
    public get template(): QTETemplate {
        return this.observe.template;
    }

    public constructor(observe?: QTEObserve) {
        super(observe);
        // 关闭多点触控
        cc.macro.ENABLE_MULTI_TOUCH = false;
    }

    // 当前读题音效
    private _guideSoundId: number = -1;

    public initInstance() { }

    /**
     * 初始化整个题板
     * @param observe 当前题板观察者
     * @param template 当前题板对象
     * @param config 题板业务组件配置
     * @param onComplete 加载完成回调
     */
    public start(template: QTETemplate, config?: { [key: string]: { cmpt: new () => QTEComponent; options?: any }[] }, onComplete?: () => void): void {
        let uuid = template.UUID;
        this.UUID = uuid;
        qte.warn("QTESystem_V2----start");
        // 重置QTESystem
        this.reset();
        // 销毁除QTESystem以外所有单例对象
        ComponentUtils.destroyInstance();
        ComponentFactory.destroyInstance();
        template["_qte"] = this;
        if (this.observe) {
            this.observe.template = template;
        }
        // 取 artInfo 里 resourceList
        let resList = QTEUtils.getResourceList(uuid);
        if (!resList) {
            qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTESystem ####--loadResource-isNull--");
            return;
        }
        this.bundleName = QTEUtils.getBundleName(uuid);
        this.subResList(resList);
        // 加载资源列表resourceList
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTESystem ####---loadResource--- begin ");
        let td = Date.now();
        qte.tracePm(TraceName.updatePreloadStart);
        QTEUtils.instance(QTEAssetsManager, uuid).loadResourceList(
            resList,
            () => {
                // TODO:  是否加载完成logcat   
                qte.warn("QTESystem_V2----资源加载完成-RESOURCE_LOADED--->new");
                qte.tracePm(TraceName.updatePreloadEnd, { type: TraceFinishType.success, duration: Date.now() - td });
                // 资源加载完成
                let currentData = QTEUtils.getCurrentData(uuid);
                // 创建场景
                qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTESystem ####---loadResource--- end   createStage  begin");
                let td1 = Date.now();
                qte.tracePm(TraceName.updateCreateNodeStart);
                QTEUtils.instance(QTEStageFactory, this.UUID).createStage(uuid, this, template, currentData, config, () => {
                    template.bolckInput();
                    qte.warn("QTESystem_V2----题目准备完成-QUESTION_READY");
                    qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "QTESystem createStage  end");
                    this.questionStart(template, onComplete);
                    qte.tracePm(TraceName.updateCreateNodeEnd, { type: TraceFinishType.success, duration: Date.now() - td1 });
                });
            },
            uuid
        );
    }
    private subResList(resList: string[]) {
        let subResMap = {};
        if (this.bundleName == "xiaoluPracticeQuestion") {
            let comps = QTEUtils.getComponents(this.UUID);
            let compXiaoluPractice = null;
            for (let i = 0; i < comps.length; i++) {
                if (comps[i].subType == "xiaoluPractice") {
                    compXiaoluPractice = comps[i];
                    break;
                }
            }
            let stuQuestions = compXiaoluPractice?.properties?.stuQuestions
            if (stuQuestions) {
                for (let j = 0; j < stuQuestions.length; j++) {
                    const question = stuQuestions[j];
                    for (let h = 0; h < question.hanziList.length; h++) {
                        const hanzi = question.hanziList[h];
                        const images = hanzi?.lianziSpine?.images;
                        if (images) {
                            for (let k = 0; k < images.length; k++) {
                                subResMap[images[k]] = 1;
                            }
                        }
                        const skeleton = hanzi?.lianziSpine?.skeleton;
                        if (skeleton) {
                            subResMap[skeleton] = 1;
                        }
                        const atlas = hanzi?.lianziSpine?.atlas;
                        if (atlas) {
                            subResMap[atlas] = 1;
                        }

                    }
                }
            }
        }
        for (let i = 0; i < resList.length; i++) {
            if (subResMap[resList[i]]) {
                resList.splice(i, 1);
                i--;
            }
        }
    }

    /** 题目准备完成后开始 */
    private async questionStart(template: QTETemplate, onComplete?: () => void) {
        if(!cc.isValid(template.node))
            return;
        template.unbolckInput();
        qte.log("QTESystem_V2 --- QUESTION_EVENT.QUESTION_START");

        // 初始化激活toucheHandler
        let rootNode = template.node.getChildByName(QTEStageFactory.ROOT_NAME);
        if (rootNode) {
            let touchHandler = rootNode.getComponent(QTEEventHandler_V2);
            if (touchHandler) {
                touchHandler.initTouchHandler();
            }
        }
        QTEUtils.instance(QTEAnimationManager, template.UUID).setLockFun(() => {
            template && template.bolckInput();
        });
        QTEUtils.instance(QTEAnimationManager, template.UUID).setUnLockFun(() => {
            template && template.unbolckInput();
        });
        // 初始化题板底层逻辑
        template.__init();
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_UPDATE, "####----QTESystem--loadPrivateResource---start");
        await template.loadPrivateResource();
        qte.logCatBoth(QTE_LOGCAT_TYPE.QTE_LOG_COMMON, "####----QTESystem--loadPrivateResource---end ");
        // 添加qte 版本信息
        qte.instance(QTEStateManager).updateState(template.UUID, "qte", { qteVersion: QTE_VERSION })
        template.onCreated();
        template.setAnswerBase();
        onComplete && await onComplete();
        await template.loadResetAndSubmit();
        template.onStart();
    }

    public stopGuiderAudio() {
        if (this._guideSoundId != -1) {
            QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.GUIDE_AFTER, this.observe);
            this._guideSoundId = -1;
        }
    }

    // 播放题干引导音频
    public async playGuide() {
        let currentData = QTEUtils.getCurrentData(this.UUID);
        console.log("#-----currentData-->", currentData);
        if (!currentData) {
            return;
        }
        let stageData = currentData.extraStageData as ExtraStageConfig;
        let guideAudio = stageData.guide;
        if (guideAudio) {
            // this.observe.template.bolckInput();
            QTEUtils.instance(QTEAudioManager, this.UUID).playAudio(guideAudio, false, () => {
                // this.observe.template.unbolckInput();
                QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.GUIDE_AFTER, this.observe);
            });
        } else {
            QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.GUIDE_AFTER, this.observe);
        }
    }

    public afterReadingQuestion(observe) {
        // 播放动画
        if (observe.template.hasAnimation("afterReadingQuestion")) {
            observe.template.playAnimation("afterReadingQuestion", () => {
                qte.instance(ObserveManager).gotoState(QTE_STATE.GUIDE_AFTER_ANMI_END, observe);
                this._guideSoundId = -1;
            });
            return;
        }
        qte.instance(ObserveManager).gotoState(QTE_STATE.GUIDE_AFTER_ANMI_END, this.observe);
    }

    /** 重置 */
    public onResetQuestion(): void {
        // 当前读题音效
        this._guideSoundId = -1;
        QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.RESET_QUESTION_END, this.observe);
    }

    /**
     * 暂未实现其他逻辑
     */
    public onResetQuestionEnd(): void { }

    /** 发题 */
    public onStartAnswering(): void {
        if (this.template) {
            this.template.onStartAnswering();
        }
    }

    /** 未答完提交反馈 */
    public answerNotComplete(): void {
        QTEUtils.showToast("请做完再提交~");
    }

    /** 回答正确 */
    public onAnswerCorrect(): void {

        // 播放动画
        if (this.template.hasAnimation("afterSubmitCorrect")) {
            this.template.playAnimation("afterSubmitCorrect", () => {
                qte.instance(ObserveManager).gotoState(QTE_STATE.ANSWER_CORRECT_END, this.observe);
            });
            return;
        }
        QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.ANSWER_CORRECT_END, this.observe);
    }

    public onAnswerCorrectEnd(): void {
        if (this.template) {
            this.template.onAnswerActionEnd();
        }
    }

    /** 回答错误 */
    public onAnswerWrong(): void {
        // 播放动画
        if (this.template.hasAnimation("afterSubmitWrong")) {
            this.template.playAnimation("afterSubmitWrong", () => {
                qte.instance(ObserveManager).gotoState(QTE_STATE.ANSWER_WRONG_END, this.observe);
            });
            return;
        }
        QTEUtils.instance(ObserveManager, this.UUID).gotoState(QTE_STATE.ANSWER_WRONG_END, this.observe);
    }

    public onAnswerWrongEnd(): void {
        if (this.template) {
            this.template.onAnswerActionEnd();
        }
    }

    // 有触摸事件触发时
    private commonTouchEvent() { }
    /** 重置 */
    public reset(): void { }
}
