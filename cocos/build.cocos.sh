#!/bin/bash
###
 # @Author: your name
 # @Date: 2021-05-12 11:16:00
 # @LastEditTime : 2022-12-02 16:23:21
 # @LastEditors  : Please set LastEditors
 # @Description: In User Settings Edit
 # @FilePath     : /cocos/build.cocos.sh
### 
set -o errexit
# env
basepath=$(cd `dirname $0`; pwd)
echo $basepath 

sysOS=''
if [[ "$(uname)" == "Darwin" ]];then
  compiler="/Applications/CocosCreator/Creator/2.4.4/CocosCreator.app/Contents/MacOS/CocosCreator"
  sysOS="Mac"
elif [[ "$(expr substr $(uname -s) 1 10)"=="MINGW32_NT" ]];then
  sysOS="Win"
  compiler="C:\\CocosDashboard_1.0.20\\resources\\.editors\\Creator\\2.4.4\\CocosCreator.exe"
fi
echo $sysOS
# https://boxcdn.zuoyebang.cc/v1/zyb-srmp/29a80b12/

rm -rf $basepath/build
mkdir $basepath/build


${compiler} --path $basepath --build;


echo "build end"