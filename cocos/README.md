# cocos-editor
## 版本功能列表
[版本功能列表排期及后续功能计划](./CHANGELOG.md)

## Editor(cocos)通用模块
- 数据中心Bridge：用于与vuex交互。
- 题板解释器（TemplateInterpreter）：业务代码，用户将题板解释生成cocos场景，以及后续的场景渲染逻辑。
- 事件管理：触摸事件统一接收和派发管理。
- 显示对象管理：显示对象需要特殊设计，支持与事件管理器、碰撞管理器和动画管理器交互。
- 层级管理（LayerManager）可拓展的层级管理设计，可以随时增加新的层级功能
  - 事件接收层：结合`事件管理器`统一事件监听和派发。
  - 表现层：用于做展现。
  - 业务显示层：用来做业务内显示对象的层级调整逻辑。
  - 背景层：固定设置。
- 碰撞管理（CollisionManager）：碰撞逻辑和碰撞展示两部分模块。
- 动画解释器（AnimationClip、AnimationTimeline、AnimationManager）：编辑、解释和预览。关键帧状态恢复。
- 路线绘制模块

## 动画编辑
- 开场动画
- 提示动画
- 解答流程

## 动作分类
- 进入
  - 飞入
  - 出现(直接出现，没有过程)
- 退出
  - 飞出
  - 消失(直接消失，没有过程)
- 强调
  - 放大缩小
  - 旋转
- 移动
  - 直线
  - 曲线（贝塞尔曲线）
## 移动类动画修改
- 动画option数据结构修改（'moduleAnimations/setActionAnimOption'）
 	现有   {id: number, option: [{x: number, y: number}]} 
  修改为 { id: number,
          option: {
            points: [{
              x: number,
              y: number
            }],
            offset: number
          }
        }
- 获取所有动画数据结构（"moduleAnimations/flatActions"）
现有： [E_Action] 
： {fragmentID: [E_Action]}, 能区分出片段
- 针对选中片段
- 选中组件： 
  1. 组件对应动画全选中，展示组件所有动画路径
  2. 属性列表禁用，展示添加动画
  3. 当前组件动画路径设置选中状态  （1 cocos通知vue选中某个cation，2. vue告诉cocos 选中动画数组）
- 选中单个动画
  1. 添加动画变为修该
  2. 选中动画路径设置选中状态
- 选中单个动画节点
  1. 路径全选中取消，设置选中当前动画节点， 
  2. vue选中当前动画片段
  3. 可以设置单个动画节点坐标
- 组件移动：
  1. 锁定： 所有节点保持和组件的相对距离
  2. 解除锁定: 组件移动不影响动画节点位置
- 自定义路径编辑
```js
/** tween动画类型 */
export enum Type {
    FLY_INTO        = 1,        // 飞入
    FLY_OUT         = 2,        // 飞出
    APPEAR          = 3,        // 出现
    DISAPPEAR       = 4,        // 消失
    SCALE           = 5,        // 放大缩小
    ROTATION        = 6,        // 旋转
    MOVE_LINE       = 7,        // 直线移动
    MOVE_BEZIRE     = 8,        // 贝塞尔曲线移动
}
```

```json
// 出现动画：透明度从0到255
{
  "__type__": "cc.AnimationClip",
  "_name": "opacity_actions",
  "_objFlags": 0,
  "_native": "",
  "_duration": 0.5,
  "sample": 60,
  "speed": 1,
  "wrapMode": 1,
  "curveData": {
    "props": {
      "opacity": [
        {
          "frame": 0,
          "value": 0
        },
        {
          "frame": 0.5,
          "value": 255
        }
      ]
    }
  },
  "events": []
}
```

## 动画片段格式
```js
"animation": {
  "actions": [
    {
      "actionId": 1,      // 动作类型
      "componentId": 200, // 组件id
      "moment": 1,        // 时机
      "delay": 200,       // 延时
      "value": {          // 动作参数
        "animType": 2,    // 动画类型： animation、tween
        "anim": {         // 动画数据
            "name": "移动",
            "type": 1,    // 动画类型，移动、旋转等
            "relative": true,   // 相对 绝对 by to
            "data": [{x: 100, y: 200}, {x: 200, y: 300}, {x: 300, y: 400}
          },   
        "speed": 50,      // 速度
        "repeat": 3,      // 重复次数
      },
      "audio": {          // 音效
        "url": "xxx.mp3",
        "moment": 1,      // 播放时机：开始、结束
        "delay": 0
      },     
      "after": {         // 动作结束
        "followType": 1, // 动作结束后：无操作、渐隐消失等
      }         
    }, 
    { },
    { },
  ]
}
```

## tween
```js
// 移动
{
  "type": 1,  // 动画类型，移动、旋转等
  "relative": true,   // 相对 绝对 by to
  "data": [[200, 200], [100, -200], [-300, -200]]
}
// 旋转
{
  "type": 2,  // 动画类型，移动、旋转等
  "relative": false,   // 相对 绝对 by to
  "data": 360
}
// 移动 move
{
  "name": "移动",
  "type": 1,  // 动画类型，移动、旋转等
  "relative": true,   // 相对 绝对 by to
  "data": [[100, 200], [200, 300], [300, 400]]
}
// 旋转 angle
 - 原有：
    {
      "name": "旋转",
      "type": 2,  // 动画类型，移动、旋转等
      "relative": true,   // 相对 绝对 by to
      "option": 360  
    }
  - 修改：
   {
      "name": "旋转",
      "type": 2,  // 动画类型，移动、旋转等
      "relative": true,   // 相对 绝对 by to
      "option": {
        "start": 0, // 开始
        "end": 360  // 结束
      }  
    }
// 飞入 move
{
  "name": "飞入",
  "type": 3,  // 动画类型，移动、旋转等
  "direction": 0, // 0-7 分别对应顺时针的八个方向，上到下、右上到左下、右到左、右下到左上、下到上、左下到右上、左到右、左上到右下
  "relative": false,   // 相对 绝对 by to
  "data": []
}
// 缩放 scale
 - 原有
  {
    "name": "缩放",
    "relative": true,
    "type": 5,
    "option": 0.5
  }
- 修改
  {
    "name": "缩放",
    "relative": true,
    "type": 5,
    "option": {
      "start": 1, // 开始
      "end": 0    // 结束
    }
  }
// 闪烁 Opacity
 - 原有
  {
    "name": "闪烁",
    "relative": true,
    "type": 14,
    "option": null
  }
- 修改
  {
    "name": "闪烁",
    "relative": true,
    "type": 14,
    "option": {
      "start": 0, // 开始
      "end": 255    // 结束
    }
  }
```

## 动画导出格式
```js
[
  {
    time: 0,        // 时间点 s
    componentId: 9  // 组件id
    type: "tween",  // 动画类型： tween或者animation
    data: [         // 缓动动画数据，数组每个元素直接作为tween的参数传入即可，不需处理。 
      {type: "to", time: 2.2, props: {position: cc.v2(100, 100)}},
      {type: "by", time: 2, props: {angle: 360}, opts: "sineOutIn"},
    ]
  }, {
    time: 0,        // 时间点 s
    componentId: 9  // 组件id
    type: "spine",  // 动画类型： spine
    data: {
      animation:"",      // 动画名称
      timeScale: 2, // 速率
      loop: false,  // 循环 写死false
      duration: 1,  // 持续时间
      after: {      // 结束事件
        loop: true, // 循环
        timeScale:1, //速率
        animationList: ["", ""] // 动画列表 默认普通列表动画，可编辑
      }
    }
  },
  {
    time: 2,      
    type: "audio",  
    data: "url"     
  },
  {
    time: 4.2,        // 时间点 s
    componentId: 7  // 组件id
    type: "anim",   // 动画类型： tween或者animation
    data: {
      "__type__": "cc.AnimationClip",
      "_name": "scale",
      "_objFlags": 0,
      "_native": "",
      "_duration": 0.5,
      "sample": 60,
      "speed": 1,
      "wrapMode": 1,
      "curveData": {
        "props": {
          "opacity": [
            {
              "frame": 0,
              "value": 0
            },
            {
              "frame": 0.5,
              "value": 255
            }
          ]
        }
      },
      "events": []
    }        
  },
  {
    time: 4.7,      // 时间点 s
    componentId: 9  // 组件id
    type: "tween",  // 动画类型： tween或者animation
    data: [         // 缓动动画数据，数组每个元素直接作为tween的参数传入即可，不需处理。 
      {type: "to", time: 1, props: {position: cc.v2(100, 100)}},
      {type: "to", time: 1, props: {position: cc.v2(400, 100)}},
      {type: "to", time: 1, props: {position: cc.v2(-100, 300)}},
    ]
  }
]
```

## 其他
- 资源远程加载spine需特殊处理:spine资源名称，纹理列表，动画名称
``` json
{
  "properties": {
      "active": true,
      "angle": 0,
      "animationList": ["idle"], // 动画名称列表
      "height": 182.4,
      "loop": true,              // 循环
      "scaleX": 1,               // 缩放
      "scaleY": 1,
      "timeScale": 1,            // 速率
      "width": 258.1,
      "x": 0,
      "y": 0
  },
  "spineData": {
    "atlas": "xxx.atlas",
    "skeleton": "xxx.json",
    "images": ["xxx.png"]
  }
}
```

## 题组生命周期
详见wiki：https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=224966871
## 题组数据结构设计
```json
{
  "bundleName": "templateGroupQuestion",
  "bundleList": ["url1", "url2"],
  "animations": {},
  "animationsForClient": {},
  "components": [],
  "extraDataMap": {},
  "extraStageData": {
    "groupList": {
      "1": {
        "tid": 1,
        "transform": {
          "type": "move_left", // 切换方式：move_left、move_right、fade、page
          "moment": 0,  // 0：前一个切换动画完成，后一个再开始切换； 1：前后两个同时开始切换
          "speed": 500  // 单位毫秒
        }
      }, 
      "2": {
        "tid": 2,
        "transform": {
          "type": "fade", // 切换方式：move_left、move_right、fade、page
          "moment": 0,  // 0：前一个切换动画完成，后一个再开始切换； 1：前后两个同时开始切换
          "speed": 500  // 单位毫秒
        }
      }
    },
  },
  "resourceList":[],
  "stageData":{},
  "template":{},
  "thumbnail":"https://yaya.cdnjtzy.com/thumbnail-e4d0b62479e7131ec3755c0eea959bbf.jpeg"
}
```