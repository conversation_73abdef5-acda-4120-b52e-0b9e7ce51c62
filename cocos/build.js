"use strict";

let fs = require('fs');
let exec = require("child_process").exec;

/************************ 命令行参数变量列表 **************************/
// 0.工程名称
let GITLAB_PROJECT_NAME;
// 1.工程git地址
let GIT_SSH_URL;
// 2.默认分支
let DEFAULT_BRANCH;
// 3.commit信息列表
let COMMITS;
// 4.Jenkins构建id
let BUILD_ID;
/************************ 命令行参数变量列表 **************************/


// ------------ 脚本入口 -------------
let arguments = process.argv.splice(2);
console.log('所传递的参数是：', arguments);

// 解析命令行参数列表
// 工程名称
GITLAB_PROJECT_NAME = arguments[0];
// 工程git地址
GIT_SSH_URL = arguments[1];
// 默认分支
DEFAULT_BRANCH = arguments[2];
// commit信息列表
COMMITS = arguments[3];
// Jenkins构建id
BUILD_ID = arguments[4];


// 判断工程目录是否存在，如果不存在则检出工程


// 检查工程目录下的build_cocos.js文件是否存在，并调用
// 如果不存在则调用默认的构建逻辑：使用2.4.3默认引擎构建cocos目录下文件

// push构建结果 commit -m "[auto-build][skip ci]${BUILD_ID}"




// let test = "test gitlab-runner";
// console.log("log:" + test);

// let cmd = 
// ```

// ```;
// exec(cmd,{
//     maxBuffer: 1024 * 2000
// }, function(err, stdout, stderr) {
//     if (err) {
//         console.log(err);
//         reject(err);
//     } else if (stderr.lenght > 0) {
//         reject(new Error(stderr.toString()));
//     } else {
//         console.log(stdout);
//         resolve();
//     }
// });

// let path = "./README.md";
// let data = fs.readFileSync(path);

// let str = data.toString();
// // str += "aa";

// fs.writeFileSync(path, str);