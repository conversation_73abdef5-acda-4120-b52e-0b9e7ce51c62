<div class="container">
    <div class="top">
        <div  v-if="isCanUpdte"  @click="downUpdate" >
            <i class="icon-record rred"> </i>
            <a>点击更新</a>
        </div>
        <span class="flex-1"></span>
        <ui-button class="red" :disabled="!isChange" style=" margin-right: 10px;" @confirm="cnacleOperator">撤销
        </ui-button>
        <ui-button class="green" :disabled="!isChange" style=" margin-right: 20px;" @confirm="apply">应用</ui-button>
    </div>
    <table id="table-ex" v-show="!isSetting">
        <thead>
            <tr>
                <th scope="col">插件名称</th>
                <th scope="col">插件版本</th>
                <th scope="col">描述 </th>
                <th scope="col">是否使用 </th>
            </tr>

        </thead>
        <tbody>
            <tr v-show="!isSetting" v-for="item in plugConfig.plugList ">
                <td>{{item.name}}</td>
                <td>{{item.version}}</td>
                <td>{{item.description}}</td>
                <td>
                    <!-- <ui-button v-show="item.isUsed&&(item.name ==='find-asset'||item.name ==='tinypng')" class="tiny"
                        @confirm="setUsed(item.name)" tooltip="">打包已开启
                    </ui-button>
                    <ui-button v-show="!item.isUsed&&(item.name ==='find-asset'||item.name ==='tinypng')" class="tiny"
                        @confirm="setUsed(item.name)" tooltip="打包前不不检查">打包已禁用
                    </ui-button>
                    <ui-button class="red" :disabled="item.isUsed" class="tiny" @confirm="cnacleOperator">删除</ui-button>
                    <ui-button class="green" :disabled="!item.isUsed" class="tiny" @confirm="apply">添加</ui-button> -->
                    <ui-checkbox :checked="updateUsedList[item.name]===true" @confirm="setNeedMoudle(item)">
                    </ui-checkbox>
                </td>
            </tr>

        </tbody>
    </table>
    <ui-loader v-if="loading"></ui-loader>
    <div class="loginQues">
        疑惑解答?:<EMAIL>
    </div>
    <div class="settingButton">
        <!-- <span>设置</span> -->
        <i class="icon-record"> </i>
        <a> 当前版本:{{plugConfig.version}}</a>
    </div>

</div>