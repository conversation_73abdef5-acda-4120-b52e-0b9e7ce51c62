/* eslint-disable no-alert */
/* eslint-disable global-require */
/* eslint-disable no-negated-condition */
/* eslint-disable max-lines */
/* eslint-disable require-unicode-regexp */

"use strict";

let fs = require("fire-fs");
let path = require("fire-path");
let { plugManage } = require(`${Editor.Project.path}/packages/plugmanage/panel/plugManage`);

Editor.Panel.extend({
    style: fs.readFileSync(Editor.url("packages://plugmanage/panel/index.css", "utf8")),
    template: fs.readFileSync(Editor.url("packages://plugmanage/panel/index.html", "utf8")),
    // eslint-disable-next-line max-lines-per-function
    ready () {
        window.plugin = new window.Vue({
            el: this.shadowRoot,
            data () {
                return {
                    loading: false,
                    plugConfig: {
                        url: "",
                        plugList: []
                    },
                    usedList: {},
                    updateUsedList: {},
                    isLogin: false,
                    isSetting: false,
                    showPlugList: [],
                    userInputPassword: "",
                    isCanUpdte: false,
                    isChange: false
                };


            },
            created () {


                /*
                 * This.plugConfig = plugManage.plugConfig;
                 * this.readPassWorld();
                 * if (this.plugConfig.userPassword === "" || this.plugConfig.userName === "") {
                 *     this.isLogin = true;
                 *     this.isSetting = true;
                 * } else {
                 *     this.readPlugList();
                 * }
                 */
                this.getStart();
            },
            watch: {},
            methods: {
                getStart () {
                    plugManage.getStart((data) => {
                        let temp = JSON.parse(data);
                        window.ipAdress = temp.ip;
                        this.readPlugConfig();
                    });
                },
                readPlugConfig () {
                    let tempData = plugManage.init();
                    if (tempData.url === "") {
                        plugManage.getVersion((data) => {
                            let temp = JSON.parse(data);
                            tempData = temp;
                            plugManage.writeDataToPath(plugManage.configUrl, JSON.stringify(tempData));
                            let tempArray = plugManage.readPageFileList(true);
                            for (let i = 0; i < tempData.plugList.length; i++) {
                                let name1 = tempData.plugList[i].name;
                                if (tempArray[name1] === true) {
                                    this.usedList[name1] = true;
                                } else {
                                    this.usedList[name1] = false;
                                }
                                if (name1 === "plugmanage") {
                                    tempData.plugList.splice(i, 1);
                                    i--;
                                }
                            }
                            this.updateUsedList = JSON.parse(JSON.stringify(this.usedList));
                            this.plugConfig = tempData;
                        });
                    } else {

                        plugManage.getVersion((data) => {
                            let temp = JSON.parse(data);
                            Editor.log(tempData.version);
                            if (tempData.version !== temp.version) {
                                this.isCanUpdte = true;
                            } else {
                                this.isCanUpdte = false;
                            }
                        });

                        let tempArray = plugManage.readPageFileList(true);
                        for (let i = 0; i < tempData.plugList.length; i++) {
                            let name1 = tempData.plugList[i].name;
                            if (tempArray[name1] === true) {
                                this.usedList[name1] = true;
                            } else {
                                this.usedList[name1] = false;
                            }
                            if (name1 === "plugmanage") {
                                tempData.plugList.splice(i, 1);
                                i--;
                            }
                        }
                        this.updateUsedList = JSON.parse(JSON.stringify(this.usedList));
                        this.plugConfig = tempData;
                    }


                },
                downUpdate () {
                    this.loading = true;
                    plugManage.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                    plugManage.getVersion((data) => {
                        let temp = JSON.parse(data);
                        let downUrl = window.ipAdress + this.plugConfig.url;
                        plugManage.downLib(downUrl, () => {
                            let n = downUrl.lastIndexOf("/");
                            let fileName = downUrl.substr(n + 1, downUrl.length);
                            let dirIndex = fileName.lastIndexOf(".zip");
                            let dirName = fileName.substr(0, dirIndex);
                            Editor.log(this.usedList);
                            plugManage.unzip(Editor.url(`${Editor.Project.path}/tmp/${fileName}`), () => {
                                for (let key in this.usedList) {
                                    if (this.usedList[key] === true) {
                                        plugManage.copDir(Editor.url(`${Editor.Project.path}/tmp/${dirName}/${key}`), Editor.url(`${Editor.Project.path}/packages/${key}`));
                                    }
                                }
                                plugManage.writeDataToPath(plugManage.configUrl, JSON.stringify(temp));
                                plugManage.copDir(Editor.url(`${Editor.Project.path}/tmp/${dirName}/plugmanage`), Editor.url(`${Editor.Project.path}/packages/plugmanage`));
                                setTimeout(() => {
                                    this.readPlugConfig();
                                    this.loading = false;
                                    this.isSetting = false;
                                    plugManage.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                                    alert("插件库已更新从，请重启动plugManage");
                                }, 1000);
                            });
                        });
                    });
                },
                downLoadPart (choosePart, callBack) {
                    let downUrl = window.ipAdress + this.plugConfig.url;
                    plugManage.downLib(downUrl, () => {
                        let n = downUrl.lastIndexOf("/");
                        let fileName = downUrl.substr(n + 1, downUrl.length);
                        let dirIndex = fileName.lastIndexOf(".zip");
                        let dirName = fileName.substr(0, dirIndex);
                        plugManage.unzip(Editor.url(`${Editor.Project.path}/tmp/${fileName}`), () => {
                            for (let key in choosePart) {
                                plugManage.copDir(Editor.url(`${Editor.Project.path}/tmp/${dirName}/${key}`), Editor.url(`${Editor.Project.path}/packages/${key}`));
                            }
                            setTimeout(() => {
                                // eslint-disable-next-line no-unused-expressions
                                callBack && callBack();
                            }, 1000);

                        });
                    });

                },

                apply () {
                    let addObj = {};
                    let deleteObj = {};
                    for (let key in this.updateUsedList) {
                        if (this.updateUsedList[key] === false && this.updateUsedList[key] !== this.usedList[key]) {
                            deleteObj[key] = true;

                        } else if (this.updateUsedList[key] === true && this.updateUsedList[key] !== this.usedList[key]) {
                            addObj[key] = true;
                        }
                    }
                    this.loading = true;

                    let addLen = Object.keys(addObj).length;
                    let delLen = Object.keys(deleteObj).length;
                    if (delLen > 0) {
                        for (let ak in deleteObj) {
                            plugManage.delPath(Editor.url(`packages://${ak}`));

                        }
                        if (addLen <= 0) {
                            setTimeout(() => {
                                Editor.log("删除");
                                this.usedList = JSON.parse(JSON.stringify(this.updateUsedList));
                                this.updateChangeState();
                                this.plugConfig = JSON.parse(JSON.stringify(this.plugConfig));
                                plugManage.writeDataToPath(plugManage.configUrl, JSON.stringify(this.plugConfig));
                                this.loading = false;
                            }, 500);

                        }

                    }

                    if (addLen > 0) {
                        plugManage.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                        this.downLoadPart(addObj, () => {
                            this.usedList = JSON.parse(JSON.stringify(this.updateUsedList));
                            this.updateChangeState();
                            this.plugConfig = JSON.parse(JSON.stringify(this.plugConfig));
                            plugManage.writeDataToPath(plugManage.configUrl, JSON.stringify(this.plugConfig));
                            this.loading = false;
                            plugManage.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                        });
                    }


                },
                // 撤销；
                cnacleOperator () {
                    this.updateUsedList = JSON.parse(JSON.stringify(this.usedList));
                    this.updateChangeState();
                    this.plugConfig = JSON.parse(JSON.stringify(this.plugConfig));

                },
                setNeedMoudle (data) {
                    this.updateUsedList[data.name] = !this.updateUsedList[data.name];
                    this.updateChangeState();
                },
                updateChangeState () {
                    let ischange = false;
                    for (let key in this.updateUsedList) {
                        if (this.updateUsedList[key] !== this.usedList[key]) {
                            ischange = true;
                            break;
                        }
                    }
                    this.isChange = ischange;
                },


                updateSetting () {
                    if (this.isSetting === false) {
                        this.isSetting = true;
                    }

                },
                updateSettingClose () {
                    if (this.isSetting === true) {
                        this.isSetting = false;
                    }

                }

            }
        });
    },
    messages: {
        // eslint-disable-next-line no-empty-function
        "scene:ready" () { },
        getmsg () {
            Editor.log("收到消息了----");
        },
        // eslint-disable-next-line no-empty-function
        "scene:enter-prefab-edit-mode" () { }
    }
});
