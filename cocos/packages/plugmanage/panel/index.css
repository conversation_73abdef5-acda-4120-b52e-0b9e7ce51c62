.container {
    margin: 10px;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.top
{
display: flex;
flex: 0 0 auto;
flex-direction: row;
align-items: center;
padding-bottom: 2px;
margin: 5px 40px 20px 0px;
border-bottom: 1px solid rgb(102, 102, 102);
height: 35px;
overflow: hidden;
}
.title
{
    font-size:28px;
    margin-bottom: 40px;
}
.setting
{
    display: flex;
    flex-direction: column;
    position: absolute;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}
.rred
{
   color: red;
}
.settingBtClose
{
    position: absolute ;
    top: 5;
    right: 5;
    width: 20px;
    height: 20px;
    border-style: solid;
    border-radius: 50%;
    border-width: 1px;
    display: flex;
    justify-content: center;
    align-items: center;

}
.settingButton
{
    position: absolute ;
    bottom: 5;
    left: 5;
    display: flex;
    justify-content: center;
    align-items: center;

}
.changeAcc
{
    margin-bottom: 150px;
}
.inputModel
{
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-items: center;
    margin-top: 30px;
}
.checkUpdate1
{
    margin-top: 10px;
    width: 50px;
}
.updateVersionInfo
{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-items: center;
    position: absolute;
    bottom: 0;
}
.loginInfo
{
    margin-bottom: 20px;
}
.versionInffo
{
    margin-top: 10px;
    margin-left: 10px;
    max-width: 280px;
    white-space:nowrap; 
    overflow:hidden; 
    text-overflow:ellipsis;
}
.loginSetting
{
    display: flex;
     flex-direction: column;
     width: 300px;
     justify-content: center;
     align-items: center;
     height: 500px;
     border-style:solid;
     border-width:1px;
     border-radius: 3%;
     position: relative;
}
.loginQues{
  position: absolute;
  right: 0;
  bottom: 3px;
}
.loginSettingInput
{
    margin-left: 20px;
    width: 100px;

}
.loginSettingLogin
{
    margin-top: 20px;
    width: 100px;
    margin-bottom: 100px;
}



.changeAccount
{
    margin-left: 10px;

}
.checkUpdate
{
    position: absolute;
    right: 20px;
    bottom: 6px;
}
.versionInfo
{
    position: absolute;
    left:  6px;
    bottom: 6px;
}
#table-ex {
    font-size: 13px;
    margin: 22px;
    text-align: left;
    border-collapse: collapse;
    text-align: center;
    margin-bottom: 50px;
}

#table-ex th {
    font-size: 14px;
    font-weight: normal;
    padding: 8px;
    border-bottom: 1px solid #666666;
}

#table-ex td {
    padding: 8px;
    border-bottom: 1px solid #666666;
    border-top: 1px solid transparent;
}

#table-ex tbody tr:hover td {
    color: #000000;
    background: #F5F7FA;
}