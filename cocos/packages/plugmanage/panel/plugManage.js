/* eslint-disable no-loop-func */
/* eslint-disable valid-jsdoc */
/* eslint-disable no-negated-condition */
/* eslint-disable no-alert */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-ternary */
/* eslint-disable prefer-reflect */
/* eslint-disable no-undefined */
/* eslint-disable prefer-named-capture-group */
/* eslint-disable init-declarations */
/* eslint-disable require-unicode-regexp */
/* eslint-disable func-style */
/* eslint-disable require-jsdoc */


/* eslint-disable max-lines */
const fs = require("fs");
const http = require("http");
const https = require("https");
const path = require("path");
const request = require("request");
let compressing = require("compressing");
let plugManage = {
    plugConfig: {
        url: "",
        plugList: [],
        usedList: {}
    },
    configUrl: Editor.url("packages://plugmanage/.plugConfig.json"),
    encode (str) {
        // 对字符串进行编码
        let encode = encodeURI(str);
        // 对编码的字符串转化base64
        let base64 = btoa(encode);
        return base64;
    },
    decode (base64) {
        // 对base64转编码
        let decode = atob(base64);
        // 编码转字符串
        let str = decodeURI(decode);
        return str;
    },
    getStart (callBack) {
        let req = https.request(`https://yaya-yuwen.zuoyebang.com/cocos/ipCfg.json?v=${Math.ceil(Date.now() / 10000)}`, (res) => {
            let body = "";
            res.on("data", (data) => {
                body += data;
            });
            res.on("end", () => {
                if (callBack) {
                    callBack(body);
                }
            });
        });
        req.on("error", (e) => {
            Editor.error(e);
        });
        req.end();
    },
    getVersion (callBack) {
        let req = http.request(`${window.ipAdress}/hfs/cocos-plug/plug-config.json?v=${Math.ceil(Date.now() / 10000)}`, (res) => {
            let body = "";
            res.on("data", (data) => {
                body += data;
            });
            res.on("end", () => {
                if (callBack) {
                    callBack(body);
                }

            });
        });
        req.on("error", (e) => {
            Editor.error(e);
        });
        req.end();
    },
    downLib (serveUrl, callBack) {
        plugManage.checkDstPath(Editor.url(`${Editor.Project.path}/tmp`));
        // Let url = `https://yaya-yuwen.zuoyebang.com/cocos/common-module/map/${fileName}`;
        let url = serveUrl;
        Editor.log(url);
        let n = serveUrl.lastIndexOf("/");
        let fileName = serveUrl.substr(n + 1, serveUrl.length);
        let stream = fs.createWriteStream(path.join(Editor.url(`${Editor.Project.path}/tmp`), fileName));
        request(url).
            pipe(stream).
            on("close", (_err) => {
                Editor.log(`文件[${serveUrl}]下载完毕`);
                callBack && callBack();
            });
    },
    unzip (fileName, callBack) {
        Editor.log("正在解压缩", fileName);
        compressing.zip.uncompress(fileName, Editor.url(`${Editor.Project.path}/tmp`)).
            then(() => {
                Editor.log("解压完成");
                callBack && callBack();
            }).
            catch((err) => {
                Editor.error(err);
            });
    },
    init () {
        let data = plugManage.readDataFromPath(plugManage.configUrl);
        if (data === "") {
            plugManage.writeDataToPath(plugManage.configUrl, JSON.stringify(plugManage.plugConfig));
        } else {
            plugManage.plugConfig = JSON.parse(data);
        }
        return plugManage.plugConfig;
    },


    // 写入文件 接口
    writeDataToPath (dataPath, data) {
        fs.writeFile(dataPath, data, "utf8", (err) => {
            if (err) {
                Editor.log(err);
                throw err;
            }
            Editor.log("done");
        });
    },
    // 读取文件数据 接口
    readDataFromPath (readPath) {
        let data = "";
        let logPath = readPath;
        if (fs.existsSync(logPath)) {
            data = String(fs.readFileSync(logPath));
            data = data.replace(/\s+/g, "");
        }
        return data;
    },
    // 删除 接口
    delPath (path1) {
        if (!fs.existsSync(path1)) {
            return "路径不存在";
        }
        let info = fs.statSync(path1);
        if (info.isDirectory()) {
            let data = fs.readdirSync(path1);
            if (data.length > 0) {
                for (let i = 0; i < data.length; i++) {
                    plugManage.delPath(`${path1}/${data[i]}`);
                    if (i === data.length - 1) {
                        plugManage.delPath(`${path1}`);
                    }
                }
            } else {
                fs.rmdirSync(path1);
            }
        } else if (info.isFile()) {
            fs.unlinkSync(path1);
        }
    },
    // 拷贝 目录接口
    copy (src, dst) {
        let paths = fs.readdirSync(src);
        for (let i = 0; i < paths.length; i++) {
            if (paths[i].indexOf(".meta") !== -1) {
                continue;
            }
            let _src = `${src}/${paths[i]}`;
            let _dst = `${dst}/${paths[i]}`;
            fs.stat(_src, (err, stats) => {
                if (err) {
                    throw err;
                }
                if (stats.isFile()) {
                    let readable = fs.createReadStream(_src);
                    let writable = fs.createWriteStream(_dst);
                    readable.pipe(writable);
                } else if (stats.isDirectory()) {
                    plugManage.copDir(_src, _dst);
                }
            });
        }
    },
    // 拷贝 目录接口
    checkDstPath (dst) {
        if (fs.existsSync(dst)) {
            return true;
        }
        if (plugManage.checkDstPath(path.dirname(dst))) {
            fs.mkdirSync(dst);
            return true;
        }

    },
    copDir (src, dst) {
        plugManage.checkDstPath(dst);
        plugManage.copy(src, dst);
    },
    //  更新当前目录配置
    readPageFileList (isShowPage) {
        let tempArray = {};
        let data = fs.readdirSync(`${Editor.Project.path}/packages`);
        for (let i = 0; i < data.length; i++) {
            let pageData = plugManage.readDataFromPath(`${Editor.Project.path}/packages/${data[i]}/package.json`);
            if (pageData !== "") {
                let tempdata = JSON.parse(pageData);
                if (isShowPage) {
                    if (tempdata.name !== "plugmanage") {
                        // TempArray.push(tempdata);
                        tempArray[tempdata.name] = true;
                    }

                } else {
                    // TempArray.push(tempdata);
                    tempArray[tempdata.name] = true;
                }

            }
        }
        return tempArray;
    }
};
module.exports = { plugManage };
