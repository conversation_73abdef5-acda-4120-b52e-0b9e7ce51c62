/*
 * @Author: your name
 * @Date: 2021-02-19 19:57:55
 * @LastEditTime: 2021-03-05 16:53:06
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /yaya-question-editor/cocos/packages/auto_build/main.js
 */

let utils = require("./utils");
let fs = require('fs');
let path = require("path");
let os = require("os");
'use strict';

function onAfterBuildFinish(options, callback) {
  let mainJsPath = path.resolve(options.dest , 'index.html');
  var script = fs.readFileSync(mainJsPath, 'utf8');
  try {
    let mainjs = script.match(/main.[^\n\r]*.js/)[0];
    let setting = script.match(/src\/settings.[^\n\r]*.js/)[0];
    let engineJs = script.match(/cocos2d-js-min.[^\n\r]*.js/);
    let cocos2djs = "";
    if (engineJs && engineJs.length >= 1) {
      cocos2djs = engineJs[0];
    } else {
      cocos2djs = script.match(/cocos2d-js.[^\n\r]*?.js/)[0];
      Editor.log("==",cocos2djs);
    }
    Editor.log("处理md5文件...");
    let str = `["${mainjs}", "${setting}", "${cocos2djs}"]`;
    Editor.log("1文件列表：", str);
    fs.writeFileSync(path.resolve(options.dest, 'md5.json'), str);
  } catch (e) {
    let str = `["main.js", "src/settings.js", "cocos2d-js.js"]`;
    Editor.log("2文件列表：", str);
    fs.writeFileSync(path.resolve(options.dest, 'md5.json'), str);
  }
  // copy指定文件到目录
  let rPath = Editor.url("packages://auto_build/");
  // rPath = rPath.replace(path.sep, "/");
  // if (os.type() == 'Windows_NT') {
  //   rPath = rPath.replace("cocos\\packages\\auto_build\\", "");
  // } else {
  //   rPath = rPath.replace("cocos/packages/auto_build/", "");
  // }
  rPath=path.resolve(Editor.Project.path,"..");
  // utils.deleteDirs(path.resolve(rPath , "public","assets"));
  utils.deleteDirs(path.resolve(rPath ,"public","cocos"));
  // utils.copyDirs(path.resolve(rPath , "cocos","build","web-mobile","assets"),path.resolve(rPath ,  "public","assets"));
  utils.copyDirs(path.resolve(rPath , "cocos","build","web-mobile"), path.resolve(rPath ,  "public","cocos"));
  Editor.log('资源已copy到publish目录');
  
  // 继续执行打包流程
  callback();
}


module.exports = {
  load() {
    // execute when package loaded
    Editor.Builder.on('build-finished', onAfterBuildFinish);
  },

  unload() {
    // execute when package unloaded
    Editor.Builder.off('build-finished', onAfterBuildFinish);
  },

  // register your ipc messages here
  messages: {
    'open'() {
      // open entry panel registered in package.json
      // Editor.Panel.open('auto_build');
      Editor.log('资源已copy到publish目录了');
      // let rPath = Editor.url("packages://auto_build/");
      // rPath = rPath.replace("cocos/packages/auto_build/", "");
      //let rPath=path.resolve(Editor.Editor.Project.path,"..");
    }
  },
};