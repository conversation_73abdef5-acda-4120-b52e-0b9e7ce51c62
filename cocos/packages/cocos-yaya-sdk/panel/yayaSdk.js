/* eslint-disable max-depth */
/* eslint-disable valid-jsdoc */
/* eslint-disable no-negated-condition */
/* eslint-disable no-alert */
/* eslint-disable no-unused-expressions */
/* eslint-disable no-ternary */
/* eslint-disable prefer-reflect */
/* eslint-disable no-undefined */
/* eslint-disable prefer-named-capture-group */
/* eslint-disable init-declarations */
/* eslint-disable require-unicode-regexp */
/* eslint-disable func-style */
/* eslint-disable require-jsdoc */
/* eslint-disable max-lines */
const fs = require("fs");
const http = require("http");
const https = require("https");
const request = require("request");
let path = require("path");
let compressing = require("compressing");
const md5File = require("md5-file");
let yayaSdk = {
    getStart (callBack) {
        let req = https.request(`https://yaya-yuwen.zuoyebang.com/cocos/ipCfg.json?v=${Math.ceil(Date.now() / 10000)}`, (res) => {
            let body = "";
            res.on("data", (data) => {
                body += data;
            });
            res.on("end", () => {
                if (callBack) {
                    callBack(body);
                }
            });
        });
        req.on("error", (e) => {
            Editor.error(e);
        });
        req.end();
    },
    getVersion (callBack) {
        let req = http.request(`${window.ipAdress}/hfs/cocos-yaya-sdk/sdk-config.json?v=${Math.ceil(Date.now() / 10000)}`, (res) => {
            let body = "";
            res.on("data", (data) => {
                body += data;
            });

            res.on("end", () => {
                if (callBack) {
                    callBack(body);
                }
            });
        });
        req.on("error", (e) => {
            Editor.error(e);
        });
        req.end();
    },
    downLib (serveUrl, callBack) {
        yayaSdk.checkDstPath(Editor.url(`${Editor.Project.path}/tmp`));
        // Let url = `https://yaya-yuwen.zuoyebang.com/cocos/common-module/map/${fileName}`;
        let url = serveUrl;
        Editor.log(url);
        let n = serveUrl.lastIndexOf("/");
       let fileName = serveUrl.substr(n + 1, serveUrl.length);
        let stream = fs.createWriteStream(path.join(Editor.url(`${Editor.Project.path}/tmp`), fileName));
        request(url).
            pipe(stream).
            on("close", (_err) => {
                Editor.log(`文件[${serveUrl}]下载完毕`);
                callBack && callBack();
            });
    },
    unzip (fileName, callBack) {
        Editor.log("正在解压缩", fileName);
        compressing.zip.uncompress(fileName, Editor.url(`${Editor.Project.path}/tmp`)).
            then(() => {
                Editor.log("解压完成");
                callBack && callBack();
            }).
            catch((err) => {
                Editor.error(err);
            });
    },
    zipLibs (url) {
        if (!fs.existsSync(url)) {
            alert("压缩目录不存在哦");

        }
        let data = yayaSdk.readDataFromPath(Editor.url("packages://cocos-yaya-sdk/sdk-config.json"));
        let tempData = JSON.parse(data);
        let targeName = `${url}.zip`;
        let rootpath = path.resolve(url, "..");
        compressing.zip.compressDir(url, targeName).
            then(() => {
                const hash = md5File.sync(targeName);
                let fileName = `${rootpath}/libs_${hash}.zip`;
                fs.renameSync(targeName, fileName);
                tempData.url = `libs_${hash}.zip`;
                yayaSdk.writeDataToPath(`${rootpath}/sdk-config.json`, JSON.stringify(tempData));
                yayaSdk.delPath(url);
                Editor.log("success");
            }).
            catch((err) => {
                Editor.error(err);
            });
    },

    getPathFromObj (obj, targetObj) {
        let ppath = "";
        for (let i = 0; i < obj.subMoudle.length; i++) {
            if (ppath !== "") {
                break;
            }
            if (obj.subMoudle[i] === targetObj) {
                ppath = obj.subMoudle[i].name;
                break;
            }
            let ooj = obj.subMoudle[i].subMoudle;
            if (ooj) {
                for (let j = 0; j < ooj.length; j++) {
                    if (ooj[j] === targetObj) {
                        ppath = `${obj.subMoudle[i].name}/${ooj[j].name}`;
                        break;
                    }
                }
            }
        }
        return ppath;
    },
    isArray (o) {
        return Object.prototype.toString.call(o) === "[object Array]";
    },

    // 拷贝 目录接口
    copy (src, dst) {
        let paths = fs.readdirSync(src);
        for (let i = 0; i < paths.length; i++) {
            if (paths[i].indexOf(".meta") !== -1) {
                continue;
            }
            let _src = `${src}/${paths[i]}`;
            let _dst = `${dst}/${paths[i]}`;
            fs.stat(_src, (err, stats) => {
                if (err) {
                    throw err;
                }
                if (stats.isFile()) {
                    let readable = fs.createReadStream(_src);
                    let writable = fs.createWriteStream(_dst);
                    readable.pipe(writable);
                } else if (stats.isDirectory()) {
                    yayaSdk.copDir(_src, _dst);
                }
            });
        }
    },
    // 拷贝 目录接口
    checkDstPath (dst) {
        if (fs.existsSync(dst)) {
            return true;
        }
        if (yayaSdk.checkDstPath(path.dirname(dst))) {
            fs.mkdirSync(dst);
            return true;
        }

    },
    copDir (src, dst) {
        yayaSdk.checkDstPath(dst);
        yayaSdk.copy(src, dst);
    },
    // 写入文件 接口
    writeDataToPath (dataPath, data) {
        fs.writeFile(dataPath, data, "utf8", (err) => {
            if (err) {
                Editor.log(err);
                throw err;
            }
            Editor.log("done");
        });
    },
    // 读取文件数据 接口
    readDataFromPath (readPath) {
        let data = "";
        let logPath = readPath;
        if (fs.existsSync(logPath)) {
            data = String(fs.readFileSync(logPath));
            data = data.replace(/\s+/g, "");
        }
        return data;
    },
    // 删除 接口
    delPath (path1) {
        if (!fs.existsSync(path1)) {
            return "路径不存在";
        }
        let info = fs.statSync(path1);
        if (info.isDirectory()) {
            let data = fs.readdirSync(path1);
            if (data.length > 0) {
                for (let i = 0; i < data.length; i++) {
                    yayaSdk.delPath(`${path1}/${data[i]}`);
                    if (i === data.length - 1) {
                        yayaSdk.delPath(`${path1}`);
                    }
                }
            } else {
                fs.rmdirSync(path1);
            }
        } else if (info.isFile()) {
            fs.unlinkSync(path1);
        }
    }
};
module.exports = { yayaSdk };
