/* eslint-disable default-case */
/* eslint-disable prefer-reflect */
/* eslint-disable no-dupe-else-if */
/* eslint-disable max-lines-per-function */
/* eslint-disable no-alert */
/* eslint-disable global-require */
/* eslint-disable no-negated-condition */
/* eslint-disable max-lines */
/* eslint-disable require-unicode-regexp */

"use strict";

let fs = require("fire-fs");
let path = require("fire-path");
let { yayaSdk } = require(`${Editor.Project.path}/packages/cocos-yaya-sdk/panel/yayaSdk`);
Editor.Panel.extend({
    style: fs.readFileSync(Editor.url("packages://cocos-yaya-sdk/panel/index.css", "utf8")),
    template: fs.readFileSync(Editor.url("packages://cocos-yaya-sdk/panel/index.html", "utf8")),
    // eslint-disable-next-line max-lines-per-function
    ready () {
        window.plugin = new window.Vue({
            el: this.shadowRoot,
            data () {
                return {
                    loading: false,
                    isLogin: false,
                    isSetting: false,
                    cusComModConfig: {
                        pathData: {},
                        moduleData: {
                            "root": {
                                "name": "cocos-yaya-sdk",
                                "canEdit": false,
                                "subMoudle": []
                            },
                            url: ""
                        },
                        localPath: ""
                    },
                    updateModuleData: {},
                    isCanUpdte: false,
                    isChange: false,
                    updatePathData: {}

                };

            },
            created () {

                this.getStart();
            },
            watch: {},
            methods: {
                getStart () {
                    yayaSdk.getStart((data) => {
                        let temp = JSON.parse(data);
                        window.ipAdress = temp.ip;
                        let cusComdata = yayaSdk.readDataFromPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`);
                        if (cusComdata === "") {
                            yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                        } else {
                            this.cusComModConfig = JSON.parse(cusComdata);
                        }
                        this.getVersionData();
                        if (this.cusComModConfig.localPath === "") {
                            this.isSetting = true;
                        }
                        this.updatePathData = JSON.parse(JSON.stringify(this.cusComModConfig.pathData));
                    });
                },
                JoinDir () {
                    if (this.cusComModConfig.localPath !== "") {
                        Editor.assetdb.queryUuidByUrl(`db://assets/${this.cusComModConfig.localPath}`, (_err, uuid) => {
                            Editor.Ipc.sendToAll("assets:hint", uuid);
                        });
                    }

                },
                moveLib () {
                    let res = Editor.Dialog.openFile({
                        title: "更换公共库路径文件夹",
                        defaultPath: Editor.url(`${Editor.Project.path}/assets`),
                        properties: ["openDirectory"]

                    });

                    if (res && res.length > 0) {
                        let targetUrl = res[0].replace(`${Editor.Project.path}/assets/`, "");
                        if (!fs.existsSync(res[0])) {
                            Editor.log("路径不存在");
                        } else if (res[0].indexOf(Editor.url(`${Editor.Project.path}/assets/`)) === -1) {
                            alert("请选择项目assets目录下");
                        } else if (targetUrl === this.cusComModConfig.localPath) {
                            alert("请选择不同的安装目录");
                        } else {
                            this.loading = true;
                            for (let i = 0; i < this.cusComModConfig.moduleData.root.subMoudle.length; i++) {
                                let obj = this.cusComModConfig.moduleData.root.subMoudle[i];
                                if (this.cusComModConfig.pathData[obj.name] === true) {
                                    yayaSdk.copDir(Editor.url(`${Editor.Project.path}/assets/${this.cusComModConfig.localPath}/${obj.name}`), Editor.url(`${Editor.Project.path}/assets/${targetUrl}/${obj.name}`));
                                }
                            }
                            setTimeout(() => {
                                for (let i = 0; i < this.cusComModConfig.moduleData.root.subMoudle.length; i++) {
                                    let obj = this.cusComModConfig.moduleData.root.subMoudle[i];
                                    if (this.cusComModConfig.pathData[obj.name] === true) {
                                        this.creatorDelete([`db://assets/${this.cusComModConfig.localPath}/${obj.name}`]);
                                    }
                                }
                                this.cusComModConfig.localPath = targetUrl;
                                yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                                Editor.assetdb.refresh("db://assets");
                                this.loading = false;
                                this.isSetting = false;
                                alert(`安装库已更换:${this.cusComModConfig.localPath}`);
                            }, 1000);
                        }
                    }

                },
                // 撤销；
                cnacleOperator () {
                    this.initCustomData(this.cusComModConfig.moduleData.root);
                    this.cusComModConfig = JSON.parse(JSON.stringify(this.cusComModConfig));
                    this.getCheckChange();
                },
                initCustomData (data) {
                    for (let i = 0; i < data.subMoudle.length; i++) {
                        let obj = data.subMoudle[i];
                        if (obj.name === "core") {
                            this.cusComModConfig.pathData[obj.name] = true;
                        } else if (typeof this.cusComModConfig.pathData[obj.name] === "undefined") {
                            this.cusComModConfig.pathData[obj.name] = false;
                        }
                        if (obj.subMoudle) {
                            for (let j = 0; j < obj.subMoudle.length; j++) {
                                if (typeof this.cusComModConfig.pathData[`${obj.name}/${obj.subMoudle[j].name}`] === "undefined") {
                                    this.cusComModConfig.pathData[`${obj.name}/${obj.subMoudle[j].name}`] = false;
                                }
                            }
                        }

                    }
                    this.updatePathData = JSON.parse(JSON.stringify(this.cusComModConfig.pathData));
                },

                creatorDelete (data) {
                    Editor.assetdb.delete(data, (_err, _results) => {
                        // Editor.log("删除成功");
                    });

                },
                getCheckChange () {
                    let isUpdate = false;
                    for (let key in this.updatePathData) {
                        if (this.updatePathData[key] !== this.cusComModConfig.pathData[key]) {
                            isUpdate = true;
                            break;
                        }
                    }
                    this.isChange = isUpdate;
                },
                addDelete (tag, obj, key) {
                    switch (tag) {
                        case "delete":
                            // 主目录
                            if (key.indexOf("/") === -1) {
                                for (let keyy in obj) {
                                    if (keyy.indexOf(`${key}/`) !== -1) {
                                        delete obj[keyy];
                                    }
                                }
                                obj[key] = false;
                            } else {
                                // 子目录
                                let strBefore = key.split("/")[0];
                                if (typeof obj[strBefore] === "undefined") {
                                    obj[key] = false;
                                }

                            }
                            break;
                        case "add":
                            // 主目录
                            if (key.indexOf("/") === -1) {
                                let ishad = false;
                                for (let keyy in obj) {
                                    if (keyy.indexOf(`${key}/`) !== -1) {
                                        ishad = true;
                                        break;
                                    }
                                }
                                if (ishad === false) {
                                    obj[key] = true;
                                }
                            } else {
                                // 子目录
                                let strBefore = key.split("/")[0];
                                if (typeof obj[strBefore] !== "undefined") {
                                    delete obj[strBefore];
                                }
                                obj[key] = true;
                            }
                            break;

                    }


                },
                apply () {
                    let addObj = {};
                    let deleteObj = {};
                    for (let key in this.updatePathData) {
                        if (this.updatePathData[key] === false && this.updatePathData[key] !== this.cusComModConfig.pathData[key]) {
                            this.addDelete("delete", deleteObj, key);

                        } else if (this.updatePathData[key] === true && this.updatePathData[key] !== this.cusComModConfig.pathData[key]) {
                            this.addDelete("add", addObj, key);
                        }
                    }
                    Editor.log(JSON.stringify(addObj));
                    Editor.log(JSON.stringify(deleteObj));
                    this.loading = true;

                    let addLen = Object.keys(addObj).length;
                    let delLen = Object.keys(deleteObj).length;
                    if (delLen > 0) {
                        for (let ak in deleteObj) {
                            this.creatorDelete([`db://assets/${this.cusComModConfig.localPath}/${ak}`]);
                        }
                        if (addLen <= 0) {
                            setTimeout(() => {
                               // Editor.log("删除");
                                Editor.assetdb.refresh(`db://assets/${this.cusComModConfig.localPath}`);
                                this.cusComModConfig.pathData = this.updatePathData;
                                yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                                this.getCheckChange();
                                this.cusComModConfig = JSON.parse(JSON.stringify(this.cusComModConfig));
                                this.loading = false;
                            }, 500);

                        }

                    }

                    if (addLen > 0) {
                        this.downLoadPart(addObj, () => {
                            this.cusComModConfig.pathData = this.updatePathData;
                            Editor.assetdb.refresh(`db://assets/${this.cusComModConfig.localPath}`);
                            this.cusComModConfig = JSON.parse(JSON.stringify(this.cusComModConfig));
                            this.getCheckChange();
                            yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                            yayaSdk.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                            this.loading = false;
                        });
                    }

                },
                setNeedMoudle (item) {
                    let data = yayaSdk.getPathFromObj(this.cusComModConfig.moduleData.root, item);
                    this.updatePathData[data] = !this.updatePathData[data];
                    if (this.updatePathData[data] === false) {
                        if (data.indexOf("/") === -1) {
                            for (let key in this.updatePathData) {
                                if (key.indexOf(`${data}/`) !== -1) {
                                    this.updatePathData[key] = false;
                                }
                            }
                        } else {
                            // 全部取消 删除 主模块也取消
                            let haveNoSubMoudle = false;
                            let strBefore = data.split("/")[0];
                            for (let key in this.updatePathData) {
                                if (key.indexOf(`${strBefore}/`) !== -1 && this.updatePathData[key] === true) {
                                    haveNoSubMoudle = true;
                                    break;
                                }
                            }
                            if (haveNoSubMoudle === false) {
                                this.updatePathData[strBefore] = false;
                            }
                        }

                    } else if (data.indexOf("/") === -1) {
                        for (let key in this.updatePathData) {
                            if (key.indexOf(`${data}/`) !== -1) {
                                this.updatePathData[key] = true;
                            }
                        }
                    } else {
                        let strBefore = data.split("/")[0];
                        for (let key in this.updatePathData) {
                            if (key === strBefore) {
                                this.updatePathData[key] = true;
                            }
                        }
                    }

                    this.getCheckChange();
                    this.cusComModConfig = JSON.parse(JSON.stringify(this.cusComModConfig));

                },
                downLoadPart (choosePart, callBack) {
                    let downUrl = window.ipAdress + this.updateModuleData.url;
                    yayaSdk.downLib(downUrl, () => {
                        let n = downUrl.lastIndexOf("/");
                        let fileName = downUrl.substr(n + 1, downUrl.length);
                        let dirIndex = fileName.lastIndexOf(".zip");
                        let dirName = fileName.substr(0, dirIndex);
                        yayaSdk.unzip(Editor.url(`${Editor.Project.path}/tmp/${fileName}`), () => {
                            for (let key in choosePart) {
                                yayaSdk.copDir(Editor.url(`${Editor.Project.path}/tmp/${dirName}/${key}`), Editor.url(`${Editor.Project.path}/assets/${this.cusComModConfig.localPath}/${key}`));
                            }
                            setTimeout(() => {
                                // eslint-disable-next-line no-unused-expressions
                                callBack && callBack();
                            }, 1000);

                        });
                    });

                },
                checkNowData (item) {
                    let data = yayaSdk.getPathFromObj(this.cusComModConfig.moduleData.root, item);
                    return this.updatePathData[data];

                },
                getVersionData () {
                    yayaSdk.getVersion((data) => {
                        let temp = JSON.parse(data);
                        if (this.cusComModConfig.moduleData.url !== temp.url) {
                            this.isCanUpdte = true;
                        } else {
                            this.isCanUpdte = false;
                        }
                    });
                },
                // 上传
                upload () {
                    yayaSdk.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                    yayaSdk.copDir(Editor.url(`${Editor.Project.path}/assets/${this.cusComModConfig.localPath}`), Editor.url(`${Editor.Project.path}/tmp/libs`));
                    yayaSdk.zipLibs(Editor.url(`${Editor.Project.path}/tmp/libs`));
                },
                downLoad () {
                    this.loading = true;
                    yayaSdk.getVersion((data) => {
                        this.updateModuleData = JSON.parse(data);
                        let downUrl = window.ipAdress + this.updateModuleData.url;
                        yayaSdk.downLib(downUrl, () => {
                            let n = downUrl.lastIndexOf("/");
                            let fileName = downUrl.substr(n + 1, downUrl.length);
                            let dirIndex = fileName.lastIndexOf(".zip");
                            let dirName = fileName.substr(0, dirIndex);
                            yayaSdk.unzip(Editor.url(`${Editor.Project.path}/tmp/${fileName}`), () => {
                                this.initCustomData(this.updateModuleData.root);
                                for (let i = 0; i < this.updateModuleData.root.subMoudle.length; i++) {
                                    let obj = this.updateModuleData.root.subMoudle[i];
                                    if (this.cusComModConfig.pathData[obj.name] === false) {
                                        yayaSdk.delPath(Editor.url(`${Editor.Project.path}/tmp/${dirName}/${obj.name}`));
                                    }
                                    if (obj.subMoudle) {
                                        for (let j = 0; j < obj.subMoudle.length; j++) {
                                            if (this.cusComModConfig.pathData[`${obj.name}/${obj.subMoudle[j].name}`] === false) {
                                                yayaSdk.delPath(Editor.url(`${Editor.Project.path}/tmp/${dirName}/${obj.name}/${obj.subMoudle[j].name}`));
                                            }

                                        }
                                    }
                                }
                                yayaSdk.copDir(Editor.url(`${Editor.Project.path}/tmp/${dirName}`), Editor.url(`${Editor.Project.path}/assets/${this.cusComModConfig.localPath}`));
                                setTimeout(() => {
                                    Editor.assetdb.refresh(`db://assets/${this.cusComModConfig.localPath}`);
                                    this.cusComModConfig.moduleData = this.updateModuleData;
                                    this.initCustomData(this.cusComModConfig.moduleData.root);
                                    this.getCheckChange();
                                    yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                                    this.isSetting = false;
                                    this.getVersionData();
                                    this.loading = false;
                                    yayaSdk.delPath(Editor.url(`${Editor.Project.path}/tmp`));
                                }, 1000);

                            });
                        });
                    });
                },
                openTarget () {
                    Editor.log("openTarget");
                    let res = Editor.Dialog.openFile({
                        title: "选择公共库路径文件夹",
                        defaultPath: Editor.url(`${Editor.Project.path}/assets`),
                        properties: ["openDirectory"]

                    });
                    if (res && res.length > 0) {
                        Editor.log("res is ", res);
                        if (!fs.existsSync(res[0])) {
                            Editor.log("路径不存在");
                        } else if (res[0].indexOf(Editor.url(`${Editor.Project.path}/assets/`)) === -1) {
                            alert("请选择项目assets目录下");
                        } else {
                            this.cusComModConfig.localPath = res[0].replace(`${Editor.Project.path}/assets/`, "");
                            yayaSdk.writeDataToPath(`${Editor.Project.path}/.cocos-yaya-sdk.json`, JSON.stringify(this.cusComModConfig));
                            this.downLoad();
                        }
                    }
                },

                updateSetting () {
                    if (this.isSetting === false) {
                        this.isSetting = true;
                    }

                },
                updateSettingClose () {
                    if (this.isSetting === true) {
                        if (this.cusComModConfig.localPath !== "") {
                            this.isSetting = false;
                        }
                    }

                }
            }
        });
    },
    messages: {
        // eslint-disable-next-line no-empty-function
        "scene:ready" () { },
        getmsg () {
            Editor.log("收到消息了----");
        },
        // eslint-disable-next-line no-empty-function
        "scene:enter-prefab-edit-mode" () { }
    }
});