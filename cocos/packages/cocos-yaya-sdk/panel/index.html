<div class="container">
    <div class="setting" v-show="isSetting">
        <div class="loginSetting" v-show="!isLogin">
            <div class="title">cocos-yaya-sdk</div>
            <ui-button class="changeAcc" v-show="cusComModConfig.localPath!==''" @confirm="moveLib">更换安装路径</ui-button>
            <ui-button class="changeAcc" v-show="cusComModConfig.localPath===''" @confirm="openTarget">选择安装目录
            </ui-button>
            <div class="updateVersionInfo">
                <div class="checkUpdate1">
                    <div v-show="isCanUpdte&&cusComModConfig.localPath!==''" class="redPoint1"></div>
                    <a v-show="isCanUpdte&&cusComModConfig.localPath!==''" @click.prevent="downLoad"> 点击更新</a>
                    <!-- <a  @click.prevent="upload"> 点击上传</a>  -->
                </div>
                <div v-show="!isCanUpdte"> 已经是最新版本</div>
                <div class="versionInffo">当前版本信息:{{cusComModConfig.moduleData.version}}</div>
            </div>
            <div class="settingBtClose" @click=updateSettingClose>
                <span>X</span>
            </div>
        </div>

    </div>
    <div class="top">
        <img @click="JoinDir" class="pixelated" src="unpack://static/icon/assets/folder.png" style="margin-right: 5px; width: 20px;">
        <div style="font-weight: bold; text-overflow: ellipsis; overflow: hidden;"> 本地目录: </div>
        <a @click.prevent="JoinDir"> {{cusComModConfig.localPath}}</a>
        <span class="flex-1"></span>
        <ui-button class="red" :disabled="!isChange" style=" margin-right: 10px;" @confirm="cnacleOperator">撤销</ui-button>
        <ui-button class="green" :disabled="!isChange" style=" margin-right: 20px;" @confirm="apply">应用</ui-button>


    </div>
    <div v-show="!isSetting" class="titlebbody">
        <div class="oneRow" scope="col">模块名</div>
        <div class="oneRow" scope="col">描述</div>
        <div class="oneRow" scope="col">是否可以开启</div>
    </div>


    <div class="bbody scroll" v-show="!isSetting">
        <div class="modele" v-for="item in cusComModConfig.moduleData.root.subMoudle">
            <div class="modele1">
                <div class="mytr">{{item.name}}</div>
                <div class="mytr">{{item.des}}</div>
                <div class="mytr">
                    <ui-checkbox v-show="!item.canEdit" disabled checked></ui-checkbox>
                    <ui-checkbox v-show="item.canEdit" :checked="checkNowData(item)" @confirm="setNeedMoudle(item)">
                    </ui-checkbox>
                </div>
            </div>
            <div class="modele2">
                <div class="mytr1" v-show="item.subMoudle">子模块</div>
                <div class="modele3" v-show="item.subMoudle">
                    <div class="modele4" v-for="ite in item.subMoudle">
                        <div class="mytr3">{{ite.name}}</div>
                        <div class="mytr3">{{ite.des}}</div>
                        <div class="mytr3">
                            <ui-checkbox v-show="!ite.canEdit" disabled checked></ui-checkbox>
                            <ui-checkbox v-show="ite.canEdit" :checked="checkNowData(ite)"
                                @confirm="setNeedMoudle(ite)"></ui-checkbox>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <ui-loader v-if="loading"></ui-loader>
    <div class="loginQues">
        <div>
            疑惑解答?:<EMAIL>
        </div>
    </div>
    <div class="settingButton" @click="updateSetting">
        <span>设置</span>
        <div v-show="isCanUpdte&&cusComModConfig.localPath!==''" class="redPoint"></div>
    </div>

</div>