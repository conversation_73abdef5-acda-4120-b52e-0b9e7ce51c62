.container {
    margin: 10px;
    display: flex;
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.oneRow{
    width: 30%;
    text-align:center
}
.top
{
display: flex;
flex: 0 0 auto;
flex-direction: row;
align-items: center;
padding-bottom: 2px;
margin: 5px 40px 20px 0px;
border-bottom: 1px solid rgb(102, 102, 102);
height: 35px;
overflow: hidden;
}
.testbG
{
    width: 100%;
    height: 200px;
    background-color: aqua;
}
.mytr
{
    border:none !important;
    padding-top:20px;
    padding-bottom:20px;
    width: 30%;
    text-align:center
}
table,table tr th td{ 
    border-collapse: separate !important;
    border-spacing: 20px !important; }
.titlebbody
{
    display: flex;
    align-items: center;
    padding-left: 40px;
    margin-right: 40px;
}
.bbody
{
    margin-right: 20px;
    overflow: hidden;
    position: relative;
    padding-right:20px;
    overflow-y: auto;
    margin-bottom: 50px;
    
}

.mytr3
{
    width: 30%;
    text-align:center;
    border:none !important;
    padding-top:20px;
    padding-bottom:20px;
}
.mytr2
{
    width: 30%;
    text-align:center;
}


.mytr1{
  width: 40px;
  flex-grow: 0;
}
.jiange
{
    width: 100%;
    height: 30px;
}
.modele3 {
    border-left:1px solid;
    border-width:1px;
    flex-grow: 1;
}
.modele
{
    width: 100%;
    border-style:solid;
    border-width:2px;
    margin-top: 30px;
    flex-direction: column;
}
.modele1
{
    display: flex;
    align-items: center;
    padding-left: 40px;
    flex-direction: row;

}
.modele4
{
    display: flex;
    align-items: center;
    flex-direction: row;
    border-bottom:1px solid;
    border-width:1px;
}
.modele2
{
    display: flex;
    align-items: center;
    justify-content: space-around;
    flex-direction: row;
    border-top:1px solid;
    border-width:1px;
    
}
.title
{
    font-size:28px;
    margin-bottom: 40px;
}
.setting
{
    display: flex;
    flex-direction: column;
    position: absolute;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}
.settingBtClose
{
    position: absolute ;
    top: 5;
    right: 5;
    width: 20px;
    height: 20px;
    border-style: solid;
    border-radius: 50%;
    border-width: 1px;
    display: flex;
    justify-content: center;
    align-items: center;

}
.settingButton
{
    position: absolute ;
    bottom: 5;
    left: 5;
    width: 30px;
    height: 30px;
    border-style: solid;
    border-radius: 50%;
    border-width: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.redPoint1
{
  
    width: 6px;
    height: 6px;
    background-color: red;
    border-style: solid;
    border-radius: 50%;
    border-width: 1px;
    margin-right: 2px;
}
 .redPoint
{
    position: absolute;
    top:0;
    right:0;
    width: 6px;
    height: 6px;
    background-color: red;
    border-style: solid;
    border-radius: 50%;
    border-width: 1px;
}
.changeAcc
{
    margin-bottom: 150px;
}
.inputModel
{
    overflow: hidden;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-items: center;
    margin-top: 30px;
}
.checkUpdate1
{
    margin-top: 10px;
    position: relative;
    width: 60px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.updateVersionInfo
{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-items: center;
    position: absolute;
    bottom: 0;
}
.loginInfo
{
    margin-bottom: 20px;
}
.versionInffo
{
    margin-top: 10px;
    margin-left: 10px;
    max-width: 280px;
    white-space:nowrap; 
    overflow:hidden; 
    text-overflow:ellipsis;
}
.loginSetting
{
    display: flex;
     flex-direction: column;
     width: 300px;
     justify-content: center;
     align-items: center;
     height: 500px;
     border-style:solid;
     border-width:1px;
     border-radius: 3%;
     position: relative;
}
.loginQues{
  position: absolute;
  right: 0;
  bottom: 3px;
}
.loginSettingInput
{
    margin-left: 20px;
    width: 100px;

}
.loginSettingLogin
{
    margin-top: 20px;
    width: 80px;
    margin-bottom: 100px;
}



.changeAccount
{
    margin-left: 10px;

}
.checkUpdate
{
    position: absolute;
    right: 20px;
    bottom: 6px;
}
.versionInfo
{
    position: absolute;
    left:  6px;
    bottom: 6px;
}
#table-ex {
    font-size: 13px;
    margin: 22px;
    text-align: left;
    border-collapse: collapse;
    text-align: center;
}

#table-ex th {
    font-size: 14px;
    font-weight: normal;
    padding: 8px;
    border-bottom: 1px solid #666666;
}

#table-ex td {
    padding: 8px;
    border-bottom: 1px solid #666666;
    border-top: 1px solid transparent;
}

#table-ex tbody tr:hover td {
    color: #000000;
    background: #F5F7FA;
}