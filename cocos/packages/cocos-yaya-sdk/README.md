
# 环境依赖
 * cocosCreator 2.4版本已上版本
 * npm install compressing

# 使用方法
1. 拷贝cocos-yaya-sdk 放置在[‘项目’]/packages 目录下
2. 打开 cocosCreator -> 扩展 -> cocos-yaya-sdk 首次打开后 选择库安装目录，选择[‘项目’]/assets任意目录即可
 
# 目录结构描述
```
 ├── README.md                                  // help
 ├── main.js                                    // 入口文件
 ├── node_modules                               // 环境依赖
 ├── package-lock.json                          // 环境配置文件
 └── panel                                      // 插件打开页面目录
 │   ├── index.css                              // 页面css文件
 │   ├── index.html                             // html文件
 │   ├── index.js                               // html页面操作的入口文件
 │   └── yayaSdk.js                             // sdk公共接口封装文件
 └──package.json                                //  插件信息 配置文件
```

 # 版本信息 v0.0.1
 1. 支持代码库选择动态目录安装
 2. 版本更新
 3. 支持模块动态勾选移除，添加
 4. 更换安装目录

  # 版本信息 v0.0.2
 1. 修复删除多个目录遗漏问题
 2. 修复首次安装只安装core目录问题
 3. ui支持不同分辨率下边线不遮挡
 