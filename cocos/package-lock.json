{"name": "cocos", "version": "1.0.0", "lockfileVersion": 1, "requires": true, "dependencies": {"@babel/code-frame": {"version": "7.12.11", "resolved": "http://*************:8888/@babel%2fcode-frame/-/code-frame-7.12.11.tgz", "integrity": "sha1-9K1DWqJj25NbjxDyxVLSP7cWpj8=", "requires": {"@babel/highlight": "^7.10.4"}}, "@babel/helper-validator-identifier": {"version": "7.19.1", "resolved": "http://*************:8888/@babel%2fhelper-validator-identifier/-/helper-validator-identifier-7.19.1.tgz", "integrity": "sha512-awrNfaMtnHUr653GgGEs++LlAvW6w+DcPrOliSMXWCKo597CwL5Acf/wWdNkf/tfEQE3mjkeD1YOVZOUV/od1w=="}, "@babel/highlight": {"version": "7.18.6", "resolved": "http://*************:8888/@babel%2fhighlight/-/highlight-7.18.6.tgz", "integrity": "sha512-u7stbOuYjaPezCuLj29hNW1v64M2Md2qupEKP1fHc7WdOA3DgLh37suiSrZYY7haUB7iBeQZ9P1uiRF359do3g==", "requires": {"@babel/helper-validator-identifier": "^7.18.6", "chalk": "^2.0.0", "js-tokens": "^4.0.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "http://*************:8888/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://*************:8888/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://*************:8888/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://*************:8888/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://*************:8888/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "has-flag": {"version": "3.0.0", "resolved": "http://*************:8888/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "supports-color": {"version": "5.5.0", "resolved": "http://*************:8888/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}}}, "@eslint/eslintrc": {"version": "0.4.3", "resolved": "http://*************:8888/@eslint%2feslintrc/-/eslintrc-0.4.3.tgz", "integrity": "sha1-nkKYHvA1vrPdSa3ResuW6P9vOUw=", "requires": {"ajv": "^6.12.4", "debug": "^4.1.1", "espree": "^7.3.0", "globals": "^13.9.0", "ignore": "^4.0.6", "import-fresh": "^3.2.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "strip-json-comments": "^3.1.1"}}, "@humanwhocodes/config-array": {"version": "0.5.0", "resolved": "http://*************:8888/@humanwhocodes%2fconfig-array/-/config-array-0.5.0.tgz", "integrity": "sha1-FAeWfUxu7Nc4j4Os8er00Mbljvk=", "requires": {"@humanwhocodes/object-schema": "^1.2.0", "debug": "^4.1.1", "minimatch": "^3.0.4"}}, "@humanwhocodes/object-schema": {"version": "1.2.1", "resolved": "http://*************:8888/@humanwhocodes%2fobject-schema/-/object-schema-1.2.1.tgz", "integrity": "sha1-tSBSnsIdjllFoYUd/Rwy6U45/0U="}, "abab": {"version": "1.0.4", "resolved": "http://*************:8888/abab/-/abab-1.0.4.tgz", "integrity": "sha1-X6rZwsB/YN12dw9xzwJbYqY8/U4=", "optional": true}, "acorn": {"version": "7.4.1", "resolved": "http://*************:8888/acorn/-/acorn-7.4.1.tgz", "integrity": "sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo="}, "acorn-globals": {"version": "1.0.9", "resolved": "http://*************:8888/acorn-globals/-/acorn-globals-1.0.9.tgz", "integrity": "sha1-VbtemGkVB7dFedBRNBMhfDgMVM8=", "optional": true, "requires": {"acorn": "^2.1.0"}, "dependencies": {"acorn": {"version": "2.7.0", "resolved": "http://*************:8888/acorn/-/acorn-2.7.0.tgz", "integrity": "sha1-q259nYhqrKiwhbwzEreaGYQz8Oc=", "optional": true}}}, "acorn-jsx": {"version": "5.3.2", "resolved": "http://*************:8888/acorn-jsx/-/acorn-jsx-5.3.2.tgz", "integrity": "sha1-ftW7VZCLOy8bxVxq8WU7rafweTc="}, "ajv": {"version": "6.12.6", "resolved": "http://*************:8888/ajv/-/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "requires": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "ansi-colors": {"version": "4.1.3", "resolved": "http://*************:8888/ansi-colors/-/ansi-colors-4.1.3.tgz", "integrity": "sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw=="}, "ansi-regex": {"version": "5.0.1", "resolved": "http://*************:8888/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ="}, "ansi-styles": {"version": "4.3.0", "resolved": "http://*************:8888/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha1-7dgDYornHATIWuegkG7a00tkiTc=", "requires": {"color-convert": "^2.0.1"}}, "argparse": {"version": "1.0.10", "resolved": "http://*************:8888/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "requires": {"sprintf-js": "~1.0.2"}}, "asn1": {"version": "0.2.6", "resolved": "http://*************:8888/asn1/-/asn1-0.2.6.tgz", "integrity": "sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=", "optional": true, "requires": {"safer-buffer": "~2.1.0"}}, "assert-plus": {"version": "1.0.0", "resolved": "http://*************:8888/assert-plus/-/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "optional": true}, "astral-regex": {"version": "2.0.0", "resolved": "http://*************:8888/astral-regex/-/astral-regex-2.0.0.tgz", "integrity": "sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ=="}, "asynckit": {"version": "0.4.0", "resolved": "http://*************:8888/asynckit/-/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "optional": true}, "aws-sign2": {"version": "0.7.0", "resolved": "http://*************:8888/aws-sign2/-/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "optional": true}, "aws4": {"version": "1.11.0", "resolved": "http://*************:8888/aws4/-/aws4-1.11.0.tgz", "integrity": "sha1-1h9G2DslGSUOJ4Ta9bCUeai0HFk=", "optional": true}, "balanced-match": {"version": "1.0.2", "resolved": "http://*************:8888/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4="}, "bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://*************:8888/bcrypt-pbkdf/-/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "optional": true, "requires": {"tweetnacl": "^0.14.3"}}, "boolbase": {"version": "1.0.0", "resolved": "http://*************:8888/boolbase/-/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24="}, "brace-expansion": {"version": "1.1.11", "resolved": "http://*************:8888/brace-expansion/-/brace-expansion-1.1.11.tgz", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "requires": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "builtin-modules": {"version": "1.1.1", "resolved": "http://*************:8888/builtin-modules/-/builtin-modules-1.1.1.tgz", "integrity": "sha1-Jw8HbFpywC9bZaR9+Uxf46J4iS8="}, "callsites": {"version": "3.1.0", "resolved": "http://*************:8888/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="}, "caseless": {"version": "0.12.0", "resolved": "http://*************:8888/caseless/-/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "optional": true}, "chalk": {"version": "4.1.2", "resolved": "http://*************:8888/chalk/-/chalk-4.1.2.tgz", "integrity": "sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=", "requires": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}}, "cheerio": {"version": "0.20.0", "resolved": "http://*************:8888/cheerio/-/cheerio-0.20.0.tgz", "integrity": "sha1-XHEPK6uVZTJyhCugHG6mGzVF7DU=", "requires": {"css-select": "~1.2.0", "dom-serializer": "~0.1.0", "entities": "~1.1.1", "htmlparser2": "~3.8.1", "jsdom": "^7.0.2", "lodash": "^4.1.0"}}, "clipboard-polyfill": {"version": "3.0.3", "resolved": "http://*************:8888/clipboard-polyfill/-/clipboard-polyfill-3.0.3.tgz", "integrity": "sha1-FZ6gdo4g7cf/2kBL0TxUxz3k/0A="}, "color-convert": {"version": "2.0.1", "resolved": "http://*************:8888/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "requires": {"color-name": "~1.1.4"}}, "color-name": {"version": "1.1.4", "resolved": "http://*************:8888/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="}, "combined-stream": {"version": "1.0.8", "resolved": "http://*************:8888/combined-stream/-/combined-stream-1.0.8.tgz", "integrity": "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==", "optional": true, "requires": {"delayed-stream": "~1.0.0"}}, "commander": {"version": "2.20.3", "resolved": "http://*************:8888/commander/-/commander-2.20.3.tgz", "integrity": "sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ=="}, "concat-map": {"version": "0.0.1", "resolved": "http://*************:8888/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s="}, "core-util-is": {"version": "1.0.3", "resolved": "http://*************:8888/core-util-is/-/core-util-is-1.0.3.tgz", "integrity": "sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U="}, "cross-spawn": {"version": "7.0.3", "resolved": "http://*************:8888/cross-spawn/-/cross-spawn-7.0.3.tgz", "integrity": "sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==", "requires": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}}, "css-select": {"version": "1.2.0", "resolved": "http://*************:8888/css-select/-/css-select-1.2.0.tgz", "integrity": "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=", "requires": {"boolbase": "~1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "~1.0.1"}}, "css-what": {"version": "2.1.3", "resolved": "http://*************:8888/css-what/-/css-what-2.1.3.tgz", "integrity": "sha512-a+EPoD+uZiNfh+5fxw2nO9QwFa6nJe2Or35fGY6Ipw1R3R4AGz1d1TEZrCegvw2YTmZ0jXirGYlzxxpYSHwpEg=="}, "cssom": {"version": "0.3.8", "resolved": "http://*************:8888/cssom/-/cssom-0.3.8.tgz", "integrity": "sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==", "optional": true}, "cssstyle": {"version": "0.2.37", "resolved": "http://*************:8888/cssstyle/-/cssstyle-0.2.37.tgz", "integrity": "sha1-VBCXI0yyUTyDzu06zdwn/yeYfVQ=", "optional": true, "requires": {"cssom": "0.3.x"}}, "dashdash": {"version": "1.14.1", "resolved": "http://*************:8888/dashdash/-/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "debug": {"version": "4.3.4", "resolved": "http://*************:8888/debug/-/debug-4.3.4.tgz", "integrity": "sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==", "requires": {"ms": "2.1.2"}}, "deep-is": {"version": "0.1.4", "resolved": "http://*************:8888/deep-is/-/deep-is-0.1.4.tgz", "integrity": "sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE="}, "delayed-stream": {"version": "1.0.0", "resolved": "http://*************:8888/delayed-stream/-/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "optional": true}, "diff": {"version": "4.0.2", "resolved": "http://*************:8888/diff/-/diff-4.0.2.tgz", "integrity": "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A=="}, "doctrine": {"version": "3.0.0", "resolved": "http://*************:8888/doctrine/-/doctrine-3.0.0.tgz", "integrity": "sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==", "requires": {"esutils": "^2.0.2"}}, "dom-serializer": {"version": "0.1.1", "resolved": "http://*************:8888/dom-serializer/-/dom-serializer-0.1.1.tgz", "integrity": "sha512-l0IU0pPzLWSHBcieZbpOKgkIn3ts3vAh7ZuFyXNwJxJXk/c4Gwj9xaTJwIDVQCXawWD0qb3IzMGH5rglQaO0XA==", "requires": {"domelementtype": "^1.3.0", "entities": "^1.1.1"}}, "domelementtype": {"version": "1.3.1", "resolved": "http://*************:8888/domelementtype/-/domelementtype-1.3.1.tgz", "integrity": "sha512-BSKB+TSpMpFI/HOxCNr1O8aMOTZ8hT3pM3GQ0w/mWRmkhEDSFJkkyzz4XQsBV44BChwGkrDfMyjVD0eA2aFV3w=="}, "domhandler": {"version": "2.3.0", "resolved": "http://*************:8888/domhandler/-/domhandler-2.3.0.tgz", "integrity": "sha1-LeWaCCLVAn+r/28DLCsloqir5zg=", "requires": {"domelementtype": "1"}}, "domutils": {"version": "1.5.1", "resolved": "http://*************:8888/domutils/-/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "requires": {"dom-serializer": "0", "domelementtype": "1"}}, "ecc-jsbn": {"version": "0.1.2", "resolved": "http://*************:8888/ecc-jsbn/-/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "optional": true, "requires": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "emoji-regex": {"version": "8.0.0", "resolved": "http://*************:8888/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A=="}, "enquirer": {"version": "2.3.6", "resolved": "http://*************:8888/enquirer/-/enquirer-2.3.6.tgz", "integrity": "sha512-yjNnPr315/FjS4zIsUxYguYUPP2e1NK4d7E7ZOLiyYCcbFBiTMyID+2wvm2w6+pZ/odMA7cRkjhsPbltwBOrLg==", "requires": {"ansi-colors": "^4.1.1"}}, "entities": {"version": "1.1.2", "resolved": "http://*************:8888/entities/-/entities-1.1.2.tgz", "integrity": "sha512-f2LZMYl1Fzu7YSBKg+RoROelpOaNrcGmE9AZubeDfrCEia483oW4MI4VyFd5VNHIgQ/7qm1I0wUHK1eJnn2y2w=="}, "escape-string-regexp": {"version": "4.0.0", "resolved": "http://*************:8888/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "integrity": "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="}, "escodegen": {"version": "1.14.3", "resolved": "http://*************:8888/escodegen/-/escodegen-1.14.3.tgz", "integrity": "sha512-qFcX0XJkdg+PB3xjZZG/wKSuT1PnQWx57+TVSjIMmILd2yC/6ByYElPwJnslDsuWuSAp4AwJGumarAAmJch5Kw==", "optional": true, "requires": {"esprima": "^4.0.1", "estraverse": "^4.2.0", "esutils": "^2.0.2", "optionator": "^0.8.1", "source-map": "~0.6.1"}, "dependencies": {"levn": {"version": "0.3.0", "resolved": "http://*************:8888/levn/-/levn-0.3.0.tgz", "integrity": "sha1-OwmSTt+fCDwEkP3UwLxEIeBHZO4=", "optional": true, "requires": {"prelude-ls": "~1.1.2", "type-check": "~0.3.2"}}, "optionator": {"version": "0.8.3", "resolved": "http://*************:8888/optionator/-/optionator-0.8.3.tgz", "integrity": "sha512-+IW9pACdk3XWmmTXG8m3upGUJst5XRGzxMRjXzAuJ1XnIFNvfhjjIuYkDvysnPQ7qzqVzLt78BCruntqRhWQbA==", "optional": true, "requires": {"deep-is": "~0.1.3", "fast-levenshtein": "~2.0.6", "levn": "~0.3.0", "prelude-ls": "~1.1.2", "type-check": "~0.3.2", "word-wrap": "~1.2.3"}}, "prelude-ls": {"version": "1.1.2", "resolved": "http://*************:8888/prelude-ls/-/prelude-ls-1.1.2.tgz", "integrity": "sha1-IZMqVJ9eUv/ZqCf1cOBL5iqX2lQ=", "optional": true}, "type-check": {"version": "0.3.2", "resolved": "http://*************:8888/type-check/-/type-check-0.3.2.tgz", "integrity": "sha1-WITKtRLPHTVeP7eE8wgEsrUg23I=", "optional": true, "requires": {"prelude-ls": "~1.1.2"}}}}, "eslint": {"version": "7.32.0", "resolved": "http://*************:8888/eslint/-/eslint-7.32.0.tgz", "integrity": "sha1-xtMooUvj+wjI0dIeEsAv23oqgS0=", "requires": {"@babel/code-frame": "7.12.11", "@eslint/eslintrc": "^0.4.3", "@humanwhocodes/config-array": "^0.5.0", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.0.1", "doctrine": "^3.0.0", "enquirer": "^2.3.5", "escape-string-regexp": "^4.0.0", "eslint-scope": "^5.1.1", "eslint-utils": "^2.1.0", "eslint-visitor-keys": "^2.0.0", "espree": "^7.3.1", "esquery": "^1.4.0", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "functional-red-black-tree": "^1.0.1", "glob-parent": "^5.1.2", "globals": "^13.6.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "js-yaml": "^3.13.1", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.0.4", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "progress": "^2.0.0", "regexpp": "^3.1.0", "semver": "^7.2.1", "strip-ansi": "^6.0.0", "strip-json-comments": "^3.1.0", "table": "^6.0.9", "text-table": "^0.2.0", "v8-compile-cache": "^2.0.3"}}, "eslint-scope": {"version": "5.1.1", "resolved": "http://*************:8888/eslint-scope/-/eslint-scope-5.1.1.tgz", "integrity": "sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=", "requires": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}}, "eslint-utils": {"version": "2.1.0", "resolved": "http://*************:8888/eslint-utils/-/eslint-utils-2.1.0.tgz", "integrity": "sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==", "requires": {"eslint-visitor-keys": "^1.1.0"}, "dependencies": {"eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://*************:8888/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="}}}, "eslint-visitor-keys": {"version": "2.1.0", "resolved": "http://*************:8888/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz", "integrity": "sha1-9lMoJZMFknOSyTjtROsKXJsr0wM="}, "espree": {"version": "7.3.1", "resolved": "http://*************:8888/espree/-/espree-7.3.1.tgz", "integrity": "sha1-8t8zC3Usb1UBn4vYm3ZgA5wbu7Y=", "requires": {"acorn": "^7.4.0", "acorn-jsx": "^5.3.1", "eslint-visitor-keys": "^1.3.0"}, "dependencies": {"eslint-visitor-keys": {"version": "1.3.0", "resolved": "http://*************:8888/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz", "integrity": "sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ=="}}}, "esprima": {"version": "4.0.1", "resolved": "http://*************:8888/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A=="}, "esquery": {"version": "1.4.0", "resolved": "http://*************:8888/esquery/-/esquery-1.4.0.tgz", "integrity": "sha1-IUj/w4uC6McFff7UhCWz5h8PJKU=", "requires": {"estraverse": "^5.1.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://*************:8888/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="}}}, "esrecurse": {"version": "4.3.0", "resolved": "http://*************:8888/esrecurse/-/esrecurse-4.3.0.tgz", "integrity": "sha1-eteWTWeauyi+5yzsY3WLHF0smSE=", "requires": {"estraverse": "^5.2.0"}, "dependencies": {"estraverse": {"version": "5.3.0", "resolved": "http://*************:8888/estraverse/-/estraverse-5.3.0.tgz", "integrity": "sha1-LupSkHAvJquP5TcDcP+GyWXSESM="}}}, "estraverse": {"version": "4.3.0", "resolved": "http://*************:8888/estraverse/-/estraverse-4.3.0.tgz", "integrity": "sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw=="}, "esutils": {"version": "2.0.3", "resolved": "http://*************:8888/esutils/-/esutils-2.0.3.tgz", "integrity": "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="}, "extend": {"version": "3.0.2", "resolved": "http://*************:8888/extend/-/extend-3.0.2.tgz", "integrity": "sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==", "optional": true}, "extsprintf": {"version": "1.3.0", "resolved": "http://*************:8888/extsprintf/-/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "optional": true}, "fast-deep-equal": {"version": "3.1.3", "resolved": "http://*************:8888/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "integrity": "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="}, "fast-json-stable-stringify": {"version": "2.1.0", "resolved": "http://*************:8888/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="}, "fast-levenshtein": {"version": "2.0.6", "resolved": "http://*************:8888/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz", "integrity": "sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc="}, "file-entry-cache": {"version": "6.0.1", "resolved": "http://*************:8888/file-entry-cache/-/file-entry-cache-6.0.1.tgz", "integrity": "sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=", "requires": {"flat-cache": "^3.0.4"}}, "flat-cache": {"version": "3.0.4", "resolved": "http://*************:8888/flat-cache/-/flat-cache-3.0.4.tgz", "integrity": "sha1-YbAzgwKy/p+Vfcwy/CqH8cMEixE=", "requires": {"flatted": "^3.1.0", "rimraf": "^3.0.2"}}, "flatted": {"version": "3.2.7", "resolved": "http://*************:8888/flatted/-/flatted-3.2.7.tgz", "integrity": "sha512-5nqDSxl8nn5BSNxyR3n4I6eDmbolI6WT+QqR547RwxQapgjQBmtktdP+HTBb/a/zLsbzERTONyUB5pefh5TtjQ=="}, "forever-agent": {"version": "0.6.1", "resolved": "http://*************:8888/forever-agent/-/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "optional": true}, "form-data": {"version": "2.3.3", "resolved": "http://*************:8888/form-data/-/form-data-2.3.3.tgz", "integrity": "sha512-1lLKB2Mu3aGP1Q/2eCOx0fNbRMe7XdwktwOruhfqqd0rIJWwN4Dh+E3hrPSlDCXnSR7UtZ1N38rVXm+6+MEhJQ==", "optional": true, "requires": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}}, "fs.realpath": {"version": "1.0.0", "resolved": "http://*************:8888/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8="}, "function-bind": {"version": "1.1.1", "resolved": "http://*************:8888/function-bind/-/function-bind-1.1.1.tgz", "integrity": "sha512-yIovAzMX49sF8Yl58fSCWJ5svSLuaibPxXQJFLmBObTuCr0Mf1KiPopGM9NiFjiYBCbfaa2Fh6breQ6ANVTI0A=="}, "functional-red-black-tree": {"version": "1.0.1", "resolved": "http://*************:8888/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz", "integrity": "sha1-GwqzvVU7Kg1jmdKcDj6gslIHgyc="}, "getpass": {"version": "0.1.7", "resolved": "http://*************:8888/getpass/-/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "optional": true, "requires": {"assert-plus": "^1.0.0"}}, "glob": {"version": "7.2.3", "resolved": "http://*************:8888/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "requires": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}}, "glob-parent": {"version": "5.1.2", "resolved": "http://*************:8888/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "requires": {"is-glob": "^4.0.1"}}, "globals": {"version": "13.19.0", "resolved": "http://*************:8888/globals/-/globals-13.19.0.tgz", "integrity": "sha512-dkQ957uSRWHw7CFXLUtUHQI3g3aWApYhfNR2O6jn/907riyTYKVBmxYVROkBcY614FSSeSJh7Xm7SrUWCxvJMQ==", "requires": {"type-fest": "^0.20.2"}}, "har-schema": {"version": "2.0.0", "resolved": "http://*************:8888/har-schema/-/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "optional": true}, "har-validator": {"version": "5.1.5", "resolved": "http://*************:8888/har-validator/-/har-validator-5.1.5.tgz", "integrity": "sha512-nmT2T0lljbxdQZfspsno9hgrG3Uir6Ks5afism62poxqBM6sDnMEuPmzTq8XN0OEwqKLLdh1jQI3qyE66Nzb3w==", "optional": true, "requires": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}}, "has": {"version": "1.0.3", "resolved": "http://*************:8888/has/-/has-1.0.3.tgz", "integrity": "sha512-f2dvO0VU6Oej7RkWJGrehjbzMAjFp5/VKPp5tTpWIV4JHHZK1/BxbFRtf/siA2SWTe09caDmVtYYzWEIbBS4zw==", "requires": {"function-bind": "^1.1.1"}}, "has-flag": {"version": "4.0.0", "resolved": "http://*************:8888/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="}, "htmlparser2": {"version": "3.8.3", "resolved": "http://*************:8888/htmlparser2/-/htmlparser2-3.8.3.tgz", "integrity": "sha1-mWwosZFRaovoZQGn15dX5ccMEGg=", "requires": {"domelementtype": "1", "domhandler": "2.3", "domutils": "1.5", "entities": "1.0", "readable-stream": "1.1"}, "dependencies": {"entities": {"version": "1.0.0", "resolved": "http://*************:8888/entities/-/entities-1.0.0.tgz", "integrity": "sha1-sph6o4ITR/zeZCsk/fyeT7cSvyY="}}}, "http-signature": {"version": "1.2.0", "resolved": "http://*************:8888/http-signature/-/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "optional": true, "requires": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}}, "ignore": {"version": "4.0.6", "resolved": "http://*************:8888/ignore/-/ignore-4.0.6.tgz", "integrity": "sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg=="}, "import-fresh": {"version": "3.3.0", "resolved": "http://*************:8888/import-fresh/-/import-fresh-3.3.0.tgz", "integrity": "sha1-NxYsJfy566oublPVtNiM4X2eDCs=", "requires": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}}, "imurmurhash": {"version": "0.1.4", "resolved": "http://*************:8888/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o="}, "inflight": {"version": "1.0.6", "resolved": "http://*************:8888/inflight/-/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "requires": {"once": "^1.3.0", "wrappy": "1"}}, "inherits": {"version": "2.0.4", "resolved": "http://*************:8888/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ=="}, "is-core-module": {"version": "2.11.0", "resolved": "http://*************:8888/is-core-module/-/is-core-module-2.11.0.tgz", "integrity": "sha512-RRjxlvLDkD1YJwDbroBHMb+cukurkDWNyHx7D3oNB5x9rb5ogcksMC5wHCadcXoo67gVr/+3GFySh3134zi6rw==", "requires": {"has": "^1.0.3"}}, "is-extglob": {"version": "2.1.1", "resolved": "http://*************:8888/is-extglob/-/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI="}, "is-fullwidth-code-point": {"version": "3.0.0", "resolved": "http://*************:8888/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg=="}, "is-glob": {"version": "4.0.3", "resolved": "http://*************:8888/is-glob/-/is-glob-4.0.3.tgz", "integrity": "sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=", "requires": {"is-extglob": "^2.1.1"}}, "is-typedarray": {"version": "1.0.0", "resolved": "http://*************:8888/is-typedarray/-/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "optional": true}, "isarray": {"version": "0.0.1", "resolved": "http://*************:8888/isarray/-/isarray-0.0.1.tgz", "integrity": "sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8="}, "isexe": {"version": "2.0.0", "resolved": "http://*************:8888/isexe/-/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA="}, "isstream": {"version": "0.1.2", "resolved": "http://*************:8888/isstream/-/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "optional": true}, "js-tokens": {"version": "4.0.0", "resolved": "http://*************:8888/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="}, "js-yaml": {"version": "3.14.1", "resolved": "http://*************:8888/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=", "requires": {"argparse": "^1.0.7", "esprima": "^4.0.0"}}, "jsbn": {"version": "0.1.1", "resolved": "http://*************:8888/jsbn/-/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "optional": true}, "jsdom": {"version": "7.2.2", "resolved": "http://*************:8888/jsdom/-/jsdom-7.2.2.tgz", "integrity": "sha1-QLQCdwwr2iNGkJa+6Rq2deOx/G4=", "optional": true, "requires": {"abab": "^1.0.0", "acorn": "^2.4.0", "acorn-globals": "^1.0.4", "cssom": ">= 0.3.0 < 0.4.0", "cssstyle": ">= 0.2.29 < 0.3.0", "escodegen": "^1.6.1", "nwmatcher": ">= 1.3.7 < 2.0.0", "parse5": "^1.5.1", "request": "^2.55.0", "sax": "^1.1.4", "symbol-tree": ">= 3.1.0 < 4.0.0", "tough-cookie": "^2.2.0", "webidl-conversions": "^2.0.0", "whatwg-url-compat": "~0.6.5", "xml-name-validator": ">= 2.0.1 < 3.0.0"}, "dependencies": {"acorn": {"version": "2.7.0", "resolved": "http://*************:8888/acorn/-/acorn-2.7.0.tgz", "integrity": "sha1-q259nYhqrKiwhbwzEreaGYQz8Oc=", "optional": true}}}, "json-schema": {"version": "0.4.0", "resolved": "http://*************:8888/json-schema/-/json-schema-0.4.0.tgz", "integrity": "sha1-995M9u+rg4666zI2R0y7paGTCrU=", "optional": true}, "json-schema-traverse": {"version": "0.4.1", "resolved": "http://*************:8888/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz", "integrity": "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="}, "json-stable-stringify-without-jsonify": {"version": "1.0.1", "resolved": "http://*************:8888/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz", "integrity": "sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE="}, "json-stringify-safe": {"version": "5.0.1", "resolved": "http://*************:8888/json-stringify-safe/-/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "optional": true}, "jsprim": {"version": "1.4.2", "resolved": "http://*************:8888/jsprim/-/jsprim-1.4.2.tgz", "integrity": "sha512-P2bSOMAc/ciLz6DzgjVlGJP9+BrJWu5UDGK70C2iweC5QBIeFf0ZXRvGjEj2uYgrY2MkAAhsSWHDWlFtEroZWw==", "optional": true, "requires": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}}, "levn": {"version": "0.4.1", "resolved": "http://*************:8888/levn/-/levn-0.4.1.tgz", "integrity": "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==", "requires": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}}, "lodash": {"version": "4.17.21", "resolved": "http://*************:8888/lodash/-/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw="}, "lodash.merge": {"version": "4.6.2", "resolved": "http://*************:8888/lodash.merge/-/lodash.merge-4.6.2.tgz", "integrity": "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="}, "lodash.truncate": {"version": "4.4.2", "resolved": "http://*************:8888/lodash.truncate/-/lodash.truncate-4.4.2.tgz", "integrity": "sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM="}, "lru-cache": {"version": "6.0.0", "resolved": "http://*************:8888/lru-cache/-/lru-cache-6.0.0.tgz", "integrity": "sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==", "requires": {"yallist": "^4.0.0"}}, "mime-db": {"version": "1.52.0", "resolved": "http://*************:8888/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==", "optional": true}, "mime-types": {"version": "2.1.35", "resolved": "http://*************:8888/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==", "optional": true, "requires": {"mime-db": "1.52.0"}}, "minimatch": {"version": "3.1.2", "resolved": "http://*************:8888/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "requires": {"brace-expansion": "^1.1.7"}}, "minimist": {"version": "1.2.7", "resolved": "http://*************:8888/minimist/-/minimist-1.2.7.tgz", "integrity": "sha512-bzfL1YUZsP41gmu/qjrEk0Q6i2ix/cVeAhbCbqH9u3zYutS1cLg00qhrD0M2MVdCcx4Sc0UpP2eBWo9rotpq6g=="}, "mkdirp": {"version": "0.5.6", "resolved": "http://*************:8888/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==", "requires": {"minimist": "^1.2.6"}}, "ms": {"version": "2.1.2", "resolved": "http://*************:8888/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w=="}, "natural-compare": {"version": "1.4.0", "resolved": "http://*************:8888/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc="}, "nth-check": {"version": "1.0.2", "resolved": "http://*************:8888/nth-check/-/nth-check-1.0.2.tgz", "integrity": "sha512-WeBOdju8SnzPN5vTUJYxYUxLeXpCaVP5i5e0LF8fg7WORF2Wd7wFX/pk0tYZk7s8T+J7VLy0Da6J1+wCT0AtHg==", "requires": {"boolbase": "~1.0.0"}}, "nwmatcher": {"version": "1.4.4", "resolved": "http://*************:8888/nwmatcher/-/nwmatcher-1.4.4.tgz", "integrity": "sha512-3iuY4N5dhgMpCUrOVnuAdGrgxVqV2cJpM+XNccjR2DKOB1RUP0aA+wGXEiNziG/UKboFyGBIoKOaNlJxx8bciQ==", "optional": true}, "oauth-sign": {"version": "0.9.0", "resolved": "http://*************:8888/oauth-sign/-/oauth-sign-0.9.0.tgz", "integrity": "sha512-fexhUFFPTGV8ybAtSIGbV6gOkSv8UtRbDBnAyLQw4QPKkgNlsH2ByPGtMUqdWkos6YCRmAqViwgZrJc/mRDzZQ==", "optional": true}, "once": {"version": "1.4.0", "resolved": "http://*************:8888/once/-/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "requires": {"wrappy": "1"}}, "optionator": {"version": "0.9.1", "resolved": "http://*************:8888/optionator/-/optionator-0.9.1.tgz", "integrity": "sha512-74RlY5FCnhq4jRxVUPKDaRwrVNXMqsGsiW6AJw4XK8hmtm10wC0ypZBLw5IIp85NZMr91+qd1RvvENwg7jjRFw==", "requires": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.3"}}, "parent-module": {"version": "1.0.1", "resolved": "http://*************:8888/parent-module/-/parent-module-1.0.1.tgz", "integrity": "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==", "requires": {"callsites": "^3.0.0"}}, "parse5": {"version": "1.5.1", "resolved": "http://*************:8888/parse5/-/parse5-1.5.1.tgz", "integrity": "sha1-m387DeMr543CQBsXVzzK8Pb1nZQ=", "optional": true}, "path-is-absolute": {"version": "1.0.1", "resolved": "http://*************:8888/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18="}, "path-key": {"version": "3.1.1", "resolved": "http://*************:8888/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="}, "path-parse": {"version": "1.0.7", "resolved": "http://*************:8888/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU="}, "performance-now": {"version": "2.1.0", "resolved": "http://*************:8888/performance-now/-/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "optional": true}, "prelude-ls": {"version": "1.2.1", "resolved": "http://*************:8888/prelude-ls/-/prelude-ls-1.2.1.tgz", "integrity": "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="}, "progress": {"version": "2.0.3", "resolved": "http://*************:8888/progress/-/progress-2.0.3.tgz", "integrity": "sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA=="}, "psl": {"version": "1.9.0", "resolved": "http://*************:8888/psl/-/psl-1.9.0.tgz", "integrity": "sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==", "optional": true}, "punycode": {"version": "2.1.1", "resolved": "http://*************:8888/punycode/-/punycode-2.1.1.tgz", "integrity": "sha512-XRsRjdf+j5ml+y/6GKHPZbrF/8p2Yga0JPtdqTIY2Xe5ohJPD9saDJJLPvp9+NSBprVvevdXZybnj2cv8OEd0A=="}, "qs": {"version": "6.5.3", "resolved": "http://*************:8888/qs/-/qs-6.5.3.tgz", "integrity": "sha512-qxXIEh4pCGfHICj1mAJQ2/2XVZkjCDTcEgfoSQxc/fYivUZxTkk7L3bDBJSoNrEzXI17oUO5Dp07ktqE5KzczA==", "optional": true}, "readable-stream": {"version": "1.1.14", "resolved": "http://*************:8888/readable-stream/-/readable-stream-1.1.14.tgz", "integrity": "sha1-fPTFTvZI44EwhMY23SB54WbAgdk=", "requires": {"core-util-is": "~1.0.0", "inherits": "~2.0.1", "isarray": "0.0.1", "string_decoder": "~0.10.x"}}, "regexpp": {"version": "3.2.0", "resolved": "http://*************:8888/regexpp/-/regexpp-3.2.0.tgz", "integrity": "sha1-BCWido2PI7rXDKS5BGH6LxIT4bI="}, "request": {"version": "2.88.3", "resolved": "http://*************:8888/request/-/request-2.88.3.tgz", "integrity": "sha512-<PERSON>hG7EMZoyMG3i6xT8reYKS9wfoYqseQbIPHjdcgDQGUNIraauZmrvWUfT6uPZlVOTMGkJfy+EDQ1YLKB3GQnQ==", "optional": true, "requires": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}}, "require-from-string": {"version": "2.0.2", "resolved": "http://*************:8888/require-from-string/-/require-from-string-2.0.2.tgz", "integrity": "sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw=="}, "resolve": {"version": "1.22.1", "resolved": "http://*************:8888/resolve/-/resolve-1.22.1.tgz", "integrity": "sha512-nBpuuYuY5jFsli/JIs1oldw6fOQCBioohqWZg/2hiaOybXOft4lonv85uDOKXdf8rhyK159cxU5cDcK/NKk8zw==", "requires": {"is-core-module": "^2.9.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}}, "resolve-from": {"version": "4.0.0", "resolved": "http://*************:8888/resolve-from/-/resolve-from-4.0.0.tgz", "integrity": "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="}, "rimraf": {"version": "3.0.2", "resolved": "http://*************:8888/rimraf/-/rimraf-3.0.2.tgz", "integrity": "sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==", "requires": {"glob": "^7.1.3"}}, "safe-buffer": {"version": "5.2.1", "resolved": "http://*************:8888/safe-buffer/-/safe-buffer-5.2.1.tgz", "integrity": "sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==", "optional": true}, "safer-buffer": {"version": "2.1.2", "resolved": "http://*************:8888/safer-buffer/-/safer-buffer-2.1.2.tgz", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "optional": true}, "sax": {"version": "1.2.4", "resolved": "http://*************:8888/sax/-/sax-1.2.4.tgz", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==", "optional": true}, "semver": {"version": "7.3.8", "resolved": "http://*************:8888/semver/-/semver-7.3.8.tgz", "integrity": "sha512-NB1ctGL5rlHrPJtFDVIVzTyQylMLu9N9VICA6HSFJo8MCGVTMW6gfpicwKmmK/dAjTOrqu5l63JJOpDSrAis3A==", "requires": {"lru-cache": "^6.0.0"}}, "shebang-command": {"version": "2.0.0", "resolved": "http://*************:8888/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "requires": {"shebang-regex": "^3.0.0"}}, "shebang-regex": {"version": "3.0.0", "resolved": "http://*************:8888/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="}, "slice-ansi": {"version": "4.0.0", "resolved": "http://*************:8888/slice-ansi/-/slice-ansi-4.0.0.tgz", "integrity": "sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==", "requires": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}}, "source-map": {"version": "0.6.1", "resolved": "http://*************:8888/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "optional": true}, "sprintf-js": {"version": "1.0.3", "resolved": "http://*************:8888/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw="}, "sshpk": {"version": "1.17.0", "resolved": "http://*************:8888/sshpk/-/sshpk-1.17.0.tgz", "integrity": "sha512-/9HIEs1ZXGhSPE8X6Ccm7Nam1z8KcoCqPdI7ecm1N33EzAetWahvQWVqLZtaZQ+IDKX4IyA2o0gBzqIMkAagHQ==", "optional": true, "requires": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}}, "string-width": {"version": "4.2.3", "resolved": "http://*************:8888/string-width/-/string-width-4.2.3.tgz", "integrity": "sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=", "requires": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}}, "string_decoder": {"version": "0.10.31", "resolved": "http://*************:8888/string_decoder/-/string_decoder-0.10.31.tgz", "integrity": "sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ="}, "strip-ansi": {"version": "6.0.1", "resolved": "http://*************:8888/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=", "requires": {"ansi-regex": "^5.0.1"}}, "strip-json-comments": {"version": "3.1.1", "resolved": "http://*************:8888/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="}, "supports-color": {"version": "7.2.0", "resolved": "http://*************:8888/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=", "requires": {"has-flag": "^4.0.0"}}, "supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "http://*************:8888/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w=="}, "symbol-tree": {"version": "3.2.4", "resolved": "http://*************:8888/symbol-tree/-/symbol-tree-3.2.4.tgz", "integrity": "sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==", "optional": true}, "table": {"version": "6.8.1", "resolved": "http://*************:8888/table/-/table-6.8.1.tgz", "integrity": "sha512-Y4X9zqrCftUhMeH2EptSSERdVKt/nEdijTOacGD/97EKjhQ/Qs8RTlEGABSJNNN8lac9kheH+af7yAkEWlgneA==", "requires": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "dependencies": {"ajv": {"version": "8.11.2", "resolved": "http://*************:8888/ajv/-/ajv-8.11.2.tgz", "integrity": "sha512-E4bfmKAhGiSTvMfL1Myyycaub+cUEU2/IvpylXkUu7CHBkBj1f/ikdzbD7YQ6FKUbixDxeYvB/xY4fvyroDlQg==", "requires": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}}, "json-schema-traverse": {"version": "1.0.0", "resolved": "http://*************:8888/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz", "integrity": "sha1-rnvLNlard6c7pcSb9lTzjmtoYOI="}}}, "text-table": {"version": "0.2.0", "resolved": "http://*************:8888/text-table/-/text-table-0.2.0.tgz", "integrity": "sha1-f17oI66AUgfACvLfSoTsP8+lcLQ="}, "tough-cookie": {"version": "2.5.0", "resolved": "http://*************:8888/tough-cookie/-/tough-cookie-2.5.0.tgz", "integrity": "sha512-nlLsUzgm1kfLXSXfRZMc1KLAugd4hqJHDTvc2hDIwS3mZAfMEuMbc03SujMF+GEcpaX/qboeycw6iO8JwVv2+g==", "optional": true, "requires": {"psl": "^1.1.28", "punycode": "^2.1.1"}}, "tr46": {"version": "0.0.3", "resolved": "http://*************:8888/tr46/-/tr46-0.0.3.tgz", "integrity": "sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=", "optional": true}, "tslib": {"version": "1.14.1", "resolved": "http://*************:8888/tslib/-/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA="}, "tslint": {"version": "6.1.3", "resolved": "http://*************:8888/tslint/-/tslint-6.1.3.tgz", "integrity": "sha512-IbR4nkT96EQOvKE2PW/djGz8iGNeJ4rF2mBfiYaR/nvUWYKJhLwimoJKgjIFEIDibBtOevj7BqCRL4oHeWWUCg==", "requires": {"@babel/code-frame": "^7.0.0", "builtin-modules": "^1.1.1", "chalk": "^2.3.0", "commander": "^2.12.1", "diff": "^4.0.1", "glob": "^7.1.1", "js-yaml": "^3.13.1", "minimatch": "^3.0.4", "mkdirp": "^0.5.3", "resolve": "^1.3.2", "semver": "^5.3.0", "tslib": "^1.13.0", "tsutils": "^2.29.0"}, "dependencies": {"ansi-styles": {"version": "3.2.1", "resolved": "http://*************:8888/ansi-styles/-/ansi-styles-3.2.1.tgz", "integrity": "sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==", "requires": {"color-convert": "^1.9.0"}}, "chalk": {"version": "2.4.2", "resolved": "http://*************:8888/chalk/-/chalk-2.4.2.tgz", "integrity": "sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==", "requires": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}}, "color-convert": {"version": "1.9.3", "resolved": "http://*************:8888/color-convert/-/color-convert-1.9.3.tgz", "integrity": "sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==", "requires": {"color-name": "1.1.3"}}, "color-name": {"version": "1.1.3", "resolved": "http://*************:8888/color-name/-/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU="}, "escape-string-regexp": {"version": "1.0.5", "resolved": "http://*************:8888/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ="}, "has-flag": {"version": "3.0.0", "resolved": "http://*************:8888/has-flag/-/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0="}, "semver": {"version": "5.7.1", "resolved": "http://*************:8888/semver/-/semver-5.7.1.tgz", "integrity": "sha512-sauaDf/PZdVgrLTNYHRtpXa1iRiKcaebiKQ1BJdpQlWH2lCvexQdX55snPFyK7QzpudqbCI0qXFfOasHdyNDGQ=="}, "supports-color": {"version": "5.5.0", "resolved": "http://*************:8888/supports-color/-/supports-color-5.5.0.tgz", "integrity": "sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==", "requires": {"has-flag": "^3.0.0"}}}}, "tsutils": {"version": "2.29.0", "resolved": "http://*************:8888/tsutils/-/tsutils-2.29.0.tgz", "integrity": "sha512-g5JVHCIJwzfISaXpXE1qvNalca5Jwob6FjI4AoPlqMusJ6ftFE7IkkFoMhVLRgK+4Kx3gkzb8UZK5t5yTTvEmA==", "requires": {"tslib": "^1.8.1"}}, "tunnel-agent": {"version": "0.6.0", "resolved": "http://*************:8888/tunnel-agent/-/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "optional": true, "requires": {"safe-buffer": "^5.0.1"}}, "tweetnacl": {"version": "0.14.5", "resolved": "http://*************:8888/tweetnacl/-/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "optional": true}, "type-check": {"version": "0.4.0", "resolved": "http://*************:8888/type-check/-/type-check-0.4.0.tgz", "integrity": "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==", "requires": {"prelude-ls": "^1.2.1"}}, "type-fest": {"version": "0.20.2", "resolved": "http://*************:8888/type-fest/-/type-fest-0.20.2.tgz", "integrity": "sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ="}, "uri-js": {"version": "4.4.1", "resolved": "http://*************:8888/uri-js/-/uri-js-4.4.1.tgz", "integrity": "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==", "requires": {"punycode": "^2.1.0"}}, "uuid": {"version": "3.4.0", "resolved": "http://*************:8888/uuid/-/uuid-3.4.0.tgz", "integrity": "sha512-HjSDRw6gZE5JMggctHBcjVak08+KEVhSIiDzFnT9S9aegmp85S/bReBVTb4QTFaRNptJ9kuYaNhnbNEOkbKb/A==", "optional": true}, "v8-compile-cache": {"version": "2.3.0", "resolved": "http://*************:8888/v8-compile-cache/-/v8-compile-cache-2.3.0.tgz", "integrity": "sha1-LeGWGMZtwkfc+2+ZM4A12CRaLO4="}, "verror": {"version": "1.10.0", "resolved": "http://*************:8888/verror/-/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "optional": true, "requires": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}, "dependencies": {"core-util-is": {"version": "1.0.2", "resolved": "http://*************:8888/core-util-is/-/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "optional": true}}}, "webidl-conversions": {"version": "2.0.1", "resolved": "http://*************:8888/webidl-conversions/-/webidl-conversions-2.0.1.tgz", "integrity": "sha1-O/glj30xjHRDw28uFpQCoaZwNQY=", "optional": true}, "whatwg-url-compat": {"version": "0.6.5", "resolved": "http://*************:8888/whatwg-url-compat/-/whatwg-url-compat-0.6.5.tgz", "integrity": "sha1-AImBEa9om7CXVBzVpFymyHmERb8=", "optional": true, "requires": {"tr46": "~0.0.1"}}, "which": {"version": "2.0.2", "resolved": "http://*************:8888/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "requires": {"isexe": "^2.0.0"}}, "word-wrap": {"version": "1.2.3", "resolved": "http://*************:8888/word-wrap/-/word-wrap-1.2.3.tgz", "integrity": "sha512-Hz/mrNwitNRh/HUAtM/VT/5VH+ygD6DV7mYKZAtHOrbs8U7lvPS6xf7EJKMF0uW1KJCl0H701g3ZGus+muE5vQ=="}, "wrappy": {"version": "1.0.2", "resolved": "http://*************:8888/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8="}, "xml-name-validator": {"version": "2.0.1", "resolved": "http://*************:8888/xml-name-validator/-/xml-name-validator-2.0.1.tgz", "integrity": "sha1-TYuPHszTQZqjYgYb7O9RXh5VljU=", "optional": true}, "yallist": {"version": "4.0.0", "resolved": "http://*************:8888/yallist/-/yallist-4.0.0.tgz", "integrity": "sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A=="}}}