## 功能列表(P0为最高优先级，P0与P1是必做功能)
### 1.0版本功能（8.25 ~ 10.25） 
- [x] 舞台&画布大小和安全区显示逻辑 P0
- [x] 编辑器舞台背景初始化和更新相关逻辑 P0
- [x] 舞台组件初始化 P0
- [x] 组件基本属性修改 P0
- [x] 添加和删除组件 P0
- [x] 组的合并与分解 P0
- [x] 选中group内子节点逻辑 P0
- [x] group的属性修改逻辑 P0
- [x] 对象选中状态相关逻辑 P0
- [x] 监听vue对象选中状态并处理 P0
- [x] 多选对象和属性修改逻辑 P0
- [x] 剪切、复制和粘贴节点 P0
- [x] 撤销和前进 P0
- [x] 坐标转换逻辑：坐标系转换为左上角（vue显示层处理） P2
- [x] 鼠标右键下拉菜单逻辑 P1
- [x] 辅助线逻辑 P2
- [x] 刻度线辅助对齐功能 p2
- [x] 组件显示碰撞区域 p2
- [x] 数值精度控制：小数点后一位 P1
- [x] 组件editable P1
- [x] 组件左上角tag标签显示逻辑 P1
- [x] 动画编辑模块 P0
  - [x] 动画编辑状态与原有状态切换
  - [x] 动画数据结构设计
  - [x] 动画管理器，管理动画 [动画片段格式](#动画片段格式)
  - [x] tween动画解析器 [tween数据格式](#tween数据格式)
  - [x] 动画UI显示层结构设计
  - [x] 动作库实现：[动作分类](#动作分类)
    - [x] 飞入
    - [x] 飞出
    - [x] 出现
    - [x] 消失
    - [x] 旋转
    - [x] 放大\缩小
    - [x] 直线移动
    - [x] 曲线移动(贝塞尔曲线)
    - [x] 自定义曲线
    - [x] 自定义路径
  - [x] 动画公共接口封装
  - [x] 动画效果预览功能
- [x] 方向键移动显示对象 P2
- [x] 组件拉伸时锁定比例功能 P2
- [x] 基础动作库新增（内容待产品提供）P1
  - [x] 进入退出增加透明度
  - [x] 强调增加闪烁和抖动

### 1.1版本功能（11.25 ~ 12.2）
- [x] 全选调整层级 P0
- [x] 属性锁定功能 P0

### 1.2版本功能（10.27 ~ 12.18） 
- [x] 动画编辑器重构及逻辑优化 P1
- [x] 编辑器增加生命周期逻辑（开始、初始化完成、进入编辑状态等）P0
- [x] Spine动效组件支持 P0
- [x] Spine动画编辑支持 P0
- [x] Group组件剪切功能bug P2
- [x] 动画编辑器路径全部显示可编辑 P1

### 待定
- [x] 富文本框 P3
  - [x] 可编辑文本框 
  - [x] 富文本组件 
- [x] 不可删除复选框（组件右上角） P0
- [ ] 组件右上角快捷替换图片按钮（考虑到后续拓展，可能有其他功能，比如编辑多边形等） P2
- [x] 动画编辑器中路径编辑增加坐标显示 P2
- [ ] 背景图片超过2048*2048尺寸限制后切割拼接处理 P3
- [ ] 组件属性重置 P3
- [ ] 多选组件的排列 P3
- [ ] 图片支持九宫格拉伸设置 P3
- [ ] 富文本组件增加填空元素功能支持 P3
- [ ] 表格组件 P3
- [x] 形状组件 P3