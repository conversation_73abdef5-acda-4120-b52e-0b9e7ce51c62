# interactive-question-editor

## 介绍

cocos题目编辑器

## 开发 & 部署

### 开发

#### 使用yarn

统一只使用 `yarn`
  - `yarn` 安装依赖
  - `yarn serve` 启动项目
  - 使用 `yarn commit` 替代 `git commit`

#### 研发流程

采用 git-flow 的工作流程，但存在多个 develop 分支，便于多项目并行开发

- master 为最近一次发布的代码
- dev/* 为项目的主开发分支
- feat/* 为特性开发分支
- release/* 为 release 分支
- hotfix/* 为修复线上 bug 分支

#### 本地开发URL

- 需要使用跨域浏览器或携带cookie的插件，解决接口请求时cookie携带问题。

### 部署
容器化部署
#### 测试环境

http://ship.suanshubang.com/#/serviceunit/?env_id=1&env_uuid=1

（需要联系**胡祖立**老师开通**基准环境**权限）

##### 测试环境地址

测试环境访问地址为：  
http://jiaoxue-tihu-${环境名称}-cc.suanshubang.cc/interactive-question-editor/

base 环境为基准环境，每日凌晨会同步 master 分支最新镜像  
base 环境访问地址：  
http://jiaoxue-tihu-base-cc.suanshubang.cc/interactive-question-editor/

#### 线上环境

http://op.zuoyebang.cc/static/odin/index.html#/devops/deploy/module

##### 线上地址

https://jiaoxue-tihu.zuoyebang.cc/interactive-question-editor/

### localStorage中__tips__的作用
- ~~获取bundleURL, 值为1时获取未启用的包，否则获取已启用的包~~
- cocos的脚本资源，值为1时使用线上资源，否则使用cdn中的资源



## 预览--更新于2023.04.18

### 功能
- 题目渲染（可正常作答，有正误反馈）
- 解析
- 答案
- 预览二维码
- 调试工具

### 预览显示调试模式的条件和步骤
- 只有在参数mode不是mobile的情况下才可调试
- 在localStorage中添加debugger=1
- 点击右侧文字【点我调试】，可打开调试工具箱，点击按钮进行sdk的测试和调试

### 依赖程序包的逻辑
- 默认从包管理平台取已启用的程序包。
- 不在启用tips方案。如果上面的方案不满足需求，可在ocalStorage中添加对应的程序包名称和url. example: qte: 
http://zyb-jiaoyan.bj.bcebos.com/cocos/dir/6ddd7d6b5686c5b64cc3cb35feb1b47e
### 预览关键字段说明
- answer<undefined | '1'>。有该字段时，预览页面的题目数据是父窗口传递的。无该字段时，从接口读取数据；无二维码
- mode< 'pc' | 'mobile' | 'screenshot'>, 默认pc. mobile/pc影响布局和功能，mobile时，无tab/解析/参考答案，只有题目的渲染。 值为pc时，有tab/解析/参考答案
- id，三种id.

### 预览业务接入现状
- 编辑器获取参考答案-依赖预览作答后获取参考答案的api. 参数：?answer=1&mode=mobile。功能：渲染题目和获取参考答案
- 编辑器预览/课件单题预览。 参数：previewId。功能：渲染题目/解析/答案/预览二维码/调试工具
- 课件整体预览。参数：?mode=mobile&snapId=21909. 功能：渲染题目
- 堂堂测预览。