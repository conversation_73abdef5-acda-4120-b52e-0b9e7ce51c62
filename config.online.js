/* eslint-disable @typescript-eslint/no-var-requires */
// const ExtraPreloadPlugin = require("../plugins/extra-preload-plugin.js");
const ReplaceCssCdnPlugin = require("./plugins/replace-css-cdn-plugin.js");
const jsNames = require("./public/cocos/md5.json");
const OverviewBuildCleanPlugin = require("./OverviewBuildCleanPlugin.js");
const CopyCocosAssets = require('qte-render/public/plugins/copy-cocos-assets');
const path = require('path');
const InsertCocosScripts = require('qte-render/public/plugins/insert-cocos-scripts')
const LoadGameScripts = require('./plugins/load-game-scripts.js')
const InsertSwScripts = require('./plugins/insert-sw-scripts.js')
const cocosPublicPath = process.env.VUE_APP_CDN_PATH ? `${process.env.VUE_APP_CDN_PATH}/interactive-question-editor/cocos/` : `cocos/`;
const jsList = jsNames.map(name => cocosPublicPath + name);
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const TerserPlugin = require('terser-webpack-plugin')
const overview = {
  title: "表单组件总览",
  entry: "src/pages/overview/index.online.ts",
  template: "public/index.html",
  chunks: ['chunk-element-ui', 'chunk-vue', 'overview', 'chunk-common', 'chunk-vendors']
};
let pages = {
  index: {
    title: "题目编辑器",
    entry: "src/pages/index/index.online.ts",
    needLoading: true,
    template: "public/index.html",
    chunks: ['chunk-element-ui', 'chunk-vue', 'index', 'chunk-common', 'chunk-vendors']
  },
  preview: {
    title: "预览",
    entry: "src/pages/preview/index.ts",
    template: "public/preview.html",
    chunks: ['chunk-element-ui', 'chunk-vue', 'preview', 'chunk-common', 'chunk-vendors']
  },
  group: {
    title: "题组编辑器",
    entry: "src/pages/group/index.ts",
    needLoading: true,
    template: "public/group.html",
    chunks: ['chunk-element-ui', 'chunk-vue', 'group', 'chunk-common', 'chunk-vendors']
  },
};
const getPublicPath = () => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return "../";
  } else if (process.env.NODE_ENV === "production") {
    return `${process.env.VUE_APP_CDN_PATH}/interactive-question-editor`;
  } else {
    return undefined;
  }
};
// 总览页打包
if (process.env.VUE_APP_IS_OVERVIEW) {
  pages = { overview };
  console.log(":::总览页打包:::");
} else {
  pages.overview = overview;
}

console.log("process.env.VUE_APP_CDN_PATH", process.env.VUE_APP_CDN_PATH);

module.exports = {
  pages,
  publicPath: getPublicPath(),
  runtimeCompiler: true,
  crossorigin: 'anonymous',
  // lintOnSave: false,
  pluginOptions: {
    cdnRetry: process.env.VUE_APP_CDN_RETRY ? {
      types: ['js', 'css', 'img', 'audio', 'video'],
      module: 'interactive-question-editor', // 替换成真实模块名
      debug: true, // 临时调试，线上稳定后可删除
      switchControl: false // 是否开启水星开关(默认不开启)
    } : null
  },
  configureWebpack: config => {
    const productionGzipExtensions = ['html', 'js', 'css']
    config.plugins.push(
      new CompressionWebpackPlugin({
        filename: '[path].gz[query]',
        algorithm: 'gzip',
        test: new RegExp(
          '\\.(' + productionGzipExtensions.join('|') + ')$'
        ),
        threshold: 10240, // 只有大小大于该值的资源会被处理 10240
        minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
        deleteOriginalAssets: false // 删除原文件
      })
    )
    config.plugins.push(new ReplaceCssCdnPlugin({
      cdn: process.env.VUE_APP_CDN_PATH,
    }));
    config.plugins.push(new InsertCocosScripts({
      dirname: __dirname,
      simple: true,
      excludeHtmlNames: ['overview.html', 'index.html', 'group.html'],
    }));
    config.plugins.push(new LoadGameScripts({
      jsList,
      excludeHtmlNames: ['overview.html', 'preview.html', 'group.html'],
    }));
    config.plugins.push(new InsertSwScripts({
      jsList,
      excludeHtmlNames: ['overview.html', 'group.html'],
    }));
    if (process.env.use_analyzer) {
      console.log("analyzer");
      const analyzer = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
      config.plugins.push(new analyzer());
    }
    config.plugins.push(new CopyCocosAssets({
      dirname: __dirname,
      target: path.resolve(__dirname, 'public')
    }));
    // if (process.env.NODE_ENV === "production") {
    //   config.plugins.push(
    //     new ExtraPreloadPlugin({
    //       rel: "preload",
    //       includeHtmlNames: ["index.html"],
    //       list: jsList,
    //       as: "script",
    //       crossorigin: "anonymous",
    //     }),
    //   );
    // }
    if (process.env.VUE_APP_IS_OVERVIEW) {
      config.plugins.push(new OverviewBuildCleanPlugin());
    }
  },
  chainWebpack: config => {
    // 移除 prefetch 插件
    if (process.env.NODE_ENV === 'production') {
      config.output
        .filename("js/[name].[chunkhash:9].js")
        .chunkFilename("js/[name].[chunkhash:9].js")
      // 生产环境开启压缩
      config.plugin('uglify').use(TerserPlugin, [{
        terserOptions: {
          compress: {
            // eslint-disable-next-line @typescript-eslint/camelcase
            keep_fnames: true,
            // eslint-disable-next-line @typescript-eslint/camelcase
            drop_debugger: true,
            // eslint-disable-next-line @typescript-eslint/camelcase
            // pure_funcs: ['console.log']
          }
        },
        sourceMap: false,
        parallel: true
      }])
    } else {
      config.output
        .filename("js/[name].[hash:9].js")
        .chunkFilename("js/[name].[hash:9].js")
    }
    config.plugins.delete("prefetch-index");
    config.optimization.merge({
      mergeDuplicateChunks: true, // 合并相同的 chunk
      splitChunks: {
        automaticNameDelimiter: '-',
        chunks: 'async',
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
            minChunks: 2,
            chunks: 'initial',
            enforce: true,
          },
          common: {
            name: `chunk-common`,
            minChunks: 2,
            priority: -20,
            chunks: 'initial',
            reuseExistingChunk: true,
            enforce: true
          },
          elementUI: {
            chunks: 'initial',
            name: 'chunk-element-ui',
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
            priority: 10,
            minChunks: 1,
            reuseExistingChunk: true,
            enforce: true
          },
          zybShapeEditor: {
            chunks: 'async',
            name: 'chunk-zyb-shape-editor',
            test: /[\\/]node_modules[\\/]_?zyb-shape-editor(.*)/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          },
          xnEditor: {
            chunks: 'async',
            name: 'chunk-xn-editor',
            test: /[\\/]EditArea[\\/]xueneng[\\/](.*)/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          },
          schemaForm: {
            chunks: 'async',
            name: 'chunk-schema-form',
            test: /[\\/]EditArea[\\/]SchemaToForm[\\/](.*)/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          },
          tinymce: {
            chunks: 'async',
            name: 'chunk-tinymce',
            test: /[\\/]node_modules[\\/]_?tinymce(.*)/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          }
        }
      }
    })
  },
  devServer: {
    disableHostCheck: true,
    historyApiFallback: true,
    proxy: {
      "^/asr": {
        target: "https://asr.zuoyebang.cc/",
        ws: true,
        pathRewrite: { "^/asr": "" },
        changeOrigin: true,
      },
      "^/hera": {
        target: "https://qu-base-cc.suanshubang.cc",
        changeOrigin: true,
        secure: false,
        headers: { 'Access-Control-Allow-Origin': '*' },
      },
      "^/artcw": {
        target: "https://qu-base-cc.suanshubang.cc",
        changeOrigin: true,
        secure: false,
        headers: { 'Access-Control-Allow-Origin': '*' },
      },
      "^/materiallib": {
        target: "https://qu-base-cc.suanshubang.cc",
        changeOrigin: true,
        secure: false,
        headers: { 'Access-Control-Allow-Origin': '*' },
      }
    },
  },
};
