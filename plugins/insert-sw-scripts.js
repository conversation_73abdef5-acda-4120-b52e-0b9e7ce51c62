/**
 * 用于在webpack编译时自动往html模板里插入js和css代码
 */
class InsertSwScripts {
  constructor(options) {
    this.options = options || { jsList: [] }
  }

  apply(compiler) {
    const headerNode = '<head>'
    // 定义要插入的script字符串
    const scriptCode = `<script type="text/javascript">
      console.log("准备执行sw注册或注销逻辑");
      if ("serviceWorker" in navigator) {
        // 注销Service Worker
        // 如果需要去掉pwa 将window.edswUnregister设置为1
        // window.edswUnregister = '1';
        window.edswUnregister = localStorage.getItem("edswUnregister") || window.edswUnregister;
        if (window.edswUnregister) {
          console.log("即将注销sw");
          navigator.serviceWorker.getRegistrations().then(registrations => {
            registrations.forEach(sw => {
              sw.unregister()
            });
          });
          // 清除缓存
          caches.keys().then(t => {
            return Promise.all(
              t.map(n => {
                return caches.delete(n);
              }),
            );
          })
        } else {
         // 页面加载后再注册sw
          window.addEventListener('load', function () {
            console.log("即将注册sw");
            const timeStamp = Date.now();
            navigator.serviceWorker
              .register('./service-worker.js?v=' + timeStamp)
              .then(() => {
                console.log("sw 注册成功了");
              })
              .catch(err => {
                console.log("sw 注册失败了", err);
              });
          })
          
        }
      }
    </script>`


    const skip = data => {
      const htmlFilename = data.plugin.options.filename;
      const exclude = this.options.excludeHtmlNames;
      return exclude && exclude.includes(htmlFilename)
    };

    // 编译时注入
    compiler.hooks.compilation.tap(
      'compilation',
      compilation => {
        compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
          'htmlWebpackPluginAfterHtmlProcessing',
          htmlPluginData => {
            if (skip(htmlPluginData)) {
              return;
            }
            let htmlStr = htmlPluginData.html.toString()

            // 合并js
            if (scriptCode) {
              const replaceStr = headerNode + scriptCode
              htmlStr = htmlStr.replace(new RegExp(headerNode), replaceStr)
            }

            htmlPluginData.html = htmlStr
          })
      })
  }
}

module.exports = InsertSwScripts