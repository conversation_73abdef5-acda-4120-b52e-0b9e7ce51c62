// const HtmlWebpackPlugin = require("html-webpack-plugin");

class ExtraPreloadPlugin {
  constructor(options) {
    this.resourceHints = null;
    this.options = Object.assign({}, {}, options);
    this.webpackMajorVersion = 4;
  }
  
  generateLinks(compilation, htmlPluginData) {
    const options = this.options;
    const links = [];
    for (const href of options.list) {
      const attributes = {
        href,
        rel: options.rel,
      };

      if (options.rel === "preload") {
        if (attributes.as === "font") {
          attributes.crossorigin = "";
        }
        if(options.crossorigin){
          attributes.crossorigin = "anonymous"
        }
        if(options.as){
          attributes.as = options.as
        }
      }
      
      links.push({
        tagName: "link",
        attributes,
      });
    }
    
    this.resourceHints = links;
    return htmlPluginData;
  }
  
  apply(compiler) {
    const skip = data => {
      const htmlFilename = data.plugin.options.filename;
      const exclude = this.options.excludeHtmlNames;
      const include = this.options.includeHtmlNames;
      return (
        (include && !(include.includes(htmlFilename))) ||
        (exclude && exclude.includes(htmlFilename))
      );
    };
    compiler.hooks.compilation.tap(
      "ExtraPreloadPlugin",
      compilation => {
        if (compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing) {
          compilation.hooks.htmlWebpackPluginBeforeHtmlProcessing.tap(
            "ExtraPreloadPlugin",
            (htmlPluginData) => {
              if (skip(htmlPluginData)) {
                return;
              }
              this.generateLinks(compilation, htmlPluginData);
              
            },
          );
        }
        compilation.hooks.htmlWebpackPluginAlterAssetTags.tap(
          "ExtraPreloadPlugin",
          (htmlPluginData) => {
            if (skip(htmlPluginData)) {
              return;
            }
            if (this.resourceHints) {
              htmlPluginData.head = [
                ...htmlPluginData.head,
                ...this.resourceHints,
              ];
            }
            
            return htmlPluginData;
          },
        );
      },
    );
  }
}

module.exports = ExtraPreloadPlugin;
