/**
 * 用于在webpack编译时自动往html模板里插入js和css代码
 */
class LoadGameScripts {
  /**
    * @param { jsList: sting[] } options jsList: 脚本的文件名
    * @description 支持参考答案时新增simple字段，解决样式污染和不加载非必要资源问题。默认0，兼容老版本
    */
  constructor(options) {
    this.options = options || { jsList: [] }
  }

  apply(compiler) {
    const jsList = this.options.jsList;
    const headerNode = '<head>'

    // 定义要插入的script字符串
    const scriptCode = `<script type="text/javascript">
        const jsList = "${jsList}";
        function loadJs(inputUrl) {
      return new Promise((resolve, reject) => {
        const url = inputUrl;
        const script = document.createElement("script");
        script.charset = "utf-8";
        script.crossOrigin = "anonymous";
        document.head.appendChild(script);
        script.onload = function() {
          resolve();
        };
        script.onerror = function(e) {
          console.log(JSON.stringify(e));
          reject(e);
        };
        // console.log("shark: 加载框架js：", url, window.cc);
        script.src = url;
        document.getElementsByTagName("head")[0].appendChild(script);
      });
    }

    async function loadGameJs (jsList) {
      window.startLoadGameJsTime = Date.now();
      for (let i = 0; i < jsList.length; i++) {
        await loadJs(jsList[i]);
      }
        window.endLoadGameJsTime = Date.now();
        // 获取到canvas后再执行window.boot() 否则会报错导致页面卡顿  
        const loop = () => {
          const canvasDestructor = requestAnimationFrame(loop);
          const canvas = document.getElementById('GameCanvas');
          // 判断app是否有子元素
          if (canvas) {
            // console.log('has canvas will boot');
            window.boot();
            cancelAnimationFrame(canvasDestructor);
          }
        };
        loop();
    }

    window.loadJsPromise = loadGameJs(jsList.split(','));
    
    </script>`


    const skip = data => {
      const htmlFilename = data.plugin.options.filename;
      console.log('htmlFilename', htmlFilename);
      const exclude = this.options.excludeHtmlNames;
      return exclude && exclude.includes(htmlFilename)
    };

    // 编译时注入
    compiler.hooks.compilation.tap(
      'compilation',
      compilation => {
        compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
          'htmlWebpackPluginAfterHtmlProcessing',
          htmlPluginData => {
            if (skip(htmlPluginData)) {
              return;
            }
            let htmlStr = htmlPluginData.html.toString()

            // 合并js
            if (scriptCode) {
              const replaceStr = headerNode + scriptCode
              htmlStr = htmlStr.replace(new RegExp(headerNode), replaceStr)
            }

            htmlPluginData.html = htmlStr
          })
      })
  }
}

module.exports = LoadGameScripts