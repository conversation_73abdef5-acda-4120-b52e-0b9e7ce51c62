/* eslint-disable @typescript-eslint/no-var-requires */
/**
 * 用于在webpack编译时自动往html模板里插入js和css代码
 */
const type = process.env.VUE_APP_USER;
class InsertScriptPlugin {
  constructor(options) {
    this.options = options || {}
  }

  apply(compiler) {
    const jsNode = '</body>'

    // 定义要插入的script字符串
    let scriptCode = ''
    scriptCode += `<script>window.user="${type}"</script>`

    // 编译时注入
    compiler.hooks.compilation.tap(
      'compilation',
      compilation => {
        compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
          'htmlWebpackPluginAfterHtmlProcessing',
          htmlPluginData => {
            let htmlStr = htmlPluginData.html.toString()

            // 合并js
            if (scriptCode) {
              const replaceStr = scriptCode + jsNode
              htmlStr = htmlStr.replace(new RegExp(jsNode), replaceStr)
            }

            htmlPluginData.html = htmlStr
          })
      })
  }
}

module.exports = InsertScriptPlugin