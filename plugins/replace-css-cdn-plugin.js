/**
 * 用于在webpack编译时替换css的cdn路径
 * 为了解决在富文本截图时，获取cdn的css文件报错，导致富文本中图片无法成功截图的问题
 */
class ReplaceCssCdnPlugin {
  constructor(options) {
    this.options = options || {}
  }

  apply(compiler) {

    // 编译时注入
    compiler.hooks.compilation.tap(
      'compilation',
      compilation => {
        compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
          'htmlWebpackPluginAfterHtmlProcessing',
          htmlPluginData => {
            let htmlStr = htmlPluginData.html.toString()
            const reg = /(href="\/\/jy-fe.cdnjtzy.com\/interactive-question-editor\/css\/)(.*?)"/g
            htmlStr = htmlStr.replace(reg, 'href=/interactive-question-editor/css/$2')
            htmlPluginData.html = htmlStr
          })
      })
  }
}

module.exports = ReplaceCssCdnPlugin