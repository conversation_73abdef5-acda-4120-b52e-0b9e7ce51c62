const fs = require("fs");
const keepFiles = ["overview.html", "img", "js", "css", "favicon.ico", "static", "fonts"];

function delFile(fileUrl) {
  if (!fs.existsSync(fileUrl)) return;
  if (fs.statSync(fileUrl).isDirectory()) {
    const files = fs.readdirSync(fileUrl);
    const len = files.length;
    let removeNumber = 0;
    if (len > 0) {
      files.forEach(function(file) {
        removeNumber++;
        const url = fileUrl + "/" + file;
        if (fs.statSync(url).isDirectory()) {
          delFile(url, true);
        } else {
          fs.unlinkSync(url);
        }
      });
      if (len === removeNumber) {
        fs.rmdirSync(fileUrl);
      }
    } else if (len == 0) {
      fs.rmdirSync(fileUrl);
    }
  } else {
    fs.unlinkSync(fileUrl);
  }
}

class OverviewBuildCleanPlugin {
  constructor() {}
  apply(compiler) {
    compiler.plugin("done", () => {
      fs.readdir("./dist", (err, fileList) => {
        fileList.forEach(fileName => {
          if (keepFiles.includes(fileName)) return;
          delFile(`./dist/${fileName}`);
        });
      });
    });
  }
}

module.exports = OverviewBuildCleanPlugin;
