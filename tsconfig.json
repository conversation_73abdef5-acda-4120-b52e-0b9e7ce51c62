{"compilerOptions": {"allowJs": true, "target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "skipLibCheck": true, "suppressImplicitAnyIndexErrors": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "sourceMap": true, "baseUrl": ".", "types": ["webpack-env"], "paths": {"@/*": ["src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.js", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "public", "cocos", "src/assets/icon/**/*", "src/common/utils/hotkeys.js", "src/pages/preview/boot.js", "src/assets/cocosicon/**", "src/components/form-question/q-editor/*"]}