/*
 * @FilePath     : /public/tinymce/myicons.js
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
const myicons = new Map([
  ['align-center', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">       <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-374.000000, -247.000000)">            <g id="juzhongduiqi" transform="translate(374.000000, 247.000000)">                <rect id="矩形备份-15" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/alignment-middlet" transform="translate(4.000000, 4.000000)" fill="#525354">                    <path d="M1,3.02678571 L15,3.02678571 L15,4.03571429 L1,4.03571429 L1,3.02678571 Z M1,12.1071429 L15,12.1071429 L15,13.1160714 L1,13.1160714 L1,12.1071429 Z M5,6.05357143 L11,6.05357143 L11,7.0625 L5,7.0625 L5,6.05357143 Z M5,9.08035714 L11,9.08035714 L11,10.0892857 L5,10.0892857 L5,9.08035714 Z" id="Combined-Shape"></path>                </g>            </g>        </g>    </g></svg>`],
  ['align-justify', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">       <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-438.000000, -247.000000)">            <g id="liangduanduiqi" transform="translate(438.000000, 247.000000)">                <rect id="矩形备份-13" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/alignment-right" transform="translate(4.000000, 4.000000)" fill="#525354">                    <path d="M1,3.02678571 L15,3.02678571 L15,4.03571429 L1,4.03571429 L1,3.02678571 Z M1,12.1071429 L15,12.1071429 L15,13.1160714 L1,13.1160714 L1,12.1071429 Z M1,6.05357143 L15,6.05357143 L15,7.0625 L1,7.0625 L1,6.05357143 Z M1,9.08035714 L15,9.08035714 L15,10.0892857 L1,10.0892857 L1,9.08035714 Z" id="Combined-Shape"></path>                </g>            </g>        </g>    </g></svg>`],
  ['align-left', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">       <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-341.000000, -247.000000)">            <g id="zuoduiqi" transform="translate(341.000000, 247.000000)">                <rect id="矩形备份-16" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/alignment-left" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M1.0625,3.02678571 L14.9375,3.02678571 L14.9375,4.03571429 L1.0625,4.03571429 L1.0625,3.02678571 Z M1.0625,12.1071429 L14.9375,12.1071429 L14.9375,13.1160714 L1.0625,13.1160714 L1.0625,12.1071429 Z M1.0625,6.05357143 L9.5625,6.05357143 L9.5625,7.0625 L1.0625,7.0625 L1.0625,6.05357143 Z M1.0625,9.08035714 L9.5625,9.08035714 L9.5625,10.0892857 L1.0625,10.0892857 L1.0625,9.08035714 Z" id="Combined-Shape" fill="#525354"></path>                </g>            </g>        </g>    </g></svg>`],
  ['align-right', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-406.000000, -247.000000)">            <g id="juyouduiqi" transform="translate(406.000000, 247.000000)">                <rect id="矩形备份-14" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/alignment-right" transform="translate(4.000000, 4.000000)" fill="#525354">                    <path d="M1,3.02678571 L15,3.02678571 L15,4.03571429 L1,4.03571429 L1,3.02678571 Z M1,12.1071429 L15,12.1071429 L15,13.1160714 L1,13.1160714 L1,12.1071429 Z M7,6.05357143 L15,6.05357143 L15,7.0625 L7,7.0625 L7,6.05357143 Z M7,9.08035714 L15,9.08035714 L15,10.0892857 L7,10.0892857 L7,9.08035714 Z" id="Combined-Shape"></path>                </g>            </g>        </g>    </g></svg>`],
  ['highlight-bg-color', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-420.000000, -278.000000)">            <g id="编组-2" transform="translate(420.000000, 278.000000)">                <rect id="矩形" fill="#D8D8D8" opacity="0" x="4" y="4" width="16" height="16"></rect>                <g id="编组" fill="#525354">                    <rect id="矩形备份-17" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>  <path id="tox-icon-highlight-bg-color__color" d="M3 18h18v3H3z"/>                  <g id="w_画笔" transform="translate(6.000000, 4.000000)" fill-rule="nonzero">                        <rect id="矩形" opacity="0" x="0" y="0" width="12" height="12"></rect>                        <path d="M3.58701169,8.03523063 C3.03514792,8.99430469 3.48059168,12.2283207 3.59917398,12.0240029 C4.55543379,10.3620144 6.02402995,11.6092681 7.04870263,10.0402901 C7.60056641,9.08121601 7.27370494,7.85530894 6.31744513,7.30334581 C5.36118531,6.74833316 4.13887546,7.07615656 3.58701169,8.03523063 Z" id="路径"></path>                        <path d="M6.65742841,6.75651487 C6.13218701,6.4540062 5.52378239,6.42928194 5,6.63289354 L8.88240933,1.23136864 C9.06186681,0.991397821 9.40619172,0.928859972 9.68048445,1.08738615 C9.94164615,1.23718611 10.0627435,1.54260351 9.96790822,1.8116617 L7.56930583,8 C7.45696254,7.4953341 7.1418177,7.03575363 6.65742841,6.75651487 L6.65742841,6.75651487 Z" id="路径"></path>                    </g>                </g>            </g>        </g>    </g></svg>`],
  ['bold', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-465.000000, -278.000000)">            <g id="jiacu" transform="translate(465.000000, 278.000000)">                <rect id="矩形备份-9" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="icon/format/font-bold" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M10.903125,7.521875 C11.428125,6.975 11.75,6.2359375 11.75,5.4234375 L11.75,5.2640625 C11.75,3.5828125 10.3734375,2.21875 8.6765625,2.21875 L4.053125,2.21875 C3.8171875,2.21875 3.625,2.4109375 3.625,2.646875 L3.625,13.2578125 C3.625,13.5125 3.83125,13.71875 4.0859375,13.71875 L9.065625,13.71875 C10.89375,13.71875 12.375,12.246875 12.375,10.4296875 L12.375,10.2578125 C12.375,9.1171875 11.790625,8.1125 10.903125,7.521875 Z M5.125,3.71875 L8.6359375,3.71875 C9.528125,3.71875 10.25,4.4125 10.25,5.2703125 L10.25,5.41875 C10.25,6.275 9.5265625,6.9703125 8.6359375,6.9703125 L5.125,6.9703125 L5.125,3.71875 Z M10.853125,10.428125 C10.853125,11.4109375 10.0453125,12.2078125 9.0484375,12.2078125 L5.125,12.2078125 L5.125,8.4796875 L9.0484375,8.4796875 C10.0453125,8.4796875 10.853125,9.2765625 10.853125,10.259375 L10.853125,10.428125 Z" id="形状" fill="#525354" fill-rule="nonzero"></path>                </g>            </g>        </g>    </g></svg>`],
  ['firstlineindent', `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="20250717富文本" transform="translate(-470.000000, -247.000000)">
            <g id="编组-28" transform="translate(276.000000, 243.000000)">
                <g id="liangduanduiqi备份" transform="translate(194.000000, 4.000000)">
                    <path d="M9,7.02678571 L19,7.02678571 L19,8.03571429 L9,8.03571429 L9,7.02678571 Z M5,16.1071429 L19,16.1071429 L19,17.1160714 L5,17.1160714 L5,16.1071429 Z M5,10.0535714 L19,10.0535714 L19,11.0625 L5,11.0625 L5,10.0535714 Z M5,13.0803571 L19,13.0803571 L19,14.0892857 L5,14.0892857 L5,13.0803571 Z" id="Combined-Shape" fill="#525354"/>
                    <rect id="矩形备份-13" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"/>
                </g>
            </g>
        </g>
    </g>
</svg>`],
  ['text-color', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-376.000000, -278.000000)">            <g id="zitiyanse" transform="translate(376.000000, 278.000000)">                <rect id="矩形备份-17" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect> <path id="tox-icon-text-color__color" d="M3 18h18v3H3z" fill="#000000"></path>               <path d="M11.3,6 L8,14.568 L9.392,14.568 L10.196,12.36 L13.88,12.36 L14.684,14.568 L16.088,14.568 L12.788,6 L11.3,6 Z M10.592,11.28 L12.02,7.404 L12.068,7.404 L13.484,11.28 L10.592,11.28 Z" id="形状" fill="#525354" fill-rule="nonzero"></path>            </g>        </g>    </g></svg>`],
  ['line-height', `<svg width="24px" height="24px" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
  <g fill="none" fill-rule="evenodd">
    <rect fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2" />
    <g transform="translate(4,4)">
      <rect fill="#FFFFFF" opacity="0" x="0" y="0" width="16" height="16" />

      <!-- 四条水平线（保持原位置） -->
      <line x1="6.66666667" y1="3.33333333" x2="14.6666667" y2="3.33333333" stroke="#444444"/>
      <line x1="6.66666667" y1="6.44444444" x2="14.6666667" y2="6.44444444" stroke="#444444"/>
      <line x1="6.66666667" y1="9.55555556" x2="14.6666667" y2="9.55555556" stroke="#444444"/>
      <line x1="6.66666667" y1="12.6666667" x2="14.6666667" y2="12.6666667" stroke="#444444"/>

      <!-- 中间竖条（左移 1） -->
      <line x1="2.333333" y1="4.5" x2="2.333333" y2="11.5" stroke="#444444" stroke-width="1.2" />

      <!-- 上箭头（左移 1） -->
      <polygon points="-0.666667 4.5 5.333333 4.5 2.333333 1.5" fill="#444444" />

      <!-- 下箭头（左移 1） -->
      <polygon points="-0.666667 11.5 5.333333 11.5 2.333333 14.5" fill="#444444" />
    </g>
  </g>
</svg>`],
  ['italic', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-497.000000, -278.000000)">            <g id="qingxie" transform="translate(497.000000, 278.000000)">                <rect id="矩形备份-8" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="icon/format/font-italic" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M12.46875,2.5 L5.71875,2.5 C5.65,2.5 5.59375,2.55625 5.59375,2.625 L5.59375,3.625 C5.59375,3.69375 5.65,3.75 5.71875,3.75 L8.55,3.75 L6.1125,12.25 L3.578125,12.25 C3.509375,12.25 3.453125,12.30625 3.453125,12.375 L3.453125,13.375 C3.453125,13.44375 3.509375,13.5 3.578125,13.5 L10.328125,13.5 C10.396875,13.5 10.453125,13.44375 10.453125,13.375 L10.453125,12.375 C10.453125,12.30625 10.396875,12.25 10.328125,12.25 L7.4125,12.25 L9.85,3.75 L12.46875,3.75 C12.5375,3.75 12.59375,3.69375 12.59375,3.625 L12.59375,2.625 C12.59375,2.55625 12.5375,2.5 12.46875,2.5 Z" id="路径" fill="#525354"></path>                </g>            </g>        </g>    </g></svg>`],
  ['mathjax', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-548.000000, -247.000000)">            <g id="gongshi" transform="translate(548.000000, 247.000000)">                <rect id="矩形备份-11" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="切图/3" transform="translate(6.153524, 7.077202)" fill="#525354" fill-rule="nonzero">                    <path d="M5.11128187,4.53752518 L2.10441828,0.727865696 L5.1541543,0.727865696 C5.1541543,0.727865696 6.22770309,0.642120838 6.61432743,1.19868994 C7.00095177,1.75525904 7.12976217,2.61135578 7.12976217,2.61135578 L7.51638651,2.61135578 L7.34470367,0 L0,0 L0,0.385272503 L3.77953291,5.00834942 L0,9.41745043 L0,9.84559536 L7.51638651,9.84559536 L7.98894882,6.93451881 L7.60232449,6.84896708 C7.60232449,6.84896708 7.47351408,7.49108791 6.95807934,8.09033632 C6.4426446,8.68977785 4.42377806,8.60403299 4.42377806,8.60403299 L1.71798706,8.60403299 L5.11128187,4.53752518 Z M10.0611162,4.93457794 L9.93558884,4.60376501 C9.84385729,4.37009096 9.79403257,4.24359798 9.78592157,4.22467232 L9.59183693,3.79324436 C9.45414305,3.48425388 9.32204962,3.32937241 9.1951704,3.32937241 C9.00340319,3.32937241 8.85238409,3.50839376 8.74191999,3.86585712 C8.66080999,3.83630991 8.62025499,3.79324436 8.62025499,3.73666048 C8.62025499,3.57019186 8.71739388,3.37919712 8.91089916,3.16425562 C9.10517693,2.949121 9.27937031,2.84174681 9.43309307,2.84174681 C9.73455191,2.84174681 10.0466323,3.2745266 10.3706861,4.14027929 L10.453341,4.35367584 L10.5466175,4.20478106 C11.1213398,3.29615593 11.6054893,2.84174681 11.999259,2.84174681 C12.1018052,2.84174681 12.2219253,2.87129402 12.3596191,2.93038845 L11.869483,3.44254017 C11.804595,3.42380762 11.7586327,3.41434479 11.731596,3.41434479 C11.4349651,3.41434479 11.0807848,3.75462055 10.6705999,4.43478584 L10.5533767,4.63080168 L10.6458807,4.8783803 C11.0008335,5.85459709 11.2924433,6.34260893 11.5234137,6.34260893 C11.7350721,6.34260893 11.8963265,6.19178295 12.0075631,5.89090347 C12.0830727,5.93937635 12.1207309,5.98900795 12.1207309,6.0401845 C12.1207309,6.17478848 12.0129704,6.33604288 11.7970634,6.52414083 C11.5811563,6.71223879 11.3953757,6.806674 11.2385631,6.806674 C10.9255171,6.806674 10.6059051,6.36694193 10.2795339,5.48786402 L10.166173,5.18910885 L10.0489497,5.39516687 C9.49856043,6.336236 8.97231102,6.806674 8.47039461,6.806674 C8.2817173,6.806674 8.12123537,6.75974607 7.98894882,6.66666269 L8.45822811,6.2095499 C8.53933811,6.29027367 8.63377333,6.33044243 8.74172688,6.33044243 C9.04627562,6.33044243 9.38944817,6.0229969 9.76950645,5.40810585 L9.98000622,5.06860256 L10.0611162,4.93457794 L10.0611162,4.93457794 Z" id="形状"></path>                </g>            </g>        </g>    </g></svg>`],
  ['myimage', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-471.000000, -247.000000)">            <g id="tupianshangchuan" transform="translate(471.000000, 247.000000)">                <rect id="矩形备份-10" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/text-deleteline" transform="translate(6.000000, 6.000000)" stroke="#525354" stroke-width="1.2">                    <g id="编组">                        <rect id="矩形" x="0.6" y="0.6" width="10.8" height="10.8" rx="2"></rect>                        <path d="M0.891658344,11.0025121 L1.71869666,10.2935118 L6.38396895,6.26904569 C7.17713458,5.58482664 8.36386256,5.62852367 9.10456068,6.3692218 L11.2963151,8.56097622 L11.2963151,8.56097622" id="路径"></path>                        <circle id="椭圆形" cx="4" cy="4" r="1"></circle>                    </g>                </g>            </g>        </g>    </g></svg>`],
  ['outdent', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-722.000000, -278.000000)">            <g id="quxiaosuojin" transform="translate(722.000000, 278.000000)">                <rect id="矩形" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="切图/80备份-2" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" x="0" y="0" width="16" height="16"></rect>                    <path d="M2,13.0892857 L14,13.0892857 L14,12.0803571 L2,12.0803571 L2,13.0892857 L2,13.0892857 Z M5,6.02678571 L2,8.04464286 L5,10.0625 L5,6.02678571 Z M2,3 L2,4.00892857 L14,4.00892857 L14,3 L2,3 L2,3 Z M8,7.03571429 L14,7.03571429 L14,6.02678571 L8,6.02678571 L8,7.03571429 L8,7.03571429 Z M8,10.0625 L14,10.0625 L14,9.05357143 L8,9.05357143 L8,10.0625 L8,10.0625 Z" id="Shape" fill="#525354"></path>                </g>            </g>        </g>    </g></svg>`],
  ['redo', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-308.000000, -247.000000)">            <g id="chongzuo" transform="translate(308.000000, 247.000000)">                <rect id="矩形备份-17" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="撤回备份" transform="translate(12.000000, 12.000000) scale(-1, 1) translate(-12.000000, -12.000000) translate(4.000000, 4.000000)">                    <rect id="矩形" x="0" y="0" width="16" height="16"></rect>                    <polygon id="路径" fill="#525354" fill-rule="nonzero" transform="translate(5.234895, 5.000460) rotate(-315.000000) translate(-5.234895, -5.000460) " points="3.6127893 2.96788796 4.80671958 3.08843045 4.54321193 5.69015921 7.14566201 5.42459428 7.26742852 6.61840034 3.93409519 6.95839557 3.20236123 7.03303139 3.27624679 6.3012213"></polygon>                    <path d="M9.86034516,4.41085553 C12.2167601,4.41085553 14.1270118,6.32110726 14.1270118,8.6775222 C14.1270118,11.0339371 12.2167601,12.9441889 9.86034516,12.9441889 L4.19367849,12.9441889 L4.19367849,11.7441889 L9.86034516,11.7441889 C11.5540184,11.7441889 12.9270118,10.3711954 12.9270118,8.6775222 C12.9270118,6.98384896 11.5540184,5.61085553 9.86034516,5.61085553 L3.52701182,5.61085553 L3.52701182,4.41085553 L9.86034516,4.41085553 Z" id="路径" fill="#525354" fill-rule="nonzero"></path>                </g>            </g>        </g>    </g></svg>`],
  ['subscript', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-625.000000, -278.000000)">            <g id="xiabian" transform="translate(625.000000, 278.000000)">                <rect id="矩形备份-4" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="font-size" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M10.25,4.625 L10.25,2.625 C10.25,2.55625 10.19375,2.5 10.125,2.5 L1.625,2.5 C1.55625,2.5 1.5,2.55625 1.5,2.625 L1.5,4.625 C1.5,4.69375 1.55625,4.75 1.625,4.75 L2.5,4.75 C2.56875,4.75 2.625,4.69375 2.625,4.625 L2.625,3.625 L5.25,3.625 L5.25,12.375 L3.8125,12.375 C3.74375,12.375 3.6875,12.43125 3.6875,12.5 L3.6875,13.375 C3.6875,13.44375 3.74375,13.5 3.8125,13.5 L7.9375,13.5 C8.00625,13.5 8.0625,13.44375 8.0625,13.375 L8.0625,12.5 C8.0625,12.43125 8.00625,12.375 7.9375,12.375 L6.5,12.375 L6.5,3.625 L9.125,3.625 L9.125,4.625 C9.125,4.69375 9.18125,4.75 9.25,4.75 L10.125,4.75 C10.19375,4.75 10.25,4.69375 10.25,4.625 Z M12.488,9.176 C13.032,9.176 13.488,9.328 13.84,9.648 C14.184,9.968 14.36,10.376 14.36,10.88 C14.36,11.376 14.168,11.824 13.8,12.232 C13.576,12.464 13.184,12.768 12.616,13.152 C12,13.56 11.632,13.92 11.512,14.232 L14.368,14.232 L14.368,15 L10.432,15 C10.432,14.432 10.616,13.936 11,13.504 C11.208,13.256 11.64,12.912 12.288,12.464 C12.672,12.192 12.944,11.976 13.104,11.808 C13.36,11.52 13.496,11.208 13.496,10.872 C13.496,10.552 13.408,10.312 13.232,10.152 C13.056,9.992 12.792,9.912 12.456,9.912 C12.096,9.912 11.824,10.032 11.64,10.28 C11.448,10.52 11.352,10.864 11.336,11.328 L10.464,11.328 C10.472,10.688 10.656,10.176 11.016,9.792 C11.384,9.376 11.88,9.176 12.488,9.176 Z" id="合并形状" fill="#525354" fill-rule="nonzero"></path>                </g>            </g>        </g>    </g></svg>`],
  ['superscript', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-657.000000, -278.000000)">            <g id="shangbiao" transform="translate(657.000000, 278.000000)">                <rect id="矩形备份-3" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="font-size" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M10.25,4.625 L10.25,2.625 C10.25,2.55625 10.19375,2.5 10.125,2.5 L1.625,2.5 C1.55625,2.5 1.5,2.55625 1.5,2.625 L1.5,4.625 C1.5,4.69375 1.55625,4.75 1.625,4.75 L2.5,4.75 C2.56875,4.75 2.625,4.69375 2.625,4.625 L2.625,3.625 L5.25,3.625 L5.25,12.375 L3.8125,12.375 C3.74375,12.375 3.6875,12.43125 3.6875,12.5 L3.6875,13.375 C3.6875,13.44375 3.74375,13.5 3.8125,13.5 L7.9375,13.5 C8.00625,13.5 8.0625,13.44375 8.0625,13.375 L8.0625,12.5 C8.0625,12.43125 8.00625,12.375 7.9375,12.375 L6.5,12.375 L6.5,3.625 L9.125,3.625 L9.125,4.625 C9.125,4.69375 9.18125,4.75 9.25,4.75 L10.125,4.75 C10.19375,4.75 10.25,4.69375 10.25,4.625 Z M13.488,1.176 C14.032,1.176 14.488,1.328 14.84,1.648 C15.184,1.968 15.36,2.376 15.36,2.88 C15.36,3.376 15.168,3.824 14.8,4.232 C14.576,4.464 14.184,4.768 13.616,5.152 C13,5.56 12.632,5.92 12.512,6.232 L15.368,6.232 L15.368,7 L11.432,7 C11.432,6.432 11.616,5.936 12,5.504 C12.208,5.256 12.64,4.912 13.288,4.464 C13.672,4.192 13.944,3.976 14.104,3.808 C14.36,3.52 14.496,3.208 14.496,2.872 C14.496,2.552 14.408,2.312 14.232,2.152 C14.056,1.992 13.792,1.912 13.456,1.912 C13.096,1.912 12.824,2.032 12.64,2.28 C12.448,2.52 12.352,2.864 12.336,3.328 L11.464,3.328 C11.472,2.688 11.656,2.176 12.016,1.792 C12.384,1.376 12.88,1.176 13.488,1.176 Z" id="合并形状" fill="#525354" fill-rule="nonzero"></path>                </g>            </g>        </g>    </g></svg>`],
  ['table', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-503.000000, -247.000000)">            <g id="biaoge" transform="translate(503.000000, 247.000000)">                <rect id="矩形备份-19" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="编组-6" transform="translate(4.000000, 4.000000)" stroke="#525354" stroke-width="1.2">                    <g id="编组" transform="translate(2.000000, 2.000000)">                        <rect id="矩形" x="0.6" y="0.6" width="10.8" height="10.8" rx="2"></rect>                        <line x1="0.766317435" y1="3.99133783" x2="12" y2="3.99133783" id="路径-4"></line>                        <line x1="0.766317435" y1="7.6" x2="12" y2="7.6" id="路径-4备份"></line>                        <line x1="4.2" y1="4" x2="4.2" y2="12" id="路径-8"></line>                        <line x1="7.8" y1="4" x2="7.8" y2="12" id="路径-8备份"></line>                    </g>                </g>            </g>        </g>    </g></svg>`],
  ['underline', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-529.000000, -278.000000)">            <g id="xiahuaxian" transform="translate(529.000000, 278.000000)">                <rect id="矩形备份-7" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="icon/format/font-underlined" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="16" height="16"></rect>                    <path d="M12.875,12.5625 L3.125,12.5625 C3.05625,12.5625 3,12.615625 3,12.68125 L3,13.63125 C3,13.696875 3.05625,13.75 3.125,13.75 L12.875,13.75 C12.94375,13.75 13,13.696875 13,13.63125 L13,12.68125 C13,12.615625 12.94375,12.5625 12.875,12.5625 Z M8,11.375 C9.084375,11.375 10.103125,10.9515625 10.871875,10.184375 C11.640625,9.4171875 12.0625,8.396875 12.0625,7.3125 L12.0625,2.4375 C12.0625,2.334375 11.978125,2.25 11.875,2.25 L10.9375,2.25 C10.834375,2.25 10.75,2.334375 10.75,2.4375 L10.75,7.3125 C10.75,8.828125 9.515625,10.0625 8,10.0625 C6.484375,10.0625 5.25,8.828125 5.25,7.3125 L5.25,2.4375 C5.25,2.334375 5.165625,2.25 5.0625,2.25 L4.125,2.25 C4.021875,2.25 3.9375,2.334375 3.9375,2.4375 L3.9375,7.3125 C3.9375,8.396875 4.3609375,9.415625 5.128125,10.184375 C5.8953125,10.953125 6.915625,11.375 8,11.375 Z" id="形状" fill="#525354" fill-rule="nonzero"></path>                </g>            </g>        </g>    </g></svg>`],
  ['underlinedot', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-593.000000, -278.000000)">            <g id="zhuozhong" transform="translate(593.000000, 278.000000)">                <rect id="矩形备份-5" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/text-deleteline备份-13" transform="translate(8.000000, 6.000000)" fill="#525354" fill-rule="nonzero">                    <path d="M4.3125,12.5 C5.00285594,12.5 5.5625,11.9403559 5.5625,11.25 C5.5625,10.5596441 5.00285594,10 4.3125,10 C3.62214406,10 3.0625,10.5596441 3.0625,11.25 C3.0625,11.9403559 3.62214406,12.5 4.3125,12.5 Z M4.0625,9.125 C5.146875,9.125 6.165625,8.7015625 6.934375,7.934375 C7.703125,7.1671875 8.125,6.146875 8.125,5.0625 L8.125,0.1875 C8.125,0.084375 8.040625,0 7.9375,0 L7,0 C6.896875,0 6.8125,0.084375 6.8125,0.1875 L6.8125,5.0625 C6.8125,6.578125 5.578125,7.8125 4.0625,7.8125 C2.546875,7.8125 1.3125,6.578125 1.3125,5.0625 L1.3125,0.1875 C1.3125,0.084375 1.228125,0 1.125,0 L0.1875,0 C0.084375,0 0,0.084375 0,0.1875 L0,5.0625 C0,6.146875 0.4234375,7.165625 1.190625,7.934375 C1.9578125,8.703125 2.978125,9.125 4.0625,9.125 Z" id="形状"></path>                </g>            </g>        </g>    </g></svg>`],
  ['undo', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-276.000000, -247.000000)">            <g id="chehui" transform="translate(276.000000, 247.000000)">                <rect id="矩形备份-18" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="撤回" transform="translate(6.360431, 6.125996)" fill="#525354" fill-rule="nonzero">                    <polygon id="路径" transform="translate(2.874464, 2.874464) rotate(-315.000000) translate(-2.874464, -2.874464) " points="1.25235799 0.841891852 2.44628827 0.96243434 2.18278062 3.5641631 4.7852307 3.29859817 4.90699721 4.49240423 1.57366388 4.83239946 0.84192992 4.90703528 0.915815481 4.17522518"></polygon>                    <path d="M7.49991385,2.28485942 C9.85632878,2.28485942 11.7665805,4.19511115 11.7665805,6.55152608 C11.7665805,8.90794102 9.85632878,10.8181928 7.49991385,10.8181928 L1.83324718,10.8181928 L1.83324718,9.61819275 L7.49991385,9.61819275 C9.19358708,9.61819275 10.5665805,8.24519932 10.5665805,6.55152608 C10.5665805,4.85785285 9.19358708,3.48485942 7.49991385,3.48485942 L1.16658051,3.48485942 L1.16658051,2.28485942 L7.49991385,2.28485942 Z" id="路径"></path>                </g>            </g>        </g>    </g></svg>`],
  ['strike-through',`<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="20250717富文本备份" transform="translate(-754.000000, -278.000000)">
            <g id="shanchuxian" transform="translate(754.000000, 278.000000)">
                <rect id="矩形备份-8" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"/>
                <path d="M13.1089844,11.40625 C12.9429687,11.375 12.7686523,11.34375 12.584375,11.3125 C12.3204102,11.2671875 12.2158203,11.2484375 12.0730469,11.221875 C11.2213867,11.065625 10.7083984,10.909375 10.3,10.6875 C9.71728516,10.3671875 9.43339844,9.9328125 9.43339844,9.3578125 C9.43339844,8.7796875 9.68574219,8.3 10.1638672,7.9671875 C10.6353516,7.6390625 11.3060547,7.465625 12.1029297,7.465625 C13.0126953,7.465625 13.7149414,7.690625 14.1914063,8.134375 C14.4337891,8.359375 14.6114258,8.6359375 14.7193359,8.95625 C14.740918,9.0203125 14.7658203,9.1125 14.7907227,9.234375 C14.8056641,9.309375 14.8770508,9.3625 14.9550781,9.3625 L16.1636719,9.3625 C16.2566406,9.3625 16.3313477,9.290625 16.3313477,9.2046875 L16.3313477,9.1890625 C16.3197266,9.0828125 16.3097656,9 16.2981445,8.9390625 C16.1769531,8.259375 15.8333008,7.6625 15.3070313,7.215625 C14.5699219,6.5828125 13.4858398,6.25 12.1743164,6.25 C10.9740234,6.25 9.89326172,6.5328125 9.13125,7.0453125 C8.70625,7.3328125 8.37753906,7.6890625 8.15839844,8.103125 C7.93427734,8.5265625 7.82138672,9.015625 7.82138672,9.5546875 C7.82138672,10.015625 7.91601562,10.40625 8.10859375,10.75 C8.24638672,10.9953125 8.43398438,11.2109375 8.67470703,11.40625 L4.84306641,11.40625 C4.77001953,11.40625 4.71025391,11.4625 4.71025391,11.53125 L4.71025391,12.46875 C4.71025391,12.5375 4.77001953,12.59375 4.84306641,12.59375 L12.0348633,12.59375 C12.0697266,12.6 12.0996094,12.60625 12.1328125,12.6125 C12.6458008,12.709375 12.9545898,12.775 13.2384766,12.85 C13.6203125,12.9515625 13.9125,13.0578125 14.1548828,13.1859375 C14.7492187,13.5015625 15.0397461,13.9546875 15.0397461,14.5765625 C15.0397461,15.128125 14.7824219,15.6203125 14.315918,15.9640625 C13.8095703,16.3375 13.0608398,16.5328125 12.1494141,16.5328125 C11.4239258,16.5328125 10.809668,16.4 10.3199219,16.1421875 C9.83681641,15.8875 9.50478516,15.5203125 9.32880859,15.05625 C9.31552734,15.021875 9.30058594,14.975 9.28398438,14.915625 C9.2640625,14.846875 9.19599609,14.7984375 9.12294922,14.7984375 L7.79980469,14.7984375 C7.70683594,14.7984375 7.63212891,14.8703125 7.63212891,14.95625 L7.63212891,14.971875 C7.63544922,15.0078125 7.63876953,15.0375 7.64208984,15.0609375 C7.75,15.8234375 8.14511719,16.4484375 8.81582031,16.9171875 C9.59775391,17.4609375 10.6984375,17.7484375 12,17.7484375 C13.3978516,17.7484375 14.5699219,17.4390625 15.3900391,16.853125 C15.8050781,16.5578125 16.1238281,16.19375 16.3379883,15.775 C16.5538086,15.3515625 16.6650391,14.8703125 16.6650391,14.3453125 C16.6650391,13.8484375 16.56875,13.4328125 16.3695313,13.0734375 C16.2732422,12.8984375 16.1520508,12.7375 16.0076172,12.5921875 L19.4524414,12.5921875 C19.5254883,12.5921875 19.5852539,12.5359375 19.5852539,12.4671875 L19.5852539,11.5296875 C19.5852539,11.4625 19.5254883,11.40625 19.4524414,11.40625 L13.1089844,11.40625 Z" id="路径" fill="#525354"/>
            </g>
        </g>
    </g>
</svg>`],
  ['wavyunderline', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-561.000000, -278.000000)">            <g id="bolangxian" transform="translate(561.000000, 278.000000)">                <rect id="矩形备份-6" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/text-deleteline备份-7" transform="translate(4.000000, 4.000000)">                    <rect id="矩形" x="0" y="0" width="16" height="16"></rect>                    <path d="M8.01520604,11.125 C9.09958104,11.125 10.118331,10.7015625 10.887081,9.934375 C11.655831,9.1671875 12.077706,8.146875 12.077706,7.0625 L12.077706,2.1875 C12.077706,2.084375 11.993331,2 11.890206,2 L10.952706,2 C10.849581,2 10.765206,2.084375 10.765206,2.1875 L10.765206,7.0625 C10.765206,8.578125 9.53083104,9.8125 8.01520604,9.8125 C6.49958104,9.8125 5.26520604,8.578125 5.26520604,7.0625 L5.26520604,2.1875 C5.26520604,2.084375 5.18083104,2 5.07770604,2 L4.14020604,2 C4.03708104,2 3.95270604,2.084375 3.95270604,2.1875 L3.95270604,7.0625 C3.95270604,8.146875 4.37614354,9.165625 5.14333104,9.934375 C5.91051854,10.703125 6.93083104,11.125 8.01520604,11.125 Z" id="形状" fill="#525354" fill-rule="nonzero"></path>                    <path d="M3.38624745,13.7447321 L4.09895852,13.4034189 C4.69885358,13.1161327 5.40231913,13.1458765 5.97582244,13.4827762 L6.03840025,13.519537 C6.64565046,13.8762611 7.39621672,13.885474 8.01203995,13.5437627 L8.09824148,13.4959307 C8.7148185,13.1538012 9.46624889,13.1627771 10.0744776,13.519537 L10.0825111,13.5242491 C10.6866473,13.8786086 11.4321337,13.8911209 12.0478214,13.557235 L12.6441646,13.2338396 L12.6441646,13.2338396" id="wavy-underline-current" stroke="#525354" stroke-width="1.2" stroke-linecap="round"></path>                </g>            </g>        </g>    </g></svg>`],
  ['wordcount', `<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">        <g id="20250717富文本备份" transform="translate(-581.000000, -247.000000)">            <g id="zishutongji" transform="translate(581.000000, 247.000000)">                <rect id="矩形备份-12" fill="#DEE0E2" opacity="0" x="0" y="0" width="24" height="24" rx="2"></rect>                <g id="ic/editor/text-deleteline备份-2" transform="translate(6.000000, 6.000000)" fill="#525354" fill-rule="nonzero">                    <g id="编组" transform="translate(0.000000, -0.000000)">                        <polygon id="路径-4" points="0 6.5 12 6.5 12 5.5 0 5.5"></polygon>                        <g id="123" stroke="#525354" stroke-width="0.1">                            <path d="M1.99700243,4.52317969 L1.12563957,4.52317969 L1.12563957,1.26907148 C0.807276862,1.56406146 0.432063673,1.78223113 0,1.92358049 L0,1.14001336 C0.227401933,1.06626587 0.474443124,0.926452906 0.741123572,0.720574482 C1.00780402,0.514696059 1.19075921,0.274504565 1.28998915,0 L1.99700243,0 L1.99700243,4.52317969 Z" id="路径"></path>                            <path d="M7.01555636,3.72117568 L7.01555636,4.52317969 L3.96113494,4.52317969 C3.99421159,4.21999555 4.09344152,3.9326876 4.25882475,3.66125585 C4.42420797,3.38982409 4.75083984,3.02979292 5.23872035,2.58116232 C5.6315055,2.21857047 5.87234482,1.97274549 5.96123831,1.84368737 C6.08114114,1.66546426 6.14109256,1.48928969 6.14109256,1.31516366 C6.14109256,1.12260076 6.08889348,0.974593632 5.98449532,0.871142285 C5.88009716,0.767690937 5.73590366,0.715965264 5.55191483,0.715965264 C5.36999328,0.715965264 5.22528296,0.770251614 5.11778386,0.878824315 C5.01028477,0.987397016 4.94826606,1.16766867 4.93172774,1.41963928 L4.06346581,1.33360053 C4.11514807,0.8583389 4.27743036,0.517256736 4.55031268,0.310354041 C4.823195,0.103451347 5.1642979,0 5.57362138,0 C6.02222337,0 6.37469637,0.119839679 6.63104036,0.359519038 C6.88738436,0.599198397 7.01555636,0.897261189 7.01555636,1.25370741 C7.01555636,1.45651303 6.97886196,1.64958807 6.90547315,1.83293253 C6.83208435,2.016277 6.71579927,2.20832777 6.55661791,2.40908484 C6.45118611,2.54224004 6.2609954,2.73377867 5.98604579,2.98370073 C5.71109618,3.2336228 5.53692697,3.39955466 5.46353817,3.48149633 C5.39014936,3.56343799 5.33071477,3.64333111 5.28523438,3.72117568 L7.01555636,3.72117568 Z" id="路径"></path>                            <path d="M8.97968887,3.32785571 L9.82314331,3.22645291 C9.85001809,3.43950122 9.92237325,3.60236028 10.0402088,3.71503006 C10.1580443,3.82769984 10.3006874,3.88403474 10.4681379,3.88403474 C10.6479921,3.88403474 10.7994212,3.81643287 10.9224249,3.68122912 C11.0454287,3.54602538 11.1069306,3.36370519 11.1069306,3.13426854 C11.1069306,2.91712314 11.0480128,2.74504565 10.9301773,2.61803607 C10.8123417,2.4910265 10.668665,2.42752171 10.4991472,2.42752171 C10.3875136,2.42752171 10.2541733,2.4490314 10.0991266,2.49205077 L10.1952556,1.78837675 C10.4309267,1.79452238 10.6107809,1.74382098 10.7348183,1.63627255 C10.8588558,1.52872411 10.9208745,1.38583834 10.9208745,1.20761523 C10.9208745,1.05602316 10.8753941,0.935159207 10.7844333,0.84502338 C10.6934725,0.754887553 10.572536,0.709819639 10.4216239,0.709819639 C10.272779,0.709819639 10.1456406,0.761033177 10.0402088,0.863460254 C9.93477699,0.96588733 9.87069099,1.11543086 9.8479508,1.31209085 L9.04480852,1.17688711 C9.10062536,0.904431084 9.18486744,0.686773547 9.29753476,0.523914496 C9.41020208,0.361055444 9.56731614,0.233021599 9.76887694,0.139812959 C9.97043775,0.0466043198 10.1962892,0 10.4464313,0 C10.8743604,0 11.2175306,0.135203741 11.4759419,0.405611222 C11.6888728,0.626853707 11.7953383,0.876775774 11.7953383,1.15537742 C11.7953383,1.55074594 11.5772391,1.86622133 11.1410409,2.10180361 C11.4015195,2.15711423 11.609799,2.28105099 11.7658794,2.47361389 C11.9219598,2.6661768 12,2.89868626 12,3.17114228 C12,3.5665108 11.854256,3.90349588 11.5627681,4.18209753 C11.2712802,4.46069918 10.9084707,4.6 10.4743398,4.6 C10.062949,4.6 9.72184609,4.482721 9.45103106,4.24816299 C9.18021603,4.01360499 9.02310197,3.70683589 8.97968887,3.32785571 Z" id="路径"></path>                        </g>                        <g id="abc" transform="translate(0.000000, 6.800000)">                            <path d="M1.00117509,2.56639248 L0.102232667,2.40423032 C0.203290247,2.04230317 0.37720329,1.77438308 0.623971798,1.60047004 C0.870740306,1.42655699 1.2373678,1.33960047 1.72385429,1.33960047 C2.16568743,1.33960047 2.4947121,1.39189189 2.71092832,1.49647474 C2.92714454,1.60105758 3.07931845,1.73384254 3.16745006,1.89482961 C3.25558167,2.05581669 3.29964747,2.35135135 3.29964747,2.78143361 L3.28907168,3.93772033 C3.28907168,4.26674501 3.30493537,4.50940071 3.33666275,4.66568743 C3.36839013,4.82197415 3.42773208,4.98942421 3.5146886,5.1680376 L2.5346651,5.1680376 C2.50881316,5.10223267 2.47708578,5.00470035 2.43948296,4.87544066 C2.42303173,4.81668625 2.41128085,4.77790834 2.40423032,4.75910693 C2.23501763,4.92361927 2.05405405,5.04700353 1.8613396,5.12925969 C1.66862515,5.21151586 1.46298472,5.25264395 1.24441833,5.25264395 C0.858989424,5.25264395 0.555229142,5.1480611 0.333137485,4.93889542 C0.111045828,4.72972973 0,4.4653349 0,4.14571093 C0,3.93419506 0.0505287897,3.74559342 0.151586369,3.57990599 C0.252643948,3.41421857 0.394242068,3.28730905 0.576380729,3.19917744 C0.758519389,3.11104583 1.02115159,3.03407756 1.36427732,2.96827262 C1.82726204,2.8813161 2.1480611,2.80023502 2.3266745,2.72502938 L2.3266745,2.62632197 C2.3266745,2.4359577 2.27967098,2.30023502 2.18566392,2.21915394 C2.09165687,2.13807286 1.91421857,2.09753231 1.653349,2.09753231 C1.47708578,2.09753231 1.33960047,2.13219741 1.24089307,2.20152761 C1.14218566,2.27085781 1.06227967,2.39247944 1.00117509,2.56639248 Z M2.3266745,3.37015276 C2.19976498,3.41245593 1.99882491,3.46298472 1.72385429,3.52173913 C1.44888367,3.58049354 1.26909518,3.63807286 1.18448884,3.69447709 C1.05522914,3.78613396 0.990599295,3.90246769 0.990599295,4.04347826 C0.990599295,4.18213866 1.04230317,4.30199765 1.14571093,4.40305523 C1.24911868,4.50411281 1.38072855,4.5546416 1.54054054,4.5546416 C1.71915394,4.5546416 1.88954172,4.49588719 2.05170388,4.37837838 C2.17156287,4.28907168 2.25029377,4.17978848 2.28789659,4.05052879 C2.31374853,3.96592244 2.3266745,3.80493537 2.3266745,3.56756757 L2.3266745,3.37015276 Z" id="形状"></path>                            <path d="M4.23384254,5.1680376 L4.23384254,0 L5.22444183,0 L5.22444183,1.8613396 C5.52996475,1.51351351 5.89189189,1.33960047 6.31022327,1.33960047 C6.76615746,1.33960047 7.14336075,1.50470035 7.44183314,1.83490012 C7.74030552,2.16509988 7.88954172,2.63924794 7.88954172,3.2573443 C7.88954172,3.89659224 7.7373678,4.38895417 7.43301998,4.73443008 C7.12867215,5.07990599 6.75910693,5.25264395 6.32432432,5.25264395 C6.11045828,5.25264395 5.89952996,5.19917744 5.69153937,5.09224442 C5.48354877,4.9853114 5.30434783,4.82726204 5.15393655,4.61809636 L5.15393655,5.1680376 L4.23384254,5.1680376 Z M5.2173913,3.21504113 C5.2173913,3.60282021 5.27849589,3.88954172 5.40070505,4.07520564 C5.57226792,4.33842538 5.80023502,4.47003525 6.08460635,4.47003525 C6.30317274,4.47003525 6.48942421,4.37661575 6.64336075,4.18977673 C6.7972973,4.00293772 6.87426557,3.70857814 6.87426557,3.306698 C6.87426557,2.87896592 6.79670975,2.57050529 6.64159812,2.3813161 C6.48648649,2.19212691 6.28789659,2.09753231 6.04582844,2.09753231 C5.80846063,2.09753231 5.61104583,2.18977673 5.45358402,2.37426557 C5.29612221,2.55875441 5.2173913,2.83901293 5.2173913,3.21504113 Z" id="形状"></path>                            <path d="M11.9506463,2.53113984 L10.9741481,2.70740306 C10.9412456,2.51233843 10.8666275,2.36545241 10.7502938,2.26674501 C10.63396,2.1680376 10.4829612,2.1186839 10.2972973,2.1186839 C10.0505288,2.1186839 9.85370153,2.20387779 9.70681551,2.37426557 C9.55992949,2.54465335 9.48648649,2.82961222 9.48648649,3.22914219 C9.48648649,3.6733255 9.56110458,3.98707403 9.71034078,4.17038778 C9.85957697,4.35370153 10.0599295,4.4453584 10.3113984,4.4453584 C10.4994125,4.4453584 10.653349,4.39189189 10.773208,4.28495887 C10.893067,4.17802585 10.9776733,3.99412456 11.027027,3.73325499 L12,3.89894242 C11.8989424,4.34547591 11.7050529,4.6827262 11.4183314,4.9106933 C11.1316099,5.1386604 10.7473561,5.25264395 10.2655699,5.25264395 C9.71797885,5.25264395 9.28143361,5.07990599 8.9559342,4.73443008 C8.63043478,4.38895417 8.46768508,3.9106933 8.46768508,3.29964747 C8.46768508,2.68155112 8.63102233,2.20035253 8.95769683,1.8560517 C9.28437133,1.51175088 9.72620447,1.33960047 10.2831962,1.33960047 C10.7391304,1.33960047 11.1016451,1.43772033 11.3707403,1.63396005 C11.6398355,1.83019976 11.8331375,2.12925969 11.9506463,2.53113984 Z" id="路径"></path>                        </g>                    </g>                </g>            </g>        </g>    </g></svg>`]
]);
export default myicons;