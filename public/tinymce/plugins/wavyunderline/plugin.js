/* eslint-disable no-var */
(function () {
  'use strict';

  // 1. 依赖 tinymce
  const tinymce = window.tinymce;
  const global = tinymce.util.Tools.resolve('tinymce.PluginManager');

  // 2. 色板颜色（可自行扩展）
  const COLORS = [
    '#c21400', // rgb(194, 20, 0)
    '#ff1f02', // rgb(255, 31, 2)
    '#ffc129', // rgb(255, 193, 41)
    '#feff3a', // rgb(254, 255, 58)
    '#90cf5a', // rgb(144, 207, 90)
    '#0aaf57', // rgb(10, 175, 87)
    '#00afee', // rgb(0, 175, 238)
    '#0171be', // rgb(1, 113, 190)
    '#7030a0', // rgb(112, 48, 160)
    '#000000'  // rgb(0, 0, 0)
  ];

  // 3. 注册所有颜色的波浪线 formatter（用 class）
  function registerFormats(editor) {
    COLORS.forEach(function(color) {
      editor.formatter.register('wavyunderline_' + color.replace('#',''), {
        inline: 'span',
        classes: 'wavy-underline-' + color.replace('#','')
      });
    });
    editor.formatter.register('wavyunderline_none', {
      inline: 'span',
      classes: ''
    });
  }

  // 4. 注册icon（每种颜色一个，U+波浪线）
  function registerIcons(editor) {
    COLORS.forEach(function(color) {
      const iconName = 'wavyicon_' + color.replace('#','');
      editor.ui.registry.addIcon(iconName, `
        <svg version="1.1" viewBox="0 0 16 20" width="20" height="20">
          <text x="4" y="13" font-size="10" fill="#525354" font-family="Arial">U</text>
          <path d="M3 17 Q5 19 8 17 Q11 15 13 17" stroke="${color}" stroke-width="1.5" fill="none"/>
        </svg>
      `);
    });
    COLORS.forEach(function(color) {
      const iconName = 'wavycolor_' + color.replace('#','');
      editor.ui.registry.addIcon(iconName, `
        <svg width="20" height="20">
          <rect x="2.5" y="2.5" width="15" height="15" rx="2" fill="${color}" stroke="#ccc" stroke-width="1"/>
        </svg>
      `);
    });
    editor.ui.registry.addIcon('wavycolor_none', `
      <svg width="20" height="20">
        <rect x="2.5" y="2.5" width="15" height="15" rx="2" fill="#fff" stroke="#ccc" stroke-width="1"/>
        <line x1="5" y1="5" x2="15" y2="15" stroke="#ff4d4f" stroke-width="2"/>
      </svg>
    `);
  }

  // 5. 获取当前选区的波浪线颜色（通过 classList）
  function getCurrentWavyColor(editor) {
    let node = editor.selection.getNode();
    while (node && node.nodeType === 1 && node !== editor.getBody()) {
      if (node.classList) {
        for (const color of COLORS) {
          if (node.classList.contains('wavy-underline-' + color.replace('#',''))) {
            return color;
          }
        }
      }
      node = node.parentNode;
    }
    return null;
  }

  // 6. 注册按钮和菜单
  function registerButton(editor) {
    editor.ui.registry.addMenuButton('wavyunderline', {
      icon: 'wavyunderline',
      tooltip: '波浪线',
      onHide: function() {
        console.log('wavyunderline-onHide');  
      },
      onShow: function() {
        console.log('wavyunderline-onShow'); 
        var aux = document.querySelector('.tox-tinymce-aux'); 
        // MutationObserver 逻辑也可以放这里
        if (!aux._wavyObserver) {
          const observer = new MutationObserver(() => {
            if (aux.childNodes.length === 0) {
              aux.classList.remove('wavy-dropdown-show');
              observer.disconnect();
              aux._wavyObserver = null;
            }
          });
          observer.observe(aux, { childList: true });
          aux._wavyObserver = observer;
        }
      },
      onAction: function() {
        console.log("%c Line:105 🍣 onAction", "color:#f5ce50");
        COLORS.forEach(function(c) {
          editor.formatter.remove('wavyunderline_' + c.replace('#',''));
        });
        const color = getCurrentWavyColor(editor) || COLORS[0];
        // 判断是否有选区或光标
        const hasFocus = editor.hasFocus();
        const sel = editor.selection.getSel && editor.selection.getSel();
        const rng = editor.selection.getRng && editor.selection.getRng();
        if (!hasFocus || !sel || !rng) {
          console.log('wavyunderline-没有焦点和选区，不生效');
        } else if (rng.collapsed) {
          // 有光标但无选区，插入空 span 并将光标定位到内部
          const span = editor.getDoc().createElement('span');
          span.className = 'wavy-underline-' + color.replace('#','');
          span.appendChild(editor.getDoc().createTextNode('\u200B'));
          rng.insertNode(span);
          // 移动光标到 span 内部
          sel.removeAllRanges();
          const newRange = editor.getDoc().createRange();
          newRange.setStart(span.firstChild, 1); // 光标到零宽字符后
          newRange.collapse(true);
          sel.addRange(newRange);
        } else {
          // 正常选区处理
          editor.formatter.apply('wavyunderline_' + color.replace('#',''));
        }
        // 移除 class
        var aux = document.querySelector('.tox-tinymce-aux');
        if (aux) aux.classList.remove('wavy-dropdown-show');
      },
      onSetup: function(api) {
        // "API永新": 始终更新为最新的 API 实例
        editor._wavyunderlineApi = api;

        // "监听器永生": 确保监听器只注册一次
        if (!editor._wavyunderlineListener) {
          editor._wavyunderlineListener = function () {
            // 确保总是在最新的 API 实例上更新状态
            if (editor._wavyunderlineApi) {
          const color = getCurrentWavyColor(editor);
          if (color) {
                if (editor._wavyunderlineApi.setActive) editor._wavyunderlineApi.setActive(true);
            setTimeout(() => {
              const path = document.querySelector('#wavy-underline-current');
              if (path) path.setAttribute('stroke', color);
            }, 0);
          } else {
                if (editor._wavyunderlineApi.setActive) editor._wavyunderlineApi.setActive(false);
            setTimeout(() => {
              const path = document.querySelector('#wavy-underline-current');
              if (path) path.setAttribute('stroke', '#525354');
            }, 0);
          }
        }
          };
          editor.on('NodeChange FormatChange', editor._wavyunderlineListener);
        }

        // 立即使用新 API 更新一次初始状态
        if(editor._wavyunderlineListener) editor._wavyunderlineListener();

        return function () {
          // 当按钮被销毁时，清除其 API 引用
          if (editor._wavyunderlineApi === api) {
            editor._wavyunderlineApi = null;
          }
        };
      },
      fetch: function(callback) {
        // 菜单弹出时加 class，并保存 selection bookmark
        setTimeout(() => {
          const aux = document.querySelector('.tox-tinymce-aux');
          if (aux) {
            aux.classList.add('wavy-dropdown-show');
          }
          // 保存 selection bookmark
          window._wavyUnderlineBookmark = editor.selection.getBookmark(2);
        }, 0);
        const items = [
          {
            type: 'menuitem',
            text: '',
            icon: 'wavycolor_none',
            onAction: function() {
              editor.focus();
              if (window._wavyUnderlineBookmark) {
                editor.selection.moveToBookmark(window._wavyUnderlineBookmark);
              }
              COLORS.forEach(function(color) {
                editor.formatter.remove('wavyunderline_' + color.replace('#',''));
              });
              // 移除 class
              const aux = document.querySelector('.tox-tinymce-aux');
              if (aux) aux.classList.remove('wavy-dropdown-show');
            }
          }
        ];
        COLORS.forEach(color => {
          items.push({
            type: 'menuitem',
            text: '',
            icon: 'wavycolor_' + color.replace('#',''),
            onAction: (function(selectedColor) {
              return function() {
                editor.focus();
                // 恢复 selection bookmark
                if (window._wavyUnderlineBookmark) {
                  editor.selection.moveToBookmark(window._wavyUnderlineBookmark);
                }

                // 移除所有其他波浪线格式
                COLORS.forEach(function(c) {
                  editor.formatter.remove('wavyunderline_' + c.replace('#',''));
                });
                
                var selection = editor.selection;
                if (selection && selection.isCollapsed()) {
                  // 有光标但无选区，插入空 span（用 tinymce API）
                  editor.insertContent('<span class="wavy-underline-' + selectedColor.replace('#','') + '">\u200B</span>');
                } else if (selection && !selection.isCollapsed()){
                  // 有选区，直接应用格式
                  editor.formatter.apply('wavyunderline_' + selectedColor.replace('#',''));
                }
                
                // 移除 class
                const aux = document.querySelector('.tox-tinymce-aux');
                if (aux) aux.classList.remove('wavy-dropdown-show');
              }
            })(color)
          });
        });
        callback(items);
      }
    });
  }

  // 7. 插件主入口
  function Plugin () {
    global.add('wavyunderline', function (editor) {
      registerIcons(editor);
      if (editor.formatter) {
        registerFormats(editor);
      } else {
        editor.on('init', function() {
          registerFormats(editor);
        });
      }
      if (editor.ui) {
        registerButton(editor);
      } else {
        editor.on('init', function() {
          registerButton(editor);
        });
      }
    });
  }

  // 全局只添加一次监听
  if (!window._wavyDropdownGlobalListener) {
    document.addEventListener('click', function(e) {
      const aux = document.querySelector('.tox-tinymce-aux');
      if (!aux) return;
      // 判断点击是否在菜单或按钮内
      const isInAux = aux.contains(e.target);
      // 你可以根据按钮的 class 或 id 判断是否是按钮
      const isWavyBtn = e.target.closest('.tox-tbtn[title="波浪线"]'); // 需根据实际按钮结构调整
      if (!isInAux && !isWavyBtn) {
        aux.classList.remove('wavy-dropdown-show');
      }
    }, true);
    window._wavyDropdownGlobalListener = true;
  }

  Plugin();
}()); 
