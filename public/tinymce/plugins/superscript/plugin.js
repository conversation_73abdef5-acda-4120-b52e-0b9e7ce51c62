/* eslint-disable no-var, prefer-const, no-undef */
/* global tinymce */
/* cSpell:words tinymce superscript formatter bogus Sups */
(function () {
  'use strict';

  var PluginManager = tinymce.util.Tools.resolve('tinymce.PluginManager');

  var register = function (editor) {
    editor.addCommand('mceToggleSuperscript', function () {
      editor.formatter.toggle('superscript');
    });
  };

  var registerButton = function (editor) {
    // 直接重写内置的superscript按钮
    editor.ui.registry.addToggleButton('superscript', {
      icon: 'superscript',
      tooltip: '上标',
      onAction: function () {
        editor.execCommand('mceToggleSuperscript');
      },
      onSetup: function (api) {
        // 初始化状态跟踪
        editor._superscriptHadContent = false;

        // 状态更新函数
        var updateState = function () {
          var isActive = false;
          var node = editor.selection.getNode();
          var selection = editor.selection;
          var isCollapsed = selection.isCollapsed();

          if (node) {
            // 检查是否在只包含br的空上标元素内
            var supElement = editor.dom.getParent(node, 'sup');
            if (supElement && supElement.innerHTML === '<br data-mce-bogus="1">') {
              console.log('🔧 [上标] 检测到空的上标元素，移出光标');
              // 将光标移到上标元素外
              var parentNode = supElement.parentNode;
              editor.dom.remove(supElement);
              editor.selection.setCursorLocation(parentNode, 0);
              // 重新获取节点
              node = editor.selection.getNode();
            }

            var elements = [];

            if (isCollapsed) {
              // 光标位置检测
              if (node.tagName === 'SUP') {
                elements = [node];
              } else {
                // 检查父级元素中的sup
                elements = editor.dom.getParents(node, 'sup', editor.getBody());

                // 如果没找到，并且当前节点是光标管理span，检查其子元素
                if (elements.length === 0 && node.getAttribute && node.getAttribute('data-mce-type') === 'format-caret') {
                  var supChild = node.querySelector ? node.querySelector('sup') : null;
                  if (supChild) {
                    elements = [supChild];
                  }
                }
              }
            } else {
              // 文本选择时，检查选择范围是否完全在上标元素内
              var range = selection.getRng();
              var startContainer = range.startContainer;
              var endContainer = range.endContainer;

              // 检查开始和结束容器是否都在同一个sup元素内
              var startSupElement = editor.dom.getParent(startContainer, 'sup');
              var endSupElement = editor.dom.getParent(endContainer, 'sup');

              if (startSupElement && endSupElement && startSupElement === endSupElement) {
                // 选择范围完全在同一个sup元素内
                elements = [startSupElement];
              }
            }

            if (elements && elements.length > 0) {
              var element = elements[0];
              var textContent = element.textContent || element.innerText || '';
              var textWithoutZWS = textContent.replace(/\uFEFF/g, '');

              if (textWithoutZWS.trim().length > 0) {
                editor._superscriptHadContent = true;
                isActive = true;
              } else if (textContent === '\uFEFF') {
                if (!editor._superscriptHadContent) {
                  isActive = true;
                } else {
                  // 之前有内容，现在只剩零宽度字符，需要进一步判断
                  var caretSpan = editor.dom.getParent(element, 'span[data-mce-type="format-caret"]');

                  if (caretSpan) {
                    // 在光标管理span中，检查是否是真正的删除操作
                    if (node.tagName === 'SUP') {
                      isActive = false;
                      editor._superscriptHadContent = false;

                      // 延迟清理
                      setTimeout(function() {
                        var currentNode = editor.selection.getNode();
                        var supElement = editor.dom.getParent(currentNode, 'sup');
                        if (supElement && supElement.textContent === '\uFEFF') {
                          // 保存光标位置
                          var parentNode = supElement.parentNode;
                          var nextSibling = supElement.nextSibling;

                          // 删除空元素
                          editor.dom.remove(supElement);

                          // 重新设置光标位置
                          if (nextSibling) {
                            editor.selection.setCursorLocation(nextSibling, 0);
                          } else {
                            editor.selection.setCursorLocation(parentNode, parentNode.childNodes.length);
                          }
                        }
                      }, 1);
                    } else {
                      isActive = true;
                    }
                  } else {
                    isActive = false;
                    editor._superscriptHadContent = false;

                    // 延迟清理
                    setTimeout(function() {
                      var currentNode = editor.selection.getNode();
                      var supElement = editor.dom.getParent(currentNode, 'sup');
                      if (supElement && supElement.textContent === '\uFEFF') {
                        // 保存光标位置
                        var parentNode = supElement.parentNode;
                        var nextSibling = supElement.nextSibling;

                        // 删除空元素
                        editor.dom.remove(supElement);

                        // 重新设置光标位置
                        if (nextSibling) {
                          editor.selection.setCursorLocation(nextSibling, 0);
                        } else {
                          editor.selection.setCursorLocation(parentNode, parentNode.childNodes.length);
                        }
                      }
                    }, 1);
                  }
                }
              } else {
                editor._superscriptHadContent = false;
                isActive = false;
              }
            } else {
              editor._superscriptHadContent = false;
              isActive = false;
            }
          } else {
            editor._superscriptHadContent = false;
            isActive = false;
          }

          api.setActive(isActive);
        };

        // 初始状态更新
        updateState();

        // 注册NodeChange监听器
        editor.on('NodeChange', updateState);

        // 返回清理函数
        return function () {
          editor.off('NodeChange', updateState);
        };
      }
    });
  };

  var setupCleanup = function (editor) {
    var cleanupEmptyElements = function () {
      var body = editor.getBody();
      if (!body) return;

      var bookmark = editor.selection.getBookmark(2, true);
      var hasProcessed = false;

      // 只清理真正空的元素，不清理包含光标的元素
      var emptySupElements = body.querySelectorAll('sup:empty, sup[data-mce-bogus="1"]');
      for (var i = 0; i < emptySupElements.length; i++) {
        var element = emptySupElements[i];

        // 检查光标是否在这个元素内
        var currentNode = editor.selection.getNode();
        var isCurrentElement = currentNode === element || element.contains(currentNode);

        // 只删除不包含光标的空元素
        if (!isCurrentElement) {
          element.remove();
          hasProcessed = true;
        }
      }

      // 如果进行了处理，恢复光标位置
      if (hasProcessed) {
        try {
          editor.selection.moveToBookmark(bookmark);
        } catch (e) {
          // 忽略恢复失败
        }
      }
    };

    // 减少清理频率，不在NodeChange时清理
    editor.on('Change SetContent KeyUp', function() {
      setTimeout(cleanupEmptyElements, 0);
    });

    editor.on('init', function() {
      setTimeout(cleanupEmptyElements, 10);
    });
  };

  var Plugin = function () {
    PluginManager.add('superscript', function (editor) {
      register(editor);

      // 使用PostRender事件注册按钮
      editor.on('PostRender', function() {
        registerButton(editor);
      });

      setupCleanup(editor);

      // 使用TinyMCE默认回车行为
      console.log('🔧 [上标] 使用TinyMCE默认回车行为');
    });
  };

  Plugin();
})();
