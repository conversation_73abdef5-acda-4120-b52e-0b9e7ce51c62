/* eslint-disable no-var, prefer-const, no-undef */
/* global tinymce */
/* cSpell:words tinymce subscript formatter bogus Subs */
(function () {
  'use strict';

  var PluginManager = tinymce.util.Tools.resolve('tinymce.PluginManager');

  var register = function (editor) {
    editor.addCommand('mceToggleSubscript', function () {
      editor.formatter.toggle('subscript');
    });
  };

  var registerButton = function (editor) {
    // 直接重写内置的subscript按钮
    editor.ui.registry.addToggleButton('subscript', {
      icon: 'subscript',
      tooltip: '下标',
      onAction: function () {
        editor.execCommand('mceToggleSubscript');
      },
      onSetup: function (api) {
        // 初始化状态跟踪
        editor._subscriptHadContent = false;

        // 状态更新函数
        var updateState = function () {
          var isActive = false;
          var node = editor.selection.getNode();
          var selection = editor.selection;
          var isCollapsed = selection.isCollapsed();

          if (node) {
            var elements = [];

            if (isCollapsed) {
              // 光标位置检测
              if (node.tagName === 'SUB') {
                elements = [node];
              } else {
                // 检查父级元素中的sub
                elements = editor.dom.getParents(node, 'sub', editor.getBody());

                // 如果没找到，并且当前节点是光标管理span，检查其子元素
                if (elements.length === 0 && node.getAttribute && node.getAttribute('data-mce-type') === 'format-caret') {
                  var subChild = node.querySelector ? node.querySelector('sub') : null;
                  if (subChild) {
                    elements = [subChild];
                  }
                }
              }
            } else {
              // 文本选择时，检查选择范围是否完全在下标元素内
              var range = selection.getRng();
              var startContainer = range.startContainer;
              var endContainer = range.endContainer;

              // 检查开始和结束容器是否都在同一个sub元素内
              var startSubElement = editor.dom.getParent(startContainer, 'sub');
              var endSubElement = editor.dom.getParent(endContainer, 'sub');

              if (startSubElement && endSubElement && startSubElement === endSubElement) {
                // 选择范围完全在同一个sub元素内
                elements = [startSubElement];
              }
            }

            if (elements && elements.length > 0) {
              var element = elements[0];
              var textContent = element.textContent || element.innerText || '';
              var textWithoutZWS = textContent.replace(/\uFEFF/g, '');

              if (textWithoutZWS.trim().length > 0) {
                editor._subscriptHadContent = true;
                isActive = true;
              } else if (textContent === '\uFEFF') {
                if (!editor._subscriptHadContent) {
                  isActive = true;
                } else {
                  // 之前有内容，现在只剩零宽度字符，需要进一步判断
                  var caretSpan = editor.dom.getParent(element, 'span[data-mce-type="format-caret"]');

                  if (caretSpan) {
                    // 在光标管理span中，检查是否是真正的删除操作
                    if (node.tagName === 'SUB') {
                      isActive = false;
                      editor._subscriptHadContent = false;

                      // 延迟清理
                      setTimeout(function() {
                        var currentNode = editor.selection.getNode();
                        var subElement = editor.dom.getParent(currentNode, 'sub');
                        if (subElement && subElement.textContent === '\uFEFF') {
                          // 检查是否在格式光标管理span内
                          var caretSpan = editor.dom.getParent(subElement, 'span[data-mce-type="format-caret"]');
                          var targetElement = caretSpan || subElement;
                          var parentNode = targetElement.parentNode;
                          var nextSibling = targetElement.nextSibling;

                          // 删除目标元素（可能是格式光标span或sub元素）
                          editor.dom.remove(targetElement);

                          // 重新设置光标位置
                          if (nextSibling) {
                            editor.selection.setCursorLocation(nextSibling, 0);
                          } else {
                            editor.selection.setCursorLocation(parentNode, parentNode.childNodes.length);
                          }
                        }
                      }, 1);
                    } else {
                      isActive = true;
                    }
                  } else {
                    isActive = false;
                    editor._subscriptHadContent = false;

                    // 延迟清理
                    setTimeout(function() {
                      var currentNode = editor.selection.getNode();
                      var subElement = editor.dom.getParent(currentNode, 'sub');
                      if (subElement && subElement.textContent === '\uFEFF') {
                        // 检查是否在格式光标管理span内
                        var caretSpan = editor.dom.getParent(subElement, 'span[data-mce-type="format-caret"]');
                        var targetElement = caretSpan || subElement;
                        var parentNode = targetElement.parentNode;
                        var nextSibling = targetElement.nextSibling;

                        // 删除目标元素（可能是格式光标span或sub元素）
                        editor.dom.remove(targetElement);

                        // 重新设置光标位置
                        if (nextSibling) {
                          editor.selection.setCursorLocation(nextSibling, 0);
                        } else {
                          editor.selection.setCursorLocation(parentNode, parentNode.childNodes.length);
                        }
                      }
                    }, 1);
                  }
                }
              } else {
                editor._subscriptHadContent = false;
                isActive = false;
              }
            } else {
              editor._subscriptHadContent = false;
              isActive = false;
            }
          } else {
            editor._subscriptHadContent = false;
            isActive = false;
          }

          api.setActive(isActive);
        };

        // 初始状态更新
        updateState();

        // 注册NodeChange监听器
        editor.on('NodeChange', updateState);

        // 返回清理函数
        return function () {
          editor.off('NodeChange', updateState);
        };
      }
    });
  };

  var setupCleanup = function (editor) {
    var cleanupEmptyElements = function () {
      var body = editor.getBody();
      if (!body) return;

      var bookmark = editor.selection.getBookmark(2, true);
      var hasProcessed = false;

      // 只清理真正空的元素，不清理包含光标的元素
      var emptySubElements = body.querySelectorAll('sub:empty, sub[data-mce-bogus="1"]');
      for (var i = 0; i < emptySubElements.length; i++) {
        var element = emptySubElements[i];

        // 检查光标是否在这个元素内
        var currentNode = editor.selection.getNode();
        var isCurrentElement = currentNode === element || element.contains(currentNode);

        // 只删除不包含光标的空元素
        if (!isCurrentElement) {
          element.remove();
          hasProcessed = true;
        }
      }

      // 如果进行了处理，恢复光标位置
      if (hasProcessed) {
        try {
          editor.selection.moveToBookmark(bookmark);
        } catch (e) {
          // 忽略恢复失败
        }
      }
    };

    // 减少清理频率，不在NodeChange时清理
    editor.on('Change SetContent KeyUp', function() {
      setTimeout(cleanupEmptyElements, 0);
    });

    editor.on('init', function() {
      setTimeout(cleanupEmptyElements, 100);
    });
  };

  var Plugin = function () {
    PluginManager.add('subscript', function (editor) {
      register(editor);

      // 使用PostRender事件注册按钮
      editor.on('PostRender', function() {
        registerButton(editor);
      });

      setupCleanup(editor);

      // 使用TinyMCE默认回车行为
      console.log('🔧 [下标] 使用TinyMCE默认回车行为');
    });
  };

  Plugin();
})();
