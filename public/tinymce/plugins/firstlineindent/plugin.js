/* eslint-disable no-undef */
/* eslint-disable no-var */
(function () {
  'use strict';

  var global = tinymce.util.Tools.resolve('tinymce.PluginManager');

  // 动态计算text-indent
  // 递归查找第一个有内容的文本节点 (REMOVED as it's no longer needed)

  function calcIndentPx (pNode) {
    // 简化逻辑：始终使用段落本身的字体大小进行计算，以避免由sup/sub等内联元素引起的复杂性。
    // This makes the indent calculation more robust and predictable.
    var fontSize = window.getComputedStyle(pNode).fontSize;
    console.log('[首行缩进] 使用p自身字号:', fontSize);

    if (fontSize) {
      var px = fontSize;
      if (!/px$/.test(px)) {
        var temp = document.createElement('span');
        temp.style.fontSize = fontSize;
        document.body.appendChild(temp);
        px = window.getComputedStyle(temp).fontSize;
        document.body.removeChild(temp);
        console.log('[首行缩进] 转换为px:', px);
      }
      var num = parseFloat(px);
      if (!isNaN(num)) {
        return (num * 2) + 'px';
      } else {
        console.warn('[首行缩进] 字号无法解析:', fontSize);
        return '0px';
      }
    } else {
      console.warn('[首行缩进] 未找到字号，未设置缩进');
      return '0px';
    }
  }

  function registerFirstLineIndentFormat (editor) {
    editor.formatter.register('firstlineindent', {
      block: 'p',
      styles: { 'text-indent': '%value' },
      attributes: { 'data-firstlineindent': '1' }
    });
  }

  function applyDynamicFirstLineIndent (editor, pNode) {
    var px = calcIndentPx(pNode);
    editor.formatter.apply('firstlineindent', { value: px }, pNode);
  }

  function removeDynamicFirstLineIndent (editor, pNode) {
    // 1. 移除格式
    var indent = pNode.style.textIndent || (pNode.getAttribute('data-mce-style') || '').match(/text-indent:([^;]+)/)?.[1];
    if (indent) {
      editor.formatter.remove('firstlineindent', { value: indent.trim() }, pNode);
    } else {
      editor.formatter.remove('firstlineindent', {}, pNode);
    }
    // 2. 手动清理
    if (pNode.style) pNode.style.textIndent = '';
    var mceStyle = pNode.getAttribute('data-mce-style');
    if (mceStyle) {
      var newStyle = mceStyle.replace(/text-indent:[^;]+;?/gi, '').replace(/^\s*;\s*|\s*;\s*$/g, '');
      if (newStyle.trim()) {
        pNode.setAttribute('data-mce-style', newStyle);
      } else {
        pNode.removeAttribute('data-mce-style');
      }
    }
    editor.nodeChanged();
  }

  // 获取当前段落
  function getCurrentParagraph (editor) {
    var node = editor.selection && editor.selection.getNode && editor.selection.getNode();
    if (!node) node = editor.selection && editor.selection.getStart && editor.selection.getStart();
    if (!node) node = editor.selection && editor.selection.getEnd && editor.selection.getEnd();
    if (!node && window.getSelection) {
      var sel = window.getSelection();
      if (sel && sel.anchorNode) node = sel.anchorNode;
    }
    while (node && node.nodeName !== 'P' && node.parentElement) {
      node = node.parentElement;
    }
    return node && node.nodeName === 'P' ? node : null;
  }

  function registerButton (editor) {
    editor.ui.registry.addToggleButton('firstlineindent', {
      icon: 'firstlineindent',
      tooltip: '首行缩进',
      onAction: function () {
        var selection = editor.selection;
        // 记录操作前是否有焦点和选区
        var hadFocus = editor.hasFocus();
        var bookmark = editor.selection.getBookmark(1); // 保存光标/选区，使用类型1书签（基于DOM ID）
        editor.undoManager.transact(function () {
          var selection = editor.selection;
          var hadSelection = selection && !selection.isCollapsed();
          var currentParagraph = getCurrentParagraph(editor);

          if (hadSelection) {
            var bookmarkSel = bookmark;
            var rng = selection.getRng();
            var ps = [];

            // 保存选区的详细信息作为备用
            var startContainer = rng.startContainer;
            var startOffset = rng.startOffset;
            var endContainer = rng.endContainer;
            var endOffset = rng.endOffset;
            var walker = document.createTreeWalker(editor.getBody(), NodeFilter.SHOW_ELEMENT, {
              acceptNode: function (n) { return n.nodeName === 'P' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP; }
            });
            var pNode;
            while ((pNode = walker.nextNode())) {
              var range = document.createRange();
              range.selectNodeContents(pNode);
              if ((range.compareBoundaryPoints(Range.END_TO_START, rng) <= 0 && range.compareBoundaryPoints(Range.START_TO_END, rng) >= 0) || rng.intersectsNode(pNode)) {
                ps.push(pNode);
              }
            }
            console.log('[首行缩进][日志] 选区内段落数:', ps.length, ps);
            var allIndentedSel = ps.length > 0 && ps.every(function (p) { return p.getAttribute('data-firstlineindent') === '1'; });
            ps.forEach(function (p) {
              if (allIndentedSel) {
                removeDynamicFirstLineIndent(editor, p);
              } else {
                if (p.getAttribute('data-firstlineindent') !== '1') {
                  applyDynamicFirstLineIndent(editor, p);
                }
              }
            });
            console.log("%c Line:142 🍔 hadFocus", "color:#93c0a4", hadFocus, hadSelection);
            // if (hadSelection) {


            // 延迟选区恢复，确保DOM操作完成
            setTimeout(function () {
              try {

                var newRng = editor.dom.createRng();
                newRng.setStart(startContainer, startOffset);
                newRng.setEnd(endContainer, endOffset);
                editor.selection.setRng(newRng);
                console.log('[首行缩进][日志] 手动选区恢复完成');

              } catch (e) {
                // 如果书签恢复失败，尝试手动恢复选区
                console.log('[首行缩进][日志] 书签恢复失败:', e);
              }
              editor.focus();
              editor.nodeChanged();
            }, 0);
            // }
            console.log('[首行缩进][日志] 恢复选区bookmark:', bookmarkSel);
            return;
          } else if (currentParagraph) {
            // 只处理当前段落
            var node = currentParagraph;
            var bookmarkNode = editor.selection.getBookmark(1); // 只在此分支保存bookmark，使用类型1书签（基于DOM ID）
            console.log('[首行缩进] 按钮点击，最近的P标签:', node);
            if (node) {
              if (node.getAttribute('data-firstlineindent') === '1') {
                removeDynamicFirstLineIndent(editor, node);
              } else {
                applyDynamicFirstLineIndent(editor, node);
              }
            } else {
              console.warn('[首行缩进] 未找到P标签，无法缩进');
            }
            // 只在此分支恢复光标
            editor.selection.moveToBookmark(bookmarkNode);
            editor.focus();
            editor.nodeChanged();
            return;
          } else {
            // 没有选区也没有当前段落，全量处理
            var allP = editor.getBody().querySelectorAll('p');
            var allIndented = true;
            allP.forEach(function (p) {
              if (p.getAttribute('data-firstlineindent') !== '1') allIndented = false;
            });
            allP.forEach(function (p) {
              if (allIndented) {
                removeDynamicFirstLineIndent(editor, p);
              } else {
                if (p.getAttribute('data-firstlineindent') !== '1') {
                  applyDynamicFirstLineIndent(editor, p);
                }
              }
            });
            editor.nodeChanged();
            return;
          }
        });
      },
      onSetup: function (api) {
        editor.on('NodeChange', function () {
          var node = editor.selection.getNode();
          while (node && node.nodeName !== 'P' && node.parentElement) {
            node = node.parentElement;
          }
          api.setActive(node && node.nodeName === 'P' && node.getAttribute('data-firstlineindent') === '1');
        });
      }
    });
  }

  function registerAutoSync (editor) {
    // 只在字号变化时同步
    editor.on('ExecCommand', function (e) {
      if (e.command && e.command.toLowerCase() === 'fontsize') {
        var node = editor.selection.getNode();
        while (node && node.nodeName !== 'P' && node.parentElement) {
          node = node.parentElement;
        }
        if (node && node.nodeName === 'P' && node.getAttribute('data-firstlineindent') === '1') {
          var px = calcIndentPx(node);
          editor.formatter.apply('firstlineindent', { value: px }, node);
          console.log('[首行缩进] 字号变化后自动同步text-indent为最新字号');
        }
      }
    });
    // NodeChange时自动修复丢失的text-indent
    // editor.on('NodeChange', function () {
    //   var node = editor.selection.getNode();
    //   while (node && node.nodeName !== 'P' && node.parentElement) {
    //     node = node.parentElement;
    //   }
    //   if (node && node.nodeName === 'P' && node.getAttribute('data-firstlineindent') === '1') {
    //     var px = calcIndentPx(node);
    //     if (!node.style.textIndent || node.style.textIndent === '0px' || node.style.textIndent === '0em' || node.style.textIndent === '2em') {
    //       editor.formatter.apply('firstlineindent', { value: px }, node);
    //       console.log('[首行缩进] NodeChange自动修复text-indent');
    //     }
    //   }
    // }); // 修复text-indent丢失的问题，暂时不自动修复，因为会执行这段代码后聚焦时缩进会丢失并且焦点会到首行
  }

  function Plugin () {
    global.add('firstlineindent', function (editor) {
      if (editor.formatter) {
        registerFirstLineIndentFormat(editor);
      } else {
        editor.on('init', function () {
          registerFirstLineIndentFormat(editor);
        });
      }

      registerButton(editor);
      // registerAutoSync(editor);
    });
  }

  Plugin();
}()); 