!function(){"use strict";var c={8667:function(c,f){f.q=void 0,f.q="3.2.2"},2308:function(c,f,i){var t,e=this&&this.__extends||(t=function(c,f){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var i in f)Object.prototype.hasOwnProperty.call(f,i)&&(c[i]=f[i])},t(c,f)},function(c,f){if("function"!=typeof f&&null!==f)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");function i(){this.constructor=c}t(c,f),c.prototype=null===f?Object.create(f):(i.prototype=f.prototype,new i)}),s=this&&this.__assign||function(){return s=Object.assign||function(c){for(var f,i=1,t=arguments.length;i<t;i++)for(var e in f=arguments[i])Object.prototype.hasOwnProperty.call(f,e)&&(c[e]=f[e]);return c},s.apply(this,arguments)};Object.defineProperty(f,"__esModule",{value:!0}),f.TeXFont=void 0;var r=i(73),o=i(2176),a=i(7841),S=i(5031),l=i(6051),n=i(9236),B=i(1937),d=i(4244),u=i(482),k=i(196),I=i(527),A=i(3518),p=i(965),m=i(9169),h=i(6736),M=i(2290),x=i(3012),H=i(8787),_=i(5392),E=i(6318),y=i(5351),R=i(873),v=i(7611),D=i(6590),b=i(8798),T=i(2138),X=i(7481),O=function(c){function f(){return null!==c&&c.apply(this,arguments)||this}return e(f,c),f.defaultCssFamilyPrefix="MJXZERO",f.defaultVariantClasses={normal:"mjx-n",bold:"mjx-b",italic:"mjx-i","bold-italic":"mjx-b mjx-i","double-struck":"mjx-ds mjx-b",fraktur:"mjx-fr","bold-fraktur":"mjx-fr mjx-b",script:"mjx-sc mjx-i","bold-script":"mjx-sc mjx-b mjx-i","sans-serif":"mjx-ss","bold-sans-serif":"mjx-ss mjx-b","sans-serif-italic":"mjx-ss mjx-i","sans-serif-bold-italic":"mjx-ss mjx-b mjx-i",monospace:"mjx-ty","-smallop":"mjx-sop","-largeop":"mjx-lop","-size3":"mjx-s3","-size4":"mjx-s4","-tex-calligraphic":"mjx-cal mjx-i","-tex-bold-calligraphic":"mjx-cal mjx-b","-tex-mathit":"mjx-mit mjx-i","-tex-oldstyle":"mjx-os","-tex-bold-oldstyle":"mjx-os mjx-b","-tex-variant":"mjx-var"},f.defaultVariantLetters={normal:"",bold:"B",italic:"MI","bold-italic":"BI","double-struck":"A",fraktur:"FR","bold-fraktur":"FRB",script:"SC","bold-script":"SCB","sans-serif":"SS","bold-sans-serif":"SSB","sans-serif-italic":"SSI","sans-serif-bold-italic":"SSBI",monospace:"T","-smallop":"S1","-largeop":"S2","-size3":"S3","-size4":"S4","-tex-calligraphic":"C","-tex-bold-calligraphic":"CB","-tex-mathit":"MI","-tex-oldstyle":"C","-tex-bold-oldstyle":"CB","-tex-variant":"A"},f.defaultDelimiters=X.delimiters,f.defaultChars={normal:I.normal,bold:S.bold,italic:d.italic,"bold-italic":a.boldItalic,"double-struck":l.doubleStruck,fraktur:B.fraktur,"bold-fraktur":n.frakturBold,script:x.script,"bold-script":M.scriptBold,"sans-serif":h.sansSerif,"bold-sans-serif":p.sansSerifBold,"sans-serif-italic":m.sansSerifItalic,"sans-serif-bold-italic":A.sansSerifBoldItalic,monospace:k.monospace,"-smallop":H.smallop,"-largeop":u.largeop,"-size3":D.texSize3,"-size4":b.texSize4,"-tex-calligraphic":E.texCalligraphic,"-tex-bold-calligraphic":_.texCalligraphicBold,"-tex-mathit":y.texMathit,"-tex-oldstyle":v.texOldstyle,"-tex-bold-oldstyle":R.texOldstyleBold,"-tex-variant":T.texVariant},f.defaultStyles=s(s({},r.CHTMLFontData.defaultStyles),{".MJX-TEX":{"font-family":"MJXZERO, MJXTEX"},".TEX-B":{"font-family":"MJXZERO, MJXTEX-B"},".TEX-I":{"font-family":"MJXZERO, MJXTEX-I"},".TEX-MI":{"font-family":"MJXZERO, MJXTEX-MI"},".TEX-BI":{"font-family":"MJXZERO, MJXTEX-BI"},".TEX-S1":{"font-family":"MJXZERO, MJXTEX-S1"},".TEX-S2":{"font-family":"MJXZERO, MJXTEX-S2"},".TEX-S3":{"font-family":"MJXZERO, MJXTEX-S3"},".TEX-S4":{"font-family":"MJXZERO, MJXTEX-S4"},".TEX-A":{"font-family":"MJXZERO, MJXTEX-A"},".TEX-C":{"font-family":"MJXZERO, MJXTEX-C"},".TEX-CB":{"font-family":"MJXZERO, MJXTEX-CB"},".TEX-FR":{"font-family":"MJXZERO, MJXTEX-FR"},".TEX-FRB":{"font-family":"MJXZERO, MJXTEX-FRB"},".TEX-SS":{"font-family":"MJXZERO, MJXTEX-SS"},".TEX-SSB":{"font-family":"MJXZERO, MJXTEX-SSB"},".TEX-SSI":{"font-family":"MJXZERO, MJXTEX-SSI"},".TEX-SC":{"font-family":"MJXZERO, MJXTEX-SC"},".TEX-T":{"font-family":"MJXZERO, MJXTEX-T"},".TEX-V":{"font-family":"MJXZERO, MJXTEX-V"},".TEX-VB":{"font-family":"MJXZERO, MJXTEX-VB"},"mjx-stretchy-v mjx-c, mjx-stretchy-h mjx-c":{"font-family":"MJXZERO, MJXTEX-S1, MJXTEX-S4, MJXTEX, MJXTEX-A ! important"}}),f.defaultFonts=s(s({},r.CHTMLFontData.defaultFonts),{"@font-face /* 1 */":{"font-family":"MJXTEX",src:'url("%%URL%%/MathJax_Main-Regular.woff") format("woff")'},"@font-face /* 2 */":{"font-family":"MJXTEX-B",src:'url("%%URL%%/MathJax_Main-Bold.woff") format("woff")'},"@font-face /* 3 */":{"font-family":"MJXTEX-I",src:'url("%%URL%%/MathJax_Math-Italic.woff") format("woff")'},"@font-face /* 4 */":{"font-family":"MJXTEX-MI",src:'url("%%URL%%/MathJax_Main-Italic.woff") format("woff")'},"@font-face /* 5 */":{"font-family":"MJXTEX-BI",src:'url("%%URL%%/MathJax_Math-BoldItalic.woff") format("woff")'},"@font-face /* 6 */":{"font-family":"MJXTEX-S1",src:'url("%%URL%%/MathJax_Size1-Regular.woff") format("woff")'},"@font-face /* 7 */":{"font-family":"MJXTEX-S2",src:'url("%%URL%%/MathJax_Size2-Regular.woff") format("woff")'},"@font-face /* 8 */":{"font-family":"MJXTEX-S3",src:'url("%%URL%%/MathJax_Size3-Regular.woff") format("woff")'},"@font-face /* 9 */":{"font-family":"MJXTEX-S4",src:'url("%%URL%%/MathJax_Size4-Regular.woff") format("woff")'},"@font-face /* 10 */":{"font-family":"MJXTEX-A",src:'url("%%URL%%/MathJax_AMS-Regular.woff") format("woff")'},"@font-face /* 11 */":{"font-family":"MJXTEX-C",src:'url("%%URL%%/MathJax_Calligraphic-Regular.woff") format("woff")'},"@font-face /* 12 */":{"font-family":"MJXTEX-CB",src:'url("%%URL%%/MathJax_Calligraphic-Bold.woff") format("woff")'},"@font-face /* 13 */":{"font-family":"MJXTEX-FR",src:'url("%%URL%%/MathJax_Fraktur-Regular.woff") format("woff")'},"@font-face /* 14 */":{"font-family":"MJXTEX-FRB",src:'url("%%URL%%/MathJax_Fraktur-Bold.woff") format("woff")'},"@font-face /* 15 */":{"font-family":"MJXTEX-SS",src:'url("%%URL%%/MathJax_SansSerif-Regular.woff") format("woff")'},"@font-face /* 16 */":{"font-family":"MJXTEX-SSB",src:'url("%%URL%%/MathJax_SansSerif-Bold.woff") format("woff")'},"@font-face /* 17 */":{"font-family":"MJXTEX-SSI",src:'url("%%URL%%/MathJax_SansSerif-Italic.woff") format("woff")'},"@font-face /* 18 */":{"font-family":"MJXTEX-SC",src:'url("%%URL%%/MathJax_Script-Regular.woff") format("woff")'},"@font-face /* 19 */":{"font-family":"MJXTEX-T",src:'url("%%URL%%/MathJax_Typewriter-Regular.woff") format("woff")'},"@font-face /* 20 */":{"font-family":"MJXTEX-V",src:'url("%%URL%%/MathJax_Vector-Regular.woff") format("woff")'},"@font-face /* 21 */":{"font-family":"MJXTEX-VB",src:'url("%%URL%%/MathJax_Vector-Bold.woff") format("woff")'}}),f}((0,o.CommonTeXFontMixin)(r.CHTMLFontData));f.TeXFont=O},7841:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.boldItalic=void 0;var t=i(73),e=i(3869);f.boldItalic=(0,t.AddCSS)(e.boldItalic,{305:{f:"B"},567:{f:"B"},8260:{c:"/"},8710:{c:"\\394"},10744:{c:"/"}})},5031:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.bold=void 0;var t=i(73),e=i(7557);f.bold=(0,t.AddCSS)(e.bold,{183:{c:"\\22C5"},305:{f:""},567:{f:""},697:{c:"\\2032"},8194:{c:""},8195:{c:""},8196:{c:""},8197:{c:""},8198:{c:""},8201:{c:""},8202:{c:""},8213:{c:"\\2014"},8214:{c:"\\2225"},8215:{c:"_"},8226:{c:"\\2219"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8254:{c:"\\2C9"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8407:{c:"\\2192",f:"VB"},8602:{c:"\\2190\\338"},8603:{c:"\\2192\\338"},8622:{c:"\\2194\\338"},8653:{c:"\\21D0\\338"},8654:{c:"\\21D4\\338"},8655:{c:"\\21D2\\338"},8708:{c:"\\2203\\338"},8710:{c:"\\394"},8716:{c:"\\220B\\338"},8740:{c:"\\2223\\338"},8742:{c:"\\2225\\338"},8769:{c:"\\223C\\338"},8772:{c:"\\2243\\338"},8775:{c:"\\2245\\338"},8777:{c:"\\2248\\338"},8802:{c:"\\2261\\338"},8813:{c:"\\224D\\338"},8814:{c:"<\\338"},8815:{c:">\\338"},8816:{c:"\\2264\\338"},8817:{c:"\\2265\\338"},8832:{c:"\\227A\\338"},8833:{c:"\\227B\\338"},8836:{c:"\\2282\\338"},8837:{c:"\\2283\\338"},8840:{c:"\\2286\\338"},8841:{c:"\\2287\\338"},8876:{c:"\\22A2\\338"},8877:{c:"\\22A8\\338"},8930:{c:"\\2291\\338"},8931:{c:"\\2292\\338"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},9653:{c:"\\25B3"},9663:{c:"\\25BD"},10072:{c:"\\2223"},10744:{c:"/",f:"BI"},10799:{c:"\\D7"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},6051:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.doubleStruck=void 0;var t=i(5674);Object.defineProperty(f,"doubleStruck",{enumerable:!0,get:function(){return t.doubleStruck}})},9236:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.frakturBold=void 0;var t=i(73),e=i(7002);f.frakturBold=(0,t.AddCSS)(e.frakturBold,{8260:{c:"/"}})},1937:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.fraktur=void 0;var t=i(73),e=i(9349);f.fraktur=(0,t.AddCSS)(e.fraktur,{8260:{c:"/"}})},4244:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.italic=void 0;var t=i(73),e=i(9741);f.italic=(0,t.AddCSS)(e.italic,{47:{f:"I"},989:{c:"\\E008",f:"A"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/",f:"I"},8710:{c:"\\394",f:"I"},10744:{c:"/",f:"I"}})},482:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.largeop=void 0;var t=i(73),e=i(2827);f.largeop=(0,t.AddCSS)(e.largeop,{8214:{f:"S1"},8260:{c:"/"},8593:{f:"S1"},8595:{f:"S1"},8657:{f:"S1"},8659:{f:"S1"},8739:{f:"S1"},8741:{f:"S1"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},9168:{f:"S1"},10072:{c:"\\2223",f:"S1"},10764:{c:"\\222C\\222C"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},196:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.monospace=void 0;var t=i(73),e=i(2970);f.monospace=(0,t.AddCSS)(e.monospace,{697:{c:"\\2032"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},932:{c:"T"},935:{c:"X"},8215:{c:"_"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8710:{c:"\\394"}})},527:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.normal=void 0;var t=i(73),e=i(1668);f.normal=(0,t.AddCSS)(e.normal,{163:{f:"MI"},165:{f:"A"},174:{f:"A"},183:{c:"\\22C5"},240:{f:"A"},697:{c:"\\2032"},913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},932:{c:"T"},935:{c:"X"},8192:{c:""},8193:{c:""},8194:{c:""},8195:{c:""},8196:{c:""},8197:{c:""},8198:{c:""},8201:{c:""},8202:{c:""},8203:{c:""},8204:{c:""},8213:{c:"\\2014"},8214:{c:"\\2225"},8215:{c:"_"},8226:{c:"\\2219"},8243:{c:"\\2032\\2032"},8244:{c:"\\2032\\2032\\2032"},8245:{f:"A"},8246:{c:"\\2035\\2035",f:"A"},8247:{c:"\\2035\\2035\\2035",f:"A"},8254:{c:"\\2C9"},8260:{c:"/"},8279:{c:"\\2032\\2032\\2032\\2032"},8288:{c:""},8289:{c:""},8290:{c:""},8291:{c:""},8292:{c:""},8407:{c:"\\2192",f:"V"},8450:{c:"C",f:"A"},8459:{c:"H",f:"SC"},8460:{c:"H",f:"FR"},8461:{c:"H",f:"A"},8462:{c:"h",f:"I"},8463:{f:"A"},8464:{c:"I",f:"SC"},8465:{c:"I",f:"FR"},8466:{c:"L",f:"SC"},8469:{c:"N",f:"A"},8473:{c:"P",f:"A"},8474:{c:"Q",f:"A"},8475:{c:"R",f:"SC"},8476:{c:"R",f:"FR"},8477:{c:"R",f:"A"},8484:{c:"Z",f:"A"},8486:{c:"\\3A9"},8487:{f:"A"},8488:{c:"Z",f:"FR"},8492:{c:"B",f:"SC"},8493:{c:"C",f:"FR"},8496:{c:"E",f:"SC"},8497:{c:"F",f:"SC"},8498:{f:"A"},8499:{c:"M",f:"SC"},8502:{f:"A"},8503:{f:"A"},8504:{f:"A"},8513:{f:"A"},8602:{f:"A"},8603:{f:"A"},8606:{f:"A"},8608:{f:"A"},8610:{f:"A"},8611:{f:"A"},8619:{f:"A"},8620:{f:"A"},8621:{f:"A"},8622:{f:"A"},8624:{f:"A"},8625:{f:"A"},8630:{f:"A"},8631:{f:"A"},8634:{f:"A"},8635:{f:"A"},8638:{f:"A"},8639:{f:"A"},8642:{f:"A"},8643:{f:"A"},8644:{f:"A"},8646:{f:"A"},8647:{f:"A"},8648:{f:"A"},8649:{f:"A"},8650:{f:"A"},8651:{f:"A"},8653:{f:"A"},8654:{f:"A"},8655:{f:"A"},8666:{f:"A"},8667:{f:"A"},8669:{f:"A"},8672:{f:"A"},8674:{f:"A"},8705:{f:"A"},8708:{c:"\\2203\\338"},8710:{c:"\\394"},8716:{c:"\\220B\\338"},8717:{f:"A"},8719:{f:"S1"},8720:{f:"S1"},8721:{f:"S1"},8724:{f:"A"},8737:{f:"A"},8738:{f:"A"},8740:{f:"A"},8742:{f:"A"},8748:{f:"S1"},8749:{f:"S1"},8750:{f:"S1"},8756:{f:"A"},8757:{f:"A"},8765:{f:"A"},8769:{f:"A"},8770:{f:"A"},8772:{c:"\\2243\\338"},8775:{c:"\\2246",f:"A"},8777:{c:"\\2248\\338"},8778:{f:"A"},8782:{f:"A"},8783:{f:"A"},8785:{f:"A"},8786:{f:"A"},8787:{f:"A"},8790:{f:"A"},8791:{f:"A"},8796:{f:"A"},8802:{c:"\\2261\\338"},8806:{f:"A"},8807:{f:"A"},8808:{f:"A"},8809:{f:"A"},8812:{f:"A"},8813:{c:"\\224D\\338"},8814:{f:"A"},8815:{f:"A"},8816:{f:"A"},8817:{f:"A"},8818:{f:"A"},8819:{f:"A"},8820:{c:"\\2272\\338"},8821:{c:"\\2273\\338"},8822:{f:"A"},8823:{f:"A"},8824:{c:"\\2276\\338"},8825:{c:"\\2277\\338"},8828:{f:"A"},8829:{f:"A"},8830:{f:"A"},8831:{f:"A"},8832:{f:"A"},8833:{f:"A"},8836:{c:"\\2282\\338"},8837:{c:"\\2283\\338"},8840:{f:"A"},8841:{f:"A"},8842:{f:"A"},8843:{f:"A"},8847:{f:"A"},8848:{f:"A"},8858:{f:"A"},8859:{f:"A"},8861:{f:"A"},8862:{f:"A"},8863:{f:"A"},8864:{f:"A"},8865:{f:"A"},8873:{f:"A"},8874:{f:"A"},8876:{f:"A"},8877:{f:"A"},8878:{f:"A"},8879:{f:"A"},8882:{f:"A"},8883:{f:"A"},8884:{f:"A"},8885:{f:"A"},8888:{f:"A"},8890:{f:"A"},8891:{f:"A"},8892:{f:"A"},8896:{f:"S1"},8897:{f:"S1"},8898:{f:"S1"},8899:{f:"S1"},8903:{f:"A"},8905:{f:"A"},8906:{f:"A"},8907:{f:"A"},8908:{f:"A"},8909:{f:"A"},8910:{f:"A"},8911:{f:"A"},8912:{f:"A"},8913:{f:"A"},8914:{f:"A"},8915:{f:"A"},8916:{f:"A"},8918:{f:"A"},8919:{f:"A"},8920:{f:"A"},8921:{f:"A"},8922:{f:"A"},8923:{f:"A"},8926:{f:"A"},8927:{f:"A"},8928:{f:"A"},8929:{f:"A"},8930:{c:"\\2291\\338"},8931:{c:"\\2292\\338"},8934:{f:"A"},8935:{f:"A"},8936:{f:"A"},8937:{f:"A"},8938:{f:"A"},8939:{f:"A"},8940:{f:"A"},8941:{f:"A"},8965:{c:"\\22BC",f:"A"},8966:{c:"\\2A5E",f:"A"},8988:{c:"\\250C",f:"A"},8989:{c:"\\2510",f:"A"},8990:{c:"\\2514",f:"A"},8991:{c:"\\2518",f:"A"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},9168:{f:"S1"},9416:{f:"A"},9484:{f:"A"},9488:{f:"A"},9492:{f:"A"},9496:{f:"A"},9585:{f:"A"},9586:{f:"A"},9632:{f:"A"},9633:{f:"A"},9642:{c:"\\25A0",f:"A"},9650:{f:"A"},9652:{c:"\\25B2",f:"A"},9653:{c:"\\25B3"},9654:{f:"A"},9656:{c:"\\25B6",f:"A"},9660:{f:"A"},9662:{c:"\\25BC",f:"A"},9663:{c:"\\25BD"},9664:{f:"A"},9666:{c:"\\25C0",f:"A"},9674:{f:"A"},9723:{c:"\\25A1",f:"A"},9724:{c:"\\25A0",f:"A"},9733:{f:"A"},10003:{f:"A"},10016:{f:"A"},10072:{c:"\\2223"},10731:{f:"A"},10744:{c:"/",f:"I"},10752:{f:"S1"},10753:{f:"S1"},10754:{f:"S1"},10756:{f:"S1"},10758:{f:"S1"},10764:{c:"\\222C\\222C",f:"S1"},10799:{c:"\\D7"},10846:{f:"A"},10877:{f:"A"},10878:{f:"A"},10885:{f:"A"},10886:{f:"A"},10887:{f:"A"},10888:{f:"A"},10889:{f:"A"},10890:{f:"A"},10891:{f:"A"},10892:{f:"A"},10901:{f:"A"},10902:{f:"A"},10933:{f:"A"},10934:{f:"A"},10935:{f:"A"},10936:{f:"A"},10937:{f:"A"},10938:{f:"A"},10949:{f:"A"},10950:{f:"A"},10955:{f:"A"},10956:{f:"A"},12296:{c:"\\27E8"},12297:{c:"\\27E9"},57350:{f:"A"},57351:{f:"A"},57352:{f:"A"},57353:{f:"A"},57356:{f:"A"},57357:{f:"A"},57358:{f:"A"},57359:{f:"A"},57360:{f:"A"},57361:{f:"A"},57366:{f:"A"},57367:{f:"A"},57368:{f:"A"},57369:{f:"A"},57370:{f:"A"},57371:{f:"A"},119808:{c:"A",f:"B"},119809:{c:"B",f:"B"},119810:{c:"C",f:"B"},119811:{c:"D",f:"B"},119812:{c:"E",f:"B"},119813:{c:"F",f:"B"},119814:{c:"G",f:"B"},119815:{c:"H",f:"B"},119816:{c:"I",f:"B"},119817:{c:"J",f:"B"},119818:{c:"K",f:"B"},119819:{c:"L",f:"B"},119820:{c:"M",f:"B"},119821:{c:"N",f:"B"},119822:{c:"O",f:"B"},119823:{c:"P",f:"B"},119824:{c:"Q",f:"B"},119825:{c:"R",f:"B"},119826:{c:"S",f:"B"},119827:{c:"T",f:"B"},119828:{c:"U",f:"B"},119829:{c:"V",f:"B"},119830:{c:"W",f:"B"},119831:{c:"X",f:"B"},119832:{c:"Y",f:"B"},119833:{c:"Z",f:"B"},119834:{c:"a",f:"B"},119835:{c:"b",f:"B"},119836:{c:"c",f:"B"},119837:{c:"d",f:"B"},119838:{c:"e",f:"B"},119839:{c:"f",f:"B"},119840:{c:"g",f:"B"},119841:{c:"h",f:"B"},119842:{c:"i",f:"B"},119843:{c:"j",f:"B"},119844:{c:"k",f:"B"},119845:{c:"l",f:"B"},119846:{c:"m",f:"B"},119847:{c:"n",f:"B"},119848:{c:"o",f:"B"},119849:{c:"p",f:"B"},119850:{c:"q",f:"B"},119851:{c:"r",f:"B"},119852:{c:"s",f:"B"},119853:{c:"t",f:"B"},119854:{c:"u",f:"B"},119855:{c:"v",f:"B"},119856:{c:"w",f:"B"},119857:{c:"x",f:"B"},119858:{c:"y",f:"B"},119859:{c:"z",f:"B"},119860:{c:"A",f:"I"},119861:{c:"B",f:"I"},119862:{c:"C",f:"I"},119863:{c:"D",f:"I"},119864:{c:"E",f:"I"},119865:{c:"F",f:"I"},119866:{c:"G",f:"I"},119867:{c:"H",f:"I"},119868:{c:"I",f:"I"},119869:{c:"J",f:"I"},119870:{c:"K",f:"I"},119871:{c:"L",f:"I"},119872:{c:"M",f:"I"},119873:{c:"N",f:"I"},119874:{c:"O",f:"I"},119875:{c:"P",f:"I"},119876:{c:"Q",f:"I"},119877:{c:"R",f:"I"},119878:{c:"S",f:"I"},119879:{c:"T",f:"I"},119880:{c:"U",f:"I"},119881:{c:"V",f:"I"},119882:{c:"W",f:"I"},119883:{c:"X",f:"I"},119884:{c:"Y",f:"I"},119885:{c:"Z",f:"I"},119886:{c:"a",f:"I"},119887:{c:"b",f:"I"},119888:{c:"c",f:"I"},119889:{c:"d",f:"I"},119890:{c:"e",f:"I"},119891:{c:"f",f:"I"},119892:{c:"g",f:"I"},119894:{c:"i",f:"I"},119895:{c:"j",f:"I"},119896:{c:"k",f:"I"},119897:{c:"l",f:"I"},119898:{c:"m",f:"I"},119899:{c:"n",f:"I"},119900:{c:"o",f:"I"},119901:{c:"p",f:"I"},119902:{c:"q",f:"I"},119903:{c:"r",f:"I"},119904:{c:"s",f:"I"},119905:{c:"t",f:"I"},119906:{c:"u",f:"I"},119907:{c:"v",f:"I"},119908:{c:"w",f:"I"},119909:{c:"x",f:"I"},119910:{c:"y",f:"I"},119911:{c:"z",f:"I"},119912:{c:"A",f:"BI"},119913:{c:"B",f:"BI"},119914:{c:"C",f:"BI"},119915:{c:"D",f:"BI"},119916:{c:"E",f:"BI"},119917:{c:"F",f:"BI"},119918:{c:"G",f:"BI"},119919:{c:"H",f:"BI"},119920:{c:"I",f:"BI"},119921:{c:"J",f:"BI"},119922:{c:"K",f:"BI"},119923:{c:"L",f:"BI"},119924:{c:"M",f:"BI"},119925:{c:"N",f:"BI"},119926:{c:"O",f:"BI"},119927:{c:"P",f:"BI"},119928:{c:"Q",f:"BI"},119929:{c:"R",f:"BI"},119930:{c:"S",f:"BI"},119931:{c:"T",f:"BI"},119932:{c:"U",f:"BI"},119933:{c:"V",f:"BI"},119934:{c:"W",f:"BI"},119935:{c:"X",f:"BI"},119936:{c:"Y",f:"BI"},119937:{c:"Z",f:"BI"},119938:{c:"a",f:"BI"},119939:{c:"b",f:"BI"},119940:{c:"c",f:"BI"},119941:{c:"d",f:"BI"},119942:{c:"e",f:"BI"},119943:{c:"f",f:"BI"},119944:{c:"g",f:"BI"},119945:{c:"h",f:"BI"},119946:{c:"i",f:"BI"},119947:{c:"j",f:"BI"},119948:{c:"k",f:"BI"},119949:{c:"l",f:"BI"},119950:{c:"m",f:"BI"},119951:{c:"n",f:"BI"},119952:{c:"o",f:"BI"},119953:{c:"p",f:"BI"},119954:{c:"q",f:"BI"},119955:{c:"r",f:"BI"},119956:{c:"s",f:"BI"},119957:{c:"t",f:"BI"},119958:{c:"u",f:"BI"},119959:{c:"v",f:"BI"},119960:{c:"w",f:"BI"},119961:{c:"x",f:"BI"},119962:{c:"y",f:"BI"},119963:{c:"z",f:"BI"},119964:{c:"A",f:"SC"},119966:{c:"C",f:"SC"},119967:{c:"D",f:"SC"},119970:{c:"G",f:"SC"},119973:{c:"J",f:"SC"},119974:{c:"K",f:"SC"},119977:{c:"N",f:"SC"},119978:{c:"O",f:"SC"},119979:{c:"P",f:"SC"},119980:{c:"Q",f:"SC"},119982:{c:"S",f:"SC"},119983:{c:"T",f:"SC"},119984:{c:"U",f:"SC"},119985:{c:"V",f:"SC"},119986:{c:"W",f:"SC"},119987:{c:"X",f:"SC"},119988:{c:"Y",f:"SC"},119989:{c:"Z",f:"SC"},120068:{c:"A",f:"FR"},120069:{c:"B",f:"FR"},120071:{c:"D",f:"FR"},120072:{c:"E",f:"FR"},120073:{c:"F",f:"FR"},120074:{c:"G",f:"FR"},120077:{c:"J",f:"FR"},120078:{c:"K",f:"FR"},120079:{c:"L",f:"FR"},120080:{c:"M",f:"FR"},120081:{c:"N",f:"FR"},120082:{c:"O",f:"FR"},120083:{c:"P",f:"FR"},120084:{c:"Q",f:"FR"},120086:{c:"S",f:"FR"},120087:{c:"T",f:"FR"},120088:{c:"U",f:"FR"},120089:{c:"V",f:"FR"},120090:{c:"W",f:"FR"},120091:{c:"X",f:"FR"},120092:{c:"Y",f:"FR"},120094:{c:"a",f:"FR"},120095:{c:"b",f:"FR"},120096:{c:"c",f:"FR"},120097:{c:"d",f:"FR"},120098:{c:"e",f:"FR"},120099:{c:"f",f:"FR"},120100:{c:"g",f:"FR"},120101:{c:"h",f:"FR"},120102:{c:"i",f:"FR"},120103:{c:"j",f:"FR"},120104:{c:"k",f:"FR"},120105:{c:"l",f:"FR"},120106:{c:"m",f:"FR"},120107:{c:"n",f:"FR"},120108:{c:"o",f:"FR"},120109:{c:"p",f:"FR"},120110:{c:"q",f:"FR"},120111:{c:"r",f:"FR"},120112:{c:"s",f:"FR"},120113:{c:"t",f:"FR"},120114:{c:"u",f:"FR"},120115:{c:"v",f:"FR"},120116:{c:"w",f:"FR"},120117:{c:"x",f:"FR"},120118:{c:"y",f:"FR"},120119:{c:"z",f:"FR"},120120:{c:"A",f:"A"},120121:{c:"B",f:"A"},120123:{c:"D",f:"A"},120124:{c:"E",f:"A"},120125:{c:"F",f:"A"},120126:{c:"G",f:"A"},120128:{c:"I",f:"A"},120129:{c:"J",f:"A"},120130:{c:"K",f:"A"},120131:{c:"L",f:"A"},120132:{c:"M",f:"A"},120134:{c:"O",f:"A"},120138:{c:"S",f:"A"},120139:{c:"T",f:"A"},120140:{c:"U",f:"A"},120141:{c:"V",f:"A"},120142:{c:"W",f:"A"},120143:{c:"X",f:"A"},120144:{c:"Y",f:"A"},120172:{c:"A",f:"FRB"},120173:{c:"B",f:"FRB"},120174:{c:"C",f:"FRB"},120175:{c:"D",f:"FRB"},120176:{c:"E",f:"FRB"},120177:{c:"F",f:"FRB"},120178:{c:"G",f:"FRB"},120179:{c:"H",f:"FRB"},120180:{c:"I",f:"FRB"},120181:{c:"J",f:"FRB"},120182:{c:"K",f:"FRB"},120183:{c:"L",f:"FRB"},120184:{c:"M",f:"FRB"},120185:{c:"N",f:"FRB"},120186:{c:"O",f:"FRB"},120187:{c:"P",f:"FRB"},120188:{c:"Q",f:"FRB"},120189:{c:"R",f:"FRB"},120190:{c:"S",f:"FRB"},120191:{c:"T",f:"FRB"},120192:{c:"U",f:"FRB"},120193:{c:"V",f:"FRB"},120194:{c:"W",f:"FRB"},120195:{c:"X",f:"FRB"},120196:{c:"Y",f:"FRB"},120197:{c:"Z",f:"FRB"},120198:{c:"a",f:"FRB"},120199:{c:"b",f:"FRB"},120200:{c:"c",f:"FRB"},120201:{c:"d",f:"FRB"},120202:{c:"e",f:"FRB"},120203:{c:"f",f:"FRB"},120204:{c:"g",f:"FRB"},120205:{c:"h",f:"FRB"},120206:{c:"i",f:"FRB"},120207:{c:"j",f:"FRB"},120208:{c:"k",f:"FRB"},120209:{c:"l",f:"FRB"},120210:{c:"m",f:"FRB"},120211:{c:"n",f:"FRB"},120212:{c:"o",f:"FRB"},120213:{c:"p",f:"FRB"},120214:{c:"q",f:"FRB"},120215:{c:"r",f:"FRB"},120216:{c:"s",f:"FRB"},120217:{c:"t",f:"FRB"},120218:{c:"u",f:"FRB"},120219:{c:"v",f:"FRB"},120220:{c:"w",f:"FRB"},120221:{c:"x",f:"FRB"},120222:{c:"y",f:"FRB"},120223:{c:"z",f:"FRB"},120224:{c:"A",f:"SS"},120225:{c:"B",f:"SS"},120226:{c:"C",f:"SS"},120227:{c:"D",f:"SS"},120228:{c:"E",f:"SS"},120229:{c:"F",f:"SS"},120230:{c:"G",f:"SS"},120231:{c:"H",f:"SS"},120232:{c:"I",f:"SS"},120233:{c:"J",f:"SS"},120234:{c:"K",f:"SS"},120235:{c:"L",f:"SS"},120236:{c:"M",f:"SS"},120237:{c:"N",f:"SS"},120238:{c:"O",f:"SS"},120239:{c:"P",f:"SS"},120240:{c:"Q",f:"SS"},120241:{c:"R",f:"SS"},120242:{c:"S",f:"SS"},120243:{c:"T",f:"SS"},120244:{c:"U",f:"SS"},120245:{c:"V",f:"SS"},120246:{c:"W",f:"SS"},120247:{c:"X",f:"SS"},120248:{c:"Y",f:"SS"},120249:{c:"Z",f:"SS"},120250:{c:"a",f:"SS"},120251:{c:"b",f:"SS"},120252:{c:"c",f:"SS"},120253:{c:"d",f:"SS"},120254:{c:"e",f:"SS"},120255:{c:"f",f:"SS"},120256:{c:"g",f:"SS"},120257:{c:"h",f:"SS"},120258:{c:"i",f:"SS"},120259:{c:"j",f:"SS"},120260:{c:"k",f:"SS"},120261:{c:"l",f:"SS"},120262:{c:"m",f:"SS"},120263:{c:"n",f:"SS"},120264:{c:"o",f:"SS"},120265:{c:"p",f:"SS"},120266:{c:"q",f:"SS"},120267:{c:"r",f:"SS"},120268:{c:"s",f:"SS"},120269:{c:"t",f:"SS"},120270:{c:"u",f:"SS"},120271:{c:"v",f:"SS"},120272:{c:"w",f:"SS"},120273:{c:"x",f:"SS"},120274:{c:"y",f:"SS"},120275:{c:"z",f:"SS"},120276:{c:"A",f:"SSB"},120277:{c:"B",f:"SSB"},120278:{c:"C",f:"SSB"},120279:{c:"D",f:"SSB"},120280:{c:"E",f:"SSB"},120281:{c:"F",f:"SSB"},120282:{c:"G",f:"SSB"},120283:{c:"H",f:"SSB"},120284:{c:"I",f:"SSB"},120285:{c:"J",f:"SSB"},120286:{c:"K",f:"SSB"},120287:{c:"L",f:"SSB"},120288:{c:"M",f:"SSB"},120289:{c:"N",f:"SSB"},120290:{c:"O",f:"SSB"},120291:{c:"P",f:"SSB"},120292:{c:"Q",f:"SSB"},120293:{c:"R",f:"SSB"},120294:{c:"S",f:"SSB"},120295:{c:"T",f:"SSB"},120296:{c:"U",f:"SSB"},120297:{c:"V",f:"SSB"},120298:{c:"W",f:"SSB"},120299:{c:"X",f:"SSB"},120300:{c:"Y",f:"SSB"},120301:{c:"Z",f:"SSB"},120302:{c:"a",f:"SSB"},120303:{c:"b",f:"SSB"},120304:{c:"c",f:"SSB"},120305:{c:"d",f:"SSB"},120306:{c:"e",f:"SSB"},120307:{c:"f",f:"SSB"},120308:{c:"g",f:"SSB"},120309:{c:"h",f:"SSB"},120310:{c:"i",f:"SSB"},120311:{c:"j",f:"SSB"},120312:{c:"k",f:"SSB"},120313:{c:"l",f:"SSB"},120314:{c:"m",f:"SSB"},120315:{c:"n",f:"SSB"},120316:{c:"o",f:"SSB"},120317:{c:"p",f:"SSB"},120318:{c:"q",f:"SSB"},120319:{c:"r",f:"SSB"},120320:{c:"s",f:"SSB"},120321:{c:"t",f:"SSB"},120322:{c:"u",f:"SSB"},120323:{c:"v",f:"SSB"},120324:{c:"w",f:"SSB"},120325:{c:"x",f:"SSB"},120326:{c:"y",f:"SSB"},120327:{c:"z",f:"SSB"},120328:{c:"A",f:"SSI"},120329:{c:"B",f:"SSI"},120330:{c:"C",f:"SSI"},120331:{c:"D",f:"SSI"},120332:{c:"E",f:"SSI"},120333:{c:"F",f:"SSI"},120334:{c:"G",f:"SSI"},120335:{c:"H",f:"SSI"},120336:{c:"I",f:"SSI"},120337:{c:"J",f:"SSI"},120338:{c:"K",f:"SSI"},120339:{c:"L",f:"SSI"},120340:{c:"M",f:"SSI"},120341:{c:"N",f:"SSI"},120342:{c:"O",f:"SSI"},120343:{c:"P",f:"SSI"},120344:{c:"Q",f:"SSI"},120345:{c:"R",f:"SSI"},120346:{c:"S",f:"SSI"},120347:{c:"T",f:"SSI"},120348:{c:"U",f:"SSI"},120349:{c:"V",f:"SSI"},120350:{c:"W",f:"SSI"},120351:{c:"X",f:"SSI"},120352:{c:"Y",f:"SSI"},120353:{c:"Z",f:"SSI"},120354:{c:"a",f:"SSI"},120355:{c:"b",f:"SSI"},120356:{c:"c",f:"SSI"},120357:{c:"d",f:"SSI"},120358:{c:"e",f:"SSI"},120359:{c:"f",f:"SSI"},120360:{c:"g",f:"SSI"},120361:{c:"h",f:"SSI"},120362:{c:"i",f:"SSI"},120363:{c:"j",f:"SSI"},120364:{c:"k",f:"SSI"},120365:{c:"l",f:"SSI"},120366:{c:"m",f:"SSI"},120367:{c:"n",f:"SSI"},120368:{c:"o",f:"SSI"},120369:{c:"p",f:"SSI"},120370:{c:"q",f:"SSI"},120371:{c:"r",f:"SSI"},120372:{c:"s",f:"SSI"},120373:{c:"t",f:"SSI"},120374:{c:"u",f:"SSI"},120375:{c:"v",f:"SSI"},120376:{c:"w",f:"SSI"},120377:{c:"x",f:"SSI"},120378:{c:"y",f:"SSI"},120379:{c:"z",f:"SSI"},120432:{c:"A",f:"T"},120433:{c:"B",f:"T"},120434:{c:"C",f:"T"},120435:{c:"D",f:"T"},120436:{c:"E",f:"T"},120437:{c:"F",f:"T"},120438:{c:"G",f:"T"},120439:{c:"H",f:"T"},120440:{c:"I",f:"T"},120441:{c:"J",f:"T"},120442:{c:"K",f:"T"},120443:{c:"L",f:"T"},120444:{c:"M",f:"T"},120445:{c:"N",f:"T"},120446:{c:"O",f:"T"},120447:{c:"P",f:"T"},120448:{c:"Q",f:"T"},120449:{c:"R",f:"T"},120450:{c:"S",f:"T"},120451:{c:"T",f:"T"},120452:{c:"U",f:"T"},120453:{c:"V",f:"T"},120454:{c:"W",f:"T"},120455:{c:"X",f:"T"},120456:{c:"Y",f:"T"},120457:{c:"Z",f:"T"},120458:{c:"a",f:"T"},120459:{c:"b",f:"T"},120460:{c:"c",f:"T"},120461:{c:"d",f:"T"},120462:{c:"e",f:"T"},120463:{c:"f",f:"T"},120464:{c:"g",f:"T"},120465:{c:"h",f:"T"},120466:{c:"i",f:"T"},120467:{c:"j",f:"T"},120468:{c:"k",f:"T"},120469:{c:"l",f:"T"},120470:{c:"m",f:"T"},120471:{c:"n",f:"T"},120472:{c:"o",f:"T"},120473:{c:"p",f:"T"},120474:{c:"q",f:"T"},120475:{c:"r",f:"T"},120476:{c:"s",f:"T"},120477:{c:"t",f:"T"},120478:{c:"u",f:"T"},120479:{c:"v",f:"T"},120480:{c:"w",f:"T"},120481:{c:"x",f:"T"},120482:{c:"y",f:"T"},120483:{c:"z",f:"T"},120488:{c:"A",f:"B"},120489:{c:"B",f:"B"},120490:{c:"\\393",f:"B"},120491:{c:"\\394",f:"B"},120492:{c:"E",f:"B"},120493:{c:"Z",f:"B"},120494:{c:"H",f:"B"},120495:{c:"\\398",f:"B"},120496:{c:"I",f:"B"},120497:{c:"K",f:"B"},120498:{c:"\\39B",f:"B"},120499:{c:"M",f:"B"},120500:{c:"N",f:"B"},120501:{c:"\\39E",f:"B"},120502:{c:"O",f:"B"},120503:{c:"\\3A0",f:"B"},120504:{c:"P",f:"B"},120506:{c:"\\3A3",f:"B"},120507:{c:"T",f:"B"},120508:{c:"\\3A5",f:"B"},120509:{c:"\\3A6",f:"B"},120510:{c:"X",f:"B"},120511:{c:"\\3A8",f:"B"},120512:{c:"\\3A9",f:"B"},120513:{c:"\\2207",f:"B"},120546:{c:"A",f:"I"},120547:{c:"B",f:"I"},120548:{c:"\\393",f:"I"},120549:{c:"\\394",f:"I"},120550:{c:"E",f:"I"},120551:{c:"Z",f:"I"},120552:{c:"H",f:"I"},120553:{c:"\\398",f:"I"},120554:{c:"I",f:"I"},120555:{c:"K",f:"I"},120556:{c:"\\39B",f:"I"},120557:{c:"M",f:"I"},120558:{c:"N",f:"I"},120559:{c:"\\39E",f:"I"},120560:{c:"O",f:"I"},120561:{c:"\\3A0",f:"I"},120562:{c:"P",f:"I"},120564:{c:"\\3A3",f:"I"},120565:{c:"T",f:"I"},120566:{c:"\\3A5",f:"I"},120567:{c:"\\3A6",f:"I"},120568:{c:"X",f:"I"},120569:{c:"\\3A8",f:"I"},120570:{c:"\\3A9",f:"I"},120572:{c:"\\3B1",f:"I"},120573:{c:"\\3B2",f:"I"},120574:{c:"\\3B3",f:"I"},120575:{c:"\\3B4",f:"I"},120576:{c:"\\3B5",f:"I"},120577:{c:"\\3B6",f:"I"},120578:{c:"\\3B7",f:"I"},120579:{c:"\\3B8",f:"I"},120580:{c:"\\3B9",f:"I"},120581:{c:"\\3BA",f:"I"},120582:{c:"\\3BB",f:"I"},120583:{c:"\\3BC",f:"I"},120584:{c:"\\3BD",f:"I"},120585:{c:"\\3BE",f:"I"},120586:{c:"\\3BF",f:"I"},120587:{c:"\\3C0",f:"I"},120588:{c:"\\3C1",f:"I"},120589:{c:"\\3C2",f:"I"},120590:{c:"\\3C3",f:"I"},120591:{c:"\\3C4",f:"I"},120592:{c:"\\3C5",f:"I"},120593:{c:"\\3C6",f:"I"},120594:{c:"\\3C7",f:"I"},120595:{c:"\\3C8",f:"I"},120596:{c:"\\3C9",f:"I"},120597:{c:"\\2202"},120598:{c:"\\3F5",f:"I"},120599:{c:"\\3D1",f:"I"},120600:{c:"\\E009",f:"A"},120601:{c:"\\3D5",f:"I"},120602:{c:"\\3F1",f:"I"},120603:{c:"\\3D6",f:"I"},120604:{c:"A",f:"BI"},120605:{c:"B",f:"BI"},120606:{c:"\\393",f:"BI"},120607:{c:"\\394",f:"BI"},120608:{c:"E",f:"BI"},120609:{c:"Z",f:"BI"},120610:{c:"H",f:"BI"},120611:{c:"\\398",f:"BI"},120612:{c:"I",f:"BI"},120613:{c:"K",f:"BI"},120614:{c:"\\39B",f:"BI"},120615:{c:"M",f:"BI"},120616:{c:"N",f:"BI"},120617:{c:"\\39E",f:"BI"},120618:{c:"O",f:"BI"},120619:{c:"\\3A0",f:"BI"},120620:{c:"P",f:"BI"},120622:{c:"\\3A3",f:"BI"},120623:{c:"T",f:"BI"},120624:{c:"\\3A5",f:"BI"},120625:{c:"\\3A6",f:"BI"},120626:{c:"X",f:"BI"},120627:{c:"\\3A8",f:"BI"},120628:{c:"\\3A9",f:"BI"},120630:{c:"\\3B1",f:"BI"},120631:{c:"\\3B2",f:"BI"},120632:{c:"\\3B3",f:"BI"},120633:{c:"\\3B4",f:"BI"},120634:{c:"\\3B5",f:"BI"},120635:{c:"\\3B6",f:"BI"},120636:{c:"\\3B7",f:"BI"},120637:{c:"\\3B8",f:"BI"},120638:{c:"\\3B9",f:"BI"},120639:{c:"\\3BA",f:"BI"},120640:{c:"\\3BB",f:"BI"},120641:{c:"\\3BC",f:"BI"},120642:{c:"\\3BD",f:"BI"},120643:{c:"\\3BE",f:"BI"},120644:{c:"\\3BF",f:"BI"},120645:{c:"\\3C0",f:"BI"},120646:{c:"\\3C1",f:"BI"},120647:{c:"\\3C2",f:"BI"},120648:{c:"\\3C3",f:"BI"},120649:{c:"\\3C4",f:"BI"},120650:{c:"\\3C5",f:"BI"},120651:{c:"\\3C6",f:"BI"},120652:{c:"\\3C7",f:"BI"},120653:{c:"\\3C8",f:"BI"},120654:{c:"\\3C9",f:"BI"},120655:{c:"\\2202",f:"B"},120656:{c:"\\3F5",f:"BI"},120657:{c:"\\3D1",f:"BI"},120658:{c:"\\E009",f:"A"},120659:{c:"\\3D5",f:"BI"},120660:{c:"\\3F1",f:"BI"},120661:{c:"\\3D6",f:"BI"},120662:{c:"A",f:"SSB"},120663:{c:"B",f:"SSB"},120664:{c:"\\393",f:"SSB"},120665:{c:"\\394",f:"SSB"},120666:{c:"E",f:"SSB"},120667:{c:"Z",f:"SSB"},120668:{c:"H",f:"SSB"},120669:{c:"\\398",f:"SSB"},120670:{c:"I",f:"SSB"},120671:{c:"K",f:"SSB"},120672:{c:"\\39B",f:"SSB"},120673:{c:"M",f:"SSB"},120674:{c:"N",f:"SSB"},120675:{c:"\\39E",f:"SSB"},120676:{c:"O",f:"SSB"},120677:{c:"\\3A0",f:"SSB"},120678:{c:"P",f:"SSB"},120680:{c:"\\3A3",f:"SSB"},120681:{c:"T",f:"SSB"},120682:{c:"\\3A5",f:"SSB"},120683:{c:"\\3A6",f:"SSB"},120684:{c:"X",f:"SSB"},120685:{c:"\\3A8",f:"SSB"},120686:{c:"\\3A9",f:"SSB"},120782:{c:"0",f:"B"},120783:{c:"1",f:"B"},120784:{c:"2",f:"B"},120785:{c:"3",f:"B"},120786:{c:"4",f:"B"},120787:{c:"5",f:"B"},120788:{c:"6",f:"B"},120789:{c:"7",f:"B"},120790:{c:"8",f:"B"},120791:{c:"9",f:"B"},120802:{c:"0",f:"SS"},120803:{c:"1",f:"SS"},120804:{c:"2",f:"SS"},120805:{c:"3",f:"SS"},120806:{c:"4",f:"SS"},120807:{c:"5",f:"SS"},120808:{c:"6",f:"SS"},120809:{c:"7",f:"SS"},120810:{c:"8",f:"SS"},120811:{c:"9",f:"SS"},120812:{c:"0",f:"SSB"},120813:{c:"1",f:"SSB"},120814:{c:"2",f:"SSB"},120815:{c:"3",f:"SSB"},120816:{c:"4",f:"SSB"},120817:{c:"5",f:"SSB"},120818:{c:"6",f:"SSB"},120819:{c:"7",f:"SSB"},120820:{c:"8",f:"SSB"},120821:{c:"9",f:"SSB"},120822:{c:"0",f:"T"},120823:{c:"1",f:"T"},120824:{c:"2",f:"T"},120825:{c:"3",f:"T"},120826:{c:"4",f:"T"},120827:{c:"5",f:"T"},120828:{c:"6",f:"T"},120829:{c:"7",f:"T"},120830:{c:"8",f:"T"},120831:{c:"9",f:"T"}})},3518:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBoldItalic=void 0;var t=i(73),e=i(6949);f.sansSerifBoldItalic=(0,t.AddCSS)(e.sansSerifBoldItalic,{305:{f:"SSB"},567:{f:"SSB"}})},965:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBold=void 0;var t=i(73),e=i(5193);f.sansSerifBold=(0,t.AddCSS)(e.sansSerifBold,{8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},9169:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifItalic=void 0;var t=i(73),e=i(2632);f.sansSerifItalic=(0,t.AddCSS)(e.sansSerifItalic,{913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},932:{c:"T"},935:{c:"X"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},6736:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerif=void 0;var t=i(73),e=i(4214);f.sansSerif=(0,t.AddCSS)(e.sansSerif,{913:{c:"A"},914:{c:"B"},917:{c:"E"},918:{c:"Z"},919:{c:"H"},921:{c:"I"},922:{c:"K"},924:{c:"M"},925:{c:"N"},927:{c:"O"},929:{c:"P"},932:{c:"T"},935:{c:"X"},8213:{c:"\\2014"},8215:{c:"_"},8260:{c:"/"},8710:{c:"\\394"}})},2290:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.scriptBold=void 0;var t=i(6466);Object.defineProperty(f,"scriptBold",{enumerable:!0,get:function(){return t.scriptBold}})},3012:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.script=void 0;var t=i(3776);Object.defineProperty(f,"script",{enumerable:!0,get:function(){return t.script}})},8787:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.smallop=void 0;var t=i(73),e=i(7405);f.smallop=(0,t.AddCSS)(e.smallop,{8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},10072:{c:"\\2223"},10764:{c:"\\222C\\222C"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},5392:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphicBold=void 0;var t=i(73),e=i(8105);f.texCalligraphicBold=(0,t.AddCSS)(e.texCalligraphicBold,{305:{f:"B"},567:{f:"B"}})},6318:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphic=void 0;var t=i(2518);Object.defineProperty(f,"texCalligraphic",{enumerable:!0,get:function(){return t.texCalligraphic}})},5351:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texMathit=void 0;var t=i(5595);Object.defineProperty(f,"texMathit",{enumerable:!0,get:function(){return t.texMathit}})},873:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyleBold=void 0;var t=i(6357);Object.defineProperty(f,"texOldstyleBold",{enumerable:!0,get:function(){return t.texOldstyleBold}})},7611:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyle=void 0;var t=i(9474);Object.defineProperty(f,"texOldstyle",{enumerable:!0,get:function(){return t.texOldstyle}})},6590:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texSize3=void 0;var t=i(73),e=i(584);f.texSize3=(0,t.AddCSS)(e.texSize3,{8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},12296:{c:"\\27E8"},12297:{c:"\\27E9"}})},8798:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texSize4=void 0;var t=i(73),e=i(4324);f.texSize4=(0,t.AddCSS)(e.texSize4,{8260:{c:"/"},9001:{c:"\\27E8"},9002:{c:"\\27E9"},12296:{c:"\\27E8"},12297:{c:"\\27E9"},57685:{c:"\\E153\\E152"},57686:{c:"\\E151\\E150"}})},2138:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.texVariant=void 0;var t=i(73),e=i(8135);f.texVariant=(0,t.AddCSS)(e.texVariant,{1008:{c:"\\E009"},8463:{f:""},8740:{c:"\\E006"},8742:{c:"\\E007"},8808:{c:"\\E00C"},8809:{c:"\\E00D"},8816:{c:"\\E011"},8817:{c:"\\E00E"},8840:{c:"\\E016"},8841:{c:"\\E018"},8842:{c:"\\E01A"},8843:{c:"\\E01B"},10887:{c:"\\E010"},10888:{c:"\\E00F"},10955:{c:"\\E017"},10956:{c:"\\E019"}})},2176:function(c,f){var i,t=this&&this.__extends||(i=function(c,f){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var i in f)Object.prototype.hasOwnProperty.call(f,i)&&(c[i]=f[i])},i(c,f)},function(c,f){if("function"!=typeof f&&null!==f)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");function t(){this.constructor=c}i(c,f),c.prototype=null===f?Object.create(f):(t.prototype=f.prototype,new t)}),e=this&&this.__assign||function(){return e=Object.assign||function(c){for(var f,i=1,t=arguments.length;i<t;i++)for(var e in f=arguments[i])Object.prototype.hasOwnProperty.call(f,e)&&(c[e]=f[e]);return c},e.apply(this,arguments)},s=this&&this.__read||function(c,f){var i="function"==typeof Symbol&&c[Symbol.iterator];if(!i)return c;var t,e,s=i.call(c),r=[];try{for(;(void 0===f||f-- >0)&&!(t=s.next()).done;)r.push(t.value)}catch(c){e={error:c}}finally{try{t&&!t.done&&(i=s.return)&&i.call(s)}finally{if(e)throw e.error}}return r},r=this&&this.__spreadArray||function(c,f,i){if(i||2===arguments.length)for(var t,e=0,s=f.length;e<s;e++)!t&&e in f||(t||(t=Array.prototype.slice.call(f,0,e)),t[e]=f[e]);return c.concat(t||Array.prototype.slice.call(f))};Object.defineProperty(f,"__esModule",{value:!0}),f.CommonTeXFontMixin=void 0,f.CommonTeXFontMixin=function(c){var f;return f=function(c){function f(){return null!==c&&c.apply(this,arguments)||this}return t(f,c),f.prototype.getDelimiterData=function(c){return this.getChar("-smallop",c)||this.getChar("-size4",c)},f}(c),f.NAME="TeX",f.defaultVariants=r(r([],s(c.defaultVariants),!1),[["-smallop","normal"],["-largeop","normal"],["-size3","normal"],["-size4","normal"],["-tex-calligraphic","italic"],["-tex-bold-calligraphic","bold-italic"],["-tex-oldstyle","normal"],["-tex-bold-oldstyle","bold"],["-tex-mathit","italic"],["-tex-variant","normal"]],!1),f.defaultCssFonts=e(e({},c.defaultCssFonts),{"-smallop":["serif",!1,!1],"-largeop":["serif",!1,!1],"-size3":["serif",!1,!1],"-size4":["serif",!1,!1],"-tex-calligraphic":["cursive",!0,!1],"-tex-bold-calligraphic":["cursive",!0,!0],"-tex-oldstyle":["serif",!1,!1],"-tex-bold-oldstyle":["serif",!1,!0],"-tex-mathit":["serif",!0,!1]}),f.defaultSizeVariants=["normal","-smallop","-largeop","-size3","-size4","-tex-variant"],f.defaultStretchVariants=["-size4"],f}},3869:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.boldItalic=void 0,f.boldItalic={47:[.711,.21,.894],305:[.452,.008,.394,{sk:.0319}],567:[.451,.201,.439,{sk:.0958}],8260:[.711,.21,.894],8710:[.711,0,.958,{sk:.192}],10744:[.711,.21,.894]}},7557:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.bold=void 0,f.bold={33:[.705,0,.35],34:[.694,-.329,.603],35:[.694,.193,.958],36:[.75,.056,.575],37:[.75,.056,.958],38:[.705,.011,.894],39:[.694,-.329,.319],40:[.75,.249,.447],41:[.75,.249,.447],42:[.75,-.306,.575],43:[.633,.131,.894],44:[.171,.194,.319],45:[.278,-.166,.383],46:[.171,0,.319],47:[.75,.25,.575],58:[.444,0,.319],59:[.444,.194,.319],60:[.587,.085,.894],61:[.393,-.109,.894],62:[.587,.085,.894],63:[.7,0,.543],64:[.699,.006,.894],91:[.75,.25,.319],92:[.75,.25,.575],93:[.75,.25,.319],94:[.694,-.52,.575],95:[-.01,.061,.575],96:[.706,-.503,.575],123:[.75,.25,.575],124:[.75,.249,.319],125:[.75,.25,.575],126:[.344,-.202,.575],168:[.695,-.535,.575],172:[.371,-.061,.767],175:[.607,-.54,.575],176:[.702,-.536,.575],177:[.728,.035,.894],180:[.706,-.503,.575],183:[.336,-.166,.319],215:[.53,.028,.894],247:[.597,.096,.894],305:[.442,0,.278,{sk:.0278}],567:[.442,.205,.306,{sk:.0833}],697:[.563,-.033,.344],710:[.694,-.52,.575],711:[.66,-.515,.575],713:[.607,-.54,.575],714:[.706,-.503,.575],715:[.706,-.503,.575],728:[.694,-.5,.575],729:[.695,-.525,.575],730:[.702,-.536,.575],732:[.694,-.552,.575],768:[.706,-.503,0],769:[.706,-.503,0],770:[.694,-.52,0],771:[.694,-.552,0],772:[.607,-.54,0],774:[.694,-.5,0],775:[.695,-.525,0],776:[.695,-.535,0],778:[.702,-.536,0],779:[.714,-.511,0],780:[.66,-.515,0],824:[.711,.21,0],8194:[0,0,.5],8195:[0,0,.999],8196:[0,0,.333],8197:[0,0,.25],8198:[0,0,.167],8201:[0,0,.167],8202:[0,0,.083],8211:[.3,-.249,.575],8212:[.3,-.249,1.15],8213:[.3,-.249,1.15],8214:[.75,.248,.575],8215:[-.01,.061,.575],8216:[.694,-.329,.319],8217:[.694,-.329,.319],8220:[.694,-.329,.603],8221:[.694,-.329,.603],8224:[.702,.211,.511],8225:[.702,.202,.511],8226:[.474,-.028,.575],8230:[.171,0,1.295],8242:[.563,-.033,.344],8243:[.563,0,.688],8244:[.563,0,1.032],8254:[.607,-.54,.575],8260:[.75,.25,.575],8279:[.563,0,1.376],8407:[.723,-.513,.575],8463:[.694,.008,.668,{sk:-.0319}],8467:[.702,.019,.474,{sk:.128}],8472:[.461,.21,.74],8501:[.694,0,.703],8592:[.518,.017,1.15],8593:[.694,.193,.575],8594:[.518,.017,1.15],8595:[.694,.194,.575],8596:[.518,.017,1.15],8597:[.767,.267,.575],8598:[.724,.194,1.15],8599:[.724,.193,1.15],8600:[.694,.224,1.15],8601:[.694,.224,1.15],8602:[.711,.21,1.15],8603:[.711,.21,1.15],8614:[.518,.017,1.15],8617:[.518,.017,1.282],8618:[.518,.017,1.282],8622:[.711,.21,1.15],8636:[.518,-.22,1.15],8637:[.281,.017,1.15],8640:[.518,-.22,1.15],8641:[.281,.017,1.15],8652:[.718,.017,1.15],8653:[.711,.21,1.15],8654:[.711,.21,1.15],8655:[.711,.21,1.15],8656:[.547,.046,1.15],8657:[.694,.193,.703],8658:[.547,.046,1.15],8659:[.694,.194,.703],8660:[.547,.046,1.15],8661:[.767,.267,.703],8704:[.694,.016,.639],8707:[.694,0,.639],8708:[.711,.21,.639],8709:[.767,.073,.575],8710:[.698,0,.958],8712:[.587,.086,.767],8713:[.711,.21,.767],8715:[.587,.086,.767],8716:[.711,.21,.767],8722:[.281,-.221,.894],8723:[.537,.227,.894],8725:[.75,.25,.575],8726:[.75,.25,.575],8727:[.472,-.028,.575],8728:[.474,-.028,.575],8729:[.474,-.028,.575],8730:[.82,.18,.958,{ic:.03}],8733:[.451,.008,.894],8734:[.452,.008,1.15],8736:[.714,0,.722],8739:[.75,.249,.319],8740:[.75,.249,.319],8741:[.75,.248,.575],8742:[.75,.248,.575],8743:[.604,.017,.767],8744:[.604,.016,.767],8745:[.603,.016,.767],8746:[.604,.016,.767],8747:[.711,.211,.569,{ic:.063}],8764:[.391,-.109,.894],8768:[.583,.082,.319],8769:[.711,.21,.894],8771:[.502,0,.894],8772:[.711,.21,.894],8773:[.638,.027,.894],8775:[.711,.21,.894],8776:[.524,-.032,.894],8777:[.711,.21,.894],8781:[.533,.032,.894],8784:[.721,-.109,.894],8800:[.711,.21,.894],8801:[.505,0,.894],8802:[.711,.21,.894],8804:[.697,.199,.894],8805:[.697,.199,.894],8810:[.617,.116,1.15],8811:[.618,.116,1.15],8813:[.711,.21,.894],8814:[.711,.21,.894],8815:[.711,.21,.894],8816:[.711,.21,.894],8817:[.711,.21,.894],8826:[.585,.086,.894],8827:[.586,.086,.894],8832:[.711,.21,.894],8833:[.711,.21,.894],8834:[.587,.085,.894],8835:[.587,.086,.894],8836:[.711,.21,.894],8837:[.711,.21,.894],8838:[.697,.199,.894],8839:[.697,.199,.894],8840:[.711,.21,.894],8841:[.711,.21,.894],8846:[.604,.016,.767],8849:[.697,.199,.894],8850:[.697,.199,.894],8851:[.604,0,.767],8852:[.604,0,.767],8853:[.632,.132,.894],8854:[.632,.132,.894],8855:[.632,.132,.894],8856:[.632,.132,.894],8857:[.632,.132,.894],8866:[.693,0,.703],8867:[.693,0,.703],8868:[.694,0,.894],8869:[.693,0,.894],8872:[.75,.249,.974],8876:[.711,.21,.703],8877:[.75,.249,.974],8900:[.523,.021,.575],8901:[.336,-.166,.319],8902:[.502,0,.575],8904:[.54,.039,1],8930:[.711,.21,.894],8931:[.711,.21,.894],8942:[.951,.029,.319],8943:[.336,-.166,1.295],8945:[.871,-.101,1.323],8968:[.75,.248,.511],8969:[.75,.248,.511],8970:[.749,.248,.511],8971:[.749,.248,.511],8994:[.405,-.108,1.15],8995:[.392,-.126,1.15],9001:[.75,.249,.447],9002:[.75,.249,.447],9651:[.711,0,1.022],9653:[.711,0,1.022],9657:[.54,.039,.575],9661:[.5,.21,1.022],9663:[.5,.21,1.022],9667:[.539,.038,.575],9711:[.711,.211,1.15],9824:[.719,.129,.894],9825:[.711,.024,.894],9826:[.719,.154,.894],9827:[.719,.129,.894],9837:[.75,.017,.447],9838:[.741,.223,.447],9839:[.724,.224,.447],10072:[.75,.249,.319],10216:[.75,.249,.447],10217:[.75,.249,.447],10229:[.518,.017,1.805],10230:[.518,.017,1.833],10231:[.518,.017,2.126],10232:[.547,.046,1.868],10233:[.547,.046,1.87],10234:[.547,.046,2.126],10236:[.518,.017,1.833],10744:[.711,.21,.894],10799:[.53,.028,.894],10815:[.686,0,.9],10927:[.696,.199,.894],10928:[.697,.199,.894],12296:[.75,.249,.447],12297:[.75,.249,.447]}},7481:function(c,f,i){Object.defineProperty(f,"__esModule",{value:!0}),f.delimiters=f.VSIZES=f.HDW3=f.HDW2=f.HDW1=void 0;var t=i(4228);f.HDW1=[.75,.25,.875],f.HDW2=[.85,.349,.667],f.HDW3=[.583,.082,.5],f.VSIZES=[1,1.2,1.8,2.4,3];var e={c:47,dir:t.V,sizes:f.VSIZES},s={c:175,dir:t.H,sizes:[.5],stretch:[0,175],HDW:[.59,-.544,.5]},r={c:710,dir:t.H,sizes:[.5,.556,1,1.444,1.889]},o={c:732,dir:t.H,sizes:[.5,.556,1,1.444,1.889]},a={c:8211,dir:t.H,sizes:[.5],stretch:[0,8211],HDW:[.285,-.248,.5]},S={c:8592,dir:t.H,sizes:[1],stretch:[8592,8722],HDW:f.HDW3},l={c:8594,dir:t.H,sizes:[1],stretch:[0,8722,8594],HDW:f.HDW3},n={c:8596,dir:t.H,sizes:[1],stretch:[8592,8722,8594],HDW:f.HDW3},B={c:8612,dir:t.H,stretch:[8592,8722,8739],HDW:f.HDW3,min:1.278},d={c:8614,dir:t.H,sizes:[1],stretch:[8739,8722,8594],HDW:f.HDW3},u={c:8656,dir:t.H,sizes:[1],stretch:[8656,61],HDW:f.HDW3},k={c:8658,dir:t.H,sizes:[1],stretch:[0,61,8658],HDW:f.HDW3},I={c:8660,dir:t.H,sizes:[1],stretch:[8656,61,8658],HDW:f.HDW3},A={c:8722,dir:t.H,sizes:[.778],stretch:[0,8722],HDW:f.HDW3},p={c:8739,dir:t.V,sizes:[1],stretch:[0,8739],HDW:[.627,.015,.333]},m={c:9180,dir:t.H,sizes:[.778,1],schar:[8994,8994],variants:[5,0],stretch:[57680,57684,57681],HDW:[.32,.2,.5]},h={c:9181,dir:t.H,sizes:[.778,1],schar:[8995,8995],variants:[5,0],stretch:[57682,57684,57683],HDW:[.32,.2,.5]},M={c:9182,dir:t.H,stretch:[57680,57684,57681,57685],HDW:[.32,.2,.5],min:1.8},x={c:9183,dir:t.H,stretch:[57682,57684,57683,57686],HDW:[.32,.2,.5],min:1.8},H={c:10216,dir:t.V,sizes:f.VSIZES},_={c:10217,dir:t.V,sizes:f.VSIZES},E={c:10502,dir:t.H,stretch:[8656,61,8739],HDW:f.HDW3,min:1.278},y={c:10503,dir:t.H,stretch:[8872,61,8658],HDW:f.HDW3,min:1.278};f.delimiters={40:{dir:t.V,sizes:f.VSIZES,stretch:[9115,9116,9117],HDW:[.85,.349,.875]},41:{dir:t.V,sizes:f.VSIZES,stretch:[9118,9119,9120],HDW:[.85,.349,.875]},45:A,47:e,61:{dir:t.H,sizes:[.778],stretch:[0,61],HDW:f.HDW3},91:{dir:t.V,sizes:f.VSIZES,stretch:[9121,9122,9123],HDW:f.HDW2},92:{dir:t.V,sizes:f.VSIZES},93:{dir:t.V,sizes:f.VSIZES,stretch:[9124,9125,9126],HDW:f.HDW2},94:r,95:a,123:{dir:t.V,sizes:f.VSIZES,stretch:[9127,9130,9129,9128],HDW:[.85,.349,.889]},124:{dir:t.V,sizes:[1],stretch:[0,8739],HDW:[.75,.25,.333]},125:{dir:t.V,sizes:f.VSIZES,stretch:[9131,9130,9133,9132],HDW:[.85,.349,.889]},126:o,175:s,710:r,713:s,732:o,770:r,771:o,818:a,8211:a,8212:a,8213:a,8214:{dir:t.V,sizes:[.602,1],schar:[0,8741],variants:[1,0],stretch:[0,8741],HDW:[.602,0,.556]},8215:a,8254:s,8407:l,8592:S,8593:{dir:t.V,sizes:[.888],stretch:[8593,9168],HDW:[.6,0,.667]},8594:l,8595:{dir:t.V,sizes:[.888],stretch:[0,9168,8595],HDW:[.6,0,.667]},8596:n,8597:{dir:t.V,sizes:[1.044],stretch:[8593,9168,8595],HDW:f.HDW1},8606:{dir:t.H,sizes:[1],stretch:[8606,8722],HDW:f.HDW3},8608:{dir:t.H,sizes:[1],stretch:[0,8722,8608],HDW:f.HDW3},8612:B,8613:{dir:t.V,stretch:[8593,9168,8869],HDW:f.HDW1,min:1.555},8614:d,8615:{dir:t.V,stretch:[8868,9168,8595],HDW:f.HDW1,min:1.555},8624:{dir:t.V,sizes:[.722],stretch:[8624,9168],HDW:f.HDW1},8625:{dir:t.V,sizes:[.722],stretch:[8625,9168],HDW:f.HDW1},8636:{dir:t.H,sizes:[1],stretch:[8636,8722],HDW:f.HDW3},8637:{dir:t.H,sizes:[1],stretch:[8637,8722],HDW:f.HDW3},8638:{dir:t.V,sizes:[.888],stretch:[8638,9168],HDW:f.HDW1},8639:{dir:t.V,sizes:[.888],stretch:[8639,9168],HDW:f.HDW1},8640:{dir:t.H,sizes:[1],stretch:[0,8722,8640],HDW:f.HDW3},8641:{dir:t.H,sizes:[1],stretch:[0,8722,8641],HDW:f.HDW3},8642:{dir:t.V,sizes:[.888],stretch:[0,9168,8642],HDW:f.HDW1},8643:{dir:t.V,sizes:[.888],stretch:[0,9168,8643],HDW:f.HDW1},8656:u,8657:{dir:t.V,sizes:[.888],stretch:[8657,8214],HDW:[.599,0,.778]},8658:k,8659:{dir:t.V,sizes:[.888],stretch:[0,8214,8659],HDW:[.6,0,.778]},8660:I,8661:{dir:t.V,sizes:[1.044],stretch:[8657,8214,8659],HDW:[.75,.25,.778]},8666:{dir:t.H,sizes:[1],stretch:[8666,8801],HDW:[.464,-.036,.5]},8667:{dir:t.H,sizes:[1],stretch:[0,8801,8667],HDW:[.464,-.036,.5]},8722:A,8725:e,8730:{dir:t.V,sizes:f.VSIZES,stretch:[57345,57344,9143],fullExt:[.65,2.3],HDW:[.85,.35,1.056]},8739:p,8741:{dir:t.V,sizes:[1],stretch:[0,8741],HDW:[.627,.015,.556]},8968:{dir:t.V,sizes:f.VSIZES,stretch:[9121,9122],HDW:f.HDW2},8969:{dir:t.V,sizes:f.VSIZES,stretch:[9124,9125],HDW:f.HDW2},8970:{dir:t.V,sizes:f.VSIZES,stretch:[0,9122,9123],HDW:f.HDW2},8971:{dir:t.V,sizes:f.VSIZES,stretch:[0,9125,9126],HDW:f.HDW2},8978:m,8994:m,8995:h,9001:H,9002:_,9130:{dir:t.V,sizes:[.32],stretch:[9130,9130,9130],HDW:[.29,.015,.889]},9135:a,9136:{dir:t.V,sizes:[.989],stretch:[9127,9130,9133],HDW:[.75,.25,.889]},9137:{dir:t.V,sizes:[.989],stretch:[9131,9130,9129],HDW:[.75,.25,.889]},9140:{dir:t.H,stretch:[9484,8722,9488],HDW:f.HDW3,min:1},9141:{dir:t.H,stretch:[9492,8722,9496],HDW:f.HDW3,min:1},9168:{dir:t.V,sizes:[.602,1],schar:[0,8739],variants:[1,0],stretch:[0,8739],HDW:[.602,0,.333]},9180:m,9181:h,9182:M,9183:x,9184:{dir:t.H,stretch:[714,713,715],HDW:[.59,-.544,.5],min:1},9185:{dir:t.H,stretch:[715,713,714],HDW:[.59,-.544,.5],min:1},9472:a,10072:p,10216:H,10217:_,10222:{dir:t.V,sizes:[.989],stretch:[9127,9130,9129],HDW:[.75,.25,.889]},10223:{dir:t.V,sizes:[.989],stretch:[9131,9130,9133],HDW:[.75,.25,.889]},10229:S,10230:l,10231:n,10232:u,10233:k,10234:I,10235:B,10236:d,10237:E,10238:y,10502:E,10503:y,10574:{dir:t.H,stretch:[8636,8722,8640],HDW:f.HDW3,min:2},10575:{dir:t.V,stretch:[8638,9168,8642],HDW:f.HDW1,min:1.776},10576:{dir:t.H,stretch:[8637,8722,8641],HDW:f.HDW3,min:2},10577:{dir:t.V,stretch:[8639,9168,8643],HDW:f.HDW1,min:.5},10586:{dir:t.H,stretch:[8636,8722,8739],HDW:f.HDW3,min:1.278},10587:{dir:t.H,stretch:[8739,8722,8640],HDW:f.HDW3,min:1.278},10588:{dir:t.V,stretch:[8638,9168,8869],HDW:f.HDW1,min:1.556},10589:{dir:t.V,stretch:[8868,9168,8642],HDW:f.HDW1,min:1.556},10590:{dir:t.H,stretch:[8637,8722,8739],HDW:f.HDW3,min:1.278},10591:{dir:t.H,stretch:[8739,8722,8641],HDW:f.HDW3,min:1.278},10592:{dir:t.V,stretch:[8639,9168,8869],HDW:f.HDW1,min:1.776},10593:{dir:t.V,stretch:[8868,9168,8643],HDW:f.HDW1,min:1.776},12296:H,12297:_,65079:M,65080:x}},5674:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.doubleStruck=void 0,f.doubleStruck={}},7002:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.frakturBold=void 0,f.frakturBold={33:[.689,.012,.349],34:[.695,-.432,.254],38:[.696,.016,.871],39:[.695,-.436,.25],40:[.737,.186,.459],41:[.735,.187,.459],42:[.692,-.449,.328],43:[.598,.082,.893],44:[.107,.191,.328],45:[.275,-.236,.893],46:[.102,.015,.328],47:[.721,.182,.593],48:[.501,.012,.593],49:[.489,0,.593],50:[.491,0,.593],51:[.487,.193,.593],52:[.495,.196,.593],53:[.481,.19,.593],54:[.704,.012,.593],55:[.479,.197,.593],56:[.714,.005,.593],57:[.487,.195,.593],58:[.457,.012,.255],59:[.458,.19,.255],61:[.343,-.168,.582],63:[.697,.014,.428],91:[.74,.13,.257],93:[.738,.132,.257],94:[.734,-.452,.59],8216:[.708,-.411,.254],8217:[.692,-.394,.254],8260:[.721,.182,.593],58113:[.63,.027,.587],58114:[.693,.212,.394,{ic:.014}],58115:[.681,.219,.387],58116:[.473,.212,.593],58117:[.684,.027,.393],58120:[.679,.22,.981],58121:[.717,.137,.727]}},9349:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.fraktur=void 0,f.fraktur={33:[.689,.012,.296],34:[.695,-.432,.215],38:[.698,.011,.738],39:[.695,-.436,.212],40:[.737,.186,.389],41:[.735,.187,.389],42:[.692,-.449,.278],43:[.598,.082,.756],44:[.107,.191,.278],45:[.275,-.236,.756],46:[.102,.015,.278],47:[.721,.182,.502],48:[.492,.013,.502],49:[.468,0,.502],50:[.474,0,.502],51:[.473,.182,.502],52:[.476,.191,.502],53:[.458,.184,.502],54:[.7,.013,.502],55:[.468,.181,.502],56:[.705,.01,.502],57:[.469,.182,.502],58:[.457,.012,.216],59:[.458,.189,.216],61:[.368,-.132,.756],63:[.693,.011,.362],91:[.74,.13,.278],93:[.738,.131,.278],94:[.734,-.452,.5],8216:[.708,-.41,.215],8217:[.692,-.395,.215],8260:[.721,.182,.502],58112:[.683,.032,.497],58113:[.616,.03,.498],58114:[.68,.215,.333],58115:[.679,.224,.329],58116:[.471,.214,.503],58117:[.686,.02,.333],58118:[.577,.021,.334,{ic:.013}],58119:[.475,.022,.501,{ic:.013}]}},9741:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.italic=void 0,f.italic={33:[.716,0,.307,{ic:.073}],34:[.694,-.379,.514,{ic:.024}],35:[.694,.194,.818,{ic:.01}],37:[.75,.056,.818,{ic:.029}],38:[.716,.022,.767,{ic:.035}],39:[.694,-.379,.307,{ic:.07}],40:[.75,.25,.409,{ic:.108}],41:[.75,.25,.409],42:[.75,-.32,.511,{ic:.073}],43:[.557,.057,.767],44:[.121,.194,.307],45:[.251,-.18,.358],46:[.121,0,.307],47:[.716,.215,.778],48:[.665,.021,.511,{ic:.051}],49:[.666,0,.511],50:[.666,.022,.511,{ic:.04}],51:[.666,.022,.511,{ic:.051}],52:[.666,.194,.511],53:[.666,.022,.511,{ic:.056}],54:[.665,.022,.511,{ic:.054}],55:[.666,.022,.511,{ic:.123}],56:[.666,.021,.511,{ic:.042}],57:[.666,.022,.511,{ic:.042}],58:[.431,0,.307],59:[.431,.194,.307],61:[.367,-.133,.767],63:[.716,0,.511,{ic:.04}],64:[.705,.011,.767,{ic:.022}],91:[.75,.25,.307,{ic:.139}],93:[.75,.25,.307,{ic:.052}],94:[.694,-.527,.511,{ic:.017}],95:[-.025,.062,.511,{ic:.043}],126:[.318,-.208,.511,{ic:.06}],305:[.441,.01,.307,{ic:.033}],567:[.442,.204,.332],768:[.697,-.5,0],769:[.697,-.5,0,{ic:.039}],770:[.694,-.527,0,{ic:.017}],771:[.668,-.558,0,{ic:.06}],772:[.589,-.544,0,{ic:.054}],774:[.694,-.515,0,{ic:.062}],775:[.669,-.548,0],776:[.669,-.554,0,{ic:.045}],778:[.716,-.542,0],779:[.697,-.503,0,{ic:.065}],780:[.638,-.502,0,{ic:.029}],989:[.605,.085,.778],8211:[.285,-.248,.511,{ic:.043}],8212:[.285,-.248,1.022,{ic:.016}],8213:[.285,-.248,1.022,{ic:.016}],8215:[-.025,.062,.511,{ic:.043}],8216:[.694,-.379,.307,{ic:.055}],8217:[.694,-.379,.307,{ic:.07}],8220:[.694,-.379,.514,{ic:.092}],8221:[.694,-.379,.514,{ic:.024}],8260:[.716,.215,.778],8463:[.695,.013,.54,{ic:.022}],8710:[.716,0,.833,{sk:.167}],10744:[.716,.215,.778]}},2827:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.largeop=void 0,f.largeop={40:[1.15,.649,.597],41:[1.15,.649,.597],47:[1.15,.649,.811],91:[1.15,.649,.472],92:[1.15,.649,.811],93:[1.15,.649,.472],123:[1.15,.649,.667],125:[1.15,.649,.667],710:[.772,-.565,1],732:[.75,-.611,1],770:[.772,-.565,0],771:[.75,-.611,0],8214:[.602,0,.778],8260:[1.15,.649,.811],8593:[.6,0,.667],8595:[.6,0,.667],8657:[.599,0,.778],8659:[.6,0,.778],8719:[.95,.45,1.278],8720:[.95,.45,1.278],8721:[.95,.45,1.444],8730:[1.15,.65,1,{ic:.02}],8739:[.627,.015,.333],8741:[.627,.015,.556],8747:[1.36,.862,.556,{ic:.388}],8748:[1.36,.862,1.084,{ic:.388}],8749:[1.36,.862,1.592,{ic:.388}],8750:[1.36,.862,.556,{ic:.388}],8896:[.95,.45,1.111],8897:[.95,.45,1.111],8898:[.949,.45,1.111],8899:[.95,.449,1.111],8968:[1.15,.649,.528],8969:[1.15,.649,.528],8970:[1.15,.649,.528],8971:[1.15,.649,.528],9001:[1.15,.649,.611],9002:[1.15,.649,.611],9168:[.602,0,.667],10072:[.627,.015,.333],10216:[1.15,.649,.611],10217:[1.15,.649,.611],10752:[.949,.449,1.511],10753:[.949,.449,1.511],10754:[.949,.449,1.511],10756:[.95,.449,1.111],10758:[.95,.45,1.111],10764:[1.36,.862,2.168,{ic:.388}],12296:[1.15,.649,.611],12297:[1.15,.649,.611]}},2970:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.monospace=void 0,f.monospace={32:[0,0,.525],33:[.622,0,.525],34:[.623,-.333,.525],35:[.611,0,.525],36:[.694,.082,.525],37:[.694,.083,.525],38:[.622,.011,.525],39:[.611,-.287,.525],40:[.694,.082,.525],41:[.694,.082,.525],42:[.52,-.09,.525],43:[.531,-.081,.525],44:[.14,.139,.525],45:[.341,-.271,.525],46:[.14,0,.525],47:[.694,.083,.525],58:[.431,0,.525],59:[.431,.139,.525],60:[.557,-.055,.525],61:[.417,-.195,.525],62:[.557,-.055,.525],63:[.617,0,.525],64:[.617,.006,.525],91:[.694,.082,.525],92:[.694,.083,.525],93:[.694,.082,.525],94:[.611,-.46,.525],95:[-.025,.095,.525],96:[.681,-.357,.525],123:[.694,.083,.525],124:[.694,.082,.525],125:[.694,.083,.525],126:[.611,-.466,.525],127:[.612,-.519,.525],160:[0,0,.525],305:[.431,0,.525],567:[.431,.228,.525],697:[.623,-.334,.525],768:[.611,-.485,0],769:[.611,-.485,0],770:[.611,-.46,0],771:[.611,-.466,0],772:[.577,-.5,0],774:[.611,-.504,0],776:[.612,-.519,0],778:[.619,-.499,0],780:[.577,-.449,0],913:[.623,0,.525],914:[.611,0,.525],915:[.611,0,.525],916:[.623,0,.525],917:[.611,0,.525],918:[.611,0,.525],919:[.611,0,.525],920:[.621,.01,.525],921:[.611,0,.525],922:[.611,0,.525],923:[.623,0,.525],924:[.611,0,.525],925:[.611,0,.525],926:[.611,0,.525],927:[.621,.01,.525],928:[.611,0,.525],929:[.611,0,.525],931:[.611,0,.525],932:[.611,0,.525],933:[.622,0,.525],934:[.611,0,.525],935:[.611,0,.525],936:[.611,0,.525],937:[.622,0,.525],8215:[-.025,.095,.525],8242:[.623,-.334,.525],8243:[.623,0,1.05],8244:[.623,0,1.575],8260:[.694,.083,.525],8279:[.623,0,2.1],8710:[.623,0,.525]}},1668:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.normal=void 0,f.normal={32:[0,0,.25],33:[.716,0,.278],34:[.694,-.379,.5],35:[.694,.194,.833],36:[.75,.056,.5],37:[.75,.056,.833],38:[.716,.022,.778],39:[.694,-.379,.278],40:[.75,.25,.389],41:[.75,.25,.389],42:[.75,-.32,.5],43:[.583,.082,.778],44:[.121,.194,.278],45:[.252,-.179,.333],46:[.12,0,.278],47:[.75,.25,.5],48:[.666,.022,.5],49:[.666,0,.5],50:[.666,0,.5],51:[.665,.022,.5],52:[.677,0,.5],53:[.666,.022,.5],54:[.666,.022,.5],55:[.676,.022,.5],56:[.666,.022,.5],57:[.666,.022,.5],58:[.43,0,.278],59:[.43,.194,.278],60:[.54,.04,.778],61:[.583,.082,.778],62:[.54,.04,.778],63:[.705,0,.472],64:[.705,.011,.778],65:[.716,0,.75],66:[.683,0,.708],67:[.705,.021,.722],68:[.683,0,.764],69:[.68,0,.681],70:[.68,0,.653],71:[.705,.022,.785],72:[.683,0,.75],73:[.683,0,.361],74:[.683,.022,.514],75:[.683,0,.778],76:[.683,0,.625],77:[.683,0,.917],78:[.683,0,.75],79:[.705,.022,.778],80:[.683,0,.681],81:[.705,.193,.778],82:[.683,.022,.736],83:[.705,.022,.556],84:[.677,0,.722],85:[.683,.022,.75],86:[.683,.022,.75],87:[.683,.022,1.028],88:[.683,0,.75],89:[.683,0,.75],90:[.683,0,.611],91:[.75,.25,.278],92:[.75,.25,.5],93:[.75,.25,.278],94:[.694,-.531,.5],95:[-.025,.062,.5],96:[.699,-.505,.5],97:[.448,.011,.5],98:[.694,.011,.556],99:[.448,.011,.444],100:[.694,.011,.556],101:[.448,.011,.444],102:[.705,0,.306,{ic:.066}],103:[.453,.206,.5],104:[.694,0,.556],105:[.669,0,.278],106:[.669,.205,.306],107:[.694,0,.528],108:[.694,0,.278],109:[.442,0,.833],110:[.442,0,.556],111:[.448,.01,.5],112:[.442,.194,.556],113:[.442,.194,.528],114:[.442,0,.392],115:[.448,.011,.394],116:[.615,.01,.389],117:[.442,.011,.556],118:[.431,.011,.528],119:[.431,.011,.722],120:[.431,0,.528],121:[.431,.204,.528],122:[.431,0,.444],123:[.75,.25,.5],124:[.75,.249,.278],125:[.75,.25,.5],126:[.318,-.215,.5],160:[0,0,.25],163:[.714,.011,.769],165:[.683,0,.75],168:[.669,-.554,.5],172:[.356,-.089,.667],174:[.709,.175,.947],175:[.59,-.544,.5],176:[.715,-.542,.5],177:[.666,0,.778],180:[.699,-.505,.5],183:[.31,-.19,.278],215:[.491,-.009,.778],240:[.749,.021,.556],247:[.537,.036,.778],305:[.442,0,.278,{sk:.0278}],567:[.442,.205,.306,{sk:.0833}],697:[.56,-.043,.275],710:[.694,-.531,.5],711:[.644,-.513,.5],713:[.59,-.544,.5],714:[.699,-.505,.5],715:[.699,-.505,.5],728:[.694,-.515,.5],729:[.669,-.549,.5],730:[.715,-.542,.5],732:[.668,-.565,.5],768:[.699,-.505,0],769:[.699,-.505,0],770:[.694,-.531,0],771:[.668,-.565,0],772:[.59,-.544,0],774:[.694,-.515,0],775:[.669,-.549,0],776:[.669,-.554,0],778:[.715,-.542,0],779:[.701,-.51,0],780:[.644,-.513,0],824:[.716,.215,0],913:[.716,0,.75],914:[.683,0,.708],915:[.68,0,.625],916:[.716,0,.833],917:[.68,0,.681],918:[.683,0,.611],919:[.683,0,.75],920:[.705,.022,.778],921:[.683,0,.361],922:[.683,0,.778],923:[.716,0,.694],924:[.683,0,.917],925:[.683,0,.75],926:[.677,0,.667],927:[.705,.022,.778],928:[.68,0,.75],929:[.683,0,.681],931:[.683,0,.722],932:[.677,0,.722],933:[.705,0,.778],934:[.683,0,.722],935:[.683,0,.75],936:[.683,0,.778],937:[.704,0,.722],8192:[0,0,.5],8193:[0,0,1],8194:[0,0,.5],8195:[0,0,1],8196:[0,0,.333],8197:[0,0,.25],8198:[0,0,.167],8201:[0,0,.167],8202:[0,0,.1],8203:[0,0,0],8204:[0,0,0],8211:[.285,-.248,.5],8212:[.285,-.248,1],8213:[.285,-.248,1],8214:[.75,.25,.5],8215:[-.025,.062,.5],8216:[.694,-.379,.278],8217:[.694,-.379,.278],8220:[.694,-.379,.5],8221:[.694,-.379,.5],8224:[.705,.216,.444],8225:[.705,.205,.444],8226:[.444,-.055,.5],8230:[.12,0,1.172],8242:[.56,-.043,.275],8243:[.56,0,.55],8244:[.56,0,.825],8245:[.56,-.043,.275],8246:[.56,0,.55],8247:[.56,0,.825],8254:[.59,-.544,.5],8260:[.75,.25,.5],8279:[.56,0,1.1],8288:[0,0,0],8289:[0,0,0],8290:[0,0,0],8291:[0,0,0],8292:[0,0,0],8407:[.714,-.516,.5],8450:[.702,.019,.722],8459:[.717,.036,.969,{ic:.272,sk:.333}],8460:[.666,.133,.72],8461:[.683,0,.778],8462:[.694,.011,.576,{sk:-.0278}],8463:[.695,.013,.54,{ic:.022}],8464:[.717,.017,.809,{ic:.137,sk:.333}],8465:[.686,.026,.554],8466:[.717,.017,.874,{ic:.161,sk:.306}],8467:[.705,.02,.417,{sk:.111}],8469:[.683,.02,.722],8472:[.453,.216,.636,{sk:.111}],8473:[.683,0,.611],8474:[.701,.181,.778],8475:[.717,.017,.85,{ic:.037,sk:.194}],8476:[.686,.026,.828],8477:[.683,0,.722],8484:[.683,0,.667],8486:[.704,0,.722],8487:[.684,.022,.722],8488:[.729,.139,.602],8492:[.708,.028,.908,{ic:.02,sk:.194}],8493:[.685,.024,.613],8496:[.707,.008,.562,{ic:.156,sk:.139}],8497:[.735,.036,.895,{ic:.095,sk:.222}],8498:[.695,0,.556],8499:[.721,.05,1.08,{ic:.136,sk:.444}],8501:[.694,0,.611],8502:[.763,.021,.667,{ic:.02}],8503:[.764,.043,.444],8504:[.764,.043,.667],8513:[.705,.023,.639],8592:[.511,.011,1],8593:[.694,.193,.5],8594:[.511,.011,1],8595:[.694,.194,.5],8596:[.511,.011,1],8597:[.772,.272,.5],8598:[.72,.195,1],8599:[.72,.195,1],8600:[.695,.22,1],8601:[.695,.22,1],8602:[.437,-.06,1],8603:[.437,-.06,1],8606:[.417,-.083,1],8608:[.417,-.083,1],8610:[.417,-.083,1.111],8611:[.417,-.083,1.111],8614:[.511,.011,1],8617:[.511,.011,1.126],8618:[.511,.011,1.126],8619:[.575,.041,1],8620:[.575,.041,1],8621:[.417,-.083,1.389],8622:[.437,-.06,1],8624:[.722,0,.5],8625:[.722,0,.5],8630:[.461,0,1],8631:[.46,0,1],8634:[.65,.083,.778],8635:[.65,.083,.778],8636:[.511,-.23,1],8637:[.27,.011,1],8638:[.694,.194,.417],8639:[.694,.194,.417],8640:[.511,-.23,1],8641:[.27,.011,1],8642:[.694,.194,.417],8643:[.694,.194,.417],8644:[.667,0,1],8646:[.667,0,1],8647:[.583,.083,1],8648:[.694,.193,.833],8649:[.583,.083,1],8650:[.694,.194,.833],8651:[.514,.014,1],8652:[.671,.011,1],8653:[.534,.035,1],8654:[.534,.037,1],8655:[.534,.035,1],8656:[.525,.024,1],8657:[.694,.194,.611],8658:[.525,.024,1],8659:[.694,.194,.611],8660:[.526,.025,1],8661:[.772,.272,.611],8666:[.611,.111,1],8667:[.611,.111,1],8669:[.417,-.083,1],8672:[.437,-.064,1.334],8674:[.437,-.064,1.334],8704:[.694,.022,.556],8705:[.846,.021,.5],8706:[.715,.022,.531,{ic:.035,sk:.0833}],8707:[.694,0,.556],8708:[.716,.215,.556],8709:[.772,.078,.5],8710:[.716,0,.833],8711:[.683,.033,.833],8712:[.54,.04,.667],8713:[.716,.215,.667],8715:[.54,.04,.667],8716:[.716,.215,.667],8717:[.44,0,.429,{ic:.027}],8719:[.75,.25,.944],8720:[.75,.25,.944],8721:[.75,.25,1.056],8722:[.583,.082,.778],8723:[.5,.166,.778],8724:[.766,.093,.778],8725:[.75,.25,.5],8726:[.75,.25,.5],8727:[.465,-.035,.5],8728:[.444,-.055,.5],8729:[.444,-.055,.5],8730:[.8,.2,.833,{ic:.02}],8733:[.442,.011,.778],8734:[.442,.011,1],8736:[.694,0,.722],8737:[.714,.02,.722],8738:[.551,.051,.722],8739:[.75,.249,.278],8740:[.75,.252,.278,{ic:.019}],8741:[.75,.25,.5],8742:[.75,.25,.5,{ic:.018}],8743:[.598,.022,.667],8744:[.598,.022,.667],8745:[.598,.022,.667],8746:[.598,.022,.667],8747:[.716,.216,.417,{ic:.055}],8748:[.805,.306,.819,{ic:.138}],8749:[.805,.306,1.166,{ic:.138}],8750:[.805,.306,.472,{ic:.138}],8756:[.471,.082,.667],8757:[.471,.082,.667],8764:[.367,-.133,.778],8765:[.367,-.133,.778],8768:[.583,.083,.278],8769:[.467,-.032,.778],8770:[.463,-.034,.778],8771:[.464,-.036,.778],8772:[.716,.215,.778],8773:[.589,-.022,.778],8775:[.652,.155,.778],8776:[.483,-.055,.778],8777:[.716,.215,.778],8778:[.579,.039,.778],8781:[.484,-.016,.778],8782:[.492,-.008,.778],8783:[.492,-.133,.778],8784:[.67,-.133,.778],8785:[.609,.108,.778],8786:[.601,.101,.778],8787:[.601,.102,.778],8790:[.367,-.133,.778],8791:[.721,-.133,.778],8796:[.859,-.133,.778],8800:[.716,.215,.778],8801:[.464,-.036,.778],8802:[.716,.215,.778],8804:[.636,.138,.778],8805:[.636,.138,.778],8806:[.753,.175,.778],8807:[.753,.175,.778],8808:[.752,.286,.778],8809:[.752,.286,.778],8810:[.568,.067,1],8811:[.567,.067,1],8812:[.75,.25,.5],8813:[.716,.215,.778],8814:[.708,.209,.778],8815:[.708,.209,.778],8816:[.801,.303,.778],8817:[.801,.303,.778],8818:[.732,.228,.778],8819:[.732,.228,.778],8820:[.732,.228,.778],8821:[.732,.228,.778],8822:[.681,.253,.778],8823:[.681,.253,.778],8824:[.716,.253,.778],8825:[.716,.253,.778],8826:[.539,.041,.778],8827:[.539,.041,.778],8828:[.58,.153,.778],8829:[.58,.154,.778],8830:[.732,.228,.778],8831:[.732,.228,.778],8832:[.705,.208,.778],8833:[.705,.208,.778],8834:[.54,.04,.778],8835:[.54,.04,.778],8836:[.716,.215,.778],8837:[.716,.215,.778],8838:[.636,.138,.778],8839:[.636,.138,.778],8840:[.801,.303,.778],8841:[.801,.303,.778],8842:[.635,.241,.778],8843:[.635,.241,.778],8846:[.598,.022,.667],8847:[.539,.041,.778],8848:[.539,.041,.778],8849:[.636,.138,.778],8850:[.636,.138,.778],8851:[.598,0,.667],8852:[.598,0,.667],8853:[.583,.083,.778],8854:[.583,.083,.778],8855:[.583,.083,.778],8856:[.583,.083,.778],8857:[.583,.083,.778],8858:[.582,.082,.778],8859:[.582,.082,.778],8861:[.582,.082,.778],8862:[.689,0,.778],8863:[.689,0,.778],8864:[.689,0,.778],8865:[.689,0,.778],8866:[.694,0,.611],8867:[.694,0,.611],8868:[.668,0,.778],8869:[.668,0,.778],8872:[.75,.249,.867],8873:[.694,0,.722],8874:[.694,0,.889],8876:[.695,0,.611],8877:[.695,0,.611],8878:[.695,0,.722],8879:[.695,0,.722],8882:[.539,.041,.778],8883:[.539,.041,.778],8884:[.636,.138,.778],8885:[.636,.138,.778],8888:[.408,-.092,1.111],8890:[.431,.212,.556],8891:[.716,0,.611],8892:[.716,0,.611],8896:[.75,.249,.833],8897:[.75,.249,.833],8898:[.75,.249,.833],8899:[.75,.249,.833],8900:[.488,-.012,.5],8901:[.31,-.19,.278],8902:[.486,-.016,.5],8903:[.545,.044,.778],8904:[.505,.005,.9],8905:[.492,-.008,.778],8906:[.492,-.008,.778],8907:[.694,.022,.778],8908:[.694,.022,.778],8909:[.464,-.036,.778],8910:[.578,.021,.76],8911:[.578,.022,.76],8912:[.54,.04,.778],8913:[.54,.04,.778],8914:[.598,.022,.667],8915:[.598,.022,.667],8916:[.736,.022,.667],8918:[.541,.041,.778],8919:[.541,.041,.778],8920:[.568,.067,1.333],8921:[.568,.067,1.333],8922:[.886,.386,.778],8923:[.886,.386,.778],8926:[.734,0,.778],8927:[.734,0,.778],8928:[.801,.303,.778],8929:[.801,.303,.778],8930:[.716,.215,.778],8931:[.716,.215,.778],8934:[.73,.359,.778],8935:[.73,.359,.778],8936:[.73,.359,.778],8937:[.73,.359,.778],8938:[.706,.208,.778],8939:[.706,.208,.778],8940:[.802,.303,.778],8941:[.801,.303,.778],8942:[1.3,.03,.278],8943:[.31,-.19,1.172],8945:[1.52,-.1,1.282],8965:[.716,0,.611],8966:[.813,.097,.611],8968:[.75,.25,.444],8969:[.75,.25,.444],8970:[.75,.25,.444],8971:[.75,.25,.444],8988:[.694,-.306,.5],8989:[.694,-.306,.5],8990:[.366,.022,.5],8991:[.366,.022,.5],8994:[.388,-.122,1],8995:[.378,-.134,1],9001:[.75,.25,.389],9002:[.75,.25,.389],9136:[.744,.244,.412],9137:[.744,.244,.412],9168:[.602,0,.667],9416:[.709,.175,.902],9484:[.694,-.306,.5],9488:[.694,-.306,.5],9492:[.366,.022,.5],9496:[.366,.022,.5],9585:[.694,.195,.889],9586:[.694,.195,.889],9632:[.689,0,.778],9633:[.689,0,.778],9642:[.689,0,.778],9650:[.575,.02,.722],9651:[.716,0,.889],9652:[.575,.02,.722],9653:[.716,0,.889],9654:[.539,.041,.778],9656:[.539,.041,.778],9657:[.505,.005,.5],9660:[.576,.019,.722],9661:[.5,.215,.889],9662:[.576,.019,.722],9663:[.5,.215,.889],9664:[.539,.041,.778],9666:[.539,.041,.778],9667:[.505,.005,.5],9674:[.716,.132,.667],9711:[.715,.215,1],9723:[.689,0,.778],9724:[.689,0,.778],9733:[.694,.111,.944],9824:[.727,.13,.778],9825:[.716,.033,.778],9826:[.727,.162,.778],9827:[.726,.13,.778],9837:[.75,.022,.389],9838:[.734,.223,.389],9839:[.723,.223,.389],10003:[.706,.034,.833],10016:[.716,.022,.833],10072:[.75,.249,.278],10216:[.75,.25,.389],10217:[.75,.25,.389],10222:[.744,.244,.412],10223:[.744,.244,.412],10229:[.511,.011,1.609],10230:[.511,.011,1.638],10231:[.511,.011,1.859],10232:[.525,.024,1.609],10233:[.525,.024,1.638],10234:[.525,.024,1.858],10236:[.511,.011,1.638],10731:[.716,.132,.667],10744:[.716,.215,.778],10752:[.75,.25,1.111],10753:[.75,.25,1.111],10754:[.75,.25,1.111],10756:[.75,.249,.833],10758:[.75,.249,.833],10764:[.805,.306,1.638,{ic:.138}],10799:[.491,-.009,.778],10815:[.683,0,.75],10846:[.813,.097,.611],10877:[.636,.138,.778],10878:[.636,.138,.778],10885:[.762,.29,.778],10886:[.762,.29,.778],10887:[.635,.241,.778],10888:[.635,.241,.778],10889:[.761,.387,.778],10890:[.761,.387,.778],10891:[1.003,.463,.778],10892:[1.003,.463,.778],10901:[.636,.138,.778],10902:[.636,.138,.778],10927:[.636,.138,.778],10928:[.636,.138,.778],10933:[.752,.286,.778],10934:[.752,.286,.778],10935:[.761,.294,.778],10936:[.761,.294,.778],10937:[.761,.337,.778],10938:[.761,.337,.778],10949:[.753,.215,.778],10950:[.753,.215,.778],10955:[.783,.385,.778],10956:[.783,.385,.778],12296:[.75,.25,.389],12297:[.75,.25,.389],57350:[.43,.023,.222,{ic:.018}],57351:[.431,.024,.389,{ic:.018}],57352:[.605,.085,.778],57353:[.434,.006,.667,{ic:.067}],57356:[.752,.284,.778],57357:[.752,.284,.778],57358:[.919,.421,.778],57359:[.801,.303,.778],57360:[.801,.303,.778],57361:[.919,.421,.778],57366:[.828,.33,.778],57367:[.752,.332,.778],57368:[.828,.33,.778],57369:[.752,.333,.778],57370:[.634,.255,.778],57371:[.634,.254,.778],119808:[.698,0,.869],119809:[.686,0,.818],119810:[.697,.011,.831],119811:[.686,0,.882],119812:[.68,0,.756],119813:[.68,0,.724],119814:[.697,.01,.904],119815:[.686,0,.9],119816:[.686,0,.436],119817:[.686,.011,.594],119818:[.686,0,.901],119819:[.686,0,.692],119820:[.686,0,1.092],119821:[.686,0,.9],119822:[.696,.01,.864],119823:[.686,0,.786],119824:[.696,.193,.864],119825:[.686,.011,.862],119826:[.697,.011,.639],119827:[.675,0,.8],119828:[.686,.011,.885],119829:[.686,.007,.869],119830:[.686,.007,1.189],119831:[.686,0,.869],119832:[.686,0,.869],119833:[.686,0,.703],119834:[.453,.006,.559],119835:[.694,.006,.639],119836:[.453,.006,.511],119837:[.694,.006,.639],119838:[.452,.006,.527],119839:[.7,0,.351,{ic:.101}],119840:[.455,.201,.575],119841:[.694,0,.639],119842:[.695,0,.319],119843:[.695,.2,.351],119844:[.694,0,.607],119845:[.694,0,.319],119846:[.45,0,.958],119847:[.45,0,.639],119848:[.452,.005,.575],119849:[.45,.194,.639],119850:[.45,.194,.607],119851:[.45,0,.474],119852:[.453,.006,.454],119853:[.635,.005,.447],119854:[.45,.006,.639],119855:[.444,0,.607],119856:[.444,0,.831],119857:[.444,0,.607],119858:[.444,.2,.607],119859:[.444,0,.511],119860:[.716,0,.75,{sk:.139}],119861:[.683,0,.759,{sk:.0833}],119862:[.705,.022,.715,{ic:.045,sk:.0833}],119863:[.683,0,.828,{sk:.0556}],119864:[.68,0,.738,{ic:.026,sk:.0833}],119865:[.68,0,.643,{ic:.106,sk:.0833}],119866:[.705,.022,.786,{sk:.0833}],119867:[.683,0,.831,{ic:.057,sk:.0556}],119868:[.683,0,.44,{ic:.064,sk:.111}],119869:[.683,.022,.555,{ic:.078,sk:.167}],119870:[.683,0,.849,{ic:.04,sk:.0556}],119871:[.683,0,.681,{sk:.0278}],119872:[.683,0,.97,{ic:.081,sk:.0833}],119873:[.683,0,.803,{ic:.085,sk:.0833}],119874:[.704,.022,.763,{sk:.0833}],119875:[.683,0,.642,{ic:.109,sk:.0833}],119876:[.704,.194,.791,{sk:.0833}],119877:[.683,.021,.759,{sk:.0833}],119878:[.705,.022,.613,{ic:.032,sk:.0833}],119879:[.677,0,.584,{ic:.12,sk:.0833}],119880:[.683,.022,.683,{ic:.084,sk:.0278}],119881:[.683,.022,.583,{ic:.186}],119882:[.683,.022,.944,{ic:.104}],119883:[.683,0,.828,{ic:.024,sk:.0833}],119884:[.683,0,.581,{ic:.182}],119885:[.683,0,.683,{ic:.04,sk:.0833}],119886:[.441,.01,.529],119887:[.694,.011,.429],119888:[.442,.011,.433,{sk:.0556}],119889:[.694,.01,.52,{sk:.167}],119890:[.442,.011,.466,{sk:.0556}],119891:[.705,.205,.49,{ic:.06,sk:.167}],119892:[.442,.205,.477,{sk:.0278}],119894:[.661,.011,.345],119895:[.661,.204,.412],119896:[.694,.011,.521],119897:[.694,.011,.298,{sk:.0833}],119898:[.442,.011,.878],119899:[.442,.011,.6],119900:[.441,.011,.485,{sk:.0556}],119901:[.442,.194,.503,{sk:.0833}],119902:[.442,.194,.446,{ic:.014,sk:.0833}],119903:[.442,.011,.451,{sk:.0556}],119904:[.442,.01,.469,{sk:.0556}],119905:[.626,.011,.361,{sk:.0833}],119906:[.442,.011,.572,{sk:.0278}],119907:[.443,.011,.485,{sk:.0278}],119908:[.443,.011,.716,{sk:.0833}],119909:[.442,.011,.572,{sk:.0278}],119910:[.442,.205,.49,{sk:.0556}],119911:[.442,.011,.465,{sk:.0556}],119912:[.711,0,.869,{sk:.16}],119913:[.686,0,.866,{sk:.0958}],119914:[.703,.017,.817,{ic:.038,sk:.0958}],119915:[.686,0,.938,{sk:.0639}],119916:[.68,0,.81,{ic:.015,sk:.0958}],119917:[.68,0,.689,{ic:.12,sk:.0958}],119918:[.703,.016,.887,{sk:.0958}],119919:[.686,0,.982,{ic:.045,sk:.0639}],119920:[.686,0,.511,{ic:.062,sk:.128}],119921:[.686,.017,.631,{ic:.063,sk:.192}],119922:[.686,0,.971,{ic:.032,sk:.0639}],119923:[.686,0,.756,{sk:.0319}],119924:[.686,0,1.142,{ic:.077,sk:.0958}],119925:[.686,0,.95,{ic:.077,sk:.0958}],119926:[.703,.017,.837,{sk:.0958}],119927:[.686,0,.723,{ic:.124,sk:.0958}],119928:[.703,.194,.869,{sk:.0958}],119929:[.686,.017,.872,{sk:.0958}],119930:[.703,.017,.693,{ic:.021,sk:.0958}],119931:[.675,0,.637,{ic:.135,sk:.0958}],119932:[.686,.016,.8,{ic:.077,sk:.0319}],119933:[.686,.016,.678,{ic:.208}],119934:[.686,.017,1.093,{ic:.114}],119935:[.686,0,.947,{sk:.0958}],119936:[.686,0,.675,{ic:.201}],119937:[.686,0,.773,{ic:.032,sk:.0958}],119938:[.452,.008,.633],119939:[.694,.008,.521],119940:[.451,.008,.513,{sk:.0639}],119941:[.694,.008,.61,{sk:.192}],119942:[.452,.008,.554,{sk:.0639}],119943:[.701,.201,.568,{ic:.056,sk:.192}],119944:[.452,.202,.545,{sk:.0319}],119945:[.694,.008,.668,{sk:-.0319}],119946:[.694,.008,.405],119947:[.694,.202,.471],119948:[.694,.008,.604],119949:[.694,.008,.348,{sk:.0958}],119950:[.452,.008,1.032],119951:[.452,.008,.713],119952:[.452,.008,.585,{sk:.0639}],119953:[.452,.194,.601,{sk:.0958}],119954:[.452,.194,.542,{sk:.0958}],119955:[.452,.008,.529,{sk:.0639}],119956:[.451,.008,.531,{sk:.0639}],119957:[.643,.007,.415,{sk:.0958}],119958:[.452,.008,.681,{sk:.0319}],119959:[.453,.008,.567,{sk:.0319}],119960:[.453,.008,.831,{sk:.0958}],119961:[.452,.008,.659,{sk:.0319}],119962:[.452,.202,.59,{sk:.0639}],119963:[.452,.008,.555,{sk:.0639}],119964:[.717,.008,.803,{ic:.213,sk:.389}],119966:[.728,.026,.666,{ic:.153,sk:.278}],119967:[.708,.031,.774,{ic:.081,sk:.111}],119970:[.717,.037,.61,{ic:.128,sk:.25}],119973:[.717,.314,1.052,{ic:.081,sk:.417}],119974:[.717,.037,.914,{ic:.29,sk:.361}],119977:[.726,.036,.902,{ic:.306,sk:.389}],119978:[.707,.008,.738,{ic:.067,sk:.167}],119979:[.716,.037,1.013,{ic:.018,sk:.222}],119980:[.717,.017,.883,{sk:.278}],119982:[.708,.036,.868,{ic:.148,sk:.333}],119983:[.735,.037,.747,{ic:.249,sk:.222}],119984:[.717,.017,.8,{ic:.16,sk:.25}],119985:[.717,.017,.622,{ic:.228,sk:.222}],119986:[.717,.017,.805,{ic:.221,sk:.25}],119987:[.717,.017,.944,{ic:.187,sk:.278}],119988:[.716,.017,.71,{ic:.249,sk:.194}],119989:[.717,.016,.821,{ic:.211,sk:.306}],120068:[.696,.026,.718],120069:[.691,.027,.884],120071:[.685,.027,.832],120072:[.685,.024,.663],120073:[.686,.153,.611],120074:[.69,.026,.785],120077:[.686,.139,.552],120078:[.68,.027,.668,{ic:.014}],120079:[.686,.026,.666],120080:[.692,.027,1.05],120081:[.686,.025,.832],120082:[.729,.027,.827],120083:[.692,.218,.828],120084:[.729,.069,.827],120086:[.692,.027,.829],120087:[.701,.027,.669],120088:[.697,.027,.646,{ic:.019}],120089:[.686,.026,.831],120090:[.686,.027,1.046],120091:[.688,.027,.719],120092:[.686,.218,.833],120094:[.47,.035,.5],120095:[.685,.031,.513],120096:[.466,.029,.389],120097:[.609,.033,.499],120098:[.467,.03,.401],120099:[.681,.221,.326],120100:[.47,.209,.504],120101:[.688,.205,.521],120102:[.673,.02,.279],120103:[.672,.208,.281],120104:[.689,.025,.389],120105:[.685,.02,.28],120106:[.475,.026,.767],120107:[.475,.022,.527],120108:[.48,.028,.489],120109:[.541,.212,.5],120110:[.479,.219,.489],120111:[.474,.021,.389],120112:[.478,.029,.443],120113:[.64,.02,.333,{ic:.015}],120114:[.474,.023,.517],120115:[.53,.028,.512],120116:[.532,.028,.774],120117:[.472,.188,.389],120118:[.528,.218,.499],120119:[.471,.214,.391],120120:[.701,0,.722],120121:[.683,0,.667],120123:[.683,0,.722],120124:[.683,0,.667],120125:[.683,0,.611],120126:[.702,.019,.778],120128:[.683,0,.389],120129:[.683,.077,.5],120130:[.683,0,.778],120131:[.683,0,.667],120132:[.683,0,.944],120134:[.701,.019,.778],120138:[.702,.012,.556],120139:[.683,0,.667],120140:[.683,.019,.722],120141:[.683,.02,.722],120142:[.683,.019,1],120143:[.683,0,.722],120144:[.683,0,.722],120172:[.686,.031,.847],120173:[.684,.031,1.044],120174:[.676,.032,.723],120175:[.683,.029,.982],120176:[.686,.029,.783],120177:[.684,.146,.722],120178:[.687,.029,.927],120179:[.683,.126,.851],120180:[.681,.025,.655],120181:[.68,.141,.652],120182:[.681,.026,.789,{ic:.017}],120183:[.683,.028,.786],120184:[.683,.032,1.239],120185:[.679,.03,.983],120186:[.726,.03,.976],120187:[.688,.223,.977],120188:[.726,.083,.976],120189:[.688,.028,.978],120190:[.685,.031,.978],120191:[.686,.03,.79,{ic:.012}],120192:[.688,.039,.851,{ic:.02}],120193:[.685,.029,.982],120194:[.683,.03,1.235],120195:[.681,.035,.849],120196:[.688,.214,.984],120197:[.677,.148,.711],120198:[.472,.032,.603],120199:[.69,.032,.59],120200:[.473,.026,.464],120201:[.632,.028,.589],120202:[.471,.027,.472],120203:[.687,.222,.388],120204:[.472,.208,.595],120205:[.687,.207,.615],120206:[.686,.025,.331],120207:[.682,.203,.332],120208:[.682,.025,.464],120209:[.681,.024,.337],120210:[.476,.031,.921],120211:[.473,.028,.654],120212:[.482,.034,.609],120213:[.557,.207,.604],120214:[.485,.211,.596],120215:[.472,.026,.46],120216:[.479,.034,.523],120217:[.648,.027,.393,{ic:.014}],120218:[.472,.032,.589,{ic:.014}],120219:[.546,.027,.604],120220:[.549,.032,.918],120221:[.471,.188,.459],120222:[.557,.221,.589],120223:[.471,.214,.461],120224:[.694,0,.667],120225:[.694,0,.667],120226:[.705,.011,.639],120227:[.694,0,.722],120228:[.691,0,.597],120229:[.691,0,.569],120230:[.704,.011,.667],120231:[.694,0,.708],120232:[.694,0,.278],120233:[.694,.022,.472],120234:[.694,0,.694],120235:[.694,0,.542],120236:[.694,0,.875],120237:[.694,0,.708],120238:[.715,.022,.736],120239:[.694,0,.639],120240:[.715,.125,.736],120241:[.694,0,.646],120242:[.716,.022,.556],120243:[.688,0,.681],120244:[.694,.022,.688],120245:[.694,0,.667],120246:[.694,0,.944],120247:[.694,0,.667],120248:[.694,0,.667],120249:[.694,0,.611],120250:[.46,.01,.481],120251:[.694,.011,.517],120252:[.46,.01,.444],120253:[.694,.01,.517],120254:[.461,.01,.444],120255:[.705,0,.306,{ic:.041}],120256:[.455,.206,.5],120257:[.694,0,.517],120258:[.68,0,.239],120259:[.68,.205,.267],120260:[.694,0,.489],120261:[.694,0,.239],120262:[.455,0,.794],120263:[.455,0,.517],120264:[.46,.01,.5],120265:[.455,.194,.517],120266:[.455,.194,.517],120267:[.455,0,.342],120268:[.46,.01,.383],120269:[.571,.01,.361],120270:[.444,.01,.517],120271:[.444,0,.461],120272:[.444,0,.683],120273:[.444,0,.461],120274:[.444,.204,.461],120275:[.444,0,.435],120276:[.694,0,.733],120277:[.694,0,.733],120278:[.704,.011,.703],120279:[.694,0,.794],120280:[.691,0,.642],120281:[.691,0,.611],120282:[.705,.011,.733],120283:[.694,0,.794],120284:[.694,0,.331],120285:[.694,.022,.519],120286:[.694,0,.764],120287:[.694,0,.581],120288:[.694,0,.978],120289:[.694,0,.794],120290:[.716,.022,.794],120291:[.694,0,.703],120292:[.716,.106,.794],120293:[.694,0,.703],120294:[.716,.022,.611],120295:[.688,0,.733],120296:[.694,.022,.764],120297:[.694,0,.733],120298:[.694,0,1.039],120299:[.694,0,.733],120300:[.694,0,.733],120301:[.694,0,.672],120302:[.475,.011,.525],120303:[.694,.01,.561],120304:[.475,.011,.489],120305:[.694,.011,.561],120306:[.474,.01,.511],120307:[.705,0,.336,{ic:.045}],120308:[.469,.206,.55],120309:[.694,0,.561],120310:[.695,0,.256],120311:[.695,.205,.286],120312:[.694,0,.531],120313:[.694,0,.256],120314:[.469,0,.867],120315:[.468,0,.561],120316:[.474,.011,.55],120317:[.469,.194,.561],120318:[.469,.194,.561],120319:[.469,0,.372],120320:[.474,.01,.422],120321:[.589,.01,.404],120322:[.458,.011,.561],120323:[.458,0,.5],120324:[.458,0,.744],120325:[.458,0,.5],120326:[.458,.205,.5],120327:[.458,0,.476],120328:[.694,0,.667],120329:[.694,0,.667,{ic:.029}],120330:[.705,.01,.639,{ic:.08}],120331:[.694,0,.722,{ic:.025}],120332:[.691,0,.597,{ic:.091}],120333:[.691,0,.569,{ic:.104}],120334:[.705,.011,.667,{ic:.063}],120335:[.694,0,.708,{ic:.06}],120336:[.694,0,.278,{ic:.06}],120337:[.694,.022,.472,{ic:.063}],120338:[.694,0,.694,{ic:.091}],120339:[.694,0,.542],120340:[.694,0,.875,{ic:.054}],120341:[.694,0,.708,{ic:.058}],120342:[.716,.022,.736,{ic:.027}],120343:[.694,0,.639,{ic:.051}],120344:[.716,.125,.736,{ic:.027}],120345:[.694,0,.646,{ic:.052}],120346:[.716,.022,.556,{ic:.053}],120347:[.688,0,.681,{ic:.109}],120348:[.694,.022,.688,{ic:.059}],120349:[.694,0,.667,{ic:.132}],120350:[.694,0,.944,{ic:.132}],120351:[.694,0,.667,{ic:.091}],120352:[.694,0,.667,{ic:.143}],120353:[.694,0,.611,{ic:.091}],120354:[.461,.01,.481],120355:[.694,.011,.517,{ic:.022}],120356:[.46,.011,.444,{ic:.055}],120357:[.694,.01,.517,{ic:.071}],120358:[.46,.011,.444,{ic:.028}],120359:[.705,0,.306,{ic:.188}],120360:[.455,.206,.5,{ic:.068}],120361:[.694,0,.517],120362:[.68,0,.239,{ic:.076}],120363:[.68,.204,.267,{ic:.069}],120364:[.694,0,.489,{ic:.054}],120365:[.694,0,.239,{ic:.072}],120366:[.455,0,.794],120367:[.454,0,.517],120368:[.461,.011,.5,{ic:.023}],120369:[.455,.194,.517,{ic:.021}],120370:[.455,.194,.517,{ic:.021}],120371:[.455,0,.342,{ic:.082}],120372:[.461,.011,.383,{ic:.053}],120373:[.571,.011,.361,{ic:.049}],120374:[.444,.01,.517,{ic:.02}],120375:[.444,0,.461,{ic:.079}],120376:[.444,0,.683,{ic:.079}],120377:[.444,0,.461,{ic:.076}],120378:[.444,.205,.461,{ic:.079}],120379:[.444,0,.435,{ic:.059}],120432:[.623,0,.525],120433:[.611,0,.525],120434:[.622,.011,.525],120435:[.611,0,.525],120436:[.611,0,.525],120437:[.611,0,.525],120438:[.622,.011,.525],120439:[.611,0,.525],120440:[.611,0,.525],120441:[.611,.011,.525],120442:[.611,0,.525],120443:[.611,0,.525],120444:[.611,0,.525],120445:[.611,0,.525],120446:[.621,.01,.525],120447:[.611,0,.525],120448:[.621,.138,.525],120449:[.611,.011,.525],120450:[.622,.011,.525],120451:[.611,0,.525],120452:[.611,.011,.525],120453:[.611,.007,.525],120454:[.611,.007,.525],120455:[.611,0,.525],120456:[.611,0,.525],120457:[.611,0,.525],120458:[.439,.006,.525],120459:[.611,.006,.525],120460:[.44,.006,.525],120461:[.611,.006,.525],120462:[.44,.006,.525],120463:[.617,0,.525],120464:[.442,.229,.525],120465:[.611,0,.525],120466:[.612,0,.525],120467:[.612,.228,.525],120468:[.611,0,.525],120469:[.611,0,.525],120470:[.436,0,.525,{ic:.011}],120471:[.436,0,.525],120472:[.44,.006,.525],120473:[.437,.221,.525],120474:[.437,.221,.525,{ic:.02}],120475:[.437,0,.525],120476:[.44,.006,.525],120477:[.554,.006,.525],120478:[.431,.005,.525],120479:[.431,0,.525],120480:[.431,0,.525],120481:[.431,0,.525],120482:[.431,.228,.525],120483:[.431,0,.525],120488:[.698,0,.869],120489:[.686,0,.818],120490:[.68,0,.692],120491:[.698,0,.958],120492:[.68,0,.756],120493:[.686,0,.703],120494:[.686,0,.9],120495:[.696,.01,.894],120496:[.686,0,.436],120497:[.686,0,.901],120498:[.698,0,.806],120499:[.686,0,1.092],120500:[.686,0,.9],120501:[.675,0,.767],120502:[.696,.01,.864],120503:[.68,0,.9],120504:[.686,0,.786],120506:[.686,0,.831],120507:[.675,0,.8],120508:[.697,0,.894],120509:[.686,0,.831],120510:[.686,0,.869],120511:[.686,0,.894],120512:[.696,0,.831],120513:[.686,.024,.958],120546:[.716,0,.75,{sk:.139}],120547:[.683,0,.759,{sk:.0833}],120548:[.68,0,.615,{ic:.106,sk:.0833}],120549:[.716,0,.833,{sk:.167}],120550:[.68,0,.738,{ic:.026,sk:.0833}],120551:[.683,0,.683,{ic:.04,sk:.0833}],120552:[.683,0,.831,{ic:.057,sk:.0556}],120553:[.704,.022,.763,{sk:.0833}],120554:[.683,0,.44,{ic:.064,sk:.111}],120555:[.683,0,.849,{ic:.04,sk:.0556}],120556:[.716,0,.694,{sk:.167}],120557:[.683,0,.97,{ic:.081,sk:.0833}],120558:[.683,0,.803,{ic:.085,sk:.0833}],120559:[.677,0,.742,{ic:.035,sk:.0833}],120560:[.704,.022,.763,{sk:.0833}],120561:[.68,0,.831,{ic:.056,sk:.0556}],120562:[.683,0,.642,{ic:.109,sk:.0833}],120564:[.683,0,.78,{ic:.026,sk:.0833}],120565:[.677,0,.584,{ic:.12,sk:.0833}],120566:[.705,0,.583,{ic:.117,sk:.0556}],120567:[.683,0,.667,{sk:.0833}],120568:[.683,0,.828,{ic:.024,sk:.0833}],120569:[.683,0,.612,{ic:.08,sk:.0556}],120570:[.704,0,.772,{ic:.014,sk:.0833}],120572:[.442,.011,.64,{sk:.0278}],120573:[.705,.194,.566,{sk:.0833}],120574:[.441,.216,.518,{ic:.025}],120575:[.717,.01,.444,{sk:.0556}],120576:[.452,.022,.466,{sk:.0833}],120577:[.704,.204,.438,{ic:.033,sk:.0833}],120578:[.442,.216,.497,{sk:.0556}],120579:[.705,.01,.469,{sk:.0833}],120580:[.442,.01,.354,{sk:.0556}],120581:[.442,.011,.576],120582:[.694,.012,.583],120583:[.442,.216,.603,{sk:.0278}],120584:[.442,0,.494,{ic:.036,sk:.0278}],120585:[.704,.205,.438,{sk:.111}],120586:[.441,.011,.485,{sk:.0556}],120587:[.431,.011,.57],120588:[.442,.216,.517,{sk:.0833}],120589:[.442,.107,.363,{ic:.042,sk:.0833}],120590:[.431,.011,.571],120591:[.431,.013,.437,{ic:.08,sk:.0278}],120592:[.443,.01,.54,{sk:.0278}],120593:[.442,.218,.654,{sk:.0833}],120594:[.442,.204,.626,{sk:.0556}],120595:[.694,.205,.651,{sk:.111}],120596:[.443,.011,.622],120597:[.715,.022,.531,{ic:.035,sk:.0833}],120598:[.431,.011,.406,{sk:.0556}],120599:[.705,.011,.591,{sk:.0833}],120600:[.434,.006,.667,{ic:.067}],120601:[.694,.205,.596,{sk:.0833}],120602:[.442,.194,.517,{sk:.0833}],120603:[.431,.01,.828],120604:[.711,0,.869,{sk:.16}],120605:[.686,0,.866,{sk:.0958}],120606:[.68,0,.657,{ic:.12,sk:.0958}],120607:[.711,0,.958,{sk:.192}],120608:[.68,0,.81,{ic:.015,sk:.0958}],120609:[.686,0,.773,{ic:.032,sk:.0958}],120610:[.686,0,.982,{ic:.045,sk:.0639}],120611:[.702,.017,.867,{sk:.0958}],120612:[.686,0,.511,{ic:.062,sk:.128}],120613:[.686,0,.971,{ic:.032,sk:.0639}],120614:[.711,0,.806,{sk:.192}],120615:[.686,0,1.142,{ic:.077,sk:.0958}],120616:[.686,0,.95,{ic:.077,sk:.0958}],120617:[.675,0,.841,{ic:.026,sk:.0958}],120618:[.703,.017,.837,{sk:.0958}],120619:[.68,0,.982,{ic:.044,sk:.0639}],120620:[.686,0,.723,{ic:.124,sk:.0958}],120622:[.686,0,.885,{ic:.017,sk:.0958}],120623:[.675,0,.637,{ic:.135,sk:.0958}],120624:[.703,0,.671,{ic:.131,sk:.0639}],120625:[.686,0,.767,{sk:.0958}],120626:[.686,0,.947,{sk:.0958}],120627:[.686,0,.714,{ic:.076,sk:.0639}],120628:[.703,0,.879,{sk:.0958}],120630:[.452,.008,.761,{sk:.0319}],120631:[.701,.194,.66,{sk:.0958}],120632:[.451,.211,.59,{ic:.027}],120633:[.725,.008,.522,{sk:.0639}],120634:[.461,.017,.529,{sk:.0958}],120635:[.711,.202,.508,{ic:.013,sk:.0958}],120636:[.452,.211,.6,{sk:.0639}],120637:[.702,.008,.562,{sk:.0958}],120638:[.452,.008,.412,{sk:.0639}],120639:[.452,.008,.668],120640:[.694,.013,.671],120641:[.452,.211,.708,{sk:.0319}],120642:[.452,0,.577,{ic:.031,sk:.0319}],120643:[.711,.201,.508,{sk:.128}],120644:[.452,.008,.585,{sk:.0639}],120645:[.444,.008,.682],120646:[.451,.211,.612,{sk:.0958}],120647:[.451,.105,.424,{ic:.033,sk:.0958}],120648:[.444,.008,.686],120649:[.444,.013,.521,{ic:.089,sk:.0319}],120650:[.453,.008,.631,{sk:.0319}],120651:[.452,.216,.747,{sk:.0958}],120652:[.452,.201,.718,{sk:.0639}],120653:[.694,.202,.758,{sk:.128}],120654:[.453,.008,.718],120655:[.71,.017,.628,{ic:.029,sk:.0958}],120656:[.444,.007,.483,{sk:.0639}],120657:[.701,.008,.692,{sk:.0958}],120658:[.434,.006,.667,{ic:.067}],120659:[.694,.202,.712,{sk:.0958}],120660:[.451,.194,.612,{sk:.0958}],120661:[.444,.008,.975],120662:[.694,0,.733],120663:[.694,0,.733],120664:[.691,0,.581],120665:[.694,0,.917],120666:[.691,0,.642],120667:[.694,0,.672],120668:[.694,0,.794],120669:[.716,.022,.856],120670:[.694,0,.331],120671:[.694,0,.764],120672:[.694,0,.672],120673:[.694,0,.978],120674:[.694,0,.794],120675:[.688,0,.733],120676:[.716,.022,.794],120677:[.691,0,.794],120678:[.694,0,.703],120680:[.694,0,.794],120681:[.688,0,.733],120682:[.715,0,.856],120683:[.694,0,.794],120684:[.694,0,.733],120685:[.694,0,.856],120686:[.716,0,.794],120782:[.654,.01,.575],120783:[.655,0,.575],120784:[.654,0,.575],120785:[.655,.011,.575],120786:[.656,0,.575],120787:[.655,.011,.575],120788:[.655,.011,.575],120789:[.676,.011,.575],120790:[.654,.011,.575],120791:[.654,.011,.575],120802:[.678,.022,.5],120803:[.678,0,.5],120804:[.677,0,.5],120805:[.678,.022,.5],120806:[.656,0,.5],120807:[.656,.021,.5],120808:[.677,.022,.5],120809:[.656,.011,.5],120810:[.678,.022,.5],120811:[.677,.022,.5],120812:[.715,.022,.55],120813:[.716,0,.55],120814:[.716,0,.55],120815:[.716,.022,.55],120816:[.694,0,.55],120817:[.694,.022,.55],120818:[.716,.022,.55],120819:[.695,.011,.55],120820:[.715,.022,.55],120821:[.716,.022,.55],120822:[.621,.01,.525],120823:[.622,0,.525],120824:[.622,0,.525],120825:[.622,.011,.525],120826:[.624,0,.525],120827:[.611,.01,.525],120828:[.622,.011,.525],120829:[.627,.01,.525],120830:[.621,.01,.525],120831:[.622,.011,.525]}},6949:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBoldItalic=void 0,f.sansSerifBoldItalic={305:[.458,0,.256],567:[.458,.205,.286]}},5193:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifBold=void 0,f.sansSerifBold={33:[.694,0,.367],34:[.694,-.442,.558],35:[.694,.193,.917],36:[.75,.056,.55],37:[.75,.056,1.029],38:[.716,.022,.831],39:[.694,-.442,.306],40:[.75,.249,.428],41:[.75,.25,.428],42:[.75,-.293,.55],43:[.617,.116,.856],44:[.146,.106,.306],45:[.273,-.186,.367],46:[.146,0,.306],47:[.75,.249,.55],58:[.458,0,.306],59:[.458,.106,.306],61:[.407,-.094,.856],63:[.705,0,.519],64:[.704,.011,.733],91:[.75,.25,.343],93:[.75,.25,.343],94:[.694,-.537,.55],95:[-.023,.11,.55],126:[.344,-.198,.55],305:[.458,0,.256],567:[.458,.205,.286],768:[.694,-.537,0],769:[.694,-.537,0],770:[.694,-.537,0],771:[.694,-.548,0],772:[.66,-.56,0],774:[.694,-.552,0],775:[.695,-.596,0],776:[.695,-.595,0],778:[.694,-.538,0],779:[.694,-.537,0],780:[.657,-.5,0],8211:[.327,-.24,.55],8212:[.327,-.24,1.1],8213:[.327,-.24,1.1],8215:[-.023,.11,.55],8216:[.694,-.443,.306],8217:[.694,-.442,.306],8220:[.694,-.443,.558],8221:[.694,-.442,.558],8260:[.75,.249,.55],8710:[.694,0,.917]}},2632:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerifItalic=void 0,f.sansSerifItalic={33:[.694,0,.319,{ic:.036}],34:[.694,-.471,.5],35:[.694,.194,.833,{ic:.018}],36:[.75,.056,.5,{ic:.065}],37:[.75,.056,.833],38:[.716,.022,.758],39:[.694,-.471,.278,{ic:.057}],40:[.75,.25,.389,{ic:.102}],41:[.75,.25,.389],42:[.75,-.306,.5,{ic:.068}],43:[.583,.083,.778],44:[.098,.125,.278],45:[.259,-.186,.333],46:[.098,0,.278],47:[.75,.25,.5,{ic:.1}],48:[.678,.022,.5,{ic:.049}],49:[.678,0,.5],50:[.678,0,.5,{ic:.051}],51:[.678,.022,.5,{ic:.044}],52:[.656,0,.5,{ic:.021}],53:[.656,.022,.5,{ic:.055}],54:[.678,.022,.5,{ic:.048}],55:[.656,.011,.5,{ic:.096}],56:[.678,.022,.5,{ic:.054}],57:[.677,.022,.5,{ic:.045}],58:[.444,0,.278],59:[.444,.125,.278],61:[.37,-.13,.778,{ic:.018}],63:[.704,0,.472,{ic:.064}],64:[.705,.01,.667,{ic:.04}],91:[.75,.25,.289,{ic:.136}],93:[.75,.25,.289,{ic:.064}],94:[.694,-.527,.5,{ic:.033}],95:[-.038,.114,.5,{ic:.065}],126:[.327,-.193,.5,{ic:.06}],305:[.444,0,.239,{ic:.019}],567:[.444,.204,.267,{ic:.019}],768:[.694,-.527,0],769:[.694,-.527,0,{ic:.063}],770:[.694,-.527,0,{ic:.033}],771:[.677,-.543,0,{ic:.06}],772:[.631,-.552,0,{ic:.064}],774:[.694,-.508,0,{ic:.073}],775:[.68,-.576,0],776:[.68,-.582,0,{ic:.04}],778:[.693,-.527,0],779:[.694,-.527,0,{ic:.063}],780:[.654,-.487,0,{ic:.06}],913:[.694,0,.667],914:[.694,0,.667,{ic:.029}],915:[.691,0,.542,{ic:.104}],916:[.694,0,.833],917:[.691,0,.597,{ic:.091}],918:[.694,0,.611,{ic:.091}],919:[.694,0,.708,{ic:.06}],920:[.715,.022,.778,{ic:.026}],921:[.694,0,.278,{ic:.06}],922:[.694,0,.694,{ic:.091}],923:[.694,0,.611],924:[.694,0,.875,{ic:.054}],925:[.694,0,.708,{ic:.058}],926:[.688,0,.667,{ic:.098}],927:[.716,.022,.736,{ic:.027}],928:[.691,0,.708,{ic:.06}],929:[.694,0,.639,{ic:.051}],931:[.694,0,.722,{ic:.091}],932:[.688,0,.681,{ic:.109}],933:[.716,0,.778,{ic:.065}],934:[.694,0,.722,{ic:.021}],935:[.694,0,.667,{ic:.091}],936:[.694,0,.778,{ic:.076}],937:[.716,0,.722,{ic:.047}],8211:[.312,-.236,.5,{ic:.065}],8212:[.312,-.236,1,{ic:.065}],8213:[.312,-.236,1,{ic:.065}],8215:[-.038,.114,.5,{ic:.065}],8216:[.694,-.471,.278,{ic:.058}],8217:[.694,-.471,.278,{ic:.057}],8220:[.694,-.471,.5,{ic:.114}],8221:[.694,-.471,.5],8260:[.75,.25,.5,{ic:.1}],8710:[.694,0,.833]}},4214:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.sansSerif=void 0,f.sansSerif={33:[.694,0,.319],34:[.694,-.471,.5],35:[.694,.194,.833],36:[.75,.056,.5],37:[.75,.056,.833],38:[.716,.022,.758],39:[.694,-.471,.278],40:[.75,.25,.389],41:[.75,.25,.389],42:[.75,-.306,.5],43:[.583,.082,.778],44:[.098,.125,.278],45:[.259,-.186,.333],46:[.098,0,.278],47:[.75,.25,.5],58:[.444,0,.278],59:[.444,.125,.278],61:[.37,-.13,.778],63:[.704,0,.472],64:[.704,.011,.667],91:[.75,.25,.289],93:[.75,.25,.289],94:[.694,-.527,.5],95:[-.038,.114,.5],126:[.327,-.193,.5],305:[.444,0,.239],567:[.444,.205,.267],768:[.694,-.527,0],769:[.694,-.527,0],770:[.694,-.527,0],771:[.677,-.543,0],772:[.631,-.552,0],774:[.694,-.508,0],775:[.68,-.576,0],776:[.68,-.582,0],778:[.694,-.527,0],779:[.694,-.527,0],780:[.654,-.487,0],913:[.694,0,.667],914:[.694,0,.667],915:[.691,0,.542],916:[.694,0,.833],917:[.691,0,.597],918:[.694,0,.611],919:[.694,0,.708],920:[.716,.021,.778],921:[.694,0,.278],922:[.694,0,.694],923:[.694,0,.611],924:[.694,0,.875],925:[.694,0,.708],926:[.688,0,.667],927:[.715,.022,.736],928:[.691,0,.708],929:[.694,0,.639],931:[.694,0,.722],932:[.688,0,.681],933:[.716,0,.778],934:[.694,0,.722],935:[.694,0,.667],936:[.694,0,.778],937:[.716,0,.722],8211:[.312,-.236,.5],8212:[.312,-.236,1],8213:[.312,-.236,1],8215:[-.038,.114,.5],8216:[.694,-.471,.278],8217:[.694,-.471,.278],8220:[.694,-.471,.5],8221:[.694,-.471,.5],8260:[.75,.25,.5],8710:[.694,0,.833]}},6466:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.scriptBold=void 0,f.scriptBold={}},3776:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.script=void 0,f.script={}},7405:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.smallop=void 0,f.smallop={40:[.85,.349,.458],41:[.85,.349,.458],47:[.85,.349,.578],91:[.85,.349,.417],92:[.85,.349,.578],93:[.85,.349,.417],123:[.85,.349,.583],125:[.85,.349,.583],710:[.744,-.551,.556],732:[.722,-.597,.556],770:[.744,-.551,0],771:[.722,-.597,0],8214:[.602,0,.778],8260:[.85,.349,.578],8593:[.6,0,.667],8595:[.6,0,.667],8657:[.599,0,.778],8659:[.6,0,.778],8719:[.75,.25,.944],8720:[.75,.25,.944],8721:[.75,.25,1.056],8730:[.85,.35,1,{ic:.02}],8739:[.627,.015,.333],8741:[.627,.015,.556],8747:[.805,.306,.472,{ic:.138}],8748:[.805,.306,.819,{ic:.138}],8749:[.805,.306,1.166,{ic:.138}],8750:[.805,.306,.472,{ic:.138}],8896:[.75,.249,.833],8897:[.75,.249,.833],8898:[.75,.249,.833],8899:[.75,.249,.833],8968:[.85,.349,.472],8969:[.85,.349,.472],8970:[.85,.349,.472],8971:[.85,.349,.472],9001:[.85,.35,.472],9002:[.85,.35,.472],9168:[.602,0,.667],10072:[.627,.015,.333],10216:[.85,.35,.472],10217:[.85,.35,.472],10752:[.75,.25,1.111],10753:[.75,.25,1.111],10754:[.75,.25,1.111],10756:[.75,.249,.833],10758:[.75,.249,.833],10764:[.805,.306,1.638,{ic:.138}],12296:[.85,.35,.472],12297:[.85,.35,.472]}},8105:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphicBold=void 0,f.texCalligraphicBold={65:[.751,.049,.921,{ic:.068,sk:.224}],66:[.705,.017,.748,{sk:.16}],67:[.703,.02,.613,{sk:.16}],68:[.686,0,.892,{sk:.0958}],69:[.703,.016,.607,{ic:.02,sk:.128}],70:[.686,.03,.814,{ic:.116,sk:.128}],71:[.703,.113,.682,{sk:.128}],72:[.686,.048,.987,{sk:.128}],73:[.686,0,.642,{ic:.104,sk:.0319}],74:[.686,.114,.779,{ic:.158,sk:.192}],75:[.703,.017,.871,{sk:.0639}],76:[.703,.017,.788,{sk:.16}],77:[.703,.049,1.378,{sk:.16}],78:[.84,.049,.937,{ic:.168,sk:.0958}],79:[.703,.017,.906,{sk:.128}],80:[.686,.067,.81,{ic:.036,sk:.0958}],81:[.703,.146,.939,{sk:.128}],82:[.686,.017,.99,{sk:.0958}],83:[.703,.016,.696,{ic:.025,sk:.16}],84:[.72,.069,.644,{ic:.303,sk:.0319}],85:[.686,.024,.715,{ic:.056,sk:.0958}],86:[.686,.077,.737,{ic:.037,sk:.0319}],87:[.686,.077,1.169,{ic:.037,sk:.0958}],88:[.686,0,.817,{ic:.089,sk:.16}],89:[.686,.164,.759,{ic:.038,sk:.0958}],90:[.686,0,.818,{ic:.035,sk:.16}],305:[.452,.008,.394,{sk:.0319}],567:[.451,.201,.439,{sk:.0958}]}},2518:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texCalligraphic=void 0,f.texCalligraphic={65:[.728,.05,.798,{ic:.021,sk:.194}],66:[.705,.022,.657,{sk:.139}],67:[.705,.025,.527,{sk:.139}],68:[.683,0,.771,{sk:.0833}],69:[.705,.022,.528,{ic:.036,sk:.111}],70:[.683,.032,.719,{ic:.11,sk:.111}],71:[.704,.119,.595,{sk:.111}],72:[.683,.048,.845,{sk:.111}],73:[.683,0,.545,{ic:.097,sk:.0278}],74:[.683,.119,.678,{ic:.161,sk:.167}],75:[.705,.022,.762,{sk:.0556}],76:[.705,.022,.69,{sk:.139}],77:[.705,.05,1.201,{sk:.139}],78:[.789,.05,.82,{ic:.159,sk:.0833}],79:[.705,.022,.796,{sk:.111}],80:[.683,.057,.696,{ic:.037,sk:.0833}],81:[.705,.131,.817,{sk:.111}],82:[.682,.022,.848,{sk:.0833}],83:[.705,.022,.606,{ic:.036,sk:.139}],84:[.717,.068,.545,{ic:.288,sk:.0278}],85:[.683,.028,.626,{ic:.061,sk:.0833}],86:[.683,.052,.613,{ic:.045,sk:.0278}],87:[.683,.053,.988,{ic:.046,sk:.0833}],88:[.683,0,.713,{ic:.094,sk:.139}],89:[.683,.143,.668,{ic:.046,sk:.0833}],90:[.683,0,.725,{ic:.042,sk:.139}]}},5595:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texMathit=void 0,f.texMathit={65:[.716,0,.743],66:[.683,0,.704],67:[.705,.021,.716],68:[.683,0,.755],69:[.68,0,.678],70:[.68,0,.653],71:[.705,.022,.774],72:[.683,0,.743],73:[.683,0,.386],74:[.683,.021,.525],75:[.683,0,.769],76:[.683,0,.627],77:[.683,0,.897],78:[.683,0,.743],79:[.704,.022,.767],80:[.683,0,.678],81:[.704,.194,.767],82:[.683,.022,.729],83:[.705,.022,.562],84:[.677,0,.716],85:[.683,.022,.743],86:[.683,.022,.743],87:[.683,.022,.999],88:[.683,0,.743],89:[.683,0,.743],90:[.683,0,.613],97:[.442,.011,.511],98:[.694,.011,.46],99:[.441,.01,.46],100:[.694,.011,.511],101:[.442,.01,.46],102:[.705,.204,.307],103:[.442,.205,.46],104:[.694,.011,.511],105:[.656,.01,.307],106:[.656,.204,.307],107:[.694,.011,.46],108:[.694,.011,.256],109:[.442,.011,.818],110:[.442,.011,.562],111:[.442,.011,.511],112:[.442,.194,.511],113:[.442,.194,.46],114:[.442,.011,.422],115:[.442,.011,.409],116:[.626,.011,.332],117:[.441,.011,.537],118:[.443,.01,.46],119:[.443,.011,.664],120:[.442,.011,.464],121:[.441,.205,.486],122:[.442,.011,.409]}},6357:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyleBold=void 0,f.texOldstyleBold={48:[.46,.017,.575],49:[.461,0,.575],50:[.46,0,.575],51:[.461,.211,.575],52:[.469,.194,.575],53:[.461,.211,.575],54:[.66,.017,.575],55:[.476,.211,.575],56:[.661,.017,.575],57:[.461,.21,.575],65:[.751,.049,.921,{ic:.068,sk:.224}],66:[.705,.017,.748,{sk:.16}],67:[.703,.02,.613,{sk:.16}],68:[.686,0,.892,{sk:.0958}],69:[.703,.016,.607,{ic:.02,sk:.128}],70:[.686,.03,.814,{ic:.116,sk:.128}],71:[.703,.113,.682,{sk:.128}],72:[.686,.048,.987,{sk:.128}],73:[.686,0,.642,{ic:.104,sk:.0319}],74:[.686,.114,.779,{ic:.158,sk:.192}],75:[.703,.017,.871,{sk:.0639}],76:[.703,.017,.788,{sk:.16}],77:[.703,.049,1.378,{sk:.16}],78:[.84,.049,.937,{ic:.168,sk:.0958}],79:[.703,.017,.906,{sk:.128}],80:[.686,.067,.81,{ic:.036,sk:.0958}],81:[.703,.146,.939,{sk:.128}],82:[.686,.017,.99,{sk:.0958}],83:[.703,.016,.696,{ic:.025,sk:.16}],84:[.72,.069,.644,{ic:.303,sk:.0319}],85:[.686,.024,.715,{ic:.056,sk:.0958}],86:[.686,.077,.737,{ic:.037,sk:.0319}],87:[.686,.077,1.169,{ic:.037,sk:.0958}],88:[.686,0,.817,{ic:.089,sk:.16}],89:[.686,.164,.759,{ic:.038,sk:.0958}],90:[.686,0,.818,{ic:.035,sk:.16}]}},9474:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texOldstyle=void 0,f.texOldstyle={48:[.452,.022,.5],49:[.453,0,.5],50:[.453,0,.5],51:[.452,.216,.5],52:[.464,.194,.5],53:[.453,.216,.5],54:[.665,.022,.5],55:[.463,.216,.5],56:[.666,.021,.5],57:[.453,.216,.5],65:[.728,.05,.798,{ic:.021,sk:.194}],66:[.705,.022,.657,{sk:.139}],67:[.705,.025,.527,{sk:.139}],68:[.683,0,.771,{sk:.0833}],69:[.705,.022,.528,{ic:.036,sk:.111}],70:[.683,.032,.719,{ic:.11,sk:.111}],71:[.704,.119,.595,{sk:.111}],72:[.683,.048,.845,{sk:.111}],73:[.683,0,.545,{ic:.097,sk:.0278}],74:[.683,.119,.678,{ic:.161,sk:.167}],75:[.705,.022,.762,{sk:.0556}],76:[.705,.022,.69,{sk:.139}],77:[.705,.05,1.201,{sk:.139}],78:[.789,.05,.82,{ic:.159,sk:.0833}],79:[.705,.022,.796,{sk:.111}],80:[.683,.057,.696,{ic:.037,sk:.0833}],81:[.705,.131,.817,{sk:.111}],82:[.682,.022,.848,{sk:.0833}],83:[.705,.022,.606,{ic:.036,sk:.139}],84:[.717,.068,.545,{ic:.288,sk:.0278}],85:[.683,.028,.626,{ic:.061,sk:.0833}],86:[.683,.052,.613,{ic:.045,sk:.0278}],87:[.683,.053,.988,{ic:.046,sk:.0833}],88:[.683,0,.713,{ic:.094,sk:.139}],89:[.683,.143,.668,{ic:.046,sk:.0833}],90:[.683,0,.725,{ic:.042,sk:.139}]}},584:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texSize3=void 0,f.texSize3={40:[1.45,.949,.736],41:[1.45,.949,.736],47:[1.45,.949,1.044],91:[1.45,.949,.528],92:[1.45,.949,1.044],93:[1.45,.949,.528],123:[1.45,.949,.75],125:[1.45,.949,.75],710:[.772,-.564,1.444],732:[.749,-.61,1.444],770:[.772,-.564,0],771:[.749,-.61,0],8260:[1.45,.949,1.044],8730:[1.45,.95,1,{ic:.02}],8968:[1.45,.949,.583],8969:[1.45,.949,.583],8970:[1.45,.949,.583],8971:[1.45,.949,.583],9001:[1.45,.95,.75],9002:[1.45,.949,.75],10216:[1.45,.95,.75],10217:[1.45,.949,.75],12296:[1.45,.95,.75],12297:[1.45,.949,.75]}},4324:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texSize4=void 0,f.texSize4={40:[1.75,1.249,.792],41:[1.75,1.249,.792],47:[1.75,1.249,1.278],91:[1.75,1.249,.583],92:[1.75,1.249,1.278],93:[1.75,1.249,.583],123:[1.75,1.249,.806],125:[1.75,1.249,.806],710:[.845,-.561,1.889,{ic:.013}],732:[.823,-.583,1.889],770:[.845,-.561,0,{ic:.013}],771:[.823,-.583,0],8260:[1.75,1.249,1.278],8730:[1.75,1.25,1,{ic:.02}],8968:[1.75,1.249,.639],8969:[1.75,1.249,.639],8970:[1.75,1.249,.639],8971:[1.75,1.249,.639],9001:[1.75,1.248,.806],9002:[1.75,1.248,.806],9115:[1.154,.655,.875],9116:[.61,.01,.875],9117:[1.165,.644,.875],9118:[1.154,.655,.875],9119:[.61,.01,.875],9120:[1.165,.644,.875],9121:[1.154,.645,.667],9122:[.602,0,.667],9123:[1.155,.644,.667],9124:[1.154,.645,.667],9125:[.602,0,.667],9126:[1.155,.644,.667],9127:[.899,.01,.889],9128:[1.16,.66,.889],9129:[.01,.899,.889],9130:[.29,.015,.889],9131:[.899,.01,.889],9132:[1.16,.66,.889],9133:[.01,.899,.889],9143:[.935,.885,1.056],10216:[1.75,1.248,.806],10217:[1.75,1.248,.806],12296:[1.75,1.248,.806],12297:[1.75,1.248,.806],57344:[.625,.014,1.056],57345:[.605,.014,1.056,{ic:.02}],57680:[.12,.213,.45,{ic:.01}],57681:[.12,.213,.45,{ic:.024}],57682:[.333,0,.45,{ic:.01}],57683:[.333,0,.45,{ic:.024}],57684:[.32,.2,.4,{ic:.01}],57685:[.333,0,.9,{ic:.01}],57686:[.12,.213,.9,{ic:.01}]}},8135:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.texVariant=void 0,f.texVariant={710:[.845,-.561,2.333,{ic:.013}],732:[.899,-.628,2.333],770:[.845,-.561,0,{ic:.013}],771:[.899,-.628,0],1008:[.434,.006,.667,{ic:.067}],8463:[.695,.013,.54,{ic:.022}],8592:[.437,-.064,.5],8594:[.437,-.064,.5],8652:[.514,.014,1],8708:[.86,.166,.556],8709:[.587,0,.778],8722:[.27,-.23,.5],8726:[.43,.023,.778],8733:[.472,-.028,.778],8739:[.43,.023,.222],8740:[.43,.023,.222,{ic:.018}],8741:[.431,.023,.389],8742:[.431,.024,.389,{ic:.018}],8764:[.365,-.132,.778],8776:[.481,-.05,.778],8808:[.752,.284,.778],8809:[.752,.284,.778],8816:[.919,.421,.778],8817:[.919,.421,.778],8840:[.828,.33,.778],8841:[.828,.33,.778],8842:[.634,.255,.778],8843:[.634,.254,.778],8872:[.694,0,.611],8901:[.189,0,.278],8994:[.378,-.122,.778],8995:[.378,-.143,.778],9651:[.575,.02,.722],9661:[.576,.019,.722],10887:[.801,.303,.778],10888:[.801,.303,.778],10955:[.752,.332,.778],10956:[.752,.333,.778]}},8955:function(c,f){MathJax._.components.global.isObject,MathJax._.components.global.combineConfig,f.PV=MathJax._.components.global.combineDefaults,f.r8=MathJax._.components.global.combineWithMathJax,MathJax._.components.global.MathJax},5074:function(c,f){MathJax._.util.Options.isObject,MathJax._.util.Options.APPEND,MathJax._.util.Options.REMOVE,MathJax._.util.Options.OPTIONS,MathJax._.util.Options.Expandable,MathJax._.util.Options.expandable,MathJax._.util.Options.makeArray,MathJax._.util.Options.keys,MathJax._.util.Options.copy,MathJax._.util.Options.insert,MathJax._.util.Options.defaultOptions,MathJax._.util.Options.userOptions,MathJax._.util.Options.selectOptions,f.Pp=MathJax._.util.Options.selectOptionsFromKeys,MathJax._.util.Options.separateOptions,MathJax._.util.Options.lookup},73:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.CHTMLFontData=MathJax._.output.chtml.FontData.CHTMLFontData,f.AddCSS=MathJax._.output.chtml.FontData.AddCSS},4228:function(c,f){Object.defineProperty(f,"__esModule",{value:!0}),f.enum=MathJax._.output.common.FontData.enum,f.V=MathJax._.output.common.FontData.V,f.H=MathJax._.output.common.FontData.H,f.NOSTRETCH=MathJax._.output.common.FontData.NOSTRETCH,f.FontData=MathJax._.output.common.FontData.FontData},4629:function(c,f){MathJax._.components.package.PackageError,f.GL=MathJax._.components.package.Package}},f={};function i(t){var e=f[t];if(void 0!==e)return e.exports;var s=f[t]={exports:{}};return c[t].call(s.exports,s,s.exports,i),s.exports}!function(){var c=i(8955),f=i(8667),t=i(2308),e=i(7841),s=i(5031),r=i(9236),o=i(1937),a=i(4244),S=i(482),l=i(196),n=i(527),B=i(3518),d=i(965),u=i(9169),k=i(6736),I=i(8787),A=i(5392),p=i(6590),m=i(8798),h=i(2138),M=i(3869),x=i(7557),H=i(7481),_=i(5674),E=i(7002),y=i(9349),R=i(9741),v=i(2827),D=i(2970),b=i(1668),T=i(6949),X=i(5193),O=i(2632),F=i(4214),C=i(6466),W=i(3776),J=i(7405),j=i(8105),z=i(2518),V=i(5595),P=i(6357),g=i(9474),w=i(584),Z=i(4324),L=i(8135);MathJax.loader&&MathJax.loader.checkVersion("output/chtml/fonts/tex",f.q,"chtml-font"),(0,c.r8)({_:{output:{chtml:{fonts:{tex_ts:t,tex:{"bold-italic":e,bold:s,"fraktur-bold":r,fraktur:o,italic:a,largeop:S,monospace:l,normal:n,"sans-serif-bold-italic":B,"sans-serif-bold":d,"sans-serif-italic":u,"sans-serif":k,smallop:I,"tex-calligraphic-bold":A,"tex-size3":p,"tex-size4":m,"tex-variant":h}}},common:{fonts:{tex:{"bold-italic":M,bold:x,delimiters:H,"double-struck":_,"fraktur-bold":E,fraktur:y,italic:R,largeop:v,monospace:D,normal:b,"sans-serif-bold-italic":T,"sans-serif-bold":X,"sans-serif-italic":O,"sans-serif":F,"script-bold":C,script:W,smallop:J,"tex-calligraphic-bold":j,"tex-calligraphic":z,"tex-mathit":V,"tex-oldstyle-bold":P,"tex-oldstyle":g,"tex-size3":w,"tex-size4":Z,"tex-variant":L}}}}}});var U=i(4629),N=i(5074);if(MathJax.startup){(0,c.PV)(MathJax.config,"chtml",{fontURL:U.GL.resolvePath("output/chtml/fonts/woff-v2",!1)});var K=(0,N.Pp)(MathJax.config.chtml||{},t.TeXFont.OPTIONS);(0,c.PV)(MathJax.config,"chtml",{font:new t.TeXFont(K)})}}()}();