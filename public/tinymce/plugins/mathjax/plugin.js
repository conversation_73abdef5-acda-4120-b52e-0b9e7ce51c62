(function () {
  // eslint-disable-next-line no-undef
  const global = tinymce.util.Tools.resolve('tinymce.PluginManager');
  function Plugin() {
    global.add('mathjax', function (editor, url) {

      // plugin configuration options
      const settings = editor.getParam('mathjax');
      const mathjaxClassName = settings.className || "math-tex";
      const mathjaxTempClassName = mathjaxClassName + '-original';
      const mathjaxSymbols = settings.symbols || { start: '\\(', end: '\\)' };
      const mathjaxUrl = settings.lib || null;
      let mathjaxConfigUrl = (settings.configUrl || url + '/config.js');
      if (settings.className) {
        mathjaxConfigUrl += '?class=' + settings.className
      }
      const mathjaxScripts = [mathjaxConfigUrl];
      if (mathjaxUrl) {
        mathjaxScripts.push(mathjaxUrl);
      }

      const checkElement = function (element) {
        // console.log('checkElement', element, element.childNodes.length, element.childNodes.length !== 2);
        
        if (element.childNodes.length != 2) {
          element.setAttribute('contenteditable', false);
          element.style.cursor = 'pointer';
          const latex = element.getAttribute('data-latex') || element.innerHTML;
          element.setAttribute('data-latex', latex);
          element.innerHTML = '';

          const math = editor.dom.create('span');
          math.innerHTML = latex;
          math.classList.add(mathjaxTempClassName);
          element.appendChild(math);

          const dummy = editor.dom.create('span');
          dummy.classList.add('dummy');
          dummy.innerHTML = 'dummy';
          dummy.setAttribute('hidden', 'hidden');
          element.appendChild(dummy);
        } else {
          // 复制时因为innerHTML和data-latex 导致生成的html不完整
          const latex = element.getAttribute('data-latex') || element.innerHTML;
          element.setAttribute('data-latex', latex);
          if (element.querySelector(`.${mathjaxTempClassName}`).innerHTML !== latex) {
            element.querySelector(`.${mathjaxTempClassName}`).innerHTML = latex;
          }
        }
      };

      const openMathjaxEditor = target => {
        const getMathText = function (value, symbols) {
          if (!symbols) {
            symbols = mathjaxSymbols;
          }
          return symbols.start + ' ' + value + ' ' + symbols.end;
        };
        const mathjaxId = editor.id + '_' + editor.dom.uniqueId();

        let latex = '';
        if (target) {
          const latexAttribute = target.getAttribute('data-latex');
          if (latexAttribute.length >= (mathjaxSymbols.start + mathjaxSymbols.end).length) {
            latex = latexAttribute.substr(mathjaxSymbols.start.length, latexAttribute.length - (mathjaxSymbols.start + mathjaxSymbols.end).length);
          }
        }
        // console.log('latex......', latex);
        editor.fire('myMathjaxOpen', { data: { mathjaxId, latex } });
        const handleMathjaxInsert = (e) => {
          // console.log('myMathjaxInsert.e', e.data.latex, target);
          if (target) {
            target.innerHTML = '';
            target.setAttribute('data-latex', getMathText(e.data.latex));
            checkElement(target);
          } else {
            const newElement = editor.getDoc().createElement('span');
            newElement.innerHTML = getMathText(e.data.latex);
            newElement.classList.add(mathjaxClassName);
            checkElement(newElement);
            const newWrapperElement = editor.getDoc().createElement('span');
            newWrapperElement.classList.add(`${mathjaxClassName}-wrapper`);
            newWrapperElement.setAttribute('style', 'position:relative');
            newWrapperElement.innerHTML = `${newElement.outerHTML}`;
            editor.selection.setContent(`&nbsp;${newWrapperElement.outerHTML}&nbsp;`);
          }
          editor.getDoc().defaultView.MathJax.typesetPromise();
          // console.log('myMathjaxOpen.typesetPromise');
          editor.off('myMathjaxInsert', handleMathjaxInsert);
        }
        const handleMathjaxInsertClose = (e) => {
          // console.log('handleMathjaxInsertClose.e', e);
          editor.off('myMathjaxInsert', handleMathjaxInsert);
          editor.off('myMathjaxInsertClose', handleMathjaxInsertClose);
        }
        editor.on('myMathjaxInsert', handleMathjaxInsert);
        editor.on('myMathjaxInsertClose', handleMathjaxInsertClose);
      };
      editor.on('mousedown', function () {
        // 公式前后添加空格解决不能选中的问题
        const selectionNode = editor.selection.getNode();
        // const closest = selectionNode.closest('.' + mathjaxClassName + 'wrapper') || selectionNode.closest('.' + mathjaxClassName);
        // console.log('mousedown...e', closest);
        // console.log('selection.getNode()', selectionNode);
        const getRng = editor.selection.getRng();
        // console.log('selection.getRng.startContainer.nextElementSibling', getRng.startContainer.nextElementSibling);
        // console.log('selection.getRng.endContainer.previousElementSibling', getRng.endContainer.previousElementSibling);
        const closestWrapper = [selectionNode, getRng.startContainer.nextElementSibling, getRng.endContainer.previousElementSibling]
  .find(item => item && item.closest && (item.closest('.' + mathjaxClassName + 'wrapper') || item.closest('.' + mathjaxClassName)));

        let closestMathWrapper = null;

        if (closestWrapper && closestWrapper.closest) {
          closestMathWrapper = closestWrapper.closest('.' + mathjaxClassName + 'wrapper');
          if (!closestMathWrapper) {
            const mathElement = closestWrapper.closest('.' + mathjaxClassName);
            closestMathWrapper = mathElement ? mathElement.parentNode : null;
          }
        }
        // console.log('closestMathWrapper', closestMathWrapper, closestWrapper);
        if (closestMathWrapper) {
          // 后
          if (closestMathWrapper.nextSibling && closestMathWrapper.nextSibling.nodeType !== 3) {
            const nextSibling = closestMathWrapper.nextSibling;
            // console.log('后有其他...add一个空文本');
            closestMathWrapper.parentNode.insertBefore(editor.dom.doc.createTextNode("\xA0"), nextSibling);
          }
          if (!closestMathWrapper.nextSibling) {
            // console.log('后无其他...add一个空文本');
            closestMathWrapper.parentNode.appendChild(editor.dom.doc.createTextNode("\xA0"));
          }
          // 前
          if (closestMathWrapper.previousSibling && closestMathWrapper.previousSibling.nodeType !== 3) {
            // const previousSibling = closestMathWrapper.previousSibling;
            // console.log('前有其他...add一个空文本');
            closestMathWrapper.parentNode.insertBefore(editor.dom.doc.createTextNode("\xA0"), closestMathWrapper);
          }
          if (!closestMathWrapper.previousSibling) {
            // console.log('前无其他...add一个空文本');
            closestMathWrapper.parentNode.insertBefore(editor.dom.doc.createTextNode("\xA0"), closestMathWrapper);
            // closestMathWrapper.appendChild(editor.dom.doc.createTextNode("\xA0"));
          }
        }
      });

      // load mathjax and its config on editor init
      editor.on('init', function () {
        const scripts = editor.getDoc().getElementsByTagName('script');
        for (let i = 0; i < mathjaxScripts.length; i++) {
          // check if script have already loaded
          const id = editor.dom.uniqueId();
          const script = editor.dom.create('script', { id: id, type: 'text/javascript', src: mathjaxScripts[i] });
          let found = false;
          for (let j = 0; j < scripts.length; j++) {
            if (scripts[j].src == script.src || scripts[j].src == mathjaxScripts[i]) {
              found = true;
              break;
            }
          }
          // load script
          if (!found) {
            editor.getDoc().getElementsByTagName('head')[0].appendChild(script);
          }
        }
      });
      // remove extra tags on get content
      editor.on('GetContent', function (e) {
        const div = editor.dom.create('div');
        div.innerHTML = e.content;
        const elements = div.querySelectorAll('.' + mathjaxClassName);
        for (let i = 0; i < elements.length; i++) {
          const children = elements[i].querySelectorAll('span');
          for (let j = 0; j < children.length; j++) {
            children[j].remove();
          }
          const latex = elements[i].getAttribute('data-latex');
          elements[i].removeAttribute('contenteditable');
          elements[i].removeAttribute('style');
          elements[i].removeAttribute('data-latex');
          elements[i].innerHTML = latex;
        }
        e.content = div.innerHTML;
      });
      // add dummy tag on set content
      editor.on('BeforeSetContent', function (e) {
        const div = editor.dom.create('div');
        div.innerHTML = e.content;
        // console.log('BeforeSetContent..content', div.innerHTML);
        const elements = div.querySelectorAll('.' + mathjaxClassName);
        for (let i = 0; i < elements.length; i++) {
          checkElement(elements[i]);
        }
        e.content = div.innerHTML;
      });

      // refresh mathjax on set content
      editor.on('SetContent', function () {
        try {
          if (editor.getDoc().defaultView && editor.getDoc().defaultView.MathJax) {
            // console.log('SetContent.typesetPromise');
            editor.getDoc().defaultView.MathJax.typesetPromise();
          }
        } catch (error) {
          console.log('SetContent.typesetPromise error', error);
        }
        
      });

      // refresh mathjax on any content change
      editor.on('Change', function () {
        // eslint-disable-next-line no-undef
        const elements = editor.dom.getRoot().querySelectorAll('.' + mathjaxClassName);
        // console.log('on Change', elements);
        if (elements.length) {
          for (let i = 0; i < elements.length; i++) {
            checkElement(elements[i]);
          }
          if (editor.getDoc().defaultView && editor.getDoc().defaultView.MathJax) {
            console.log('Change.typesetPromise');
            try {
              if (editor.getDoc().defaultView && editor.getDoc().defaultView.MathJax) {
                editor.getDoc().defaultView.MathJax.typesetPromise();
              }
            } catch (error) {
              console.log('SetContent.typesetPromise error', error);
            }
          }
        }
      });

      editor.on("dblclick", function (e) {
        const closest = e.target.closest('.' + mathjaxClassName);
        if (closest) {
          openMathjaxEditor(closest);
        }
      });

      // editor.ui.registry.addIcon(
      //   "mathjax",
      //   '<svg t="1718878765438" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1482" width="24" height="24"><path d="M442.092 487.964L247.486 241.402h197.382s69.479-5.549 94.498 30.472c25.024 36.026 33.359 91.432 33.359 91.432h25.024l-11.11-169.008H111.288v24.934L355.9 518.435 111.288 803.792v27.711H597.75l30.584-188.405-25.024-5.535s-8.335 41.557-41.694 80.342c-33.359 38.797-164.022 33.246-164.022 33.246H222.476l219.616-263.187z m320.351 25.696l-8.122-21.41c-5.94-15.119-9.165-23.306-9.688-24.531l-12.562-27.923c-8.912-19.998-17.46-30.022-25.671-30.022-12.414 0-22.186 11.584-29.336 34.723-5.248-1.916-7.875-4.701-7.875-8.364 0-10.774 6.29-23.134 18.813-37.043 12.571-13.924 23.85-20.876 33.794-20.876 19.514 0 39.709 28.012 60.685 84.04l5.348 13.812 6.039-9.633c37.196-58.81 68.526-88.218 94.015-88.218 6.637 0 14.408 1.915 23.321 5.737l-31.721 33.146c-4.201-1.209-7.174-1.827-8.927-1.827-19.198 0-42.119 22.028-68.665 66.047l-7.589 12.685 5.989 16.023c22.971 63.178 41.843 94.766 56.794 94.766 13.697 0 24.136-9.763 31.335-29.237 4.884 3.141 7.323 6.35 7.323 9.663 0 8.71-6.978 19.148-20.951 31.321-13.969 12.175-25.997 18.284-36.145 18.284-20.26 0-40.943-28.457-62.067-85.35l-7.337-19.337-7.585 13.337c-35.621 60.903-69.682 91.35-102.167 91.35-12.211 0-22.595-3.038-31.157-9.061l30.371-29.583c5.25 5.224 11.363 7.822 18.35 7.822 19.707 0 41.917-19.896 66.517-59.693l13.623-21.973 5.248-8.675z m0 0" p-id="1483" fill="#222f3e"></path></svg>',
      // );

      editor.ui.registry.addButton('mathjax', {
        icon: 'mathjax',
        tooltip: '公式',
        onAction: function () {
          const selected = editor.selection.getNode();
          let target = undefined;
          if (selected.classList.contains(mathjaxClassName)) {
            target = selected;
          }
          openMathjaxEditor(target);
        },
        onSetup: function (buttonApi) {
          return editor.selection.selectorChangedWithUnbind('.' + mathjaxClassName, buttonApi.setActive).unbind;
        }
      });
      // console.log('math plugins 2');
    });
  }
  Plugin();
})()
