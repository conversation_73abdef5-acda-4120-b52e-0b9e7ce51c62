/*
 * @FilePath     : /public/tinymce/plugins/underlinedot/plugin.js
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
/* eslint-disable no-undef */
/* eslint-disable no-var */
(function () {
  'use strict';

  var global = tinymce.util.Tools.resolve('tinymce.PluginManager');

  var register = function (editor) {
    // 判断formatter是否存在
    if (editor.formatter) {
      editor.formatter.register('underlinedot', {
        inline: 'span',
        classes: ['underlinedot'],
        exact: true
      });
    } else {
      editor.on('init', function () {
        editor.formatter.register('underlinedot', {
          inline: 'span',
          classes: ['underlinedot'],
          exact: true
        });
      });
    }
  };

  var registerButton = function (editor) {
    editor.ui.registry.addToggleButton('underlinedot', {
      icon: 'underlinedot',
      tooltip: '着重号',
      onAction: function () {
        // 恢复原始的 onAction 逻辑，以更好地处理无选区的情况
        const selection = editor.selection;
        if (selection.isCollapsed()) {
          // 插入一个零宽字符作为标记
          const marker = '\uFEFF';
          editor.insertContent(marker);

          // // 选中刚插入的字符
          // const rng = editor.selection.getRng();
          // rng.setStart(rng.startContainer, rng.startOffset - marker.length);
          // rng.setEnd(rng.startContainer, rng.startOffset);
          // editor.selection.setRng(rng);

          // 应用格式
          editor.formatter.toggle('underlinedot');

          // 将光标移回原位
          // editor.selection.collapse(false);
        } else {
          // 对选区应用格式
          editor.formatter.toggle('underlinedot');
        }
        // 直接使用 mceToggleFormat 命令，它能正确处理折叠和非折叠选区
        // editor.execCommand('mceToggleFormat', false, 'underlinedot');
      },
      onSetup: function (api) {
        // "API永新": 始终更新为最新的 API 实例
        editor._underlinedotApi = api;

        // "监听器永生": 确保监听器只注册一次
        if (!editor._underlinedotListener) {
          editor._underlinedotListener = function () {
            // 确保总是在最新的 API 实例上更新状态
            if (editor._underlinedotApi) {
              let isActive = false;
              const node = editor.selection.getNode();
              if (node) {
                // 使用手动DOM检查以确保可靠性
                const elements = editor.dom.getParents(node, '.underlinedot', editor.getBody());
                if (elements && elements.length > 0) {
                  // 检查着重号元素是否包含有效内容（非零宽度字符）
                  const element = elements[0];
                  const textContent = element.textContent || element.innerText || '';
                  const hasValidContent = textContent.replace(/\uFEFF/g, '').trim().length > 0;

                  // 只有当着重号元素包含有效内容时才激活按钮
                  if (hasValidContent) {
                    isActive = true;
                  }
                }
              }
              console.log("onSetup===",isActive);
              editor._underlinedotApi.setActive(isActive);
            }
          };
          editor.on('NodeChange', editor._underlinedotListener);
        }

        return function () {
          // 当按钮被销毁时，清除其 API 引用
          if (editor._underlinedotApi === api) {
            editor._underlinedotApi = null;
          }
        };
      }
    });
  };

  function Plugin () {
    global.add('underlinedot', function (editor) {
      console.log('underlinedot plugin registered', editor);
      register(editor);
      registerButton(editor);
    });
  }

  Plugin();
}());