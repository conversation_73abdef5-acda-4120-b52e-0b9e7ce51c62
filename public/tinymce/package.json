{"_from": "<PERSON><PERSON><PERSON>", "_id": "tinymce@5.6.2", "_inBundle": false, "_integrity": "sha1-TCDCLjKMXQuqsgSSjbwrDsvtGLQ=", "_location": "/tinymce", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "<PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/", "/@packy-tang/vue-tinymce"], "_resolved": "http://*************:8888/tinymce/-/tinymce-5.6.2.tgz", "_shasum": "4c20c22e328c5d0baab204928dbc2b0ecbed18b4", "_spec": "<PERSON><PERSON><PERSON>", "_where": "/Users/<USER>/workSpace/pyramid", "author": {"name": "Tiny Technologies, Inc"}, "bugs": {"url": "https://github.com/tinymce/tinymce/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "https://github.com/tinymce/tinymce-dist#readme", "keywords": ["editor", "wysiwyg", "<PERSON><PERSON><PERSON>", "richtext", "javascript", "html"], "license": "LGPL-2.1", "main": "tinymce.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/tinymce/tinymce-dist.git"}, "types": "tinymce.d.ts", "version": "5.6.2"}