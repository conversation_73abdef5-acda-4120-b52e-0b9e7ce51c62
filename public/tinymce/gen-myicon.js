/*
 * @Date: 2025-07-17 19:12:44
 * @LastEditors: chxu
 * @LastEditTime: 2025-07-17 19:12:53
 * @FilePath: /interactive-question-editor/public/tinymce/gen-myicon.js
 * @Author: chxu
 */
const fs = require('fs');
const path = require('path');

const iconsDir = path.resolve(__dirname, './myicons'); // 你的svg文件夹
const outputFile = path.resolve(__dirname, './myicons.js');

const files = fs.readdirSync(iconsDir).filter(f => f.endsWith('.svg'));

const arr = files.map(file => {
  const name = path.basename(file, '.svg');
  let svg = fs.readFileSync(path.join(iconsDir, file), 'utf8');
  // 去掉 <?xml ... ?> 标签
  svg = svg.replace(/<\?xml[\s\S]*?\?>/gi, '').trim();
  // 去掉换行
  svg = svg.replace(/\r?\n|\r/g, '');
  return `  ['${name}', \`${svg}\`]`;
});
const output = `const myicons = new Map([\n${arr.join(',\n')}\n]);\nexport default myicons;`;

fs.writeFileSync(outputFile, output, 'utf8');

console.log('myicons.js 已生成！');