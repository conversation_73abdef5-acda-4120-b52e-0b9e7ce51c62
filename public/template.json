{"animations": {"afterSubmitCorrect": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}, "afterSubmitWrong": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}}, "animationsForClient": {}, "components": [{"type": "optionComponent", "subType": "sudoku", "componentLabel": "数独", "tag": "", "canCombine": false, "deletable": false, "dragable": false, "editable": {"properties": {"width": false, "height": false, "x": false, "y": false, "angle": false}}, "properties": {"active": true, "width": 1280, "height": 720, "opacity": 255, "angle": 0, "x": 0, "y": 0, "stuSoduku": {"colors": ["#E5E5E5", "#88DDFF", "#FDBBD8", "#AFF392", "#FFE27E", "#DFDEFF", "#ABECF6", "#FFC896", "#9CF1CA", "#E8D7FF"], "grid": "4*4", "col": 4, "row": 4, "cells": [[{"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}], [{"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}], [{"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}], [{"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}, {"color": "", "text": "", "groupId": ""}]], "groups": [], "dashAreaColor": "#ff0000", "type": "0", "isMark": "1"}, "stuHelp1": false, "stuHelp2": false, "stuFeedback": false, "stuTimingType": "0", "stuStartTime": 90, "stuStarTime3": 1, "stuStartEvaluate3": "", "stuStarTime2": 2, "stuStartEvaluate2": "", "stuStarTime1": 3, "stuStartEvaluate1": "", "stuStartPage": null, "stuSpineSettle": null, "stuDefaultColorGridUrl": "", "stuBaseGridUrl1": "", "stuBaseGridUrl2": "", "stuBaseGridUrl3": "", "stuBaseGridUrl4": "", "stuBaseGridUrl5": "", "stuBaseGridUrl6": "", "stuBaseGridUrl7": "", "stuBaseGridUrl8": "", "stuBaseGridUrl9": "", "stuBaseGridUrl10": "", "stuStemStyleUrl": "", "stuOptionStyleUrl": "", "stuOptionGridUrl": "", "stuClickGridUrl": "", "stuHelp1Url": "", "stuHelp2Url": "", "stuDefaultGridUrl": "", "stuStartAudio": "", "stuFeedBack3Audio": "", "stuFeedBack2Audio": "", "stuFeedBack1Audio": "", "stuFeedBack0Audio": "", "stuFailAudio": "", "stuDragAudio": "", "stuHighLightAudio": "", "stuClickAudio": "", "stuPositioningAudio": "", "stuErrorAudio": "", "propertiesKey1": " "}, "id": "1", "extra": {"tag": ""}}], "extraDataMap": {}, "extraStageData": {"isAutoSubmit": false, "hasRecover": true}, "resourceList": [], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "template": {"name": "数独", "tempType": 98, "category": 1184, "bundleUrl": "", "questionType": 3, "bundleName": "sudokuQuestion", "tags": [], "supportSetReferenceAnswer": true, "referenceAnswer": {}, "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "extraConfig": [], "formConfig": {"sudoku": [{"formItemType": "collapse", "collapseName": "题目属性", "formList": [{"formItemType": "SpecialSudoConfig", "key": "stu<PERSON><PERSON><PERSON>", "label": "", "labelPosition": "top", "labelTips": "", "value": {"dashAreaColor": "#EA344B", "type": "0", "grid": "4*4", "isMark": "1", "groups": [], "cells": [], "col": 4, "row": 4}}, {"formItemType": "BaseSwitch", "key": "stuHelp1", "label": "帮助1", "labelTips": "点击按钮使某行或某列高亮，当一行和一列同时高亮时，交叉位置有特殊颜色，再次点击按钮取消高亮。", "value": false}, {"formItemType": "BaseSwitch", "key": "stuHelp2", "label": "帮助2", "labelTips": "点击数独方格内任意数字，该数字所在行、列、宫/区域高亮，再次点击数字取消高亮。（B端配置和用户操作的都可点击）", "value": false}, {"formItemType": "BaseSwitch", "key": "stuFeedback", "label": "即时反馈", "value": false}, {"formItemType": "BaseRadioGroup", "key": "stuTimingType", "label": "计时方式", "value": "0", "rule": [{"required": true, "message": "请设置计时方式", "trigger": "change"}], "options": [{"label": "正计时", "value": "0", "span": 6, "associatedForm": [{"formItemType": "BaseStaticText", "key": "propertiesKey1", "label": "等级配置", "text": " ", "value": " ", "rule": [{"required": true, "message": "请设置等级配置", "trigger": "change"}]}, {"formItemType": "BaseStaticText", "key": "propertiesKey2", "label": "【三颗星】等级设置", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime3", "label": "时间级值", "labelTips": "", "value": 1, "stepStrictly": true, "step": 1, "span": 12, "unit": "秒", "min": 1, "max": 1000, "rule": [{"required": true, "message": "请设置三颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate3", "label": "评价", "value": "", "span": 18}, {"formItemType": "BaseStaticText", "key": "propertiesKey3", "label": "【两颗星】等级设置", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime2", "label": "时间级值", "labelTips": "", "value": 2, "stepStrictly": true, "step": 1, "unit": "秒", "span": 12, "min": 1, "max": 1000, "rule": [{"required": true, "message": "请设置l两颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate2", "label": "评价", "value": "", "span": 18}, {"formItemType": "BaseStaticText", "key": "propertiesKey4", "label": "【一颗星】等级设置", "labelTips": "游戏时间高于设置的【一颗星】时间级值，则默认获得零颗星。", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime1", "label": "时间级值", "labelTips": "", "value": 3, "stepStrictly": true, "step": 1, "unit": "秒", "span": 12, "min": 1, "max": 1000, "rule": [{"required": true, "message": "请设置一颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate1", "label": "评价", "value": "", "span": 18}]}, {"label": "倒计时", "value": "1", "span": 6, "associatedForm": [{"formItemType": "BaseInputNumber", "key": "stuStartTime", "label": "时间设置", "labelTips": "", "value": 90, "stepStrictly": true, "step": 1, "span": 12, "min": 0, "max": 900, "unit": "秒"}, {"formItemType": "BaseStaticText", "key": "propertiesKey1", "label": "等级配置", "text": " ", "value": " ", "rule": [{"required": true, "message": "请设置等级配置", "trigger": "change"}]}, {"formItemType": "BaseStaticText", "key": "propertiesKey2", "label": "【三颗星】等级设置", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime3", "label": "时间级值", "labelTips": "", "value": 1, "stepStrictly": true, "step": 1, "span": 12, "unit": "秒", "min": 1, "max": 900, "rule": [{"required": true, "message": "请设置三颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate3", "label": "评价", "value": "", "span": 18}, {"formItemType": "BaseStaticText", "key": "propertiesKey3", "label": "【两颗星】等级设置", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime2", "label": "时间级值", "labelTips": "", "value": 2, "stepStrictly": true, "step": 1, "unit": "秒", "span": 12, "min": 1, "max": 900, "rule": [{"required": true, "message": "请设置l两颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate2", "label": "评价", "value": "", "span": 18}, {"formItemType": "BaseStaticText", "key": "propertiesKey4", "label": "【一颗星】等级设置", "labelTips": "游戏时间高于设置的【一颗星】时间级值，则默认获得零颗星。", "text": ""}, {"formItemType": "BaseInputNumber", "key": "stuStarTime1", "label": "时间级值", "labelTips": "", "value": 3, "stepStrictly": true, "step": 1, "unit": "秒", "span": 12, "min": 1, "max": 900, "rule": [{"required": true, "message": "请设置一颗星时间级值", "trigger": "change"}]}, {"formItemType": "BaseInput", "key": "stuStartEvaluate1", "label": "评价", "value": "", "span": 18}]}, {"label": "不计时", "value": "2", "span": 6}]}]}, {"formItemType": "collapse", "collapseName": "业务属性", "formList": [{"formItemType": "BaseSpineSelect", "key": "stuStartPage", "label": "启动页", "hasDefaultOption": false, "value": {"atlas": "", "images": [], "skeleton": "", "cover": ""}, "options": [{"label": "动画列表", "value": "anim"}]}, {"formItemType": "BaseSpineSelect", "key": "stuSpineSettle", "label": "结果反馈动画", "hasDefaultOption": false, "value": {"atlas": "", "images": [], "skeleton": "", "cover": ""}, "options": [{"label": "3星动画", "value": "star3"}, {"label": "2星动画", "value": "star2"}, {"label": "1星动画", "value": "star1"}, {"label": "0星动画", "value": "star0"}, {"label": "失败动画", "value": "fail"}], "validation": [], "rule": [{"required": true, "message": "请设置结果反馈动画", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuDefaultGridUrl", "label": "题板默认格子（图片推荐80*80）", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置题板默认格子", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuDefaultColorGridUrl", "label": "题板默认格子变色（图片推荐80*80）", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置题板默认格子变色", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl1", "label": "题板默认格子灰色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl2", "label": "题板默认格子蓝色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl3", "label": "题板默认格子粉色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl4", "label": "题板默认格子绿色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl5", "label": "题板默认格子黄色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl6", "label": "题板默认格子紫色（图片推荐80*80）", "labelPosition": "top", "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl7", "label": "题板默认格子浅蓝色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl8", "label": "题板默认格子橙色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl9", "label": "题板默认格子青色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuBaseGridUrl10", "label": "题板默认格子浅紫色（图片推荐80*80）", "labelPosition": "top", "actions": {"change": true}, "value": ""}, {"formItemType": "BaseImageSelect", "key": "stuStemStyleUrl", "label": "题板样式图", "labelTips": "4*4图片推荐368*368，6*6图片推荐532*532", "labelPosition": "top", "actions": {"change": true}, "value": "", "rule": [{"required": true, "message": "请设置题板样式图", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuOptionStyleUrl", "label": "选项区样式图", "labelTips": "4*4图片推荐437*406，6*6图片推荐437*514", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置选项区样式图", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuOptionGridUrl", "label": "选项区格子图（图片推荐80*80），png形式", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置选项区格子图", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuClickGridUrl", "label": "数字被点击格子图（图片推荐80*80）", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置数字被点击格子图", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuHelp1Url", "label": "帮助1高亮框（图片推荐80*80）", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置帮助1高亮框", "trigger": "change"}]}, {"formItemType": "BaseImageSelect", "key": "stuHelp2Url", "label": "帮助2高亮框（图片推荐80*80）", "labelPosition": "top", "value": "", "rule": [{"required": true, "message": "请设置帮助2高亮框", "trigger": "change"}]}, {"formItemType": "BaseAudioSelect", "key": "stuStartAudio", "label": "启动页音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuFeedBack3Audio", "label": "结果反馈3星音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuFeedBack2Audio", "label": "结果反馈2星音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuFeedBack1Audio", "label": "结果反馈1星音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuFeedBack0Audio", "label": "结果反馈0星音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuFailAudio", "label": "失败动画音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuDragAudio", "label": "拖拽音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuHighLightAudio", "label": "高亮音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuClickAudio", "label": "点击数字音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuPositioningAudio", "label": "落位音效", "value": ""}, {"formItemType": "BaseAudioSelect", "key": "stuErrorAudio", "label": "错误提示音效", "value": ""}]}]}, "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "features": {"isQuestion": 1, "newCocos": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "questionType": 3, "versionInfo": {"v": 1, "timeStamp": 1651030337}, "thumbnail": ""}