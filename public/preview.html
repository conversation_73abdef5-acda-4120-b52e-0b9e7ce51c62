<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <% if ( NODE_ENV === 'production') {%>
    <meta http-equiv="x-dns-prefetch-control" content="on" />
  <link rel="preconnect" href="//jiaoxue-tihu.zuoyebang.cc">
  <link rel="preconnect" href="<%= VUE_APP_CDN_PATH %>">
  <link rel="preconnect" href="//yaya.cdnjtzy.com">
  <link rel="dns-prefetch" href="//jiaoxue-tihu.zuoyebang.cc">
  <link rel="dns-prefetch" href="<%= VUE_APP_CDN_PATH %>">
  <link rel="dns-prefetch" href="//yaya.cdnjtzy.com">
  <% } %>
  <link rel="icon" href="<%= BASE_URL %>favicon.ico">
  <title><%= htmlWebpackPlugin.options.title %></title>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
      Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- 预览是否需要公式 不需要的话只在editor引入该文件 -->
  <!-- <script src="https://yy-s.zuoyebang.cc/static/mathjax_274/MathJax.js?config=svgsnippet-update200113"></script> -->
<!--  <script src="./tinymce/tinymce.min.js"></script>-->
  <!-- built files will be auto injected -->
</body>

</html>
