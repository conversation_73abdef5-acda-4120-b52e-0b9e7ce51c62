[1, ["ecpdLyjvZBwrvm+cedCcQy", "a1U5RdJRFMFL57BdJC9H1X", "41D7kWhyFGY7q4NDlzkazn", "e7Mg4ILWBOPLnQmP5yNqGc", "57rCSzfPRD7JKJj8aP+v0m"], ["node", "_spriteFrame", "root", "_parent", "debug<PERSON><PERSON><PERSON>", "topLayer", "touchLayer", "anim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayObjectLayer", "background<PERSON>ayer", "shootCameraNode", "data"], [["cc.Node", ["_name", "_prefab", "_components", "_parent", "_contentSize", "_children", "_trs", "_color"], 2, 4, 9, 1, 5, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "alignMode", "node"], -1, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Graphics", ["node", "_materials", "_strokeColor", "_fillColor"], 3, 1, 3, 5, 5], ["cc.Prefab", ["_name"], 2], ["cc.Camera", ["_cullingMask", "_clearFlags", "_depth", "_nearClip", "node"], -1, 1], ["d0ba0izD/5O/anLK1jJT5ka", ["node"], 3, 1], ["a8df9IFzihOro7kpvXaBbQs", ["node"], 3, 1], ["36ac5+HGQJGG7v4jiq7HgUp", ["node", "shootCameraNode", "background<PERSON>ayer", "displayObjectLayer", "anim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "touchLayer", "topLayer", "debug<PERSON><PERSON><PERSON>"], 3, 1, 1, 1, 1, 1, 1, 1, 1]], [[3, 0, 1, 2], [0, 0, 3, 2, 1, 4, 2], [2, 0, 4, 2], [0, 0, 3, 2, 1, 2], [0, 0, 3, 5, 1, 4, 6, 2], [0, 0, 2, 1, 4, 2], [1, 1, 0, 2, 3, 4, 3], [2, 3, 0, 1, 2, 4, 5], [5, 0, 2], [0, 0, 5, 2, 1, 4, 2], [0, 0, 3, 2, 1, 6, 2], [0, 0, 3, 5, 2, 1, 4, 2], [0, 0, 3, 5, 2, 1, 7, 4, 2], [0, 0, 3, 5, 1, 2], [6, 0, 1, 2, 3, 4, 5], [3, 1, 1], [4, 0, 1, 2, 1], [4, 0, 1, 2, 3, 1], [1, 0, 2, 3, 2], [1, 0, 2, 3, 4, 2], [2, 0, 1, 2, 4, 4], [7, 0, 1], [8, 0, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[8, "stage_prefab"], [9, "stage_prefab", [-10, -11, -12], [[23, -9, -8, -7, -6, -5, -4, -3, -2]], [15, -1], [5, 1280, 960]], [11, "stage", 1, [-14, -15, -16, -17, -18, -19, -20], [[2, 45, -13]], [0, "a6bzFbQABK7K4VnU8fgvQK", 1], [5, 1280, 960]], [13, "com_BtnNextAndSubmit", 1, [-22, -23], [0, "93poEdrGZHMYHNCxwmUDwr", -21]], [12, "background", 2, [-26, -27], [[19, 0, -24, [2], 3], [20, 45, 2, 2, -25]], [0, "d08zMYnoxL6Z+y80Fla0EE", 1], [4, 4278190080], [5, 1280, 960]], [1, "touch_layer", 2, [[2, 45, -28], [22, -29]], [0, "3d3XEewBBEgpb0ju6hPBX6", 1], [5, 1280, 960]], [1, "debug_layer", 2, [[2, 45, -30], [17, -31, [4], [4, 4278190335], [4, 4278190335]]], [0, "c56X9EorJPLppB5ysAeUYo", 1], [5, 1280, 960]], [10, "shootCamera", 1, [[14, -3, 7, -1, 0.1, -32]], [0, "95ogdDdSNKmqBy1uctDdwe", 1], [0, 0, 297.9127389018469, 0, 0, 0, 1, 1, 1, 1]], [1, "object_layer", 2, [[2, 45, -33]], [0, "eeUCUDT+9AELXMMTUxZbyQ", 1], [5, 1280, 960]], [1, "anim_layer", 2, [[2, 45, -34]], [0, "d4PvrZ0N9KVKr+qJlv+7JW", 1], [5, 1280, 960]], [1, "top_layer", 2, [[2, 45, -35]], [0, "8eaboPLB1M8YG8HjLEmPZK", 1], [5, 1280, 960]], [5, "Background", [[6, 1, 0, -36, [5], 6], [7, 0, 45, 100, 40, -37]], [0, "8eUpyMxetHhrr6cqZyKMB4", 3], [5, 114, 114]], [5, "Background", [[6, 1, 0, -38, [7], 8], [7, 0, 45, 100, 40, -39]], [0, "3cE9+D0OJArpZX8GD8oHmN", 3], [5, 114, 114]], [3, "edge", 4, [[16, -40, [0], [4, 4278519287]]], [0, "d1KOiAVvZDtL+CovA3ET34", 1]], [1, "img", 4, [[18, 0, -41, [1]]], [0, "0eDNbZpv9K47ztGSynHdsw", 1], [5, 40, 36]], [3, "hint_layer", 2, [[21, -42]], [0, "bcBlda3BpF1ZyZgfA5B0qT", 1]], [4, "com_btnReset", 3, [11], [0, "20TTRhXMRCOplD4h020DSn", 3], [5, 114, 114], [556.22900390625, -137.49600219726562, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "com_btnSubmit", 3, [12], [0, "4e9PuJ8PJJyKIE7NG9VMjq", 3], [5, 114, 114], [556, -274, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 4, 6, 0, 5, 10, 0, 6, 5, 0, 7, 9, 0, 8, 8, 0, 9, 4, 0, 10, 7, 0, 0, 1, 0, -1, 7, 0, -2, 2, 0, -3, 3, 0, 0, 2, 0, -1, 4, 0, -2, 8, 0, -3, 15, 0, -4, 9, 0, -5, 5, 0, -6, 10, 0, -7, 6, 0, 2, 3, 0, -1, 16, 0, -2, 17, 0, 0, 4, 0, 0, 4, 0, -1, 13, 0, -2, 14, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 11, 1, 11, 3, 16, 12, 3, 17, 42], [0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, 1, -1, -1, 1, -1, 1], [1, 0, 0, 2, 1, 0, 3, 0, 4]]