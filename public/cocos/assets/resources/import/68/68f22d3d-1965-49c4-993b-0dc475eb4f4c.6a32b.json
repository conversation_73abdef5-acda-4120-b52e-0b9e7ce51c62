[1, ["ecpdLyjvZBwrvm+cedCcQy"], ["node", "root", "_N$placeholderLabel", "_N$textLabel", "data"], [["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], 1, 1, 12, 4, 5, 5, 7, 5], ["cc.Label", ["_fontSize", "_lineHeight", "_enableWrapText", "_N$verticalAlign", "_N$overflow", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize"], 2, 2, 9, 4, 5], ["cc.Widget", ["alignMode", "_alignFlags", "_left", "_originalWidth", "_originalHeight", "node"], -2, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.EditBox", ["max<PERSON><PERSON><PERSON>", "_N$inputMode", "node", "_N$textLabel", "_N$placeholderLabel"], 1, 1, 1, 1]], [[5, 0, 1, 2], [4, 0, 1, 2, 3, 4, 5, 6], [2, 0, 2], [3, 0, 1, 2, 3, 4, 2], [0, 0, 1, 2, 3, 4, 5, 6, 7, 3], [0, 0, 2, 3, 4, 8, 5, 6, 7, 2], [1, 0, 1, 2, 3, 4, 5, 6], [1, 0, 1, 2, 3, 4, 5, 6, 6], [6, 0, 1, 2, 3, 4, 3]], [[2, "EditBox"], [3, "EditBox", [-5, -6], [[8, 500, 6, -4, -3, -2]], [0, "f75jfipihBO5JYEd9h/orw", -1], [5, 160, 40]], [4, "TEXT_LABEL", false, 1, [[-7, [1, 0, 45, 2, 158, 40, -8]], 1, 4], [0, "37W8xZ+pBBvYPKjypoZwuE", 1], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "PLACEHOLDER_LABEL", 1, [[-9, [1, 0, 45, 2, 158, 40, -10]], 1, 4], [0, "bcMmFs6tBIdJnAMjoBeNis", 1], [4, 4290493371], [5, 158, 40], [0, 0, 1], [-78, 20, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 20, 25, false, 1, 1, 2], [7, 20, 25, false, 1, 1, 3, [0]]], 0, [0, 1, 1, 0, 2, 5, 0, 3, 4, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -1, 4, 0, 0, 2, 0, -1, 5, 0, 0, 3, 0, 4, 1, 10], [0], [-1], [0]]