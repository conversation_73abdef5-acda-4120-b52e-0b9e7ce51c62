window.__require=function e(t,n,o){function i(a,s){if(!n[a]){if(!t[a]){var c=a.split("/");if(c=c[c.length-1],!t[c]){var p="function"==typeof __require&&__require;if(!s&&p)return p(c,!0);if(r)return r(c,!0);throw new Error("Cannot find module '"+a+"'")}a=c}var l=n[a]={exports:{}};t[a][0].call(l.exports,function(e){return i(t[a][1][e]||e)},l,l.exports,e,t,n,o)}return n[a].exports}for(var r="function"==typeof __require&&__require,a=0;a<o.length;a++)i(o[a]);return i}({1:[function(e,t,n){!function(e,o){"object"==typeof n&&void 0!==t?o(n):"function"==typeof define&&define.amd?define(["exports"],o):o((e=e||self).clipboard={})}(this,function(e){"use strict";function t(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(e){r(e)}}function s(e){try{c(o.throw(e))}catch(e){r(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})}function n(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!((i=(i=a.trys).length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(e){r=[6,e],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}}var o=function(){};function i(e){o(e)}var r=!0;function a(){return r}(function(){(console.warn||console.log).apply(console,arguments)}).bind("[clipboard-polyfill]");var s,c,p,l,u="undefined"==typeof navigator?void 0:navigator,d=null==u?void 0:u.clipboard,y=null===(s=null==d?void 0:d.read)||void 0===s?void 0:s.bind(d),f=null===(c=null==d?void 0:d.readText)||void 0===c?void 0:c.bind(d),h=null===(p=null==d?void 0:d.write)||void 0===p?void 0:p.bind(d),m=null===(l=null==d?void 0:d.writeText)||void 0===l?void 0:l.bind(d),g="undefined"==typeof window?void 0:window,_=null==g?void 0:g.ClipboardItem,v=g;function b(){return"undefined"==typeof ClipboardEvent&&void 0!==v.clipboardData&&void 0!==v.clipboardData.setData}function C(){return t(this,void 0,void 0,function(){var e;return n(this,function(){if(""===(e=v.clipboardData.getData("Text")))throw new Error("Empty clipboard or could not read plain text from clipboard");return[2,e]})})}var O=function(){this.success=!1};function T(e,t,n){for(var o in i("listener called"),e.success=!0,t){var r=t[o],a=n.clipboardData;a.setData(o,r),"text/plain"===o&&a.getData(o)!==r&&(i("setting text/plain failed"),e.success=!1)}n.preventDefault()}function w(e){var t=new O,n=T.bind(this,t,e);document.addEventListener("copy",n);try{document.execCommand("copy")}finally{document.removeEventListener("copy",n)}return t.success}function I(e,t){P(e);var n=w(t);return S(),n}function P(e){var t=document.getSelection();if(t){var n=document.createRange();n.selectNodeContents(e),t.removeAllRanges(),t.addRange(n)}}function S(){var e=document.getSelection();e&&e.removeAllRanges()}function A(e){return t(this,void 0,void 0,function(){var t;return n(this,function(){if(t="text/plain"in e,b()){if(!t)throw new Error("No `text/plain` value was specified.");if(n=e["text/plain"],v.clipboardData.setData("Text",n))return[2,!0];throw new Error("Copying failed, possibly because the user rejected it.")}var n;return w(e)?(i("regular execCopy worked"),[2,!0]):navigator.userAgent.indexOf("Edge")>-1?(i('UA "Edge" => assuming success'),[2,!0]):I(document.body,e)?(i("copyUsingTempSelection worked"),[2,!0]):function(e){var t=document.createElement("div");t.setAttribute("style","-webkit-user-select: text !important"),t.textContent="temporary element",document.body.appendChild(t);var n=I(t,e);return document.body.removeChild(t),n}(e)?(i("copyUsingTempElem worked"),[2,!0]):function(e){i("copyTextUsingDOM");var t=document.createElement("div");t.setAttribute("style","-webkit-user-select: text !important");var n=t;t.attachShadow&&(i("Using shadow DOM."),n=t.attachShadow({mode:"open"}));var o=document.createElement("span");o.innerText=e,n.appendChild(o),document.body.appendChild(t),P(o);var r=document.execCommand("copy");return S(),document.body.removeChild(t),r}(e["text/plain"])?(i("copyTextUsingDOM worked"),[2,!0]):[2,!1]})})}function D(){return t(this,void 0,void 0,function(){return n(this,function(){if(f)return i("Using `navigator.clipboard.readText()`."),[2,f()];if(b())return i("Reading text using IE strategy."),[2,C()];throw new Error("Read is not supported in your browser.")})})}function x(e,t){for(var n=0,o=e;n<o.length;n++)if(-1!==o[n].types.indexOf(t))return!0;return!1}var E=function(){function e(e,t){var n;for(var o in void 0===t&&(t={}),this.types=Object.keys(e),this._items={},e){var i=e[o];this._items[o]="string"==typeof i?L(o,i):i}this.presentationStyle=null!==(n=null==t?void 0:t.presentationStyle)&&void 0!==n?n:"unspecified"}return e.prototype.getType=function(e){return t(this,void 0,void 0,function(){return n(this,function(){return[2,this._items[e]]})})},e}();function L(e,t){return new Blob([t],{type:e})}function M(e){return t(this,void 0,void 0,function(){return n(this,function(){return[2,new Promise(function(t,n){var o=new FileReader;o.addEventListener("load",function(){var e=o.result;"string"==typeof e?t(e):n("could not convert blob to string")}),o.readAsText(e)})]})})}function j(e){return t(this,void 0,void 0,function(){var t,o,i,r,a,s,c;return n(this,function(n){switch(n.label){case 0:t={},o=0,i=e.types,n.label=1;case 1:return o<i.length?(r=i[o],a=t,s=r,[4,e.getType(r)]):[3,4];case 2:a[s]=n.sent(),n.label=3;case 3:return o++,[3,1];case 4:return c={},e.presentationStyle&&(c.presentationStyle=e.presentationStyle),[2,new _(t,c)]}})})}function R(e){var t={};return t["text/plain"]=L(e,"text/plain"),new E(t)}function N(e,o){return t(this,void 0,void 0,function(){return n(this,function(t){switch(t.label){case 0:return[4,e.getType(o)];case 1:return[4,M(t.sent())];case 2:return[2,t.sent()]}})})}function B(e){return t(this,void 0,void 0,function(){var t,o,i,r,a,s;return n(this,function(n){switch(n.label){case 0:t={},o=0,i=e.types,n.label=1;case 1:return o<i.length?(r=i[o],a=t,s=r,[4,N(e,r)]):[3,4];case 2:a[s]=n.sent(),n.label=3;case 3:return o++,[3,1];case 4:return[2,t]}})})}e.ClipboardItem=E,e.read=function(){return t(this,void 0,void 0,function(){var e;return n(this,function(t){switch(t.label){case 0:return y?(i("Using `navigator.clipboard.read()`."),[2,y()]):(e=R,[4,D()]);case 1:return[2,[e.apply(void 0,[t.sent()])]]}})})},e.readText=D,e.setDebugLog=function(e){o=e},e.suppressWarnings=function(){r=!1},e.write=function(e){return t(this,void 0,void 0,function(){var t,o,r,s;return n(this,function(n){switch(n.label){case 0:return h&&_?(i("Using `navigator.clipboard.write()`."),[4,Promise.all(e.map(j))]):[3,5];case 1:t=n.sent(),n.label=2;case 2:return n.trys.push([2,4,,5]),[4,h(t)];case 3:return[2,n.sent()];case 4:if(o=n.sent(),!x(e,"text/plain")&&!x(e,"text/html"))throw o;return[3,5];case 5:return r=x(e,"text/plain"),a&&!r&&i("clipboard.write() was called without a `text/plain` data type. On some platforms, this may result in an empty clipboard. Call suppressWarnings() to suppress this warning."),s=A,[4,B(e[0])];case 6:if(!s.apply(void 0,[n.sent()]))throw new Error("write() failed");return[2]}})})},e.writeText=function(e){return t(this,void 0,void 0,function(){return n(this,function(){if(m)return i("Using `navigator.clipboard.writeText()`."),[2,m(e)];if(!A(function(e){var t={};return t["text/plain"]=e,t}(e)))throw new Error("writeText() failed");return[2]})})},Object.defineProperty(e,"__esModule",{value:!0})})},{}],2:[function(e,t,n){(function(e){(function(){var o,i=200,r="Expected a function",a="__lodash_placeholder__",s=1,c=2,p=4,l=1,u=2,d=1,y=2,f=4,h=8,m=16,g=32,_=64,v=128,b=256,C=512,O=800,T=16,w=1/0,I=9007199254740991,P=17976931348623157e292,S=NaN,A=4294967295,D=A-1,x=A>>>1,E=[["ary",v],["bind",d],["bindKey",y],["curry",h],["curryRight",m],["flip",C],["partial",g],["partialRight",_],["rearg",b]],L="[object Arguments]",M="[object Array]",j="[object AsyncFunction]",R="[object Boolean]",N="[object Date]",B="[object DOMException]",F="[object Error]",U="[object Function]",k="[object GeneratorFunction]",H="[object Map]",V="[object Number]",Y="[object Null]",G="[object Object]",z="[object Proxy]",q="[object RegExp]",W="[object Set]",X="[object String]",Z="[object Symbol]",K="[object Undefined]",$="[object WeakMap]",J="[object ArrayBuffer]",Q="[object DataView]",ee="[object Float32Array]",te="[object Float64Array]",ne="[object Int8Array]",oe="[object Int16Array]",ie="[object Int32Array]",re="[object Uint8Array]",ae="[object Uint8ClampedArray]",se="[object Uint16Array]",ce="[object Uint32Array]",pe=/\b__p \+= '';/g,le=/\b(__p \+=) '' \+/g,ue=/(__e\(.*?\)|\b__t\)) \+\n'';/g,de=/&(?:amp|lt|gt|quot|#39);/g,ye=/[&<>"']/g,fe=RegExp(de.source),he=RegExp(ye.source),me=/<%-([\s\S]+?)%>/g,ge=/<%([\s\S]+?)%>/g,_e=/<%=([\s\S]+?)%>/g,ve=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,be=/^\w*$/,Ce=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Oe=/[\\^$.*+?()[\]{}|]/g,Te=RegExp(Oe.source),we=/^\s+/,Ie=/\s/,Pe=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Se=/\{\n\/\* \[wrapped with (.+)\] \*/,Ae=/,? & /,De=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,xe=/[()=,{}\[\]\/\s]/,Ee=/\\(\\)?/g,Le=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Me=/\w*$/,je=/^[-+]0x[0-9a-f]+$/i,Re=/^0b[01]+$/i,Ne=/^\[object .+?Constructor\]$/,Be=/^0o[0-7]+$/i,Fe=/^(?:0|[1-9]\d*)$/,Ue=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ke=/($^)/,He=/['\n\r\u2028\u2029\\]/g,Ve="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",Ye="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Ge="["+Ye+"]",ze="["+Ve+"]",qe="\\d+",We="[a-z\\xdf-\\xf6\\xf8-\\xff]",Xe="[^\\ud800-\\udfff"+Ye+qe+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",Ze="\\ud83c[\\udffb-\\udfff]",Ke="[^\\ud800-\\udfff]",$e="(?:\\ud83c[\\udde6-\\uddff]){2}",Je="[\\ud800-\\udbff][\\udc00-\\udfff]",Qe="[A-Z\\xc0-\\xd6\\xd8-\\xde]",et="(?:"+We+"|"+Xe+")",tt="(?:"+Qe+"|"+Xe+")",nt="(?:"+ze+"|"+Ze+")?",ot="[\\ufe0e\\ufe0f]?"+nt+"(?:\\u200d(?:"+[Ke,$e,Je].join("|")+")[\\ufe0e\\ufe0f]?"+nt+")*",it="(?:"+["[\\u2700-\\u27bf]",$e,Je].join("|")+")"+ot,rt="(?:"+[Ke+ze+"?",ze,$e,Je,"[\\ud800-\\udfff]"].join("|")+")",at=RegExp("['\u2019]","g"),st=RegExp(ze,"g"),ct=RegExp(Ze+"(?="+Ze+")|"+rt+ot,"g"),pt=RegExp([Qe+"?"+We+"+(?:['\u2019](?:d|ll|m|re|s|t|ve))?(?="+[Ge,Qe,"$"].join("|")+")",tt+"+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?(?="+[Ge,Qe+et,"$"].join("|")+")",Qe+"?"+et+"+(?:['\u2019](?:d|ll|m|re|s|t|ve))?",Qe+"+(?:['\u2019](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",qe,it].join("|"),"g"),lt=RegExp("[\\u200d\\ud800-\\udfff"+Ve+"\\ufe0e\\ufe0f]"),ut=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,dt=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],yt=-1,ft={};ft[ee]=ft[te]=ft[ne]=ft[oe]=ft[ie]=ft[re]=ft[ae]=ft[se]=ft[ce]=!0,ft[L]=ft[M]=ft[J]=ft[R]=ft[Q]=ft[N]=ft[F]=ft[U]=ft[H]=ft[V]=ft[G]=ft[q]=ft[W]=ft[X]=ft[$]=!1;var ht={};ht[L]=ht[M]=ht[J]=ht[Q]=ht[R]=ht[N]=ht[ee]=ht[te]=ht[ne]=ht[oe]=ht[ie]=ht[H]=ht[V]=ht[G]=ht[q]=ht[W]=ht[X]=ht[Z]=ht[re]=ht[ae]=ht[se]=ht[ce]=!0,ht[F]=ht[U]=ht[$]=!1;var mt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},gt=parseFloat,_t=parseInt,vt="object"==typeof e&&e&&e.Object===Object&&e,bt="object"==typeof self&&self&&self.Object===Object&&self,Ct=vt||bt||Function("","return this")(),Ot="object"==typeof n&&n&&!n.nodeType&&n,Tt=Ot&&"object"==typeof t&&t&&!t.nodeType&&t,wt=Tt&&Tt.exports===Ot,It=wt&&vt.process,Pt=function(){try{return Tt&&Tt.require&&Tt.require("util").types||It&&It.binding&&It.binding("util")}catch(e){}}(),St=Pt&&Pt.isArrayBuffer,At=Pt&&Pt.isDate,Dt=Pt&&Pt.isMap,xt=Pt&&Pt.isRegExp,Et=Pt&&Pt.isSet,Lt=Pt&&Pt.isTypedArray;function Mt(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)}function jt(e,t,n,o){for(var i=-1,r=null==e?0:e.length;++i<r;){var a=e[i];t(o,a,n(a),e)}return o}function Rt(e,t){for(var n=-1,o=null==e?0:e.length;++n<o&&!1!==t(e[n],n,e););return e}function Nt(e,t){for(var n=null==e?0:e.length;n--&&!1!==t(e[n],n,e););return e}function Bt(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(!t(e[n],n,e))return!1;return!0}function Ft(e,t){for(var n=-1,o=null==e?0:e.length,i=0,r=[];++n<o;){var a=e[n];t(a,n,e)&&(r[i++]=a)}return r}function Ut(e,t){return!(null==e||!e.length)&&$t(e,t,0)>-1}function kt(e,t,n){for(var o=-1,i=null==e?0:e.length;++o<i;)if(n(t,e[o]))return!0;return!1}function Ht(e,t){for(var n=-1,o=null==e?0:e.length,i=Array(o);++n<o;)i[n]=t(e[n],n,e);return i}function Vt(e,t){for(var n=-1,o=t.length,i=e.length;++n<o;)e[i+n]=t[n];return e}function Yt(e,t,n,o){var i=-1,r=null==e?0:e.length;for(o&&r&&(n=e[++i]);++i<r;)n=t(n,e[i],i,e);return n}function Gt(e,t,n,o){var i=null==e?0:e.length;for(o&&i&&(n=e[--i]);i--;)n=t(n,e[i],i,e);return n}function zt(e,t){for(var n=-1,o=null==e?0:e.length;++n<o;)if(t(e[n],n,e))return!0;return!1}var qt=tn("length");function Wt(e){return e.split("")}function Xt(e){return e.match(De)||[]}function Zt(e,t,n){var o;return n(e,function(e,n,i){if(t(e,n,i))return o=n,!1}),o}function Kt(e,t,n,o){for(var i=e.length,r=n+(o?1:-1);o?r--:++r<i;)if(t(e[r],r,e))return r;return-1}function $t(e,t,n){return t==t?An(e,t,n):Kt(e,Qt,n)}function Jt(e,t,n,o){for(var i=n-1,r=e.length;++i<r;)if(o(e[i],t))return i;return-1}function Qt(e){return e!=e}function en(e,t){var n=null==e?0:e.length;return n?an(e,t)/n:S}function tn(e){return function(t){return null==t?o:t[e]}}function nn(e){return function(t){return null==e?o:e[t]}}function on(e,t,n,o,i){return i(e,function(e,i,r){n=o?(o=!1,e):t(n,e,i,r)}),n}function rn(e,t){var n=e.length;for(e.sort(t);n--;)e[n]=e[n].value;return e}function an(e,t){for(var n,i=-1,r=e.length;++i<r;){var a=t(e[i]);a!==o&&(n=n===o?a:n+a)}return n}function sn(e,t){for(var n=-1,o=Array(e);++n<e;)o[n]=t(n);return o}function cn(e,t){return Ht(t,function(t){return[t,e[t]]})}function pn(e){return e?e.slice(0,Ln(e)+1).replace(we,""):e}function ln(e){return function(t){return e(t)}}function un(e,t){return Ht(t,function(t){return e[t]})}function dn(e,t){return e.has(t)}function yn(e,t){for(var n=-1,o=e.length;++n<o&&$t(t,e[n],0)>-1;);return n}function fn(e,t){for(var n=e.length;n--&&$t(t,e[n],0)>-1;);return n}function hn(e,t){for(var n=e.length,o=0;n--;)e[n]===t&&++o;return o}var mn=nn({"\xc0":"A","\xc1":"A","\xc2":"A","\xc3":"A","\xc4":"A","\xc5":"A","\xe0":"a","\xe1":"a","\xe2":"a","\xe3":"a","\xe4":"a","\xe5":"a","\xc7":"C","\xe7":"c","\xd0":"D","\xf0":"d","\xc8":"E","\xc9":"E","\xca":"E","\xcb":"E","\xe8":"e","\xe9":"e","\xea":"e","\xeb":"e","\xcc":"I","\xcd":"I","\xce":"I","\xcf":"I","\xec":"i","\xed":"i","\xee":"i","\xef":"i","\xd1":"N","\xf1":"n","\xd2":"O","\xd3":"O","\xd4":"O","\xd5":"O","\xd6":"O","\xd8":"O","\xf2":"o","\xf3":"o","\xf4":"o","\xf5":"o","\xf6":"o","\xf8":"o","\xd9":"U","\xda":"U","\xdb":"U","\xdc":"U","\xf9":"u","\xfa":"u","\xfb":"u","\xfc":"u","\xdd":"Y","\xfd":"y","\xff":"y","\xc6":"Ae","\xe6":"ae","\xde":"Th","\xfe":"th","\xdf":"ss","\u0100":"A","\u0102":"A","\u0104":"A","\u0101":"a","\u0103":"a","\u0105":"a","\u0106":"C","\u0108":"C","\u010a":"C","\u010c":"C","\u0107":"c","\u0109":"c","\u010b":"c","\u010d":"c","\u010e":"D","\u0110":"D","\u010f":"d","\u0111":"d","\u0112":"E","\u0114":"E","\u0116":"E","\u0118":"E","\u011a":"E","\u0113":"e","\u0115":"e","\u0117":"e","\u0119":"e","\u011b":"e","\u011c":"G","\u011e":"G","\u0120":"G","\u0122":"G","\u011d":"g","\u011f":"g","\u0121":"g","\u0123":"g","\u0124":"H","\u0126":"H","\u0125":"h","\u0127":"h","\u0128":"I","\u012a":"I","\u012c":"I","\u012e":"I","\u0130":"I","\u0129":"i","\u012b":"i","\u012d":"i","\u012f":"i","\u0131":"i","\u0134":"J","\u0135":"j","\u0136":"K","\u0137":"k","\u0138":"k","\u0139":"L","\u013b":"L","\u013d":"L","\u013f":"L","\u0141":"L","\u013a":"l","\u013c":"l","\u013e":"l","\u0140":"l","\u0142":"l","\u0143":"N","\u0145":"N","\u0147":"N","\u014a":"N","\u0144":"n","\u0146":"n","\u0148":"n","\u014b":"n","\u014c":"O","\u014e":"O","\u0150":"O","\u014d":"o","\u014f":"o","\u0151":"o","\u0154":"R","\u0156":"R","\u0158":"R","\u0155":"r","\u0157":"r","\u0159":"r","\u015a":"S","\u015c":"S","\u015e":"S","\u0160":"S","\u015b":"s","\u015d":"s","\u015f":"s","\u0161":"s","\u0162":"T","\u0164":"T","\u0166":"T","\u0163":"t","\u0165":"t","\u0167":"t","\u0168":"U","\u016a":"U","\u016c":"U","\u016e":"U","\u0170":"U","\u0172":"U","\u0169":"u","\u016b":"u","\u016d":"u","\u016f":"u","\u0171":"u","\u0173":"u","\u0174":"W","\u0175":"w","\u0176":"Y","\u0177":"y","\u0178":"Y","\u0179":"Z","\u017b":"Z","\u017d":"Z","\u017a":"z","\u017c":"z","\u017e":"z","\u0132":"IJ","\u0133":"ij","\u0152":"Oe","\u0153":"oe","\u0149":"'n","\u017f":"s"}),gn=nn({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function _n(e){return"\\"+mt[e]}function vn(e,t){return null==e?o:e[t]}function bn(e){return lt.test(e)}function Cn(e){return ut.test(e)}function On(e){for(var t,n=[];!(t=e.next()).done;)n.push(t.value);return n}function Tn(e){var t=-1,n=Array(e.size);return e.forEach(function(e,o){n[++t]=[o,e]}),n}function wn(e,t){return function(n){return e(t(n))}}function In(e,t){for(var n=-1,o=e.length,i=0,r=[];++n<o;){var s=e[n];s!==t&&s!==a||(e[n]=a,r[i++]=n)}return r}function Pn(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n}function Sn(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=[e,e]}),n}function An(e,t,n){for(var o=n-1,i=e.length;++o<i;)if(e[o]===t)return o;return-1}function Dn(e,t,n){for(var o=n+1;o--;)if(e[o]===t)return o;return o}function xn(e){return bn(e)?jn(e):qt(e)}function En(e){return bn(e)?Rn(e):Wt(e)}function Ln(e){for(var t=e.length;t--&&Ie.test(e.charAt(t)););return t}var Mn=nn({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});function jn(e){for(var t=ct.lastIndex=0;ct.test(e);)++t;return t}function Rn(e){return e.match(ct)||[]}function Nn(e){return e.match(pt)||[]}var Bn=function e(t){var n,Ie=(t=null==t?Ct:Bn.defaults(Ct.Object(),t,Bn.pick(Ct,dt))).Array,De=t.Date,Ve=t.Error,Ye=t.Function,Ge=t.Math,ze=t.Object,qe=t.RegExp,We=t.String,Xe=t.TypeError,Ze=Ie.prototype,Ke=Ye.prototype,$e=ze.prototype,Je=t["__core-js_shared__"],Qe=Ke.toString,et=$e.hasOwnProperty,tt=0,nt=(n=/[^.]+$/.exec(Je&&Je.keys&&Je.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"",ot=$e.toString,it=Qe.call(ze),rt=Ct._,ct=qe("^"+Qe.call(et).replace(Oe,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),pt=wt?t.Buffer:o,lt=t.Symbol,ut=t.Uint8Array,mt=pt?pt.allocUnsafe:o,vt=wn(ze.getPrototypeOf,ze),bt=ze.create,Ot=$e.propertyIsEnumerable,Tt=Ze.splice,It=lt?lt.isConcatSpreadable:o,Pt=lt?lt.iterator:o,qt=lt?lt.toStringTag:o,Wt=function(){try{var e=ia(ze,"defineProperty");return e({},"",{}),e}catch(t){}}(),nn=t.clearTimeout!==Ct.clearTimeout&&t.clearTimeout,An=De&&De.now!==Ct.Date.now&&De.now,jn=t.setTimeout!==Ct.setTimeout&&t.setTimeout,Rn=Ge.ceil,Fn=Ge.floor,Un=ze.getOwnPropertySymbols,kn=pt?pt.isBuffer:o,Hn=t.isFinite,Vn=Ze.join,Yn=wn(ze.keys,ze),Gn=Ge.max,zn=Ge.min,qn=De.now,Wn=t.parseInt,Xn=Ge.random,Zn=Ze.reverse,Kn=ia(t,"DataView"),$n=ia(t,"Map"),Jn=ia(t,"Promise"),Qn=ia(t,"Set"),eo=ia(t,"WeakMap"),to=ia(ze,"create"),no=eo&&new eo,oo={},io=ka(Kn),ro=ka($n),ao=ka(Jn),so=ka(Qn),co=ka(eo),po=lt?lt.prototype:o,lo=po?po.valueOf:o,uo=po?po.toString:o;function yo(e){if(ic(e)&&!Ws(e)&&!(e instanceof go)){if(e instanceof mo)return e;if(et.call(e,"__wrapped__"))return Va(e)}return new mo(e)}var fo=function(){function e(){}return function(t){if(!oc(t))return{};if(bt)return bt(t);e.prototype=t;var n=new e;return e.prototype=o,n}}();function ho(){}function mo(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=o}function go(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=A,this.__views__=[]}function _o(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function vo(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function bo(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var o=e[t];this.set(o[0],o[1])}}function Co(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new bo;++t<n;)this.add(e[t])}function Oo(e){var t=this.__data__=new vo(e);this.size=t.size}function To(e,t){var n=Ws(e),o=!n&&qs(e),i=!n&&!o&&$s(e),r=!n&&!o&&!i&&dc(e),a=n||o||i||r,s=a?sn(e.length,We):[],c=s.length;for(var p in e)!t&&!et.call(e,p)||a&&("length"==p||i&&("offset"==p||"parent"==p)||r&&("buffer"==p||"byteLength"==p||"byteOffset"==p)||ga(p,c))||s.push(p);return s}function wo(e){var t=e.length;return t?e[xi(0,t-1)]:o}function Io(e,t){return Ba(hr(e),Ro(t,0,e.length))}function Po(e){return Ba(hr(e))}function So(e,t,n){(n===o||Ys(e[t],n))&&(n!==o||t in e)||Mo(e,t,n)}function Ao(e,t,n){var i=e[t];et.call(e,t)&&Ys(i,n)&&(n!==o||t in e)||Mo(e,t,n)}function Do(e,t){for(var n=e.length;n--;)if(Ys(e[n][0],t))return n;return-1}function xo(e,t,n,o){return Ho(e,function(e,i,r){t(o,e,n(e),r)}),o}function Eo(e,t){return e&&mr(t,jc(t),e)}function Lo(e,t){return e&&mr(t,Rc(t),e)}function Mo(e,t,n){"__proto__"==t&&Wt?Wt(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function jo(e,t){for(var n=-1,i=t.length,r=Ie(i),a=null==e;++n<i;)r[n]=a?o:Dc(e,t[n]);return r}function Ro(e,t,n){return e==e&&(n!==o&&(e=e<=n?e:n),t!==o&&(e=e>=t?e:t)),e}function No(e,t,n,i,r,a){var l,u=t&s,d=t&c,y=t&p;if(n&&(l=r?n(e,i,r,a):n(e)),l!==o)return l;if(!oc(e))return e;var f=Ws(e);if(f){if(l=da(e),!u)return hr(e,l)}else{var h=ca(e),m=h==U||h==k;if($s(e))return ar(e,u);if(h==G||h==L||m&&!r){if(l=d||m?{}:ya(e),!u)return d?_r(e,Lo(l,e)):gr(e,Eo(l,e))}else{if(!ht[h])return r?e:{};l=fa(e,h,u)}}a||(a=new Oo);var g=a.get(e);if(g)return g;a.set(e,l),pc(e)?e.forEach(function(o){l.add(No(o,t,n,o,e,a))}):rc(e)&&e.forEach(function(o,i){l.set(i,No(o,t,n,i,e,a))});var _=f?o:(y?d?$r:Kr:d?Rc:jc)(e);return Rt(_||e,function(o,i){_&&(o=e[i=o]),Ao(l,i,No(o,t,n,i,e,a))}),l}function Bo(e){var t=jc(e);return function(n){return Fo(n,e,t)}}function Fo(e,t,n){var i=n.length;if(null==e)return!i;for(e=ze(e);i--;){var r=n[i],a=t[r],s=e[r];if(s===o&&!(r in e)||!a(s))return!1}return!0}function Uo(e,t,n){if("function"!=typeof e)throw new Xe(r);return Ma(function(){e.apply(o,n)},t)}function ko(e,t,n,o){var r=-1,a=Ut,s=!0,c=e.length,p=[],l=t.length;if(!c)return p;n&&(t=Ht(t,ln(n))),o?(a=kt,s=!1):t.length>=i&&(a=dn,s=!1,t=new Co(t));e:for(;++r<c;){var u=e[r],d=null==n?u:n(u);if(u=o||0!==u?u:0,s&&d==d){for(var y=l;y--;)if(t[y]===d)continue e;p.push(u)}else a(t,d,o)||p.push(u)}return p}yo.templateSettings={escape:me,evaluate:ge,interpolate:_e,variable:"",imports:{_:yo}},yo.prototype=ho.prototype,yo.prototype.constructor=yo,mo.prototype=fo(ho.prototype),mo.prototype.constructor=mo,go.prototype=fo(ho.prototype),go.prototype.constructor=go,_o.prototype.clear=function(){this.__data__=to?to(null):{},this.size=0},_o.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},_o.prototype.get=function(e){var t=this.__data__;if(to){var n=t[e];return"__lodash_hash_undefined__"===n?o:n}return et.call(t,e)?t[e]:o},_o.prototype.has=function(e){var t=this.__data__;return to?t[e]!==o:et.call(t,e)},_o.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=to&&t===o?"__lodash_hash_undefined__":t,this},vo.prototype.clear=function(){this.__data__=[],this.size=0},vo.prototype.delete=function(e){var t=this.__data__,n=Do(t,e);return!(n<0||(n==t.length-1?t.pop():Tt.call(t,n,1),--this.size,0))},vo.prototype.get=function(e){var t=this.__data__,n=Do(t,e);return n<0?o:t[n][1]},vo.prototype.has=function(e){return Do(this.__data__,e)>-1},vo.prototype.set=function(e,t){var n=this.__data__,o=Do(n,e);return o<0?(++this.size,n.push([e,t])):n[o][1]=t,this},bo.prototype.clear=function(){this.size=0,this.__data__={hash:new _o,map:new($n||vo),string:new _o}},bo.prototype.delete=function(e){var t=na(this,e).delete(e);return this.size-=t?1:0,t},bo.prototype.get=function(e){return na(this,e).get(e)},bo.prototype.has=function(e){return na(this,e).has(e)},bo.prototype.set=function(e,t){var n=na(this,e),o=n.size;return n.set(e,t),this.size+=n.size==o?0:1,this},Co.prototype.add=Co.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Co.prototype.has=function(e){return this.__data__.has(e)},Oo.prototype.clear=function(){this.__data__=new vo,this.size=0},Oo.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},Oo.prototype.get=function(e){return this.__data__.get(e)},Oo.prototype.has=function(e){return this.__data__.has(e)},Oo.prototype.set=function(e,t){var n=this.__data__;if(n instanceof vo){var o=n.__data__;if(!$n||o.length<i-1)return o.push([e,t]),this.size=++n.size,this;n=this.__data__=new bo(o)}return n.set(e,t),this.size=n.size,this};var Ho=Cr(Ko),Vo=Cr($o,!0);function Yo(e,t){var n=!0;return Ho(e,function(e,o,i){return n=!!t(e,o,i)}),n}function Go(e,t,n){for(var i=-1,r=e.length;++i<r;){var a=e[i],s=t(a);if(null!=s&&(c===o?s==s&&!uc(s):n(s,c)))var c=s,p=a}return p}function zo(e,t,n,i){var r=e.length;for((n=gc(n))<0&&(n=-n>r?0:r+n),(i=i===o||i>r?r:gc(i))<0&&(i+=r),i=n>i?0:_c(i);n<i;)e[n++]=t;return e}function qo(e,t){var n=[];return Ho(e,function(e,o,i){t(e,o,i)&&n.push(e)}),n}function Wo(e,t,n,o,i){var r=-1,a=e.length;for(n||(n=ma),i||(i=[]);++r<a;){var s=e[r];t>0&&n(s)?t>1?Wo(s,t-1,n,o,i):Vt(i,s):o||(i[i.length]=s)}return i}var Xo=Or(),Zo=Or(!0);function Ko(e,t){return e&&Xo(e,t,jc)}function $o(e,t){return e&&Zo(e,t,jc)}function Jo(e,t){return Ft(t,function(t){return ec(e[t])})}function Qo(e,t){for(var n=0,i=(t=nr(t,e)).length;null!=e&&n<i;)e=e[Ua(t[n++])];return n&&n==i?e:o}function ei(e,t,n){var o=t(e);return Ws(e)?o:Vt(o,n(e))}function ti(e){return null==e?e===o?K:Y:qt&&qt in ze(e)?ra(e):Sa(e)}function ni(e,t){return e>t}function oi(e,t){return null!=e&&et.call(e,t)}function ii(e,t){return null!=e&&t in ze(e)}function ri(e,t,n){return e>=zn(t,n)&&e<Gn(t,n)}function ai(e,t,n){for(var i=n?kt:Ut,r=e[0].length,a=e.length,s=a,c=Ie(a),p=1/0,l=[];s--;){var u=e[s];s&&t&&(u=Ht(u,ln(t))),p=zn(u.length,p),c[s]=!n&&(t||r>=120&&u.length>=120)?new Co(s&&u):o}u=e[0];var d=-1,y=c[0];e:for(;++d<r&&l.length<p;){var f=u[d],h=t?t(f):f;if(f=n||0!==f?f:0,!(y?dn(y,h):i(l,h,n))){for(s=a;--s;){var m=c[s];if(!(m?dn(m,h):i(e[s],h,n)))continue e}y&&y.push(h),l.push(f)}}return l}function si(e,t,n,o){return Ko(e,function(e,i,r){t(o,n(e),i,r)}),o}function ci(e,t,n){var i=null==(e=Da(e,t=nr(t,e)))?e:e[Ua(Qa(t))];return null==i?o:Mt(i,e,n)}function pi(e){return ic(e)&&ti(e)==L}function li(e,t,n,o,i){return e===t||(null==e||null==t||!ic(e)&&!ic(t)?e!=e&&t!=t:ui(e,t,n,o,li,i))}function ui(e,t,n,o,i,r){var a=Ws(e),s=Ws(t),c=a?M:ca(e),p=s?M:ca(t),u=(c=c==L?G:c)==G,d=(p=p==L?G:p)==G,y=c==p;if(y&&$s(e)){if(!$s(t))return!1;a=!0,u=!1}if(y&&!u)return r||(r=new Oo),a||dc(e)?qr(e,t,n,o,i,r):Wr(e,t,c,n,o,i,r);if(!(n&l)){var f=u&&et.call(e,"__wrapped__"),h=d&&et.call(t,"__wrapped__");if(f||h){var m=f?e.value():e,g=h?t.value():t;return r||(r=new Oo),i(m,g,n,o,r)}}return!!y&&(r||(r=new Oo),Xr(e,t,n,o,i,r))}function di(e,t,n,i){var r=n.length,a=r,s=!i;if(null==e)return!a;for(e=ze(e);r--;){var c=n[r];if(s&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++r<a;){var p=(c=n[r])[0],d=e[p],y=c[1];if(s&&c[2]){if(d===o&&!(p in e))return!1}else{var f=new Oo;if(i)var h=i(d,y,p,e,t,f);if(!(h===o?li(y,d,l|u,i,f):h))return!1}}return!0}function yi(e){return!(!oc(e)||(t=e,nt&&nt in t))&&(ec(e)?ct:Ne).test(ka(e));var t}function fi(e){return"function"==typeof e?e:null==e?ap:"object"==typeof e?Ws(e)?bi(e[0],e[1]):vi(e):hp(e)}function hi(e){if(!Oa(e))return Yn(e);var t=[];for(var n in ze(e))et.call(e,n)&&"constructor"!=n&&t.push(n);return t}function mi(e){if(!oc(e))return Pa(e);var t=Oa(e),n=[];for(var o in e)("constructor"!=o||!t&&et.call(e,o))&&n.push(o);return n}function gi(e,t){return e<t}function _i(e,t){var n=-1,o=Zs(e)?Ie(e.length):[];return Ho(e,function(e,i,r){o[++n]=t(e,i,r)}),o}function vi(e){var t=oa(e);return 1==t.length&&t[0][2]?wa(t[0][0],t[0][1]):function(n){return n===e||di(n,e,t)}}function bi(e,t){return va(e)&&Ta(t)?wa(Ua(e),t):function(n){var i=Dc(n,e);return i===o&&i===t?xc(n,e):li(t,i,l|u)}}function Ci(e,t,n,i,r){e!==t&&Xo(t,function(a,s){if(r||(r=new Oo),oc(a))Oi(e,t,s,n,Ci,i,r);else{var c=i?i(Ea(e,s),a,s+"",e,t,r):o;c===o&&(c=a),So(e,s,c)}},Rc)}function Oi(e,t,n,i,r,a,s){var c=Ea(e,n),p=Ea(t,n),l=s.get(p);if(l)So(e,n,l);else{var u=a?a(c,p,n+"",e,t,s):o,d=u===o;if(d){var y=Ws(p),f=!y&&$s(p),h=!y&&!f&&dc(p);u=p,y||f||h?Ws(c)?u=c:Ks(c)?u=hr(c):f?(d=!1,u=ar(p,!0)):h?(d=!1,u=lr(p,!0)):u=[]:sc(p)||qs(p)?(u=c,qs(c)?u=bc(c):oc(c)&&!ec(c)||(u=ya(p))):d=!1}d&&(s.set(p,u),r(u,p,i,a,s),s.delete(p)),So(e,n,u)}}function Ti(e,t){var n=e.length;if(n)return ga(t+=t<0?n:0,n)?e[t]:o}function wi(e,t,n){t=t.length?Ht(t,function(e){return Ws(e)?function(t){return Qo(t,1===e.length?e[0]:e)}:e}):[ap];var o=-1;return t=Ht(t,ln(ta())),rn(_i(e,function(e){return{criteria:Ht(t,function(t){return t(e)}),index:++o,value:e}}),function(e,t){return dr(e,t,n)})}function Ii(e,t){return Pi(e,t,function(t,n){return xc(e,n)})}function Pi(e,t,n){for(var o=-1,i=t.length,r={};++o<i;){var a=t[o],s=Qo(e,a);n(s,a)&&Ni(r,nr(a,e),s)}return r}function Si(e){return function(t){return Qo(t,e)}}function Ai(e,t,n,o){var i=o?Jt:$t,r=-1,a=t.length,s=e;for(e===t&&(t=hr(t)),n&&(s=Ht(e,ln(n)));++r<a;)for(var c=0,p=t[r],l=n?n(p):p;(c=i(s,l,c,o))>-1;)s!==e&&Tt.call(s,c,1),Tt.call(e,c,1);return e}function Di(e,t){for(var n=e?t.length:0,o=n-1;n--;){var i=t[n];if(n==o||i!==r){var r=i;ga(i)?Tt.call(e,i,1):Xi(e,i)}}return e}function xi(e,t){return e+Fn(Xn()*(t-e+1))}function Ei(e,t,n,o){for(var i=-1,r=Gn(Rn((t-e)/(n||1)),0),a=Ie(r);r--;)a[o?r:++i]=e,e+=n;return a}function Li(e,t){var n="";if(!e||t<1||t>I)return n;do{t%2&&(n+=e),(t=Fn(t/2))&&(e+=e)}while(t);return n}function Mi(e,t){return ja(Aa(e,t,ap),e+"")}function ji(e){return wo(Yc(e))}function Ri(e,t){var n=Yc(e);return Ba(n,Ro(t,0,n.length))}function Ni(e,t,n,i){if(!oc(e))return e;for(var r=-1,a=(t=nr(t,e)).length,s=a-1,c=e;null!=c&&++r<a;){var p=Ua(t[r]),l=n;if("__proto__"===p||"constructor"===p||"prototype"===p)return e;if(r!=s){var u=c[p];(l=i?i(u,p,c):o)===o&&(l=oc(u)?u:ga(t[r+1])?[]:{})}Ao(c,p,l),c=c[p]}return e}var Bi=no?function(e,t){return no.set(e,t),e}:ap,Fi=Wt?function(e,t){return Wt(e,"toString",{configurable:!0,enumerable:!1,value:op(t),writable:!0})}:ap;function Ui(e){return Ba(Yc(e))}function ki(e,t,n){var o=-1,i=e.length;t<0&&(t=-t>i?0:i+t),(n=n>i?i:n)<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var r=Ie(i);++o<i;)r[o]=e[o+t];return r}function Hi(e,t){var n;return Ho(e,function(e,o,i){return!(n=t(e,o,i))}),!!n}function Vi(e,t,n){var o=0,i=null==e?o:e.length;if("number"==typeof t&&t==t&&i<=x){for(;o<i;){var r=o+i>>>1,a=e[r];null!==a&&!uc(a)&&(n?a<=t:a<t)?o=r+1:i=r}return i}return Yi(e,t,ap,n)}function Yi(e,t,n,i){var r=0,a=null==e?0:e.length;if(0===a)return 0;for(var s=(t=n(t))!=t,c=null===t,p=uc(t),l=t===o;r<a;){var u=Fn((r+a)/2),d=n(e[u]),y=d!==o,f=null===d,h=d==d,m=uc(d);if(s)var g=i||h;else g=l?h&&(i||y):c?h&&y&&(i||!f):p?h&&y&&!f&&(i||!m):!f&&!m&&(i?d<=t:d<t);g?r=u+1:a=u}return zn(a,D)}function Gi(e,t){for(var n=-1,o=e.length,i=0,r=[];++n<o;){var a=e[n],s=t?t(a):a;if(!n||!Ys(s,c)){var c=s;r[i++]=0===a?0:a}}return r}function zi(e){return"number"==typeof e?e:uc(e)?S:+e}function qi(e){if("string"==typeof e)return e;if(Ws(e))return Ht(e,qi)+"";if(uc(e))return uo?uo.call(e):"";var t=e+"";return"0"==t&&1/e==-w?"-0":t}function Wi(e,t,n){var o=-1,r=Ut,a=e.length,s=!0,c=[],p=c;if(n)s=!1,r=kt;else if(a>=i){var l=t?null:kr(e);if(l)return Pn(l);s=!1,r=dn,p=new Co}else p=t?[]:c;e:for(;++o<a;){var u=e[o],d=t?t(u):u;if(u=n||0!==u?u:0,s&&d==d){for(var y=p.length;y--;)if(p[y]===d)continue e;t&&p.push(d),c.push(u)}else r(p,d,n)||(p!==c&&p.push(d),c.push(u))}return c}function Xi(e,t){return null==(e=Da(e,t=nr(t,e)))||delete e[Ua(Qa(t))]}function Zi(e,t,n,o){return Ni(e,t,n(Qo(e,t)),o)}function Ki(e,t,n,o){for(var i=e.length,r=o?i:-1;(o?r--:++r<i)&&t(e[r],r,e););return n?ki(e,o?0:r,o?r+1:i):ki(e,o?r+1:0,o?i:r)}function $i(e,t){var n=e;return n instanceof go&&(n=n.value()),Yt(t,function(e,t){return t.func.apply(t.thisArg,Vt([e],t.args))},n)}function Ji(e,t,n){var o=e.length;if(o<2)return o?Wi(e[0]):[];for(var i=-1,r=Ie(o);++i<o;)for(var a=e[i],s=-1;++s<o;)s!=i&&(r[i]=ko(r[i]||a,e[s],t,n));return Wi(Wo(r,1),t,n)}function Qi(e,t,n){for(var i=-1,r=e.length,a=t.length,s={};++i<r;){var c=i<a?t[i]:o;n(s,e[i],c)}return s}function er(e){return Ks(e)?e:[]}function tr(e){return"function"==typeof e?e:ap}function nr(e,t){return Ws(e)?e:va(e,t)?[e]:Fa(Cc(e))}var or=Mi;function ir(e,t,n){var i=e.length;return n=n===o?i:n,!t&&n>=i?e:ki(e,t,n)}var rr=nn||function(e){return Ct.clearTimeout(e)};function ar(e,t){if(t)return e.slice();var n=e.length,o=mt?mt(n):new e.constructor(n);return e.copy(o),o}function sr(e){var t=new e.constructor(e.byteLength);return new ut(t).set(new ut(e)),t}function cr(e,t){var n=t?sr(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)}function pr(e){var t=new e.constructor(e.source,Me.exec(e));return t.lastIndex=e.lastIndex,t}function lr(e,t){var n=t?sr(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)}function ur(e,t){if(e!==t){var n=e!==o,i=null===e,r=e==e,a=uc(e),s=t!==o,c=null===t,p=t==t,l=uc(t);if(!c&&!l&&!a&&e>t||a&&s&&p&&!c&&!l||i&&s&&p||!n&&p||!r)return 1;if(!i&&!a&&!l&&e<t||l&&n&&r&&!i&&!a||c&&n&&r||!s&&r||!p)return-1}return 0}function dr(e,t,n){for(var o=-1,i=e.criteria,r=t.criteria,a=i.length,s=n.length;++o<a;){var c=ur(i[o],r[o]);if(c)return o>=s?c:c*("desc"==n[o]?-1:1)}return e.index-t.index}function yr(e,t,n,o){for(var i=-1,r=e.length,a=n.length,s=-1,c=t.length,p=Gn(r-a,0),l=Ie(c+p),u=!o;++s<c;)l[s]=t[s];for(;++i<a;)(u||i<r)&&(l[n[i]]=e[i]);for(;p--;)l[s++]=e[i++];return l}function fr(e,t,n,o){for(var i=-1,r=e.length,a=-1,s=n.length,c=-1,p=t.length,l=Gn(r-s,0),u=Ie(l+p),d=!o;++i<l;)u[i]=e[i];for(var y=i;++c<p;)u[y+c]=t[c];for(;++a<s;)(d||i<r)&&(u[y+n[a]]=e[i++]);return u}function hr(e,t){var n=-1,o=e.length;for(t||(t=Ie(o));++n<o;)t[n]=e[n];return t}function mr(e,t,n,i){var r=!n;n||(n={});for(var a=-1,s=t.length;++a<s;){var c=t[a],p=i?i(n[c],e[c],c,n,e):o;p===o&&(p=e[c]),r?Mo(n,c,p):Ao(n,c,p)}return n}function gr(e,t){return mr(e,aa(e),t)}function _r(e,t){return mr(e,sa(e),t)}function vr(e,t){return function(n,o){var i=Ws(n)?jt:xo,r=t?t():{};return i(n,e,ta(o,2),r)}}function br(e){return Mi(function(t,n){var i=-1,r=n.length,a=r>1?n[r-1]:o,s=r>2?n[2]:o;for(a=e.length>3&&"function"==typeof a?(r--,a):o,s&&_a(n[0],n[1],s)&&(a=r<3?o:a,r=1),t=ze(t);++i<r;){var c=n[i];c&&e(t,c,i,a)}return t})}function Cr(e,t){return function(n,o){if(null==n)return n;if(!Zs(n))return e(n,o);for(var i=n.length,r=t?i:-1,a=ze(n);(t?r--:++r<i)&&!1!==o(a[r],r,a););return n}}function Or(e){return function(t,n,o){for(var i=-1,r=ze(t),a=o(t),s=a.length;s--;){var c=a[e?s:++i];if(!1===n(r[c],c,r))break}return t}}function Tr(e,t,n){var o=t&d,i=Pr(e);return function t(){return(this&&this!==Ct&&this instanceof t?i:e).apply(o?n:this,arguments)}}function wr(e){return function(t){var n=bn(t=Cc(t))?En(t):o,i=n?n[0]:t.charAt(0),r=n?ir(n,1).join(""):t.slice(1);return i[e]()+r}}function Ir(e){return function(t){return Yt(ep(qc(t).replace(at,"")),e,"")}}function Pr(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=fo(e.prototype),o=e.apply(n,t);return oc(o)?o:n}}function Sr(e,t,n){var i=Pr(e);return function r(){for(var a=arguments.length,s=Ie(a),c=a,p=ea(r);c--;)s[c]=arguments[c];var l=a<3&&s[0]!==p&&s[a-1]!==p?[]:In(s,p);return(a-=l.length)<n?Fr(e,t,xr,r.placeholder,o,s,l,o,o,n-a):Mt(this&&this!==Ct&&this instanceof r?i:e,this,s)}}function Ar(e){return function(t,n,i){var r=ze(t);if(!Zs(t)){var a=ta(n,3);t=jc(t),n=function(e){return a(r[e],e,r)}}var s=e(t,n,i);return s>-1?r[a?t[s]:s]:o}}function Dr(e){return Zr(function(t){var n=t.length,i=n,a=mo.prototype.thru;for(e&&t.reverse();i--;){var s=t[i];if("function"!=typeof s)throw new Xe(r);if(a&&!c&&"wrapper"==Qr(s))var c=new mo([],!0)}for(i=c?i:n;++i<n;){var p=Qr(s=t[i]),l="wrapper"==p?Jr(s):o;c=l&&ba(l[0])&&l[1]==(v|h|g|b)&&!l[4].length&&1==l[9]?c[Qr(l[0])].apply(c,l[3]):1==s.length&&ba(s)?c[p]():c.thru(s)}return function(){var e=arguments,o=e[0];if(c&&1==e.length&&Ws(o))return c.plant(o).value();for(var i=0,r=n?t[i].apply(this,e):o;++i<n;)r=t[i].call(this,r);return r}})}function xr(e,t,n,i,r,a,s,c,p,l){var u=t&v,f=t&d,g=t&y,_=t&(h|m),b=t&C,O=g?o:Pr(e);return function o(){for(var d=arguments.length,y=Ie(d),h=d;h--;)y[h]=arguments[h];if(_)var m=ea(o),v=hn(y,m);if(i&&(y=yr(y,i,r,_)),a&&(y=fr(y,a,s,_)),d-=v,_&&d<l){var C=In(y,m);return Fr(e,t,xr,o.placeholder,n,y,C,c,p,l-d)}var T=f?n:this,w=g?T[e]:e;return d=y.length,c?y=xa(y,c):b&&d>1&&y.reverse(),u&&p<d&&(y.length=p),this&&this!==Ct&&this instanceof o&&(w=O||Pr(w)),w.apply(T,y)}}function Er(e,t){return function(n,o){return si(n,e,t(o),{})}}function Lr(e,t){return function(n,i){var r;if(n===o&&i===o)return t;if(n!==o&&(r=n),i!==o){if(r===o)return i;"string"==typeof n||"string"==typeof i?(n=qi(n),i=qi(i)):(n=zi(n),i=zi(i)),r=e(n,i)}return r}}function Mr(e){return Zr(function(t){return t=Ht(t,ln(ta())),Mi(function(n){var o=this;return e(t,function(e){return Mt(e,o,n)})})})}function jr(e,t){var n=(t=t===o?" ":qi(t)).length;if(n<2)return n?Li(t,e):t;var i=Li(t,Rn(e/xn(t)));return bn(t)?ir(En(i),0,e).join(""):i.slice(0,e)}function Rr(e,t,n,o){var i=t&d,r=Pr(e);return function t(){for(var a=-1,s=arguments.length,c=-1,p=o.length,l=Ie(p+s),u=this&&this!==Ct&&this instanceof t?r:e;++c<p;)l[c]=o[c];for(;s--;)l[c++]=arguments[++a];return Mt(u,i?n:this,l)}}function Nr(e){return function(t,n,i){return i&&"number"!=typeof i&&_a(t,n,i)&&(n=i=o),t=mc(t),n===o?(n=t,t=0):n=mc(n),Ei(t,n,i=i===o?t<n?1:-1:mc(i),e)}}function Br(e){return function(t,n){return"string"==typeof t&&"string"==typeof n||(t=vc(t),n=vc(n)),e(t,n)}}function Fr(e,t,n,i,r,a,s,c,p,l){var u=t&h;t|=u?g:_,(t&=~(u?_:g))&f||(t&=~(d|y));var m=[e,t,r,u?a:o,u?s:o,u?o:a,u?o:s,c,p,l],v=n.apply(o,m);return ba(e)&&La(v,m),v.placeholder=i,Ra(v,e,t)}function Ur(e){var t=Ge[e];return function(e,n){if(e=vc(e),(n=null==n?0:zn(gc(n),292))&&Hn(e)){var o=(Cc(e)+"e").split("e");return+((o=(Cc(t(o[0]+"e"+(+o[1]+n)))+"e").split("e"))[0]+"e"+(+o[1]-n))}return t(e)}}var kr=Qn&&1/Pn(new Qn([,-0]))[1]==w?function(e){return new Qn(e)}:up;function Hr(e){return function(t){var n=ca(t);return n==H?Tn(t):n==W?Sn(t):cn(t,e(t))}}function Vr(e,t,n,i,a,s,c,p){var l=t&y;if(!l&&"function"!=typeof e)throw new Xe(r);var u=i?i.length:0;if(u||(t&=~(g|_),i=a=o),c=c===o?c:Gn(gc(c),0),p=p===o?p:gc(p),u-=a?a.length:0,t&_){var f=i,v=a;i=a=o}var b=l?o:Jr(e),C=[e,t,n,i,a,f,v,s,c,p];if(b&&Ia(C,b),e=C[0],t=C[1],n=C[2],i=C[3],a=C[4],!(p=C[9]=C[9]===o?l?0:e.length:Gn(C[9]-u,0))&&t&(h|m)&&(t&=~(h|m)),t&&t!=d)O=t==h||t==m?Sr(e,t,p):t!=g&&t!=(d|g)||a.length?xr.apply(o,C):Rr(e,t,n,i);else var O=Tr(e,t,n);return Ra((b?Bi:La)(O,C),e,t)}function Yr(e,t,n,i){return e===o||Ys(e,$e[n])&&!et.call(i,n)?t:e}function Gr(e,t,n,i,r,a){return oc(e)&&oc(t)&&(a.set(t,e),Ci(e,t,o,Gr,a),a.delete(t)),e}function zr(e){return sc(e)?o:e}function qr(e,t,n,i,r,a){var s=n&l,c=e.length,p=t.length;if(c!=p&&!(s&&p>c))return!1;var d=a.get(e),y=a.get(t);if(d&&y)return d==t&&y==e;var f=-1,h=!0,m=n&u?new Co:o;for(a.set(e,t),a.set(t,e);++f<c;){var g=e[f],_=t[f];if(i)var v=s?i(_,g,f,t,e,a):i(g,_,f,e,t,a);if(v!==o){if(v)continue;h=!1;break}if(m){if(!zt(t,function(e,t){if(!dn(m,t)&&(g===e||r(g,e,n,i,a)))return m.push(t)})){h=!1;break}}else if(g!==_&&!r(g,_,n,i,a)){h=!1;break}}return a.delete(e),a.delete(t),h}function Wr(e,t,n,o,i,r,a){switch(n){case Q:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case J:return!(e.byteLength!=t.byteLength||!r(new ut(e),new ut(t)));case R:case N:case V:return Ys(+e,+t);case F:return e.name==t.name&&e.message==t.message;case q:case X:return e==t+"";case H:var s=Tn;case W:var c=o&l;if(s||(s=Pn),e.size!=t.size&&!c)return!1;var p=a.get(e);if(p)return p==t;o|=u,a.set(e,t);var d=qr(s(e),s(t),o,i,r,a);return a.delete(e),d;case Z:if(lo)return lo.call(e)==lo.call(t)}return!1}function Xr(e,t,n,i,r,a){var s=n&l,c=Kr(e),p=c.length;if(p!=Kr(t).length&&!s)return!1;for(var u=p;u--;){var d=c[u];if(!(s?d in t:et.call(t,d)))return!1}var y=a.get(e),f=a.get(t);if(y&&f)return y==t&&f==e;var h=!0;a.set(e,t),a.set(t,e);for(var m=s;++u<p;){var g=e[d=c[u]],_=t[d];if(i)var v=s?i(_,g,d,t,e,a):i(g,_,d,e,t,a);if(!(v===o?g===_||r(g,_,n,i,a):v)){h=!1;break}m||(m="constructor"==d)}if(h&&!m){var b=e.constructor,C=t.constructor;b!=C&&"constructor"in e&&"constructor"in t&&!("function"==typeof b&&b instanceof b&&"function"==typeof C&&C instanceof C)&&(h=!1)}return a.delete(e),a.delete(t),h}function Zr(e){return ja(Aa(e,o,Xa),e+"")}function Kr(e){return ei(e,jc,aa)}function $r(e){return ei(e,Rc,sa)}var Jr=no?function(e){return no.get(e)}:up;function Qr(e){for(var t=e.name+"",n=oo[t],o=et.call(oo,t)?n.length:0;o--;){var i=n[o],r=i.func;if(null==r||r==e)return i.name}return t}function ea(e){return(et.call(yo,"placeholder")?yo:e).placeholder}function ta(){var e=yo.iteratee||sp;return e=e===sp?fi:e,arguments.length?e(arguments[0],arguments[1]):e}function na(e,t){var n,o,i=e.__data__;return("string"==(o=typeof(n=t))||"number"==o||"symbol"==o||"boolean"==o?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map}function oa(e){for(var t=jc(e),n=t.length;n--;){var o=t[n],i=e[o];t[n]=[o,i,Ta(i)]}return t}function ia(e,t){var n=vn(e,t);return yi(n)?n:o}function ra(e){var t=et.call(e,qt),n=e[qt];try{e[qt]=o;var i=!0}catch(a){}var r=ot.call(e);return i&&(t?e[qt]=n:delete e[qt]),r}var aa=Un?function(e){return null==e?[]:(e=ze(e),Ft(Un(e),function(t){return Ot.call(e,t)}))}:_p,sa=Un?function(e){for(var t=[];e;)Vt(t,aa(e)),e=vt(e);return t}:_p,ca=ti;function pa(e,t,n){for(var o=-1,i=n.length;++o<i;){var r=n[o],a=r.size;switch(r.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=zn(t,e+a);break;case"takeRight":e=Gn(e,t-a)}}return{start:e,end:t}}function la(e){var t=e.match(Se);return t?t[1].split(Ae):[]}function ua(e,t,n){for(var o=-1,i=(t=nr(t,e)).length,r=!1;++o<i;){var a=Ua(t[o]);if(!(r=null!=e&&n(e,a)))break;e=e[a]}return r||++o!=i?r:!!(i=null==e?0:e.length)&&nc(i)&&ga(a,i)&&(Ws(e)||qs(e))}function da(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&et.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function ya(e){return"function"!=typeof e.constructor||Oa(e)?{}:fo(vt(e))}function fa(e,t,n){var o,i=e.constructor;switch(t){case J:return sr(e);case R:case N:return new i(+e);case Q:return cr(e,n);case ee:case te:case ne:case oe:case ie:case re:case ae:case se:case ce:return lr(e,n);case H:return new i;case V:case X:return new i(e);case q:return pr(e);case W:return new i;case Z:return o=e,lo?ze(lo.call(o)):{}}}function ha(e,t){var n=t.length;if(!n)return e;var o=n-1;return t[o]=(n>1?"& ":"")+t[o],t=t.join(n>2?", ":" "),e.replace(Pe,"{\n/* [wrapped with "+t+"] */\n")}function ma(e){return Ws(e)||qs(e)||!!(It&&e&&e[It])}function ga(e,t){var n=typeof e;return!!(t=null==t?I:t)&&("number"==n||"symbol"!=n&&Fe.test(e))&&e>-1&&e%1==0&&e<t}function _a(e,t,n){if(!oc(n))return!1;var o=typeof t;return!!("number"==o?Zs(n)&&ga(t,n.length):"string"==o&&t in n)&&Ys(n[t],e)}function va(e,t){if(Ws(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!uc(e))||be.test(e)||!ve.test(e)||null!=t&&e in ze(t)}function ba(e){var t=Qr(e),n=yo[t];if("function"!=typeof n||!(t in go.prototype))return!1;if(e===n)return!0;var o=Jr(n);return!!o&&e===o[0]}(Kn&&ca(new Kn(new ArrayBuffer(1)))!=Q||$n&&ca(new $n)!=H||Jn&&"[object Promise]"!=ca(Jn.resolve())||Qn&&ca(new Qn)!=W||eo&&ca(new eo)!=$)&&(ca=function(e){var t=ti(e),n=t==G?e.constructor:o,i=n?ka(n):"";if(i)switch(i){case io:return Q;case ro:return H;case ao:return"[object Promise]";case so:return W;case co:return $}return t});var Ca=Je?ec:vp;function Oa(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||$e)}function Ta(e){return e==e&&!oc(e)}function wa(e,t){return function(n){return null!=n&&n[e]===t&&(t!==o||e in ze(n))}}function Ia(e,t){var n=e[1],o=t[1],i=n|o,r=i<(d|y|v),s=o==v&&n==h||o==v&&n==b&&e[7].length<=t[8]||o==(v|b)&&t[7].length<=t[8]&&n==h;if(!r&&!s)return e;o&d&&(e[2]=t[2],i|=n&d?0:f);var c=t[3];if(c){var p=e[3];e[3]=p?yr(p,c,t[4]):c,e[4]=p?In(e[3],a):t[4]}return(c=t[5])&&(p=e[5],e[5]=p?fr(p,c,t[6]):c,e[6]=p?In(e[5],a):t[6]),(c=t[7])&&(e[7]=c),o&v&&(e[8]=null==e[8]?t[8]:zn(e[8],t[8])),null==e[9]&&(e[9]=t[9]),e[0]=t[0],e[1]=i,e}function Pa(e){var t=[];if(null!=e)for(var n in ze(e))t.push(n);return t}function Sa(e){return ot.call(e)}function Aa(e,t,n){return t=Gn(t===o?e.length-1:t,0),function(){for(var o=arguments,i=-1,r=Gn(o.length-t,0),a=Ie(r);++i<r;)a[i]=o[t+i];i=-1;for(var s=Ie(t+1);++i<t;)s[i]=o[i];return s[t]=n(a),Mt(e,this,s)}}function Da(e,t){return t.length<2?e:Qo(e,ki(t,0,-1))}function xa(e,t){for(var n=e.length,i=zn(t.length,n),r=hr(e);i--;){var a=t[i];e[i]=ga(a,n)?r[a]:o}return e}function Ea(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var La=Na(Bi),Ma=jn||function(e,t){return Ct.setTimeout(e,t)},ja=Na(Fi);function Ra(e,t,n){var o=t+"";return ja(e,ha(o,Ha(la(o),n)))}function Na(e){var t=0,n=0;return function(){var i=qn(),r=T-(i-n);if(n=i,r>0){if(++t>=O)return arguments[0]}else t=0;return e.apply(o,arguments)}}function Ba(e,t){var n=-1,i=e.length,r=i-1;for(t=t===o?i:t;++n<t;){var a=xi(n,r),s=e[a];e[a]=e[n],e[n]=s}return e.length=t,e}var Fa=function(){var e=Bs(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ce,function(e,n,o,i){t.push(o?i.replace(Ee,"$1"):n||e)}),t},function(e){return 500===t.size&&t.clear(),e}),t=e.cache;return e}();function Ua(e){if("string"==typeof e||uc(e))return e;var t=e+"";return"0"==t&&1/e==-w?"-0":t}function ka(e){if(null!=e){try{return Qe.call(e)}catch(t){}try{return e+""}catch(t){}}return""}function Ha(e,t){return Rt(E,function(n){var o="_."+n[0];t&n[1]&&!Ut(e,o)&&e.push(o)}),e.sort()}function Va(e){if(e instanceof go)return e.clone();var t=new mo(e.__wrapped__,e.__chain__);return t.__actions__=hr(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var Ya=Mi(function(e,t){return Ks(e)?ko(e,Wo(t,1,Ks,!0)):[]}),Ga=Mi(function(e,t){var n=Qa(t);return Ks(n)&&(n=o),Ks(e)?ko(e,Wo(t,1,Ks,!0),ta(n,2)):[]}),za=Mi(function(e,t){var n=Qa(t);return Ks(n)&&(n=o),Ks(e)?ko(e,Wo(t,1,Ks,!0),o,n):[]});function qa(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var i=null==n?0:gc(n);return i<0&&(i=Gn(o+i,0)),Kt(e,ta(t,3),i)}function Wa(e,t,n){var i=null==e?0:e.length;if(!i)return-1;var r=i-1;return n!==o&&(r=gc(n),r=n<0?Gn(i+r,0):zn(r,i-1)),Kt(e,ta(t,3),r,!0)}function Xa(e){return null!=e&&e.length?Wo(e,1):[]}function Za(e){return e&&e.length?e[0]:o}var Ka=Mi(function(e){var t=Ht(e,er);return t.length&&t[0]===e[0]?ai(t):[]}),$a=Mi(function(e){var t=Qa(e),n=Ht(e,er);return t===Qa(n)?t=o:n.pop(),n.length&&n[0]===e[0]?ai(n,ta(t,2)):[]}),Ja=Mi(function(e){var t=Qa(e),n=Ht(e,er);return(t="function"==typeof t?t:o)&&n.pop(),n.length&&n[0]===e[0]?ai(n,o,t):[]});function Qa(e){var t=null==e?0:e.length;return t?e[t-1]:o}var es=Mi(ts);function ts(e,t){return e&&e.length&&t&&t.length?Ai(e,t):e}var ns=Zr(function(e,t){var n=null==e?0:e.length,o=jo(e,t);return Di(e,Ht(t,function(e){return ga(e,n)?+e:e}).sort(ur)),o});function os(e){return null==e?e:Zn.call(e)}var is=Mi(function(e){return Wi(Wo(e,1,Ks,!0))}),rs=Mi(function(e){var t=Qa(e);return Ks(t)&&(t=o),Wi(Wo(e,1,Ks,!0),ta(t,2))}),as=Mi(function(e){var t=Qa(e);return t="function"==typeof t?t:o,Wi(Wo(e,1,Ks,!0),o,t)});function ss(e){if(!e||!e.length)return[];var t=0;return e=Ft(e,function(e){if(Ks(e))return t=Gn(e.length,t),!0}),sn(t,function(t){return Ht(e,tn(t))})}function cs(e,t){if(!e||!e.length)return[];var n=ss(e);return null==t?n:Ht(n,function(e){return Mt(t,o,e)})}var ps=Mi(function(e,t){return Ks(e)?ko(e,t):[]}),ls=Mi(function(e){return Ji(Ft(e,Ks))}),us=Mi(function(e){var t=Qa(e);return Ks(t)&&(t=o),Ji(Ft(e,Ks),ta(t,2))}),ds=Mi(function(e){var t=Qa(e);return t="function"==typeof t?t:o,Ji(Ft(e,Ks),o,t)}),ys=Mi(ss),fs=Mi(function(e){var t=e.length,n=t>1?e[t-1]:o;return n="function"==typeof n?(e.pop(),n):o,cs(e,n)});function hs(e){var t=yo(e);return t.__chain__=!0,t}function ms(e,t){return t(e)}var gs=Zr(function(e){var t=e.length,n=t?e[0]:0,i=this.__wrapped__,r=function(t){return jo(t,e)};return!(t>1||this.__actions__.length)&&i instanceof go&&ga(n)?((i=i.slice(n,+n+(t?1:0))).__actions__.push({func:ms,args:[r],thisArg:o}),new mo(i,this.__chain__).thru(function(e){return t&&!e.length&&e.push(o),e})):this.thru(r)}),_s=vr(function(e,t,n){et.call(e,n)?++e[n]:Mo(e,n,1)}),vs=Ar(qa),bs=Ar(Wa);function Cs(e,t){return(Ws(e)?Rt:Ho)(e,ta(t,3))}function Os(e,t){return(Ws(e)?Nt:Vo)(e,ta(t,3))}var Ts=vr(function(e,t,n){et.call(e,n)?e[n].push(t):Mo(e,n,[t])}),ws=Mi(function(e,t,n){var o=-1,i="function"==typeof t,r=Zs(e)?Ie(e.length):[];return Ho(e,function(e){r[++o]=i?Mt(t,e,n):ci(e,t,n)}),r}),Is=vr(function(e,t,n){Mo(e,n,t)});function Ps(e,t){return(Ws(e)?Ht:_i)(e,ta(t,3))}var Ss=vr(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),As=Mi(function(e,t){if(null==e)return[];var n=t.length;return n>1&&_a(e,t[0],t[1])?t=[]:n>2&&_a(t[0],t[1],t[2])&&(t=[t[0]]),wi(e,Wo(t,1),[])}),Ds=An||function(){return Ct.Date.now()};function xs(e,t,n){return t=n?o:t,t=e&&null==t?e.length:t,Vr(e,v,o,o,o,o,t)}function Es(e,t){var n;if("function"!=typeof t)throw new Xe(r);return e=gc(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=o),n}}var Ls=Mi(function(e,t,n){var o=d;if(n.length){var i=In(n,ea(Ls));o|=g}return Vr(e,o,t,n,i)}),Ms=Mi(function(e,t,n){var o=d|y;if(n.length){var i=In(n,ea(Ms));o|=g}return Vr(t,o,e,n,i)});function js(e,t,n){var i,a,s,c,p,l,u=0,d=!1,y=!1,f=!0;if("function"!=typeof e)throw new Xe(r);function h(t){var n=i,r=a;return i=a=o,u=t,c=e.apply(r,n)}function m(e){return u=e,p=Ma(v,t),d?h(e):c}function g(e){var n=t-(e-l);return y?zn(n,s-(e-u)):n}function _(e){var n=e-l;return l===o||n>=t||n<0||y&&e-u>=s}function v(){var e=Ds();if(_(e))return b(e);p=Ma(v,g(e))}function b(e){return p=o,f&&i?h(e):(i=a=o,c)}function C(){var e=Ds(),n=_(e);if(i=arguments,a=this,l=e,n){if(p===o)return m(l);if(y)return rr(p),p=Ma(v,t),h(l)}return p===o&&(p=Ma(v,t)),c}return t=vc(t)||0,oc(n)&&(d=!!n.leading,s=(y="maxWait"in n)?Gn(vc(n.maxWait)||0,t):s,f="trailing"in n?!!n.trailing:f),C.cancel=function(){p!==o&&rr(p),u=0,i=l=a=p=o},C.flush=function(){return p===o?c:b(Ds())},C}var Rs=Mi(function(e,t){return Uo(e,1,t)}),Ns=Mi(function(e,t,n){return Uo(e,vc(t)||0,n)});function Bs(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new Xe(r);var n=function(){var o=arguments,i=t?t.apply(this,o):o[0],r=n.cache;if(r.has(i))return r.get(i);var a=e.apply(this,o);return n.cache=r.set(i,a)||r,a};return n.cache=new(Bs.Cache||bo),n}function Fs(e){if("function"!=typeof e)throw new Xe(r);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Bs.Cache=bo;var Us=or(function(e,t){var n=(t=1==t.length&&Ws(t[0])?Ht(t[0],ln(ta())):Ht(Wo(t,1),ln(ta()))).length;return Mi(function(o){for(var i=-1,r=zn(o.length,n);++i<r;)o[i]=t[i].call(this,o[i]);return Mt(e,this,o)})}),ks=Mi(function(e,t){var n=In(t,ea(ks));return Vr(e,g,o,t,n)}),Hs=Mi(function(e,t){var n=In(t,ea(Hs));return Vr(e,_,o,t,n)}),Vs=Zr(function(e,t){return Vr(e,b,o,o,o,t)});function Ys(e,t){return e===t||e!=e&&t!=t}var Gs=Br(ni),zs=Br(function(e,t){return e>=t}),qs=pi(function(){return arguments}())?pi:function(e){return ic(e)&&et.call(e,"callee")&&!Ot.call(e,"callee")},Ws=Ie.isArray,Xs=St?ln(St):function(e){return ic(e)&&ti(e)==J};function Zs(e){return null!=e&&nc(e.length)&&!ec(e)}function Ks(e){return ic(e)&&Zs(e)}var $s=kn||vp,Js=At?ln(At):function(e){return ic(e)&&ti(e)==N};function Qs(e){if(!ic(e))return!1;var t=ti(e);return t==F||t==B||"string"==typeof e.message&&"string"==typeof e.name&&!sc(e)}function ec(e){if(!oc(e))return!1;var t=ti(e);return t==U||t==k||t==j||t==z}function tc(e){return"number"==typeof e&&e==gc(e)}function nc(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=I}function oc(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function ic(e){return null!=e&&"object"==typeof e}var rc=Dt?ln(Dt):function(e){return ic(e)&&ca(e)==H};function ac(e){return"number"==typeof e||ic(e)&&ti(e)==V}function sc(e){if(!ic(e)||ti(e)!=G)return!1;var t=vt(e);if(null===t)return!0;var n=et.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&Qe.call(n)==it}var cc=xt?ln(xt):function(e){return ic(e)&&ti(e)==q},pc=Et?ln(Et):function(e){return ic(e)&&ca(e)==W};function lc(e){return"string"==typeof e||!Ws(e)&&ic(e)&&ti(e)==X}function uc(e){return"symbol"==typeof e||ic(e)&&ti(e)==Z}var dc=Lt?ln(Lt):function(e){return ic(e)&&nc(e.length)&&!!ft[ti(e)]},yc=Br(gi),fc=Br(function(e,t){return e<=t});function hc(e){if(!e)return[];if(Zs(e))return lc(e)?En(e):hr(e);if(Pt&&e[Pt])return On(e[Pt]());var t=ca(e);return(t==H?Tn:t==W?Pn:Yc)(e)}function mc(e){return e?(e=vc(e))===w||e===-w?(e<0?-1:1)*P:e==e?e:0:0===e?e:0}function gc(e){var t=mc(e),n=t%1;return t==t?n?t-n:t:0}function _c(e){return e?Ro(gc(e),0,A):0}function vc(e){if("number"==typeof e)return e;if(uc(e))return S;if(oc(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=oc(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=pn(e);var n=Re.test(e);return n||Be.test(e)?_t(e.slice(2),n?2:8):je.test(e)?S:+e}function bc(e){return mr(e,Rc(e))}function Cc(e){return null==e?"":qi(e)}var Oc=br(function(e,t){if(Oa(t)||Zs(t))mr(t,jc(t),e);else for(var n in t)et.call(t,n)&&Ao(e,n,t[n])}),Tc=br(function(e,t){mr(t,Rc(t),e)}),wc=br(function(e,t,n,o){mr(t,Rc(t),e,o)}),Ic=br(function(e,t,n,o){mr(t,jc(t),e,o)}),Pc=Zr(jo),Sc=Mi(function(e,t){e=ze(e);var n=-1,i=t.length,r=i>2?t[2]:o;for(r&&_a(t[0],t[1],r)&&(i=1);++n<i;)for(var a=t[n],s=Rc(a),c=-1,p=s.length;++c<p;){var l=s[c],u=e[l];(u===o||Ys(u,$e[l])&&!et.call(e,l))&&(e[l]=a[l])}return e}),Ac=Mi(function(e){return e.push(o,Gr),Mt(Bc,o,e)});function Dc(e,t,n){var i=null==e?o:Qo(e,t);return i===o?n:i}function xc(e,t){return null!=e&&ua(e,t,ii)}var Ec=Er(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=ot.call(t)),e[t]=n},op(ap)),Lc=Er(function(e,t,n){null!=t&&"function"!=typeof t.toString&&(t=ot.call(t)),et.call(e,t)?e[t].push(n):e[t]=[n]},ta),Mc=Mi(ci);function jc(e){return Zs(e)?To(e):hi(e)}function Rc(e){return Zs(e)?To(e,!0):mi(e)}var Nc=br(function(e,t,n){Ci(e,t,n)}),Bc=br(function(e,t,n,o){Ci(e,t,n,o)}),Fc=Zr(function(e,t){var n={};if(null==e)return n;var o=!1;t=Ht(t,function(t){return t=nr(t,e),o||(o=t.length>1),t}),mr(e,$r(e),n),o&&(n=No(n,s|c|p,zr));for(var i=t.length;i--;)Xi(n,t[i]);return n}),Uc=Zr(function(e,t){return null==e?{}:Ii(e,t)});function kc(e,t){if(null==e)return{};var n=Ht($r(e),function(e){return[e]});return t=ta(t),Pi(e,n,function(e,n){return t(e,n[0])})}var Hc=Hr(jc),Vc=Hr(Rc);function Yc(e){return null==e?[]:un(e,jc(e))}var Gc=Ir(function(e,t,n){return t=t.toLowerCase(),e+(n?zc(t):t)});function zc(e){return Qc(Cc(e).toLowerCase())}function qc(e){return(e=Cc(e))&&e.replace(Ue,mn).replace(st,"")}var Wc=Ir(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),Xc=Ir(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),Zc=wr("toLowerCase"),Kc=Ir(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),$c=Ir(function(e,t,n){return e+(n?" ":"")+Qc(t)}),Jc=Ir(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),Qc=wr("toUpperCase");function ep(e,t,n){return e=Cc(e),(t=n?o:t)===o?Cn(e)?Nn(e):Xt(e):e.match(t)||[]}var tp=Mi(function(e,t){try{return Mt(e,o,t)}catch(n){return Qs(n)?n:new Ve(n)}}),np=Zr(function(e,t){return Rt(t,function(t){t=Ua(t),Mo(e,t,Ls(e[t],e))}),e});function op(e){return function(){return e}}var ip=Dr(),rp=Dr(!0);function ap(e){return e}function sp(e){return fi("function"==typeof e?e:No(e,s))}var cp=Mi(function(e,t){return function(n){return ci(n,e,t)}}),pp=Mi(function(e,t){return function(n){return ci(e,n,t)}});function lp(e,t,n){var o=jc(t),i=Jo(t,o);null!=n||oc(t)&&(i.length||!o.length)||(n=t,t=e,e=this,i=Jo(t,jc(t)));var r=!(oc(n)&&"chain"in n&&!n.chain),a=ec(e);return Rt(i,function(n){var o=t[n];e[n]=o,a&&(e.prototype[n]=function(){var t=this.__chain__;if(r||t){var n=e(this.__wrapped__),i=n.__actions__=hr(this.__actions__);return i.push({func:o,args:arguments,thisArg:e}),n.__chain__=t,n}return o.apply(e,Vt([this.value()],arguments))})}),e}function up(){}var dp=Mr(Ht),yp=Mr(Bt),fp=Mr(zt);function hp(e){return va(e)?tn(Ua(e)):Si(e)}var mp=Nr(),gp=Nr(!0);function _p(){return[]}function vp(){return!1}var bp,Cp=Lr(function(e,t){return e+t},0),Op=Ur("ceil"),Tp=Lr(function(e,t){return e/t},1),wp=Ur("floor"),Ip=Lr(function(e,t){return e*t},1),Pp=Ur("round"),Sp=Lr(function(e,t){return e-t},0);return yo.after=function(e,t){if("function"!=typeof t)throw new Xe(r);return e=gc(e),function(){if(--e<1)return t.apply(this,arguments)}},yo.ary=xs,yo.assign=Oc,yo.assignIn=Tc,yo.assignInWith=wc,yo.assignWith=Ic,yo.at=Pc,yo.before=Es,yo.bind=Ls,yo.bindAll=np,yo.bindKey=Ms,yo.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return Ws(e)?e:[e]},yo.chain=hs,yo.chunk=function(e,t,n){t=(n?_a(e,t,n):t===o)?1:Gn(gc(t),0);var i=null==e?0:e.length;if(!i||t<1)return[];for(var r=0,a=0,s=Ie(Rn(i/t));r<i;)s[a++]=ki(e,r,r+=t);return s},yo.compact=function(e){for(var t=-1,n=null==e?0:e.length,o=0,i=[];++t<n;){var r=e[t];r&&(i[o++]=r)}return i},yo.concat=function(){var e=arguments.length;if(!e)return[];for(var t=Ie(e-1),n=arguments[0],o=e;o--;)t[o-1]=arguments[o];return Vt(Ws(n)?hr(n):[n],Wo(t,1))},yo.cond=function(e){var t=null==e?0:e.length,n=ta();return e=t?Ht(e,function(e){if("function"!=typeof e[1])throw new Xe(r);return[n(e[0]),e[1]]}):[],Mi(function(n){for(var o=-1;++o<t;){var i=e[o];if(Mt(i[0],this,n))return Mt(i[1],this,n)}})},yo.conforms=function(e){return Bo(No(e,s))},yo.constant=op,yo.countBy=_s,yo.create=function(e,t){var n=fo(e);return null==t?n:Eo(n,t)},yo.curry=function e(t,n,i){var r=Vr(t,h,o,o,o,o,o,n=i?o:n);return r.placeholder=e.placeholder,r},yo.curryRight=function e(t,n,i){var r=Vr(t,m,o,o,o,o,o,n=i?o:n);return r.placeholder=e.placeholder,r},yo.debounce=js,yo.defaults=Sc,yo.defaultsDeep=Ac,yo.defer=Rs,yo.delay=Ns,yo.difference=Ya,yo.differenceBy=Ga,yo.differenceWith=za,yo.drop=function(e,t,n){var i=null==e?0:e.length;return i?ki(e,(t=n||t===o?1:gc(t))<0?0:t,i):[]},yo.dropRight=function(e,t,n){var i=null==e?0:e.length;return i?ki(e,0,(t=i-(t=n||t===o?1:gc(t)))<0?0:t):[]},yo.dropRightWhile=function(e,t){return e&&e.length?Ki(e,ta(t,3),!0,!0):[]},yo.dropWhile=function(e,t){return e&&e.length?Ki(e,ta(t,3),!0):[]},yo.fill=function(e,t,n,o){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&_a(e,t,n)&&(n=0,o=i),zo(e,t,n,o)):[]},yo.filter=function(e,t){return(Ws(e)?Ft:qo)(e,ta(t,3))},yo.flatMap=function(e,t){return Wo(Ps(e,t),1)},yo.flatMapDeep=function(e,t){return Wo(Ps(e,t),w)},yo.flatMapDepth=function(e,t,n){return n=n===o?1:gc(n),Wo(Ps(e,t),n)},yo.flatten=Xa,yo.flattenDeep=function(e){return null!=e&&e.length?Wo(e,w):[]},yo.flattenDepth=function(e,t){return null!=e&&e.length?Wo(e,t=t===o?1:gc(t)):[]},yo.flip=function(e){return Vr(e,C)},yo.flow=ip,yo.flowRight=rp,yo.fromPairs=function(e){for(var t=-1,n=null==e?0:e.length,o={};++t<n;){var i=e[t];o[i[0]]=i[1]}return o},yo.functions=function(e){return null==e?[]:Jo(e,jc(e))},yo.functionsIn=function(e){return null==e?[]:Jo(e,Rc(e))},yo.groupBy=Ts,yo.initial=function(e){return null!=e&&e.length?ki(e,0,-1):[]},yo.intersection=Ka,yo.intersectionBy=$a,yo.intersectionWith=Ja,yo.invert=Ec,yo.invertBy=Lc,yo.invokeMap=ws,yo.iteratee=sp,yo.keyBy=Is,yo.keys=jc,yo.keysIn=Rc,yo.map=Ps,yo.mapKeys=function(e,t){var n={};return t=ta(t,3),Ko(e,function(e,o,i){Mo(n,t(e,o,i),e)}),n},yo.mapValues=function(e,t){var n={};return t=ta(t,3),Ko(e,function(e,o,i){Mo(n,o,t(e,o,i))}),n},yo.matches=function(e){return vi(No(e,s))},yo.matchesProperty=function(e,t){return bi(e,No(t,s))},yo.memoize=Bs,yo.merge=Nc,yo.mergeWith=Bc,yo.method=cp,yo.methodOf=pp,yo.mixin=lp,yo.negate=Fs,yo.nthArg=function(e){return e=gc(e),Mi(function(t){return Ti(t,e)})},yo.omit=Fc,yo.omitBy=function(e,t){return kc(e,Fs(ta(t)))},yo.once=function(e){return Es(2,e)},yo.orderBy=function(e,t,n,i){return null==e?[]:(Ws(t)||(t=null==t?[]:[t]),Ws(n=i?o:n)||(n=null==n?[]:[n]),wi(e,t,n))},yo.over=dp,yo.overArgs=Us,yo.overEvery=yp,yo.overSome=fp,yo.partial=ks,yo.partialRight=Hs,yo.partition=Ss,yo.pick=Uc,yo.pickBy=kc,yo.property=hp,yo.propertyOf=function(e){return function(t){return null==e?o:Qo(e,t)}},yo.pull=es,yo.pullAll=ts,yo.pullAllBy=function(e,t,n){return e&&e.length&&t&&t.length?Ai(e,t,ta(n,2)):e},yo.pullAllWith=function(e,t,n){return e&&e.length&&t&&t.length?Ai(e,t,o,n):e},yo.pullAt=ns,yo.range=mp,yo.rangeRight=gp,yo.rearg=Vs,yo.reject=function(e,t){return(Ws(e)?Ft:qo)(e,Fs(ta(t,3)))},yo.remove=function(e,t){var n=[];if(!e||!e.length)return n;var o=-1,i=[],r=e.length;for(t=ta(t,3);++o<r;){var a=e[o];t(a,o,e)&&(n.push(a),i.push(o))}return Di(e,i),n},yo.rest=function(e,t){if("function"!=typeof e)throw new Xe(r);return Mi(e,t=t===o?t:gc(t))},yo.reverse=os,yo.sampleSize=function(e,t,n){return t=(n?_a(e,t,n):t===o)?1:gc(t),(Ws(e)?Io:Ri)(e,t)},yo.set=function(e,t,n){return null==e?e:Ni(e,t,n)},yo.setWith=function(e,t,n,i){return i="function"==typeof i?i:o,null==e?e:Ni(e,t,n,i)},yo.shuffle=function(e){return(Ws(e)?Po:Ui)(e)},yo.slice=function(e,t,n){var i=null==e?0:e.length;return i?(n&&"number"!=typeof n&&_a(e,t,n)?(t=0,n=i):(t=null==t?0:gc(t),n=n===o?i:gc(n)),ki(e,t,n)):[]},yo.sortBy=As,yo.sortedUniq=function(e){return e&&e.length?Gi(e):[]},yo.sortedUniqBy=function(e,t){return e&&e.length?Gi(e,ta(t,2)):[]},yo.split=function(e,t,n){return n&&"number"!=typeof n&&_a(e,t,n)&&(t=n=o),(n=n===o?A:n>>>0)?(e=Cc(e))&&("string"==typeof t||null!=t&&!cc(t))&&!(t=qi(t))&&bn(e)?ir(En(e),0,n):e.split(t,n):[]},yo.spread=function(e,t){if("function"!=typeof e)throw new Xe(r);return t=null==t?0:Gn(gc(t),0),Mi(function(n){var o=n[t],i=ir(n,0,t);return o&&Vt(i,o),Mt(e,this,i)})},yo.tail=function(e){var t=null==e?0:e.length;return t?ki(e,1,t):[]},yo.take=function(e,t,n){return e&&e.length?ki(e,0,(t=n||t===o?1:gc(t))<0?0:t):[]},yo.takeRight=function(e,t,n){var i=null==e?0:e.length;return i?ki(e,(t=i-(t=n||t===o?1:gc(t)))<0?0:t,i):[]},yo.takeRightWhile=function(e,t){return e&&e.length?Ki(e,ta(t,3),!1,!0):[]},yo.takeWhile=function(e,t){return e&&e.length?Ki(e,ta(t,3)):[]},yo.tap=function(e,t){return t(e),e},yo.throttle=function(e,t,n){var o=!0,i=!0;if("function"!=typeof e)throw new Xe(r);return oc(n)&&(o="leading"in n?!!n.leading:o,i="trailing"in n?!!n.trailing:i),js(e,t,{leading:o,maxWait:t,trailing:i})},yo.thru=ms,yo.toArray=hc,yo.toPairs=Hc,yo.toPairsIn=Vc,yo.toPath=function(e){return Ws(e)?Ht(e,Ua):uc(e)?[e]:hr(Fa(Cc(e)))},yo.toPlainObject=bc,yo.transform=function(e,t,n){var o=Ws(e),i=o||$s(e)||dc(e);if(t=ta(t,4),null==n){var r=e&&e.constructor;n=i?o?new r:[]:oc(e)&&ec(r)?fo(vt(e)):{}}return(i?Rt:Ko)(e,function(e,o,i){return t(n,e,o,i)}),n},yo.unary=function(e){return xs(e,1)},yo.union=is,yo.unionBy=rs,yo.unionWith=as,yo.uniq=function(e){return e&&e.length?Wi(e):[]},yo.uniqBy=function(e,t){return e&&e.length?Wi(e,ta(t,2)):[]},yo.uniqWith=function(e,t){return t="function"==typeof t?t:o,e&&e.length?Wi(e,o,t):[]},yo.unset=function(e,t){return null==e||Xi(e,t)},yo.unzip=ss,yo.unzipWith=cs,yo.update=function(e,t,n){return null==e?e:Zi(e,t,tr(n))},yo.updateWith=function(e,t,n,i){return i="function"==typeof i?i:o,null==e?e:Zi(e,t,tr(n),i)},yo.values=Yc,yo.valuesIn=function(e){return null==e?[]:un(e,Rc(e))},yo.without=ps,yo.words=ep,yo.wrap=function(e,t){return ks(tr(t),e)},yo.xor=ls,yo.xorBy=us,yo.xorWith=ds,yo.zip=ys,yo.zipObject=function(e,t){return Qi(e||[],t||[],Ao)},yo.zipObjectDeep=function(e,t){return Qi(e||[],t||[],Ni)},yo.zipWith=fs,yo.entries=Hc,yo.entriesIn=Vc,yo.extend=Tc,yo.extendWith=wc,lp(yo,yo),yo.add=Cp,yo.attempt=tp,yo.camelCase=Gc,yo.capitalize=zc,yo.ceil=Op,yo.clamp=function(e,t,n){return n===o&&(n=t,t=o),n!==o&&(n=(n=vc(n))==n?n:0),t!==o&&(t=(t=vc(t))==t?t:0),Ro(vc(e),t,n)},yo.clone=function(e){return No(e,p)},yo.cloneDeep=function(e){return No(e,s|p)},yo.cloneDeepWith=function(e,t){return No(e,s|p,t="function"==typeof t?t:o)},yo.cloneWith=function(e,t){return No(e,p,t="function"==typeof t?t:o)},yo.conformsTo=function(e,t){return null==t||Fo(e,t,jc(t))},yo.deburr=qc,yo.defaultTo=function(e,t){return null==e||e!=e?t:e},yo.divide=Tp,yo.endsWith=function(e,t,n){e=Cc(e),t=qi(t);var i=e.length,r=n=n===o?i:Ro(gc(n),0,i);return(n-=t.length)>=0&&e.slice(n,r)==t},yo.eq=Ys,yo.escape=function(e){return(e=Cc(e))&&he.test(e)?e.replace(ye,gn):e},yo.escapeRegExp=function(e){return(e=Cc(e))&&Te.test(e)?e.replace(Oe,"\\$&"):e},yo.every=function(e,t,n){var i=Ws(e)?Bt:Yo;return n&&_a(e,t,n)&&(t=o),i(e,ta(t,3))},yo.find=vs,yo.findIndex=qa,yo.findKey=function(e,t){return Zt(e,ta(t,3),Ko)},yo.findLast=bs,yo.findLastIndex=Wa,yo.findLastKey=function(e,t){return Zt(e,ta(t,3),$o)},yo.floor=wp,yo.forEach=Cs,yo.forEachRight=Os,yo.forIn=function(e,t){return null==e?e:Xo(e,ta(t,3),Rc)},yo.forInRight=function(e,t){return null==e?e:Zo(e,ta(t,3),Rc)},yo.forOwn=function(e,t){return e&&Ko(e,ta(t,3))},yo.forOwnRight=function(e,t){return e&&$o(e,ta(t,3))},yo.get=Dc,yo.gt=Gs,yo.gte=zs,yo.has=function(e,t){return null!=e&&ua(e,t,oi)},yo.hasIn=xc,yo.head=Za,yo.identity=ap,yo.includes=function(e,t,n,o){e=Zs(e)?e:Yc(e),n=n&&!o?gc(n):0;var i=e.length;return n<0&&(n=Gn(i+n,0)),lc(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&$t(e,t,n)>-1},yo.indexOf=function(e,t,n){var o=null==e?0:e.length;if(!o)return-1;var i=null==n?0:gc(n);return i<0&&(i=Gn(o+i,0)),$t(e,t,i)},yo.inRange=function(e,t,n){return t=mc(t),n===o?(n=t,t=0):n=mc(n),ri(e=vc(e),t,n)},yo.invoke=Mc,yo.isArguments=qs,yo.isArray=Ws,yo.isArrayBuffer=Xs,yo.isArrayLike=Zs,yo.isArrayLikeObject=Ks,yo.isBoolean=function(e){return!0===e||!1===e||ic(e)&&ti(e)==R},yo.isBuffer=$s,yo.isDate=Js,yo.isElement=function(e){return ic(e)&&1===e.nodeType&&!sc(e)},yo.isEmpty=function(e){if(null==e)return!0;if(Zs(e)&&(Ws(e)||"string"==typeof e||"function"==typeof e.splice||$s(e)||dc(e)||qs(e)))return!e.length;var t=ca(e);if(t==H||t==W)return!e.size;if(Oa(e))return!hi(e).length;for(var n in e)if(et.call(e,n))return!1;return!0},yo.isEqual=function(e,t){return li(e,t)},yo.isEqualWith=function(e,t,n){var i=(n="function"==typeof n?n:o)?n(e,t):o;return i===o?li(e,t,o,n):!!i},yo.isError=Qs,yo.isFinite=function(e){return"number"==typeof e&&Hn(e)},yo.isFunction=ec,yo.isInteger=tc,yo.isLength=nc,yo.isMap=rc,yo.isMatch=function(e,t){return e===t||di(e,t,oa(t))},yo.isMatchWith=function(e,t,n){return n="function"==typeof n?n:o,di(e,t,oa(t),n)},yo.isNaN=function(e){return ac(e)&&e!=+e},yo.isNative=function(e){if(Ca(e))throw new Ve("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return yi(e)},yo.isNil=function(e){return null==e},yo.isNull=function(e){return null===e},yo.isNumber=ac,yo.isObject=oc,yo.isObjectLike=ic,yo.isPlainObject=sc,yo.isRegExp=cc,yo.isSafeInteger=function(e){return tc(e)&&e>=-I&&e<=I},yo.isSet=pc,yo.isString=lc,yo.isSymbol=uc,yo.isTypedArray=dc,yo.isUndefined=function(e){return e===o},yo.isWeakMap=function(e){return ic(e)&&ca(e)==$},yo.isWeakSet=function(e){return ic(e)&&"[object WeakSet]"==ti(e)},yo.join=function(e,t){return null==e?"":Vn.call(e,t)},yo.kebabCase=Wc,yo.last=Qa,yo.lastIndexOf=function(e,t,n){var i=null==e?0:e.length;if(!i)return-1;var r=i;return n!==o&&(r=(r=gc(n))<0?Gn(i+r,0):zn(r,i-1)),t==t?Dn(e,t,r):Kt(e,Qt,r,!0)},yo.lowerCase=Xc,yo.lowerFirst=Zc,yo.lt=yc,yo.lte=fc,yo.max=function(e){return e&&e.length?Go(e,ap,ni):o},yo.maxBy=function(e,t){return e&&e.length?Go(e,ta(t,2),ni):o},yo.mean=function(e){return en(e,ap)},yo.meanBy=function(e,t){return en(e,ta(t,2))},yo.min=function(e){return e&&e.length?Go(e,ap,gi):o},yo.minBy=function(e,t){return e&&e.length?Go(e,ta(t,2),gi):o},yo.stubArray=_p,yo.stubFalse=vp,yo.stubObject=function(){return{}},yo.stubString=function(){return""},yo.stubTrue=function(){return!0},yo.multiply=Ip,yo.nth=function(e,t){return e&&e.length?Ti(e,gc(t)):o},yo.noConflict=function(){return Ct._===this&&(Ct._=rt),this},yo.noop=up,yo.now=Ds,yo.pad=function(e,t,n){e=Cc(e);var o=(t=gc(t))?xn(e):0;if(!t||o>=t)return e;var i=(t-o)/2;return jr(Fn(i),n)+e+jr(Rn(i),n)},yo.padEnd=function(e,t,n){e=Cc(e);var o=(t=gc(t))?xn(e):0;return t&&o<t?e+jr(t-o,n):e},yo.padStart=function(e,t,n){e=Cc(e);var o=(t=gc(t))?xn(e):0;return t&&o<t?jr(t-o,n)+e:e},yo.parseInt=function(e,t,n){return n||null==t?t=0:t&&(t=+t),Wn(Cc(e).replace(we,""),t||0)},yo.random=function(e,t,n){if(n&&"boolean"!=typeof n&&_a(e,t,n)&&(t=n=o),n===o&&("boolean"==typeof t?(n=t,t=o):"boolean"==typeof e&&(n=e,e=o)),e===o&&t===o?(e=0,t=1):(e=mc(e),t===o?(t=e,e=0):t=mc(t)),e>t){var i=e;e=t,t=i}if(n||e%1||t%1){var r=Xn();return zn(e+r*(t-e+gt("1e-"+((r+"").length-1))),t)}return xi(e,t)},yo.reduce=function(e,t,n){var o=Ws(e)?Yt:on,i=arguments.length<3;return o(e,ta(t,4),n,i,Ho)},yo.reduceRight=function(e,t,n){var o=Ws(e)?Gt:on,i=arguments.length<3;return o(e,ta(t,4),n,i,Vo)},yo.repeat=function(e,t,n){return t=(n?_a(e,t,n):t===o)?1:gc(t),Li(Cc(e),t)},yo.replace=function(){var e=arguments,t=Cc(e[0]);return e.length<3?t:t.replace(e[1],e[2])},yo.result=function(e,t,n){var i=-1,r=(t=nr(t,e)).length;for(r||(r=1,e=o);++i<r;){var a=null==e?o:e[Ua(t[i])];a===o&&(i=r,a=n),e=ec(a)?a.call(e):a}return e},yo.round=Pp,yo.runInContext=e,yo.sample=function(e){return(Ws(e)?wo:ji)(e)},yo.size=function(e){if(null==e)return 0;if(Zs(e))return lc(e)?xn(e):e.length;var t=ca(e);return t==H||t==W?e.size:hi(e).length},yo.snakeCase=Kc,yo.some=function(e,t,n){var i=Ws(e)?zt:Hi;return n&&_a(e,t,n)&&(t=o),i(e,ta(t,3))},yo.sortedIndex=function(e,t){return Vi(e,t)},yo.sortedIndexBy=function(e,t,n){return Yi(e,t,ta(n,2))},yo.sortedIndexOf=function(e,t){var n=null==e?0:e.length;if(n){var o=Vi(e,t);if(o<n&&Ys(e[o],t))return o}return-1},yo.sortedLastIndex=function(e,t){return Vi(e,t,!0)},yo.sortedLastIndexBy=function(e,t,n){return Yi(e,t,ta(n,2),!0)},yo.sortedLastIndexOf=function(e,t){if(null!=e&&e.length){var n=Vi(e,t,!0)-1;if(Ys(e[n],t))return n}return-1},yo.startCase=$c,yo.startsWith=function(e,t,n){return e=Cc(e),n=null==n?0:Ro(gc(n),0,e.length),t=qi(t),e.slice(n,n+t.length)==t},yo.subtract=Sp,yo.sum=function(e){return e&&e.length?an(e,ap):0},yo.sumBy=function(e,t){return e&&e.length?an(e,ta(t,2)):0},yo.template=function(e,t,n){var i=yo.templateSettings;n&&_a(e,t,n)&&(t=o),e=Cc(e),t=wc({},t,i,Yr);var r,a,s=wc({},t.imports,i.imports,Yr),c=jc(s),p=un(s,c),l=0,u=t.interpolate||ke,d="__p += '",y=qe((t.escape||ke).source+"|"+u.source+"|"+(u===_e?Le:ke).source+"|"+(t.evaluate||ke).source+"|$","g"),f="//# sourceURL="+(et.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++yt+"]")+"\n";e.replace(y,function(t,n,o,i,s,c){return o||(o=i),d+=e.slice(l,c).replace(He,_n),n&&(r=!0,d+="' +\n__e("+n+") +\n'"),s&&(a=!0,d+="';\n"+s+";\n__p += '"),o&&(d+="' +\n((__t = ("+o+")) == null ? '' : __t) +\n'"),l=c+t.length,t}),d+="';\n";var h=et.call(t,"variable")&&t.variable;if(h){if(xe.test(h))throw new Ve("Invalid `variable` option passed into `_.template`")}else d="with (obj) {\n"+d+"\n}\n";d=(a?d.replace(pe,""):d).replace(le,"$1").replace(ue,"$1;"),d="function("+(h||"obj")+") {\n"+(h?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(r?", __e = _.escape":"")+(a?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+d+"return __p\n}";var m=tp(function(){return Ye(c,f+"return "+d).apply(o,p)});if(m.source=d,Qs(m))throw m;return m},yo.times=function(e,t){if((e=gc(e))<1||e>I)return[];var n=A,o=zn(e,A);t=ta(t),e-=A;for(var i=sn(o,t);++n<e;)t(n);return i},yo.toFinite=mc,yo.toInteger=gc,yo.toLength=_c,yo.toLower=function(e){return Cc(e).toLowerCase()},yo.toNumber=vc,yo.toSafeInteger=function(e){return e?Ro(gc(e),-I,I):0===e?e:0},yo.toString=Cc,yo.toUpper=function(e){return Cc(e).toUpperCase()},yo.trim=function(e,t,n){if((e=Cc(e))&&(n||t===o))return pn(e);if(!e||!(t=qi(t)))return e;var i=En(e),r=En(t);return ir(i,yn(i,r),fn(i,r)+1).join("")},yo.trimEnd=function(e,t,n){if((e=Cc(e))&&(n||t===o))return e.slice(0,Ln(e)+1);if(!e||!(t=qi(t)))return e;var i=En(e);return ir(i,0,fn(i,En(t))+1).join("")},yo.trimStart=function(e,t,n){if((e=Cc(e))&&(n||t===o))return e.replace(we,"");if(!e||!(t=qi(t)))return e;var i=En(e);return ir(i,yn(i,En(t))).join("")},yo.truncate=function(e,t){var n=30,i="...";if(oc(t)){var r="separator"in t?t.separator:r;n="length"in t?gc(t.length):n,i="omission"in t?qi(t.omission):i}var a=(e=Cc(e)).length;if(bn(e)){var s=En(e);a=s.length}if(n>=a)return e;var c=n-xn(i);if(c<1)return i;var p=s?ir(s,0,c).join(""):e.slice(0,c);if(r===o)return p+i;if(s&&(c+=p.length-c),cc(r)){if(e.slice(c).search(r)){var l,u=p;for(r.global||(r=qe(r.source,Cc(Me.exec(r))+"g")),r.lastIndex=0;l=r.exec(u);)var d=l.index;p=p.slice(0,d===o?c:d)}}else if(e.indexOf(qi(r),c)!=c){var y=p.lastIndexOf(r);y>-1&&(p=p.slice(0,y))}return p+i},yo.unescape=function(e){return(e=Cc(e))&&fe.test(e)?e.replace(de,Mn):e},yo.uniqueId=function(e){var t=++tt;return Cc(e)+t},yo.upperCase=Jc,yo.upperFirst=Qc,yo.each=Cs,yo.eachRight=Os,yo.first=Za,lp(yo,(bp={},Ko(yo,function(e,t){et.call(yo.prototype,t)||(bp[t]=e)}),bp),{chain:!1}),yo.VERSION="4.17.21",Rt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){yo[e].placeholder=yo}),Rt(["drop","take"],function(e,t){go.prototype[e]=function(n){n=n===o?1:Gn(gc(n),0);var i=this.__filtered__&&!t?new go(this):this.clone();return i.__filtered__?i.__takeCount__=zn(n,i.__takeCount__):i.__views__.push({size:zn(n,A),type:e+(i.__dir__<0?"Right":"")}),i},go.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}}),Rt(["filter","map","takeWhile"],function(e,t){var n=t+1,o=1==n||3==n;go.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:ta(e,3),type:n}),t.__filtered__=t.__filtered__||o,t}}),Rt(["head","last"],function(e,t){var n="take"+(t?"Right":"");go.prototype[e]=function(){return this[n](1).value()[0]}}),Rt(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");go.prototype[e]=function(){return this.__filtered__?new go(this):this[n](1)}}),go.prototype.compact=function(){return this.filter(ap)},go.prototype.find=function(e){return this.filter(e).head()},go.prototype.findLast=function(e){return this.reverse().find(e)},go.prototype.invokeMap=Mi(function(e,t){return"function"==typeof e?new go(this):this.map(function(n){return ci(n,e,t)})}),go.prototype.reject=function(e){return this.filter(Fs(ta(e)))},go.prototype.slice=function(e,t){e=gc(e);var n=this;return n.__filtered__&&(e>0||t<0)?new go(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==o&&(n=(t=gc(t))<0?n.dropRight(-t):n.take(t-e)),n)},go.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},go.prototype.toArray=function(){return this.take(A)},Ko(go.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),i=/^(?:head|last)$/.test(t),r=yo[i?"take"+("last"==t?"Right":""):t],a=i||/^find/.test(t);r&&(yo.prototype[t]=function(){var t=this.__wrapped__,s=i?[1]:arguments,c=t instanceof go,p=s[0],l=c||Ws(t),u=function(e){var t=r.apply(yo,Vt([e],s));return i&&d?t[0]:t};l&&n&&"function"==typeof p&&1!=p.length&&(c=l=!1);var d=this.__chain__,y=!!this.__actions__.length,f=a&&!d,h=c&&!y;if(!a&&l){t=h?t:new go(this);var m=e.apply(t,s);return m.__actions__.push({func:ms,args:[u],thisArg:o}),new mo(m,d)}return f&&h?e.apply(this,s):(m=this.thru(u),f?i?m.value()[0]:m.value():m)})}),Rt(["pop","push","shift","sort","splice","unshift"],function(e){var t=Ze[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",o=/^(?:pop|shift)$/.test(e);yo.prototype[e]=function(){var e=arguments;if(o&&!this.__chain__){var i=this.value();return t.apply(Ws(i)?i:[],e)}return this[n](function(n){return t.apply(Ws(n)?n:[],e)})}}),Ko(go.prototype,function(e,t){var n=yo[t];if(n){var o=n.name+"";et.call(oo,o)||(oo[o]=[]),oo[o].push({name:t,func:n})}}),oo[xr(o,y).name]=[{name:"wrapper",func:o}],go.prototype.clone=function(){var e=new go(this.__wrapped__);return e.__actions__=hr(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=hr(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=hr(this.__views__),e},go.prototype.reverse=function(){if(this.__filtered__){var e=new go(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},go.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,n=Ws(e),o=t<0,i=n?e.length:0,r=pa(0,i,this.__views__),a=r.start,s=r.end,c=s-a,p=o?s:a-1,l=this.__iteratees__,u=l.length,d=0,y=zn(c,this.__takeCount__);if(!n||!o&&i==c&&y==c)return $i(e,this.__actions__);var f=[];e:for(;c--&&d<y;){for(var h=-1,m=e[p+=t];++h<u;){var g=l[h],_=g.iteratee,v=g.type,b=_(m);if(2==v)m=b;else if(!b){if(1==v)continue e;break e}}f[d++]=m}return f},yo.prototype.at=gs,yo.prototype.chain=function(){return hs(this)},yo.prototype.commit=function(){return new mo(this.value(),this.__chain__)},yo.prototype.next=function(){this.__values__===o&&(this.__values__=hc(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?o:this.__values__[this.__index__++]}},yo.prototype.plant=function(e){for(var t,n=this;n instanceof ho;){var i=Va(n);i.__index__=0,i.__values__=o,t?r.__wrapped__=i:t=i;var r=i;n=n.__wrapped__}return r.__wrapped__=e,t},yo.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof go){var t=e;return this.__actions__.length&&(t=new go(this)),(t=t.reverse()).__actions__.push({func:ms,args:[os],thisArg:o}),new mo(t,this.__chain__)}return this.thru(os)},yo.prototype.toJSON=yo.prototype.valueOf=yo.prototype.value=function(){return $i(this.__wrapped__,this.__actions__)},yo.prototype.first=yo.prototype.head,Pt&&(yo.prototype[Pt]=function(){return this}),yo}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(Ct._=Bn,define(function(){return Bn})):Tt?((Tt.exports=Bn)._=Bn,Ot._=Bn):Ct._=Bn}).call(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],ActionManager:[function(e,t,n){"use strict";cc._RF.push(t,"31feaew/dlJe7t1AChXUQIm","ActionManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/core/base/SingleBase"),a=e("../../../qte/core/utils/ValueUtils"),s=e("../display/base/DisplayCocosAni"),c=e("../display/base/DisplaySpine"),p=e("../display/DisplayObjectManager"),l=e("./AnimationData"),u=e("./tween/TweenAppear"),d=e("./tween/TweenAppearOpacity"),y=e("./tween/TweenBlink"),f=e("./tween/TweenCustomCurve"),h=e("./tween/TweenCustomLine"),m=e("./tween/TweenDisappear"),g=e("./tween/TweenDisappearOpacity"),_=e("./tween/TweenFlyInto"),v=e("./tween/TweenFlyOut"),b=e("./tween/TweenMoveBezier"),C=e("./tween/TweenMoveLine"),O=e("./tween/TweenRotation"),T=e("./tween/TweenScale"),w=e("./tween/TweenShake"),I=e("./tween/TweenSpecial"),P=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._tweenMap=new Map,t._tweenDataMap=new Map,t}return i(t,e),t.prototype.initInstance=function(){for(var e in l.TweenType){var t=this.getImpClsByType(Number(e));t&&this._tweenMap.set(Number(e),t)}},t.prototype.reloadTweenData=function(e){this._tweenDataMap.clear();for(var t=0,n=e.length;t<n;t++){var o=e[t].value;if(o.animType===l.AnimType.TWEEN){var i=e[t].id,r=this.getTweenActionDatas(e[t].componentId,o);this._tweenDataMap.set(i,r)}}},t.prototype.getTweenData=function(e){return this._tweenDataMap.get(e)},t.prototype.getTweenActionDatas=function(e,t){var n=null,o=null,i=t.anim,r=this._tweenMap.get(i.type);if(!r)return yy.error("\u7c7b\u578b"+i.type+"\u7684\u5b9e\u4f8b\u4e0d\u5b58\u5728"),[];"string"==typeof e?(console.log("getTweenActionDatas",e),n=yy.single.instance(p.default).getDisplayObjectById(e).node):n=e;var a=r.actionData(n,t.speed,i);return o=this.structDAta(r.relative,t.repeat,t.easing,a),i.type===l.TweenType.MOVE_BEZIRE&&(o[1].type="bezier"),o},t.prototype.structDAta=function(e,t,n,o){var i=[];return o.forEach(function(t){var o={type:"",time:0,props:null,easing:null};o.type=e?"by":"to",o.time=a.default.setOneDecimal(t.duration,3),o.props=t.props,o.easing=n,i.push(o)}),i},t.prototype.getClipCurveData=function(){return{props:{scale:[{frame:0,value:{__type__:"cc.Vec2",x:.5,y:1}},{frame:.5833333333333334,value:{__type__:"cc.Vec2",x:1,y:1}}]}}},t.prototype.getSpineData=function(e,t){var n=null;n="string"==typeof e?yy.single.instance(p.default).getDisplayObjectById(e):e.getComponent(c.default);var o=t.value,i=t.after,r={},a={},s=o.anim;return r.animation=s.animName,r.timeScale=s.timeScale,r.loop=!1,r.duration=n.getSpineAnimationTime(s.animName)/r.timeScale,a.loop=i.loop,a.endTimeScale=i.endTimeScale,a.animList=i.animList,r.after=a,r},t.prototype.getCocosAniData=function(e,t){for(var n=("string"==typeof e?yy.single.instance(p.default).getDisplayObjectById(e):e.getComponent(s.default)).node.getChildByName("cocosAniNode").getComponent(cc.Animation).getClips(),o=t.value,i=t.after,r={},a={},c=o.anim,l=null,u=0;u<n.length;u++){var d=n[u];if(d.name===t.value.anim.animName){l=d;break}}return r.animation=c.animName,r.timeScale=c.timeScale,r.loop=!1,r.duration=l._duration,r.curveData=l.curveData,r.events=l.events,r.wrapMode=l.wrapMode,r.sample=l.sample,r.speed=l.speed,r._name=l.name,r._duration=l._duration,a.loop=i.loop,a.endTimeScale=i.endTimeScale,a.animList=i.animList,r.after=a,r},t.prototype.getActionByType=function(e,t){return console.log("getActionByType",e,t.value),t.value.animType===l.AnimType.SPINE?this.getSpineData(e,t):t.value.animType===l.AnimType.ANIM?this.getCocosAniData(e,t):this.getTweenActionDatas(e,t.value)},t.prototype.getImpClsByType=function(e){var t=null;if(e>=l.TweenType.SIWEI_SIZE_CHANGE&&e<=l.TweenType.SIWEI_MOVE_LINE)return new I.default;switch(e){case l.TweenType.APPEAR:t=new u.default;break;case l.TweenType.APPEAR_OPACITY:t=new d.default;break;case l.TweenType.BLINK:t=new y.default;break;case l.TweenType.CUSTOM_LINE:t=new h.default;break;case l.TweenType.CUSTON_CURVE:t=new f.default;break;case l.TweenType.DISAPPEAR:t=new m.default;break;case l.TweenType.DISAPPEAR_OPACITY:t=new g.default;break;case l.TweenType.FLY_INTO:t=new _.default;break;case l.TweenType.FLY_OUT:t=new v.default;break;case l.TweenType.MOVE_BEZIRE:t=new b.default;break;case l.TweenType.MOVE_LINE:t=new C.default;break;case l.TweenType.ROTATION:t=new O.default;break;case l.TweenType.SCALE:t=new T.default;break;case l.TweenType.SHAKE:t=new w.default}return t},t}(r.SingleBase);n.default=P,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../display/DisplayObjectManager":"DisplayObjectManager","../display/base/DisplayCocosAni":"DisplayCocosAni","../display/base/DisplaySpine":"DisplaySpine","./AnimationData":"AnimationData","./tween/TweenAppear":"TweenAppear","./tween/TweenAppearOpacity":"TweenAppearOpacity","./tween/TweenBlink":"TweenBlink","./tween/TweenCustomCurve":"TweenCustomCurve","./tween/TweenCustomLine":"TweenCustomLine","./tween/TweenDisappear":"TweenDisappear","./tween/TweenDisappearOpacity":"TweenDisappearOpacity","./tween/TweenFlyInto":"TweenFlyInto","./tween/TweenFlyOut":"TweenFlyOut","./tween/TweenMoveBezier":"TweenMoveBezier","./tween/TweenMoveLine":"TweenMoveLine","./tween/TweenRotation":"TweenRotation","./tween/TweenScale":"TweenScale","./tween/TweenShake":"TweenShake","./tween/TweenSpecial":"TweenSpecial"}],AddChildComponent:[function(e,t,n){"use strict";cc._RF.push(t,"fdd0aKc+G5E25rn3m+hWNSW","AddChildComponent");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.AddChildComponent=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){console.log("###-AddChildComponent-body---\x3e",e);var t=yy.single.instance(a.default),n=t.getDisplayObjectById(e.payload.id),o=t.getDisplayObjectById(e.payload.childId),i=o.node.parent;console.log("###-AddChildComponent-oldParent---\x3e",i),e.payload.oldParent=i,o.node.parent=n.node,n.addChildObject(e.payload.childId,o),this.oldBody=e},t.prototype.undo=function(e){console.log("###--AddChildComponent---undo-body---\x3e",e);var t=yy.single.instance(a.default);t.getDisplayObjectById(e.payload.childId).node.parent=e.payload.oldParent,t.getDisplayObjectById(e.payload.id).removeChildObject(e.payload.childId)},t}(r.default);n.AddChildComponent=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],AddCmptCommand:[function(e,t,n){"use strict";cc._RF.push(t,"e6a1eXaS6pNUqyHNe3Ww52x","AddCmptCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0}),n.AddCmptCommand=void 0;var s=e("../../../../../qte/core/extension/command/SimpleCommand"),c=e("../../../../common/EditorEnum"),p=e("../../../display/base/DisplayObject"),l=e("../../../display/DisplayObjectFactory"),u=e("../../../display/DisplayObjectManager"),d=e("../../../display/TemplateInterpreter"),y=e("../../../proxy/ComptData"),f=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=e.payload.id,n=e.payload.type,o=e.payload.properties,i=e.payload.spineData,r=e.payload.cocosAniData,a=new y.default;a.id=t,a.type=n,a.properties=o,a.spineData=i,a.cocosAniData=r,a.dragable=e.payload.dragable,a.deletable=e.payload.deletable,"object"==typeof e.payload.editable&&e.payload.editable.properties?(a.editable=new y.EditableProperties,a.editable.properties=e.payload.editable.properties):a.editable=e.payload.editable,void 0!==e.payload.subType&&(a.subType=e.payload.subType),this.createNode(a,t),this.oldBody=t,yy.log("----AddCmptCommand---execute---\x3e",this.oldBody)},t.prototype.createNode=function(e,t){return r(this,void 0,void 0,function(){var n,o,i,r;return a(this,function(){return n=yy.single.instance(l.default),o=yy.single.instance(u.default),i=null,i=n.getDisplayNodeAsync(e,!0),yy.single.instance(d.default).addObjFunc(i,c.CmptLayer.OBJECT_LAYER),r=i.getComponent(p.default),o.addDisplayObject(t,r),[2]})})},t.prototype.undo=function(e){yy.log("----AddCmptCommand---undo---\x3e",e),yy.single.instance(u.default).removeDisplayObject(e)},t}(s.default);n.AddCmptCommand=f,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../common/EditorEnum":"EditorEnum","../../../display/DisplayObjectFactory":"DisplayObjectFactory","../../../display/DisplayObjectManager":"DisplayObjectManager","../../../display/TemplateInterpreter":"TemplateInterpreter","../../../display/base/DisplayObject":"DisplayObject","../../../proxy/ComptData":"ComptData"}],AnimDisplayControl:[function(e,t,n){"use strict";cc._RF.push(t,"c6cf2Suf7pFcIhq6TXIiz04","AnimDisplayControl");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../qte/core/base/SingleBase"),a=e("../../../../qte/core/utils/MathUtil"),s=e("../../../../qte/core/utils/ValueUtils"),c=e("../../../common/EditorEnum"),p=e("../../display/TemplateInterpreter"),l=e("../../proxy/DataCenterBridge"),u=e("../AnimationData"),d=e("./AnimDisplayManager"),y=e("./AnimDisplayObject"),f=function(){function e(e,t){this.actionId="",this.actionType=-1,this.actionId=e,this.value=t}return Object.defineProperty(e.prototype,"tweenType",{get:function(){return this.value.anim.type},enumerable:!1,configurable:!0}),e}(),h=function(){function e(e){this._fragmentId="-1",this._actionMap=new Map,this._fragmentId=e}return Object.defineProperty(e.prototype,"fragmentId",{get:function(){return this._fragmentId},enumerable:!1,configurable:!0}),e.prototype.addAction=function(e,t,n){var o=new f(n,t);this._actionMap.has(e)?this._actionMap.get(e).push(o):this._actionMap.set(e,[o])},e.prototype.getAction=function(e){return this._actionMap.has(e)?this._actionMap.get(e):[]},e}(),m=function(){this._editActionId="",this._optionLength=0},g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._editFragment="",t._cmptEditFragment="",t._editMode=c.EditMode.NORMAL,t}return i(t,e),Object.defineProperty(t.prototype,"editMode",{get:function(){return this._editMode},set:function(e){this._editMode=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isEditingComponentAnimations",{get:function(){return this._isEditingComponentAnimations},set:function(e){this._isEditingComponentAnimations=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"idAnimationEdit",{get:function(){return this._isEditingComponentAnimations||this.editMode===c.EditMode.ANIM},enumerable:!1,configurable:!0}),t.prototype.initInstance=function(){this._fragments=new Map,this.customLineData=new m,this._animdisplayManager=yy.single.instance(d.default)},t.prototype.endCustomLine=function(){this.editType=u.TweenEditType.VERTEX,this.setActiveAction(this.customLineData._editActionId)},t.prototype.resetMode=function(){this._animdisplayManager.clearAnimDisplayObject(),this._fragments=new Map,this.customLineData=new m,this.editType=u.TweenEditType.DEFAULT},t.prototype.getEditType=function(){return this.editType},t.prototype.drawMouseLine=function(e){var t=this._animdisplayManager.getAnimDisplayObjects(this.customLineData._editActionId);!t||t.length<=0||(this.customLineData._editTweenType===u.TweenType.CUSTOM_LINE?this._animdisplayManager.drawCustomLine(!1,t,e):this.customLineData._editTweenType===u.TweenType.CUSTON_CURVE&&this._animdisplayManager.drawCustomCurve(t,e))},t.prototype.drawDisplayObj=function(e){if(!this.isDuplicateNode(e)){var t=this.customLineData._editActionId,n=this.customLineData._editTweenType,o=this._animdisplayManager.getAnimDisplayObjects(t);if(n===u.TweenType.CUSTON_CURVE&&o.length>30){var i=this.getActionOption("-1");return this.saveOptionData(t,i),void this.endCustomLine()}var r=this._animdisplayManager.getAnimDisplayObjects(t),a="cid_"+this.customLineData._editComponentId.toString()+"_"+t+"_"+(r.length+1),s=null;(s=0===r.length?this._animdisplayManager.createAnimObject(a,{x:e.x,y:e.y},0,2):this._animdisplayManager.createAnimObject(a,{x:e.x,y:e.y})).name=a;var l=s.getComponent(y.default);l.displayType=n,this._animdisplayManager.addAnimDisplayObject(a,l,t),yy.single.instance(p.default).addObjFunc(s,c.CmptLayer.ANIM_LAYER),this.drawCustomPath(t,n)}},t.prototype.updateDispObject=function(e,t){for(var n=0,o=this.getActionsByCmptID(e);n<o.length;n++){var i=o[n],r=i.value.anim;if(r.type!==u.TweenType.SIWEI_MOVE_LINE){if(r.relative)for(var a=0,s=this._animdisplayManager.getAnimDisplayObjects(i.actionId);a<s.length;a++){var c=s[a];l=new cc.Vec3(t.x,t.y),this.updateDisplayObj(c,l,i.actionId)}}else{var p=this._animdisplayManager.getAnimDisplayObjects(i.actionId),l=new cc.Vec3(t.x,t.y);this.updateDisplayObj(p[0],l,i.actionId)}}this.updateActionOption(e)},t.prototype.updateDisplayObj=function(e,t,n){this._animdisplayManager.updateDisplayObjectDeltaPos(n,e,t);var o=this._animdisplayManager.getAnimDisplayObjects(n);this._animdisplayManager.setDisObjectDir(o)},t.prototype.getActionOption=function(e){if("-1"!==this.customLineData._editActionId){for(var t=this.getActiveAction().id,n=new d.OptionData,o=this._animdisplayManager.getAnimDisplayObjects(t),i=0;i<o.length;i++){var r=this._animdisplayManager.getAnimDisplayObject(o[i]);n.points.push({x:r.node.x,y:r.node.y})}return n.currIndex=this._animdisplayManager.getIndex(e),n.offset=this._animdisplayManager.getOffestPosition(o[0],this.customLineData._editComponentId),n}},t.prototype.reSetEditType=function(){var e=u.TweenEditType.DEFAULT;switch(this.customLineData._editTweenType){case u.TweenType.MOVE_LINE:case u.TweenType.MOVE_BEZIRE:case u.TweenType.SIWEI_MOVE_LINE:e=u.TweenEditType.VERTEX;break;case u.TweenType.CUSTOM_LINE:case u.TweenType.CUSTON_CURVE:e=this.customLineData._optionLength>1?u.TweenEditType.VERTEX:u.TweenEditType.DRAW}this.editType=e},t.prototype.isDuplicateNode=function(e){var t=this._animdisplayManager.getAnimDisplayObjects(this.customLineData._editActionId);if(!t||t.length<1)return!1;var n=t[t.length-1],o=this._animdisplayManager.getAnimDisplayObject(n),i=a.default.pointSubtractionForPoint(e,o.node.position);return Math.abs(i.x)<30&&Math.abs(i.y)<30},t.prototype.updateActionOption=function(e,t){void 0===t&&(t=!0);var n=this.getAllActionsByCmptID(e);if(n&&!(n.length<=0))for(var o=0;o<n.length;o++){var i=n[o].value.anim;if(i.option&&!(i.option.length<=0)){i.option=this._animdisplayManager.resetOption(e,n[o].actionId,i);var r=this._animdisplayManager.getAnimDisplayObjects(n[o].actionId);r.length>0&&(i.option.offset=this._animdisplayManager.getOffestPosition(r[0],e)),t&&this.saveOptionData(n[o].actionId,i.option,n[o].actionType)}}},t.prototype.showAllPah=function(e,t){if(e){if(this.editType=u.TweenEditType.DEFAULT,this._animdisplayManager.clearAnimDisplayObject(),"object"==typeof e)for(var n=0,o=e;n<o.length;n++){var i=o[n],r=this.getActionsByCmptID(i);!r||r.length<=0||this.showActionPath(r,i)}else{var a=this.getActionsByCmptID(e.toString());if(!a||a.length<=0)return;this.showActionPath(a,e.toString())}if(t){this._animdisplayManager.clearSelect(),this._animdisplayManager.setSelectAction(t,!0);var s=this.getActiveAction();if(!s)return;this.setCustomData(s),this.reSetEditType()}}},t.prototype.showActionPath=function(e,t){for(var n=0,o=e;n<o.length;n++){var i=o[n],r=i.value.anim;(!r.option||!r.option.points||r.option.points.length<=1)&&(r=this._animdisplayManager.structPointsData(r,t),this.saveOptionData(i.actionId,r.option)),this._animdisplayManager.addDisObjectByDatas(r.option,t,r.type,i.actionId),this.drawCustomPath(i.actionId,r.type),this._animdisplayManager.setOffsetPos(t,i.actionId,r),r.option=this._animdisplayManager.resetOption(t,i.actionId,r),r.type!==u.TweenType.SIWEI_MOVE_LINE&&(r.option.currIndex=-1),this._animdisplayManager.setSelectAction(i.actionId,!0)}},t.prototype.selecAnimObj=function(e){if("-1"!==e){var t=this._animdisplayManager.getActionById(e);this._animdisplayManager.clearSelect(),this._animdisplayManager.setSelectId(e);var n=yy.single.instance(l.default);this.editMode===c.EditMode.ANIM?n.setAnimModelActiveActionId(t):this.isEditingComponentAnimations&&n.setCmptModelActiveActionId(t);var o=this.getActiveAction();o&&this.setCustomData(o)}},t.prototype.setActiveAction=function(e){if(this.getActiveAction()){var t=this._animdisplayManager.getAnimDisplayObjects(e);this._animdisplayManager.getAnimDisplayObject(t[t.length-1]).changeEndSprite(),this._animdisplayManager.clearSelect(),this._animdisplayManager.setSelectAction(e,!0)}else this._animdisplayManager.clearSelect()},t.prototype.drawCustomPath=function(e,t){var n=this._animdisplayManager.getAnimDisplayObjects(e);!n||n.length<=0||(t===u.TweenType.MOVE_LINE?this._animdisplayManager.drawCustomLine(!0,n):t===u.TweenType.MOVE_BEZIRE?this._animdisplayManager.drawBezier(n):t===u.TweenType.CUSTOM_LINE||t===u.TweenType.SIWEI_MOVE_LINE?this._animdisplayManager.drawCustomLine(!0,n):t===u.TweenType.CUSTON_CURVE&&this._animdisplayManager.drawCustomCurve(n),this._animdisplayManager.setDisObjectDir(n))},t.prototype.setFragmentId=function(e){this._editMode===c.EditMode.ANIM?this._editFragment=e:this.isEditingComponentAnimations&&(this._cmptEditFragment=e)},t.prototype.getFlatActions=function(){var e=[],t=yy.single.instance(l.default);return this.editMode===c.EditMode.ANIM?e=t.getAnimModelFlatActions():this.isEditingComponentAnimations&&(e=t.getCmptModelFlatActions()),yy.cloneValues(e)},t.prototype.getActiveAction=function(){var e=[],t=yy.single.instance(l.default);return this.editMode===c.EditMode.ANIM?e=t.getAnimModelActiveAction():this.isEditingComponentAnimations&&(e=t.getCmptModelActiveAction()),s.default.clone(e)},t.prototype.getFragmentByActionId=function(e,t){var n="";return this._fragments.forEach(function(o){for(var i=0,r=o.getAction(t);i<r.length;i++)r[i].actionId===e&&(n=o.fragmentId)}),n},t.prototype.updateFragmentsMap=function(){var e=this.getFlatActions();e?this.setActionMap(e):yy.log("======allActions null===> ",this.editMode)},t.prototype.setActionMap=function(e){this._fragments=new Map;for(var t=0,n=e;t<n.length;t++){var o=n[t],i=o.value.anim,r=o.fragmentId;if(this.isCustomAnimType(i)){var a=null;this._fragments.has(r)?a=this._fragments.get(r):(a=new h(r),this._fragments.set(r,a)),a.addAction(o.componentId,o.value,o.id)}}},t.prototype.setCustomData=function(e){var t=e,n=t.value.anim;this.customLineData._editActionId=t.id,this.customLineData._editComponentId=t.componentId,this.customLineData._editTweenType=n.type,n.option&&n.option.points&&(this.customLineData._optionLength=n.option.points.length,this.customLineData._relative=n.relative)},t.prototype.getActionsByCmptID=function(e){var t="";this._editMode===c.EditMode.ANIM?t=this._editFragment:this.isEditingComponentAnimations&&(t=this._cmptEditFragment);var n=this._fragments.get(t);return n?n.getAction(e):[]},t.prototype.getAllActionsByCmptID=function(e){var t=[],n=yy.single.instance(l.default),o=yy.cloneValues(n.getCmptModelFlatActions()),i=yy.cloneValues(n.getAnimModelFlatActions());return o.forEach(function(n){if(n.componentId===e){var o=new f(n.id,n.value);o.actionType=0,t.push(o)}}),i.forEach(function(n){if(n.componentId===e){var o=new f(n.id,n.value);o.actionType=1,t.push(o)}}),t},t.prototype.isCustomAnimType=function(e){return e.type===u.TweenType.MOVE_LINE||e.type===u.TweenType.MOVE_BEZIRE||e.type===u.TweenType.CUSTOM_LINE||e.type===u.TweenType.CUSTON_CURVE||e.type===u.TweenType.SIWEI_MOVE_LINE},t.prototype.saveOptionData=function(e,t,n){void 0===n&&(n=-1),-1===n?this.editMode===c.EditMode.ANIM?yy.single.instance(l.default).setActionAnimOption(e,t):this.isEditingComponentAnimations&&yy.single.instance(l.default).setCmptActionOption(e,t):0===n?yy.single.instance(l.default).setCmptActionOption(e,t):yy.single.instance(l.default).setActionAnimOption(e,t)},t}(r.SingleBase);n.default=g,cc._RF.pop()},{"../../../../qte/core/base/SingleBase":void 0,"../../../../qte/core/utils/MathUtil":void 0,"../../../../qte/core/utils/ValueUtils":void 0,"../../../common/EditorEnum":"EditorEnum","../../display/TemplateInterpreter":"TemplateInterpreter","../../proxy/DataCenterBridge":"DataCenterBridge","../AnimationData":"AnimationData","./AnimDisplayManager":"AnimDisplayManager","./AnimDisplayObject":"AnimDisplayObject"}],AnimDisplayManager:[function(e,t,n){"use strict";cc._RF.push(t,"426ffFov6lFQ4HdZ5GZVs0C","AnimDisplayManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.OptionData=void 0;var r=e("../../../../qte/core/base/SingleBase"),a=e("../../../../qte/core/extension/action/ActionUtiil"),s=e("../../../../qte/core/utils/MathUtil"),c=e("../../../../qte/core/utils/ValueUtils"),p=e("../../../common/EditorEnum"),l=e("../../display/DisplayObjectManager"),u=e("../../display/TemplateInterpreter"),d=e("../AnimationData"),y=e("./AnimDisplayObject"),f=function(){this.currIndex=-1,this.points=[]};n.OptionData=f;var h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._curActiveAnimationId="",t._dpMap=null,t}return i(t,e),Object.defineProperty(t.prototype,"curActiveAnimationId",{get:function(){return this._curActiveAnimationId},set:function(e){this._curActiveAnimationId=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dpMap",{get:function(){return this._dpMap},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"componentIds",{get:function(){return this._componentIds},enumerable:!1,configurable:!0}),t.prototype.initInstance=function(){this._dpMap=new Map,this._componentIds=new Map},t.prototype.addDisObjectByDatas=function(e,t,n,o){if(e&&!(e.points.length<=0))for(var i=0;i<e.points.length;i++){var r="cid_"+t+"_"+o+"_"+i,a=this.createAnimObject(r,e.points[i],i,e.points.length),s=a.getComponent(y.default);a.name=r,s.displayType=n,this.addAnimDisplayObject(r,s,o),yy.single.instance(u.default).addObjFunc(a,p.CmptLayer.ANIM_LAYER)}},t.prototype.setOffsetPos=function(e,t,n){var o=n.option;if(!o||!o.offset)return o;if(n.relative){var i=yy.single.instance(l.default).getDisplayObjectById(e),r=this.getAnimDisplayObjects(t);if(r&&!(r.length<=0)){var a,s,c=this.getAnimDisplayObject(r[0]);a=c.node.x-i.node.x,s=c.node.y-i.node.y;for(var p=0,u=r;p<u.length;p++){var d=u[p],y=this.getAnimDisplayObject(d);y.node.x-=a-o.offset.x,y.node.y-=s-o.offset.y}}}},t.prototype.resetOption=function(e,t,n){var o=yy.single.instance(l.default).getDisplayObjectById(e),i=new f;if(!n.option||!n.option.points||n.option.points.length<2)return n.option;if(n.relative){var r,a,s=n.option.points;r=s[0].x-o.node.x,a=s[0].y-o.node.y;for(var p=0;p<s.length;p++){var u=s[p].x-(r-n.option.offset.x),y=s[p].y-(a-n.option.offset.y);i.points.push(c.default.setOneDecimal({x:u,y:y}))}}else i.points=n.option.points;return n.type===d.TweenType.SIWEI_MOVE_LINE&&(i.points[0]=c.default.setOneDecimal({x:o.node.x,y:o.node.y})),i.offset=n.option.offset,i.currIndex=n.option.currIndex,i},t.prototype.setDisObjectDir=function(e,t){if(void 0===t&&(t=!1),!(e.length<2))for(var n=0;n<e.length;n++){var o=null;if(0===n&&(o=this.getAnimDisplayObject(e[n]).node.getChildByName("ctrl_sp"),this.setAngle(e[n],e[n+1],o)),t)return;n===e.length-1&&(o=this.getAnimDisplayObject(e[n]).node.getChildByName("ctrl_sp"),this.setAngle(e[n-1],e[n],o))}},t.prototype.setAngle=function(e,t,n){var o=this.getAnimDisplayObject(e),i=this.getAnimDisplayObject(t),r=this.getAngle(cc.v2(o.node.x,o.node.y),cc.v2(i.node.x,i.node.y));n&&(n.angle=r)},t.prototype.getOffestPosition=function(e,t){var n=this.getAnimDisplayObject(e),o={x:0,y:0},i=yy.single.instance(l.default).getDisplayObjectById(t),r=n.node.position;return o.x=i.node.parent.convertToNodeSpaceAR(cc.v3(r.x,r.y)).x-i.node.x,o.y=i.node.parent.convertToNodeSpaceAR(cc.v3(r.x,r.y)).y-i.node.y,o},t.prototype.addAnimDisplayObject=function(e,t,n){this._dpMap.set(e,t),this._componentIds.has(n)?this._componentIds.get(n).push(e):this._componentIds.set(n,[e])},t.prototype.getIndex=function(e){var t=-1;return this._componentIds.forEach(function(n){n.indexOf(e)>-1&&(t=n.indexOf(e))}),t},t.prototype.getActionById=function(e){var t="";return this._componentIds.forEach(function(n,o){n.indexOf(e)>-1&&(t=o)}),t},t.prototype.getAnimDisplayObject=function(e){return this._dpMap.get(e)},t.prototype.getAnimDisplayObjects=function(e){return this._componentIds.has(e)?this._componentIds.get(e):[]},t.prototype.getComponetIdArray=function(){var e=[];return this._componentIds.forEach(function(t){e.push.apply(e,t)}),e},t.prototype.getObjByType=function(e,t){var n=[];return this._dpMap.forEach(function(o){o.displayType===t&&o.cid!==e&&n.push(o)}),n},t.prototype.setSelectId=function(e){var t=this.getAnimDisplayObject(e);t&&t.setSelect(!0)},t.prototype.setSelectAction=function(e,t){var n=this.getAnimDisplayObjects(e);if(n)for(var o=0;o<n.length;o++)this.getAnimDisplayObject(n[o]).setSelect(t)},t.prototype.clearSelect=function(){this._dpMap.forEach(function(e){e.setSelect(!1)})},t.prototype.structPointsData=function(e,t){var n=yy.single.instance(l.default).getDisplayObjectById(t),o=new f,i=0;return e.type===d.TweenType.MOVE_LINE||e.type===d.TweenType.SIWEI_MOVE_LINE?(o.points.push({x:n.node.position.x,y:n.node.position.y}),i=n.node.position.y>0?n.node.position.y-150:n.node.position.y+150,o.points.push({x:n.node.position.x,y:i})):e.type===d.TweenType.MOVE_BEZIRE&&(o.points.push({x:n.node.position.x,y:n.node.position.y}),i=n.node.position.y>0?-1:1,o.points.push({x:n.node.position.x,y:n.node.position.y+150*i}),o.points.push({x:n.node.position.x,y:n.node.position.y+200*i}),o.points.push({x:n.node.position.x,y:n.node.position.y+250*i})),o.offset={x:0,y:0},o.currIndex=-1,e.type===d.TweenType.SIWEI_MOVE_LINE&&(o.currIndex=1),e.option=o,e},t.prototype.clearAnimDisplayObject=function(){this._dpMap.forEach(function(e){e.clearEdgeline(),e.node.destroy()}),this._dpMap.clear(),this._dpMap=new Map,this._componentIds=new Map},t.prototype.updateDisplayObjectDeltaPos=function(e,t,n){var o=this.getAnimDisplayObject(t);if(o){o.node.position=o.node.position.add(n);var i=this.getAnimDisplayObjects(e);o.displayType===d.TweenType.MOVE_LINE?this.drawCustomLine(!0,i):o.displayType===d.TweenType.MOVE_BEZIRE?this.drawBezier(i):o.displayType===d.TweenType.CUSTOM_LINE||o.displayType===d.TweenType.SIWEI_MOVE_LINE?this.drawCustomLine(!0,i):o.displayType===d.TweenType.CUSTON_CURVE&&this.drawCustomCurve(i)}else yy.error("id = "+t+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728")},t.prototype.createAnimObject=function(e,t,n,o){var i=new cc.Node,r=i.addComponent(y.default),a=new cc.Node("ctrl_sp"),s=a.addComponent(cc.Sprite);s.sizeMode=cc.Sprite.SizeMode.CUSTOM;var c="img/arrow",l=(new cc.Color).fromHEX("#ffffff"),u=cc.v2(30,20);return 0===n?l=cc.Color.GREEN:n===o-1?l=cc.Color.RED:(u.x=20,c="singleColor"),yy.loader.loadRes(c,cc.SpriteFrame,function(e,t){e?yy.warn(e):s&&cc.isValid(i)&&(s.spriteFrame=t)}),i.x=t.x,i.y=t.y,i.width=u.x,i.height=u.y,i.scale=1,a.width=u.x,a.height=u.y,a.color=l,r.cid=e,i.addChild(a),i.group=p.EditorGroup.EDGE,i},t.prototype.drawBezier=function(e){for(var t,n=this.getAnimDisplayObject(e[0]),o=[],i=0;i<e.length;i++){var r=this.getAnimDisplayObject(e[i]).node;o.push([r.position.x,r.position.y])}if(o.length<4)yy.error("drawBezier points length \u5c0f\u4e8e 4");else{t=a.default.getBezierPoints(30,o[0],o[1],o[2],o[3]);var s=[];for(i=1;i<t.length;i++){var c=new cc.Vec3(t[i][0],t[i][1],0);s.push(c)}n.clearEdgeline(),n.drawLine(s),n.drawBezierVertex([o[0],o[2]],[o[1],o[3]])}},t.prototype.drawCustomLine=function(e,t,n){var o=this.getAnimDisplayObject(t[0]);if(o){var i=t.length-1;t.length>=2&&(i=t.length-2);var r=this.getAnimDisplayObject(t[t.length-1]),a=this.getAnimDisplayObject(t[i]);if(r.clearEdgeline(),e){a.clearEdgeline();var s=this.getNodePos(t);return o.clearEdgeline(),void o.drawLine(s,!1)}if(n){var c=[n];r.drawLine(c),0!==i&&a.clearEdgeline()}if(!n){a.clearEdgeline();var p=this.getNodePos(t);o.drawLastPos(p)}}},t.prototype.drawCustomCurve=function(e,t){var n=this.getAnimDisplayObject(e[0]);if(n){var o=this.getNodePos(e);t&&o.push(t);var i;i=o.length>2?a.default.getHermitePoints(o):o,n.drawLine(i,!1)}},t.prototype.getNodePos=function(e){for(var t=[],n=0;n<e.length;n++){var o=this.getAnimDisplayObject(e[n]);if(o){var i={x:o.node.x,y:o.node.y};t.push(i)}}return t},t.prototype.getAngle=function(e,t){var n=t.sub(e);return 90-s.default.calcYaw(cc.v2(n.x,n.y))},t}(r.SingleBase);n.default=h,cc._RF.pop()},{"../../../../qte/core/base/SingleBase":void 0,"../../../../qte/core/extension/action/ActionUtiil":void 0,"../../../../qte/core/utils/MathUtil":void 0,"../../../../qte/core/utils/ValueUtils":void 0,"../../../common/EditorEnum":"EditorEnum","../../display/DisplayObjectManager":"DisplayObjectManager","../../display/TemplateInterpreter":"TemplateInterpreter","../AnimationData":"AnimationData","./AnimDisplayObject":"AnimDisplayObject"}],AnimDisplayObject:[function(e,t,n){"use strict";cc._RF.push(t,"36741rBw+tEkJ68ftO2prmq","AnimDisplayObject");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../common/EditorEnum"),a=e("../../display/base/DisplayObject"),s=e("../AnimationData"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),Object.defineProperty(t.prototype,"displayType",{get:function(){return this._displayType},set:function(e){this._displayType=e,e===s.TweenType.SIWEI_MOVE_LINE?"0"===this.cid.split("_")[3]&&(this.editable=!1):this.editable=!0},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"oldProperties",{get:function(){return this._oldProperties},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"newProperties",{get:function(){return this._newProperties},enumerable:!1,configurable:!0}),t.prototype.setSelect=function(e){e?this.showEdgeColor(cc.Color.RED):this.showEdgeColor(cc.Color.BLACK),this._oldProperties={x:this.node.position.x,y:this.node.position.y}},t.prototype.showEdgeColor=function(e){var t=this.node.getChildByName("ctrl_sp"),n=null;t&&(n=t.getChildByName("edge_node")),n||((n=new cc.Node).addComponent(cc.Graphics),n.name="edge_node",n.group=r.EditorGroup.EDGE,t.addChild(n));var o=n.getComponent(cc.Graphics);o.strokeColor=e,o.lineWidth=3,o.clear(!0),o.moveTo(-this.node.width/2,-this.node.height/2),o.lineTo(-this.node.width/2,this.node.height/2),o.lineTo(this.node.width/2,this.node.height/2),o.lineTo(this.node.width/2,-this.node.height/2),o.lineTo(-this.node.width/2,-this.node.height/2),o.stroke()},t.prototype.clearEdgeline=function(){this._graphics&&this._graphics.clear()},t.prototype.drawLine=function(e,t){if(void 0===t&&(t=!0),!this._graphics){var n=this.node.getChildByName("line_node");n=new cc.Node,this._graphics=n.addComponent(cc.Graphics),n.name="line_node",this.node.addChild(n),this._graphics.strokeColor=cc.Color.BLUE,this._graphics.lineWidth=3}this._graphics.clear(),this._graphics.moveTo(0,0);var o=1;for(t&&(o=0);o<e.length;o++){var i=new cc.Vec3(e[o].x,e[o].y,0),r=this.node.convertToNodeSpaceAR(i);this._graphics.lineTo(r.x,r.y)}this._graphics.stroke()},t.prototype.drawLastPos=function(e){if(!(e.length<2)){if(!this._graphics){var t=this.node.getChildByName("line_node");t=new cc.Node,this._graphics=t.addComponent(cc.Graphics),t.name="line_node",this.node.addChild(t),this._graphics.lineWidth=3}this._graphics.strokeColor=cc.Color.BLUE;var n=e[e.length-1],o=e[e.length-2];this._graphics.moveTo(this.convertToNodeSpaceAR(this.node,o).x,this.convertToNodeSpaceAR(this.node,o).y),this._graphics.lineTo(this.convertToNodeSpaceAR(this.node,n).x,this.convertToNodeSpaceAR(this.node,n).y),this._graphics.stroke()}},t.prototype.convertToNodeSpaceAR=function(e,t){var n=new cc.Vec3(t.x,t.y,0);return e.convertToNodeSpaceAR(n)},t.prototype.drawBezierVertex=function(e,t){var n=this;e.length===t.length&&(e.forEach(function(e,o){var i=n.convertToNodeSpaceAR(n.node,cc.v2(e[0],e[1])),r=n.convertToNodeSpaceAR(n.node,cc.v2(t[o][0],t[o][1]));n._graphics.moveTo(i.x,i.y),n._graphics.lineTo(r.x,r.y)}),this._graphics.stroke())},t.prototype.changeEndSprite=function(){var e=this,t=this.node.getChildByName("ctrl_sp"),n=t.getComponent(cc.Sprite);n&&yy.loader.loadRes("img/arrow",cc.SpriteFrame,function(o,i){o?yy.warn(o):cc.isValid(e.node)&&(n.spriteFrame=i,t.width=30,t.height=20,t.color=cc.Color.RED)})},t}(a.default);n.default=c,cc._RF.pop()},{"../../../common/EditorEnum":"EditorEnum","../../display/base/DisplayObject":"DisplayObject","../AnimationData":"AnimationData"}],AnimTouchHandler:[function(e,t,n){"use strict";cc._RF.push(t,"ab813xy3d1O+bXe3uTs4HpM","AnimTouchHandler");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../qte/core/base/SingleBase"),a=e("../../qte/core/utils/ValueUtils"),s=e("../core/animation/AnimationData"),c=e("../core/animation/display/AnimDisplayControl"),p=e("../core/animation/display/AnimDisplayManager"),l=e("../core/collision/CollisionManager"),u=e("../core/command/operate/anim/UpdateAnimPropertiesCommand"),d=e("../core/command/operate/CommandFactory"),y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._selectId="-1",t._isHoldOn=!1,t._lastTimeStamp=-1,t._drawPointStamp=.07,t}return i(t,e),t.prototype.initInstance=function(){this._animDisplayManager=yy.single.instance(p.default),this._collisionManager=yy.single.instance(l.default),this._animDisplayControl=yy.single.instance(c.default),this._timearray=[]},t.prototype.onTouchStart=function(e){if(!this._animDisplayControl.idAnimationEdit)return this._selectId="-1",!1;var t=(new Date).getTime();this._lastTimeStamp=t,this._timearray.length>0&&(t-this._timearray[0])/1e3>.2&&(this._timearray=[]),this._timearray.push(t);var n=e.getLocation(),o=cc.v2(n.x-cc.winSize.width/2,n.y-cc.winSize.height/2);if(this._animDisplayControl.getEditType()===s.TweenEditType.DRAW)return this._selectId="-1",this._timearray.length>=2||(this._animDisplayControl.drawDisplayObj(o),!0);var i=this._collisionManager.testDisplayObject(o,this._animDisplayManager.getComponetIdArray(),this._animDisplayManager.dpMap);return this._selectId="-1"!==i?i:"-1",this._animDisplayControl.selecAnimObj(this._selectId),"-1"!==this._selectId},t.prototype.onTouchMove=function(e){if(!this._isHoldOn)return!1;var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2);if(this._animDisplayControl.editType===s.TweenEditType.DRAW&&this._animDisplayControl.customLineData._editTweenType===s.TweenType.CUSTOM_LINE){var o=(new Date).getTime();return(o-this._lastTimeStamp)/1e3>this._drawPointStamp&&(this._animDisplayControl.drawDisplayObj(n),this._lastTimeStamp=o),!0}if("-1"===this._selectId)return!1;var i=e.getDelta(),r=new cc.Vec3(a.default.setOneDecimal(i.x),a.default.setOneDecimal(i.y));return this._animDisplayControl.updateDisplayObj(this._selectId,r,this._animDisplayControl.customLineData._editActionId),!0},t.prototype.onTouchEnd=function(){if(2===this._timearray.length&&this._animDisplayControl.getEditType()===s.TweenEditType.DRAW){var e=this._animDisplayControl.getActionOption("-1");e.points.length>0&&(this._animDisplayControl.saveOptionData(this._animDisplayControl.customLineData._editActionId,e),this._animDisplayControl.endCustomLine())}return this._animDisplayControl.getEditType()===s.TweenEditType.DRAW||(this.updateVue(this._selectId),this._selectId="-1",!1)},t.prototype.mouseMove=function(e){var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2);if(this.onTouchMove(e),this._animDisplayControl.getEditType()===s.TweenEditType.DRAW)return cc.game.canvas.style.cursor="move",this._animDisplayControl.drawMouseLine(n),!0;if("-1"!==this._selectId)return!0;var o=this._collisionManager.testDisplayObject(n,this._animDisplayManager.getComponetIdArray(),this._animDisplayManager.dpMap),i="default";return"-1"!==o&&(i="move"),cc.game.canvas.style.cursor=i,"-1"!==o},t.prototype.mouseDown=function(){this._isHoldOn=!0},t.prototype.mouseUp=function(e){return this._isHoldOn=!1,this.onTouchEnd(e)},t.prototype.selectedDisplayObject=function(e){this._animDisplayControl.idAnimationEdit&&(this._animDisplayControl.updateFragmentsMap(),this._animDisplayControl.showAllPah(e))},t.prototype.updateVue=function(e){var t=this._animDisplayManager.getAnimDisplayObject(e);if(t){var n={id:e,newProperties:t.oldProperties,actionId:this._animDisplayManager.getActionById(e)},o={id:e,newProperties:{x:t.node.x,y:t.node.y},actionId:this._animDisplayManager.getActionById(e)};yy.single.instance(d.default).pushCommand(u.UpdateAnimPropertiesCommand,o,n);var i=this._animDisplayControl.getActionOption(e);this._animDisplayControl.saveOptionData(this._animDisplayControl.customLineData._editActionId,i)}},t}(r.SingleBase);n.default=y,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../../qte/core/utils/ValueUtils":void 0,"../core/animation/AnimationData":"AnimationData","../core/animation/display/AnimDisplayControl":"AnimDisplayControl","../core/animation/display/AnimDisplayManager":"AnimDisplayManager","../core/collision/CollisionManager":"CollisionManager","../core/command/operate/CommandFactory":"CommandFactory","../core/command/operate/anim/UpdateAnimPropertiesCommand":"UpdateAnimPropertiesCommand"}],AnimationData:[function(e,t,n){"use strict";cc._RF.push(t,"d5c6eBDab9Hi5HjQtWCPz8r","AnimationData");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.SpineActionData=n.CocosAniActionData=n.TweenData=n.ActionDataBase=n.TweenEditType=n.SpineType=n.TweenType=n.DirectionType=n.EPoint=n.SpineAfter=n.EAfter=n.BaseAfter=n.EAudio=n.EValue=n.EAction=n.EAnimation=n.ShakeDirections=n.AfterType=n.AnimType=n.MomentType=void 0,function(e){e[e.BEFORE=0]="BEFORE",e[e.AFTER=1]="AFTER"}(n.MomentType||(n.MomentType={})),function(e){e.TWEEN="tween",e.ANIM="cocosAni",e.SPINE="spine"}(n.AnimType||(n.AnimType={})),function(e){e[e.NONE=0]="NONE",e[e.HIDE=1]="HIDE"}(n.AfterType||(n.AfterType={})),function(e){e[e.VERTICAL=0]="VERTICAL",e[e.HORIZONTAL=1]="HORIZONTAL"}(n.ShakeDirections||(n.ShakeDirections={}));n.EAnimation=function(){};n.EAction=function(){};n.EValue=function(){};n.EAudio=function(){};var r=function(){};n.BaseAfter=r;var a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(r);n.EAfter=a;var s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(r);n.SpineAfter=s;n.EPoint=function(){},function(e){e[e.UP=0]="UP",e[e.RIGHT_UP=1]="RIGHT_UP",e[e.RIGHT=2]="RIGHT",e[e.RIGHT_DOWN=3]="RIGHT_DOWN",e[e.DOWN=4]="DOWN",e[e.LEFT_DOWN=5]="LEFT_DOWN",e[e.LEFT=6]="LEFT",e[e.LEFT_UP=7]="LEFT_UP"}(n.DirectionType||(n.DirectionType={})),function(e){e[e.DEFAULT=0]="DEFAULT",e[e.FLY_INTO=1]="FLY_INTO",e[e.FLY_OUT=2]="FLY_OUT",e[e.APPEAR=3]="APPEAR",e[e.DISAPPEAR=4]="DISAPPEAR",e[e.SCALE=5]="SCALE",e[e.ROTATION=6]="ROTATION",e[e.MOVE_LINE=7]="MOVE_LINE",e[e.MOVE_BEZIRE=8]="MOVE_BEZIRE",e[e.CUSTOM_LINE=9]="CUSTOM_LINE",e[e.CUSTON_CURVE=10]="CUSTON_CURVE",e[e.APPEAR_OPACITY=11]="APPEAR_OPACITY",e[e.DISAPPEAR_OPACITY=12]="DISAPPEAR_OPACITY",e[e.SHAKE=13]="SHAKE",e[e.BLINK=14]="BLINK",e[e.SIWEI_SIZE_CHANGE=100]="SIWEI_SIZE_CHANGE",e[e.SIWEI_SHAKE_X=101]="SIWEI_SHAKE_X",e[e.SIWEI_SHAKE_Y=102]="SIWEI_SHAKE_Y",e[e.SIWEI_REPEATEDLY=103]="SIWEI_REPEATEDLY",e[e.SIWEI_AFTER_APPEAR=104]="SIWEI_AFTER_APPEAR",e[e.SIWEI_STAGNATION=105]="SIWEI_STAGNATION",e[e.SIWEI_APPEAR=106]="SIWEI_APPEAR",e[e.SIWEI_DISAPPEAR=107]="SIWEI_DISAPPEAR",e[e.SIWEI_MOVE_LINE=108]="SIWEI_MOVE_LINE"}(n.TweenType||(n.TweenType={})),n.SpineType=15,function(e){e[e.DEFAULT=0]="DEFAULT",e[e.DRAW=1]="DRAW",e[e.VERTEX=2]="VERTEX"}(n.TweenEditType||(n.TweenEditType={}));var c=function(){};n.ActionDataBase=c;var p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(c);n.TweenData=p;var l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(c);n.CocosAniActionData=l;var u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t}(c);n.SpineActionData=u,cc._RF.pop()},{}],AnimationManager:[function(e,t,n){"use strict";cc._RF.push(t,"a720cBW26NEm4+LP1qjS4qW","AnimationManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/core/base/SingleBase"),a=e("../../utils/AudioUtil"),s=e("../../common/EditorEnum"),c=e("./ActionManager"),p=e("./AnimationData"),l=e("./AnimationStep"),u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._actionCount=0,t._actionFinishCount=0,t._rootNode=null,t}return i(t,e),t.prototype.play=function(e,t,n){var o=this;this._cb=n,this._data=t,this._rootNode=e,this._actionCount=0,this._actionFinishCount=0,this.initAppearDisplayObject();var i=[];for(var r in t.points){var s=t.points[r].fragmentId;i.push.apply(i,this.getFragmentData(s))}yy.single.instance(c.default).reloadTweenData(i);var p=[];p.push(t.audio.url),a.default.preLoadEffectList(p,null,function(){setTimeout(function(){a.default.playEffect(o._data.audio.url)},1e3*o._data.audio.delay),o.next()})},t.prototype.playStep=function(e,t,n){var o=this;this._rootNode=e,this._cb=n;var i=new l.default;yy.single.instance(c.default).reloadTweenData(t),i.play(this._rootNode,t,function(){o._cb&&o._cb(s.AnimPreviewType.FRAGMENT)})},t.prototype.next=function(){var e=this,t=this._data.points,n=function(n){o._actionCount++;var i=t[n],r=new l.default;setTimeout(function(){r.play(e._rootNode,e.getFragmentData(i.fragmentId),function(){e._actionFinishCount++,e.checkFinish()})},1e3*i.startTime)},o=this;for(var i in t)n(i);this.checkFinish()},t.prototype.isDisappear=function(e){return e===p.TweenType.FLY_INTO||e===p.TweenType.APPEAR||e===p.TweenType.APPEAR_OPACITY||e===p.TweenType.SIWEI_APPEAR||e===p.TweenType.SIWEI_AFTER_APPEAR},t.prototype.initAppearDisplayObject=function(){for(var e in this._data.points){var t=this._data.points[e],n=this.getFragmentData(t.fragmentId);if(n)for(var o=0,i=n;o<i.length;o++){var r=i[o],a=r.componentId,s=this._rootNode.getChildByName("cid_"+a);this.isDisappear(r.value.anim.type)&&(console.warn("\u7f29\u653e\u4e3a0"),s.setScale(0))}}},t.prototype.checkFinish=function(){this._actionFinishCount>=this._actionCount&&this._cb&&this._cb(s.AnimPreviewType.ALL)},t.prototype.getFragmentData=function(e){return this._data.fragments[e]},t}(r.SingleBase);n.default=u,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../common/EditorEnum":"EditorEnum","../../utils/AudioUtil":"AudioUtil","./ActionManager":"ActionManager","./AnimationData":"AnimationData","./AnimationStep":"AnimationStep"}],AnimationStep:[function(e,t,n){"use strict";cc._RF.push(t,"6947fk07FRMl44BpOT9dDlP","AnimationStep"),Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../utils/AudioUtil"),i=e("../../common/EditorEnum"),r=e("../display/DisplayObjectManager"),a=e("./AnimationData"),s=e("./AnimationManager"),c=e("./ClipAction"),p=e("./SpineAction"),l=e("./TweenAction"),u=function(){function e(){}return Object.defineProperty(e.prototype,"actionCount",{get:function(){return this._actionCount},set:function(e){this._actionCount=e},enumerable:!1,configurable:!0}),e.prototype.play=function(e,t,n){var i=this;this.actionCount=0,this._cb=n,this._data=t,this._rootNode=e,this.initAppearDisplayObject();for(var r=[],a=0,s=this._data;a<s.length;a++){var c=s[a];r.push(c.audio.url)}o.default.preLoadEffectList(r,null,function(){i.step()})},e.prototype.initAppearDisplayObject=function(){for(var e=0,t=this._data;e<t.length;e++){var n=t[e],o=n.componentId,a=yy.single.instance(r.default).getDisplayObjectById(o),c=this.getNodePath(a.node,i.CmptLayer.OBJECT_LAYER),p=cc.find(c,this._rootNode);yy.single.instance(s.default).isDisappear(n.value.anim.type)&&(console.warn("1111111111"),p.setScale(0))}},e.prototype.step=function(){var e=this._data;if(e.length<=0)this._cb&&this._cb();else{var t=e.shift();this.actionHandler(t),e.length>0&&e[0].moment===a.MomentType.BEFORE&&this.step()}},e.prototype.actionHandler=function(e){var t=this,n=e.componentId,o=yy.single.instance(r.default).getDisplayObjectById(n),s=this.getNodePath(o.node,i.CmptLayer.OBJECT_LAYER),c=cc.find(s,this._rootNode),p=e.value,l=this.createAction(p.animType);this.actionCount++,l.play(c,e,function(){t.playAudioEffect(e.audio,!1),t.actionCount--,e.after.type===a.AfterType.HIDE&&(c.scale=0),t.checkNext()}),this.playAudioEffect(e.audio,!0)},e.prototype.checkNext=function(){this.actionCount<=0&&this.step()},e.prototype.createAction=function(e){var t=null;return e===a.AnimType.ANIM?t=new c.default:e===a.AnimType.TWEEN?t=new l.default:e===a.AnimType.SPINE&&(t=new p.default),t},e.prototype.playAudioEffect=function(e,t){(0===e.moment&&t||1===e.moment&&!t)&&setTimeout(function(){o.default.playEffect(e.url)},1e3*e.delay)},e.prototype.getNodePath=function(e,t){for(var n="";e&&e.name!==t;)n=n?e.name+"/"+n:e.name,e=e.parent;return n},e}();n.default=u,cc._RF.pop()},{"../../common/EditorEnum":"EditorEnum","../../utils/AudioUtil":"AudioUtil","../display/DisplayObjectManager":"DisplayObjectManager","./AnimationData":"AnimationData","./AnimationManager":"AnimationManager","./ClipAction":"ClipAction","./SpineAction":"SpineAction","./TweenAction":"TweenAction"}],AnimationUtils:[function(e,t,n){"use strict";cc._RF.push(t,"50bcdBjzuNCG4mT/irW48v+","AnimationUtils"),Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function e(){}return e.getTimeBySpeed=function(e,t,n){return cc.Vec2.distance(t,n)/e},e}();n.default=o,cc._RF.pop()},{}],AssembleGroupCommand:[function(e,t,n){"use strict";cc._RF.push(t,"16afffx5KJEeZiYdS7OO37B","AssembleGroupCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.AssembleGroupCommand=void 0;var r=e("../../../../../qte/core/extension/command/CommandManager"),a=e("../../../../../qte/core/extension/command/SimpleCommand"),s=e("../../../display/DisplayObjectManager"),c=e("../../../hintline/HintLineManager"),p=e("../../simple/UpdateProp2VueCmd"),l=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e,t){var n=yy.single.instance(s.default),o=n.getDisplayObjectById(e.id),i=o.node,a=o.node.width,l=o.node.height,u=o.getNodeAngle(),d=[];if(o)for(var y=0,f=e.ids;y<f.length;y++){var h=f[y],m=n.getDisplayObjectById(h);d.push({subId:m.cid,x:m.node.x,y:m.node.y,angle:m.getNodeAngle(),width:m.node.width,height:m.node.height});var g=m.node.position.subtract(i.position);m.node.parent=i,m.node.position=g;var _=m.node.getSiblingIndex();yy.single.instance(s.default).updateDisplayObjectIndex(h,_),yy.single.instance(c.default).removeHintLineByDisplay(h),o.addSubObject(h,m)}var v=yy.single.instance(s.default);o.scheduleOnce(function(){v.dpIndexMap.forEach(function(e,t){v.isSubNode(t)||v.updateDisplayObjectIndex(t,v.getDisplayObjectById(t).node.getSiblingIndex())}),v.sortDisplayObjectByIndex()},0),this.oldBody=e.id,this.oldBody={id:e.id,pos:cc.v2(o.node.x,o.node.y),groupWidth:a,groupHeight:l,groupAngle:u,tempGroupPropertes:d},o.resetLayoutSize();var b=[];b.push(e.id),yy.single.instance(r.default).executeCommand(p.default,{selectIds:b,isUpdateGroupData:!0,notUpdate2Vue:!t,notRunCmd:!0}),yy.log("----AssembleGroupCommand---execute---\x3e",e),yy.single.instance(c.default).updateLineByDisplayScale(e.id)},t.prototype.undo=function(e){yy.log("----AssembleGroupCommand---undo---\x3e",e),yy.single.instance(s.default).removeGroupDisplayObject(e.id);var t=yy.single.instance(s.default),n=t.getDisplayObjectById(e.id);n.node.x=e.pos.x,n.node.y=e.pos.y,n.node.width=e.groupWidth,n.node.height=e.groupHeight,n.node.angle=e.groupAngle,n.node.cAngle=e.groupAngle;for(var o=0;o<e.tempGroupPropertes.length;o++){var i=e.tempGroupPropertes[o],r=t.getDisplayObjectById(i.subId);r&&(r.node.x=i.x,r.node.y=i.y,r.node.angle=i.angle,r.node.cAngle=i.angle,r.node.width=i.width,r.node.height=i.height)}},t}(a.default);n.AssembleGroupCommand=l,cc._RF.pop()},{"../../../../../qte/core/extension/command/CommandManager":void 0,"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager","../../../hintline/HintLineManager":"HintLineManager","../../simple/UpdateProp2VueCmd":"UpdateProp2VueCmd"}],AssetsManager:[function(e,t,n){"use strict";cc._RF.push(t,"13c7fzYwNpDzLrTfKalUMmX","AssetsManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../qte/core/base/SingleBase"),s=cc._decorator.ccclass,c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.loadBundel=function(e){return void 0===e&&(e="record"),new Promise(function(t,n){yy.loader.loadBundle(e,null,function(e,o){e?(yy.error(e),n(e)):t(o)})})},t.prototype.getBundel=function(e){return cc.assetManager.getBundle(e)},r([s],t)}(a.SingleBase);n.default=c,cc._RF.pop()},{"../qte/core/base/SingleBase":void 0}],AudioUtil:[function(e,t,n){"use strict";var o;cc._RF.push(t,"f8ee5lnJ3tJM7Az0Pm4Z3jZ","AudioUtil"),Object.defineProperty(n,"__esModule",{value:!0}),n.MusicType=void 0,function(e){e[e.SOUND=0]="SOUND",e[e.MUSIC=1]="MUSIC",e[e.LOOP_SOUND=2]="LOOP_SOUND"}(o=n.MusicType||(n.MusicType={}));var i=function(){function e(e,t){this._loaded=!1,this._audioSource=new cc.AudioSource,this._path="",this._path=e,this._musicType=t}return e.prototype.loadRes=function(e){var t=this;this._loaded||yy.loader.loadRes(this._path,cc.AudioClip,function(n,o){n?yy.warn("\u97f3\u9891\u8d44\u6e90\u52a0\u8f7d\u51fa\u9519:",t._path):(t._audioSource.clip=o,t._loaded=!0,e&&e())})},e.prototype.play=function(e){var t=this;this._isStop=!1,this._loaded?(this._audioSource.loop=e,this._audioSource.play()):this.loadRes(function(){t._isStop||t.play(e)})},e.prototype.isPlaying=function(){return this._audioSource.isPlaying},e.prototype.stop=function(){this._isStop=!0,this._loaded&&this._audioSource.stop()},e.prototype.pause=function(){this._loaded&&this._audioSource.pause()},e.prototype.resume=function(){this._loaded&&this._audioSource.resume()},e.prototype.destroy=function(){this._audioSource.stop(),this._audioSource=null},e}(),r=function(){function e(){}return e.preLoadEffectList=function(t,n,o){if(!t||t.length<=0)return yy.warn("\u97f3\u9891\u52a0\u8f7d\u5217\u8868\u4e3a\u7a7a"),void(o&&o());for(var i=t.length,r=0,a=function(t){e.preloadEffect(t,function(){r++,n&&n(i,r,t),r>=i&&o&&o()})},s=0,c=t;s<c.length;s++)a(c[s])},e.preloadEffect=function(t,n){if(""===t||!t)return yy.warn("\u97f3\u9891\u8d44\u6e90\u4e3a\u7a7a"),void(n&&n());var r=e._soundPool.get(t);if(r)n&&n();else{r=[];var a=new i(t,o.SOUND);a.loadRes(n),r.push(a),e._soundPool.set(t,r)}},e.playEffect=function(t){if(""!==t&&t&&!e._pauseFlag){var n=(new Date).getTime();if(t===e._curSound&&n-e._lastSoundTime<50)return;e._curSound=t,e._lastSoundTime=n;var r=e.getEffectFromPool(t);r?r.play(!1):(r=new i(t,o.SOUND),e.addEffectToPool(t,r),r.play(!1))}},e.stopEffect=function(){e._soundPool.forEach(function(e){if(e)for(var t=0,n=e;t<n.length;t++)n[t].stop()})},e.playLoopEffect=function(t){if(!e._pauseFlag){var n=e._loopSoundPool.get(t);n||(n=new i(t,o.LOOP_SOUND),e._loopSoundPool.set(t,n),n.play(!0))}},e.stopLoopEffect=function(t){var n=e._loopSoundPool.get(t);n&&(n.destroy(),e._loopSoundPool.delete(t))},e.playMusic=function(t){if(!e._pauseFlag){e._curMusic&&e._curMusic.stop();var n=e._musicPool.get(t);n?n.play(!0):(n=new i(t,o.MUSIC),e._musicPool.set(t,n),n.play(!0)),e._curMusic=n}},e.stopMusic=function(){e._curMusic&&e._curMusic.stop()},e.getEffectFromPool=function(t){var n=e._soundPool.get(t);if(n)for(var o=0,i=n;o<i.length;o++){var r=i[o];if(!r.isPlaying())return r}return null},e.addEffectToPool=function(t,n){var o=e._soundPool.get(t);o?o.push(n):((o=[]).push(n),e._soundPool.set(t,o))},e.setPauseFlag=function(t,n){void 0===n&&(n=0),e._level=n,e._pauseFlag=t,t?(e._curMusic&&e._curMusic.pause(),e.pauseLoopEffects()):(e._curMusic&&e._curMusic.resume(),e.resumeLoopEffects())},e.pauseLoopEffects=function(){e._loopSoundPool.forEach(function(e){e.pause()})},e.resumeLoopEffects=function(){e._loopSoundPool.forEach(function(e){e.resume()})},e._musicPool=new Map,e._soundPool=new Map,e._loopSoundPool=new Map,e._curSound="",e._lastSoundTime=0,e._pauseFlag=!1,e._level=0,e}();n.default=r,cc._RF.pop()},{}],BaseAction:[function(e,t,n){"use strict";cc._RF.push(t,"d55fdEQtCdOj6yQv/WSGQyX","BaseAction"),Object.defineProperty(n,"__esModule",{value:!0});n.default=function(){},cc._RF.pop()},{}],BaseTween:[function(e,t,n){"use strict";cc._RF.push(t,"ea220XtDvVPMZG/l1Qwpx6K","BaseTween"),Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../../../qte/core/utils/ValueUtils"),i=e("../AnimationData"),r=function(){function e(){this.tweenType=i.TweenType.DEFAULT}return e.prototype.getCustomPoints=function(e){var t,n=[];if(!e.option)return n;t=e.option;for(var i=0;i<t.points.length;i++)n.push(o.default.setOneDecimal({x:t.points[i].x,y:t.points[i].y}));return n},e.prototype.getCustomData=function(e,t){var n=[];if(e.length<=0)return n;n.push({props:{x:e[0].x,y:e[0].y},duration:0});for(var i=0;i<e.length-1;i++){var r=e[i+1],a=new cc.Vec2(r.x,r.y),s=0;i!==e.length-1&&(s=this.duration(i,e,t)),n.push({props:o.default.setOneDecimal({x:a.x,y:a.y}),duration:s})}return n},e.prototype.duration=function(e,t,n){var o=0,i=0;return t.forEach(function(n,r){if(r>t.length-2)return 0;var a=cc.Vec2.distance(n,t[r+1]);r===e&&(o=a),i+=a}),o/i*n},e}();n.default=r,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"../AnimationData":"AnimationData"}],ChangeAutoSubmitCommand:[function(e,t,n){"use strict";cc._RF.push(t,"c61dddr1fRJHI4oW69Sp09d","ChangeAutoSubmitCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.ChangeAutoSubmitCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../../stage/StageController"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){if(void 0!==e.submitType.hasRecover){var t=e.submitType.hasRecover;return this.oldBody={submitType:{hasRecover:!t}},void yy.instance(a.default).changeAutoRest(t)}var n=e.submitType.isAutoSubmit;this.oldBody={submitType:{isAutoSubmit:!n}},yy.instance(a.default).changeAutoSubmit(n)},t.prototype.undo=function(e){if(void 0===e.submitType.hasRecover){var t=e.submitType.isAutoSubmit;yy.instance(a.default).changeAutoSubmit(t)}else{var n=e.submitType.hasRecover;yy.instance(a.default).changeAutoRest(n)}},t}(r.default);n.ChangeAutoSubmitCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../stage/StageController":"StageController"}],ClipAction:[function(e,t,n){"use strict";cc._RF.push(t,"6ec20MbFZJFY71NzYBUGBvW","ClipAction");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("./BaseAction"),a=e("../display/base/DisplayCocosAni"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.play=function(e,t,n){var o=this,i=t.value,r=t.after,s=t.delay;e.active=!0;var c=i.anim,p=e.getComponent(a.default),l=e.getComponentInChildren(cc.Animation);if(""!==c.animName){l.on(cc.Animation.EventType.FINISHED,function(){o.complete(p,r,l,n)},this);var u=i.anim.animName,d=null;setTimeout(function(){for(var e=l.getClips(),t=0;t<e.length;t++)if(e[t].name==u){d=e[t];break}d.speed=c.timeScale,l.play(u)},1e3*s)}else this.complete(p,r,l,n)},t.prototype.complete=function(e,t,n,o){e.addListener(n),e.setTimeScale(t.endTimeScale),e.setLoop(t.loop),e.setAnimation(t.animList),o&&o()},t}(r.default);n.default=s,cc._RF.pop()},{"../display/base/DisplayCocosAni":"DisplayCocosAni","./BaseAction":"BaseAction"}],CocosAniManager:[function(e,t,n){"use strict";cc._RF.push(t,"e18efr0gC9OPK8+wDekqP0v","CocosAniManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../extends/cocosAni/CocosAniRect"),c=e("../../../qte/core/base/SingleBase"),p=e("../../../qte/core/utils/ValueUtils"),l=e("../../../qte/core/extension/command/CommandManager"),u=e("../../common/EditorEnum"),d=e("../command/simple/UpdateProp2VueCmd"),y=e("./base/DisplayCocosAni"),f=e("./DisplayObjectManager"),h=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initCocosAniData=function(e,t,n){return r(this,void 0,void 0,function(){var o=this;return a(this,function(){return[2,new Promise(function(i){var r=yy.single.instance(f.default),a=null;if(a=r.getDisplayObjectById(e)){var c=a.node.getChildByName("cocosAniNode"),u=c.getComponent(s.default),y=c.getComponent(cc.Animation);a.cocosAniData=n;var h=c.getContentSize();h.width=0===h.width?200:h.width,h.height=0===h.height?200:h.height,a.size=h,a.node.width=a.size.width,a.node.height=a.size.height,a.addListener(y),u.calcOffset(function(){setTimeout(function(){a.anchor=p.default.setOneDecimal(u.getAnchor(),2),a.resetCocosAniPos(),o.setProperty(a,t),a.refreshRectPoint(),yy.single.instance(l.default).executeCommand(d.default,{selectIds:[a.cid],isUpdateGroupData:!1,notUpdate2Vue:!1,notRunCmd:!0}),i()},20)})}})]})})},t.prototype.setProperty=function(e,t){var n={};for(var o in t)"x"!==o&&"y"!==o&&("scaleX"!==o?"scaleY"!==o?"loop"===o&&!0===t[o]||(n[o]=t[o]):e.node.height*=t.scaleY:e.node.width*=t.scaleX);yy.single.instance(f.default).updateDisplayObject(e.cid,n)},t.prototype.playActionByCmptLayer=function(e){var t=this;yy.single.instance(f.default).dpMap.forEach(function(n){var o=n;if(o.type===u.CmptType.COCOSANI){var i=t.getNodePath(o.node,u.CmptLayer.OBJECT_LAYER),r=cc.find(i,e);if(r){var a=r.getComponent(y.default);a.addListener(a.getComponentInChildren(cc.Animation)),a.setLoop(o.loop),a.setTimeScale(o.timeScale),a.setAnimation(o.actionList)}}})},t.prototype.getCocosAniProperties=function(e){var t=null,n=(t=yy.single.instance(f.default).getDisplayObjectById(e)).node.getChildByName("cocosAniNode");if(!t||!n)return null;var o={offsetX:0,offsetY:0,scaleX:1,scaleY:1};return o.scaleX=n.scaleX,o.scaleY=n.scaleY,o.offsetX=t.offset.x,o.offsetY=t.offset.y,o},t.prototype.getNodePath=function(e,t){for(var n="";e&&e.name!==t;)n=n?e.name+"/"+n:e.name,e=e.parent;return n},t}(c.SingleBase);n.default=h,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/extension/command/CommandManager":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../../common/EditorEnum":"EditorEnum","../../extends/cocosAni/CocosAniRect":"CocosAniRect","../command/simple/UpdateProp2VueCmd":"UpdateProp2VueCmd","./DisplayObjectManager":"DisplayObjectManager","./base/DisplayCocosAni":"DisplayCocosAni"}],CocosAniRect:[function(e,t,n){"use strict";cc._RF.push(t,"ef9a7EVeppMi4VnIGQeT6W7","CocosAniRect");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=cc._decorator.ccclass,s=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.spineW=-1,t.spineH=0,t.centerPoint=new cc.Vec2(0,0),t.anchorX=.5,t.anchorY=.5,t}return i(t,e),t.prototype.calcOffset=function(e){this.node.getComponent(cc.Animation),e&&e()},t.prototype.getAnchor=function(){return cc.v2(this.anchorX,this.anchorY)},r([a],t)}(cc.Component);n.default=s,cc._RF.pop()},{}],CocosExport:[function(e,t,n){"use strict";cc._RF.push(t,"fa7c1kADc5E0ZN67ZEo6tKU","CocosExport");var o=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0}),n.CocosExport=void 0;var r=e("../core/animation/ActionManager"),a=e("../core/animation/AnimationData"),s=e("../core/display/CocosAniManager"),c=e("../core/display/DisplayObjectManager"),p=e("../core/display/SpineManager"),l=e("./StageController"),u=function(){function e(){}return e.initExport=function(){var e=this;window.cocos||(window.cocos={}),window.cocos.screenshot=function(){return o(e,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,yy.single.instance(l.default).screenshot()];case 1:return[2,e.sent()]}})})},window.cocos.screenSignalShot=function(t){return o(e,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,yy.single.instance(l.default).screenshotSignal(t)];case 1:return[2,e.sent()]}})})},window.cocos.hideComponentShot=function(t){return o(e,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,yy.single.instance(l.default).hideComponentShot(t)];case 1:return[2,e.sent()]}})})},window.cocos.hideComponentShotWithSignal=function(t,n){return o(e,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,yy.single.instance(l.default).hideComponentShotWithSignal(t,n)];case 1:return[2,e.sent()]}})})},window.cocos.getSpineProperties=function(e){return yy.single.instance(p.default).getSpineProperties(e.id)},window.cocos.getCocosAniProperties=function(e){return yy.single.instance(s.default).getCocosAniProperties(e.id)},window.cocos.cancleStageMouse=function(){var e=yy.single.instance(l.default);e&&e.cancleStageMouse()},window.cocos.getActionData=function(e,t){return yy.single.instance(r.default).getActionByType(e,t)},window.cocos.getAnimaPropMap=function(){var e=new Map;return e.set(a.TweenType.DEFAULT,[]),e.set(a.TweenType.FLY_INTO,["move"]),e.set(a.TweenType.FLY_OUT,["move"]),e.set(a.TweenType.APPEAR,["scale"]),e.set(a.TweenType.DISAPPEAR,["scale"]),e.set(a.TweenType.SCALE,["scale"]),e.set(a.TweenType.ROTATION,["angle"]),e.set(a.TweenType.MOVE_LINE,["move"]),e.set(a.TweenType.MOVE_BEZIRE,["move"]),e.set(a.TweenType.CUSTOM_LINE,["move"]),e.set(a.TweenType.CUSTON_CURVE,["move"]),e.set(a.TweenType.APPEAR_OPACITY,["opcity"]),e.set(a.TweenType.DISAPPEAR_OPACITY,["opcity"]),e.set(a.TweenType.SHAKE,["angle"]),e.set(a.TweenType.BLINK,["opcity"]),e.set(a.TweenType.SIWEI_SIZE_CHANGE,["scale"]),e.set(a.TweenType.SIWEI_SHAKE_X,["scale"]),e.set(a.TweenType.SIWEI_SHAKE_Y,["move"]),e.set(a.TweenType.SIWEI_REPEATEDLY,["opacity"]),e.set(a.TweenType.SIWEI_AFTER_APPEAR,["scale","angle"]),e.set(a.TweenType.SIWEI_STAGNATION,["opacity"]),e.set(a.TweenType.SIWEI_APPEAR,["opacity"]),e.set(a.TweenType.SIWEI_DISAPPEAR,["opacity"]),e.set(a.TweenType.SIWEI_MOVE_LINE,["move"]),e.set(a.SpineType,["spine"]),e},window.cocos||(window.cocos={}),window.cocos.getWorldPos=function(e){return yy.single.instance(c.default).getWorldPos(e)},window.cocos.getWorldRect=function(e){return yy.single.instance(c.default).getWorldRect(e)},window.cocos.convertEditorWorldPos=function(e,t){return yy.single.instance(c.default).convertEditorWorldPos(e,t)},window.cocos.getRotateEWordRect=function(e){return yy.single.instance(c.default).getRotateEWordRect(e)},window.cocos.getEditPointWorldPos=function(e,t){return yy.single.instance(c.default).getEditPointWorldPos(e,t)},window.cocos.getNodeRealPos=function(e){console.log("call getNodeRealPos"),yy.single.instance(c.default).getNodeRealPos(e)},window.cocos.getCaptureByNode=function(e){return console.log("getCaptureByNode",e),yy.single.instance(c.default).getCaptureByNode(e)},window.cocos.getSmallCaptureById=function(t,n){return o(e,void 0,void 0,function(){return i(this,function(e){switch(e.label){case 0:return[4,yy.single.instance(c.default).getSubUpdateFinish(t,n)];case 1:return e.sent(),[2,yy.single.instance(c.default).getCaptureByNode(t)]}})})},window.cocos.getNodeCentEdtiorPos=function(e){return yy.single.instance(c.default).getNodeCentEdtiorPos(e)},window.cocos.showSocpedComponent=!1,window.cocos.getLableString=function(e){var t=yy.single.instance(c.default).getDisplayObjectById(e);if(!t)return"";var n=t.getEditBoxCom();return n?(yy.log("=====getLableString===>3===>",n.convertToRichText()),n.convertToRichText()):""},window.cocos.getLableSimplyString=function(e){var t=yy.single.instance(c.default).getDisplayObjectById(e);if(!t)return"";var n=t.getEditBoxCom();return n?(yy.log("###str------\x3e",n.getEditBoxLabel()),n.getEditBoxLabel()):""},window.cocos.getSpecialComponentProperties=function(e){var t=yy.single.instance(c.default).getDisplayObjectById(e.id);return t?{width:t.size.width,height:t.size.height,scaleX:t.componentScale.x,scaleY:t.componentScale.y}:""},window.cocos.setResetAndSubmitShow=function(e){var t=cc.find("Canvas");if(t){var n=t.getChildByName("stage_prefab");if(n){var o=n.getChildByName("com_BtnNextAndSubmit");o&&(o.active=e)}}},window.cocos.createQuestionContent=function(e){console.log("qte createQuestionContent",e),qte.createQuestionContent(e)}},e}();n.CocosExport=u,cc._RF.pop()},{"../core/animation/ActionManager":"ActionManager","../core/animation/AnimationData":"AnimationData","../core/display/CocosAniManager":"CocosAniManager","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/SpineManager":"SpineManager","./StageController":"StageController"}],CollisionManager:[function(e,t,n){"use strict";cc._RF.push(t,"8172axvY/dIfqH7cwUUjJF5","CollisionManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/core/base/SingleBase"),a=e("../../../qte/core/utils/MathUtil"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.testDisplayObject=function(e,t,n){for(var o=t.length-1;o>=0;o--){var i,r=t[o];if((i="string"==typeof r?n.get(r):n.get(r.id))&&i.editable&&a.default.isPosInRotationRect(e,i.rect,yy.checkValue(i.node.angle,0))&&(r=i.cid)&&r.length>0)return r}return"-1"},t.prototype.testDisplaySelect=function(e,t,n){for(var o=[],i=t.length-1;i>=0;i--){var r,s=t[i];if((r="string"==typeof s?n.get(s):n.get(s.id))&&r.editable){var c=cc.v2(r.node.x,r.node.y);a.default.isPosInRotationRect(c,e,yy.checkValue(r.node.angle,0))&&(s=r.cid)>0&&o.push(s)}}return o},t}(r.SingleBase);n.default=s,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/utils/MathUtil":void 0}],ComUtils:[function(e,t,n){"use strict";cc._RF.push(t,"2d6a9vlczNMV7hNQCLctrpz","ComUtils");var o=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0}),n.ComUtil=void 0;var r=function(){function e(){}return e.setLabelBlend=function(e){e._srcBlendFactor=cc.macro.BlendFactor.ONE,e._dstBlendFactor=cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA},e.addPremultiplyAlpha=function(e,t){e.setPremultiplyAlpha&&e.setPremultiplyAlpha(!0),t.spriteFrame=new cc.SpriteFrame(e),t.sizeMode=cc.Sprite.SizeMode.CUSTOM,t.srcBlendFactor=cc.macro.BlendFactor.ONE,t.dstBlendFactor=cc.macro.BlendFactor.ONE_MINUS_SRC_ALPHA},e.setGray=function(t,n){for(var o=e.getNodeList(t),i=0;i<o.length;i++){var r=o[i].getComponent(cc.Sprite),a=o[i].getComponent(sp.Skeleton);r&&e.setSpriteGray(r,n),a&&e.setSpineGray(a,n)}},e.setSpineGray=function(e,t){t?yy.loader.loadRes("material/gray-spine",cc.Material,function(t,n){t?yy.error("\u52a0\u8f7dgray-spine\u5931\u8d25"):(e.setMaterial(0,n),e.markForRender(!0))}):(e.setMaterial(0,cc.Material.getBuiltinMaterial("2d-spine")),e.markForRender(!0))},e.setSpriteGray=function(e,t){var n;n=t?cc.Material.getBuiltinMaterial("2d-gray-sprite"):cc.Material.getBuiltinMaterial("2d-sprite"),e.setMaterial(0,n)},e.setSvgShapeOpacity=function(e,t){return new Promise(function(n,o){cc.resources.load("effects/svg_graph",cc.EffectAsset,function(i,r){if(i)return yy.warn(i),void o(i);var a=cc.Material.create(r);a.setProperty("alphaU",t),e&&e.getComponent(cc.Graphics)&&e.getComponent(cc.Graphics).setMaterial(0,a),n(e)})})},e.getNodeList=function(e){for(var t=[e],n=e.getChildren(),o=0;o<n.length;o++){var i=this.getNodeList(n[o]);t.push.apply(t,i)}return t},e.setMateria=function(e,t){return o(this,void 0,void 0,function(){return i(this,function(){return[2,new Promise(function(n,o){cc.resources.load("effects/sprite-flip-effect",cc.EffectAsset,function(i,r){if(i)return yy.warn(i),void o(i);var a=cc.Material.create(r);t&&a.setProperty("filpType",Number(t.toFixed(1))),e&&e.setMaterial(0,a),n(e)})})]})})},e}();n.ComUtil=r,cc._RF.pop()},{}],CommandFactory:[function(e,t,n){"use strict";cc._RF.push(t,"2094fcLkfNGRZXxjtZLzpwV","CommandFactory");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../qte/core/base/SingleBase"),a=e("../../../../qte/core/extension/command/CommandManager"),s=e("./MergeCommand"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initInstance=function(){this._backwardCommandList=[],this._forwordCommandList=[],this._mergeCommond=new s.default},t.prototype.execute=function(e,t){var n=yy.single.instance(a.default).executeCommand(e,t,!0);this._mergeCommond.addCmdInstance(n)},t.prototype.pushCommand=function(e,t,n,o){var i=new e;i.newBody=t,i.oldBody=n,this._mergeCommond.addCmdInstance(i,null,o)},t.prototype.step=function(){var e=this._mergeCommond.cloneReverse();console.warn("\u524d\u8fdb+++++++++",e),this._backwardCommandList.push(e),this._forwordCommandList=[],this._mergeCommond=new s.default},t.prototype.backward=function(){var e=this._backwardCommandList.pop();e&&e.oldBody&&e.oldBody!=={}&&(console.warn("\u56de\u9000+++++++++",e),e.undo(e.oldBody,!1),this._forwordCommandList.length>t.MAX_LENGTH&&this._forwordCommandList.shift(),this._forwordCommandList.push(e.cloneReverse()))},t.prototype.forward=function(){var e=this._forwordCommandList.pop();e&&(console.warn("\u524d\u8fdb+++++++++",e),e.execute(e.newBody,!1),this._backwardCommandList.length>t.MAX_LENGTH&&this._backwardCommandList.shift(),this._backwardCommandList.push(e.cloneReverse()))},t.MAX_LENGTH=100,t}(r.SingleBase);n.default=c,cc._RF.pop()},{"../../../../qte/core/base/SingleBase":void 0,"../../../../qte/core/extension/command/CommandManager":void 0,"./MergeCommand":"MergeCommand"}],ComptData:[function(e,t,n){"use strict";cc._RF.push(t,"4f8068VkpRCkYctEb42wTLl","ComptData"),Object.defineProperty(n,"__esModule",{value:!0}),n.EditableProperties=n.StageInfo=n.CocosAniData=n.SpineData=void 0;n.default=function(){};var o=function(){function e(){}return Object.defineProperty(e.prototype,"imageNames",{get:function(){for(var e=[],t=0,n=this.images;t<n.length;t++){var o=n[t].split("/");e.push(o[o.length-1])}return e},enumerable:!1,configurable:!0}),e}();n.SpineData=o;n.CocosAniData=function(){};n.StageInfo=function(){};n.EditableProperties=function(){this.properties={}},cc._RF.pop()},{}],Config:[function(e,t,n){"use strict";cc._RF.push(t,"40aa4fNZ6tKpZ1JKjjblurJ","Config"),Object.defineProperty(n,"__esModule",{value:!0});var o=function(){function e(){}return e.DEBUG=!1,e.CUSTOM_CURSOR="",e}();n.default=o,cc._RF.pop()},{}],CreatePointCommand:[function(e,t,n){"use strict";cc._RF.push(t,"fe558ezwo5IEIj7OaVhQYm8","CreatePointCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.CreatePointCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){yy.single.instance(a.default).getDisplayObjectById(e.cmptIds).createEditPoint(e.pointDatas),this.oldBody={id:e.cmptIds,pointId:e.pointDatas.id,editable:e.pointDatas.editable}},t.prototype.undo=function(e){yy.single.instance(a.default).getDisplayObjectById(e.id).removeEditPoint(e.pointId,e.editable)},t}(r.default);n.CreatePointCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],CusorStyle:[function(e,t,n){"use strict";cc._RF.push(t,"daba3J092VFvYBBPua8rnwZ","CusorStyle"),Object.defineProperty(n,"__esModule",{value:!0});var o=e("../core/display/base/cmpt/DisplaySelect"),i=function(){function e(){}return e._getCursorTyle=function(e,t){var n=["n-resize","ne-resize","e-resize","se-resize","s-resize","sw-resize","w-resize","nw-resize","pointer"],i=Number(e);return e===o.SEL_POINT_ENUM.VERTEX?n[i-1]:(t>-45&&t<0?(i+=1)>7&&(i-=8):t>=-90&&t<=-45?(i+=2)>7&&(i-=8):t>=-180&&t<=-90?(i+=3)>7&&(i-=8):t<-180&&(i+=4)>7&&(i-=8),n[i])},e}();n.default=i,cc._RF.pop()},{"../core/display/base/cmpt/DisplaySelect":"DisplaySelect"}],CutPoint:[function(e,t,n){"use strict";cc._RF.push(t,"cce8dHJ80lL5ZtlAgqL5kqK","CutPoint");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../../qte/core/utils/ValueUtils"),s=e("../../../../common/ComUtils"),c=e("../../../../common/EditorEnum"),p=cc._decorator.ccclass,l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._pointData=null,t.circleRadius=20,t}return i(t,e),Object.defineProperty(t.prototype,"pointData",{get:function(){return this._pointData},enumerable:!1,configurable:!0}),t.prototype.getRect=function(){return new cc.Rect(this.node.x-this.node.width/2,this.node.y-this.node.height/2,this.node.width,this.node.height)},t.prototype.createPoint=function(e){this._pointData=e,this.node.setPosition(cc.v2(e.x,e.y)),this.node.setContentSize(cc.size(40,40)),this.node.group=c.EditorGroup.EDGE;var t=this.node.getComponent(cc.Graphics);t.clear(),t.strokeColor=cc.Color.BLUE,t.lineWidth=5,t.arc(0,0,this.circleRadius,0,180),t.stroke(),t.moveTo(0,-15),t.lineTo(0,15),t.stroke(),t.moveTo(-15,0),t.lineTo(15,0),t.stroke();var n=new cc.Node;n.setAnchorPoint(0,.5),n.setPosition(cc.v2(30,0)),n.color=cc.Color.BLACK,n.parent=this.node;var o=n.addComponent(cc.Label);o.string=e.label,o.fontSize=25,o.lineHeight=25,s.ComUtil.setLabelBlend(o);var i=n.addComponent(cc.LabelOutline);i.color=cc.Color.WHITE,i.width=2},t.prototype.updatePointActive=function(e){this.node.active=!e},t.prototype.updatePos=function(e){var t=a.default.setOneDecimal(e.x),n=a.default.setOneDecimal(e.y);this.node.setPosition(cc.v2(t,n)),this._pointData.x=t,this._pointData.y=n},r([p],t)}(cc.Component);n.default=l,cc._RF.pop()},{"../../../../../qte/core/utils/ValueUtils":void 0,"../../../../common/ComUtils":"ComUtils","../../../../common/EditorEnum":"EditorEnum"}],CutShapeManager:[function(e,t,n){"use strict";cc._RF.push(t,"4b510B2BgNMvrmdP2eHojxp","CutShapeManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../../qte/core/base/SingleBase"),a=e("../../../../common/EditorEnum"),s=e("../../DisplayObjectManager"),c=e("./DisplayCutShape"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.checkSelectedType=function(e){if("-1"===e)return null;var t=yy.single.instance(s.default).getDisplayObjectById(e);return t?t.type!==a.CmptType.CUTSHAPE?null:t:null},t.prototype.mouseBegin=function(e,t){var n=this.checkSelectedType(t);return!(!n||!n.touchBegin(e))},t.prototype.mouseMove=function(e,t){var n=this.checkSelectedType(t);return!!(n&&n.isEditing&&n.touchMove(e))},t.prototype.mouseUp=function(e){var t=this.checkSelectedType(e);return t&&t.updateShapeDatasToVue&&t.updateShapeDatasToVue(),!!(t&&t.isEditing&&t.touchUp())},t.prototype.reloadShap=function(e){var t=this;yy.single.instance(s.default).dpMap.forEach(function(n){if(n.type===a.CmptType.CUTSHAPE){var o=n,i=t.getNodePath(o.node,a.CmptLayer.OBJECT_LAYER),r=cc.find(i,e);if(r){var s=r.getComponent(c.default);s.initData(o.shapeDatas,o.subType),s.init(),s.initFinish()}}})},t.prototype.getNodePath=function(e,t){for(var n="";e&&e.name!==t;)n=n?e.name+"/"+n:e.name,e=e.parent;return n},t}(r.SingleBase);n.default=p,cc._RF.pop()},{"../../../../../qte/core/base/SingleBase":void 0,"../../../../common/EditorEnum":"EditorEnum","../../DisplayObjectManager":"DisplayObjectManager","./DisplayCutShape":"DisplayCutShape"}],DataCenterBridge:[function(e,t,n){"use strict";cc._RF.push(t,"f76d1lXu3hF6aoxoE2WmGpg","DataCenterBridge");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/core/base/SingleBase"),a=e("../../../qte/core/utils/ValueUtils"),s=e("../../../qte/core/extension/command/CommandManager"),c=e("../../stage/StageController"),p=e("../animation/display/AnimDisplayControl"),l=e("../animation/display/AnimDisplayManager"),u=e("../command/commond"),d=e("../command/operate/base/AddChildComponent"),y=e("../command/operate/base/ChangeAutoSubmitCommand"),f=e("../command/operate/base/UpdateComponentEditable"),h=e("../command/operate/CommandFactory"),m=e("../command/operate/cutShape/CreatePointCommand"),g=e("../command/operate/cutShape/RemovePointCommand"),_=e("../command/operate/cutShape/UpdatePointPosCommand"),v=e("../command/simple/UpdateProp2VueCmd"),b=e("../display/base/DisplayEditBox"),C=e("../display/DisplayObjectManager"),O=e("../hintline/HintLineDisplay"),T=e("../hintline/HintLineManager"),w=e("./MockData"),I=e("../command/operate/base/UpdateComponentOrderCommand"),P=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initInstance=function(){this.initData()},t.prototype.initData=function(){var e=this;w.default.init(),window.storeUnSubscribe=window._$store.subscribe(function(t){var n=yy.cloneValues(t);if(yy.log("subscribe mutation :",n),"updateComponentProperties"===n.type)e.updateDisplayObjectProperties(n.payload.id,n.payload.newProperties);else if("updateStage"===n.type)yy.single.instance(h.default).execute(u.UpdateBgCommand,n.payload),yy.single.instance(s.default).executeCommand(v.default,{selectIds:[n.payload.id],isUpdateGroupData:!1,notUpdate2Vue:!1,notRunCmd:!0});else if("updateComponentLevel"===n.type)e.updateComponentLevel(n.payload.id,n.payload.type);else if("addComponent"===n.type)e.addComponet(n.payload);else if("updateComponentOrder"===n.type||"updateSubComponentOrder"===n.type)e.updateComponentOrder(n.payload);else if("removeComponent"===n.type)e.removeComponet(n.payload);else if("updateComponentIdsWhenSeparateGroupComponent"===n.type)e.unassembleGroup(n.payload);else if("updateComponentIdsWhenCombineComponents"===n.type)e.assembleGroup(n.payload.id,n.payload.ids);else if("timeTravel/push"===n.type)yy.single.instance(h.default).step();else if("timeTravel/redo"===n.type)yy.single.instance(h.default).forward();else if("timeTravel/undo"===n.type)yy.single.instance(h.default).backward();else if("replaceCurrentComponentIds"===n.type)yy.single.instance(h.default).execute(u.ReplaceCmptCommand,{ids:n.payload,isVue:!0});else if("moduleAnimations/playFragment"===n.type){var o=yy.cloneValues(n.payload);yy.single.instance(c.default).playFragmentAnimation(o)}else if("moduleAnimations/playAnimation"===n.type)o=yy.cloneValues(n.payload),yy.single.instance(c.default).playGroupAnim(o);else if("rulerTick/addVerticalLine"===n.type)yy.single.instance(T.default).addHintLine(window._$store.state.rulerTick.verticalLines.length-1,!0,n.payload,O.LINT_DISPLAY_TYPE.VERTICAL);else if("rulerTick/removeVerticalLine"===n.type)yy.single.instance(T.default).removeHintLine(n.payload,!0,O.LINT_DISPLAY_TYPE.VERTICAL);else if("rulerTick/addHorizontalLine"===n.type)yy.single.instance(T.default).addHintLine(window._$store.state.rulerTick.horizontalLines.length-1,!0,n.payload,O.LINT_DISPLAY_TYPE.HORIZONTAL);else if("rulerTick/removeHorizontalLine"===n.type)yy.single.instance(T.default).removeHintLine(n.payload,!0,O.LINT_DISPLAY_TYPE.HORIZONTAL);else if("rulerTick/updateVerticalLine"===n.type)yy.single.instance(T.default).updateLinePosAndCollide(n.payload.index,!0,n.payload.value,O.LINT_DISPLAY_TYPE.VERTICAL,O.LINT_POS_TYPE.HORIZONTAL,!1);else if("rulerTick/updateHorizontalLine"===n.type)yy.single.instance(T.default).updateLinePosAndCollide(n.payload.index,!0,n.payload.value,O.LINT_DISPLAY_TYPE.HORIZONTAL,O.LINT_POS_TYPE.HORIZONTAL,!1);else if("updateMode"===n.type)yy.single.instance(h.default).execute(u.UpdateModeCommand,n.payload);else if("moduleAnimations/setActiveActionId"===n.type||"setComponentActiveActionId"===n.type)e.setActiveActionId(n.payload);else if("moduleAnimations/updateActionConfigData"===n.type||"updateComponentActionConfigData"===n.type){var i=yy.single.instance(p.default).getActiveAction();e.setActiveActionId(i.id)}else if("moduleAnimations/setFragmentId"===n.type||"setComponentFragmentId"===n.type)yy.single.instance(h.default).execute(u.SetFragmentCommand,{fragmentId:n.payload});else if("moduleAnimations/setActionAnimRelative"===n.type||"setComponentActionAnimRelative"===n.type)i=yy.single.instance(p.default).getActiveAction(),e.setActiveActionId(i.id);else if("moduleAnimations/setActionAnimOption"===n.type||"setComponentActionAnimOption"===n.type)e.updateActiveAction(n.payload);else if("moduleAnimations/removeFragment"===n.type||"removeComponentAnimationFragment"===n.type)yy.single.instance(h.default).execute(u.RemoveFragmentCommand,{fragment:n.payload});else if("moduleAnimations/addFragment"===n.type||"addComponentAnimationFragment"===n.type)yy.single.instance(h.default).execute(u.SetFragmentCommand,{fragmentId:n.payload});else if("moduleAnimations/removeAction"===n.type||"removeComponentAnimationAction"===n.type)yy.single.instance(h.default).execute(u.RemoveActionCommand,n.payload);else if("updateComponentTag"===n.type)e.updateComponentTag(n.payload);else if("moduleAnimations/stopFragment"===n.type||"moduleAnimations/stopAnimation"===n.type)yy.single.instance(c.default).stopAnimation();else if("setComponentsDragable"===n.type)yy.single.instance(h.default).execute(u.UpdateDragableCommand,{cmptIds:n.payload.ids,dragable:n.payload.dragable});else if("addCutShapePoint"===n.type)yy.single.instance(h.default).execute(m.CreatePointCommand,{cmptIds:n.payload.componentId,pointDatas:yy.cloneValues(n.payload.point)});else if("removeCutShapePoint"===n.type)yy.single.instance(h.default).execute(g.RemovePointCommand,{cmptIds:n.payload.componentId,pointId:n.payload.pointId});else if("updateCutShapePointPosition"===n.type)yy.single.instance(h.default).execute(_.UpdatePointPosCommand,{cmptIds:n.payload.componentId,pointId:n.payload.pointId,pointPos:n.payload.position,isVue:!0});else if("setIsEditingComponentAnimations"===n.type)yy.single.instance(h.default).execute(u.UpdateEditingComponentAnimations,{editType:n.payload});else if("updateExtraStageData"===n.type)(n.payload.hasOwnProperty("isAutoSubmit")||n.payload.hasOwnProperty("hasRecover"))&&(n.payload.isAutoSubmit,yy.single.instance(h.default).execute(y.ChangeAutoSubmitCommand,{submitType:n.payload}));else if("updateComponentEditable"===n.type)yy.single.instance(h.default).execute(f.default,{componentIds:n.payload.componentIds,newEditable:n.payload.newEditable.properties});else if("addChildComponent"===n.type)console.log("########----\x3eaddChildComponent---\x3e",n.payload),e.addChildComponent(n.payload);else if("startEditLabel"==n.type){var r=yy.single.instance(C.default),a=n.payload[0],l=r.getDisplayObjectById(a).getComponent(b.default);l&&l.setStartEdit()}else"timeTravel/resetCommandFactory"==n.type&&yy.destoryAll()})},t.prototype.getStageInfo=function(){return window._$store.state.stageData},t.prototype.getExtraStageData=function(){return window._$store.state.extraStageData},t.prototype.getComponentIds=function(){return window._$store.state.componentIds},t.prototype.getTemplate=function(){return window._$store.state.template},t.prototype.getComponentMap=function(){return window._$store.state.componentMap},t.prototype.getComponetTagName=function(e){var t=window._$store.state.componentMap[e].tag;return t?window._$store.state.template.tags.find(function(e){return e.name===t}).label:""},t.prototype.getSubComponetTagName=function(e){var t=this,n="",o=this.getComponetTagName(e);return window._$store.state.componentMap.forEach(function(o){var i=o.extra,r=void 0===i?{}:i;console.error("extra.point",r),r.point&&r.point.id===e&&(n=t.getComponetTagName(o.id))}),o+"-"+n},t.prototype.showContextMenu=function(e){window._$store.commit("showContextMenu",e)},t.prototype.doubleClick=function(e){window._$store.commit("doubleClick",e)},t.prototype.updateDisplayObjectProperties=function(e,t){var n=yy.single.instance(C.default),o=n.getDisplayObjectById(e),i=a.default.clone(t),r=n.getNodePos(e,t);t.x=r.x,t.y=r.y;var c=n.getDisplayGroupID(e);if("-1"!==c)if(t.x||t.y||t.width||t.height||t.angle){yy.single.instance(h.default).execute(u.UpdatePropertiesCommand,{id:e,newProperties:t}),n.updateDisplayObject(e,t);var p=n.getDisplayObjectById(c);p.resetLayoutSize();var l=p.groupIds;l.push(c),yy.single.instance(s.default).executeCommand(v.default,{selectIds:l,isUpdateGroupData:!0,notUpdate2Vue:!1,notRunCmd:!1})}else yy.single.instance(h.default).execute(u.UpdatePropertiesCommand,{id:e,newProperties:t}),yy.single.instance(s.default).executeCommand(v.default,{selectIds:[e],isUpdateGroupData:!1,notUpdate2Vue:!1,notRunCmd:!0});else{if(!o||!o.node)return void console.log("\u7ec4\u4ef6\u4e0d\u5b58\u5728\u4e86\uff0c\u65e0\u6cd5\u66f4\u65b0\u4e86",o);"object_layer"!==o.node.parent.name&&(t=i,console.log("############")),yy.single.instance(h.default).execute(u.UpdatePropertiesCommand,{id:e,newProperties:t}),yy.single.instance(s.default).executeCommand(v.default,{selectIds:[e],isUpdateGroupData:!1,notUpdate2Vue:!1,notRunCmd:!0})}},t.prototype.replaceComponet=function(e){window._$store.commit("cocos/replaceCurrentComponentIds",e)},t.prototype.updateComponetProperties=function(e,t){window._$store.commit("cocos/updateComponentProperties",{id:e,newProperties:t})},t.prototype.setActionAnimOption=function(e,t){window._$store.commit("moduleAnimations/cocos/setActionAnimOption",{id:e,option:t})},t.prototype.setCmptActionOption=function(e,t){window._$store.commit("cocos/setComponentActionAnimOption",{id:e,option:t})},t.prototype.setAnimModelActiveActionId=function(e){window._$store.commit("moduleAnimations/cocos/setActiveActionId",e)},t.prototype.setCmptModelActiveActionId=function(e){window._$store.commit("cocos/setComponentActiveActionId",e)},t.prototype.stopAnimatioinCommit=function(){window._$store.commit("moduleAnimations/stopAnimation")},t.prototype.stopFragmentCommit=function(){window._$store.commit("moduleAnimations/stopFragment")},t.prototype.cocosInitFinished=function(){window._$store.commit("cocosInitFinished")},t.prototype.cocosLoadQuestionBundleFinished=function(){window._$store.commit("cocosLoadQuestionBundleFinished")},t.prototype.cocosLoadGameSceneFinished=function(){window._$store.commit("cocosLoadGameSceneFinished")},t.prototype.cocosLoadStageRootNodeFinished=function(){window._$store.commit("cocosLoadStageRootNodeFinished")},t.prototype.cocosLoadCompListStart=function(){window._$store.commit("cocosLoadCompListStart")},t.prototype.cocosLoadCompListFinished=function(){window._$store.commit("cocosLoadCompListFinished")},t.prototype.getAnimModelFlatActions=function(){return window._$store.getters["moduleAnimations/flatActions"]},t.prototype.getCmptModelFlatActions=function(){return window._$store.getters.flatComponentActions},t.prototype.getAnimModelActiveAction=function(){return window._$store.getters["moduleAnimations/activeAction"]},t.prototype.getCmptModelActiveAction=function(){return window._$store.getters.activeComponentAction},t.prototype.setCutShapePointsData=function(e,t){window._$store.commit("cocos/setCutShapePointsData",{componentId:e,pointsData:yy.cloneValues(t)})},t.prototype.setCutShapeLinesData=function(e,t){window._$store.commit("cocos/setCutShapeLinesData",{componentId:e,linesData:yy.cloneValues(t)})},t.prototype.updateCutShapePointPosition=function(e,t,n){window._$store.commit("cocos/updateCutShapePointPosition",{componentId:e,pointId:t,position:yy.cloneValues(n)})},t.prototype.updateComponentOrder=function(e){yy.single.instance(h.default).execute(I.UpdateComponentOrderCommand,e)},t.prototype.updateComponentLevel=function(e,t){yy.single.instance(h.default).execute(u.UpdateCmptLevelCommand,{id:e,type:t})},t.prototype.addComponet=function(e){yy.single.instance(h.default).execute(u.AddCmptCommand,{payload:e})},t.prototype.removeComponet=function(e){yy.single.instance(h.default).execute(u.RemoveCmptCommand,e)},t.prototype.addChildComponent=function(e){yy.single.instance(h.default).execute(d.AddChildComponent,{payload:e})},t.prototype.assembleGroup=function(e,t){yy.single.instance(h.default).execute(u.AssembleGroupCommand,{id:e,ids:t})},t.prototype.unassembleGroup=function(e){yy.single.instance(h.default).execute(u.UnassembleGroupCommand,e)},t.prototype.setActiveActionId=function(e){e&&""!==e&&yy.single.instance(h.default).execute(u.SetActiveActionCommand,{actionId:e})},t.prototype.updateActiveAction=function(e){var t=yy.single.instance(p.default).getActiveAction();if(t){var n=t.id,o=yy.single.instance(l.default).getAnimDisplayObjects(n);if(n&&""!==n&&o&&e.option&&e.option.points){var i=e.option.currIndex;yy.single.instance(h.default).execute(u.UpdateAnimPropertiesCommand,{id:o[i],newProperties:e.option.points[i],actionId:n})}}},t.prototype.updateComponentTag=function(e){yy.single.instance(h.default).execute(u.UpdateComponentTagCommand,{id:e.id,label:e.tag.label})},t.prototype.destory=function(){yy.log("====destory====")},t.prototype.updateCocosAniClips=function(e){window._$store.commit("updateCocosAniClips",{id:e.id,clips:e.clips})},t.prototype.textToImg=function(e){console.warn("textToImg",e.texture),window._$store.commit("textToImg",{selectId:e.selectId,texture:e.texture})},t.prototype.getComponentEditable=function(e){var t=window._$store.state.componentMap[Number(e)];return!t||("boolean"==typeof t.editable?t.editable:t.editable.properties)},t}(r.SingleBase);n.default=P,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/extension/command/CommandManager":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../../stage/StageController":"StageController","../animation/display/AnimDisplayControl":"AnimDisplayControl","../animation/display/AnimDisplayManager":"AnimDisplayManager","../command/commond":"commond","../command/operate/CommandFactory":"CommandFactory","../command/operate/base/AddChildComponent":"AddChildComponent","../command/operate/base/ChangeAutoSubmitCommand":"ChangeAutoSubmitCommand","../command/operate/base/UpdateComponentEditable":"UpdateComponentEditable","../command/operate/base/UpdateComponentOrderCommand":"UpdateComponentOrderCommand","../command/operate/cutShape/CreatePointCommand":"CreatePointCommand","../command/operate/cutShape/RemovePointCommand":"RemovePointCommand","../command/operate/cutShape/UpdatePointPosCommand":"UpdatePointPosCommand","../command/simple/UpdateProp2VueCmd":"UpdateProp2VueCmd","../display/DisplayObjectManager":"DisplayObjectManager","../display/base/DisplayEditBox":"DisplayEditBox","../hintline/HintLineDisplay":"HintLineDisplay","../hintline/HintLineManager":"HintLineManager","./MockData":"MockData"}],DataProxyImp:[function(e,t,n){"use strict";cc._RF.push(t,"f189aLCciFDg55YIhicpl6d","DataProxyImp"),Object.defineProperty(n,"__esModule",{value:!0}),cc._RF.pop()},{}],DisplayCocosAni:[function(e,t,n){"use strict";cc._RF.push(t,"69b31VVvDtH2JujY1A64XsV","DisplayCocosAni");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../qte/core/utils/ValueUtils"),s=e("./DisplayObject"),c=cc._decorator.ccclass,p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._anchor=null,t._size=new cc.Size(200,200),t._animationCmpt=null,t._actionList=[],t._offet=cc.v2(0,0),t._playIndex=0,t._loop=!0,t._timeScale=1,t.cocosAniData=null,t}return i(t,e),Object.defineProperty(t.prototype,"anchor",{get:function(){return this._anchor},set:function(e){this._anchor=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"size",{get:function(){return this._size},set:function(e){this._size=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"animationCmpt",{get:function(){return this._animationCmpt},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"actionList",{get:function(){return this._actionList},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"offset",{get:function(){return this._offet},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loop",{get:function(){return this._loop},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timeScale",{get:function(){return this._timeScale},enumerable:!1,configurable:!0}),t.prototype.addListener=function(e){this._animationCmpt=e},t.prototype.resetCocosAniPos=function(){var e=(this._anchor.x-.5)*this._size.width*this._animationCmpt.node.scaleX,t=(this._anchor.y-.5)*this._size.height*this._animationCmpt.node.scaleY;this._animationCmpt.node.setPosition(cc.v3(e,t)),this._offet=cc.v2(e,t)},t.prototype.refreshRectPoint=function(){e.prototype.refreshRectPoint.call(this);var t=this.node.getContentSize(),n=t.width/this._size.width,o=t.height/this._size.height,i=this._animationCmpt.node;i.scaleX=n,i.scaleY=o,this.resetCocosAniPos()},t.prototype.setAnimation=function(e){this._actionList=e,this.step()},t.prototype.setLoop=function(e){this._loop!==e&&(this._loop=e,this._loop&&this.step())},t.prototype.setTimeScale=function(e){this._timeScale=Math.max(e,0)},t.prototype.step=function(){this.actionList.length<=0||this._playIndex>=this.actionList.length||""!==this.actionList[this._playIndex]||this.completeListener()},t.prototype.endListener=function(){cc.log("")},t.prototype.completeListener=function(){this._playIndex++,this.checkActionList()?this._playIndex>this.actionList.length-1&&(this._playIndex=0,!this.loop)||this.step():this._playIndex=0},t.prototype.checkActionList=function(){for(var e=0,t=this.actionList;e<t.length;e++)if(""!==t[e])return!0;return!1},t.prototype.getSpineAnimationTime=function(e){var t=0,n=this._animationCmpt.attachUtil;if(!n)return t;for(var o=n._skeleton.data.animations,i=0;i<o.length;i++){var r=o[i];if(r.name===e){t=yy.checkValue(r.duration,0);break}}return a.default.setOneDecimal(t,2)},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this);return t.extra.cocosAniData=this.cocosAniData,t},r([c],t)}(s.default);n.default=p,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"./DisplayObject":"DisplayObject"}],DisplayCutShape:[function(e,t,n){"use strict";cc._RF.push(t,"1fe02IQ/YpHs7UWzNV2MVng","DisplayCutShape");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a,s=e("../../../../../qte/core/utils/ValueUtils"),c=e("../../../../common/ComUtils"),p=e("../../../../common/EditorEnum"),l=e("../../../proxy/DataCenterBridge"),u=e("./DisplayEditObject"),d=cc._decorator.ccclass;(function(e){e[e.TRIAGLE_1=0]="TRIAGLE_1",e[e.TRIAGLE_2=1]="TRIAGLE_2",e[e.TRIAGLE_3=2]="TRIAGLE_3",e[e.TRIAGLE_3_1=3]="TRIAGLE_3_1",e[e.TRAPEZOID_1=4]="TRAPEZOID_1",e[e.TRAPEZOID_2=5]="TRAPEZOID_2",e[e.TRAPEZOID_2_1=6]="TRAPEZOID_2_1",e[e.TRAPEZOID_3=7]="TRAPEZOID_3",e[e.RHOMBOID=8]="RHOMBOID",e[e.RHOMBOID_1=9]="RHOMBOID_1",e[e.SQUARE=10]="SQUARE",e[e.PENTAGON=11]="PENTAGON",e[e.LIUBIANXING=12]="LIUBIANXING"})(a||(a={}));var y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._shapeType=null,t._shapeDatas=null,t._initShape=!0,t._isScaleChange=!1,t._pointDatas=null,t._lineDatas=null,t}return i(t,e),Object.defineProperty(t.prototype,"subType",{get:function(){return this._shapeType},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"shapeDatas",{get:function(){return this._shapeDatas},enumerable:!1,configurable:!0}),t.prototype.initFinish=function(){e.prototype.initFinish.call(this),this.isHidePoint=this._shapeDatas.isHidePointLine,this._createShape();for(var t=0,n=this._pointDatas;t<n.length;t++){var o=n[t];o.editable&&this.createPoint(o)}},t.prototype.initData=function(e,t){this._shapeDatas=e,this._shapeType=t,this._pointDatas=e.pointsData,this._pointDatas.length>0&&(this._initShape=!1)},t.prototype.refreshRectPoint=function(){this._initShape=!1,this._isScaleChange=!0,e.prototype.refreshRectPoint.call(this),this._createShape()},t.prototype.updateLinePointActive=function(e){var t=this.isHidePoint;this.isHidePoint=e,this.updatePointActive(e);for(var n=0;n<this._pointDatas.length;n++){var o=this._pointDatas[n];if(o.editable)return;this._createPointHint(o.x,o.y,n,e);var i=0;this._pointDatas[n+1]&&!this._pointDatas[n+1].editable&&(i=n+1);var r=(this._pointDatas[n].x+this._pointDatas[i].x)/2,a=(this._pointDatas[n].y+this._pointDatas[i].y)/2;this._createSidetHint(r,a,n,e)}return t},t.prototype._createShape=function(){switch(this._shapeType){case a.TRIAGLE_1:this._createTriagle1();break;case a.TRIAGLE_2:this._createTriagle2();break;case a.TRIAGLE_3:this._createTriagle3();break;case a.TRIAGLE_3_1:this._createTriagle31();break;case a.TRAPEZOID_1:this._createTrapezoid1();break;case a.TRAPEZOID_2:this._createTrapezoid2();break;case a.TRAPEZOID_2_1:this._createTrapezoid21();break;case a.TRAPEZOID_3:this._createTrapezoid3();break;case a.RHOMBOID:this._createSibianxing();break;case a.RHOMBOID_1:this._createSibianxing1();break;case a.SQUARE:this._createSquare();break;case a.PENTAGON:this._createPentagon();break;case a.LIUBIANXING:this._createLiubianxing()}},t.prototype._createTriagle1=function(){this._createGraphics([{left:.2,top:.2},{left:.5,top:.8},{left:.8,top:.2}])},t.prototype._createTriagle2=function(){this._createGraphics([{left:.1,top:.2},{left:.5,top:.8},{left:.9,top:.2}])},t.prototype._createTriagle3=function(){this._createGraphics([{left:.1,top:.2},{left:.1,top:.8},{left:.9,top:.2}])},t.prototype._createTriagle31=function(){this._createGraphics([{left:.1,top:.2},{left:.9,top:.8},{left:.9,top:.2}])},t.prototype._createTrapezoid1=function(){this._createGraphics([{left:.1,top:.1},{left:.2,top:.9},{left:.6,top:.9},{left:.9,top:.1}])},t.prototype._createTrapezoid2=function(){this._createGraphics([{left:.1,top:.1},{left:.1,top:.9},{left:.5,top:.9},{left:.9,top:.1}])},t.prototype._createTrapezoid21=function(){this._createGraphics([{left:.1,top:.1},{left:.5,top:.9},{left:.9,top:.9},{left:.9,top:.1}])},t.prototype._createTrapezoid3=function(){this._createGraphics([{left:.1,top:.1},{left:.3,top:.9},{left:.7,top:.9},{left:.9,top:.1}])},t.prototype._createSibianxing=function(){this._createGraphics([{left:.1,top:.1},{left:.3,top:.9},{left:.9,top:.9},{left:.7,top:.1}])},t.prototype._createSibianxing1=function(){this._createGraphics([{left:.3,top:.1},{left:.1,top:.9},{left:.7,top:.9},{left:.9,top:.1}])},t.prototype._createSquare=function(){this._createGraphics([{left:.1,top:.1},{left:.1,top:.9},{left:.9,top:.9},{left:.9,top:.1}])},t.prototype._createPentagon=function(){this._createGraphics([{top:.09,left:.21},{top:.65,left:.03},{top:1,left:.5},{top:.65,left:.97},{top:.09,left:.79}])},t.prototype._createLiubianxing=function(){this._createGraphics([{left:.25,top:.07},{left:0,top:.5},{left:.25,top:.93},{left:.75,top:.93},{left:1,top:.5},{left:.75,top:.07}])},t.prototype._createGraphics=function(e){var t=[];this._lineDatas=[];for(var n=0;n<e.length;n++){var o=e[n],i=s.default.setOneDecimal(-this.node.width/2+this.node.width*o.left),r=s.default.setOneDecimal(-this.node.height/2+this.node.height*o.top);t.push(cc.v2(i,r))}this.drawShape(t),this.setPointData(t),this.setLindeData(t),this.updateLinePointActive(this.isHidePoint)},t.prototype.setPointData=function(e){for(var t=function(t){var o={id:"P"+t,label:"\u70b9"+(t+1),x:e[t].x,y:e[t].y,editable:!1},i=n._pointDatas.findIndex(function(e){return e.id===o.id});-1===i?n._pointDatas.push(o):n._pointDatas[i]=o},n=this,o=0;o<e.length;o++)t(o);this._initShape&&yy.single.instance(l.default).setCutShapePointsData(this.cid,this._pointDatas)},t.prototype.setLindeData=function(e){this._lineDatas=[];for(var t=0;t<e.length;t++){var n=0;e[t+1]&&(n=t+1);var o={id:"L"+t,label:"\u8fb9"+(t+1),points:[this._pointDatas[t].id,this._pointDatas[n].id]};this._lineDatas.push(o)}this._initShape&&yy.single.instance(l.default).setCutShapeLinesData(this.cid,this._lineDatas)},t.prototype.drawShape=function(e){var t=this.node.getChildByName("shape_node");t||((t=new cc.Node).addComponent(cc.Graphics),this.node.addChild(t,0,"shape_node"));var n=t.getComponent(cc.Graphics),o=(new cc.Color).fromHEX(this._shapeDatas.fillColor);o.a=this.node.opacity;var i=(new cc.Color).fromHEX(this._shapeDatas.strokeColor);i.a=this.node.opacity,n.strokeColor=i,n.fillColor=o,n.lineWidth=this._shapeDatas.lineWidth,n.clear();for(var r=0;r<e.length;r++)0===r?n.moveTo(e[r].x,e[r].y):n.lineTo(e[r].x,e[r].y);n.close(),n.fill(),n.stroke()},t.prototype.updateShapeDatasToVue=function(){if(this._isScaleChange){if(this._pointDatas)for(var e=0,t=this._pointDatas;e<t.length;e++){var n=t[e];yy.single.instance(l.default).updateCutShapePointPosition(this.cid,n.id,{x:n.x,y:n.y})}this._lineDatas&&yy.single.instance(l.default).setCutShapeLinesData(this.cid,this._lineDatas),this._isScaleChange=!1}},t.prototype._createPointHint=function(e,t,n,o){var i=this.node.getChildByName("circle"+n);i||((i=new cc.Node("circle"+n)).group=p.EditorGroup.EDGE,i.parent=this.node,i.addComponent(cc.Graphics));var r=i.getComponent(cc.Graphics);r.clear(),o||(r.strokeColor=cc.Color.RED,r.lineWidth=5,r.arc(e,t,20,0,180),r.stroke());var a=this.node.getChildByName("pointLabel_"+n);if(!a){(a=new cc.Node("pointLabel_"+n)).color=cc.Color.BLACK,a.group=p.EditorGroup.EDGE,a.parent=this.node;var s=a.addComponent(cc.LabelOutline);s.color=cc.Color.WHITE,s.width=2;var l=a.addComponent(cc.Label);l.fontSize=25,l.string="\u70b9"+(n+1),c.ComUtil.setLabelBlend(l)}a.active=!o,e>=0?e+=20:e-=20,t>=0?t+=20:t-=20,a.setPosition(cc.v2(e,t))},t.prototype._createSidetHint=function(e,t,n,o){var i=this.node.getChildByName("sideLabel_"+n);if(!i){(i=new cc.Node("sideLabel_"+n)).group=p.EditorGroup.EDGE,i.color=cc.Color.WHITE,i.parent=this.node;var r=i.addComponent(cc.LabelOutline);r.color=cc.Color.BLACK,r.width=2;var a=i.addComponent(cc.Label);a.fontSize=30,a.string="\u8fb9"+(n+1),c.ComUtil.setLabelBlend(a)}i.active=!o,i.setPosition(cc.v2(e,t))},t.prototype.setLineWidht=function(e){var t=this._shapeDatas.lineWidth;return this._shapeDatas.lineWidth=e,this._createShape(),t},t.prototype.setStrockColor=function(e){var t=this._shapeDatas.strokeColor;return this._shapeDatas.strokeColor=e,this._createShape(),t},t.prototype.setFillColor=function(e){var t=this._shapeDatas.fillColor;return this._shapeDatas.fillColor=e,this._createShape(),t},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this),n=this._pointDatas;return t.extra.isHidePointLine=this._shapeDatas.isHidePointLine,t.extra.strokeColor=this._shapeDatas.strokeColor,t.extra.fillColor=this._shapeDatas.fillColor,t.extra.lineWidth=this._shapeDatas.lineWidth,t.extra.pointsData=n,t.extra.linesData=this._lineDatas,t.extra.subType=this.subType,t},t.prototype.createEditPoint=function(t){var n={id:t.id,label:t.label,x:t.x,y:t.y,editable:t.editable};this._pointDatas.push(n),e.prototype.createPoint.call(this,t)},t.prototype.removeEditPoint=function(t,n){var o=e.prototype.removePoint.call(this,t,n),i=this._pointDatas.findIndex(function(e){return e.id===t});return-1!==i&&this._pointDatas.splice(i,1),o},t.prototype.updateEditPointPos=function(e,t){var n=this._pointDatas.findIndex(function(t){return t.id===e});-1!==n&&(this._pointDatas[n].x=t.x,this._pointDatas[n].y=t.y)},r([d],t)}(u.default);n.default=y,cc._RF.pop()},{"../../../../../qte/core/utils/ValueUtils":void 0,"../../../../common/ComUtils":"ComUtils","../../../../common/EditorEnum":"EditorEnum","../../../proxy/DataCenterBridge":"DataCenterBridge","./DisplayEditObject":"DisplayEditObject"}],DisplayEditBox:[function(e,t,n){"use strict";cc._RF.push(t,"3ae46wUlg5Oz5PeZCBiv0W9","DisplayEditBox");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../qte/core/utils/ValueUtils"),s=e("../../../../qte/core/extension/command/CommandManager"),c=e("../../command/operate/editbox/TextToImgCommand"),p=e("../../command/simple/UpdateProp2VueCmd"),l=e("../../proxy/DataCenterBridge"),u=e("./DisplayObject"),d=e("clipboard-polyfill"),y=cc._decorator.ccclass,f=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.editBoxData=null,t.editProperties={enableBold:!1,enableItalic:!1,enableUnderline:!1,fontSize:30,lineHeight:40,color:"#000000",string:"\u8bf7\u8f93\u5165\u6587\u672c",str:"",cusorIndex:0,isLabelRight:!0,selectArr:[],horizontalAlign:0,isFixed:!1},t._editBoxCom=null,t._editBoxNode=null,t._clickInStatue=!0,t._isSelected=!1,t}return i(t,e),t.prototype.getEditBoxCom=function(){return this._editBoxCom},t.prototype.initFinish=function(){this.initNode()},t.prototype.initNode=function(){var e=this;yy.log("=======initNode====="),this._editBoxNode||(this._editBoxNode=new cc.Node,this._editBoxNode.setPosition(cc.v2(-100,0)),this._editBoxNode.parent=this.node,this._editBoxNode.setContentSize(this.node.getContentSize()),this._editBoxCom=this._editBoxNode.addComponent(editbox.EditBoxTool),this._editBoxCom.textToImg=this.textToImg.bind(this),this._editBoxCom.writeToText=this.writeToClipBoard.bind(this),this._editBoxCom.widthHeightFixed=this.editBoxData.isFixed,this._editBoxCom.editable=this.editable||null,this._editBoxCom.fontSize=this.editBoxData.fontSize,this._editBoxCom.scheduleOnce(function(){e.editBoxData&&""!==e.editBoxData.str?(e._editBoxCom.pikerviewStatue=1,yy.log("initNode_editBoxData======>",e.editBoxData),e._editBoxCom.setEditBoxHorizontalStatue(e.editBoxData.horizontalAlign),e._editBoxCom.setNodeSize(e.node.getContentSize().width,e.node.getContentSize().height),e.editBoxData.lineHeight&&(e._editBoxCom.lineHeight=e.editBoxData.lineHeight),e.editBoxData.rowSpacing&&(e._editBoxCom.rowSpace=e.editBoxData.rowSpacing),e._editBoxCom.createEditBoxWithString(e.editBoxData.str)):(e._editBoxCom.pikerviewStatue=2,e._editBoxCom.createEditBox()),e.initFinished()},0),this._editBoxCom.updateEditProperties=this.updateEditProperties.bind(this),this._editBoxCom.updateNodeSize=this.refreshNodeSize.bind(this),this._editBoxCom.updateEditPropertiesWithStr=this.updateEditPropertiesWithStr.bind(this))},t.prototype.mouseBegin=function(e){var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2);this._clickInStatue=this._editBoxCom.rect().contains(n),0===this._editBoxCom.pikerviewStatue?this._editBoxCom.pikerviewStatue=1:this._editBoxCom.mouseBeginLayout(e,n)},t.prototype.mouseMove=function(e){var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2);this._editBoxCom.mouseMoveLayout(e,n)},t.prototype.mouseUp=function(){this._editBoxCom.mouseUpLayout()},t.prototype.initData=function(e){var t=a.default.clone(e);for(var n in yy.log("=======initData=====",JSON.stringify(e),JSON.stringify(t)),this.node.color=(new cc.Color).fromHEX(yy.checkValue(t.color,"#ffffff")),this.node.x=t.x,this.node.y=t.y,this.node.width=t.width,this.node.height=t.height,this.node.scaleX=yy.checkValue(t.scaleX,1),this.node.scaleY=yy.checkValue(t.scaleY,1),this.editBoxData=t,t)void 0!==this.editProperties[n]&&(this.editProperties[n]=t[n])},t.prototype.setBoldEnabled=function(e){return this._editBoxCom.setBoldEnabled(e)},t.prototype.setItalicEnabled=function(e){return this._editBoxCom.setItalicEnabled(e)},t.prototype.setUnderLine=function(e){return this._editBoxCom.setUnderLine(e)},t.prototype.setIsFixed=function(e){return this._editBoxCom.setIsFixed(e)},t.prototype.setRowSpace=function(e){return this._editBoxCom.setRowSpace(e)},t.prototype.setLineHeight=function(e){return this._editBoxCom.setLineHeight(e)},t.prototype.setFontSize=function(e){return this._editBoxCom.setFontSize(e)},t.prototype.setFontColor=function(e){return this._editBoxCom.setFontColor(e)},t.prototype.setNodeWidth=function(e,t){this._editBoxCom.setNodeWidth(e,!t)},t.prototype.setNodeHeight=function(e){this._editBoxCom.setNodeHeight(e)},t.prototype.setNodeAngle=function(){this._editBoxCom.setNodeAngle()},t.prototype.setHorizontalAlign=function(e){return this._editBoxCom.setHorizontalAlign(e)},t.prototype.clickDirectionKey=function(e,t){this._editBoxCom.clickDirectionKey(e,t)},t.prototype.clickCtrlA=function(){this._editBoxCom.clickCtrlA()},t.prototype.clickCtrlC=function(){this._editBoxCom.clickCtrlC()},t.prototype.clickCtrlX=function(){this._editBoxCom.clickCtrlX()},t.prototype.clickDeleteKey=function(){this._editBoxCom.clickDeleteKey()},t.prototype.setCursorIsLabelRight=function(e){return this._editBoxCom.setCursorIsLabelRight(e)},t.prototype.setCursorLabelIndex=function(e){return this._editBoxCom.setCursorLabelIndex(e)},t.prototype.setRichTextToEditBox=function(e){return this._editBoxCom.convertToEditBox(e)},t.prototype.refreshNodeSize=function(){this.node.setContentSize(this._editBoxNode.getContentSize()),e.prototype.refreshRectPoint.call(this)},t.prototype.getNodeEditStatue=function(){return this._editBoxCom.isEditing},t.prototype.setpikerviewStatue=function(e){this._editBoxCom.pikerviewStatue=e},t.prototype.setSelected=function(t){e.prototype.setSelected.call(this,t),t?this._editBoxCom.setSelected():this._editBoxCom.cancelSelected()},t.prototype.cancelSelected=function(){this._editBoxCom.cancelSelected()},t.prototype.mouseBeginLayout=function(e){this.mouseBegin(e)},t.prototype.mouseMoveLayout=function(e){this.mouseMove(e)},t.prototype.mouseUpLayout=function(){this.mouseUp()},t.prototype.mouseWheel=function(e){this._editBoxCom.mouseWheelLayout(e)},t.prototype.setStartEdit=function(){this._editBoxCom.createWithEdit=!0},t.prototype.updateEditPropertiesWithStr=function(e,t){void 0===t&&(t=!0),this.updateEditProperties(e),yy.single.instance(s.default).executeCommand(p.default,{selectIds:[this.cid],isUpdateGroupData:!1,notUpdate2Vue:t,notRunCmd:!0})},t.prototype.updateEditProperties=function(e){for(var t in console.warn("displayEditor updateEditProperties",e),e)void 0!==this.editProperties[t]&&(this.editProperties[t]=e[t])},t.prototype.textToImg=function(e){yy.single.instance(s.default).executeCommand(c.default,{selectId:this.cid,texture:e})},t.prototype.getOldProperties=function(){var e=yy.single.instance(l.default).getComponentMap()[this.cid].properties;return{id:this.cid,newProperties:{x:e.x,y:e.y,width:e.width,height:e.height,angle:e.angle||0,enableBold:Boolean(e.enableBold),enableItalic:Boolean(e.enableItalic),enableUnderline:Boolean(e.enableUnderline),fontSize:e.fontSize,lineHeight:e.lineHeight,color:e.color||"#FFFFFF",str:e.str,cusorIndex:e.cusorIndex,isLabelRight:e.isLabelRight,selectArr:e.selectArr,horizontalAlign:e.horizontalAlign,isFixed:this.editProperties.isFixed}}},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this),n={x:this.node.x,y:this.node.y,width:this.node.width,height:this.node.height,angle:t.newProperties.angle||0,enableBold:Boolean(this.editProperties.enableBold),enableItalic:Boolean(this.editProperties.enableItalic),enableUnderline:Boolean(this.editProperties.enableUnderline),fontSize:this.editProperties.fontSize,lineHeight:this.editProperties.lineHeight,color:this.editProperties.color||"#FFFFFF",str:this.editProperties.str,cusorIndex:this.editProperties.cusorIndex,isLabelRight:this.editProperties.isLabelRight,selectArr:this.editProperties.selectArr,horizontalAlign:this.editProperties.horizontalAlign,isFixed:this.editProperties.isFixed},o={};for(var i in n)o[i]=a.default.setOneDecimal(n[i]);return t.newProperties=n,t},t.prototype.writeToClipBoard=function(e){d.writeText(e)},r([y],t)}(u.default);n.default=f,cc._RF.pop()},{"../../../../qte/core/extension/command/CommandManager":void 0,"../../../../qte/core/utils/ValueUtils":void 0,"../../command/operate/editbox/TextToImgCommand":"TextToImgCommand","../../command/simple/UpdateProp2VueCmd":"UpdateProp2VueCmd","../../proxy/DataCenterBridge":"DataCenterBridge","./DisplayObject":"DisplayObject","clipboard-polyfill":1}],DisplayEditObject:[function(e,t,n){"use strict";cc._RF.push(t,"2838fZXbWBCjLd2pa32per7","DisplayEditObject");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a},a=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=e("../../../../../qte/core/utils/ValueUtils"),p=e("../../../../common/ComUtils"),l=e("../../../command/operate/CommandFactory"),u=e("../../../command/operate/cutShape/UpdatePointPosCommand"),d=e("../../../proxy/DataCenterBridge"),y=e("../../DisplayObjectManager"),f=e("../DisplayObject"),h=e("./CutPoint"),m=cc._decorator.ccclass,g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._bIsEdting=!1,t._bIsHidePoint=!1,t.pitchPoint=null,t._pitchOldPos=null,t._editButton=null,t._editLabel=null,t._editPointArr=[],t}return i(t,e),Object.defineProperty(t.prototype,"isEditing",{get:function(){return this._bIsEdting},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"isHidePoint",{get:function(){return this._bIsHidePoint},set:function(e){this._bIsHidePoint=e},enumerable:!1,configurable:!0}),t.prototype.createPoint=function(e){var t=this.node.getChildByName("editablePoint"+e.id);t||((t=new cc.Node("editablePoint"+e.id)).addComponent(cc.Graphics),this.node.addChild(t));var n=t.addComponent(h.default);n.createPoint(e),n.updatePointActive(this._bIsHidePoint),this._editPointArr.push(n),this.refreshEditBtn()},t.prototype.refreshEditBtn=function(){this._editButton.active=!1,this.getPointList().length>0&&this.selected&&!this._editButton.active&&(this._editButton.active=!0)},t.prototype.updatePointActive=function(e){for(var t=0,n=this._editPointArr;t<n.length;t++)n[t].updatePointActive(e)},t.prototype.removePoint=function(e,t){if(t){var n=this._editPointArr.findIndex(function(t){return t.pointData.id===e}),o=null;if(-1!==n){var i=this._editPointArr.splice(n,1)[0];o=i.pointData,i.node.destroy()}return o}},t.prototype.pointList=function(e){this._editPointArr=e},t.prototype.getPointList=function(){return this._editPointArr},t.prototype.clearPointList=function(){this._editPointArr=[]},t.prototype.updatePoint=function(e,t){e&&t>0&&t<this._editPointArr.length&&(this._editPointArr[t]=e)},t.prototype.init=function(){e.prototype.init.call(this),this.initEditButton()},t.prototype.beginEdit=function(){this._bIsEdting=!0,this._editLabel.string=" \u7f16\u8f91\u4e2d "},t.prototype.stopEdit=function(){this._bIsEdting=!1,this._editLabel.string=" \u53bb\u7f16\u8f91 "},t.prototype.touchBegin=function(e){var t=this.node.convertToNodeSpaceAR(e);if(new cc.Rect(this._editButton.x,this._editButton.y-this._editButton.height/2,this._editButton.width,this._editButton.height).contains(t))return!("object"!=typeof this.editable||!this.editable.properties||this.editable.properties.cutShapePoints)||(this._bIsEdting?this.stopEdit():this.beginEdit(),!0);if(!this._bIsEdting)return!1;for(var n=0,o=this._editPointArr;n<o.length;n++){var i=o[n];if(!i.node.active)return!1;if(cc.Vec2.distance(t,cc.v2(i.pointData.x,i.pointData.y))<=i.circleRadius)return this.pitchPoint=i.node,this._pitchOldPos=this.pitchPoint.getPosition(),!0}return!1},t.prototype.touchMove=function(e){var t=this.node.convertToNodeSpaceAR(e);return!!this.pitchPoint&&(this.pitchPoint.getComponent(h.default).updatePos(t),!0)},t.prototype.touchUp=function(){if(this.pitchPoint){if(this.pitchPoint.getPosition()!==this._pitchOldPos){var e=this.pitchPoint.getComponent(h.default).pointData.id,t={x:c.default.setOneDecimal(this.pitchPoint.getComponent(h.default).pointData.x,2),y:c.default.setOneDecimal(this.pitchPoint.getComponent(h.default).pointData.y,2)};yy.single.instance(d.default).updateCutShapePointPosition(this.cid,e,t),this._updateEditPointPos(e,t),yy.single.instance(l.default).pushCommand(u.UpdatePointPosCommand,{cmptIds:this.cid,pointId:e,pointPos:t},{cmptIds:this.cid,pointId:e,pointPos:c.default.clone(this._pitchOldPos)})}this._pitchOldPos=null,this.pitchPoint=null}return!1},t.prototype.updatePointPos=function(e,t){var n=this._editPointArr.findIndex(function(t){return t.pointData.id===e});if(-1!==n){var o=cc.v2(0,0),i=this._editPointArr[n].node.x,r=this._editPointArr[n].node.y;return o.x=i,o.y=r,void 0!==t.x&&(i=t.x),void 0!==t.y&&(r=t.y),i=c.default.setOneDecimal(i,2),r=c.default.setOneDecimal(r,2),this._editPointArr[n].updatePos(cc.v2(i,r)),yy.single.instance(d.default).updateCutShapePointPosition(this.cid,e,cc.v2(i,r)),this._updateEditPointPos(e,cc.v2(i,r)),o}},t.prototype.refreshRectPoint=function(){e.prototype.refreshRectPoint.call(this),this.selected&&(this._editButton.x=this.rect.width/2,this._editButton.y=this.rect.height/2-this._editButton.height)},t.prototype.setSelected=function(t){e.prototype.setSelected.call(this,t),this._bIsEdting&&!t&&this.stopEdit(),this.refreshEditBtn()},t.prototype.initEditButton=function(){return a(this,void 0,void 0,function(){var e,t,n,o,i,r;return s(this,function(a){switch(a.label){case 0:return(e=new cc.Node).height=30,e.anchorX=0,e.anchorY=.5,e.name="EditBtn_Node",this._editButton=e,(t=e.addComponent(cc.Layout)).type=cc.Layout.Type.HORIZONTAL,t.resizeMode=cc.Layout.ResizeMode.CONTAINER,(n=e.addComponent(cc.Widget)).isAlignRight=!0,n.isAlignTop=!0,n.right=0,n.top=0,n.alignMode=cc.Widget.AlignMode.ALWAYS,(o=e.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,[4,new Promise(function(e,t){cc.loader.loadRes("img/bg",cc.Texture2D,function(n,i){if(n)return yy.warn(n),void t(n);p.ComUtil.addPremultiplyAlpha(i,o),e()})})];case 1:return a.sent(),i=new cc.Node,r=i.addComponent(cc.Label),p.ComUtil.setLabelBlend(r),i.anchorX=0,i.anchorY=.5,i.color=(new cc.Color).fromHEX("#63390E"),r.useSystemFont=!0,r.fontFamily="Arial",r.fontSize=20,r.horizontalAlign=cc.Label.HorizontalAlign.LEFT,this.node.addChild(e),e.addChild(i),i.position=cc.v3(0,-14),e.group="edge",r.string=" \u53bb\u7f16\u8f91 ",this._editLabel=r,this._editButton.active=!1,[2]}})})},t.prototype._updateEditPointPos=function(e,t){yy.single.instance(y.default).getDisplayObjectById(this.cid).updateEditPointPos(e,t)},r([m],t)}(f.default);n.default=g,cc._RF.pop()},{"../../../../../qte/core/utils/ValueUtils":void 0,"../../../../common/ComUtils":"ComUtils","../../../command/operate/CommandFactory":"CommandFactory","../../../command/operate/cutShape/UpdatePointPosCommand":"UpdatePointPosCommand","../../../proxy/DataCenterBridge":"DataCenterBridge","../../DisplayObjectManager":"DisplayObjectManager","../DisplayObject":"DisplayObject","./CutPoint":"CutPoint"}],DisplayGameBaseComponent:[function(e,t,n){"use strict";cc._RF.push(t,"03edcvAbNdDgr6Yidx74NKs","DisplayGameBaseComponent");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=cc._decorator,s=a.ccclass,c=(a.property,function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.onSizeChange=function(){},r([s],t)}(cc.Component));n.default=c,cc._RF.pop()},{}],DisplayObjectFactory:[function(e,t,n){"use strict";cc._RF.push(t,"9cc7btZBh5BwqG2eK4No2Di","DisplayObjectFactory");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../../qte/core/component/spine/SpineRect"),c=e("../../../qte/core/base/SingleBase"),p=e("../../../qte/core/utils/ValueUtils"),l=e("../../common/EditorEnum"),u=e("../hintline/HintLineDisplay"),d=e("../hintline/HintLineManager"),y=e("../proxy/ComptData"),f=e("../proxy/DataCenterBridge"),h=e("./base/DisplayEditBox"),m=e("./base/DisplayObject"),g=e("./base/DisplayObjectGroup"),_=e("./base/DisplaySpine"),v=e("./base/DisplaySvg"),b=e("./SpineManager"),C=e("./base/edit/DisplayCutShape"),O=e("../../common/ComUtils"),T=e("../../AssetsManager"),w=e("./base/DisplaySpecialComponent"),I=e("./OptSubObjectFactory"),P=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initInstance=function(){},t.prototype.getDisplayNodeAsync=function(e,t){var n=e.type,o=new cc.Node;switch(console.log("%c Line:37 \ud83c\udf69 objectData","color:#33a5ff",e),n){case l.CmptType.SPRITE:r(this,void 0,void 0,function(){return a(this,function(n){switch(n.label){case 0:return[4,this.createSprite(e,t,o)];case 1:return n.sent(),[2]}})});break;case l.CmptType.SHAPE:r(this,void 0,void 0,function(){return a(this,function(n){switch(n.label){case 0:return[4,this.createShape2(e,t,o)];case 1:return n.sent(),[2]}})});break;case l.CmptType.FORMULA:r(this,void 0,void 0,function(){return a(this,function(n){switch(n.label){case 0:return[4,this.createFormula(e,t,o)];case 1:return n.sent(),[2]}})});break;case l.CmptType.LABEL:o=this.createEditBox(e);break;case l.CmptType.SPINE:r(this,void 0,void 0,function(){return a(this,function(n){switch(n.label){case 0:return[4,this.createSpine(e,t,o)];case 1:return n.sent(),[2]}})});break;case l.CmptType.GROUP:o=this.createGroup(e);break;case l.CmptType.SVGSHAPE:o=this.createSvgShape(e);break;case l.CmptType.CUTSHAPE:o=this.createShape(e);break;case l.CmptType.SPECIALCOMPONENT:r(this,void 0,void 0,function(){return a(this,function(t){switch(t.label){case 0:return[4,this.createChildComponent(e,o)];case 1:return t.sent(),[2]}})});break;case l.CmptType.OPTIONCOMPONENT:r(this,void 0,void 0,function(){return a(this,function(t){switch(t.label){case 0:return[4,this.createOptionComp(e,o)];case 1:return t.sent(),[2]}})});break;case l.CmptType.RICHTEXTSPRITE:r(this,void 0,void 0,function(){return a(this,function(n){switch(n.label){case 0:return[4,this.createRichTextSprite(e,t,o)];case 1:return n.sent(),[2]}})})}return o.name="cid_"+e.id,this.displayInit(o,e,t),o},t.prototype.getDisplayNode=function(e,t){return r(this,void 0,Promise,function(){var n,o;return a(this,function(i){switch(i.label){case 0:switch(console.log("%c Line:89 \ud83c\udf4a objectData","color:#e41a6a",e),n=e.type,o=new cc.Node,n){case l.CmptType.SPRITE:return[3,1];case l.CmptType.RICHTEXTSPRITE:return[3,3];case l.CmptType.SHAPE:return[3,5];case l.CmptType.FORMULA:return[3,7];case l.CmptType.LABEL:return[3,9];case l.CmptType.SPINE:return[3,10];case l.CmptType.GROUP:return[3,12];case l.CmptType.SVGSHAPE:return[3,13];case l.CmptType.CUTSHAPE:return[3,14];case l.CmptType.SPECIALCOMPONENT:return[3,15];case l.CmptType.OPTIONCOMPONENT:return[3,17]}return[3,19];case 1:return[4,this.createSprite(e,t,o)];case 2:return i.sent(),[3,20];case 3:return[4,this.createRichTextSprite(e,t,o)];case 4:return i.sent(),[3,20];case 5:return[4,this.createShape2(e,t,o)];case 6:return i.sent(),[3,20];case 7:return[4,this.createFormula(e,t,o)];case 8:return i.sent(),[3,20];case 9:return o=this.createEditBox(e),[3,20];case 10:return[4,this.createSpine(e,t,o)];case 11:return i.sent(),[3,20];case 12:return o=this.createGroup(e),[3,20];case 13:return o=this.createSvgShape(e),[3,20];case 14:return o=this.createShape(e),[3,20];case 15:return[4,this.createSpecialLibrary(e)];case 16:return o=i.sent(),[3,20];case 17:return[4,this.createOptionComp(e)];case 18:return o=i.sent(),[3,20];case 19:return[3,20];case 20:return o.name="cid_"+e.id,this.displayInit(o,e,t),[2,o]}})})},t.prototype.spineCheckData=function(e){return r(this,void 0,void 0,function(){return a(this,function(){return e.type===l.CmptType.SPINE?[2,yy.single.instance(b.default).initSpineData(e.id,e.properties,e.spineData)]:[2]})})},t.prototype.displayInit=function(e,t,n){return r(this,void 0,void 0,function(){var o,i,r;return a(this,function(a){switch(a.label){case 0:return(o=e.getComponent(m.default))?(o.init(),void 0===t.editable?o.editable=!0:o.editable=t.editable,o.cid=t.id,t.cName&&(o.cName=t.cName),i=t.dragable,o.dragable=void 0===i||i,o.type=this.getCmptTypeStr(t.type),r=t.properties,o.node.angle=0,o.node.cAngle=0,void 0!==t.properties.active&&o.type!==l.CmptType.SPINE&&o.type!==l.CmptType.SVGSHAPE&&(o.node.active=t.properties.active),void 0!==r.angle&&(o.node.angle=Number(r.angle),o.node.cAngle=Number(r.angle)),e.color=(new cc.Color).fromHEX(yy.checkValue(r.color,"#ffffff")),e.opacity=yy.checkValue(r.opacity,255),this.addHint(t)):yy.error("\u7ec4\u4ef6id="+t.id+"\uff0ctype="+t.type+"\u7684\u7ec4\u4ef6\u521b\u5efa\u5931\u8d25\u3002"),n?(this.addTageNode(o,n),this.addSignalNode(o,n),[3,4]):[3,1];case 1:return[4,this.addTageNode(o,n)];case 2:return a.sent(),[4,this.addSignalNode(o,n)];case 3:a.sent(),a.label=4;case 4:return o.initFinish(),[2]}})})},t.prototype.addHint=function(e){yy.log("====objectData= ==>",e),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.y),u.LINT_DISPLAY_TYPE.HORIZONTAL,u.LINT_POS_TYPE.HORIZONTAL),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.y)+Number(e.properties.height/2),u.LINT_DISPLAY_TYPE.HORIZONTAL,u.LINT_POS_TYPE.TOP),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.y)-Number(e.properties.height/2),u.LINT_DISPLAY_TYPE.HORIZONTAL,u.LINT_POS_TYPE.BOTTOM),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.x),u.LINT_DISPLAY_TYPE.VERTICAL,u.LINT_POS_TYPE.VERTICAL),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.x)-Number(e.properties.width/2),u.LINT_DISPLAY_TYPE.VERTICAL,u.LINT_POS_TYPE.LEFT),yy.single.instance(d.default).addHintLine(Number(e.id),!1,Number(e.properties.x)+Number(e.properties.width/2),u.LINT_DISPLAY_TYPE.VERTICAL,u.LINT_POS_TYPE.RIGHT)},t.prototype.createSprite=function(e,t,n){return r(this,void 0,void 0,function(){var o,i,s,c,p,l,u=this;return a(this,function(d){switch(d.label){case 0:return o=e.properties,(i=n)||(i=new cc.Node),s=i.addComponent(m.default),(c=i.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,p=function(){return r(u,void 0,void 0,function(){return a(this,function(e){switch(e.label){case 0:return i.x=o.x,i.y=o.y,i.scaleX=yy.checkValue(o.scaleX,1),i.scaleY=yy.checkValue(o.scaleY,1),i.width=Number(o.width),i.height=Number(o.height),[4,O.ComUtil.setMateria(c,o.flipType)];case 1:return e.sent(),[2]}})})},l=null,l=function(){return new Promise(function(e){yy.loader.loadRes(o.texture,cc.Texture2D,function(t,n){t?yy.warn(t):(n.packable=!1,O.ComUtil.addPremultiplyAlpha(n,c),c.___textureUrl=o.texture,s.initFinished(),e(i))})})},t?(l(),[3,3]):[3,1];case 1:return[4,l()];case 2:d.sent(),d.label=3;case 3:return[4,p()];case 4:return d.sent(),[2,i]}})})},t.prototype.createRichTextSprite=function(e,t,n){return r(this,void 0,void 0,function(){var o,i,s,c,p,l,u=this;return a(this,function(d){switch(d.label){case 0:return o=e.properties,(i=n)||(i=new cc.Node),s=i.addComponent(m.default),(c=i.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,p=function(){return r(u,void 0,void 0,function(){return a(this,function(e){switch(e.label){case 0:return i.x=o.x,i.y=o.y,i.scaleX=yy.checkValue(o.scaleX,1),i.scaleY=yy.checkValue(o.scaleY,1),i.width=Number(o.width),i.height=Number(o.height),[4,O.ComUtil.setMateria(c,o.flipType)];case 1:return e.sent(),[2]}})})},l=null,l=function(){return new Promise(function(e){o.texture&&""!=o.texture?yy.loader.loadRes(o.texture,cc.Texture2D,function(t,n){t?yy.warn(t):(n.packable=!1,O.ComUtil.addPremultiplyAlpha(n,c),c.___textureUrl=o.texture,s.initFinished(),e(i))}):(c.___textureUrl=o.texture,s.initFinished(),e(i))})},t?(l(),[3,3]):[3,1];case 1:return[4,l()];case 2:d.sent(),d.label=3;case 3:return[4,p()];case 4:return d.sent(),[2,i]}})})},t.prototype.createShape2=function(e,t,n){return r(this,void 0,void 0,function(){var o,i,s,c,p,l,u=this;return a(this,function(d){switch(d.label){case 0:return o=e.properties,(i=n)||(i=new cc.Node),s=i.addComponent(m.default),(c=i.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,p=function(){return r(u,void 0,void 0,function(){return a(this,function(){return i.x=o.x,i.y=o.y,i.scaleX=yy.checkValue(o.scaleX,1),i.scaleY=yy.checkValue(o.scaleY,1),i.width=Number(o.width),i.height=Number(o.height),[2]})})},l=null,l=function(){return new Promise(function(e){o.texture?yy.loader.loadRes(o.texture,cc.Texture2D,function(t,n){t?yy.warn(t):(c.spriteFrame=new cc.SpriteFrame(n),n.packable=!1,O.ComUtil.addPremultiplyAlpha(n,c),c.___textureUrl=o.texture,s.initFinished(),e(i))}):(s.initFinished(),e(i))})},t?(l(),[3,3]):[3,1];case 1:return[4,l()];case 2:d.sent(),d.label=3;case 3:return[4,p()];case 4:return d.sent(),[2,i]}})})},t.prototype.createFormula=function(e,t,n){return r(this,void 0,void 0,function(){var o,i,r,s,c,p;return a(this,function(a){switch(a.label){case 0:return o=e.properties,(i=n)||(i=new cc.Node),console.warn(JSON.stringify(o)),r=i.addComponent(m.default),(s=i.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,c=function(){i.x=o.x,i.y=o.y,i.scaleX=yy.checkValue(o.scaleX,1),i.scaleY=yy.checkValue(o.scaleY,1),i.width=Number(o.width),i.height=Number(o.height)},p=null,p=function(){return new Promise(function(e){yy.loader.loadRes(o.url,cc.Texture2D,function(t,n){t?yy.warn(t):(O.ComUtil.addPremultiplyAlpha(n,s),s.___textureUrl=o.url,r.initFinished(),e(i))})})},t?(p(),[3,3]):[3,1];case 1:return[4,p()];case 2:a.sent(),a.label=3;case 3:return c(),[2,i]}})})},t.prototype.createEditBox=function(e){var t=e.properties,n=new cc.Node;return n.addComponent(h.default).initData(t),yy.log("======>",t),n},t.prototype.createSpine=function(e,t,n){return r(this,void 0,Promise,function(){var o,i,r,c,l,u,d,f,h,m,g,v;return a(this,function(a){switch(a.label){case 0:return o=e.properties,i=p.default.clone(o),r=new y.SpineData,(c=n)||(c=new cc.Node),l=new cc.Node("spineNode"),u=l.addComponent(sp.Skeleton),d=[],f=0,h=new sp.SkeletonData,c.addComponent(_.default),l.addComponent(s.default),r.atlas=e.spineData.atlas,r.images=e.spineData.images,r.skeleton=e.spineData.skeleton,c.addChild(l),c.x=i.x,c.y=i.y,c.width=Number(i.width),c.height=Number(i.height),m=new Promise(function(e,t){yy.loader.loadRes(r.atlas,cc.TextAsset,function(n,o){n&&(yy.error(n),t(n)),e(o)})}),g=new Promise(function(e,t){yy.loader.loadRes(r.skeleton,cc.JsonAsset,function(n,o){n&&(yy.error(n),t(n)),e(o)})}),v=new Promise(function(e,t){for(var n=new Map,o=function(o,i){yy.loader.loadRes(o,cc.Texture2D,function(o,a){if(o)yy.error(o),t(o);else if(a.setPremultiplyAlpha(!0),n.set(i,a),a.setPremultiplyAlpha&&a.setPremultiplyAlpha(!0),++f>=r.images.length){for(var s=0;s<r.images.length;s++)d.push(n.get(s));e(d)}})},i=0;i<r.images.length;i++)o(r.images[i],i)}),t?(Promise.all([m,g,v]).then(function(t){h.atlasText=t[0].text,h.skeletonJson=t[1].json,h.textures=t[2],h.textureNames=r.imageNames,u.skeletonData=h,yy.single.instance(b.default).initSpineData(e.id,i,r)}),[3,3]):[3,1];case 1:return[4,Promise.all([m,g,v]).then(function(e){h.atlasText=e[0].text,h.skeletonJson=e[1].json,h.textures=e[2],h.textureNames=r.imageNames,u.skeletonData=h})];case 2:a.sent(),a.label=3;case 3:return[2,c]}})})},t.prototype.createSvgShape=function(e){var t=e.properties,n=new cc.Node,o=n.addComponent(v.default);return o.onInit(t),o.scheduleOnce(function(){o.getShapeRect(),n.setPosition(t.x,t.y);var e=cc.size(t.width,t.height);n.setContentSize(t.width,t.height),o.setSvgNodeSize(e),o.createVertexBtn(),o.initFinished()},0),n},t.prototype.createShape=function(e){var t=new cc.Node,n=t.addComponent(C.default),o=yy.cloneValues(e.properties);return t.x=e.properties.x,t.y=e.properties.y,t.width=e.properties.width,t.height=e.properties.height,n.initData(o,e.subType),n.initFinished(),t},t.prototype.createGroup=function(e){var t=e.properties,n=new cc.Node,o=n.addComponent(g.default);return n.x=t.x,n.y=t.y,n.width=t.width,n.height=t.height,o.initFinished(),n},t.prototype.createChildComponent=function(e,t){return r(this,void 0,void 0,function(){var n,o,i,r;return a(this,function(a){switch(a.label){case 0:return n=e.subType.toString(),(o=t.addComponent(w.default)).dragable=e.dragable,o.cid=e.id,o.editable=e.editable,t.x=e.properties.x,t.y=e.properties.y,t.width=e.properties.width,t.height=e.properties.height,i=yy.single.instance(T.default).getBundel("qte"),[4,assemble.factory.createObject(n,e,i)];case 1:return r=a.sent(),t.addChild(r.node),o.assembleComponent=r,console.log(e),o.initComponent(e),o.initFinished(),[2]}})})},t.prototype.createSpecialLibrary=function(e,t){return r(this,void 0,Promise,function(){var n,o,i,r,s;return a(this,function(a){switch(a.label){case 0:return n=t||new cc.Node,o=e.subType.toString(),i=yy.single.instance(T.default).getBundel("qte"),console.log("%c Line:505 \ud83c\udf6b bundel","color:#3f7cff",i),[4,assemble.factory.createObject(o,e,i)];case 1:return r=a.sent(),n.addChild(r.node),n.x=e.properties.x,n.y=e.properties.y,n.width=e.properties.width,n.height=e.properties.height,(s=n.addComponent(w.default)).dragable=e.dragable,s.cid=e.id,s.editable=e.editable,s.assembleComponent=r,console.log(e),s.initComponent(e),s.initFinished(),[2,n]}})})},t.prototype.createOptionComp=function(e,t){return r(this,void 0,Promise,function(){var n,o,i,r,s;return a(this,function(a){switch(a.label){case 0:return console.log("%c Line:525 \ud83c\udf56 objectData","color:#f5ce50",e),n=t||new cc.Node,o=e.subType.toString(),i=yy.single.instance(T.default).getBundel(window._$store.state.template.bundleName),[4,I.default.getInstance().createObject(o,e,i)];case 1:return r=a.sent(),console.log("%c Line:528 \ud83c\udf30 baseComponent","color:#e41a6a",r),n.addChild(r.node),n.x=e.properties.x,n.y=e.properties.y,n.width=e.properties.width,n.height=e.properties.height,(s=n.addComponent(w.default)).dragable=e.dragable,s.cid=e.id,s.editable=e.editable,s.assembleComponent=r,console.log(e),s.initComponent(e),s.initFinished(),[2,n]}})})},t.prototype.getDisplayGroupNode=function(e){void 0===e&&(e="1");var t=new y.default;return t.id=e,t.type=l.CmptType.GROUP,this.createGroup(t)},t.prototype.getCmptTypeStr=function(e){switch(e){case l.CmptType.COCOSANI:return"cocosAni";case l.CmptType.SPRITE:return"sprite";case l.CmptType.RICHTEXTSPRITE:return"richTextSprite";case l.CmptType.FORMULA:return"formula";case l.CmptType.SPINE:return"spine";case l.CmptType.LABEL:return"label";case l.CmptType.GROUP:return"group";case l.CmptType.SVGSHAPE:return"svgShape";case l.CmptType.CUTSHAPE:return"cutShape";case l.CmptType.SPECIALCOMPONENT:return"specialComponent";case l.CmptType.OPTIONCOMPONENT:return"optionComponent";case l.CmptType.SHAPE:return"shape"}},t.prototype.specialComponentPath=function(e){var t="";switch(e){case l.SpecialComponentSubTypes.CmptRecord:t="assemble/record/res/prefabs/entry";break;case l.SpecialComponentSubTypes.KeyBord:t="assemble/keyboard/prefabs/keyboard";break;case l.SpecialComponentSubTypes.KeyBordEnglish:t="assemble/keyboardEnglish/prefabs/keyboardEnglish";break;case l.SpecialComponentSubTypes.Write:t="assemble/write/prefabs/write";break;case l.SpecialComponentSubTypes.MatchBoard:t="assemble/matchBoard/prefab/matchBoard";break;case l.SpecialComponentSubTypes.Counter:t="assemble/countCompoent/prefabs/counter";break;case l.SpecialComponentSubTypes.Clock:t="assemble/clock/prefabs/clockRoot";break;case l.SpecialComponentSubTypes.Tangram:t="assemble/tangram/prefab/tangram";break;case l.SpecialComponentSubTypes.Speaker:t="assemble/speaker/prefabs/speaker";break;case l.SpecialComponentSubTypes.Brush:t="assemble/drawControl/prefab/drawControl"}return t},t.prototype.addTageNode=function(e,t){return r(this,void 0,void 0,function(){var n,o,i,r,s,c,p,l,u;return a(this,function(a){switch(a.label){case 0:return n=yy.single.instance(f.default).getComponetTagName(e.cid),o=yy.single.instance(f.default).getComponentMap()[e.cid].extra,(i=new cc.Node).height=30,i.anchorX=0,i.anchorY=.5,i.name="tag_Node",(r=i.addComponent(cc.Layout)).type=cc.Layout.Type.HORIZONTAL,r.resizeMode=cc.Layout.ResizeMode.CONTAINER,(s=i.addComponent(cc.Widget)).isAlignLeft=!0,s.isAlignTop=!0,s.left=5,s.top=-35,s.alignMode=cc.Widget.AlignMode.ALWAYS,(c=i.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,p=function(){return new Promise(function(e,t){cc.loader.loadRes("img/bg",cc.Texture2D,function(n,o){if(n)return yy.warn(n),void t(n);O.ComUtil.addPremultiplyAlpha(o,c),e()})})},t?(p(),[3,3]):[3,1];case 1:return[4,p()];case 2:a.sent(),a.label=3;case 3:return l=new cc.Node,u=l.addComponent(cc.Label),l.anchorX=0,l.anchorY=.5,l.color=(new cc.Color).fromHEX("#63390E"),u.useSystemFont=!0,u.fontFamily="Arial",u.fontSize=20,u.horizontalAlign=cc.Label.HorizontalAlign.LEFT,O.ComUtil.setLabelBlend(u),e.node.addChild(i),i.addChild(l),l.position=cc.v3(0,-14),i.group="edge",e.showDisplayTag(n,o),console.log("showDisplayTag1"),[2]}})})},t.prototype.addSignalNode=function(e,t){return r(this,void 0,void 0,function(){var n,o,i,r,s,c,p,l;return a(this,function(a){switch(a.label){case 0:return n=yy.single.instance(f.default).getComponetTagName(e.cid),console.log("%c Line:674 \ud83e\udd55 tagStr","color:#7f2b82",n),(o=new cc.Node).height=30,o.anchorX=0,o.anchorY=.5,o.name="signs_Node",(i=o.addComponent(cc.Layout)).type=cc.Layout.Type.HORIZONTAL,i.resizeMode=cc.Layout.ResizeMode.CONTAINER,(r=o.addComponent(cc.Widget)).isAlignLeft=!0,r.isAlignTop=!0,r.left=5,r.top=-35,r.alignMode=cc.Widget.AlignMode.ALWAYS,(s=o.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,c=function(){return new Promise(function(e,t){cc.loader.loadRes("img/bg",cc.Texture2D,function(n,o){if(n)return yy.warn(n),void t(n);O.ComUtil.addPremultiplyAlpha(o,s),e()})})},t?(c(),[3,3]):[3,1];case 1:return[4,c()];case 2:a.sent(),a.label=3;case 3:return p=new cc.Node,l=p.addComponent(cc.Label),p.anchorX=0,p.anchorY=.5,p.color=(new cc.Color).fromHEX("#63390E"),l.useSystemFont=!0,l.fontFamily="Arial",l.fontSize=20,l.horizontalAlign=cc.Label.HorizontalAlign.LEFT,O.ComUtil.setLabelBlend(l),e.node.addChild(o),o.addChild(p),p.position=cc.v3(0,-14),o.group="default",e.showDisplaySignal(0),[2]}})})},t}(c.SingleBase);n.default=P,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/component/spine/SpineRect":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../../AssetsManager":"AssetsManager","../../common/ComUtils":"ComUtils","../../common/EditorEnum":"EditorEnum","../hintline/HintLineDisplay":"HintLineDisplay","../hintline/HintLineManager":"HintLineManager","../proxy/ComptData":"ComptData","../proxy/DataCenterBridge":"DataCenterBridge","./OptSubObjectFactory":"OptSubObjectFactory","./SpineManager":"SpineManager","./base/DisplayEditBox":"DisplayEditBox","./base/DisplayObject":"DisplayObject","./base/DisplayObjectGroup":"DisplayObjectGroup","./base/DisplaySpecialComponent":"DisplaySpecialComponent","./base/DisplaySpine":"DisplaySpine","./base/DisplaySvg":"DisplaySvg","./base/edit/DisplayCutShape":"DisplayCutShape"}],DisplayObjectGroup:[function(e,t,n){"use strict";cc._RF.push(t,"a30f1zZoeJJJ5srMt2lNevT","DisplayObjectGroup");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../qte/core/utils/MathUtil"),s=e("../../../../qte/core/utils/ValueUtils"),c=e("./DisplayObject"),p=cc._decorator.ccclass,l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._groupIds=[],t._groupSubs=[],t._oldSubDisplay=[],t}return i(t,e),Object.defineProperty(t.prototype,"groupIds",{get:function(){return s.default.clone(this._groupIds)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"groudSubs",{get:function(){return this._groupSubs},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"rect",{get:function(){return new cc.Rect(this.node.x,this.node.y,this.node.width,this.node.height)},enumerable:!1,configurable:!0}),t.prototype.showEdge=function(){e.prototype.showEdge.call(this)},t.prototype.showCenterEdge=function(e,t){var n=new cc.Node;n.addComponent(cc.Graphics),n.name="edge_node_node",this.node.parent.addChild(n);var o=n.getComponent(cc.Graphics);o.strokeColor=t,o.clear();var i=e;o.moveTo(i.x-10,i.y-10),o.lineTo(i.x-10,i.y+10),o.lineTo(i.x+10,i.y+10),o.lineTo(i.x+10,i.y-10),o.lineTo(i.x-10,i.y-10),o.stroke()},t.prototype.addSubObject=function(e,t){this._groupIds.indexOf(e)>=0?yy.warn("id = "+e+"\u7684\u5b50\u7ec4\u4ef6\u4ee5\u53ca\u6dfb\u52a0\u8fc7\u4e86."):(this._groupIds.push(e),this._groupSubs.push(t),this.showSubCmptTag(e,!1),this.showSubCmptSignal(e,!1))},t.prototype.removeSubObject=function(e){var t=this._groupIds.indexOf(e),n=-1;if(t<0)yy.warn("id = "+e+"\u7684\u5b50\u7ec4\u4ef6\u4e0d\u5b58\u5728.");else{for(var o=0;o<this._groupSubs.length-1;o++)this._groupSubs[o].cid===e&&(n=o);this._groupIds.splice(t,1),this._groupSubs.splice(n,1),this.showSubCmptTag(e,!0)}},t.prototype.resetLayoutSize=function(e){void 0===e&&(e=!1);var t=[];if(0!==this._groupSubs.length){for(var n=cc.v3(this.node.position.x,this.node.position.y),o=this.getNodeAngle(),i=0,r=this._groupSubs;i<r.length;i++)for(var s=r[i],c=a.default.getRotaRectNew(s.node),p=0;p<c.length;p++){var l=c[p],u=cc.v3(l.x,l.y).add(n);t.push(cc.v3(u.x,u.y))}if(0!==t.length){var d=a.default.getXY(t),y=a.default.getCenterPosByPosArr(t),f=d.minX,h=d.maxX,m=d.minY,g=d.maxY;if(this.node.width=h-f,this.node.height=g-m,!e){var _=y.sub(this.node.position);this.node.position=y;for(var v=0,b=this._groupSubs;v<b.length;v++)(s=b[v]).node.position=s.node.position.sub(_);if(0!==o){var C=cc.v2(y.x,y.y),O=cc.v2(n.x,n.y),T=a.default.getAngleAfterPos(o,O,C);this.node.setPosition(T)}}this.refreshRectPoint()}}},t.prototype.clearGroupIds=function(){for(var e=0,t=this._groupSubs;e<t.length;e++)t[e].setTagActive(!0);this._oldSubDisplay=this._groupSubs,this._groupIds=[],this._groupSubs=[]},t.prototype.showSubCmptSignal=function(e,t){for(var n=0,o=this._groupSubs;n<o.length;n++){var i=o[n];i.cid===e&&i.setSignalActive(t)}},t.prototype.showSubCmptTag=function(e,t){for(var n=0,o=this._groupSubs;n<o.length;n++){var i=o[n];i.cid===e&&i.setTagActive(t)}},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this);t.extra.subProperties=[];for(var n=0,o=this._groupSubs;n<o.length;n++){var i=o[n];t.extra.subProperties.push(i.getNewProperties())}return t.extra.groupIds=this._groupIds,t},r([p],t)}(c.default);n.default=l,cc._RF.pop()},{"../../../../qte/core/utils/MathUtil":void 0,"../../../../qte/core/utils/ValueUtils":void 0,"./DisplayObject":"DisplayObject"}],DisplayObjectManager:[function(e,t,n){"use strict";cc._RF.push(t,"87731mr/M9PXb+fRDzCCGhm","DisplayObjectManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../../qte/core/base/SingleBase"),c=e("../../../qte/core/utils/MathUtil"),p=e("../../../qte/core/utils/ValueUtils"),l=e("../../common/EditorEnum"),u=e("../../utils/Properties"),d=e("../hintline/HintLineManager"),y=e("../proxy/DataCenterBridge"),f=e("../../../qte/util/QTEUtils"),h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._dpMap=null,t._dpIndexMap=null,t._selectId="-1",t._selectSubId="-1",t}return i(t,e),Object.defineProperty(t.prototype,"dpMap",{get:function(){return this._dpMap},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dpIndexMap",{get:function(){return this._dpIndexMap},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectId",{get:function(){return this._selectId},set:function(e){this._selectId=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectSubId",{get:function(){return this._selectSubId},set:function(e){this._selectSubId=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selectedIds",{get:function(){return this._selectedIds},enumerable:!1,configurable:!0}),t.prototype.initInstance=function(){this._dpMap=new Map,this._selectedIds=[],this._dpIndexMap=new Map},t.prototype.hasOwnedGroup=function(e,t){for(var n=0;n<t.length;n++){var o=t[n],i=this.getDisplayObjectById(o);if(i.type===l.CmptType.GROUP)for(var r=i.groupIds,a=0;a<r.length;a++)if(r[a]===e)return!0}return!1},t.prototype.getRootIds=function(){for(var e=yy.cloneValues(this.selectedIds),t=[],n=0,o=e;n<o.length;n++){var i=o[n];this.getDisplayObjectById(i).type===l.CmptType.GROUP?t.push(i):this.hasOwnedGroup(i,e)||t.push(i)}return t},t.prototype.getSubIds=function(){var e=yy.cloneValues(this.selectedIds),t=[];if(2==e.length){var n=this.getDisplayObjectById(e[0]);if(n.type===l.CmptType.GROUP)for(var o=n.groupIds,i=0;i<o.length;i++)if(o[i]===e[1])return t.push(e[1]),t}return t},t.prototype.addDisplayObject=function(e,t){this._dpMap.set(e,t),console.log("%c Line:133 \ud83c\udf54 object.node.getSiblingIndex()","color:#fca650",t.node.getSiblingIndex(),e),this._dpIndexMap.set(e,t.node.getSiblingIndex())},t.prototype.updateDisplayObjectIndex=function(e,t){this._dpIndexMap.set(e,t)},t.prototype.compareAndRefreshDisplayObjectIndex=function(){var e=yy.single.instance(y.default).getComponentIds();console.log("%c Line:148 \ud83c\udf36 editorData","color:#33a5ff",e);for(var t=[],n=[],o=0,i=Array.from(this._dpIndexMap.entries());o<i.length;o++){var r=i[o],a=r[0],s=r[1];this.isSubNode(a)?(console.log("%c Line:170 \ud83c\udf82 index","color:#7f2b82",s),n.push([a,s])):t.push([a,s])}var c=!1;console.log("%c Line:177 \ud83e\udd64 _dpIndex","color:#f5ce50",t),console.log("%c Line:178 \ud83e\udd64 _dpSubIndex","color:#f5ce50",n);for(var p=function(o){var i=e[o];if("string"==typeof i){if(-1!==(r=t.findIndex(function(e){return e[0]===i}))&&t[r][1]!==o)return c=!0,"break"}else{var r;if(-1!==(r=t.findIndex(function(e){return e[0]===i.id}))&&t[r][1]!==o)return c=!0,"break";for(var a=function(e){var t=i.subIds[e],o=n.findIndex(function(e){return e[0]===t});if(-1!==o&&n[o][1]!==e+2)return c=!0,"break"},s=0;s<i.subIds.length&&"break"!==a(s);s++);if(c)return"break"}},l=0;l<e.length&&"break"!==p(l);l++);if(console.log("%c Line:177 \ud83e\udd64 isDiff","color:#f5ce50",c),c){var u=new Map;for(l=0;l<e.length;l++){var d=e[l];if("string"==typeof d)u.set(d,l);else{u.set(d.id,l);for(var f=0;f<d.subIds.length;f++)u.set(d.subIds[f],f+2)}}console.log("%c Line:223 \ud83c\udf6f newDpIndexMap","color:#4fff4B",u),this._dpIndexMap=u,this.sortDisplayObjectByIndex()}},t.prototype.sortDisplayObjectByIndex=function(){if(console.log("\u8c03\u7528 sortDisplayObjectByIndex, _dpIndexMap:",JSON.stringify(Array.from(this._dpIndexMap.entries()))),console.log("\u8c03\u7528 sortDisplayObjectByIndex, _dpMap:",Array.from(this._dpMap.keys())),0!==this._dpMap.size){for(var e=new Map,t=0,n=Array.from(this._dpMap.entries());t<n.length;t++){var o=n[t],i=o[0],r=o[1],a=this._dpIndexMap.get(i);if(void 0!==a&&r.node&&r.node.parent){var s=r.node.parent;e.has(s)||e.set(s,[]),e.get(s).push({node:r.node,targetIndex:a,id:i})}else console.warn("DisplayObjectManager: Node, parent, or targetIndex not found for id "+i)}for(var c=0,p=Array.from(e.entries());c<p.length;c++){var l=p[c],u=(s=l[0],l[1]);u.sort(function(e,t){return e.targetIndex!==t.targetIndex?e.targetIndex-t.targetIndex:e.id.localeCompare(t.id)}),console.log("Sorted nodes for parent "+s.name+":",u.map(function(e){return{id:e.id,targetIndex:e.targetIndex}})),u.forEach(function(e,t){e.node&&e.node.setSiblingIndex(t)})}}},t.prototype.getOriginSiblingIndexById=function(e){return this._dpIndexMap.get(e)},t.prototype.isSubNode=function(e){for(var t=0,n=Array.from(this._dpMap.entries());t<n.length;t++){var o=n[t],i=o[0],r=o[1];if(r.type===l.CmptType.GROUP&&r.groupIds.includes(e))return i}return!1},t.prototype.updateOriginSiblingIndexById=function(e,t){if(console.log("%c Line:219 \ud83c\udf6b updates","color:#42b983",e),e&&0!==e.length){for(var n=0,o=e;n<o.length;n++){var i=o[n];if(!this._dpIndexMap.has(i.id))return void console.warn("[updateOriginSiblingIndexById] ID "+i.id+" not found in _dpIndexMap.")}for(var r=this.isSubNode(e[0].id)||"root",a=0,s=e;a<s.length;a++){i=s[a];var c=this.isSubNode(i.id)||"root";if(c!==r)return void console.warn("[updateOriginSiblingIndexById] \u6240\u6709\u66f4\u65b0\u8282\u70b9\u5fc5\u987b\u5c5e\u4e8e\u540c\u4e00\u4e0a\u4e0b\u6587. \u8282\u70b9 "+i.id+" \u7684\u4e0a\u4e0b\u6587 "+c+" \u4e0e\u9884\u671f "+r+" \u4e0d\u5339\u914d")}for(var p=new Map,l=new Map,u=0,d=Array.from(this._dpIndexMap.entries());u<d.length;u++){var y=d[u],f=y[0],h=y[1];(this.isSubNode(f)||"root")===r?p.set(f,h):l.set(f,h)}console.log("%c Line:333 \ud83c\udf4c sameContextNodes","color:#6ec1c2",Array.from(p.entries())),console.log("%c Line:337 \ud83e\udd65 otherContextNodes","color:#42b983",Array.from(l.entries()));for(var m=new Set(e.map(function(e){return e.id})),g=new Set(e.map(function(e){return e.newIndex})),_=new Map,v=[],b=0,C=Array.from(p.entries());b<C.length;b++){var O=C[b],T=(f=O[0],O[1]);m.has(f)||(g.has(T)?v.push({id:f,originalIndex:T}):_.set(f,T))}for(var w=0,I=e;w<I.length;w++)i=I[w],_.set(i.id,i.newIndex);for(var P=0,S=v;P<S.length;P++){var A=S[P],D=this.findNextAvailableIndex(_,A.originalIndex);_.set(A.id,D)}console.log("%c \ud83c\udfc6 \u5206\u9636\u6bb5\u5904\u7406\u5b8c\u6210\uff0c\u6700\u7ec8\u5206\u914d\u65b9\u6848","color:#e41a6a",Array.from(_.entries())),this._dpIndexMap.clear();for(var x=0,E=Array.from(l.entries());x<E.length;x++){var L=E[x];f=L[0],h=L[1],this._dpIndexMap.set(f,h)}for(var M=0,j=Array.from(_.entries());M<j.length;M++){var R=j[M];f=R[0],h=R[1],this._dpIndexMap.set(f,h)}t&&this.sortDisplayObjectByIndex(),console.log("[updateOriginSiblingIndexById] \u6279\u91cf\u66f4\u65b0\u5b8c\u6210 (\u7b56\u75652-\u5206\u9636\u6bb5\u5904\u7406):",JSON.stringify(Array.from(this._dpIndexMap.entries())))}},t.prototype.findNextAvailableIndex=function(e,t){for(var n=new Set(Array.from(e.values())),o=t;n.has(o);)o++;return o},t.prototype.removeDisplayObject=function(e){var t=this.getDisplayObjectById(e);if(t&&cc.isValid(t.node)){this._dpMap.delete(e),this._dpIndexMap.delete(e),t.node&&(console.warn(yy.loader.cmptAssets.get(e),yy.loader.assetsMap,"dengchao"),yy.loader.cmptAssets.get(e)&&(yy.loader.cmptAssets.get(e).forEach(function(e){yy.loader.assetsMap.get(e).refCount>0&&yy.loader.assetsMap.get(e).decRef()}),yy.loader.cmptAssets.delete(e)),t.node.destroy()),yy.single.instance(d.default).removeHintLineByDisplay(e);for(var n=-1,o=0;o<this.selectedIds.length;o++)if(this.selectedIds[o]===e){n=o;break}-1!==n&&this.selectedIds.splice(n,1)}},t.prototype.getDisplayObjectById=function(e){return this._dpMap.get(e)},t.prototype.getDisplayObjectIndexById=function(e){return this._dpIndexMap.get(e)},t.prototype.getDisplayObjectByTag=function(e){var t=[];return this._dpMap.forEach(function(n,o){yy.single.instance(y.default).getComponentMap()[o].tag==e&&t.push(n)}),t},t.prototype.updateDisplayObject=function(e,t,n){var o=this.getDisplayObjectById(e);if(o){var i={},r=(o.editable||{}).properties;for(var a in t)if(!r||"boolean"!=typeof r[a]||r[a]){var s=u.default.setProperty(o,o.type,a,t[a],n);void 0!==s.value&&(i[a]=s.value)}if(yy.single.instance(d.default).updateLineByDisplayScale(e),o.type===l.CmptType.GROUP)for(var c=o.groupIds,p=0;p<c.length;p++)yy.single.instance(d.default).updateLineByDisplayScale(c[p]);return o.refreshRectPoint(),i}yy.error("id = "+e+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728")},t.prototype.updateDisplayObjectDeltaPos=function(e,t){var n=this.getDisplayObjectById(e);if(n){if(n.dragable){var o=cc.v3(t.x,t.y),i=(n.editable||{}).properties;"object"==typeof n.editable&&i&&("boolean"!=typeof i.x||i.x||(o.x=0),"boolean"!=typeof i.y||i.y||(o.y=0));var r=this.getDisplayGroupID(e);if("-1"!==r){var a=this.getDisplayObjectById(r);a&&c.default.rotatePosSelf(o,a.node.angle)}n.node.position=n.node.position.add(o),n.type===l.CmptType.GROUP&&n.refreshRectPoint(),yy.single.instance(d.default).updateLineByDisplayScale(e);var s=this.getDisplayGroupID(e);if("-1"!==s){var p=this.getDisplayObjectById(s);p.resetLayoutSize();for(var u=p.groupIds,y=0;y<u.length;y++)yy.single.instance(d.default).updateLineByDisplayScale(u[y]);yy.single.instance(d.default).updateLineByDisplayScale(s)}}}else yy.error("id = "+e+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728")},t.prototype.setSelected=function(e,n){if(this.checkIsValidNode(this._selectedIds))if("-1"!==e){var o=this.getDisplayObjectById(e);if(o&&cc.isValid(o.node))if(n){var i=o.getSelected();if(i){var r=this._selectedIds.indexOf(e);r>=0&&this._selectedIds.splice(r,1)}else this._selectedIds.push(e);o.setSelected(!i);var a=yy.single.instance(t).selectedIds;if(a.length>0)for(var s=0,c=a;s<c.length;s++){var p=c[s];yy.single.instance(t).getDisplayObjectById(p).moveToTop()}}else{if(this._selectedIds.indexOf(e)>=0)return;for(var l=0,u=this._selectedIds;l<u.length;l++)f=u[l],this.getDisplayObjectById(f).setSelected(!1);this._selectedIds=[],this._selectedIds.push(e),o.setSelected(!o.getSelected())}}else{for(var d=0,y=this._selectedIds;d<y.length;d++){var f=y[d];this.getDisplayObjectById(f).setSelected(!1)}this._selectedIds=[]}else this._selectedIds=[]},t.prototype.removeGroupDisplayObject=function(e){var t=this.getDisplayObjectById(e);if(t){if(t.type===l.CmptType.GROUP){t.setSelected(!1);for(var n=this._dpIndexMap.get(e),o=t.node.parent,i=t.groupIds,r=t,a=[],s=0,p=i;s<p.length;s++){var u=p[s];if(v=this.getDisplayObjectById(u)){var y=v.node.getSiblingIndex();a.push({id:u,_index:y})}}a=c.default.getSortDisplayArr(a),console.log("%c Line:545 \ud83e\udd65 sortArr","color:#ffdd4d",a);for(var f=0,h=a;f<h.length;f++)for(var m=h[f],g=0,_=i;g<_.length;g++){var v;if(u=_[g],(v=this.getDisplayObjectById(u))&&m.id===u){v.node.parent=o,console.log("tempObj==index222=",u,"tempObj.node.getSiblingIndex()",v.node.getSiblingIndex());var b=c.default.getRotationPoint(v.node.x+r.node.x,v.node.y+r.node.y,r.node.x,r.node.y,yy.checkValue(r.node.angle,0));v.node.x=b.x,v.node.y=b.y;var C=yy.checkValue(v.node.angle,0)+yy.checkValue(r.node.angle,0);v.node.angle>360&&(C=v.node.angle%360),v.node.cAngle=C,v.node.angle=C,v.node.opacity=v.node.opacity,yy.single.instance(d.default).addHintLinebyDisplay(u);break}}t.clearGroupIds();for(var O=[],T=this.isSubNode(e)||"root",w=[],I=0,P=Array.from(this._dpIndexMap.entries());I<P.length;I++){var S=P[I],A=S[0],D=S[1];(this.isSubNode(A)||"root")===T&&w.push({id:A,originalIndex:D})}w.sort(function(e,t){return e.originalIndex-t.originalIndex});for(var x=i.length>0?i.length-1:0,E=new Set(a.map(function(e){return e.id})),L=w.filter(function(e){return e.originalIndex>n&&!E.has(e.id)}),M=0,j=0,R=a;j<R.length;j++){m=R[j];var N=this._dpIndexMap.get(m.id);O.push({id:m.id,newIndex:n+M,oldIndex:N}),M++}for(var B=0,F=L;B<F.length;B++){var U=F[B];U.id!==e&&O.push({id:U.id,newIndex:U.originalIndex+x,oldIndex:U.originalIndex})}return this.updateOriginSiblingIndexById(O,!0),i}yy.warn("id = "+e+"\u7684\u7ec4\u4ef6\u4e0d\u662fGroup")}else yy.warn("id = "+e+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728")},t.prototype.checkIsValidNode=function(e){for(var t=0,n=e;t<n.length;t++){var o=n[t];if(!this.getDisplayObjectById(o))return yy.warn("id = "+o+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728"),!1}return!0},t.prototype.getDisplayParentID=function(e){for(var t=yy.single.instance(y.default).getComponentIds(),n=0;n<t.length;n++){var o=t[n];if(o.id){var i=this.getDisplayObjectById(o.id);if(i&&i.childIds.find(function(t){return t===e}))return o.id}}return"-1"},t.prototype.getDisplayGroupID=function(e){for(var t=yy.single.instance(y.default).getComponentIds(),n=0;n<t.length;n++){var o=t[n];if(o.id){var i=this.getDisplayObjectById(o.id);if(i&&i.type===l.CmptType.GROUP&&i.groupIds.find(function(t){return t===e}))return o.id}}return"-1"},t.prototype.getWorldPos=function(e){var t=this.getDisplayObjectById(e);if(!t)return{x:0,y:0};var n=t.rect,o=t.node.convertToWorldSpaceAR(cc.v2(-n.width/2,n.height/2)),i=o.y,r=cc.winSize,a=cc.v2(r.width/2+o.x,-i+r.height/2);return{x:p.default.setOneDecimal(a.x,1),y:p.default.setOneDecimal(a.y)}},t.prototype.getWorldRect=function(e){var t=this.getWorldPos(e),n=this.getDisplayObjectById(e),o=n.node.getBoundingBoxToWorld();return n||(o=cc.rect(0,0,0,0)),cc.rect(t.x,t.y,o.width,o.height)},t.prototype.cWorldPosToEWorldPos=function(e){return cc.v2(cc.winSize.width/2+e.x,-e.y+cc.winSize.height/2)},t.prototype.getRotateEWordRect=function(e){var t=this.getDisplayObjectById(e);if(!t)return cc.rect(0,0,0,0);var n=t.rect,o=new cc.Node;o.parent=t.node,o.setContentSize(cc.size(n.width,n.height));var i=o.getBoundingBoxToWorld(),r=this.cWorldPosToEWorldPos(cc.v2(i.xMin,i.yMax));return cc.rect(r.x,r.y,i.width,i.height)},t.prototype.traverseObject=function(e,t){var n=Object.keys(e);for(var o in n)if(n[o]===t)return!0;return!1},t.prototype.getSubUpdateFinish=function(e,t){return r(this,void 0,Promise,function(){var n=this;return a(this,function(){return[2,new Promise(function(o){return r(n,void 0,void 0,function(){var n,i,r,s;return a(this,function(a){switch(a.label){case 0:for(s in n=this.getDisplayObjectById(e),i=n.getNewProperties(),r=null,t)if(this.traverseObject(i.newProperties,s)){r=s;break}return[4,n.assembleComponent.changeProperties(r,t[r])];case 1:return a.sent(),o(),[2]}})})})]})})},t.prototype.convertEditorWorldPos=function(e,t){t=cc.v2(p.default.setOneDecimal(t.x,1),p.default.setOneDecimal(t.y));var n=this.getDisplayObjectById(e),o=this.getWorldPos(e);if(n){var i=n.rect,r=(cc.winSize,n.node.convertToWorldSpaceAR(cc.v2(-i.width/2,i.height/2))),a=cc.instantiate(n.node);a.angle=0,a.position=n.node.position,a.parent=n.node.parent,a.active=!1;var s=a.convertToWorldSpaceAR(cc.v2(-i.width/2,i.height/2)),c=this.cWorldPosToEWorldPos(r).sub(this.cWorldPosToEWorldPos(s)),l=a.getBoundingBoxToWorld(),u=n.node.getBoundingBoxToWorld(),d=cc.v2(u.x-l.x,u.y-l.y),y=t.add(c).add(cc.v2(-d.x,-d.y));return a.destroy(),t.x===o.x&&(y.x=t.x),t.y===o.y&&(y.y=t.y),{x:p.default.setOneDecimal(y.x,1),y:p.default.setOneDecimal(y.y)}}},t.prototype.getCaptureByNode=function(e){var t=this.getDisplayObjectById(e);t.setTagActive(!1),t||console.error("getCaptureByNode error: display is null");var n=f.default.captureNode(t);return t.setTagActive(!0),n},t.prototype.localToWorld=function(e,t){return c.default.localToWorld(void 0,e,t)},t.prototype.resetNodePos=function(e,t){var n=this.getDisplayObjectById(e);if(n){var o,i=n.rect,r=cc.winSize,a=n.node.convertToWorldSpaceAR(cc.v2(-i.width/2,i.height/2)),s=a.y,p=cc.v2(r.width/2+a.x,-s+r.height/2),l=cc.v2(p.x-r.width/2,-p.y+r.height/2),u=n.node.convertToNodeSpaceAR(l),d=cc.v2(t.x-r.width/2,-t.y+r.height/2),y=n.node.convertToNodeSpaceAR(d).sub(u);if("object_layer"!==n.node.parent.name){var f=c.default.getRotationPoint(y.x,y.y,0,0,n.node.angle);return{x:(o=n.node.position.add(cc.v3(f.x,f.y))).x,y:o.y}}return y=d.sub(l),{x:(o=n.node.position.add(cc.v3(y.x,y.y))).x,y:o.y}}},t.prototype.getEditPointWorldPos=function(e,t){var n=this.getDisplayObjectById(e);if(!n)return cc.v2(0,0);var o=n.getPointList(),i=o.findIndex(function(e){return e.pointData.id===t});if(-1!==i){var r=o[i],a=n.node.convertToWorldSpaceAR(cc.v2(r.node.x,r.node.y)),s=a.y,c=cc.winSize,l=cc.v2(c.width/2+a.x,-s+c.height/2);return cc.v2(p.default.setOneDecimal(l.x,1),p.default.setOneDecimal(l.y,1))}return cc.v2(0,0)},t.prototype.getNodePos=function(e,t){var n=this.getWorldPos(e),o=n.x;void 0!==t.x&&(o=t.x);var i=n.y;void 0!==t.y&&(i=t.y);var r=cc.v2(o,i),a=this.resetNodePos(e,r);return a?{x:a.x,y:a.y}:{x:0,y:0}},t.prototype.resetEditPointPos=function(e,t,n){var o=this.getDisplayObjectById(e);if(!o)return cc.v3(0,0);var i=o.getPointList(),r=i.findIndex(function(e){return e.pointData.id===t});if(-1!==r){var a=i[r],s=cc.winSize,c=o.node.convertToWorldSpaceAR(cc.v2(a.node.x,a.node.y)),p=c.y,l=cc.v2(s.width/2+c.x,-p+s.height/2),u=cc.v2(l.x-s.width/2,-l.y+s.height/2),d=o.node.convertToNodeSpaceAR(u),y=cc.v2(n.x-s.width/2,-n.y+s.height/2),f=o.node.convertToNodeSpaceAR(y).sub(d);return a.node.position.add(cc.v3(f.x,f.y))}return cc.v3(0,0)},t.prototype.getEditPointPos=function(e,t,n){var o=this.getEditPointWorldPos(e,t),i=o.x;void 0!==n.x&&(i=n.x);var r=o.y;void 0!==n.y&&(r=n.y);var a=cc.v2(i,r),s=this.resetEditPointPos(e,t,a);return{x:s.x,y:s.y}},t.prototype.getNodeRealPos=function(e){var t=this.getDisplayObjectById(e);return t?{x:p.default.setOneDecimal(t.node.x,1),y:p.default.setOneDecimal(t.node.y,1)}:{x:0,y:0}},t.prototype.getNodeCentEdtiorPos=function(e){var t=this.getDisplayObjectById(e);if(!t)return{x:0,y:0};var n=t.node.convertToWorldSpaceAR(cc.v2(0,0)),o=n.y,i=cc.winSize,r=cc.v2(i.width/2+n.x,-o+i.height/2);return{x:r.x,y:r.y}},t.prototype.setAllObjectSignalView=function(e,n){console.log("%c Line:551 \ud83c\udf64 data","color:#3f7cff",n),console.log("%c Line:551 \ud83c\udf4b isView","color:#3f7cff",e);for(var o=0;o<n.length;o++){var i=this.getDisplayObjectById(n[o].id);i&&n[o].newExtra&&null!=n[o].newExtra.signalId&&("-1"!==yy.single.instance(t).getDisplayGroupID(n[o].id)?i.setSignalActive(!1):(e&&i.showDisplaySignal(n[o].newExtra.signalId),i.setSignalActive(e),console.log("isCorrect--",n[o].newExtra.isCorrect),n[o].newExtra.isCorrect&&i.addSignalSelectView(e)))}},t.prototype.onDestoryInstance=function(){e.prototype.onDestoryInstance.call(this),this._dpIndexMap.clear()},t}(s.SingleBase);n.default=h,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/utils/MathUtil":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../../../qte/util/QTEUtils":void 0,"../../common/EditorEnum":"EditorEnum","../../utils/Properties":"Properties","../hintline/HintLineManager":"HintLineManager","../proxy/DataCenterBridge":"DataCenterBridge"}],DisplayObject:[function(e,t,n){"use strict";cc._RF.push(t,"f1f81h9VG5K5qc4aYgV8V3f","DisplayObject");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a},a=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=e("../../../../qte/core/utils/ValueUtils"),p=e("../../../common/EditorEnum"),l=e("../../proxy/DataCenterBridge"),u=e("../DisplayObjectManager"),d=e("./cmpt/DisplaySelect"),y=e("../../../common/ComUtils"),f=cc._decorator.ccclass,h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._childIds=[],t._childs=[],t._cName="",t._tagName="",t._bSeleted=!1,t._originalZIndex=null,t.isInitFinished=!1,t}return i(t,e),Object.defineProperty(t.prototype,"childIds",{get:function(){return c.default.clone(this._childIds)},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"childs",{get:function(){return this._childs},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"cName",{get:function(){return this._cName},set:function(e){this._cName=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"cid",{get:function(){return this._cid},set:function(e){this._cid=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"type",{get:function(){return this._type},set:function(e){this._type=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"editable",{get:function(){return!!this.node.active&&!(this.node.opacity<=0)&&this._editable},set:function(e){this._editable=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"dragable",{get:function(){return this._dragable},set:function(e){this._dragable=e,this.refreshRectPointLine()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"deletable",{get:function(){return this._deletable},set:function(e){this._deletable=e,void 0===e&&(this._deletable=!0),this.changeObjectDeletable()},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"tagName",{get:function(){return this._tagName},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"selected",{get:function(){return this._bSeleted},enumerable:!1,configurable:!0}),t.prototype.init=function(){},t.prototype.initFinish=function(){},t.prototype.initFinished=function(){this.isInitFinished=!0},t.prototype.initAllComponentsFinish=function(){},Object.defineProperty(t.prototype,"rect",{get:function(){return new cc.Rect(this.node.x,this.node.y,this.node.width,this.node.height)},enumerable:!1,configurable:!0}),t.prototype.getSelectRect=function(){return new cc.Rect(-Math.abs(this.rect.width)/2,-Math.abs(this.rect.height)/2,Math.abs(this.rect.width),Math.abs(this.rect.height))},t.prototype.onLoad=function(){},t.prototype.addChildObject=function(e,t){this._childIds.indexOf(e)>=0?yy.warn("id = "+e+"\u7684\u5b50\u7ec4\u4ef6\u4ee5\u53ca\u6dfb\u52a0\u8fc7\u4e86."):(this._childIds.push(e),this.childs.push(t))},t.prototype.removeChildObject=function(e){var t=this._childIds.indexOf(e),n=-1;if(t<0)yy.warn("id = "+e+"\u7684\u5b50\u7ec4\u4ef6\u4e0d\u5b58\u5728.");else{for(var o=0;o<this._childIds.length-1;o++)this._childs[o].cid===e&&(n=o);this._childIds.splice(t,1),this._childs.splice(n,1)}},t.prototype.showDisplayTag=function(e,t){var n="",o=this.node.getChildByName("tag_Node");if(o){var i=o.getComponentInChildren(cc.Label);if(n=i.string,""!==e)if(console.log("%c Line:120 \ud83c\udf62 extra","color:#42b983",t),t&&""!=t.tagPrefix){var r=this.cid;console.log("%c Line:130 \ud83e\udd5d this.cid","color:#4fff4B",this.cid),"tagIndex"==t.tagPrefix&&(r=t.tagIndex),i.string=e+" ID: "+r}else console.log(e+"id:"+this.cid),-1!==e.indexOf(this.cid)?i.string=""+e:i.string=e+"ID:"+this.cid;else i.string="";i._forceUpdateRenderData(),"-1"!==yy.single.instance(u.default).getDisplayGroupID(this.cid)||""===e?o.active=!1:o.active=!0}return n},t.prototype.addSignalSelectView=function(e){return a(this,void 0,void 0,function(){var t,n,o,i;return s(this,function(r){switch(r.label){case 0:return console.log("%c Line:147 \ud83c\udf45 isView","color:#93c0a4",e),(t=this.node.getChildByName("signal_Frame"))?(t.active=!!e,[2]):(n=new cc.Node,this.node.addChild(n,-1),n.name="signal_Frame",(o=n.addComponent(cc.Sprite)).sizeMode=cc.Sprite.SizeMode.CUSTOM,[4,new Promise(function(e,t){cc.loader.loadRes("img/answer_border_new",cc.Texture2D,function(n,i){if(n)return yy.warn(n),void t(n);y.ComUtil.addPremultiplyAlpha(i,o),e()})})]);case 1:return r.sent(),(i=o.spriteFrame).insetTop=i.insetBottom=i.insetLeft=i.insetRight=35,o.type=cc.Sprite.Type.SLICED,n.width=this.rect.width+30,n.height=this.rect.height+30,n.angle=this.getNodeAngle(),n.group="default",[2]}})})},t.prototype.showDisplaySignal=function(e){console.log("%c Line:150 \ud83e\udd52 showDisplaySignal str","color:#465975",e);var t="",n=this.node.getChildByName("signs_Node");if(n){var o=n.getComponentInChildren(cc.Label);t=o.string,""!==e&&(o.string="  "+e+"  "),o._forceUpdateRenderData(),n.active=!1}return t},t.prototype.setSignalActive=function(e){var t=this.node.getChildByName("signs_Node");t&&(t.active=e)},t.prototype.setTagActive=function(e){var t=this.node.getChildByName("tag_Node");t&&(t.active=e)},t.prototype.showAllNodes=function(e){console.log("%c Line:308 \ud83c\udf82 click","color:#e41a6a",e);for(var t=yy.single.instance(u.default).dpMap,n=0,o=Array.from(t.values());n<o.length;n++){var i=o[n];console.log("\u5c42\u7ea7 node ,"+i.node.name+",id= "+i.cid+", index:"+i.node.getSiblingIndex()+" zIndex:"+i.node.zIndex)}},t.prototype.moveToTop=function(){this.node.parent.insertChild(this.node,this.node.parent.children.length+1)},t.prototype.restoreZIndex=function(){yy.single.instance(u.default).sortDisplayObjectByIndex()},t.prototype.setSelected=function(e){e?(this.showEdge(),this.moveToTop()):(this.clearEdge(),this.restoreZIndex());var t=this.node.getComponent(d.default);t||(t=this.node.addComponent(d.default)),e&&t.init(),t.setShowStatus(e),this._bSeleted=e},t.prototype.getSelected=function(){return this._bSeleted},t.prototype.inSelRect=function(e){var t=this.node.getComponent(d.default);if(t){var n=this.node.convertToNodeSpaceAR(e,cc.v2(this.rect.x,this.rect.y));return t.inRectSelPoint(n)}return null},t.prototype.clearEdge=function(){var e=this.node.getChildByName("edge_node");e&&e.getComponent(cc.Graphics).clear()},t.prototype.showEdge=function(){var e=this.node.getChildByName("edge_node");e||((e=new cc.Node).addComponent(cc.Graphics),e.name="edge_node",e.group=p.EditorGroup.EDGE,this.node.addChild(e));var t=e.getComponent(cc.Graphics);t.strokeColor=cc.Color.WHITE,!1===this._dragable&&(t.strokeColor=cc.Color.RED),t.clear(),t.rect(-this.node.width/2,-this.node.height/2,this.node.width,this.node.height),t.stroke()},t.prototype.refreshRectPoint=function(){this.refreshRectPointLine()},t.prototype.refreshRectPointLine=function(){this._bSeleted?this.showEdge():this.clearEdge();var e=this.node.getComponent(d.default);e&&e.refreshPointPos()},t.prototype.hidePoint=function(){var e=this.node.getComponent(d.default);e&&e.hidePoint()},t.prototype.resumePoint=function(){var e=this.node.getComponent(d.default);e&&e.resumePoint()},t.prototype.setActive=function(e){e?this.node.setScale(1):this.node.setScale(0)},t.prototype.showPreSelectRect=function(){var e=this.node.getChildByName("preSelectNode");e||((e=new cc.Node).addComponent(cc.Graphics),e.name="preSelectNode",this.node.addChild(e)),e.active=!0;var t=e.getComponent(cc.Graphics);t.fillColor.set(cc.color(0,0,0,78)),t.clear(),t.moveTo(-this.node.width/2,-this.node.height/2),t.lineTo(-this.node.width/2,this.node.height/2),t.lineTo(this.node.width/2,this.node.height/2),t.lineTo(this.node.width/2,-this.node.height/2),t.lineTo(-this.node.width/2,-this.node.height/2),t.fill()},t.prototype.clearPreSelectRect=function(){var e=this.node.getChildByName("preSelectNode");e&&(e.active=!1)},t.prototype.getOldProperties=function(){var e=yy.single.instance(l.default).getComponentMap()[this._cid];return{id:this._cid,newProperties:{x:e.properties.x,y:e.properties.y,width:e.properties.width,height:e.properties.height,angle:e.properties.angle}}},t.prototype.getNewProperties=function(){this.fixProperties();var e={x:this.node.x,y:this.node.y,width:this.node.width,height:this.node.height,angle:this.getNodeAngle()},t=this.getSelected()?yy.single.instance(u.default).dpIndexMap.get(this._cid):this.node.getSiblingIndex();console.log("%c Line:528 \ud83e\udd54 zIndex","color:#42b983",t);var n={type:this.type,active:this.node.active,angle:yy.checkValue(this.getNodeAngle(),0),color:yy.checkValue("#"+this.node.color.toHEX(),"#ffffff"),dragable:this.dragable,zIndex:t};return this.type!==p.CmptType.SPRITE&&this.type!==p.CmptType.RICHTEXTSPRITE||!this.node._renderComponent?this.type===p.CmptType.FORMULA&&this.node._renderComponent?n.url=yy.checkValue(this.node._renderComponent.___textureUrl,""):this.type===p.CmptType.SHAPE&&this.node._renderComponent&&(n.texture=yy.checkValue(this.node._renderComponent.___textureUrl,"")):n.texture=yy.checkValue(this.node._renderComponent.___textureUrl,""),e=c.default.setOneDecimal(e),{id:this._cid,newProperties:e,extra:n}},t.prototype.getNodeAngle=function(){var e=this.node.angle;return void 0!==this.node.cAngle&&(e=this.node.cAngle),e<0&&(e+=360),Math.round(e%360)},t.prototype.fixProperties=function(){var e=this.getFixedNum(this.getNodeAngle(),1);this.node.width=this.getFixedNum(this.node.width,1),this.node.height=this.getFixedNum(this.node.height,1),this.node.angle=e,this.node.x=this.getFixedNum(this.node.x,1),this.node.y=this.getFixedNum(this.node.y,1)},t.prototype.getFixedNum=function(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)},t.prototype.onDoubleClick=function(){yy.log("\u5feb\u901f\u53cc\u51fb")},t.prototype.onTwiceClick=function(){yy.log("\u8fde\u7eed\u70b9\u51fb\u4e24\u6b21\uff0c\u4e5f\u5c31\u662f\u9009\u4e2d\u540e\u518d\u6b21\u70b9\u51fb\u903b\u8f91")},t.prototype.changeObjectDeletable=function(){var e=this.node.getChildByName("deletableTag");if(e){e.active=!this._deletable;var t=this.node.getChildByName("toggle");t&&(t.getComponent(cc.Toggle).isChecked=!this._deletable)}},r([f],t)}(cc.Component);n.default=h,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"../../../common/ComUtils":"ComUtils","../../../common/EditorEnum":"EditorEnum","../../proxy/DataCenterBridge":"DataCenterBridge","../DisplayObjectManager":"DisplayObjectManager","./cmpt/DisplaySelect":"DisplaySelect"}],DisplaySelectPoint:[function(e,t,n){"use strict";cc._RF.push(t,"3a56cAmbJ9N06mP6Rj2KdlA","DisplaySelectPoint");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../../qte/core/utils/MathUtil"),s=e("../../../../common/EditorEnum"),c=e("../../../hintline/HintLineManager"),p=e("../../../proxy/DataCenterBridge"),l=e("../../DisplayObjectManager"),u=e("./DisplaySelect"),d=cc._decorator.ccclass,y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._nPointWH=10,t}return i(t,e),Object.defineProperty(t.prototype,"data",{get:function(){return this._data},enumerable:!1,configurable:!0}),t.prototype.init=function(e){this._data=e;var t=this.node.getChildByName("edge_node");t||((t=new cc.Node).addComponent(cc.Graphics),t.name="edge_node",t.group=s.EditorGroup.EDGE,this.node.addChild(t));var n=t.getComponent(cc.Graphics);n.strokeColor=cc.Color.BLUE,n.clear(),n.circle(0,0,this._nPointWH),n.fillColor=cc.Color.WHITE,n.fill(),n.stroke()},Object.defineProperty(t.prototype,"rect",{get:function(){return new cc.Rect(this.node.x-this._nPointWH,this.node.y-this._nPointWH,2*this._nPointWH,2*this._nPointWH)},enumerable:!1,configurable:!0}),t.prototype.inRect=function(e){return!!this.rect.contains(e)},t.prototype.getGroupAbsPos=function(e,t,n){var o=e.getDisplayGroupID(t);if("-1"!==o){var i=e.getDisplayObjectById(o);i&&(n=this.data.type!==u.SEL_POINT_ENUM.ROTATE?i.node.convertToNodeSpaceAR(n):n.sub(i.node.position))}return n},t.prototype.getHintOffsetY=function(e,t,n,o){var i=t.sub(e);if(0===i.x)return e.x===n?o.y:null;var r=(n-e.x)/i.x;return e.y+i.y*r},t.prototype.getHintOffsetX=function(e,t,n,o){var i=t.sub(e);if(0===i.y)return e.y===n?o.x:null;var r=(n-e.y)/i.y;return e.x+i.x*r},t.prototype.getMag=function(e,t,n,o){var i=qte.QTEUtils.convertToStageSpaceAR(e,t),r=qte.QTEUtils.convertToStageSpaceAR(e,n),a=r.sub(i),s=0;if(a.normalize().dot(o.sub(i).normalize())>0){var c=a.mag(),p=o.sub(i).project(a),l=i.add(p),u=p.mag()/c;s=l.sub(r).mag(),u<1&&(s=-s)}return s},t.prototype.getScaleOffset=function(e,t,n,o){var i=qte.QTEUtils.convertToStageSpaceAR(e,t),r=qte.QTEUtils.convertToStageSpaceAR(e,n),a=r.sub(i),s={scale:1,offsetPos:cc.v2(0,0)},c=a.normalize().multiplyScalar(o),p=r.add(c);if(a.normalize().dot(p.sub(i).normalize())>0){var l=a.mag(),u=p.sub(i).mag();s.scale=u/l,s.offsetPos=c}return s},t.prototype.getRotateProperty=function(e,t,n,o){var i=e.getDisplayGroupID(t);if("-1"!==i){var r=e.getDisplayObjectById(i);r&&a.default.rotatePosSelf(o,r.node.angle)}var c=e.getDisplayObjectById(t),p=cc.v2(c.node.x,c.node.y),l=cc.v2(o.x,o.y),u=a.default.getAngle4Rotate(p,l);u=a.default.getAbsorAngle(u);var d=this.updateRotateProperty(e,n,t),y=d._groupParent;if(d.bFindInGroup)e.updateDisplayObject(t,{angle:u}),y.refreshRectPoint();else for(var f=0,h=e.selectedIds;f<h.length;f++){var m=h[f];if("-1"===e.getDisplayGroupID(m)){var g=e.getDisplayObjectById(m);g.type!=s.CmptType.GROUP&&("number"!=typeof g.node.angle&&(g.node.angle=0),e.updateDisplayObject(m,{angle:u}))}}},t.prototype.updateRotateProperty=function(e,t,n){for(var o=yy.single.instance(p.default).getComponentIds(),i=!1,r=null,a=0;a<o.length;a++){var c=e.getDisplayObjectById(o[a].id);if(c&&c.type===s.CmptType.GROUP)for(var l=c,u=0;u<l.groupIds.length;u++)l.groupIds[u]===n&&(i=!0,r=c)}return{bFindInGroup:i,_groupParent:r}},t.prototype.getPosFromType=function(e){var t=cc.v2(0,0),n=cc.v2(0,0);switch(this.data.type){case u.SEL_POINT_ENUM.UP:t=cc.v2(0,-e.height/2),n=cc.v2(0,e.height/2);break;case u.SEL_POINT_ENUM.DOWN:t=cc.v2(0,e.height/2),n=cc.v2(0,-e.height/2);break;case u.SEL_POINT_ENUM.LEFT:t=cc.v2(e.width/2,0),n=cc.v2(-e.width/2,0);break;case u.SEL_POINT_ENUM.RIGHT:t=cc.v2(-e.width/2,0),n=cc.v2(e.width/2,0);break;case u.SEL_POINT_ENUM.LEFT_UP:t=cc.v2(e.width/2,-e.height/2),n=cc.v2(-e.width/2,e.height/2);break;case u.SEL_POINT_ENUM.LEFT_DOWN:t=cc.v2(e.width/2,e.height/2),n=cc.v2(-e.width/2,-e.height/2);break;case u.SEL_POINT_ENUM.RIGHT_UP:t=cc.v2(-e.width/2,-e.height/2),n=cc.v2(e.width/2,e.height/2);break;case u.SEL_POINT_ENUM.RIGHT_DOWN:t=cc.v2(-e.width/2,e.height/2),n=cc.v2(e.width/2,-e.height/2)}return{centPos:t,targetPos:n}},t.prototype.updateDisplayObjectByHandle=function(e,t){for(var n=yy.single.instance(l.default),o=this.data.type,i=n.getDisplayObjectById(e),r=n.getDisplayGroupID(e),a=0,s=n.selectedIds;a<s.length;a++){var p=s[a],d=n.getDisplayObjectById(p);if(p==e&&0==d.dragable)return;if(p!==e&&"-1"==n.getDisplayGroupID(p)&&p!=r&&0==d.dragable)return;if((o==u.SEL_POINT_ENUM.UP||o==u.SEL_POINT_ENUM.DOWN)&&d.editable&&d.editable.properties&&!1===d.editable.properties.height)return;if((o==u.SEL_POINT_ENUM.LEFT||o==u.SEL_POINT_ENUM.RIGHT)&&d.editable&&d.editable.properties&&!1===d.editable.properties.width)return;if((o==u.SEL_POINT_ENUM.LEFT_UP||o==u.SEL_POINT_ENUM.LEFT_DOWN||o==u.SEL_POINT_ENUM.RIGHT_UP||o==u.SEL_POINT_ENUM.RIGHT_DOWN)&&d.editable&&d.editable.properties&&(!1===d.editable.properties.width||!1===d.editable.properties.height))return}var y=0,f=cc.v2(0,0),h=cc.v2(0,0);switch(o){case u.SEL_POINT_ENUM.UP:case u.SEL_POINT_ENUM.DOWN:case u.SEL_POINT_ENUM.LEFT:case u.SEL_POINT_ENUM.RIGHT:case u.SEL_POINT_ENUM.LEFT_UP:case u.SEL_POINT_ENUM.LEFT_DOWN:case u.SEL_POINT_ENUM.RIGHT_UP:case u.SEL_POINT_ENUM.RIGHT_DOWN:var m=this.getPosFromType(i.node);f=m.centPos,h=m.targetPos;break;case u.SEL_POINT_ENUM.ROTATE:t=this.getGroupAbsPos(n,e,t),this.getRotateProperty(n,e,i,t)}if(!f.equals(cc.v2(0,0))&&!h.equals(cc.v2(0,0))){if(o!==u.SEL_POINT_ENUM.ROTATE&&1==n.selectedIds.length){var g=yy.single.instance(c.default),_=qte.QTEUtils.convertToStageSpaceAR(i.node,f),v=qte.QTEUtils.convertToStageSpaceAR(i.node,h),b=g.updateMagLineByMove(e,v,cc.v2(t.x,t.y));if(b.isColl)if(0!=b.culPps.x){var C=this.getHintOffsetY(_,v,b.culPps.x,cc.v2(t.x,t.y));null!=C&&(t.x=b.culPps.x,t.y=C)}else if(0!=b.culPps.y){var O=this.getHintOffsetX(_,v,b.culPps.y,cc.v2(t.x,t.y));null!=O&&(t.x=O,t.y=b.culPps.y)}}y=this.getMag(i.node,f,h,cc.v2(t.x,t.y))}0!=y&&this.updateDisplayProperty(n,e,y);for(var T=0,w=n.selectedIds;T<w.length;T++){p=w[T],(d=n.getDisplayObjectById(p)).refreshRectPoint();var I=n.getDisplayGroupID(p);"-1"!==I&&(n.getDisplayObjectById(I).resetLayoutSize(),yy.single.instance(c.default).updateLineByDisplayScale(I))}},t.prototype.updateDisplayProperty=function(e,t,n){var o=JSON.parse(JSON.stringify(e.selectedIds)),i=e.getDisplayObjectById(t),r={},a=e.getDisplayGroupID(t);if("-1"!==a){r[a]=!0;for(var c=0;c<o.length;c++)r[o[c]]&&(o.splice(c,1),c--)}else if(i.type===s.CmptType.GROUP){var p=i.cid;for(c=0;c<o.length;c++){var l=e.getDisplayGroupID(o[c]);"-1"!==l&&l==p&&(o.splice(c,1),c--)}}for(var u=0,d=o;u<d.length;u++){var y=d[u],f=e.getDisplayObjectById(y),h=this.getPosFromType(f.node),m=h.centPos,g=h.targetPos,_=this.getScaleOffset(f.node,m,g,n);1==_.scale||_.offsetPos.equals(cc.v2(0,0))||this.setDisplayProperty(e,f,_.scale,_.offsetPos)}},t.prototype.getAngleDisplayNode=function(e){var t=e.angle;return void 0!==e.cAngle&&(t=e.cAngle),0==Math.round(t%360)},t.prototype.setDisplayProperty=function(e,t,n,o){var i=this.data.type,r=Math.abs(t.node.height*n),a=Math.abs(t.node.width*n),c=t.node.getPosition().add(o.div(2));if(t.type===s.CmptType.GROUP){var p=!0,l=t;if(i==u.SEL_POINT_ENUM.UP||i==u.SEL_POINT_ENUM.DOWN||i==u.SEL_POINT_ENUM.LEFT||i==u.SEL_POINT_ENUM.RIGHT)for(var d=0,y=l.groupIds;d<y.length;d++){var f=y[d],h=e.getDisplayObjectById(f);if(!this.getAngleDisplayNode(h.node)){p=!1;break}}else p=!1;for(var m=0,g=l.groupIds;m<g.length;m++){f=g[m],h=e.getDisplayObjectById(f);var _=Math.abs(h.rect.height*n),v=Math.abs(h.rect.width*n),b=h.node.getPosition().multiplyScalar(n),C={height:_,width:v,x:b.x,y:b.y};p&&(i==u.SEL_POINT_ENUM.UP||i==u.SEL_POINT_ENUM.DOWN?(C.width=Math.abs(h.rect.width),C.x=h.rect.x):(C.height=Math.abs(h.rect.height),C.y=h.rect.y)),e.updateDisplayObject(f,C)}p&&(i==u.SEL_POINT_ENUM.UP||i==u.SEL_POINT_ENUM.DOWN?a=Math.abs(t.node.width):r=Math.abs(t.node.height));var O={height:r,width:a,x:c.x,y:c.y};e.updateDisplayObject(t.cid,O)}else i==u.SEL_POINT_ENUM.UP||i==u.SEL_POINT_ENUM.DOWN?a=Math.abs(t.node.width):i!=u.SEL_POINT_ENUM.LEFT&&i!=u.SEL_POINT_ENUM.RIGHT||(r=Math.abs(t.node.height)),O={height:r,width:a,x:c.x,y:c.y},e.updateDisplayObject(t.cid,O)},r([d],t)}(cc.Component);n.default=y,cc._RF.pop()},{"../../../../../qte/core/utils/MathUtil":void 0,"../../../../common/EditorEnum":"EditorEnum","../../../hintline/HintLineManager":"HintLineManager","../../../proxy/DataCenterBridge":"DataCenterBridge","../../DisplayObjectManager":"DisplayObjectManager","./DisplaySelect":"DisplaySelect"}],DisplaySelect:[function(e,t,n){"use strict";cc._RF.push(t,"e878c7NbFhFt4G9sppXocSr","DisplaySelect");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0}),n.PointData=n.SVG_INDEX=n.SEL_POINT_ENUM=void 0;var a,s=e("../../../../common/EditorEnum"),c=e("../DisplayObject"),p=e("./DisplaySelectPoint");(function(e){e[e.UP=0]="UP",e[e.RIGHT_UP=1]="RIGHT_UP",e[e.RIGHT=2]="RIGHT",e[e.RIGHT_DOWN=3]="RIGHT_DOWN",e[e.DOWN=4]="DOWN",e[e.LEFT_DOWN=5]="LEFT_DOWN",e[e.LEFT=6]="LEFT",e[e.LEFT_UP=7]="LEFT_UP",e.ROTATE="ROTATE",e.NONE="NONE"})(a=n.SEL_POINT_ENUM||(n.SEL_POINT_ENUM={})),n.SVG_INDEX={5:0,6:1,7:2,0:3,1:4,2:5,3:6,4:7};var l=function(){function e(){}return Object.defineProperty(e.prototype,"type",{get:function(){return this._type},enumerable:!1,configurable:!0}),e}();n.PointData=l;var u=cc._decorator.ccclass,d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initSelectData=function(){this._display=this.node.getComponent(c.default),this._arrSelPoint=[]},t.prototype.init=function(){this._nodeSelect||(this._nodeSelect=new cc.Node,this._nodeSelect.name="_nodeSelect",this.node.addChild(this._nodeSelect),this._nodeSelect.zIndex=1),this.refreshSelectNode();var e=this.getPoints();this._arrSelPoint=[];for(var t=0;t<e.length;t++){var n=e[t],o=new cc.Node;o.position=n.pos,this._nodeSelect.addChild(o);var i=o.addComponent(p.default);i.init(n),this._arrSelPoint.push(i)}this.refreshPointPos()},t.prototype.refreshSelectNode=function(){this._display||this.initSelectData();var e=this._display.rect;this._nodeSelect.width=e.width,this._nodeSelect.height=e.height},t.prototype.refreshPointPos=function(){for(var e=this.getPoints(),t=0;t<this._arrSelPoint.length;t++){var n=e[t];this._arrSelPoint[t].node.position=n.pos,!1===this._display.dragable?this._arrSelPoint[t].node.active=!1:this._arrSelPoint[t].node.active=!0}},t.prototype.setShowStatus=function(e){if(!e){if(!this._nodeSelect)return;for(var t=0;t<this._arrSelPoint.length;t++)this._arrSelPoint[t].node.removeFromParent();this._nodeSelect.removeFromParent(),this._nodeSelect=null,this._arrSelPoint=[]}},t.prototype.getPoints=function(){var e=[],t=this._display.getSelectRect(),n=new l;n.pos=new cc.Vec3(t.x,t.y+t.height/2),n._type=a.LEFT,e.push(n);var o=new l;o.pos=new cc.Vec3(t.x+t.width,t.y+t.height/2),o._type=a.RIGHT,e.push(o);var i=new l;if(i.pos=new cc.Vec3(t.x+t.width/2,t.y+t.height+20),i._type=a.ROTATE,this._display.type!=s.CmptType.GROUP&&e.push(i),this.node.getComponent(c.default).type===s.CmptType.LABEL)return e;var r=new l;r.pos=new cc.Vec3(t.x,t.y),r._type=a.LEFT_DOWN,e.push(r);var p=new l;p.pos=new cc.Vec3(t.x,t.y+t.height),p._type=a.LEFT_UP,e.push(p);var u=new l;u.pos=new cc.Vec3(t.x+t.width,t.y+t.height),u._type=a.RIGHT_UP,e.push(u);var d=new l;d.pos=new cc.Vec3(t.x+t.width,t.y),d._type=a.RIGHT_DOWN,e.push(d);var y=new l;y.pos=new cc.Vec3(t.x+t.width/2,t.y+t.height),y._type=a.UP,e.push(y);var f=new l;return f.pos=new cc.Vec3(t.x+t.width/2,t.y),f._type=a.DOWN,e.push(f),e},t.prototype.inRectSelPoint=function(e){for(var t=0;t<this._arrSelPoint.length;t++){var n=this._arrSelPoint[t];if(n.inRect(e))return n}return null},t.prototype.hidePoint=function(){this.setPointEnable(!1)},t.prototype.resumePoint=function(){this.setPointEnable(!0)},t.prototype.setPointEnable=function(e){for(var t=0;t<this._arrSelPoint.length;t++)this._arrSelPoint[t].node.active=e},r([u],t)}(cc.Component);n.default=d,cc._RF.pop()},{"../../../../common/EditorEnum":"EditorEnum","../DisplayObject":"DisplayObject","./DisplaySelectPoint":"DisplaySelectPoint"}],DisplaySpecialComponent:[function(e,t,n){"use strict";cc._RF.push(t,"eb03aqzeNhFx4PxA3NztR7I","DisplaySpecialComponent");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("lodash"),s=e("../../../../qte/core/utils/ValueUtils"),c=e("../../../common/EditorEnum"),p=e("../../command/commond"),l=e("../../command/operate/CommandFactory"),u=e("./DisplayObject"),d=cc._decorator,y=d.ccclass,f=(d.property,function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._size=null,t.customDataProperties={},t.componentScale={x:0,y:0},t}return i(t,e),Object.defineProperty(t.prototype,"size",{get:function(){return this._size},set:function(e){this._size=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"subType",{get:function(){return this._subType},set:function(e){this._subType=e},enumerable:!1,configurable:!0}),t.prototype.customDataPropertiesForkey=function(e){return this.customDataProperties[e]},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this);for(var n in this.customDataProperties)void 0===t.newProperties[n]&&(t.newProperties[n]=this.customDataProperties[n]);return t.extra.subType=this.subType,t},t.prototype.initAllComponentsFinish=function(){this.assembleComponent&&this.assembleComponent&&this.assembleComponent.initAllComponentsFinish()},t.prototype.initComponent=function(e){var t=this;console.log(e,"dengchao initComponent"),"object"==typeof e.properties&&(this.customDataProperties=JSON.parse(JSON.stringify(e.properties)));var n=a.cloneDeep(e);n.isEditor=!0,n.updateComponentProperties=this.updateComponentProperties.bind(this),this.assembleComponent.initComponent(n),this.assembleComponent.onSizeChange=function(e){console.log("\u8bbe\u7f6edisplay node\u5c5e\u6027",e,t.node),t.node.width=e.width,t.node.height=e.height,t.size=t.assembleComponent.node.getContentSize()},this.size=this.assembleComponent.node.getContentSize(),e.subType&&(this.subType=e.subType),this.refreshRectPoint()},t.prototype.changeProperties=function(e,t){var n;return n=this[e],this.customDataProperties[e]="object"==typeof t?JSON.parse(JSON.stringify(t)):t,this.assembleComponent.changeProperties(e,t),n},t.prototype.updateComponentProperties=function(e){console.warn({id:this.cid,newProperties:e}),yy.single.instance(l.default).execute(p.UpdatePropertiesCommand,{id:this.cid,newProperties:e}),window._$store.commit("updateComponentProperties",{id:this.cid,newProperties:e})},t.prototype.refreshRectPoint=function(){e.prototype.refreshRectPoint.call(this);var t=this.node.getContentSize();this._subType!=c.SpecialComponentSubTypes.H5Label&&this.assembleComponent&&(this.assembleComponent.node.scaleX=s.default.setOneDecimal(t.width/this.size.width,2),this.assembleComponent.node.scaleY=s.default.setOneDecimal(t.height/this.size.height,2),this.componentScale.x=this.assembleComponent.node.scaleX,this.componentScale.y=this.assembleComponent.node.scaleY)},t.prototype.start=function(){},r([y],t)}(u.default));n.default=f,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"../../../common/EditorEnum":"EditorEnum","../../command/commond":"commond","../../command/operate/CommandFactory":"CommandFactory","./DisplayObject":"DisplayObject",lodash:2}],DisplaySpine:[function(e,t,n){"use strict";cc._RF.push(t,"7f52andHPJOaYNyFU+oWx4l","DisplaySpine");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../qte/core/utils/ValueUtils"),s=e("./DisplayObject"),c=cc._decorator.ccclass,p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._anchor=null,t._size=null,t._spineCmpt=null,t._actionList=[""],t._offet=cc.v2(0,0),t._playIndex=0,t._loop=!0,t._timeScale=1,t.spineData=null,t}return i(t,e),Object.defineProperty(t.prototype,"anchor",{get:function(){return this._anchor},set:function(e){this._anchor=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"size",{get:function(){return this._size},set:function(e){this._size=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"spineCmpt",{get:function(){return this._spineCmpt},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"actionList",{get:function(){return this._actionList},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"offset",{get:function(){return this._offet},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"loop",{get:function(){return this._loop},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"timeScale",{get:function(){return this._timeScale},enumerable:!1,configurable:!0}),t.prototype.addListener=function(e){this._spineCmpt=e,this._spineCmpt.setCompleteListener(this.completeListener.bind(this))},t.prototype.resetSpinePos=function(){var e=this._spineCmpt.node.scaleX||1,t=this._spineCmpt.node.scaleY||1,n=(this._anchor.x-.5)*this._size.width*e,o=(this._anchor.y-.5)*this._size.height*t;this._spineCmpt.node.setPosition(cc.v3(n,o)),this._offet=cc.v2(n,o)},t.prototype.refreshRectPoint=function(){e.prototype.refreshRectPoint.call(this);var t=this.node.getContentSize(),n=t.width/this._size.width,o=t.height/this._size.height,i=this._spineCmpt.node;0==this._size.height&&(o=1),0==this._size.width&&(n=1),i.scaleX=n,i.scaleY=o,this.resetSpinePos()},t.prototype.setAnimation=function(e){this._actionList=e,this.step()},t.prototype.setLoop=function(e){this._loop!==e&&(this._loop=e,this._loop&&this.step())},t.prototype.setTimeScale=function(e){this._timeScale=Math.max(e,0),this._spineCmpt.timeScale=this._timeScale},t.prototype.step=function(){if(!(this.actionList.length<=0||this._playIndex>=this.actionList.length)){var e=this.actionList[this._playIndex];""!==e?this._spineCmpt.setAnimation(0,e,!1):this.completeListener()}},t.prototype.endListener=function(){cc.log("")},t.prototype.completeListener=function(){if(this._playIndex++,!this.checkActionList())return this._playIndex=0,void(this._spineCmpt.animation="");this._playIndex>this.actionList.length-1&&(this._playIndex=0,!this.loop)||this.step()},t.prototype.checkActionList=function(){for(var e=0,t=this.actionList;e<t.length;e++)if(""!==t[e])return!0;return!1},t.prototype.getSpineAnimationTime=function(e){var t=0,n=this._spineCmpt.attachUtil;if(!n)return t;for(var o=n._skeleton.data.animations,i=0;i<o.length;i++){var r=o[i];if(r.name===e){t=yy.checkValue(r.duration,0);break}}return a.default.setOneDecimal(t,2)},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this),n=JSON.parse(JSON.stringify(this.actionList));return n.length<=0&&(n=[""]),t.extra.spineData=this.spineData,t.newProperties.animationList=n,t.newProperties.loop=this.loop,t.newProperties.timeScale=this.timeScale,t.extra.color&&delete t.extra.color,t},r([c],t)}(s.default);n.default=p,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"./DisplayObject":"DisplayObject"}],DisplaySvg:[function(e,t,n){"use strict";cc._RF.push(t,"731cfkOIqxAFL8fIQKPYD6w","DisplaySvg");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0}),n.EditorType=void 0;var a,s=e("../../../../qte/core/utils/ValueUtils"),c=e("../../../common/ComUtils"),p=e("../../proxy/DataCenterBridge"),l=e("../DisplayObjectManager"),u=e("./cmpt/DisplayVertex"),d=e("./DisplayObject"),y=cc._decorator,f=y.ccclass;y.property,function(e){e[e.rect=0]="rect",e[e.vertex=1]="vertex"}(a=n.EditorType||(n.EditorType={}));var h=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isShowVertex=!1,t.svgComp=null,t._displayVertex=null,t._shapeDatas=null,t._editorType=a.rect,t}return i(t,e),Object.defineProperty(t.prototype,"displayVertex",{get:function(){return this._displayVertex},set:function(e){this._displayVertex=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"shapeDatas",{get:function(){return this._shapeDatas},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"editorType",{get:function(){return this._editorType},set:function(e){this._editorType=e},enumerable:!1,configurable:!0}),t.prototype.onInit=function(e){e&&(this._shapeDatas=e),this._displayObjectMgr=yy.single.instance(l.default);var t=new cc.Node;t.addComponent(svg.RaphaelComponent),t.name="display_svg",this.node.addChild(t),console.warn(e,"svgOnInit"),this.svgComp=t.getComponent(svg.RaphaelComponent),e.fillColor&&(this.svgComp.fillColor=(new cc.Color).fromHEX(e.fillColor)),e.strokeColor&&(this.svgComp.strokeColor=(new cc.Color).fromHEX(e.strokeColor)),this.node.on(cc.Node.EventType.COLOR_CHANGED,function(){var e=t.getChildByName("svg_group");e&&c.ComUtil.setSvgShapeOpacity(e,t.parent.opacity/255)}),this.svgComp.lineWidth=e.lineWidth||1;var n=new cc.TextAsset;n.text=e.svgText,this.setPath(n,e)},t.prototype.vertexBtnView=function(){this._changeVertexBtn.active=!0},t.prototype.createVertexBtn=function(){if(this._changeVertexBtn)this._changeVertexBtn.active=!0;else{var e=new cc.Node;this._changeVertexBtn=e,e.active=!1,this.node.addChild(e),e.position=cc.v3(this.node.width/2+40,this.node.height/2+40),e.setContentSize(cc.size(100,100));var t=e.addComponent(cc.Sprite);cc.resources.load("img/bg",cc.SpriteFrame,function(e,n){e||(t.spriteFrame=n)})}},t.prototype.clickVertexBtn=function(e){var t=this._changeVertexBtn.getBoundingBoxToWorld().contains(e);if(t){var n=!0;this.editorType===a.vertex&&(n=!1),this.showVertexPoint(n)}return t},t.prototype.setPath=function(e,t){var n=this;this.scheduleOnce(function(){n.svgComp.svgText=e,n.svgComp.onInit(),n.changeFillColor(t.fillColor),n.changeStrokeColor(t.strokeColor),n.changeStrokeWidth(t.lineWidth),!n.cid&&(n.node.active=t.active),n.cid&&yy.single.instance(l.default).updateDisplayObject(n.cid,{active:t.active}),n.svgComp.getVertexPoss(),n.scheduleOnce(function(){var e=n.getShapeRect().width,o=n.getShapeRect().height,i=t.width/e,r=t.height/o,a=Math.min(i,r);t.origin?(n.svgComp.setSvgScaleX(a),n.svgComp.setSvgScaleY(a)):(n.svgComp.setSvgScaleX(i),n.svgComp.setSvgScaleY(r)),n.cid&&(console.warn(n.cid,"this.cid"),t.origin&&yy.single.instance(l.default).updateDisplayObject(n.cid,{width:n.getShapeRect().width*a,height:n.getShapeRect().height*a}),window._$store.commit("updateComponentProperties",{id:n.cid,newProperties:{origin:!1}})),n.shapeDatas.origin=!1,n.refreshRectPoint()})},0)},t.prototype.setSvgNodeSize=function(e){this.svgComp.node.setContentSize(e),this._originSize=e},t.prototype.showVertexPoint=function(t){var n=this.node.getComponent(u.default);n||(n=this.node.addComponent(u.default)).init(this.getVertexPoss()),this._displayVertex=n,!0===t?(this.editorType=a.vertex,e.prototype.setSelected.call(this,!1),this.setUpdateAnchor()):(this.editorType=a.rect,this.setUpdateAnchor(!1)),n.setShowStatus(t)},t.prototype.editorToRect=function(){this._displayVertex&&this._displayVertex.setShowStatus(!1),this.editorType=a.rect},t.prototype.changeShapeSvg=function(e,t){var n=this;this.setUpdateAnchor(!0);var o=this.getShapeRect(),i=cc.v2(o.x+o.width/2,o.y+o.height/2);this.svgComp.changeShape(e,cc.v2(t.x,t.y)),this.scheduleOnce(function(){n._displayVertex.refreshPointPos()},0),this._displayVertex.changePosByMove(e,t);var r=this.getShapeRect();this._originSize=cc.size(r.width/this.svgComp.node.scaleX,r.height/this.svgComp.node.scaleY),this.node.setContentSize(cc.size(r.width,r.height)),this.svgComp.node.setContentSize(cc.size(r.width,r.height));var a=cc.v2(r.x+r.width/2,r.y+r.height/2),s=cc.v2(a.x-i.x,a.y-i.y),c=this.node.getPosition();this.node.position=cc.v3(c.x+s.x,c.y+s.y,0)},t.prototype.refreshVertexWordPos=function(){this._displayVertex.changeWordPos(this.getVertexPoss())},t.prototype.getShapeRect=function(){return this.svgComp.getShapeRect()},t.prototype.getVertexPoss=function(){return this.svgComp.getVertexPoss()},t.prototype.setUpdateAnchor=function(e){void 0===e&&(e=!1),this.svgComp.setUpdateAnchor(e)},t.prototype.changeFillColor=function(e){var t=(new cc.Color).fromHEX(e);this.svgComp.fillColor=t,this._shapeDatas.fillColor=e,this.svgComp.changeFillColor(t)},t.prototype.changeStrokeColor=function(e){var t=(new cc.Color).fromHEX(e);this._shapeDatas.strokeColor=e,this.svgComp.changeStrokeColor(t),this.svgComp.strokeColor=t},t.prototype.changeStrokeWidth=function(e){this.svgComp.changeStrokeWidth(e),this.svgComp.lineWidth=e,this._shapeDatas.lineWidth=e},t.prototype.setNodeWidth=function(e){if(this.getShapeRect().width){var t=this.getShapeRect().width;this._shapeDatas.width=e,this.svgComp.setSvgScaleX(e/t)}},t.prototype.setNodeHeight=function(e){if(this.getShapeRect().height){var t=this.getShapeRect().height;this._shapeDatas.height=e,this.svgComp.setSvgScaleY(e/t)}},t.prototype.setNodeScaleX=function(){},t.prototype.setNodeScaleY=function(){},t.prototype.checkVertexType=function(){return this.editorType===a.vertex},t.prototype.getVertexIndex=function(e){var t=this.node.getComponent(u.default);if(t){var n=this.node.convertToNodeSpaceAR(e);return t.inRectSelPoint(n,!0)}return null},t.prototype.refreshRectPoint=function(){e.prototype.refreshRectPoint.call(this)},t.prototype.clearEdge=function(){e.prototype.clearEdge.call(this)},t.prototype.getOldProperties=function(){var e=yy.single.instance(p.default).getComponentMap()[this.cid];return{id:this.cid,newProperties:{x:e.properties.x,y:e.properties.y,width:e.properties.width,height:e.properties.height,angle:e.properties.angle,svgText:e.properties.svgText,strokeColor:e.properties.strokeColor,fillColor:e.properties.fillColor,lineWidth:e.properties.lineWidth}}},t.prototype.getNewProperties=function(){var t=e.prototype.getNewProperties.call(this),n={strokeColor:"#"+this.svgComp.strokeColor.toHEX("#rrggbb"),fillColor:"#"+this.svgComp.fillColor.toHEX("#rrggbb"),lineWidth:this.svgComp.lineWidth,svgText:this._shapeDatas.svgText};for(var o in n)void 0===t.newProperties[o]&&(t.newProperties[o]=n[o]);return console.log("superData== svg==>",t),n=s.default.setOneDecimal(n),t},t.prototype.exportPath=function(){for(var e=this.svgComp._path.vertexPoints,t="<path ",n=0;n<e.length;n++)t+=0===n?'d="M'+e[0][0]+" "+e[0][1]:"L"+e[n][0]+" "+e[1][1];console.log("%c \ud83c\udf71 _path: ","font-size:20px;background-color: #4b4b4b;color:#fff;",t);var o='stroke="'+this.svgComp.strokeColor.toHEX(),i='fill="'+this.svgComp.fillColor.toHEX();return this.svgComp.lineWidth,t+o+i+"/>"},r([f],t)}(d.default);n.default=h,cc._RF.pop()},{"../../../../qte/core/utils/ValueUtils":void 0,"../../../common/ComUtils":"ComUtils","../../proxy/DataCenterBridge":"DataCenterBridge","../DisplayObjectManager":"DisplayObjectManager","./DisplayObject":"DisplayObject","./cmpt/DisplayVertex":"DisplayVertex"}],DisplayVertex:[function(e,t,n){"use strict";cc._RF.push(t,"070967fKeVB85hKTmCjqAHx","DisplayVertex");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../common/EditorEnum"),s=e("../DisplayObject"),c=e("./DisplaySelect"),p=e("./DisplaySelectPoint"),l=cc._decorator.ccclass,u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._vertexPointComp=[],t._nodeVertex=null,t._vertexData=[],t.chooseIndex=null,t}return i(t,e),t.prototype.onLoad=function(){this._display=this.node.getComponent(s.default),this._vertexPointComp=[],this.chooseIndex=null},t.prototype.init=function(e){this._nodeVertex||(this._nodeVertex=new cc.Node,this._nodeVertex.name="_nodeVertex",this.node.group=a.EditorGroup.EDGE,this.node.addChild(this._nodeVertex)),this.changeWordPos(e);for(var t=0;t<this._vertexData.length;t++){var n=this._vertexData[t],o=new cc.Node;o.setPosition(n),o.name="DisplaySelectPoint",this._nodeVertex.addChild(o);var i=o.addComponent(p.default),r=new c.PointData;r._value=t,r.pos=cc.v3(n.x,n.y,0),i.init(r),this._vertexPointComp.push(i)}},t.prototype.changeWordPos=function(e){this._vertexData=[];for(var t=0;t<e.length;t++){var n=this._nodeVertex.convertToNodeSpaceAR(yy.cloneValues(e[t]));this._vertexData.push(n)}},t.prototype.changePosByMove=function(e,t){var n=this._nodeVertex.convertToNodeSpaceAR(t);this._vertexPointComp[e].node.position=n,this._vertexData[e]=n},t.prototype.refreshPointPos=function(){this._display.refreshVertexWordPos();for(var e=0;e<this._vertexPointComp.length;e++)this._vertexPointComp[e].node.position=cc.v3(this._vertexData[e].x,this._vertexData[e].y,0)},t.prototype.setShowStatus=function(e){if(!e){if(!this._nodeVertex)return;for(var t=0;t<this._vertexPointComp.length;t++)this._vertexPointComp[t].node.removeFromParent();this._nodeVertex.removeFromParent(),this._nodeVertex=null,this._vertexPointComp=[],this.chooseIndex=null,this.destroy()}},t.prototype.inRectSelPoint=function(e,t){void 0===t&&(t=!1);for(var n=0;n<this._vertexData.length;n++){var o=this._vertexPointComp[n];if(t&&null!==this.chooseIndex)return this.chooseIndex;if(o.inRect(e)&&t)return this.chooseIndex=o._data._value,o._data._value}return c.SEL_POINT_ENUM.NONE},t.prototype.hidePoint=function(){this.setPointEnable(!1)},t.prototype.resumePoint=function(){this.setPointEnable(!0)},t.prototype.setPointEnable=function(e){for(var t=0;t<this._vertexPointComp.length;t++)this._vertexPointComp[t].node.active=e},r([l],t)}(cc.Component);n.default=u,cc._RF.pop()},{"../../../../common/EditorEnum":"EditorEnum","../DisplayObject":"DisplayObject","./DisplaySelect":"DisplaySelect","./DisplaySelectPoint":"DisplaySelectPoint"}],EditBoxTouchHandler:[function(e,t,n){"use strict";cc._RF.push(t,"0cb2cpL5W9NNbHp6FFm7bX9","EditBoxTouchHandler");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../qte/core/base/SingleBase"),a=e("../common/EditorEnum"),s=e("../core/display/base/DisplayEditBox"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.checkSelectedType=function(e,t){if("-1"===e)return!1;var n=t.getDisplayObjectById(e);if(!n)return!1;if(n.type!==a.CmptType.LABEL)return!1;var o=n.getComponent(s.default);return(!o.editable||!1!==(o.editable.properties?o.editable.properties.focus:o.editable.focus))&&(o||!1)},t.prototype.checkSelectedTypeH5Label=function(e,t){if("-1"===e)return!1;var n=t.getDisplayObjectById(e);if(!n)return!1;if(n.type!==a.CmptType.SPECIALCOMPONENT&&"h5Label"!=n.subType)return!1;if(n.assembleComponent&&n.assembleComponent.node){var o=n.assembleComponent.node.getComponent("h5LabelComponent");if(o)return o}return!1},t.prototype.mouseBegin=function(e,t,n){var o=this.checkSelectedType(t,n);o&&o.mouseBeginLayout(e)},t.prototype.mouseMove=function(e,t,n){var o=this.checkSelectedType(t,n);return!(!o||!o.getNodeEditStatue()||(o.mouseMoveLayout(e),0))},t.prototype.mouseUp=function(e,t){var n=this.checkSelectedType(e,t);n&&n.mouseUpLayout()},t.prototype.mouseWheel=function(e,t,n){var o=this.checkSelectedType(t,n);o&&o.mouseWheel(e);var i=this.checkSelectedTypeH5Label(t,n);i&&i.mouseWheel(e)},t.prototype.stopEdit=function(e,t){var n=this.checkSelectedType(e,t);n&&!n.getNodeEditStatue()&&n.setpikerviewStatue(1)},t.prototype.isEditState=function(e,t){var n=this.checkSelectedType(e,t);return n&&n.getNodeEditStatue()},t.prototype.keyDown=function(e,t,n,o){var i=this.checkSelectedType(e,t);return!(!i||!i.getNodeEditStatue()||(n>=37&&n<=40?i.clickDirectionKey(n,o):13===n||8==n&&i.clickDeleteKey(),0))},t.prototype.ctrlKeyDown=function(e,t,n){var o=this.checkSelectedType(e,t);if(o&&o.getNodeEditStatue()){switch(n){case 65:o.clickCtrlA();break;case 67:o.clickCtrlC();break;case 88:o.clickCtrlX()}return!0}return!1},t}(r.SingleBase);n.default=c,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../common/EditorEnum":"EditorEnum","../core/display/base/DisplayEditBox":"DisplayEditBox"}],EditorEnum:[function(e,t,n){"use strict";cc._RF.push(t,"05b7d3RNQJLdrUSfOsDeZiK","EditorEnum"),Object.defineProperty(n,"__esModule",{value:!0}),n.FlipType=n.EditorGroup=n.CmptLayer=n.OptionComponentSubTypes=n.SpecialComponentSubTypes=n.CmptType=n.EditMode=n.AnimPreviewType=n.QuestionType=n.UpdateLevelType=void 0,function(e){e[e.TOP=0]="TOP",e[e.BOTTOM=1]="BOTTOM",e[e.FORWARD=3]="FORWARD",e[e.BACKWARD=4]="BACKWARD"}(n.UpdateLevelType||(n.UpdateLevelType={})),function(e){e[e.GROUP=1]="GROUP",e[e.NORIGHTWRONG=2]="NORIGHTWRONG",e[e.HASRIGHTWRONG=3]="HASRIGHTWRONG",e[e.CHOOSE=4]="CHOOSE",e[e.BLANK=5]="BLANK",e[e.ORAL=44]="ORAL",e[e.GodTips=14]="GodTips"}(n.QuestionType||(n.QuestionType={})),function(e){e[e.ALL=1]="ALL",e[e.FRAGMENT=2]="FRAGMENT"}(n.AnimPreviewType||(n.AnimPreviewType={})),function(e){e[e.NORMAL=0]="NORMAL",e[e.ANIM=1]="ANIM"}(n.EditMode||(n.EditMode={})),function(e){e.COCOSANI="cocosAni",e.SPRITE="sprite",e.LABEL="label",e.SPINE="spine",e.GROUP="group",e.FORMULA="formula",e.SVGSHAPE="svgShape",e.CUTSHAPE="cutShape",e.SPECIALCOMPONENT="specialComponent",e.OPTIONCOMPONENT="optionComponent",e.SHAPE="shape",e.SVG="SVG",e.RICHTEXTSPRITE="richTextSprite"}(n.CmptType||(n.CmptType={})),function(e){e.CmptRecord="voice",e.KeyBord="keyboard",e.KeyBordEnglish="keyboardEnglish",e.MatchBoard="matchboard",e.Counter="counter",e.Write="write",e.Clock="clock",e.Tangram="tangram",e.Speaker="speaker",e.Brush="brush",e.H5Label="h5Label"}(n.SpecialComponentSubTypes||(n.SpecialComponentSubTypes={})),(n.OptionComponentSubTypes||(n.OptionComponentSubTypes={})).huarongdao="huatongdao",function(e){e.OBJECT_LAYER="object_layer",e.ANIM_LAYER="anim_layer",e.TOUCH_LAYER="touch_layer",e.TOP_LAYER="top_layer"}(n.CmptLayer||(n.CmptLayer={})),function(e){e.DEFAULT="default",e.EDGE="edge"}(n.EditorGroup||(n.EditorGroup={})),function(e){e[e.FNORMAL=0]="FNORMAL",e[e.FLIPX=1]="FLIPX",e[e.FLIPY=2]="FLIPY",e[e.FLIPALL=3]="FLIPALL"}(n.FlipType||(n.FlipType={})),cc._RF.pop()},{}],ExtraModule:[function(e,t,n){"use strict";cc._RF.push(t,"cd767t/VYNLzZSMndbupF/g","ExtraModule");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.init=function(e){console.log("Extramodunle init",e),this.bootData=e},t.prototype.getBundleName=function(){},t.prototype.initInstance=function(){},t.prototype.onDestoryInstance=function(){},t}(e("../../../qte/core/base/SingleBase").SingleBase);n.default=r,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0}],HintLineCmpt:[function(e,t,n){"use strict";cc._RF.push(t,"d0ba0izD/5O/anLK1jJT5ka","HintLineCmpt");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0}),n.LineNode=void 0;var a=e("./HintLineManager"),s=e("../display/DisplayObjectManager"),c=e("./HintLineDisplay"),p=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.findLineData=null,t.lineData=null,t.posType=null,t}return i(t,e),t}(cc.Node);n.LineNode=p;var l=cc._decorator.ccclass,u=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._instanceManager=null,t._arrNode=[],t}return i(t,e),t.prototype.onLoad=function(){var e=this;cc.director.on("REMOVE_ALL_LINE",this.removeAllLine,this),this._instanceManager=yy.single.instance(a.default),this._displayManager=yy.single.instance(s.default),this._instanceManager.callAdd=function(t,n,o){e.drawLine(t,n,o)},this._instanceManager._callDebugDraw=function(t,n){e.node.addChild(t),t.x=n.x,t.y=n.y,e.node.runAction(cc.sequence(cc.delayTime(1),cc.callFunc(function(){t.removeFromParent()})))}},t.prototype.drawLine=function(e,t,n){for(var o=0;o<this._arrNode.length;o++){var i=this._arrNode[o];if(i.findLineData.id===t.id&&i.lineData.id===e.id&&i.posType===n)return}var r=new p;r.findLineData=t,r.lineData=e,r.posType=n,r.addComponent(cc.Graphics),this.node.addChild(r),this._arrNode.push(r);var a=r.getComponent(cc.Graphics);if(a.strokeColor=cc.Color.RED,-66===e.id&&(e.typeDisplay===c.LINT_DISPLAY_TYPE.VERTICAL?a.lineWidth=8:a.lineWidth=4),a.clear(),e.typeDisplay===c.LINT_DISPLAY_TYPE.HORIZONTAL){var s=e.pos;a.moveTo(-cc.winSize.width/2,s),a.lineTo(cc.winSize.width/2,s)}else{var l=e.pos;a.moveTo(l,-cc.winSize.height/2),a.lineTo(l,cc.winSize.height/2)}a.stroke();var u=this._displayManager.getDisplayObjectById(e.id.toString());u&&u.showPreSelectRect()},t.prototype.removeAllLine=function(){for(var e=[],t=this._arrNode.length-1;t>=0;t--)e.push(t);for(t=0;t<e.length;t++){var n=this._arrNode.splice(e[t],1);this.node.removeChild(n[0]);var o=this._displayManager.getDisplayObjectById(n[0].lineData.id.toString());o&&o.clearPreSelectRect()}},t.prototype.onDestroy=function(){cc.director.off("REMOVE_ALL_LINE",this.removeAllLine,this)},t.prototype.update=function(){for(var e=[],t=this._arrNode.length-1;t>=0;t--){var n=this._arrNode[t];this._instanceManager.checkLineCollider(n.lineData,n.findLineData)||e.push(t)}for(t=0;t<e.length;t++){var o=this._arrNode.splice(e[t],1);this.node.removeChild(o[0]);var i=this._displayManager.getDisplayObjectById(o[0].lineData.id.toString());i&&i.clearPreSelectRect()}},r([l],t)}(cc.Component);n.default=u,cc._RF.pop()},{"../display/DisplayObjectManager":"DisplayObjectManager","./HintLineDisplay":"HintLineDisplay","./HintLineManager":"HintLineManager"}],HintLineDisplay:[function(e,t,n){"use strict";var o,i,r;cc._RF.push(t,"9d00bMwvQZLHLfMbjKy0N7k","HintLineDisplay"),Object.defineProperty(n,"__esModule",{value:!0}),n.LINEDATA=n.LINT_DISPLAY_TYPE=n.LINT_POS_TYPE=n.HINT_LINE_TYPE=void 0,function(e){e[e.EDITOR=0]="EDITOR",e[e.DISPLAY=1]="DISPLAY"}(o=n.HINT_LINE_TYPE||(n.HINT_LINE_TYPE={})),function(e){e[e.HORIZONTAL=0]="HORIZONTAL",e[e.VERTICAL=1]="VERTICAL",e[e.LEFT=2]="LEFT",e[e.RIGHT=3]="RIGHT",e[e.TOP=4]="TOP",e[e.BOTTOM=5]="BOTTOM"}(i=n.LINT_POS_TYPE||(n.LINT_POS_TYPE={})),function(e){e[e.HORIZONTAL=0]="HORIZONTAL",e[e.VERTICAL=1]="VERTICAL"}(r=n.LINT_DISPLAY_TYPE||(n.LINT_DISPLAY_TYPE={}));var a=function(){function e(){this._pos=-1,this.mousePos=null,this.findId=null,this._type=o.EDITOR,this._id=-1,this._posType=i.HORIZONTAL,this._typeDisplay=r.HORIZONTAL}return Object.defineProperty(e.prototype,"pos",{get:function(){return this._pos},set:function(e){this._pos=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"type",{get:function(){return this._type},set:function(e){this._type=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"id",{get:function(){return this._id},set:function(e){this._id=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"posType",{get:function(){return this._posType},set:function(e){this._posType=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"typeDisplay",{get:function(){return this._typeDisplay},set:function(e){this._typeDisplay=e},enumerable:!1,configurable:!0}),e.prototype.isEqaul=function(e){return this._id===e.id&&this._pos===e.pos&&this._posType===e.posType&&this._type===e.type&&this._typeDisplay===e.typeDisplay},e}();n.LINEDATA=a,cc._RF.pop()},{}],HintLineManager:[function(e,t,n){"use strict";cc._RF.push(t,"f5a48Ddjr5JF5y+85YNglw9","HintLineManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.LINE_COLLIDE_TYPE=void 0;var r,a=e("../../../qte/core/base/SingleBase"),s=e("./HintLineDisplay"),c=e("../display/DisplayObjectManager"),p=e("../../../qte/core/utils/MathUtil");(function(e){e[e.NODE=0]="NODE",e[e.HOR=1]="HOR",e[e.VER=2]="VER",e[e.HORandVER=3]="HORandVER"})(r=n.LINE_COLLIDE_TYPE||(n.LINE_COLLIDE_TYPE={}));var l=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._arrHintLine=[],t._callAdd=null,t._callDebugDraw=null,t._callDel=null,t.nColliderChar=5,t.nAdsorbHor=0,t.nOldPosY=0,t.nOldHorLine=null,t.nOldFindHorLine=null,t.nOldHorType=s.LINT_POS_TYPE.HORIZONTAL,t.nAdsorbVer=0,t.nOldPosX=0,t.nOldVerLine=null,t.nOldFindVerLine=null,t.nOldVerType=s.LINT_POS_TYPE.VERTICAL,t}return i(t,e),Object.defineProperty(t.prototype,"callAdd",{get:function(){return this._callAdd},set:function(e){this._callAdd=e},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"callDel",{get:function(){return this._callDel},set:function(e){this._callDel=e},enumerable:!1,configurable:!0}),t.prototype.initInstance=function(){this._displayManager=yy.single.instance(c.default),this._arrHintLine=[];var e=new s.LINEDATA;e.id=-66,e.type=s.HINT_LINE_TYPE.DISPLAY,e.pos=360,e.typeDisplay=s.LINT_DISPLAY_TYPE.HORIZONTAL,e.posType=s.LINT_POS_TYPE.TOP,this._arrHintLine.push(e);var t=new s.LINEDATA;t.id=-66,t.type=s.HINT_LINE_TYPE.DISPLAY,t.pos=-360,t.typeDisplay=s.LINT_DISPLAY_TYPE.HORIZONTAL,t.posType=s.LINT_POS_TYPE.BOTTOM,this._arrHintLine.push(t);var n=new s.LINEDATA;n.id=-66,n.type=s.HINT_LINE_TYPE.DISPLAY,n.pos=-640,n.typeDisplay=s.LINT_DISPLAY_TYPE.VERTICAL,n.posType=s.LINT_POS_TYPE.LEFT,this._arrHintLine.push(n);var o=new s.LINEDATA;o.id=-66,o.type=s.HINT_LINE_TYPE.DISPLAY,o.pos=640,o.typeDisplay=s.LINT_DISPLAY_TYPE.VERTICAL,o.posType=s.LINT_POS_TYPE.RIGHT,this._arrHintLine.push(o)},t.prototype.removeHintLineByDisplay=function(e){this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL),this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP),this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM),this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL),this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT),this.removeHintLine(Number(e),!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT)},t.prototype.addHintLinebyDisplay=function(e){var t=this._displayManager.getDisplayObjectById(e);if(t){var n=Number(e),o=p.default.getRotaRectNew(t.node).sort(function(e,t){return e.y>t.y?-1:e.y<t.y?1:0}),i=o[0].y,r=o[3].y;this.addHintLine(n,!1,t.node.y,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL),this.addHintLine(n,!1,i,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP),this.addHintLine(n,!1,r,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM);var a=p.default.getRotaRectNew(t.node).sort(function(e,t){return e.x>t.x?1:e.x<t.x?-1:0}),c=a[0].x,l=a[3].x;this.addHintLine(n,!1,t.node.x,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL),this.addHintLine(n,!1,c,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT),this.addHintLine(n,!1,l,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT)}},t.prototype.addHintLine=function(e,t,n,o,i){void 0===i&&(i=s.LINT_POS_TYPE.HORIZONTAL);var r=new s.LINEDATA;r.id=e;var a=s.HINT_LINE_TYPE.DISPLAY;t&&(a=s.HINT_LINE_TYPE.EDITOR),r.type=a,r.pos=n,r.typeDisplay=o,t||(r.posType=i),this._arrHintLine.push(r)},t.prototype.removeHintLine=function(e,t,n,o){void 0===o&&(o=s.LINT_POS_TYPE.HORIZONTAL);var i=this.findLineData(e,t,n,o);if(-1!==i){if(this._arrHintLine.splice(i,1),!t)return;for(var r=0;r<this._arrHintLine.length;r++){var a=this._arrHintLine[r];a.type===s.HINT_LINE_TYPE.EDITOR&&a.typeDisplay===n&&a.id>e&&(a.id-=1)}}},t.prototype.findLineData=function(e,t,n,o){void 0===o&&(o=s.LINT_POS_TYPE.HORIZONTAL);var i=-1,r=s.HINT_LINE_TYPE.DISPLAY;t&&(r=s.HINT_LINE_TYPE.EDITOR);for(var a=0;a<this._arrHintLine.length;a++){var c=this._arrHintLine[a];if(c.id===e&&c.typeDisplay===n&&c.posType===o&&c.type===r){i=a;break}}return i},t.prototype.checkLineCollider=function(e,t){var n=this.findLineData(t.id,t.type===s.HINT_LINE_TYPE.EDITOR,t.typeDisplay,t.posType);if(-1===n)return!1;var o=this.findLineData(e.id,e.type===s.HINT_LINE_TYPE.EDITOR,e.typeDisplay,e.posType);if(-1===o)return!1;var i=this._arrHintLine[n],r=this._arrHintLine[o];return i.findId==r.id+"_"+r.posType},t.prototype.updateLineByDisplayScale=function(e){var t=this._displayManager.getDisplayObjectById(e);if(t){var n=Number(e),o=p.default.getRotaRectNew(t.node).sort(function(e,t){return e.y>t.y?-1:e.y<t.y?1:0}),i=o[0].y,r=o[3].y;this.updateLinePosAndCollide(n,!1,Number(t.node.y),s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL,!1),this.updateLinePosAndCollide(n,!1,i,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP,!1),this.updateLinePosAndCollide(n,!1,r,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM,!1);var a=p.default.getRotaRectNew(t.node).sort(function(e,t){return e.x>t.x?1:e.x<t.x?-1:0}),c=a[0].x,l=a[3].x;this.updateLinePosAndCollide(n,!1,Number(t.node.x),s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL,!1),this.updateLinePosAndCollide(n,!1,c,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT,!1),this.updateLinePosAndCollide(n,!1,l,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT,!1)}},t.prototype.updateVer=function(e,t,n){var o=this._displayManager.getDisplayObjectById(e);if(!o)return!1;var i=Number(e),r=p.default.getRotaRectNew(o.node).sort(function(e,t){return e.y>t.y?-1:e.y<t.y?1:0}),a=r[0].y,c=r[3].y,l=this.updateLinePosAndCollide(i,!1,Number(o.node.y),s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL,!0),u=this.updateLinePosAndCollide(i,!1,a,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP,!0),d=this.updateLinePosAndCollide(i,!1,c,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM,!0),y=!1;if(-1!==l||-1!==u||-1!==d){var f=null,h=null,m=null,g=null;if(-1!==l){var _=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL);m=this._arrHintLine[_],h=(f=this._arrHintLine[l]).pos,g=s.LINT_POS_TYPE.HORIZONTAL}else-1!==u?(_=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP),m=this._arrHintLine[_],h=(f=this._arrHintLine[u]).pos-a+o.node.y,g=s.LINT_POS_TYPE.TOP):-1!==d&&(_=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM),m=this._arrHintLine[_],h=(f=this._arrHintLine[d]).pos-c+o.node.y,g=s.LINT_POS_TYPE.BOTTOM);0===this.nAdsorbHor?(this.nAdsorbHor=t.y-this.nOldPosY,0===this.nAdsorbHor&&(this.nAdsorbHor=n),this.adsorbLineY(e,h),this.nOldHorLine=f,this.nOldFindHorLine=m,this.nOldHorType=g,y=!0):this.nAdsorbHor>0?t.y<this.nOldPosY&&(this.nAdsorbHor=t.y-this.nOldPosY,this.adsorbLineY(e,h),this.nOldHorLine=f,this.nOldFindHorLine=m,this.nOldHorType=g,y=!0):t.y>this.nOldPosY&&(this.nAdsorbHor=t.y-this.nOldPosY,this.adsorbLineY(e,h),this.nOldHorLine=f,this.nOldFindHorLine=m,this.nOldHorType=g,y=!0)}return this.nOldPosY=t.y,y},t.prototype.updateHor=function(e,t,n){var o=this._displayManager.getDisplayObjectById(e);if(!o)return!1;var i=Number(e),r=p.default.getRotaRectNew(o.node).sort(function(e,t){return e.x>t.x?1:e.x<t.x?-1:0}),a=!1,c=r[0].x,l=r[3].x,u=this.updateLinePosAndCollide(i,!1,Number(o.node.x),s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL,!0),d=this.updateLinePosAndCollide(i,!1,c,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT,!0),y=this.updateLinePosAndCollide(i,!1,l,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT,!0);if(-1!==u||-1!==d||-1!==y){var f=null,h=null,m=null,g=null;if(-1!==u){var _=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL);m=this._arrHintLine[_],h=(f=this._arrHintLine[u]).pos,g=s.LINT_POS_TYPE.VERTICAL}else-1!==d?(_=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT),m=this._arrHintLine[_],h=(f=this._arrHintLine[d]).pos-c+o.node.x,g=s.LINT_POS_TYPE.LEFT):-1!==y&&(_=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT),m=this._arrHintLine[_],h=(f=this._arrHintLine[y]).pos-l+o.node.x,g=s.LINT_POS_TYPE.RIGHT);0===this.nAdsorbVer?(this.nAdsorbVer=t.x-this.nOldPosX,0===this.nAdsorbVer&&(this.nAdsorbVer=n),this.adsorbLineX(e,h),this.nOldVerLine=f,this.nOldFindVerLine=m,this.nOldVerType=g,a=!0):this.nAdsorbVer>0?t.x<this.nOldPosX&&(this.nAdsorbVer=t.x-this.nOldPosX,this.adsorbLineX(e,h),this.nOldVerLine=f,this.nOldFindVerLine=m,this.nOldVerType=g,a=!0):t.x>this.nOldPosX&&(this.nAdsorbVer=t.x-this.nOldPosX,this.adsorbLineX(e,h),this.nOldVerLine=f,this.nOldFindVerLine=m,this.nOldVerType=g,a=!0)}return this.nOldPosX=t.x,a},t.prototype.updateLineByDisplayMove=function(e,t,n,o){var i=this.updateVer(e,t,o),a=this.updateHor(e,t,n);return i&&!a?r.HOR:a&&!i?r.VER:r.NODE},t.prototype.updateMagLineByMove=function(e,t,n){var o=!1,i=cc.v2(0,0),r=this._displayManager.getDisplayObjectById(e);if(!r)return{isColl:!1,culPps:i};if(r){var a=Number(e),c=p.default.getRotaRectNew(r.node).sort(function(e,t){return e.y>t.y?-1:e.y<t.y?1:0}),l=c[0].y,u=c[3].y,d=Number(r.node.y),y=p.default.getRotaRectNew(r.node).sort(function(e,t){return e.x>t.x?1:e.x<t.x?-1:0}),f=y[0].x,h=y[3].x,m=Number(r.node.x),g=s.LINT_POS_TYPE.LEFT,_=Math.abs(n.x-f);_>Math.abs(n.x-h)&&(g=s.LINT_POS_TYPE.RIGHT,_=Math.abs(n.x-h)),_>Math.abs(n.x-m)&&(g=s.LINT_POS_TYPE.VERTICAL,_=Math.abs(n.x-m));var v=s.LINT_POS_TYPE.TOP,b=Math.abs(n.y-l);b>Math.abs(n.y-u)&&(v=s.LINT_POS_TYPE.BOTTOM,b=Math.abs(n.y-u)),b>Math.abs(n.y-d)&&(v=s.LINT_POS_TYPE.HORIZONTAL,b=Math.abs(n.y-d));var C=g,O=-1,T=null,w=this.preCheckLineMagCollide(!1,t.x,s.LINT_DISPLAY_TYPE.VERTICAL),I=this.preCheckLineMagCollide(!1,t.y,s.LINT_DISPLAY_TYPE.HORIZONTAL);if(-1!==w.nReturnIndex&&-1!==I.nReturnIndex?w.mag<I.mag?(O=w.nReturnIndex,T=s.LINT_DISPLAY_TYPE.HORIZONTAL,C=g):(O=I.nReturnIndex,T=s.LINT_DISPLAY_TYPE.HORIZONTAL,C=v):-1!==I.nReturnIndex?(O=I.nReturnIndex,T=s.LINT_DISPLAY_TYPE.HORIZONTAL,C=v):-1!==w.nReturnIndex&&(O=w.nReturnIndex,T=s.LINT_DISPLAY_TYPE.VERTICAL,C=g),-1!=O){var P=this.findLineData(a,!1,T,C),S=null;-1!==P&&(S=this._arrHintLine[P]);for(var A=0;A<this._arrHintLine.length;A++)if(A!==P){var D=this._arrHintLine[A];D.findId&&(D.findId=null)}if((E=this._arrHintLine[O])&&S){S&&S.findId!==E.id+"_"+E.posType&&(S.mousePos=n,S.findId=E.id+"_"+E.posType,this._callAdd(E,S,C));var x=n.sub(S.mousePos).mag();E.typeDisplay===s.LINT_DISPLAY_TYPE.HORIZONTAL?x<this.nColliderChar?(o=!0,i=cc.v2(0,E.pos)):S.findId==E.id+"_"+E.posType&&(S.findId=null):E.typeDisplay===s.LINT_DISPLAY_TYPE.VERTICAL&&(x<this.nColliderChar?(o=!0,i=cc.v2(E.pos,0)):S.findId==E.id+"_"+E.posType&&(S.findId=null))}}else for(A=0;A<this._arrHintLine.length;A++){var E;(E=this._arrHintLine[A]).findId&&(E.findId=null)}return{isColl:o,culPps:i}}},t.prototype.preCheckLineMagCollide=function(e,t,n){for(var o=-1,i=null,r=function(r){var c=a._arrHintLine[r];return-66!==c.id?"continue":e&&c.type===s.HINT_LINE_TYPE.EDITOR?"continue":n!==c.typeDisplay?"continue":a._displayManager.selectedIds.find(function(e){return e===c.id.toString()})?"continue":void(t>c.pos-a.nColliderChar&&t<c.pos+a.nColliderChar&&(null==i?(i=Math.abs(c.pos-t),o=r):Math.abs(c.pos-t)<i&&(i=Math.abs(c.pos-t),o=r)))},a=this,c=0;c<this._arrHintLine.length;c++)r(c);return{nReturnIndex:o,mag:i}},t.prototype.preCheckLinePosAndCollide=function(e,t,n,o,i){null===i&&(i=s.LINT_POS_TYPE.HORIZONTAL);for(var r=null,a=-1,c=function(e){var i=p._arrHintLine[e];return t&&i.type===s.HINT_LINE_TYPE.EDITOR?"continue":o!==i.typeDisplay?"continue":p._displayManager.selectedIds.find(function(e){return e===i.id.toString()})?"continue":void(n>i.pos-p.nColliderChar&&n<i.pos+p.nColliderChar&&(null==r?(r=Math.abs(i.pos-n),a=e):Math.abs(i.pos-n)<r&&(r=Math.abs(i.pos-n),a=e)))},p=this,l=0;l<this._arrHintLine.length;l++)c(l);return{nReturnIndex:a,mag:r}},t.prototype.updateLineByMove=function(e,t,n,o){var i=this.updateVerNextMove(e,t,o),a=this.updateHorNextMove(e,t,n);return i.isColl&&!a.isColl?{type:r.HOR,deltaX:a.deltaX,deltaY:i.deltaY}:a.isColl&&!i.isColl?{type:r.VER,deltaX:a.deltaX,deltaY:i.deltaY}:(i.isColl&&a.isColl,{type:r.NODE,deltaX:a.deltaX,deltaY:i.deltaY})},t.prototype.resetHintLineHandler=function(){this._arrHintLine.forEach(function(e){e.findId=null})},t.prototype.updateVerNextMove=function(e,t,n){var o=this._displayManager.getDisplayObjectById(e);if(!o)return{isColl:!1,deltaY:n};var i=Number(e),r=p.default.getRotaRectNew(o.node).sort(function(e,t){return e.y>t.y?-1:e.y<t.y?1:0}),a=r[0].y,c=r[3].y,l=Number(o.node.y),u=this.preCheckLinePosAndCollide(i,!1,l+n,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.HORIZONTAL),d=this.preCheckLinePosAndCollide(i,!1,a+n,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.TOP),y=this.preCheckLinePosAndCollide(i,!1,c+n,s.LINT_DISPLAY_TYPE.HORIZONTAL,s.LINT_POS_TYPE.BOTTOM),f=null,h=-1,m=null;-1!==u.nReturnIndex&&(null==f?(f=u.mag,h=u.nReturnIndex,m=s.LINT_POS_TYPE.HORIZONTAL):u.mag<f&&(f=u.mag,h=u.nReturnIndex,m=s.LINT_POS_TYPE.HORIZONTAL)),-1!==d.nReturnIndex&&(null==f?(f=d.mag,h=d.nReturnIndex,m=s.LINT_POS_TYPE.TOP):d.mag<f&&(f=d.mag,h=d.nReturnIndex,m=s.LINT_POS_TYPE.TOP)),-1!==y.nReturnIndex&&(null==f?(f=y.mag,h=y.nReturnIndex,m=s.LINT_POS_TYPE.BOTTOM):y.mag<f&&(f=y.mag,h=y.nReturnIndex,m=s.LINT_POS_TYPE.BOTTOM));var g=!1,_=0,v=n;if(-1!=h){var b=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.HORIZONTAL,m),C=null;-1!==b&&(C=this._arrHintLine[b]);for(var O=0;O<this._arrHintLine.length;O++)if(O!==b&&O!==h){var T=this._arrHintLine[O];T.findId&&T.typeDisplay===s.LINT_DISPLAY_TYPE.HORIZONTAL&&(T.findId=null)}(w=this._arrHintLine[h])&&C&&(C.findId!==w.id+"_"+w.posType&&(C.mousePos=t,C.findId=w.id+"_"+w.posType,this._callAdd(w,C,m)),Math.abs(t.y-C.mousePos.y)<this.nColliderChar?(g=!0,v=w.pos-C.pos):C.findId==w.id+"_"+w.posType&&(C.findId=null,_=t.y-C.mousePos.y))}else for(O=0;O<this._arrHintLine.length;O++){var w;(w=this._arrHintLine[O]).findId&&w.typeDisplay===s.LINT_DISPLAY_TYPE.HORIZONTAL&&(w.findId=null)}return{isColl:g,deltaY:v+_}},t.prototype.updateHorNextMove=function(e,t,n){var o=this._displayManager.getDisplayObjectById(e);if(!o)return{isColl:!1,deltaX:n};var i=Number(e),r=p.default.getRotaRectNew(o.node).sort(function(e,t){return e.x>t.x?1:e.x<t.x?-1:0}),a=r[0].x,c=r[3].x,l=Number(o.node.x),u=this.preCheckLinePosAndCollide(i,!1,l+n,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.VERTICAL),d=this.preCheckLinePosAndCollide(i,!1,a+n,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.LEFT),y=this.preCheckLinePosAndCollide(i,!1,c+n,s.LINT_DISPLAY_TYPE.VERTICAL,s.LINT_POS_TYPE.RIGHT),f=null,h=-1,m=null;-1!==u.nReturnIndex&&(null==f?(f=u.mag,h=u.nReturnIndex,m=s.LINT_POS_TYPE.VERTICAL):u.mag<f&&(f=u.mag,h=u.nReturnIndex,m=s.LINT_POS_TYPE.VERTICAL)),-1!==d.nReturnIndex&&(null==f?(f=d.mag,h=d.nReturnIndex,m=s.LINT_POS_TYPE.LEFT):d.mag<f&&(f=d.mag,h=d.nReturnIndex,m=s.LINT_POS_TYPE.LEFT)),-1!==y.nReturnIndex&&(null==f?(f=y.mag,h=y.nReturnIndex,m=s.LINT_POS_TYPE.RIGHT):y.mag<f&&(f=y.mag,h=y.nReturnIndex,m=s.LINT_POS_TYPE.RIGHT));var g=n,_=0,v=!1;if(-1!==h){var b=this.findLineData(i,!1,s.LINT_DISPLAY_TYPE.VERTICAL,m),C=null;-1!==b&&(C=this._arrHintLine[b]);for(var O=0;O<this._arrHintLine.length;O++)if(O!==b&&O!==h){var T=this._arrHintLine[O];T.findId&&T.typeDisplay===s.LINT_DISPLAY_TYPE.VERTICAL&&(T.findId=null)}(w=this._arrHintLine[h])&&C&&(C&&C.findId!==w.id+"_"+w.posType&&(C.mousePos=t,C.findId=w.id+"_"+w.posType,this._callAdd(w,C,m)),Math.abs(t.x-C.mousePos.x)<this.nColliderChar?(v=!0,g=w.pos-C.pos):C.findId==w.id+"_"+w.posType&&(C.findId=null,_=t.x-C.mousePos.x))}else for(O=0;O<this._arrHintLine.length;O++){var w;(w=this._arrHintLine[O]).findId&&w.typeDisplay===s.LINT_DISPLAY_TYPE.VERTICAL&&(w.findId=null)}return{isColl:v,deltaX:g+_}},t.prototype.adsorbLineX=function(e,t){var n=t-this._displayManager.getDisplayObjectById(e).node.x;this._displayManager.updateDisplayObject(e,{x:t});var o=this._displayManager.selectedIds;if(o.length>1)for(var i=0;i<o.length;i++){var r=o[i];if(r!==e){var a=this._displayManager.getDisplayObjectById(r).node.x+n;this._displayManager.updateDisplayObject(r,{x:a})}}},t.prototype.adsorbLineY=function(e,t){var n=t-this._displayManager.getDisplayObjectById(e).node.y;this._displayManager.updateDisplayObject(e,{y:t});var o=this._displayManager.selectedIds;if(o.length>1)for(var i=0;i<o.length;i++){var r=o[i];if(r!==e){var a=this._displayManager.getDisplayObjectById(r).node.y+n;this._displayManager.updateDisplayObject(r,{y:a})}}},t.prototype.updateLinePosAndCollide=function(e,t,n,o,i,r){null===i&&(i=s.LINT_POS_TYPE.HORIZONTAL);var a=this.findLineData(e,t,o,i);if(-1===a)return-1;var c=this._arrHintLine[a];if(c.pos=n,r){for(var p=-1,l=function(e){if(e===a)return"continue";var n=u._arrHintLine[e];return t&&n.type===s.HINT_LINE_TYPE.EDITOR?"continue":c.typeDisplay!==n.typeDisplay?"continue":u._displayManager.selectedIds.find(function(e){return e===n.id.toString()})?"continue":void(c.pos>n.pos-u.nColliderChar&&c.pos<n.pos+u.nColliderChar&&(u._callAdd&&u._callAdd(n,c,i),-1===p&&(p=e)))},u=this,d=0;d<this._arrHintLine.length;d++)l(d);return p}},t}(a.SingleBase);n.default=l,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/utils/MathUtil":void 0,"../display/DisplayObjectManager":"DisplayObjectManager","./HintLineDisplay":"HintLineDisplay"}],InitSceneCmpt:[function(e,t,n){"use strict";cc._RF.push(t,"5e575IsNoZKF5n11y/ehehS","InitSceneCmpt");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a},a=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=e("./extends/yy"),p=e("./AssetsManager"),l=e("./Config"),u=e("./core/proxy/DataCenterBridge"),d=e("./stage/CocosExport"),y=e("./stage/StageSceneCmpt"),f=e("./core/proxy/ExtraModule");c.default.log("initSceneCmpts");var h=cc._decorator.ccclass,m=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initData=function(e){c.default.single.instance(f.default).init(e)},t.prototype.start=function(){d.CocosExport.initExport(),this.initCustomCursor(),this.init()},t.prototype.init=function(){return a(this,void 0,void 0,function(){return s(this,function(e){switch(e.label){case 0:return console.log("(window as any)._$store.state.template==",window._$store.state.template),console.log("test"),console.log("%c Line:33 \ud83e\udd54 y.single.instance(ExtraModule).bootData","color:#3f7cff",c.default.single.instance(f.default).bootData),c.default.single.instance(f.default).bootData?[4,c.default.single.instance(p.default).loadBundel(Object.values(c.default.single.instance(f.default).bootData.resourceMap)[0])]:[3,2];case 1:e.sent(),c.default.single.instance(u.default).cocosLoadQuestionBundleFinished(),e.label=2;case 2:return this.runToStageScene(),[2]}})})},t.prototype.runToStageScene=function(){var e=c.default.single.instance(u.default).getStageInfo(),t=c.default.single.instance(u.default).getTemplate();qte.getTemplateByKey=function(e){return t[e]},qte.openMode=qte.QTE_OPENMODE.SHOW_NORMAL;var n=cc.size(e.safeWidth,e.safeHeight),o=new cc.Scene;o.autoReleaseAssets=!0,o.name="Scene";var i=new cc.Node;i.name="Canvas",i.width=n.width,i.height=n.height;var r=i.addComponent(cc.Canvas);r.designResolution=n,r.fitHeight=!1,r.fitWidth=!1,o.addChild(i),r.addComponent(y.default),setTimeout(function(){console.log("\u821e\u53f0\u521b\u5efa\u5b8c\u6210InitSceneCmptFinish",(new Date).getTime()),cc.director.runSceneImmediate(o),c.default.single.instance(u.default).cocosLoadGameSceneFinished()},1)},t.prototype.initCustomCursor=function(){cc.loader.loadRes("img/refresh",cc.SpriteFrame,function(e,t){e?c.default.error(e):l.default.CUSTOM_CURSOR="url("+t._texture.nativeUrl+") 8 8,auto"})},r([h],t)}(cc.Component);n.default=m,cc._RF.pop()},{"./AssetsManager":"AssetsManager","./Config":"Config","./core/proxy/DataCenterBridge":"DataCenterBridge","./core/proxy/ExtraModule":"ExtraModule","./extends/yy":"yy","./stage/CocosExport":"CocosExport","./stage/StageSceneCmpt":"StageSceneCmpt"}],MergeCommand:[function(e,t,n){"use strict";cc._RF.push(t,"ef302Tg1CpPQoYEYmyeOyQk","MergeCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.addCmdInstance=function(e,t,n){n?this.commandList.splice(0,0,{cmd:e,body:t}):this.commandList.push({cmd:e,body:t})},t.prototype.cloneReverse=function(){for(var e=new t,n=this.commandList.length-1;n>=0;n--){var o=this.commandList[n];e.commandList.push(o)}return e},t.prototype.clone=function(){for(var e=new t,n=0;n<this.commandList.length;n++){var o=this.commandList[n];e.commandList.push(o)}return e},t}(e("../../../../qte/core/extension/command/MacroCommand").default);n.default=r,cc._RF.pop()},{"../../../../qte/core/extension/command/MacroCommand":void 0}],MockData:[function(e,t,n){"use strict";cc._RF.push(t,"6d791tNAa5NOY67i0bKTfGF","MockData"),Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../Config"),i=e("./ExtraModule"),r=function(){function e(){}return e.init=function(){window._$store||(o.default.DEBUG=!0,window._$store={},yy.single.instance(i.default).bootData={resourceMap:{quickResponseQuestion:"http://jiaoyanbos.cdnjtzy.com/cocos/dir/2dccc26fb389dec8a1b81f3e9c6f6956/quickResponseQuestion"}},window._$store.state={userInfo:{mobile:"15611194527",uid:2509023235,username:"wanghuan13",uname:"wanghuan13",zhname:"\u738b\u6b2213",dinguid:0,emplId:"Z076447",create:1705459462,userType:1,email:"<EMAIL>",expire:2549283},mode:0,initialDataLoaded:!0,cocosInitFinished:!0,name:"\u5feb\u901f\u8fde\u7b54",templateId:192,template:{name:"\u5feb\u901f\u8fde\u7b54",tempType:85,category:1169,bundleUrl:"",questionType:2,bundleName:"quickResponseQuestion",tags:[],stage:{width:1280,height:720,safeWidth:1280,safeHeight:960,backgroundColor:"#ffffff",texture:"",textureType:0},extraConfig:[],animationConfig:[{label:"\u8bfb\u9898\u540e",value:"afterReadingQuestion"},{label:"\u6b63\u786e\u540e",value:"afterSubmitCorrect"},{label:"\u7b54\u9519\u540e",value:"afterSubmitWrong"}],features:{isQuestion:1,newCocos:1,hasMys:0,canInteract:1,isOral:0,isGroup:0,demoPage:0,hasVideo:0,liveResources:1,groupData:{questionLength:0}},categoryName:"\u5feb\u901f\u8fde\u7b54"},extData:{formConfig:{quickResponse:[{formItemType:"collapse",collapseName:"\u9898\u5e72\u914d\u7f6e",formList:[{formItemType:"BaseRadioGroup",key:"stuStemModeLeft",label:"\u9898\u5e72\u7c7b\u578b",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"stuStemTextLeft",label:"\u5de6\u9898\u5e72",value:"",span:16,maxLength:29,rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u5de6\u9898\u5e72\u6587\u672c",trigger:"blur"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListLeft",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u5de6\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u8350147*52\uff09",labelPosition:"top",value:""}]}]}]}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"stuStemImgLeft",label:"\u5de6\u9898\u5e72\u56fe\u7247\uff08\u56fe\u7247\u63a8\u835000*00\uff09",labelPosition:"top",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u5de6\u9898\u5e72\u56fe\u7247",trigger:"change"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListLeft",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u5de6\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u835000*00\uff09",labelPosition:"top",value:""}]}]}]}]}],actions:{change:!0},rule:[{required:!0,message:"\u8bf7\u9009\u62e9\u9898\u5e72\u7c7b\u578b",trigger:"change"}]},{formItemType:"BaseInput",key:"stuStemTextLeft",label:"\u5de6\u9898\u5e72",value:"",span:16,maxLength:29,rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u5de6\u9898\u5e72\u6587\u672c",trigger:"blur"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListLeft",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u5de6\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u8350147*52\uff09",labelPosition:"top",value:""}]}]}]},{formItemType:"BaseRadioGroup",key:"stuStemModeRight",label:"\u9898\u5e72\u7c7b\u578b",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"stuStemTextRight",label:"\u53f3\u9898\u5e72",value:"",span:16,maxLength:29,rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u53f3\u9898\u5e72\u6587\u672c",trigger:"blur"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListRight",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u53f3\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u835000*00\uff09",labelPosition:"top",value:""}]}]}]}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"stuStemImgRight",label:"\u53f3\u9898\u5e72\u56fe\u7247\uff08\u56fe\u7247\u63a8\u835000*00\uff09",labelPosition:"top",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u53f3\u9898\u5e72\u56fe\u7247",trigger:"change"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListRight",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u53f3\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u8350147*52\uff09",value:""}]}]}]}]}],actions:{change:!0},rule:[{required:!0,message:"\u8bf7\u9009\u62e9\u9898\u5e72\u7c7b\u578b",trigger:"change"}]},{formItemType:"BaseImageSelect",key:"stuStemImgRight",label:"\u53f3\u9898\u5e72\u56fe\u7247\uff08\u56fe\u7247\u63a8\u835000*00\uff09",labelPosition:"top",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u53f3\u9898\u5e72\u56fe\u7247",trigger:"change"}]},{formItemType:"SpecialDynamicList",key:"stuOptionListRight",label:"\u9009\u9879\u5217\u8868",labelPosition:"top",orderConfig:{show:!0,labelPosition:"left",decorate:"\u53f3\u9009\u9879{{$}}:"},max:29,min:1,value:[{stuOptionType:"0",text:"",imgUrl:""}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u9009\u9879",trigger:"change"}],subFormConfigs:[{formItemType:"BaseRadioGroup",key:"stuOptionType",value:"0",options:[{label:"\u6587\u672c",value:"0",span:8,associatedForm:[{formItemType:"BaseInput",key:"text",label:"\u8bbe\u7f6e\u9009\u9879\u6587\u672c",labelPosition:"top",value:""}]},{label:"\u56fe\u7247",value:"1",span:8,associatedForm:[{formItemType:"BaseImageSelect",key:"imgUrl",label:"\u8bbe\u7f6e\u9009\u9879\u56fe\u7247\uff08\u56fe\u7247\u63a8\u8350147*52\uff09",value:""}]}]}]}]},{formItemType:"collapse",collapseName:"\u9898\u76ee\u5c5e\u6027",formList:[{formItemType:"BaseSingleSelect",key:"stuGameTime",label:"\u8bbe\u7f6e\u6e38\u620f\u65f6\u957f",value:"1",options:[{label:"30\u79d2",value:"0"},{label:"60\u79d2",value:"1"},{label:"90\u79d2",value:"2"},{label:"120\u79d2",value:"3"},{label:"150\u79d2",value:"4"},{label:"180\u79d2",value:"5"}],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u6e38\u620f\u65f6\u957f",trigger:"change"}]},{key:"stuLevelConfigs",labelPosition:"top",formItemType:"SpecialDynamicList",label:"\u7b49\u7ea7\u914d\u7f6e",isShowOrder:!0,orderConfig:{show:!0,labelPosition:"top",type:"function",args:{optionLen:3},decorate:"const strs = ['\u4e09', '\u4e8c', '\u4e00']; \n  const arr = [...Array(optionLen)].map((item, index) => `\u3010${strs[index]}\u9897\u661f\u3011\u7b49\u7ea7\u8bbe\u7f6e`); \nreturn arr"},min:3,max:3,rule:[{required:!0,message:"\u8bf7\u5408\u7406\u8bbe\u7f6e\u7b49\u7ea7\u914d\u7f6e",trigger:"blur"}],subFormConfigs:[{formItemType:"BaseInputNumber",key:"rightCount",label:"\u7b54\u5bf9\u6b63\u786e\u4e2a\u6570",value:1,span:16,stepStrictly:!0,step:1,min:1,rule:[{required:!0,trigger:"blur",message:"\u201d\u7b54\u5bf9\u6b63\u786e\u4e2a\u6570\u201c\u8868\u5355\u4e3a\u5fc5\u586b\u9879"}]},{formItemType:"BaseInput",key:"evaluation",label:"\u8bc4\u4ef7",value:"",span:16,maxLength:30}],value:[{rightCount:3,evaluation:""},{rightCount:2,evaluation:""},{rightCount:1,evaluation:""}]}]},{formItemType:"collapse",collapseName:"\u4e1a\u52a1\u5c5e\u6027",formList:[{formItemType:"BaseSpineSelect",key:"stuSpineSettle",label:"\u7ed3\u679c\u53cd\u9988\u52a8\u753b",hasDefaultOption:!1,value:{atlas:"",images:[],skeleton:"",cover:""},options:[{label:"\u3007\u661f\u52a8\u753b",value:"star0"},{label:"\u4e00\u661f\u52a8\u753b",value:"star1"},{label:"\u4e8c\u661f\u52a8\u753b",value:"star2"},{label:"\u4e09\u661f\u52a8\u753b",value:"star3"}],validation:[],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7ed3\u679c\u53cd\u9988\u52a8\u753b",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundStar0",label:"\u7ed3\u679c\u3007\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7ed3\u679c\u3007\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundStar1",label:"\u7ed3\u679c\u4e00\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7ed3\u679c\u4e00\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundStar2",label:"\u7ed3\u679c\u4e8c\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7ed3\u679c\u4e8c\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundStar3",label:"\u7ed3\u679c\u4e09\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7ed3\u679c\u4e09\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseSpineSelect",key:"stuSpineDirector",label:"\u96ea\u7403\u52a8\u753b",hasDefaultOption:!1,value:{atlas:"",images:[],skeleton:"",cover:""},options:[{label:"\u96ea\u7403\u80cc\u9762\u5f85\u673a\u52a8\u753b",value:"idle"},{label:"\u96ea\u7403\u80cc\u9762\u53f3\u624b\u4e3e\u65d7\u52a8\u753b",value:"right"},{label:"\u96ea\u7403\u80cc\u9762\u5de6\u624b\u4e3e\u65d7\u52a8\u753b",value:"left"},{label:"\u96ea\u7403\u80cc\u9762\u6447\u5934\u52a8\u753b",value:"shakeHead"},{label:"\u96ea\u7403\u6b63\u9762\u5f00\u5fc3\u52a8\u753b",value:"happy"}],validation:[],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u96ea\u7403\u52a8\u753b",trigger:"change"}]},{formItemType:"BaseSpineSelect",key:"stuSpineIP",label:"\u79fb\u52a8\u5143\u7d20\u52a8\u753b",hasDefaultOption:!1,value:{atlas:"",images:[],skeleton:"",cover:""},options:[{label:"\u6b63\u9762\u5f85\u673a",value:"idleFront"},{label:"\u6b63\u9762\u79fb\u52a8",value:"moveFront"},{label:"\u6b63\u9762\u9ad8\u5174\u79fb\u52a8",value:"happyFront"},{label:"\u6b63\u9762\u4f24\u5fc3\u79fb\u52a8",value:"sadFront"},{label:"\u53f3\u4fa7\u5f85\u673a",value:"idleRight"},{label:"\u53f3\u4fa7\u8d70\u8fdb",value:"enterRight"},{label:"\u53f3\u4fa7\u9ad8\u5174\u8d70\u51fa",value:"exitHappyRight"},{label:"\u53f3\u4fa7\u4f24\u5fc3\u8d70\u51fa",value:"exitSadRight"}],validation:[],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u79fb\u52a8\u5143\u7d20\u52a8\u753b",trigger:"change"}]},{formItemType:"BaseSpineSelect",key:"stuSpineAnswerCorrect",label:"\u7b54\u5bf9\u52a8\u753b",hasDefaultOption:!1,value:{atlas:"",images:[],skeleton:"",cover:""},options:[{label:"\u64ad\u653e\u5217\u8868",value:"animation"}],validation:[],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7b54\u5bf9\u52a8\u753b",trigger:"change"}]},{formItemType:"BaseSpineSelect",key:"stuSpineStarProgress",label:"\u661f\u661f\u8fdb\u5ea6\u6761\u52a8\u753b",hasDefaultOption:!1,value:{atlas:"",images:[],skeleton:"",cover:""},options:[{label:"\u9ed8\u8ba4\u72b6\u6001",value:"normal"},{label:"\u8fbe\u6210\u4e00\u661f",value:"star_1"},{label:"\u8fbe\u6210\u4e8c\u661f",value:"star_2"},{label:"\u8fbe\u6210\u4e09\u661f",value:"star_3"}],validation:[],rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u661f\u661f\u8fdb\u5ea6\u6761\u52a8\u753b",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuAudioAnswerCorrect",label:"\u7b54\u5bf9\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7b54\u5bf9\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuAudioAnswerWrong",label:"\u7b54\u9519\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u7b54\u9519\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundArchieve1",label:"\u8fbe\u6210\u4e00\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u8fbe\u6210\u4e00\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundArchieve2",label:"\u8fbe\u6210\u4e8c\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u8fbe\u6210\u4e8c\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuSoundArchieve3",label:"\u8fbe\u6210\u4e09\u661f\u97f3\u6548",value:"",rule:[{required:!0,message:"\u8bf7\u8bbe\u7f6e\u8fbe\u6210\u4e09\u661f\u97f3\u6548",trigger:"change"}]},{formItemType:"BaseAudioSelect",key:"stuAudioGameOver",label:"\u6e38\u620f\u7ed3\u675f\u97f3\u6548",value:""}]}]}},stageData:{width:1280,height:720,safeWidth:1280,safeHeight:960,backgroundColor:"#ffffff",texture:"https://jiaoyanbos.cdnjtzy.com/cw_13fc06689795679ebc4e21c3f5fed0bb.png",textureType:0,bgColor:""},extraStageData:{isAutoSubmit:!0,hasRecover:!1,analysis:{text:"",audio:{url:"",fileName:"",duration:0},imageUrl:""}},componentMap:{1:{type:"optionComponent",subType:"quickResponse",componentLabel:"\u5feb\u901f\u8fde\u7b54",tag:"",canCombine:!1,deletable:!1,dragable:!1,editable:{properties:{width:!1,height:!1,x:!1,y:!1,angle:!1}},properties:{active:!0,width:1280,height:720,opacity:255,angle:0,x:0,y:0,stuStemModeLeft:"0",stuStemModeRight:"1",stuStemTextLeft:"\u8fd9\u662f\u5de6\u9898\u5e72\u5de6\u9898\u5e72",stuOptionListLeft:[{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_27af93650859d1810564ca7317007907.png",_id:1705045343176},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_8236d984721f90eac88ba342bdc5c752.png",_id:1706001943615},{stuOptionType:"0",text:"\u5bf9\u7684\u5bf9\u7684",imgUrl:"",_id:1706001942335},{stuOptionType:"0",text:"\u6b63\u786e\u6b63\u786e",imgUrl:"",_id:1706001940024},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_b0d9b008d4f52bf1ec01a325255a1564.png",_id:1706001938659}],stuStemTextRight:"",stuOptionListRight:[{_id:"1706002023651-0",stuOptionType:"0",text:"\u8fd9\u662f\u5b9d\u7bb1",imgUrl:""},{stuOptionType:"0",text:"\u8fd9\u662f\u602a\u517d",imgUrl:"",_id:1706002041783},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_3f8ea459a6925b8587d425484e177361.png",_id:1706002041287},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_3b27c7271ce97121ecfb5386121eef4b.png",_id:1706002038943},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_ead8d2ee911159c3a7cbe6e9c6d1fe52.png",_id:1706002037399},{stuOptionType:"0",text:"\u8fd9\u662f\u5feb\u901f\u8fde\u7b54",imgUrl:"",_id:1706002036215},{stuOptionType:"1",text:"",imgUrl:"https://jiaoyanbos.cdnjtzy.com/cw_609662d596045cbb0de4479cf5a18b6f.png",_id:1706002035144}],stuStemImgLeft:"",stuStemImgRight:"https://jiaoyanbos.cdnjtzy.com/cw_b362605f46b322caf938d73abca2b9c6.png",stuGameTime:"5",stuLevelConfigs:[{rightCount:300,evaluation:"A",_id:1705045511020},{rightCount:6,evaluation:"B",_id:1705045515338},{rightCount:4,evaluation:"C",_id:1705045516668}],stuSpineSettle:{atlas:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.atlas",images:["https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.png"],skeleton:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/jieguo_069ea27b80e2376e488585f6ebed8ba2/jieguo.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-e90b511bfca5fdd6875fce577c03d4c2.png",star0:"jieguo_0",star1:"jieguo_1",star2:"jieguo_2",star3:"jieguo_3"},stuSoundStar0:"https://yaya.cdnjtzy.com/cchd_dev/tihu-97fed0719ff34082ca08824ea1b14eba.mp3",stuSoundStar1:"https://yaya.cdnjtzy.com/cchd_dev/tihu-3bfba1f5cf6ba34ef8e8610b7e23aa80.mp3",stuSoundStar2:"https://yaya.cdnjtzy.com/cchd_dev/tihu-7ebde8d2e9627caa1116331ea7e7f926.mp3",stuSoundStar3:"https://yaya.cdnjtzy.com/cchd_dev/tihu-99c869442c72b5613a6f18b43e1b6716.mp3",stuSpineDirector:{atlas:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.atlas",images:["https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.png"],skeleton:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xueqiu_454b48bffbe13d397bd786468f76f97a/xueqiu/xueqiu.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-790e341e821d40d47d6f9b6405206447.png",idle:"idle",right:"right",left:"left",shakeHead:"error",happy:"happy"},stuSpineIP:{atlas:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.atlas",images:["https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.png"],skeleton:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/tuanzi_d8e07b2f890fa5f837381dfaaf303689/tuanzi/tuanzi.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-ad1c78dbe263b2351f2def2e53542d54.png",idleFront:"zheng_idle",moveFront:"zheng_walk",happyFront:"zheng_happy",sadFront:"zheng_sad",idleRight:"idle_right",enterRight:"in_right",exitHappyRight:"out_happy_right",exitSadRight:"out_sad_right"},stuSpineAnswerCorrect:{atlas:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.atlas",images:["https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.png"],skeleton:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/sahua_9190e05b739b931535f0bf34ea53ef82/sahua/sahua.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-3ce80d7de31408ebb0b10760e63e3b65.png",animation:"sahua"},stuSpineStarProgress:{atlas:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.atlas",images:["https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.png"],skeleton:"https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/xx_fe2ae68a81ca1f41b2daab648b28627e/xingxing.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-1fff7447849baa33c150fe80d39843fd.png",normal:"normal",star_1:"xing_1",star_2:"xing_2",star_3:"xing_3"},stuAudioAnswerCorrect:"https://yaya.cdnjtzy.com/cchd_dev/tihu-0eba914481d030442811b667de0c1de0.mp3",stuAudioAnswerWrong:"https://yaya.cdnjtzy.com/cchd_dev/tihu-a471fd6d061932a59f4bd0c6f6d68976.mp3",stuSoundArchieve1:"https://yaya.cdnjtzy.com/cchd_dev/tihu-3bfba1f5cf6ba34ef8e8610b7e23aa80.mp3",stuSoundArchieve2:"https://yaya.cdnjtzy.com/cchd_dev/tihu-7ebde8d2e9627caa1116331ea7e7f926.mp3",stuSoundArchieve3:"https://yaya.cdnjtzy.com/cchd_dev/tihu-36cc275ba7a55c19348c83ec9e57bdb7.mp3",stuAudioGameOver:"https://yaya.cdnjtzy.com/cchd_dev/tihu-84d52be14d33c63b51c3ef1559ad51fd.mp3"},id:"1",extra:{tag:""}},2:{tag:"",type:"spine",properties:{timeScale:1,animationList:[""],loop:!0,active:!0,scaleX:1,scaleY:1,x:0,y:0,width:1305.5,height:734.5,angle:0,offsetX:-52.73199999999997,offsetY:37.080000000000034},spineData:{atlas:"http://************:8000/yindao.atlas",images:["http://************:8000/yindao.png"],skeleton:"http://************:8000/yindao.json",cover:"https://yaya.cdnjtzy.com/cchd_dev/tihu-2a457c606f55b0b7df978b910c6489fe.png"},id:"2",extra:{tag:""}}},componentIds:["1","2"],currentComponentIds:[],animations:{afterReadingQuestion:{audio:{url:"",delay:0},fragments:{},points:{}},afterSubmitCorrect:{audio:{url:"",delay:0},fragments:{},points:{}},afterSubmitWrong:{audio:{url:"",delay:0},fragments:{},points:{}}},contextMenu:{top:400,left:200,visible:!1},clipboard:{type:0,components:[]},isEditingComponentAnimations:!1,editingAnimationsComponentId:"",componentAnimationConfig:[],componentAnimationVal:"",componentFragmentId:"",componentActiveActionId:"",tagsData:'{"ability":[],"difficulty":{"id":0,"name":null},"pointList":[{"id":1,"name":"\u8d28\u91cf\u53ca\u8d28\u91cf\u7684\u5e38\u7528\u5355\u4f4d"}]}',editFocus:!1,parentVersion:1,renderFormula:!1,containerWidth:468,containerHeight:351,componentManagerCard:{visible:!0,activeName:"LIBRARY"},moduleAnimations:{animationVal:"afterReadingQuestion",fragmentId:"",activeActionId:"",animations:{afterReadingQuestion:{audio:{url:"",delay:0},fragments:{},points:{}},afterSubmitCorrect:{audio:{url:"",delay:0},fragments:{},points:{}},afterSubmitWrong:{audio:{url:"",delay:0},fragments:{},points:{}}},isPlayingAnimation:!1,isPlayingFragment:!1},timeTravel:{currentIndex:0,total:0},rulerTick:{verticalLines:[],horizontalLines:[]},gradeId:0,subjectId:0,status:1},window._$store.commit=function(e,t){yy.log(e,t)},window._$store.subscribe=function(e,t){yy.log(e,t)},window._$store.subscribeAction=function(e){yy.log(e)},window._$store.getters={newId:"3",currentComponents:[],componentIdsAllObjectType:[{id:"1",subIds:[]},{id:"2",subIds:[]}],isNormalMode:!0,isAnimationMode:!1,flatComponentActions:[],"moduleAnimations/flatActions":[],"moduleAnimations/currentComponentsActions":[],"timeTravel/canRedo":!1,"timeTravel/canUndo":!0})},e}();n.default=r,cc._RF.pop()},{"../../Config":"Config","./ExtraModule":"ExtraModule"}],MoveTouchHandler:[function(e,t,n){"use strict";cc._RF.push(t,"e3398LD7JBK0pZS6IZz9G9h","MoveTouchHandler");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../qte/core/base/SingleBase"),a=e("../../qte/core/extension/command/CommandManager"),s=e("../common/EditorEnum"),c=e("../core/collision/CollisionManager"),p=e("../core/command/commond"),l=e("../core/command/operate/CommandFactory"),u=e("../core/command/simple/UpdateProp2VueCmd"),d=e("../core/display/base/DisplayObjectGroup"),y=e("../core/display/DisplayObjectManager"),f=e("../core/hintline/HintLineManager"),h=e("./EditBoxTouchHandler"),m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._collideMap=new Map,t._instanceManager=null,t._typeHandlePoint=null,t.bUpdateGroupData=!1,t.mouseSate=-1,t}return i(t,e),t.prototype.initInstance=function(){this._displayManager=yy.single.instance(y.default),this._collisionManager=yy.single.instance(c.default),this._editBoxTouchHandler=yy.single.instance(h.default),this._instanceManager=yy.single.instance(f.default)},t.prototype.mouseBegin=function(e,t,n){if(this.mouseSate=0,"-1"!==this._displayManager.selectId){this._typeHandlePoint=null;for(var o=-1,i=0;i<this._displayManager.selectedIds.length;i++){var r=this._displayManager.selectedIds[i],a=this._displayManager.getDisplayObjectById(r),c=a.inSelRect(e);if(null!==c){var u=a.node.getSiblingIndex();0!=a.dragable&&u>o&&(this._typeHandlePoint=c,o=u,this._displayManager.selectId=r)}}if(null!==this._typeHandlePoint)return;this._displayManager.selectId="-1"}else this.mouseSate=2;var y="-1",f=this._displayManager.getDisplayObjectById(t);if(f&&f.type===s.CmptType.GROUP&&this._displayManager.selectedIds.find(function(e){return e===t})){e=f.node.convertToNodeSpaceAR(e);var h=f.getComponent(d.default).groupIds;h.sort(this.compareByZIndex.bind(this));var m=this._collisionManager.testDisplayObject(e,h,this._displayManager.dpMap);"-1"!==m&&(this.isUnSelect(h,t)||n||(y=m))}this._displayManager.selectedIds.length>0&&this._displayManager.selectedIds[0]!=t&&(this.mouseSate=2),"-1"!=this._displayManager.selectSubId&&y!==this._displayManager.selectSubId&&(this._displayManager.selectSubId="-1",yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:[],isVue:!1,isMulti:!0})),"-1"!==t?(this._displayManager.selectId=t,yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:[t],isVue:!1,isMulti:n})):yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:[],isVue:!1,isMulti:n})},t.prototype.mouseMove=function(e){if(this.mouseSate=1,"-1"!==this._displayManager.selectId){var t=e.getDelta(),n=e.getLocation(),o=cc.v2(n.x-cc.winSize.width/2,n.y-cc.winSize.height/2);if("-1"===this._displayManager.selectSubId)this._typeHandlePoint?this._typeHandlePoint.updateDisplayObjectByHandle(this._displayManager.selectId,new cc.Vec3(o.x,o.y)):this.hintLineHandler(o,t);else{var i=this._displayManager.getDisplayObjectById(this._displayManager.selectId);if(this._typeHandlePoint?(i.type,s.CmptType.GROUP,this._typeHandlePoint.updateDisplayObjectByHandle(this._displayManager.selectId,new cc.Vec3(o.x,o.y))):this._displayManager.getDisplayObjectById(this._displayManager.selectSubId)&&(this._editBoxTouchHandler.stopEdit(this._displayManager.selectSubId,this._displayManager),this._displayManager.updateDisplayObjectDeltaPos(this._displayManager.selectSubId,new cc.Vec3(t.x,t.y))),i)if(i.type===s.CmptType.GROUP)this.bUpdateGroupData=!0;else{var r=this._displayManager.getDisplayGroupID(this._displayManager.selectSubId);"-1"!==r&&this._displayManager.getDisplayObjectById(r)&&(this.bUpdateGroupData=!0)}}}},t.prototype.mouseUp=function(e,t){"-1"!==this._displayManager.selectId&&(this._typeHandlePoint=null,this.updateEditor(this._displayManager.selectedIds),0==this.mouseSate&&this.groupSubIdsChoose(e,t)),this.resetHintLineHandler()},t.prototype.cancleStageMouse=function(e,t){e&&this.mouseUp(e,t),this._typeHandlePoint&&(this._typeHandlePoint=null)},t.prototype.compareByZIndex=function(e,t){var n=this._displayManager.getDisplayObjectById(e),o=this._displayManager.getDisplayObjectById(t);return n.node.getSiblingIndex()-o.node.getSiblingIndex()},t.prototype.groupSubIdsChoose=function(e,t){var n=this._displayManager.selectId,o="-1",i=this._displayManager.getDisplayObjectById(n);if(i&&i.type===s.CmptType.GROUP&&this._displayManager.selectedIds.find(function(e){return e===n})){e=i.node.convertToNodeSpaceAR(e);var r=i.getComponent(d.default).groupIds;r.sort(this.compareByZIndex.bind(this));var a=this._collisionManager.testDisplayObject(e,r,this._displayManager.dpMap);"-1"!==a&&(this.isUnSelect(r,n)||t||(o=a))}"-1"!==o&&o!==this._displayManager.selectSubId&&yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:[o],isVue:!1,isMulti:!0}),this._displayManager.selectSubId=o},t.prototype.onPressDirection=function(e){var t=this._displayManager.getRootIds(),n=this._displayManager.getSubIds(),o=!1;1==n.length&&(t=n,o=!0);for(var i=0,r=t;i<r.length;i++){var a=r[i];if(0==this._displayManager.getDisplayObjectById(a).dragable)return}for(var s=0,c=t;s<c.length;s++){a=c[s];var p=this._displayManager.getDisplayObjectById(a);p&&(p.editable&&p.editable.properties&&!1===p.editable.properties.x&&(e.x=0),p.editable&&p.editable.properties&&!1===p.editable.properties.y&&(e.y=0),0==p.dragable&&(e.x=0,e.y=0),p.node.position=p.node.position.add(e))}if(this.updateEditor(t),o){var l=this._displayManager.getDisplayGroupID(t[0]);"-1"!==l&&this._displayManager.getDisplayObjectById(l).resetLayoutSize()}},t.prototype.resetHintLineHandler=function(){this._instanceManager.resetHintLineHandler()},t.prototype.hintLineHandler=function(e,t){for(var n=0,o=this._displayManager.selectedIds;n<o.length;n++){var i=o[n];if(0==this._displayManager.getDisplayObjectById(i).dragable)return}this._editBoxTouchHandler.stopEdit(this._displayManager.selectId,this._displayManager);var r=t.x,a=t.y;if(1==this._displayManager.selectedIds.length&&this._displayManager.selectedIds[0]&&"-1"!==this._displayManager.selectedIds[0]){i=this._displayManager.selectedIds[0];var s=this._instanceManager.updateLineByMove(i,e,r,a);r=s.deltaX,a=s.deltaY}for(var c=0,p=this._displayManager.selectedIds;c<p.length;c++)i=p[c],this._displayManager.updateDisplayObjectDeltaPos(i,new cc.Vec3(r,a))},t.prototype.isUnSelect=function(e,t){for(var n=!1,o=function(o){if(!e.find(function(e){return e===o})&&o!==t)return n=!0,"break"},i=0,r=this._displayManager.selectedIds;i<r.length&&"break"!==o(r[i]);i++);return n},t.prototype.updateEditor=function(e){yy.single.instance(a.default).executeCommand(u.default,{selectIds:e,isUpdateGroupData:!0})},t}(r.SingleBase);n.default=m,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../../qte/core/extension/command/CommandManager":void 0,"../common/EditorEnum":"EditorEnum","../core/collision/CollisionManager":"CollisionManager","../core/command/commond":"commond","../core/command/operate/CommandFactory":"CommandFactory","../core/command/simple/UpdateProp2VueCmd":"UpdateProp2VueCmd","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/base/DisplayObjectGroup":"DisplayObjectGroup","../core/hintline/HintLineManager":"HintLineManager","./EditBoxTouchHandler":"EditBoxTouchHandler"}],OptSubObjectFactory:[function(e,t,n){"use strict";cc._RF.push(t,"fd756mt4fpORb+yZDTNyIMO","OptSubObjectFactory");var o=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/assemble/core/BaseComponent"),a=function(){function e(){}return e.getInstance=function(){return this._instance||(this._instance=new e),this._instance},e.destroyInstance=function(){this._instance=null},e.prototype.createObject=function(e,t,n){return o(this,void 0,Promise,function(){return i(this,function(e){switch(e.label){case 0:return this.bundle=n,console.log("%c Line:41 \ud83c\udf7a bundle","color:#6ec1c2",n),null,[4,this.createOptionComp(n)];case 1:return[2,e.sent().getComponent(r.default)]}})})},e.prototype.createOptionComp=function(e){return console.log("%c Line:62 \ud83c\udf47 bundle","color:#6ec1c2",e),new Promise(function(t){e.load("prefabs/optComp",cc.Prefab,function(e,n){if(e)console.log("%c Line:65 \ud83c\udf4f err","color:#2eafb0",e);else{var o=cc.instantiate(n);o.active=!0,t(o)}})}).catch(function(e){return console.log("%c Line:75 \ud83c\udf77 err","color:#3f7cff",e),null})},e._instance=null,e}();n.default=a,cc._RF.pop()},{"../../../qte/assemble/core/BaseComponent":void 0}],Properties:[function(e,t,n){"use strict";cc._RF.push(t,"6de93l+fG1Jsacl83BkqLzV","Properties"),Object.defineProperty(n,"__esModule",{value:!0}),n.EValue=void 0;var o=e("../../qte/core/utils/ValueUtils"),i=e("../common/ComUtils"),r=e("../common/EditorEnum"),a=function(){};n.EValue=a;var s=function(){function e(){}return e.colorHex=function(e){if(/^(rgba|RGBA)/.test(e)){for(var t=e.replace(/(?:\(|\)|rgba|RGBA)*/g,"").split(","),n="#",o=0;o<t.length;o++)"0"===(r=Number(t[o]).toString(16))&&(r+=r),n+=r;return 7!==n.length&&(n=e),n}if(/^(rgb|RGB)/.test(e)){t=e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),n="#";for(var i=0;i<t.length;i++){var r;"0"===(r=Number(t[i]).toString(16))&&(r+=r),n+=r}return 7!==n.length&&(n=e),n}if(!/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6}|[0-9a-fA-f]{8})$/.test(e))return e;var a=e.replace(/#/,"").split("");if(8===a.length){for(var s="#",c=0;c<6;c+=1)s+=a[c];return s}if(6===a.length)return e;if(3===a.length){s="#";for(var p=0;p<a.length;p+=1)s+=a[p]+a[p];return s}},e.setDefaultColor=function(t){var n=t.toUpperCase();n=(n=n.replace(/\s*/g,"")).replace(/\uff0c/g,",");var o={hex1:/^#[0-9A-F]{3}$/,hex2:/^#[0-9A-F]{6}$/,rgb:/^(RGB\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\)?$/,argb:/^#[0-9A-F]{8}$/,rgba:/^(RGBA\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),([01]|0?\.\d+|1\.0+)\)?$/},i="";for(var r in o)if(o[r].test(n)){i=r;break}if(i){var a="";switch(i){case"hex1":case"hex2":a=e.hexToRgba(n);break;case"rgb":a=e.rgbToRgba(n);break;case"argb":a=e.argbToRgba(n);break;case"rgba":a=n.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),((0?\.\d+)|(1\.0+)|[01])/)[0]}return console.log(a),e.rgbaToHex(a)}},e.hexToRgba=function(e){if(/^#[0-9A-F]{3}$/.test(e)){var t=e.match(/[0-9A-F]/g).slice(0,3),n=t[0],o=t[1],i=t[2];return"".concat(parseInt(n+n,16).toString(),",").concat(parseInt(o+o,16).toString(),",").concat(parseInt(i+i,16).toString(),",1")}if(/^#[0-9A-F]{6}$/.test(e)){var r=e.match(/[0-9A-F]{2}/g).slice(0,3),a=r[0],s=r[1],c=r[2];return"".concat(parseInt(a,16).toString(),",").concat(parseInt(s,16).toString(),",").concat(parseInt(c,16).toString(),",1")}return""},e.rgbToRgba=function(e){return(e=e.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])/)[0])+",1"},e.argbToRgba=function(e){var t=t=e.match(/[0-9A-F]{2}/g).slice(0,4),n=t[0],o=t[1],i=t[2],r=t[3];return n=(parseInt(n,16)/255).toFixed(3),"".concat(parseInt(o,16).toString(),",").concat(parseInt(i,16).toString(),",").concat(parseInt(r,16).toString(),",").concat(n)},e.rgbaToHex=function(e){var t=e.split(",").slice(0,4),n=t[0],o=t[1],i=t[2],r=t[3];return r=parseFloat(r),n=Math.floor(r*parseInt(n)+255*(1-r)),o=Math.floor(r*parseInt(o)+255*(1-r)),i=Math.floor(r*parseInt(i)+255*(1-r)),"#".concat(("0"+n.toString(16).toUpperCase()).slice(-2)).concat(("0"+o.toString(16).toUpperCase()).slice(-2)).concat(("0"+i.toString(16).toUpperCase()).slice(-2))},e.setProperty=function(t,n,s,c,p){var l=new a,u=null;t.type===r.CmptType.SVGSHAPE&&(u=t);var d=null;t.type===r.CmptType.CUTSHAPE&&(d=t);var y=null;if(t.type===r.CmptType.LABEL&&(y=t),t.type===r.CmptType.SPECIALCOMPONENT||t.type===r.CmptType.OPTIONCOMPONENT){var f=t;l.value=f[s],void 0===l.value&&(l.value=f.customDataPropertiesForkey(s)),f.changeProperties(s,c)}switch(s){case"anchorY":l.type="number",l.value=t.node.anchorY,t.node.anchorY=Number(c);break;case"anchorX":l.type="number",l.value=t.node.anchorX,t.node.anchorX=Number(c);break;case"angle":l.type="number",l.value=t.getNodeAngle();var h=yy.checkValue(c);t.node.cAngle=h,t.node.angle=h;break;case"rotation":l.type="number",l.value=t.node.rotation,t.node.rotation=Number(c);break;case"opacity":if(l.type="number",l.value=t.node.opacity,t.node.opacity=Number(c),console.warn("opacity change==>",t),t.type===r.CmptType.SVGSHAPE){console.warn("svgNode====>",t.node,c);var m=t.node.getChildByName("display_svg").getChildByName("svg_group");m&&i.ComUtil.setSvgShapeOpacity(m,c/255),u.shapeDatas.opacity=c}break;case"scaleX":l.type="number",l.value=t.node.scaleX,t.node.scaleX=Number(c);break;case"scaleY":l.type="number",l.value=t.node.scaleY,t.node.scaleY=Number(c);break;case"active":l.type="boolean",l.value=t.node.active,t.node.active=Boolean(c);break;case"width":l.type="number",l.value=t.node.width,t.node.width=Number(c),t.type===r.CmptType.LABEL?y.setNodeWidth(Number(c),p):t.type===r.CmptType.SVGSHAPE&&u.setNodeWidth(o.default.setOneDecimal(c));break;case"height":l.type="number",l.value=t.node.height,t.node.height=Number(c),t.type===r.CmptType.LABEL?y.setNodeHeight(Number(c)):t.type===r.CmptType.SVGSHAPE&&u.setNodeHeight(o.default.setOneDecimal(c));break;case"x":l.type="number",l.value=t.node.x,t.node.x=Number(c);break;case"y":l.type="number",l.value=t.node.y,t.node.y=Number(c);break;case"color":l.type="color",l.value="#"+t.node.color.toHEX();var g=e.setDefaultColor(c);console.log("color:",g),t.node.color=(new cc.Color).fromHEX(g),t.type===r.CmptType.LABEL&&(l.value=y.setFontColor(g));break;case"string":var _=t.node.getComponent(cc.Label);l.type="string",l.value=_.string,_.string=c;break;case"enableBold":l.type="number",t.type===r.CmptType.LABEL&&(l.value=y.setBoldEnabled(Boolean(c)));break;case"enableItalic":l.type="boolean",t.type===r.CmptType.LABEL&&(l.value=y.setItalicEnabled(Boolean(c)));break;case"enableUnderline":l.type="boolean",t.type===r.CmptType.LABEL&&(l.value=y.setUnderLine(Boolean(c)));break;case"isFixed":l.type="boolean",t.type===r.CmptType.LABEL&&(l.value=y.setIsFixed(Boolean(c)));break;case"rowSpacing":l.type="number",t.type===r.CmptType.LABEL&&(l.value=y.setRowSpace(Number(c)));break;case"fontSize":l.type="number",t.type===r.CmptType.LABEL&&(l.value=y.setFontSize(Number(c)));break;case"lineHeight":l.type="number",t.type===r.CmptType.LABEL&&(l.value=y.setLineHeight(Number(c)));break;case"spacingX":var v=t.node.getComponent(cc.Label);l.type="number",l.value=v.spacingX,v.spacingX=Number(c);break;case"horizontalAlign":l.type="number",t.type===r.CmptType.LABEL&&(l.value=y.setHorizontalAlign(Number(c)));break;case"verticalAlign":var b=t.node.getComponent(cc.Label);l.type="number",l.value=b.verticalAlign,b.verticalAlign=Number(c);break;case"overflow":var C=t.node.getComponent(cc.Label);l.type="number",l.value=C.overflow,C.overflow=Number(c);break;case"texture":case"url":var O=t.node.getComponent(cc.Sprite);if(yy.loader.cmptAssets.get(t.cid)&&yy.loader.cmptAssets.get(t.cid).forEach(function(e){yy.loader.assetsMap.get(e).refCount>0&&yy.loader.assetsMap.get(e).decRef()}),yy.loader.cmptAssets.set(t.cid,[]),console.log("yy.loader.cmptAssets.set==",yy.loader.cmptAssets.get(t.cid)),!c)break;yy.loader.loadRes(String(c),cc.Texture2D,function(e,t){e?yy.warn(e):(t.packable=!1,i.ComUtil.addPremultiplyAlpha(t,O),O.___textureUrl=c)},null,t.cid),l.type="string",l.value=O.___textureUrl;break;case"flipType":l.type="number",l.value=Number(c);var T=t.node.getComponent(cc.Sprite);T&&i.ComUtil.setMateria(T,c);break;case"animationList":var w=t;l.value=w.actionList,w.setAnimation(c);break;case"loop":l.value=t.loop,t.setLoop(c);break;case"timeScale":l.value=t.timeScale,t.setTimeScale(c);break;case"textAsset":l.type="textAsset",l.value=c,u.setPath(c);break;case"lineWidth":l.type="color",l.value=c,t.type===r.CmptType.SVGSHAPE?u.changeStrokeWidth(c):t.type===r.CmptType.CUTSHAPE&&(l.value=d.setLineWidht(c));break;case"strokeColor":l.type="color",l.value=c,t.type===r.CmptType.SVGSHAPE?u.changeStrokeColor(c):t.type===r.CmptType.CUTSHAPE&&(l.value=d.setStrockColor(c));break;case"fillColor":l.type="color",l.value=c,t.type===r.CmptType.SVGSHAPE?u.changeFillColor(c):t.type===r.CmptType.CUTSHAPE&&(l.value=d.setFillColor(c));break;case"dragable":l.type="boolean",l.value=t.dragable,t.dragable=c;break;case"str":l.type="string",l.value=c,t.type===r.CmptType.LABEL&&(console.log("\u4fee\u6539\u5bcc\u6587\u672c1",c),l.value=y.setRichTextToEditBox(c));break;case"cusorIndex":l.type="number",l.value=c,t.type===r.CmptType.LABEL&&(l.value=y.setCursorLabelIndex(c));break;case"isLabelRight":l.type="boolean",l.value=c,t.type===r.CmptType.LABEL&&(l.value=y.setCursorIsLabelRight(c));break;case"selectArr":l.type="number",l.value=c,t.type,r.CmptType.LABEL;break;case"isHidePointLine":l.type="boolean",t.type===r.CmptType.CUTSHAPE&&(l.value=d.updateLinePointActive(c));break;case"displayType":case"boardType":l.type="number",l.value=c;break;case"background":case"lineColor":l.type="string",l.value=c;break;case"keyboardType":case"offsetX":case"offsetY":l.type="number",l.value=c;break;default:yy.log("properties error")}return l},e}();n.default=s,cc._RF.pop()},{"../../qte/core/utils/ValueUtils":void 0,"../common/ComUtils":"ComUtils","../common/EditorEnum":"EditorEnum"}],RemoveActionCommand:[function(e,t,n){"use strict";cc._RF.push(t,"dc69fm9AjxAtaQZFoDUE4+I","RemoveActionCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveActionCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(){var e=yy.single.instance(s.default).selectedIds,t=yy.single.instance(a.default);t.updateFragmentsMap(),t.showAllPah(e)},t.prototype.undo=function(){this.execute()},t}(r.default);n.RemoveActionCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],RemoveCmptCommand:[function(e,t,n){"use strict";cc._RF.push(t,"5232cwaf1ZNYZF3qVD0ljZq","RemoveCmptCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveCmptCommand=void 0;var s=e("../../../../../qte/core/extension/command/SimpleCommand"),c=e("../../../../common/EditorEnum"),p=e("../../../display/base/DisplayObject"),l=e("../../../display/DisplayObjectFactory"),u=e("../../../display/DisplayObjectManager"),d=e("../../../display/TemplateInterpreter"),y=e("../../../hintline/HintLineManager"),f=e("../../../proxy/ComptData"),h=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(u.default),n=t.getDisplayObjectById(e),o=yy.single.instance(l.default).getCmptTypeStr(n.type),i=n.getNewProperties(),r=t.getDisplayGroupID(e),a=[],s=null;if("-1"!==r){var p=(m=t.getDisplayObjectById(r)).getNewProperties(),d=p.extra.subProperties;s=p.newProperties;for(var y=0;y<d.length;y++){var f=d[y];a.push({subId:f.id,x:f.newProperties.x,y:f.newProperties.y,angle:f.newProperties.angle})}t.selectSubId="-1"}var h=[];if(o===c.CmptType.GROUP)for(var m,g=0,_=(m=n).groupIds;g<_.length;g++){var v=_[g];(w=t.getDisplayObjectById(v))&&(h.push(w.getNewProperties()),t.removeDisplayObject(v))}"-1"!==r&&((m=t.getDisplayObjectById(r)).removeSubObject(e),m.resetLayoutSize());var b=t.getDisplayParentID(e);"-1"!==b&&t.getDisplayObjectById(b).removeChildObject(e);for(var C=[],O=0,T=n.childIds;O<T.length;O++){var w;v=T[O],(w=t.getDisplayObjectById(v))&&(C.push(w.getNewProperties()),n.removeChildObject(v),t.removeDisplayObject(v))}this.oldBody={id:e,type:o,properties:i,subComponent:h,groupid:r,subDisplayPropertes:a,groupPropertes:s,parentId:b,childComponent:C},t.removeDisplayObject(e),yy.log("----RemoveCmptCommand---execute---\x3e",this.oldBody)},t.prototype.undo=function(e){yy.log("----RemoveCmptCommand---undo---\x3e",e);var t=this.getCmptProp(e.properties);this.addDisplay(e.id,e.type,t,e.properties.extra.spineData,e.properties.extra.cocosAniData,e.properties.extra.dragable,e.properties.extra.subType);for(var n=yy.single.instance(u.default),o=n.getDisplayObjectById(e.id),i=0,r=e.subComponent;i<r.length;i++){var a=r[i],s=this.getCmptProp(a),p=a.extra.type;this.addDisplay(a.id,p,s,a.extra.spineData,e.properties.extra.cocosAniData,a.extra.dragable,a.extra.subType)}if(e.type===c.CmptType.GROUP&&this.assenmleGroup({id:e.id,subComponent:e.properties.extra.groupIds}),"-1"!==e.groupid){var l=n.getDisplayObjectById(e.groupid);o.node.parent=l.node,l.addSubObject(e.id,o),l.resetLayoutSize(!0);for(var d=l.groudSubs,y=function(t){var n=d[t],o=e.subDisplayPropertes.find(function(e){return e.subId===n.cid});o&&(n.node.x=o.x,n.node.y=o.y,n.node.angle=o.angle,n.node.cAngle=o.angle)},f=0;f<d.length;f++)y(f);for(var h in e.groupPropertes)l.node[h]=e.groupPropertes[h],"angle"==h&&(l.node.cAngle=e.groupPropertes[h]);l.refreshRectPoint()}if("-1"!==e.parentId){var m=yy.single.instance(u.default).getDisplayObjectById(e.parentId);m.addChildObject(e.id,o),o.node.parent=m.node}for(var g=0,_=e.childComponent;g<_.length;g++){var v=_[g];s=this.getCmptProp(v),p=v.extra.type,this.addDisplay(v.id,p,s,v.extra.spineData,v.extra.cocosAniData,v.extra.dragable,v.extra.subType);var b=n.getDisplayObjectById(v.id);o.addChildObject(v.id,b),b.node.parent=o.node}},t.prototype.addDisplay=function(e,t,n,o,i,s,y){return r(this,void 0,void 0,function(){var r,h,m,g,_;return a(this,function(a){switch(a.label){case 0:return r=yy.single.instance(l.default),h=yy.single.instance(u.default),m=new f.default,h.getDisplayObjectById(e)?[2]:(m.id=e,m.type=t,m.properties=n,m.spineData=o,m.cocosAniData=i,m.dragable=s,m.subType=y,g=null,t!==c.CmptType.SPECIALCOMPONENT?[3,2]:[4,r.getDisplayNode(m,!0)]);case 1:return g=a.sent(),[3,3];case 2:g=r.getDisplayNodeAsync(m,!0),a.label=3;case 3:return yy.single.instance(d.default).addObjFunc(g,c.CmptLayer.OBJECT_LAYER),_=g.getComponent(p.default),g.setSiblingIndex(n.zIndex),h.addDisplayObject(e,_),[2]}})})},t.prototype.assenmleGroup=function(e){var t=yy.single.instance(u.default),n=t.getDisplayObjectById(e.id),o=n.node;if(n)for(var i=0,r=e.subComponent;i<r.length;i++){var a=r[i],s=t.getDisplayObjectById(a);s.node.parent=o,n.addSubObject(a,s),yy.single.instance(y.default).removeHintLineByDisplay(a)}},t.prototype.getCmptProp=function(e){var t={};if(e.newProperties)for(var n in e.newProperties)t[n]=e.newProperties[n];if(e.extra)for(var n in e.extra)t[n]=e.extra[n];return t},t}(s.default);n.RemoveCmptCommand=h,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../common/EditorEnum":"EditorEnum","../../../display/DisplayObjectFactory":"DisplayObjectFactory","../../../display/DisplayObjectManager":"DisplayObjectManager","../../../display/TemplateInterpreter":"TemplateInterpreter","../../../display/base/DisplayObject":"DisplayObject","../../../hintline/HintLineManager":"HintLineManager","../../../proxy/ComptData":"ComptData"}],RemoveFragmentCommand:[function(e,t,n){"use strict";cc._RF.push(t,"7bc73TR3j9OA6mrPAuIa3ds","RemoveFragmentCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.RemoveFragmentCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../animation/display/AnimDisplayManager"),c=e("../../../display/DisplayObjectManager"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default);this.oldBody=e,t.setFragmentId(""),t.updateFragmentsMap(),yy.single.instance(s.default).clearAnimDisplayObject()},t.prototype.undo=function(e){var t=yy.single.instance(a.default),n=yy.single.instance(c.default).selectedIds;t.updateFragmentsMap(),t.setFragmentId(e.fragment),t.showAllPah(n)},t}(r.default);n.RemoveFragmentCommand=p,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../animation/display/AnimDisplayManager":"AnimDisplayManager","../../../display/DisplayObjectManager":"DisplayObjectManager"}],RemovePointCommand:[function(e,t,n){"use strict";cc._RF.push(t,"74df8n94CtPEKbFFYZstz6C","RemovePointCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.RemovePointCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default).getDisplayObjectById(e.cmptIds).removeEditPoint(e.pointId,!0);this.oldBody={id:e.cmptIds,pointData:t}},t.prototype.undo=function(e){yy.single.instance(a.default).getDisplayObjectById(e.id).createEditPoint(e.pointData)},t}(r.default);n.RemovePointCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],ReplaceCmptCommand:[function(e,t,n){"use strict";cc._RF.push(t,"0be59YBGu5K7I5iyz863poS","ReplaceCmptCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.ReplaceCmptCommand=void 0;var r=e("../../../../../qte/core/utils/ValueUtils"),a=e("../../../../../qte/core/extension/command/SimpleCommand"),s=e("../../../../cmpt/AnimTouchHandler"),c=e("../../../../common/EditorEnum"),p=e("../../../display/DisplayObjectManager"),l=e("../../../proxy/DataCenterBridge"),u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(p.default);if(this.oldBody=t.selectedIds,console.warn("ReplaceCmptCommand this.oldBody",JSON.stringify(this.oldBody)),e.isVue&&this.setSelectIds(["-1"],e.isMulti,e.isVue),e.ids.length<=0?this.setSelectIds(["-1"],e.isMulti,e.isVue):this.setSelectIds(e.ids,e.isMulti,e.isVue),!e.isVue&&!e.option){var n=yy.cloneValues(t.selectedIds),o=this.getSubIdsExceptGroupId(n);console.warn("ReplaceCmptCommand temp",n),console.warn("ReplaceCmptCommand result",o),yy.single.instance(l.default).replaceComponet(o)}console.warn("ReplaceCmptCommand",e),this.newBody.option=!0,(0==e.ids.length||1===e.ids.length&&"-1"===e.ids[0])&&yy.single.instance(p.default).compareAndRefreshDisplayObjectIndex()},t.prototype.undo=function(e){yy.log("----ReplaceCmptCommand---undo---\x3e",e),yy.single.instance(p.default).setSelected("-1"),this.setSelectIds(e,!0,!1)},t.prototype.setSelectIds=function(e,t,n){var o=yy.single.instance(p.default);console.warn("setSelectIds=======>",e,t,n);for(var i=0,r=e;i<r.length;i++){var a=r[i];o.setSelected(a,t||e.length>1)}if(1===e.length&&n){var l,u=o.getDisplayGroupID(e[0]);"-1"!==u?(o.selectId=u,o.selectSubId=e[0],o.setSelected(u,!1),o.setSelected(e[0],!0),(l=o.getDisplayObjectById(e[0]))&&l.type===c.CmptType.LABEL&&l.setpikerviewStatue(2)):(o.selectId=e[0],o.selectSubId="-1",(l=o.getDisplayObjectById(e[0]))&&l.type===c.CmptType.LABEL&&l.setpikerviewStatue(2))}yy.single.instance(s.default).selectedDisplayObject(o.selectedIds)},t.prototype.getSubIdsExceptGroupId=function(e){for(var t=yy.single.instance(p.default),n=[],o=[],i=!1,a=0;a<e.length;a++){var s=e[a],l=t.getDisplayObjectById(s);if(console.warn("getSubIdsExceptGroupId",l),l.type===c.CmptType.GROUP){var u=l.groupIds,d=r.default.isMixedBy2Arr(e,u);d.length>0&&(n.push(s),d.forEach(function(e){o.push(e)}))}else t.hasOwnedGroup(s,e)||(i=!0)}var y=[];if(i){var f=function(t){var n=e[t];o.find(function(e){return e===n})||y.push(n)};for(a=0;a<e.length;a++)f(a)}else{var h=function(t){var o=e[t];n.find(function(e){return e===o})||y.push(o)};for(a=0;a<e.length;a++)h(a)}return y},t}(a.default);n.ReplaceCmptCommand=u,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../../qte/core/utils/ValueUtils":void 0,"../../../../cmpt/AnimTouchHandler":"AnimTouchHandler","../../../../common/EditorEnum":"EditorEnum","../../../display/DisplayObjectManager":"DisplayObjectManager","../../../proxy/DataCenterBridge":"DataCenterBridge"}],SetActiveActionCommand:[function(e,t,n){"use strict";cc._RF.push(t,"07da6iCo61AJpRSd1l9P3E1","SetActiveActionCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.SetActiveActionCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){if(e.actionId&&""!==e.actionId){var t=yy.single.instance(s.default),n=yy.single.instance(a.default);this.oldBody={actionId:n.customLineData._editActionId},n.updateFragmentsMap(),n.showAllPah(t.selectedIds,e.actionId)}},t.prototype.undo=function(e){var t=yy.single.instance(s.default),n=yy.single.instance(a.default);n.updateFragmentsMap(),n.showAllPah(t.selectedIds,e.actionId)},t}(r.default);n.SetActiveActionCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],SetFragmentCommand:[function(e,t,n){"use strict";cc._RF.push(t,"354ea06VW1Gao5tlJkQWQbI","SetFragmentCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.SetFragmentCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default),n=yy.single.instance(s.default).selectedIds;this.oldBody={fragmentId:t._editFragment},t.updateFragmentsMap(),t.setFragmentId(e.fragmentId),t.showAllPah(n)},t.prototype.undo=function(e){var t=yy.single.instance(a.default),n=yy.single.instance(s.default).selectedIds;t.updateFragmentsMap(),t.setFragmentId(e.fragmentId),t.showAllPah(n)},t}(r.default);n.SetFragmentCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],SpineAction:[function(e,t,n){"use strict";cc._RF.push(t,"b73539Vua1OeZN0mpoyJ3zP","SpineAction");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("./BaseAction"),a=e("./../display/base/DisplaySpine"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.play=function(e,t,n){var o=this,i=t.value,r=t.after,s=t.delay;e.active=!0;var c=i.anim,p=null,l=e.getComponent(a.default);p=e.getComponentInChildren(sp.Skeleton),setTimeout(function(){""!==c.animName?(p.setCompleteListener(function(){o.complete(l,r,p,n)}),p.timeScale=c.timeScale,p.setAnimation(0,c.animName,!1)):o.complete(l,r,p,n)},1e3*s)},t.prototype.complete=function(e,t,n,o){e.addListener(n),e.setTimeScale(t.endTimeScale),e.setLoop(t.loop),e.setAnimation(t.animList),o&&o()},t}(r.default);n.default=s,cc._RF.pop()},{"./../display/base/DisplaySpine":"DisplaySpine","./BaseAction":"BaseAction"}],SpineManager:[function(e,t,n){"use strict";cc._RF.push(t,"a6decE0cFlNTqL52n88MVqU","SpineManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../../qte/core/component/spine/SpineRect"),c=e("../../../qte/core/base/SingleBase"),p=e("../../../qte/core/utils/ValueUtils"),l=e("../../../qte/core/extension/command/CommandManager"),u=e("../../common/EditorEnum"),d=e("../command/simple/UpdateProp2VueCmd"),y=e("./base/DisplaySpine"),f=e("./DisplayObjectManager"),h=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initSpineData=function(e,t,n){return r(this,void 0,void 0,function(){var o=this;return a(this,function(){return[2,new Promise(function(i){var r=yy.single.instance(f.default),a=null,c=(a=r.getDisplayObjectById(e)).node.getChildByName("spineNode"),u=c.getComponent(s.default),y=c.getComponent(sp.Skeleton);!y||y.skeletonData?(a.spineData=n,a.size=c.getContentSize(),a.node.width=a.size.width,a.node.height=a.size.height,a.addListener(y),u.calcOffset(function(){setTimeout(function(){a.anchor=p.default.setOneDecimal(u.getAnchor(),2),(a.size.width>1280||a.size.height>720)&&(a.anchor=cc.v2(.5,.5)),a.resetSpinePos(),o.setProperty(a,t),a.refreshRectPoint(),yy.single.instance(l.default).executeCommand(d.default,{selectIds:[a.cid],isUpdateGroupData:!1,notUpdate2Vue:!1,notRunCmd:!0}),a.initFinished(),i()},20)})):i()})]})})},t.prototype.setProperty=function(e,t){var n={};for(var o in t)"x"!==o&&"y"!==o&&("scaleX"!==o?"scaleY"!==o?"loop"===o&&!0===t[o]||(n[o]=t[o]):e.node.height*=t.scaleY:e.node.width*=t.scaleX);yy.single.instance(f.default).updateDisplayObject(e.cid,n)},t.prototype.playActionByCmptLayer=function(e){var t=this;yy.single.instance(f.default).dpMap.forEach(function(n){var o=n;if(o.type===u.CmptType.SPINE){var i=t.getNodePath(o.node,u.CmptLayer.OBJECT_LAYER),r=cc.find(i,e);if(r){var a=r.getComponent(y.default);a.addListener(a.getComponentInChildren(sp.Skeleton)),a.setLoop(o.loop),a.setTimeScale(o.timeScale),a.setAnimation(o.actionList)}}})},t.prototype.getSpineProperties=function(e){var t=null,n=(t=yy.single.instance(f.default).getDisplayObjectById(e)).node.getChildByName("spineNode");if(!t||!n)return null;var o={offsetX:0,offsetY:0,scaleX:1,scaleY:1};return o.scaleX=n.scaleX,o.scaleY=n.scaleY,o.offsetX=t.offset.x,o.offsetY=t.offset.y,o},t.prototype.getNodePath=function(e,t){for(var n="";e&&e.name!==t;)n=n?e.name+"/"+n:e.name,e=e.parent;return n},t}(c.SingleBase);n.default=h,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../../qte/core/component/spine/SpineRect":void 0,"../../../qte/core/extension/command/CommandManager":void 0,"../../../qte/core/utils/ValueUtils":void 0,"../../common/EditorEnum":"EditorEnum","../command/simple/UpdateProp2VueCmd":"UpdateProp2VueCmd","./DisplayObjectManager":"DisplayObjectManager","./base/DisplaySpine":"DisplaySpine"}],StageController:[function(e,t,n){"use strict";cc._RF.push(t,"b42beLz+RJHiLYXIV4IiavT","StageController");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../qte/core/base/SingleBase"),c=e("../cmpt/TouchHandlerCmpt"),p=e("../common/EditorEnum"),l=e("../core/animation/AnimationManager"),u=e("../core/display/base/edit/CutShapeManager"),d=e("../core/display/base/SvgShapeManager"),y=e("../core/display/CocosAniManager"),f=e("../core/display/SpineManager"),h=e("../core/proxy/DataCenterBridge"),m=e("../core/display/DisplayObjectManager"),g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._actionPlaying=!1,t}return i(t,e),t.prototype.register=function(e){this._cmpt=e,this.onInitStart()},t.prototype.changeAutoSubmit=function(e){var t=cc.find("Canvas");if(t){var n=t.getChildByName("stage_prefab");if(n){var o=n.getChildByName("com_BtnNextAndSubmit");o&&(o.getChildByName("com_btnSubmit").active=!e)}}},t.prototype.changeAutoRest=function(e){var t=cc.find("Canvas");if(t){var n=t.getChildByName("stage_prefab");if(n){var o=n.getChildByName("com_BtnNextAndSubmit");o&&(o.getChildByName("com_btnReset").active=e)}}},t.prototype.updateBackground=function(e){var t=e.textureType;return void 0!==t&&(0===t?t=cc.Sprite.Type.SIMPLE:2===t&&(t=cc.Sprite.Type.TILED)),this._cmpt.updateBackground(e.bgColor,e.texture,t)},t.prototype.playFragmentAnimation=function(e){if(this.onEnterAnimEdit(),yy.log("==============playStageAnimationStep=============="),!this._actionPlaying){this._actionPlaying=!0;var t=this.initTopLayer();yy.single.instance(l.default).playStep(t,e,this.playFinished.bind(this))}},t.prototype.playGroupAnim=function(e){if(this.onEnterAnimEdit(),!this._actionPlaying&&e){yy.log("==============playStageAnimation=============="),this._actionPlaying=!0;var t=this.initTopLayer();yy.single.instance(l.default).play(t,e,this.playFinished.bind(this))}},t.prototype.stopAnimation=function(){clearTimeout(this._settimeoutId),this._settimeoutId=null,this.clearTopLayer()},t.prototype.cancleStageMouse=function(){this._cmpt&&this._cmpt.touchLayer&&this._cmpt.touchLayer.getComponent(c.default).cancleStageMouse()},t.prototype.initTopLayer=function(){this._cmpt.touchLayer.getComponent(c.default).unregistEvent();var e=cc.instantiate(this._cmpt.backgroundLayer);e.name="clone_bg",this._cmpt.addDisplayObject(e,p.CmptLayer.TOP_LAYER);var t=cc.instantiate(this._cmpt.displayObjectLayer);return t.name="clone_obj",this._cmpt.addDisplayObject(t,p.CmptLayer.TOP_LAYER),yy.single.instance(f.default).playActionByCmptLayer(t),yy.single.instance(y.default).playActionByCmptLayer(t),yy.single.instance(u.default).reloadShap(t),yy.single.instance(d.default).reloadSvgShap(t),t},t.prototype.playFinished=function(e){var t=this;this._settimeoutId=setTimeout(function(){t.clearTopLayer(),e===p.AnimPreviewType.ALL?yy.single.instance(h.default).stopAnimatioinCommit():yy.single.instance(h.default).stopFragmentCommit(),t.onExitAnimEdit()},1500)},t.prototype.clearTopLayer=function(){this._cmpt.removeChildFromLayer("clone_bg",p.CmptLayer.TOP_LAYER),this._cmpt.removeChildFromLayer("clone_obj",p.CmptLayer.TOP_LAYER),this._cmpt.touchLayer.getComponent(c.default).registEvent(),this._actionPlaying=!1},t.prototype.screenshot=function(){return r(this,void 0,void 0,function(){var e=this;return a(this,function(){return[2,new Promise(function(t){e._cmpt.screenshootShow(),setTimeout(function(){t(e._cmpt.screenshoot())},100)})]})})},t.prototype.screenshotSignal=function(e){var t=this;return new Promise(function(n){yy.single.instance(m.default).setAllObjectSignalView(!0,e);var o=t;setTimeout(function(){var t=o._cmpt.screenshoot();setTimeout(function(){yy.single.instance(m.default).setAllObjectSignalView(!1,e),n(t)},100)},100),o._cmpt.screenshootShow()})},t.prototype.hideComponentShot=function(e){var t=this;return new Promise(function(n){for(var o=0;o<e.length;o++)yy.single.instance(m.default).getDisplayObjectById(e[o]+"").setActive(!1);t._cmpt.screenshootShow(),setTimeout(function(){n(t._cmpt.screenshoot());for(var o=0;o<e.length;o++)yy.single.instance(m.default).getDisplayObjectById(e[o]+"").setActive(!0)},100)})},t.prototype.hideComponentShotWithSignal=function(e,t){var n=this;return new Promise(function(o){yy.single.instance(m.default).setAllObjectSignalView(!0,e);for(var i=0;i<t.length;i++)yy.single.instance(m.default).getDisplayObjectById(t[i]+"").setActive(!1);var r=n;setTimeout(function(){var n=r._cmpt.screenshoot();setTimeout(function(){yy.single.instance(m.default).setAllObjectSignalView(!1,e);for(var i=0;i<t.length;i++)yy.single.instance(m.default).getDisplayObjectById(t[i]+"").setActive(!0);o(n)},100)},100),r._cmpt.screenshootShow()})},t.prototype.onInitStart=function(){yy.log("----- onInitStart -----")},t.prototype.onInitFinished=function(){yy.log("----- onInitFinished -----"),yy.single.instance(h.default).cocosInitFinished()},t.prototype.onEnterAnimEdit=function(){yy.log("----- onEnterAnimEdit -----")},t.prototype.onExitAnimEdit=function(){yy.log("----- onExitAnimEdit -----")},t.prototype.onDestoryInstance=function(){this._cmpt=null},t}(s.SingleBase);n.default=g,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../cmpt/TouchHandlerCmpt":"TouchHandlerCmpt","../common/EditorEnum":"EditorEnum","../core/animation/AnimationManager":"AnimationManager","../core/display/CocosAniManager":"CocosAniManager","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/SpineManager":"SpineManager","../core/display/base/SvgShapeManager":"SvgShapeManager","../core/display/base/edit/CutShapeManager":"CutShapeManager","../core/proxy/DataCenterBridge":"DataCenterBridge"}],StageLayerCmpt:[function(e,t,n){"use strict";cc._RF.push(t,"36ac5+HGQJGG7v4jiq7HgUp","StageLayerCmpt");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a},a=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=e("../common/EditorEnum"),p=e("../core/display/DisplayObjectManager"),l=e("../core/display/TemplateInterpreter"),u=e("../core/proxy/DataCenterBridge"),d=e("./StageController"),y=cc._decorator,f=y.ccclass,h=y.property,m=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.shootCameraNode=null,t.backgroundLayer=null,t.displayObjectLayer=null,t.animDisplayLayer=null,t.touchLayer=null,t.topLayer=null,t.debugLayer=null,t}return i(t,e),t.prototype.onLoad=function(){yy.single.instance(d.default).register(this)},t.prototype.start=function(){return a(this,void 0,void 0,function(){var e,t,n;return s(this,function(o){switch(o.label){case 0:return console.log("\u5f00\u59cbcocos\u52a0\u8f7d\u7ec4\u4ef6\u5efa",(new Date).getTime()),e=yy.single.instance(u.default),(t=yy.single.instance(l.default)).registerLayer(this.addDisplayObject.bind(this)),yy.single.instance(u.default).cocosLoadCompListStart(),n=e.getStageInfo(),this.addBackground(n.texture,n.bgColor,n),console.log(window._$store.state.template),this.shootCameraNode.active=!1,[4,t.generate(e.getComponentMap(),e.getComponentIds())];case 1:return o.sent(),yy.single.instance(d.default).onInitFinished(),console.log("cocos\u7ec4\u4ef6\u52a0\u8f7d\u5b8c\u6210",(new Date).getTime()),this.schedule(this.checkAllCompLoadFinish,.1),[2]}})})},t.prototype.checkAllCompLoadFinish=function(){var e,t=yy.single.instance(u.default),n=yy.single.instance(l.default);if(!cc.isValid(n,!0))return this.unschedule(this.checkAllCompLoadFinish),void(null===(e=yy.single.instance(u.default))||void 0===e||e.cocosLoadCompListFinished());n.checkCompLoadFinish(t.getComponentMap(),t.getComponentIds())&&(this.unschedule(this.checkAllCompLoadFinish),n.initAllComponentsFinish(t.getComponentMap(),t.getComponentIds()),console.log("\u5168\u90e8\u7ec4\u4ef6\u52a0\u8f7d\u5b8c\u6210\u56de\u8c03\u4e86"),yy.single.instance(u.default).cocosLoadCompListFinished(),cc.game.setFrameRate(30))},t.prototype.addBackground=function(e,t,n){this.backgroundLayer.width=n.width,this.backgroundLayer.height=n.height,this.updateBackground(t,e,cc.Sprite.Type.SIMPLE)},t.prototype.updateBackground=function(e,t){var n={},o=this.backgroundLayer.getChildByName("img");return e&&""!==e?(n.bgColor=e,this.backgroundLayer.color=(new cc.Color).fromHEX(yy.checkValue(e,"#ffffff")),o.active=!1,n):(this.backgroundLayer.color=(new cc.Color).fromHEX("#ffffff"),o.active=!0,n.texture=o.__textureUrl__,t&&""!==t?cc.assetManager.loadRemote(t,function(e,n){e?yy.warn(e):(o.__textureUrl__=t,o.getComponent(cc.Sprite).spriteFrame=new cc.SpriteFrame(n))}):(o.getComponent(cc.Sprite).spriteFrame=null,o.__textureUrl__=""),o.getComponent(cc.Sprite).sizeMode=cc.Sprite.SizeMode.RAW,n)},t.prototype.addDisplayObject=function(e,t){var n=this.getDisplayLayer(t),o=this.topLayer.getChildByName(e.name);o&&o.destroy(),n.addChild(e)},t.prototype.removeChildFromLayer=function(e,t){var n=this.getDisplayLayer(t).getChildByName(e);n&&n.destroy()},t.prototype.showDebug=function(){for(var e=this.debugLayer.getComponent(cc.Graphics),t=yy.single.instance(p.default).dpMap,n=0,o=Array.from(t.keys());n<o.length;n++){var i=o[n],r=t.get(i);e.moveTo(r.rect.x,r.rect.y),e.lineTo(r.rect.x,r.rect.y+r.rect.height),e.lineTo(r.rect.x+r.rect.width,r.rect.y+r.rect.height),e.lineTo(r.rect.x+r.rect.width,r.rect.y),e.lineTo(r.rect.x,r.rect.y),e.stroke()}},t.prototype.onDestroy=function(){yy.single.destory(d.default)},t.prototype.getDisplayLayer=function(e){switch(e){case c.CmptLayer.OBJECT_LAYER:return this.displayObjectLayer;case c.CmptLayer.ANIM_LAYER:return this.animDisplayLayer;case c.CmptLayer.TOUCH_LAYER:return this.touchLayer;case c.CmptLayer.TOP_LAYER:return this.topLayer;default:return this.displayObjectLayer}},t.prototype.screenshootHide=function(){this.shootCameraNode.active=!1},t.prototype.screenshootShow=function(){this.shootCameraNode.active=!0},t.prototype.screenshoot=function(){this.unschedule(this.screenshootHide);var e=this.shootCameraNode.getComponent(cc.Camera),t=new cc.RenderTexture,n=cc.game._renderContext;return t.initWithSize(cc.visibleRect.width,cc.visibleRect.height,n.DEPTH_STENCIL),e.targetTexture=t,e.render(),this.scheduleOnce(this.screenshootHide,1),t},r([h({type:cc.Node,tooltip:"\u622a\u56fe\u6444\u50cf\u673a"})],t.prototype,"shootCameraNode",void 0),r([h({type:cc.Node,tooltip:"\u80cc\u666f\u5c42"})],t.prototype,"backgroundLayer",void 0),r([h({type:cc.Node,tooltip:"\u663e\u793a\u5bf9\u8c61\u5c42"})],t.prototype,"displayObjectLayer",void 0),r([h({type:cc.Node,tooltip:"\u52a8\u753b\u5c42"})],t.prototype,"animDisplayLayer",void 0),r([h({type:cc.Node,tooltip:"\u4e8b\u4ef6\u5c42"})],t.prototype,"touchLayer",void 0),r([h({type:cc.Node,tooltip:"\u6700\u9ad8\u5bf9\u8c61\u5c42"})],t.prototype,"topLayer",void 0),r([h({type:cc.Node,tooltip:"\u8c03\u8bd5\u5c42"})],t.prototype,"debugLayer",void 0),r([f],t)}(cc.Component);n.default=m,cc._RF.pop()},{"../common/EditorEnum":"EditorEnum","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/TemplateInterpreter":"TemplateInterpreter","../core/proxy/DataCenterBridge":"DataCenterBridge","./StageController":"StageController"}],StageSceneCmpt:[function(e,t,n){"use strict";cc._RF.push(t,"578f1LIzj9G5r86bhQoCj4o","StageSceneCmpt");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../core/proxy/DataCenterBridge"),s=cc._decorator.ccclass,c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.start=function(){var e=this;cc.resources.load("prefabs/stage_prefab",cc.Prefab,function(t,n){var o=yy.single.instance(a.default).getStageInfo(),i=cc.size(o.safeWidth,o.safeHeight),r=cc.instantiate(n);r.width=i.width,r.height=i.height,e.node.addChild(r);var s=window._$store.state.extraStageData.isAutoSubmit,c=window._$store.state.extraStageData.hasRecover;console.log("_isAutoSubmit",s,"_hasRecover",c),yy.single.instance(a.default).cocosLoadStageRootNodeFinished();var p=r.getChildByName("com_BtnNextAndSubmit");console.log("-----stageNode---\x3e",r),console.log("-----btn---\x3e",p),p.getChildByName("com_btnSubmit").active=!s,p.getChildByName("com_btnReset").active=c})},r([s],t)}(cc.Component);n.default=c,cc._RF.pop()},{"../core/proxy/DataCenterBridge":"DataCenterBridge"}],SvgShapeManager:[function(e,t,n){"use strict";cc._RF.push(t,"c4187XbnAtLvI7VD9QbbO1L","SvgShapeManager");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../qte/core/base/SingleBase"),a=e("../../../common/EditorEnum"),s=e("../DisplayObjectManager"),c=e("./DisplaySvg"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.reloadSvgShap=function(e){var t=this;yy.single.instance(s.default).dpMap.forEach(function(n){if(n.type===a.CmptType.SVGSHAPE){var o=n,i=t.getNodePath(o.node,a.CmptLayer.OBJECT_LAYER),r=cc.find(i,e);r.active&&r.getComponent(c.default).onInit(o.shapeDatas)}})},t.prototype.setProperty=function(e){yy.single.instance(s.default).updateDisplayObject(e.cid,{})},t.prototype.getNodePath=function(e,t){for(var n="";e&&e.name!==t;)n=n?e.name+"/"+n:e.name,e=e.parent;return n},t}(r.SingleBase);n.default=p,cc._RF.pop()},{"../../../../qte/core/base/SingleBase":void 0,"../../../common/EditorEnum":"EditorEnum","../DisplayObjectManager":"DisplayObjectManager","./DisplaySvg":"DisplaySvg"}],SvgTouchHandler:[function(e,t,n){"use strict";cc._RF.push(t,"d46a7pi2pVP9KABzKCsM24b","SvgTouchHandler");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../qte/core/base/SingleBase"),s=e("../common/EditorEnum"),c=e("../core/display/base/cmpt/DisplaySelect"),p=e("../core/display/base/DisplaySvg"),l=e("../core/display/DisplayObjectManager"),u=cc._decorator.ccclass,d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.initInstance=function(){this._displayObjectMgr=yy.single.instance(l.default)},t.prototype.onTouchStart=function(e,t){var n=this._displayObjectMgr.getDisplayObjectById(e),o=t.getLocation(),i=cc.v2(o.x-cc.winSize.width/2,o.y-cc.winSize.height/2);if("-1"===e){for(var r=0;r<this._displayObjectMgr.selectedIds.length;r++){var a=this._displayObjectMgr.selectedIds[r],p=this._displayObjectMgr.getDisplayObjectById(a);if(p.type===s.CmptType.SVG){var l=p.clickVertexBtn(i);if(l)return l;p.checkVertexType()&&p.editorToRect()}}return!1}if(n&&"-1"!==e&&n.type===s.CmptType.SVG){var u=n.getVertexIndex(cc.v2(i.x,i.y));if(n.checkVertexType()){if(null!==u&&u!==c.SEL_POINT_ENUM.NONE)return!0;n.setSelected(!0)}else n.vertexBtnView();n.editorToRect()}return!1},t.prototype.onTouchMove=function(e,t){var n=this._displayObjectMgr.getDisplayObjectById(e);if(n&&"-1"!==e&&n.type===s.CmptType.SVG&&n.checkVertexType()){var o=t.getLocation(),i=cc.v2(o.x-cc.winSize.width/2,o.y-cc.winSize.height/2),r=n.getVertexIndex(cc.v2(i.x,i.y));if(null===r||r===c.SEL_POINT_ENUM.NONE)return;return n.changeShapeSvg(r,cc.v3(i.x,i.y)),this._selectId=e,e}return!1},t.prototype.onTouchEnd=function(){var e=this._displayObjectMgr.getDisplayObjectById(this._selectId);if(e&&e.type===s.CmptType.SVG){var t=e;t.editorType===p.EditorType.vertex&&(t.displayVertex.chooseIndex=null)}return!1},r([u],t)}(a.SingleBase);n.default=d,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../common/EditorEnum":"EditorEnum","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/base/DisplaySvg":"DisplaySvg","../core/display/base/cmpt/DisplaySelect":"DisplaySelect"}],TemplateInterpreter:[function(e,t,n){"use strict";cc._RF.push(t,"b568cIZq2xNcqJy5a75INXO","TemplateInterpreter");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},a=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var s=e("../../../qte/core/base/SingleBase"),c=e("../../common/EditorEnum"),p=e("./base/DisplayObject"),l=e("./base/DisplayObjectGroup"),u=e("./DisplayObjectFactory"),d=e("./DisplayObjectManager"),y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),Object.defineProperty(t.prototype,"addObjFunc",{get:function(){return this._addObjFunc},enumerable:!1,configurable:!0}),t.prototype.registerLayer=function(e){this._addObjFunc=e},t.prototype.generate=function(e,t){return r(this,void 0,void 0,function(){var n,o,i,r,s,y,f,h,m,g,_,v,b,C,O;return a(this,function(a){switch(a.label){case 0:n=yy.single.instance(u.default),o=yy.single.instance(d.default),qte.displayObjectManager=o,console.log("#------comptsData->",e),console.log("#------comptIds->",t),i=0,a.label=1;case 1:return i<t.length?"string"!=typeof(r=t[i])?[3,4]:(s=e[r],[4,n.getDisplayNode(s,!0)]):[3,17];case 2:return y=a.sent(),console.log("#------com->",s),this._addObjFunc(y,c.CmptLayer.OBJECT_LAYER),o.addDisplayObject(s.id,y.getComponent(p.default)),[4,n.spineCheckData(s)];case 3:return a.sent(),[3,16];case 4:return s=e[r.id],y=null,"group"!=s.type?[3,11]:[4,n.getDisplayNode(s,!0)];case 5:y=a.sent(),this._addObjFunc(y,c.CmptLayer.OBJECT_LAYER),f=y.getComponent(l.default),h=f.node,o.addDisplayObject(s.id,f),m=0,g=r.subIds,a.label=6;case 6:return m<g.length?(_=g[m],(v=e[_]).cName?[3,9]:[4,n.getDisplayNode(v,!0)]):[3,10];case 7:return b=a.sent(),h.addChild(b),f.addSubObject(_,b.getComponent(p.default)),o.addDisplayObject(_,b.getComponent(p.default)),[4,n.spineCheckData(v)];case 8:a.sent(),a.label=9;case 9:return m++,[3,6];case 10:return[3,14];case 11:return[4,n.getDisplayNode(s,!0)];case 12:return y=a.sent(),console.log("#--222----com->",s),this._addObjFunc(y,c.CmptLayer.OBJECT_LAYER),o.addDisplayObject(s.id,y.getComponent(p.default)),[4,n.spineCheckData(s)];case 13:a.sent(),a.label=14;case 14:return s.childComponents&&s.childComponents[0]?(y||console.error("\u63d2\u69fd\u7236\u8282\u70b9\u4e3a\u7a7a"),C=y.getComponent(p.default),[4,n.getDisplayNode(s.childComponents[0],!0)]):[3,16];case 15:(O=a.sent()).parent=y,C.addChildObject(s.childComponents[0].id,O.getComponent(p.default)),o.addDisplayObject(s.childComponents[0].id,O.getComponent(p.default)),a.label=16;case 16:return i++,[3,1];case 17:return[2]}})})},t.prototype.checkCompLoadFinish=function(e,t){for(var n=yy.single.instance(d.default),o=0,i=0;i<t.length;i++){var r=t[i];if("string"==typeof r)(y=n.getDisplayObjectById(r))&&y.isInitFinished&&o++;else if("group"==e[r.id].type){var a=0,s=n.getDisplayObjectById(r.id);s&&s.isInitFinished&&a++;for(var c=0,p=r.subIds;c<p.length;c++){var l=p[c];if(!e[l].cName){var u=n.getDisplayObjectById(l);u&&u.isInitFinished&&a++}}a>=r.subIds.length+1&&o++}else{var y;(y=n.getDisplayObjectById(r))&&y.isInitFinished&&o++}}return o>=t.length},t.prototype.initAllComponentsFinish=function(e,t){for(var n=yy.single.instance(d.default),o=0;o<t.length;o++){var i=t[o];"string"==typeof i?n.getDisplayObjectById(i).initAllComponentsFinish():(e[i.id].type,n.getDisplayObjectById(i).initAllComponentsFinish())}},t}(s.SingleBase);n.default=y,cc._RF.pop()},{"../../../qte/core/base/SingleBase":void 0,"../../common/EditorEnum":"EditorEnum","./DisplayObjectFactory":"DisplayObjectFactory","./DisplayObjectManager":"DisplayObjectManager","./base/DisplayObject":"DisplayObject","./base/DisplayObjectGroup":"DisplayObjectGroup"}],TestScript:[function(e,t,n){"use strict";cc._RF.push(t,"481c6ZfwC1H+6wTZKRcATAY","TestScript");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a},a=this&&this.__awaiter||function(e,t,n,o){return new(n||(n=Promise))(function(i,r){function a(e){try{c(o.next(e))}catch(t){r(t)}}function s(e){try{c(o.throw(e))}catch(t){r(t)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n(function(e){e(t)})).then(a,s)}c((o=o.apply(e,t||[])).next())})},s=this&&this.__generator||function(e,t){var n,o,i,r,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(e){return function(t){return c([e,t])}}function c(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,o&&(i=2&r[0]?o.return:r[0]?o.throw||((i=o.return)&&i.call(o),0):o.next)&&!(i=i.call(o,r[1])).done)return i;switch(o=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,o=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(i=(i=a.trys).length>0&&i[i.length-1])&&(6===r[0]||2===r[0])){a=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){a.label=r[1];break}if(6===r[0]&&a.label<i[1]){a.label=i[1],i=r;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(r);break}i[2]&&a.ops.pop(),a.trys.pop();continue}r=t.call(e,a)}catch(s){r=[6,s],o=0}finally{n=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}};Object.defineProperty(n,"__esModule",{value:!0});var c=e("./AssetsManager"),p=e("./core/proxy/DataCenterBridge"),l=e("./core/proxy/ExtraModule"),u=e("./core/proxy/MockData"),d=e("./stage/CocosExport"),y=e("./stage/StageSceneCmpt"),f=cc._decorator,h=f.ccclass,m=f.property,g=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.testNode=null,t.cccc="cccc",t}return i(t,e),t.prototype.start=function(){d.CocosExport.initExport(),u.default.init(),this.init()},t.prototype.init=function(){return a(this,void 0,void 0,function(){return s(this,function(e){switch(e.label){case 0:return console.log("(window as any)._$store.state.template==",window._$store.state.template),console.log("%c Line:33 \ud83e\udd54 y.single.instance(ExtraModule).bootData","color:#3f7cff",yy.single.instance(l.default).bootData),yy.single.instance(l.default).bootData?[4,yy.single.instance(c.default).loadBundel(Object.values(yy.single.instance(l.default).bootData.resourceMap)[0])]:[3,2];case 1:e.sent(),e.label=2;case 2:return this.runToStageScene(),[2]}})})},t.prototype.runToStageScene=function(){var e=yy.single.instance(p.default).getStageInfo(),t=yy.single.instance(p.default).getTemplate();qte.getTemplateByKey=function(e){return t[e]},qte.openMode=qte.QTE_OPENMODE.SHOW_NORMAL;var n=cc.size(e.safeWidth,e.safeHeight),o=new cc.Scene;o.autoReleaseAssets=!0,o.name="Scene";var i=new cc.Node;i.name="Canvas",i.width=n.width,i.height=n.height;var r=i.addComponent(cc.Canvas);r.designResolution=n,r.fitHeight=!1,r.fitWidth=!1,o.addChild(i),r.addComponent(y.default),setTimeout(function(){console.log("\u821e\u53f0\u521b\u5efa\u5b8c\u6210InitSceneCmptFinish",(new Date).getTime()),cc.director.runSceneImmediate(o)},1),cc.game.setFrameRate(30)},r([m(cc.Node)],t.prototype,"testNode",void 0),r([h],t)}(cc.Component);n.default=g,cc._RF.pop()},{"./AssetsManager":"AssetsManager","./core/proxy/DataCenterBridge":"DataCenterBridge","./core/proxy/ExtraModule":"ExtraModule","./core/proxy/MockData":"MockData","./stage/CocosExport":"CocosExport","./stage/StageSceneCmpt":"StageSceneCmpt"}],TextToImgCommand:[function(e,t,n){"use strict";cc._RF.push(t,"13dcfSMc81GbZEJtz1hB1gh","TextToImgCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../proxy/DataCenterBridge"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=e.selectId,n=e.texture;yy.single.instance(a.default).textToImg({selectId:t,texture:n})},t.prototype.undo=function(e){yy.log(e)},t}(r.default);n.default=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../proxy/DataCenterBridge":"DataCenterBridge"}],TouchHandlerCmpt:[function(e,t,n){"use strict";cc._RF.push(t,"a8df9IFzihOro7kpvXaBbQs","TouchHandlerCmpt");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../qte/core/utils/MathUtil"),s=e("../Config"),c=e("../core/collision/CollisionManager"),p=e("../core/command/operate/base/ReplaceCmptCommand"),l=e("../core/command/operate/CommandFactory"),u=e("../core/display/base/cmpt/DisplaySelect"),d=e("../core/display/base/edit/CutShapeManager"),y=e("../core/display/DisplayObjectManager"),f=e("../core/proxy/DataCenterBridge"),h=e("../utils/CusorStyle"),m=e("./AnimTouchHandler"),g=e("./EditBoxTouchHandler"),_=e("./MoveTouchHandler"),v=e("./SvgTouchHandler"),b=cc._decorator.ccclass,C=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._multiSelect=!1,t._mouseBeginPos=null,t._mouseEndPos=null,t._isHoldOnMouseLeft=!1,t._lastSelectTime=0,t._keydownEvent=null,t._keyupEvent=null,t._mousedownEvent=null,t._mouseLastPos=null,t}return i(t,e),t.prototype.onLoad=function(){this._displayManager=yy.single.instance(y.default),this._dataCenterBridge=yy.single.instance(f.default),this._collisionManager=yy.single.instance(c.default),this._animTouchHandler=yy.single.instance(m.default),this._editBoxTouchHandler=yy.single.instance(g.default),this._svgTouchHandler=yy.single.instance(v.default),this._moveHandler=yy.single.instance(_.default)},t.prototype.start=function(){var e,t=this;this.registEvent(),this._keydownEvent=function(n){window.cocos.vueHandeKeyDown&&window.cocos.vueHandeKeyDown(n),window.cocos.showSocpedComponent||(console.warn("cocos keyboard",n.key),16===n.keyCode||17===n.keyCode||91===n.keyCode?(t._multiSelect=!0,e=n.keyCode):t._multiSelect?t.onPressMultiKey(e,n.keyCode):t.onPressKey(n.keyCode))},this._keyupEvent=function(e){window.cocos.vuehandleKeyUp&&window.cocos.vuehandleKeyUp(e),window.cocos.showSocpedComponent||16!==e.keyCode&&17!==e.keyCode&&91!==e.keyCode||(t._multiSelect=!1)},this._mousedownEvent=function(e){window.cocos.showSocpedComponent||e.metaKey||e.ctrlKey||e.shiftKey||(t._multiSelect=!1)},window.document.addEventListener("keydown",this._keydownEvent,!0),window.document.addEventListener("keyup",this._keyupEvent,!0),window.document.addEventListener("mousedown",this._mousedownEvent,!0)},t.prototype.registEvent=function(){this.node.on(cc.Node.EventType.MOUSE_DOWN,this.mouseBegin,this),this.node.on(cc.Node.EventType.MOUSE_MOVE,this.mouseMove,this),this.node.on(cc.Node.EventType.MOUSE_UP,this.mouseUp,this),this.node.on(cc.Node.EventType.TOUCH_CANCEL,this.mouseUp,this),this.node.on(cc.Node.EventType.MOUSE_WHEEL,this.mouseWheel,this)},t.prototype.unregistEvent=function(){this.node.off(cc.Node.EventType.MOUSE_DOWN,this.mouseBegin,this),this.node.off(cc.Node.EventType.MOUSE_MOVE,this.mouseMove,this),this.node.off(cc.Node.EventType.MOUSE_UP,this.mouseUp,this),this.node.off(cc.Node.EventType.TOUCH_CANCEL,this.mouseUp,this),this.node.off(cc.Node.EventType.MOUSE_WHEEL,this.mouseWheel,this),window.document.removeEventListener("keydown",this._keydownEvent,!0),window.document.removeEventListener("keyup",this._keyupEvent,!0),window.document.removeEventListener("mousedown",this._mousedownEvent,!0)},t.prototype.isTouchEditId=function(e,t,n,o){if("-1"!==t&&e!==t&&(s=this._displayManager.getDisplayObjectById(t))&&(0!=yy.instance(y.default).isSubNode(t)&&(t=yy.instance(y.default).isSubNode(t),console.log("%c Line:163 \ud83c\udf77 editId","color:#33a5ff",t),s=this._displayManager.getDisplayObjectById(t)),a.default.isPosInRotationRect(n,s.rect,yy.checkValue(s.node.angle,0)))){var i=s.cid;if(console.log("%c Line:169 \ud83e\udd53 _id","color:#3f7cff",i),i&&t==i&&i.length>0)return i}if(o)for(var r=0;r<this._displayManager.selectedIds.length;r++){var s=this._displayManager.getDisplayObjectById(this._displayManager.selectedIds[r]);if(a.default.isPosInRotationRect(n,s.rect,yy.checkValue(s.node.angle,0)))return this._displayManager.selectedIds[r]}return e},t.prototype.mouseBegin=function(e){if(this._animTouchHandler.mouseDown(e),this._animTouchHandler.onTouchStart(e))this._displayManager.selectId="-1";else{this._mouseBeginPos=null,this._isHoldOnMouseLeft=!0;var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2);this._mouseLastPos=n;var o=this._displayManager.selectId;if("-1"!==this._displayManager.selectSubId&&(o=this._displayManager.selectSubId),!yy.single.instance(d.default).mouseBegin(n,o)){var i=this._displayManager.getDisplayObjectById(o);i&&i.node.children[0].emit(e.type+"_proxy",e);var r=this._collisionManager.testDisplayObject(n,this._dataCenterBridge.getComponentIds(),this._displayManager.dpMap);r=this._displayManager.selectedIds.length>0?this.isTouchEditId(r,o,n,!0):this.isTouchEditId(r,o,n),this._svgTouchHandler.onTouchStart(r,e)||("-1"===r&&(this._mouseBeginPos=n,this._mouseEndPos=null),this._moveHandler.mouseBegin(n,r,this._multiSelect),this._editBoxTouchHandler.mouseBegin(e,o,this._displayManager))}}},t.prototype.mouseUp=function(e){if(!this._animTouchHandler.mouseUp(e)){var t=this._displayManager.selectId;if("-1"!==this._displayManager.selectSubId&&(t=this._displayManager.selectSubId),!yy.single.instance(d.default).mouseUp(t)){var n=this._displayManager.getDisplayObjectById(t);n&&n.node.children[0].emit(e.type+"_proxy",e),this._editBoxTouchHandler.mouseUp(t,this._displayManager),this._isHoldOnMouseLeft=!1,this._svgTouchHandler.onTouchEnd(e);var o=e.getLocation(),i=o.x,r=cc.winSize.height-o.y,a=cc.v2(o.x-cc.winSize.width/2,o.y-cc.winSize.height/2);if(this._mouseLastPos=a,e.getButton&&e.getButton()===cc.Event.EventMouse.BUTTON_RIGHT){var s=this._collisionManager.testDisplayObject(a,this._dataCenterBridge.getComponentIds(),this._displayManager.dpMap);"-1"!==s?(s=this._displayManager.selectedIds.length>0?this.isTouchEditId(s,t,a,!0):this.isTouchEditId(s,t,a),this._displayManager.selectId=s,yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:[s],isVue:!1,isMulti:this._multiSelect}),this._dataCenterBridge.showContextMenu({id:s,left:i,top:r})):this._dataCenterBridge.showContextMenu({left:i,top:r})}var c=this._collisionManager.testDisplayObject(a,this._dataCenterBridge.getComponentIds(),this._displayManager.dpMap);c=this._displayManager.selectedIds.length>0?this.isTouchEditId(c,t,a,!0):this.isTouchEditId(c,t,a),0===this._lastSelectTime?this._lastSelectTime=(new Date).getTime()/1e3:((new Date).getTime()/1e3-this._lastSelectTime<.3&&(console.warn("\u4f60\u53cc\u51fb\u554a\uff01\uff01"),this._dataCenterBridge.doubleClick({id:c})),this._lastSelectTime=(new Date).getTime()/1e3),this.clearDrawRect(),this._moveHandler.mouseUp(a,this._multiSelect),cc.director.emit("REMOVE_ALL_LINE")}}},t.prototype.cancleStageMouse=function(){this._isHoldOnMouseLeft&&(this._isHoldOnMouseLeft=!1,this.clearDrawRect(),this._moveHandler.cancleStageMouse(this._mouseLastPos,this._multiSelect),cc.director.emit("REMOVE_ALL_LINE"))},t.prototype.mouseMove=function(e){if(!this._animTouchHandler.mouseMove(e)){var t=e.getLocation(),n=cc.v2(t.x-cc.winSize.width/2,t.y-cc.winSize.height/2),o=this._collisionManager.testDisplayObject(n,this._dataCenterBridge.getComponentIds(),this._displayManager.dpMap);this._mouseLastPos=n;var i=this._displayManager.selectId;if("-1"!==this._displayManager.selectSubId&&(i=this._displayManager.selectSubId),!yy.single.instance(d.default).mouseMove(n,i)){var r=this._displayManager.getDisplayObjectById(i);if(r&&r.node.children[0].emit(e.type+"_proxy",e),!(this._editBoxTouchHandler.mouseMove(e,i,this._displayManager)||this._isHoldOnMouseLeft&&this._svgTouchHandler.onTouchMove(this._displayManager.selectId,e))){this._isHoldOnMouseLeft&&this._moveHandler.mouseMove(e);var a="default";if("-1"!==o&&(a="move"),"-1"!==this._displayManager.selectId){for(var c=null,p=0,l=-1,y=0;y<this._displayManager.selectedIds.length;y++){var f=this._displayManager.selectedIds[y],m=this._displayManager.getDisplayObjectById(f),g=m.inSelRect(n);if(null!==g){var _=m.node.getSiblingIndex();0!=m.dragable&&_>l&&(c=g,l=_,p=m.getNodeAngle())}}c&&(a=c.data.type===u.SEL_POINT_ENUM.ROTATE?s.default.CUSTOM_CURSOR:h.default._getCursorTyle(c.data.type,p),this._mouseBeginPos=null,this.clearDrawRect())}this._mouseEndPos=n,this.drawSelectRect(),cc.game.canvas.style.cursor=a}}}},t.prototype.mouseWheel=function(e){this._editBoxTouchHandler.mouseWheel(e,this._displayManager.selectId,this._displayManager)},t.prototype.drawSelectRect=function(){if(this._mouseBeginPos&&this._mouseEndPos){var e=a.default.getMinMaxXY(this._mouseBeginPos,this._mouseEndPos),t=e.minX,n=e.maxX,o=e.minY,i=e.maxY,r=this.node.getChildByName("draw_rect");r||((r=new cc.Node).addComponent(cc.Graphics),r.name="draw_rect",this.node.addChild(r));var s=r.getComponent(cc.Graphics);s.lineWidth=3,s.strokeColor=cc.Color.CYAN,s.clear(),s.moveTo(t,o),s.lineTo(t,i),s.lineTo(n,i),s.lineTo(n,o),s.lineTo(t,o),s.stroke()}},t.prototype.clearDrawRect=function(){var e=this,t=this.node.getChildByName("draw_rect");t&&t.removeFromParent();var n=function(){e._mouseBeginPos=null,e._mouseEndPos=null};if(this._mouseBeginPos&&this._mouseEndPos){var o=a.default.getMinMaxXY(this._mouseBeginPos,this._mouseEndPos),i=o.minX,r=o.maxX,s=o.minY,c=r-i,u=o.maxY-s,d=new cc.Rect(i+c/2,s+u/2,c,u),y=this._collisionManager.testDisplaySelect(d,this._dataCenterBridge.getComponentIds(),this._displayManager.dpMap);y.length>0&&yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:y,isVue:!1,isMulti:this._multiSelect}),n()}else n()},t.prototype.selectEditor=function(e,t){var n=this._displayManager.selectedIds;e.length>0&&!t&&n.indexOf(e[e.length-1])>=0||n.length<=0&&e.length<=0||yy.single.instance(l.default).execute(p.ReplaceCmptCommand,{ids:e,isVue:!1,isMulti:t})},t.prototype.onPressDirection=function(e){var t=new cc.Vec3(0,0,0);switch(e){case 37:t.x-=1;break;case 38:t.y+=1;break;case 39:t.x+=1;break;case 40:t.y-=1;break;default:t.x+=1}this._multiSelect&&(t.x*=10,t.y*=10);var n=this._displayManager.selectId;"-1"!==this._displayManager.selectSubId&&(n=this._displayManager.selectSubId),this._editBoxTouchHandler.keyDown(n,this._displayManager,e,this._multiSelect)||(window.vueFocus&&(t.x=0,t.y=0),this._moveHandler.onPressDirection(t))},t.prototype.onPressMultiKey=function(e,t){var n=this._displayManager.selectId;"-1"!==this._displayManager.selectSubId&&(n=this._displayManager.selectSubId),17==e||91==e?this._editBoxTouchHandler.ctrlKeyDown(n,this._displayManager,t):16==e&&t>=37&&t<=40&&this.onPressDirection(t)},t.prototype.onPressKey=function(e){var t=this._displayManager.selectId;"-1"!==this._displayManager.selectSubId&&(t=this._displayManager.selectSubId),e>=37&&e<=40?this.onPressDirection(e):this._editBoxTouchHandler.keyDown(t,this._displayManager,e)},t.prototype.onDestroy=function(){this.unregistEvent()},r([b],t)}(cc.Component);n.default=C,cc._RF.pop()},{"../../qte/core/utils/MathUtil":void 0,"../Config":"Config","../core/collision/CollisionManager":"CollisionManager","../core/command/operate/CommandFactory":"CommandFactory","../core/command/operate/base/ReplaceCmptCommand":"ReplaceCmptCommand","../core/display/DisplayObjectManager":"DisplayObjectManager","../core/display/base/cmpt/DisplaySelect":"DisplaySelect","../core/display/base/edit/CutShapeManager":"CutShapeManager","../core/proxy/DataCenterBridge":"DataCenterBridge","../utils/CusorStyle":"CusorStyle","./AnimTouchHandler":"AnimTouchHandler","./EditBoxTouchHandler":"EditBoxTouchHandler","./MoveTouchHandler":"MoveTouchHandler","./SvgTouchHandler":"SvgTouchHandler"}],TweenAction:[function(e,t,n){"use strict";cc._RF.push(t,"36affd4s3JCg65gImuwpGLF","TweenAction");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../qte/core/utils/ValueUtils"),a=e("./ActionManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.play=function(e,t,n){var o=t.value,i=t.delay,s=yy.single.instance(a.default).getTweenData(t.id);if(s.length<=0)n&&n();else{for(var c=cc.tween(e),p=0,l=s;p<l.length;p++){var u=l[p],d={};if(u.easing&&(d.easing=u.easing),"bezier"===u.type){var y=cc.v2(u.props.c1.x,u.props.c1.y),f=cc.v2(u.props.c2.x,u.props.c2.y),h=cc.v2(u.props.p1.x,u.props.p1.y);c.bezierTo(o.speed,y,f,h)}else c[u.type](u.time,u.props,d)}c.repeat(o.repeat,c),setTimeout(function(){c.call(function(){n&&n()}).start()},r.default.check(yy.checkValue(1e3*i,0),0))}},t}(e("./BaseAction").default);n.default=s,cc._RF.pop()},{"../../../qte/core/utils/ValueUtils":void 0,"./ActionManager":"ActionManager","./BaseAction":"BaseAction"}],TweenAppearOpacity:[function(e,t,n){"use strict";cc._RF.push(t,"a0654/gaJZCP4X+vmOUM9jV","TweenAppearOpacity");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.APPEAR_OPACITY,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t){var n=[];return n.push({props:{opacity:0,scale:1},duration:0}),n.push({props:{opacity:255},duration:t}),n},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenAppear:[function(e,t,n){"use strict";cc._RF.push(t,"afbcb9HQmpBX7mTrKH+2eCs","TweenAppear");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.APPEAR,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t){var n=[];return n.push({props:{scale:0},duration:0}),n.push({props:{scale:1},duration:t}),n},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenBlink:[function(e,t,n){"use strict";cc._RF.push(t,"eba77VJ1elGwoFSecKvmN2B","TweenBlink");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.BLINK,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[],i={start:0,end:0};return n.option&&"number"!=typeof n.option&&(i=n.option),o.push({props:{opacity:i.start},duration:0}),o.push({props:{opacity:i.end},duration:t}),o},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenCustomCurve:[function(e,t,n){"use strict";cc._RF.push(t,"e45bfbMduRNhKzAsy6laIyc","TweenCustomCurve");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../qte/core/extension/action/ActionUtiil"),a=e("../AnimationData"),s=function(e){function t(){var t=e.call(this)||this;return t.tweenType=a.TweenType.CUSTON_CURVE,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=this.getCustomPoints(n),i=r.default.getHermitePoints(o);return this.getCustomData(i,t)},t}(e("./BaseTween").default);n.default=s,cc._RF.pop()},{"../../../../qte/core/extension/action/ActionUtiil":void 0,"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenCustomLine:[function(e,t,n){"use strict";cc._RF.push(t,"e2587d6aGdIB5xHflAWd51e","TweenCustomLine");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.CUSTOM_LINE,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=this.getCustomPoints(n);return this.getCustomData(o,t)},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenDisappearOpacity:[function(e,t,n){"use strict";cc._RF.push(t,"33b6bOoD7pCeqoDJwQZkOtJ","TweenDisappearOpacity");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.DISAPPEAR_OPACITY,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t){var n=[];return n.push({props:{opacity:255},duration:0}),n.push({props:{opacity:0},duration:t}),n.push({props:{scale:0},duration:0}),n},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenDisappear:[function(e,t,n){"use strict";cc._RF.push(t,"057479cbqdLsrgP8tfzybB7","TweenDisappear");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.DISAPPEAR,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t){var n=[];return n.push({props:{scale:1},duration:0}),n.push({props:{scale:0},duration:t}),n},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenFlyInto:[function(e,t,n){"use strict";cc._RF.push(t,"77953PJ4HhEcKHlRfi936oR","TweenFlyInto");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../proxy/DataCenterBridge"),a=e("../AnimationData"),s=function(e){function t(){var t=e.call(this)||this;return t.tweenType=a.TweenType.FLY_INTO,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[],i=this.getFlyIntoData(e,n.option);return o.push({props:{scale:1,x:i[0].x,y:i[0].y},duration:0}),o.push({props:{x:i[1].x,y:i[1].y},duration:t}),o},t.prototype.getFlyIntoData=function(e,t){var n=[],o=e.position,i=yy.single.instance(r.default).getStageInfo(),s=cc.v2(0,0);switch(t){case a.DirectionType.UP:s=cc.v2(o.x,i.safeHeight/2+e.height);break;case a.DirectionType.RIGHT_UP:s=cc.v2(i.width/2+e.width,i.safeHeight/2+e.height);break;case a.DirectionType.LEFT:s=cc.v2(-i.width/2-e.width,o.y);break;case a.DirectionType.RIGHT_DOWN:s=cc.v2(i.width/2+e.width,-i.safeHeight/2-e.height);break;case a.DirectionType.DOWN:s=cc.v2(o.x,-i.safeHeight/2-e.height);break;case a.DirectionType.LEFT_DOWN:s=cc.v2(-i.width/2-e.width,-i.safeHeight/2-e.height);break;case a.DirectionType.RIGHT:s=cc.v2(i.width/2+e.width,o.y);break;case a.DirectionType.LEFT_UP:s=cc.v2(-i.width/2-e.width,i.safeHeight/2+e.height);break;default:s=cc.v2(o.x,i.safeHeight/2+e.height)}return n.push(s),n.push(o),n},t}(e("./BaseTween").default);n.default=s,cc._RF.pop()},{"../../proxy/DataCenterBridge":"DataCenterBridge","../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenFlyOut:[function(e,t,n){"use strict";cc._RF.push(t,"c37b9jH7lZO84M+CxlORjO0","TweenFlyOut");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../proxy/DataCenterBridge"),a=e("../AnimationData"),s=function(e){function t(){var t=e.call(this)||this;return t.tweenType=a.TweenType.FLY_OUT,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[],i=this.getFlyOutData(e,n.option);return o.push({props:{x:i[0].x,y:i[0].y},duration:0}),o.push({props:{x:i[1].x,y:i[1].y},duration:t}),o},t.prototype.getFlyOutData=function(e,t){var n=this.getFlyIntoData(e,t),o=[];return o.push(n[1]),o.push(n[0]),o},t.prototype.getFlyIntoData=function(e,t){var n=[],o=e.position,i=yy.single.instance(r.default).getStageInfo(),s=cc.v2(0,0);switch(t){case a.DirectionType.DOWN:s=cc.v2(o.x,i.safeHeight/2+e.height);break;case a.DirectionType.LEFT_DOWN:s=cc.v2(i.width/2+e.width,i.safeHeight/2+e.height);break;case a.DirectionType.RIGHT:s=cc.v2(-i.width/2-e.width,o.y);break;case a.DirectionType.LEFT_UP:s=cc.v2(i.width/2+e.width,-i.safeHeight/2-e.height);break;case a.DirectionType.UP:s=cc.v2(o.x,-i.safeHeight/2-e.height);break;case a.DirectionType.RIGHT_UP:s=cc.v2(-i.width/2-e.width,-i.safeHeight/2-e.height);break;case a.DirectionType.LEFT:s=cc.v2(i.width/2+e.width,o.y);break;case a.DirectionType.RIGHT_DOWN:s=cc.v2(-i.width/2-e.width,i.safeHeight/2+e.height);break;default:s=cc.v2(o.x,i.safeHeight/2+e.height)}return n.push(s),n.push(o),n},t}(e("./BaseTween").default);n.default=s,cc._RF.pop()},{"../../proxy/DataCenterBridge":"DataCenterBridge","../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenMoveBezier:[function(e,t,n){"use strict";cc._RF.push(t,"3ee50t4AltMlJgOVNqBimvw","TweenMoveBezier");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.MOVE_BEZIRE,t}return i(t,e),t.prototype.actionData=function(e,t,n){this.relative=!1;var o=[],i=this.getCustomPoints(n);return o.push({props:{x:i[0].x,y:i[0].y},duration:0}),o.push({props:{c1:{x:i[1].x,y:i[1].y},c2:{x:i[2].x,y:i[2].y},p2:{x:i[3].x,y:i[3].y}},duration:t}),o},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenMoveLine:[function(e,t,n){"use strict";cc._RF.push(t,"cbb8cIqwXlJyoISm5KSoqqY","TweenMoveLine");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.MOVE_LINE,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[],i=this.getCustomPoints(n);return i.length<=0?o:(o.push({props:{x:i[0].x,y:i[0].y},duration:0}),o.push({props:{x:i[1].x,y:i[1].y},duration:t}),o)},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenRotation:[function(e,t,n){"use strict";cc._RF.push(t,"bd23f9my11OtIVha0AinZLN","TweenRotation");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.ROTATION,t}return i(t,e),t.prototype.actionData=function(e,t,n){this.relative=n.relative;var o={start:0,end:0};return n.option&&"number"!=typeof n.option&&(o=n.option),[{props:{angle:o.start},duration:0},{props:{angle:o.end},duration:t}]},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenScale:[function(e,t,n){"use strict";cc._RF.push(t,"3c320H8n0FH5rC24xCCVPmv","TweenScale");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.SCALE,t.relative=!1,t}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[],i={start:0,end:0};return"number"!=typeof n.option&&(i=n.option),o.push({props:{scale:i.start},duration:0}),o.push({props:{scale:i.end},duration:t}),o},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenShake:[function(e,t,n){"use strict";cc._RF.push(t,"1c408U6BLBG47D2lE7ixU9g","TweenShake");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){var t=e.call(this)||this;return t.tweenType=r.TweenType.SHAKE,t.relative=!0,t}return i(t,e),t.prototype.actionData=function(e,t,n){return this.getShakeData(n,t)},t.prototype.getShakeData=function(e,t){var n=[];if(e.option===r.ShakeDirections.HORIZONTAL){var o=t/4;n.push({props:{angle:-10},duration:o}),n.push({props:{angle:27},duration:o}),n.push({props:{angle:-28},duration:o}),n.push({props:{angle:11},duration:o})}else e.option===r.ShakeDirections.VERTICAL&&(o=t/5,n.push({props:{y:11.5},duration:o}),n.push({props:{y:-18.2},duration:o}),n.push({props:{y:14.3},duration:o}),n.push({props:{y:-11.7},duration:o}),n.push({props:{y:4.1},duration:o}));return n},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],TweenSpecial:[function(e,t,n){"use strict";cc._RF.push(t,"fc64arC0aZIY7D6CrgMeoaj","TweenSpecial");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../AnimationData"),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.actionData=function(e,t,n){var o=[];switch(n.type){case r.TweenType.SIWEI_AFTER_APPEAR:o=this.afterAppearAni(e);break;case r.TweenType.SIWEI_APPEAR:o=this.appearAni();break;case r.TweenType.SIWEI_DISAPPEAR:o=this.disappearAni();break;case r.TweenType.SIWEI_REPEATEDLY:o=this.repeatedlyAni();break;case r.TweenType.SIWEI_SHAKE_X:o=this.shakeXAni();break;case r.TweenType.SIWEI_SHAKE_Y:o=this.shakeYAni();break;case r.TweenType.SIWEI_SIZE_CHANGE:o=this.sizeChangeAni();break;case r.TweenType.SIWEI_STAGNATION:o=this.stagnationAni();break;case r.TweenType.SIWEI_MOVE_LINE:this.relative=!1;var i=this.getCustomPoints(n);if(i.length<=0)return o;o.push({props:{x:i[1].x,y:i[1].y},duration:t})}return o},t.prototype.sizeChangeAni=function(){return this.relative=!1,[{duration:0,props:{scale:1}},{duration:16.67*10/1e3,props:{scale:1.08}},{duration:16.67*6/1e3,props:{scale:1}}]},t.prototype.shakeXAni=function(){return this.relative=!0,[{duration:0,props:{angle:0}},{duration:16.67*6/1e3,props:{angle:-5}},{duration:16.67*6/1e3,props:{angle:8}},{duration:16.67*6/1e3,props:{angle:-4}},{duration:16.67*6/1e3,props:{angle:1}}]},t.prototype.repeatedlyAni=function(){return this.relative=!1,[{duration:0,props:{scale:1}},{duration:0,props:{opacity:255}},{duration:16.67*20/1e3,props:{opacity:0}},{duration:16.67*10/1e3,props:{opacity:255}},{duration:16.67*10/1e3,props:{opacity:255}},{duration:16.67*10/1e3,props:{opacity:0}},{duration:0,props:{scale:0}}]},t.prototype.afterAppearAni=function(e){return this.relative=!1,[{duration:0,props:{scale:1,opacity:0}},{duration:16.67*20/1e3,props:{opacity:255}},{duration:16.67*12/1e3,props:{angle:e.angle}},{duration:16.67*6/1e3,props:{angle:e.angle-10}},{duration:16.67*6/1e3,props:{angle:e.angle+7}},{duration:16.67*6/1e3,props:{angle:e.angle-4}},{duration:16.67*6/1e3,props:{angle:e.angle+2}},{duration:16.67*6/1e3,props:{angle:e.angle}}]},t.prototype.shakeYAni=function(){return this.relative=!0,[{duration:16.67*6/1e3,props:{y:11.5}},{duration:16.67*6/1e3,props:{y:-18.5}},{duration:16.67*6/1e3,props:{y:14.375}},{duration:16.67*6/1e3,props:{y:-11.75}},{duration:16.67*6/1e3,props:{y:4.125}}]},t.prototype.stagnationAni=function(){return this.relative=!1,[{duration:0,props:{opacity:0}},{duration:16.67*20/1e3,props:{opacity:255}}]},t.prototype.appearAni=function(){return this.relative=!1,[{duration:0,props:{scale:1}},{duration:0,props:{opacity:0}},{duration:16.67*20/1e3,props:{opacity:255}}]},t.prototype.disappearAni=function(){return this.relative=!1,[{duration:0,props:{opacity:255}},{duration:16.67*20/1e3,props:{opacity:0}},{duration:0,props:{scale:0}}]},t}(e("./BaseTween").default);n.default=a,cc._RF.pop()},{"../AnimationData":"AnimationData","./BaseTween":"BaseTween"}],UnassembleGroupCommand:[function(e,t,n){"use strict";cc._RF.push(t,"ab8f8akQ4FNC5JRbNowiF/C","UnassembleGroupCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UnassembleGroupCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=e("../../../hintline/HintLineManager"),c=e("../../../proxy/DataCenterBridge"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){for(var t=yy.single.instance(a.default).getDisplayObjectById(e),n=t.groudSubs,o=t.node.width,i=t.node.height,r=t.getNodeAngle(),s=[],p=[],l=0;l<n.length;l++){var u=n[l];s.push({subId:u.cid,x:u.node.x,y:u.node.y,angle:u.getNodeAngle()}),p.push(u.cid)}var d=yy.single.instance(a.default).removeGroupDisplayObject(e);for(this.oldBody={id:e,ids:d,pos:t.node.position,groupWidth:o,groupHeight:i,groupAngle:r,tempGroupPropertes:s},l=0;l<p.length;l++){var y=p[l],f=yy.single.instance(a.default).getDisplayObjectById(y);if(f){var h=f.getNewProperties();yy.single.instance(c.default).updateComponetProperties(y,h.newProperties)}}yy.log("----UnassembleGroupCommand---execute---\x3e",this.oldBody)},t.prototype.undo=function(e){yy.log("----UnassembleGroupCommand---undo---\x3e",e);var t=yy.single.instance(a.default),n=t.getDisplayObjectById(e.id),o=n.node;if(o.position=e.pos,o.angle=e.groupAngle,o.cAngle=e.groupAngle,o.width=e.groupWidth,o.height=e.groupHeight,n)for(var i=function(i){var r=t.getDisplayObjectById(i);r.node.parent=o,yy.single.instance(s.default).removeHintLineByDisplay(i),n.addSubObject(i,r);var a=e.tempGroupPropertes.find(function(e){return e.subId===i});a&&(r.node.x=a.x,r.node.y=a.y,r.node.angle=a.angle,r.node.cAngle=a.angle)},r=0,c=e.ids;r<c.length;r++)i(c[r]);this.oldBody=e.id},t}(r.default);n.UnassembleGroupCommand=p,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager","../../../hintline/HintLineManager":"HintLineManager","../../../proxy/DataCenterBridge":"DataCenterBridge"}],UpdateAnimPropertiesCommand:[function(e,t,n){"use strict";cc._RF.push(t,"7c6c25vjVlNtY59x/BSMgPA","UpdateAnimPropertiesCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateAnimPropertiesCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../animation/display/AnimDisplayManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(s.default),n=yy.single.instance(a.default),o=t.getAnimDisplayObject(e.id);if(o&&cc.isValid(o.node)){o.setSelect(!0),this.oldBody={id:e.id,newProperties:o.oldProperties,actionId:e.actionId},t.updateDisplayObjectDeltaPos(e.actionId,e.id,cc.v3(e.newProperties.x-o.node.x,e.newProperties.y-o.node.y));var i=t.getAnimDisplayObjects(e.actionId);t.setDisObjectDir(i),n.updateFragmentsMap();var r=n.getActionOption(e.id);n.saveOptionData(t.getActionById(e.id),r)}},t.prototype.undo=function(e){var t=yy.single.instance(s.default),n=yy.single.instance(a.default),o=t.getAnimDisplayObject(e.id);if(o&&cc.isValid(o.node)){t.updateDisplayObjectDeltaPos(e.actionId,e.id,cc.v3(e.newProperties.x-o.node.x,e.newProperties.y-o.node.y));var i=t.getAnimDisplayObjects(e.actionId);t.setDisObjectDir(i),n.updateFragmentsMap()}},t}(r.default);n.UpdateAnimPropertiesCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../animation/display/AnimDisplayManager":"AnimDisplayManager"}],UpdateBgCommand:[function(e,t,n){"use strict";cc._RF.push(t,"0b9052SKFlKA4b/Lpg4qMok","UpdateBgCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateBgCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../../stage/StageController"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){this.oldBody=yy.single.instance(a.default).updateBackground(e),console.warn("bg--execute",e,this.oldBody)},t.prototype.undo=function(e){console.warn("bg--undo",e),yy.single.instance(a.default).updateBackground(e)},t}(r.default);n.UpdateBgCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../stage/StageController":"StageController"}],UpdateCmptLevelCommand:[function(e,t,n){"use strict";cc._RF.push(t,"7d62dPXbE9IhLRikStfaTg+","UpdateCmptLevelCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateCmptLevelCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../../common/EditorEnum"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=e.id,n=e.type,o=yy.single.instance(s.default),i=o.getDisplayObjectById(t);if(this.oldBody={id:t,type:n,sIdx:i.node.getSiblingIndex()},i){var r=0,c=o.isSubNode(t),p=o.getOriginSiblingIndexById(t),l=this.getContextIndexRange(c||"root");n===a.UpdateLevelType.TOP?r=l.maxIndex:n===a.UpdateLevelType.BOTTOM?r=l.minIndex:n===a.UpdateLevelType.FORWARD?r=p+1>l.maxIndex?l.maxIndex:p+1:n===a.UpdateLevelType.BACKWARD&&(r=Math.max(p-1,0)),console.log("%c Line:46 \ud83c\udf2e _index","color:#f5ce50",r);var u=this.calculateAffectedNodes(t,n,r);console.log("%c Line:47 \ud83c\udf6a affectedNodes","color:#ffdd4d",u),u.updates.length>0&&o.updateOriginSiblingIndexById(u.updates,!0)}else yy.warn("id = "+t+"\u7684\u7ec4\u4ef6\u4e0d\u5b58\u5728!")},t.prototype.getContextIndexRange=function(e){for(var t=new Map,n=yy.single.instance(s.default),o=0,i=Array.from(n.dpIndexMap.entries());o<i.length;o++){var r=i[o],a=r[0],c=r[1];(n.isSubNode(a)||"root")===e&&t.set(a,c)}var p=Array.from(t.values()).sort(function(e,t){return e-t});return{nodes:t,minIndex:p.length>0?p[0]:0,maxIndex:p.length>0?p[p.length-1]:0,count:p.length}},t.prototype.calculateAffectedNodes=function(e,t,n){var o=yy.single.instance(s.default),i=o.dpIndexMap.get(e);if(i===n)return console.warn("\u26a1 \u4f4d\u7f6e\u672a\u53d8\u5316\uff0c\u65e0\u9700\u66f4\u65b0"),{updates:[],reason:"\u4f4d\u7f6e\u672a\u53d8\u5316"};var r=o.isSubNode(e)||"root",a=this.getContextIndexRange(r),c=[];c.push({id:e,newIndex:n,oldIndex:i});for(var p=n>i,l=0,u=Array.from(a.nodes.entries());l<u.length;l++){var d=u[l],y=d[0],f=d[1];if(y!==e){var h=f;p?f>i&&f<=n&&(h=f-1):f>=n&&f<i&&(h=f+1),h!==f&&c.push({id:y,newIndex:h,oldIndex:f})}}return{updates:c,currentIndex:i,targetIndex:n}},t.prototype.undo=function(e){yy.single.instance(s.default).getDisplayObjectById(e.id).node.setSiblingIndex(e.sIdx)},t}(r.default);n.UpdateCmptLevelCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../common/EditorEnum":"EditorEnum","../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateComponentDeletable:[function(e,t,n){"use strict";cc._RF.push(t,"f3cabldqXlIoa0ngpLpgQq+","UpdateComponentDeletable");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../../qte/core/extension/command/SimpleCommand"),s=e("../../../display/DisplayObjectManager"),c=cc._decorator.ccclass,p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(s.default).getDisplayObjectById(e.cid);this.oldBody={cid:e.cid,deletable:t.deletable},t.deletable=e.deletable},t.prototype.undo=function(e){yy.single.instance(s.default).getDisplayObjectById(e.cid).deletable=e.deletable},r([c],t)}(a.default);n.default=p,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateComponentEditable:[function(e,t,n){"use strict";cc._RF.push(t,"bd6a1g/715MzqOQoBjFY9I4","UpdateComponentEditable");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__decorate||function(e,t,n,o){var i,r=arguments.length,a=r<3?t:null===o?o=Object.getOwnPropertyDescriptor(t,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,n,o);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(a=(r<3?i(a):r>3?i(t,n,a):i(t,n))||a);return r>3&&a&&Object.defineProperty(t,n,a),a};Object.defineProperty(n,"__esModule",{value:!0});var a=e("../../../../../qte/core/extension/command/SimpleCommand"),s=e("../../../display/DisplayObjectManager"),c=e("../../../proxy/ComptData"),p=e("../../../proxy/DataCenterBridge"),l=cc._decorator.ccclass,u=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=e.componentIds,n=yy.single.instance(s.default);this.oldBody={componentIds:e.componentIds};for(var o=0;o<t.length;o++){var i=n.getDisplayObjectById(t[o]),r=new c.EditableProperties;for(var a in"object"==typeof i.editable&&(r=i.editable),e.newEditable)r.properties[a]=e.newEditable[a];i.editable=r}},t.prototype.undo=function(e){for(var t=e.componentIds,n=yy.single.instance(s.default),o=0;o<t.length;o++)n.getDisplayObjectById(t[o]).editable=yy.single.instance(p.default).getComponentEditable(t[o])},r([l],t)}(a.default);n.default=u,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager","../../../proxy/ComptData":"ComptData","../../../proxy/DataCenterBridge":"DataCenterBridge"}],UpdateComponentOrderCommand:[function(e,t,n){"use strict";cc._RF.push(t,"ea5dblv+rVKupq0SMaREk49","UpdateComponentOrderCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}),r=this&&this.__spreadArrays||function(){for(var e=0,t=0,n=arguments.length;t<n;t++)e+=arguments[t].length;var o=Array(e),i=0;for(t=0;t<n;t++)for(var r=arguments[t],a=0,s=r.length;a<s;a++,i++)o[i]=r[a];return o};Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateComponentOrderCommand=void 0;var a=e("../../../../../qte/core/extension/command/SimpleCommand"),s=e("../../../display/DisplayObjectManager"),c=e("../../../../../qte/core/utils/ValueUtils"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(s.default);this.oldBody=c.default.clone(e),console.log("Array.from(dpm.dpIndexMap.entries())==",Array.from(t.dpIndexMap.entries()));var n=t.isSubNode(e[0].id)||"root";console.log("body,,",JSON.stringify(e));var o=e[0].newIndex-e[0].oldIndex,i=e.reduce(function(e,t){return Math.max(e,o>=0?t.newIndex:t.oldIndex)},0);console.log("%c Line:39 \ud83e\udd52 maxNewIndex","color:#4fff4B",i);var a=e.reduce(function(e,t){return Math.max(e,o>=0?t.oldIndex:t.newIndex)},0);console.log("%c Line:41 \ud83c\udf63 minNewIndex","color:#3f7cff",a),"root"!=n&&(i=Math.max(i+2,0),a=Math.max(a+2,0));for(var p=new Map,l=0,u=Array.from(t.dpIndexMap.entries());l<u.length;l++){var d=u[l],y=d[0],f=d[1];(t.isSubNode(y)||"root")===n&&p.set(y,f)}var h=Array.from(p.entries());h.sort(function(e,t){return e[1]-t[1]});for(var m=[],g=new Set(e.map(function(e){return e.id})),_=0;_<h.length;_++)(S=h[_])&&Array.isArray(S)&&S.length>=2&&(y=S[0])&&g.has(y)&&m.push(_);for(var v=0,b=m;v<b.length;v++)h[f=b[v]]=null;console.log("\u7b2c\u4e00\u6b21\u5220\u9664 body ,",JSON.stringify(h));for(var C=[],O=new Set,T=function(t,n){n>=a&&n<=i&&(e.some(function(e){return e.id===t})||(C.push({id:t,oldIndex:n}),O.add(t)))},w=0,I=Array.from(p.entries());w<I.length;w++){var P=I[w];T(y=P[0],f=P[1])}for(console.log("%c Line:96 \ud83c\udf62 affectedNodes","color:#f5ce50",C),_=0;_<h.length;_++){var S;(S=h[_])&&Array.isArray(S)&&S.length>=2&&(y=S[0])&&O.has(y)&&(h[_]=null)}console.log("\u7b2c\u4e8c\u6b21 \u5220\u9664",JSON.stringify(h));for(var A=0,D=e;A<D.length;A++)h[(L=D[A]).newIndex]=[L.id,L.newIndex];console.log("\u7b2c\u4e00\u6b21\u586b\u5165  ",JSON.stringify(h)),C.sort(function(e,t){return e.oldIndex-t.oldIndex});for(var x=0,E=C;x<E.length;x++)for(var L=E[x],M=0;M<h.length;M++)if(null===h[M]){h[M]=[L.id,L.oldIndex],L.newIndex="root"!=n?M+2:M;break}console.log("\u7b2c\u4e8c\u6b21\u586b\u5165  ",JSON.stringify(h)),C.sort(function(e,t){return e.oldIndex-t.oldIndex}),"root"!=n&&e.forEach(function(e){e.newIndex+=2,e.oldIndex+=2});var j=r(C,e);console.log("affectedNodes",j),t.updateOriginSiblingIndexById(j,!0)},t.prototype.undo=function(){var e=yy.single.instance(s.default),t=this.oldBody.map(function(e){return{id:e.id,newIndex:e.oldIndex,oldIndex:e.newIndex}});e.updateOriginSiblingIndexById(t,!0)},t}(a.default);n.UpdateComponentOrderCommand=p,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../../qte/core/utils/ValueUtils":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateComponentTagCommand:[function(e,t,n){"use strict";cc._RF.push(t,"97275OwB15KPYXZVhUDLtip","UpdateComponentTagCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateComponentTagCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default).getDisplayObjectById(e.id).showDisplayTag(e.label);this.oldBody={id:e.id,label:t},console.log("showDisplayTag2")},t.prototype.undo=function(e){yy.single.instance(a.default).getDisplayObjectById(e.id).showDisplayTag(e.label),console.log("showDisplayTag3")},t}(r.default);n.UpdateComponentTagCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateDragableCommand:[function(e,t,n){"use strict";cc._RF.push(t,"1cd38l3/ZpJcbm1cmpGMsTX","UpdateDragableCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateDragableCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=e.cmptIds,n=e.dragable;this.oldBody={cmptIds:t,dragable:!n},this.updateProperty(t,n)},t.prototype.undo=function(e){this.updateProperty(e.cmptIds,e.dragable)},t.prototype.updateProperty=function(e,t){for(var n=yy.single.instance(a.default),o=0,i=e;o<i.length;o++){var r=i[o];n.updateDisplayObject(r,{dragable:t})}},t}(r.default);n.UpdateDragableCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateEditingComponentAnimations:[function(e,t,n){"use strict";cc._RF.push(t,"e08d07Gi5VBBZ2ndAslsRkR","UpdateEditingComponentAnimations");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateEditingComponentAnimations=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default);this.oldBody=t.isEditingComponentAnimations,t.isEditingComponentAnimations=e,this.updateEditMode(e)},t.prototype.undo=function(e){yy.single.instance(a.default).isEditingComponentAnimations=e,this.updateEditMode(e)},t.prototype.updateEditMode=function(e){if(e){var t=yy.single.instance(a.default),n=yy.single.instance(s.default).selectedIds;t.updateFragmentsMap(),t.showAllPah(n)}else yy.single.instance(a.default).resetMode()},t}(r.default);n.UpdateEditingComponentAnimations=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateModeCommand:[function(e,t,n){"use strict";cc._RF.push(t,"fb891J8RchNYJd73dIjuMpc","UpdateModeCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdateModeCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../animation/display/AnimDisplayControl"),s=e("../../../display/DisplayObjectManager"),c=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default);this.oldBody=t.editMode,t.editMode=e,this.updateEditMode(e)},t.prototype.undo=function(e){this.updateEditMode(e)},t.prototype.updateEditMode=function(e){var t=yy.single.instance(a.default);if(t.editMode=e,t.idAnimationEdit){var n=yy.single.instance(s.default).selectedIds;t.updateFragmentsMap(),t.showAllPah(n)}else yy.single.instance(a.default).resetMode()},t}(r.default);n.UpdateModeCommand=c,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdatePointPosCommand:[function(e,t,n){"use strict";cc._RF.push(t,"fe9544xzFVD+rL/s2DfY9eY","UpdatePointPosCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdatePointPosCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../display/DisplayObjectManager"),s=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(a.default),n=t.getDisplayObjectById(e.cmptIds),o=e.pointPos;e.isVue&&(o=t.getEditPointPos(e.cmptIds,e.pointId,e.pointPos));var i=n.updatePointPos(e.pointId,o);this.oldBody={cmptIds:e.cmptIds,pointId:e.pointId,pointPos:i}},t.prototype.undo=function(e){yy.single.instance(a.default).getDisplayObjectById(e.cmptIds).updatePointPos(e.pointId,e.pointPos)},t}(r.default);n.UpdatePointPosCommand=s,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../display/DisplayObjectManager":"DisplayObjectManager"}],UpdateProp2VueCmd:[function(e,t,n){"use strict";cc._RF.push(t,"32633apYkxPupypOMMYSgX9","UpdateProp2VueCmd");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0});var r=e("../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../common/EditorEnum"),s=e("../../animation/display/AnimDisplayControl"),c=e("../../display/DisplayObjectManager"),p=e("../../proxy/DataCenterBridge"),l=e("../operate/base/UpdatePropertiesCommand"),u=e("../operate/CommandFactory"),d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){console.log("UpdateProp2VueCmd",e);var t=e.selectIds,n=(e.notRunCmd,e.notUpdate2Vue),o=e.isUpdateGroupData,i=yy.single.instance(c.default),r=yy.single.instance(p.default),d=yy.cloneValues(t);if(o){for(var y=[],f=0,h=d;f<h.length;f++){var m=h[f];if((P=i.getDisplayObjectById(m)).type===a.CmptType.GROUP)for(var g=function(e){y.find(function(t){return t===e})||y.push(e)},_=0,v=P.groupIds;_<v.length;_++)g(v[_])}for(var b=function(e){d.find(function(t){return t===e})||d.push(e)},C=0,O=y;C<O.length;C++)b(m=O[C])}for(var T=yy.single.instance(u.default),w=0,I=d;w<I.length;w++){m=I[w];var P=i.getDisplayObjectById(m);if(r.getComponentMap()[m]&&P){var S=P.getOldProperties(),A=P.getNewProperties();T.pushCommand(l.UpdatePropertiesCommand,A,S),n||r.updateComponetProperties(m,A.newProperties);var D={x:A.newProperties.x-S.newProperties.x,y:A.newProperties.y-S.newProperties.y};yy.single.instance(s.default).updateDispObject(m,D)}}},t.prototype.undo=function(e){yy.log(e)},t.prototype.isChangePropertie=function(e,t){for(var n in e.newProperties)if(yy.checkValue(t.newProperties[n])!==yy.checkValue(e.newProperties[n]))return!0;return!1},t}(r.default);n.default=d,cc._RF.pop()},{"../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../common/EditorEnum":"EditorEnum","../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../display/DisplayObjectManager":"DisplayObjectManager","../../proxy/DataCenterBridge":"DataCenterBridge","../operate/CommandFactory":"CommandFactory","../operate/base/UpdatePropertiesCommand":"UpdatePropertiesCommand"}],UpdatePropertiesCommand:[function(e,t,n){"use strict";cc._RF.push(t,"47860WYm8dMwK3Oe62DRWzD","UpdatePropertiesCommand");var o,i=this&&this.__extends||(o=function(e,t){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},function(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)});Object.defineProperty(n,"__esModule",{value:!0}),n.UpdatePropertiesCommand=void 0;var r=e("../../../../../qte/core/extension/command/SimpleCommand"),a=e("../../../../common/EditorEnum"),s=e("../../../animation/display/AnimDisplayControl"),c=e("../../../display/DisplayObjectManager"),p=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.execute=function(e){var t=yy.single.instance(c.default).updateDisplayObject(e.id,e.newProperties);this.oldBody.id=e.id,this.oldBody.newProperties=t,yy.single.instance(s.default).updateActionOption(e.id),console.log(e),yy.log("----UpdatePropertiesCommand---execute---\x3e",this.oldBody);var n={x:this.newBody.newProperties.x-this.oldBody.newProperties.x,y:this.newBody.newProperties.y-this.oldBody.newProperties.y};yy.single.instance(s.default).updateDispObject(e.id,n)},t.prototype.undo=function(e,t){yy.log("----UpdatePropertiesCommand---undo---\x3e",e,t),this.updateUndo(e)},t.prototype.updateUndo=function(e,t){var n=this;if(e){var o=yy.single.instance(c.default).getDisplayObjectById(e.id);if(!o||!o.isInitFinished){yy.log("display",e.id,"\u7ec4\u4ef6\u4e0d\u5b58\u5728\u8fd8\u6ca1\u51fa\u521d\u59cb\u5316\u6210\u529f\uff0c\u7a0d\u540e\u66f4\u65b0");var i=null;return e&&void 0===t?(i=JSON.parse(JSON.stringify(e)),t=0):(i=e,t++),t<=10?void setTimeout(function(){n.updateUndo(i,t)},100):void yy.log("display",e.id,"\u7ec4\u4ef6\u4e0d\u5b58\u5728\u8fd8\u6ca1\u51fa\u521d\u59cb\u5316\u6210\u529f\uff0c\u4e0d\u5728\u66f4\u65b0")}o.type===a.CmptType.LABEL&&(this.newBody=o.getNewProperties()),yy.single.instance(c.default).updateDisplayObject(e.id,e.newProperties,!0);var r={x:e.newProperties.x-this.newBody.newProperties.x,y:e.newProperties.y-this.newBody.newProperties.y};yy.single.instance(s.default).updateDispObject(e.id,r)}},t}(r.default);n.UpdatePropertiesCommand=p,cc._RF.pop()},{"../../../../../qte/core/extension/command/SimpleCommand":void 0,"../../../../common/EditorEnum":"EditorEnum","../../../animation/display/AnimDisplayControl":"AnimDisplayControl","../../../display/DisplayObjectManager":"DisplayObjectManager"}],commond:[function(e,t,n){"use strict";cc._RF.push(t,"47b7d2bKW5At5oXyvPhg0IC","commond");var o=this&&this.__createBinding||(Object.create?function(e,t,n,o){void 0===o&&(o=n),Object.defineProperty(e,o,{enumerable:!0,get:function(){return t[n]}})}:function(e,t,n,o){void 0===o&&(o=n),e[o]=t[n]}),i=this&&this.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||o(t,e,n)};Object.defineProperty(n,"__esModule",{value:!0}),i(e("./operate/anim/RemoveActionCommand"),n),i(e("./operate/anim/RemoveFragmentCommand"),n),i(e("./operate/anim/SetActiveActionCommand"),n),i(e("./operate/anim/SetFragmentCommand"),n),i(e("./operate/anim/UpdateAnimPropertiesCommand"),n),i(e("./operate/base/AddCmptCommand"),n),i(e("./operate/base/AssembleGroupCommand"),n),i(e("./operate/base/RemoveCmptCommand"),n),i(e("./operate/base/ReplaceCmptCommand"),n),i(e("./operate/base/UnassembleGroupCommand"),n),i(e("./operate/base/UpdateBgCommand"),n),i(e("./operate/base/UpdateCmptLevelCommand"),n),i(e("./operate/base/UpdateComponentTagCommand"),n),i(e("./operate/base/UpdateModeCommand"),n),i(e("./operate/base/UpdatePropertiesCommand"),n),i(e("./operate/base/UpdateEditingComponentAnimations"),n),i(e("./operate/base/UpdateDragableCommand"),n),cc._RF.pop()},{"./operate/anim/RemoveActionCommand":"RemoveActionCommand","./operate/anim/RemoveFragmentCommand":"RemoveFragmentCommand","./operate/anim/SetActiveActionCommand":"SetActiveActionCommand","./operate/anim/SetFragmentCommand":"SetFragmentCommand","./operate/anim/UpdateAnimPropertiesCommand":"UpdateAnimPropertiesCommand","./operate/base/AddCmptCommand":"AddCmptCommand","./operate/base/AssembleGroupCommand":"AssembleGroupCommand","./operate/base/RemoveCmptCommand":"RemoveCmptCommand","./operate/base/ReplaceCmptCommand":"ReplaceCmptCommand","./operate/base/UnassembleGroupCommand":"UnassembleGroupCommand","./operate/base/UpdateBgCommand":"UpdateBgCommand","./operate/base/UpdateCmptLevelCommand":"UpdateCmptLevelCommand","./operate/base/UpdateComponentTagCommand":"UpdateComponentTagCommand","./operate/base/UpdateDragableCommand":"UpdateDragableCommand","./operate/base/UpdateEditingComponentAnimations":"UpdateEditingComponentAnimations","./operate/base/UpdateModeCommand":"UpdateModeCommand","./operate/base/UpdatePropertiesCommand":"UpdatePropertiesCommand"}],use_reversed_rotateBy:[function(e,t){"use strict";cc._RF.push(t,"d740aXEA3NHIICOmn4FDSRE","use_reversed_rotateBy"),cc.RotateBy._reverse=!0,cc._RF.pop()},{}],yy:[function(e,t,n){"use strict";cc._RF.push(t,"9777138VApI94Lw5W5neDsz","yy"),Object.defineProperty(n,"__esModule",{value:!0});var o=e("../../qte/core/base/SingleBase"),i=e("../../qte/core/extension/action/ActionUtiil"),r=e("../../qte/core/loader/ResLoader"),a=e("../../qte/core/utils/ValueUtils"),s=e("../../qte/util/LogUtil");window.yy||(window.yy={}),window.yy.single={},window.yy.single.instance=o.SingleFactory.getInstance,window.yy.single.destory=o.SingleFactory.destory,window.yy.single.destoryAll=o.SingleFactory.destoryAll,window.yy.instance=o.SingleFactory.getInstance,window.yy.destory=o.SingleFactory.destory,window.yy.destoryAll=o.SingleFactory.destoryAll,window.yy.log=s.default.getInstance().log,window.yy.debug=s.default.getInstance().debug,window.yy.warn=s.default.getInstance().warn,window.yy.error=s.default.getInstance().error,window.yy.saveLog=s.default.save,window.yy.setLogStatus=s.default.setLogStatus,window.yy.logTrace=s.default.logTrace,window.yy.checkValue=a.default.check,window.yy.cloneValues=a.default.clone,window.yy.loader||(window.yy.loader={}),window.yy.loader.assetsMap=r.default.assetsMap,window.yy.loader.cmptAssets=r.default.cmptAssets,window.yy.loader.loadRes=r.default.loadRes,window.yy.loader.loadDir=r.default.loadDir,window.yy.loader.loadBundle=r.default.loadBundle,window.yy.loader.addObserver=r.default.addObserver,window.yy.loader.removeObserver=r.default.removeObserver,window.yy.Action||(window.yy.Action={}),window.yy.Action.getBezierPoints=i.default.getBezierPoints,window.yy.Action.getBezierByArray=i.default.getBezierByArray,window.yy.Action.getHermitePoints=i.default.getHermitePoints,n.default=yy,cc._RF.pop()},{"../../qte/core/base/SingleBase":void 0,"../../qte/core/extension/action/ActionUtiil":void 0,"../../qte/core/loader/ResLoader":void 0,"../../qte/core/utils/ValueUtils":void 0,"../../qte/util/LogUtil":void 0}]},{},["use_reversed_rotateBy","AssetsManager","Config","InitSceneCmpt","TestScript","AnimTouchHandler","EditBoxTouchHandler","MoveTouchHandler","SvgTouchHandler","TouchHandlerCmpt","ComUtils","EditorEnum","ActionManager","AnimationData","AnimationManager","AnimationStep","AnimationUtils","BaseAction","ClipAction","SpineAction","TweenAction","AnimDisplayControl","AnimDisplayManager","AnimDisplayObject","BaseTween","TweenAppear","TweenAppearOpacity","TweenBlink","TweenCustomCurve","TweenCustomLine","TweenDisappear","TweenDisappearOpacity","TweenFlyInto","TweenFlyOut","TweenMoveBezier","TweenMoveLine","TweenRotation","TweenScale","TweenShake","TweenSpecial","CollisionManager","commond","CommandFactory","MergeCommand","RemoveActionCommand","RemoveFragmentCommand","SetActiveActionCommand","SetFragmentCommand","UpdateAnimPropertiesCommand","AddChildComponent","AddCmptCommand","AssembleGroupCommand","ChangeAutoSubmitCommand","RemoveCmptCommand","ReplaceCmptCommand","UnassembleGroupCommand","UpdateBgCommand","UpdateCmptLevelCommand","UpdateComponentDeletable","UpdateComponentEditable","UpdateComponentOrderCommand","UpdateComponentTagCommand","UpdateDragableCommand","UpdateEditingComponentAnimations","UpdateModeCommand","UpdatePropertiesCommand","CreatePointCommand","RemovePointCommand","UpdatePointPosCommand","TextToImgCommand","UpdateProp2VueCmd","CocosAniManager","DisplayObjectFactory","DisplayObjectManager","OptSubObjectFactory","SpineManager","TemplateInterpreter","DisplayCocosAni","DisplayEditBox","DisplayGameBaseComponent","DisplayObject","DisplayObjectGroup","DisplaySpecialComponent","DisplaySpine","DisplaySvg","SvgShapeManager","DisplaySelect","DisplaySelectPoint","DisplayVertex","CutPoint","CutShapeManager","DisplayCutShape","DisplayEditObject","HintLineCmpt","HintLineDisplay","HintLineManager","ComptData","DataCenterBridge","DataProxyImp","ExtraModule","MockData","CocosAniRect","yy","CocosExport","StageController","StageLayerCmpt","StageSceneCmpt","AudioUtil","CusorStyle","Properties"]);