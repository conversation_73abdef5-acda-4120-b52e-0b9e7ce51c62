[1, ["ecpdLyjvZBwrvm+cedCcQy", "3ae7efMv1CLq2ilvUY/tQi"], ["node", "_normalMaterial", "_grayMaterial", "root", "cancelBtn", "selectedNode", "data"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children"], 1, 9, 4, 5, 1, 7, 2], ["cc.Sprite", ["_srcBlendFactor", "_sizeMode", "node", "_materials"], 1, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "_N$interactable", "_N$enableAutoGrayEffect", "node", "clickEvents"], 0, 1, 9], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 4, 5, 7], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["3d24d4RDsxKnblADLMXumpL", ["node", "selectedNode", "cancelBtn"], 3, 1, 1, 1]], [[2, 0, 1, 2], [1, 0, 2, 3, 2], [6, 0, 1, 2, 3], [4, 0, 2], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 1, 5, 2, 3, 4, 6, 3], [0, 0, 5, 2, 3, 4, 6, 2], [5, 0, 1, 2, 3, 4, 5, 2], [1, 0, 1, 2, 3, 3], [2, 1, 1], [3, 0, 3, 4, 2], [3, 1, 2, 0, 3, 4, 4], [7, 0, 1, 2, 1]], [[3, "drawControl"], [4, "drawControl", [-5, -6, -7, -8], [[13, -4, -3, -2]], [10, -1], [5, 95, 182]], [6, "selected", false, 1, [[1, 1, -9, [1]]], [0, "05y/7AfuhFpoRxbp2q7zSz", 1], [5, 88, 88], [0, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "pen", 1, [[1, 1, -10, [2]], [11, 3, -11, [[2, "3d24d4RDsxKnblADLMXumpL", "onPenClick", 1]]]], [0, "32TwJHyp1EVootfCCpRADd", 1], [5, 36, 35], [0, 45, 0, 0, 0, 0, 1, 1, 1, 0]], [8, "cancel", 1, [[[1, 1, -12, [3]], -13], 4, 1], [0, "aa4w/0cW5Bn6loVcBq5/K1", 1], [5, 38, 34], [0, -45, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "bg", 1, [[9, 1, 0, -14, [0]]], [0, "71tnzDZ/FLGoXPYQX6fNux", 1], [5, 95, 182]], [12, false, true, 3, 4, [[2, "3d24d4RDsxKnblADLMXumpL", "onCancelClick", 1]]]], 0, [0, 3, 1, 0, 4, 6, 0, 5, 2, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 3, 0, -4, 4, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, -2, 6, 0, 0, 5, 0, 6, 1, 14], [0, 0, 0, 0, 6, 6], [-1, -1, -1, -1, 1, 2], [0, 0, 0, 1, 0, 1]]