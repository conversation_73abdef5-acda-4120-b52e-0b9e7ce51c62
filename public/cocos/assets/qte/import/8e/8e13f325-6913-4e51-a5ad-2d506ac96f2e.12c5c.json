[1, ["ecpdLyjvZBwrvm+cedCcQy", "20NTKzzBZH07AoPrmIqA6x"], ["node", "root", "_spriteFrame", "scrollView", "content", "data", "_parent", "_N$verticalScrollBar"], [["cc.Widget", ["alignMode", "_alignFlags", "_originalWidth", "_originalHeight", "_left", "node"], -2, 1], ["cc.Node", ["_name", "_opacity", "_children", "_components", "_prefab", "_contentSize", "_parent", "_anchorPoint", "_trs"], 1, 2, 12, 4, 5, 1, 5, 7], ["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_anchorPoint", "_parent", "_children", "_trs"], 2, 9, 4, 5, 5, 1, 2, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials"], 0, 1, 3], ["cc.<PERSON>", ["_N$direction", "node", "_scrollView", "_N$handle"], 2, 1, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content"], -2, 1, 1], ["ce10csPzaNFipKQWXU3AtXs", ["node", "content", "scrollView"], 3, 1, 1, 1]], [[3, 0, 1, 2], [4, 0, 2], [1, 0, 2, 3, 4, 5, 2], [1, 0, 1, 6, 2, 3, 4, 5, 7, 8, 3], [2, 0, 5, 6, 1, 2, 3, 4, 7, 2], [2, 0, 1, 2, 3, 4, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [0, 0, 1, 2, 5, 4], [0, 0, 1, 2, 3, 5, 5], [0, 0, 1, 4, 3, 5, 5], [3, 1, 1], [6, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 4], [8, 0, 1, 2, 3, 2], [9, 0, 1, 2, 3, 4, 5, 6, 6], [10, 0, 1, 2, 1]], [[1, "h5LabelComponent"], [2, "h5LabelComponent", [-6, -7], [[-2, [15, -5, -4, -3]], 1, 4], [10, -1], [5, 1000, 640]], [5, "content", [[7, 2, 40, 1000, -9]], [0, "0b1dZ5CuhBdanIvFbADEg6", -8], [5, 1000, 250], [0, 0, 1]], [4, "view", 1, [2], [[11, 0, -10, [0]], [8, 2, 45, 1000, 640, -11]], [0, "53+Wl6sRNCIYyUxujpAJYA", 1], [5, 1000, 640], [0, 0, 1], [-500, 320, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "scrollBar", 0, 1, [-14], [[-12, [9, 2, 37, 350.07654921020657, 237, -13]], 1, 4], [0, "ccjp9cBvlOCaa3z4FcHM2l", 1], [5, 12, 640], [0, 1, 0.5], [500, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, false, 0.75, false, 0.23, null, 1, 2], [6, "bar", 0, 4, [-15], [0, "2eyg4jIrdEJrry9FzXUaZ2", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, 1, 1, 0, 6, [1]], [13, 1, 4, 5, 7]], 0, [0, 1, 1, 0, -1, 5, 0, 3, 5, 0, 4, 2, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, 1, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, -1, 8, 0, 0, 4, 0, -1, 6, 0, -1, 7, 0, 5, 1, 2, 6, 3, 5, 7, 8, 15], [0, 0, 7], [-1, -1, 2], [0, 0, 1]]