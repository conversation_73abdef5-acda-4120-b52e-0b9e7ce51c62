[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "f9sdpfMutGjo+lr/jEhR7y", "aeIHbB3n1HRrtZTU/khVsg", "27oFxjW/lKxp2oviS6p/4v", "8bUlbDHsVHmL1vS1QErqv+", "abWjGH2bBK+pesZ2ArX+r6", "254zdbB85MNK8sQHWKr8uv", "e9Ld2m4QtHYb8B9TVtjEyB", "cfkg00niVGJ7e3evU8A+uw", "b9p1UwqKxPSJr9BTlD49UR", "92FjfSrYROyoFtUdU/mpSY", "8eOYX2Wk9CrICAa4eO+hiq", "0aATkyKypLaaM6iNxEjDGd", "c5rzzp2uRHCJADGcXGEZkY", "71JaJaio9F/6o2yn3ENjHI", "f73zMf+4JJ6LN1azFX30c7"], ["node", "_spriteFrame", "_textureSetter", "_N$skeletonData", "_N$file", "root", "_N$barSprite", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_parent", "_components", "_children", "_trs", "_color"], 1, 4, 5, 1, 9, 2, 7, 5], ["cc.Sprite", ["_sizeMode", "_srcBlendFactor", "_isTrimmedMode", "_type", "_fillType", "_fillStart", "_fillRange", "node", "_materials", "_spriteFrame", "_fillCenter"], -4, 1, 3, 6, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "_playTimes", "defaultAnimation", "node", "_materials", "_N$skeletonData"], -3, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_isSystemFontUsed", "_N$horizontalAlign", "_N$verticalAlign", "_spacingX", "node", "_materials", "_N$file"], -4, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_active", "_parent", "_components", "_prefab", "_contentSize"], 1, 1, 12, 4, 5], ["cc.<PERSON><PERSON>", ["node"], 3, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1]], [[3, 0, 1, 2], [0, 0, 4, 5, 2, 3, 7, 2], [0, 0, 4, 5, 2, 3, 2], [0, 0, 1, 4, 5, 2, 3, 7, 3], [1, 1, 7, 8, 9, 2], [1, 7, 8, 9, 1], [4, 0, 1, 2, 3, 4, 6, 7, 8, 6], [6, 0, 2], [0, 0, 6, 2, 3, 2], [0, 0, 4, 6, 5, 2, 3, 2], [0, 0, 1, 4, 6, 5, 2, 3, 3], [0, 0, 1, 4, 6, 2, 3], [0, 0, 4, 5, 2, 8, 3, 7, 2], [7, 0, 1, 2, 3, 4, 5, 3], [3, 1, 1], [8, 0, 1], [1, 2, 7, 8, 9, 2], [1, 0, 7, 8, 9, 2], [1, 1, 3, 0, 4, 5, 6, 7, 8, 10, 7], [9, 0, 1, 2, 2], [4, 0, 5, 1, 2, 3, 6, 7, 8, 6], [5, 0, 1, 2, 3, 4, 5, 7, 8, 9, 7], [5, 0, 1, 2, 3, 6, 4, 5, 7, 8, 9, 8]], [[[[7, "entry"], [8, "entry", [-2, -3, -4], [14, -1], [5, 600, 120]], [9, "da<PERSON><PERSON><PERSON>", 1, [-6, -7, -8, -9, -10, -11, -12], [[15, -5]], [0, "31KNiKbZxLGY3rAz2lozay", 1], [5, 115, 115]], [10, "pBack", false, 1, [-14, -15], [[16, false, -13, [17], 18]], [0, "03VtCWMTpL4rTfKccMfi+A", 1], [5, 256, 124]], [13, "progress", false, 2, [[-16, [19, 2, -18, -17]], 1, 4], [0, "88or6IwEpCorY4qE7oPCfb", 1], [5, 115, 115]], [11, "score", false, 1, [-19, -20], [0, "dcciooTIRLDp/9MZm3+UoD", 1]], [2, "back", 2, [[17, 0, -21, [0], 1]], [0, "c1A+JbbGdEs6z+SXXMHf7w", 1], [5, 115, 115]], [18, 1, 3, 0, 2, 0.25, 1, 4, [2], [0, 0.5, 0.5]], [1, "res", 2, [[4, 1, -22, [3], 4]], [0, "c5q16vUEtHyYLW06UhFCwb", 1], [5, 49, 64], [0, -5.289, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [1, "spine1", 2, [[6, "default", 0, false, "animation", 1, -23, [5], 6]], [0, "a4xdfOfUBB4o1Zwaoe/+sb", 1], [5, 202, 120], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "spine2", 2, [[6, "default", 0, false, "animation", 1, -24, [7], 8]], [0, "678JLR5DhH3JVIRWsUbb1v", 1], [5, 202, 120], [-200, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [3, "idleLab", false, 2, [[21, "3", 11, 0, false, 1, 1, -25, [9], 10]], [0, "69H2QtxHJBJKUsgDDffpI6", 1], [5, 44, 0], [0, 27.682, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "tishi", false, 2, [[5, -26, [11], 12]], [0, "28FhoLsSFDbqonErkgDGiT", 1], [5, 208, 63], [0, 93.191, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "down", 3, [[5, -27, [13], 14]], [0, "93m7usxH5MyqEylysOxvP6", 1], [5, 279, 94], [36.264, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [1, "spine", 3, [[20, "default", "animation", 0, false, "animation", -28, [15], 16]], [0, "8fw2LGRH5E6o1moKfQbvIV", 1], [5, 54, 56], [-63.908, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "New Sprite", 5, [[4, 1, -29, [19], 20]], [0, "47oFTD1W9JLb7y8aWoMdhp", 1], [5, 256, 124]], [12, "lab", 5, [[22, "11", 10, 0, false, 5, 1, 1, -30, [21], 22]], [0, "ecnJk/ROBKxbanhgOvASJI", 1], [4, 4294901502], [5, 69, 0], [-4.175, 23.302, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 5, 1, 0, -1, 2, 0, -2, 3, 0, -3, 5, 0, 0, 2, 0, -1, 6, 0, -2, 4, 0, -3, 8, 0, -4, 9, 0, -5, 10, 0, -6, 11, 0, -7, 12, 0, 0, 3, 0, -1, 13, 0, -2, 14, 0, -1, 7, 0, 6, 7, 0, 0, 4, 0, -1, 15, 0, -2, 16, 0, 0, 6, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 7, 1, 30], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7], [-1, 1, -1, -1, 1, -1, 3, -1, 3, -1, 4, -1, 1, -1, 1, -1, 3, -1, 1, -1, 1, -1, 4, 1], [0, 3, 0, 0, 4, 1, 2, 1, 2, 0, 5, 0, 6, 0, 7, 1, 8, 0, 9, 0, 10, 0, 11, 12]], [[{"name": "res", "rect": [29, 23, 49, 64], "offset": [-0.5, -1], "originalSize": [108, 108], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [13]], [[{"name": "countdownbg", "rect": [0, 0, 96, 96], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [14]], [[{"name": "tishi", "rect": [0, 0, 208, 63], "offset": [0, 0], "originalSize": [208, 63], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [15]], [[{"name": "back2", "rect": [1, 0, 121, 120], "offset": [0, 0], "originalSize": [123, 120], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [16]]]]