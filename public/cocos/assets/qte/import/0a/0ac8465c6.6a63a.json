[1, ["ecpdLyjvZBwrvm+cedCcQy", "542sSC06hFMKPIr/o/YhCN", "54VKXMICBNAJH/HVSLmY4d", "a321p8ke1O+qtZddn3oiH3", "70+38U8p1H7qUvQzC/ilhJ", "74Z6sSY3xFcJRy7TzHmXqI", "bdO11EmVBK0545s4turoYg", "5c7wlEoldFYZ/7jpVsSsra", "44cVfgzxBMioazmki7A67j", "938NF6ts5D5JGLh8ycM4pA", "9b3BvTbPJJIadLeZOmyJ6Z", "0dTleqe4JJ5o6/5S+E/82i", "7cqH/VYKpKN6DBPQutzx5K", "3chd+gdtdBKbAiQm5PEu8K"], ["node", "_textureSetter", "_spriteFrame", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite", "root", "next", "pre", "<PERSON><PERSON><PERSON><PERSON>", "result", "content", "data", "Edit", "HtoP", "PtoH", "End"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs"], 1, 4, 5, 9, 1, 2, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["51fc8FlIFNH65zLtnWgwFGE", ["node", "content", "result", "<PERSON><PERSON><PERSON><PERSON>", "pre", "next", "Edit", "HtoP", "PtoH", "End"], 3, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalSprite", "_N$pressedSprite", "_N$hoverSprite"], 2, 1, 6, 6, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1]], [[2, 0, 1, 2], [1, 0, 1, 5, 2, 3, 3], [1, 0, 5, 4, 2, 3, 7, 2], [3, 2, 3, 4, 1], [6, 0, 1, 2, 3, 4, 2], [4, 0, 2], [1, 0, 6, 4, 2, 3, 2], [1, 0, 1, 5, 4, 2, 3, 3], [5, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [2, 1, 1], [3, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4]], [[[[5, "write"], [6, "write", [-8, -9, -10, -11, -12, -13], [[8, -7, -6, -5, -4, -3, -2, 12, 13, 14, 15]], [9, -1], [5, 1280, 720]], [2, "next", 1, [[3, -14, [2], 3], [4, 2, -15, 4, 5, 6]], [0, "04I4gxujNEmqg3UXU3dU7H", 1], [5, 114, 114], [574, -283, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "pre", 1, [[3, -16, [7], 8], [4, 2, -17, 9, 10, 11]], [0, "d0XBOVfddI7Zf+hUWaCM1a", 1], [5, 114, 114], [-573.112, -283.583, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bottom", 512, 1, [[10, 2, false, -18, [0], 1], [11, 45, 100, 100, -19]], [0, "a5lPUrlOFGmaYBNv/vJs2J", 1], [5, 1280, 720]], [1, "content", 512, 1, [0, "6eBjg9RaxMp4wTZ6DSB2gh", 1], [5, 1280, 720]], [1, "result", 512, 1, [0, "a6uaNG7VRJKZSwDTLyfYwt", 1], [5, 1280, 720]], [1, "<PERSON><PERSON><PERSON><PERSON>", 512, 1, [0, "8fc7i350VO84UEEnAQ7hFE", 1], [5, 1280, 720]]], 0, [0, 6, 1, 0, 7, 2, 0, 8, 3, 0, 9, 7, 0, 10, 6, 0, 11, 5, 0, 0, 1, 0, -1, 4, 0, -2, 5, 0, -3, 2, 0, -4, 3, 0, -5, 6, 0, -6, 7, 0, 0, 2, 0, 0, 2, 0, 0, 3, 0, 0, 3, 0, 0, 4, 0, 0, 4, 0, 12, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, 3, 4, 5, -1, 2, 3, 4, 5, 13, 14, 15, 16], [0, 5, 0, 1, 1, 2, 2, 0, 3, 3, 4, 4, 6, 7, 8, 9]], [[{"name": "alice_btn_2_selected", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [10]], [[{"name": "alice_btn_2_normal", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [11]], [[{"name": "alice_btn_1_selected", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [12]], [[{"name": "alice_btn_1_normal", "rect": [0, 0, 114, 114], "offset": [0, 0], "originalSize": [114, 114], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [13]]]]