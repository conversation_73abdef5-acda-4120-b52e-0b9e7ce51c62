[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "editbox.d", "/*\r\n * <AUTHOR> ya<PERSON><PERSON><PERSON><PERSON>\r\n * @description  : editBox配置文件\r\n */\r\n\r\ndeclare namespace editbox {\r\n  export class EditBoxTool extends cc.Component {\r\n    // 文本粗体状态\r\n    public enableBold: boolean = false;\r\n    // 文本倾斜状态\r\n    public enableItalic: boolean = false;\r\n    // 文本下划线状态\r\n    public enableUnderline: boolean = false;\r\n    // 文本是否固定宽高\r\n    public widthHeightFixed: boolean = false;\r\n    // 文本字号\r\n    public fontSize: number = 30;\r\n    // 输入框行高\r\n    public lineHeight: number = 40;\r\n    // 输入框行距\r\n    public rowSpace: number = 1;\r\n    // 文本颜色\r\n    public color: string = \"#FFFFFF\";\r\n    // 默认文本\r\n    public string: string = \"请输入文本\";\r\n    // 选中次数\r\n    public pikerviewStatue: number = 0;\r\n    // 是否第一次选中\r\n    public isSelectedFirst: boolean = true;\r\n    // 是否在编辑状态\r\n    public isEditing: boolean = false;\r\n    // 创建后是否获取焦点\r\n    public createWithEdit: boolean = false;\r\n    public editable:any =null;\r\n    /**\r\n     * 更新node尺寸\r\n     */\r\n    // eslint-disable-next-line accessor-pairs\r\n    public set updateNodeSize(value: () => void);\r\n\r\n    /**\r\n     * 更新输入框属性\r\n     */\r\n    // eslint-disable-next-line accessor-pairs\r\n    public set updateEditProperties(value: (data) => void);\r\n\r\n    /**\r\n     * 因为修改文本更新输入框属性\r\n     */\r\n    // eslint-disable-next-line accessor-pairs\r\n    public set updateEditPropertiesWithStr(value: (data) => void);\r\n\r\n    // 文本转图片\r\n    public set textToImg(value: (data) => void);\r\n    public set writeToText(value: (data) => void);\r\n\r\n    /**\r\n     * 生成可编辑 富文本\r\n     */\r\n    public createEditBox(): void;\r\n\r\n    /**\r\n     * 通过富文本字符串 生成 （不可）可编辑文本框   题板使用\r\n     * @param {string}string 富文本格式字符串\r\n     * @param {isEditable} boolean 生成是否可编辑的文本\r\n     * @warn  调用此方法之前，要先调用 setEditBoxHorizontalStatue();设置文本的对齐方式，默认为左对齐\r\n     * @warn  调用此方法之前，要先调用 setNodeSize(),设置node的尺寸\r\n     */\r\n    public createEditBoxWithString(\r\n      string: string,\r\n      isEditable: boolean = true,\r\n      isSdk: boolean = false\r\n    ): Promise<void>;\r\n\r\n    /**\r\n     * 设置node尺寸\r\n     */\r\n    public setNodeSize(width: number, height: number): void;\r\n\r\n    /**\r\n     * 不可编辑富文本的 对齐方式\r\n     * @param {string}statue 对齐方式\r\n     * @warn 默认为左对齐\r\n     */\r\n    public setEditBoxHorizontalStatue(statue: layoutHorizontallys): void;\r\n\r\n    /**\r\n     *  点击按下\r\n     */\r\n    public mouseBeginLayout(event: cc.Event.EventMouse, pos = null): void;\r\n\r\n    /**\r\n     *  滑动选中文本\r\n     */\r\n    public mouseMoveLayout(event: cc.Event.EventMouse, pos = null): void;\r\n\r\n    /**\r\n     *  滚动文本\r\n     */\r\n    public mouseWheelLayout(event: cc.Event.EventMouse, pos = null): void;\r\n\r\n    /**\r\n     * 编辑状态下 ，按下上下左右方向键\r\n     * @param keyNum 键值\r\n     * @param shift 是否按下 Shift 键\r\n     */\r\n    public clickDirectionKey(keyNum: number, shift?: boolean): void;\r\n\r\n    /**\r\n     * 编辑状态下 ，按下 Ctrl+A 键\r\n     */\r\n    public clickCtrlA(): void;\r\n\r\n    /**\r\n     * 编辑状态下 ，按下 Ctrl+C 键\r\n     */\r\n    public clickCtrlC(): void;\r\n\r\n    /**\r\n     * 编辑状态下 ，按下 Ctrl+X 键\r\n     */\r\n    public clickCtrlX(): void;\r\n\r\n    /**\r\n     * 编辑状态下 ，按下删除键\r\n     */\r\n    public clickDeleteKey(): void;\r\n\r\n    /**\r\n     *  点击抬起\r\n     */\r\n    public mouseUpLayout(): void;\r\n\r\n    /**\r\n     * 获取输入框矩形\r\n     */\r\n    public rect();\r\n\r\n    /**\r\n     * 输入框成为选中状态\r\n     */\r\n    public setSelected(): void;\r\n\r\n    /**\r\n     * 输入框失去选中状态\r\n     */\r\n    public cancelSelected(): number;\r\n\r\n    /**\r\n     * 转换成texture\r\n     */\r\n    public convertToTexture(): void;\r\n\r\n    /**\r\n     * 设置选中文本粗体\r\n     */\r\n    public setBoldEnabled(data: boolean): boolean;\r\n\r\n    /**\r\n     * 设置选中文本倾斜\r\n     */\r\n    public setItalicEnabled(data: boolean): boolean;\r\n\r\n    /**\r\n     * 设置选中文本下划线\r\n     */\r\n    public setUnderLine(data: boolean): boolean;\r\n\r\n    /**\r\n     * 设置选中文本是否固定宽高\r\n     */\r\n    public setIsFixed(data: boolean): boolean;\r\n\r\n    /**\r\n     * 设置文本行间距\r\n     */\r\n    public setRowSpace(data: number): number;\r\n\r\n    /**\r\n     * 设置选中文本字体\r\n     */\r\n    public setFontSize(data: number): number;\r\n\r\n    /**\r\n     * 设置输入框的行高\r\n     */\r\n    public setLineHeight(data: number): number;\r\n\r\n    /**\r\n     * 设置输入框的字体颜色\r\n     */\r\n    public setFontColor(data: string): string;\r\n\r\n    /**\r\n     * 设置输入框的宽\r\n     */\r\n    public setNodeWidth(data: number, fixed?: boolean): void;\r\n\r\n    /**\r\n     * 设置输入框的高\r\n     */\r\n    public setNodeHeight(data: number): void;\r\n\r\n    public setNodeAngle(): void;\r\n\r\n    /**\r\n     * 设置对齐方式\r\n     * @param data 对齐方式     0:左对齐  1:中对齐  2.右对齐\r\n     * @warn 默认为左对齐\r\n     */\r\n    public setHorizontalAlign(data: number): number;\r\n\r\n    /**\r\n     * 输入框内容转富文本格式字符串\r\n     *return 返回 富文本格式字符串\r\n     */\r\n    public convertToRichText(): string;\r\n\r\n    /**\r\n     * 题板使用\r\n     * @param colorList  每个字的下角标对应着 需要修改的颜色\r\n     * @param 返回值      每个下角标对应的旧的颜色\r\n     */\r\n    public changeColorByIndex(colorList: {}): {};\r\n\r\n    /**\r\n     * 题板使用 获取文本内容\r\n     */\r\n    public getEditBoxLabel(): string;\r\n\r\n    /**\r\n     * 设计光标在文字左右状态\r\n     * @param data 布尔值，默认 true\r\n     */\r\n    public setCursorIsLabelRight(data: boolean): boolean;\r\n\r\n    /**\r\n     * 设置光标周边相关 文字的下角标\r\n     * @param data  index值，number\r\n     */\r\n    public setCursorLabelIndex(data: number): number;\r\n\r\n    public convertToEditBox(data: string): string;\r\n\r\n    /**\r\n     * 开始输入模式\r\n     */\r\n    public startEdit(): void;\r\n  }\r\n}\r\n"]], 0, 0, [], [], []]