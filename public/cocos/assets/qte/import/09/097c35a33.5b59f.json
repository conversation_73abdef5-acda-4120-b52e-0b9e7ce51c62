[1, ["ecpdLyjvZBwrvm+cedCcQy", "eesgdixshOUa8lxmFO+rUb", "c2Dphrr71J05V8Um/299+k", "20NTKzzBZH07AoPrmIqA6x", "34ogpqywtNqaIG0XchOhhB", "27GrQn2FZFh67S/etxswqB", "e9RNpX7GJHAYTsoLFGv/M6", "8cIxjsOF5B+q8SzSn9FtrY", "e9r9zNWhlLpYZlaYebPrHA", "23/mivwmdEqI4KRdC6bX/J", "bfi/LenaVMLJcWqGjZq+UR", "83mkI4D3dNVqjaFULqfF0Z", "755PeldrtFtKEAdMbJaMib", "58Q10xwe9HlaKTJhfGWdkn", "3901dKl2RJlp5H/uAGG+Zn", "4fA7qTWkJN7Kr75+jM8pc3"], ["node", "_spriteFrame", "_textureSetter", "root", "_scrollView", "lostNode", "gradeLabel", "subMBt", "soundLostLabel", "time<PERSON><PERSON><PERSON>", "result", "bottom", "questionNode", "resultTipsLabel", "timeTitleLabel", "tipsDecLabel", "qViewNode", "<PERSON><PERSON><PERSON><PERSON>", "data", "_parent", "dingAudio"], [["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_anchorPoint", "_color"], 0, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Label", ["_fontSize", "_string", "_N$verticalAlign", "_lineHeight", "_N$horizontalAlign", "_N$cacheMode", "_styleFlags", "_N$overflow", "node", "_materials"], -5, 1, 3], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_top", "_bottom", "_right", "alignMode", "node"], -5, 1], ["cc.Node", ["_name", "_opacity", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color", "_children"], 0, 1, 2, 4, 5, 5, 7, 5, 2], "cc.SpriteFrame", ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingBottom", "_N$paddingTop", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cb4a7o7pEpOXKs3WaVoZ14K", ["node", "<PERSON><PERSON><PERSON><PERSON>", "qViewNode", "tipsDecLabel", "timeTitleLabel", "resultTipsLabel", "questionNode", "bottom", "result", "time<PERSON><PERSON><PERSON>", "soundLostLabel", "subMSp", "subMBt", "gradeLabel", "lostNode", "dingAudio"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -2, 1, 1, 1], ["cc.RichText", ["_N$string", "_N$fontSize", "_N$cacheMode", "_N$maxWidth", "_N$lineHeight", "_N$handleTouchEvent", "node"], -3, 1]], [[7, 0, 1, 2], [1, 1, 0, 4, 2, 8, 9, 5], [0, 0, 6, 5, 3, 10, 4, 9, 8, 2], [4, 0, 3, 4, 5, 9, 6, 7, 8, 2], [0, 0, 6, 5, 3, 4, 8, 2], [2, 0, 3, 4, 5, 2], [1, 1, 0, 3, 4, 2, 8, 9, 6], [0, 0, 6, 5, 3, 10, 4, 8, 2], [2, 0, 1, 2, 3, 4, 5, 4], [3, 0, 6, 8, 3], [0, 0, 6, 7, 5, 3, 4, 9, 2], [0, 0, 6, 7, 5, 3, 4, 8, 2], [6, 0, 2, 7, 3, 4, 5, 8, 6, 2], [6, 0, 1, 2, 7, 3, 4, 5, 8, 6, 3], [4, 0, 1, 3, 4, 5, 6, 7, 8, 3], [2, 3, 4, 5, 1], [2, 0, 1, 2, 3, 4, 4], [3, 0, 1, 2, 8, 4], [3, 7, 0, 3, 2, 8, 5], [12, 0, 1, 2, 2], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4, 5, 6, 7, 6], [9, 0, 2], [0, 0, 2, 7, 5, 3, 4, 3], [0, 0, 1, 6, 7, 5, 3, 4, 3], [0, 0, 6, 7, 3, 4, 8, 2], [0, 0, 7, 5, 3, 4, 9, 2], [0, 0, 6, 7, 5, 3, 4, 9, 8, 2], [0, 0, 1, 6, 7, 5, 3, 10, 4, 8, 3], [6, 0, 2, 3, 4, 5, 6, 2], [4, 0, 2, 3, 10, 4, 5, 9, 6, 7, 8, 3], [4, 0, 1, 3, 4, 5, 9, 6, 7, 8, 3], [2, 0, 3, 4, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 1], [7, 1, 1], [11, 0, 1], [3, 0, 4, 5, 1, 2, 8, 6], [3, 0, 3, 1, 8, 4], [8, 0, 1, 3, 2, 4, 5, 5], [8, 0, 1, 2, 4, 5, 4], [1, 1, 0, 6, 4, 2, 8, 9, 6], [1, 0, 3, 7, 5, 8, 9, 5], [1, 1, 0, 3, 2, 5, 8, 9, 6], [1, 1, 0, 3, 2, 8, 9, 5], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3], [17, 0, 1, 2, 3, 4, 5, 6, 7]], [[[{"name": "icon1", "rect": [0, 0, 24, 24], "offset": [0, 0], "originalSize": [24, 24], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [2], [4]], [[[22, "ReadPageComponent"], [23, "template", 512, [-17, -18, -19, -20], [[15, -2, [36], 37], [33, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, -6, [38, 39], -5, -4, -3, 40]], [34, -1], [5, 1280, 720]], [24, "result", false, 1, [-23, -24, -25, -26, -27, -28, -29], [[35, -21], [15, -22, [34], 35]], [0, "18pcngTFRKjZWIE/gF00gC", 1], [5, 1280, 720]], [12, "questionNode", 1, [-32, -33, -34, -35], [[[8, 1, 1, 0, -30, [9], 10], -31], 4, 1], [0, "558fcRXRFAjaH0LiJKOa1y", 1], [5, 1230, 440], [0, 0, 1], [-613, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "iconNode", 2, [-36, -37, -38, -39, -40, -41], [0, "402T8IyYlLc7ktWvH5KBZe", 1], [5, 248, 59], [-478, 273, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "content", [-45], [[17, 40, 220, 250, -43], [38, 1, 2, 22, 32, -44, [5, 1183, 111.96000000000001]]], [0, "0b1dZ5CuhBdanIvFbADEg6", -42], [5, 1183, 111.96000000000001], [0, 0, 1]], [27, "view", 3, [-48], [[19, 0, -46, [2]], [36, 45, 99.99999999999997, 1.9539925233402755e-14, 240, 250, -47]], [0, "53+Wl6sRNCIYyUxujpAJYA", 1], [5, 1230, 340], [0, 0, 1], [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "lostRank", false, 2, [-51], [[40, "评级丢失了", 26, 1, 1, 1, -49, [26]], [9, 32, 49.18399999999997, -50]], [0, "79k+qw5wVDfpBw4ji0mszV", 1], [4, 4288913565], [5, 130, 51], [525.816, -273.516, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "list", 2, [-54, -55], [[[8, 1, 1, 0, -52, [32], 33], -53], 4, 1], [0, "c7g8qtguFCMIcMW2ovTeIa", 1], [5, 1183, 439.3], [0, 0, 1], [-591, 240, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "content", 6, [-57], [[39, 1, 2, 32, -56, [5, 300, 78]]], [0, "d6uB2gkRtFYahK7ERAMZpP", 1], [5, 300, 78], [0, 0, 1]], [13, "scrollBar", 0, 3, [-60], [[-58, [18, 0, 37, 350.07654921020657, 237, -59]], 1, 4], [0, "ccjp9cBvlOCaa3z4FcHM2l", 1], [5, 12, 440], [0, 1, 0.5], [1230, -220, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "downTimeNode", 3, [-62, -63], [[8, 1, 1, 0, -61, [6], 7]], [0, "bbg816+ypMFa3yLMapQXb3", 1], [5, 258, 88], [1101.415, -42.909, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bottom", 1, [-65], [[37, 44, 990.5, 1280, -64]], [0, "7d/WQwZ91MSYAV9JJqXdRu", 1], [5, 289.5, 158], [495.25, -281, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "grade", false, 2, [-67, -68], [-66], [0, "3dCmP4jdVDJoX+X3a3uQr/", 1], [4, 4288859181], [5, 72, 89], [0, 1, 0.5], [555.27, -275.874, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "view", 8, [5], [[19, 0, -69, [30]], [17, 45, 240, 250, -70]], [0, "63oUaeBIdDCZLE8v8mGgLw", 1], [5, 1183, 439.3], [0, 0, 1]], [13, "scrollBar", 0, 8, [-73], [[-71, [18, 0, 37, 350.07654921020657, 237, -72]], 1, 4], [0, "19DV36RYZM6qkQh/8fu/XX", 1], [5, 12, 439.3], [0, 1, 0.5], [1183, -219.65, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "nextBtn", 12, [[[44, 3, -74, [[45, "cb4a7o7pEpOXKs3WaVoZ14K", "startResult", 1]]], -75], 4, 1], [0, "f0UA2tLzxFrKpnQOCne9Ky", 1], [5, 176, 64], [9.828, 15.565, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "myRank", 7, [[1, "评级：", 28, 1, 1, -76, [25]], [9, 32, 144.05200000000002, -77]], [0, "e6nWutnkNBGZ4A514o/H6j", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-79.05200000000002, 0.304, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "myRank", 13, [[1, "评级：", 28, 1, 1, -78, [28]], [9, 32, 87.953, -79]], [0, "c03rf4iopNj7wIIsE2TrtO", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-87.953, 3.431, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "title", 1, [[1, "朗读短文", 30, 1, 1, -80, [0]]], [0, "43EhzewKVC+okoNp515rET", 1], [4, 4284900450], [5, 120, 51], [0, 0, 0.5], [-591.935, 306.105, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "questionDec", 9, [-81], [0, "9eZNzymp9H86weVcJdG5/g", 1], [4, 4279505940], [5, 1184, 46], [0, 0, 1], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [41, 32, 46, 3, 2, 20, [1]], [14, "bar", 0, 10, [-82], [0, "2eyg4jIrdEJrry9FzXUaZ2", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 1, 1, 0, 22, [3]], [20, 1, 10, 23], [21, false, 0.75, false, 0.23, null, 3, 9, 24], [3, "timeTitle", 11, [-83], [0, "7fBBjbW6tFTLPggxC4ggq1", 1], [4, 4284900450], [5, 96, 51], [0, 1, 0.5], [18.947, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "倒计时：", 24, 1, 1, 26, [4]], [3, "time", 11, [-84], [0, "99EqgSsdVMLqj8YkqohWhx", 1], [4, 4279505940], [5, 23, 51], [0, 0, 0.5], [19.962, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "- -", 24, 1, 1, 28, [5]], [31, "dec", 153, 3, [-85], [0, "dcbCA9bxhNpJunRFw8qDqB", 1], [4, 4278190080], [5, 329.38, 30], [0, 0, 0.5], [24, -39.515, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "你在，请在120秒内完成作答", 26, 30, 1, 2, 30, [8]], [32, 1, 16, [11]], [4, "icon0", 4, [[5, 1, -86, [12], 13]], [0, "8du2Qv3lRMCa4c+s2HvAjo", 1], [5, 24, 24], [-102.752, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "icon1", 4, [[5, 1, -87, [14], 15]], [0, "b8W10ix49OVaL9EqLZxU5a", 1], [5, 24, 24], [-15.735, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "icon2", 4, [[5, 1, -88, [16], 17]], [0, "4dYPnoiglCRJVoGfxnbgnm", 1], [5, 24, 24], [70.108, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "la0", 4, [[6, "优", 24, 33, 1, 1, -89, [18]]], [0, "c5hi5kuupOxaF8ZrpsJC/8", 1], [4, 4284900966], [5, 24, 42], [-68.834, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "la1", 4, [[6, "良", 24, 33, 1, 1, -90, [19]]], [0, "56HIEtBf9MIqeVfLve5o6R", 1], [4, 4284900966], [5, 24, 42], [17.752, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "la2", 4, [[6, "差", 24, 33, 1, 1, -91, [20]]], [0, "45rspS+blB6qIRdKnvgRHc", 1], [4, 4284900966], [5, 24, 42], [105.526, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "resultTitle", 2, [[5, 1, -92, [21], 22]], [0, "abgU+JhotPEa9rXX8rtQ/h", 1], [5, 272, 34], [0, 291.755, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "mySound", 2, [[1, "我的录音：", 28, 1, 1, -93, [23]]], [0, "06/2xI/yZNiY83XbZgRBGi", 1], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [-451.102, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "mySoundLost", 2, [-94], [0, "4aZxe6EAhA27GoSpwjHXJs", 1], [4, 4288913565], [5, 196, 51], [0, 0, 0.5], [-436.678, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "未生成我的录音", 28, 1, 1, 41, [24]], [2, "unit", 13, [[43, "分", 28, 30, 1, -95, [27]]], [0, "972J0vccdJlZP4PPiLfs0N", 1], [4, 4288913565], [5, 28, 38], [0, 0, 0.5], [8.808, 1.806, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "89", 64, 70, 1, 1, 13, [29]], [3, "startNewtLabel", 5, [-96], [0, "194mLONDpChK3KMc9xVX+V", 5], [4, 4279505940], [5, 1088, 57.96], [0, 0, 1], [48, -22, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "", 32, 2, 1088, 46, false, 45], [14, "bar", 0, 15, [-97], [0, "6bgx9o0yFBorF3zEyBcKcI", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, 1, 1, 0, 47, [31]], [20, 1, 15, 48], [21, false, 0.75, false, 0.23, null, 8, 5, 49]], 0, [0, 3, 1, 0, 0, 1, 0, 5, 7, 0, 6, 44, 0, 7, 32, 0, 8, 42, 0, 9, 29, 0, 10, 2, 0, 11, 12, 0, 12, 3, 0, 13, 46, 0, 14, 27, 0, 15, 31, 0, 16, 6, 0, 17, 21, 0, 0, 1, 0, -1, 19, 0, -2, 3, 0, -3, 12, 0, -4, 2, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, -2, 39, 0, -3, 40, 0, -4, 41, 0, -5, 7, 0, -6, 13, 0, -7, 8, 0, 0, 3, 0, -2, 25, 0, -1, 6, 0, -2, 10, 0, -3, 11, 0, -4, 30, 0, -1, 33, 0, -2, 34, 0, -3, 35, 0, -4, 36, 0, -5, 37, 0, -6, 38, 0, 3, 5, 0, 0, 5, 0, 0, 5, 0, -1, 45, 0, 0, 6, 0, 0, 6, 0, -1, 9, 0, 0, 7, 0, 0, 7, 0, -1, 17, 0, 0, 8, 0, -2, 50, 0, -1, 14, 0, -2, 15, 0, 0, 9, 0, -1, 20, 0, -1, 24, 0, 0, 10, 0, -1, 22, 0, 0, 11, 0, -1, 26, 0, -2, 28, 0, 0, 12, 0, -1, 16, 0, -1, 44, 0, -1, 43, 0, -2, 18, 0, 0, 14, 0, 0, 14, 0, -1, 49, 0, 0, 15, 0, -1, 47, 0, 0, 16, 0, -2, 32, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, -1, 21, 0, -1, 23, 0, -1, 27, 0, -1, 29, 0, -1, 31, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, -1, 42, 0, 0, 43, 0, -1, 46, 0, -1, 48, 0, 18, 1, 5, 19, 14, 24, 4, 25, 49, 4, 50, 97], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 32, 48], [-1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -2, 20, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 0, 0, 6, 0, 7, 0, 8, 0, 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 10, 0, 11, 2, 12, 13, 3, 2, 3]], [[{"name": "icon0", "rect": [0, 0, 24, 24], "offset": [0, 0], "originalSize": [24, 24], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [2], [14]], [[{"name": "icon2", "rect": [0, 0, 24, 24], "offset": [0, 0], "originalSize": [24, 24], "capInsets": [0, 0, 0, 0]}], [5], 0, [0], [2], [15]]]]