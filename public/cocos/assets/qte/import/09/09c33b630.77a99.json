[1, ["ecpdLyjvZBwrvm+cedCcQy", "16634ef31", "6da2cbtDhA+6HsLF+FqTgr", "d3WeyIb5hMj5ukp/WDINnj", "8fBwSAFJFLhYwuj+wAXAxu", "71sohzYbVCl5g6/giMJCJK", "f08Q/T6FpJfJjywUZq5xMM", "b5ijcCgB1J4a6+jMLT5Mo5", "2aCbQtc35LdL43jnLs5aCV", "f1trBguDpLGreeEYqCOvVi", "b37ljVEZhJCqHK4nccITpL", "1bSbV2/8FBqKnl/iJEvu72", "59s5oX0Q9DDrcJsi3Yw4ir", "a7i6g0f2VDjIn6NFaib3lz", "d42mvr86dPEKYoItsCj7Oe", "24QNHUZaJJB4oTAaOMXARc", "37nO9zDNxOhp46rRne9la2", "d7gsLTh+pPSJPY0Lt92okE", "715n3UyKpI+K0RSqa0ppMV", "00BeX+PKpMX5itI6MBreMv", "3bWdgYkh1BJKMPvJErunyN", "609QMalipDmpd1G/swEl9v", "52aMoZNChEyb0MSMGcdFsS", "15Hwula35IPY7JakcWbjsi", "eaEq5+j+lNFZL9xw0dSMNV", "27uZ/ref9BupC9MAGJcjpu", "b5dXDVyY5F46aLAccd4WXo", "4fGppZxtRGB7uJJ8HUEI3m"], ["node", "_spriteFrame", "_textureSetter", "root", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs"], 1, 9, 4, 5, 2, 1, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["cc.Sprite", ["_srcBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6]], [[2, 0, 1, 2, 2], [5, 0, 1, 2, 3, 2], [1, 0, 6, 2, 3, 4, 2], [1, 0, 6, 5, 2, 3, 4, 7, 2], [1, 0, 1, 6, 5, 2, 3, 4, 3], [3, 0, 2], [1, 0, 5, 2, 3, 4, 2], [4, 0, 1, 2, 3, 4, 4], [2, 1, 2, 1]], [[[[5, "entry"], [6, "entry", [-3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14], [[7, 1, 1, 6.5, -2, [5, 1043.5, 81]]], [8, -1, 0], [5, 1043.5, 81]], [3, "1", 1, [-16, -17], [[1, 1, -15, [6], 7]], [0, "411YAIaURJZo1lGrhoRB7y", 1, 0], [5, 81, 81], [-481.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "2", 1, [-19, -20], [[1, 1, -18, [14], 15]], [0, "b5RpXE+AJPG5POx27by7Zl", 1, 0], [5, 81, 81], [-393.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "3", 1, [-22, -23], [[1, 1, -21, [22], 23]], [0, "abcLuv6HBOFJB3GzVfy2Ew", 1, 0], [5, 81, 81], [-306.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "4", 1, [-25, -26], [[1, 1, -24, [30], 31]], [0, "34pdh/pFFAMbMNCNil6FIl", 1, 0], [5, 81, 81], [-218.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "5", 1, [-28, -29], [[1, 1, -27, [38], 39]], [0, "b7akuwYnhLSbREoGq0ouWo", 1, 0], [5, 81, 81], [-131.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "6", 1, [-31, -32], [[1, 1, -30, [46], 47]], [0, "35YIfLm6tLHaZXQN4QaRg2", 1, 0], [5, 81, 81], [-43.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "7", 1, [-34, -35], [[1, 1, -33, [54], 55]], [0, "26DGcvkDlJyJgjAJ3mjYz9", 1, 0], [5, 81, 81], [43.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "8", 1, [-37, -38], [[1, 1, -36, [62], 63]], [0, "00A9cSRzxF861E+MoNSgD5", 1, 0], [5, 81, 81], [131.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "9", 1, [-40, -41], [[1, 1, -39, [70], 71]], [0, "b4IiafK9pCJ5exlz3M2emJ", 1, 0], [5, 81, 81], [218.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "0", 1, [-43, -44], [[1, 1, -42, [78], 79]], [0, "27BSEwTq9ORYmcfRrc8meQ", 1, 0], [5, 81, 81], [306.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, ".", 1, [-46, -47], [[1, 1, -45, [86], 87]], [0, "b8FbEINsVLL7V9QKaej/iW", 1, 0], [5, 81, 81], [393.75, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "x", 1, [-49, -50], [[1, 1, -48, [94], 95]], [0, "fccV3n21tHkZVOsHaDL6Xp", 1, 0], [5, 81, 81], [481.25, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "Bg", false, 2, [-52], [[1, 1, -51, [4], 5]], [0, "c6jgW14VRHmoMOgwJObhpG", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 3, [-54], [[1, 1, -53, [12], 13]], [0, "52ODWyhTFDXJlbfmjTZ9KQ", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 4, [-56], [[1, 1, -55, [20], 21]], [0, "b7+TKzKtpOL6EPiO4nhbsN", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 5, [-58], [[1, 1, -57, [28], 29]], [0, "4dglMbmE5Fh6oAsIqVGI6g", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 6, [-60], [[1, 1, -59, [36], 37]], [0, "57FSVLlE5G5r30yEqHIxH6", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 7, [-62], [[1, 1, -61, [44], 45]], [0, "a7UUQ7ixRJEIxeqnWt97b0", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 8, [-64], [[1, 1, -63, [52], 53]], [0, "d0TxJfae1Nm6mP6hGOSXLm", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 9, [-66], [[1, 1, -65, [60], 61]], [0, "63qZlZctdEG59bJqqSfSPk", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 10, [-68], [[1, 1, -67, [68], 69]], [0, "13KXjz2wtK2baDezchiN/5", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 11, [-70], [[1, 1, -69, [76], 77]], [0, "752Prxgs1LVZF30MT7eQzF", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 12, [-72], [[1, 1, -71, [84], 85]], [0, "d5pLmdUT1Kmo3SswOjiVhY", 1, 0], [5, 81, 81]], [4, "Bg copy", false, 13, [-74], [[1, 1, -73, [92], 93]], [0, "782mNwM1xJUK/0wbXcWlGo", 1, 0], [5, 81, 81]], [2, "New Sprite", 2, [[1, 1, -75, [0], 1]], [0, "d19XI/rpRLjb3APU1bcdb7", 1, 0], [5, 22, 41]], [2, "1_2", 14, [[1, 1, -76, [2], 3]], [0, "67txYHkKRMPYN+tZgnTjQs", 1, 0], [5, 22, 41]], [2, "New Sprite", 3, [[1, 1, -77, [8], 9]], [0, "8f4deSrwJJ86hcd6q3G9Oa", 1, 0], [5, 33, 40]], [2, "1_2", 15, [[1, 1, -78, [10], 11]], [0, "81ObNn5upMHo4LsSneR88E", 1, 0], [5, 33, 40]], [2, "New Sprite", 4, [[1, 1, -79, [16], 17]], [0, "d4M0RAzP1NibEXSE2buVlv", 1, 0], [5, 32, 41]], [2, "1_2", 16, [[1, 1, -80, [18], 19]], [0, "dbMLno9FJL94zb+NABfR2R", 1, 0], [5, 32, 41]], [2, "New Sprite", 5, [[1, 1, -81, [24], 25]], [0, "16x44dY1RLCIq2GFrfI3CE", 1, 0], [5, 35, 41]], [2, "1_2", 17, [[1, 1, -82, [26], 27]], [0, "abQwR6NvVE0JrvkCeloGzE", 1, 0], [5, 35, 41]], [2, "New Sprite", 6, [[1, 1, -83, [32], 33]], [0, "e4n7af3p9DGZyug04d5xx4", 1, 0], [5, 32, 40]], [2, "1_2", 18, [[1, 1, -84, [34], 35]], [0, "3d5ZfFBtdAcYRaIXK2niN9", 1, 0], [5, 32, 40]], [2, "New Sprite", 7, [[1, 1, -85, [40], 41]], [0, "a7AAMcQk5BPIEBhQAiLXZE", 1, 0], [5, 34, 41]], [2, "1_2", 19, [[1, 1, -86, [42], 43]], [0, "323J391WZFf7yLO7fNtVBi", 1, 0], [5, 34, 41]], [2, "New Sprite", 8, [[1, 1, -87, [48], 49]], [0, "afDRv3nh9EvJJmNJ9jaBqI", 1, 0], [5, 33, 40]], [2, "1_2", 20, [[1, 1, -88, [50], 51]], [0, "fflQknt1xDpbbb33ZMfJoL", 1, 0], [5, 33, 40]], [2, "New Sprite", 9, [[1, 1, -89, [56], 57]], [0, "d6Sz4s7F1EJYS9frDIzSNA", 1, 0], [5, 34, 41]], [2, "1_2", 21, [[1, 1, -90, [58], 59]], [0, "79HHS9lotD8b+7f7TRuOCQ", 1, 0], [5, 34, 41]], [2, "New Sprite", 10, [[1, 1, -91, [64], 65]], [0, "53hv/eBylNcISQcosBv9kK", 1, 0], [5, 34, 41]], [2, "1_2", 22, [[1, 1, -92, [66], 67]], [0, "8ciJvnA6ZJWJLMlWbgugce", 1, 0], [5, 34, 41]], [2, "New Sprite", 11, [[1, 1, -93, [72], 73]], [0, "08nfSJ0SdBUq5C8tnOz6Qx", 1, 0], [5, 35, 41]], [2, "1_2", 23, [[1, 1, -94, [74], 75]], [0, "0du+yFDM1Im4V2X4fCCsKM", 1, 0], [5, 35, 41]], [2, "New Sprite", 12, [[1, 1, -95, [80], 81]], [0, "afXSODSYhHab/m9dhAYfYc", 1, 0], [5, 9, 9]], [2, "1_2", 24, [[1, 1, -96, [82], 83]], [0, "eeC4YV07pM/r+Z3hglza11", 1, 0], [5, 9, 9]], [2, "New Sprite", 13, [[1, 1, -97, [88], 89]], [0, "8frizlvM1Avqgm28GQTECy", 1, 0], [5, 58, 51]], [2, "1_2", 25, [[1, 1, -98, [90], 91]], [0, "a3FiGAKlFNsozd11VnABx+", 1, 0], [5, 58, 51]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, -5, 6, 0, -6, 7, 0, -7, 8, 0, -8, 9, 0, -9, 10, 0, -10, 11, 0, -11, 12, 0, -12, 13, 0, 0, 2, 0, -1, 26, 0, -2, 14, 0, 0, 3, 0, -1, 28, 0, -2, 15, 0, 0, 4, 0, -1, 30, 0, -2, 16, 0, 0, 5, 0, -1, 32, 0, -2, 17, 0, 0, 6, 0, -1, 34, 0, -2, 18, 0, 0, 7, 0, -1, 36, 0, -2, 19, 0, 0, 8, 0, -1, 38, 0, -2, 20, 0, 0, 9, 0, -1, 40, 0, -2, 21, 0, 0, 10, 0, -1, 42, 0, -2, 22, 0, 0, 11, 0, -1, 44, 0, -2, 23, 0, 0, 12, 0, -1, 46, 0, -2, 24, 0, 0, 13, 0, -1, 48, 0, -2, 25, 0, 0, 14, 0, -1, 27, 0, 0, 15, 0, -1, 29, 0, 0, 16, 0, -1, 31, 0, 0, 17, 0, -1, 33, 0, 0, 18, 0, -1, 35, 0, 0, 19, 0, -1, 37, 0, 0, 20, 0, -1, 39, 0, 0, 21, 0, -1, 41, 0, 0, 22, 0, -1, 43, 0, 0, 23, 0, -1, 45, 0, 0, 24, 0, -1, 47, 0, 0, 25, 0, -1, 49, 0, 0, 26, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, 0, 30, 0, 0, 31, 0, 0, 32, 0, 0, 33, 0, 0, 34, 0, 0, 35, 0, 0, 36, 0, 0, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 4, 1, 98], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 4, 0, 5, 0, 2, 0, 3, 0, 6, 0, 7, 0, 2, 0, 3, 0, 8, 0, 9, 0, 2, 0, 3, 0, 10, 0, 11, 0, 2, 0, 3, 0, 12, 0, 13, 0, 2, 0, 3, 0, 14, 0, 15, 0, 2, 0, 3, 0, 16, 0, 17, 0, 2, 0, 3, 0, 18, 0, 19, 0, 2, 0, 3, 0, 20, 0, 21, 0, 2, 0, 3, 0, 22, 0, 23, 0, 2, 0, 3, 0, 24, 0, 25, 0, 2, 0, 3, 0, 26, 0, 27, 0, 2, 0, 3]], [[{"name": "jian<PERSON>_teshu_3_nor", "rect": [49, 270, 32, 41], "offset": [0, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_7_nor", "rect": [67, 231, 33, 40], "offset": [0.5, 0], "originalSize": [80, 80], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_9_nor", "rect": [90, 97, 34, 41], "offset": [0, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jianpan_teshu_0_nor", "rect": [90, 3, 35, 41], "offset": [0.5, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jianpan_teshu_5nor", "rect": [3, 273, 32, 40], "offset": [0, 0], "originalSize": [80, 80], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_bg_click", "rect": [3, 3, 81, 81], "offset": [0, 0], "originalSize": [81, 81], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_8_nor", "rect": [90, 144, 34, 41], "offset": [0, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_1_nor", "rect": [87, 270, 22, 41], "offset": [0, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_4_nor", "rect": [90, 50, 35, 41], "offset": [0.5, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_close_nor", "rect": [3, 177, 58, 51], "offset": [-2.5, 0], "originalSize": [81, 81], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_bg_nor", "rect": [3, 90, 81, 81], "offset": [0, 0], "originalSize": [81, 81], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_6_nor", "rect": [82, 191, 34, 41], "offset": [0, -0.5], "originalSize": [80, 80], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_dian_nor", "rect": [67, 177, 9, 9], "offset": [0.5, -0.5], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]], [[{"name": "jian<PERSON>_teshu_2_nor", "rect": [3, 234, 33, 40], "offset": [0.5, 0], "originalSize": [80, 80], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [1]]]]