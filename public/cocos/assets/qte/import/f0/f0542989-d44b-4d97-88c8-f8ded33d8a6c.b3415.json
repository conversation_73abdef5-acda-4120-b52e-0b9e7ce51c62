[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "qte-api-export.d", "/*\n * @Author: cheng\n * @Date: 2021-09-02 13:52:43\n * @LastEditTime: 2025-02-19 11:26:44\n * @LastEditors: gedengyang_v <EMAIL>\n * @Description: In User Settings Edit\n * @FilePath: /qte_sdk/sdk/assets/qte/qte-api-export.d.ts\n */\ndeclare namespace qte {\n\n    /** QTE初始化公参 */\n\n    export class QTEParams {\n        uid?: string;\n        os?: string;\n    }\n\n    /** 重置按钮显示逻辑枚举 */\n    export enum QTE_RESET_TYPE {\n        /** 默认逻辑,提交后隐藏重置按钮 */\n        DEFAULT = 0,\n        /** 一直显示重置按钮 */\n        SHOW = 1,\n        /** 一直隐藏重置按钮 */\n        HIDE = 2\n    }\n\n    export enum AutoSubmitMode {\n        RIGHT_SUBMIT = 0, // 答对自动提交\n        OPERATE_SUBMIT = 1, //作答即提交\n    }\n\n\n    /** 重置按钮显示逻辑枚举 */\n    export enum QTE_SUBMIT_TYPE {\n        /** 默认逻辑*/\n        DEFAULT = 0,\n        /** 一直显示 */\n        SHOW = 1,\n        /** 一直隐藏 */\n        HIDE = 2\n    }\n    /** 返回结构 */\n    export interface ExportTemData {\n        guideDuration: number;\n        /** 答案 */\n        answer?: string;\n        /** 答案列表 */\n        optionList?: string;\n    }\n    /**\n     * 鲨鱼题版公参\n     */\n    export class QTETemplateCommonParams {\n        type: number;\n        tempType: number;\n        /** 题目类型 */\n        questionType: number;\n        name: string;\n        /** 资源列表, 需要在初始化时加载 */\n        resourceList: string[];\n        /** 封面截图资源地址 */\n        thumbnail: string;\n        /** 题干音频资源地址 */\n        guide: string;\n        width: number;\n        height: number;\n        safeHeight: number;\n        safeWidth: number;\n        /** 背景颜色 */\n        backgroundColor: string;\n        /** 背景图片资源 */\n        texture: string;\n        bundleUrl: string;\n        /** 选择题 1000 拖拽题 1001 填空题 1002 连线题 1003 口述题 1004 画图题 1005 拼图题 1006 特殊题 1028 讲解页 1029 */\n        category: number;\n        /** 题干 */\n        information?: {\n            /** 描述 */\n            desc: string\n        };\n        /** 作答信息 */\n        answerBase?: {};\n        /** 题版key 创建时传入的 */\n        qteKey: string;\n    }\n\n    /**\n     * shark题版生命周期回传参数\n     */\n    export class QTETemplateParams extends QTETemplateCommonParams {\n        /** 是否有作答痕迹 */\n        answerMark: boolean;\n        /** 题目是否正确 */\n        correct: boolean;\n    }\n    /**\n     * 连续题UI参数\n     */\n    export class QTESerialUIParams {\n        questionIndex: number; // 当前题目下标\n        questionCount: number; // 题目总数\n        arrResult?: boolean[] // 作答结果\n        // 小题切换时的回调\n        changeQuestion: (params: number) => Promise<void> | void;\n    }\n\n    /**\n     * shark题版提交周期使用的参数\n     */\n    export class QTETemplateSubmitParams extends QTETemplateParams {\n        submitAnswer: SubmitAnswer;\n    }\n    /**\n     * 每次触发判断正误逻辑回传参数\n     */\n    export class QTETemplateJudgeParams extends QTETemplateParams {\n        /** 本次操作是否正确 */\n        singleCorrect: boolean;\n    }\n\n    /**\n     * shark题版生命周期函数\n     */\n    export interface QTEHook {\n        /** 题版创建完成后调用 */\n        onCreate: (vo: QTETemplateCommonParams) => Promise<void> | void;\n        /** 创建完成onCreate钩子函数执行完,下一帧调用 */\n        onStart: (vo: QTETemplateCommonParams) => Promise<void> | void;\n        /** 题版重置时调用 */\n        onReset: (vo: QTETemplateParams) => Promise<void> | void;\n        /** 题版提交时调用 */\n        onSubmit: (vo: QTETemplateSubmitParams) => Promise<void> | void;\n        /** 题版暂停时调用 */\n        onPause: (vo: QTETemplateParams) => Promise<void> | void;\n        /** 题版回复时调用 */\n        onResume: (vo: QTETemplateParams) => Promise<void> | void;\n        /** 题版销毁时调用 */\n        onDestroy: (vo: QTETemplateParams) => Promise<void> | void;\n        /** 题版触发判断时调用 */\n        onJudge: (vo: QTETemplateJudgeParams) => Promise<void> | void;\n        /** 题版状态数据更改时调用 */\n        onStateChange: (vo: QTETemplateJudgeParams, states: any) => Promise<void> | void;\n\n        /**普通题结束 调用通用反馈弹窗 */\n        onShowResult: (vo: any) => Promise<void> | void;\n    }\n    /**\n     * 对应的wiki地址: https://wiki.zuoyebang.cc/pages/viewpage.action?pageId=300011273\n     */\n    export interface QTEHooks extends Partial<QTEHook> { }\n    // --------------------- 对外暴露的class ----------------------\n    // 适配器\n    export class Adapter {\n\n        playAudio?: (clip: cc.AudioClip, loop: boolean, volume: number, callback: () => void) => number;\n        stopAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;\n        pauseAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;\n        resumeAudio?: (audioItem: { url: string, nId: number, bIsLoop: boolean }) => void;\n        startRecordAction?: (recordData: any, startRecordCb: any, removeRecordCb: any, startEndCb: any) => void;\n        stopRecordAction?: (stopEndCb?: any) => void;\n        checkRecordAuthorityAction?: (callBack) => void;\n        toast?: (content: string) => void;\n        logCat?: (logType, type, args) => void;\n        getUserInfo?: (callBack: Function) => void;\n        //小鹿练字获取作答结果\n        xlPracticeRecover?(callback: Function): void;\n        //小鹿练字--拍照上传\n        uploadImageAutoMark?(data: {\n            type: 1 | 2,\n            characters: string,\n            tImg?: string,\n            tArray?: { tId: number, state: number }[],\n            tID?: string\n        }, callback: Function): void;\n        //小鹿练字--跳转学习报告页面\n        jumpStudyReport?(): void;\n        // 设置页面到最高层\n        setToTopLayer?(childNode: cc.Node): void;\n\n        // bundle或者资源加载失败调用action 检查\n        assetsCheck?(param: { path: string, isAssest?: boolean }): void;\n\n        // 开始识别\n        startRecognitionAction?(param: {}, cb: Function): void;\n        //结束识别\n        stopRecognitionAction?(): void;\n        //检查麦克分权限无弹框\n        checkAudioPermission?(cb: Function): void;\n        // 上报学生端状态\n        uploadstustatus?(param: { status: number }): void;\n        // 点赞送花 互动\n        zanInter?(param: { \"toUid\": string, \"stats\": any[] }): void;\n        // 切座位\n        switch?(param: { timeout: number, round: number, isFinial: boolean }): void;\n\n        // 请求互动题信令恢复\n        interRecover?: () => void;\n\n        // 获取当前用用户信息\n        getUserInfo?: (callBack: Function) => void;\n        // 上台\n        hdAvatarLocate?(param: { num: number, positions: any[], type?: string }): void\n        // 下台\n        hdRemoveAvatar?(param: { uid: any[], type?: string }): void;\n        // 加学分通用反馈\n        corebusInteractResult?: (param: any) => void;\n        // 点赞\n        liveShowThumb?: (param: any) => void;\n\n        // 获取小组学生信息\n        getGroupUserInfo?: (callBack: Function) => void;\n        // 获取系统时间\n        pingTime?(param: Function): void;\n        // 个性化纠音跳转到报告页并关闭容器\n        closeGXHJYContainer?(): void;\n        // 本地存储\n        setLocalData?: (key: string, value: any) => void;\n        // 获取本地存储\n        getLocalData?: (key: string) => any;\n    }\n\n    export var adapter: Adapter;\n    class QuoteNode extends cc.Node {\n        /** 题版play接口，开始播放题干/自动播放的音频 */\n        play(): void;\n        submit(): QTETemplateSubmitParams | undefined;\n        reset(): void;\n        pause(): void;\n        resume(): void;\n        /*** 展示正确答案 */\n        showCorrect(): void;\n        getBaseVo: QTETemplateCommonParams | undefined;\n        async getTemData(): Promise<ExportTemData>;\n        upateCustomData(key: string, data: any);\n        playFeedBackAction(data: any): void;\n        showUserAnswerState(data: any): void;\n        /**编辑器预览获取参考答案/*/\n        setReferenceAnswer(): any;\n        // 切换小题\n        switchQuestion(index?: number): void;\n    }\n\n    // 题版创建的扩展参数\n    class QTEOption {\n        /** \n         * qte主键，用于区分多个qte对象同时存在的值，如果不传入则会默认生产一个uuid作为key值\n         *  例如：\n         *      直播业务中，可以传入pageId作为key，这样主讲端状态同步时就以该key值进行状态同步。\n         *      如果不自定义key值会导致状态同步时，无法找到对应同步的qte实例。\n         */\n        qteKey?: string;\n        resourceMap?: any;\n        // 恢复数据\n        recoverData?: any;\n        /** 配置信息 */\n        settings?: QTE_SETTINGS;\n        /** 操作模式 */\n        openMode?: QTE_OPENMODE\n    }\n\n    export enum QTE_OPENMODE {\n        SHOW_NORMAL = 0,\n        SHOW_CORRECT = 1,\n        SHOW_USER_ANSWER = 2,\n        SHOW_TEACHER = 3            // 主讲端模式\n    }\n\n    export class QTE_SETTINGS {\n        /** 重置按钮显示逻辑 */\n        reset?: QTE_RESET_TYPE;\n        submit?: QTE_SUBMIT_TYPE;\n        playSubmitAni?: boolean;\n        isAnswerResultUI?: boolean;     // 是否展示小鳄鱼答案结果UI\n    }\n\n    export interface Analysis {\n        text?: string;\n        audio?: {\n            url: string;\n            duration: number;\n        }\n        imageUrl?: string;\n    }\n\n\n    function getTemplateByKey(key: string): any;\n\n    declare var openMode: QTE_OPENMODE;// 默认值\n    // ------------------ qte作用域下的一级API -------------------\n    function initialize(adapter?: Adapter, qteHooks?: QTEHooks, params?: QTEParams);\n\n    function create(data, options?: QTEOption): Promise<QuoteNode>;\n\n    function destroy();\n    /**\n     * 获取当前实体渲染数据\n     */\n    function getStates();\n\n    /**\n     * 状态恢复\n     * @param dataStates \n     */\n    function recover(dataStates: any[]);\n\n    /**\n     * 暂停\n     */\n    function pause();\n\n    /**\n     * 恢复\n     */\n    function resume();\n\n    /**\n     * 回到前台\n     */\n    function page_show();\n\n    /**\n     * 退到后台\n     */\n    function page_hide();\n\n    /** \n     * 页面最小化\n     */\n    function fold();\n    /** \n     * 页面最大化\n     */\n    function unfold();\n\n    /**\n    * 添加全局组件\n    */\n    function fWAddComponent(data: any, parent: cc.Node): Promise<QTEEntityNode>;\n    /**\n     * 停用全局组件\n    */\n    function stopfWComponents();\n    /**\n     * 停止播放全局组件\n    */\n    function stopVoicefWComponents();\n    /**\n     * 获取全局播放时长\n    */\n    function getfWComponentsTemData(data: ComponentData[]): Promise<ExportTemData>;\n\n    /**\n     * 播放播放全局组件\n    */\n    function playfWComponents();\n\n    /**\n    * 重制播放播放全局组件\n   */\n    function resetPlayfWComponents();\n    /**\n        * 打开解析\n       */\n    function createAnalysis(data: Analysis, node: cc.Node);\n    /** 删除解析 */\n    function deletAnalysis();\n\n    export interface QuestionConfigData {\n        components: ComponentData[];\n        stageData: StageConfig;\n        extraStageData: ExtraStageConfig;\n        extraDataMap: QuestionMetaData;\n        resourceList: ResourceConfig;\n        audios: { [key: string]: string };\n        animationsForClient: {\n            [key: string]: AnimationData[];\n        };\n        tid: number;\n    }\n\n    export class QTETemplateData {\n        components: any[];\n        extraDataMap: {};\n        resourceList: string[];\n        template: {\n            questionType: number;\n            isSpecialQuestion: boolean;\n            name: string;\n            category: number;\n            tempType: number;\n            bundleUrl: string;\n            bundleName: string;\n            tags: [];\n            stage: {\n                width: number;\n                height: number;\n                safeWidth: number;\n                safeHeight: number;\n            };\n            extraConfig: [];\n            animationConfig: [];\n            video: number;\n            smts: number;\n            features: {\n                isQuestion?: number,//是否组卷、是否可发题\n                isOral?: number,//是否含有口述逻辑\n                isGroup?: number,//是否是题组\n                hasVideo?: number,//是否包含视频\n                hasMys?: number,//是否包含神秘提示\n                demoPage?: number,//讲解页\n                canInteract?: number,//是否可交互\n            }\n        };\n        animations: {};\n        animationsForClient: {};\n        stageData: StageConfigData;\n        extraStageData: {\n            isAutoSubmit?: boolean;\n            answerWrongCount?: number;\n            errorType?: number;\n            defaultAction?: boolean;\n            dragRelation?: any;\n            closeDefaultFeedback?: boolean;\n            hasRecover?: boolean;\n        };\n        thumbnail: string;\n        questionType: number;\n\n        [key: string]: unknown;\n\n        constructor(data: any) {\n        }\n\n        /**\n         * 解析ArtInfo，用于渲染每个page的bundle\n         * @param data\n         */\n        parse: (data: any) => void;\n\n        clear: () => void;\n    }\n    export abstract class BaseComponent extends cc.Component {\n        //#region 基础属性设置\n        public setActive(active: boolean): void;\n        public setWidth(width: number): void;\n        public setHeight(height: number): void;\n        public setPosX(x: number): void;\n        public setPosY(y: number): void;\n        public setColor(color: string | cc.Color): void;\n        public setzIndex(val: number): void;\n        public setAngle(val: number): void;\n        public setScaleX(val: number): void;\n        public setScaleY(val: number): void;\n        public playAudio(audio: cc.AudioClip, loop: boolean = false, cb: () => void = null): number;\n        public stopAudio(audioId: number): void;\n        public getRemoteRes(url: string, type: typeof cc.Asset, onComplete: (err, res) => void): void;\n        public clearRemoteRes(url: string[]): void;\n        public showToast(content: string): void;\n        public showNoticeChoice(str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean): void;\n        /**\n         * 初始化\n         * @param {data: {data: any, engine: anys}} \n         */\n        public abstract initComponent(data?: any): void;\n\n        // 编辑器中使用全部组件加载完成\n        public initAllComponentsFinish(): void;\n        /**\n         * 属性更新\n         * @param key 属性key\n         * @param data 参数 \n         */\n        public abstract changeProperties(key: string, data: any): void;\n\n        /**\n         * @deprecated 弃用\n         * 更换skin\n         * @param path 资源路径 \n         */\n        public abstract changeSkine(path: string, param?: any): void;\n\n        /** 宽高变化后触发的函数 */\n        public onSizeChange(rect: cc.Size): void;\n\n        public setPlayFunc(func: (audio: cc.AudioClip, loop: boolean, cb: () => void) => number): void;\n\n        public setStopAudioFunc(func: (audioId: number) => void): void;\n\n        public setGetRemoteFunc(func: (url: string, type: typeof cc.Asset, onComplete: (err, res) => void) => void): void;\n\n        setShowToastFun(func: (content: string) => void): void;\n\n        public setShowNoticeChoice(func: (str: string, okcall: Function, failcall: Function, isChangeFocus?: boolean) => void): void;\n    }\n\n    interface ResultData {\n        status: number,\n        wordList: [],\n        score: number,\n        audioUrl?: string,\n        audioTime?: number,\n    }\n\n\n    enum EngineCallBack {\n        // CreateReadyCb       = 1,    // 创建成功回调\n        // RecordReadyCb       = 2,    // 准备完成回调\n        StartRecordingCb = 3, // 开始录音回调\n        // VolumeCb            = 4,    // 音量返回\n        // RecordingResultCb   = 5,    // 测评中回调\n        // ResultCb            = 6,    // 测评结果回调\n        RecordSuccess = 6,\n        // FailCb              = 7,    //\n        // RecordFailCb        = 8,    // 录音失败回调\n        // StopCb              = 9,    //\n        // ReadGuideAudio      = 10,   // 阅读提干音频\n        AnswerCorrectCb = 11, // 答题完成回调\n        UploadStop = 12,   // 开始停止录音\n        // NoSpeakeCb          = 13,   // 无声状态\n        WordAudioStop = 14,     // 跟读单词音频停止\n        RecordError = 15, // 答题失败\n        CheckAuthority = 16     // 检测麦克风是否有权限\n    }\n\n\n\n    export class QTERecordAdapter {\n    }\n\n\n    export class MicropComponent extends BaseComponent {\n\n        /** 初始化 */\n        public templateInit(engine: QTERecordAdapter, isCheck?: boolean): void;\n        /** 开始录音 */\n        public micBegin(): void;\n        /**\n      * 注册回调事件\n      * @param callBack\n      * @param cb\n      */\n        public addCallBackListener(option: { key: EngineCallBack; callBack: (res?: ResultData) => void }[]): void;\n        /** 停止音频播放 */\n        public stopAudioLaba();\n    }\n\n\n    export class SpeakerComponent extends BaseComponent {\n        /** 重置音频组件 */\n        public resetVoice(): void\n        /** 播放完成后回调 */\n        public setPlayReplateCountCallBack(cb: Function): void\n        /** 自动播放 */\n        public autoPlayVoice(): void;\n        /** 设置倒计时父级 */\n        public setCountParent(node: cc.Node): void;\n        /** 停止音频 */\n        public stopVoice(): void;\n        /**停止音频 不允许再播放了 */\n        public stopPlayEnd(): void;\n    }\n\n    export enum RecordState {\n        Idle = \"idle\",\n        Ready = 'ready',\n        Recording = \"recording\",\n        Loading = \"loading\",\n        Stop = \"stop\",\n        Error = \"error\",\n    }\n    export class RecordComponent extends BaseComponent {\n        //初始化组件\n        public initComponent(data: any);\n        //设置是否跳过准备阶段\n        public setIsSkipReady(bool: boolean);\n        //设置回调是否结束，多次调用组件时使用\n        public setIsFinish(bool: boolean);\n        //设置是否显示麦克风分数\n        public setIsShowScore(bool: boolean);\n        //开始录音\n        public startRecord();\n        //停止录音\n        public stopRecord(reason: number = 1);\n        //注册回调\n        public addCallBackListener(option: { key: EngineCallBack; callBack: (data) => void }[]);\n        //触发回调\n        public sendCallBack(callBack: EngineCallBack, data: any)\n        //修改状态\n        changeState(recordState: RecordState, score?: string)\n        //初始化事件\n        templateInit(engine);\n        //重置组件\n        reset();\n        //设置麦克风分数显示\n        stopShow(num);\n    }\n    export class VoiceEngineProp {\n        // 评测类型：1 - 单词；2 - 句子； 3 - 段落； 4 - 问题回答； 5 - 复述;  6 - 音标\n        wordType: WordType;\n        evaluatingText: string; // 评测内容\n        answerDuration: number = 15; // 答题时长\n        isUsePhonetics?: boolean;        // 是否使用音标\n        evaluatePhonetics?: string;    // 音标评测内容\n        // 最低分\n        minScore?: number;\n    }\n    export interface recordResultData {\n        // 分数\n        score?: number;\n        // 音频\n        audio?: string;\n        // 状态 是否完成\n        status?: number;\n    }\n\n    export class RTRComponent extends BaseComponent {\n\n        // 初始化组件容器\n        public async templateInit(engine): Promise<boolean>;\n        // 更新评测\n        public async updateWord(data: VoiceEngineProp): Promise<boolean>;\n        // 注册回调\n        public addCallBackListener(option: { key: EngineCallBack; callBack: (data: recordResultData) => void }[]);\n        // 暂停评测\n        public onGamePause(): void;\n        // 恢复评测\n        public onGameResume(): void;\n        // 重置组件\n        reset();\n        // 销毁组件\n        onDestroy();\n    }\n\n    //画笔组件控制器\n    export class DrawControl extends BaseComponent {\n        //当前仅有物理作图题与画笔组件使用 触发画笔\n        public onPenClick(index: number = -1);\n        //当前仅有物理作图题与画笔组件使用 触发撤销\n        public onCancelClick();\n        //修改画笔颜色\n        public setDrawColor(color: string[]);\n        //重置画笔组件 清空state\n        public resetCmpt();\n        //获取所有绘图组件绘制线段的总段数\n        public getDrawLineLength();\n        //获取绘图组件是否正在绘制线段\n        public getDrawCmptIsTouching();\n        //关闭画笔组件\n        public closePen();\n    }\n\n    export abstract class QTETemplate extends cc.Component {\n        // 当前题板唯一id\n        public UUID: string = \"invalid template uuid\";\n        // 当前同步数据\n        state: QTEBaseState;\n        // qte\n        private _qte: QTESystem;\n        public get qteSys(): QTESystem;\n        // 题板配置属性\n        protected _stageConfig: StageConfigData = null;\n        // 锁屏引用计数\n        private _nLockCount: number = 0;\n        // 题干音频&音频组件默认时长\n        private _nGuideDuration = -1;\n\n        /** 是否操作过 */\n        protected get _bIsOperated(): boolean\n        protected set _bIsOperated(operate: boolean): void\n        /** 题版是否正确 */\n        protected get _bCorrect(): boolean;\n        protected set _bCorrect(correct: boolean): void\n        private _openMode: QTE_OPENMODE;\n        /** 打开模式 */\n        public set openMode(mode: QTE_OPENMODE): void;\n        public get openMode(): QTE_OPENMODE;\n\n        /** 答案信息 */\n        protected _answerBase = {};\n        public setAnswerBase() { // 是否需要\n            console.log('you can modify the fun to set answer base data')\n        }\n\n        /** 题版bundle名称 */\n        protected _strBundleName: string = \"\";\n        /** 本地资源列表 */\n        protected _arrLocalResList: { path: string, type: typeof cc.Asset }[] = [];\n        /** 本地资源缓存,方便题版内部获取 */\n        private _arrLocalRes = {}\n\n        onLoad(): void\n\n        /** 题版内私有资源加载函数 */\n        async loadPrivateResource(): Promise<void>;\n        /** 题版创建组件API */\n        async createEntityNode(componetData: ComponentData, extraProp: QuestionMetaData, parent: cc.Node): Promise<QTEEntityNode>;\n\n        getLocalAsset(path: string): cc.Asset;\n        /**\n         * 此方法需要重写，用于watch所需要的数据\n         * @returns {[key: string]: (val: any, oldVal: any) => void}\n         */\n        watcher(): { [key: string]: (val: any, oldVal: any) => void } {\n            return null;\n        }\n\n\n        /**\n         * 根据业务组件id获取实体节点\n         * @param {string} id 业务组件id\n         */\n        public getEntityById(id: string): QTEEntityNode;\n        /**\n         * 根据业务组件tag获取实体节点\n         * @param {string} tag 业务组件tag\n         */\n        public getEntitiesByTag(tag: string): QTEEntityNode[];\n\n        public getEntitiesMap(): Map<string, QTEEntityNode[]>;\n\n        public getEntitiesList(): QTEEntityNode[];\n\n        public getEntitiesBySubType(subType: string): QTEEntityNode[];\n\n\n        page_hide(): void;\n\n        page_show(): void;\n\n        /** \n         * 页面最小化点, 击右上方最小化\n         */\n        fold(): void;\n        /** \n         * 页面最大化, 点击左下角继续作答\n         */\n        unfold(): void;\n\n        /** 编辑器引导动画结束后执行 */\n        public onStartAnswering() {\n\n        }\n        /** 提交前设置结构化数据 */\n        protected setSubmitAnswer(): any;\n        /**\n         * framework 同步特殊消息\n         * @param key \n         * @param data \n         */\n        public upateCustomData(key: string, data: any): void;\n\n        /** 调用编辑器配置动画之前 业务自定义 */\n        onAnswerActionFeedBackBefore(): Promise<void>;\n        /**\n        * framework 播放反馈动画\n        * @param key \n        * @param data \n        */\n        public async playFeedBackAction(data: any): void;\n\n        /** 调用编辑器配置动画之后 业务自定义 */\n        onAnswerActionFeedBackEnd(): Promise<void>;\n\n        /** 题版  主动提交前提示 */\n        onSubmitBeforeCheck(): boolean;\n        /**\n         * 提交\n         */\n        submit(): QTETemplateSubmitParams;\n\n        pause(): void;\n\n        resume(): void;\n\n        getBaseVo(): QTETemplateCommonParams;\n\n        play(): void;\n\n        getAutoSubmitMode(): AutoSubmitMode;\n        /** 题版 重置前提示 */\n        onResetBeforeCheck(): boolean;\n\n        reset(): void;\n        /**\n         * 重置后给题版内部使用\n         */\n        protected onResetEnd(): void;\n\n        /** 提交结束后给题版内部使用  */\n        protected onSubmitEnd(): void;\n\n        public playAudio(url: string | cc.AudioClip, loop: boolean = false, callback?: () => void): number;\n        /** 跟进题版内置资源地址, 创建entity Sprite\n         * @id entityNode Id\n         * @tag entityNode tag\n         * @localUrl 题版bundle内路径\n         */\n        async createLocalSprie(id: string, tag: string, localUrl: string, parent: cc.Node): Promise<QTEEntityNode>;\n\n\n        onDestroy(): void;\n        /** 显示正确答案 */\n        public showCorrect(): void;\n        /** 获取全局配置 */\n        public getStageConfig(): StageConfigData;\n        public bolckInput();\n        public unbolckInput();\n        /** 增加连续题UI */\n        public addSerialUI(data: qte.QTESerialUIParams): void;\n        /** 增加透明触摸屏蔽层 */\n        public addTouchMask(): void;\n        /**编辑器预览获取参考答案/*/\n        public setReferenceAnswer(): any;\n        /**  获取参考答案*/\n        public getReferenceAnswer(): any[] | null;\n        /** 增加作答详情 objResult  .arrResult作答数据数组类型  .dt回调时间<可选>  .cb回调函数<可选> */\n        public async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node>;\n    }\n\n    export class QTEComponent extends cc.Component {\n\n    }\n    export class QTESystem extends QTEBaseMeta {\n        public start(template: QTETemplate, config?: { [key: string]: { cmpt: new () => QTEComponent; options?: any }[] }, onComplete?: () => void): void;\n    }\n\n\n    export interface SubmitAnswer {\n        answerContent?: AnswerContentItem[] | any[],           // 作答内容\n        pContent?: PContentItem[] | any[],                      // 作答产生数据\n        [propName: string]: any\n    }\n    export interface AnswerContentItem {\n        signalId: number,                // 标号\n        type?: string,                    // 类型\n        isCorrect?: boolean,                 // 单个空的对错\n        content?: string                  // 内容\n    }\n    export interface PContentItem {\n        type: string,                    // 类型\n        content: string                  // 内容\n        expendType?: string               // 指定类型 \n    }\n\n    export class QTEEntityNode extends cc.Node {\n        // component\n        public componentData: any;\n        // qte uuid\n        public qteUUID: string;\n        // id\n        public qid: string;\n        // tag\n        public qtag: string;\n\n        // 节点属性\n        public properties: {\n            [key: string]: any;\n        };\n        // 业务属性\n        public extraDataMap: {\n            [key: string]: any;\n        };\n        // 额外数据\n        public extra: any = null;\n\n        /**\n         * 返回 node 的指定动画数据，如果没有该动画，返回 null\n         * @param name \n         */\n        public hasAnimation(name: string);\n        public skipAnimation(name: string);\n        // 播放动画\n        public playAnimation(name: string, callback: () => void, canSkip?: boolean): void;\n\n    }\n    export class QTEQuestionNumberUI extends cc.Component {\n        node: cc.Node;\n        public setQuestionNumber(num_str: string): void;\n    }\n    export class QTEUtils {\n        /**\n         * 播放 spine 动画\n         * @param spine \n         * @param array \n         * @param loop \n         * @param index \n         */\n\n        public static playSpineArr(spine: sp.Skeleton, array: string[], loop: boolean, index: number = 0): void\n        public static instance<T extends SingleBase>(type: new () => T, uuid: string): T;\n        /**舞台空间坐标转换节点坐标 */\n        public static convertStageToNodeSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2;\n        /**节点坐标转换为舞台空间坐标  */\n        public static convertToStageSpaceAR(node: cc.Node, pos: cc.Vec2): cc.Vec2;\n        /** 加载预制答案框 */\n        public static async loadAnswerBorder(): Promise<cc.Prefab>;\n        public static flowerAudioRecord(param: { backgroundSoundPath?: string, type: number, timeMax: number, callBack?: Function }): void;\n        /** 加载预制题号 */\n        public static async loadQuestionNumberUI(num_str: string): Promise<qte.QTEQuestionNumberUI>;\n        /** 增加语音题分数  */\n        public static addListenQuestionSorce(num: number): number;\n        /** 小鳄鱼弹框 增加作答结果 UI */\n        public static async addAnswerResultUI(objResult: { arrResult: boolean[], delayTime?: number, cb?: Function }): Promise<cc.Node>;\n        // 获取切换小题截图\n        public static async getSwitchQuestionPicture(): Promise<cc.Texture2D[]>;\n        // 获取角度\n        public static getAngelEulerAngles(node: cc.Node): number;\n        /** 安全等待异步操作完成 */\n        public static async safeAwait<T>(promise: Promise<T>, target: cc.Object): Promise<T>\n    }\n\n    export class QTEAssetsManager extends SingleBase {\n        getAsset(url: string)\n\n    }\n    export class ClickCmpt extends QTEComponent {\n        public onLoad();\n\n        public onClick(e: cc.Touch);\n    }\n\n    export class QTEBaseState {\n\n        /** 是否操作过 */\n        public isOperated: boolean = false;\n        /** 题版是否正确 */\n        public isCorrect: boolean = false;\n    }\n\n    export enum QTE_STATE {\n        /** 语音读题 */\n        GUIDE,\n        GUIDE_AFTER,\n        /** 语音读题后动画开始 */\n        GUIDE_AFTER_ANMI_BEGIN,\n        /** 语音读题后动画结束 */\n        GUIDE_AFTER_ANMI_END,\n        /** 作答阶段 */\n        ANSWERING,\n\n        ANSWERING_AFTER,\n        ANSWER_CORRECT,\n        ANSWER_CORRECT_END,\n        ANSWER_WRONG,\n        ANSWER_WRONG_END,\n        /** 重置 */\n        RESET_QUESTION,\n        RESET_QUESTION_END,\n    }\n\n    export interface QTEObserve {\n        // 当前题目模板\n        template: QTETemplate;\n        /** 当前状态 */\n        curState: QTE_STATE;\n    }\n\n    export interface ComponentData {\n        id: string;\n        type: \"sprite\" | \"label\" | \"group\" | \"spine\" | \"cutShape\" | \"cocosAni\" | \"specialComponent\" | \"formula\" | \"svgShape\" | \"optionComponent\" | \"shape\";\n        tag: string;\n        properties: ComponentProperty;\n        hideBeforeAnimation?: boolean;\n        extra: Object;\n        spineData?: any;\n        cocosAniData?: any;\n        subComponents?: ComponentData[];\n        childComponents?: ComponentData[];\n        subType?: string;\n        dragable?: boolean;\n        editable?: {\n            properties: {\n                [key: string]: boolean\n            };\n        }\n    }\n    export interface QuestionMetaData {\n        [key: string]: any;\n    }\n\n    export interface ComponentProperty {\n        answerDuration?: number;  // 看图说话倒计时\n        x: number;\n        y: number;\n        width: number;\n        height: number;\n        active: boolean;\n        texture?: string;\n        zIndex?: number;\n        color?: string;\n        angle?: number;\n        opacity?: number;\n        scaleX?: number;\n        scaleY?: number;\n        offsetX?: number;\n        offsetY?: number;\n        timeScale?: number;\n        animList?: string[];\n        animationList?: string[];\n        loop?: boolean;\n        fillColor?: string;\n        lineWidth?: number;\n        strokeColor?: string;\n        linesData?: LineData[];\n        pointsData?: PointData[];\n        lineHeight?: any;\n        str?: string;\n        horizontalAlign?: any;\n        svgText?: string;  // svg文本描述\n\n        speakerType?: number,\n        audioUrl?: string\n        count?: number,\n        autoPlay?: boolean,\n        notStopPlaying?: boolean,\n        countdown?: boolean,\n        autoRepeatCount?: number,\n        countdownSkin?: number,\n        duration?: number\n        isNotTouch?: boolean\n        [key: string]: any;\n    }\n    export class QTEBaseMeta {\n        // 当前QTE唯一id\n        public UUID: string = \"invalid qte uuid\";\n        // 当前bundleName\n        public bundleName: string;\n        // 状态观察者\n        public observe: QTEObserve;\n        public constructor(observe: QTEObserve): void;\n    }\n    export function logCatBoth(type, args): void;\n    export function log(...data: any[]): void;\n\n    // 定时器 \n    export class TimerUtils {\n        /**\n         * 添加一个默认定时器\n         * @param cb 回调   \n         * @param time 间隔\n         */\n        public static addTimer(cb: (dt) => void, time: number): number;\n        /**\n         * 添加一个默认定时器\n         * @param cb 回调\n         * @param time 间隔\n         * @param executionType 选择定时器类型\n         */\n        public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType): number;\n        /**\n         * 添加一个默认定时器\n         * @param cb 回调\n         * @param time 间隔\n         * @param executionType 选择定时器类型\n         * @param isBack 后台后是否记录后台时间。\n         */\n        public static addTimer(cb: (dt) => void, time: number, executionType: ExecutionType, isBack: boolean): number;\n\n        public static clearTimer(timerId: number, group = \"global\");\n\n    }\n    export enum ExecutionType {\n        once, // 默认是一次的\n        cycle,\n    }\n\n\n\n\n    export class DisplayObjectManager extends SingleBase {\n        /**\n         * 这个id是否是选中id组中id的subId\n         * @param id 该ID\n         * @param arrIds 选中的id组\n         */\n        public hasOwnedGroup(id: string, arrIds: string[]): boolean;\n        /** 获取所有根节点id，不包括组内id，即subId */\n        public getRootIds(): string[];\n\n\n\n        /**\n         * 添加显示对象\n         * @param id\n         * @param object\n         */\n        public addDisplayObject(id: string, object: DisplayObject): void;\n        /**\n         * 移除显示对象组件\n         * @param id 组件id\n         */\n        public removeDisplayObject(id: string): void;\n\n        /**\n         * 根据id获取显示对象\n         * @param {string} id\n         */\n        public getDisplayObjectById(id: string): DisplayObject;\n\n        /**\n        * 根据tag获取显示对象数组\n        * @param {string} tag\n        */\n        public getDisplayObjectByTag(tag: string): DisplayObject[];\n        /**\n         * 更新显示对象属性\n         * @param id 组件id\n         * @param newProperties 属性\n         */\n        public updateDisplayObject(id: string, newProperties: any, isUndo?: boolean): any;\n        /**\n         * 更新显示对象坐标属性的增量值\n         * @param id 组件id\n         * @param pos 坐标的增量\n         */\n        public updateDisplayObjectDeltaPos(id: string, pos: cc.Vec3): void;\n        /**\n         * 设置选中状态\n         * @param id 选中的id\n         * @param multi 是否是多选，默认单选false\n         */\n        public setSelected(id: string, multi?: boolean): void;\n        /**\n         * 解除group\n         * @param id\n         */\n        public removeGroupDisplayObject(id: string): string[];\n\n        /** 检查是否是有效节点 */\n        private checkIsValidNode(cids: string[]): boolean;\n\n        /**\n         * 获取当前选中ID的ParentId\n         * @param selectId\n         */\n        public getDisplayParentID(selectId: string): string;\n        /**\n         * 获取当前选中ID的组ID\n         * @param selectId\n         */\n        public getDisplayGroupID(selectId: string): string;\n\n        /**\n         * 获取组件的设计坐标\n         * @param id\n         */\n        public getWorldPos(id: string): { x: number; y: number };\n        /**\n         * 获得组件的设计rect\n         * @param id \n         */\n        public getWorldRect(id: string): { x: number; y: number; width: number; height: number };\n\n        public getRotateEWordRect(id: string): cc.Rect;\n\n\n        public getCaptureByNode(id: string);\n\n\n        private resetNodePos(id: string, worldPos: cc.Vec2): { x: number; y: number };\n\n        /**\n         * 获取操作点的设计坐标\n         * @param id\n         * @param pointId\n         */\n        public getEditPointWorldPos(id: string, pointId: string): cc.Vec2;\n\n        public getNodePos(id: string, newProperties: any): { x: number; y: number };\n\n        private resetEditPointPos(id: string, pointId: string, worldPos: cc.Vec2): cc.Vec3;\n\n        public getEditPointPos(id: string, pId: string, newProperties: any): { x: number; y: number };\n\n        /**\n         * 获取节点的真是坐标\n         * @param id\n         */\n        public getNodeRealPos(id: string): { x: number; y: number };\n\n        /**\n        * 获取节点的真是坐标\n        * @param id\n        */\n        public getNodeCentEdtiorPos(id: string): { x: number; y: number };\n        /**\n         * @msg     : 根据所给数据显示标号节点\n         * @param    {boolean} isView\n         * @return   {*}\n         */\n        public setAllObjectSignalView(isView: boolean, data: SignalData[]);\n    }\n\n    declare let displayObjectManager: DisplayObjectManager;\n\n}\n"]], 0, 0, [], [], []]