[1, 0, 0, [["cc.Json<PERSON>set", ["_name", "json"], 1]], [[0, 0, 1, 3]], [[0, "package", {"_from": "crypto-js", "_id": "crypto-js@4.0.0", "_inBundle": false, "_integrity": "sha512-bzHZN8Pn+gS7DQA6n+iUmBfl0hO5DJq++QP3U6uTucDtk/0iGpXd/Gg7CGR0p8tJhofJyaKoWBuJI4eAO00BBg==", "_location": "/crypto-js", "_resolved": "https://registry.npmjs.org/crypto-js/-/crypto-js-4.0.0.tgz", "_shasum": "2904ab2677a9d042856a2ea2ef80de92e4a36dcc", "_spec": "crypto-js", "_where": "C:\\Program Files\\nodejs\\node_modules", "bundleDependencies": false, "deprecated": false, "description": "JavaScript library of crypto standards.", "homepage": "http://github.com/brix/crypto-js", "license": "MIT", "main": "index.js", "name": "crypto-js", "version": "4.0.0", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "crypto-js", "name": "crypto-js", "escapedName": "crypto-js", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "author": {"name": "<PERSON>", "url": "http://github.com/evanvosberg"}, "bugs": {"url": "https://github.com/brix/crypto-js/issues"}, "dependencies": {}, "keywords": ["security", "crypto", "Hash", "MD5", "SHA1", "SHA-1", "SHA256", "SHA-256", "RC4", "Rabbit", "AES", "DES", "PBKDF2", "HMAC", "OFB", "CFB", "CTR", "CBC", "Base64"], "repository": {"type": "git", "url": "git+ssh://**************/brix/crypto-js.git"}}]], 0, 0, [], [], []]