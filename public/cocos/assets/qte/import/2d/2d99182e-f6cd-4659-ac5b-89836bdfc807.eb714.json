[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "assemble.d", "declare namespace assemble {\n\n    export class SpecialComponentType {\n        static CmptRecord = 'voice'; // 录音组件\n        static KeyBord = 'keyboard'; // 键盘组件\n        static MatchBoard = 'matchboard'; // 火柴组件\n        static Counter = 'counter'; // 计数组件 \n        static Write = 'write'; // 手写组件 \n        static Clock = \"clock\" // 钟表组件\n        static Tangram = \"tangram\" // 七巧板组件\n        static Brush = \"brush\"; // 画笔组件\n    }\n    export class factory {\n        static async createObject(type: SpecialComponentType, data: any, bundle: cc.AssetManager.Bundle): Promise<qte.BaseComponent>;\n    }\n    \n}"]], 0, 0, [], [], []]