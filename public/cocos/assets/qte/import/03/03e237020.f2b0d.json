[1, ["ecpdLyjvZBwrvm+cedCcQy", "20QfP7grtE7o3wcuyRbIIo", "f9F3PiZv1CtL2QermI+Bvz", "eesgdixshOUa8lxmFO+rUb", "83mkI4D3dNVqjaFULqfF0Z", "b6S/M1DCpILaP/RdYH/0bw", "55/cS2JyBIOLaxnokR1YsK", "58Q10xwe9HlaKTJhfGWdkn", "c2HQQKFnBN8LmTm5gaPjHe", "464ffxbFJD6pH1jwdLMeIR", "15wcwlKK9JOZlYYP/KBpUW", "606u+09SdJ5r+wUHCnjU6S", "a1v9rRBUFDw6zqL+7f1/bu"], ["node", "_textureSetter", "_parent", "_spriteFrame", "root", "sorceBg", "lostNode", "gradeLabel", "sorceLable", "data", "dingAudio"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_active", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 0, 4, 5, 9, 1, 7, 2, 5, 5], "cc.SpriteFrame", ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "node", "_materials"], -3, 1, 3], ["cc.AudioClip", ["_name", "_native", "duration"], 0], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 12, 12, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["da6a0lT6ClL3a8/XF4hOJKM", ["node", "sorceLable", "gradeLabel", "lostNode", "sorceBgIcon", "sorceBg", "succAudio", "dingAudio"], 3, 1, 1, 1, 1, 3, 1, 3, 6], ["cc.Layout", ["_resize", "_N$layoutType", "node", "_layoutSize"], 1, 1, 5]], [[3, 0, 1, 2], [5, 0, 1, 2, 4], [0, 0, 6, 3, 4, 7, 2], [8, 0, 1, 2, 3, 4, 5, 6, 2], [4, 0, 1, 2, 3, 4, 6, 7, 6], [6, 0, 2], [0, 0, 1, 8, 5, 3, 4, 3], [0, 0, 2, 6, 5, 3, 9, 4, 7, 3], [0, 0, 6, 5, 3, 4, 10, 7, 2], [7, 0, 1, 2, 3, 4, 5, 6, 2], [2, 3, 4, 5, 1], [2, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 2, 3, 4, 4], [9, 0, 1, 2, 3, 4, 5, 6, 7, 1], [3, 1, 1], [10, 0, 1, 2, 3, 3], [4, 0, 1, 2, 5, 3, 4, 6, 7, 7]], [[[[1, "come", ".mp3", 1.048667], -1], 0, 0, [], [], []], [[{"name": "perfect", "rect": [0, 0, 160, 68], "offset": [0, 0], "originalSize": [160, 68], "capInsets": [83, 0, 35, 0]}], [1], 0, [0], [1], [2]], [[[1, "good", ".mp3", 0.944333], -1], 0, 0, [], [], []], [[[5, "FollowWordsComponent"], [6, "template", 512, [-8, -9], [[10, -2, [6], 7], [13, -7, -6, -5, -4, [8, 9, 10], -3, [12, 13, 14], 11]], [14, -1], [5, 1280, 720]], [9, "bg", 1, [[[2, "New Node", -12, [0, "d3PFMXMg9CL5JhZUJu9yAM", 1], [5, 96, 68], [-101.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], -13, -14, [2, "New Node", -15, [0, "2d2o/Wg8tJ0Li4qsPU6aO0", 1], [5, 24, 68], [27.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], -16, [2, "New Node", -17, [0, "1fCQWHyWBDPbgu/KDOQXqS", 1], [5, 24, 68], [137.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 4, 1, 1, 4, 1, 4], [[-10, [15, 1, 1, -11, [5, 299, 68]]], 1, 4], [0, "20R6s95OtIs4V0UhMh3YL4", 1], [5, 299, 68], [0, -116, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "lost", false, 2, [[4, "再试一次吧~", 28, 30, 1, 1, -18, [3]]], [0, "c9nzwYlApAGKWMXqNd1xIS", 1], [4, 4279505940], [5, 157, 38], [15.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "questionNode", 1, [[11, 1, 1, 0, -19, [0], 1]], [0, "558fcRXRFAjaH0LiJKOa1y", 1], [5, 1230, 440], [0, 0, 1], [-613, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "sorceStr", 2, [-20], [0, "713FEmFZ9MbpEJn1GeV/LU", 1], [4, 4279505940], [5, 69, 36], [-19, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "Good", 28, 28, 2, 1, 1, 5, [2]], [3, "sorce<PERSON><PERSON><PERSON>", 2, [-21], [0, "30iPUARqNBPrUvNywa0yAS", 1], [4, 4280295679], [5, 86, 46], [82.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "100分", 32, 36, 1, 1, 7, [4]], [12, 1, 1, 0, 2, [5]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 9, 0, 6, 3, 0, 7, 6, 0, 8, 8, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, -1, 9, 0, 0, 2, 0, 2, 2, 0, -2, 5, 0, -3, 3, 0, 2, 2, 0, -5, 7, 0, 2, 2, 0, 0, 3, 0, 0, 4, 0, -1, 6, 0, -1, 8, 0, 9, 1, 21], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9], [-1, 3, -1, -1, -1, -1, -1, 3, -1, -2, -3, 10, -1, -2, -3, 3], [0, 3, 0, 0, 0, 0, 0, 4, 5, 6, 1, 7, 8, 9, 10, 1]], [[{"name": "good", "rect": [0, 0, 160, 68], "offset": [0, 0], "originalSize": [160, 68], "capInsets": [83, 0, 35, 0]}], [1], 0, [0], [1], [11]], [[{"name": "come", "rect": [0, 0, 160, 68], "offset": [0, 0], "originalSize": [160, 68], "capInsets": [83, 0, 35, 0]}], [1], 0, [0], [1], [12]], [[[1, "great", ".mp3", 1.0505], -1], 0, 0, [], [], []]]]