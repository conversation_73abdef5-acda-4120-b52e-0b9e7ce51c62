[1, ["ecpdLyjvZBwrvm+cedCcQy", "e2u+Nf8p1CbZGU7DwhIOnT", "eaw3UL4CFCpJAWb//N+wmp", "78w9R2wwlAFo36/mNsfPFD", "60slg9IGBCh7vNRi1HBolp", "8dLIhWvMdDS7tjge5aIaD8", "c95d57o0ZKhrd/1f4H0GD+", "1fMeLppENA+bfMlDZy2qzZ", "9cZwGovotKtaERRsshMj5E", "c7I+T/qCpF+rrvNcV/66K0", "482gDqZTNHFqhOdjWkr1xQ", "93YHQSyMtGVb2Ym0+Np1NK", "4duEwAntJB3q0WsepwftpV", "213iNa7WlFipvCQhxuozvE", "ecKAlXPYhEQZwfcPc9j3Jn", "ccvCyvI6ZLe59ohI+Dp1Rw", "52BqLQqqBKX6quJtGjNMkw", "29dOZS0+FEy52BVzOdzpZq", "00lwr0oItEma+EgVUl4moH", "f8CqwcLrFANb5i9TDncGaU", "44VOeywAJAYqw/tJ7UFov7", "2cspOL6y5MZK/ODWS5s8X5", "6cyqQcm0pI47gdW+JFJ3lq", "eeMMo3UXJKpLyQKvdxZ20q", "b5SFMGZjdNHKaaPwZWNfEi", "b3moyc7wZD26zsOujU0G+Q", "f3lKhQCGtHip4qBMfwtChN", "54he/EwjhM1L3l0QCf0e13", "bd6B/7kn5Lm6LJt4UzvqB2", "58spT9GV5E0ZIXW1bIoZ/f", "1c42KQdoZPrbQP44tzwLnv", "7e3l8F9QVFbatLeD3U8FA4", "02uxA46fNL14g4vrXHdRHc", "eb/ySjrcRMC7UPmsslkLqC", "51dk4xkclDnL9Tv3ZvMBRj", "eb+q2MYftPXJwLnEhxOnbr", "d2li7pGyRG44Xfcbghy2O9", "15S82losZAbIbVd4MCruQE", "90MPRR2chGjb7Rr+R/ya7U", "e0lwcSsKtHzI6Ntp6qpPry", "713lYYgDVIUaFXoLWieXLD", "416F9KI/lMFIuXtBSNRFAr", "0a9FPFcDpA1pW90vIH+fjc", "3a9A4VQT5EL5/nDTJ7+ZBi", "15tPMSs7pMkJ/YE0c7160o", "92QwQgfZpIdp+/6LcnQdVa", "3coCjZXk5H/YZbZ6gO99mZ", "ebOCksfxxASYfg40thNbR0", "03rl2K7lVC8L4d/9/ACNgL"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "large_nd1015", "large_nd610", "large_nd15", "nd610", "nd15", "ndBgWrong", "ndBgRight", "data", "large_bg_R", "large_bg_W"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_children", "_components", "_parent", "_trs", "_anchorPoint", "_color"], 0, 4, 5, 2, 9, 1, 7, 5, 5], ["cc.Sprite", ["_srcBlendFactor", "_sizeMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.BlockInputEvents", ["node"], 3, 1], ["b1294fOrA5FGJ7yfCW7eEAA", ["node", "ndBgRight", "ndBgWrong", "nd15", "nd610", "large_nd15", "large_nd610", "large_nd1015", "large_bg_R", "large_bg_W"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 6, 6]], [[3, 0, 1, 2, 2], [1, 0, 7, 6, 3, 4, 8, 2], [2, 2, 3, 4, 1], [1, 0, 1, 7, 5, 3, 4, 8, 3], [1, 0, 1, 5, 6, 3, 4, 9, 8, 3], [4, 0, 1, 2, 4, 5, 4], [1, 0, 5, 6, 3, 4, 8, 2], [4, 0, 1, 3, 4, 5, 4], [2, 0, 2, 3, 4, 2], [5, 0, 2], [1, 0, 5, 6, 3, 4, 2], [1, 0, 7, 5, 3, 4, 2], [1, 0, 7, 5, 3, 2], [1, 0, 2, 7, 6, 3, 10, 4, 3], [6, 0, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [3, 1, 2, 1], [2, 1, 2, 3, 4, 2]], [[[{"name": "default_sprite1", "rect": [0, 0, 2, 2], "offset": [0, 0], "originalSize": [2, 2], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [15]], [[{"name": "num_3", "rect": [25, 20, 26, 36], "offset": [-1, 0.5], "originalSize": [78, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [16]], [[{"name": "num_9", "rect": [25, 20, 26, 36], "offset": [-1, 1], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [17]], [[[9, "answerResultUI"], [10, "answerResultUI", [-11, -12, -13], [[14, -2], [15, -10, -9, -8, -7, -6, -5, -4, -3, 256, 257]], [16, -1, 0], [5, 1280, 720]], [4, "nd15", false, [-15, -16, -17, -18, -19], [[5, 1, 1, 23, -14, [5, 23, 200]]], [0, "01EvTvPCJOXrXEm5RFalx3", 1, 0], [5, 23, 200], [0, 0, 0.5], [-250, 45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nd610", false, [-21, -22, -23, -24, -25], [[5, 1, 1, 23, -20, [5, 23, 200]]], [0, "822HTP8N9IE6ZohIOaNTcx", 1, 0], [5, 23, 200], [0, 0, 0.5], [-250, -45, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nd15", false, [-27, -28, -29, -30, -31], [[5, 1, 1, 23, -26, [5, 508, 180]]], [0, "9c1SUxEYRH96zDtMAgCQC3", 1, 0], [5, 508, 180], [0, 0, 0.5], [-250, 95, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nd610", false, [-33, -34, -35, -36, -37], [[5, 1, 1, 23, -32, [5, 508, 200]]], [0, "c1Mx9UHLhCP4R5gci5ffmT", 1, 0], [5, 508, 200], [0, 0, 0.5], [-250, 15, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "nd1015", false, [-39, -40, -41, -42, -43], [[5, 1, 1, 23, -38, [5, 508, 210]]], [0, "5aFWbTHndM9b7L0UvtBHZV", 1, 0], [5, 508, 210], [0, 0, 0.5], [-250, -80, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 2, [-44, -45, -46, -47, -48], [0, "7f2pFsR9RPTo1z5vM3O4gY", 1, 0], [5, 97, 130], [71.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 2, [-49, -50, -51, -52, -53], [0, "b6sErsHCVPy4vOyvibz7tl", 1, 0], [5, 97, 130], [168.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 2, [-54, -55, -56, -57, -58], [0, "755gc2Uq1GE6KWa+KU/3bl", 1, 0], [5, 97, 130], [265.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 2, [-59, -60, -61, -62, -63], [0, "f0zBa12IBCspVOuw6+M68r", 1, 0], [5, 97, 130], [362.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 2, [-64, -65, -66, -67, -68], [0, "ffO1t6321OnI5+as8R3gEP", 1, 0], [5, 97, 130], [459.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 3, [-69, -70, -71, -72, -73], [0, "704WjpXTxKGorcNnR8M368", 1, 0], [5, 97, 130], [71.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 3, [-74, -75, -76, -77, -78], [0, "11KmUd8YBDFqWpd/CG/uqq", 1, 0], [5, 97, 130], [168.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 3, [-79, -80, -81, -82, -83], [0, "f1WXh8TJlE2awTAZzDkNnS", 1, 0], [5, 97, 130], [265.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 3, [-84, -85, -86, -87, -88], [0, "34IJQDyJxK5pYTLYR+vvK6", 1, 0], [5, 97, 130], [362.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 3, [-89, -90, -91, -92, -93], [0, "80REmIYtBCE5AfH+XbvDxB", 1, 0], [5, 97, 130], [459.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 4, [-94, -95, -96, -97, -98], [0, "18gnnNIsRPnb2KdyXZBJWD", 1, 0], [5, 97, 130], [71.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 4, [-99, -100, -101, -102, -103], [0, "1cU1350+JGNJxvonmU0OS4", 1, 0], [5, 97, 130], [168.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 4, [-104, -105, -106, -107, -108], [0, "61mdSk0zNLG4Pv+Ui3QzrX", 1, 0], [5, 97, 130], [265.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 4, [-109, -110, -111, -112, -113], [0, "fbZbeGDcNN3JYdZWeEJF8V", 1, 0], [5, 97, 130], [362.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 4, [-114, -115, -116, -117, -118], [0, "2dnB6VnlxEMLvNhsc+1af5", 1, 0], [5, 97, 130], [459.5, 10.283, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 5, [-119, -120, -121, -122, -123], [0, "018RG/tn9C6aPg5Yo6FQok", 1, 0], [5, 97, 130], [71.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 5, [-124, -125, -126, -127, -128], [0, "3d20THdkBMzJGvMkCzdJeU", 1, 0], [5, 97, 130], [168.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 5, [-129, -130, -131, -132, -133], [0, "cfzYWBOCtH6I5NRwmvx4Fz", 1, 0], [5, 97, 130], [265.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 5, [-134, -135, -136, -137, -138], [0, "b6fAP7zX9BkIyRn/Qsqm5g", 1, 0], [5, 97, 130], [362.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 5, [-139, -140, -141, -142, -143], [0, "1bXSEgIJJLY4+sIrJoLuOC", 1, 0], [5, 97, 130], [459.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 6, [-144, -145, -146, -147, -148], [0, "efev9nR+FHn53JawMyroHk", 1, 0], [5, 97, 130], [71.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 6, [-149, -150, -151, -152, -153], [0, "70DZU5LcJOjYLNF+dN57pF", 1, 0], [5, 97, 130], [168.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 6, [-154, -155, -156, -157, -158], [0, "b4z13zA+VIA5UHKErgGNKH", 1, 0], [5, 97, 130], [265.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 6, [-159, -160, -161, -162, -163], [0, "b7CgBMZFNPPY3btgKf16NF", 1, 0], [5, 97, 130], [362.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "ndItem", false, 6, [-164, -165, -166, -167, -168], [0, "4ddu8a+NBD54bi/5ECdgHn", 1, 0], [5, 97, 130], [459.5, -6.124, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "largendLayout", [4, 5, 6], [[7, 1, 2, -110, -169, [5, 480, 370]]], [0, "f1sa32HA9K6Y0unSrnsSn3", 1, 0], [5, 480, 370], [100, -51.5, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "ndLayout", [2, 3], [[7, 1, 2, -110, -170, [5, 480, 110]]], [0, "1d+gPkNX5HuLvajSDtiKyh", 1, 0], [5, 480, 110], [100, -60, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bg", 1, [-171, -172], [0, "368WN1XtZIu7r8s10M+mJq", 1, 0], [5, 1280, 720]], [1, "result_error", 34, [[8, 1, -173, [2], 3]], [0, "26iYLU/T9EQpOIqyuh7JTo", 1, 0], [5, 981, 565], [13.058, -7.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "result_right", 34, [[8, 1, -174, [4], 5]], [0, "3bKbuyZDVDMqHWcO+OojVA", 1, 0], [5, 984, 572], [15.391, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "ndLevelResult", 1, [33, 32], [0, "63Q8fAbDhNE72US3nnqEk6", 1, 0]], [13, "block", 178.5, 1, [[17, 0, -175, [0], 1]], [0, "f16ht2x3dGeZRmzHf92D40", 1, 0], [4, 4278190080], [5, 1280, 720]], [1, "bgWrong", 7, [[2, -176, [6], 7]], [0, "1awmqAotFDLYhlJmSAhIf5", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 7, [[2, -177, [8], 9]], [0, "a3M0Owqq9MKb+g6L74LIRG", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 7, [[2, -178, [10], 11]], [0, "9ckHpCqydAapyHpO5wDKhF", 1, 0], [5, 15, 36], [-1.95, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 7, [[2, -179, [12], 13]], [0, "e2wHXeBBlDAIm5LN/5Ouos", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 7, [[2, -180, [14], 15]], [0, "24ptbdbj5F/70Bq6kPMCJZ", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 8, [[2, -181, [16], 17]], [0, "83opPu/7pBQbWWCMBuvb2j", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 8, [[2, -182, [18], 19]], [0, "7cgk515sRMwJnycDVqgXxA", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 8, [[2, -183, [20], 21]], [0, "a3QIv4wJlJgbYUAIJt5Z3p", 1, 0], [5, 25, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 8, [[2, -184, [22], 23]], [0, "1c25+IuXVJVrYr1XNEzNol", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 8, [[2, -185, [24], 25]], [0, "49SLS9afFDpLRtT2SX0LGd", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 9, [[2, -186, [26], 27]], [0, "f4dm0XLsxN/KxKRzjeM2vM", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 9, [[2, -187, [28], 29]], [0, "8dAd2RK3dKa6ZychwTET+X", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 9, [[2, -188, [30], 31]], [0, "f2cDoAyOxPUJ3lJ79mey4j", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 9, [[2, -189, [32], 33]], [0, "2cZYUKEhlIDpfLZVG5mHiB", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 9, [[2, -190, [34], 35]], [0, "16QbNEeHJCqb+9QYY6e9n1", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 10, [[2, -191, [36], 37]], [0, "8cEJw9O7tBVqMNZSELF74K", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 10, [[2, -192, [38], 39]], [0, "e9LfL2WQ9KdL2fiAU7sfYK", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 10, [[2, -193, [40], 41]], [0, "9f/knh6EBECIvE4JbqMpdm", 1, 0], [5, 27, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 10, [[2, -194, [42], 43]], [0, "5569ZIUcFJJ7aRbpsyYpkq", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 10, [[2, -195, [44], 45]], [0, "bdLNbcqTlMxpj24aIOjjs+", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 11, [[2, -196, [46], 47]], [0, "6bEnk5+OZNHbdf9QwdsHF+", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 11, [[2, -197, [48], 49]], [0, "25JbjmccJBs4aq2gcsTFF+", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 11, [[2, -198, [50], 51]], [0, "baIPPVCfZDVp7GsV32jCXv", 1, 0], [5, 26, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 11, [[2, -199, [52], 53]], [0, "6ayVKBzH9NBLJhj44qDifH", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 11, [[2, -200, [54], 55]], [0, "371aejrHNGYaQy4YWv7zaP", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 12, [[2, -201, [56], 57]], [0, "f7ChLFg+9D1rmVppG7mMEd", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 12, [[2, -202, [58], 59]], [0, "864v1VBGJHLK/PjXFQzHOc", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 12, [[2, -203, [60], 61]], [0, "a2XWaO75VPFJ5isnzQoRFV", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 12, [[2, -204, [62], 63]], [0, "92NVS7CX5L248bRWKSWY4V", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 12, [[2, -205, [64], 65]], [0, "28uD0GdAdIxreRKSrDqD9K", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 13, [[2, -206, [66], 67]], [0, "78wE2GxnhLI4xdm8jRuCqk", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 13, [[2, -207, [68], 69]], [0, "1bWGhvGm5OEK/T27nPExvR", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 13, [[2, -208, [70], 71]], [0, "a5a/FZv35EzI6zWgF0LdlJ", 1, 0], [5, 25, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 13, [[2, -209, [72], 73]], [0, "c0IdsBBwlGxp/D2mEiU5SE", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 13, [[2, -210, [74], 75]], [0, "97r3CetFJP9psssrAlysjn", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 14, [[2, -211, [76], 77]], [0, "d9OZSnHcFDxZkmj8trMBYT", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 14, [[2, -212, [78], 79]], [0, "14RO62lDJGV41kLtTOhBbS", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 14, [[2, -213, [80], 81]], [0, "50pbbpwhhLzJPmIHNrhNWR", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 14, [[2, -214, [82], 83]], [0, "6dDbdNlDlIwYBXkwV/h6tz", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 14, [[2, -215, [84], 85]], [0, "56zMwtD69HG4QhJgBgGdqt", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 15, [[2, -216, [86], 87]], [0, "9ak7f86tNEYYUPNVt5929W", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 15, [[2, -217, [88], 89]], [0, "c6DFdbWddGvKQ3Bbu84kxC", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 15, [[2, -218, [90], 91]], [0, "32H0wwHg1GmJjI6ohhLzj7", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 15, [[2, -219, [92], 93]], [0, "96/+oxMZFH7phYaL6ieKnE", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 15, [[2, -220, [94], 95]], [0, "f2zuin6V1LvJMFm7OcUsk+", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 16, [[2, -221, [96], 97]], [0, "8a97fdbuxI64R05OmpGksi", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 16, [[2, -222, [98], 99]], [0, "9fu02gsXJGLIqITP5tQ1er", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 16, [[2, -223, [100], 101]], [0, "7fyGJqu5xM44l7bLkEXxWc", 1, 0], [5, 45, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 16, [[2, -224, [102], 103]], [0, "866MyL845I94//HSjLO6CB", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 16, [[2, -225, [104], 105]], [0, "eeuw1mHJhJlZtczg8fhabM", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 17, [[2, -226, [106], 107]], [0, "24CmsCuwZAEoovsjT/kMUg", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 17, [[2, -227, [108], 109]], [0, "00SO2u2pZFX5LXtseFdIUc", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 17, [[2, -228, [110], 111]], [0, "97K4ptAuhGoqpSPXJBFiDT", 1, 0], [5, 15, 36], [-1.95, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 17, [[2, -229, [112], 113]], [0, "84DIBMMytNkZUD2GBr7lw9", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 17, [[2, -230, [114], 115]], [0, "23D47vjLxKW5t6olmcOmul", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 18, [[2, -231, [116], 117]], [0, "43NfqqHrNAvZRLc9SUmUnl", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 18, [[2, -232, [118], 119]], [0, "7528p1nXNLabHVE7uL1fu5", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 18, [[2, -233, [120], 121]], [0, "45ZVRiLitKTYDDKvcNUS/T", 1, 0], [5, 25, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 18, [[2, -234, [122], 123]], [0, "25gGVaivhFUYhziRHKA1WO", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 18, [[2, -235, [124], 125]], [0, "5dcPlhTS5Ku7KSlQYOZo4z", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 19, [[2, -236, [126], 127]], [0, "45VYDWLmVITbNUvBAfGUmX", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 19, [[2, -237, [128], 129]], [0, "bfUV/BX9NCnbWXkMCllHsW", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 19, [[2, -238, [130], 131]], [0, "6dwShaFS5Ns6VFS8Cp+Wnu", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 19, [[2, -239, [132], 133]], [0, "abylhdkThFfYd3WyKUOkGF", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 19, [[2, -240, [134], 135]], [0, "b701E++x1LZYlwozR1/UcY", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 20, [[2, -241, [136], 137]], [0, "0aA+8EB6dHGKXq4nsl6j7U", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 20, [[2, -242, [138], 139]], [0, "a5McjswlJPqJVR+AR59Ohi", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 20, [[2, -243, [140], 141]], [0, "50tfXnJ5FJso5TP549G2Eu", 1, 0], [5, 27, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 20, [[2, -244, [142], 143]], [0, "c2fdjI2FJCI4yIS4L5dPAp", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 20, [[2, -245, [144], 145]], [0, "51drPqTD1DDKTYgR4jZLOB", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 21, [[2, -246, [146], 147]], [0, "37CeHeYnNKyYg05TVkeNQA", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 21, [[2, -247, [148], 149]], [0, "73C7zswKRIqJvT8aG4A8cc", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 21, [[2, -248, [150], 151]], [0, "c1YOP+fiVLm4LV2UtrW8Wz", 1, 0], [5, 26, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 21, [[2, -249, [152], 153]], [0, "02fXCEEUZOdqCRqN1ok/ca", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 21, [[2, -250, [154], 155]], [0, "e6Jqov7YpBlaLLm9dQkv1V", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 22, [[2, -251, [156], 157]], [0, "1beq1e6llPS6ItrcxsFdhP", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 22, [[2, -252, [158], 159]], [0, "afoEvJITdIX4fQn67lz77v", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 22, [[2, -253, [160], 161]], [0, "eaM1kp+D1BMZkgVrLNHq4q", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 22, [[2, -254, [162], 163]], [0, "85vn+ck7RLi4twrD/HlH4b", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 22, [[2, -255, [164], 165]], [0, "b7L77O4+VFQLlCogqRX7Ec", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 23, [[2, -256, [166], 167]], [0, "76LtNqrFdHU6qocWteIz/W", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 23, [[2, -257, [168], 169]], [0, "ce/9o9d4lPPq4gs395dIAi", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 23, [[2, -258, [170], 171]], [0, "30ziR/4EVOuKSerxw+yO80", 1, 0], [5, 25, 35], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 23, [[2, -259, [172], 173]], [0, "110aJyDMhMZqWf98x5D73J", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 23, [[2, -260, [174], 175]], [0, "ae7BsWTxRP35Lzc7YLM7nz", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 24, [[2, -261, [176], 177]], [0, "4e8Hw4JjdMUZprwFA9pXd2", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 24, [[2, -262, [178], 179]], [0, "99j14QrV1JSreDvtXu5Y4l", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 24, [[2, -263, [180], 181]], [0, "b7dxesWLhFrZAD1ysHfHsQ", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 24, [[2, -264, [182], 183]], [0, "65R/KrF4ZL3KeoK5ZxC1on", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 24, [[2, -265, [184], 185]], [0, "d4QfmSLm1NNKFaBC6OQWxL", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 25, [[2, -266, [186], 187]], [0, "9eHiPonehFYKhzxd/fW7H+", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 25, [[2, -267, [188], 189]], [0, "07xqqVEwNJvbDQxYGqWe0A", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 25, [[2, -268, [190], 191]], [0, "6csbkcTOBMrIMN/ONRx4tt", 1, 0], [5, 26, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 25, [[2, -269, [192], 193]], [0, "7385Ei/VJAQL/5YPG9qMbe", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 25, [[2, -270, [194], 195]], [0, "cc1FUQdi5KtYO+Y16ndk/F", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 26, [[2, -271, [196], 197]], [0, "d65I7g8r5FEL0qyTcm8NnF", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 26, [[2, -272, [198], 199]], [0, "347fNR8ChIWJED2pWQFwwz", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 26, [[2, -273, [200], 201]], [0, "beZiG9j6JAaLfzSZhQXfqx", 1, 0], [5, 45, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 26, [[2, -274, [202], 203]], [0, "4cTw3h2SFAd5rEG/d3lOPq", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 26, [[2, -275, [204], 205]], [0, "60HqKDXVJNr6zX2BeHt2jF", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 27, [[2, -276, [206], 207]], [0, "8e5cT1UkBPI5Fg/pBtX5vP", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 27, [[2, -277, [208], 209]], [0, "10WCf0/BZHK5QvWFM3KhnT", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 27, [[2, -278, [210], 211]], [0, "55eYrPjmdMAZUhHIwhm+s+", 1, 0], [5, 39, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 27, [[2, -279, [212], 213]], [0, "69j2fE6xJNQpKJS5S3dkd7", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 27, [[2, -280, [214], 215]], [0, "22RW1q/qdGWLvHiCmBXUtn", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 28, [[2, -281, [216], 217]], [0, "4bwEi5oLBOiqDZmj1YkNi1", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 28, [[2, -282, [218], 219]], [0, "41JTPR2c1EuIKdCvwWjPT5", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 28, [[2, -283, [220], 221]], [0, "b1iIMdJUROCZm9QW0917DR", 1, 0], [5, 47, 37], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 28, [[2, -284, [222], 223]], [0, "157Bi7igVJsa+9Y3pYJjP4", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 28, [[2, -285, [224], 225]], [0, "c78PpRpU5FZLZBH8TzyRrH", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 29, [[2, -286, [226], 227]], [0, "49I5w+nnRFrK+PUXKEvqv8", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 29, [[2, -287, [228], 229]], [0, "0bJLLpnjBFj4U06rauqzqT", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 29, [[2, -288, [230], 231]], [0, "67wvADuAlLUZxsdTI/iQQ6", 1, 0], [5, 47, 37], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 29, [[2, -289, [232], 233]], [0, "8dXcXOxMlHLKTtAyhpaELw", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 29, [[2, -290, [234], 235]], [0, "a9DQvKsepFDYPLVBhmAoto", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 30, [[2, -291, [236], 237]], [0, "a58I5iQXlMRr7kmOmE1WXY", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 30, [[2, -292, [238], 239]], [0, "3a+O7n7A5C5IsgMZHibuqq", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 30, [[2, -293, [240], 241]], [0, "10zDBEia1BRICehagAEHI0", 1, 0], [5, 48, 37], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 30, [[2, -294, [242], 243]], [0, "c9QwgQ36hPE4Kdp+KW2nV/", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 30, [[2, -295, [244], 245]], [0, "2feObvVSpFCbF7DHiU0XBX", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgWrong", 31, [[2, -296, [246], 247]], [0, "08QYVekE1H16PdJrS8p7rq", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bgRight", 31, [[2, -297, [248], 249]], [0, "baeKPx2JRIxpXrsFBQZKxI", 1, 0], [5, 76, 76], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ndNum", 31, [[2, -298, [250], 251]], [0, "ea1mgmdvlPnoAFTC1CxEX0", 1, 0], [5, 47, 36], [0, -13, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagWrong", 31, [[2, -299, [252], 253]], [0, "edMj7pyk1NK7PFvmPcGEHN", 1, 0], [5, 43, 43], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "flagRight", 31, [[2, -300, [254], 255]], [0, "81WoiNRwFEEokd48G2guw9", 1, 0], [5, 59, 45], [0, 27.702, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 0, 1, 0, 5, 6, 0, 6, 5, 0, 7, 4, 0, 8, 3, 0, 9, 2, 0, 10, 35, 0, 11, 36, 0, 0, 1, 0, -1, 38, 0, -2, 34, 0, -3, 37, 0, 0, 2, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, -4, 10, 0, -5, 11, 0, 0, 3, 0, -1, 12, 0, -2, 13, 0, -3, 14, 0, -4, 15, 0, -5, 16, 0, 0, 4, 0, -1, 17, 0, -2, 18, 0, -3, 19, 0, -4, 20, 0, -5, 21, 0, 0, 5, 0, -1, 22, 0, -2, 23, 0, -3, 24, 0, -4, 25, 0, -5, 26, 0, 0, 6, 0, -1, 27, 0, -2, 28, 0, -3, 29, 0, -4, 30, 0, -5, 31, 0, -1, 39, 0, -2, 40, 0, -3, 41, 0, -4, 42, 0, -5, 43, 0, -1, 44, 0, -2, 45, 0, -3, 46, 0, -4, 47, 0, -5, 48, 0, -1, 49, 0, -2, 50, 0, -3, 51, 0, -4, 52, 0, -5, 53, 0, -1, 54, 0, -2, 55, 0, -3, 56, 0, -4, 57, 0, -5, 58, 0, -1, 59, 0, -2, 60, 0, -3, 61, 0, -4, 62, 0, -5, 63, 0, -1, 64, 0, -2, 65, 0, -3, 66, 0, -4, 67, 0, -5, 68, 0, -1, 69, 0, -2, 70, 0, -3, 71, 0, -4, 72, 0, -5, 73, 0, -1, 74, 0, -2, 75, 0, -3, 76, 0, -4, 77, 0, -5, 78, 0, -1, 79, 0, -2, 80, 0, -3, 81, 0, -4, 82, 0, -5, 83, 0, -1, 84, 0, -2, 85, 0, -3, 86, 0, -4, 87, 0, -5, 88, 0, -1, 89, 0, -2, 90, 0, -3, 91, 0, -4, 92, 0, -5, 93, 0, -1, 94, 0, -2, 95, 0, -3, 96, 0, -4, 97, 0, -5, 98, 0, -1, 99, 0, -2, 100, 0, -3, 101, 0, -4, 102, 0, -5, 103, 0, -1, 104, 0, -2, 105, 0, -3, 106, 0, -4, 107, 0, -5, 108, 0, -1, 109, 0, -2, 110, 0, -3, 111, 0, -4, 112, 0, -5, 113, 0, -1, 114, 0, -2, 115, 0, -3, 116, 0, -4, 117, 0, -5, 118, 0, -1, 119, 0, -2, 120, 0, -3, 121, 0, -4, 122, 0, -5, 123, 0, -1, 124, 0, -2, 125, 0, -3, 126, 0, -4, 127, 0, -5, 128, 0, -1, 129, 0, -2, 130, 0, -3, 131, 0, -4, 132, 0, -5, 133, 0, -1, 134, 0, -2, 135, 0, -3, 136, 0, -4, 137, 0, -5, 138, 0, -1, 139, 0, -2, 140, 0, -3, 141, 0, -4, 142, 0, -5, 143, 0, -1, 144, 0, -2, 145, 0, -3, 146, 0, -4, 147, 0, -5, 148, 0, -1, 149, 0, -2, 150, 0, -3, 151, 0, -4, 152, 0, -5, 153, 0, -1, 154, 0, -2, 155, 0, -3, 156, 0, -4, 157, 0, -5, 158, 0, -1, 159, 0, -2, 160, 0, -3, 161, 0, -4, 162, 0, -5, 163, 0, 0, 32, 0, 0, 33, 0, -1, 35, 0, -2, 36, 0, 0, 35, 0, 0, 36, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, 0, 43, 0, 0, 44, 0, 0, 45, 0, 0, 46, 0, 0, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, 0, 52, 0, 0, 53, 0, 0, 54, 0, 0, 55, 0, 0, 56, 0, 0, 57, 0, 0, 58, 0, 0, 59, 0, 0, 60, 0, 0, 61, 0, 0, 62, 0, 0, 63, 0, 0, 64, 0, 0, 65, 0, 0, 66, 0, 0, 67, 0, 0, 68, 0, 0, 69, 0, 0, 70, 0, 0, 71, 0, 0, 72, 0, 0, 73, 0, 0, 74, 0, 0, 75, 0, 0, 76, 0, 0, 77, 0, 0, 78, 0, 0, 79, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, 0, 90, 0, 0, 91, 0, 0, 92, 0, 0, 93, 0, 0, 94, 0, 0, 95, 0, 0, 96, 0, 0, 97, 0, 0, 98, 0, 0, 99, 0, 0, 100, 0, 0, 101, 0, 0, 102, 0, 0, 103, 0, 0, 104, 0, 0, 105, 0, 0, 106, 0, 0, 107, 0, 0, 108, 0, 0, 109, 0, 0, 110, 0, 0, 111, 0, 0, 112, 0, 0, 113, 0, 0, 114, 0, 0, 115, 0, 0, 116, 0, 0, 117, 0, 0, 118, 0, 0, 119, 0, 0, 120, 0, 0, 121, 0, 0, 122, 0, 0, 123, 0, 0, 124, 0, 0, 125, 0, 0, 126, 0, 0, 127, 0, 0, 128, 0, 0, 129, 0, 0, 130, 0, 0, 131, 0, 0, 132, 0, 0, 133, 0, 0, 134, 0, 0, 135, 0, 0, 136, 0, 0, 137, 0, 0, 138, 0, 0, 139, 0, 0, 140, 0, 0, 141, 0, 0, 142, 0, 0, 143, 0, 0, 144, 0, 0, 145, 0, 0, 146, 0, 0, 147, 0, 0, 148, 0, 0, 149, 0, 0, 150, 0, 0, 151, 0, 0, 152, 0, 0, 153, 0, 0, 154, 0, 0, 155, 0, 0, 156, 0, 0, 157, 0, 0, 158, 0, 0, 159, 0, 0, 160, 0, 0, 161, 0, 0, 162, 0, 0, 163, 0, 12, 1, 2, 3, 33, 3, 3, 33, 4, 3, 32, 5, 3, 32, 6, 3, 32, 32, 3, 37, 33, 3, 37, 300], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, -1, 1, 13, 14], [0, 18, 0, 19, 0, 20, 0, 1, 0, 2, 0, 5, 0, 3, 0, 4, 0, 1, 0, 2, 0, 6, 0, 3, 0, 4, 0, 1, 0, 2, 0, 7, 0, 3, 0, 4, 0, 1, 0, 2, 0, 8, 0, 3, 0, 4, 0, 1, 0, 2, 0, 9, 0, 3, 0, 4, 0, 1, 0, 2, 0, 10, 0, 3, 0, 4, 0, 1, 0, 2, 0, 11, 0, 3, 0, 4, 0, 1, 0, 2, 0, 12, 0, 3, 0, 4, 0, 1, 0, 2, 0, 13, 0, 3, 0, 4, 0, 1, 0, 2, 0, 14, 0, 3, 0, 4, 0, 1, 0, 2, 0, 5, 0, 3, 0, 4, 0, 1, 0, 2, 0, 6, 0, 3, 0, 4, 0, 1, 0, 2, 0, 7, 0, 3, 0, 4, 0, 1, 0, 2, 0, 8, 0, 3, 0, 4, 0, 1, 0, 2, 0, 9, 0, 3, 0, 4, 0, 1, 0, 2, 0, 10, 0, 3, 0, 4, 0, 1, 0, 2, 0, 11, 0, 3, 0, 4, 0, 1, 0, 2, 0, 12, 0, 3, 0, 4, 0, 1, 0, 2, 0, 13, 0, 3, 0, 4, 0, 1, 0, 2, 0, 14, 0, 3, 0, 4, 0, 1, 0, 2, 0, 21, 0, 3, 0, 4, 0, 1, 0, 2, 0, 22, 0, 3, 0, 4, 0, 1, 0, 2, 0, 23, 0, 3, 0, 4, 0, 1, 0, 2, 0, 24, 0, 3, 0, 4, 0, 1, 0, 2, 0, 25, 0, 3, 0, 4, 26, 27]], [[{"name": "num_11", "rect": [16, 19, 39, 36], "offset": [-3, 2], "originalSize": [77, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [28]], [[{"name": "bg_R", "rect": [58, 32, 984, 572], "offset": [16, -4], "originalSize": [1068, 628], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [29]], [[{"name": "num_6", "rect": [25, 19, 26, 36], "offset": [0, 1.5], "originalSize": [76, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [30]], [[{"name": "num_8", "rect": [25, 20, 26, 36], "offset": [-1, 1], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [31]], [[{"name": "large_bg_W", "rect": [63, 17, 986, 594], "offset": [22, 0], "originalSize": [1068, 628], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [32]], [[{"name": "result_question_right", "rect": [1, 1, 59, 45], "offset": [0.5, -0.5], "originalSize": [60, 46], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [33]], [[{"name": "num_12", "rect": [14, 18, 47, 37], "offset": [-1.5, 2.5], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [34]], [[{"name": "result_question_error", "rect": [1, 1, 43, 43], "offset": [0.5, -0.5], "originalSize": [44, 44], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [35]], [[{"name": "num_1", "rect": [27, 19, 15, 36], "offset": [-3.5, 1], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [36]], [[{"name": "num_7", "rect": [26, 21, 25, 35], "offset": [-0.5, 0.5], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [37]], [[{"name": "num_4", "rect": [23, 20, 27, 36], "offset": [-2.5, 0.5], "originalSize": [78, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [38]], [[{"name": "num_15", "rect": [13, 19, 47, 36], "offset": [-2.5, 2], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [39]], [[{"name": "num_14", "rect": [14, 18, 48, 37], "offset": [-1, 2.5], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [40]], [[{"name": "num_5", "rect": [25, 21, 26, 35], "offset": [-1, 0], "originalSize": [78, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [41]], [[{"name": "num_2", "rect": [26, 20, 25, 35], "offset": [-0.5, 1], "originalSize": [78, 77], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [42]], [[{"name": "bg_wrong", "rect": [0, 0, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [43]], [[{"name": "bg_right", "rect": [0, 0, 76, 76], "offset": [0, 0], "originalSize": [76, 76], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [44]], [[{"name": "num_10", "rect": [16, 20, 45, 36], "offset": [-0.5, 1], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [45]], [[{"name": "num_13", "rect": [14, 18, 47, 37], "offset": [-1.5, 2.5], "originalSize": [78, 78], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [46]], [[{"name": "large_bg_R", "rect": [58, 14, 991, 600], "offset": [19.5, 0], "originalSize": [1068, 628], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [47]], [[{"name": "bg_W", "rect": [58, 36, 981, 565], "offset": [14.5, -4.5], "originalSize": [1068, 628], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [2], [48]]]]