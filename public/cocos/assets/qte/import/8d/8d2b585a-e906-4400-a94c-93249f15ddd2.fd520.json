[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "action.d", "export class Action {\r\n    /** 获取贝塞尔路径 */\r\n    public static getBezierPoints(num, p1, c1, c2, p2): any[];\r\n\r\n    /**\r\n     * 获取埃尔米特曲线路径点 经过控制点\r\n     * @param controlPoints \r\n     */\r\n    public static getHermitePoints(controlPoints: Array<{ x: number, y: number }>): Array<{ x: number, y: number }>;\r\n    /**\r\n     * 获取多个点的贝塞尔\r\n     * @param num 返回点基础数量\r\n     * @param ctrlPosArray 控制点数组\r\n     */\r\n    public static getBezierByArray(num, ctrlPosArray: any[]): any[]; \r\n}\r\n"]], 0, 0, [], [], []]