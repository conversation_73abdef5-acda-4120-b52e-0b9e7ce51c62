[1, ["ecpdLyjvZBwrvm+cedCcQy", "f5xN0W8bNEsYDAUDCTLUFd", "47G6UXy0RJKL18MoAxta7C", "6bXKX48/dMf7zbXAe1GIsn"], ["node", "_spriteFrame", "H_prefab", "root", "draw1Node", "draw0Node", "rebbage1", "rebbage0", "timu1", "timu0", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_parent", "_components", "_children", "_trs", "_color"], 1, 4, 5, 1, 9, 2, 7, 5], ["cc.Label", ["_fontSize", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "_string", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["0e293s3FWVLMaWUxihQlDZn", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$spacingX", "node", "_layoutSize"], -1, 1, 5], ["0c371+ci5hF84YbQW/PhsBj", ["node", "timu0", "timu1", "rebbage0", "rebbage1", "draw0Node", "draw1Node", "H_prefab"], 3, 1, 1, 1, 1, 1, 1, 1, 6]], [[2, 0, 1, 2], [0, 0, 4, 5, 2, 3, 2], [0, 0, 1, 4, 6, 2, 3, 7, 3], [0, 0, 4, 5, 2, 3, 7, 2], [0, 0, 4, 6, 2, 3, 7, 2], [5, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 5, 1, 2, 3, 6, 7, 6], [3, 3, 4, 5, 1], [3, 0, 1, 2, 3, 4, 5, 4], [6, 0, 1], [4, 0, 2], [0, 0, 6, 5, 2, 3, 2], [0, 0, 4, 5, 2, 8, 3, 7, 2], [0, 0, 4, 6, 5, 2, 3, 2], [1, 4, 0, 1, 2, 3, 6, 7, 6], [2, 1, 1], [7, 0, 1, 2, 3, 4, 5, 5], [8, 0, 1, 2, 3, 4, 5, 6, 7, 1]], [[10, "PtoH_1"], [11, "PtoH_1", [-9, -10], [[17, -8, -7, -6, -5, -4, -3, -2, 11]], [15, -1], [5, 1280, 720]], [13, "group", 1, [-12, -13], [[16, 1, 1, -50, 250, -11, [5, -300, 720]]], [0, "40tLYqGXFM6ZarJpME3O1P", 1], [5, -300, 720]], [2, "g1", false, 2, [-14, -15, -16], [0, "5dNiWdSrRJZqPTAim3qfZK", 1], [5, 369, 369], [-334.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "g2", false, 2, [-17, -18, -19], [0, "011gu0/LlGnZDqSQm93KZU", 1], [5, 369, 369], [284.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "lajitong1", 3, [[7, -20, [2], 3]], [0, "3drNVP9dVKBpS5XTyyNs8m", 1], [5, 72, 72], [0, -318.556, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "drawGroup1", 3, [-21, -22], [0, "d8AdZDBT9Av7LELGGQ18Ds", 1], [5, 369, 369], [0, -57.771, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "draw1", 6, [[9, -23]], [0, "33uJIm/8NN+LTrcGuGR7uQ", 1], [5, 369, 369]], [3, "lajitong2", 4, [[7, -24, [7], 8]], [0, "e0IsELLDZGkaYJYfrwsL94", 1], [5, 72, 72], [0, -318.556, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "drawGroup2", 4, [-25, -26], [0, "16iWqa3hFHkI5hSyiHlqOm", 1], [5, 369, 369], [0, -57.771, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "draw2", 9, [[9, -27]], [0, "e8QKzCSgRFqJa32/GmHdwz", 1], [5, 369, 369]], [12, "timu", 1, [[14, "根据拼音写汉字", 34, 1, 1, 1, -28, [0]]], [0, "bccbj2hGJLOZkvYvyzQbXc", 1], [4, 4280905377], [5, 238, 51], [0, 284.603, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "pyin1", 3, [-29], [0, "1fhbsT0p9ENpDSYdj4LITj", 1], [4, 4278190080], [5, 196, 111], [0, 207.561, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 88, 88, 1, 1, 1, 12, [1]], [1, "bg", 6, [[8, 1, 1, 2, -30, [4], 5]], [0, "8dq7LVtfxGx7AGA7lEkgxw", 1], [5, 396, 396]], [5, "pyin2", 4, [-31], [0, "5e5MTsyspM7Ye4fpGJkM/6", 1], [4, 4278190080], [5, 196, 111], [0, 207.561, 0, 0, 0, 0, 1, 1, 1, 1]], [6, 88, 88, 1, 1, 1, 15, [6]], [1, "bg", 9, [[8, 1, 1, 2, -32, [9], 10]], [0, "abF0TDSo9FDbQMM7MrVeNH", 1], [5, 396, 396]]], 0, [0, 3, 1, 0, 4, 10, 0, 5, 7, 0, 6, 8, 0, 7, 5, 0, 8, 16, 0, 9, 13, 0, 0, 1, 0, -1, 11, 0, -2, 2, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -1, 12, 0, -2, 5, 0, -3, 6, 0, -1, 15, 0, -2, 8, 0, -3, 9, 0, 0, 5, 0, -1, 14, 0, -2, 7, 0, 0, 7, 0, 0, 8, 0, -1, 17, 0, -2, 10, 0, 0, 10, 0, 0, 11, 0, -1, 13, 0, 0, 14, 0, -1, 16, 0, 0, 17, 0, 10, 1, 32], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, -1, -1, 1, -1, 1, -1, -1, 1, -1, 1, 2], [0, 0, 0, 1, 0, 2, 0, 0, 1, 0, 2, 3]]