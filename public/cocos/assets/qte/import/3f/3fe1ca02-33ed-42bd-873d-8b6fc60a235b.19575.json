[1, ["ecpdLyjvZBwrvm+cedCcQy", "02o8C7QCFE3acyKiZ978ZE"], ["node", "_spriteFrame", "root", "questionNumber", "data"], [["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize", "_trs"], 2, 2, 9, 4, 5, 7], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3], ["cc.Sprite", ["_srcBlendFactor", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["1e644Iut8VMSoybIJPVF4lA", ["node", "questionNumber"], 3, 1, 1]], [[1, 0, 2], [2, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 2, 3, 4, 5, 5], [0, 0, 1, 2], [0, 1, 1], [5, 0, 1, 2, 3, 2], [6, 0, 1, 1]], [[0, "questionNumberUI"], [1, "questionNumberUI", [-5], [[6, 1, -2, [1], 2], [7, -4, -3]], [5, -1], [5, 151, 59], [-564.5, 296.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "labelNum", 1, [-6], [4, "96JeyR3W5A1a2NHAsflNC4", 1], [5, 45, 51], [-4, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "0/0", 32, 1, 1, 2, [0]]], 0, [0, 2, 1, 0, 0, 1, 0, 3, 3, 0, 0, 1, 0, -1, 2, 0, -1, 3, 0, 4, 1, 6], [0, 0, 0], [-1, -1, 1], [0, 0, 1]]