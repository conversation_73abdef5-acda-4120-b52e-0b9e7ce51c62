[1, ["ecpdLyjvZBwrvm+cedCcQy", "827dFyqQZM0YXyn898vF1d", "4deYyVkUlEvK05dvytL70I", "74Z6sSY3xFcJRy7TzHmXqI", "6bXKX48/dMf7zbXAe1GIsn", "7bd9PqRjBPCImOIpK5pT/N", "d559ZysGRMPoKFAAtTHiGT"], ["node", "_spriteFrame", "root", "touchSp", "H_prefab", "H_prefab_big", "P_prefab", "rightAnswerContent", "answerContent", "<PERSON>im<PERSON><PERSON><PERSON><PERSON>", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_color", "_children"], 1, 9, 4, 5, 1, 7, 5, 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "_top", "_bottom", "alignMode", "node"], -5, 1], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["e2e14btcdlLI5mE4KMzIyz/", ["node", "touchSp"], 3, 1, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "_N$paddingRight", "_N$spacingX", "_N$affectedByScale", "node", "_layoutSize"], -3, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["c35abhVuR9CjZ52I7kwxyzW", ["node", "<PERSON>im<PERSON><PERSON><PERSON><PERSON>", "answerContent", "rightAnswerContent", "H_prefab", "H_prefab_big", "P_prefab"], 3, 1, 1, 1, 1, 6, 6, 6]], [[2, 0, 1, 2], [0, 0, 5, 8, 2, 3, 4, 6, 2], [4, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 5, 6, 6], [0, 0, 5, 2, 3, 4, 2], [0, 0, 5, 2, 3, 7, 4, 2], [1, 0, 1, 2, 8, 4], [1, 0, 3, 4, 5, 6, 1, 2, 8, 8], [6, 0, 1, 1], [7, 0, 1, 2, 3, 4, 5, 6, 7, 7], [0, 0, 1, 5, 2, 3, 4, 3], [3, 0, 2], [0, 0, 8, 2, 3, 4, 2], [0, 0, 5, 2, 3, 7, 4, 6, 2], [0, 0, 1, 5, 2, 3, 7, 4, 6, 3], [0, 0, 5, 2, 3, 4, 6, 2], [2, 1, 1], [1, 7, 0, 8, 3], [8, 0, 1], [9, 0, 1, 2, 3, 4, 5, 6, 1]], [[11, "End"], [12, "End", [-6, -7, -8, -9, -10, -11], [[19, -5, -4, -3, -2, 19, 20, 21]], [16, -1], [5, 1280, 720]], [1, "timu", 1, [-13, -14, -15, -16], [[9, 1, 1, 120, 220, 200, true, -12, [5, 1308, 92]]], [0, "4aoKy4cXJL+aCgCTZgILUV", 1], [5, 1308, 92], [0, 215.842, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "H", 2, [-19, -20], [[8, -18, 5]], [0, "f6KfNkYORHvJh0ukh10laJ", -17], [5, 92, 92], [-488, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "H", 2, [-23, -24], [[8, -22, 9]], [0, "75DcOYFHJBw5nRJC+bnD+i", -21], [5, 92, 92], [-196, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "H", 2, [-27, -28], [[8, -26, 13]], [0, "02ArUS/yhIopoiGWFvW1hl", -25], [5, 92, 92], [96, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "H", 2, [-31, -32], [[8, -30, 17]], [0, "71m07mIPRHyprEY0KKsICH", -29], [5, 92, 92], [388, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "bg", 3, [[2, -33, [2], 3], [6, 45, 77, 98, -34]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 3], [5, 92, 92]], [5, "label", 3, [[3, "生", 77, 77, 1, 1, -35, [4]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -36]], [0, "d5f5UruGhA9IQWYSnwRjwM", 3], [4, 4278190084], [5, 77, 98]], [4, "bg", 4, [[2, -37, [6], 7], [6, 45, 77, 98, -38]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 4], [5, 92, 92]], [5, "label", 4, [[3, "生", 77, 77, 1, 1, -39, [8]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -40]], [0, "d5f5UruGhA9IQWYSnwRjwM", 4], [4, 4278190084], [5, 77, 98]], [4, "bg", 5, [[2, -41, [10], 11], [6, 45, 77, 98, -42]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 5], [5, 92, 92]], [5, "label", 5, [[3, "生", 77, 77, 1, 1, -43, [12]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -44]], [0, "d5f5UruGhA9IQWYSnwRjwM", 5], [4, 4278190084], [5, 77, 98]], [4, "bg", 6, [[2, -45, [14], 15], [6, 45, 77, 98, -46]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 6], [5, 92, 92]], [5, "label", 6, [[3, "生", 77, 77, 1, 1, -47, [16]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -48]], [0, "d5f5UruGhA9IQWYSnwRjwM", 6], [4, 4278190084], [5, 77, 98]], [13, "answer", 1, [[9, 1, 1, 120, 220, 200, true, -49, [5, 1199.04, 150]]], [0, "92VwufU09GLqkt9pi6Dunj", 1], [4, 4279571777], [5, 1199.04, 150], [0, 96, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "mask", 512, 1, [[17, 2, 45, -50], [18, -51]], [0, "59h9XqAzZAIZI07Ap0xZhy", 1], [5, 1280, 720]], [15, "rightAnswer", 1, [[9, 1, 1, 120, 220, 200, true, -52, [5, 1280, 150]]], [0, "a6Sgp+zE1D25N6rd609jJ3", 1], [5, 1280, 150], [0, -170.625, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg", 512, 1, [[2, -53, [0], 1]], [0, "b5Z0z125VBsaG1M2eqSPT7", 1], [5, 1280, 720]], [14, "label", 512, 1, [[3, "正确答案", 33, 33, 1, 1, -54, [18]]], [0, "d4MmbktyJCBp1qbTOlVZSX", 1], [4, 4280374665], [5, 132, 42], [-523.375, -32.917, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 2, 1, 0, 7, 17, 0, 8, 15, 0, 9, 2, 0, 0, 1, 0, -1, 18, 0, -2, 2, 0, -3, 15, 0, -4, 16, 0, -5, 19, 0, -6, 17, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, 2, 3, 0, 0, 3, 0, -1, 7, 0, -2, 8, 0, 2, 4, 0, 0, 4, 0, -1, 9, 0, -2, 10, 0, 2, 5, 0, 0, 5, 0, -1, 11, 0, -2, 12, 0, 2, 6, 0, 0, 6, 0, -1, 13, 0, -2, 14, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 18, 0, 0, 19, 0, 10, 1, 54], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 1, -1, 3, -1, 4, 5, 6], [0, 3, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 1, 0, 2, 0, 4, 5, 6]]