[1, ["ecpdLyjvZBwrvm+cedCcQy", "eesgdixshOUa8lxmFO+rUb", "c2Dphrr71J05V8Um/299+k", "20NTKzzBZH07AoPrmIqA6x", "27GrQn2FZFh67S/etxswqB", "23/mivwmdEqI4KRdC6bX/J", "bfi/LenaVMLJcWqGjZq+UR", "83mkI4D3dNVqjaFULqfF0Z", "755PeldrtFtKEAdMbJaMib", "58Q10xwe9HlaKTJhfGWdkn"], ["node", "_spriteFrame", "root", "_scrollView", "dingAudio", "lostNode", "gradeLabel", "orNode", "subMBt", "soundLostLabel", "time<PERSON><PERSON><PERSON>", "result", "bottom", "questionNode", "resultAnswerNode", "resultTipsLabel", "timeTitleLabel", "tipsDecLabel", "qViewNode", "<PERSON><PERSON><PERSON><PERSON>", "data", "_parent"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_anchorPoint", "_color"], 1, 9, 4, 5, 2, 1, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_top", "_bottom", "alignMode", "_right", "node"], -5, 1], ["cc.Node", ["_name", "_opacity", "_active", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_color", "_trs", "_children"], 0, 1, 2, 4, 5, 5, 5, 7, 2], ["cc.Label", ["_fontSize", "_string", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "_N$overflow", "_N$cacheMode", "_styleFlags", "node", "_materials"], -5, 1, 3], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingBottom", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -2, 1, 1, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["430021Ar2xBs4NsbkqYGehc", ["node", "<PERSON><PERSON><PERSON><PERSON>", "qViewNode", "tipsDecLabel", "timeTitleLabel", "resultTipsLabel", "resultAnswerNode", "questionNode", "bottom", "result", "time<PERSON><PERSON><PERSON>", "soundLostLabel", "subMSp", "subMBt", "orNode", "gradeLabel", "lostNode", "dingAudio"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 6]], [[6, 0, 1, 2], [0, 0, 6, 2, 3, 9, 4, 8, 7, 2], [3, 1, 0, 2, 3, 8, 9, 5], [0, 0, 6, 5, 2, 3, 4, 8, 7, 2], [2, 0, 3, 4, 5, 8, 6, 7, 9, 2], [1, 0, 7, 8, 3], [4, 0, 1, 2, 3, 4, 5, 4], [0, 0, 6, 5, 2, 3, 4, 7, 2], [0, 0, 6, 5, 2, 3, 4, 8, 2], [5, 0, 2, 7, 3, 4, 5, 8, 6, 2], [5, 0, 1, 2, 7, 3, 4, 5, 8, 6, 3], [2, 0, 1, 3, 4, 5, 6, 7, 9, 3], [3, 0, 4, 5, 6, 8, 9, 5], [3, 1, 0, 4, 2, 3, 8, 9, 6], [3, 1, 0, 4, 2, 8, 9, 5], [7, 0, 1, 2, 4, 5, 4], [9, 0, 1, 2, 2], [1, 6, 0, 3, 2, 8, 5], [1, 0, 8, 2], [1, 0, 1, 2, 8, 4], [4, 0, 1, 2, 3, 4, 4], [4, 3, 4, 5, 1], [10, 0, 1, 2, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 6], [8, 0, 2], [0, 0, 5, 2, 3, 4, 2], [0, 0, 1, 6, 5, 2, 3, 4, 3], [0, 0, 6, 2, 3, 4, 7, 2], [0, 0, 1, 6, 5, 2, 3, 9, 4, 7, 3], [0, 0, 5, 2, 3, 4, 8, 2], [5, 0, 2, 3, 4, 5, 6, 2], [2, 0, 3, 4, 5, 8, 6, 7, 2], [2, 0, 1, 3, 4, 5, 8, 6, 7, 9, 3], [2, 0, 2, 3, 4, 5, 8, 6, 7, 9, 3], [2, 0, 2, 3, 10, 4, 5, 8, 6, 7, 9, 3], [3, 1, 0, 7, 2, 3, 8, 9, 6], [3, 1, 0, 4, 3, 8, 9, 5], [6, 1, 1], [7, 0, 1, 3, 4, 5, 4], [1, 0, 4, 5, 1, 2, 8, 6], [1, 0, 3, 1, 8, 4], [1, 0, 1, 8, 3], [4, 0, 3, 4, 2], [4, 0, 3, 4, 5, 2], [12, 0, 1, 2, 2], [13, 0, 1, 2, 3], [14, 0, 1], [15, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 1]], [[24, "ListenAnswerComponent"], [25, "template", [-19, -20, -21, -22], [[21, -2, [31], 32], [47, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, -7, [33, 34], -6, -5, -4, -3, 35]], [37, -1], [5, 1280, 720]], [29, "content", [-26, -27, -28], [[19, 40, 220, 250, -24], [15, 1, 2, 32, -25, [5, 1183, 375.4]]], [0, "0b1dZ5CuhBdanIvFbADEg6", -23], [5, 1183, 375.4], [0, 0, 1]], [26, "result", false, 1, [-31, -32, -33, -34, -35, -36], [[46, -29], [21, -30, [29], 30]], [0, "18pcngTFRKjZWIE/gF00gC", 1], [5, 1280, 720]], [9, "questionNode", 1, [-39, -40, -41, -42], [[[6, 1, 1, 0, -37, [9], 10], -38], 4, 1], [0, "558fcRXRFAjaH0LiJKOa1y", 1], [5, 1230, 440], [0, 0, 1], [-613, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "view", 4, [-45], [[16, 0, -43, [2]], [39, 45, 99.99999999999997, 1.9539925233402755e-14, 240, 250, -44]], [0, "53+Wl6sRNCIYyUxujpAJYA", 1], [5, 1230, 340], [0, 0, 1], [0, -100, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "lostRank", false, 3, [-48], [[35, "评级丢失了", 26, 1, 1, 1, -46, [17]], [5, 32, 49.18399999999997, -47]], [0, "79k+qw5wVDfpBw4ji0mszV", 1], [4, 4288913565], [5, 130, 51], [525.816, -273.516, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "list", 3, [-51, -52], [[[6, 1, 1, 0, -49, [27], 28], -50], 4, 1], [0, "c7g8qtguFCMIcMW2ovTeIa", 1], [5, 1183, 439.3], [0, 0, 1], [-591, 240, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "content", 5, [-54], [[15, 1, 2, 32, -53, [5, 300, 78]]], [0, "d6uB2gkRtFYahK7ERAMZpP", 1], [5, 300, 78], [0, 0, 1], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "scrollBar", 0, 4, [-57], [[-55, [17, 0, 37, 350.07654921020657, 237, -56]], 1, 4], [0, "ccjp9cBvlOCaa3z4FcHM2l", 1], [5, 12, 440], [0, 1, 0.5], [1230, -220, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "downTimeNode", 4, [-59, -60], [[6, 1, 1, 0, -58, [6], 7]], [0, "bbg816+ypMFa3yLMapQXb3", 1], [5, 258, 88], [1101.415, -42.909, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "bottom", 1, [-62], [[40, 44, 990.5, 1280, -61]], [0, "7d/WQwZ91MSYAV9JJqXdRu", 1], [5, 289.5, 158], [495.25, -281, 0, 0, 0, 0, 1, 1, 1, 1]], [34, "grade", false, 3, [-64, -65], [-63], [0, "3dCmP4jdVDJoX+X3a3uQr/", 1], [4, 4288859181], [5, 72, 89], [0, 1, 0.5], [555.27, -275.874, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "view", 7, [2], [[16, 0, -66, [25]], [19, 45, 240, 250, -67]], [0, "63oUaeBIdDCZLE8v8mGgLw", 1], [5, 1183, 439.3], [0, 0, 1]], [8, "orNode", 2, [-69], [[18, 40, -68]], [0, "254pc/MTFDBYc5Cpa4PZ08", 2], [5, 1183, 112.4], [0, 0, 1]], [3, "ortopNode", 2, [-71, -72], [[41, 40, 140, -70]], [0, "a2UMjr6GFMMqw+PtHoKh2X", 2], [5, 1183, 97], [0, 0, 1], [0, -112.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "orBottomNode", 2, [-74, -75], [[18, 40, -73]], [0, "93dYeP/VZH76NKi1921SLq", 2], [5, 1183, 134], [0, 0, 1], [0, -209.4, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "scrollBar", 0, 7, [-78], [[-76, [17, 0, 37, 350.07654921020657, 237, -77]], 1, 4], [0, "19DV36RYZM6qkQh/8fu/XX", 1], [5, 12, 439.3], [0, 1, 0.5], [1183, -219.65, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "nextBtn", 11, [[[44, 3, -79, [[45, "430021Ar2xBs4NsbkqYGehc", "startResult", 1]]], -80], 4, 1], [0, "f0UA2tLzxFrKpnQOCne9Ky", 1], [5, 176, 64], [9.828, 15.565, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "myRank", 6, [[2, "评级：", 28, 1, 1, -81, [16]], [5, 32, 144.05200000000002, -82]], [0, "e6nWutnkNBGZ4A514o/H6j", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-79.05200000000002, 0.304, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "myRank", 12, [[2, "评级：", 28, 1, 1, -83, [19]], [5, 32, 87.953, -84]], [0, "c03rf4iopNj7wIIsE2TrtO", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-87.953, 3.431, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "answertNode", 16, [[38, 1, 2, 32, -85, [5, 932, 0]]], [0, "efh3Pw9j5B1J8eqkDxDH0l", 2], [4, 4279505940], [5, 932, 0], [0, 0, 1], [204, -44.293, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "title", 1, [[2, "听后回答", 30, 1, 1, -86, [0]]], [0, "43EhzewKVC+okoNp515rET", 1], [4, 4284900450], [5, 120, 51], [0, 0, 0.5], [-591.935, 306.105, 0, 0, 0, 0, 1, 1, 1, 1]], [31, "questionDec", 8, [-87], [0, "9eZNzymp9H86weVcJdG5/g", 1], [4, 4279505940], [5, 1190, 46], [0, 0, 1]], [12, 32, 46, 3, 2, 23, [1]], [11, "bar", 0, 9, [-88], [0, "2eyg4jIrdEJrry9FzXUaZ2", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 1, 1, 0, 25, [3]], [22, 1, 9, 26], [23, false, 0.75, false, 0.23, null, 4, 8, 27], [4, "timeTitle", 10, [-89], [0, "7fBBjbW6tFTLPggxC4ggq1", 1], [4, 4284900450], [5, 96, 51], [0, 1, 0.5], [18.947, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "倒计时：", 24, 1, 1, 29, [4]], [4, "time", 10, [-90], [0, "99EqgSsdVMLqj8YkqohWhx", 1], [4, 4279505940], [5, 23, 51], [0, 0, 0.5], [19.962, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "- -", 24, 1, 1, 31, [5]], [32, "dec", 153, 4, [-91], [0, "dcbCA9bxhNpJunRFw8qDqB", 1], [4, 4278190080], [5, 330, 38], [0, 0, 0.5], [19.743, -39.515, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "现在，请在120秒内完成作答", 26, 30, 1, 1, 33, [8]], [42, 1, 18, [11]], [27, "resultTitle", 3, [[43, 1, -92, [12], 13]], [0, "abgU+JhotPEa9rXX8rtQ/h", 1], [5, 272, 34], [0, 291.755, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "mySound", 3, [[2, "我的录音：", 28, 1, 1, -93, [14]]], [0, "06/2xI/yZNiY83XbZgRBGi", 1], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [-451.102, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [33, "mySoundLost", false, 3, [-94], [0, "4aZxe6EAhA27GoSpwjHXJs", 1], [4, 4288913565], [5, 196, 51], [0, 0, 0.5], [-436.678, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "未生成我的录音", 28, 1, 1, 38, [15]], [1, "unit", 12, [[36, "分", 28, 30, 1, -95, [18]]], [0, "972J0vccdJlZP4PPiLfs0N", 1], [4, 4288913565], [5, 28, 38], [0, 0, 0.5], [8.808, 1.806, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "89", 64, 70, 1, 1, 12, [20]], [1, "soundLabel", 14, [[2, "原文音频：", 28, 1, 1, -96, [21]]], [0, "3cXI2hjXtKELeQTOThNOCd", 2], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [187.224, -61.312, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 15, [[14, "问题：", 28, 46, 1, -97, [22]]], [0, "18NTpn6B5Pn7ykjKoArjcq", 2], [4, 4284900966], [5, 84, 58], [0, 0, 1], [47.852, -14.556, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "startNewtLabel", 15, [-98], [0, "74GjwZdbNN0rwglTbx1gey", 2], [4, 4279505940], [5, 932, 46], [0, 0, 1], [204, -11.769, 0, 0, 0, 0, 1, 1, 1, 1]], [12, 32, 46, 3, 2, 44, [23]], [1, "soundLabel", 16, [[14, "参考答案：", 28, 46, 1, -99, [24]]], [0, "34xogbo21B2ImxY9I/jVkz", 2], [4, 4284900966], [5, 140, 58], [0, 1, 1], [188.03, -47.748, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "bar", 0, 17, [-100], [0, "6bgx9o0yFBorF3zEyBcKcI", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 1, 1, 0, 47, [26]], [22, 1, 17, 48], [23, false, 0.75, false, 0.23, null, 7, 2, 49]], 0, [0, 2, 1, 0, 0, 1, 0, 5, 6, 0, 6, 41, 0, 7, 14, 0, 8, 35, 0, 9, 39, 0, 10, 32, 0, 11, 3, 0, 12, 11, 0, 13, 4, 0, 14, 21, 0, 15, 45, 0, 16, 30, 0, 17, 34, 0, 18, 5, 0, 19, 24, 0, 0, 1, 0, -1, 22, 0, -2, 4, 0, -3, 11, 0, -4, 3, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 14, 0, -2, 15, 0, -3, 16, 0, 0, 3, 0, 0, 3, 0, -1, 36, 0, -2, 37, 0, -3, 38, 0, -4, 6, 0, -5, 12, 0, -6, 7, 0, 0, 4, 0, -2, 28, 0, -1, 5, 0, -2, 9, 0, -3, 10, 0, -4, 33, 0, 0, 5, 0, 0, 5, 0, -1, 8, 0, 0, 6, 0, 0, 6, 0, -1, 19, 0, 0, 7, 0, -2, 50, 0, -1, 13, 0, -2, 17, 0, 0, 8, 0, -1, 23, 0, -1, 27, 0, 0, 9, 0, -1, 25, 0, 0, 10, 0, -1, 29, 0, -2, 31, 0, 0, 11, 0, -1, 18, 0, -1, 41, 0, -1, 40, 0, -2, 20, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, -1, 42, 0, 0, 15, 0, -1, 43, 0, -2, 44, 0, 0, 16, 0, -1, 46, 0, -2, 21, 0, -1, 49, 0, 0, 17, 0, -1, 47, 0, 0, 18, 0, -2, 35, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 22, 0, -1, 24, 0, -1, 26, 0, -1, 30, 0, -1, 32, 0, -1, 34, 0, 0, 36, 0, 0, 37, 0, -1, 39, 0, 0, 40, 0, 0, 42, 0, 0, 43, 0, -1, 45, 0, 0, 46, 0, -1, 48, 0, 20, 1, 2, 21, 13, 27, 3, 28, 49, 3, 50, 100], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 35, 48], [-1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -2, 4, 1, 1, 1], [0, 0, 0, 0, 0, 0, 0, 4, 0, 0, 1, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 6, 0, 7, 2, 8, 9, 3, 2, 3]]