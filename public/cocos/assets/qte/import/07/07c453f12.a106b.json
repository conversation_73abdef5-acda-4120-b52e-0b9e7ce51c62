[1, ["ecpdLyjvZBwrvm+cedCcQy", "ectO3hTgdPsoUP9C1PG1eF", "f6TeSz8LRIvZ/pn9ExV5OM", "163tBl30NNypjq40T+8Yrp", "1bEh3mfPRP47uOhAl2vN+o", "b3fAAUqcVC8ojsP1Q2zUVF", "42G6K/nQ1EjouakW6DgXUw", "11+dVOjcxEz5j1Bf/OUovM", "52i+jTGS5HZ4NPg1w+hSIc", "3bAQGFj01E152N6suzjKRG", "41lUM4waBCv7Mwx2g0phnj", "9baRWoma1LKJRGM8eK/OWQ", "2dTjl2gR9H37oeMOdwaZIB", "01PTMoEOVCw5Av1i26EGh7", "77IqKZAUJC0YuYZFLWiRxv", "65SVQFOsBOTaKno9J1Xms7", "b7XoX0ZdFD1Zypes5Rbb+o", "25r59pJ1pKaZl+U4X10Pi5", "33SmVCOqZHsovutuDn2Siy", "1clRuMyMVFcp0zJisOwIN9", "b2mquWwOVGh54doF1JLsg7", "0crxPDIrBCvJjqShem+3Yd", "e5YTe0a1hM+oHGqC4CTyEB", "9e/HH4NkVEUpyXSdcrO93P", "ceD0JwxLREUJ+My3uEO2Ut", "52l9t3tB5H1K7DOWXhjTjO", "59GbgEosZDtpVpeloLhOw4", "293P5oBU9PlqhI9VColmQs"], ["_textureSetter", "root", "node", "_spriteFrame", "unitTagetSp", "btAddSp", "unitLabel", "_parent", "data", "addSp0", "addSp1", "zhuPre"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_prefab", "_contentSize", "_trs", "_children", "_components", "_parent", "_color"], 2, 4, 5, 7, 12, 9, 1, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color"], 2, 1, 2, 4, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["2893b/cLAhNcqSfiYE/3twh", ["node", "unitLabel", "btAddSp", "unitTagetSp", "unitSp", "addSp0", "addSp1", "zhuPre"], 3, 1, 1, 1, 1, 3, 6, 6, 6], ["cc.Label", ["_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3]], [[3, 0, 1, 2], [2, 0, 1, 2, 3, 4, 5, 2], [4, 0, 1, 1], [5, 0, 2], [1, 0, 4, 5, 1, 2, 3, 2], [1, 0, 6, 1, 7, 2, 3, 2], [2, 0, 1, 2, 3, 6, 4, 5, 2], [3, 1, 1], [4, 0, 1, 2, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7, 1], [7, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "ge", "rect": [0, 0, 26, 26], "offset": [0, 0], "originalSize": [26, 26], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [2]], [[{"name": "yi", "rect": [0, 0, 26, 26], "offset": [0, 0], "originalSize": [26, 26], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [3]], [[{"name": "baiwan", "rect": [0, 0, 52, 25], "offset": [0, 0], "originalSize": [52, 25], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [4]], [[{"name": "wan", "rect": [0, 0, 25, 24], "offset": [0, 0], "originalSize": [25, 24], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [5]], [[{"name": "shiwan", "rect": [0, 0, 52, 26], "offset": [0, 0], "originalSize": [52, 26], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [6]], [[{"name": "gan", "rect": [0, 0, 40, 411], "offset": [0, 0], "originalSize": [40, 411], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [7]], [[{"name": "bai", "rect": [0, 0, 26, 25], "offset": [0, 0], "originalSize": [26, 25], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [8]], [[{"name": "shi", "rect": [0, 0, 26, 26], "offset": [0, 0], "originalSize": [26, 26], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [9]], [[[3, "gan"], [4, "gan", [[-7, -8, [5, "touchNode", -10, [0, "09XeTC+6JE6rVP9RZCGFBj", -9], [4, 4278190335], [5, 63.3, 61], [-15.644, 243.773, 0, 0, 0, 0, 1, 1, 1, 1]], -11], 1, 1, 4, 1], [[8, -2, [3], 4], [9, -6, -5, -4, -3, [8, 9, 10, 11, 12, 13, 14, 15, 16, 17], 5, 6, 7]], [7, -1], [5, 40, 411], [18, -2, 0, 0, 0, 0, 1, 1, 1, 1]], [6, "unitLabel", 1, [-12], [0, "1berqrhIRD0odBRr7bOq8y", 1], [4, 4279907420], [5, 0, 38], [-9.314, -220.957, 0, 0, 0, 0, 1, 1, 1, 1]], [10, 22, 30, 1, 1, 2, [0]], [1, "btAdd", 1, [-13], [0, "614BRozfFPM4UjO0bp2Mep", 1], [5, 68, 68], [-16.429, 241.981, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 4, [1]], [1, "ge", 1, [-14], [0, "9bpZKDD9BJ8aBTZGJ81YOh", 1], [5, 26, 26], [-17.58, -239.661, 0, 0, 0, 0, 1, 1, 1, 1]], [2, 6, [2]]], 0, [0, 1, 1, 0, 2, 1, 0, 4, 7, 0, 5, 5, 0, 6, 3, 0, 2, 1, 0, -1, 2, 0, -2, 4, 0, 1, 1, 0, 7, 1, 0, -4, 6, 0, -1, 3, 0, -1, 5, 0, -1, 7, 0, 8, 1, 14], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [-1, -1, -1, -1, 3, 9, 10, 11, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, 3], [0, 0, 0, 0, 10, 11, 1, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 1]], [[{"name": "bt_add0", "rect": [0, 0, 68, 68], "offset": [0, 0], "originalSize": [68, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [23]], [[{"name": "q<PERSON><PERSON>", "rect": [0, 0, 52, 25], "offset": [0, 0], "originalSize": [52, 25], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [24]], [[{"name": "qian", "rect": [0, 0, 26, 25], "offset": [0, 0], "originalSize": [26, 25], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [25]], [[{"name": "shiyi", "rect": [0, 0, 53, 26], "offset": [0, 0], "originalSize": [53, 26], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [26]], [[{"name": "bt_add1", "rect": [0, 0, 68, 68], "offset": [0, 0], "originalSize": [68, 68], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [27]]]]