[1, ["ecpdLyjvZBwrvm+cedCcQy", "74Z6sSY3xFcJRy7TzHmXqI", "6bXKX48/dMf7zbXAe1GIsn", "d559ZysGRMPoKFAAtTHiGT"], ["node", "_spriteFrame", "H_prefab", "P_prefab", "root", "label", "content", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs"], 2, 9, 4, 5, 1, 2, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_color", "_contentSize", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.Sprite", ["node", "_materials", "_spriteFrame"], 3, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_styleFlags", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingLeft", "node", "_layoutSize"], 0, 1, 5], ["1cb76MO3a1DOJ83+GY++EkD", ["node", "content", "label", "H_prefab", "P_prefab"], 3, 1, 1, 1, 6, 6]], [[1, 0, 1, 2], [2, 0, 2], [0, 0, 5, 1, 2, 3, 2], [0, 0, 4, 1, 2, 3, 2], [0, 0, 4, 1, 2, 3, 6, 2], [3, 0, 1, 2, 3, 4, 5, 6, 7, 3], [4, 0, 1, 2, 1], [1, 1, 1], [5, 0, 1, 2, 3, 4, 5, 6, 6], [6, 0, 1, 2, 3, 4, 4], [7, 0, 1, 2, 3, 4, 1]], [[1, "Edit"], [2, "Edit", [-5, -6, -7], [[10, -4, -3, -2, 3, 4]], [7, -1], [5, 1280, 720]], [4, "group", 1, [[9, 1, 1, -100, -8, [5, 968, 0]]], [0, "d42LcGJn1N668Hm8DHlDhI", 1], [5, 968, 0], [0, -85.432, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "bg", 1, [[6, -9, [0], 1]], [0, "2a8NObiAFNG5hHzSXxbEon", 1], [5, 1280, 720]], [5, "timu", 512, 1, [-10], [0, "bccbj2hGJLOZkvYvyzQbXc", 1], [4, 4280905377], [5, 340, 51], [0, 260.228, 0, 0, 0, 0, 1, 1, 1, 1]], [8, "请写出下列文字的拼音", 34, 1, 1, 1, 4, [2]]], 0, [0, 4, 1, 0, 5, 5, 0, 6, 2, 0, 0, 1, 0, -1, 3, 0, -2, 4, 0, -3, 2, 0, 0, 2, 0, 0, 3, 0, -1, 5, 0, 7, 1, 10], [0, 0, 0, 0, 0], [-1, 1, -1, 2, 3], [0, 1, 0, 2, 3]]