[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "9clvGEl7NOo4klGVulmdez", "e3ZBa/LqBBH5RbT30CpntR", "d9TvLZ8u1GEr7fjB1DuLHv", "a7tsGmh39O7bGIX7uEJ2xN"], ["node", "_spriteFrame", "_N$skeletonData", "root", "_N$barSprite", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs"], 2, 9, 4, 5, 1, 2, 7], ["cc.Sprite", ["_sizeMode", "_type", "_fillType", "_fillStart", "_fillRange", "node", "_materials", "_spriteFrame", "_fillCenter"], -2, 1, 3, 6, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "_playTimes", "node", "_materials", "_N$skeletonData"], -2, 1, 3, 6], ["cc.Label", ["_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], 0, 1, 3], ["cc.<PERSON><PERSON>", ["node"], 3, 1], ["0e3ba/AQZFGMbEyI7l5xWj8", ["node"], 3, 1]], [[2, 0, 1, 2], [0, 0, 4, 1, 2, 3, 6, 2], [6, 0, 1, 2, 3, 4, 5, 6, 7, 6], [3, 0, 2], [0, 0, 5, 1, 2, 3, 2], [0, 0, 4, 5, 1, 2, 3, 2], [0, 0, 4, 1, 2, 3, 2], [4, 0, 1, 2, 3, 4, 2], [1, 0, 5, 6, 7, 2], [1, 1, 0, 2, 3, 4, 5, 6, 8, 6], [1, 5, 6, 7, 1], [2, 1, 1], [5, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 4], [8, 0, 1], [9, 0, 1]], [[3, "entry"], [4, "entry", [-3], [[15, -2]], [11, -1], [5, 600, 120]], [5, "da<PERSON><PERSON><PERSON>", 1, [-5, -6, -7, -8, -9, -10], [[14, -4]], [0, "e7uNQbakxDA4KzWJDLMKGO", 1], [5, 115, 115]], [7, "progress", 2, [[-11, [12, 2, -13, -12]], 1, 4], [0, "caMgVcwDtIYJ++pEg82MV8", 1], [5, 115, 115]], [6, "back", 2, [[8, 0, -14, [0], 1]], [0, "43MgSDQ1tJqZi1BaOGL2ky", 1], [5, 115, 115]], [9, 3, 0, 2, 0.25, 1, 3, [2], [0, 0.5, 0.5]], [1, "res", 2, [[10, -15, [3], 4]], [0, "80jq109t5HiqbG/SsU7o0c", 1], [5, 49, 64], [0, -5.289, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [1, "spine1", 2, [[2, "default", 0, false, "animation", 1, -16, [5], 6]], [0, "5foy8vj3lGT4GnAzrRcEbN", 1], [5, 202, 120], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "spine2", 2, [[2, "default", 0, false, "animation", 1, -17, [7], 8]], [0, "d6YvHXVbRMa7lJYA2XdPJ8", 1], [5, 202, 120], [-200, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [1, "lab_tips", 2, [[13, 25, 1, 1, -18, [9]]], [0, "58OFwdZDBHHbpxy7CYApWs", 1], [5, 0, 51], [0, -101.173, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, -1, 4, 0, -2, 3, 0, -3, 6, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, -1, 5, 0, 4, 5, 0, 0, 3, 0, 0, 4, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 5, 1, 18], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [-1, 1, -1, -1, 1, -1, 2, -1, 2, -1, 1], [0, 3, 0, 0, 4, 1, 2, 1, 2, 0, 5]]