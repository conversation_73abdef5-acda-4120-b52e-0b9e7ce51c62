[1, ["ecpdLyjvZBwrvm+cedCcQy", "9f1SIyrCFCkpiWB8rryzaK", "27GJ/2YRtNBJCI6f7rmarW", "dduhNLD5hNSZ9bUh3oXoNZ", "543c+jfJhP/Z1HgER/UrOD", "52O+ANZQNNZ4FsMu3MMsI4", "3fO1LydF9FyI8UMaihHYPl"], ["node", "_spriteFrame", "_defaultClip", "root", "_N$barSprite", "data"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_color", "_anchorPoint"], 1, 9, 4, 5, 1, 7, 2, 5, 5], ["cc.Sprite", ["_srcBlendFactor", "_type", "_fillType", "_fillStart", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame", "_fillCenter"], -3, 1, 3, 6, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["cc.ProgressBar", ["_N$mode", "_N$progress", "_N$reverse", "node", "_N$barSprite"], 0, 1, 1], ["cc.Animation", ["playOnLoad", "node", "_clips", "_defaultClip"], 2, 1, 3, 6], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3]], [[2, 0, 1, 2], [7, 0, 1, 2, 3, 4, 5, 5], [0, 0, 5, 2, 3, 8, 4, 6, 2], [1, 0, 6, 7, 8, 2], [1, 0, 4, 5, 6, 7, 8, 4], [3, 0, 2], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 7, 2, 3, 4, 6, 2], [0, 0, 5, 2, 3, 4, 6, 2], [0, 0, 1, 5, 2, 3, 4, 6, 3], [0, 0, 5, 2, 3, 8, 4, 9, 6, 2], [4, 0, 1, 2, 3, 4, 2], [1, 0, 1, 2, 3, 6, 7, 9, 5], [2, 1, 1], [5, 0, 1, 2, 3, 4, 4], [6, 0, 1, 2, 3, 2]], [[5, "voice_5"], [6, "voice_5", [-3, -4, -5, -6, -7, -8], [[3, 1, -2, [12], 13]], [13, -1], [5, 258, 88]], [7, "pbBar", 1, [-12], [[14, 2, 0, true, -10, -9], [3, 1, -11, [1], 2]], [0, "acADGDrq5Pl5aEw/DawhgC", 1], [5, 46, 46], [-81.52, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "playing", false, 1, [[4, 1, 2, false, -13, [5], 6], [15, true, -14, [8], 7]], [0, "59T7KtCD1ER7Sxod7/pAJb", 1], [5, 28, 21], [-81.479, 1.02, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "pb", 2, [-15], [0, "9cfaE6fKZELLhcnfZimKPC", 1], [5, 48, 48]], [12, 1, 3, 2, 0.25, 4, [0], [0, 0.5, 0.5]], [8, "sp", 1, [[4, 1, 2, false, -16, [3], 4]], [0, "a9ogdfGEVBzopz/nI+0W0Q", 1], [5, 32, 32], [-81.18, 0.046, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "maxTime", 1, [[1, "00:00", 24, 1, 1, -17, [9]]], [0, "c0CZOqhGhCKrNmaShnN04T", 1], [4, 4288913565], [5, 61, 51], [68.913, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "time", 1, [[1, "00:00", 24, 1, 1, -18, [10]]], [0, "1ePO5LSGVOU78GyhYpZwV2", 1], [4, 4284900450], [5, 61, 51], [-16.999, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "xian", 1, [[1, "/", 24, 1, 1, -19, [11]]], [0, "2d9K3Pi2lPqYo1PZUib8n7", 1], [4, 4288913565], [5, 7, 51], [0, 0, 0.5], [23.204, 0, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -2, 6, 0, -3, 3, 0, -4, 7, 0, -5, 8, 0, -6, 9, 0, 4, 5, 0, 0, 2, 0, 0, 2, 0, -1, 4, 0, 0, 3, 0, 0, 3, 0, -1, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 0, 9, 0, 5, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5], [-1, -1, 1, -1, 1, -1, 1, 2, -1, -1, -1, -1, -1, 1, 1], [0, 0, 2, 0, 3, 0, 4, 1, 1, 0, 0, 0, 0, 5, 6]]