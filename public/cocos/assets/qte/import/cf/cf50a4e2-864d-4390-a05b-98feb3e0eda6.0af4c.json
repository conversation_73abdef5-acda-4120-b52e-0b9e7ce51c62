[1, 0, 0, [["cc.EffectAsset", ["_name", "shaders", "techniques"], 0]], [[0, 0, 1, 2, 4]], [[0, "svg_graph", [{"hash": 754078048, "record": null, "name": "svg_graph|vs|fs", "glsl3": {"vert": "\nprecision highp float;\nuniform CCGlobal {\n  mat4 cc_matView;\n  mat4 cc_matViewInv;\n  mat4 cc_matProj;\n  mat4 cc_matProjInv;\n  mat4 cc_matViewProj;\n  mat4 cc_matViewProjInv;\n  vec4 cc_cameraPos;\n  vec4 cc_time;\n  mediump vec4 cc_screenSize;\n  mediump vec4 cc_screenScale;\n};\nuniform CCLocal {\n  mat4 cc_matWorld;\n  mat4 cc_matWorldIT;\n};\nin vec3 a_position;\nin vec2 a_uv0;\nin vec4 a_color;\nout vec4 v_color;\nout vec3 v_position;\nout vec2 v_uv0;\nin float a_dist;\nout float v_dist;\nuniform FragConstants {\n  bool isFlipX;\n  bool isFlipY;\n  vec2 oPosition;\n  float alphaU;\n};\nout float v_alpha;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  if(isFlipX){\n    pos.x = oPosition.x - pos.x + oPosition.x;\n  }\n  if(isFlipY){\n    pos.y = oPosition.y - pos.y + oPosition.y;\n  }\n  v_position = a_position;\n  v_alpha = alphaU;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n  uniform ALPHA_TEST {\n    float alphaThreshold;\n  };\n#endif\nin vec4 v_color;\nin vec2 v_uv0;\nuniform sampler2D texture;\nin float v_dist;\nin vec3 v_position;\nin float v_alpha;\nvoid main () {\n  vec4 o = v_color;\n  #if CC_SUPPORT_standard_derivatives\n    float aa = fwidth(v_dist);\n  #else\n    float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  o *= v_alpha;\n  gl_FragColor = o;\n}"}, "glsl1": {"vert": "\nprecision highp float;\nuniform mat4 cc_matViewProj;\nuniform mat4 cc_matWorld;\nattribute vec3 a_position;\nattribute vec2 a_uv0;\nattribute vec4 a_color;\nvarying vec4 v_color;\nvarying vec3 v_position;\nvarying vec2 v_uv0;\nattribute float a_dist;\nvarying float v_dist;\nuniform bool isFlipX;\nuniform bool isFlipY;\nuniform vec2 oPosition;\nuniform float alphaU;\nvarying float v_alpha;\nvoid main () {\n  vec4 pos = vec4(a_position, 1);\n  pos = cc_matViewProj * cc_matWorld * pos;\n  v_color = a_color;\n  v_dist = a_dist;\n  if(isFlipX){\n    pos.x = oPosition.x - pos.x + oPosition.x;\n  }\n  if(isFlipY){\n    pos.y = oPosition.y - pos.y + oPosition.y;\n  }\n  v_position = a_position;\n  v_alpha = alphaU;\n  gl_Position = pos;\n}", "frag": "\n#if CC_SUPPORT_standard_derivatives\n  #extension GL_OES_standard_derivatives : enable\n#endif\nprecision highp float;\n#if USE_ALPHA_TEST\n#endif\nvarying vec4 v_color;\nvarying vec2 v_uv0;\nuniform sampler2D texture;\nvarying float v_dist;\nvarying vec3 v_position;\nvarying float v_alpha;\nvoid main () {\n  vec4 o = v_color;\n  #if CC_SUPPORT_standard_derivatives\n    float aa = fwidth(v_dist);\n  #else\n    float aa = 0.05;\n  #endif\n  float alpha = 1. - smoothstep(-aa, 0., abs(v_dist) - 1.0);\n  o.rgb *= o.a;\n  o *= alpha;\n  o *= v_alpha;\n  gl_FragColor = o;\n}"}, "builtins": {"globals": {"blocks": [{"name": "CCGlobal", "defines": []}], "samplers": []}, "locals": {"blocks": [{"name": "CCLocal", "defines": []}], "samplers": []}}, "defines": [{"name": "CC_SUPPORT_standard_derivatives", "type": "boolean", "defines": []}, {"name": "USE_ALPHA_TEST", "type": "boolean", "defines": []}], "blocks": [{"name": "FragConstants", "binding": 0, "members": [{"name": "isFlipX", "type": 1, "count": 1}, {"name": "isFlipY", "type": 1, "count": 1}, {"name": "oPosition", "type": 14, "count": 1}, {"name": "alphaU", "type": 13, "count": 1}], "defines": []}, {"name": "ALPHA_TEST", "binding": 1, "members": [{"name": "alphaThreshold", "type": 13, "count": 1}], "defines": ["USE_ALPHA_TEST"]}], "samplers": [{"name": "texture", "type": 29, "count": 1, "binding": 30, "defines": []}]}], [{"passes": [{"program": "svg_graph|vs|fs", "blendState": {"targets": [{"blend": true, "blendSrc": 1, "blendDst": 771, "blendSrcAlpha": 1, "blendDstAlpha": 771}]}, "rasterizerState": {"cullMode": 0}, "properties": {"alphaThreshold": {"type": 13, "value": [0.5]}}}]}]]], 0, 0, [], [], []]