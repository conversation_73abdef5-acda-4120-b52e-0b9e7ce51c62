[1, ["ecpdLyjvZBwrvm+cedCcQy", "bemf7/h2JOdKJgWGu03FhG", "4c+x373XFFP4SWdKUK8kof", "16k42K1/pAraHYI/1VAmrT", "46qxVhue1I2oMqpAMcgzrR", "6aj5gvcSBA4atKUGFrmN4B", "6dldbcAqVBi4Ts4XzWHNES", "9dFvKMLitEV7Amj4Hbzdff", "77vnhEtHFNoKro49n41NKY", "6cErbUD41Jla5FqOy0nDAe", "39PTJxnmZPpodlbXWLuhSH"], ["node", "_textureSetter", "_spriteFrame", "root", "nodClockBg", "nod<PERSON>inutePointer", "nod<PERSON>ourPointer", "data"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint", "_color", "_eulerAngles"], 1, 9, 4, 5, 1, 7, 2, 5, 5, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Label", ["_string", "_N$horizontalAlign", "_N$verticalAlign", "_N$overflow", "_fontSize", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["926c0JYHJdHhJWBpAQkH4cv", ["node", "nod<PERSON>ourPointer", "nod<PERSON>inutePointer", "nodClockBg"], 3, 1, 1, 1, 1]], [[2, 0, 1, 2], [3, 2, 3, 4, 1], [0, 0, 5, 2, 3, 4, 2], [3, 0, 1, 2, 3, 4, 3], [5, 0, 2], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 7, 2, 3, 4, 2], [0, 0, 5, 2, 3, 4, 8, 2], [0, 0, 5, 2, 3, 4, 8, 6, 10, 2], [0, 0, 1, 5, 2, 3, 9, 4, 6, 3], [0, 0, 5, 2, 3, 9, 4, 6, 2], [6, 0, 1, 2, 3, 1], [2, 1, 1], [4, 0, 1, 2, 3, 5, 6, 5], [4, 0, 4, 1, 2, 5, 6, 5]], [[[{"name": "clockOuter", "rect": [0, 0, 480, 480], "offset": [0, 0], "originalSize": [480, 480], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [1]], [[{"name": "clockHourPointer", "rect": [0, 0, 125, 24], "offset": [0, 0], "originalSize": [125, 24], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [2]], [[[4, "clockRoot"], [5, "clockRoot", [-6, -7, -8, -9], [[11, -5, -4, -3, -2]], [12, -1], [5, 500, 500]], [6, "clockBg", 1, [-11, -12, -13], [[1, -10, [8], 9]], [0, "6fQFCpH4FAA4/DlgaEurWu", 1], [5, 430, 430]], [7, "clockHourPointer", 2, [[3, 2, false, -14, [2], 3]], [0, "cfFb4xoQVOR5ubYMXQRFn4", 1], [5, 125, 24], [0, 0, 0.5]], [8, "clockMinutePointer", 2, [[3, 2, false, -15, [4], 5]], [0, "45RcMJy5NDtaLX9J3/eMyu", 1], [5, 158, 24], [0, 0, 0.5], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, 1, 1, 1], [1, 0, 0, 90]], [2, "clockBgFrame", 1, [[1, -16, [0], 1]], [0, "6d/j9wzQVNo79reezru3cA", 1], [5, 480, 480]], [2, "clockCenter", 2, [[1, -17, [6], 7]], [0, "53tFK8Yw1AjpGGV1bRL7Cx", 1], [5, 40, 40]], [9, "label", false, 1, [[13, "test", 1, 1, 3, -18, [10]]], [0, "1algwbeAxF96MVZLuAUIog", 1], [4, 4278190080], [5, 400, 51], [7.554, 55.399, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "labelTIme", 1, [[14, "  03:00  ", 60, 1, 1, -19, [11]]], [0, "11DTuPL19DMpmIEnqzUOl/", 1], [4, 4278255360], [5, 217, 51], [-4.704, 275.179, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 4, 2, 0, 5, 4, 0, 6, 3, 0, 0, 1, 0, -1, 5, 0, -2, 2, 0, -3, 7, 0, -4, 8, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 6, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 8, 0, 7, 1, 19], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, -1, 2, -1, 2, -1, 2, -1, -1], [0, 3, 0, 4, 0, 5, 0, 6, 0, 7, 0, 0]], [[{"name": "clockMinutePointer", "rect": [0, 6, 158, 12], "offset": [0, 0], "originalSize": [158, 24], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [8]], [[{"name": "clockCenter", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [9]], [[{"name": "clockInner", "rect": [0, 0, 430, 430], "offset": [0, 0], "originalSize": [430, 430], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [10]]]]