[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "README", "# [editbox]富文本框\n### *canEdit: true*\n## 功能列表\n\n# 环境依赖\n* cocosCreator 2.4版本已上版本\n\n# 组件功能介绍\n\n* 支持：      \n    1.文本输入\n    2.选中文本，进行 删除，修改字号、行高、颜色、倾斜、加粗、下划线的编辑，修改输入框的宽高，控制文本对齐方式\n    3.供题板 进行文本展示，进行 对应文字染色\n# 目录结构描述\n\n├── EditBoxData .ts           // 数据配置文件\n\n├── EditBoxTool .ts           // 主要逻辑文件\n\n├── SimpleLabelActor.ts       // 单个文字逻辑文件\n\n├── TypeNode.ts               // 重写node组件文件             为了满足node有type属性\n\n\n# 创建方式\n\nEditBoxTool.ts  \n\n\t/**\n     * 生成可编辑 输入框\n     */\n    public createEditBox ():void {};\n\n    /**\n    * 根据字符串直接生成可编辑输入框\n    * @param {string}string 富文本格式字符串\n    * @warn  调用此方法之前，要先调用 setEditBoxHorizontalStatue();设置文本的对齐方式，默认为左对齐\n    * @warn  调用此方法之前，要先调用 setNodeSize(),设置node的尺寸\n    */\n    public createEditBoxWithString (string: string):void {}\n\n\n# 主要操作属性\n* EditBoxTool.ts  \n    ```\n\t//设置加粗\n    public setBoldEnabled (data:boolean): boolean {}\n    //设置倾斜\n    public setItalicEnabled (data:boolean): boolean {}\n\t//设置下划线\n    public setUnderLine (data:boolean): boolean {}\n\t//设置字号\n    public setFontSize (data:number): number {}\n\t//设置行高\n    public setLineHeight (data:number): number {}\n\t//设置色号\n    public setFontColor (data:string): string {}\n    ```"]], 0, 0, [], [], []]