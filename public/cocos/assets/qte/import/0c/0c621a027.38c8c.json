[1, ["ecpdLyjvZBwrvm+cedCcQy", "eesgdixshOUa8lxmFO+rUb", "20NTKzzBZH07AoPrmIqA6x", "23/mivwmdEqI4KRdC6bX/J", "bfi/LenaVMLJcWqGjZq+UR", "c2Dphrr71J05V8Um/299+k", "0ftqmKpspDupdkiVU1hvbM", "27GrQn2FZFh67S/etxswqB", "ca+DSlxNdAYpDA6PG9CCmd", "83mkI4D3dNVqjaFULqfF0Z", "755PeldrtFtKEAdMbJaMib", "58Q10xwe9HlaKTJhfGWdkn", "c2FrCVcJdEu79Tj92ACuUG", "55FtPkkFJLh50tXOC9SyyI"], ["node", "_spriteFrame", "root", "_scrollView", "_parent", "_textureSetter", "resultAnswer", "lostNode", "gradeLabel", "orNode", "subMBt", "soundLostLabel", "time<PERSON><PERSON><PERSON>", "result", "bottom", "anwersNode", "questionNode", "resultAnswerTipsLabel", "resultTipsLabel", "timeTitleLabel", "tipsDecLabel", "tipsStartLabel", "qViewNode", "questionLabelNode", "titleLabel", "data", "dingAudio"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_components", "_parent", "_children", "_anchorPoint", "_trs", "_color"], 1, 4, 5, 9, 1, 2, 5, 7, 5], ["cc.Label", ["_fontSize", "_string", "_lineHeight", "_N$verticalAlign", "_N$cacheMode", "_N$overflow", "_N$horizontalAlign", "_styleFlags", "node", "_materials"], -5, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_right", "_left", "_top", "_bottom", "alignMode", "node"], -5, 1], ["cc.Node", ["_name", "_active", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color", "_children"], 0, 1, 2, 4, 5, 5, 7, 5, 2], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 1, 1, 12, 4, 5, 7, 2, 5], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingBottom", "_N$paddingTop", "_N$spacingY", "node", "_layoutSize"], -2, 1, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["3c978sT189J34bFWznNV0OJ", ["node", "titleLabel", "questionLabelNode", "qViewNode", "tipsStartLabel", "tipsDecLabel", "timeTitleLabel", "resultTipsLabel", "resultAnswerTipsLabel", "questionNode", "anwersNode", "bottom", "result", "time<PERSON><PERSON><PERSON>", "soundLostLabel", "subMSp", "subMBt", "orNode", "gradeLabel", "lostNode", "resultAnswer", "dingAudio"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1, 1, 1, 1, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -2, 1, 1, 1]], [[8, 0, 1, 2], [0, 0, 5, 4, 2, 9, 3, 7, 8, 2], [1, 1, 0, 6, 3, 8, 9, 5], [0, 0, 5, 6, 4, 2, 3, 7, 8, 2], [4, 3, 4, 5, 1], [3, 0, 3, 4, 5, 9, 6, 7, 8, 2], [2, 0, 3, 8, 3], [4, 0, 1, 2, 3, 4, 5, 4], [2, 0, 8, 2], [0, 0, 5, 6, 4, 2, 3, 7, 2], [0, 0, 5, 4, 2, 3, 8, 2], [5, 0, 2, 7, 3, 4, 5, 8, 6, 2], [5, 0, 1, 2, 7, 3, 4, 5, 8, 6, 3], [3, 0, 2, 3, 4, 5, 6, 7, 8, 3], [4, 0, 1, 2, 3, 4, 4], [2, 0, 1, 2, 8, 4], [2, 7, 0, 4, 2, 8, 5], [12, 0, 1, 2, 2], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3, 4, 5, 6, 7, 6], [0, 0, 6, 4, 2, 3, 7, 2], [6, 0, 1, 2, 5, 6, 4], [1, 1, 0, 2, 6, 3, 8, 9, 6], [0, 0, 1, 5, 6, 4, 2, 3, 3], [0, 0, 1, 5, 6, 4, 2, 9, 3, 8, 3], [0, 0, 5, 6, 4, 2, 3, 8, 2], [2, 0, 1, 8, 3], [2, 0, 4, 1, 8, 4], [6, 0, 1, 3, 2, 4, 5, 6, 6], [11, 0, 1], [1, 1, 0, 7, 6, 3, 8, 9, 6], [1, 1, 0, 2, 3, 5, 8, 9, 6], [1, 1, 0, 2, 3, 8, 9, 5], [1, 1, 0, 4, 8, 9, 4], [1, 1, 0, 6, 4, 8, 9, 5], [1, 0, 2, 7, 5, 4, 8, 9, 6], [9, 0, 2], [0, 0, 6, 4, 2, 3, 2], [0, 0, 5, 2, 3, 7, 8, 2], [0, 0, 5, 6, 2, 3, 7, 2], [0, 0, 5, 6, 4, 2, 9, 3, 7, 8, 2], [0, 0, 5, 4, 2, 3, 7, 8, 2], [0, 0, 1, 5, 4, 2, 9, 3, 7, 8, 3], [5, 0, 2, 3, 4, 5, 6, 2], [3, 0, 1, 3, 10, 4, 5, 9, 6, 7, 8, 3], [3, 0, 2, 3, 4, 5, 9, 6, 7, 8, 3], [3, 0, 1, 3, 4, 5, 9, 6, 7, 8, 3], [4, 0, 3, 4, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 1], [8, 1, 1], [2, 0, 3, 1, 2, 8, 5], [2, 0, 5, 6, 1, 2, 8, 6], [2, 0, 4, 3, 5, 6, 1, 2, 8, 8], [6, 0, 1, 3, 5, 6, 4], [1, 0, 2, 3, 5, 4, 8, 9, 6], [1, 0, 2, 5, 4, 8, 9, 5], [1, 1, 0, 2, 5, 4, 8, 9, 6], [13, 0, 1, 2, 2], [14, 0, 1, 2, 3]], [[[[36, "ListenRetellComponent"], [37, "template", [-23, -24, -25, -26, -27, -28], [[4, -2, [63], 64], [48, -22, -21, -20, -19, -18, -17, -16, -15, -14, -13, -12, -11, -10, -9, -8, [65, 66], -7, -6, -5, -4, -3, 67]], [49, -1], [5, 1280, 720]], [20, "content", [-32, -33, -34, -35], [[15, 40, 220, 250, -30], [21, 1, 2, 32, -31, [5, 1183, 455.4]]], [0, "0b1dZ5CuhBdanIvFbADEg6", -29], [5, 1183, 455.4], [0, 0, 1]], [20, "content", [-39, -40, -41], [[15, 40, 220, 250, -37], [21, 1, 2, 32, -38, [5, 1183, 375.4]]], [0, "0b1dZ5CuhBdanIvFbADEg6", -36], [5, 1183, 375.4], [0, 0, 1]], [20, "content", [-45, -46], [[50, 40, 6, 220, 250, -43], [21, 1, 2, 16, -44, [5, 274, 164.1]]], [0, "c3OMbBlMNHSakoGufrcka0", -42], [5, 274, 164.1], [0, 0, 1]], [23, "result", false, 1, [-49, -50, -51, -52, -53, -54], [[29, -47], [4, -48, [40], 41]], [0, "18pcngTFRKjZWIE/gF00gC", 1], [5, 1280, 720]], [23, "resultAnswer", false, 1, [-57, -58, -59, -60, -61, -62], [[29, -55], [4, -56, [61], 62]], [0, "7dGpIUtvJDJp8zs2A0gow2", 1], [5, 1280, 720]], [11, "anwersNode", 1, [-65, -66, -67, -68], [[[7, 1, 1, 0, -63, [19], 20], -64], 4, 1], [0, "818TIZ9gpNzLK6O9yykyTJ", 1], [5, 280, 439], [0, -0.004049525850767715, 1], [336, 261, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "questionNode", 1, [-71, -72, -73], [[[7, 1, 1, 0, -69, [4], 5], -70], 4, 1], [0, "558fcRXRFAjaH0LiJKOa1y", 1], [5, 931, 440], [0, 0, 1], [-613, 262, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "view", 8, [-76], [[17, 0, -74, [1]], [51, 45, 78.00000000000001, -2.3092638912203256e-14, 240, 250, -75]], [0, "53+Wl6sRNCIYyUxujpAJYA", 1], [5, 931, 362], [0, 0, 1], [0, -78, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lostRank", false, 5, [-79], [[30, "评级丢失了", 26, 1, 1, 1, -77, [27]], [6, 32, 49.18399999999997, -78]], [0, "79k+qw5wVDfpBw4ji0mszV", 1], [4, 4288913565], [5, 130, 51], [525.816, -273.516, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "list", 5, [-82, -83], [[[7, 1, 1, 0, -80, [38], 39], -81], 4, 1], [0, "c7g8qtguFCMIcMW2ovTeIa", 1], [5, 1183, 439.3], [0, 0, 1], [-591, 240, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "list", 6, [-86, -87], [[[7, 1, 1, 0, -84, [59], 60], -85], 4, 1], [0, "48eFA7/3VFOqjh5VepscdP", 1], [5, 1183, 439.3], [0, 0, 1], [-591, 240, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "questionStem", 2, [-90, -91], [[8, 40, -88], [28, 1, 2, 20, 20, -40, -89, [5, 1183, 91]]], [0, "faUl/czdVFRKPzWof+ZWjK", 2], [5, 1183, 91], [0, 0, 1]], [3, "ortopNode", 2, [-94, -95], [[26, 40, 140, -92], [28, 1, 2, 20, 20, -40, -93, [5, 1183, 86]]], [0, "a2UMjr6GFMMqw+PtHoKh2X", 2], [5, 1183, 86], [0, 0, 1], [0, -203.4, 0, 0, 0, 0, 1, 1, 1, 1]], [38, "content", 9, [0, "0b1dZ5CuhBdanIvFbADEg6", -96], [5, 886, 250], [0, 0, 1], [24, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "scrollBar", 0, 8, [-99], [[-97, [16, 0, 37, 350.07654921020657, 237, -98]], 1, 4], [0, "ccjp9cBvlOCaa3z4FcHM2l", 1], [5, 12, 440], [0, 1, 0.5], [931, -220, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "view", 7, [4], [[17, 0, -100, [11]], [52, 45, -0.15816439331956644, 0.15816439331956644, 97, 2.5757174171303632e-14, 240, 250, -101]], [0, "a79wO8+/5D1L8g4mIPS2Oj", 1], [5, 280, 342], [0, 0, 1], [0.9757028448953937, -97, 0, 0, 0, 0, 1, 1, 1, 1]], [39, "tips", 4, [-102, -103, -104], [0, "72QN3pd6VNIJX0lykbQBad", 4], [5, 276, 116.1], [0, 0, 1]], [12, "scrollBar", 0, 7, [-107], [[-105, [16, 0, 37, 350.07654921020657, 237, -106]], 1, 4], [0, "bcNfaroD5Ip7jKqtzVXk+g", 1], [5, 12, 439], [0, 1, 0.5], [281.133867238215, -219.5, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "downTimeNode", 7, [-109, -110], [[7, 1, 1, 0, -108, [15], 16]], [0, "a9+Ka5nnlMtqIuwO9dKSWY", 1], [5, 258, 88], [138.085, -46.623, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "bottom", 1, [-112], [[27, 44, 990.5, 1280, -111]], [0, "7d/WQwZ91MSYAV9JJqXdRu", 1], [5, 289.5, 158], [495.25, -281, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "grade", false, 5, [-114, -115], [-113], [0, "3dCmP4jdVDJoX+X3a3uQr/", 1], [4, 4288859181], [5, 72, 89], [0, 1, 0.5], [555.27, -275.874, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 11, [3], [[17, 0, -116, [36]], [15, 45, 240, 250, -117]], [0, "63oUaeBIdDCZLE8v8mGgLw", 1], [5, 1183, 439.3], [0, 0, 1]], [9, "orNode", 3, [-119], [[8, 40, -118]], [0, "254pc/MTFDBYc5Cpa4PZ08", 3], [5, 1183, 112.4], [0, 0, 1]], [3, "ortopNode", 3, [-121, -122], [[26, 40, 140, -120]], [0, "a2UMjr6GFMMqw+PtHoKh2X", 3], [5, 1183, 97], [0, 0, 1], [0, -112.4, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "orBottomNode", 3, [-124, -125], [[8, 40, -123]], [0, "93dYeP/VZH76NKi1921SLq", 3], [5, 1183, 134], [0, 0, 1], [0, -209.4, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "scrollBar", 0, 11, [-128], [[-126, [16, 0, 37, 350.07654921020657, 237, -127]], 1, 4], [0, "19DV36RYZM6qkQh/8fu/XX", 1], [5, 12, 439.3], [0, 1, 0.5], [1183, -219.65, 0, 0, 0, 0, 1, 1, 1, 1]], [24, "lostRank", false, 6, [-131], [[30, "评级丢失了", 26, 1, 1, 1, -129, [47]], [6, 32, 49.18399999999997, -130]], [0, "f8GpvcTspLraidZ1RpR9uq", 1], [4, 4288913565], [5, 130, 51], [525.816, -273.516, 0, 0, 0, 0, 1, 1, 1, 1]], [40, "grade", 6, [-133, -134], [[22, "89", 64, 70, 1, 1, -132, [50]]], [0, "cd9lz6VnBHqoWfK9shqz87", 1], [4, 4288859181], [5, 72, 89], [0, 1, 0.5], [555.27, -275.874, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "view", 12, [2], [[17, 0, -135, [57]], [15, 45, 240, 250, -136]], [0, "35jIiQH9hGOpnClmZIahY6", 1], [5, 1183, 439.3], [0, 0, 1]], [3, "orBottomNode", 2, [-138, -139], [[8, 40, -137]], [0, "93dYeP/VZH76NKi1921SLq", 2], [5, 1183, 134], [0, 0, 1], [0, -289.4, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "scrollBar", 0, 12, [-142], [[-140, [16, 0, 37, 350.07654921020657, 237, -141]], 1, 4], [0, "aePiJAgGZDeKAKSUk2bS96", 1], [5, 12, 439.3], [0, 1, 0.5], [1183, -219.65, 0, 0, 0, 0, 1, 1, 1, 1]], [43, "nextBtn", 21, [[[57, 3, -143, [[58, "3c978sT189J34bFWznNV0OJ", "startResult", 1]]], -144], 4, 1], [0, "f0UA2tLzxFrKpnQOCne9Ky", 1], [5, 176, 64], [9.828, 15.565, 0, 0, 0, 0, 1, 1, 1, 0]], [1, "myRank", 10, [[2, "评级：", 28, 1, 1, -145, [26]], [6, 32, 144.05200000000002, -146]], [0, "e6nWutnkNBGZ4A514o/H6j", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-79.05200000000002, 0.304, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "myRank", 22, [[2, "评级：", 28, 1, 1, -147, [29]], [6, 32, 87.953, -148]], [0, "c03rf4iopNj7wIIsE2TrtO", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-87.953, 3.431, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "myRank", 28, [[2, "评级：", 28, 1, 1, -149, [46]], [6, 32, 144.05200000000002, -150]], [0, "87eJiSoLpO54vVEMaI7cKP", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-79.05200000000002, 0.304, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "myRank", 29, [[2, "评级：", 28, 1, 1, -151, [49]], [6, 32, 87.953, -152]], [0, "55If4DhepDp5GRHSjQOzp+", 1], [4, 4284900966], [5, 84, 51], [0, 1, 0.5], [-87.953, 3.431, 0, 0, 0, 0, 1, 1, 1, 1]], [41, "questionContent", 13, [[53, 1, 2, -24, -153, [5, 993, 40]], [27, 40, 190, 176, -154]], [0, "6fMoOgH6VP1pH6ZKlZqLk8", 2], [5, 993, 40], [0, 0, 1], [190, -31, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "orNode", 2, [-156], [[8, 40, -155]], [0, "254pc/MTFDBYc5Cpa4PZ08", 2], [5, 1183, 112.4], [0, 0, 1], [0, -91, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "title", 1, [-157], [0, "43EhzewKVC+okoNp515rET", 1], [4, 4284900450], [5, 120, 51], [0, 0, 0.5], [-591.935, 306.105, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "听后转述", 30, 1, 1, 40, [0]], [13, "bar", 0, 16, [-158], [0, "2eyg4jIrdEJrry9FzXUaZ2", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 1, 1, 0, 42, [2]], [18, 1, 16, 43], [19, false, 0.75, false, 0.23, null, 8, 15, 44], [45, "dec", 153, 8, [-159], [0, "dcbCA9bxhNpJunRFw8qDqB", 1], [4, 4278190080], [5, 330, 38], [0, 0, 0.5], [22.153, -38.712, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "现在，请在120秒内完成作答", 26, 30, 1, 1, 46, [3]], [1, "New Label", 18, [[31, "小提示", 24, 30, 1, 3, -160, [6]]], [0, "94mtfqqRlLwrMhd60oEn+u", 4], [4, 4284900450], [5, 219, 38], [0, 0, 1], [60, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "icon_deng", 18, [[4, -161, [7], 8]], [0, "1aP2Am/MNM84slQmBtCNf2", 4], [5, 20, 26], [36.748, -39.082, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "New Label", 18, [[31, "你可以这样开始：", 24, 30, 1, 3, -162, [9]]], [0, "96lEZIBKhG6p05JqOOXloR", 4], [4, 4284900450], [5, 256.5, 38], [0, 0, 1], [21.8, -60.9, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "tipsStartLabel", 4, [-163], [0, "00dOYchGhBJIK2QDCT4Ffx", 4], [4, 4279505940], [5, 240.6, 32], [0, 0, 1], [21.339, -116.1, 0, 0, 0, 0, 1, 1, 1, 1]], [54, 24, 32, 1, 3, 2, 51, [10]], [13, "bar", 0, 19, [-164], [0, "d46DXJIL9PRpo2101zjgcW", 1], [5, 4, 174], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 1, 1, 0, 53, [12]], [18, 1, 19, 54], [19, false, 0.75, false, 0, null, 7, 4, 55], [5, "timeTitle", 20, [-165], [0, "27pAH/+0dINYeTOOJ35qxK", 1], [4, 4284900450], [5, 96, 51], [0, 1, 0.5], [18.947, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "倒计时：", 24, 1, 1, 57, [13]], [5, "time", 20, [-166], [0, "cab3JUOpZIo63DE2LUQRHo", 1], [4, 4279505940], [5, 23, 51], [0, 0, 0.5], [19.962, -1.134, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "- -", 24, 1, 1, 59, [14]], [10, "line", 7, [[4, -167, [17], 18]], [0, "70t4mk9CRO1Jx12zpjIurO", 1], [5, 282, 1], [138.682, -94.917, 0, 0, 0, 0, 1, 1, 1, 1]], [47, 1, 33, [21]], [10, "resultTitle", 5, [[4, -168, [22], 23]], [0, "abgU+JhotPEa9rXX8rtQ/h", 1], [5, 272, 34], [0, 291.755, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "mySound", 5, [[2, "我的录音：", 28, 1, 1, -169, [24]]], [0, "06/2xI/yZNiY83XbZgRBGi", 1], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [-451.102, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [46, "mySoundLost", false, 5, [-170], [0, "4aZxe6EAhA27GoSpwjHXJs", 1], [4, 4288913565], [5, 196, 51], [0, 0, 0.5], [-436.678, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "未生成我的录音", 28, 1, 1, 65, [25]], [1, "unit", 22, [[32, "分", 28, 30, 1, -171, [28]]], [0, "972J0vccdJlZP4PPiLfs0N", 1], [4, 4288913565], [5, 28, 38], [0, 0, 0.5], [8.808, 1.806, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "89", 64, 70, 1, 1, 22, [30]], [1, "soundLabel", 24, [[2, "原文音频：", 28, 1, 1, -172, [31]]], [0, "3cXI2hjXtKELeQTOThNOCd", 3], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [187.224, -61.312, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 25, [[33, "转述开头：", 28, 2, -173, [32]]], [0, "18NTpn6B5Pn7ykjKoArjcq", 3], [4, 4284900966], [5, 140, 40], [0, 0, 1], [47.852, -20.735, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "startNewtLabel", 25, [-174], [0, "74GjwZdbNN0rwglTbx1gey", 3], [4, 4279505940], [5, 966, 46], [0, 0, 1], [189.266, -18.735, 0, 0, 0, 0, 1, 1, 1, 1]], [55, 32, 46, 3, 2, 71, [33]], [1, "soundLabel", 26, [[34, "参考答案：", 28, 1, 2, -175, [34]]], [0, "34xogbo21B2ImxY9I/jVkz", 3], [4, 4284900966], [5, 140, 40], [0, 0, 1], [48.342, -47.293, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "answertLabel", 26, [-176], [0, "caU5V5yepJFq0d0jzgVKsV", 3], [4, 4279505940], [5, 966, 46], [0, 0, 1], [189.266, -44.293, 0, 0, 0, 0, 1, 1, 1, 1]], [35, 32, 46, 1, 3, 2, 74, [35]], [13, "bar", 0, 27, [-177], [0, "6bgx9o0yFBorF3zEyBcKcI", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 1, 1, 0, 76, [37]], [18, 1, 27, 77], [19, false, 0.75, false, 0.23, null, 11, 3, 78], [10, "resultTitle", 6, [[4, -178, [42], 43]], [0, "81x6FqyIVAC5bolQHl8jzT", 1], [5, 272, 34], [0, 291.755, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "mySound", 6, [[2, "我的录音：", 28, 1, 1, -179, [44]]], [0, "8e8w5C3mpEwqhrH/VkLp52", 1], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [-451.102, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [42, "mySoundLost", false, 6, [[2, "未生成我的录音", 28, 1, 1, -180, [45]]], [0, "99qQNYG1NDwY2y+4zwMRvM", 1], [4, 4288913565], [5, 196, 51], [0, 0, 0.5], [-436.678, -274.083, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "unit", 29, [[32, "分", 28, 30, 1, -181, [48]]], [0, "cbILcNaVZDLqDUlVbPUqb/", 1], [4, 4288913565], [5, 28, 38], [0, 0, 0.5], [8.808, 1.806, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 13, [[2, "题       干：", 28, 1, 1, -182, [51]]], [0, "98dsZ0tDNJIoxE5MU+olla", 2], [4, 4284900966], [5, 139, 51], [0, 1, 0.5], [187.224, -45.5, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 39, [[2, "原文音频：", 28, 1, 1, -183, [52]]], [0, "3cXI2hjXtKELeQTOThNOCd", 2], [4, 4284900966], [5, 140, 51], [0, 1, 0.5], [187.224, -61.312, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 14, [[33, "转述开头：", 28, 2, -184, [53]]], [0, "18NTpn6B5Pn7ykjKoArjcq", 2], [4, 4284900966], [5, 140, 40], [0, 0, 1], [47.852, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "startNewtLabel", 14, [[56, "ssssssss", 32, 46, 3, 2, -185, [54]]], [0, "74GjwZdbNN0rwglTbx1gey", 2], [4, 4279505940], [5, 966, 46], [0, 0, 1], [189.266, -20, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "soundLabel", 31, [[34, "参考答案：", 28, 1, 2, -186, [55]]], [0, "34xogbo21B2ImxY9I/jVkz", 2], [4, 4284900966], [5, 140, 40], [0, 0, 1], [48.342, -47.293, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "answertLabel", 31, [[35, 32, 46, 1, 3, 2, -187, [56]]], [0, "caU5V5yepJFq0d0jzgVKsV", 2], [4, 4279505940], [5, 966, 46], [0, 0, 1], [189.266, -44.293, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "bar", 0, 32, [-188], [0, "0cB0f5GxJBL6YCoom9nzzk", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [14, 1, 1, 0, 90, [58]], [18, 1, 32, 91], [19, false, 0.75, false, 0.23, null, 12, 2, 92]], 0, [0, 2, 1, 0, 0, 1, 0, 6, 6, 0, 7, 10, 0, 8, 68, 0, 9, 24, 0, 10, 62, 0, 11, 66, 0, 12, 60, 0, 13, 5, 0, 14, 21, 0, 15, 7, 0, 16, 8, 0, 17, 75, 0, 18, 72, 0, 19, 58, 0, 20, 47, 0, 21, 52, 0, 22, 9, 0, 23, 15, 0, 24, 41, 0, 0, 1, 0, -1, 40, 0, -2, 8, 0, -3, 7, 0, -4, 21, 0, -5, 5, 0, -6, 6, 0, 2, 2, 0, 0, 2, 0, 0, 2, 0, -1, 13, 0, -2, 39, 0, -3, 14, 0, -4, 31, 0, 2, 3, 0, 0, 3, 0, 0, 3, 0, -1, 24, 0, -2, 25, 0, -3, 26, 0, 2, 4, 0, 0, 4, 0, 0, 4, 0, -1, 18, 0, -2, 51, 0, 0, 5, 0, 0, 5, 0, -1, 63, 0, -2, 64, 0, -3, 65, 0, -4, 10, 0, -5, 22, 0, -6, 11, 0, 0, 6, 0, 0, 6, 0, -1, 80, 0, -2, 81, 0, -3, 82, 0, -4, 28, 0, -5, 29, 0, -6, 12, 0, 0, 7, 0, -2, 56, 0, -1, 17, 0, -2, 19, 0, -3, 20, 0, -4, 61, 0, 0, 8, 0, -2, 45, 0, -1, 9, 0, -2, 16, 0, -3, 46, 0, 0, 9, 0, 0, 9, 0, -1, 15, 0, 0, 10, 0, 0, 10, 0, -1, 34, 0, 0, 11, 0, -2, 79, 0, -1, 23, 0, -2, 27, 0, 0, 12, 0, -2, 93, 0, -1, 30, 0, -2, 32, 0, 0, 13, 0, 0, 13, 0, -1, 84, 0, -2, 38, 0, 0, 14, 0, 0, 14, 0, -1, 86, 0, -2, 87, 0, 2, 15, 0, -1, 44, 0, 0, 16, 0, -1, 42, 0, 0, 17, 0, 0, 17, 0, -1, 48, 0, -2, 49, 0, -3, 50, 0, -1, 55, 0, 0, 19, 0, -1, 53, 0, 0, 20, 0, -1, 57, 0, -2, 59, 0, 0, 21, 0, -1, 33, 0, -1, 68, 0, -1, 67, 0, -2, 35, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, -1, 69, 0, 0, 25, 0, -1, 70, 0, -2, 71, 0, 0, 26, 0, -1, 73, 0, -2, 74, 0, -1, 78, 0, 0, 27, 0, -1, 76, 0, 0, 28, 0, 0, 28, 0, -1, 36, 0, 0, 29, 0, -1, 83, 0, -2, 37, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, -1, 88, 0, -2, 89, 0, -1, 92, 0, 0, 32, 0, -1, 90, 0, 0, 33, 0, -2, 62, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, -1, 85, 0, -1, 41, 0, -1, 43, 0, -1, 47, 0, 0, 48, 0, 0, 49, 0, 0, 50, 0, -1, 52, 0, -1, 54, 0, -1, 58, 0, -1, 60, 0, 0, 61, 0, 0, 63, 0, 0, 64, 0, -1, 66, 0, 0, 67, 0, 0, 69, 0, 0, 70, 0, -1, 72, 0, 0, 73, 0, -1, 75, 0, -1, 77, 0, 0, 80, 0, 0, 81, 0, 0, 82, 0, 0, 83, 0, 0, 84, 0, 0, 85, 0, 0, 86, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, -1, 91, 0, 25, 1, 2, 4, 30, 3, 4, 23, 4, 4, 17, 44, 3, 45, 55, 3, 56, 78, 3, 79, 92, 3, 93, 188], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 54, 62, 77, 91], [-1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, 1, -1, 1, -1, -2, 26, 1, 1, 1, 1, 1], [0, 0, 0, 0, 0, 1, 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 7, 0, 8, 0, 1, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 4, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 4, 0, 9, 5, 10, 11, 2, 2, 5, 2, 2]], [[{"name": "icon_deng", "rect": [6, 3, 20, 26], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}], [7], 0, [0], [5], [12]], [[{"name": "line", "rect": [0, 0, 282, 1], "offset": [0, 0], "originalSize": [282, 1], "capInsets": [0, 0, 0, 0]}], [7], 0, [0], [5], [13]]]]