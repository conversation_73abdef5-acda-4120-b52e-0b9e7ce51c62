[1, ["ecpdLyjvZBwrvm+cedCcQy", "a7mkVBr09FA4EcPVPO3Mhi", "38eg96ydBOgpkUp2ms646x", "08q5UbDwNFe42p15l6LrQ2", "bekSZb+A9NpZ7ua3S/Cv4l", "016e/INotGRoUwNuvENNQU", "caPxzaEcVBXoxHJJkEDzbC", "caFys8T89N8YHXCjGqJFZH", "c9vSIDRKJMootgKks878re", "25/lyLID1I9r4ZyeUrbbhL", "a48/8DOgpKU79aHtjWdlUE", "63W+x9eYpE9pT25hCR7A1Y", "c0rrW3ha9Bpr4nNmyVxsT3", "55iSMytDJN274A8FugVkbF", "21lj1UAplAnb4yTErUc41r", "8fNkdYP49B2ZcwtQdr4e6L", "d2zlMrbD1DPoKD6sHYoyDx", "42ga4IBCNEdp8pXhGUqoM+", "7aPK6HSPBCZrTDMRrkEgxW"], ["node", "_spriteFrame", "_textureSetter", "_parent", "root", "scrollViewContent", "btnExpandText", "btnExpand", "btnBack", "btnClose", "contentNode", "maskNode", "data", "_scrollView"], [["cc.Node", ["_name", "_opacity", "_prefab", "_contentSize", "_components", "_parent", "_children", "_anchorPoint", "_trs", "_color"], 1, 4, 5, 9, 1, 2, 5, 7, 5], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_type", "_srcBlendFactor", "_isTrimmedMode", "_enabled", "node", "_materials", "_spriteFrame"], -2, 1, 3, 6], ["cc.Widget", ["_alignFlags", "_right", "_top", "alignMode", "_left", "_bottom", "_originalHeight", "node", "_target"], -4, 1, 1], ["cc.Node", ["_name", "_active", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 12, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Mask", ["_type", "node", "_materials", "_spriteFrame"], 2, 1, 3, 6], ["cc.AnimationClip", ["_name", "_duration", "curveData", "sample"], -1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint"], 2, 1, 2, 4, 5, 5], ["3d24fSQL7dKZbiZsf8pRvyl", ["node", "maskNode", "contentNode", "btnClose", "btnBack", "btnExpand", "btnExpandText", "scrollViewContent"], 3, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 3], ["cc.<PERSON><PERSON>", ["node", "clickEvents"], 3, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -2, 1, 3], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON>", ["_N$direction", "node", "_N$handle"], 2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content", "_N$verticalScrollBar"], -2, 1, 1, 1]], [[5, 0, 1, 2, 2], [0, 0, 5, 4, 2, 3, 8, 2], [2, 0, 5, 6, 7, 2], [12, 0, 1, 1], [13, 0, 1, 2, 3], [2, 0, 3, 5, 6, 7, 3], [8, 0, 2], [0, 0, 6, 4, 2, 3, 2], [0, 0, 6, 4, 2, 3, 7, 2], [0, 0, 5, 6, 4, 2, 3, 7, 8, 2], [0, 0, 6, 2, 3, 7, 8, 2], [0, 0, 5, 4, 2, 3, 7, 8, 2], [0, 0, 1, 5, 4, 2, 9, 3, 3], [0, 0, 5, 6, 4, 2, 3, 2], [0, 0, 5, 4, 2, 3, 2], [4, 0, 1, 2, 3, 4, 5, 6, 3], [4, 0, 2, 3, 4, 5, 6, 7, 8, 2], [9, 0, 1, 2, 3, 4, 5, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 1], [5, 1, 2, 1], [2, 1, 0, 5, 6, 3], [2, 4, 1, 0, 5, 6, 7, 4], [2, 2, 0, 5, 6, 7, 3], [2, 2, 1, 0, 5, 6, 4], [6, 1, 2, 1], [6, 0, 1, 2, 3, 2], [11, 0, 1, 1], [3, 0, 1, 2, 7, 8, 4], [3, 3, 0, 4, 1, 2, 5, 6, 7, 8], [3, 0, 1, 7, 3], [14, 0, 1, 2, 3, 4, 5, 6, 6], [15, 0, 1], [16, 0, 1, 2, 2], [17, 0, 1, 2, 3, 4, 5, 6, 7, 6], [7, 0, 1, 2, 4], [7, 0, 1, 3, 2, 5]], [[[[6, "questionContent"], [7, "questionContent", [-10, -11], [[18, -9, -8, -7, -6, -5, -4, -3, -2]], [19, -1, 0], [5, 1280, 720]], [15, "scrollview", false, 1, [-14, -15, -16, -17, -18], [[[20, 1, 0, -12, [21]], -13], 4, 1], [0, "0ckDFegeFECJBPKbHVPZU8", 1, 0], [5, 900, 656]], [8, "btn_spr", [-20, -21, -22, -23], [[2, 0, -19, [3], 4]], [0, "f1wsB5CClNSIjIIGpv/fuw", 1, 0], [5, 172, 72], [0, 0, 0.5]], [9, "mask", 1, [3], [[24, -24, [5]], [26, -25, [6, 7]]], [0, "1d2aWmj9pF77Iqj6QlZNgg", 1, 0], [5, 172, 72], [0, 0, 0.5], [-640, -232, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_close", 2, [[5, 0, false, -26, [12], 13], [27, 33, 118, 32, -27, 1], [3, -28, [[4, "3d24fSQL7dKZbiZsf8pRvyl", "btnCloseClick", 1]]]], [0, "8caTmqIhpHI5PiLptIE0X6", 1, 0], [5, 32, 32], [506, 312, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "scrollBar", 2, [-32], [[-29, [28, 0, 37, 350.07654921020657, 12, 25, 25, 237, -30], [21, false, 1, 0, -31, [19], 20]], 1, 4, 4], [0, "eemeY7pNhBFLmrZg/+hvFm", 1, 0], [5, 12, 606], [0, 1, 0.5], [438, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "content", [-33], [0, "7aYmtnAV9DiZzAXoZU8/c1", 1, 0], [5, 900, 656], [0, 0.5, 1], [0, 328, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "spr_bi<PERSON>shi", 3, [[5, 0, false, -34, [0], 1], [29, 32, 13, -35]], [0, "d7WWZaJ7dJ1JbGJbwVyawt", 1, 0], [5, 24, 24], [147, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lab_txt", 3, [[30, "查看原文", 24, 30, 1, 1, -36, [2]]], [0, "3aqLSYIXdIY6FYEuc9OrVV", 1, 0], [5, 96, 38], [0, 0, 0.5], [16, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_back", 3, [[3, -37, [[4, "3d24fSQL7dKZbiZsf8pRvyl", "btnBackClick", 1]]]], [0, "5bRTQhANlMbr/BMybcaMbY", 1, 0], [5, 45, 72], [149.355, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "btn_chakan", 3, [[3, -38, [[4, "3d24fSQL7dKZbiZsf8pRvyl", "btnOriginalClick", 1]]]], [0, "d9/UrxGgRCfYlX75+UziEH", 1, 0], [5, 110, 72], [63.781, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "back", 177, 2, [[2, 0, -39, [8], 9], [31, -40]], [0, "51RehHIuxIlKN43j4CSmzV", 1, 0], [4, 4278190080], [5, 1300, 960]], [13, "view", 2, [7], [[25, 2, -41, [15], 16]], [0, "100XNZy7VEB5n71QMEOOnV", 1, 0], [5, 900, 656]], [14, "back1", 2, [[22, 1, 0, -42, [10], 11]], [0, "25sW5e8k9K/pzZpByGFlgt", 1, 0], [5, 900, 656]], [17, "bar", 6, [-43], [0, "9awd2+KulI4I8uX9ecN897", 1, 0], [5, 10, 140], [0, 1, 0.5]], [23, 1, 1, 0, 15, [14]], [32, 1, 6, 16], [33, false, 0.75, false, 0.23, null, 2, 7, 17], [1, "title", 7, [[2, 0, -44, [17], 18]], [0, "f0YmtsDDdLh6vWYvvYLVXH", 1, 0], [5, 136, 36], [0, -50, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 4, 1, 0, 5, 7, 0, 6, 9, 0, 7, 11, 0, 8, 10, 0, 9, 5, 0, 10, 2, 0, 11, 4, 0, 0, 1, 0, -1, 4, 0, -2, 2, 0, 0, 2, 0, -2, 18, 0, -1, 12, 0, -2, 14, 0, -3, 5, 0, -4, 6, 0, -5, 13, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, -3, 10, 0, -4, 11, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, -1, 17, 0, 0, 6, 0, 0, 6, 0, -1, 15, 0, -1, 19, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 10, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 14, 0, -1, 16, 0, 0, 19, 0, 12, 1, 3, 3, 4, 7, 3, 13, 17, 13, 18, 44], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16], [-1, 1, -1, -1, 1, -1, -1, -2, -1, 1, -1, 1, -1, 1, -1, -1, 1, -1, 1, -1, 1, -1, 1], [0, 2, 0, 0, 3, 0, 4, 5, 0, 6, 0, 1, 0, 7, 0, 0, 1, 0, 8, 0, 9, 0, 10]], [[[34, "zhankai", 0.16666666666666666, {"props": {"width": [{"frame": 0, "value": 48}, {"frame": 0.16666666666666666, "value": 172}]}, "paths": {"btn_spr": {"props": {"x": [{"frame": 0, "value": -124}, {"frame": 0.16666666666666666, "value": 0}]}}, "btn_spr/spr_biaoshi": {"props": {"scaleX": [{"frame": 0, "value": -1}, {"frame": 0.16666666666666666, "value": 1}]}}}}]], 0, 0, [], [], []], [[{"name": "spr_btn", "rect": [0, 0, 344, 144], "offset": [0, 0], "originalSize": [344, 144], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [11]], [[{"name": "default_scrollbar_vertical_bg", "rect": [0, 0, 15, 30], "offset": [0, 0], "originalSize": [15, 30], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [12]], [[{"name": "spr_jt", "rect": [10, 8, 18, 32], "offset": [-5, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [13]], [[{"name": "spr_hudo<PERSON><PERSON>o", "rect": [0, 0, 30, 420], "offset": [0, 0], "originalSize": [30, 420], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [14]], [[{"name": "spr_black1", "rect": [0, 0, 1800, 1312], "offset": [0, 0], "originalSize": [1800, 1312], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [15]], [[[35, "guanbi", 0.2857142857142857, 35, {"props": {"width": [{"frame": 0, "value": 172}, {"frame": 0.2857142857142857, "value": 48}]}, "paths": {"btn_spr": {"props": {"x": [{"frame": 0, "value": 0}, {"frame": 0.2857142857142857, "value": -124}]}}, "btn_spr/spr_biaoshi": {"props": {"scaleX": [{"frame": 0, "value": 1}, {"frame": 0.2857142857142857, "value": -1}]}}}}]], 0, 0, [], [], []], [[{"name": "spr_title", "rect": [0, 5, 272, 67], "offset": [0, -2.5], "originalSize": [272, 72], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [16]], [[{"name": "btn_close", "rect": [6, 6, 84, 84], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [17]], [[{"name": "default_sprite_splash", "rect": [0, 0, 2, 2], "offset": [0, 0], "originalSize": [2, 2], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [18]]]]