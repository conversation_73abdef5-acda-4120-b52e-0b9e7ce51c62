[1, ["ecpdLyjvZBwrvm+cedCcQy", "7a/QZLET9IDreTiBfRn2PD", "089Zdvm7lDyoZKTlWtR7PE", "a4dVk29aBFZ61Un9P8WCIj", "06ZReAPb5Ln79AE4MctrUA", "9clvGEl7NOo4klGVulmdez", "4cDmzQBmlMb6D5iSPdBmCP", "e3ZBa/LqBBH5RbT30CpntR", "d9TvLZ8u1GEr7fjB1DuLHv", "254zdbB85MNK8sQHWKr8uv", "cfkg00niVGJ7e3evU8A+uw", "b9p1UwqKxPSJr9BTlD49UR", "a2MjXRFdtLlYQ5ouAFv/+R", "a7tsGmh39O7bGIX7uEJ2xN", "e9Ld2m4QtHYb8B9TVtjEyB", "92FjfSrYROyoFtUdU/mpSY", "cdnx+S1l5BU7bC8ZtXZU6T", "620ibcLJhFAq+wtvXhYgN5", "1eLLjRgGxLUaEsbDNyaASr", "8dioZBe4xBWLUk1HFPuYoj", "b4yermyilErpm6kXoGRZB5", "651DSV8uhPvZXk5nXqCIW0", "f9YiVJQ6dL1agqUqdgu/FK", "ebz1EUDkFOUZ09lrKmw5Ux", "fbbugkbrBNpI4sg6cCMunB", "540Etyk7RPj5XOyGu3OrTJ", "cdW5pDd7pO0pagWWPZO/P7", "d9Xd9pLNVCbqEPnIW+3OKp", "1cYC9F2A5BpJ9qxlL+tdpC", "4f7usp7SVBCLBqfaqbkOI4", "a72NAVo5JGlZNn4feFSn12", "08JJ5EtEVK5JIacf0FM7Vn"], ["node", "_spriteFrame", "_textureSetter", "_parent", "_N$skeletonData", "root", "data", "_N$file", "scrollView", "pkR", "pkL", "pkAnima", "pk", "noOneNode", "cutDownEndNode", "cutDownNode", "anwerNode", "fightNode", "<PERSON><PERSON><PERSON>", "content", "quetionPic", "quetionLabel1", "quetionLabel0", "succRight", "succLeft", "starRight", "starLeft", "quetionName", "bg", "pf"], [["cc.Node", ["_name", "_active", "_opacity", "_prefab", "_contentSize", "_parent", "_children", "_components", "_trs", "_color", "_anchorPoint"], 0, 4, 5, 1, 2, 9, 7, 5, 5], ["cc.Label", ["_N$verticalAlign", "_fontSize", "_N$horizontalAlign", "_lineHeight", "_srcBlendFactor", "_N$overflow", "_string", "_styleFlags", "_isSystemFontUsed", "_spacingX", "node", "_materials"], -7, 1, 3], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_srcBlendFactor", "_type", "_isTrimmedMode", "_fillType", "_fillStart", "_fillRange", "node", "_materials", "_spriteFrame", "_fillCenter"], -4, 1, 3, 6, 5], ["cc.Widget", ["_alignFlags", "_bottom", "_right", "_originalWidth", "_left", "_top", "_originalHeight", "alignMode", "node", "_target"], -5, 1, 1], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_color", "_anchorPoint"], 2, 1, 2, 4, 5, 7, 5, 5], ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_children", "_trs", "_anchorPoint"], 1, 12, 4, 5, 1, 2, 7, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "_playTimes", "defaultAnimation", "node", "_materials"], -3, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.AnimationClip", ["_name", "_duration", "curveData"], 1, 11], ["ba8ccb/2+lGdLHIDt5GQfee", ["node", "bg", "quetionName", "starLeft", "starRight", "succLeft", "succRight", "quetionLabel0", "quetionLabel1", "quetionPic", "content", "<PERSON><PERSON><PERSON>", "studentHead", "studentName", "studentSorce", "defaultHead", "studentBan", "pkStudentHead", "pkStudentName", "pkStudentSorce", "pkStudentBan", "fightNode", "anwerNode", "cutDownNode", "cutDownEndNode", "randomNameNode", "noOneNode", "pk", "pkAnima", "pkL", "pkR", "scrollView", "countProgressList", "soundWaveSpines", "pingFenSpines", "pingFenLabel", "timeOutFenLabel", "pf"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2, 2, 2, 3, 2, 2, 2, 2, 2, 1, 1, 1, 1, 2, 1, 1, 1, 1, 1, 1, 2, 2, 2, 2, 2, 6], ["cc.Layout", ["_resize", "_N$layoutType", "_N$paddingTop", "_N$spacingY", "node", "_layoutSize"], -1, 1, 5], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -2, 1, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["cc.Animation", ["node", "_clips"], 3, 1, 3]], [[7, 0, 1, 2], [3, 7, 8, 9, 1], [0, 0, 5, 7, 3, 4, 8, 2], [5, 0, 1, 2, 3, 4, 2], [0, 0, 5, 6, 3, 4, 8, 2], [5, 0, 1, 2, 3, 4, 5, 2], [0, 0, 5, 7, 3, 4, 2], [3, 1, 7, 8, 2], [3, 0, 7, 8, 9, 2], [4, 0, 4, 1, 8, 4], [3, 7, 8, 1], [0, 0, 1, 6, 7, 3, 4, 8, 3], [6, 0, 5, 6, 2, 3, 4, 8, 7, 2], [6, 0, 5, 2, 3, 4, 8, 7, 2], [1, 4, 6, 1, 3, 2, 0, 10, 11, 7], [4, 0, 2, 5, 1, 8, 5], [4, 7, 0, 4, 2, 1, 8, 9, 6], [3, 1, 7, 8, 9, 2], [9, 0, 1, 2, 3, 4, 6, 7, 6], [0, 0, 1, 5, 6, 7, 3, 4, 3], [0, 0, 5, 6, 7, 3, 4, 8, 2], [0, 0, 2, 5, 7, 3, 9, 4, 3], [10, 0, 2], [0, 0, 6, 7, 3, 4, 2], [0, 0, 1, 6, 3, 4, 3], [0, 0, 1, 5, 6, 7, 3, 4, 8, 3], [0, 0, 1, 5, 6, 3, 3], [0, 0, 2, 5, 7, 3, 9, 4, 8, 3], [5, 0, 1, 2, 3, 6, 4, 7, 5, 2], [5, 0, 1, 2, 3, 6, 4, 5, 2], [5, 0, 1, 2, 3, 4, 7, 5, 2], [1, 4, 1, 3, 0, 5, 10, 11, 6], [1, 4, 1, 3, 2, 0, 5, 10, 11, 7], [1, 1, 3, 8, 9, 2, 0, 10, 11, 7], [1, 6, 3, 7, 2, 0, 10, 11, 6], [1, 1, 3, 0, 5, 10, 11, 5], [1, 6, 1, 3, 2, 0, 10, 11, 6], [7, 1, 1], [14, 0, 1], [3, 1, 2, 0, 7, 8, 9, 4], [3, 3, 7, 8, 9, 2], [3, 2, 0, 4, 5, 6, 7, 8, 10, 6], [8, 1, 2, 1], [18, 0, 1, 2, 2], [9, 0, 5, 1, 2, 3, 4, 6, 7, 7], [0, 0, 7, 3, 4, 2], [0, 0, 6, 7, 3, 4, 10, 8, 2], [0, 0, 5, 6, 7, 3, 4, 2], [0, 0, 5, 6, 3, 4, 2], [0, 0, 1, 5, 6, 3, 9, 4, 3], [0, 0, 1, 5, 6, 3, 4, 8, 3], [0, 0, 1, 5, 3, 4, 8, 3], [6, 0, 1, 5, 6, 2, 3, 4, 3], [6, 0, 6, 2, 3, 4, 7, 2], [1, 4, 1, 2, 0, 5, 10, 11, 6], [1, 4, 1, 7, 2, 0, 5, 10, 11, 7], [1, 4, 6, 1, 2, 0, 10, 11, 6], [11, 0, 1, 2, 3], [12, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 1], [4, 0, 3, 8, 3], [4, 0, 3, 6, 8, 4], [4, 0, 2, 1, 8, 4], [13, 0, 1, 2, 3, 4, 5, 5], [3, 2, 0, 7, 8, 3], [8, 0, 1, 2, 2], [15, 0, 1, 2, 2], [16, 0, 1, 2, 3], [17, 0, 1, 2, 3, 4, 5, 6, 6], [19, 0, 1, 1]], [[[{"name": "ban", "rect": [0, 0, 225, 36], "offset": [0, 0], "originalSize": [225, 36], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [16]], [[[22, "namePre"], [45, "namePre", [[54, 1, 28, 1, 1, 2, -2, [0]]], [37, -1], [5, 224, 40]]], 0, [0, 5, 1, 0, 0, 1, 0, 6, 1, 2], [0], [-1], [0]], [[{"name": "pic0", "rect": [72, 14, 82, 109], "offset": [0.5, 1], "originalSize": [225, 139], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [17]], [[{"name": "btn", "rect": [0, 0, 96, 88], "offset": [0, 0], "originalSize": [96, 88], "capInsets": [43, 0, 42, 0]}], [2], 0, [0], [2], [18]], [[{"name": "randomBg", "rect": [0, 0, 225, 139], "offset": [0, 0], "originalSize": [225, 139], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [19]], [[{"name": "bgW", "rect": [0, 0, 158, 95], "offset": [0, 0], "originalSize": [158, 95], "capInsets": [51, 40, 56, 41]}], [2], 0, [0], [2], [20]], [[{"name": "hong", "rect": [59, 25, 349, 291], "offset": [-0.5, 2.5], "originalSize": [468, 346], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [21]], [[{"name": "gold", "rect": [0, 0, 18, 18], "offset": [0, 0], "originalSize": [18, 18], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [22]], [[{"name": "pic1", "rect": [72, 13, 82, 109], "offset": [0.5, 1.5], "originalSize": [225, 138], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [23]], [[{"name": "lan", "rect": [59, 25, 349, 291], "offset": [-0.5, 9], "originalSize": [468, 359], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [2], [24]], [[[57, "pk", 0.7166666666666667, [{}, "paths", 11, [{"left": {"props": {"x": [{"frame": 0, "value": -1500}, {"frame": 0.3333333333333333, "value": -640}]}}, "right": {"props": {"x": [{"frame": 0, "value": 1500}, {"frame": 0.3333333333333333, "value": 640}]}}}, "cent", 11, [{}, "props", 11, [{}, "scale", 12, [[[{"frame": 0}, "value", 8, [0, 0, 0]], [{"frame": 0.26666666666666666}, "value", 8, [0, 0, 0]], [{"frame": 0.31666666666666665}, "value", 8, [0, 1.1, 1.1]], [{"frame": 0.45}, "value", 8, [0, 1.4, 1.4]], [{"frame": 0.5}, "value", 8, [0, 1.1, 1.1]], [{"frame": 0.6}, "value", 8, [0, 0.95, 0.95]], [{"frame": 0.7166666666666667}, "value", 8, [0, 1, 1]]], 11, 11, 11, 11, 11, 11, 11]]]]]]], 0, 0, [], [], []], [[[22, "EnPKComponent"], [23, "EnPKComponent", [-54, -55, -56, -57, -58, -59, -60, -61, -62, -63], [[58, -53, -52, -51, -50, -49, -48, -47, -46, -45, -44, -43, -42, [-40, -41], [-38, -39], [-36, -37], [94, 95], [-34, -35], [-32, -33], [-30, -31], [-28, -29], [-26, -27], -25, -24, -23, -22, [-20, -21], -19, -18, -17, -16, -15, -14, [-12, -13], [-8, -9, -10, -11], [-6, -7], [-4, -5], [-2, -3], 96]], [37, -1], [5, 1280, 720]], [46, "content", [-66, -67, -68, -69], [[59, 40, 220, -64], [62, 1, 2, 21, 8, -65, [5, 440, 111]]], [0, "39YsroaslLjbrgiNOWiNB8", 1], [5, 440, 111], [0, 0.5, 1], [0, 152.384, 0, 0, 0, 0, 1, 1, 1, 1]], [52, "pkNode", false, 1, [-72, -73, -74, -75, -76], [[-70, [38, -71]], 1, 4], [0, "ef+YE9FkhH9bYUj2dRS2d+", 1], [5, 1280, 720]], [24, "da<PERSON><PERSON><PERSON>", false, [-77, -78, -79, -80, -81], [0, "12jwJhyl9MqKtbQ4+b5n8g", 1], [5, 115, 115]], [24, "da<PERSON><PERSON><PERSON>", false, [-82, -83, -84, -85, -86], [0, "a4QqprN91OA7xA6EB+q3jY", 1], [5, 115, 115]], [19, "finghtNode", false, 1, [-88, -89, -90], [[38, -87]], [0, "6cAnWSqtBAgZVEDECaU60d", 1], [5, 1280, 720]], [11, "ban", false, [-92, -93], [[1, -91, [6], 7]], [0, "7ciQpNZGRFVKUHyFxxgloG", 1], [5, 225, 36], [-0.148, -52.169, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "socre", 7, [-96], [[-94, [15, 32, 17, 8, 8, -95]], 1, 4], [0, "b7ae5GI8BN06ATsSxJ2NAo", 1], [5, 8, 26], [0, 1, 0.5], [95.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ban", false, [-98, -99], [[1, -97, [16], 17]], [0, "c3dxuzmY1OC798pUpja0jq", 1], [5, 225, 36], [-0.148, -52.169, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "socre", 9, [-102], [[-100, [15, 32, 17, 8, 8, -101]], 1, 4], [0, "01XQJ1OfFKwrtta3XzERv5", 1], [5, 8, 26], [0, 1, 0.5], [95.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ban", false, [-104, -105], [[1, -103, [66], 67]], [0, "b8wQjHt55FT5g+vHgwnmYD", 1], [5, 225, 36], [-0.148, -52.169, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "socre", 11, [-108], [[-106, [15, 32, 17, 8, 8, -107]], 1, 4], [0, "62Ug1dwhJC2Y/PEJePoCl6", 1], [5, 8, 26], [0, 1, 0.5], [95.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "ban", false, [-110, -111], [[1, -109, [75], 76]], [0, "42ojC2p5hGXY3yvPMnVwzr", 1], [5, 225, 36], [-0.148, -52.169, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "socre", 13, [-114], [[-112, [15, 32, 17, 8, 8, -113]], 1, 4], [0, "7bgzqxd5FOzKfZhrASV97X", 1], [5, 8, 26], [0, 1, 0.5], [95.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [23, "bg", [-116, -117, -118], [[39, 1, 1, 0, -115, [92], 93]], [0, "615h/vz1tKOK2VsVwRmJso", 1], [5, 600, 364]], [4, "left", 1, [-119, -120], [0, "2bHXKY5T1KFYcYt7n/bvko", 1], [5, 285.6, 232.5], [-457.7, 108.4, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "randomNode", false, 16, [-122], [[42, -121, [10]]], [0, "0bLzz1onVO5KVIQwPqPm+v", 1], [5, 225, 139], [0, -18.646, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "right", 1, [-123, -124], [0, "61VhQ2K1dFb6Ga0MaT8E2+", 1], [5, 285.6, 232.5], [457.7, 108.4, 0, 0, 0, 0, 1, 1, 1, 1]], [25, "randomNode", false, 18, [-126], [[42, -125, [20]]], [0, "53c5NeZ71Lq7vnTe0U3zL7", 1], [5, 225, 139], [0, -18.646, 0, 0, 0, 0, 1, 1, 1, 1]], [53, "list", [-129], [[[63, 1, 0, -127, [26]], -128], 4, 1], [0, "85WJUoyk9LM6m/2BmxWsyO", 1], [5, 440, 305], [5, 7.56, 0, 0, 0, 0, 1, 1, 1, 1]], [47, "view", 20, [2], [[64, 0, -130, [25]], [60, 45, 240, 250, -131]], [0, "e1Il4hSkpCIYox1TqLSUWn", 1], [5, 440, 305]], [48, "anwerNode", 1, [-132, -133], [0, "83FeqVRLtBGI0le4KJpQe0", 1], [5, 1280, 720]], [4, "freeMic0", 22, [4, -134, -135], [0, "30q3iHGVRFgq87fbJf21jD", 1], [5, 600, 120], [-466, -164, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]], [19, "pBack", false, 23, [-137, -138], [[40, false, -136, [37], 38]], [0, "23IhMHFUxBWIfRrvps8U8M", 1], [5, 256, 124]], [26, "score", false, 23, [-139, -140, -141], [0, "9enBCnl3pLDqw9hxjJurB9", 1]], [4, "freeMic1", 22, [5, -142, -143], [0, "44/OPNJs1Hw58Ek9tmt804", 1], [5, 600, 120], [466, -164, 0, 0, 0, 0, 1, 0.5, 0.5, 0.5]], [19, "pBack", false, 26, [-145, -146], [[40, false, -144, [53], 54]], [0, "f9qVxLDrpDPaUkPw9SU7DQ", 1], [5, 256, 124]], [26, "score", false, 26, [-147, -148, -149], [0, "b8HcVBnLZNC7l7aMEqOGWO", 1]], [49, "noOneNode", false, 1, [-150, 15], [0, "f3LeypqIlNa52pAXmZ2cpE", 1], [4, 4278190080], [5, 1280, 720]], [20, "btn", 15, [-153], [[39, 1, 1, 0, -151, [90], 91], [65, 3, -152, [[66, "ba8ccb/2+lGdLHIDt5GQfee", "closeNoOneNode", 1]]]], [0, "e50+fKU8tH7rrRW07bAp0A", 1], [5, 520, 88], [0, -98, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "normal", 16, [-154, 7], [0, "d9pZY+DutEzKPyJhXMJc/M", 1], [5, 222.7, 137.5], [0.1, -16.4, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 7, [[-155, [9, 12, 16, 4, -156]], 1, 4], [0, "cdnXL12zxB/qX7SISLvi5b", 1], [5, 140, 28], [0, 0, 0.5], [-96.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gold", 8, [[17, 1, -157, [3], 4], [16, 2, 8, -24, 6, 4, -158, 8]], [0, "278Ku76k5JuLEwMIbRCeOy", 1], [5, 18, 18], [-23, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "normal", 18, [-159, 9], [0, "00nIFSYERHIYaQp58GQgTR", 1], [5, 222.7, 137.5], [0.1, -16.4, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 9, [[-160, [9, 12, 16, 4, -161]], 1, 4], [0, "f2/UoVXPBK46rrk/OzGQiq", 1], [5, 140, 28], [0, 0, 0.5], [-96.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gold", 10, [[17, 1, -162, [13], 14], [16, 2, 8, -24, 6, 4, -163, 10]], [0, "d2TSzFHhdHzoECo4siePNC", 1], [5, 18, 18], [-23, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "middle", 1, [-164, 20], [0, "17zodagaRMO7r+PuSguPfL", 1], [5, 567, 447], [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "progress", 4, [-165, -166], [0, "c7ADMBPX1M17G57B1XMtIb", 1], [5, 115, 115]], [3, "progress", 5, [-167, -168], [0, "6cLwToSj5M9ZwPqOtQRYZK", 1], [5, 115, 115]], [2, "succLeft", 1, [[9, 12, 6, 59, -169]], [0, "61ZKBrpn5PTIBjaolthumd", 1], [5, 348, 201], [-460, -200.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "succRight", 1, [[61, 36, 6, 59, -170]], [0, "796aZAMhREupfn84s28t1a", 1], [5, 348, 201], [460, -200.5, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "hong", 3, [-172], [[1, -171, [68], 69]], [0, "66HlOliiFNHbqx685mQXRz", 1], [5, 349, 291], [-453.871, 125.149, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "normal", 42, [-173, 11], [0, "7dW/A9s3xJ4aFcLIMCLnUK", 1], [5, 222.7, 137.5], [0.1, -33.597, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 11, [[-174, [9, 12, 16, 4, -175]], 1, 4], [0, "44E/2mbnNBd6GvElYcjlmf", 1], [5, 140, 28], [0, 0, 0.5], [-96.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gold", 12, [[1, -176, [63], 64], [16, 2, 8, -24, 6, 4, -177, 12]], [0, "0aA8/58alLpprNtY1xs5YZ", 1], [5, 18, 18], [-23, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "lan", 3, [-179], [[1, -178, [77], 78]], [0, "30MxEPdDxCILJPa/kwQCg8", 1], [5, 349, 291], [455.358, 125.149, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "normal", 46, [-180, 13], [0, "90g9d/P0pCroXqt7K+vDSy", 1], [5, 222.7, 137.5], [0.1, -33.597, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "name", 13, [[-181, [9, 12, 16, 4, -182]], 1, 4], [0, "bcnHVF5xRMma61tcZnExDH", 1], [5, 140, 28], [0, 0, 0.5], [-96.5, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "gold", 14, [[1, -183, [72], 73], [16, 2, 8, -24, 6, 4, -184, 14]], [0, "dc6lpZ2P5GzLEVbWsfFHHL", 1], [5, 18, 18], [-23, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "chooseNode", 6, [[10, -185, [83]]], [0, "4f9QIneadCkYP4FkK1Tc7J", 1], [5, 100, 100], [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "cutDownNode", 6, [[7, 1, -186, [84]]], [0, "a31K7SW7ZIrKe+EDOp4CZf", 1], [5, 18, 18]], [3, "bg", 1, [-187], [0, "c2+gvmYIBPRZfq3qYuqA51", 1], [5, 1280, 720]], [10, 52, [0]], [3, "pic", 31, [-188], [0, "27Oa6KoUtEcqE8UslXmBj8", 1], [5, 82, 109]], [7, 1, 54, [1]], [31, 1, 16, 22, 1, 2, 32, [2]], [14, 1, "0", 14, 20, 1, 1, 8, [5]], [6, "bg", 17, [[8, 0, -189, [8], 9]], [0, "225/1LqhhGW7CJC47eeAsD", 1], [5, 225, 139]], [3, "pic", 34, [-190], [0, "47Nw1x+CFPaYMvQqimmiig", 1], [5, 82, 109]], [7, 1, 59, [11]], [31, 1, 16, 22, 1, 2, 35, [12]], [14, 1, "0", 14, 20, 1, 1, 10, [15]], [6, "bg", 19, [[8, 0, -191, [18], 19]], [0, "79DBaCNTBJMb5cb3i1d8Gm", 1], [5, 225, 139]], [5, "questionName", 37, [-192], [0, "c2X1v7hj9Mu6G4WKIMQ9pk", 1], [5, 360, 51], [0, 191.785, 0, 0, 0, 0, 1, 1, 1, 1]], [55, 1, 28, 1, 1, 1, 2, 64, [21]], [50, "image", false, 2, [-193], [0, "c97DmYZgVFMaQR15YueC0s", 1], [5, 140, 140], [0, -91, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "image", 66, [-194], [0, "15gBbNFBFOva2uCskw8GoF", 1], [5, 100, 100]], [7, 1, 67, [22]], [51, "<PERSON><PERSON><PERSON>", false, 2, [0, "e6b4HSXT1BopqXDZCEu3JR", 1], [5, 140, 50], [0, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [28, "item", 2, [-195], [0, "bbDXwhy3VMiLJWZjnqw7DT", 1], [4, 4280962690], [5, 436, 41], [0, 0.5, 1], [0, -21, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 1, 30, 32, 1, 1, 3, 70, [23]], [28, "item", 2, [-196], [0, "4fuE8kgb5CvbjnJPua3Laj", 1], [4, 4280962690], [5, 436, 41], [0, 0.5, 1], [0, -70, 0, 0, 0, 0, 1, 1, 1, 1]], [32, 1, 30, 32, 1, 1, 3, 72, [24]], [67, false, 0.75, 0.23, null, null, 20, 2], [6, "back", 4, [[8, 0, -197, [27], 28]], [0, "54NOgz8GFD/KRbkFFKJLZD", 1], [5, 115, 115]], [41, 3, 0, 2, 0.25, 1, 38, [29], [0, 0.5, 0.5]], [43, 2, 38, 76], [2, "res", 4, [[1, -198, [30], 31]], [0, "99/VdQByJLcYOpUd4AhtR6", 1], [5, 49, 64], [0, -5.289, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [5, "spine1", 4, [-199], [0, "b6dhkw9ydDybKXSHt7yn+f", 1], [5, 202, 120], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "default", 0, false, "animation", 1, 79, [32]], [5, "spine2", 4, [-200], [0, "25Ac00z/dCO5QV94pho87D", 1], [5, 202, 120], [-200, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [18, "default", 0, false, "animation", 1, 81, [33]], [2, "down", 24, [[1, -201, [34], 35]], [0, "e1ZzKrfAVEc7SIGbV/uPB8", 1], [5, 279, 94], [36.264, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "spine", 24, [-202], [0, "5fqboXHF5OyJb1ckfjqfTY", 1], [5, 54, 56], [-63.908, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "default", "animation", 0, false, "animation", 1, 84, [36]], [6, "New Sprite", 25, [[17, 1, -203, [39], 40]], [0, "cak/41Vv5ABI6H4o6Yzai6", 1], [5, 256, 124]], [29, "lab", 25, [-204], [0, "d1X501C9NJ7LKzpTC/XToJ", 1], [4, 4294901502], [5, 40, 0], [-4.175, 23.302, 0, 0, 0, 0, 1, 1, 1, 1]], [33, 10, 0, false, 5, 1, 1, 87, [41]], [3, "timeOut", 25, [-205], [0, "e56xupfS9Ci5XqLevWp/cE", 1], [5, 160, 57]], [34, "评分超时", 45, 1, 1, 1, 89, [42]], [6, "back", 5, [[8, 0, -206, [43], 44]], [0, "91LngYCMpH8oIpLQzCgOZ5", 1], [5, 115, 115]], [41, 3, 0, 2, 0.25, 1, 39, [45], [0, 0.5, 0.5]], [43, 2, 39, 92], [2, "res", 5, [[1, -207, [46], 47]], [0, "95OOANlkNOIoI+Sqb0rWST", 1], [5, 49, 64], [0, -5.289, 0, 0, 0, 0, 1, 1.2, 1.2, 1.2]], [5, "spine1", 5, [-208], [0, "7fj1qYhb5BKqgxvPgGZoXm", 1], [5, 202, 120], [200, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [18, "default", 0, false, "animation", 1, 95, [48]], [5, "spine2", 5, [-209], [0, "71XOj/Fu5FLaTirbw1zB8W", 1], [5, 202, 120], [-200, 0, 0, 0, 0, 0, 1, -1, 1, 1]], [18, "default", 0, false, "animation", 1, 97, [49]], [2, "down", 27, [[1, -210, [50], 51]], [0, "caqqPoYKRO5ZdZCF+azCAb", 1], [5, 279, 94], [36.264, 0, 0, 0, 0, 0, 1, 0.4, 0.4, 1]], [5, "spine", 27, [-211], [0, "02owhlAV5HkrwfjPZnCWep", 1], [5, 54, 56], [-63.908, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [44, "default", "animation", 0, false, "animation", 1, 100, [52]], [6, "New Sprite", 28, [[17, 1, -212, [55], 56]], [0, "67VE/pjO9Ad6z/KvnrW9C/", 1], [5, 256, 124]], [29, "lab", 28, [-213], [0, "30h2IIdL5DVZpIjckeTnFa", 1], [4, 4294901502], [5, 69, 0], [-4.175, 23.302, 0, 0, 0, 0, 1, 1, 1, 1]], [33, 10, 0, false, 5, 1, 1, 103, [57]], [3, "timeOut", 28, [-214], [0, "b3KVTygk5INoBI0kKpZ3Yd", 1], [5, 160, 57]], [34, "评分超时", 45, 1, 1, 1, 105, [58]], [30, "right", 3, [-215], [0, "92xO7AE8ZMm6/C2xFQCcTq", 1], [5, 711, 721], [0, 1, 0.5], [640, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, 107, [59]], [30, "left", 3, [-216], [0, "f8Dko4J6ZM9amyR8jz0kqV", 1], [5, 706, 722], [0, 0, 0.5], [-640, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, 1, 109, [60]], [3, "pic", 43, [-217], [0, "2dbtJOYPNO6YHeRvggN7Co", 1], [5, 82, 109]], [10, 111, [61]], [35, 16, 22, 1, 2, 44, [62]], [36, "0", 14, 20, 1, 1, 12, [65]], [3, "pic", 47, [-218], [0, "bfP3TAIwxGvpI0Wz/si9Q9", 1], [5, 82, 109]], [10, 115, [70]], [35, 16, 22, 1, 2, 48, [71]], [36, "0", 14, 20, 1, 1, 14, [74]], [5, "cent", 3, [-219], [0, "b72gWi8LJBqJ3INfIDmyWM", 1], [5, 521, 393], [-4.5, 51.751, 0, 0, 0, 0, 1, 1, 1, 1]], [10, 119, [79]], [68, 3, [80]], [21, "New Sprite(Splash)", 178.5, 6, [[8, 0, -220, [81], 82]], [0, "9a46T9hrREfpMC8/XqPFHL", 1], [4, 4278190080], [5, 1280, 720]], [21, "mask", 125.5, 29, [[8, 0, -221, [85], 86]], [0, "84YuloLDZBOI9hpE2zTr09", 1], [4, 4278190080], [5, 1280, 720]], [27, "title", 254, 15, [[14, 1, "提示", 36, 42, 1, 1, -222, [87]]], [0, "991quon8VENLdQBI+G+BAy", 1], [4, 4279505940], [5, 72, 53], [0, 103, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "label", 254, 15, [[14, 1, "本轮暂无同学上台哦~", 28, 36, 1, 1, -223, [88]]], [0, "72gzPtWDxM1pIo55pa6RKU", 1], [4, 4288913565], [5, 269, 46], [0, 17, 0, 0, 0, 0, 1, 1, 1, 1]], [21, "label", 254, 30, [[56, 1, "知道啦", 32, 1, 1, -224, [89]]], [0, "9bKeHCuD9H94+aVDEL/FZG", 1], [4, 4280891737], [5, 96, 51]]], 0, [0, 5, 1, 0, -1, 90, 0, -2, 106, 0, -1, 88, 0, -2, 104, 0, -1, 85, 0, -2, 101, 0, -1, 80, 0, -2, 82, 0, -3, 96, 0, -4, 98, 0, -1, 77, 0, -2, 93, 0, 8, 74, 0, 9, 108, 0, 10, 110, 0, 11, 121, 0, 12, 120, 0, 13, 29, 0, -1, 17, 0, -2, 19, 0, 14, 50, 0, 15, 51, 0, 16, 22, 0, 17, 6, 0, -1, 11, 0, -2, 13, 0, -1, 114, 0, -2, 118, 0, -1, 113, 0, -2, 117, 0, -1, 112, 0, -2, 116, 0, -1, 7, 0, -2, 9, 0, -1, 57, 0, -2, 62, 0, -1, 56, 0, -2, 61, 0, -1, 55, 0, -2, 60, 0, 18, 69, 0, 19, 2, 0, 20, 68, 0, 21, 73, 0, 22, 71, 0, 23, 41, 0, 24, 40, 0, 25, 18, 0, 26, 16, 0, 27, 65, 0, 28, 53, 0, 0, 1, 0, -1, 52, 0, -2, 16, 0, -3, 18, 0, -4, 37, 0, -5, 22, 0, -6, 40, 0, -7, 41, 0, -8, 3, 0, -9, 6, 0, -10, 29, 0, 0, 2, 0, 0, 2, 0, -1, 66, 0, -2, 69, 0, -3, 70, 0, -4, 72, 0, -1, 121, 0, 0, 3, 0, -1, 107, 0, -2, 109, 0, -3, 42, 0, -4, 46, 0, -5, 119, 0, -1, 75, 0, -2, 38, 0, -3, 78, 0, -4, 79, 0, -5, 81, 0, -1, 91, 0, -2, 39, 0, -3, 94, 0, -4, 95, 0, -5, 97, 0, 0, 6, 0, -1, 122, 0, -2, 50, 0, -3, 51, 0, 0, 7, 0, -1, 32, 0, -2, 8, 0, -1, 57, 0, 0, 8, 0, -1, 33, 0, 0, 9, 0, -1, 35, 0, -2, 10, 0, -1, 62, 0, 0, 10, 0, -1, 36, 0, 0, 11, 0, -1, 44, 0, -2, 12, 0, -1, 114, 0, 0, 12, 0, -1, 45, 0, 0, 13, 0, -1, 48, 0, -2, 14, 0, -1, 118, 0, 0, 14, 0, -1, 49, 0, 0, 15, 0, -1, 124, 0, -2, 125, 0, -3, 30, 0, -1, 31, 0, -2, 17, 0, 0, 17, 0, -1, 58, 0, -1, 34, 0, -2, 19, 0, 0, 19, 0, -1, 63, 0, 0, 20, 0, -2, 74, 0, -1, 21, 0, 0, 21, 0, 0, 21, 0, -1, 23, 0, -2, 26, 0, -2, 24, 0, -3, 25, 0, 0, 24, 0, -1, 83, 0, -2, 84, 0, -1, 86, 0, -2, 87, 0, -3, 89, 0, -2, 27, 0, -3, 28, 0, 0, 27, 0, -1, 99, 0, -2, 100, 0, -1, 102, 0, -2, 103, 0, -3, 105, 0, -1, 123, 0, 0, 30, 0, 0, 30, 0, -1, 126, 0, -1, 54, 0, -1, 56, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, -1, 59, 0, -1, 61, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, -1, 64, 0, -1, 76, 0, -2, 77, 0, -1, 92, 0, -2, 93, 0, 0, 40, 0, 0, 41, 0, 0, 42, 0, -1, 43, 0, -1, 111, 0, -1, 113, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, -1, 47, 0, -1, 115, 0, -1, 117, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 51, 0, -1, 53, 0, -1, 55, 0, 0, 58, 0, -1, 60, 0, 0, 63, 0, -1, 65, 0, -1, 67, 0, -1, 68, 0, -1, 71, 0, -1, 73, 0, 0, 75, 0, 0, 78, 0, -1, 80, 0, -1, 82, 0, 0, 83, 0, -1, 85, 0, 0, 86, 0, -1, 88, 0, -1, 90, 0, 0, 91, 0, 0, 94, 0, -1, 96, 0, -1, 98, 0, 0, 99, 0, -1, 101, 0, 0, 102, 0, -1, 104, 0, -1, 106, 0, -1, 108, 0, -1, 110, 0, -1, 112, 0, -1, 116, 0, -1, 120, 0, 0, 122, 0, 0, 123, 0, 0, 124, 0, 0, 125, 0, 0, 126, 0, 6, 1, 2, 3, 21, 4, 3, 23, 5, 3, 26, 7, 3, 31, 9, 3, 34, 11, 3, 43, 13, 3, 47, 15, 3, 29, 20, 3, 37, 224], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 60, 76, 80, 82, 85, 88, 92, 96, 98, 101, 104, 112, 116], [-1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, -1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, 1, -1, 1, -1, -1, -1, 1, -1, -1, -1, 1, -1, -1, -1, -1, 1, -1, 1, -1, -2, 29, 1, 1, 1, 4, 4, 4, 7, 1, 4, 4, 4, 7, 1, 1], [0, 0, 0, 0, 3, 0, 0, 4, 0, 6, 0, 0, 0, 0, 3, 0, 0, 4, 0, 6, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 0, 8, 1, 1, 0, 9, 1, 0, 10, 0, 11, 0, 0, 0, 7, 0, 0, 8, 1, 1, 0, 9, 1, 0, 10, 0, 11, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 4, 0, 25, 0, 0, 0, 3, 0, 0, 4, 0, 26, 0, 27, 0, 12, 0, 0, 0, 12, 0, 0, 0, 0, 28, 0, 29, 2, 30, 31, 2, 2, 13, 5, 5, 14, 15, 13, 5, 5, 14, 15, 2, 2]]]]