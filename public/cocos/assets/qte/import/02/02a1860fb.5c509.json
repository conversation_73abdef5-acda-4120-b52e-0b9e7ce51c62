[1, ["ecpdLyjvZBwrvm+cedCcQy", "4fU61AgYVIx6lKWw+s36nO", "20NTKzzBZH07AoPrmIqA6x", "fcwSYetNpAW63GnUW9TcjC", "14TijMUFdGWZRMe+xCRarW", "d9Xpu9bI5LnoPNlF0QbqKt", "14dLelSWhBgYpmXSJTSDpt", "3dIFLSz5hF0bn/0js0R4L5", "b8kH5t6udFnbvYB6+Yd597", "0cU2/oRs9Bs50nibSfIOBX", "677toz4qxLtJRw6OJ45CGu", "c24nBjKIxMfrGMajT2xRGM", "27EOJO421P/75P7debMQGF", "ffeRk4XpFJ4ply58MimDye", "6emzR4KehNFJoEtyxo7lfd", "25rSHinb5NKZERmEqh+e2C", "82yRt1j8JHCYmr3GaExsW8", "f6NLJxcZ9IXKYV5RwZPio2", "653p58vlZPfq8sjqGGzxxp", "047pUYQH5M/5pJKQ3sPyG1", "d6sQNQFDNK+bho7vPo29em", "5eCiKjuNRIbZ86hXB+Olj2", "6eCUqUl9pPx7X8EUAZ9zQo", "3cv7oq1udL3Y906mPZqxhH", "fdJ03c7VhA/oFxReU1acZk", "54889T5x1CFq4y1wNPELCS", "d4PO4aCKlLeYt844F+FDeD", "b8LWsYsu1LcI0pNp+xIfEA", "22oC4uNBFIFKhoH8X/vFlz", "5bqrmM/HROQ5iRYhX4/0hY", "5actZmPJ1PDIFc+mkBfD56", "9e0XtsN3NFgJfwIqzFrSmb", "8fy8jQWi1F6q6UyBQXO8He", "ddAiwohwxFJ72lvi2Pe+3Z", "f2Ng37IoZEBZkYPK8PCiYt", "e1ab+fGKZBw43PAFALIvHs", "ceA1UDZ5tE36oFV++KUvWX", "d0wel+n45LmIZMiTpJlDqH", "f5srLO2OVK/7VfPmXzxIN4", "59+UmhiRhBKYKUBju8OUNo", "665Oni/bNCXaOp9h71YNjf", "71mA+SU6NOpref4QHj+xpp", "ad76EtE8tMQIEHYP8oAZkX", "7d3R/gBXpDcLMPrQvliYre", "84AaLXDARFA4ujQZ7IAcX5", "77adJtEHlEcbSr1xPAbhub", "9dAJhv19JH4KLZVYhKBAdw", "8arOJ5vxdEFbkxF/ixoKuf", "41GhozDLZAkor2XcY4f8ua", "2cIOmmyG1GeLt1gBsaWJP+", "eaJ/l4mDZKcq3CQH7tAN5Q", "84BJNPwehKiJmq1E/JHXW8", "eeTKdQfGVNM7Te5ma93vLC", "26SEvIknhMAJvsXbCGowL7", "6fvKh4I2JFxZE6DrxSciT5", "ba2ZjylapMjrbBmWcLrAtJ", "14pOz02U9ESqyhByKFJvZt", "1caVDCyztJl4NkGdBd6MWs", "16cJQEUmRJgbtwEqcn6oy0", "86bApzBPtPv7qFdy1phDOI", "c4wDAUEuxARpsdJ6GEkmWq", "64qvLhvmxIebYQPEoRkkbk", "11+mUIACpMYK8HHtIUWVr2", "51SE0l18dMgLRQA1W5nGnT", "d1fNbIM/JDZow20cod22Ip", "58Q2iAciVBVI+5Ilw34sY6", "89OllmKPlL9a0wgZVtBnCh", "76lLYR3S1ITrGAGvw6lDDk", "2aF1+cdOJOJKt8FV0fCLLO"], ["node", "_textureSetter", "_spriteFrame", "root", "data", "_N$verticalScrollBar", "toastNode", "questionRootNode", "resultNode", "aViewNode", "qViewNode", "aScrollView", "qScrollView", "itemBigNode", "time<PERSON><PERSON><PERSON>", "topItemNode", "next<PERSON><PERSON><PERSON>", "previousBtn", "answerPrefab", "questionNum", "itemBigPrefab", "contentPrefab"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs", "_anchorPoint", "_color"], 0, 4, 5, 9, 1, 2, 7, 5, 5], ["cc.Widget", ["_alignFlags", "_left", "_originalHeight", "_originalWidth", "alignMode", "_top", "_bottom", "node"], -4, 1], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint", "_color"], 1, 1, 12, 4, 5, 7, 2, 5, 5], ["cc.Node", ["_name", "_opacity", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 1, 1, 2, 4, 5, 5, 7], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "node", "_layoutSize"], -1, 1, 5], ["cc.Prefab", ["_name"], 2], ["124e6JTiWZOPJuwqcwWSRzH", ["node", "topBtn", "previousBtn", "next<PERSON><PERSON><PERSON>", "topItemNode", "time<PERSON><PERSON><PERSON>", "optionBtnSp", "itemBigSp", "itemBigNode", "qScrollView", "aScrollView", "qViewNode", "aViewNode", "resultNode", "questionRootNode", "optionABSp", "toastNode", "answerPrefab", "questionNum", "itemBigPrefab", "contentPrefab"], 3, 1, 3, 1, 1, 1, 1, 3, 3, 1, 1, 1, 1, 1, 1, 1, 3, 1, 6, 6, 6, 6], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.<PERSON><PERSON>", ["_N$transition", "clickEvents", "node"], 1, 1], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$horizontalScrollBar", "node", "_N$content"], -2, 1, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "elastic", "bounceDuration", "_N$content", "_N$horizontalScrollBar", "node"], -3, 1], ["cc.<PERSON>", ["_N$direction", "node", "_scrollView", "_N$handle"], 2, 1, 1, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3]], [[6, 0, 1, 2], [3, 0, 1, 2, 3, 4, 5, 4], [3, 0, 3, 4, 5, 2], [18, 0, 1, 2, 3, 4, 5, 5], [1, 0, 6, 7, 5, 3, 4, 8, 2], [1, 0, 6, 5, 3, 4, 2], [8, 0, 2], [1, 0, 6, 5, 3, 4, 8, 2], [6, 1, 1], [2, 0, 3, 2, 7, 4], [4, 0, 2, 7, 3, 4, 5, 8, 6, 2], [12, 0, 1, 2, 2], [14, 0, 1, 2, 3], [1, 0, 6, 5, 3, 4, 9, 2], [1, 0, 1, 6, 7, 5, 3, 4, 8, 3], [1, 0, 6, 5, 3, 4, 9, 8, 2], [1, 0, 6, 5, 3, 10, 4, 8, 2], [2, 4, 0, 1, 2, 7, 5], [10, 0, 1], [3, 0, 1, 2, 3, 4, 4], [3, 0, 3, 4, 2], [11, 0, 1, 2, 2], [17, 0, 1, 2, 3, 2], [1, 0, 7, 5, 3, 4, 2], [1, 0, 1, 6, 7, 5, 3, 4, 3], [1, 0, 6, 7, 3, 4, 2], [1, 0, 6, 7, 5, 3, 4, 9, 2], [1, 0, 6, 7, 3, 4, 8, 2], [1, 0, 5, 3, 4, 9, 2], [1, 0, 7, 5, 3, 4, 8, 2], [1, 0, 2, 7, 5, 3, 4, 8, 3], [4, 0, 1, 2, 7, 3, 4, 5, 8, 6, 3], [4, 0, 2, 3, 4, 9, 5, 6, 2], [5, 0, 1, 2, 3, 4, 5, 6, 7, 3], [5, 0, 2, 3, 4, 5, 6, 7, 2], [5, 0, 2, 3, 4, 5, 2], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 1], [2, 0, 7, 2], [2, 0, 1, 5, 7, 4], [2, 0, 6, 7, 3], [2, 0, 1, 7, 3], [13, 0, 1, 2, 3], [7, 0, 1, 2, 4, 5, 4], [7, 0, 1, 3, 2, 4, 5, 5], [15, 0, 1, 2, 3, 4, 5, 6, 6], [16, 0, 1, 2, 3, 4, 5, 6, 7]], [[[[6, "ReadcomComponent"], [23, "ReadcomComponent", [-15, -16, -17, -18, -19, -20], [[36, -14, [38, 39, 40, 41, 42, 43], -13, -12, -11, -10, [45, 46, 47, 48], [50, 51], -9, -8, -7, -6, -5, -4, -3, [53, 54, 55, 56, 57, 58, 59, 60], -2, 37, 44, 49, 52]], [8, -1], [5, 1280, 720]], [24, "result", false, 1, [-23, -24, -25], [[37, 45, -21], [18, -22]], [0, "5aMXhcdV1GnLMExcMwWfYc", 1], [5, 1280, 720]], [25, "questionRoot", 1, [-26, -27, -28, -29], [0, "40MK6u30hOxJYXM0jme10a", 1], [5, 1280, 720]], [10, "questionNode", 3, [-32, -33], [[[1, 1, 1, 0, -30, [6], 7], -31], 4, 1], [0, "a68qHN43JLopUuKJvR3PAG", 1], [5, 726, 598], [0, 0, 1], [-616.136, 259.464, 0, 0, 0, 0, 1, 1, 1, 1]], [26, "view", 4, [-36], [[21, 0, -34, [4]], [9, 45, 240, 250, -35]], [0, "e8H7kS/b1FjJM4IREl3+EI", 1], [5, 726, 598], [0, 0, 1]], [10, "anwersNode", 3, [-39, -40], [[[1, 1, 1, 0, -37, [10], 11], -38], 4, 1], [0, "4d+Ctc7u5DJI6agzXU9mdt", 1], [5, 486, 598], [0, 0, 1], [130.732, 259.464, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "content", 5, [[9, 40, 220, 250, -42]], [0, "0b1dZ5CuhBdanIvFbADEg6", -41], [5, 726, 250], [0, 0, 1]], [31, "scrollBar", 0, 4, [-45], [[-43, [17, 0, 37, 350.07654921020657, 237, -44]], 1, 4], [0, "b660yavX5N0aEKJEBmFylZ", 1], [5, 12, 598], [0, 1, 0.5], [726, -299, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "view", 6, [[21, 0, -46, [8]], [9, 45, 240, 250, -47]], [0, "78jbPYepxM457NAcZPDNtn", 1], [5, 486, 598], [0, 0, 1]], [10, "scrollBar", 6, [-50], [[-48, [17, 0, 37, 350.07654921020657, 237, -49]], 1, 4], [0, "62SVAwe0NI66PJgfd5LSNF", 1], [5, 12, 598], [0, 1, 0.5], [486, -299, 0, 0, 0, 0, 1, 1, 1, 1]], [27, "bottom", 3, [-51, -52, -53], [0, "b9yePfBXxEJKFUOIOp0yJ1", 1], [5, 1280, 200], [0, -339.158, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "beforeBtn", 11, [[2, 1, -54, [14], 15], [11, 3, -55, [[12, "124e6JTiWZOPJuwqcwWSRzH", "previousButton", 1]]]], [0, "181jl45T9GQ6KpFExbFuAx", 1], [5, 64, 64], [351.802, 49.036, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "nextBtn", 11, [-58], [[1, 1, 1, 0, -56, [17], 18], [11, 3, -57, [[12, "124e6JTiWZOPJuwqcwWSRzH", "nextButton", 1]]]], [0, "8ewL3phmZF+7yTwqA46djQ", 1], [5, 176, 64], [504.581, 46.797, 0, 0, 0, 0, 1, 1, 1, 0]], [4, "bj", 2, [-60, -61], [[1, 1, 1, 0, -59, [27], 28]], [0, "6b31RmFzBJ+4KZ2OzYUdOS", 1], [5, 899, 256], [-1, 71, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "tip", 14, [-63, -64], [[38, 9, 18.69799999999998, 23.310000000000002, -62]], [0, "a6Iuzh+KhDZIBAyNybIT+2", 1], [5, 100, 50], [-380.802, 79.69, 0, 0, 0, 0, 1, 1, 1, 1]], [14, "bg_toast", false, 1, [-66], [[1, 1, 1, 0, -65, [35], 36]], [0, "0dJOlvFIpGP7pJRE212M/i", 1], [5, 339, 72], [0, 1, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "top", 3, [-68], [[39, 41, 360, -67]], [0, "15OCYj38JFZr0M3MFshWmD", 1], [5, 1280, 100], [0, 310, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "item", 17, [[42, 1, 1, 36, -69, [5, 48, 80]]], [0, "cbMx6WrzhJV5Ycu0EQOxQ5", 1], [5, 48, 80], [0, 0, 0.5], [-609.108, -3.972, 0, 0, 0, 0, 1, 1, 1, 1]], [44, false, 0.75, false, 0.23, null, 4, 7], [45, false, 0.75, false, 0, null, null, 6], [7, "bottomBg", 11, [[1, 1, 1, 0, -70, [12], 13], [18, -71]], [0, "d0/EgQKytJF72Qvv1foosZ", 1], [5, 486, 92], [373.024, 46, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "correct", 15, [-73], [[2, 1, -72, [22], 23]], [0, "a7W34Xcx9BCboBj6Vdeq1g", 1], [5, 16, 16], [-25.46, -3.247, 0, 0, 0, 0, 1, 1, 1, 1]], [4, "wrong", 15, [-75], [[2, 1, -74, [25], 26]], [0, "e99cr3Qy1NTp42H0ZEY6YQ", 1], [5, 16, 16], [61.662, -2.526, 0, 0, 0, 0, 1, 1, 1, 1]], [15, "item", 14, [[43, 1, 1, 20, 40, -76, [5, 580, 80]]], [0, "796iC4gFBB0o8en0C6+wuy", 1], [5, 580, 80], [0, 0, 0.5], [-435.627, -22.908, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "detailBtn", 2, [[2, 1, -77, [29], 30], [11, 3, -78, [[12, "124e6JTiWZOPJuwqcwWSRzH", "closeDetail", 1]]]], [0, "520ywahJFGtbGZPbqZfu4d", 1], [5, 396, 80], [0, -218.988, 0, 0, 0, 0, 1, 1, 1, 0]], [14, "timeIcon", false, 1, [-80], [[2, 1, -79, [32], 33]], [0, "fdnR7CeBFKFJsOv7tX2ynB", 1], [5, 30, 30], [440.023, 307.244, 0, 0, 0, 0, 1, 1, 1, 1]], [32, "time<PERSON><PERSON><PERSON>", 26, [[-81, [40, 8, 39.363, -82]], 1, 4], [0, "25mJSPo75AvbC8fdrWHTXy", 1], [4, 4279505939], [5, 76, 51], [62.363, 2.213, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 1, [[2, 1, -83, [0], 1]], [0, "basF0W5qRMkp6VgwrDcPFf", 1], [5, 1280, 720]], [5, "bian<PERSON>ang", 1, [[1, 1, 1, 0, -84, [2], 3]], [0, "70RACBNXJPyaNJQEIqc5mH", 1], [5, 1280, 720]], [33, "bar", 0, 8, [-85], [0, "f5jCLH+zFBupfgTmkieICS", 1], [5, 4, 172], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 1, 1, 0, 30, [5]], [22, 1, 8, 19, 31], [34, "bar", 10, [-86], [0, "c1emtFgbBAP6mSO/HH48DK", 1], [5, 4, 174], [0, 1, 0], [-1, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [19, 1, 1, 0, 33, [9]], [22, 1, 10, 20, 34], [35, "tip", 13, [-87], [0, "3fCkFwYT1FQ6SHPwYCUUfS", 1], [5, 84, 51]], [3, "下一题", 28, 1, 1, 36, [16]], [7, "title", 2, [[2, 1, -88, [19], 20]], [0, "deoVAC3tZHIInLkeLFHBSL", 1], [5, 272, 34], [0, 266.229, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "r<PERSON>abel", 22, [[3, "正确", 20, 1, 1, -89, [21]]], [0, "33IQbCrwpM/7L9Ck7XEPuI", 1], [4, 4284900966], [5, 40, 51], [34.651, 2.074, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "w<PERSON><PERSON><PERSON>", 23, [[3, "错误", 20, 1, 1, -90, [24]]], [0, "a23vmnJs9N2bkFKfXzM44N", 1], [4, 4284900966], [5, 40, 51], [35.605, 0.744, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "01:24", 30, 1, 1, 27, [31]], [5, "lab", 16, [[3, "请作答全部小题后提交～", 24, 1, 1, -91, [34]]], [0, "91W7Re3YNLbavK8h7ouLRc", 1], [5, 264, 51]]], 0, [0, 3, 1, 0, 6, 16, 0, 7, 3, 0, 8, 2, 0, 9, 9, 0, 10, 5, 0, 11, 20, 0, 12, 19, 0, 13, 24, 0, 14, 41, 0, 15, 18, 0, 16, 37, 0, 17, 12, 0, 0, 1, 0, -1, 28, 0, -2, 29, 0, -3, 3, 0, -4, 2, 0, -5, 26, 0, -6, 16, 0, 0, 2, 0, 0, 2, 0, -1, 38, 0, -2, 14, 0, -3, 25, 0, -1, 17, 0, -2, 4, 0, -3, 6, 0, -4, 11, 0, 0, 4, 0, -2, 19, 0, -1, 5, 0, -2, 8, 0, 0, 5, 0, 0, 5, 0, -1, 7, 0, 0, 6, 0, -2, 20, 0, -1, 9, 0, -2, 10, 0, 3, 7, 0, 0, 7, 0, -1, 32, 0, 0, 8, 0, -1, 30, 0, 0, 9, 0, 0, 9, 0, -1, 35, 0, 0, 10, 0, -1, 33, 0, -1, 21, 0, -2, 12, 0, -3, 13, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, -1, 36, 0, 0, 14, 0, -1, 15, 0, -2, 24, 0, 0, 15, 0, -1, 22, 0, -2, 23, 0, 0, 16, 0, -1, 42, 0, 0, 17, 0, -1, 18, 0, 0, 18, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, -1, 39, 0, 0, 23, 0, -1, 40, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, -1, 27, 0, -1, 41, 0, 0, 27, 0, 0, 28, 0, 0, 29, 0, -1, 31, 0, -1, 34, 0, -1, 37, 0, 0, 38, 0, 0, 39, 0, 0, 40, 0, 0, 42, 0, 4, 1, 19, 5, 32, 20, 5, 35, 91], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 34], [-1, 2, -1, 2, -1, -1, -1, 2, -1, -1, -1, 2, -1, 2, -1, 2, -1, -1, 2, -1, 2, -1, -1, 2, -1, -1, 2, -1, 2, -1, 2, -1, -1, 2, -1, -1, 2, 18, -1, -2, -3, -4, -5, -6, 19, -1, -2, -3, -4, 20, -1, -2, 21, -1, -2, -3, -4, -5, -6, -7, -8, 2, 2], [0, 3, 0, 4, 0, 0, 0, 1, 0, 0, 0, 1, 0, 5, 0, 6, 0, 0, 7, 0, 8, 0, 0, 9, 0, 0, 10, 0, 1, 0, 11, 0, 0, 12, 0, 0, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 2, 2]], [[{"name": "chooseMid4", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [38]], [[{"name": "correct", "rect": [0, 0, 16, 16], "offset": [0, 0], "originalSize": [16, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [39]], [[{"name": "bian<PERSON>ang", "rect": [0, 0, 248, 164], "offset": [0, 0], "originalSize": [248, 164], "capInsets": [38, 34, 38, 34]}], [0], 0, [0], [1], [40]], [[{"name": "beforeBtn", "rect": [0, 0, 64, 64], "offset": [0, 0], "originalSize": [64, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [41]], [[{"name": "wrongBig", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [42]], [[{"name": "chooseMid0", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [43]], [[{"name": "timeIcon", "rect": [3, 3, 30, 30], "offset": [0, 0], "originalSize": [36, 36], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [44]], [[{"name": "choose1", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [12, 11, 13, 12]}], [0], 0, [0], [1], [45]], [[{"name": "nextBtn", "rect": [0, 0, 176, 64], "offset": [0, 0], "originalSize": [176, 64], "capInsets": [32, 0, 37, 0]}], [0], 0, [0], [1], [46]], [[{"name": "bj", "rect": [0, 0, 320, 124], "offset": [0, 0], "originalSize": [320, 124], "capInsets": [23, 22, 26, 21]}], [0], 0, [0], [1], [47]], [[{"name": "choose3", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [14, 15, 10, 14]}], [0], 0, [0], [1], [48]], [[{"name": "A", "rect": [1, 9, 33, 24], "offset": [0.5, -1], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [49]], [[[6, "contentItem"], [28, "contentItem", [[9, 40, 220, 250, -2]], [8, -1], [5, 1280, 250], [0, 0, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 1, 2], [], [], []], [[[6, "questionNum"], [29, "questionNum", [-4], [[20, 1, -2, [1]], [41, 3, [null], -3]], [8, -1], [5, 48, 48], [24, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [5, "num", 1, [[3, "1", 32, 1, 1, -5, [0]]], [0, "dcGUbt3mdKDZ0bt9/2LipI", 1], [5, 18, 51]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 4, 1, 5], [0, 0], [-1, -1], [0, 0]], [[{"name": "chooseMid3", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [50]], [[{"name": "wrong", "rect": [0, 0, 16, 16], "offset": [0, 0], "originalSize": [16, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [51]], [[{"name": "chooseMid1", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [52]], [[{"name": "C", "rect": [1, 8, 33, 24], "offset": [0.5, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [53]], [[{"name": "B", "rect": [2, 8, 32, 24], "offset": [1, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [54]], [[{"name": "correctBig", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [55]], [[{"name": "title", "rect": [3, 19, 272, 34], "offset": [0, 0], "originalSize": [278, 72], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [56]], [[{"name": "detailBtn", "rect": [0, 0, 396, 80], "offset": [0, 0], "originalSize": [396, 80], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [57]], [[{"name": "G", "rect": [1, 8, 33, 24], "offset": [0.5, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [58]], [[{"name": "H", "rect": [2, 8, 32, 24], "offset": [1, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [59]], [[[6, "questionBigItme"], [30, "questionBigItme", 512, [-3], [[20, 1, -2, [1]]], [8, -1], [5, 80, 80], [60, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "num", 1, [[3, "1", 32, 1, 1, -4, [0]]], [0, "99qWA8mFZAJq0kR/P0p9Ci", 1], [5, 18, 51]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, 0, 2, 0, 4, 1, 4], [0, 0], [-1, -1], [0, 0]], [[{"name": "chooseMid5", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [60]], [[{"name": "bottomBg", "rect": [0, 0, 363, 88], "offset": [0, 0], "originalSize": [363, 88], "capInsets": [50, 0, 53, 20]}], [0], 0, [0], [1], [61]], [[{"name": "D", "rect": [2, 8, 32, 24], "offset": [1, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [62]], [[{"name": "F", "rect": [2, 8, 32, 24], "offset": [1, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [63]], [[{"name": "E", "rect": [2, 8, 32, 24], "offset": [1, 0], "originalSize": [34, 40], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [64]], [[{"name": "chooseMid2", "rect": [0, 0, 48, 48], "offset": [0, 0], "originalSize": [48, 48], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [65]], [[{"name": "bg", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [360, 72, 303, 109]}], [0], 0, [0], [1], [66]], [[{"name": "choose2", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [15, 12, 8, 16]}], [0], 0, [0], [1], [67]], [[{"name": "bg_toast", "rect": [0, 0, 180, 72], "offset": [0, 0], "originalSize": [180, 72], "capInsets": [44, 0, 35, 0]}], [0], 0, [0], [1], [68]]]]