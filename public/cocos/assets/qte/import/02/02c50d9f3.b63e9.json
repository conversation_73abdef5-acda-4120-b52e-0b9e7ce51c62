[1, ["62IskDv5NAOZa/bBPen3/2", "d1EqUd4VZHNLkn79g8Y/22", "095RJ4CjJORb1Jmofu6ZUl", "e2LU7L7UNO+r8dXD8aBkLV", "672np0ZTxJdoWm+V0/M9ns", "5cLzMUpxlEPKd+19EWM1nD", "aecvHJRiJHvJOCyyHh/btv", "1bEt9QXdNIiYVSd0UnllSG", "19jiSzRqVAE4g1YiJ96BHw", "75bC3j0CdFzILMGr/KPvGS", "eezBfSjRJF0LcFUIwu0+U0", "4a38JTQ9lO/qzYW97+sMDm", "20hgeM1LBEvaJP2DcgqvDR", "c74FDtYa9E9IdY8jrJiRDp", "937WVYOelHHa+lvYArRgqH", "543c+jfJhP/Z1HgER/UrOD", "b8CdOQgrBJw5pWw+0guW1N", "790vkNaFtBt6jwtOnvAj1T", "5anfyRTlxOnokun8t18ydj", "05rdZlTxFEvbfj92J4rwnc", "feEygQkFpGyJkr5Ojx3lmH", "627tSDrORAxazb+1nKXAf/", "e2qxXW4X9AkImR65Vpn8eQ", "0agPlkyxBI5aPZHZ/fFmPZ", "99eTV38KhP5qaMXlOa/8qR", "13r118eshOQZ0odsDExf4N", "cej/VL+9pP6ZWANVsD2gAJ", "3btmkKVv5Pp5HpwiUSZmEh", "e8mSUFFCxKfbr757C7/qrV", "97u0odPxROeIPezcvROgpj", "2bXHeMEotPC6BRmxT0JIgY", "e936HY5lFFZrNvuM0kREr+", "84x9W2hsJNNo9tMqtU2GmV", "bfG4nOOGBPhbRnyx8aiU+g", "71fSdS0BBAwZqAvAE3Fvbl", "31sTjwUDpC950oX9c8znBE", "f7w4EvzcpC87ZRe1pJNExg", "50pX8AhXtPybvXC+ACZNT1", "beEqgqJARG5K1P8Ryt9ROz", "efbWjCfuZPwrR1Ljv3LO0b", "fadDA8u51Ofol7PR6wTU4n", "46FA4hvCFMXIXW3qvp3Erj", "ffzzyurHRN5aRLoDfpXzX/", "13ZdzdJjtE8IL/Jl2YyITr", "901qhVHJBAOIFUE/ODs67s", "b11j461WdGaJNNa3Gnt3N1", "83vBizUC5MTZ3/VjgCNxCk", "aaF0u//HlE/akL2EQfy4Xn", "c8izX+r6lH26Q7s3Hn/Ntu", "e8cFhLDo1P7Yvh94DMRzL4"], ["_textureSetter", "value"], ["cc.SpriteFrame", ["cc.AnimationClip", ["_name", "_duration", "sample", "speed", "wrapMode", "curveData"], -2, 11]], [[1, 0, 1, 2, 3, 4, 5, 6]], [[[{"name": "p4", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [0]], [[{"name": "p8", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [1]], [[{"name": "p10", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [2]], [[{"name": "p15", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [3]], [[{"name": "p20", "rect": [0, 0, 28, 16], "offset": [0, 0], "originalSize": [28, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [4]], [[{"name": "p12", "rect": [0, 0, 28, 21], "offset": [0, 0], "originalSize": [28, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [5]], [[{"name": "p22", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [6]], [[{"name": "p0", "rect": [0, 0, 28, 21], "offset": [0, 0], "originalSize": [28, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [7]], [[{"name": "p3", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [8]], [[{"name": "p6", "rect": [0, 0, 28, 21], "offset": [0, 0], "originalSize": [28, 21], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [9]], [[{"name": "p19", "rect": [0, 0, 28, 16], "offset": [0, 0], "originalSize": [28, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [10]], [[{"name": "p2", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [11]], [[{"name": "p17", "rect": [0, 0, 28, 16], "offset": [0, 0], "originalSize": [28, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [12]], [[{"name": "p14", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [13]], [[{"name": "p9", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [14]], [[[0, "playing", 4.033333333333333, 30, 2.5, 2, [{}, "comps", 11, [{}, "cc.Sprite", 11, [{}, "spriteFrame", 12, [[[{"frame": 0}, "value", 6, 0], [{"frame": 0.16666666666666666}, "value", 6, 1], [{"frame": 0.3333333333333333}, "value", 6, 2], [{"frame": 0.5}, "value", 6, 3], [{"frame": 0.6666666666666666}, "value", 6, 4], [{"frame": 0.8333333333333334}, "value", 6, 5], [{"frame": 1}, "value", 6, 6], [{"frame": 1.1666666666666667}, "value", 6, 7], [{"frame": 1.3333333333333333}, "value", 6, 8], [{"frame": 1.5}, "value", 6, 9], [{"frame": 1.6666666666666667}, "value", 6, 10], [{"frame": 1.8333333333333333}, "value", 6, 11], [{"frame": 2}, "value", 6, 12], [{"frame": 2.1666666666666665}, "value", 6, 13], [{"frame": 2.3333333333333335}, "value", 6, 14], [{"frame": 2.5}, "value", 6, 15], [{"frame": 2.6666666666666665}, "value", 6, 16], [{"frame": 2.8333333333333335}, "value", 6, 17], [{"frame": 3}, "value", 6, 18], [{"frame": 3.1666666666666665}, "value", 6, 19], [{"frame": 3.3333333333333335}, "value", 6, 20], [{"frame": 3.5}, "value", 6, 21], [{"frame": 3.6666666666666665}, "value", 6, 22], [{"frame": 3.8333333333333335}, "value", 6, 23], [{"frame": 4}, "value", 6, 24]], 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11, 11]]]]]], 0, 0, [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39]], [[{"name": "p1", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [40]], [[{"name": "p23", "rect": [0, 0, 28, 19], "offset": [0, 0], "originalSize": [28, 19], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [41]], [[{"name": "p18", "rect": [0, 0, 28, 16], "offset": [0, 0], "originalSize": [28, 16], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [42]], [[{"name": "p11", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [43]], [[{"name": "p7", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [44]], [[{"name": "p13", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [45]], [[{"name": "p16", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [46]], [[{"name": "p24", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [47]], [[{"name": "p21", "rect": [0, 0, 28, 18], "offset": [0, 0], "originalSize": [28, 18], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [48]], [[{"name": "p5", "rect": [0, 0, 28, 20], "offset": [0, 0], "originalSize": [28, 20], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [49]]]]