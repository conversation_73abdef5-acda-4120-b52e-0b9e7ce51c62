[1, ["ecpdLyjvZBwrvm+cedCcQy", "e86jEOg/xI3aypnWhtbeiJ", "65kQCWow5GeZcK03N4t6rK", "40xGpWe2VLqZM8dW7Thvvz", "6eCUqUl9pPx7X8EUAZ9zQo"], ["node", "_textureSetter", "_spriteFrame", "root", "_parent", "data"], [["cc.Node", ["_name", "_active", "_prefab", "_contentSize", "_components", "_anchorPoint", "_parent", "_trs", "_children"], 1, 4, 5, 9, 5, 1, 7, 2], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize", "_anchorPoint"], 2, 1, 12, 9, 4, 5, 5], ["cc.Widget", ["_enabled", "_alignFlags", "_left", "_right", "_top", "_originalWidth", "node"], -3, 1]], [[3, 0, 1, 2], [4, 0, 2], [0, 0, 8, 4, 2, 3, 5, 2], [0, 0, 6, 2, 3, 5, 7, 2], [0, 0, 6, 4, 2, 3, 5, 7, 2], [0, 0, 1, 6, 4, 2, 3, 7, 3], [5, 0, 1, 2, 3, 4, 5, 6, 2], [1, 0, 1, 2, 3, 4, 5, 4], [1, 0, 3, 4, 2], [1, 3, 4, 5, 1], [3, 1, 1], [6, 0, 1, 2, 3, 4, 5, 6, 7]], [[[{"name": "noAnswer", "rect": [0, 0, 76, 28], "offset": [0, 0], "originalSize": [76, 28], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [1], [1]], [[{"name": "choose0", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [10, 14, 12, 12]}], [2], 0, [0], [1], [2]], [[[1, "anwerItem"], [2, "anwerItem", [-3, -4], [[7, 1, 1, 0, -2, [3], 4]], [10, -1], [5, 438, 300], [0, 0, 1]], [6, "mangerNode", 1, [[-6, [3, "itemPic", -7, [0, "4a5TVNsodDrKYj8dogCWNR", 1], [5, 372, 40], [0, 0, 1], [58.735, -12, 0, 0, 0, 0, 1, 1, 1, 1]]], 1, 4], [[11, false, 41, 16, 16, 16, 372, -5]], [0, "6bXO9qPNRG2YE6G40hGlvc", 1], [5, 406, 40], [0, 0, 1]], [4, "A", 2, [[8, 1, -8, [0]]], [0, "739c5+MLRIbKpuhsa2Np3I", 1], [5, 33, 24], [0, 0, 1], [16, -24, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "noAnswer", false, 1, [[9, -9, [1], 2]], [0, "fcSIaY0aJPxZ4aaEYxd7xt", 1], [5, 76, 28], [400.042, -13.99, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -2, 4, 0, 0, 2, 0, -1, 3, 0, 4, 2, 0, 0, 3, 0, 0, 4, 0, 5, 1, 9], [0, 0, 0, 0, 0], [-1, -1, 2, -1, 2], [0, 0, 3, 0, 4]]]]