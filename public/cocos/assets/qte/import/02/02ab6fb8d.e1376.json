[1, ["ecpdLyjvZBwrvm+cedCcQy", "f5bX0VZGJPfbCSUg3z5T+m", "5arfOv1DlGcpbLDGTa7BNS", "28b4bLttdGzLqsDysm3Mok", "89vWVrlgVFRKWNHKMs/Aea", "45o+1V9/hN9KAgCjHYjrqO", "d8FLlcWOhEe4iKc19s00jQ", "21SJbbx/tKEaSl3Yo1gA0/", "35ZEGkz7NJ2J2uue+RzRnS", "10kyOC49tAV6rOFZadv6bO", "b9UUYolSpE+7wsBE/FsHl8", "8efU2XLHhIKZKMLK5W9QT+", "b73/xq8npBu5+xdUNIhGfg", "7dySnqltxH1ozNKsCnwRZj", "c5VCWasNxM2INb12A2rAdA", "7beYzm1glHz6S50LuM226n", "fcKslN0dxFiIzp8+E274wN", "27kPBHbcRN0L+JImCXwglT", "c7xz0l4B1Ks7/sEO5MgII8"], ["node", "_textureSetter", "_spriteFrame", "root", "imgSp", "audioSp", "pbPointNode", "pb", "sumTimeLabel", "time<PERSON><PERSON><PERSON>", "textLabel", "textNode", "playNode", "_N$content", "data", "_parent"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_children", "_parent", "_trs", "_anchorPoint"], 1, 9, 4, 5, 2, 1, 7, 5], ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs", "_color"], 1, 1, 2, 4, 5, 5, 7, 5], ["cc.Label", ["_fontSize", "_string", "_N$verticalAlign", "_N$horizontalAlign", "_lineHeight", "_N$overflow", "_N$cacheMode", "node", "_materials"], -4, 1, 3], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs", "_children", "_anchorPoint"], 2, 1, 12, 4, 5, 7, 2, 5], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_N$layoutType", "_N$paddingTop", "_N$spacingY", "_resize", "_N$paddingBottom", "node", "_layoutSize"], -2, 1, 5], ["cc.Prefab", ["_name"], 2], ["e8348sA1F1PvJ872ecWkifY", ["node", "playNode", "textNode", "textLabel", "time<PERSON><PERSON><PERSON>", "sumTimeLabel", "pb", "pbPointNode", "audioIcon", "audioSp", "imgSp"], 3, 1, 1, 1, 1, 1, 1, 1, 1, 3, 1, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["<PERSON><PERSON>", ["horizontal", "brake", "bounceDuration", "cancelInnerEvents", "_N$horizontalScrollBar", "_N$verticalScrollBar", "node", "_N$content"], -3, 1, 1], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.Mask", ["_N$alphaThreshold", "node", "_materials"], 2, 1, 3], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1], ["cc.ProgressBar", ["_N$totalLength", "_N$progress", "node", "_N$barSprite"], 1, 1, 1]], [[6, 0, 1, 2], [2, 0, 1, 2, 3, 4, 5, 4], [1, 0, 6, 5, 2, 3, 4, 2], [3, 0, 2, 3, 4, 8, 5, 6, 7, 2], [2, 0, 3, 4, 5, 2], [12, 0, 1, 2, 3, 4], [8, 0, 2], [1, 0, 5, 2, 3, 4, 2], [1, 0, 5, 2, 3, 4, 7, 2], [1, 0, 1, 6, 5, 2, 3, 4, 7, 3], [1, 0, 6, 5, 2, 3, 4, 8, 7, 2], [1, 0, 6, 2, 3, 4, 2], [5, 0, 1, 6, 2, 3, 4, 7, 5, 2], [5, 0, 1, 2, 3, 4, 5, 2], [3, 0, 1, 2, 3, 4, 5, 6, 3], [3, 0, 2, 3, 4, 5, 7, 2], [2, 0, 3, 4, 2], [2, 0, 1, 2, 3, 4, 4], [2, 3, 4, 1], [9, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 1], [10, 0, 1], [6, 1, 1], [7, 0, 1, 2, 5, 4], [7, 3, 0, 1, 4, 5, 6, 5], [11, 0, 1, 2, 3, 4, 5, 6, 7, 7], [13, 0, 1, 2, 2], [14, 0, 1, 2, 2], [15, 0, 1, 2, 3], [16, 0, 1, 2, 3, 3], [4, 1, 0, 2, 7, 8, 4], [4, 1, 0, 3, 2, 7, 8, 5], [4, 0, 4, 5, 6, 7, 8, 5]], [[[{"name": "pgPoint", "rect": [0, 0, 32, 32], "offset": [0, 0], "originalSize": [32, 32], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "pause", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [3]], [[{"name": "analysisBg", "rect": [0, 0, 407, 255], "offset": [0, 0], "originalSize": [407, 255], "capInsets": [95, 73, 85, 66]}], [0], 0, [0], [1], [4]], [[{"name": "playBg", "rect": [0, 0, 400, 96], "offset": [0, 0], "originalSize": [400, 96], "capInsets": [90, 0, 50, 0]}], [0], 0, [0], [1], [5]], [[{"name": "analysisBgH", "rect": [0, 0, 373, 218], "offset": [0, 0], "originalSize": [373, 218], "capInsets": [48, 50, 69, 48]}], [0], 0, [0], [1], [6]], [[{"name": "pgBg", "rect": [0, 0, 120, 8], "offset": [0, 0], "originalSize": [120, 8], "capInsets": [5, 0, 5, 0]}], [0], 0, [0], [1], [7]], [[{"name": "bg", "rect": [0, 0, 1280, 720], "offset": [0, 0], "originalSize": [1280, 720], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [8]], [[[6, "<PERSON><PERSON><PERSON><PERSON>"], [7, "<PERSON><PERSON><PERSON><PERSON>", [-14, -15], [[4, 1, -2, [17], 18], [19, -12, -11, -10, -9, -8, -7, -6, -5, [19, 20], -4, -3], [20, -13]], [21, -1], [5, 1280, 720]], [8, "playNode", [-17, -18, -19, -20], [[1, 1, 1, 0, -16, [8], 9]], [0, "2eWo5yXfNFqocxNA+VWlrq", 1], [5, 932, 96], [0, 224, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "analysisNode", 1, [2, -23], [[1, 1, 1, 0, -21, [14], 15], [22, 2, 40, 32, -22]], [0, "24MYz3geRD2IJoEVEzkuNx", 1], [5, 1012, 624]], [12, "pg", 2, [-26, -27], [[[1, 1, 1, 0, -24, [4], 5], -25], 4, 1], [0, "b7f5WscbdJSZBK7dbvezr7", 1], [5, 552, 8], [0, 0, 0.5], [-341.528, 1.105, 0, 0, 0, 0, 1, 1, 1, 1]], [9, "analysisBgH", false, 3, [-29], [[1, 1, 1, 0, -28, [12], 13]], [0, "26/T6WoaNFKJWAWNTIkt/b", 1], [5, 932, 416], [0, 64, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "list", 5, [-33], [[24, false, 0.75, 0.23, false, null, null, -31, -30], [5, 45, 240, 250, -32]], [0, "84fCdTUU1K1I1wwGU0WdZc", 1], [5, 932, 416]], [2, "view", 6, [-36], [[25, 0, -34, [11]], [5, 45, 240, 250, -35]], [0, "0asSxdAnBO/5OlbB4s6lE8", 1], [5, 932, 416]], [10, "content", 7, [-38], [[23, 1, 2, 40, 40, -37, [5, 852, 128]]], [0, "8a6ToVkP1BJ5OgMRu15UHP", 1], [5, 852, 128], [0, 0, 1], [-426, 207.652, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "play", 2, [[-39, [26, 3, -40, [[27, "e8348sA1F1PvJ872ecWkifY", "playAudio", 1]]]], 1, 4], [0, "03kNuJuWpPkLVVRutDFXOZ", 1], [5, 72, 72], [-419.418, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [11, "pgPoint", 4, [[4, 1, -41, [2], 3]], [0, "310NW/RctDjZ+JzEtbMK2G", 1], [5, 32, 32]], [16, 1, 9, [0]], [14, "bar", 512, 4, [-42], [0, "56YzE8JcNCpJAbIn63Yaf4", 1], [5, 0, 8], [0, 0, 0.5]], [17, 1, 1, 0, 12, [1]], [28, 552, 0, 4, 13], [3, "sumLabel", 2, [-43], [0, "5bKmar0HNMoaiBa9Oq5bHx", 1], [4, 4288913565], [5, 94, 51], [0, 0, 0.5], [333.638, 0.654, 0, 0, 0, 0, 1, 1, 1, 1]], [29, "/  00:00", 28, 1, 15, [6]], [3, "cur<PERSON><PERSON>l", 2, [-44], [0, "04AIU9k5lNZIDjVsPNmli3", 1], [4, 4279505940], [5, 71, 51], [0, 1, 0.5], [322.777, 0.654, 0, 0, 0, 0, 1, 1, 1, 1]], [30, "00:00", 28, 2, 1, 17, [7]], [3, "item", 8, [-45], [0, "51aC4UMqpCTZoyLz3HNq+p", 1], [4, 4278190080], [5, 852, 48], [0, 0, 1], [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [31, 32, 48, 3, 2, 19, [10]], [15, "Image", 1, [-46], [0, "f451B9qm9E5r4lDMkZfZhJ", 1], [5, 932, 300], [0, -64, 0, 0, 0, 0, 1, 1, 1, 0.7938]], [18, 21, [16]]], 0, [0, 3, 1, 0, 0, 1, 0, 4, 22, 0, 5, 11, 0, 6, 10, 0, 7, 14, 0, 8, 16, 0, 9, 18, 0, 10, 20, 0, 11, 5, 0, 12, 2, 0, 0, 1, 0, 0, 1, 0, -1, 3, 0, -2, 21, 0, 0, 2, 0, -1, 9, 0, -2, 4, 0, -3, 15, 0, -4, 17, 0, 0, 3, 0, 0, 3, 0, -2, 5, 0, 0, 4, 0, -2, 14, 0, -1, 12, 0, -2, 10, 0, 0, 5, 0, -1, 6, 0, 13, 8, 0, 0, 6, 0, 0, 6, 0, -1, 7, 0, 0, 7, 0, 0, 7, 0, -1, 8, 0, 0, 8, 0, -1, 19, 0, -1, 11, 0, 0, 9, 0, 0, 10, 0, -1, 13, 0, -1, 16, 0, -1, 18, 0, -1, 20, 0, -1, 22, 0, 14, 1, 2, 15, 3, 46], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 13], [-1, -1, -1, 2, -1, 2, -1, -1, -1, 2, -1, -1, -1, 2, -1, 2, -1, -1, 2, -1, -2, 2, 2], [0, 0, 0, 9, 0, 10, 0, 0, 0, 11, 0, 0, 0, 12, 0, 13, 0, 0, 14, 1, 15, 1, 16]], [[{"name": "play", "rect": [0, 0, 72, 72], "offset": [0, 0], "originalSize": [72, 72], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [17]], [[{"name": "pgBar", "rect": [0, 0, 120, 8], "offset": [0, 0], "originalSize": [120, 8], "capInsets": [5, 0, 5, 0]}], [0], 0, [0], [1], [18]]]]