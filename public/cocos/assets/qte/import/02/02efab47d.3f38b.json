[1, ["ecpdLyjvZBwrvm+cedCcQy", "0aEFOt2wdCa7ek5w83H69M", "02o8C7QCFE3acyKiZ978ZE", "209Zm9hadMzrZG3yLGoU9y", "92OX69fdxHQIF5q0qnWzWv", "28I+dqLaBK34vTqQxTPEkc", "f6TbwoljRBjLwieVF2Egn6", "71MtKW5PFHBbbVMXBLAQ/a", "dbKT9LtN9KQ5H/eXOHZ1Q1", "c4Y0U0I3hP3LLMk1NpU+V4", "a27csnxzZKRrrUPqVAv+Rb", "6fRqYC9DVDwoIDtf6+tkVy", "50dR8lf/ZFP6lHzkfqZv1H", "71fgeZBwVDBqaPLcK8KGoT", "03s45fj2dO/KOrLYXp2le/"], ["node", "_textureSetter", "_spriteFrame", "root", "data", "navNode", "suoxiaoNode", "zhankaiNode", "rightNode", "numLabel", "leftNode", "clickPre", "errorSf0", "errorSf1", "rightSf0", "rightSf1"], [["cc.Node", ["_name", "_active", "_components", "_prefab", "_contentSize", "_parent", "_trs", "_children", "_anchorPoint"], 1, 9, 4, 5, 1, 7, 2, 5], "cc.SpriteFrame", ["cc.Sprite", ["_srcBlendFactor", "_type", "_sizeMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "_N$paddingLeft", "_N$paddingRight", "node", "_layoutSize"], -2, 1, 5], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents"], 2, 1, 9], ["cc.Widget", ["_alignFlags", "_top", "_left", "_right", "node"], -1, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_srcBlendFactor", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["d22c0LhaelLqY0gVDspMiQG", ["node", "leftNode", "numLabel", "rightNode", "zhankaiNode", "suoxiaoNode", "navNode", "clickPre", "errorSf0", "errorSf1", "rightSf0", "rightSf1"], 3, 1, 1, 1, 1, 1, 1, 1, 6, 6, 6, 6, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[3, 0, 1, 2], [2, 0, 3, 4, 5, 2], [0, 0, 5, 2, 3, 4, 6, 2], [5, 0, 1, 2, 2], [11, 0, 1, 2, 3], [8, 0, 2], [0, 0, 5, 2, 3, 4, 2], [3, 1, 1], [0, 0, 7, 2, 3, 4, 2], [0, 0, 5, 7, 2, 3, 4, 8, 6, 2], [0, 0, 1, 5, 7, 2, 3, 4, 6, 3], [0, 0, 5, 2, 3, 4, 8, 6, 2], [0, 0, 7, 2, 3, 4, 6, 2], [9, 0, 1, 2, 3, 4, 5, 2], [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1], [2, 0, 1, 2, 3, 4, 5, 4], [2, 3, 4, 1], [4, 0, 1, 3, 4, 2, 5, 6, 6], [4, 0, 1, 2, 5, 6, 4], [5, 0, 1, 2], [6, 0, 2, 1, 4, 4], [6, 0, 3, 1, 4, 4], [7, 0, 1, 2, 3, 5, 6, 5], [7, 4, 0, 1, 2, 3, 5, 6, 6]], [[[{"name": "bt_jian<PERSON>ou", "rect": [0, 0, 16, 20], "offset": [0, 0], "originalSize": [16, 20], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [4]], [[{"name": "change_question_bt", "rect": [0, 0, 80, 80], "offset": [0, 0], "originalSize": [80, 80], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [5]], [[{"name": "bt_right1", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [6]], [[{"name": "bt_right0", "rect": [4, 4, 32, 32], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [7]], [[[5, "serialChangeUI"], [8, "serialChangeUI", [-9, -10, -11, -12], [[14, -8, -7, -6, -5, -4, -3, -2, 13, 14, 15, 16, 17]], [7, -1], [5, 1280, 720]], [9, "bg_broderW", 1, [-15, -16], [[15, 1, 1, 0, -13, [2], 3], [17, 1, 1, 18, 25, 16, -14, [5, 75, 59]]], [0, "bak70SMhREvrn2WxiyG7vh", 1], [5, 75, 59], [0, 0, 0.5], [-640, 296.5, 0, 0, 0, 0, 1, 1, 1, 1]], [10, "bg_broder", false, 1, [-18, -19], [[1, 1, -17, [7], 8]], [0, "1byz51rZpNHJqUd9BKAr3j", 1], [5, 151, 59], [-564.5, 296.5, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "left", 1, [[1, 1, -20, [9], 10], [3, 3, -21, [[4, "d22c0LhaelLqY0gVDspMiQG", "preChange", 1]]], [20, 9, 30, 388, -22]], [0, "8fa1EbdpFDK60PUQ4MwsfU", 1], [5, 80, 80], [-570, -68, 0, 0, 0, 0, 1, 1, 1, 0]], [2, "right", 1, [[1, 1, -23, [11], 12], [3, 3, -24, [[4, "d22c0LhaelLqY0gVDspMiQG", "nextChange", 1]]], [21, 33, 30, 388, -25]], [0, "d6wsyl37ZCOqjtYKLiMPb/", 1], [5, 80, 80], [570, -68, 0, 0, 0, 0, 1, -1, 1, 0]], [11, "navNode", 2, [[18, 1, 1, 37.32, -26, [5, 0, 59]]], [0, "b3o0th3M9LPonWOF8NDZ/V", 1], [5, 0, 59], [0, 0, 0.5], [18, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [2, "bt_jian<PERSON>ou", 2, [[1, 1, -27, [0], 1], [3, 3, -28, [[4, "d22c0LhaelLqY0gVDspMiQG", "su<PERSON>iao", 1]]]], [0, "69lbldT9dPoYuSXfshO42g", 1], [5, 16, 20], [42, 0, 0, 0, 0, 0, 1, -1, 1, 0]], [2, "bt_jian<PERSON>ou", 3, [[1, 1, -29, [4], 5], [3, 3, -30, [[4, "d22c0LhaelLqY0gVDspMiQG", "zhanKai", 1]]]], [0, "f06tp9zsZN3Zn8ig8TO5H9", 1], [5, 16, 20], [36.5, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [13, "labelNum", 3, [-31], [0, "91x0+1uoFLuoKInQ3SAwn+", 1], [5, 45, 51], [-17, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [22, "0/0", 32, 1, 1, 9, [6]]], 0, [0, 3, 1, 0, 5, 6, 0, 6, 3, 0, 7, 2, 0, 8, 5, 0, 9, 10, 0, 10, 4, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, 0, 2, 0, -1, 6, 0, -2, 7, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, 0, 4, 0, 0, 4, 0, 0, 4, 0, 0, 5, 0, 0, 5, 0, 0, 5, 0, 0, 6, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, -1, 10, 0, 4, 1, 31], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, 2, -1, 2, -1, -1, 2, -1, 2, -1, 2, 11, 12, 13, 14, 15], [0, 1, 0, 2, 0, 1, 0, 0, 2, 0, 3, 0, 3, 8, 9, 10, 11, 12]], [[{"name": "bt_error1", "rect": [0, 0, 40, 40], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [13]], [[{"name": "bt_error0", "rect": [4, 4, 32, 32], "offset": [0, 0], "originalSize": [40, 40], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [14]], [[[5, "orderNode"], [12, "orderNode", [-3, -4], [[19, 3, -2]], [7, -1], [5, 40, 40], [33, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [6, "bg", 1, [[16, -5, [0]]], [0, "782UIEKxZF84cdrffNWZcb", 1], [5, 40, 40]], [6, "label", 1, [[23, 1, "1", 19.8, 1, 1, -6, [1]]], [0, "874CE7wZBNlYySzWcNHKjo", 1], [5, 12, 51]]], 0, [0, 3, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, 0, 2, 0, 0, 3, 0, 4, 1, 6], [0, 0], [-1, -1], [0, 0]]]]