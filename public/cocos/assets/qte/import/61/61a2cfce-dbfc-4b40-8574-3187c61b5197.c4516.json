[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "svg.d", "/*\n * @FilePath     : /src/component/svg/svg.d.ts\n * <AUTHOR> wanghuan\n * @description  : svg 配置文件\n * @warn         :\n */\n\ndeclare namespace svg {\n  export class RaphaelComponent extends cc.Component {\n    onLoad(): void;\n    svgText: cc.TextAsset;\n    fillColor: cc.Color;\n    strokeColor: cc.Color;\n    lineWidth: number;\n    onInit(): void;\n    setSvgScale(delta): void;\n    setSvgScaleX(delta): void;\n    setSvgScaleY(delta): void;\n    refreshSvg(): void;\n    changeFillColor(color: cc.Color): void;\n    changeStrokeColor(color: cc.Color): void;\n    changeStrokeWidth(width: number): void;\n    changeFillColorByIndex(color: cc.Color, index: number): void;\n    changeStrokeColorByIndex(color: cc.Color, index: number): void;\n    changeStrokeWidthByIndex(width: number, index: number): void;\n\n    /**\n     * 获得编辑页面8个坐标\n     *  2   3   4\n     *  1       5\n     *  0   7   6\n     */\n    getEditorPos(): void;\n    /**\n     * 获得当前图形Rect\n     */\n    getShapeRect(): cc.Rect;\n    /**\n     * 修改图形顶点位置\n     * @param index 选择的点位\n     * @param offsetX\n     * @param offsetY\n     */\n    changeShape(index, wordPos): void;\n    setUpdateAnchor(isTrue = false): void;\n    /**\n     * 获得图形顶点坐标\n     */\n    getVertexPoss(): Array<cc.Vec2>;\n  }\n}\n"]], 0, 0, [], [], []]