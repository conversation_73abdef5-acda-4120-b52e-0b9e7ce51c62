[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "CONTRIBUTING", "# Contribution\n\n# Git Flow \n\nThe crypto-js project uses [git flow](https://github.com/nvie/gitflow) to manage branches. \nDo your changes on the `develop` or even better on a `feature/*` branch. Don't do any changes on the `master` branch.\n\n# Pull request\n\nTarget your pull request on `develop` branch. Other pull request won't be accepted.\n\n# How to build\n\n1. Clone\n\n2. Run\n\n    ```sh\n    npm install\n    ```\n\n3. Run\n\n    ```sh\n    npm run build\n    ```\n    \n4. Check `build` folder"]], 0, 0, [], [], []]