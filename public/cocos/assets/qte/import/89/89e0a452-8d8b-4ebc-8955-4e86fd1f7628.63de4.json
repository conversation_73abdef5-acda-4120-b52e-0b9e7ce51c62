[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "README", "# crypto-js [![Build Status](https://travis-ci.org/brix/crypto-js.svg?branch=develop)](https://travis-ci.org/brix/crypto-js)\n\nJavaScript library of crypto standards.\n\n## Node.js (Install)\n\nRequirements:\n\n- Node.js\n- npm (Node.js package manager)\n\n```bash\nnpm install crypto-js\n```\n\n### Usage\n\nES6 import for typical API call signing use case:\n\n```javascript\nimport sha256 from 'crypto-js/sha256';\nimport hmacSHA512 from 'crypto-js/hmac-sha512';\nimport Base64 from 'crypto-js/enc-base64';\n\nconst message, nonce, path, privateKey; // ...\nconst hashDigest = sha256(nonce + message);\nconst hmacDigest = Base64.stringify(hmacSHA512(path + hashDigest, privateKey));\n```\n\nModular include:\n\n```javascript\nvar AES = require(\"crypto-js/aes\");\nvar SHA256 = require(\"crypto-js/sha256\");\n...\nconsole.log(SHA256(\"Message\"));\n```\n\nIncluding all libraries, for access to extra methods:\n\n```javascript\nvar CryptoJS = require(\"crypto-js\");\nconsole.log(CryptoJS.HmacSHA1(\"Message\", \"Key\"));\n```\n\n## Client (browser)\n\nRequirements:\n\n- Node.js\n- Bower (package manager for frontend)\n\n```bash\nbower install crypto-js\n```\n\n### Usage\n\nModular include:\n\n```javascript\nrequire.config({\n    packages: [\n        {\n            name: 'crypto-js',\n            location: 'path-to/bower_components/crypto-js',\n            main: 'index'\n        }\n    ]\n});\n\nrequire([\"crypto-js/aes\", \"crypto-js/sha256\"], function (AES, SHA256) {\n    console.log(SHA256(\"Message\"));\n});\n```\n\nIncluding all libraries, for access to extra methods:\n\n```javascript\n// Above-mentioned will work or use this simple form\nrequire.config({\n    paths: {\n        'crypto-js': 'path-to/bower_components/crypto-js/crypto-js'\n    }\n});\n\nrequire([\"crypto-js\"], function (CryptoJS) {\n    console.log(CryptoJS.HmacSHA1(\"Message\", \"Key\"));\n});\n```\n\n### Usage without RequireJS\n\n```html\n<script type=\"text/javascript\" src=\"path-to/bower_components/crypto-js/crypto-js.js\"></script>\n<script type=\"text/javascript\">\n    var encrypted = CryptoJS.AES(...);\n    var encrypted = CryptoJS.SHA256(...);\n</script>\n```\n\n## API\n\nSee: https://cryptojs.gitbook.io/docs/\n\n### AES Encryption\n\n#### Plain text encryption\n\n```javascript\nvar CryptoJS = require(\"crypto-js\");\n\n// Encrypt\nvar ciphertext = CryptoJS.AES.encrypt('my message', 'secret key 123').toString();\n\n// Decrypt\nvar bytes  = CryptoJS.AES.decrypt(ciphertext, 'secret key 123');\nvar originalText = bytes.toString(CryptoJS.enc.Utf8);\n\nconsole.log(originalText); // 'my message'\n```\n\n#### Object encryption\n\n```javascript\nvar CryptoJS = require(\"crypto-js\");\n\nvar data = [{id: 1}, {id: 2}]\n\n// Encrypt\nvar ciphertext = CryptoJS.AES.encrypt(JSON.stringify(data), 'secret key 123').toString();\n\n// Decrypt\nvar bytes  = CryptoJS.AES.decrypt(ciphertext, 'secret key 123');\nvar decryptedData = JSON.parse(bytes.toString(CryptoJS.enc.Utf8));\n\nconsole.log(decryptedData); // [{id: 1}, {id: 2}]\n```\n\n### List of modules\n\n\n- ```crypto-js/core```\n- ```crypto-js/x64-core```\n- ```crypto-js/lib-typedarrays```\n\n---\n\n- ```crypto-js/md5```\n- ```crypto-js/sha1```\n- ```crypto-js/sha256```\n- ```crypto-js/sha224```\n- ```crypto-js/sha512```\n- ```crypto-js/sha384```\n- ```crypto-js/sha3```\n- ```crypto-js/ripemd160```\n\n---\n\n- ```crypto-js/hmac-md5```\n- ```crypto-js/hmac-sha1```\n- ```crypto-js/hmac-sha256```\n- ```crypto-js/hmac-sha224```\n- ```crypto-js/hmac-sha512```\n- ```crypto-js/hmac-sha384```\n- ```crypto-js/hmac-sha3```\n- ```crypto-js/hmac-ripemd160```\n\n---\n\n- ```crypto-js/pbkdf2```\n\n---\n\n- ```crypto-js/aes```\n- ```crypto-js/tripledes```\n- ```crypto-js/rc4```\n- ```crypto-js/rabbit```\n- ```crypto-js/rabbit-legacy```\n- ```crypto-js/evpkdf```\n\n---\n\n- ```crypto-js/format-openssl```\n- ```crypto-js/format-hex```\n\n---\n\n- ```crypto-js/enc-latin1```\n- ```crypto-js/enc-utf8```\n- ```crypto-js/enc-hex```\n- ```crypto-js/enc-utf16```\n- ```crypto-js/enc-base64```\n\n---\n\n- ```crypto-js/mode-cfb```\n- ```crypto-js/mode-ctr```\n- ```crypto-js/mode-ctr-gladman```\n- ```crypto-js/mode-ofb```\n- ```crypto-js/mode-ecb```\n\n---\n\n- ```crypto-js/pad-pkcs7```\n- ```crypto-js/pad-ansix923```\n- ```crypto-js/pad-iso10126```\n- ```crypto-js/pad-iso97971```\n- ```crypto-js/pad-zeropadding```\n- ```crypto-js/pad-nopadding```\n\n\n## Release notes\n\n### 4.0.0\n\nThis is an update including breaking changes for some environments.\n\nIn this version `Math.random()` has been replaced by the random methods of the native crypto module.\n\nFor this reason CryptoJS might does not run in some JavaScript environments without native crypto module. Such as IE 10 or before or React Native.\n\n### 3.3.0\n\nRollback, `3.3.0` is the same as `3.1.9-1`.\n\nThe move of using native secure crypto module will be shifted to a new `4.x.x` version. As it is a breaking change the impact is too big for a minor release.\n\n### 3.2.1\n\nThe usage of the native crypto module has been fixed. The import and access of the native crypto module has been improved.\n\n### 3.2.0\n\nIn this version `Math.random()` has been replaced by the random methods of the native crypto module.\n\nFor this reason CryptoJS might does not run in some JavaScript environments without native crypto module. Such as IE 10 or before.\n\nIf it's absolute required to run CryptoJS in such an environment, stay with `3.1.x` version. Encrypting and decrypting stays compatible. But keep in mind `3.1.x` versions still use `Math.random()` which is cryptographically not secure, as it's not random enough. \n\nThis version came along with `CRITICAL` `BUG`. \n\nDO NOT USE THIS VERSION! Please, go for a newer version!\n\n### 3.1.x\n\nThe `3.1.x` are based on the original CryptoJS, wrapped in CommonJS modules.\n\n\n"]], 0, 0, [], [], []]