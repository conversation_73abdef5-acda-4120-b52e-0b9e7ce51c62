[1, ["ecpdLyjvZBwrvm+cedCcQy", "a9Ej+75LREa6YCDPyO1Avm", "94ReHnBelKc6FT9lM/rkBs", "b6Zm1+MIBGT7Oar4moG1DG", "eePTFfRelL+7d1StsBMXiy", "a4iZ1kIfdO4oneAKjGYrnC", "abIlAMNAhL6Yd2XDsLXcS8", "d27XFIdFxNSI78X3idJM8r", "7a/QZLET9IDreTiBfRn2PD", "4eISdQPuxIZq9lxuYkH7y7", "e57lCYb7xI7bRmxKv2kLaB"], ["_parent", "node", "_textureSetter", "_spriteFrame", "root", "maskNode", "microphone", "tipsImg", "countProgress", "data", "_N$skeletonData"], [["cc.Node", ["_name", "_prefab", "_children", "_components", "_contentSize", "_parent", "_trs"], 2, 4, 2, 9, 5, 1, 7], "cc.SpriteFrame", ["cc.Sprite", ["_sizeMode", "_srcBlendFactor", "_type", "_fillType", "_fillRange", "_fillStart", "node", "_materials", "_spriteFrame", "_fillCenter"], -3, 1, 3, 6, 5], ["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_prefab", "_children", "_parent", "_components", "_contentSize", "_trs", "_eulerAngles"], 1, 4, 12, 1, 2, 5, 7, 5], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_children", "_components", "_prefab", "_contentSize"], 2, 2, 12, 4, 5], ["cc.Node", ["_name", "_parent", "_children", "_components", "_prefab", "_contentSize"], 2, 1, 2, 2, 4, 5], ["c9b51FFMkZFyIaigxcgQg7E", ["node", "countProgress", "tipsImg", "microphone", "maskNode"], 3, 1, 1, 1, 1, 1], ["cc.ProgressBar", ["_N$mode", "node", "_N$barSprite"], 2, 1, 1], ["cc.Label", ["_string", "_fontSize", "_lineHeight", "node", "_materials"], 0, 1, 3], ["sp.Skeleton", ["defaultSkin", "_preCacheMode", "premultipliedAlpha", "_animationName", "node", "_materials"], -1, 1, 3]], [[4, 0, 1, 2, 2], [0, 0, 5, 1, 2], [0, 0, 5, 2, 1, 2], [0, 0, 5, 3, 1, 4, 6, 2], [5, 0, 2], [0, 0, 2, 3, 1, 4, 2], [0, 0, 2, 3, 1, 4, 6, 2], [3, 0, 3, 2, 2], [3, 0, 1, 4, 5, 2, 6, 7, 8, 3], [6, 0, 1, 2, 3, 4, 2], [7, 0, 1, 2, 3, 4, 5, 2], [8, 0, 1, 2, 3, 4, 1], [4, 1, 2, 1], [2, 1, 2, 0, 3, 4, 6, 7, 9, 8, 6], [2, 0, 6, 7, 8, 2], [2, 6, 7, 8, 1], [2, 1, 2, 0, 3, 5, 4, 6, 7, 9, 7], [9, 0, 1, 2, 2], [10, 0, 1, 2, 3, 4, 4], [11, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "pro_2", "rect": [1, 0, 287, 286], "offset": [0.5, 0], "originalSize": [288, 286], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [1]], [[{"name": "mengceng", "rect": [0, 0, 1560, 247], "offset": [0, 0], "originalSize": [1560, 247], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [2]], [[{"name": "pro_1", "rect": [1, 0, 287, 286], "offset": [0.5, 0], "originalSize": [288, 286], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [3]], [[{"name": "tips_lab", "rect": [0, 0, 386, 116], "offset": [0, 0], "originalSize": [386, 116], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [2], [4]], [[[4, "entry"], [5, "entry", [-7, -8], [[11, -6, -5, -4, -3, -2]], [12, -1, 0], [5, 230, 500]], [7, "ATTACHED_NODE:atexiao", [[-9, [1, "ATTACHED_NODE:cone117", -10, [0, "04r3qhwh5AP7/qa+2JM6qh", 1, 0]], [1, "ATTACHED_NODE:cone118", -11, [0, "04axPQ8ehGEa6ecaF3aSS7", 1, 0]], [1, "ATTACHED_NODE:aone100", -12, [0, "861ApUErtA84y3Nc7pIJRT", 1, 0]], [1, "ATTACHED_NODE:aone101", -13, [0, "ab/orAKtBGbZJIwz2jclyK", 1, 0]], [1, "ATTACHED_NODE:aone102", -14, [0, "e7hTjizbZKcanNxi9cKkJv", 1, 0]], [1, "ATTACHED_NODE:aone103", -15, [0, "23IhwVof9GeaVfgPmZ/f3J", 1, 0]], [1, "ATTACHED_NODE:aone115", -16, [0, "79nJJUPGhJ1oAvY5KW7qrw", 1, 0]]], 1, 4, 4, 4, 4, 4, 4, 4], [0, "8fkm5AB+BJx4FHgffLCnB6", 1, 0]], [9, "prog", [-19], [[[13, 1, 3, 0, 2, 1, -17, [3], [0, 0.5, 0.5], 4], -18], 4, 1], [0, "68HDrfj9hGYpvgXJkxfcgw", 1, 0], [5, 144, 143]], [6, "tips", [-21], [[14, 0, -20, [6], 7]], [0, "b3jwWpYS5N96Gtt/Vrbf3M", 1, 0], [5, 193, 58], [0, 120, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "mengceng", 1, [[15, -22, [0], 1]], [0, "96+f/5iCRJbKk/uVB3xj5Y", 1, 0], [5, 1560, 247], [-1.4899999999998954, 121.200275893563, 1000, 0, 0, 0, 1, 1, 1, 1]], [10, "spine_mkf", 1, [-24], [-23], [0, "73HTfyt29NerwalsBwqNMv", 1, 0], [5, 183.62, 180.56]], [2, "ATTACHED_NODE:b<PERSON><PERSON><PERSON><PERSON>", 2, [3, 4], [0, "48yMY1+gtDO6lLbbMy9DTA", 1, 0]], [2, "ATTACHED_NODE_TREE", 6, [-25], [0, "acwr9bhnhPK78PMIwZAxg8", 1, 0]], [2, "ATTACHED_NODE:root", 8, [-26], [0, "11CqxVeqhKNpI8/lnJbZNa", 1, 0]], [2, "ATTACHED_NODE:bone96", 9, [-27], [0, "15ExSm71dEk67xC7IjmaOs", 1, 0]], [2, "ATTACHED_NODE:bone97", 10, [2], [0, "f6TgfhhMBB2IUAYMT0NYlX", 1, 0]], [8, "bar", 512, 3, [-28], [0, "93JZLXTrdNtpCny2Um8PM4", 1, 0], [5, 144, 143], [0, 0, 0, 0, 0, 0.7071067811865475, 0.7071067811865476, -1, 1, 1], [1, 0, 0, 90]], [16, 1, 3, 0, 2, 1, 1, 12, [2], [0, 0.5, 0.5]], [17, 2, 3, 13], [3, "1", 4, [[18, "开始发言吧", 28, 28, -29, [5]]], [0, "682CN7Cj9EcrqUdFGSj93P", 1, 0], [5, 140, 36], [0, -2.814, 0, 0, 0, 0, 1, 1, 1, 1]], [19, "default", 0, false, "<PERSON>ua<PERSON><PERSON><PERSON><PERSON><PERSON>", 6, [8]]], 0, [0, 4, 1, 0, 5, 5, 0, 6, 16, 0, 7, 4, 0, 8, 14, 0, 1, 1, 0, -1, 5, 0, -2, 6, 0, -1, 7, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 0, 2, 0, 1, 3, 0, -2, 14, 0, -1, 12, 0, 1, 4, 0, -1, 15, 0, 1, 5, 0, -1, 16, 0, -1, 8, 0, -1, 9, 0, -1, 10, 0, -1, 11, 0, -1, 13, 0, 1, 15, 0, 9, 1, 2, 0, 11, 3, 0, 7, 4, 0, 7, 29], [0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 16], [-1, 3, -1, -1, 3, -1, -1, 3, -1, 3, 10], [0, 5, 0, 0, 6, 0, 0, 7, 8, 9, 10]]]]