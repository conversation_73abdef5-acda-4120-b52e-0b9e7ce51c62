[1, ["ecpdLyjvZBwrvm+cedCcQy", "58Iz1tLlBAA5Ik9roHQU9v", "50zSd8svJN/IhIiwnOm/h/", "702TWl43hJPJxeF2rq1vgB", "06429h9ERO56+9IH9L0SMh", "92q8aJzYFAE5SBNjAGxIAr", "a1BjBLLslFX7tUtlFbffCd"], ["node", "_textureSetter", "_spriteFrame", "root", "data"], [["cc.Node", ["_name", "_components", "_prefab", "_contentSize", "_trs", "_parent", "_children", "_color"], 2, 9, 4, 5, 7, 1, 2, 5], "cc.SpriteFrame", ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Sprite", ["_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.Prefab", ["_name"], 2], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "node"], 0, 1], ["cc.BlockInputEvents", ["node"], 3, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "node", "_materials"], -1, 1, 3]], [[2, 0, 1, 2], [0, 0, 5, 1, 2, 3, 4, 2], [3, 0, 2, 3, 4, 2], [4, 0, 2], [0, 0, 6, 1, 2, 3, 4, 2], [0, 0, 5, 1, 2, 3, 2], [0, 0, 5, 1, 2, 7, 3, 4, 2], [5, 0, 1, 2, 3, 4], [6, 0, 1], [2, 1, 1], [3, 0, 1, 2, 3, 4, 3], [7, 0, 1, 2, 3, 4, 5, 5]], [[[{"name": "ditu", "rect": [0, 0, 607, 298], "offset": [0, 0], "originalSize": [607, 298], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [1]], [[{"name": "quxiao", "rect": [0, 0, 200, 76], "offset": [0, 0], "originalSize": [200, 76], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [2]], [[{"name": "queren", "rect": [0, 0, 204, 80], "offset": [0, 0], "originalSize": [204, 80], "capInsets": [0, 0, 0, 0]}], [1], 0, [0], [1], [3]], [[[3, "noticeChoice"], [4, "noticeChoice", [-4, -5, -6, -7], [[7, 45, 1280, 720, -2], [8, -3]], [9, -1], [5, 1280, 720], [640, 360, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "bg", 1, [[10, 0, false, -8, [0], 1]], [0, "bdCWKKVPlK1KV8DyAfW7B2", 1], [5, 607, 298]], [6, "label", 1, [[11, "确认删除图片，开始打字？", 34, 1, 1, -9, [2]]], [0, "d2sVW2xftOIJDj/bTRxnt5", 1], [4, 4278190088], [5, 408, 51], [0, 61.869, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "fail", 1, [[2, 0, -10, [3], 4]], [0, "b4kpchcV1LSrf5PqImWlOy", 1], [5, 200, 80], [130.563, -65.012, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "ok", 1, [[2, 2, -11, [5], 6]], [0, "e7EwMu8S1L+4XbDVQ1OXmx", 1], [5, 204, 80], [-132.066, -65.012, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 3, 1, 0, 0, 1, 0, 0, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, -4, 5, 0, 0, 2, 0, 0, 3, 0, 0, 4, 0, 0, 5, 0, 4, 1, 11], [0, 0, 0, 0, 0, 0, 0], [-1, 2, -1, -1, 2, -1, 2], [0, 4, 0, 0, 5, 0, 6]]]]