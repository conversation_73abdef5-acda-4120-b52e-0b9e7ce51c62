[1, ["bfuFQfw+JLza8Ju71S1Z1B", "2cyiGc1iJIbYD13SNQSOU3", "39kDvw/YVMMI6IIDmdrazp", "dfvqgz2vJIa6ax0qgsGcsW", "c8ERq0BoVFI7BSf1FJQ8lg", "f60uKg/hxES5AJ3bolcUm8", "081NlZGo9GJa2k9nLIPbI5"], ["_textureSetter", "root", "matchNode", "vacancyNode", "targetNode", "node", "data", "matchNodePrefab", "trashCanPrefab", "matchActive", "matchGoBack", "matchClickBtn", "finger"], [["cc.Node", ["_name", "_prefab", "_contentSize", "_parent", "_children", "_components"], 2, 4, 5, 1, 2, 9], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], "cc.SpriteFrame", ["cc.Prefab", ["_name"], 2], ["dd5c4fmh3FKTpU01zzJZNUC", ["node", "targetNode", "vacancyNode", "matchNode", "matchNodePrefab", "trashCanPrefab", "matchActive", "matchGoBack", "matchClickBtn", "finger"], 3, 1, 1, 1, 1, 6, 6, 6, 6, 6, 6], ["cc.AudioClip", ["_name", "_native", "duration"], 0]], [[1, 0, 1, 2], [5, 0, 1, 2, 4], [0, 0, 3, 1, 2, 2], [3, 0, 2], [0, 0, 4, 5, 1, 2, 2], [0, 0, 3, 1, 2], [4, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 1], [1, 1, 1]], [[[{"name": "finger", "rect": [43, 54, 192, 149], "offset": [3, -4], "originalSize": [272, 249], "capInsets": [0, 0, 0, 0]}], [2], 0, [0], [0], [0]], [[[3, "matchBoard"], [4, "matchBoard", [-6, -7, -8], [[6, -5, -4, -3, -2, 0, 1, 2, 3, 4, 5]], [7, -1], [5, 1280, 720]], [5, "vacancyNode", 1, [0, "70gk49cD1O65tIyqsMiIyJ", 1]], [2, "matchNode", 1, [0, "3a28TUZJlIBIpNKGivSrmh", 1], [5, 756, 540]], [2, "targetNode", 1, [0, "d75GxV1adCrKJBs928wtn2", 1], [5, 2, 2]]], 0, [0, 1, 1, 0, 2, 3, 0, 3, 2, 0, 4, 4, 0, 5, 1, 0, -1, 2, 0, -2, 3, 0, -3, 4, 0, 6, 1, 8], [0, 0, 0, 0, 0, 0], [7, 8, 9, 10, 11, 12], [1, 2, 3, 4, 5, 6]], [[[1, "match-go-back", ".mp3", 0.20898], -1], 0, 0, [], [], []], [[[1, "match-active", ".mp3", 0.391837], -1], 0, 0, [], [], []], [[[1, "match-clickbtn", ".mp3", 0.286688], -1], 0, 0, [], [], []]]]