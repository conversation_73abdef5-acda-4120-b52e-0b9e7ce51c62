[1, ["ecpdLyjvZBwrvm+cedCcQy", "bdmJonsdZBh4idiuVu+m0Y", "685K+xb2NJhZ8BEz1Tqfqw", "9chTx9r+5CMJPUud12/I9i", "6cBCmlH5hErYDxn054Scy3", "bcEDxKdSpG4ZgYghWM4tzr", "dbAThTxJFD+YFGugnppOTW", "c8o4hQ2BdDMpFU819CuBmn", "4bhDep18pNgbE3buUilxqW", "f12zLnwFpNUpIZScG1KRJi", "c7fszkRjZA+7vbxXE1TcMI", "99VNTv1pVLR6D3r0ckcXgV", "10AwYuGkxE465hn6aGfzgd", "cdc+/9xGlKtaZELjZKPjl2", "a2ODcsWoBBi7qRV6uxPKzZ", "c6D1eQO0FKfZ4U2u9zIa5H", "b73u6okDBADLT6lB8MDHkS", "3fcE0Laq9JOK0DtGfDDpBO", "a35iJmK0ZNtZoQDrnFyv9V", "377Gs3K4FCVYZlmEmI3i8o", "2562JFtFpHGpku9l5q5IDs", "a0PMAkWP1EQryLOPAFLbfF", "b8EkvGYVxCqJRdahvcSV6i", "b3fpkUbntGRIkx7PsHJQb4", "63qKNTX8hEkq1oPHddG/ut", "fbhc8hVNZBdokWgNeF5sau", "0cLaieJINKvYym7E8NejpL", "9dXgTTXPRA9pVL7eNXk1Gy", "76fskSl2lKu5PUofrvQUk5", "09HuJYCrNLroFiU/Y64IH9", "28RefWhn1Pfp8lIO8sNTYz", "1deJNblo1Pq4EjoQZnh+Xw", "44aBkZrTtA6IKxjAiKtBjD", "50PG3Xm11Lgr9voJ+UbnOq", "4dvDAT4qpOWocoYo0j4t4S", "7bI74jqeVDKIDAS8TJ0meF", "39c1gMCK1PT7MN+BmIsqtZ", "32atIbtt9KgYdSkt4sfkOV", "11Goz73DJJX4Kz4uVHJ1JS", "d8IaOCIa9FFoLJpHYhwkmN", "c94HHC6V9G0KrTkv9cCGqF", "ebVyjXFgFAp4frGEoX3Hg0", "d54izP2hROMKeCgUKU2oW3", "94ir8X4/tKtrNYH+NdBDea", "52346rqd1BgaNQD+eouyEN", "e5AN2SuaNBiKBNuXga/ZsF", "b2gjaKVb9G/YFV/DXeAJ2r", "5dz5s5xFhCb6lSwdohV7dD", "d4mSzjKKdMXr3/vBa7LnMB", "d2reCNWupOWrNxeK+ogEol", "dc28IKCBVFlbkJDqTDlPoG", "dbLMxMe3BJXaOBGtmUGtan", "b7YbMPH8NAXbQ8aSnDv5fo", "d8QA579kFGEJijMqyOk3e7", "cdMbwqLaxDypHQX6iUgOE/", "e28baqrExOYrPXMGlOBorZ", "6e4Io+qS1MepIFI4Mdq2vz", "7ekFT3nsdDtr2hshTnW4jj", "e2zWYaMPFKZZHgqHgCJa16", "56/y2SHaFDMod+fOd1MGw4", "51tWjuF7hIULSsyoQcrtvE", "4bwaOVi8RDS5Ofkb+sCewp", "e3F15SSolNupY4ZabuNsm8", "0dd4Du1qpLs6zSijzC/AeF", "48DLZUYhlBCrygFi0hlKxZ", "56S3eY+AVLpZEaCcDGR8cM", "78IMXyqNhP76VFcPJ9iTot", "176Uv7+cdK+aRIbbOcQoH3", "58bKm+ISBCu5rChVQp52mI", "89vh/+TFJC5qfHngI26vlE", "fdNMATlddLvIb5SsD34Rmu", "bbu+6B9hNEYp3mfdXmpgRy", "b2unxA5rxCt5JCCbbChEDk", "e98gPDUU5BMp3zAQmRfU0h", "dfA7fnij9KrpHJg7eGbVYj", "b0/3qZRn5CbaDCQ7ZPn/td", "66rPG+IhpPVJ5oy7bkgXWz", "dbtCc+PXhClIIXgZhbZpOE", "0cWWM0ggNG9r58ugvMlAJj", "1aBZdXt0hFRaC0UJuIUKra", "44PNRVxFRMxLcN+2kaqibI", "44tb4A/2BHw5g1lrjnh2+x", "d5UybLXptPwJO+/GBgCYiQ", "c7yq5LJLxPKKdVneaGlqXl", "50KauSq6RCLYGaULnMT05E", "c5rqujisJC7pCOuOefsZhs", "e0Dhry6aVHnZk05F1H1aXT", "0eHwYkmCdEM5fTQwqSilVq", "deA/qpkExGYL5iSJ477SlA", "c2UJ+44GpHU6KOFVUJyAVt", "f5u/s5VgRG3avx2Qtl1TrT", "dezanAlHpOQ5N3zArnHgSS", "48c6ZtY4lK9q6jMf/emwA+", "b4QB9z1JRC6Kk59VL2YuDm", "6by/Cb2PFJx6OvZuoiEd7d", "458z2ZqaJOeouSkibxAYpg", "b2yj20Fx5I+6aW5apnUEbG", "a4P3J+t8lAyqJIgafzIzgc", "86YHyZBCxACo5d/UdlukWD", "4dOhudb2pBzYmx5V4IwgWf", "99iOH+SFRCoak+Ch82IJ59", "d1LL79y5BAqZD+ZFH1qS9j", "22UdsyLuJGBKICCuAdJz8D", "can9WKDh5NG6+Rk2bOmlWS", "abBaNL3UdJi5wieqjThNgP", "45XB3tErlIUYAAXtYqFdQI", "76mbXzTJREFo4YiihRH4Q6", "dbQYt9aRdAvI6QYDHAky9G", "21wmKos+1BQ4v9ejVJ+4dz", "e0G3Zxe2JApaFxYo+Xl74r", "8e61tf7SxCEbuimAH6bt8S", "f5zKYZ0qBPsZ8MuL2/6Iun"], ["_textureSetter", "node", "_spriteFrame", "_N$normalSprite", "_N$disabledSprite", "_defaultClip", "_N$barSprite", "root", "wordAudioView", "wordAudioNode", "authorityNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadNode", "tipsWordNode", "micNode", "labaNode", "idleNode", "soundByteNodeRight", "soundByteNodeLeft", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_prefab", "_components", "_contentSize", "_parent", "_children", "_trs", "_color"], 1, 4, 9, 5, 1, 2, 7, 5], ["cc.Sprite", ["_srcBlendFactor", "_sizeMode", "_type", "_isTrimmedMode", "_fillType", "_fillRange", "_fillStart", "node", "_materials", "_spriteFrame", "_fillCenter"], -4, 1, 3, 6, 5], ["cc.AnimationClip", ["_name", "_duration", "sample", "curveData", "speed", "wrapMode", "events"], -4], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "clickEvents", "_N$normalSprite", "_N$disabledSprite"], 2, 1, 9, 6, 6], ["cc.ProgressBar", ["_N$mode", "_N$progress", "node", "_N$barSprite"], 1, 1, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_lineHeight", "node", "_materials"], -2, 1, 3], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 12, 4, 5], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_anchorPoint", "_trs"], 2, 1, 2, 4, 5, 5, 7], ["cc8a3tcxABOKrY1aZGsQEdn", ["node", "soundByteNodeLeft", "soundByteNodeRight", "soundByteImages", "idleNode", "labaNode", "micNode", "tipsWordNode", "loadNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authorityNode", "wordAudioNode", "wordAudioView"], 3, 1, 1, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1], ["cc.Animation", ["node", "_clips", "_defaultClip"], 3, 1, 3, 6], ["cc.ClickEvent", ["_componentId", "handler", "target"], 1, 1]], [[4, 0, 1, 2], [2, 0, 1, 3, 7, 8, 9, 4], [13, 0, 1, 2, 3], [1, 0, 5, 3, 2, 4, 7, 2], [5, 1, 2, 1], [1, 0, 5, 3, 2, 4, 2], [2, 7, 8, 9, 1], [1, 0, 1, 5, 3, 2, 4, 7, 3], [12, 0, 1, 2, 1], [5, 0, 1, 2, 3, 4, 2], [1, 0, 1, 5, 6, 3, 2, 4, 7, 3], [1, 0, 1, 5, 6, 3, 2, 4, 3], [2, 0, 7, 8, 9, 2], [8, 0, 2], [1, 0, 6, 3, 2, 4, 2], [1, 0, 1, 5, 6, 2, 7, 3], [1, 0, 5, 6, 3, 2, 4, 2], [1, 0, 5, 3, 2, 8, 4, 7, 2], [1, 0, 5, 3, 2, 8, 4, 2], [9, 0, 1, 2, 3, 4, 2], [10, 0, 1, 2, 3, 4, 5, 6, 2], [11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 1], [4, 1, 1], [2, 0, 1, 7, 8, 9, 3], [2, 0, 2, 1, 4, 6, 5, 7, 8, 10, 7], [2, 0, 2, 1, 4, 5, 3, 7, 8, 10, 7], [2, 2, 1, 7, 8, 9, 3], [6, 0, 2, 3, 2], [6, 0, 1, 2, 3, 3], [7, 0, 1, 4, 2, 3, 5, 6, 6], [7, 0, 1, 2, 3, 5, 6, 5], [3, 0, 1, 2, 4, 5, 3, 7], [3, 0, 1, 2, 4, 6, 3, 7], [3, 0, 1, 2, 5, 3, 6]], [[[{"name": "bofang_00004", "rect": [10, 7, 92, 41], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [8]], [[{"name": "bofang_00001", "rect": [10, 17, 92, 21], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [9]], [[{"name": "spr_idle_no", "rect": [6, 9, 108, 108], "offset": [0, -3], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [10]], [[{"name": "bofang_00013", "rect": [10, 11, 92, 33], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [11]], [[[13, "entry"], [14, "entery", [-14, -15, -16, -17, -18, -19, -20, -21, -22], [[21, -13, -12, -11, [48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82], -10, -9, -8, -7, -6, -5, -4, -3, -2]], [22, -1], [5, 600, 140]], [10, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, 1, [-26, -27], [[6, -23, [12], 13], [8, -24, [15], 14], [4, -25, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onLabaBtn", 1]]]], [0, "972EPHmdJN97BcTvgfa5m6", 1], [5, 64, 64], [124, 0, 0, 0, 0, 0, 1, 1, 1, 0]], [10, "audioView", false, 1, [-31, -32], [[1, 1, 2, false, -28, [44], 45], [8, -29, [47], 46], [4, -30, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onClickWordAudio", 1]]]], [0, "83QhQxFLdLHYqGWDVNR8g8", 1], [5, 88, 88], [-124, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "micNode", false, 1, [-34, -35, -36], [[4, -33, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "manualStop", 1]]]], [0, "46xpnU7ZBDZJWW3aZrdWeE", 1], [5, 120, 120]], [5, "idelNode", 1, [[1, 1, 2, false, -37, [0], 1], [9, 2, -38, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onIdleBtn", 1]], 2, 3], [4, -39, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onDisIdleBtn", 1]]]], [0, "baR2VeyChJnY691fDaH0ku", 1], [5, 120, 120]], [3, "labaNode", 1, [[1, 1, 2, false, -40, [4], 5], [9, 2, -41, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onLabaBtn", 1]], 6, 7], [4, -42, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onDisLalbBtn", 1]]]], [0, "4c+WcL5DBOfJzQ5r5LZrww", 1], [5, 88, 88], [124, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "waitNode", false, 1, [-44, -45], [[1, 1, 2, false, -43, [29], 30]], [0, "abBKY0CXVCSqNRfjfqpFf5", 1], [5, 120, 120]], [15, "authorityNode", false, 1, [-46, -47, -48], [0, "7dvjKZWstO5a9lpUTnJp05", 1], [-410, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [16, "timeNode", 4, [-52], [[23, 1, 0, -49, [17], 18], [27, 2, -51, -50]], [0, "cfLyA3531BcrNS2veMdLa/", 1], [5, 102, 102]], [7, "audioNode", false, 1, [[1, 1, 2, false, -53, [36], 37], [9, 2, -54, [[2, "cc8a3tcxABOKrY1aZGsQEdn", "onClickWordAudio", 1]], 38, 39]], [0, "8bPUYW3YhMIrLoKGmxJTum", 1], [5, 88, 88], [-124, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [7, "yinboSpr1", false, 4, [[12, 1, -55, [19], 20]], [0, "d2AqP06XxB0ZRqFj9wVBW0", 1], [5, 92, 16], [-165, 0, 0, 0, 0, 0, 1, -1, -1, 1]], [7, "yinboSpr2", false, 4, [[12, 1, -56, [21], 22]], [0, "e7OUDVNSZGPaPEPGMstC7o", 1], [5, 92, 16], [165, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [17, "tips", 1, [[29, "点击话筒，可重新录音", 18, 20, 1, 1, -57, [23]]], [0, "efF0ahrttIGaw/ggI7ZPD3", 1], [4, 4284900450], [5, 180, 26], [0, -79.282, 0, 0, 0, 0, 1, 1, 1, 1]], [5, "prog", 7, [[1, 1, 2, false, -58, [24], 25], [28, 2, 0.8, -60, -59]], [0, "3aSZ/upt1IEJI1yXtLKwf0", 1], [5, 120, 120]], [19, "bar", 7, [[-61, [8, -62, [27], 26]], 1, 4], [0, "a4n0b5g9RKIJ7ybTw0nLG9", 1], [5, 48, 48]], [3, "spr_a", 2, [[6, -63, [8], 9]], [0, "f3fKjEQkxOGrSBpOnW8/yX", 1], [5, 7, 15], [12.423, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "spr_b", 2, [[6, -64, [10], 11]], [0, "1260Al+5JMCawdbf/UeCCu", 1], [5, 12, 22], [15.162, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [20, "sprBar", 9, [-65], [0, "f5vkZ9zoVHg5zOu8+bjL0q", 1], [5, 102, 102], [0, 0, 0.5], [-51, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [24, 1, 3, 0, 2, 0.25, 1, 18, [16], [0, 0.5, 0.5]], [25, 1, 3, 0, 2, 0.8, false, 15, [28], [0, 0.5, 0.5]], [3, "New Sprite", 8, [[26, 1, 0, -66, [31], 32]], [0, "6fW0LFT5lPj4zQXApmnc/H", 1], [5, 340, 48], [-10.245, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "New Sprite", 8, [[6, -67, [33], 34]], [0, "92k0lpuV9I3LgcR4EeEG2X", 1], [5, 58, 78], [-152.827, 0, 0, 0, 0, 0, 1, 0.3, 0.3, 1]], [18, "lab", 8, [[30, "打开麦克风权限才能答题", 24, 1, 1, -68, [35]]], [0, "47h9pJwehEsZQNWbcauHJi", 1], [4, 4280389368], [5, 264, 51]], [5, "item_1", 3, [[1, 1, 2, false, -69, [40], 41]], [0, "87abfRjUVLvbjZzIrJYE6w", 1], [5, 88, 88]], [5, "item_2", 3, [[1, 1, 2, false, -70, [42], 43]], [0, "d64WtWBM1ADr97Ld7dC9xG", 1], [5, 88, 88]]], 0, [0, 7, 1, 0, 8, 3, 0, 9, 10, 0, 10, 8, 0, 11, 2, 0, 12, 7, 0, 13, 13, 0, 14, 4, 0, 15, 6, 0, 16, 5, 0, 17, 12, 0, 18, 11, 0, 1, 1, 0, -1, 5, 0, -2, 6, 0, -3, 2, 0, -4, 4, 0, -5, 13, 0, -6, 7, 0, -7, 8, 0, -8, 10, 0, -9, 3, 0, 1, 2, 0, 1, 2, 0, 1, 2, 0, -1, 16, 0, -2, 17, 0, 1, 3, 0, 1, 3, 0, 1, 3, 0, -1, 24, 0, -2, 25, 0, 1, 4, 0, -1, 9, 0, -2, 11, 0, -3, 12, 0, 1, 5, 0, 1, 5, 0, 1, 5, 0, 1, 6, 0, 1, 6, 0, 1, 6, 0, 1, 7, 0, -1, 14, 0, -2, 15, 0, -1, 21, 0, -2, 22, 0, -3, 23, 0, 1, 9, 0, 6, 19, 0, 1, 9, 0, -1, 18, 0, 1, 10, 0, 1, 10, 0, 1, 11, 0, 1, 12, 0, 1, 13, 0, 1, 14, 0, 6, 20, 0, 1, 14, 0, -1, 20, 0, 1, 15, 0, 1, 16, 0, 1, 17, 0, -1, 19, 0, 1, 21, 0, 1, 22, 0, 1, 23, 0, 1, 24, 0, 1, 25, 0, 19, 1, 70], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 20], [-1, 2, 3, 4, -1, 2, 3, 4, -1, 2, -1, 2, -1, 2, 5, -1, -1, -1, 2, -1, 2, -1, 2, -1, -1, 2, 5, -1, -1, -1, 2, -1, 2, -1, 2, -1, -1, 2, 3, 4, -1, 2, -1, 2, -1, 2, 5, -1, -1, -2, -3, -4, -5, -6, -7, -8, -9, -10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, 2, 2], [0, 2, 2, 12, 0, 3, 3, 13, 0, 14, 0, 15, 0, 16, 4, 4, 0, 0, 17, 0, 1, 0, 1, 0, 0, 18, 5, 5, 0, 0, 19, 0, 20, 0, 21, 0, 0, 6, 6, 22, 0, 23, 0, 24, 0, 25, 7, 7, 1, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61]], [[{"name": "bofang_00006", "rect": [10, 5, 92, 45], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [62]], [[{"name": "back", "rect": [0, 0, 80, 48], "offset": [0, 0], "originalSize": [80, 48], "capInsets": [25, 14, 31, 15]}], [0], 0, [0], [0], [63]], [[{"name": "bofang_00005", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [64]], [[{"name": "bofang_00012", "rect": [10, 8, 92, 39], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [65]], [[{"name": "spr_wait", "rect": [9, 9, 102, 102], "offset": [0, 0], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [66]], [[{"name": "bofang_00011", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [67]], [[{"name": "spr_time3", "rect": [18, 18, 204, 204], "offset": [0, 0], "originalSize": [240, 240], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [68]], [[{"name": "bofang_00007", "rect": [10, 6, 92, 44], "offset": [-8, 0], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [69]], [[{"name": "spr_waitAn2", "rect": [36, 36, 48, 48], "offset": [0, 0], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [70]], [[{"name": "bofang_00009", "rect": [10, 5, 92, 45], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [71]], [[{"name": "bofang_00008", "rect": [10, 5, 92, 45], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [72]], [[{"name": "spr_time1", "rect": [18, 18, 204, 204], "offset": [0, 0], "originalSize": [240, 240], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [73]], [[{"name": "bofang_00019", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [74]], [[{"name": "bofang_00034", "rect": [10, 20, 92, 14], "offset": [-8, 1], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [75]], [[{"name": "bofang_00022", "rect": [10, 6, 92, 44], "offset": [-8, 0], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [76]], [[{"name": "spr_default_2", "rect": [47, 34, 12, 22], "offset": [9, -1], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [77]], [[{"name": "spr_idle", "rect": [6, 9, 108, 108], "offset": [0, -3], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [78]], [[[31, "laba", 0.5, 30, 0.2, 2, {"paths": {"spr_a": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.16666666666666666, "value": true}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.5, "value": false}]}}, "spr_b": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.16666666666666666, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.5, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "bofang_00031", "rect": [10, 15, 92, 25], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [79]], [[{"name": "bofang_00003", "rect": [10, 10, 92, 36], "offset": [-8, 0], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [80]], [[{"name": "bofang_00010", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [81]], [[{"name": "bofang_00032", "rect": [10, 18, 92, 18], "offset": [-8, 1], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [82]], [[{"name": "bofang_00018", "rect": [10, 8, 92, 40], "offset": [-8, 0], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [83]], [[{"name": "spr_laba", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [84]], [[{"name": "bofang_00002", "rect": [10, 13, 92, 29], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [85]], [[{"name": "deng", "rect": [19, 9, 58, 78], "offset": [0, 0], "originalSize": [96, 96], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [86]], [[{"name": "spr_labaA", "rect": [52, 37, 7, 15], "offset": [11.5, -0.5], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [87]], [[{"name": "spr_waitAn", "rect": [36, 36, 48, 48], "offset": [0, 0], "originalSize": [120, 120], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [88]], [[{"name": "bofang_00021", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [89]], [[{"name": "spr_default_1", "rect": [47, 37, 7, 15], "offset": [6.5, -0.5], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [90]], [[{"name": "bofang_00027", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [91]], [[{"name": "spr_laba1", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [92]], [[{"name": "spr_audio_no", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [93]], [[[32, "prog", 0.5, 20, 0.4, [{"frame": 0.5, "func": "progCallFunc", "params": []}], {"paths": {}, "props": {"angle": [{"frame": 0, "value": 0, "curve": "quartInOut"}, {"frame": 0.5, "value": -360}]}}]], 0, 0, [], [], []], [[{"name": "bofang_00000", "rect": [10, 19, 92, 16], "offset": [-8, 1], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [94]], [[{"name": "spr_labaB", "rect": [52, 34, 12, 22], "offset": [14, -1], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [95]], [[[33, "word", 1, 15, 2, {"paths": {"item_1": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": true}, {"frame": 0.6666666666666666, "value": false}, {"frame": 1, "value": false}]}}, "item_2": {"props": {"active": [{"frame": 0, "value": false}, {"frame": 0.3333333333333333, "value": false}, {"frame": 0.6666666666666666, "value": true}]}}}}]], 0, 0, [], [], []], [[{"name": "bofang_00015", "rect": [10, 17, 92, 21], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [96]], [[{"name": "bofang_00029", "rect": [10, 9, 92, 37], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [97]], [[{"name": "spr_laba_no", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [98]], [[{"name": "bofang_00024", "rect": [10, 5, 92, 45], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [99]], [[{"name": "bofang_00023", "rect": [10, 5, 92, 45], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [100]], [[{"name": "bofang_00017", "rect": [10, 11, 92, 34], "offset": [-8, 0], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [101]], [[{"name": "bofang_00014", "rect": [10, 14, 92, 27], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [102]], [[{"name": "bofang_00028", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [103]], [[{"name": "spr_audio", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [104]], [[{"name": "bofang_00026", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [105]], [[{"name": "bofang_00025", "rect": [10, 5, 92, 44], "offset": [-8, 1], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [106]], [[{"name": "bofang_00033", "rect": [10, 20, 92, 14], "offset": [-8, 1], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [107]], [[{"name": "bofang_00030", "rect": [10, 12, 92, 31], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [108]], [[{"name": "bofang_00020", "rect": [10, 6, 92, 43], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [109]], [[{"name": "bofang_00016", "rect": [10, 14, 92, 27], "offset": [-8, 0.5], "originalSize": [128, 56], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [110]], [[{"name": "spr_default", "rect": [12, 12, 64, 64], "offset": [0, 0], "originalSize": [88, 88], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [0], [111]]]]