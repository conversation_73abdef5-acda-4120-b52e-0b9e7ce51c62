[1, ["12d9b2cb4", "ecpdLyjvZBwrvm+cedCcQy", "14e94df15", "f0OhcUg1hDQIEIdFU/l1G6", "6eOl5uDKpNc4Z4KMP7g+vZ", "b8pODJbwBPAYGKr8fFmNQC", "3bCTckaYJOlq4cLsMGOeh2", "46MRC3hmJK+pEvq2iaxx3c", "20yKFQCXRCx6BQNoIc+3uR", "a5WNJSs5BAfbMt5lPtZLKx", "24r+hzqplGgIHd2B38WAO+", "d5aqxUtFFJD6uYaIpQ8sQL", "547FMoro5OWaFJ+hmqOHk/", "8dSMADGOFN5anbdu8ib659", "d6xaSbtkZCQI4FdSzmfkwZ", "aciZ67p+5OW629uM5fmiCW", "56fYK9J4NAMLtnf+FjbMS5", "88Komidk9F4ZiVyV07MAr1", "4akjx1lQVGbL+ek/ZcpBxq", "79UMliS11E+4ByokLVffQF", "dbXlgRjzdC2a5ReKEfVVLr", "914Oi2ViNLbqor0CWIUv3U", "3328qxNp9D2JZD+PvM3PTw", "53ZyRUvKFIxKmEb5u0AD3F", "96aKtdSt1OLKVlC1AbskZZ", "c3jYEBj4hCD6ZxMt6oxZRb", "10IRtF9b1F0Jj8vvgAzXPW", "38YLtHIOJCJ6ST/earLznd", "a1KoTvFMVJ3ZhM0mCedFh3", "03RDWgDpVBj5UOKC9E8jHI", "e0g1soDwZHhbWROBVIny3R", "6eySxHjFVOmKt6j4ssnU5G", "92v2brlEFJ6I8AnAMexi+j", "5aO/luXNlL7pH9Wgt146Zt", "757cRMbh5AGaUFMcSf9bBa", "54qC6eKnpBoIwhne1COEqe", "73DiDR/tVNZamobCedmf15", "c8LHVKMcxMxaMGZUQX9G0P", "ebVoZA3mxKDqQV3Or/kuCQ", "01SoXZXUVJwboohLznqJOe", "e5eQ2eSaNBb7rzoVhL2Ixm", "6cwmAdEeRFO6ODHt7YjQ4N", "41u3MygVdJrbD+s1FOjUyu", "9adsxnUg1Kpb9jVb5hLfgk", "0efpQZWbtOq7dGAI9QGJmY", "76IyfN3jxJBb6OLxzI/eRA", "96XqSB1qFGH6ucOS34wgRU", "e6ExRPDHhAJLE4mWMMlRmI", "a7zj+YRzhL+7+xkW2jtbgM", "f02B3Oq3VMYrB0hdKgn9+z", "99MgPiFilHWbImnPiPvNS3", "56fjN1tX9MWYjNVoB7AzA6", "13wXaN3ONI7KEd0w6p+4SZ", "6aAnirDOxHzYZHaSWtHXm+", "99ADqNiaNMJJuglldyTn/4", "e3xzmWwZpCNoDf51jZlX9S", "758nJuePJGop7L27IJnvL0", "1dpPy6vGtFNa0+d2DZxGnJ", "32FQZbajFAtru8Q62kPSqU", "20A7hyqhBHGaYl7KKXDb9u", "0b/7I4R+lOlagiE8NB8gtm", "00MmWYVg5P7qhrEjLwNRg/", "8b2ocG581Pxa5FbQIRIPVh", "24pW6993hMkJRacJgD33m8", "a9f6LEyE1I16qftpgkfOii", "63uXcCbpRNOLTOlUO9NmOu", "c1PZ1LDnFDCpdPAWBTlzrc", "fcny+Fq0dK/Z8XnPlJlaNo", "45QdlkOxlNBL+NYrWOGDhA", "a7PEQTUT1Bqa4izdkn7AVM", "f995G/SIhFEbnhQEisx+mT", "84vDUqtwJAMIPyx+2ZX2tB", "0e0eKgFONPPKkILo99kwYY", "94DWHUoR1KJqXdNh3NvjJH", "93ZdAoh0xLLbNliH83As+N", "5fyCguA85JprpFAPHe66PD", "252s4izFxCzb6sawzhuq5g", "305zgIh4VNp5FsImMCSatu", "3a/wLegodKEJnow75HbA7P", "aa6GBxjaJCIYzi5WGXNXFl", "70rIp7bvhGO7TjMkgtAs5R", "179kt1ZLJGXYjISgv4Ezk+", "51S+Z6gelE9Iug9m90Q/Jc", "76VHRiw8pLs78r6E2ael74", "f1X9A1M9VL36u80OkNi+st", "91tz0SRHpPJpm2ycMMKBVc", "05XxxTY6FDXI32tQFIJfzj", "c35e7uWYdCcqJ0ibW3IEQR", "a7b6e5xudGdKgm5EGAS82F", "b1Sf0xMShGV51hkwLoJ97E", "85qCz+nChK577TDNNKSCZM", "03eEDo7dJPXqdYwI+PiPal", "92RgAfIbNHMa3jkA28zJLE", "e2B0H3QCdMNr+Y85+RgavV", "67TqEH0yNGdIC2eM/NIaVd", "8cF9q3od5GI7E1WfQPkib3", "7fiWVYWq1IGaom2J9iNZmJ", "738L0zoDROYZoHaWCQAKNt", "a5ua+uEaRCr6x27Y5wDbKP", "bckXvsjNxDVYZ9I7h55LY7", "281FmgL5BJk6HxXyiLZLSb", "a6kvkDwh5Fhp94IggLabHs", "1dlWA2G6xFjbEzZA5Hu+4k", "1eRF6ajE1ITKXTsHVIri5F", "31wRJc48hKu7dbNfVnTuhT", "76ZYQxdIFEPbLFv6O+eyz3", "a6fxqR5stM4r4cRnfG0Ndk", "5bORqs1ERBo62gWoArVDVX", "893nG2I4BEDIeUaIfroUxx", "58DhayzrJGsqfo1mr71hk9", "74cyIn0yJMbJEyE6Y/f3yd", "0cG/ZGpwZFYpgaygXQSs7k", "7f9mh77oRKVbYERa9l76nl", "2cFEXeCndNlp2B1XKUMKha", "a6WOS5WB1PMpDI+i+7qIBU", "acKU3XpYpJ37nQtExsE7m5", "71TjbKX6lAfIiM1zB6jJAE", "2aBin3rTlIprsbFfQzPb95", "6aDvkAhs5BV5b44bwO1o73", "85yjyWNENGRq7JzNgQWWMk", "a43lEKPMhFvo7ywA8PvO38", "5ewUyjbl5GR7fqFG0NkLiR", "45lIYnquZCvIoTA4+irN+y", "1e4HtwtLVHdJjtPHGbkthN", "a4pysr5nBHeKgAP7/Q0KNM", "6fQZY+9dNHzI89YZyHPvQ2", "58YMRfxPlLK7+b5tWWPnhH", "d7Mn7VgThJDaAxmBiitsmu", "71DFIHJK1CgbuXjIPItISt", "2bpVGZ9UpEd7tNI0/FoREJ", "e6lPj0ubxGyK0WZKfpGYUk", "edfmx4ONpAPa57NESeT5qb", "529wCnIztB3blXPubH/wwF", "c5lBGEaUtCirwwmIckwhwq", "86LzXLZQBN8rSWxa3lyg3C", "69L7xdbgNDXL1AmNEdbAOh", "66IqwHLN9J1Ji74tEL1Hr/", "8e2DCM2W1C77DRSnFCjilR", "29TtLOaKpFyLccRobeVV4B", "31dsOdPGtEAaH3yjWEFYEB", "f3Kk+Kq65BNLqaVZ4nD7+O", "48CR/G7vNHiJauN2jAUco3", "b3zyV9WghG15BFSv8ydyzl", "8bwM8gfOxPwKEa98ulASN7", "84WRD0V31FgIVfy2CiTWxW", "202l93rANAV6dEqLJStxGW", "d0s4Vz0HJERrHJiFfXt5I7", "f2rGkhSyRGc6b+b2Ycv4jI", "a9zkCnAwpJgYSxh/ztjSYE", "56MNetWplBR7rV0tRYhVlJ", "87vgasG+JCQZIPUh+9T9lb", "74WTcbv4pJQqtp0jHJ7wYC", "29q1Kwu85L1rRryvMfWjen", "608JNAFqpKDZFRRixVPcZR", "44zVjaEvlG2L9UYWKim818", "8aC4n8YdlAp5HxdD2YFy2R", "0bF8BDOaVFFKN482zNx32V", "98B97D0cBM5b6aM4DJcpGk", "aa7EhrBkJIca5Pcus1wTl6", "ebk621V49P46cwL0V5HSr/", "4brP2oXmBJ/pZcld9rHW7u", "1cbFTEkF1DAYtNTVUxx7ys", "c3zIUxl6RODK9bRnTwP5z3", "b0UWc+IXpLba+C7VHkSw5O", "3fgAyBC9VHFLOPLS+Dq2ML", "67RsI6A0FDRbYzrS5nUI2V"], ["node", "_textureSetter", "_spriteFrame", "_N$normalSprite", "_N$pressedSprite", "_parent", "root", "daxieRootNode", "xiaoxieRootNode", "AZSpr", "azSpr", "data"], ["cc.SpriteFrame", ["cc.Node", ["_name", "_active", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_children", "_trs"], 0, 4, 5, 9, 1, 2, 7], ["cc.Sprite", ["_srcBlendFactor", "_sizeMode", "_isTrimmedMode", "node", "_materials", "_spriteFrame"], 0, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root", "asset"], 2, 1, 1], ["cc.Widget", ["alignMode", "_alignFlags", "_top", "node"], 0, 1], ["cc.<PERSON><PERSON>", ["_N$transition", "node", "_N$normalSprite", "_N$pressedSprite"], 2, 1, 6, 6], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize", "_trs"], 2, 1, 2, 4, 5, 7], ["4b3b6VCB/FHA5h7XGTWhHhz", ["node", "azList", "AZList", "azSpr", "AZSpr", "xiaoxieRootNode", "daxieRootNode"], 3, 1, 3, 3, 1, 1, 1, 1]], [[3, 0, 1, 2, 2], [1, 0, 6, 5, 3, 4, 8, 2], [5, 0, 1, 2, 3, 2], [2, 0, 1, 2, 3, 4, 5, 4], [2, 0, 1, 3, 4, 5, 3], [2, 0, 3, 4, 5, 2], [1, 0, 1, 7, 3, 4, 3], [5, 0, 1, 2, 2], [7, 0, 1, 2, 3, 4, 5, 2], [6, 0, 2], [1, 0, 7, 5, 3, 4, 2], [1, 0, 6, 7, 5, 3, 4, 2], [1, 0, 6, 7, 5, 3, 4, 8, 2], [1, 0, 6, 5, 3, 4, 2], [1, 0, 2, 6, 5, 3, 4, 3], [1, 0, 1, 6, 5, 3, 4, 8, 3], [8, 0, 1, 2, 3, 4, 5, 6, 1], [3, 1, 2, 1], [4, 0, 1, 3, 3], [4, 0, 1, 2, 3, 4], [2, 0, 1, 2, 3, 4, 4], [2, 0, 1, 3, 4, 3]], [[[{"name": "xia<PERSON>ie", "rect": [275, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "T", "rect": [377, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "b", "rect": [479, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "r", "rect": [581, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [3, 3, 130, 60], "offset": [0, 0], "originalSize": [130, 60], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "!", "rect": [683, 3, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "M", "rect": [897, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "b", "rect": [3, 69, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "P", "rect": [105, 69, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "-", "rect": [207, 73, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "x", "rect": [421, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "K", "rect": [523, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "kongge", "rect": [207, 143, 432, 64], "offset": [0, 0], "originalSize": [432, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "select_shanchu", "rect": [625, 73, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "g", "rect": [783, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "V", "rect": [885, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "h", "rect": [3, 139, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "P", "rect": [105, 139, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "N", "rect": [645, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "e", "rect": [747, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "5", "rect": [849, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "2", "rect": [951, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "t", "rect": [3, 209, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": ",", "rect": [207, 213, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "d", "rect": [105, 209, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "N", "rect": [421, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "shuangyinh<PERSON>", "rect": [523, 213, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "R", "rect": [737, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[[9, "entry"], [10, "entry", [-7, -8, -9], [[16, -6, [331, 332], [333, 334], -5, -4, -3, -2]], [17, -1, 0], [5, 1280, 326]], [6, "xia<PERSON>ie", false, [-10, -11, -12, -13, -14, -15, -16, -17, -18, -19, -20, -21, -22, -23, -24, -25, -26, -27, -28, -29, -30, -31, -32, -33, -34, -35, -36, -37, -38, -39, -40], [0, "686zZFzBNLaIIb43u3R7Qj", 1, 0], [5, 1280, 326]], [6, "daxie", false, [-41, -42, -43, -44, -45, -46, -47, -48, -49, -50, -51, -52, -53, -54, -55, -56, -57, -58, -59, -60, -61, -62, -63, -64, -65, -66, -67, -68, -69, -70, -71], [0, "96jefv9a1Heo6yTGqGJK6w", 1, 0], [5, 1280, 326]], [6, "<PERSON><PERSON><PERSON><PERSON><PERSON>", false, [-72, -73, -74, -75, -76, -77, -78, -79, -80, -81, -82, -83, -84, -85, -86, -87, -88, -89, -90, -91, -92], [0, "f3HWr2jkdAaoMZKXOKgE8X", 1, 0], [5, 1280, 326]], [11, "group", 1, [-94, 2, 3, 4], [[18, 2, 4, -93]], [0, "3epw9wN/VKy5vkaC5HxAl6", 1, 0], [5, 1280, 326]], [12, "ctril", 1, [-97], [[5, 1, -95, [2], 3], [19, 2, 1, -1.0490000000000066, -96]], [0, "64y0SEEQ9Of5jWOan1AWSV", 1, 0], [5, 130, 60], [421.177, 134.049, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "q", 2, [[3, 1, 2, false, -98, [6], 7], [2, 2, -99, 8, 9]], [0, "5en/hWFvNPRbaCagvYyCt1", 1, 0], [5, 96, 64], [-573.172, 111.291, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "w", 2, [[3, 1, 2, false, -100, [10], 11], [2, 2, -101, 12, 13]], [0, "33Q1aW0LpCoJsjBZfuVqCB", 1, 0], [5, 96, 64], [-458.582, 108.661, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "e", 2, [[3, 1, 2, false, -102, [14], 15], [2, 2, -103, 16, 17]], [0, "50ChjmpgBAYYBy8+KPdc7P", 1, 0], [5, 96, 64], [-347.956, 109.524, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "r", 2, [[3, 1, 2, false, -104, [18], 19], [2, 2, -105, 20, 21]], [0, "f5AFsMvDNIGKK5AQBk96bP", 1, 0], [5, 96, 64], [-236.075, 110.962, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "t", 2, [[3, 1, 2, false, -106, [22], 23], [2, 2, -107, 24, 25]], [0, "53G9ckaHFA0aLFdj8EsDL3", 1, 0], [5, 96, 64], [-124.111, 109.606, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "y", 2, [[3, 1, 2, false, -108, [26], 27], [2, 2, -109, 28, 29]], [0, "10L7gtJGlFTr3AmD3+e5Dc", 1, 0], [5, 96, 64], [-12.093, 108.069, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "u", 2, [[3, 1, 2, false, -110, [30], 31], [2, 2, -111, 32, 33]], [0, "036X/zS4tNxaqpwrqQN5jm", 1, 0], [5, 96, 64], [101.318, 108.949, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "i", 2, [[3, 1, 2, false, -112, [34], 35], [2, 2, -113, 36, 37]], [0, "8c0HLKzEpCiYwKrzwfG3Km", 1, 0], [5, 96, 64], [212.578, 109.524, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "o", 2, [[3, 1, 2, false, -114, [38], 39], [2, 2, -115, 40, 41]], [0, "1ep5nSZcRKrowG3AVlVp6R", 1, 0], [5, 96, 64], [323.729, 111.537, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "p", 2, [[3, 1, 2, false, -116, [42], 43], [2, 2, -117, 44, 45]], [0, "0eq2fJOzpFWKZNtE90DTh9", 1, 0], [5, 96, 64], [436.107, 111.824, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "a", 2, [[3, 1, 2, false, -118, [46], 47], [2, 2, -119, 48, 49]], [0, "20z6eX88ZCzKh3pLxR1+mV", 1, 0], [5, 96, 64], [-507.958, 34.487, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "s", 2, [[3, 1, 2, false, -120, [50], 51], [2, 2, -121, 52, 53]], [0, "61I8z7W7hCG7zflcUjFv+b", 1, 0], [5, 96, 64], [-396.367, 31.68, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "d", 2, [[3, 1, 2, false, -122, [54], 55], [2, 2, -123, 56, 57]], [0, "bfOFg0TqBCKZtfLhJPShWS", 1, 0], [5, 96, 64], [-283.3, 32.239, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "f", 2, [[3, 1, 2, false, -124, [58], 59], [2, 2, -125, 60, 61]], [0, "60aWAcflJLMKkhs8QZhUXE", 1, 0], [5, 96, 64], [-172.285, 34.187, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "g", 2, [[3, 1, 2, false, -126, [62], 63], [2, 2, -127, 64, 65]], [0, "fcy2tJVrZOMrX1FBWbwObo", 1, 0], [5, 96, 64], [-59.871, 34.43, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "h", 2, [[3, 1, 2, false, -128, [66], 67], [2, 2, -129, 68, 69]], [0, "1bg8lHLSJGJb6RxrCsq8jr", 1, 0], [5, 96, 64], [51.785, 33.943, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "j", 2, [[3, 1, 2, false, -130, [70], 71], [2, 2, -131, 72, 73]], [0, "4fWh1t+LhFkrRroHq9cVcI", 1, 0], [5, 96, 64], [163.783, 34.43, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "k", 2, [[3, 1, 2, false, -132, [74], 75], [2, 2, -133, 76, 77]], [0, "0f4J67XSdMd732OidqZVD6", 1, 0], [5, 96, 64], [275.916, 33.212, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "l", 2, [[3, 1, 2, false, -134, [78], 79], [2, 2, -135, 80, 81]], [0, "a4APvIfsNHWqUec7h7PCAm", 1, 0], [5, 96, 64], [387.063, 32.969, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "z", 2, [[3, 1, 2, false, -136, [82], 83], [2, 2, -137, 84, 85]], [0, "74AIiAAQdPxbxSptjcMt24", 1, 0], [5, 96, 64], [-395.565, -42.717, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "x", 2, [[3, 1, 2, false, -138, [86], 87], [2, 2, -139, 88, 89]], [0, "a3AyxMxQlPLKwFW/CbopwJ", 1, 0], [5, 96, 64], [-284.076, -42.378, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "c", 2, [[3, 1, 2, false, -140, [90], 91], [2, 2, -141, 92, 93]], [0, "e7h4m9gLNFeaNhW+DwCMf8", 1, 0], [5, 96, 64], [-172.669, -42.717, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "v", 2, [[3, 1, 2, false, -142, [94], 95], [2, 2, -143, 96, 97]], [0, "48oT0BlltBmLuxw72yC59b", 1, 0], [5, 96, 64], [-60.307, -42.717, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "b", 2, [[3, 1, 2, false, -144, [98], 99], [2, 2, -145, 100, 101]], [0, "58NHcqmIRORKsUXfvHH3na", 1, 0], [5, 96, 64], [52.323, -43.396, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "n", 2, [[3, 1, 2, false, -146, [102], 103], [2, 2, -147, 104, 105]], [0, "7eloWkaYZJV4fSFJ0ipfcS", 1, 0], [5, 96, 64], [163.117, -43.017, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "m", 2, [[3, 1, 2, false, -148, [106], 107], [2, 2, -149, 108, 109]], [0, "b0FSOkfxFDlKSG3vnvpGF5", 1, 0], [5, 96, 64], [276.172, -43.057, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "daxie", 2, [[3, 1, 2, false, -150, [110], 111], [7, 2, -151, 112]], [0, "dcmf/bnpRBbL2x55AVPMB2", 1, 0], [5, 96, 64], [-466, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 2, [[3, 1, 2, false, -152, [113], 114], [7, 2, -153, 115]], [0, "a0qN5tUA9CpJZPnCtGIcYd", 1, 0], [5, 175, 64], [-314.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "kongge", 2, [[3, 1, 2, false, -154, [117], 118], [2, 2, -155, 119, 120]], [0, "96GJYM991NH5L/uTgmh4If", 1, 0], [5, 264, 64], [112, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "shanchu", 2, [[3, 1, 2, false, -156, [121], 122], [2, 2, -157, 123, 124]], [0, "24J7lmu8tNR6psc7ZNquAK", 1, 0], [5, 152, 64], [336, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Q", 3, [[4, 1, 2, -158, [125], 126], [2, 2, -159, 127, 128]], [0, "53MQ0WScNO9Zb7y6BhjWqe", 1, 0], [5, 96, 64], [-572, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "W", 3, [[4, 1, 2, -160, [129], 130], [2, 2, -161, 131, 132]], [0, "a7os4HsAtG5oCAQ/b4lMap", 1, 0], [5, 96, 64], [-460, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "E", 3, [[4, 1, 2, -162, [133], 134], [2, 2, -163, 135, 136]], [0, "33nJtnCStN051UpEGtMp1J", 1, 0], [5, 96, 64], [-348, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "R", 3, [[4, 1, 2, -164, [137], 138], [2, 2, -165, 139, 140]], [0, "6dF5tkfbFBna15T4jD3AJB", 1, 0], [5, 96, 64], [-236, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "T", 3, [[4, 1, 2, -166, [141], 142], [2, 2, -167, 143, 144]], [0, "93B+J7QZpF4JyY7utYiwys", 1, 0], [5, 96, 64], [-124, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Y", 3, [[4, 1, 2, -168, [145], 146], [2, 2, -169, 147, 148]], [0, "3dhZW2p+JFlqmiqLORhpfV", 1, 0], [5, 96, 64], [-12, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "U", 3, [[4, 1, 2, -170, [149], 150], [2, 2, -171, 151, 152]], [0, "57+IRduStPSL/G9cxtYCEo", 1, 0], [5, 96, 64], [100, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "I", 3, [[4, 1, 2, -172, [153], 154], [2, 2, -173, 155, 156]], [0, "07TpoDRXpKKYpiVHFWu2/1", 1, 0], [5, 96, 64], [212, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "O", 3, [[4, 1, 2, -174, [157], 158], [2, 2, -175, 159, 160]], [0, "39cGKUJQhGuKBqzE3L58cw", 1, 0], [5, 96, 64], [324, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "P", 3, [[4, 1, 2, -176, [161], 162], [2, 2, -177, 163, 164]], [0, "9dBi8iD8NLt5cuKzPaqVkD", 1, 0], [5, 96, 64], [436, 109, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "A", 3, [[4, 1, 2, -178, [165], 166], [2, 2, -179, 167, 168]], [0, "60EGALk2VMopusaK/6GJ+c", 1, 0], [5, 96, 64], [-508, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "S", 3, [[4, 1, 2, -180, [169], 170], [2, 2, -181, 171, 172]], [0, "8706l8Bt9AQLVVbDhlir0/", 1, 0], [5, 96, 64], [-396, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "D", 3, [[4, 1, 2, -182, [173], 174], [2, 2, -183, 175, 176]], [0, "f0zaKk9mpIR6mK8z6ZG6UN", 1, 0], [5, 96, 64], [-284, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "F", 3, [[4, 1, 2, -184, [177], 178], [2, 2, -185, 179, 180]], [0, "cdbpOowjxFgrtiTUeRlkGm", 1, 0], [5, 96, 64], [-172, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "G", 3, [[4, 1, 2, -186, [181], 182], [2, 2, -187, 183, 184]], [0, "41HrmiF7dOso2EU5PFvkA4", 1, 0], [5, 96, 64], [-60, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "H", 3, [[4, 1, 2, -188, [185], 186], [2, 2, -189, 187, 188]], [0, "e1+lBtms5F4r9xxqsliZVF", 1, 0], [5, 96, 64], [52, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "J", 3, [[4, 1, 2, -190, [189], 190], [2, 2, -191, 191, 192]], [0, "022DdLE7tIbIDwP6sBM7xV", 1, 0], [5, 96, 64], [164, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "K", 3, [[4, 1, 2, -192, [193], 194], [2, 2, -193, 195, 196]], [0, "9buB0foahOsbl52MN3s6Mf", 1, 0], [5, 96, 64], [276, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "L", 3, [[4, 1, 2, -194, [197], 198], [2, 2, -195, 199, 200]], [0, "31xlhJzZZFBq/RM975V3RS", 1, 0], [5, 96, 64], [388, 31, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "Z", 3, [[4, 1, 2, -196, [201], 202], [2, 2, -197, 203, 204]], [0, "8050InP7VIZ4cMxUHqaEpU", 1, 0], [5, 96, 64], [-396, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "X", 3, [[4, 1, 2, -198, [205], 206], [2, 2, -199, 207, 208]], [0, "24Ov2AoS1Pr4crbjaHqMlG", 1, 0], [5, 96, 64], [-284, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "C", 3, [[4, 1, 2, -200, [209], 210], [2, 2, -201, 211, 212]], [0, "a7ay1wvzdKhrQFYU8PPtc4", 1, 0], [5, 96, 64], [-172, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "V", 3, [[4, 1, 2, -202, [213], 214], [2, 2, -203, 215, 216]], [0, "ba056y5y1J/7+wD0aYBi6B", 1, 0], [5, 96, 64], [-60, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "B", 3, [[4, 1, 2, -204, [217], 218], [2, 2, -205, 219, 220]], [0, "0bjN7wJLNPLpnGBWv0jsVr", 1, 0], [5, 96, 64], [52, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "N", 3, [[4, 1, 2, -206, [221], 222], [2, 2, -207, 223, 224]], [0, "91LddqpLZIW6yB9rkq3A3l", 1, 0], [5, 96, 64], [164, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "M", 3, [[4, 1, 2, -208, [225], 226], [2, 2, -209, 227, 228]], [0, "eaIWB2+XtKrYBNoteIoJGL", 1, 0], [5, 96, 64], [276, -46, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "xia<PERSON>ie", 3, [[4, 1, 2, -210, [229], 230], [2, 2, -211, 231, 232]], [0, "d7k0/qiSNG6Yb9B6JkHfnA", 1, 0], [5, 96, 64], [-466, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "<PERSON><PERSON><PERSON><PERSON><PERSON>", 3, [[4, 1, 2, -212, [233], 234], [7, 2, -213, 235]], [0, "31cHS0L3dAfqTEDMIhakg+", 1, 0], [5, 175, 64], [-314.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "kongge", 3, [[4, 1, 2, -214, [237], 238], [2, 2, -215, 239, 240]], [0, "7bp6BJihZOf4eRo457BsUe", 1, 0], [5, 264, 64], [112, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "shanchu", 3, [[4, 1, 2, -216, [241], 242], [2, 2, -217, 243, 244]], [0, "12GISzd6ZGE7Pklv63Sff3", 1, 0], [5, 152, 64], [336, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "1", 4, [[5, 1, -218, [245], 246], [2, 2, -219, 247, 248]], [0, "77OdrAXERN5bpuO/gTPdYP", 1, 0], [5, 96, 64], [-572, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "2", 4, [[5, 1, -220, [249], 250], [2, 2, -221, 251, 252]], [0, "52PEmrOL9A5JaSKYGrneyB", 1, 0], [5, 96, 64], [-460, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "3", 4, [[5, 1, -222, [253], 254], [2, 2, -223, 255, 256]], [0, "39IxT8whNEB7oqMIFSs1Dq", 1, 0], [5, 96, 64], [-348, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "4", 4, [[5, 1, -224, [257], 258], [2, 2, -225, 259, 260]], [0, "eedRU33XxPY6ONJbvHH80q", 1, 0], [5, 96, 64], [-236, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "5", 4, [[5, 1, -226, [261], 262], [2, 2, -227, 263, 264]], [0, "a2dc3xl7tC+6EcwcZqCywK", 1, 0], [5, 96, 64], [-124, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "6", 4, [[5, 1, -228, [265], 266], [2, 2, -229, 267, 268]], [0, "19AsJiRdNBIbWoRGNrhUq6", 1, 0], [5, 96, 64], [-12, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "7", 4, [[5, 1, -230, [269], 270], [2, 2, -231, 271, 272]], [0, "d1UzcuA+FIUqg7spzg7rEX", 1, 0], [5, 96, 64], [100, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "8", 4, [[5, 1, -232, [273], 274], [2, 2, -233, 275, 276]], [0, "8ak1OSLuxN9ZDrbpi/5EHJ", 1, 0], [5, 96, 64], [212, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "9", 4, [[5, 1, -234, [277], 278], [2, 2, -235, 279, 280]], [0, "17sOE8urtA17EvgIm2Held", 1, 0], [5, 96, 64], [324, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "0", 4, [[5, 1, -236, [281], 282], [2, 2, -237, 283, 284]], [0, "21eN+fA9FDxbEdAvecQwVc", 1, 0], [5, 96, 64], [436, 108, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "-", 4, [[5, 1, -238, [285], 286], [2, 2, -239, 287, 288]], [0, "90QubeikZDxqU6d9+pBAaI", 1, 0], [5, 208, 64], [-384, 34, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "\"", 4, [[5, 1, -240, [289], 290], [2, 2, -241, 291, 292]], [0, "f7rR620R1NWblub3t0++kF", 1, 0], [5, 208, 64], [-160, 34, 0, 0, 0, 0, 1, 1, 1, 1]], [1, ":", 4, [[5, 1, -242, [293], 294], [2, 2, -243, 295, 296]], [0, "53WzGrmVZK6psODbD3FmF5", 1, 0], [5, 208, 64], [64, 34, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "'", 4, [[5, 1, -244, [297], 298], [2, 2, -245, 299, 300]], [0, "8aF13NkNNCY6DN22rStmiL", 1, 0], [5, 208, 64], [288, 34, 0, 0, 0, 0, 1, 1, 1, 1]], [1, ",", 4, [[5, 1, -246, [301], 302], [2, 2, -247, 303, 304]], [0, "8b1mNEDSpH7oYCyl1UL1z5", 1, 0], [5, 208, 64], [-423, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "!", 4, [[5, 1, -248, [305], 306], [2, 2, -249, 307, 308]], [0, "adSG8rZVlPtohFMzkPVw8l", 1, 0], [5, 208, 64], [-199, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "?", 4, [[5, 1, -250, [309], 310], [2, 2, -251, 311, 312]], [0, "7aspBAw5NOmpEAcHs/PAul", 1, 0], [5, 208, 64], [25, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, ".", 4, [[5, 1, -252, [313], 314], [2, 2, -253, 315, 316]], [0, "2dhr0seN5DxL1U4/F6ugwg", 1, 0], [5, 208, 64], [249, -40, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "xia<PERSON>ie", 4, [[5, 1, -254, [317], 318], [2, 2, -255, 319, 320]], [0, "93bO55sA1LWJcoIrr6XQXs", 1, 0], [5, 152, 64], [-376, -114, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "kongge", 4, [[5, 1, -256, [321], 322], [2, 2, -257, 323, 324]], [0, "c1zWDt939NVKXwmaKJUPtL", 1, 0], [5, 432, 64], [-68, -114, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "shanchu", 4, [[5, 1, -258, [325], 326], [2, 2, -259, 327, 328]], [0, "64aLE502BAR7Xcg/y2LPZM", 1, 0], [5, 152, 64], [240, -114, 0, 0, 0, 0, 1, 1, 1, 1]], [13, "zhankai", 6, [[5, 1, -260, [0], 1]], [0, "1clsvETUhLtqW39JOTDR6h", 1, 0], [5, 130, 60]], [14, "bg", 512, 5, [[5, 1, -261, [4], 5]], [0, "f5cpRckFZJxpmXNA6YOsL+", 1, 0], [5, 1280, 328]], [8, "az", 2, [-262], [0, "14tWK2ppRHB60tdufRSIS6", 1, 0], [5, 175, 64], [-123.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [20, 1, 2, false, 90, [116]], [8, "az", 3, [-263], [0, "0f/lYeUfdNvZDxeRwVfoUO", 1, 0], [5, 175, 64], [-123.5, -122, 0, 0, 0, 0, 1, 1, 1, 1]], [21, 1, 2, 92, [236]], [15, "pcGuide", false, 1, [[5, 1, -264, [329], 330]], [0, "f1GV5sOmRI8os7jv162jio", 1, 0], [5, 335, 73], [-401.012, 209.375, 0, 0, 0, 0, 1, 1, 1, 1]]], 0, [0, 6, 1, 0, 7, 3, 0, 8, 2, 0, 9, 93, 0, 10, 91, 0, 0, 1, 0, -1, 6, 0, -2, 5, 0, -3, 94, 0, -1, 7, 0, -2, 8, 0, -3, 9, 0, -4, 10, 0, -5, 11, 0, -6, 12, 0, -7, 13, 0, -8, 14, 0, -9, 15, 0, -10, 16, 0, -11, 17, 0, -12, 18, 0, -13, 19, 0, -14, 20, 0, -15, 21, 0, -16, 22, 0, -17, 23, 0, -18, 24, 0, -19, 25, 0, -20, 26, 0, -21, 27, 0, -22, 28, 0, -23, 29, 0, -24, 30, 0, -25, 31, 0, -26, 32, 0, -27, 33, 0, -28, 34, 0, -29, 90, 0, -30, 35, 0, -31, 36, 0, -1, 37, 0, -2, 38, 0, -3, 39, 0, -4, 40, 0, -5, 41, 0, -6, 42, 0, -7, 43, 0, -8, 44, 0, -9, 45, 0, -10, 46, 0, -11, 47, 0, -12, 48, 0, -13, 49, 0, -14, 50, 0, -15, 51, 0, -16, 52, 0, -17, 53, 0, -18, 54, 0, -19, 55, 0, -20, 56, 0, -21, 57, 0, -22, 58, 0, -23, 59, 0, -24, 60, 0, -25, 61, 0, -26, 62, 0, -27, 63, 0, -28, 64, 0, -29, 92, 0, -30, 65, 0, -31, 66, 0, -1, 67, 0, -2, 68, 0, -3, 69, 0, -4, 70, 0, -5, 71, 0, -6, 72, 0, -7, 73, 0, -8, 74, 0, -9, 75, 0, -10, 76, 0, -11, 77, 0, -12, 78, 0, -13, 79, 0, -14, 80, 0, -15, 81, 0, -16, 82, 0, -17, 83, 0, -18, 84, 0, -19, 85, 0, -20, 86, 0, -21, 87, 0, 0, 5, 0, -1, 89, 0, 0, 6, 0, 0, 6, 0, -1, 88, 0, 0, 7, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, 0, 16, 0, 0, 16, 0, 0, 17, 0, 0, 17, 0, 0, 18, 0, 0, 18, 0, 0, 19, 0, 0, 19, 0, 0, 20, 0, 0, 20, 0, 0, 21, 0, 0, 21, 0, 0, 22, 0, 0, 22, 0, 0, 23, 0, 0, 23, 0, 0, 24, 0, 0, 24, 0, 0, 25, 0, 0, 25, 0, 0, 26, 0, 0, 26, 0, 0, 27, 0, 0, 27, 0, 0, 28, 0, 0, 28, 0, 0, 29, 0, 0, 29, 0, 0, 30, 0, 0, 30, 0, 0, 31, 0, 0, 31, 0, 0, 32, 0, 0, 32, 0, 0, 33, 0, 0, 33, 0, 0, 34, 0, 0, 34, 0, 0, 35, 0, 0, 35, 0, 0, 36, 0, 0, 36, 0, 0, 37, 0, 0, 37, 0, 0, 38, 0, 0, 38, 0, 0, 39, 0, 0, 39, 0, 0, 40, 0, 0, 40, 0, 0, 41, 0, 0, 41, 0, 0, 42, 0, 0, 42, 0, 0, 43, 0, 0, 43, 0, 0, 44, 0, 0, 44, 0, 0, 45, 0, 0, 45, 0, 0, 46, 0, 0, 46, 0, 0, 47, 0, 0, 47, 0, 0, 48, 0, 0, 48, 0, 0, 49, 0, 0, 49, 0, 0, 50, 0, 0, 50, 0, 0, 51, 0, 0, 51, 0, 0, 52, 0, 0, 52, 0, 0, 53, 0, 0, 53, 0, 0, 54, 0, 0, 54, 0, 0, 55, 0, 0, 55, 0, 0, 56, 0, 0, 56, 0, 0, 57, 0, 0, 57, 0, 0, 58, 0, 0, 58, 0, 0, 59, 0, 0, 59, 0, 0, 60, 0, 0, 60, 0, 0, 61, 0, 0, 61, 0, 0, 62, 0, 0, 62, 0, 0, 63, 0, 0, 63, 0, 0, 64, 0, 0, 64, 0, 0, 65, 0, 0, 65, 0, 0, 66, 0, 0, 66, 0, 0, 67, 0, 0, 67, 0, 0, 68, 0, 0, 68, 0, 0, 69, 0, 0, 69, 0, 0, 70, 0, 0, 70, 0, 0, 71, 0, 0, 71, 0, 0, 72, 0, 0, 72, 0, 0, 73, 0, 0, 73, 0, 0, 74, 0, 0, 74, 0, 0, 75, 0, 0, 75, 0, 0, 76, 0, 0, 76, 0, 0, 77, 0, 0, 77, 0, 0, 78, 0, 0, 78, 0, 0, 79, 0, 0, 79, 0, 0, 80, 0, 0, 80, 0, 0, 81, 0, 0, 81, 0, 0, 82, 0, 0, 82, 0, 0, 83, 0, 0, 83, 0, 0, 84, 0, 0, 84, 0, 0, 85, 0, 0, 85, 0, 0, 86, 0, 0, 86, 0, 0, 87, 0, 0, 87, 0, 0, 88, 0, 0, 89, 0, -1, 91, 0, -1, 93, 0, 0, 94, 0, 11, 1, 2, 5, 5, 3, 5, 5, 4, 5, 5, 264], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 91, 93], [-1, 2, -1, 2, -1, 2, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, -1, 2, 3, -1, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, -1, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, 3, 4, -1, 2, -1, -2, -1, -2, 2, 2], [1, 85, 1, 86, 1, 87, 1, 6, 6, 88, 1, 7, 7, 89, 1, 8, 8, 90, 1, 9, 9, 91, 1, 10, 10, 92, 1, 11, 11, 93, 1, 12, 12, 94, 1, 13, 13, 95, 1, 14, 14, 96, 1, 15, 15, 97, 1, 16, 16, 98, 1, 17, 17, 99, 1, 18, 18, 100, 1, 19, 19, 101, 1, 20, 20, 102, 1, 21, 21, 103, 1, 22, 22, 104, 1, 23, 23, 105, 1, 24, 24, 106, 1, 25, 25, 107, 1, 26, 26, 108, 1, 27, 27, 109, 1, 28, 28, 110, 1, 29, 29, 111, 1, 30, 30, 112, 1, 31, 31, 113, 1, 32, 32, 1, 3, 3, 1, 1, 4, 4, 33, 1, 5, 5, 34, 1, 35, 35, 114, 1, 36, 36, 115, 1, 37, 37, 116, 1, 38, 38, 117, 1, 39, 39, 118, 1, 40, 40, 119, 1, 41, 41, 120, 1, 42, 42, 121, 1, 43, 43, 122, 1, 44, 44, 123, 1, 45, 45, 124, 1, 46, 46, 125, 1, 47, 47, 126, 1, 48, 48, 127, 1, 49, 49, 128, 1, 50, 50, 129, 1, 51, 51, 130, 1, 52, 52, 131, 1, 53, 53, 132, 1, 54, 54, 133, 1, 55, 55, 134, 1, 56, 56, 135, 1, 57, 57, 136, 1, 58, 58, 137, 1, 59, 59, 138, 1, 60, 60, 139, 1, 61, 61, 140, 1, 3, 3, 1, 1, 4, 4, 33, 1, 5, 5, 34, 1, 62, 62, 141, 1, 63, 63, 142, 1, 64, 64, 143, 1, 65, 65, 144, 1, 66, 66, 145, 1, 67, 67, 146, 1, 68, 68, 147, 1, 69, 69, 148, 1, 70, 70, 149, 1, 71, 71, 150, 1, 72, 72, 151, 1, 73, 73, 152, 1, 74, 74, 153, 1, 75, 75, 154, 1, 76, 76, 155, 1, 77, 77, 156, 1, 78, 78, 157, 1, 79, 79, 158, 1, 80, 80, 159, 1, 81, 81, 160, 1, 82, 82, 161, 1, 162, 83, 163, 84, 164, 83, 84]], [[{"name": "H", "rect": [839, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "m", "rect": [941, 245, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "!", "rect": [207, 283, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "M", "rect": [3, 279, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "j", "rect": [105, 279, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "B", "rect": [421, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "j", "rect": [523, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "c", "rect": [625, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [727, 283, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "q", "rect": [3, 349, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "Q-<PERSON>", "rect": [207, 353, 175, 64], "offset": [0, 0], "originalSize": [175, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "I", "rect": [105, 349, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "'", "rect": [388, 353, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "7", "rect": [602, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "O", "rect": [704, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "w", "rect": [806, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "1", "rect": [908, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "d", "rect": [3, 419, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "kongge", "rect": [207, 423, 432, 64], "offset": [0, 0], "originalSize": [432, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "shanchu", "rect": [645, 423, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "L", "rect": [105, 419, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "k", "rect": [803, 423, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "Q", "rect": [905, 423, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "u", "rect": [3, 489, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "9", "rect": [105, 489, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "a", "rect": [207, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "J", "rect": [309, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "c", "rect": [411, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "D", "rect": [513, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "select_kongge", "rect": [615, 493, 264, 64], "offset": [0, 0], "originalSize": [264, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "z", "rect": [885, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "I", "rect": [3, 559, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "'", "rect": [207, 563, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [421, 563, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "4", "rect": [105, 559, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "V", "rect": [635, 563, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "u", "rect": [737, 563, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "C", "rect": [839, 563, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "L", "rect": [941, 563, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "T", "rect": [3, 629, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "U", "rect": [105, 629, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "kongge", "rect": [207, 633, 264, 64], "offset": [0, 0], "originalSize": [264, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "m", "rect": [477, 633, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "S", "rect": [579, 633, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "zimu", "rect": [681, 633, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "G", "rect": [839, 633, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "E", "rect": [941, 665, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "W", "rect": [3, 699, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "p", "rect": [105, 699, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "-", "rect": [207, 703, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "v", "rect": [421, 703, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "select_shanchu", "rect": [523, 703, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "C", "rect": [681, 703, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "A", "rect": [783, 703, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "a-z", "rect": [3, 769, 175, 64], "offset": [0, 0], "originalSize": [175, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "k", "rect": [885, 767, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "f", "rect": [184, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "o", "rect": [286, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "n", "rect": [388, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "4", "rect": [490, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "0", "rect": [592, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "e", "rect": [694, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "Y", "rect": [796, 773, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "X", "rect": [866, 837, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "0", "rect": [3, 839, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "s", "rect": [105, 843, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "x", "rect": [207, 843, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": ",", "rect": [309, 843, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "3", "rect": [523, 843, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "1", "rect": [625, 843, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "i", "rect": [727, 875, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "i", "rect": [829, 907, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "B", "rect": [3, 909, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "zhankai", "rect": [139, 3, 130, 60], "offset": [0, 0], "originalSize": [130, 60], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "h", "rect": [105, 913, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "t", "rect": [207, 913, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "daxie", "rect": [309, 913, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [411, 913, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "shuangyinh<PERSON>", "rect": [3, 3, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "S", "rect": [625, 913, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "l", "rect": [931, 907, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "rotated": 1, "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [217, 3, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "Z", "rect": [727, 945, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [0]], [[{"name": "H", "rect": [431, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "O", "rect": [533, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "v", "rect": [635, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "A", "rect": [737, 3, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "U", "rect": [3, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "r", "rect": [105, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "a", "rect": [207, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "Q", "rect": [309, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "l", "rect": [411, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "f", "rect": [513, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "8", "rect": [615, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "q", "rect": [717, 73, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "F", "rect": [3, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "3", "rect": [105, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "8", "rect": [207, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [309, 143, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "<PERSON><PERSON><PERSON>", "rect": [523, 143, 208, 64], "offset": [0, 0], "originalSize": [208, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "W", "rect": [737, 143, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "p", "rect": [3, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "q-m", "rect": [105, 213, 175, 64], "offset": [0, 0], "originalSize": [175, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "w", "rect": [286, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "2", "rect": [388, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "shanchu", "rect": [490, 213, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "s", "rect": [648, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "5", "rect": [750, 213, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "z", "rect": [3, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "pcGuide", "rect": [207, 493, 335, 74], "offset": [0, 0], "originalSize": [335, 74], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "bg", "rect": [0, 0, 1280, 328], "offset": [0, 0], "originalSize": [1280, 328], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [165]], [[{"name": "Z", "rect": [105, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "E", "rect": [207, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "6", "rect": [309, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "y", "rect": [411, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "o", "rect": [513, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "F", "rect": [615, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "g", "rect": [717, 283, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "n", "rect": [3, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "y", "rect": [105, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "X", "rect": [207, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "Y", "rect": [309, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "D", "rect": [411, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "J", "rect": [513, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "R", "rect": [615, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "zimu", "rect": [3, 423, 152, 64], "offset": [0, 0], "originalSize": [152, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "K", "rect": [717, 353, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rect": [161, 423, 175, 64], "offset": [0, 0], "originalSize": [175, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "G", "rect": [342, 423, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "A-Z", "rect": [444, 423, 175, 64], "offset": [0, 0], "originalSize": [175, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "7", "rect": [625, 423, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "xia<PERSON>ie", "rect": [727, 423, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "9", "rect": [3, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]], [[{"name": "6", "rect": [105, 493, 96, 64], "offset": [0, 0], "originalSize": [96, 64], "capInsets": [0, 0, 0, 0]}], [0], 0, [0], [1], [2]]]]