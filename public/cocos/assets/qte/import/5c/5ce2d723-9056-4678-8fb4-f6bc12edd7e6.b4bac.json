[1, 0, 0, [["cc.TextAsset", ["_name", "text"], 1]], [[0, 0, 1, 3]], [[0, "qte-core.d", "/*\n * @Author: your name\n * @Date: 2021-09-02 13:52:43\n * @LastEditTime : 2023-05-12 20:08:47\n * @LastEditors  : Please set LastEditors\n * @Description: In User Settings Edit\n * @FilePath     : /sdk/assets/qte/qte-core.d.ts\n */\ndeclare namespace qte {\n\n    export class SingleBase {\n        public initInstance();\n    }\n\n    // eslint-disable-next-line @typescript-eslint/class-name-casing\n    export class single {\n        static instance<T extends SingleBase>(type: new () => T, symbol?: string): T;\n        static destory<T extends SingleBase>(type: new () => T, symbol?: string): void;\n        static destoryAll(): void;\n    }\n\n    // 单例相关接口导出\n    export function instance<T extends SingleBase>(type: new () => T, symbol?: string): T;\n    export function destoryAll<T extends SingleBase>(except?: new () => T): void;\n    export function destoryBySymbol(symbol: string): void;\n\n\n    // LogUtil 日志相关\n    export function log(msg: any, ...subst: any[]): void;\n    export function warn(msg: any, ...subst: any[]): void;\n    export function debug(msg: any, ...subst: any[]): void;\n    export function error(msg: any, ...subst: any[]): void;\n    export function logTrace(message?: any): void;\n    export function logCatToNative(type:QTE_LOGCAT_TYPE,args):void;\n    export function logCatToAPM(type,args):void;\n    export function logCatBoth(type,args):void;\n    export function tracePm(type,param?):void;\n\n\n    // --------------   utils   ----------------\n    /**\n     * 检查数值有效性\n     * @param val 数值\n     * @param d 无效时的默认值\n     */\n    export function checkValue<T>(val: T, d?: T): T;\n    /** clone 数值 */\n    export function cloneValues(obj: any);\n    /** 生成唯一uuid */\n    export function genUUID(key?: string): string;\n\n    // eslint-disable-next-line @typescript-eslint/class-name-casing\n    export class loader {\n        /**\n         * 通用资源加载接口（包括本地资源、网络资源和远程资源）\n         * @param {string} path 资源路径，可以是本地资源、网络资源和远程资源\n         * @param {cc.Asset | Record<string, any>} options 资源类型 | 远程资源可选参数\n         * @param {(err, res) => void} onComplete 加载完成回调\n         * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。\n         * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度\n         */\n        static loadRes(path: string, type: typeof cc.Asset | Record<string, any>, onComplete: (err, res) => void, bundle?: cc.AssetManager.Bundle | string, onProgress?: (finish: number, total: number, item: cc.AssetManager.RequestItem) => void): void;\n\n        /**\n         * 加载目录\n         * @param {string} dir 资源目录\n         * @param {cc.Asset} type 资源类型\n         * @param {(finish: number, total: number, item: cc.AssetManager.RequestItem) => void} onProgress 加载进度回调\n         * @param {(error: Error, assets: Array<T>) => void} onComplete 加载完成回调\n         * @param {cc.AssetManager.Bundle | string} bundle 资源所属bundle，可选。 \n         */\n        static loadDir<T extends cc.Asset>(dir: string, type: typeof cc.Asset, onProgress: (finish: number, total: number, item: cc.AssetManager.RequestItem) => void, onComplete: (error: Error, assets: Array<T>) => void, bundle?: cc.AssetManager.Bundle | string): void;\n\n        /**\n         * 加载bundle\n         * @param {string} nameOrUrl bundle名称或地址\n         * @param {Record<string, any>} options 下载bundle的可选参数\n         * @param {(err: Error, bundle: cc.AssetManager.Bundle) => void} onComplete 加载完成回调\n         */\n        loadBundle(nameOrUrl: string, options: Record<string, any>, onComplete: (err: Error, bundle: cc.AssetManager.Bundle) => void): void;\n\n        /**\n         * 注册观察者\n         * @param {LoaderObserver} observer 自定义观察者\n         */\n        static addObserver(observer: LoaderObserver): void;\n        /**\n         * 移除观察者\n         * @param {LoaderObserver} observer 自定义观察者 \n         * @returns {boolean} 是否移除成功\n         */\n        static removeObserver(observer: LoaderObserver): boolean;\n    }\n\n}"]], 0, 0, [], [], []]