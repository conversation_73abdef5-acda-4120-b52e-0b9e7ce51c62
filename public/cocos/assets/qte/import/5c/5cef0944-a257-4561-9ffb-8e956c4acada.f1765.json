[1, ["ecpdLyjvZBwrvm+cedCcQy", "827dFyqQZM0YXyn898vF1d", "f5xN0W8bNEsYDAUDCTLUFd", "2fBqdWBYRCi5gziu3QhyCo", "6bXKX48/dMf7zbXAe1GIsn", "d559ZysGRMPoKFAAtTHiGT"], ["node", "_spriteFrame", "root", "H_prefab", "P_prefab", "draw", "rebbage", "timu_group", "data"], [["cc.Node", ["_name", "_obj<PERSON><PERSON>s", "_prefab", "_contentSize", "_components", "_parent", "_trs", "_children", "_color"], 1, 4, 5, 9, 1, 7, 2, 5], ["cc.Sprite", ["_srcBlendFactor", "_type", "node", "_materials", "_spriteFrame"], 1, 1, 3, 6], ["cc.PrefabInfo", ["fileId", "root"], 2, 1], ["cc.Label", ["_string", "_fontSize", "_N$horizontalAlign", "_N$verticalAlign", "_styleFlags", "_lineHeight", "node", "_materials"], -3, 1, 3], ["cc.Widget", ["_alignFlags", "_originalWidth", "_originalHeight", "_left", "_right", "_top", "_bottom", "node"], -4, 1], ["cc.Prefab", ["_name"], 2], ["cc.Node", ["_name", "_parent", "_components", "_prefab", "_contentSize"], 2, 1, 2, 4, 5], ["e2e14btcdlLI5mE4KMzIyz/", ["node"], 3, 1], ["cc.Layout", ["_resize", "_N$layoutType", "_N$spacingX", "node", "_layoutSize"], 0, 1, 5], ["0e293s3FWVLMaWUxihQlDZn", ["node"], 3, 1], ["51e7fFAHcVFyLPJQ5EQVjSY", ["node", "timu_group", "rebbage", "draw", "H_prefab", "P_prefab"], 3, 1, 1, 1, 1, 6, 6]], [[2, 0, 1, 2], [0, 0, 5, 4, 2, 3, 2], [1, 2, 3, 4, 1], [0, 0, 5, 7, 4, 2, 3, 6, 2], [0, 0, 5, 4, 2, 8, 3, 2], [3, 0, 1, 5, 2, 3, 6, 7, 6], [4, 0, 1, 2, 7, 4], [4, 0, 3, 4, 5, 6, 1, 2, 7, 8], [7, 0, 1], [5, 0, 2], [0, 0, 7, 4, 2, 3, 2], [0, 0, 5, 4, 2, 3, 6, 2], [0, 0, 5, 4, 2, 8, 3, 6, 2], [0, 0, 1, 5, 7, 4, 2, 3, 6, 3], [0, 0, 5, 7, 2, 3, 6, 2], [6, 0, 1, 2, 3, 4, 2], [1, 0, 1, 2, 3, 4, 3], [2, 1, 1], [3, 0, 1, 4, 2, 3, 6, 7, 6], [8, 0, 1, 2, 3, 4, 4], [9, 0, 1], [10, 0, 1, 2, 3, 4, 5, 1]], [[9, "HtoP"], [10, "HtoP", [-6, -7, -8, -9], [[21, -5, -4, -3, -2, 17, 18]], [17, -1], [5, 1280, 720]], [13, "timu_group", 512, 1, [-11, -12, -13, -14], [[19, 1, 1, 200, -10, [5, 968, 166]]], [0, "d42LcGJn1N668Hm8DHlDhI", 1], [5, 968, 166], [0, 134.742, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "H", 2, [-17, -18], [[8, -16]], [0, "05nYkgQ1ZBrLiM5k/bBg0l", -15], [5, 92, 92], [-438, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "H", 2, [-21, -22], [[8, -20]], [0, "ecU3zusRFM7oqUWGbPKd9T", -19], [5, 92, 92], [-146, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "H", 2, [-25, -26], [[8, -24]], [0, "dccoJKA6NNuKKtfbyWjhWd", -23], [5, 92, 92], [146, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [3, "H", 2, [-29, -30], [[8, -28]], [0, "f9rBcWvd1E/6ZVYXr5TFVF", -27], [5, 92, 92], [438, 0, 0, 0, 0, 0, 1, 1, 1, 1]], [11, "lajitong", 1, [[2, -31, [0], 1]], [0, "43OH6OvadFI5ICuqCzfJ/q", 1], [5, 72, 72], [0, -318.556, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 3, [[2, -32, [3], 4], [6, 45, 77, 98, -33]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 3], [5, 92, 92]], [4, "label", 3, [[5, "生", 77, 77, 1, 1, -34, [5]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -35]], [0, "d5f5UruGhA9IQWYSnwRjwM", 3], [4, 4278190084], [5, 77, 98]], [1, "bg", 4, [[2, -36, [6], 7], [6, 45, 77, 98, -37]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 4], [5, 92, 92]], [4, "label", 4, [[5, "生", 77, 77, 1, 1, -38, [8]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -39]], [0, "d5f5UruGhA9IQWYSnwRjwM", 4], [4, 4278190084], [5, 77, 98]], [1, "bg", 5, [[2, -40, [9], 10], [6, 45, 77, 98, -41]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 5], [5, 92, 92]], [4, "label", 5, [[5, "生", 77, 77, 1, 1, -42, [11]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -43]], [0, "d5f5UruGhA9IQWYSnwRjwM", 5], [4, 4278190084], [5, 77, 98]], [1, "bg", 6, [[2, -44, [12], 13], [6, 45, 77, 98, -45]], [0, "a9GHwSqtFHkLpyHCtWGQIH", 6], [5, 92, 92]], [4, "label", 6, [[5, "生", 77, 77, 1, 1, -46, [14]], [7, 45, 7.5, 7.5, -3, -3, 77, 98, -47]], [0, "d5f5UruGhA9IQWYSnwRjwM", 6], [4, 4278190084], [5, 77, 98]], [14, "drawGroup", 1, [-48, -49], [0, "72q7V7eQNIup2Mc4XqSpgt", 1], [5, 1122, 303], [0, -115.69, 0, 0, 0, 0, 1, 1, 1, 1]], [12, "timu", 1, [[18, "请写出下列文字的拼音", 34, 1, 1, 1, -50, [2]]], [0, "bccbj2hGJLOZkvYvyzQbXc", 1], [4, 4280905377], [5, 340, 51], [0, 260.228, 0, 0, 0, 0, 1, 1, 1, 1]], [1, "bg", 16, [[16, 1, 1, -51, [15], 16]], [0, "f9Wj76F/5NQruDL9FIRlUe", 1], [5, 1122, 303]], [15, "draw", 16, [-52], [0, "bafggB/PFAmYlsllP0OGth", 1], [5, 1122, 303]], [20, 19]], 0, [0, 2, 1, 0, 5, 20, 0, 6, 7, 0, 7, 2, 0, 0, 1, 0, -1, 7, 0, -2, 17, 0, -3, 2, 0, -4, 16, 0, 0, 2, 0, -1, 3, 0, -2, 4, 0, -3, 5, 0, -4, 6, 0, 2, 3, 0, 0, 3, 0, -1, 8, 0, -2, 9, 0, 2, 4, 0, 0, 4, 0, -1, 10, 0, -2, 11, 0, 2, 5, 0, 0, 5, 0, -1, 12, 0, -2, 13, 0, 2, 6, 0, 0, 6, 0, -1, 14, 0, -2, 15, 0, 0, 7, 0, 0, 8, 0, 0, 8, 0, 0, 9, 0, 0, 9, 0, 0, 10, 0, 0, 10, 0, 0, 11, 0, 0, 11, 0, 0, 12, 0, 0, 12, 0, 0, 13, 0, 0, 13, 0, 0, 14, 0, 0, 14, 0, 0, 15, 0, 0, 15, 0, -1, 18, 0, -2, 19, 0, 0, 17, 0, 0, 18, 0, -1, 20, 0, 8, 1, 52], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [-1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, -1, -1, 1, 3, 4], [0, 2, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 1, 0, 0, 3, 4, 5]]