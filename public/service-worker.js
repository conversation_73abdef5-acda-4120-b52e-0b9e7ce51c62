/* eslint-disable no-useless-escape */
/* eslint-disable no-undef */
self.addEventListener('error', function (e) {
  console.log('sw-error', e);
  self.clients.matchAll()
    .then(function (clients) {
      if (clients && clients.length) {
        clients[0].postMessage({
          type: 'ERROR',
          msg: e.message || null,
          stack: e.error ? e.error.stack : null
        });
      }
    });
});

self.addEventListener('unhandledrejection', function (e) {
  console.log('sw-unhandledrejection', e);
  self.clients.matchAll()
    .then(function (clients) {
      if (clients && clients.length) {
        clients[0].postMessage({
          type: 'REJECTION',
          msg: e.reason ? e.reason.message : null,
          stack: e.reason ? e.reason.stack : null
        });
      }
    });
})

if (location.href.includes('zuoyebang')) {
  importScripts('https://jy-fe.cdnjtzy.com/interactive-question-editor/workbox/4.3.1/workbox-sw.js');
} else {
  importScripts('./workbox/4.3.1/workbox-sw.js');
}

// eslint-disable-next-line no-undef
// importScripts('./workbox/4.3.1/workbox-sw.js');
workbox.setConfig({
  debug: false,
  modulePathPrefix: location.href.includes('zuoyebang') || location.href.includes('localhost') ? 'https://jy-fe.cdnjtzy.com/interactive-question-editor/workbox/4.3.1/' : './workbox/4.3.1/'
});

// 删除web-mobile的版本号， 版本规则：x.y.z(-alpha.m)
const replaceVersionStr = (url) => {
  // 正则表达式匹配任意路径段，后跟 /web-mobile- 及任意版本的路径
  const regex = /\/web-mobile-[\w.-]*\//;
  // 替换函数，用于将版本号部分替换为空字符串，从而只保留 /web-mobile/
  const result = url.replace(regex, '/web-mobile/');
  // console.log('replaceVersionStr', url, result);
  return result;

}

// 删除url中有/.md5./的md5值
const replaceMd5Str = (url) => {
  // 正则表达式匹配文件名后的点、5-32位的哈希值以及文件扩展名
  const regex = /\.[\w]{5,32}\.*/g;

  // 替换哈希值
  const result = url.replace(regex, '.');

  return result;
}

// 删除bundle题版的md5 cocos/dir/md5（32位）/xxx
const replace32Md5Str = (url) => {
  // 正则表达式匹配路径中的32位十六进制数（MD5哈希值）
  const regex = /\/[\da-fA-F]{32}\//g;

  // 替换MD5哈希值为一个空字符串，并去掉前后的斜杠
  const result = url.replace(regex, '/');

  return result;
}

const removeOldCache = async (cacheNames) => {
  return Promise.all(
    // 遍历所有的缓存空间
    cacheNames.map(function (cacheName) {
      if (!['the:runtime:resources'].includes(cacheName)) {
        caches.open(cacheName).then((cachedUrls) => {
          cachedUrls.keys().then(function (keys) {
            // 去掉hash或者版本号后的所有的key以及对应的文件和缓存时间
            const removeMd5Urls = {};
            const tempKeys = keys;
            tempKeys.map(function (request, index) {
              // 去除md5或版本号后文件名称
              const removeMd5Url = cacheName === 'the:runtime:web-mobile' ? replaceVersionStr(request.url) : request.url.includes('/cocos/dir/') ? replace32Md5Str(request.url) : replaceMd5Str(request.url);
              // console.log('url.xu', request.url);
              // console.log('url-replace-md5.xu', removeMd5Url);
              cachedUrls.match(request).then((res) => {
                removeMd5Urls[removeMd5Url] ? removeMd5Urls[removeMd5Url].push({
                  timestamp: new Date(res.headers.get('date')).getTime(),
                  request: request
                }) : removeMd5Urls[removeMd5Url] = [{
                  timestamp: new Date(res.headers.get('date')).getTime(),
                  request: request
                }]
                if (index === tempKeys.length - 1) {
                  return Promise.all(Object.values(removeMd5Urls).filter(item => item.length > 1).map((cacheArr) => {
                    // 遍历缓存多份的文件，只保留最新缓存的文件，其他的删除
                    if (cacheArr.length > 1) {
                      // 按照缓存的时间排序
                      cacheArr.sort((a, b) => b.timestamp - a.timestamp);
                      // 删除其他的
                      return Promise.all(cacheArr.slice(1).map((delItem) => {
                        return cachedUrls.delete(delItem.request);
                      }))
                    } else {
                      return Promise.resolve();
                    }
                  }))
                }
              })
            });
          });
        })
      }
    })
  );
}

let cacheTimer = null; // 缓存定时器
//直接激活跳过等待阶段
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  }
  if (event.data && event.data.type === "cocosInitFinished") {
    // 10s后清除缓存 防止文件删除文件缓存并发引起的请求耗时长的问题
    if (cacheTimer) {
      clearTimeout(cacheTimer);
    }
    console.log('sw-event2 cacheTimer', event.data.type);
    cacheTimer = setTimeout(() => {
      clearTimeout(cacheTimer);
      console.log('sw 开始清除缓存了');
      caches.keys().then(removeOldCache);
    }, 10 * 1000)
  }
  if (event.data && event.data.type === "pageDestroy") {
    clearTimeout(cacheTimer);
  }

  console.log('sw-event', event.data.type);
});


//静态资源采用staleWhileRevalidate策略，安全可靠 半年
workbox.routing.registerRoute(
  new RegExp('\/(js|css|fonts)\/'),
  new workbox.strategies.CacheFirst({
    cacheName: 'the:static',
    plugins: [
      new workbox.expiration.Plugin({
        maxEntries: 2000,
        maxAgeSeconds: 30 * 6 * 24 * 60 * 60  // 30 * 6 day
      })
    ]
  })
);
// cocos题版资源 半年
workbox.routing.registerRoute(
  new RegExp('\/(cocos)\/'),
  new workbox.strategies.CacheFirst({
    cacheName: 'the:runtime:cocos',
    plugins: [
      new workbox.expiration.Plugin({
        maxEntries: 2000,
        maxAgeSeconds: 30 * 6 * 24 * 60 * 60  // 30 * 6 day
      })
    ]
  })
);

// 题版资源 半年
workbox.routing.registerRoute(
  new RegExp('https://jiaoyanbos\.cdnjtzy\.com/cocos'),
  new workbox.strategies.CacheFirst({
    cacheName: 'the:runtime:cocos',
    plugins: [
      new workbox.cacheableResponse.Plugin({
        statuses: [200]
      }),
      new workbox.expiration.Plugin({
        maxEntries: 600,
        purgeOnQuotaError: false,
        maxAgeSeconds: 30 * 6 * 24 * 60 * 60  // 60day
      })
    ]
  })
);

// 预览资源 半年
workbox.routing.registerRoute(
  new RegExp('\/(web-mobile)'),
  new workbox.strategies.CacheFirst({
    cacheName: 'the:runtime:web-mobile',
    plugins: [
      new workbox.expiration.Plugin({
        maxEntries: 2000,
        maxAgeSeconds: 30 * 6 * 24 * 60 * 60  // 30 * 6 day
      })
    ]
  })
);

// 题目的数据资源 2个月
const questionResourcesCatchList = [
  'https://yaya\.cdnjtzy\.com/',
  'https://jiaoyanbos\.cdnjtzy\.com/(cw_|th_|shark)',
  'https://zyb-uxc\.zuoyebang\.cc/'

];
questionResourcesCatchList.forEach((item) => {
  workbox.routing.registerRoute(
    new RegExp(item),
    new workbox.strategies.CacheFirst({
      cacheName: 'the:runtime:resources',
      plugins: [
        new workbox.cacheableResponse.Plugin({
          statuses: [200]
        }),
        new workbox.expiration.Plugin({
          maxEntries: 1000,
          purgeOnQuotaError: false,
          maxAgeSeconds: 60 * 24 * 60 * 60  // 60day
        })
      ]
    })
  );
});

const feCDNCatchList = [
  'https://jy-fe\.cdnjtzy\.com/',
  'https://jy-fe-[a-zA-Z]{1,}-cdn\.suanshubang\.cc/',
  'https://jy-fe-api-cdn\.suanshubang\.cc/interactive-question-editor/'
]
feCDNCatchList.forEach((item) => {
  workbox.routing.registerRoute(
    new RegExp(item),
    new workbox.strategies.CacheFirst({
      cacheName: 'the:runtime:cdn',
      plugins: [
        new workbox.cacheableResponse.Plugin({
          statuses: [200]
        }),
        new workbox.expiration.Plugin({
          maxEntries: 2000,
          purgeOnQuotaError: false,
          maxAgeSeconds: 30 * 6 * 24 * 60 * 60  // 60day
        })
      ]
    })
  );
})

// iframe资源
const iframeCatchList = [
  'static/pyramid',
  'https://yy-s\.zuoyebang\.cc/',
  'https://latex\.cdnjtzy\.com'
]
iframeCatchList.forEach((item) => {
  workbox.routing.registerRoute(
    new RegExp(item),
    new workbox.strategies.CacheFirst({
      cacheName: 'the:runtime:iframe',
      plugins: [
        new workbox.cacheableResponse.Plugin({
          statuses: [200]
        }),
        new workbox.expiration.Plugin({
          maxEntries: 2000,
          purgeOnQuotaError: false,
          maxAgeSeconds: 365 * 24 * 60 * 60  // 60day
        })
      ]
    })
  );
});
