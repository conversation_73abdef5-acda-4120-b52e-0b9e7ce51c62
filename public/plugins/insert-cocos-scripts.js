/**
 * 用于在webpack编译时自动往html模板里插入js和css代码
 */
 const fs = require('fs')
 const path = require('path')
 const packageConfig = require('../../package.json');
 class InsertCocosScripts {
   constructor (options) {
     this.options = options || {}
   }
 
   apply (compiler) {
      const jsNode = '</body>'
      const headerNode = '</head>'
      const jsList = ['src/settings.js', 'main.js', 'cocos2d-js-min.js'];
      const bundleList = [
        'assets/qte/index.js',
        'assets/qte/config.json',
        'assets/internal/index.js',
        'assets/internal/config.json',
        'assets/main/index.js',
        'assets/resources/index.js',
        'assets/resources/config.json',
        'assets/sharkBlankQuestion/index.js',
        'assets/sharkBlankQuestion/config.json',
        'assets/sharkDragQuestion/index.js',
        'assets/sharkDragQuestion/config.json',
        'assets/sharkLineQuestion/index.js',
        'assets/sharkLineQuestion/config.json',
        'assets/sharkSelectQuestion/index.js',
        'assets/sharkSelectQuestion/config.json'
      ]
 
     // 定义要插入的script字符串
     let scriptCode = ''
     jsList.forEach(v => {
       if (v) {
         scriptCode += `<link href="web-mobile-${packageConfig.version}/${v}" rel="preload" as="script"/>`
       }
     })
     bundleList.forEach(v => {
       if (v) {
         scriptCode += `<link href="web-mobile-${packageConfig.version}/${v}" rel="prefetch" as="script"/>`
       }
     })
     scriptCode+=`<script>window.cocosAssetsDirName="web-mobile-${packageConfig.version}"</script>`

     // 定义要插入head的字符串
     let headCode = `<meta name="viewport" content="width=device-width,user-scalable=no,initial-scale=1, minimum-scale=1,maximum-scale=1"/>
     <meta name="apple-mobile-web-app-capable" content="yes">
     <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
     <meta name="format-detection" content="telephone=no">
     <meta name="renderer" content="webkit"/>
     <meta name="force-rendering" content="webkit"/>
     <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
     <meta name="msapplication-tap-highlight" content="no">
     <meta name="full-screen" content="yes"/>
     <meta name="x5-fullscreen" content="true"/>
     <meta name="360-fullscreen" content="true"/>
     <meta name="screen-orientation" content="landscape"/>
     <meta name="x5-orientation" content="landscape">
     <meta name="x5-page-mode" content="app">
     <link rel="stylesheet" type="text/css" href="web-mobile-${packageConfig.version}/style-mobile.css"/>`
     
     // 编译时注入
     compiler.hooks.compilation.tap(
       'compilation',
       compilation => {
         compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
           'htmlWebpackPluginAfterHtmlProcessing',
           htmlPluginData => {
             let htmlStr = htmlPluginData.html.toString()
 
             // 合并js
             if (scriptCode) {
               const replaceStr = scriptCode + jsNode
               htmlStr = htmlStr.replace(new RegExp(jsNode), replaceStr)
             }
             // 合并headcode
             const headerReplaceStr = headCode + headerNode;
             htmlStr = htmlStr.replace(new RegExp(headerNode), headerReplaceStr)
 
             htmlPluginData.html = htmlStr
           })
       })
   }
 }
 
 module.exports = InsertCocosScripts