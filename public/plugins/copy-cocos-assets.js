
const fs = require('fs')
const path = require('path');
const packageConfig = require('../../package.json');
const childProcess = require('child_process')
class CopyCocosAssets {
    constructor(options = {}) {
        const { dirname, target } = options
        this.from = path.resolve(
          dirname,
          "node_modules/qte-render/public/web-mobile"
        )
        this.to = path.resolve(
          target,
          `web-mobile-${packageConfig.version}`
        )
        this.dirname = dirname
        this.target = target
        this.fileName = `web-mobile-${packageConfig.version}`;
    }
    apply(compiler) {
        compiler.hooks.compilation.tap('compilation', (compliaction) => {
            const { from, to, target, fileName } = this
            if(!fs.existsSync(from)) {
                throw new Error('from is not found')
            }
            fs.readdir(`${target}`, (err,files)=>{
              if(err) throw err;
              files.forEach((file) => {
                if(file !== fileName && file.includes('web-mobile')) {
                  childProcess.execSync(`rm -rf ${path.resolve(
                    target,
                    file
                  )}`)
                }
              })
            })
            if(fs.existsSync(to)) {
                console.log('asset is copied')
                return;
            }
            childProcess.execSync(`cp -r ${from} ${to}`)
        })
    }
}
module.exports = CopyCocosAssets