/*
 * @FilePath     : /src/pages/index/questionMock.ts
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
 */
import counter from "../../../mock/question/counter.json";
import drag from "../../../mock/question/drag.json";
import select from "../../../mock/question/select.json";
import qiqiaoban from "../../../mock/question/qiqiaoban.json";
import h5Follow from "../../../mock/question/h5Follow.json";
import h5Line from "../../../mock/question/h5Line.json";
import write from "../../../mock/question/write.json";
import line from "../../../mock/question/line.json";
import Math<PERSON> from "../../../mock/question/Mathe.json";
import follow from "../../../mock/question/follow.json";
import h5Blank from "../../../mock/question/h5Blank.json";
import readcom from "../../../mock/question/readCom.json";
import lookSpeak from "../../../mock/question/lookSpeak.json";
import subJect from "../../../mock/question/sub.json";
import h5Puzzle from "../../../mock/question/h5Puzzle.json";
import puzzle from "../../../mock/question/puzzle.json";
import puzzle1 from "../../../mock/question/puzzle1.json";
import h5BlankDraw from "../../../mock/question/h5BlankDraw.json";
import h5BlankDrag from "../../../mock/question/h5BlankDrag.json";
import h5BlankEnWords from "../../../mock/question/h5BlankEnWords.json";
import h5BlankEnSentence from "../../../mock/question/h5BlankEnSentence.json";
import ldPage from "../../../mock/question/ldPage.json";
import followWords from "../../../mock/question/followWords.json";
import read from "../../../mock/question/read.json";
import klotski from "../../../mock/question/klotskiMock.json";
import enpk from "../../../mock/question/enpk.json";
export {
  counter,
  drag,
  select,
  qiqiaoban,
  h5Follow,
  h5Line,
  write,
  line,
  Mathe,
  follow,
  h5Blank,
  readcom,
  lookSpeak,
  subJect,
  h5Puzzle,
  h5BlankDraw,
  h5BlankDrag,
  h5BlankEnWords,
  h5BlankEnSentence,
  puzzle,
  puzzle1,
  ldPage,
  followWords,
  read,
  klotski,
  enpk
}
