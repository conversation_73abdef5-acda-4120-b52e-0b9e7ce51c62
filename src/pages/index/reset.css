.el-color-dropdown {
  width: 300px !important;
}

.el-color-svpanel {
  height: 180px !important;
}
.el-color-hue-slider {
  margin-top: 0px !important;
}   
.tox-tinymce-aux {
  z-index: 9999 !important;
}
.el-slider__button-wrapper {
  z-index: 1 !important;
}
.el-message .el-icon-info:before {
    content: "\e7a1" !important;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: rgba(131, 134, 143, 0.4);
  background: rgba(82,92,102,.3);
}

::-webkit-scrollbar-track {
  border-radius: 6px;
  background: transparent;
}

 @font-face {
  font-family: 'Arial';
  src: url('../../assets/font/customArial.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
} 