/*
 * @Date: 2021-10-09 10:20:02
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-07 16:01:54
 * @FilePath: /interactive-question-editor/src/pages/index/index.ts
 * @Author: chxu
 */
import Vue from "vue";
import svgicon from "vue-svgicon";
import "./ele";
import "@/common/utils/initComp.ts";
import App from "./App.vue";
import store from "./store";
import "./normalize.css";
import "./reset.css";
import "@/assets/cocosicon/iconfont.js";
import storage from "@/common/utils/storage";
import bus from "./common/utils/bus";
import VueContentPlaceholders from 'vue-content-placeholders'
import { debounce, DebouncedFunc } from "lodash-es";

Vue.config.productionTip = false;

Vue.use(svgicon);
Vue.use(VueContentPlaceholders);
Vue.prototype.$bus = bus;
Vue.prototype.$storage = storage;

new Vue({
  store,
  render: h => h(App),
}).$mount("#app");

const loadingEl = document.getElementById("loading");
if (loadingEl) {
  document.body.removeChild(loadingEl);
}

// 解决报错 ResizeObserver loop completed with undelivered notifications
const _ResizeObserver = (window as any).ResizeObserver;
(window as any).ResizeObserver = class ResizeObserver extends _ResizeObserver {
  constructor(callback: DebouncedFunc<any>) {
    callback = debounce(callback, 100);
    super(callback);
  }
};
