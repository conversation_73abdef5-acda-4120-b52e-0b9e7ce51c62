/*
 * @Date: 2025-06-27 15:37:46
 * @LastEditors: chxu
 * @LastEditTime: 2025-07-17 19:17:35
 * @FilePath: /interactive-question-editor/src/pages/index/tinymce.ts
 * @Author: chxu
 */
// tinymce的webpack使用
// https://github.com/tinymce/tinymce-webpack-example/blob/main/src/editor.js

import VueTinymce from '@packy-tang/vue-tinymce';
import tinymce from 'tinymce/tinymce';
import Vue from "vue";

import 'tinymce/skins/ui/oxide/skin.min.css';

//主题
import 'tinymce/themes/silver';

import 'tinymce/plugins/paste'; //全屏插件
import 'tinymce/plugins/table'; //表格插件
import 'tinymce/plugins/wordcount'; //全屏插件
import '../../../public/tinymce/plugins/mathjax'; //数学公式
import '../../../public/tinymce/plugins/underlinedot/plugin.js'; // 着重号
import '../../../public/tinymce/plugins/wavyunderline/plugin.js'; // 波浪线
import '../../../public/tinymce/plugins/firstlineindent/plugin.js'; // 首行缩进
import '../../../public/tinymce/plugins/superscript/plugin.js'; // 上标
import '../../../public/tinymce/plugins/subscript/plugin.js'; // 下标

// 新组件需要安装的插件 开始
import 'tinymce/plugins/advlist'; // 高级项目列表
import 'tinymce/plugins/lists'; // 项目列表
// 行间距
// 新组件需要安装的插件 结束
// 本地化
import '../../../public/tinymce/langs/zh_CN.js';
/**
 * 注：
 * 5.3.x版本需要额外引进图标，没有所有按钮就会显示not found
 */
import 'tinymce/icons/default/icons';

Vue.prototype.$tinymce = tinymce;
Vue.use(VueTinymce);
