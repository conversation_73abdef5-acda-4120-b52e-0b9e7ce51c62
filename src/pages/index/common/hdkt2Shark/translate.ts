import { getQuestionTemplateById } from "@/common/api/templateList";
import { checkPicturesSize } from "@/common/utils/resizePic";
import { parseLabel, setDefaultColor, Widget } from "@/common/utils/widgetToComponent";
import { useQuestionTemplateList } from "./useQuestionTemplateList";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
enum QUESTION_CATEGORY {
  sharkSelectQuestion = 1010,
  sharkDragQuestion = 1011,
  sharkBlankQuestion = 1012,
  sharkLineQuestion = 1013,
  followRecordQuestion = 1020,
  countQuestion = 1021,
  matchPatternQuestion = 1019,
  readQuestion = 1022,
}
enum ARTTEMPID {
  BLANK = 5,
  BLANK_DRAW = 6,
  BLANK_DRAG = 7,
  BLANK_WORDS = 39, // 英文单词和英文句子
  CHOOSE_1 = 4,
  CHOOSE_2 = 9,
  LINE_1 = 11,
  LINE_2 = 41,
  COUNTER = 20,
  DRAG_1 = 1,
  DRAG_2 = 3,
  DRAG_3 = 21,
  DRAG_4 = 36,
  DRAG_5 = 2,
  FOLLOWRECORD = 30,
  DRAG_6 = 45,
}

interface RectToImgParams {
  width: number,
  height: number,
  fillColor: string,
  strokeColor: string,
  opacity: number,
  strokeWidth: number,
}

let newComponentId = 0;
const getID = () => {
  return ++newComponentId + "";
};

let content: any = {};
let pageJson: any = {};
let dragAreaUrl = 'https://yaya.cdnjtzy.com/dragAreaImg-35e1f5.jpeg';
let dragAreaInfo = '';

const roleAndCatogery: any = {};
const getTagByName = (name: string) => {
  //获取tag
  for (const tag of content["template"]["tags"]) {
    if (tag.name == name) {
      return tag;
    }
  }
  return null;
};

const getExtraConfigByKey = (key: string, type: string) => {
  //获取tag
  for (const tag of content["template"]["extraConfig"]) {
    if (tag.key == key && tag.type == type) {
      return tag;
    }
  }
  return null;
};

const hexToRgb = (hex: string, opacity = 1) => {
  if (opacity === 1) return hex;
  const r = parseInt(`0x${hex.slice(1, 3)}`);
  const g = parseInt(`0x${hex.slice(3, 5)}`);
  const b = parseInt(`0x${hex.slice(5, 7)}`);
  return `rgba(${r},${g},${b},${opacity})`
}

const rectToImg = async (params: RectToImgParams) => {
  const { width: w, height: h, strokeColor, fillColor, opacity, strokeWidth } = params;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = w;
  canvas.height = h;
  ctx.lineWidth = strokeWidth;
  ctx.fillStyle = hexToRgb(fillColor, opacity);
  ctx.rect(0, 0, w, h);
  ctx.strokeStyle = hexToRgb(strokeColor, opacity);
  ctx.fill();
  ctx.stroke();
  const format = 'image/png';
  const quality = 0.92;
  const fileName = 'dragAreaImg.png';
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成图片失败"));
          return;
        }
        const file = new File([blob], fileName, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        formData.append("minify", "0");
        const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
        resolve(url);
      },
      format,
      quality,
    );
  });
}

const addGlobTagOptions = (name: string, type: string, data: any) => {
  const tagInfo = getTagByName(name);
  if (tagInfo && tagInfo.editorConfig) {
    for (const item of tagInfo.editorConfig) {
      if (item["type"] && item["type"] == type) {
        let findIndex = -1;
        for (const opt of item["params"]["options"]) {
          if (opt.value == data.value) {
            findIndex = 1;
            break;
          }
        }
        if (findIndex === -1) {
          item["params"]["options"].push(data);
        }
      }
    }
  }
};
const addGlobExtraConfigOptions = (key: string, type: string, data: any) => {
  const keyInfo = getExtraConfigByKey(key, type);
  if (keyInfo) {
    let findIndex = -1;
    for (const opt of keyInfo["params"]["options"]) {
      if (opt.value == data.value) {
        findIndex = 1;
        break;
      }
    }
    if (findIndex === -1) {
      keyInfo["params"]["options"].push(data);
    }
  }
};

const checkData = (content: any) => {
  const artTempId = pageJson.artTempId ? pageJson.artTempId : pageJson.activityId;
  switch (artTempId) {
    case ARTTEMPID.LINE_1:
    case ARTTEMPID.LINE_2:
      content.components.forEach((cmpt: any) => {
        if (roleAndCatogery[cmpt.id].role === 2) {
          Object.keys(roleAndCatogery).forEach(key => {
            if (roleAndCatogery[key].role === 1 && roleAndCatogery[key].category === roleAndCatogery[cmpt.id].category) {
              cmpt.extra.pairingObject.push(key);
            }
          });
        }
      });
      break;

    case ARTTEMPID.DRAG_1:
    case ARTTEMPID.DRAG_2:
    case ARTTEMPID.DRAG_3:
    case ARTTEMPID.DRAG_4:
    case ARTTEMPID.DRAG_5:
    case ARTTEMPID.DRAG_6:
      {
        const compList: any[] = [];
        for (let i = 0; i < content.components.length; i++) {
          if (content.components[i].tag == "dragableObject") {
            compList.push(content.components[i]);
            content.components.splice(i, 1);
            i--;
          }
        }
        if (artTempId == ARTTEMPID.DRAG_4) {
          //  36类型： 拖拽次数等于 答题区域数量
          let dragNum = 0;
          content.components.forEach((cmpt: any) => {
            if (cmpt.tag == "dragArea") {
              dragNum++;
            }
          });
          compList.forEach((cmpt: any) => {
            cmpt.extra.num = dragNum || 1;
          });
        } else if (artTempId == ARTTEMPID.DRAG_3) {
          console.log(ARTTEMPID.DRAG_3, ":单选类型");
        } else {
          //答题区分类落位坐标数量 取决于 多选和单选
          const typeList = {};
          let oneChoose = true;
          for (let i = 0; i < compList.length; i++) {
            if (compList[i].extra && compList[i].extra.type) {
              if (!typeList[compList[i].extra.type]) {
                typeList[compList[i].extra.type] = 1;
              } else {
                typeList[compList[i].extra.type]++;
              }
            }
            if (i == compList.length - 1) {
              for (const keyTpe in typeList) {
                if (typeList[keyTpe] > 1) {
                  oneChoose = false;
                  break;
                }
              }
            }
          }
          if (!oneChoose) {
            const len = compList.length || 0;
            content.components.forEach((cmpt: any) => {
              if (cmpt.tag == "dragArea") {
                for (let i = 0; i < len; i++) {
                  if (cmpt.extra && cmpt.extra.position && !cmpt.extra.position[i]) {
                    const posDtata = cmpt.extra.position[0];
                    if (posDtata) {
                      cmpt.extra.position.push({
                        endX: posDtata.endX,
                        endY: posDtata.endY,
                      });
                    }
                  }
                }
              }
            });
          }
        }
        // 添加全局拖拽 option
        content.components = content.components.concat(compList);
        const widgets = pageJson.artInfo ? pageJson.artInfo.detail : pageJson.detail;

        const setExtraConfigOptions = (widget: any) => {
          if (widget.role == 2) {
            // 答题区
            addGlobExtraConfigOptions("dragableObject", "custom", {
              label: widget.category + "",
              value: widget.category + "",
            });
          } else if (widget.role == 1) {
            // 拖拽元素
            addGlobExtraConfigOptions("dragableObject", "custom", {
              label: widget.category + "",
              value: widget.category + "",
            });
          }
        };
        for (const widget of widgets) {
          if (widget.name == "group") {
            const groupWidgets = widget.widgets;
            for (const groupWidget of groupWidgets) {
              setExtraConfigOptions(groupWidget);
            }
          }
          setExtraConfigOptions(widget);
        }
      }
      break;
    case ARTTEMPID.COUNTER:
      for (let i = 0; i < content.components.length; i++) {
        if (roleAndCatogery[content.components[i].id] && roleAndCatogery[content.components[i].id].role === 1) {
          content.components.splice(i, 1);
          i--;
        }
      }

      break;
    default:
      break;
  }
};

const getComponentsBySubType = (type: string, subType: string) => {
  for (const compo of content["components"]) {
    if (compo.type == type && compo.subType == subType) {
      return compo;
    }
  }
  return null;
};

// 处理特殊文本
const setSpecialTextStyle = (currComData: any, widget: any, str: string) => {
  if (str.indexOf("<") == -1) {
    let globColor = "#000000";
    let globSize = "32";
    if (widget.props.color) {
      globColor = widget.props.color;
    }
    if (widget.props.fontSize) {
      globSize = widget.props.fontSize;
    }
    let targetRichStr = "<color=" + globColor + "><size=" + globSize + ">" + str + "</size></color>";

    if (widget.props.bold) {
      targetRichStr = "<b>" + targetRichStr + "</b>";
    }
    if (widget.props.italic) {
      targetRichStr = "<i>" + targetRichStr + "</i>";
    }

    if (widget.props.underline) {
      targetRichStr = "<u>" + targetRichStr + "</u>";
    }
    currComData.properties.str = targetRichStr;
  }
};

const setNewDragPostion = (currComData: any, artTempId: any) => {
  const widgets = pageJson.artInfo ? pageJson.artInfo.detail : pageJson.detail;
  let culWidth = 0;
  let culHeight = 0;
  let num = 0;
  for (const widget of widgets) {
    if (widget.role == 1) {
      if (widget.props && widget.props.width && widget.props.height) {
        culWidth += widget.props.width;
        culHeight += widget.props.height;
        num++;
      }
    }
  }
  culWidth = parseInt(culWidth / num + "");
  culHeight = parseInt(culHeight / num + "");
  // if(  artTempId != ARTTEMPID.DRAG_2)
  const posData = currComData.extra.position[0];
  posData.endX = posData.endX + parseInt(currComData.properties.width / 2 + "") - parseInt(culWidth / 2 + "");
  posData.endY = posData.endY + parseInt(currComData.properties.height / 2 + "") - parseInt(culHeight / 2 + "");
};
/**根据组件不同数据设置 相关题型数据 */
const setComDataExtend = (currComData: any, widget: any) => {
  const artTempId = pageJson.artTempId ? pageJson.artTempId : pageJson.activityId;
  switch (artTempId) {
    case ARTTEMPID.BLANK:
    case ARTTEMPID.BLANK_DRAW:
    case ARTTEMPID.BLANK_DRAG:
      if (widget.role == 1 && widget.tag === 'blankModule') {
        currComData.extra = widget.extra;
        currComData.tag = widget.tag;
      } else if (widget.role == 1 && artTempId === ARTTEMPID.BLANK_DRAG) {
        currComData.extra = { tag: 'oneDragableObject' };
        currComData.tag = 'oneDragableObject';
      } else if (widget.role == 1 && artTempId === ARTTEMPID.BLANK_DRAW) {
        currComData.extra = { tag: '' };
        currComData.tag = '';
      }
      break;
    case ARTTEMPID.FOLLOWRECORD:
      if (widget.role === 1 && widget.category === 4) {
        const voiceCmpt = getComponentsBySubType("specialComponent", "voice");
        voiceCmpt.properties.answerDuration = widget.props.time;
        voiceCmpt.properties.wordType = widget.props.readType;
        voiceCmpt.properties.autoBegin = true;
        voiceCmpt.properties.evaluatingText = widget.content.replace(/<\/?.+?\/?>/g, "").trim();
      }
      break;
    case ARTTEMPID.CHOOSE_1:
    case ARTTEMPID.CHOOSE_2:
      if (widget.role == 1) {
        currComData.tag = "answer";
        if (widget.category == 1) {
          currComData.extra = {
            isCorrect: true,
            tag: "answer",
          };
        } else if (widget.category == 0) {
          currComData.extra = {
            tag: "answer",
          };
        }
      }
      break;
    case ARTTEMPID.LINE_1:
    case ARTTEMPID.LINE_2:
      if (widget.role === 2) {
        currComData.tag = "dragArea";
        currComData.extra = {
          pairingObject: [],
          linkPoint: {
            texture: "https://testimg.zuoyebang.cc/cw_db4a5e73e0a9c5ebc3264fbf92efbe07.png",
            linkPointType: artTempId === ARTTEMPID.LINE_1 ? "left" : "up",
          },
          tag: "dragArea",
        };
        currComData.childComponents = [
          {
            cName: "linePoint",
            tag: "",
            isFocus: false,
            type: "sprite",
            editable: false,
            deletable: false,
            tagName: "",
            properties: {
              active: true,
              width: 30,
              height: 30,
              x: artTempId === ARTTEMPID.LINE_1 ? -currComData.properties.width / 2 - 20 : 0,
              y: artTempId === ARTTEMPID.LINE_1 ? 0 : currComData.properties.height / 2 + 20,
              texture: "https://testimg.zuoyebang.cc/cw_db4a5e73e0a9c5ebc3264fbf92efbe07.png",
              angle: 0,
            },
            id: getID(),
            extra: {},
          },
        ];
      } else if (widget.role === 1) {
        currComData.tag = "ligature";
        currComData.extra = {
          pairingObject: [],
          linkPoint: {
            texture: "https://testimg.zuoyebang.cc/cw_db4a5e73e0a9c5ebc3264fbf92efbe07.png",
            linkPointType: artTempId === ARTTEMPID.LINE_1 ? "right" : "down",
          },
          tag: "ligature",
        };
        currComData.childComponents = [
          {
            cName: "linePoint",
            tag: "",
            isFocus: false,
            type: "sprite",
            editable: false,
            deletable: false,
            tagName: "",
            properties: {
              active: true,
              width: 30,
              height: 30,
              x: artTempId === ARTTEMPID.LINE_1 ? currComData.properties.width / 2 + 20 : 0,
              y: artTempId === ARTTEMPID.LINE_1 ? 0 : -currComData.properties.height / 2 - 20,
              texture: "https://testimg.zuoyebang.cc/cw_db4a5e73e0a9c5ebc3264fbf92efbe07.png",
              angle: 0,
            },
            id: getID(),
            extra: {},
          },
        ];
      }
      break;
    case ARTTEMPID.COUNTER:
      if (widget.type && widget.type == "special-text") {
        const com = getComponentsBySubType("specialComponent", "counter");
        if (widget.props.rightAnswer && com) {
          com.properties.countNum = widget.props.rightAnswer;
          setSpecialTextStyle(currComData, widget, widget.props.rightAnswer);
        }
      }
      if (widget.type && widget.type == "special-pic") {
        const com = getComponentsBySubType("specialComponent", "counter");
        if (com) {
          com.properties.width = currComData.properties.width;
          com.properties.height = currComData.properties.height;
          com.properties.x = currComData.properties.x;
          com.properties.y = currComData.properties.y;
          currComData.properties.active = false;
        }
      }
      break;
    case ARTTEMPID.DRAG_1:
    case ARTTEMPID.DRAG_2:
    case ARTTEMPID.DRAG_3:
    case ARTTEMPID.DRAG_4:
    case ARTTEMPID.DRAG_5:
    case ARTTEMPID.DRAG_6:
      {
        if (widget.role == 2) {
          // 答题区
          currComData.tag = "dragArea";
          currComData.extra = {
            position: [
              {
                endX: widget.props.left,
                endY: widget.props.top + 120,
              },
            ],
            type: String(widget.category),
          };
          setNewDragPostion(currComData, artTempId);
        } else if (widget.role == 1) {
          // 拖拽元素
          currComData.tag = "dragableObject";
          currComData.extra = {
            type: String(widget.category),
            num: 1,
            tag: "dragableObject",
          };
        }
      }
      break;
    default:
      break;
  }
};
const resetTxt = (richN: string, widget: any): string => {
  let richT = richN;
  if (widget.props.fontSize) {
    const fontS = /(?=<size=).*?>/g;
    richT = richT.replace(fontS, "<size=" + widget.props.fontSize + ">");
  }

  if (typeof widget.props.color == "string" && widget.props.color && widget.props.color != "#000000") {
    const clor = setDefaultColor(widget.props.color);
    const colo = /(?=<color=).*?>/g;
    richT = richT.replace(colo, "<color=" + clor + ">");
  }
  if (widget.props.bold && richT.indexOf("<b>") === -1) {
    richT = "<b>" + richT + "</b>";
  }
  if (widget.props.italic && richT.indexOf("<i>") === -1) {
    richT = "<i>" + richT + "</i>";
  }
  if (widget.props.underline && richT.indexOf("<u>") === -1) {
    richT = "<u>" + richT + "</u>";
  }
  return richT;
};
const setNodeAngle = (angle: number): number => {
  let angle1 = angle;
  if (angle1 < 0) {
    angle1 = Math.ceil(Math.abs(angle1) / 360) * 360 + angle1;
  } else if (angle1 > 360) {
    angle1 = angle1 - parseInt(angle1 / 360 + "") * 360;
  }
  return angle1;
};
const setNodeFilp = (data: any): number => {
  let filp = 0;
  if (data.rotateX && data.rotateY) {
    if (data.rotateX == "0deg" && data.rotateY == "180deg") {
      // 水平翻转
      filp = 1;
    } else if (data.rotateX == "180deg" && data.rotateY == "0deg") {
      //竖直翻转
      filp = 2;
    } else if (data.rotateX == "180deg" && data.rotateY == "180deg") {
      // 水平竖直翻转
      filp = 3;
    }
  }
  return filp;
};

const createBrushComponentsData = (mountedComponentId: string) => {
  return {
    type: 'specialComponent',
    subType: 'brush',
    tag: '',
    dragable: true,
    editable: {
      properties: {
        x: false,
        y: false,
        width: false,
        height: false,
        angle: false,
        rotation: false,
        opacity: false,
        scaleX: false,
        scaleY: false,
        color: false,
        active: false
      }
    },
    properties: {
      active: true,
      width: 95,
      height: 182,
      x: 557,
      y: 156,
      color: "#000000",
      style: 0,
      mountList: [mountedComponentId],
      angle: 0
    },
    id: getID(),
    extra: {
      tag: ''
    }
  }
}


// console.log(unescapeHtml(htmlStr))

const isIncludeswordBlanks = (widget: any) => {
  const { type, content } = widget;
  return type === 'text' && content.includes('word-input-blank');
}

const creatorComponentsData = async (widget: any) => {
  const props = widget.props;
  let newComponent = null;
  if (widget.role === 100) {
    return newComponent;
  }
  switch (widget.name) {
    case "text":
    case "textplaceholder":
    case "special-text":
      {
        // 英文填空题
        if (isIncludeswordBlanks(widget)) return null;
        if (widget.content.indexOf("ql-mathjax") !== -1) {
          return newComponent;
        }
        let width: number = props.clientWidth ? Number(props.clientWidth) : 600;
        if (props.width && typeof props.width === "number") {
          width = props.width;
        }
        const height: number = props.clientHeight ? Number(props.clientHeight) : 101;
        const fontSize =
          typeof props.fontSize === "number"
            ? props.fontSize
            : ((props.fontSize as unknown) as string).slice(
              0,
              //@ts-ignore
              props.fontSize.length - 2,
            );

        let richtxt = parseLabel(widget.content);
        richtxt = resetTxt(richtxt, widget);


        let clor = null;
        if (typeof widget.props.color == "string" && widget.props.color) {
          clor = setDefaultColor(widget.props.color);
        }
        const labelComponent: LabelComponent = {
          type: "label",
          tag: "",
          dragable: true,
          properties: {
            active: true,
            fontSize: Number(fontSize),
            lineHeight: Number(fontSize) + 5,
            width: width,
            height: height,
            x: props.left - 1280 / 2 + width / 2,
            y: 720 / 2 - props.top - height / 2,
            string: "请输入文本",
            str: richtxt,
            color: clor || "#000000",
            cusorIndex: 1,
            selectArr: [],
            isLabelRight: true,
            isFixed: false,
            opacity: Math.round(props.opacity * 255),
            angle: setNodeAngle(-Number(props.rotate.replace("deg", ""))),
          },
          id: getID(),
          extra: {
            tag: "",
          },
        };
        newComponent = labelComponent;
      }

      break;
    case "pic":
    case "picPlaceholder":
    case "special-pic":
      {
        if (widget.content.indexOf(".png") !== -1) {
          if (!/\.(png)$/.test(widget.content)) widget.content = widget.content.slice(0, widget.content.indexOf(".png")) + ".png";
        }
        if (widget.content.indexOf(".gif") !== -1) {
          // git图拦截+提示
          showErrorMessage(new Error("cocos不支持插入gif图~"));
          return;
        }
        const urls = await checkPicturesSize([widget.content]);
        if (urls.length === 0) return;
        const spriteComponent: SpriteComponent = {
          tag: "",
          type: "sprite",
          dragable: true,
          properties: {
            active: true,
            width: Number(props.width),
            height: Number(props.height),
            x: props.left - 1280 / 2 + Number(props.width) / 2,
            y: 720 / 2 - props.top - Number(props.height) / 2,
            texture: urls[0],
            opacity: Math.round(props.opacity * 255),
            angle: setNodeAngle(-Number(props.rotate.replace("deg", ""))),
            flipType: setNodeFilp(props) || 0,
          },
          id: getID(),
          extra: {
            tag: "",
          },
        };
        newComponent = spriteComponent;
        // 如果是拖拽绘图，需要再次添加绘图的组件 spriteComponent
      }
      break;
    case "group":
      {
        const width = Number(props.width);
        const height = Number(props.height);
        const groupComponent: GroupComponent = {
          id: getID(),
          tag: "",
          type: "group",
          editable: true,
          deletable: true,
          properties: {
            active: true,
            x: props.left - 1280 / 2 + width / 2,
            y: 720 / 2 - props.top - height / 2,
            width: width,
            height: height,
            opacity: Math.round(props.opacity * 255),
            angle: setNodeAngle(-Number(props.rotate.replace("deg", ""))),
          },
          extra: {
            tag: "",
          },
          subComponents: [],
        };

        const groupWidgets = widget.widgets;
        for (const groupWidget of groupWidgets) {
          const subComponent = await creatorComponentsData(groupWidget);
          if (subComponent) {
            // setComDataExtend(subComponent, groupWidget); // 子组件不可设置业务属性
            subComponent.properties.x -= groupComponent.properties.x;
            subComponent.properties.y -= groupComponent.properties.y;
            roleAndCatogery[subComponent.id].role = null;
            roleAndCatogery[subComponent.id].category = null;
          }
          subComponent && groupComponent.subComponents.push(subComponent);
        }
        if (groupComponent.subComponents.length < 1) {
          newComponent = null;
        } else {
          newComponent = groupComponent;
        }
      }
      break;

    case "ques-stem-pic": // 添加题干音频
      {
        if (props.isReadQueStem == 1 && props.audioUrl && props.playType == 0) {
          content.extraStageData.guide = props.audioUrl;
        }
      }
      break;
    case "shape":
      {
        const artTempId = pageJson.artTempId ? pageJson.artTempId : pageJson.activityId;

        if (artTempId === ARTTEMPID.DRAG_6 && widget.role === 2) {
          // width height opacity stroke fill  stroke
          const {
            width,
            height,
            opacity,
            stroke,
            fill,
            strokeWidth
          } = props;
          const rectImgInfo = `${width}-${height}-${stroke}-${fill}-${opacity}-${strokeWidth}`;
          if (!dragAreaUrl || rectImgInfo !== dragAreaInfo) {
            const url = await rectToImg({
              width: width,
              height: height,
              opacity: opacity,
              strokeColor: stroke,
              fillColor: fill,
              strokeWidth: strokeWidth
            })
            dragAreaUrl = url as string;
            dragAreaInfo = `${width}-${height}-${stroke}-${fill}-${opacity}-${strokeWidth}`;
          }

          const spriteComponent: SpriteComponent = {
            tag: "",
            type: "sprite",
            dragable: true,
            properties: {
              active: true,
              width: Number(props.width),
              height: Number(props.height),
              x: props.left - 1280 / 2 + Number(props.width) / 2,
              y: 720 / 2 - props.top - Number(props.height) / 2,
              texture: dragAreaUrl,
              opacity: 255,
              angle: setNodeAngle(-Number(props.rotate.replace("deg", ""))),
              flipType: setNodeFilp(props) || 0,
            },
            id: getID(),
            extra: {
              tag: "",
            },
          };
          newComponent = spriteComponent;
        }
      }
      break;
    default:
      break;
  }
  if (newComponent) {
    roleAndCatogery[newComponent.id] = {};
    roleAndCatogery[newComponent.id].role = widget.role;
    roleAndCatogery[newComponent.id].category = widget.category;
  }

  return newComponent;
};

const getTempIdByArtTempId = (artTempId: number, questionTemplateList: any[]) => {
  const tempCategoryMap = new Map([
    [ARTTEMPID.BLANK, QUESTION_CATEGORY.sharkBlankQuestion],
    [ARTTEMPID.BLANK_DRAW, QUESTION_CATEGORY.sharkBlankQuestion],
    [ARTTEMPID.BLANK_DRAG, QUESTION_CATEGORY.sharkBlankQuestion],
    [ARTTEMPID.BLANK_WORDS, QUESTION_CATEGORY.sharkBlankQuestion],
    [ARTTEMPID.CHOOSE_1, QUESTION_CATEGORY.sharkSelectQuestion],
    [ARTTEMPID.CHOOSE_2, QUESTION_CATEGORY.sharkSelectQuestion],
    [ARTTEMPID.COUNTER, QUESTION_CATEGORY.countQuestion],
    [ARTTEMPID.LINE_1, QUESTION_CATEGORY.sharkLineQuestion],
    [ARTTEMPID.LINE_2, QUESTION_CATEGORY.sharkLineQuestion],
    [ARTTEMPID.FOLLOWRECORD, QUESTION_CATEGORY.followRecordQuestion],
    [ARTTEMPID.DRAG_1, QUESTION_CATEGORY.sharkDragQuestion],
    [ARTTEMPID.DRAG_2, QUESTION_CATEGORY.sharkDragQuestion],
    [ARTTEMPID.DRAG_3, QUESTION_CATEGORY.sharkDragQuestion],
    [ARTTEMPID.DRAG_4, QUESTION_CATEGORY.sharkDragQuestion],
    [ARTTEMPID.DRAG_5, QUESTION_CATEGORY.sharkDragQuestion],
    [ARTTEMPID.DRAG_6, QUESTION_CATEGORY.sharkDragQuestion],
  ]);
  return questionTemplateList.filter((template: any) => {
    return template.category === tempCategoryMap.get(artTempId);
  })[0].id;
}

const translateMathToEnBoard = async (content: any) => {
  // 将cocos填空题默认的数字键盘转换为英文键盘
  const { components } = content;
  const findMathCompIndex = components.findIndex((item: any) => item.type === 'specialComponent' && item.subType === 'keyboard')
  const compId = components[findMathCompIndex].id;
  await import('./keyboardEnglishData.json').then((res: any) => {
    components.splice(findMathCompIndex, 1, {
      ...res.default,
      id: compId
    })
  })
}

const translateEnBlankComps = async (widget: any, hdktOthers = { rightAnswers: [] }) => {
  const comps: any[] = [];
  if (!isIncludeswordBlanks(widget)) return Promise.resolve(comps);
  const { content, fontSize, props } = widget;
  const color = '#be5e1d';
  const fontColor = typeof widget.props.color == "string" && setDefaultColor(props.color) || setDefaultColor("#000000");
  const height = 40;
  let startX = 0;
  const perStrSize = 16;
  const minInputW = 63;
  const { left, top } = props;
  const tempDom = document.createElement('div');
  tempDom.innerHTML = content;
  // 获取所有dom节点
  const childNodes: any[] = [];
  [...(tempDom.querySelector('p') as any).childNodes].forEach((childNode: any) => {
    if (childNode.nodeName === "#text") {
      childNodes.push(childNode)
    } else if (childNode.classList && [...childNode.classList].includes('wi-blank-ques')) {
      childNodes.push(childNode)
    } else {
      childNodes.push(...childNode.childNodes)
    }
  });
  const promiseArray: any[] = [];
  [...childNodes].forEach((node: any) => {
    if (node.getAttribute && node.getAttribute('blank-id')) {
      const blankId = node.getAttribute('blank-id');
      const nodeText = node.innerText.trim();
      const rightAnswer = (hdktOthers.rightAnswers[blankId] as string[]) || [nodeText];
      const h = height;
      const maxLength = Math.max(...rightAnswer.map((item) => item.length));
      const w = Math.max(perStrSize * (maxLength - 1) + minInputW, minInputW);
      const x = startX + left - 1280 / 2 + w / 2;
      startX += (w + 10);
      // 生成图片
      promiseArray.push(rectToImg({
        width: w,
        height: h,
        opacity: 1,
        strokeColor: color,
        fillColor: color,
        strokeWidth: 1
      }).then((url) => {
        const tempComp = {
          tag: 'blankModule',
          type: 'sprite',
          dragable: true,
          properties: {
            active: true,
            width: w,
            height,
            x,
            y: 720 / 2 - top - height / 2,
            texture: url,
            angle: 0
          },
          id: getID(),
          extra: {
            characterLimit: maxLength,
            labelColor: fontColor,
            fontSize,
            correctArray: rightAnswer,
            hasAllCorrect: true,
            tag: 'blankModule'
          }
        }
        return tempComp;
      }))
      // console.log('create 答题区域 component', node.getAttribute('blank-id'), node.innerText);
    } else if (node.nodeName === "#text" && node.data && node.data.trim().length) {
      const nodeValue = node.data.trim();
      const w = nodeValue.length * perStrSize;
      const x = startX + left - 1280 / 2 + w / 2;
      startX += (w + 10);
      promiseArray.push(Promise.resolve().then(() => {
        const tempComp = {
          type: 'label',
          tag: '',
          dragable: true,
          properties: {
            active: true,
            fontSize,
            width: w,
            height,
            x,
            y: 720 / 2 - top - height / 2,
            string: '单击添加文本',
            str: `<size=${fontSize}><color=${fontColor}>${nodeValue}</color></size>`,
            color: fontColor,
            cusorIndex: 25,
            selectArr: [

            ],
            isLabelRight: true,
            isFixed: false,
            rowSpacing: 1,
            angle: 0,
            enableBold: false,
            enableItalic: false,
            enableUnderline: false,
            lineHeight: fontSize,
            horizontalAlign: 0,
            textureArray: []
          },
          id: getID(),
          extra: {
            tag: ''
          }
        }
        return tempComp;
      }))
    }
  })
  return Promise.all(promiseArray).then(res => {
    res.forEach(comp => {
      comp && comps.push(comp);
    })
    return Promise.resolve(comps);
  });
}

export const translate = async (pageJ: any) => {
  pageJson = pageJ;
  console.warn("接收的h5数据------》", pageJ);
  console.log("接收的h5数据------str》", JSON.stringify(pageJ));
  const othersInfo = pageJson.artInfo ? pageJson.artInfo.others : pageJson.others;
  const widgets = pageJson.artInfo ? pageJson.artInfo.detail : pageJson.detail;
  const hdktOthers = pageJson.artInfo ? pageJson.artInfo.hdktOthers : pageJson.hdktOthers;
  const questionTemplateList = await useQuestionTemplateList(1000);
  console.warn("模板列表------》", questionTemplateList);
  const artTempId = pageJson.artTempId ? pageJson.artTempId : pageJson.activityId;
  const id = getTempIdByArtTempId(artTempId, questionTemplateList);

  switch (artTempId) {
    case ARTTEMPID.BLANK:
    case ARTTEMPID.BLANK_DRAW:
    case ARTTEMPID.BLANK_DRAG:
      {
        for (let i = 0; i < widgets.length - 1; i++) {
          if (widgets[i].name === "text") {
            let content = widgets[i].content;
            let answerNum = 0;
            while (content.indexOf("in-blank") !== -1) {
              const pos = content.indexOf("in-blank");
              const pos2 = content.indexOf(">", pos);
              const pos3 = content.indexOf("<", pos);
              const str = (content as string)
                .slice(pos2 + 1, pos3)
                .replace(/<\/?.+?\/?>/g, "")
                .trim();
              const answerWidget = {
                type: "pic",
                name: "pic",
                extra: {
                  characterLimit: str.length + 2,
                  labelColor: "#000000",
                  fontSize: 24,
                  correct: str,
                  hasAllCorrect: true,
                  tag: "blankModule",
                },
                tag: "blankModule",
                props: {
                  top: widgets[i].props.top,
                  left: widgets[i].props.left + widgets[i].props.clientWidth - 274 / 2 + answerNum * 200,
                  width: 274,
                  height: 72,
                  opacity: 1,
                  rotate: "0deg",
                  ratio: 2.0606060606061,
                  rotateX: "0deg",
                  rotateY: "0deg",
                  clipType: "rect",
                  imageWidth: 274,
                  imageHeight: 72,
                  clipWidth: 274,
                  clipHeight: 72,
                },
                content: "https://yaya.cdnjtzy.com/image-b1a392.png",
                category: 0,
                role: 1,
              };
              widgets.push(answerWidget);
              content = content.replace(str, "").replace("in-blank", "");
              answerNum++;
            }
            widgets[i].content = content;
          }
        }
      }
      break;
    case ARTTEMPID.FOLLOWRECORD:
      {
        widgets.forEach((widget: Widget) => {
          if (widget.name === "hx-read-english-text") {
            widget.name = "text";
          }
        });
      }
      break;
  }

  const templateData = await getQuestionTemplateById(id).then(res => res.data.data);
  (templateData as any).tempId = id;
  content = JSON.parse(templateData.content);
  // 改造templateData
  if (artTempId === ARTTEMPID.BLANK_WORDS) {
    translateMathToEnBoard(content);
  }
  let background = pageJson.artInfo ? pageJson.artInfo.background : pageJson.background;
  if (background.indexOf(".png") !== -1) {
    if (!/\.(png)$/.test(background)) background = background.slice(0, background.indexOf(".png")) + ".png";
  }
  if (background.indexOf(".gif") !== -1) {
    // git图拦截+提示
    showErrorMessage(new Error("cocos不支持插入gif图~"));
    background = '';
  }
  // 背景图为空时，不予处理
  let urls: string | any[] = [];
  if (background) {
    urls = await checkPicturesSize([background]);
  }
  if (content.components) {
    newComponentId = content["components"].length;
  }
  for (const widget of widgets) {
    const comData = await creatorComponentsData(widget);
    if (comData) {
      comData && content.components && content.components.push(comData) && setComDataExtend(comData, widget);

      // 填空绘图题-添加绘图组件并挂载
      if (artTempId === ARTTEMPID.BLANK_DRAW && widget.role === 1 && widget.tag !== 'blankModule') {
        const brushComponentData = createBrushComponentsData(comData.id);
        content.components && content.components.push(brushComponentData)
      }
      // 填空拖拽题-组件tag变为单个拖拽元素
      if (artTempId === ARTTEMPID.BLANK_DRAG && widget.role === 1) {
        content.extraDataMap[comData.id] = {
          tag: 'oneDragableObject'
        }
      }
    }
    // 英语填空题-转换填空和文本内容组件
    const enComps = await translateEnBlankComps(widget, hdktOthers);
    enComps.forEach(comp => {
      content.components && content.components.push(comp);
      content.extraDataMap[comp.id] = comp.extra
    });
  }
  checkData(content);
  if (urls.length > 0) {
    // content.stageData.texture = urls[0];
    const spriteComponent: SpriteComponent = {
      tag: "",
      type: "sprite",
      dragable: true,
      properties: {
        active: true,
        width: 1280,
        height: 720,
        x: 0,
        y: 0,
        texture: urls[0],
        opacity: 255,
        angle: 0,
      },
      id: getID(),
      extra: {
        tag: "",
      },
    };
    content.components.unshift(spriteComponent);
  }
  if (artTempId === ARTTEMPID.DRAG_6) {
    const imgList = [];
    let left = 0;
    let top = 0;
    let width = 0;
    let height = 0;
    let midSplit = 0;
    let borderSplit = 4;
    for (const widget of widgets) {
      const { src = "", role, content, props } = widget;
      if (role === 1) {
        imgList.push(src || content);
        if (left <= props.containerLeft) {
          midSplit = props.left - width
          borderSplit = midSplit + 4
          width = props.width + props.left
        }
        if (top !== props.containerTop) {
          height = props.height + props.top
        }
        if (!left) {
          left = props.containerLeft || 0;
        } else {
          left = Math.min(left, props.containerLeft || 0)
        }
        if (!top) {
          top = props.containerTop || 0;
        } else {
          top = Math.min(top, props.containerTop || 0)
        }
      }
    }
    // todo0113 removegif
    if (othersInfo.basePic.url.includes('gif')) {
      othersInfo.basePic.url = '';
    };
    othersInfo.basePic.imgList = imgList;
    othersInfo.basePic.containerLeft = left;
    othersInfo.basePic.containerTop = top;
    othersInfo.basePic.containerWidth = width - left;
    othersInfo.basePic.containerHeight = height - top;
    othersInfo.basePic.itemMidSplit = midSplit;
    othersInfo.basePic.itemBorderSplit = borderSplit;
    let hasPuzzle = false;
    content.template.extraConfig.forEach((config: any) => {
      if (config.key === "pintuObject") {
        hasPuzzle = true;
        config.params = {
          showPuzzle: true,
          puzzleInfo: othersInfo.basePic,
        };
      }
    });
    if (!hasPuzzle) {
      content.template.extraConfig.push({
        key: "pintuObject",
        label: "拼图组件",
        type: "DragPintuEditor",
        required: false,
        params: {
          showPuzzle: true,
          puzzleInfo: othersInfo.basePic,
        },
      });
    }
  }

  templateData.content = JSON.stringify(content);
  console.log("templateData.content:", templateData.content);
  return templateData;
};
