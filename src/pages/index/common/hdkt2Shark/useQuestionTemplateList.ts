/*
 * @Date: 2021-12-16 11:25:15
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-23 16:50:15
 * @FilePath: /interactive-question-editor/src/pages/index/common/hdkt2Shark/useQuestionTemplateList.ts
 * @Author: chxu
 */
import { getQuestionTemplateList } from "@/common/api/templateList";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import store from "@/pages/index/store";

export const useQuestionTemplateList = async (count = 100) => {
  // const { data, error, loading } = useRequest(
  //   () => {
  //     return getQuestionTemplateList({
  //       pn: 1,
  //       rn: count,
  //       pageNum: 1,
  //       pageSize: count,
  //     }).then(res => {
  //       return res.data.data.list;
  //     });
  //   },
  //   {
  //     onError: showErrorMessage,
  //     initialData: [],
  //     loadingDelay: 300,
  //   },
  // );
  const data = await getQuestionTemplateList({
    pn: 1,
    rn: count,
    pageNum: 1,
    pageSize: count,
    parentVersion: store.state.parentVersion,
  }).then(res => {
    return res.data.data.list;
  });
  return data;
  // return { data: data || [], error, loading };
};
