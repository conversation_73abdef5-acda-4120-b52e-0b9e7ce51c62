import { getQuestionTemplateById } from "@/common/api/templateList";
import { postFile } from "@/common/utils/renderTextureToPicture";
import { useQuestionTemplateList } from "./useQuestionTemplateList";

enum CATEGORY {
  READCOM = 1100,
}
const cutH = 720;

let content: any = {};
let pageJson: any = {};

const pixelsPicUpload = async (resolve: any, currData: any, blob: any) => {
  const file = new File([blob], currData.fileName, { type: blob.type });
  const formData = new FormData();
  formData.append("file", file);
  if (currData.minify) {
    formData.append("minify", currData.minify)
  }

  const callBack = (url: string) => {
    if (url) {
      resolve(url);
      return;
    }
  }

  postFile(formData, callBack);
}


const pixelsToPic = async (currData: any, h1: number, h2: number): Promise<string> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    const width = currData.width;
    canvas.width = width;
    canvas.height = h2 - h1;
    const rowBytes = width * 4;
    for (let row = h1; row < h2; row++) {
      const imageData = ctx.createImageData(width, 1);
      const srow = row;
      const start = srow * width * 4;
      for (let i = 0; i < rowBytes; i++) {
        imageData.data[i] = currData.pixels[start + i];
      }
      ctx.putImageData(imageData, 0, row - h1);
    }
    canvas.toBlob(
      async blob => {
        if (!blob) {
          console.error("生成缩略图失败");
          return;
        }
        pixelsPicUpload(resolve, currData, blob);
      },

      currData.format,
      currData.quality,
    );
  });
}



export const picReSize = async (data: Blob): Promise<string[]> => {
  // 阅读理解截图
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(data);
    reader.onload = (e) => {
      const img = new Image();
      img.crossOrigin = "Anonymous";
      if (e.target && e.target.result) {
        img.src = e.target.result.toString();
        // console.error(e.target.result)
      }
      img.onload = async () => {
        const height = img.height;
        const picPackArrayHeight = [0];
        let currIndex = 0;
        while (currIndex < height) {
          if (currIndex + cutH < height) {
            currIndex += cutH;
            picPackArrayHeight.push(currIndex);
          } else {
            currIndex = height;
            picPackArrayHeight.push(height);
            break;
          }
        }
        // console.error(picPackArrayHeight)
        const canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;
        const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
        ctx.drawImage(img, 0, 0);
        const pixles = ctx.getImageData(0, 0, canvas.width, canvas.height).data;
        const resultUrl = [];
        for (let indexI = 0; indexI < picPackArrayHeight.length; indexI++) {
          const h1 = picPackArrayHeight[indexI];
          const h2 = picPackArrayHeight[indexI + 1];
          const imageData = {
            pixels: pixles,
            width: img.width,
            height: img.height,
            fileName: "cocosPic.png",
            quality: 0.92,
            format: 'image/png',
            minify:"0"
          }
          if (h2) {
            const url = await pixelsToPic(imageData, h1, h2);
            resultUrl.push(url);
          }
        }
        resolve(resultUrl)
      }
    }
  });
};
const getComponentsBySubType = (type: string, subType: string) => {
  for (const compo of content["components"]) {
    if (compo.type == type && compo.subType == subType) {
      return compo;
    }
  }
  return null;
};
const readComTranslate = async (callBack: Function) => { // 管理逐个数据汇总
  const snapshotInfo = pageJson.snapshotInfo;
  let questionList: string[] = [];
  for (let i = 0; i < snapshotInfo.questionContent.length; i++) {
    const blob = snapshotInfo.questionContent[i];
    questionList = questionList.concat(await picReSize(blob))
  }
  const readCom = getComponentsBySubType("specialComponent", "readcom");
  const question = readCom.properties.question;
  question.questionList = questionList;
  question.optionsList = [];
  readCom.properties.customH5 = pageJson.originInfo;
  for (let i = 0; i < snapshotInfo.subQuestions.length; i++) {
    const obj: any = {
      optionsQuestion: [],
      options: []
    }
    const subQ = snapshotInfo.subQuestions[i];
    for (let j = 0; j < subQ.title.length; j++) { // 小题标题
      const blob = subQ.title[j];
      obj.optionsQuestion = obj.optionsQuestion.concat(await picReSize(blob))
    }

    for (let k = 0; k < subQ.options.length; k++) { // 小题选项
      const optionItem = subQ.options[k];
      const OptionsItem: any = { picList: [], isCorrect: false };
      if (optionItem.isAnswer == 1) {
        OptionsItem.isCorrect = true;
      }
      for (let l = 0; l < optionItem.content.length; l++) { // 小题选项图片
        const blob = optionItem.content[l];
        OptionsItem.picList = OptionsItem.picList.concat(await picReSize(blob))
      }
      obj.options.push(OptionsItem);
    }
    question.optionsList.push(obj);
  }
  callBack();
}
const readComTrManger = (): Promise<string> => { // 管理总进度
  return new Promise((resolve, reject) => {
    const call = () => {
      resolve("")
    }
    readComTranslate(call);
  });
}

export const h5Switch = async (pageJ: any) => {
  pageJson = pageJ;
  console.warn("接收的h5数据------》", pageJ);
  const questionTemplateList = await useQuestionTemplateList(1000);
  let id = 0;
  console.warn("模板列表------》", questionTemplateList);
  console.log("category", pageJ.category);
  id = questionTemplateList.filter((template: any) => {
    return template.category === pageJ.category;
  })[0].id;

  const templateData = await getQuestionTemplateById(id).then(res => res.data.data);
  (templateData as any).tempId = id;
  content = JSON.parse(templateData.content);
  if (templateData.category == CATEGORY.READCOM) {
    await readComTrManger();
  }
  templateData.content = JSON.stringify(content);
  console.log(content, "content");
  return templateData;
};
