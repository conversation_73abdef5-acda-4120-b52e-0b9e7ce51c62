import { cloneDeep } from "lodash-es";

export const exportVue = (files: any) => {
  const modules: any = {};
  files.keys().forEach((key: any) => {
    const m = files(key).default;
    modules[m.componentName] = m;
  });
  return modules;
};

export const strToUnicode = (str: string) => {
  let val = "";
  for (let i = 0; i < str.length; i++) {
    val += str.charCodeAt(i).toString(16);
  }
  return val;
};

export const formSchemaToFlat = (formSchema: FormSchema) => {
  let formItemList: FormItemConfigs[] = [];
  formSchema.forEach(item => {
    if (item.formItemType === "collapse") {
      formItemList = [...formItemList, ...(item as CollapseForm).formList];
    } else {
      formItemList.push(item as FormItemConfigs);
    }
  });
  return formItemList;
};

export const configsToRules = (
  formItemList: FormItemConfigs[],
  {beforeRules, val}: {beforeRules?: Record<string, FormRule>, val?: any} = {}
) => {
  const rules: Record<string, FormRule> = beforeRules || {};
  if (formItemList && formItemList.length) {
    const temp = cloneDeep(formItemList);
    temp?.forEach(item => {
      if (item.rule?.length) {
        if (item.validation) {
          const validatePass = (rule: { message: string }, value: string, callback: any) => {
            try {
              const reg = new RegExp(item.validation as string);
              if (!reg.test(value)) {
                callback(new Error(rule.message || "格式校验不通过"));
              } else {
                callback();
              }
            } catch (error) {
              console.error(error);
              callback();
            }
          };
          item.rule[0].validator = validatePass;
        }
        rules[item.key] = item.rule;
      }
      // 存在options
      if (item.options?.length && val) {
        const option = item.options.find(option => option.value === val[item.key]);
        if (option?.associatedForm?.length) {
          configsToRules(option.associatedForm, { beforeRules: rules })
        }
      }
    });
  }
  
  return rules;
};

// 获取tabs需要清除的表单key和当前项option
export const getCleanFormKeyAndOption: (
  options: FormOption[],
  value: any,
) => {
  cleanFormKey: string[];
  option: FormOption | null;
} = (options, value) => {
  const cleanFormKey: string[] = [];
  let option: FormOption | null = null;
  options.forEach(item => {
    item.associatedForm?.forEach(formItem => {
      cleanFormKey.push(formItem.key);
    });
    if (item.value === value) {
      option = item;
    }
  });
  return {
    cleanFormKey,
    option,
  };
};

export const translateChangeProps = (changeProps: any, val: any) => {
  if (!val) return [];
  // {{$$}}
  try {
    const _stringifyChangeProps = JSON.stringify(changeProps, function (k, v) {
      // 忽略文本组件的内容
      if (typeof v === "string") {
        const result = v.match(/\{{(.*?)\}}/);
        let expression = '';
        if (result) {
          console.log('replace', result[1].replace('$$', `${val}`));
          expression = result[1].replace('$$', `${val}`);
          try {
            const temp = v.replace(/\{{(.*?)\}}/, eval(expression))
            return isNaN(Number(temp)) ? temp : Number(temp)
          } catch (e) {
            return v.replace(/\{{(.*?)\}}/, expression)
          }
        }
        return v; // 如果没有匹配，返回null
      }
      return v;
    });
    return JSON.parse(_stringifyChangeProps);
  } catch (e) {
    return changeProps
  }

}

// 联动更新表单列表属性
export const updateConfigsProps: (
  { key, value }: { key: string; value: any },
  formConfigs: FormItemConfigs[],
) => null | {
  changeValData: Record<string, any>;
  newFormConfigs: FormItemConfigs[];
} = ({ key, value }, formConfigs) => {
  const formItemConfigs = formConfigs.find(item => item.key === key);

  if (!formItemConfigs) return null;
  let changeProps = formItemConfigs.changeProps || [];
  if (formItemConfigs?.options?.length) {
    const option = formItemConfigs.options.find(item => item.value === value);
    if (option?.changeProps?.length) {
      changeProps = [...changeProps, ...option.changeProps]
    }
  };
  if (!changeProps.length) return null;

  const tempChangeProps = translateChangeProps(changeProps, value);

  console.log("tempChangeProps", tempChangeProps);

  // // 是否存在关联options
  // if (!formItemConfigs?.options?.length) return null;
  // const option = formItemConfigs.options.find(item => item.value === value);

  // // 是否存在修改其他组件属性
  // if (!option?.changeProps?.length) return null;

  // 是否存在changeProps
  // 最初只有option中支持changeProps 后面升级后 formItemConfigs也支持changeProps 并且支持给予关联值的简单运算
  const newFormConfigs = cloneDeep(formConfigs);
  const changeValData: Record<string, any> = {};
  tempChangeProps.forEach((item: { props: any; targetKey: string; }) => {
    // 浅克隆,避免value删除对原数据造成影响
    const props = { ...item.props };
    // 是否存在value属性
    if ("value" in props) {
      changeValData[item.targetKey] = props.value;
      delete props.value;
    }
    const formItem = newFormConfigs.find(formItem => formItem.key === item.targetKey);
    if (formItem) Object.assign(formItem, props);
  });
  return {
    changeValData,
    newFormConfigs,
  };
};

const delAndAddFormItem = (formList: FormSchema, cleanFormKey: string[], optionArr: Array<FormOption | null>, propertiesKeyArr: string[]) => {
  for (let i = formList.length; i--; ) {
    const formItem = formList[i];
    if (formItem.formItemType !== "collapse") {
      if (cleanFormKey.includes((formItem as FormItemConfigs).key)) {
        formList.splice(i, 1);
      }
      const index = propertiesKeyArr.findIndex(propertiesKey => propertiesKey === (formItem as FormItemConfigs).key);
      if (index !== -1 && optionArr[index]?.associatedForm?.length) {
        formList.splice(i + 1, 0, ...(optionArr[index]?.associatedForm as Array<FormItemConfigs>));
      }
    }
  }
};

export const updateFormList = (formConfig: FormSchema, cleanFormKey: string[], optionArr: Array<FormOption | null>, propertiesKeyArr: string[]) => {
  // 浅克隆表单配置json
  const newFormConfig = formConfig.map((item: FormItemConfigs | CollapseForm) => {
    if (item.formItemType === "collapse") {
      return { ...item, formList: [...(item as CollapseForm).formList] };
    } else {
      return item;
    }
  });
  // 更新表单,删除其他项表单,加入当前项表单
  delAndAddFormItem(newFormConfig, cleanFormKey, optionArr, propertiesKeyArr);
  // 处理折叠面板下表单更新
  newFormConfig.forEach(item => {
    if (item.formItemType === "collapse") {
      delAndAddFormItem((item as CollapseForm).formList, cleanFormKey, optionArr, propertiesKeyArr);
    }
  });
  return newFormConfig;
};

export const getRuntimeFormConfigs = (valItem: Record<string, any>, formConfigs: FormItemConfigs[]) => {
  const subFormConfigs = cloneDeep(formConfigs);
  const optionArr: FormOption[] = [];
  const propertiesKeyArr: string[] = [];
  const cleanFormKey: string[] = [];
  subFormConfigs.forEach(formConfig => {
    if (formConfig.options?.length) {
      formConfig.options.forEach(option => {
        if (option.value === valItem[formConfig.key]) {
          optionArr.push(option);
          propertiesKeyArr.push(formConfig.key);
        }
        // 收集其他选项表单key,用于隐藏对应表单
        option.associatedForm?.forEach(formItem => {
          cleanFormKey.push(formItem.key);
        });
      });
    }
  });
  let _newFormConfig = updateFormList(subFormConfigs, cleanFormKey, optionArr, propertiesKeyArr) as FormItemConfigs[];
  const _changeValData: Record<string, any> = {};
  Object.keys(valItem).forEach(key => {
    const res = updateConfigsProps({ key, value: valItem[key] }, _newFormConfig);
    if (res) {
      const { changeValData, newFormConfigs } = res;
      _newFormConfig = newFormConfigs;
      Object.assign(_changeValData, changeValData);
    }
  });
  return {
    changeValData: _changeValData,
    newFormConfigs: _newFormConfig,
  };
};

// 获取组件默认配置
export const getCompDefaultConfig = (type: string) => {
  if (type === "sprite") {
    return {
      tag: "",
      type: "sprite",
      dragable: true,
      deletable: false,
      canCombine: false,
      properties: {
        active: true,
        width: 240,
        height: 200,
        x: 0,
        y: 0,
        angle: 0,
        texture: "https://jiaoyanbos.cdnjtzy.com/cw_12e64254aa041f3afedbaf96c3c198ee.png",
      },
    };
  } else if (type === "label") {
    return {
      type: "label",
      tag: "",
      dragable: true,
      properties: {
        active: true,
        fontSize: 32,
        width: 195,
        height: 32,
        x: 0,
        y: 0,
        string: "单击添加文本",
        str: "",
        color: "#000000",
        cusorIndex: 0,
        selectArr: [],
        isLabelRight: true,
        isFixed: false,
        rowSpacing: 1.0,
      },
    };
  } else {
    return {};
  }
};

export const UPDATE_DATA_OBJ_DEFAULT = "NO_VALUE_PASSED_IN";

export const DEBOUNCE_TIME = 300;
