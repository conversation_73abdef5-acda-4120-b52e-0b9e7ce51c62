import { getQuestionTemporaryStorage, getVersionInfo } from "@/common/api/question";
import { plat, temporaryStorageId, snapId, id, cchdId, hdkt2Shark, h5Switch, fromGroup } from "@/pages/index/common/utils/getQueryParams";
import { requestPhpServer } from "@/common/utils/request";
import APIS from "@/common/api/constants";
import { refreshExtData, refreshQuestionData } from "@/common/utils/refreshTemplateData";
import store from "@/pages/index/store";
import { types as moduleAnimationsTypes } from "@/pages/index/store/modules/animations";
import { TimeMonitorType } from "@/common/utils/monitorUtil";

const hdId = id || cchdId;


// 根据id查找快照id
const fetchSnap = (id = snapId) => {
  return new Promise((resolve, reject) => {
    getVersionInfo(id)
      .then(res => {
        const data = res?.data?.data;
        if (!res.data.errNo && data && !Array.isArray(data)) {
          resolve(data);
        } else {
          reject('数据返回错误，请检查')
        }
      })
      .catch((error) => {
        reject(error);
      });
  })
}

const fetchData = async (id = hdId) => {
  try {
    if (!id && !temporaryStorageId && !hdkt2Shark) {
      throw new Error('页面参数错误，请检查后重试');
    }
    const res = temporaryStorageId ? await getQuestionTemporaryStorage(temporaryStorageId) : await requestPhpServer.post(APIS.PHP_GET_QUESTION_INFO, {
      qid: Number(id),
    });
    if (!res.data.errNo && res.data.data && !Array.isArray(res.data.data)) {
      const { data } = res.data;
      if (fromGroup && plat === "ykt" && localStorage.getItem("tagsData")) {
        const tagsData = JSON.parse(localStorage.getItem("tagsData") || "{}");
        data.tagsData = tagsData;
        if (!data.tagsData.contentTags) {
          data.tagsData.contentTags = [];
        }
        data.subjectId = tagsData.subjectId;
        data.gradeId = tagsData.gradeId;
      }
      data.parentVersion = data.parentVersion ?? 1;
      return Promise.resolve(data);
    } else {
      return Promise.reject('数据返回错误，请检查');
    }
  } catch (error) {
    return Promise.reject(error);
  }
}
export const fetchPageData = () => {
  return new Promise((resolve, reject) => {
    try {
      if (fromGroup && !hdId) {
        resolve(undefined);
      } else if (h5Switch) {
        resolve(undefined);
      } else if (hdkt2Shark) {
        resolve(undefined);
      } else if (snapId) {
        resolve(fetchSnap())
      } else {
        // 请求题板数据
        resolve(fetchData());
      }
    } catch (errData) {
      const err = errData as any;
      reject(err);
    }
  })
}

export const commitPageData2Store = (data: any) => {
  if (!data) {
    return false;
  }
  const { content, name, tempId, client, tagsData, gradeId, subjectId, status, parentVersion } = data;
  const questionData: Record<string, any> = (() => {
    const data = JSON.parse(content);
    //  兼容旧数据, 新数据没有 jsonData 这一层
    const { jsonData } = data;
    if (jsonData) {
      return JSON.parse(jsonData);
    }
    return data;
  })();
  // 将模版数据中的formConfig提取到extData
  let extData = data.extData;
  try {
    extData = refreshExtData(questionData, extData);
  } catch (error) {
    console.error('refreshExtData', error);
  }
  // 编辑器优化-待自测
  refreshQuestionData(data.category, extData, questionData);
  store.commit("initState", {
    ...questionData,
    tagsData: JSON.stringify(tagsData),
    gradeId,
    subjectId,
    name,
    templateId: tempId,
    status,
    parentVersion,
    extData,
  });
  // 初始化animation数据
  store.commit(moduleAnimationsTypes.mutations.initState, {
    config: store.state.template.animationConfig || [],
    initialData: store.state.animations,
  });
  store.commit("setClient", client);
  // 初始化数据成功
  store.commit("initialDataLoaded");
  if (localStorage.getItem('mockCocosInitFinished') === '1') {
    store.commit("cocosInitFinished");
  }
  (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.DataInit);
}
