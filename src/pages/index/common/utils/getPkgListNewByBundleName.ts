import APIS from "@/common/api/constants";
import { requestPhpServerPre } from "@/common/utils/request";

export const getPkgListNewByBundleName = async (bundleName: string, status = 4) => {
  // 先从缓存中读取
  let pkgUrl = await requestPhpServerPre
    .get(APIS.PHP_GET_PKGS, {
      params: {
        status, //2未启用 3 启用中 4已启用
      },
    })
    .then(res => {
      const pkg: any = res.data.data.pkgList ? res.data.data.pkgList[bundleName] : null;
      console.log("pkg===>", pkg);
      // const cdnUrl = pkg ? pkg.dirUrls.BdUrl : null;
      return pkg;
    });
  if (status === 2 && !pkgUrl) {
    pkgUrl = await getPkgListNewByBundleName(bundleName, 3);
  } else if (status === 3 && !pkgUrl) {
    pkgUrl = await getPkgListNewByBundleName(bundleName, 4);
  }
  return pkgUrl;
}