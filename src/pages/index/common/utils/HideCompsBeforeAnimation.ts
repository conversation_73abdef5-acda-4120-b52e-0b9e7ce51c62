import { isEnterTypeAnim, isExitTypeAnim } from "../../components/AnimationEditor/AddAction/options";
import { ComponentWithSubs } from "../../components/TopBar/Operations/dataProcessor";
import store from "../../store";
import { Animations, AnimationsState } from "../../store/modules/animations";

/**
 * @desc 获取需要在动画播放前隐藏的组件id
 *
 * @returns {string[]} ids
 */
export const getHideBeforeAnimationComponentIds = (animations: Animations) => {
  const hideBeforeAnimationComponentIds: string[] = [];
  const animationKeys = Object.keys(animations);
  animationKeys.forEach(animationKey => {
    const fragments = animations[animationKey].fragments;
    const fragmentKeys = Object.keys(fragments);
    fragmentKeys.forEach(fragmentKey => {
      const fragment = fragments[fragmentKey];
      fragment.forEach(action => {
        if (isEnterTypeAnim(action.value.anim.type)) {
          hideBeforeAnimationComponentIds.push(action.componentId);
        }
      });
    });
  });
  return hideBeforeAnimationComponentIds;
};


const getAnimationsList = () =>  {
  const animationsList: Animations[] = [];

  // 全局动画
  const { moduleAnimations } = store.state as {
    moduleAnimations: AnimationsState;
  };
  const { animations } = moduleAnimations;
  animationsList.push(animations);

  // 组件动画
  const { componentMap } = store.state as {
    componentMap: State["componentMap"];
  };
  Object.keys(componentMap).forEach(componentId => {
    const { animations } = componentMap[componentId].extra;
    if (animations) {
      animationsList.push(animations);
    }
  });

  return animationsList;
}



/**
* @desc 获取需要在动画播放前隐藏的组件id
*
* @returns {string[]} ids
*/
const getHideComponentIdsByAnimationsList = () => {
  const animationsList = getAnimationsList();
  const componentIds: string[] = [];
  animationsList.forEach(animations => {
    componentIds.push(...getHideBeforeAnimationComponentIds(animations));
  });
  return componentIds.filter((id) => store.state.componentMap[id].properties.active);
}

export class HideCompsBeforeAnimation {
  componentIds: string[];
  constructor() {
    this.componentIds = getHideComponentIdsByAnimationsList();
  }
  /**
  * @desc 隐藏组件
  *
  */
  hide() {
    return new Promise(resolve => {
      // 隐藏第一个动效资源
      // const firstCompId = Object.keys(store.state.componentMap).at(-1);
      // if (store.state.componentMap[firstCompId] && store.state.componentMap[firstCompId].type === 'spine') {
      //   this.componentIds = [firstCompId, ...this.componentIds]
      // }
      
      this.componentIds.forEach((id) => {
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['active']: false,
          },
          ignoreHistory: true,
        });
      });
      // 时间 需要根据测试结果调整
      const delay = 10 * this.componentIds.length;
      const timer = setTimeout(() => {
        clearTimeout(timer);
        resolve(true);
      }, delay);
      // resolve(true);
    });
  }
  /**
  * @desc 将隐藏的组件重新展示出来
  *
  */
  unhide() {
    this.componentIds.forEach((id) => {
      store.commit("updateComponentProperties", {
        id,
        newProperties: {
          ['active']: true,
        },
        ignoreHistory: true,
      });
    });
  }
  /**
   * @desc 遍历components，在component上添加hideBeforeAnimation属性
   *
   * @param hideBeforeAnimationComponentIds 需要添加的组件id
   * @param components 组件数组
   */
  addHide(val: boolean, components: ComponentWithSubs[]) {
    const helper = (component: Component) => {
      const { id } = component;
      if (this.componentIds.includes(id)) {
        component.hideBeforeAnimation = val;
      } else {
        delete component.hideBeforeAnimation;
      }
    };
    components.forEach(component => {
      const { subComponents = [] } = component;
      helper(component);
      if (subComponents.length) {
        subComponents.forEach((comp: Component) => {
          helper(comp);
        });
      }
    });
  }
}

/**
 * @desc 获取需要在动画播放前隐藏的组件id
 *
 * @returns {string[]} ids
 */
export const getHideBeforeScreenShotComponentIds = (animations: Animations) => {
  const hideBeforeAnimationComponentIds: string[] = [];
  const animationKeys = Object.keys(animations);
  animationKeys.forEach(animationKey => {
    const fragments = animations[animationKey].fragments;
    const fragmentKeys = Object.keys(fragments);
    fragmentKeys.forEach(fragmentKey => {
      const fragment = fragments[fragmentKey];
      fragment.forEach(action => {
        // 如果是读题后 隐藏退出的
        if (animationKey === 'afterReadingQuestion') {
          if (isExitTypeAnim(action.value.anim.type)) {
            console.log('读题后退出的动画', animationKey, action.componentId);
            hideBeforeAnimationComponentIds.push(action.componentId);
          }
        } else {
          // 其他的动画时机 隐藏进入的
          if (isEnterTypeAnim(action.value.anim.type)) {
            console.log('非读题后进入的动画', animationKey, action.componentId);
            hideBeforeAnimationComponentIds.push(action.componentId);
          }
        }
      });
    });
  });
  return hideBeforeAnimationComponentIds;
};

/**
* @desc 获取需要在截图前隐藏的组件id
*
* @returns {string[]} ids
*/
export const getHideComponentIdsBeforeScreenShot = () => {
  const animationsList = getAnimationsList();
  const componentIds: string[] = [];
  animationsList.forEach(animations => {
    componentIds.push(...getHideBeforeScreenShotComponentIds(animations));
  });
  return componentIds.filter((id) => store.state.componentMap[id].properties.active);
}
