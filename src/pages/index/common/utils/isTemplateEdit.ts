import { parse } from "query-string";

export const TEMPLATE_TYPE = "template";
export const SUB_TEMPLATE_TYPE = "subTemplate";

const data = parse(location.search);
const { type, id, temporaryStorageId } = data;

// 题版编辑 ?id=7&type=template
export const isTemplateEdit =
  type === TEMPLATE_TYPE && !!id && Object.keys(data).length === 2;

// 模板编辑 ?id=38&type=subTemplate
export const isSubTemplateEdit =
  type === SUB_TEMPLATE_TYPE && !!id && Object.keys(data).length === 2;

// 模板创建 ?temporaryStorageId=7824&type=subTemplate
export const isSubTemplateCreate =
  type === SUB_TEMPLATE_TYPE &&
  !!temporaryStorageId &&
  Object.keys(data).length === 2;

export default isTemplateEdit;
