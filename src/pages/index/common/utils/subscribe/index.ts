import { refreshCdnUrl } from "@/common/utils/resizePic";
import bus from "@/pages/index/common/utils/bus";
import store from "@/pages/index/store";
import { MutationType } from "@/pages/index/store/mutationType";
import throttle from "lodash-es/throttle";
import { parse } from "query-string";

const subscriberMap = {
  1134: (mutation: any) => {
    if (mutation.type === 'updateComponentExtra') {
      // console.log("FE-SubScribe-1134", mutation);
      // 找到所有同类tag的组件 tag === "hotImage" extra
      const {
        newExtra: { targerUrl }
      } = mutation.payload;
      if (typeof targerUrl === undefined) return;
      const { componentMap } = store.state;
      const url: string[] = [];
      let theComp: Component = null;
      Object.values(componentMap).forEach((item: any) => {
        if (item.tag === "hotImage" && item.extra) {
          url.push(item.extra.targerUrl)
        }
        if (item.subType === 'clues') {
          theComp = item;
        }
      });
      if (theComp) {
        store.commit("updateComponentProperties", {
          id: theComp.id,
          newProperties: {
            ['stuTargetItems']: url
          },
        });
      }
    }
  },
  1131: (mutation: any) => {
    if (mutation.type === "replaceCurrentComponentIds" || mutation.type === "cocos/replaceCurrentComponentIds") {
      const { componentMap } = store.state;
      const theCompt = componentMap[mutation.payload];
      if (!theCompt) return;
      if (theCompt.tag === "linkPoint") {
        // 切换到图片组件时 图片/宽高/图片 不可编辑
        store.commit("updateComponentEditable", {
          componentIds: [theCompt.id],
          newEditable: {
            ['properties']: {
              ['width']: false,
              ['height']: false,
              ['texture']: false,
            },
          },
        });
      } else if (theCompt.subType === "drawLineQuestion") {
        // 切换到连点成画组件时 linkPoint的图片/宽高/图片 可编辑
        const { componentMap } = store.state;
        Object.values(componentMap).forEach((item: any) => {
          if (item.tag === "linkPoint") {
            // 更新editable
            store.commit("updateComponentEditable", {
              componentIds: [item.id],
              newEditable: {
                ['properties']: {
                  ['width']: true,
                  ['height']: true,
                  ['texture']: true,
                },
              },
            });
          }
        });
      }
    }
  },
  unSelectComponent: (mutation: any) => {
    if (mutation.type === "unSelectComponent") {
      console.log('unSelectComponent subs emit');
      bus.$emit('unSelectComponent', mutation.payload);
    }
  },
  replaceCurrentComponentIds: throttle((mutation: any) => {
    const query = parse(window.location.search);
    const fromGroup = Number(query.fromGroup);
    if (mutation.type === "replaceCurrentComponentIds" || mutation.type === "cocos/replaceCurrentComponentIds") {
      // console.log('replaceCurrentComponentIds...', store.state.currentComponentIds[0], mutation.type, mutation.payload);
      bus.$emit('changeCurrentComponent', mutation.payload || []);
      fromGroup &&
        window.parent.postMessage(
          {
            action: "unselectedComponents",
          },
          "*",
        );
    }
  }, 10, { leading: true, trailing: true }),
  doubleClick: (mutation: any) => {
    if (mutation.type === "doubleClick") {
      // console.log('doubleClick...doubleClickComponent', mutation.type, mutation.payload);
      bus.$emit('doubleClickComponent', mutation.payload);
    }
  },
  1010: (mutation: any) => {
    if (mutation.type === "updateExtraStageData") {
      const { isAutoSubmit } = mutation.payload;
      // 选择自动提交时，提交方式默认置为0
      if (isAutoSubmit === true) {
        store.commit("updateExtraStageData", {
          autoSubmitMode: 0
        });
      }
    }
  },
  cocosResourcesLoaded: (mutation: any) => {
    const whiteTypes = [
      "cocosLoadQuestionBundleFinished",
      "cocosLoadGameSceneFinished",
      "cocosLoadStageRootNodeFinished",
      "cocosLoadCompListStart",
      "cocosLoadCompListFinished",
      "cocosInitFinished"
    ];
    if (whiteTypes.includes(mutation.type)) {
      bus.$emit('cocosResourcesLoaded', mutation.type);
    }
  },
  componentLengthChange: throttle(mutation => {
    // console.log('throttle...', mutation);
    // console.log('mutation...', mutation.type, mutation);
    // 组件数量变化时
    const componentLengthChangeMutationsTypes = [MutationType.initState, MutationType.addComponent, MutationType.removeAllComponentActionByComponentId];
    const typeMap = new Map([
      /**
       * 数据初始化时
       */
      [MutationType.initState, "init"],
      /**
       * 添加组件时
       */
      [MutationType.addComponent, "add"],
      /**
       * 移除组件时
       */
      [MutationType.removeAllComponentActionByComponentId, "remove"],
    ]);
    if (componentLengthChangeMutationsTypes.includes(mutation.type as any)) {
      console.log('fe-组件发生变化了!!!', 'mutation.type', mutation.type, 'componentLengthChange type', typeMap.get(mutation.type), Object.keys(store.state.componentMap).length);
      const timer = setTimeout(() => {
        clearTimeout(timer);
        bus.$emit('componentLengthChange', { length: Object.keys(store.state.componentMap).length, type: typeMap.get(mutation.type), components: Object.values(store.state.componentMap) });
      }, 300)

    }
  }, 300, { leading: true, trailing: true }),
  refreshCdnUrl: (mutation: any) => {
    if (mutation.type === MutationType.initState || mutation.type === MutationType.addComponent) {
    // console.log('mutation...', mutation.type);
      const timer = setTimeout(() => {
        clearTimeout(timer);
        // 处理cdn图片带参数导致图片不渲染的问题
        if (mutation.type === MutationType.initState || mutation.type === MutationType.addComponent) {
          refreshCdnUrl();
        }
      }, 300)
    }
  }
}

const mutationCounter: any = {};

export const getMutationCounter = () => {
  return mutationCounter;
}

export default class SubScribe {
  removesubscriber: any;
  
  constructor() {
    this.removesubscriber = store.subscribe(mutation => {
      subscriberMap['unSelectComponent'](mutation);
      subscriberMap['componentLengthChange'](mutation);
      subscriberMap['replaceCurrentComponentIds'](mutation);
      subscriberMap['doubleClick'](mutation);
      subscriberMap['cocosResourcesLoaded'](mutation);
      subscriberMap['refreshCdnUrl'](mutation);
      const { category } = store.state.template;
      subscriberMap[category] && subscriberMap[category](mutation);
      this.sumMutationCounter(mutation);
    });
    bus.$once("hook:beforeDestroy", () => {
      this.removesubscriber();
    });
  }

  sumMutationCounter(mutation: any) {
    const type = mutation.type;
      // console.log('mutation.type', type);
      if (type.includes('timeTravel')) {
        return;
      }
      if (!mutationCounter[type]) {
        mutationCounter[type] = 0;
      }
      mutationCounter[type] += 1;
      // console.log('mutationCounter', mutationCounter);
  }
  remove() {
    this.removesubscriber();
  }
}

