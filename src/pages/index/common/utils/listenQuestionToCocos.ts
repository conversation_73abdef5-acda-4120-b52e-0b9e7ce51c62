import { CATEGORY } from "@/common/constants";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import { isRenderFormEdit } from '@/common/utils/isRenderFormEdit';
import { getIsUploadEnable } from "@/common/utils/renderTextureToPicture";
import bus from "@/pages/index/common/utils/bus";
import store from '@/pages/index/store';
import { cloneDeep } from "lodash-es";
import { extractPicUrlFromJson } from "../../components/TopBar/Operations/dataProcessor";
import { picReSize } from "../hdkt2Shark/h5Switch";
const listenQDataToCompoent = async (dataOr: any) => {
  console.log("listenQDataToCompoent:", dataOr)
  const { category } = dataOr;
  if (category == CATEGORY.LISTENRETELL) {
    store.state.componentIds.forEach((Id: any) => {
      const element = store.state.componentMap[Id];

      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.LISTENRETELL) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: {
              ['question']: dataOr.question,
            },
          });
          console.log("question", dataOr.question)
        }
        else if (element.subType && element.subType == SpecialComponentSubTypes.MICROP) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: dataOr.speakerData
          });
          console.log("speakerData", dataOr.speakerData)
        }
      }
    });

  } else if (category == CATEGORY.LISTENANSWER) {
    store.state.componentIds.forEach((Id: any) => {
      const element = store.state.componentMap[Id];
      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.LISTENANSWER) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: {
              ['question']: dataOr.question,
            },
          });
          console.log("question", dataOr.question)
        }
        else if (element.subType && element.subType == SpecialComponentSubTypes.MICROP) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: dataOr.speakerData
          });
          console.log("speakerData", dataOr.speakerData)
        }
      }
    });
  } else if (category == CATEGORY.LOUDREADING) {
    store.state.componentIds.forEach((Id: any) => {
      const element = store.state.componentMap[Id];
      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.LOUDREADING) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: {
              ['question']: dataOr.question,
            },
          });
          console.log("question", dataOr.question)
        }
        else if (element.subType && element.subType == SpecialComponentSubTypes.MICROP) {
          store.commit("updateComponentProperties", {
            id: element.id,
            newProperties: dataOr.speakerData
          });
          console.log("speakerData", dataOr.speakerData)
        }
      }
    });
  } else if (category == CATEGORY.ENPK) {
    const components = Object.values(store.state.componentMap);
    components.forEach((comp) => {
      if (comp.subType && comp.subType == SpecialComponentSubTypes.ENPK) {
        store.commit("updateComponentProperties", {
          id: comp.id,
          newProperties: {
            ['pkData']: dataOr.pkData,
          },
        });
      }
    })
  }
  else if (category == CATEGORY.ENGROUPPK) {
    console.log('全员pk');
    const components = Object.values(store.state.componentMap);
    components.forEach((comp) => {
      if (comp.subType && comp.subType == SpecialComponentSubTypes.ENGROUPPK) {
        store.commit("updateComponentProperties", {
          id: comp.id,
          newProperties: {
            ['pkData']: dataOr.pkData,
          },
        });
        console.log('全员pk', comp);
      }
    })
  }
}

export const listenQuestionToCocos = async (dataOr: any) => {
  console.log(dataOr);
  const { category, data } = dataOr;
  switch (category) {
    case CATEGORY.LISTENRETELL:
      {
        const question = {
          "questionTitlePicList": [""], // (string)[]题干图片url (最大宽度 ：910)
          "tipsStart": "", // (string)label //转述开头
          "lookTime": 0, // (number s) // 看题时间
          "audioUrl": "", // (string) // 音频url

          "audioDuration": 0,  // 音频时长
          "playCount": 1,//(number ) // 播放次数
          "readyTime": 1,//(number s) // 准备时长
          "answerTime": 0,// (number s) // 答题时长
          "answerSetting": "",// (string) // 答案设置
          "keywordList": [],//string[] // 关键字设置
          "customH5": "", // 表格数据
          "title": "" // 表格数据
        }
        question.questionTitlePicList = [];
        question.tipsStart = data.tipsStart;
        question.lookTime = data.lookTime;
        question.audioUrl = data.audioUrl;
        question.audioDuration = data.audioDuration;
        question.playCount = data.playCount;
        question.readyTime = data.readyTime;
        question.answerTime = data.answerTime;
        question.answerSetting = data.answerSetting;
        question.keywordList = data.keywordList;
        question.customH5 = data.customH5;
        question.title = data.title;
        const speakerData = {
          "answerDuration": 0,
          "evaluatingText": "",      // 答案设置
          "answerkeyword": [],   // 关键字
        }
        speakerData.answerDuration = data.answerTime;
        speakerData.evaluatingText = data.answerSetting;
        speakerData.answerkeyword = data.keywordList;
        getIsUploadEnable(true);
        const list = await picReSize(data.questionTitlePicList[0]);
        question.questionTitlePicList = list;
        getIsUploadEnable(false);
        listenQDataToCompoent({ question, category, speakerData })
      }
      break;

    case CATEGORY.LISTENANSWER:
      {

        const question = {
          "questionTitle": "", // (string)[]题干图片url (最大宽度 ：910)
          "lookTime": 0, // (number s) // 看题时间
          "audioUrl": "", // (string) // 音频url

          "audioDuration": 0,  // 音频时长
          "playCount": 1,//(number ) // 播放次数
          "answerTime": 0,// (number s) // 答题时长
          "answerSetting": [],// (string) // 答案设置
          "keywordList": [],//string[] // 关键字设置
          "customH5": "" // 表格数据
        }
        question.questionTitle = data.questionTitle;
        question.lookTime = data.lookTime;
        question.audioUrl = data.audioUrl;
        question.audioDuration = data.audioDuration;
        question.playCount = data.playCount;
        question.answerTime = data.answerTime;
        question.answerSetting = data.answerSetting; // 数组
        question.keywordList = data.keywordList;
        question.customH5 = data.customH5;
        const speakerData = {
          "answerDuration": 0,
          "evaluatingText": "",      // 答案设置
          "answerkeyword": [],   // 关键字
        }
        speakerData.answerDuration = data.answerTime;
        speakerData.evaluatingText = JSON.stringify(data.answerSetting);// 数组需要转换
        speakerData.answerkeyword = data.keywordList;
        listenQDataToCompoent({ question, category, speakerData })
      }
      break;


    case CATEGORY.LOUDREADING:
      {
        const question = {
          "questionTitle": "", // (string)[]题干图片url (最大宽度 ：910)
          "readyTime": 0, // (number s) // 看题时间
          "answerTime": 0,// (number s) // 答题时长
          "customH5": "" // 表格数据
        }
        question.questionTitle = data.questionTitle;
        question.readyTime = data.readyTime;
        question.answerTime = data.answerTime;
        question.customH5 = data.customH5;
        const speakerData = {
          "answerDuration": 0,
          "evaluatingText": "",      // 答案设置
        }
        speakerData.answerDuration = data.answerTime;
        speakerData.evaluatingText = data.questionTitle;// 数组需要转换
        listenQDataToCompoent({ question, category, speakerData })
        console.log(data)
      }
      break;
    case CATEGORY.ENPK:
    case CATEGORY.ENGROUPPK:
      {
        listenQDataToCompoent({ category, pkData: { ...data, questionIndex: 0 } })

      }
      break;
    default:
      break;
  }

}

export const getFormQuestionOriginData = () => {
  console.log('...getFormQuestionOriginData start', store.state.template.category);
  const components = Object.values(store.state.componentMap);
  const { category } = store.state.template;
  const subTypeMap = new Map([
    [CATEGORY.LISTENRETELL, SpecialComponentSubTypes.LISTENRETELL],
    [CATEGORY.LISTENANSWER, SpecialComponentSubTypes.LISTENANSWER],
    [CATEGORY.LOUDREADING, SpecialComponentSubTypes.LOUDREADING]
  ])
  const targetComp = components.find((comp) => {
    return comp.subType === subTypeMap.get(category);
  })
  if (targetComp) {
    const { customH5 } = targetComp.properties.question;
    if (customH5) {
      const cloneData = cloneDeep(customH5);
      Object.values(cloneData).forEach((value: any) => {
        if (Array.isArray(value)) {
          value.forEach((item) => {
            if (item.error) item.error = [];
          })
        }
      })
      // 转述开头数据类型由数组改为字符串
      if (cloneData.answerStart && Array.isArray(cloneData.answerStart)) {
        cloneData.answerStart = cloneData.answerStart[0].value;
      }
      return cloneData;
    }
    return cloneDeep(customH5);
  }
  return null;
}

export const renderFormQuestionOriginData = () => {
  const { category } = store.state.template;
  if (!isRenderFormEdit(category)) return;

  bus.$once('form-question-mounted', () => {
    bus.$emit('form-question-render', ({ category, customH5: getFormQuestionOriginData() }))
  })
  bus.$emit('form-question-render', ({ category, customH5: getFormQuestionOriginData() }))
}

const confgLableMap = {
  text0: {
    x: 0,
    y: 188,
    fontSize: 64,
    color: "#000000",
    width: 1152,
    height: 96,
  },
  text1: {
    x: 0,
    y: 105,
    fontSize: 28,
    color: "#666666",
    width: 1152,
    height: 42,
  },
  text2: {
    x: 0,
    y: 54,
    fontSize: 28,
    color: "#666666",
    width: 1152,
    height: 42,
  },
  text3: {
    x: 0,
    y: 3,
    fontSize: 28,
    color: "#666666",
    width: 1152,
    height: 42,
  },
  text4: {
    x: 0,
    y: -48,
    fontSize: 28,
    color: "#666666",
    width: 1152,
    height: 42,
  }
}

const getNewID = (data: any) => {
  let maxComponentId = 0;
  for (let i = 0; i < data.length; i++) {
    const nId = Number(data[i]['id']);
    if (maxComponentId < nId) {
      maxComponentId = nId;
    }
  }

  return ++maxComponentId + "";
};

const addLableFollowWordQuesttion = (str: string, tag: string, newId: string) => {
  const confgLabl = confgLableMap[tag];
  const component: LabelComponent = {
    id: newId,
    type: "label",
    tag: tag || "",
    "dragable": true,
    "deletable": false,
    "isNoChangeTag": true,
    "canCombine": false,
    "editable": {
      "properties": {
        "str": false
      }
    },
    properties: {
      active: true,
      fontSize: confgLabl.fontSize,
      width: confgLabl.width,
      height: confgLabl.height,
      x: confgLabl.x,
      y: confgLabl.y,
      string: "单击添加文本",
      str: "<color=" + confgLabl.color + "><size=" + confgLabl.fontSize + ">" + str + "</size></color>",
      color: "#000000",
      cusorIndex: 0,
      horizontalAlign: 1,
      selectArr: [],
      isLabelRight: true,
      isFixed: false,
      rowSpacing: 1.0,
      lineHeight: confgLabl.height
    },
    extra: {
      tag: tag || "",
    },
  };
  return component;
}

const compertLabelTag = (tag: string) => {
  let isFind = false;
  if (tag == "text0" || tag == "text1" || tag == "text2" || tag == "text3" || tag == "text4") {
    isFind = true;
  }
  return isFind;
}
const findCustomFollowordData = (data: any[], index: number) => {
  let findData = null;
  if (data[index]) {
    findData = data[index];
  }
  return findData;

}

(window as any).updateFollowWordsQuesttionData = (madeData: any) => {
  const question = madeData.questions;
  const customData = madeData.customH5;

  for (let i = 0; i < question.length; i++) {
    question[i].content = JSON.parse(question[i].content);
    const components = question[i].content.components;
    const smartH5Data = findCustomFollowordData(customData, i);
    for (let j = 0; j < components.length; j++) {
      if (components[j]["type"] == "label" && compertLabelTag(components[j]['tag'])) {
        components.splice(j, 1);
        j--;
      }
      if (components[j]['subType'] == "microp") {
        components[j].properties['audioUrl'] = (smartH5Data && smartH5Data.audioUrl) || "";
        components[j].properties['evaluatingText'] = (smartH5Data && smartH5Data.word) || "";
        components[j].properties['answerDuration'] = (smartH5Data && smartH5Data.answerTime) || 0;
      }
    }

    if (smartH5Data && smartH5Data.word) {
      components.push(addLableFollowWordQuesttion(smartH5Data.word, "text0", getNewID(components)));
    }
    if (smartH5Data && smartH5Data.explains) {
      for (let k = 0; k < smartH5Data.explains.length; k++) {
        if (k < 4) {
          components.push(addLableFollowWordQuesttion(smartH5Data.explains[k], "text" + (k + 1), getNewID(components)));
        }
      }
    }
    const resourceList = extractPicUrlFromJson(question[i].content);
    question[i].content.resourceList = resourceList;
    // 把question[i].content中的远程资源找出来
    question[i].content = JSON.stringify(question[i].content);
  }

  console.log("testReturn", question)
  return question;
}




