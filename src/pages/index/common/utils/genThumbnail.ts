import { HideCompsBeforeAnimation, getHideComponentIdsBeforeScreenShot } from "./HideCompsBeforeAnimation";
import { getTagToken, getTagsByToken } from "@/common/api/bindTag";
import bus from "./bus";

const sleep = (time: number) => {
  return new Promise(resolve => (time === 0 ? resolve(0) : setTimeout(resolve, time)));
};
/**
 * @desc 生成缩略图
 */
export const genScreenshot = async (): Promise<string> => {
  const texture = await (window as MyWindow).cocos.screenshot();
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

export const genTextureCdnUrl = async (texture: any): Promise<string> => {
  const data = texture.data;
  const width = texture.texture.width;
  const height = texture.texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
    // document.body.appendChild(canvas);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

/**
 * @desc 生成缩略图
 */
export const genSubQuestionScreenshot = async (index: number, compId = '1', delay = 0): Promise<string> => {
  await (window as MyWindow).cocos.getSmallCaptureById(compId, {
    stuFocus: index,
    subQuestionIndex: index,
    // eslint-disable-next-line @typescript-eslint/camelcase
    stu_chosenQuestionId: index,
  });
  await sleep(delay);
  const hideCompIds = getHideComponentIdsBeforeScreenShot();
  console.log('hideCompIds', hideCompIds);
  // hideComponentShot 
  const hideIds = hideCompIds.map(item => Number(item));
  const texture = await (window as MyWindow).cocos.hideComponentShot(hideIds);
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          console.log('genSubQuestionScreenshot-url', url);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}
/**
 * @desc 生成缩略图，在原有基础上隐藏了在播放动画之前需要隐藏的动效
 */
export const genThumbnailV2 = async (): Promise<string> => {
  // 先隐藏需要在动画播放之前隐藏的组件
  const hideComponents = new HideCompsBeforeAnimation();
  await hideComponents.hide();

  // console.log('hideComponents', hideComponents);
  // 取消上一步的隐藏操作

  hideComponents.unhide();
  const texture = await (window as MyWindow).cocos.screenshot();
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          console.log('生成缩略图-done', url);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

/**
 * @desc 生成缩略图，在原有基础上隐藏了在播放动画之前需要隐藏的动效
 */
export const genThumbnailNew = async (): Promise<string> => {
  // 先隐藏需要在动画播放之前隐藏的组件
  const hideCompIds = getHideComponentIdsBeforeScreenShot();
  console.log('hideCompIds', hideCompIds);
  // hideComponentShot 
  const hideIds = hideCompIds.map(item => Number(item));
  const texture = await (window as MyWindow).cocos.hideComponentShot(hideIds);
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          console.log('生成缩略图-done', url);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

/**
  * @desc 生成缩略图
  */
export const genThumbnailPre = async (): Promise<string> => {
  const texture = await (window as MyWindow).cocos.screenshot();
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }
  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          if (url && url !== "") {
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

export class AIPicTask {
  public url!: string;
  public token!: string;
  public awaitTimer!: any;
  public taskTimer!: any;
  public taskDelay = 1000;
  public AITags: number[] = [];
  /** 记录请求token的所有url */
  public tasks: Array<{ url: string, token: string, duration: number, startTime: number, endTime: number, tags: number[] }> = [];
  constructor(url: string) {
    console.log('AIPic constructor');
    bus.$emit('clearAIPicTags')
    this.url = url;
    this.setToken();
  }
  public async setToken() {
    getTagToken(this.url).then((res) => {
      console.log('setToken', res);
      if (!this.tasks.find((item) => item.token === res)) {
        this.tasks.push({
          url: this.url,
          token: res,
          startTime: Date.now(),
          endTime: 0,
          duration: 0,
          tags: []
        });
      }
      if (res) {
        this.token = res;
        this.start();
        (window as MyWindow).monitorManager.liveLog('I5C_004', {
          token: res,
          url: this.url,
          startTime: Date.now(),
          dotType: 'aiToken'
        });
      }
    })
  }
  public async setUrl(url: string) {
    // 需要清空原来的 aiTags
    bus.$emit('clearAIPicTags')
    if (this.taskTimer) {
      this.clear();
    }
    this.url = url;
    this.setToken();
  }
  public async start() {
    if (!this.token) return;
    if (this.awaitTimer) {
      clearTimeout(this.awaitTimer);
    }
    this.awaitTimer = setTimeout(() => {
      if (this.taskTimer) {
        clearInterval(this.taskTimer);
      }
      this.taskTimer = setInterval(() => {
        getTagsByToken(this.token).then((res) => {
          if (res && res.status === 1) {
            clearInterval(this.taskTimer);
            this.taskTimer = null;
            const theTask = this.tasks.find((item) => item.token === this.token);
            if (theTask) {
              theTask.tags = res.list;
              theTask.endTime = Date.now();
              theTask.duration = theTask.endTime - theTask.startTime;
              (window as MyWindow).monitorManager.liveLog('I5C_005', {
                ...theTask,
                dotType: 'aiRes'
              });
            }
            // console.log('theTask～～～', theTask, this.tasks);
            this.token = '';
            this.AITags = res.list;
            bus.$emit('getAIPicTags', { url: this.url, AITags: this.AITags });

          }
        })
      }, this.taskDelay)
    }, 3000);
  }
  public async clear() {
    if (this.taskTimer) {
      clearInterval(this.taskTimer);
      this.taskTimer = null;
    }
    if (this.awaitTimer) {
      clearTimeout(this.awaitTimer);
      this.awaitTimer = null;
    }
    this.token = ''
    this.url = ''
    this.AITags = []
  }
}

(window as any).genThumbnail = genThumbnailV2;
