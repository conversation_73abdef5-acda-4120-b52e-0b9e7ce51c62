import Vue from "vue";
import store from "@/pages/index/store";

export const cache = new Map<string, Component>();

// 是否常驻
export class CreateComp {
  instance: any;
  dom: HTMLElement = document.createElement("div");
  idPrefix: string;
  keepLive: boolean;
  constructor(params: { component: Component, idPrefix: string, keepLive?: boolean, createdFn?: (instance: any) => void; parent?: any, propsData?: any, handleConfirm?: (instance: any) => void; handleCancel?: (instance: any) => void; }) {
    const { component, idPrefix, keepLive = false, parent, createdFn, propsData = {} } = params;
    this.idPrefix = idPrefix;
    this.keepLive = keepLive;
    if(keepLive && cache.has(idPrefix)) {
      this.instance = cache.get(idPrefix);
    } else {
      const div = this.dom;
      const el = document.createElement("div");

      const id = idPrefix + Math.random();
      div.id = id;
      div.appendChild(el);
      if (parent) {
        parent.appendChild(div);
      } else {
        document.body.appendChild(div);
      }
      const ComponentConstructor = Vue.extend(component);
      this.instance = new ComponentConstructor({
        propsData,
        parent,
        store,
      }).$mount(el);
      if (keepLive) {
        cache.set(idPrefix, this.instance);
      }
      if(createdFn) {
        createdFn(this.instance);
      }
    }
  }
  destroy() {
    const { instance, dom, idPrefix } = this;
    if (instance && dom.parentNode) {
      cache.delete(idPrefix);
      instance.$destroy();
      // @ts-ignore
      this.instance = null;
      dom.parentNode && dom.parentNode.removeChild(dom);
    }
  }
  confirm(showFn?: (instance: any) => void) {
    // console.log('confirm...', this.instance);
    if(showFn) {
      showFn(this.instance);
    }
    const instance = this.instance;
    return new Promise((resolve, reject) => {
      // emit 一个 done 事件关闭
      this.instance.$once("done", (data: any) => {
        this.instance.$off("cancel");
        this.instance.$off("destroy");
        if (!this.keepLive) {
          this.destroy();
        }
        
        resolve({data, instance});
      })
      // emit 一个 cancel 事件取消
      this.instance.$once("cancel", (data: any) => {
        // 销毁done的监听
        this.instance.$off("done");
        this.instance.$off("destroy");
        if (!this.keepLive) {
          this.destroy();
        }
        reject({data, instance});
      });
      this.instance.$once("destroy", (data: any) => {
        this.instance.$off("done");
        this.instance.$off("cancel");
        if (!this.keepLive) {
          this.destroy();
        }
        reject({data, instance});
      });
    });
  }
}
