export function toFixed(num: number, n: number) {
  return Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
}

export function convertToLeftTopCoordinate(
  x: number,
  y: number,
  stageWidth: number,
  stageHeight: number
) {
  const newX = stageWidth / 2 + x;
  const newY = stageHeight / 2 - y;
  return { x: toFixed(newX, 1), y: toFixed(newY, 1) };
}

export function convertToCenterCoordinate(
  x: number,
  y: number,
  stageWidth: number,
  stageHeight: number
) {
  const newX = -stageWidth / 2 + x;
  const newY = stageHeight / 2 - y;
  return { x: toFixed(newX, 1), y: toFixed(newY, 1) };
}
