/*
 * @Date: 2021-12-23 16:37:43
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-23 16:42:44
 * @FilePath: /interactive-question-editor/src/pages/index/common/utils/initParentVersion.ts
 * @Author: chxu
 */
import { parse } from "query-string";
/** 
 *取parentVersion 优先级
  1. 路由
  2. localStorage 测试使用
  3. 当前版本值 现在是1
  */
export const initParentVersion = () => {
  const query = parse(window.location.search);
  const fromQuery = query.parentVersion ? Number(query.parentVersion) : undefined;
  const fromLocalStorage = localStorage.getItem('parentVersion') ? Number(localStorage.getItem('parentVersion')) : undefined;
  const fromCurrent = 1;
  return fromQuery || fromLocalStorage || fromCurrent;
}