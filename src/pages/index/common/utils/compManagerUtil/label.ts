import { CompCommonClass, store } from "./CompCommonClass";
import { cloneDeep } from 'lodash';
console.log('LabelCompClass start');
class LabelCompClass extends CompCommonClass {
  private defaultCompData: Omit<LabelComponent, "id"> = {
    type: "label",
    tag: "",
    dragable: true,
    properties: {
      active: true,
      fontSize: 32,
      // lineHeight: 32,
      width: 195,
      height: 32,
      x: 0,
      y: 0,
      string: "单击添加文本",
      str: "",
      color: "#000000",
      cusorIndex: 0,
      selectArr: [],
      isLabelRight: true,
      isFixed: false,
      rowSpacing: 1.0,
    },
  };
  constructor() {
    super('label');
  }
  create() {
    store.dispatch("addComponentAndFocus", cloneDeep(this.defaultCompData));
  }
}

export default new LabelCompClass;