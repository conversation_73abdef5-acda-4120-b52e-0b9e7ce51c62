interface CompManagerItem {
  create: () => void,
  edit?: () => void,
}

export interface ComponentLibraryItem {
  type: BaseComponent["type"] | "speaker" | "h5Label" | "shape" | "richTextSprite";
  label: string;
  icon: string;
  disabled?: boolean;
  forbiddenText?: string;
  util?: () => Promise<CompManagerItem>;
}

export const compClassMap = new Map<ComponentLibraryItem['type'], any>([
  ['label', () => import(/* webpackChunkName: "Label" */ "./label")],
  ['brush', () => import(/* webpackChunkName: "Brush" */ "./brush")],
  ['richTextSprite', () => import(/* webpackChunkName: "RichTextSprite" */ "./richTextSprite")],
  ['h5Label', () => import(/* webpackChunkName: "H5Label" */ "./h5Label")],
  ['formula', () => import(/* webpackChunkName: "formula" */ "./formula")],
  ['sprite', () => import(/* webpackChunkName: "sprite" */ "./sprite")],
  ['spine', () => import(/* webpackChunkName: "spine" */ "./spine")],
  ['speaker', () => import(/* webpackChunkName: "speaker" */ "./speaker")],
  ['shape', () => import(/* webpackChunkName: "speaker" */ "./shape")],
]);

class CompManagerUtil {
  private configs: Record<string, CompManagerItem> = {};
  constructor() {
    console.log("CompManagerUtil init");
  };
  public registerComponent(type: ComponentLibraryItem['type']): Promise<CompManagerItem> {
    return new Promise((resolve, reject) => {
      if (this.configs[type]) {
        resolve(this.configs[type]);
      } else {
        compClassMap.get(type)().then((module: { default: CompManagerItem }) => {
          this.configs[type] = (module.default as CompManagerItem);
          resolve(module.default);
        }).catch((err: any) => {
          reject(err);
        });
      }
    });
  };
  public async addComponent(type: ComponentLibraryItem['type']) {
    try {
      if (!this.configs[type] && compClassMap.get(type)) {
        await this.registerComponent(type);
      }
      if (this.configs[type]) {
        this.configs[type]?.create();
      } else {
        console.warn(`未找到组件${type}`);
      }
    } catch (error) {
      console.error(error);
    }

  };
  public async editComponent(type: ComponentLibraryItem['type']) {
    try {
      if (!this.configs[type] && compClassMap.get(type)) {
        await this.registerComponent(type);
      }
      if (this.configs[type]) {
        this.configs[type]?.edit && this.configs[type]?.edit?.();
      }
    } catch (error) {
      console.error(error);
    }
  }
}

export const compManagerUtil = new CompManagerUtil();
