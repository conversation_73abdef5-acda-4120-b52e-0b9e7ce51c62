import { bus, CompCommonClass } from "./CompCommonClass";
console.log('ShapeCompClass start');
import { CreateComp } from "@/pages/index/common/utils/createComp";
import ShapeEditModal, { ShapeStatus } from "@/pages/index/components/EditArea/H5ShapeComponentEditor/ShapeEditModal.vue";
class ShapeCompClass extends CompCommonClass {
  constructor() {
    console.log('ShapeCompClass constructor');
    super('shape');
  }
  create() {
    bus.$emit("dialogVisible", true);
    new CreateComp({
      component: ShapeEditModal,
      idPrefix: 'shape-editor',
      propsData: {
        status: ShapeStatus.Create
      }
    }).confirm().then((res) => {
      bus.$emit("dialogVisible", false);
      return res;
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }
  edit() {
    bus.$emit("dialogVisible", true);
    new CreateComp({
      component: ShapeEditModal,
      idPrefix: 'shape-editor',
      propsData: {
        status: ShapeStatus.Edit
      }
    }).confirm().then((res) => {
      bus.$emit("dialogVisible", false);
      return res;
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }
}

export default new ShapeCompClass;
