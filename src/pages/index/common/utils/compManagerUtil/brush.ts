import { cloneDeep } from "lodash-es";
import { CompCommonClass, store } from "./CompCommonClass";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
class BrushCompClass extends CompCommonClass {
  constructor() {
    super(SpecialComponentSubTypes.BRUSH);
  }
  private defaultCompData: Omit<BrushComponent, "id"> = {
    type: "specialComponent",
    subType: SpecialComponentSubTypes.BRUSH,
    tag: "",
    dragable: true,
    editable: {
      properties: {
        x: false,
        y: false,
        width: false,
        height: false,
        angle: false,
        rotation: false,
        opacity: false,
        scaleX: false,
        scaleY: false,
        color: false,
        active: false,
      },
    },
    properties: {
      active: true,
      width: 95,
      height: 182,
      x: 557,
      y: 156,
      color: "#000000",
      style: 0,
      mountList: [],
    },
  };
  create() {
    store.dispatch("addComponentAndFocus", cloneDeep(this.defaultCompData));
  }
}


export default new BrushCompClass;