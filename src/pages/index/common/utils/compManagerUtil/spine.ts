import { CompCommonClass, store, bus } from "./CompCommonClass";

class SpineCompClass extends CompCommonClass {
  constructor() {
    console.log('SpineCompClass constructor');
    super('spine');
  }
  create() {
    (window as MyWindow).$getPageConfigByKey("SpineLibrary")().then((comp: { SpineLibrary: (arg0: { isMultiple: boolean; classify: string; onConfirm: (spines: any) => void }) => void }) => {
      comp.SpineLibrary({
        isMultiple: true,
        classify: "spine",
        onConfirm: (spines: any[]) => {
          spines.forEach((spineItem: { atlas: any; images: any; skeleton: any; cover: any }, index: number) => {
            const component: Omit<SpineComponent, "id"> = {
              tag: "",
              type: "spine",
              properties: {
                timeScale: 1,
                animationList: [""],
                loop: true,
                active: true,
                scaleX: 1,
                scaleY: 1,
                x: 0 + index * 20,
                y: 0 - index * 20,
              },
              spineData: {
                atlas: spineItem.atlas,
                images: spineItem.images,
                skeleton: spineItem.skeleton,
                cover: spineItem.cover,
              },
            };
            store.dispatch("addComponentAndFocus", component);
          });
        },
      });
    });
  }
}

export default new SpineCompClass;
