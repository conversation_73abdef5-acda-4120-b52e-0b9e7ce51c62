import { bus, CompCommonClass, store } from "./CompCommonClass";
console.log('H5LabelCompClass start');
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import { CreateComp } from "@/pages/index/common/utils/createComp";
import EditModal from "@/pages/index/components/EditArea/H5LabelComponentEditor/EditDialog.vue";

const handleCreate = (params: {
  formData
  : any; labelPicList: string[];
}) => {
  console.log('handleCreate..', params);
  const component: Omit<H5LabelComponent, "id"> = {
    type: "specialComponent",
    subType: SpecialComponentSubTypes.H5LABEl,
    tag: "",
    dragable: true,
    canCombine: false,
    properties: {
      active: true,
      width: 1000,
      height: 640,
      x: 0,
      y: 0,
      customH5label: params.formData,
      labelPicList: params.labelPicList,
    },
  };
  store.dispatch("addComponentAndFocus", component);
}

const handleEdit = (params: {
  formData
  : any; labelPicList: string[];
}) => {
  store.commit("updateComponentProperties", {
    id: store.state.currentComponentIds[0],
    newProperties: {
      customH5label: params.formData,
      labelPicList: params.labelPicList,
    },
  });
}
const idPrefix = "h5label-editor";

class H5LabelCompClass extends CompCommonClass {
  constructor() {
    console.log('H5LabelCompClass constructor');
    super('h5Label');
  }
  /**
   * 创建组件
   */
  create() {
    bus.$emit("dialogVisible", true);
    new CreateComp({
      component: EditModal,
      idPrefix,
    }).confirm((instance) => {
      instance.open('create', '');
    }).then((res: any) => {
      handleCreate(res.data);
      bus.$emit("dialogVisible", false);
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }
  /**
   * 编辑组件
   */
  edit() {
    bus.$emit("dialogVisible", true);
    const content = this.getPropValByKey('customH5label');
    new CreateComp({
      component: EditModal,
      idPrefix,
    }).confirm((instance) => {
      instance.open('edit', content);
    }).then((res: any) => {
      handleEdit(res.data);
      bus.$emit("dialogVisible", false);
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }

  /**
   * 创建富文本并返回一个组件的id 
   */
  createById(id: string) {
    return new Promise((resolve, reject) => {
      bus.$emit("dialogVisible", true);
      new CreateComp({
        component: EditModal,
        idPrefix,
      }).confirm((instance) => {
        instance.open('create', '');
      }).then((res: any) => {
        // handleCreate(res.data);
        bus.$emit("dialogVisible", false);
        resolve({
          id,
          data: res.data
        });
      }).catch(() => {
        bus.$emit("dialogVisible", false);
        reject();
      });
    })
  }
  /**
   * 根据组件id获取编辑内容后创建
   */
  editById(id: string) {
    return new Promise((resolve, reject) => {
      bus.$emit("dialogVisible", true);
      const content = store.state.componentMap[id].properties['customH5label']
      new CreateComp({
        component: EditModal,
        idPrefix,
      }).confirm((instance) => {
        instance.open('edit', content);
      }).then((res: any) => {
        // handleEdit(res.data);
        bus.$emit("dialogVisible", false);
        resolve({
          id,
          data: res.data
        })
      }).catch(() => {
        bus.$emit("dialogVisible", false);
        reject();
      });
    })

  }

  dialog(content? : any, propsData?: any) {
    return new Promise((resolve, reject) => {
      new CreateComp({
        component: EditModal,
        idPrefix,
        propsData
      }).confirm((instance) => {
        if (content) {
          instance.open('edit', content);
        } else {
          instance.open('create', '');
        }
      }).then((res: any) => {
        // handleCreate(res.data);
        bus.$emit("dialogVisible", false);
        resolve({
          data: res.data
        });
      }).catch(() => {
        bus.$emit("dialogVisible", false);
        reject();
      });
    });
  }

}

export default new H5LabelCompClass;