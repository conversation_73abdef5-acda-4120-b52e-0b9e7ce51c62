import { bus, CompCommonClass, store } from "./CompCommonClass";
console.log('FormulaCompClass start');
import FormulaModal, { OutputType } from "@/pages/index/components/FormulaModal";

class FormulaCompClass extends CompCommonClass {
  constructor() {
    console.log('FormulaCompClass constructor');
    super('formula');
  }
  create() {
    bus.$emit("dialogVisible", true);
    FormulaModal({
      onClose: () => {
        console.log('FormulaModal...onClose');
        bus.$emit("dialogVisible", false);
      },
      onConfirm: (params) => {
        console.log("FormulaModal...onConfirm", params);
        const component: Omit<FormulaComponent, "id"> = {
          type: "formula",
          tag: "",
          dragable: true,
          properties: {
            active: true,
            width: params.width,
            height: params.height,
            x: 0,
            y: 0,
            color: "#000000",
            url: params.url,
            latex: params.latex,
          },
        };
        store.dispatch("addComponentAndFocus", component);
        bus.$emit("dialogVisible", false);
      },
      latex: "",
      type: OutputType.SVG,
    });
  }
  edit() {
    bus.$emit("dialogVisible", true);
    const latex = this.getPropValByKey('latex');
    FormulaModal({
      onClose: () => {
        console.log('FormulaModal...onClose');
        bus.$emit("dialogVisible", false);
      },
      onConfirm: (params) => {
        console.log("FormulaModal...onConfirm", params);
        store.dispatch("updateComponentsProperties", {
          ids: [store.getters.currentComponents[0].id],
          newProperties: {
            url: params.url,
            latex: params.latex,
            width: params.width,
            height: params.height,
          },
        });
        bus.$emit("dialogVisible", false);
      },
      latex,
      type: OutputType.SVG,
    });
  }
}

export default new FormulaCompClass;