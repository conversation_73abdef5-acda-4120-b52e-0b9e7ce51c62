import { Message } from "element-ui";
import { CompCommonClass, store } from "./CompCommonClass";
import { SpecialComponentSubTypes } from "@/common/constants";

class SpeakerCompClass extends CompCommonClass {
  constructor() {
    console.log('SpeakerCompClass constructor');
    super('speaker');
  }
  create() {
    let count = 0;
          store.state.componentIds.forEach((Id: any) => {
            const element = store.state.componentMap[Id];
            if (element) {
              if (element.subType && element.subType == SpecialComponentSubTypes.SPEAKER) {
                count++;
              }
            }
          });
          if (count >= 10) {
            Message.error("最多支持插入10个音频");
            return;
          }
  
          const component: Omit<SpeakerComponent, "id"> = {
            type: "specialComponent",
            subType: SpecialComponentSubTypes.SPEAKER,
            tag: "",
            dragable: true,
            properties: {
              active: true,
              width: 72,
              height: 72,
              x: count * 10,
              y: count * -10,
              speakerType: 1,
              audioUrl: "",
              count: -1,
              autoPlay: false,
              notStopPlaying: false,
              countdown: false,
              autoRepeatCount: 1,
              countdownSkin: 0,
              duration: 0,
            },
          };
          store.dispatch("addComponentAndFocus", component);
  }
}

export default new SpeakerCompClass;
