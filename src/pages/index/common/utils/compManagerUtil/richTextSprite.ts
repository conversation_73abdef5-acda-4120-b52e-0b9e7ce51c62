import { bus, CompCommonClass, store } from "./CompCommonClass";
import { CreateComp } from "@/pages/index/common/utils/createComp";
import RichTextEditModal from "@/pages/index/components/EditArea/NewH5LabelComponentEditor/RichTextEditModal.vue";

const handleCreate = (params: { url: string; content: string; }) => {
  console.log('handleCreate', params);
  const { url, content,  } = params;
  const propKey = `texture`;
  const uniqueKeyValue = `${propKey}${Date.now()}`;
  const component: Omit<RichTextSpriteComponent, "id"> = {
    type: "richTextSprite",
    tag: "",
    dragable: true,
    canCombine: true,
    properties: {
      active: true,
      x: (params as any).left,
      y: (params as any).top,
      textureRichTextKey: uniqueKeyValue,
      [propKey]: url,
      width: (params as any).width || 300,
      height: (params as any).height || 300,
    },
  };
  store.dispatch("addComponentAndFocus", component);
  const compId = store.state.currentComponentIds[0];
  console.log('compId...', compId);
  store.commit("updateComponentProperties", {
    id: compId,
    newProperties: {
      x: (params as any).left,
      y: (params as any).top,
    },
    ignoreHistory: true,
  });
  store.commit("updateExtProps", {
    key: uniqueKeyValue,
    value: [encodeURIComponent(content)],
    ignoreHistory: true,
  });

  store.commit("updateComponentEditable", {
    componentIds: [compId],
    newEditable: {
      ["properties"]: {
        ["width"]: false,
        ["height"]: false,
        ["color"]: false,
        ["scaleX"]: true,
        ["scaleY"]: true,
      },
      ignoreHistory: true,
    },
  });
}

class RichTextSpriteCompClass extends CompCommonClass {
  constructor() {
    console.log('RichTextSpriteCompClass constructor');
    super('richTextSprite');
  }
  create() {
    bus.$emit("dialogVisible", true);
    new CreateComp({
      component: RichTextEditModal,
      idPrefix:'richtext-editor',
      keepLive:  true
    }).confirm((instance) => {
      instance.setStatus('create');
      instance.handleClick();
    }).then((res: any) => {
      handleCreate(res.data);
      bus.$emit("dialogVisible", false);
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }
  edit() {
    bus.$emit("dialogVisible", true);
    new CreateComp({
      component: RichTextEditModal,
      idPrefix:'richtext-editor',
      keepLive:  true,
    }).confirm((instance) => {
      instance.setStatus('edit');
      instance.handleClick();
    }).then(() => {
      bus.$emit("dialogVisible", false);
    }).catch(() => {
      bus.$emit("dialogVisible", false);
    });
  }
}

export default new RichTextSpriteCompClass;