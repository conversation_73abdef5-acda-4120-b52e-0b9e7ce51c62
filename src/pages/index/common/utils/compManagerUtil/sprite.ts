import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary";
import { CompCommonClass, store, bus } from "./CompCommonClass";
import getImageSize from "@/common/utils/getImageSize";

class SpriteCompClass extends CompCommonClass {
  constructor() {
    console.log('SpriteCompClass constructor');
    super('sprite');
  }
  create() {
    bus.$emit("material-show");
    ImageLibrary({
      isMultiple: true,
      onConfirm: images => {
        images.forEach((image, index) => {
          const { url } = image;
          getImageSize(url).then(({ width, height }) => {
            const component: Omit<SpriteComponent, "id"> = {
              tag: "",
              type: "sprite",
              dragable: true,
              properties: {
                active: true,
                width,
                height,
                x: 0 + index * 20,
                y: 0 - index * 20,
                texture: url,
              },
            };
            store.dispatch("addComponentAndFocus", component);
          });
        });
      },
    });
  }
}

export default new SpriteCompClass;
