import store from "@/pages/index/store";
import bus from "@/pages/index/common/utils/bus";
import { ComponentLibraryItem } from ".";

class CompCommonClass {
  public type: ComponentLibraryItem['type'];
  constructor(type: ComponentLibraryItem['type']) {
    this.type = type;
  }
  public getCurrCompId() {
    return store.getters.currentComponents[0]?.id
  }
  public getCurrComp() {
    return store.getters.currentComponents[0]
  }
  public getPropValByKey(key: string) {
    return store.getters.currentComponents[0].properties[key]
  }
}
// 导出store bus CompCommonClass作为默认导出
export { store, bus, CompCommonClass, ComponentLibraryItem };