// import JS<PERSON><PERSON> from "jszip";

//  /**
//    * 获取图片尺寸
//    */
//   function getFileSize(fileData: any) {
//     return new Promise((resolve, reject) => {
//       const reader = new FileReader();
//       reader.onload = (e: any) => {
//         // 创建一个image对象，判断宽高
//         const image = new Image();
//         image.onload = () => {
//           const w = image.width;
//           const h = image.height;
//           console.log("wwwwwwhhhhhh", w, h);
//           resolve({ width: w, height: h });
//         };
//         image.onerror = () => {
//           reject(null);
//         };
//         image.src = e.target.result;
//       };
//       reader.onerror = () => {
//         reject(null);
//       };
//       reader.readAsDataURL(fileData);
//     });
// }

// function getFileSizeByBase64(data: any) {
//     return new Promise((resolve, reject) => {
//         // 创建一个image对象，判断宽高
//         const image = new Image();
//         image.onload = () => {
//         const w = image.width;
//         const h = image.height;
//         console.log("wwwwwwhhhhhh", w, h);
//         resolve({ width: w, height: h });
//         };
//         image.onerror = () => {
//         reject(null);
//         };
//         image.src = data;
//     });
// }

// function getZipFile(e: any): void {
//     console.log(e.target.files);
//     const files = e.target.files;
//     // 所有文件
//     handleFile(files[0], getFileSize);
//   }

// function  handleFile(f: File, call: Function) {
//     const dateBefore = new Date();
//     JSZip.loadAsync(f) // 1) read the Blob
//       .then(
//         zip => {
//           const dateAfter = new Date();
//           const t = dateAfter.getTime() - dateBefore.getTime();
//           console.log(`加载时间: ${t}ms`);
//           const list = [];
//           zip
//             .file("native/19/19c65f8d-986e-40ad-bc0a-e9aebf148a6d.png")
//             ?.async("base64")
//             .then(data => {
//               // console.log("ddd",data);
//               const base64Data = "data:image/png;base64," + data;
//               getFileSizeByBase64(base64Data);
//             });
//         },
//         err => {
//           console.log("加载失败");
//         },
//       );
//   }
