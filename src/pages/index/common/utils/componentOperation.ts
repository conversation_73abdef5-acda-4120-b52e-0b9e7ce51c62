
import store from "../../store";

import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
export const specialAddComponent = (subType: string) => {
  switch (subType) {
    case SpecialComponentSubTypes.VOICE: {
      const component: Omit<SpecialVoiceComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.VOICE,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          width: 840,
          height: 240,
          x: 0,
          y: -200,
          answerDuration: 15, //
          evaluatingText: "", //
          // overScore: [60, 90], //
          overTimeNum: 4,
          accuracy: 60, //
          wordType: 1, //1,2,3,4,5,6
          autoBegin: false, //
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.AUDIO_HISTORY_ANSWER: {
      const component: Omit<SpecialAudioHistoryAnswerComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.AUDIO_HISTORY_ANSWER,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          width: 296,
          height: 146,
          x: 0,
          y: 0,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.GRAPHICS: {
      const component: Omit<SpecialGraphicsComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.GRAPHICS,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          width: 640,
          height: 360,
          x: 0,
          y: 0,
          displayType: 1,
          boardType: 1,
          lineColor: "#000000",
          background: "",
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.COUNTER: {
      const component: Omit<SpecialCounterComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.COUNTER,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          width: 146,
          height: 487,
          x: 0,
          y: 0,
          countNum: 1,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.MATCHBOARD: {
      const component: Omit<SpecialMatchBoardComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.MATCHBOARD,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          matchType: 0, // 0 算术 1 图形
          mathType: 1, // 1 二等式 2 三等式
          grid: {
            row: 3,
            col: 6,
          },
          width: 1280,
          height: 720,
          opacity: 255,
          angle: 0,
          x: 0,
          y: 0,
          pattern: [
            [0, 0, 1, 0, 0, 1, 0],
            [0, 1, 0, 1],
            [1, 0, 1, 0, 0, 1, 0],
            [1, 0, 1, 0],
            [1, 1, 1, 1, 1, 1, 1],
          ],
          rightAnswer: [],
          actionType: 0,
          actionNumbers: 1,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.KEYBOARD: {
      const component: Omit<SpecialKeyboardComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.KEYBOARD,
        tag: "",
        "dragable": true,
        "deletable": false,
        "canCombine": false,
        editable: {
          "properties": {
            "x": false,
            "y": false,
            "width": false,
            "height": false,
            "angle": false,
            "rotation": false,
            "opacity": false,
            "scaleX": false,
            "scaleY": false,
            "color": false
          }

        },
        properties: {
          active: true,
          width: 1015,
          height: 80,
          x: -50,
          y: -300,
          keyboardType: 1,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.KEYBOARD_ENGLISH: {
      const component: Omit<SpecialKeyboardEnglishComponent, "id"> = {
        type: "specialComponent",
        subType:SpecialComponentSubTypes.KEYBOARD_ENGLISH,
        tag: "",
        "dragable": true,
        "deletable": false,
        "canCombine": false,
        editable: {
          "properties": {
            "x": false,
            "y": false,
            "width": false,
            "height": false,
            "angle": false,
            "rotation": false,
            "opacity": false,
            "scaleX": false,
            "scaleY": false,
            "color": false
          }
        },
        properties: {
          active: true,
          "width": 1280,
          "height": 326,
          "x": 0,
          "y": -200,
          isPackUp: false,
          keyboardType: 1
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }
    case SpecialComponentSubTypes.CLOCK: {
      const component: Omit<SpecialClockComponent, "id"> = {
        type: "specialComponent",
        subType: SpecialComponentSubTypes.CLOCK,
        tag: "",
        dragable: true,
        properties: {
          active: true,
          width: 146,
          height: 487,
          x: 0,
          y: 0,
          linkage: false,
          hour: 3,
          minute: 0,
          correctHour: 4,
          correctMinute: 30,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      break;
    }

    default: {
      break;
    }
  }

}