import { parse } from "query-string";
// 1 oc-pyramid-创建互动题 1
// temporaryStorageId=10725&plat=ykt
// 2 oc-pyramid-公共题库-编辑 1
// snapId=17522&id=1653&plat=ykt&tiku=1
// 3 oc-pyramid-cocos互动库-编辑
// id=1675&plat=ykt
// 4 oc-h5 1
// hdkt2Shark=1&plat=ykt&parentVersion=1
// 5 platform-创建 1
// ?temporaryStorageId=10728 1
// 6 platform-编辑
// ?id=1677
// 7 oc-编辑
// plat=ykt&id=1599&kjtype=2&uid=2032&parentVersion=&snapId=17431
export const {
  plat,
  temporaryStorageId,
  snapId,
  id,
  cchdId,
  tiku,
  hdkt2Shark,
  h5Switch,
  parentVersion,
  read,
  fromGroup: nativeFromGroup,
} = parse(location.search);
export const fromGroup = nativeFromGroup === '1'
// export const temporaryStorageId =
//   parse(location.search).temporaryStorageId || null;
// export const isAdd = () =>{
//   if (snapId) return true;
//   if (temporaryStorageId) return true;
//   if (hdkt2Shark) return true;
// }