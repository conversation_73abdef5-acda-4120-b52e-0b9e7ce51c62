// export const standardConfig = {
//   likeChoice: {
//     isEnumAnswer: true,
//     isStandardAnswer: true,
//     categoryName: '',
//     questionModule: 0,
//     optionItemConfig: {
//       parentKey: '',
//       key: 'yyyy',
//       typeKey: '',
//       contentKey: ''
//     }, // isEnumAnswer 为false时 该字段为空
//     compSubType: '',
//   },
//   group: {
//     isEnumAnswer: true,
//     isStandardAnswer: true,
//     categoryName: '',
//     questionModule: 3,
//     optionItemConfig: {
//       parentKey: '',
//       key: 'yyyy',
//       typeKey: '',
//       contentKey: ''
//     }, // isEnumAnswer 为false时 该字段为空
//     compSubType: '',
//   },
//   default: {
//     isEnumAnswer: false,
//     isStandardAnswer: false,
//     categoryName: '',
//     questionModule: 0,
//   }
// }