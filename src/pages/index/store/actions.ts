import { ActionTree } from "vuex";
import { cloneDeep, reverse } from "lodash-es";
import { ClipboardTypes } from "./constants";
import { UpdateLevelType } from "./mutations";
import { Message } from "element-ui";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import * as clipboard from "clipboard-polyfill";
import bus from "../common/utils/bus";
import { setCacheData } from "@/common/utils/autoCache";
const prefixComponents = "::__courseware__::__components__::";

const copyComponentDataAndGetSubIds = (id: string, componentMap: State["componentMap"], componentIds: State["componentIds"]): [Component, string[]] => {
  const newComponent = cloneDeep(componentMap[id]);

  // 删除复制组件的动画属性，因为动画属性可能有组件的联动
  delete newComponent.extra.animations;

  let newSubIds: string[] = [];
  if (newComponent.type === "group") {
    const { subIds } = componentIds.find(i => {
      if (typeof i === "object" && i.id === id) {
        return true;
      }
    }) as { subIds: string[] };
    (newComponent as GroupComponent).subComponents = subIds.map(subId => cloneDeep(componentMap[subId]));
    newSubIds = subIds.concat();
  } else {
    const componentId = componentIds.find(i => {
      if (typeof i === "object" && i.id === id) {
        return true;
      }
    }) as { subIds: string[] };
    if (componentId && componentId.subIds) {
      (newComponent as BaseComponent).childComponents = componentId.subIds.map(subId => cloneDeep(componentMap[subId]));
      newSubIds = componentId.subIds.concat();
    }
    console.warn(componentId, "subIds");
  }

  return [newComponent, newSubIds];
};

const actions: ActionTree<State, State> = {
  addComponentAndFocus({ commit, getters }, component: Component) {
    if (component.id === undefined) {
      component.id = getters.newId;
      console.log(component.id, " component.id");
    }
    commit("addComponent", component);
    commit("replaceCurrentComponentIds", [component.id]);
    if (component.type === "label") {
      commit("startEditLabel", [component.id]);
    }

    // if (component.type === 'specialComponent' && component.subType === 'h5Label') {
    //   // 新增富文本组件后 显示弹窗
    //   bus.$emit('h5Label-dialog-toggle', true);
    // }
  },
  editorAddComponentAndFocus({ commit, getters }, component: Component) {
    if (component.id === undefined) {
      component.id = getters.newId;
    }
    if ((component as Component).isFocus) {
      commit("replaceCurrentComponentIds", [component.id]);
    }
    commit("addComponent", component);
  },
  addComponentNoFocus({ commit, getters }, component: Component) {
    if (component.id === undefined) {
      component.id = getters.newId;
    }
    commit("addComponent", component);
    // commit("replaceCurrentComponentIds", []);
  },
  removeComponent({ commit, dispatch, state, getters }, id) {
    // 移除组件身上的动画
    dispatch("moduleAnimations/removeActionByComponentId", id);

    // 移除所有组件上对应联动组件的动画片段
    commit("removeAllComponentActionByComponentId", id);

    // 汇总画笔组件需要刷新的数据
    const burshRemoveIdMap = { [id]: true };
    let brushId = null;
    let isMoveParentId = null;
    for (const cId of state.componentIds) {
      // 找到画笔组件id
      if (typeof cId == "string") {
        if (state.componentMap[cId].subType && state.componentMap[cId].subType == "brush") {
          brushId = cId;
        }
      } else {
        if (cId.id === id) {
          // 组
          burshRemoveIdMap[cId.id] = true;
          for (const nKey of cId.subIds) {
            burshRemoveIdMap[nKey] = true;
          }
        }
      }
    }
    // 移除组件为子组件且只有一个兄弟组件时，拆组
    state.componentIds.some(i => {
      if (typeof i === "object") {
        if (i.subIds.includes(id)) {
          let subIdsLen = 0;
          for (let j = 0; j < i.subIds.length; j++) {
            if (!state.componentMap[i.subIds[j]].cName) {
              subIdsLen += 1;
            }
          }
          if (subIdsLen == 2 && !state.componentMap[id].cName) {
            burshRemoveIdMap[i.id] = true; // 拆组画笔要更新
            isMoveParentId = i.id;

            return true;
          }
        }
      }
    });

    // 查找画笔组件关联&并删除
    if (brushId && brushId != id) {
      // 画笔数据更新
      const updateList = [];
      const moutList = JSON.parse(JSON.stringify(state.componentMap[brushId].properties.mountList));
      for (const key of moutList) {
        if (!burshRemoveIdMap[key]) {
          updateList.push(key);
        }
      }
      this.dispatch("updateComponentsProperties", {
        ids: [brushId],
        newProperties: {
          mountList: updateList,
        },
      });
    }
    if (!isMoveParentId) {
      commit("removeComponent", id);
    } else {
      dispatch("separateGroupComponent", isMoveParentId);
      commit("removeComponent", id);
    }

  },
  // 批量移除组件
  removeComponents({ dispatch }, ids: string[]) {
    ids.forEach(id => {
      dispatch("removeComponent", id);
    });
  },
  updateComponentsProperties({ commit }, payload: { ids: string[]; newProperties: Component["properties"] }) {
    const { ids, newProperties } = payload;
    ids.forEach(id => {
      commit("updateComponentProperties", { id, newProperties });
    });
  },
  updateComponentsExtra({ commit }, payload: { ids: string[]; newExtra: Component["extra"] }) {
    const { ids, newExtra } = payload;
    ids.forEach(id => {
      commit("updateComponentExtra", { id, newExtra });
    });
  },
  updateComponentExtraClear({ commit }, payload: { ids: string[]; newExtra: Component["extra"] }) {
    const { ids, newExtra } = payload;
    ids.forEach(id => {
      commit("updateComponentExtraClear", { id, newExtra });
    });
  },


  addChildComponent({ commit, getters }, payload: { id: string; childComponent: Component }) {
    const { id, childComponent } = payload;
    console.log(id, childComponent);
    if (childComponent.id === undefined) {
      childComponent.id = getters.newId;
      console.log(childComponent.id, "id");
    }
    commit("addComponent", childComponent);
    commit("addChildComponent", { id, childId: childComponent.id });
    // 2: 将finalIds移动到group里
    commit("updateComponentIdsWhenAddChildComponent", {
      id,
      childId: childComponent.id,
    });
  },
  removeChildComponents({ commit, state, getters, dispatch }, payload: { ids: string[] }) {
    payload.ids.forEach(id => {
      // if (state.componentMap[id].type !== "group") {
      state.componentIds.forEach(componentId => {
        if (typeof componentId === "object" && id === componentId.id) {
          componentId.subIds.forEach(subId => {
            if (state.componentMap[id].type == 'group') {
              if (state.componentMap[subId].cName) {
                dispatch("removeComponent", subId);
                commit("removeChildComponent", { id, childId: subId });
              }
            } else {
              dispatch("removeComponent", subId);
              commit("removeChildComponent", { id, childId: subId });
            }

          });
          componentId = componentId.id;
        }
      });
      // }
    });
    commit("replaceCurrentComponentIds", payload.ids);
  },


  addGroupComponentAndBus({ commit, state, dispatch, getters }, data: { components: Component[], groupComponent: Component }) {
    const ids: string[] = [];
    let finalIds: string[] = [];

    data.components.forEach(component => {
      if (component.id === undefined) {
        component.id = getters.newId;
      }
      component.properties.x += data.groupComponent.properties.x;
      component.properties.y += data.groupComponent.properties.y;
      commit("addComponent", component);
      ids.push(component.id);

    });
    const idIndexs: number[] = [];
    ids.forEach(id => dispatch("moduleAnimations/removeActionByComponentId", id));
    data.components.forEach(component => finalIds.push(component.id));
    finalIds.forEach(id =>
      state.componentIds.forEach((cmptId: any, index: number) => {
        if (id === cmptId) {
          idIndexs.push(index);
        }
      }),
    );
    idIndexs.sort((a, b) => a - b);
    finalIds = [];
    idIndexs.forEach(index => {
      finalIds.push(state.componentIds[index] as string);
    });

    setTimeout(() => {
      commit("addComponent", data.groupComponent);
      // 2: 将finalIds移动到group里
      commit("updateComponentIdsWhenCombineComponents", { ids: finalIds, id: data.groupComponent.id });
    }, 100);
  },

  addGroupComponent({ commit, state, dispatch, getters }, components: Component[]) {
    return new Promise(resolve => {
      const ids: string[] = [];
      components.forEach(component => {
        if (component.id === undefined) {
          component.id = getters.newId;
          commit("addComponent", component);
          ids.push(component.id);
        }
      });
      const timer = setTimeout(() => {
        clearTimeout(timer);
        dispatch("combineComponents", ids);
        resolve(true);
      }, 100);
    });
  },

  combineComponents({ commit, state, dispatch, getters }, ids: string[]) {
    console.log("%c Line:267 🍢 ids  combineComponents", "color:#465975", ids);
    let finalIds: string[] = [];
    let hasChildComponent = false;
    // 0: group组件先取消组合，将subIds添加进finalIds。非group组件直接添加到finalIds
    ids.forEach(id => {
      if (state.componentMap[id].type === "group") {
        state.componentIds.some(i => {
          if (typeof i === "object" && i.id === id) {
            for (let j = 0; j < i.subIds.length; j++) {
              if (!state.componentMap[i.subIds[j]].cName) {
                finalIds.push(i.subIds[j]);
              }
            }
            return true;
          }
        });
        dispatch("separateGroupComponent", id);
        return;
      } else {
        if (state.componentMap[id].childComponents && state.componentMap[id].childComponents.length > 0) {
          hasChildComponent = true;
          return;
        }
        state.componentIds.some(i => {
          if (typeof i === "object" && i.id === id) {
            if (i.subIds.length > 0) {
              hasChildComponent = true;
              return;
            }
          }
        });
        finalIds.unshift(id);
      }

    });
    if (hasChildComponent) {
      console.log("该组件不支持组合");
      Message.error("该组件不支持组合");
      return;
    }
    // 1: 创建group
    const id = getters.newId;
    const groupComponent: InnerGroupComponent = {
      id,
      tag: "",
      type: "group",
      editable: true,
      deletable: true,
      properties: {
        active: true,
        x: 0,
        y: 0,
        width: 500,
        height: 500,
      },
    };
    const idIndexs: number[] = [];
    ids.forEach(id => dispatch("moduleAnimations/removeActionByComponentId", id));
    console.log("%c Line:325 🍖 finalIds", "color:#ea7e5c", finalIds);
    finalIds.forEach(id =>
      state.componentIds.forEach((cmptId: any, index: number) => {
        if (id === cmptId) {
          idIndexs.push(index);
        }
      }),
    );
    console.log("%c Line:333 🥖 idIndexs", "color:#4fff4B", idIndexs);
    idIndexs.sort((a, b) => a - b);

    finalIds = [];
    console.log("%c Line:333 🥖 idIndexs", "color:#4fff4B", idIndexs);
    idIndexs.forEach(index => {
      finalIds.push(state.componentIds[index] as string);
    });
    commit("addComponent", groupComponent);
    console.log("%c Line:343 🍅 finalIds", "color:#2eafb0", finalIds);
    // 2: 将finalIds移动到group里
    commit("updateComponentIdsWhenCombineComponents", { ids: finalIds, id });
    // 3: 焦点切换到group
    commit("replaceCurrentComponentIds", [id]);
  },
  separateGroupComponent({ commit, state, dispatch }, id: string) {
    const childList: string[] = [];
    if (state.componentMap[id].type === "group") {
      state.componentIds.some(i => {
        if (typeof i === "object" && i.id === id) {
          for (let j = 0; j < i.subIds.length; j++) {
            if (state.componentMap[i.subIds[j]].cName) {
              childList.push(i.subIds[j]);
            }
          }
        }
      });
    }
    commit("replaceCurrentComponentIds", []);
    commit("updateComponentIdsWhenSeparateGroupComponent", id);
    if (childList.length > 0) {
      for (let i = 0; i < childList.length; i++) {
        dispatch("removeComponent", childList[i]);
      }
    }
    dispatch("removeComponent", id);
  },
  cutComponent({ commit, state, dispatch }, id: string) {
    const [newComponent, subIds] = copyComponentDataAndGetSubIds(id, state.componentMap, state.componentIds);
    subIds.forEach(subId => {
      dispatch("removeComponent", subId);
    });

    dispatch("removeComponent", id);
    commit("replaceClipboard", {
      type: ClipboardTypes.CUT,
      components: [newComponent],
    });
  },
  cutComponents({ commit, state, dispatch }, ids: string[]) {
    const newComponents: Component[] = [];

    ids.forEach(id => {
      const [newComponent, subIds] = copyComponentDataAndGetSubIds(id, state.componentMap, state.componentIds);
      subIds.forEach(subId => {
        dispatch("removeComponent", subId);
      });

      // removeComponent中有拆组的逻辑
      if (newComponent.type !== "group") {
        dispatch("removeComponent", id);
      }

      newComponents.push(newComponent);
    });
    commit("replaceClipboard", {
      type: ClipboardTypes.CUT,
      components: newComponents,
    });
  },
  copyComponents({ commit, state }, ids: string[]) {
    const newComponents: Component[] = [];

    ids.forEach(id => {
      const [newComponent] = copyComponentDataAndGetSubIds(id, state.componentMap, state.componentIds);
      newComponents.push(newComponent);
    });

    commit("replaceClipboard", {
      type: ClipboardTypes.COPY,
      components: newComponents,
    });
  },
  copyComponent({ commit, state }, id: string) {
    const newComponents = cloneDeep(state.componentMap[id]);
    newComponents.forEach((component: any, index: number) => {
      if (component.type === "group") {
        state.componentIds.forEach((id: any) => {
          if (typeof id === "object" && id.id === component.id) {
            const subComponents: any[] = [];
            id.subIds.forEach((subId: any) => {
              const subComponent = cloneDeep(state.componentMap[subId]);
              subComponent.properties.x += component.properties.x;
              subComponent.properties.y += component.properties.y;
              delete subComponent.id;
              subComponent.tag = "";
              subComponent.extra = {};
              subComponents.push(subComponent);
            });
            const newGroupCompoent = cloneDeep(component);
            newGroupCompoent.subComponents = subComponents;
            newComponents.splice(index, 1, newGroupCompoent);
          }
        });
      }
      delete component.id;
      component.tag = "";
      component.extra = {};
    });
    clipboard.writeText(prefixComponents + JSON.stringify(newComponents));
    // commit("replaceClipboard", {
    //   type: ClipboardTypes.COPY,
    //   components: [
    //     copyComponentDataAndGetSubIds(
    //       id,
    //       state.componentMap,
    //       state.componentIds,
    //     )[0],
    //   ],
    // });
  },

  pasteComponent({ commit, state, getters, dispatch }) {
    const { clipboard } = state;
    const { components } = clipboard;

    components.forEach(component => {
      if (!component) {
        return;
      }
      if (component.type === "group") {
        // 0 添加子组件
        const newIds: string[] = [];
        (component as GroupComponent).subComponents.forEach(subComp => {
          const newComp = cloneDeep(subComp);
          newComp.id = getters.newId;
          newIds.push(newComp.id);
          commit("addComponent", newComp);
        });
        // 1 组合
        dispatch("combineComponents", newIds);
      } else {
        if (component.subType) {
          switch (component.subType) {
            case SpecialComponentSubTypes.AUDIO_HISTORY_ANSWER:
            case SpecialComponentSubTypes.COUNTER:
            case SpecialComponentSubTypes.GRAPHICS:
            case SpecialComponentSubTypes.KEYBOARD:
            case SpecialComponentSubTypes.KEYBOARD_ENGLISH:
            case SpecialComponentSubTypes.MATCHBOARD:
            case SpecialComponentSubTypes.VOICE:
            case SpecialComponentSubTypes.CLOCK:
              Message.error("该元素不可粘贴");
              return;
              break;
            default:
              break;
          }
        }
        const newComponent = cloneDeep(component);
        newComponent.properties.x += 20;
        newComponent.properties.y -= 20;
        newComponent.id = getters.newId;
        commit("addComponent", newComponent);
        if ((component as BaseComponent).childComponents) {
          const newIds: string[] = [];
          //@ts-ignore
          (component as BaseComponent).childComponents.forEach(subComp => {
            const newComp = cloneDeep(subComp);
            newComp.id = getters.newId;
            newIds.push(newComp.id);
            dispatch("addChildComponent", {
              id: newComponent.id,
              childComponent: newComp,
            });
          });
        }
      }
    });

    switch (clipboard.type) {
      case ClipboardTypes.CUT:
        commit("replaceClipboard", {
          type: ClipboardTypes.NONE,
          component: undefined,
        });
        break;
      case ClipboardTypes.COPY:
        break;
      default:
        break;
    }
  },

  updateComponentOrders({ commit, state }, payload: { ids: string; oldIndex: number; newIndex: number }[]) {
    // const { componentIds } = state;
    // const { ids, oldIndex, newIndex } = payload;
    // if (!ids || ids.length === 0) {
      // return; // 没有需要移动的项
    // }
    // 这里先处理ids的顺序，再处理componentIds的顺序；
    // commit("updateComponentOrder", {ids, oldIndex, newIndex });
  },

  // 批量更新组件层级，暂时不考虑组合组件的情况
  updateComponentsLevel({ commit, state }, payload: { ids: string[]; type: UpdateLevelType }) {
    const { componentIds } = state;
    const { ids, type } = payload;

    if (!ids || ids.length === 0) {
      return; // 没有需要移动的项
    }

    let targetArray: (string | { id: string; subIds: string[] })[] | null = null;
    let parentGroupId: string | null = null;

    // 1. 查找目标数组上下文 (基于第一个选中的 ID)
    const firstId = ids[0];
    for (const component of componentIds) {
      if (typeof component === "object" && component.subIds.includes(firstId)) {
        targetArray = component.subIds; // 目标是组内 subIds
        parentGroupId = component.id;    // 记录父组 ID
        break;
      }
    }
    // 如果在任何组内都未找到，则假定目标是根数组
    if (targetArray === null) {
      // 验证 firstId 确实存在于根数组中
      if (componentIds.some(comp => (typeof comp === 'object' ? comp.id : comp) === firstId)) {
        targetArray = componentIds; // 目标是根 componentIds
      }
    }

    if (!targetArray) {
      console.error(`ID 为 ${firstId} 的组件在任何上下文中都未找到。`);
      return; // ID 在任何地方都找不到
    }

    // 2. 获取选中 ID 在目标数组内的索引
    const idWithIndex = ids
      .map(id => {
        // 在已确定的上下文中查找索引
        const index = targetArray!.findIndex(comp => (typeof comp === 'object' ? comp.id : comp) === id);
        return { id, index };
      })
      .filter(item => item.index !== -1); // 过滤掉在当前上下文中未找到的 ID

    if (idWithIndex.length === 0) {
      return; // 所有选中的 ID 在当前上下文中都未找到
    }

    // 3. 按索引排序
    const sortedIdWithIndex = idWithIndex.sort((a, b) => a.index - b.index);
    const sortedIds = sortedIdWithIndex.map(item => item.id);

    // 4. 边界检查并确定最终要迭代的 ID 列表
    const minIndex = sortedIdWithIndex[0].index;
    const maxIndex = sortedIdWithIndex[sortedIdWithIndex.length - 1].index;
    const targetLength = targetArray.length;

    let idsToCommit: string[] = [];

    switch (type) {
      case UpdateLevelType.TOP: {
        // 从最小索引开始向上移动 (保持原始顺序)
        idsToCommit = sortedIds;
        break;
      }
      case UpdateLevelType.BOTTOM: {
        // 从最大索引开始向下移动 (反转原始顺序)
        idsToCommit = [...sortedIds].reverse();
        break;
      }
      case UpdateLevelType.FORWARD: {
        // 向上移动一层 (反转原始顺序), 仅当不在最顶部时
        if (maxIndex < targetLength - 1) {
          idsToCommit = [...sortedIds].reverse();
        }
        break;
      }
      case UpdateLevelType.BACKWARD: {
        // 向下移动一层 (保持原始顺序), 仅当不在最底部时
        if (minIndex > 0) {
          idsToCommit = sortedIds;
        }
        break;
      }
      default: {
        break;
      }
    }

    // 5. 提交 Mutations
    // 'updateComponentLevel' mutation 需要能隐式或显式地正确处理上下文
    // (根数组 vs. 子ID数组)。
    // 此处假设 mutation 能基于 ID 查找隐式处理。
    idsToCommit.forEach(id => {
      commit("updateComponentLevel", { id, type });
    });
  },
  // 批量操作组件 tag
  updateComponentsTag({ commit }, payload: { ids: string[]; tag: Tag }) {
    const { ids, tag } = payload;
    ids.forEach(id => {
      commit("updateComponentTag", { id, tag });
    });
  },
  // 关闭组件动画编辑
  closeComponentAnimationEditor({ commit }) {
    commit("setIsEditingComponentAnimations", false);
    commit("setEditingAnimationsComponentId", "");
    commit("setComponentAnimationVal", "");
    commit("setComponentFragmentId", "");
    commit("setComponentActiveActionId", "");
  },
  cacheState({ state }): Promise<void> {
    // 当内容变化小于3时，不缓存。解决每次点击返回，即使没做任何更改也会缓存的问题
    if ((state.timeTravel || { total: 0 }).total < 3) {
      return Promise.resolve();
    }
    return new Promise((resolve) => {
      bus.$emit('getContent', (data: any) => {
        const data1: any = cloneDeep(state);
        data1.content = JSON.stringify(data);
        data1.category = state.template.category;
        data1.tempId = state.templateId
        if (data1.tagsData) {
          data1.tagsData = JSON.parse(data1.tagsData)
        }
        setCacheData(JSON.stringify(data1))
        resolve()
      })
    })
  }
};

const cocosActions = (() => {
  const cocosActions = {} as ActionTree<State, State>;
  for (const key in actions) {
    if (Object.prototype.hasOwnProperty.call(actions, key)) {
      cocosActions["cocos/" + key] = function (...args) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (actions[key] as any)(...args);
      };
    }
  }
  return cocosActions;
})();

export default { ...cocosActions, ...actions };
