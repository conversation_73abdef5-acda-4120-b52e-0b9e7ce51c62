import Vue from "vue";
import Vuex from "vuex";
import TimeTravel from "./timeTravel";
import mutations from "./mutations";
import actions from "./actions";
import getters from "./getters";
import { ClipboardTypes, Mode } from "./constants";
import moduleComponent<PERSON>anagerCard, {
  moduleName as moduleComponentManagerCardName,
} from "./modules/componentManagerCard";
import moduleAnimations, {
  moduleName as moduleAnimationsName,
} from "./modules/animations";
import moduleTimeTravel, {
  moduleName as moduleTimeTravelName,
} from "./modules/timeTravel";
import moduleRulerTick, {
  moduleName as moduleRulerTickName,
} from "./modules/rulerTick";
import { initParentVersion } from '@/pages/index/common/utils/initParentVersion';
import storage from "@/common/utils/storage";

Vue.use(Vuex);

const state: State = {
  /**
   * @description 用户信息
   */
  userInfo: storage.get("userInfo") || {},
  // 当前业务线
  client: undefined,
  mode: Mode.NORMAL,
  initialDataLoaded: false,
  cocosInitFinished: false,
  cocosLoadQuestionBundleFinished: false,
  cocosLoadGameSceneFinished: false,
  cocosLoadStageRootNodeFinished: false,
  cocosLoadCompListStart: false,
  cocosLoadCompListFinished: false,
  name: "",
  templateId: 0,
  template: {
    id: 0,
    bundleUrl: "",
    bundleName: "",
    category: 0,
    tempType: 0,
    stage: {
      width: 1334,
      height: 750,
      safeWidth: 1334,
      safeHeight: 1334,
      backgroundColor: "#fff",
      texture: "",
      textureType: 0,
    },
    features: {},
    referenceAnswer: {}
  },
  extData: {
    formConfig: {}
  },
  stageData: {
    width: 1334,
    height: 750,
    safeWidth: 1334,
    safeHeight: 1334,
    backgroundColor: "#fff",
  },
  extraStageData: {},
  componentMap: {},
  componentIds: [],
  currentComponentIds: [],
  animations: {},
  contextMenu: {
    top: 400,
    left: 200,
    visible: false,
    componentId: undefined,
  },
  clipboard: {
    type: ClipboardTypes.NONE,
    components: [],
  },

  /**
   * @description 组件动画编辑相关数据
   */
  isEditingComponentAnimations: false,
  editingAnimationsComponentId: "",
  componentAnimationConfig: [],
  componentAnimationVal: "",
  componentFragmentId: "",
  componentActiveActionId: "",
  // 标签数据
  tagsData: "",
  gradeId: undefined,
  subjectId: undefined,
  status: undefined,
  editFocus: false,
  parentVersion: initParentVersion(),
  containerWidth: 0,
  containerHeight: 0,
};
const store = new Vuex.Store({
  state: JSON.parse(JSON.stringify(state)),
  getters,
  mutations,
  actions,
  modules: {
    [moduleComponentManagerCardName]: moduleComponentManagerCard,
    [moduleAnimationsName]: moduleAnimations,
    [moduleTimeTravelName]: moduleTimeTravel,
    [moduleRulerTickName]: moduleRulerTick,
  },
  strict: process.env.NODE_ENV !== "production",
});

export { UpdateLevelType } from "./mutations";
export const timeTravel = new TimeTravel(store);
export default store;
export const resetStore = () => {
  const newsState = JSON.parse(JSON.stringify(state));
  let cardVisable = true;
  if ((window as any)._$store && (window as any)._$store.state && (window as any)._$store.state.componentManagerCard) {
    cardVisable = (window as any)._$store.state.componentManagerCard.visible;
  }

  newsState.componentManagerCard = {
    "visible": cardVisable,
    "activeName": "LIBRARY"
  }

  newsState.moduleAnimations = {
    "animationVal": "afterReadingQuestion",
    "fragmentId": "",
    "activeActionId": "",
    "animations": {
      "afterReadingQuestion": {
        "audio": {
          "url": "",
          "delay": 0
        },
        "fragments": {

        },
        "points": {

        }
      },
      "afterSubmitCorrect": {
        "audio": {
          "url": "",
          "delay": 0
        },
        "fragments": {

        },
        "points": {

        }
      },
      "afterSubmitWrong": {
        "audio": {
          "url": "",
          "delay": 0
        },
        "fragments": {

        },
        "points": {

        }
      }
    },
    "isPlayingAnimation": false,
    "isPlayingFragment": false
  },
    newsState.timeTravel = {
      "currentIndex": 0,
      "total": 0
    };
  newsState.rulerTick = {
    "verticalLines": [

    ],
    "horizontalLines": [

    ]
  }
  timeTravel.resetState(newsState);
}
