import { Module } from "vuex";
import { ActiveNames, CompState } from "./constants";
import { mutation } from "@/common/utils/vuexHelpers";
import keyMirror from "@/common/utils/keyMirror";

interface ComponentManagerCardState {
  visible: boolean;
  activeName: ActiveNames;
  currCompState: CompState;
}

const state = {
  visible: true,
  activeName: ActiveNames.LIBRARY,
  currCompState: CompState.DEFAULT
};

const mutations = mutation(state, {
  setVisible(state, visible) {
    state.visible = visible;
  },
  setActiveNames(state, activeName: ActiveNames) {
    state.activeName = activeName;
  },
  setCurrCompState(state, compState: CompState) {
    state.currCompState = compState;
    console.log('currCompState.组件状态', compState);
  }
});

export const module: Module<ComponentManagerCardState, State> = {
  namespaced: true,
  state,
  mutations
};

export const moduleName = "componentManagerCard";

export const prefix = moduleName + "/";

export const types = {
  mutations: keyMirror(mutations, prefix)
};

export default module;
