import { Module } from "vuex";
import keyMirror from "@/common/utils/keyMirror";
import { mutation } from "@/common/utils/vuexHelpers";

interface RulerTickState {
  verticalLines: number[];
  horizontalLines: number[];
}

const state: RulerTickState = {
  verticalLines: [],
  horizontalLines: []
};

const mutations = mutation(state, {
  addVerticalLine(state, payload) {
    state.verticalLines.push(payload);
  },
  removeVerticalLine(state, index) {
    state.verticalLines.splice(index, 1);
  },
  addHorizontalLine(state, payload) {
    state.horizontalLines.push(payload);
  },
  removeHorizontalLine(state, index) {
    state.horizontalLines.splice(index, 1);
  },
  updateVerticalLine(state, paylod) {
    const { index, value } = paylod;
    state.verticalLines.splice(index, 1, value);
  },
  updateHorizontalLine(state, paylod) {
    const { index, value } = paylod;
    state.horizontalLines.splice(index, 1, value);
  }
});

export const module: Module<RulerTickState, State> = {
  namespaced: true,
  state,
  mutations
};

export const moduleName = "rulerTick";
export const prefix = moduleName + "/";

export const types = {
  mutations: keyMirror(mutations, prefix)
};

export default module;
