import { Module } from "vuex";
import keyMirror from "@/common/utils/keyMirror";
import { mutation } from "@/common/utils/vuexHelpers";
interface TimeTravelState {
  currentIndex: number;
  total: number;
}

const state = {
  currentIndex: 0,
  total: 0
};

const mutations = mutation(state, {
  updateCurrentIndex(state, payload) {
    state.currentIndex = payload;
  },
  updateTotal(state, payload) {
    state.total = payload;
  },
  redo() {
    // 勿删，cocos中使用
  },
  undo() {
    // 勿删，cocos中使用
  },
  push() {
    // 勿删，cocos中使用
  },
  splice() {
    // 勿删，cocos中使用
  },
  resetCommandFactory() {
    // 释放专用
  }
});

export const module: Module<TimeTravelState, State> = {
  namespaced: true,
  state,
  mutations,
  getters: {
    canRedo(state) {
      return state.currentIndex < state.total - 1;
    },
    canUndo(state) {
      return state.currentIndex > 0;
    }
  }
};

export const moduleName = "timeTravel";
export const prefix = moduleName + "/";

export const types = {
  mutations: keyMirror(mutations, prefix)
};
export default module;
