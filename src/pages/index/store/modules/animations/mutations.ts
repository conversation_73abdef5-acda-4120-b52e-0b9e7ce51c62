import vue from "vue";
import { MutationTree } from "vuex";
import { mutation } from "@/common/utils/vuexHelpers";
import { state } from "./state";
import {
  Animations,
  AnimationAction,
  Moment,
  AnimationsState,
} from "./constants";

/**
 * 通过action.id查找action
 *
 * @param {Animations} animations animations
 * @param {string} actionId action id
 */
const findActionById = (animations: Animations, actionId: string) => {
  const animationKeys = Object.keys(animations);
  for (const animationKey of animationKeys) {
    const fragments = animations[animationKey].fragments;
    const fragmentKeys = Object.keys(fragments);
    for (const fragmentKey of fragmentKeys) {
      const fragment = fragments[fragmentKey];
      const result = fragment.find(action => actionId === action.id);
      if (result) {
        return result;
      }
    }
  }
};

export const mutations = mutation(state, {
  initState(
    state,
    payload: { config: AnimationConfig; initialData: Animations },
  ) {
    const { config, initialData } = payload;
    // 创建animations
    const emptyAnimations: Animations = {};
    config.forEach(i => {
      emptyAnimations[i.value] = {
        audio: { url: "", delay: 0 },
        fragments: {},
        points: {},
      };
    });
    // 赋初始值
    vue.set(state, "animations", Object.assign(emptyAnimations, initialData));
    // 默认选中第一个动画
    const firstAnimationConfig = config[0];
    if (firstAnimationConfig) {
      state.animationVal = firstAnimationConfig.value;
    }
  },
  setAnimationAudioUrl(state, url) {
    const { animationVal, animations } = state;
    animations[animationVal].audio.url = url;
  },
  setAnimationAudioDelay(state, delay) {
    const { animationVal, animations } = state;
    vue.set(animations[animationVal].audio, "delay", delay);
  },
  addFragment(state, payload: string) {
    const { animationVal, animations } = state;
    const fragments = animations[animationVal].fragments;
    vue.set(fragments, payload, []);
    state.activeActionId = "";
    state.fragmentId = payload;
  },
  removeFragment(state, payload: string) {
    const { animationVal, animations } = state;
    const fragments = animations[animationVal].fragments;
    state.fragmentId = "";
    vue.delete(fragments, payload);
  },
  addPoint(
    state,
    {
      id,
      fragmentId,
      startTime,
    }: { id: string; fragmentId: string; startTime: number },
  ) {
    const { animationVal, animations } = state;
    const points = animations[animationVal].points;
    vue.set(points, id, { fragmentId, startTime });
  },
  updatePointStartTime(
    state,
    { id, startTime }: { id: string; startTime: number },
  ) {
    const { animationVal, animations } = state;
    const points = animations[animationVal].points;
    vue.set(points[id], "startTime", startTime);
  },
  removePoint(state, pointId) {
    const { animationVal, animations } = state;
    const points = animations[animationVal].points;
    vue.delete(points, pointId);
  },
  replaceActionList(
    state,
    payload: { animationVal: string; fragmentId: string; actionList: [] },
  ) {
    const { animationVal, fragmentId, actionList } = payload;
    vue.set(state.animations[animationVal].fragments, fragmentId, actionList);
  },
  addAction(
    state,
    payload: {
      animationVal: string;
      fragmentId: string;
      action: AnimationAction;
    },
  ) {
    const { animationVal, fragmentId, action } = payload;
    state.animations[animationVal].fragments[fragmentId].push(action);
  },
  removeAction(
    state,
    payload: {
      animationVal: string;
      fragmentId: string;
      index: number;
    },
  ) {
    const { animationVal, fragmentId, index } = payload;
    state.animations[animationVal].fragments[fragmentId].splice(index, 1);
  },
  updateActionConfigData(
    state,
    payload: {
      id?: string;
      type: "tween" | "animation" | "spine" | "cocosAni";
      data: any;
      name: string;
    },
  ) {
    const { data, name, type, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    action.value.name = name;
    vue.set(action.value, "anim", data);
    action.value.animType = type;
  },
  setAnimationVal(state, payload: string) {
    state.animationVal = payload;
  },
  setFragmentId(state, payload: string) {
    state.activeActionId = "";
    state.fragmentId = payload;
  },
  setActiveActionId(state, payload: string) {
    state.activeActionId = payload;
  },
  setActionMoment(
    state,
    payload: {
      id?: string;
      moment: Moment;
    },
  ) {
    const { moment, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    action.moment = moment;
  },
  setActionDelay(
    state,
    payload: {
      id?: string;
      delay: number;
    },
  ) {
    const { delay, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action, "delay", delay);
  },
  setActionSpeed(
    state,
    payload: {
      id?: string;
      speed: number;
    },
  ) {
    const { speed, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    action.value.speed = speed;
  },
  setActionAnimOption(
    state,
    payload: {
      id?: string;
      option: any;
    },
  ) {
    const { option, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.value.anim, "option", option);
  },
  setActionRepeat(
    state,
    payload: {
      id?: string;
      repeat: number;
    },
  ) {
    const { repeat, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.value, "repeat", repeat);
  },
  setActionAfterType(
    state,
    payload: {
      id?: string;
      afterType: number;
    },
  ) {
    const { afterType, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    action.after.type = afterType;
  },
  setActionAudioMoment(
    state,
    payload: {
      id?: string;
      moment: Moment;
    },
  ) {
    const { moment, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    action.audio.moment = moment;
  },
  setActionAudioDelay(
    state,
    payload: {
      id?: string;
      delay: number;
    },
  ) {
    const { delay, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.audio, "delay", delay);
  },
  setActionEasing(
    state,
    payload: {
      id?: string;
      easing: string;
    },
  ) {
    const { easing, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.value, "easing", easing);
  },
  setActionAudioUrl(
    state,
    payload: {
      id?: string;
      url: string;
    },
  ) {
    const { url, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.audio, "url", url);
  },
  setActionAnimRelative(
    state,
    payload: {
      id?: string;
      relative: boolean;
    },
  ) {
    const { relative, id = state.activeActionId } = payload;
    const action = findActionById(state.animations, id);
    if (!action) {
      return;
    }
    vue.set(action.value.anim, "relative", relative);
  },

  /**
   * @description spine 动画相关
   */
  setSpineAnimName(state, payload: string) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.value.anim.animName = payload;
  },
  setSpineTimeScale(state, timeScale: number) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.value.anim.timeScale = timeScale;
  },
  setSpineAfterLoop(state, loop: boolean) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.after.loop = loop;
  },
  setSpineAfterEndTimeScale(state, endTimeScale: number) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.after.endTimeScale = endTimeScale;
  },
  setSpineAfterAnimList(state, animList: string[]) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    vue.set(action.after, "animList", animList);
  },

  /**
   * @description cocos 动画相关 TODOMM
   */
  setCocosAniName(state, payload: string) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.value.anim.animName = payload;
  },
  setCocosAniTimeScale(state, timeScale: number) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.value.anim.timeScale = timeScale;
  },
  setCocosAniAfterLoop(state, loop: boolean) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.after.loop = loop;
  },
  setCocosAniAfterEndTimeScale(state, endTimeScale: number) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    action.after.endTimeScale = endTimeScale;
  },
  setCocosAniAfterAnimList(state, animList: string[]) {
    const { activeActionId } = state;
    const action = findActionById(state.animations, activeActionId);

    if (!action) return;

    vue.set(action.after, "animList", animList);
  },

  playAnimation(state) {
    // 勿删，cocos使用
    state.isPlayingAnimation = true;
  },
  playFragment(state) {
    // 勿删，cocos使用
    state.isPlayingFragment = true;
  },
  stopAnimation(state) {
    // 勿删，cocos使用
    state.isPlayingAnimation = false;
  },
  stopFragment(state) {
    // 勿删，cocos使用
    state.isPlayingFragment = false;
  },
  addMovePath() {
    // 勿删，cocos使用
  },
  removeMovePath() {
    // 勿删，cocos使用
  },
});

const cocosMutations = (() => {
  const cocosMutations: MutationTree<AnimationsState> = {};
  for (const key in mutations) {
    if (Object.prototype.hasOwnProperty.call(mutations, key)) {
      cocosMutations["cocos/" + key] = function(...args) {
        mutations[key as keyof typeof mutations](...args);
      };
    }
  }
  return cocosMutations;
})();

export default { ...mutations, ...cocosMutations };
