import { AnimType } from "@/pages/index/components/AnimationEditor/AddAction/options";

export enum Moment {
  BEFORE = 0,
  AFTER = 1,
}

export const momentOptions = [
  {
    label: "与上一动画同时",
    value: Moment.BEFORE,
  },
  {
    label: "上一动画结束后",
    value: Moment.AFTER,
  },
];

export enum AfterType {
  NONE = 0,
  HIDE = 1,
}

export enum Direction {
  TOP_TO_BOTTOM = 0,
  RIGHT_TOP_TO_LEFT_BOTTOM = 1,
  RIGHT_TO_LEFT = 2,
  RIGHT_BOTTOM_TO_LEFT_TOP = 3,
  BOTTOM_TO_TOP = 4,
  LEFT_BOTTOM_TO_RIGHT_TOP = 5,
  LEFT_TO_RIGHT = 6,
  LEFT_TOP_TO_RIGHT_BOTTOM = 7,
}

export enum ShakeDirections {
  VERTICAL = 0,
  HORIZONTAL = 1,
}

export enum Sizes {
  VERY_SMALL = 0.25,
  SMALL = 0.5,
  LITTLE_SMALL = 0.75,
  NORMAL = 1,
  LITTLE_BIG = 1.25,
  BIG = 1.5,
  VERY_BIG = 1.75,
}

export interface AnimationAction {
  id: string;
  componentId: string;
  moment: Moment; // 在上一个动作 开始前/结束后 播放
  delay?: number;
  value: {
    name: string;
    animType: "tween" | "animation" | "spine" | "cocosAni";
    direction?: Direction;
    speed: number;
    repeat: number;
    easing?: string;
    anim: {
      name: string;
      type: AnimType;
      relative: boolean;
      data: any;
      option?: any;
      animName?: string; // 需要播放的骨骼动画名称
      timeScale?: number; // 播放速率
    };
  };
  audio: {
    url: string;
    delay?: number;
    moment: Moment; // 在动画 开始播放前/播放后 播放
  };
  after: {
    type: AfterType;
    delay?: number;
    loop?: boolean;
    animList?: string[];
    endTimeScale?: number;
    value?: {
      animType: "tween" | "animation";
      option?: any;
      // 播放时间 单位秒
      speed: number;
      // 循环次数
      repeat: number;
      // 数据
      anim: any; // todo
    };
  };
}

export interface SpineAnimationAction {
  id: string; // actionId
  componentId: string; // 作用于组件的 id
  moment: Moment; // 在上一个动作 开始前/结束后 播放
  delay: number; // 延迟
  // 高级设置——音频
  audio: {
    url: string; // 音频 url
    delay: number; // 音频延迟时长
    moment: Moment; // 在动画 开始播放前/播放后 播放
  };
  value: {
    name: string;
    animType: "spine";
    anim: {
      type: number;
      animName: string; // 需要播放的骨骼动画名称
      timeScale: number; // 播放速率
    };
  };
  // 结束后的属性
  after: {
    loop: boolean;
    animList: string[];
    endTimeScale: number;
    type: AfterType;
  };
}

export interface CocosAnimationAction {
  id: string; // actionId
  componentId: string; // 作用于组件的 id
  moment: Moment; // 在上一个动作 开始前/结束后 播放
  delay: number; // 延迟
  // 高级设置——音频
  audio: {
    url: string; // 音频 url
    delay: number; // 音频延迟时长
    moment: Moment; // 在动画 开始播放前/播放后 播放
  };
  value: {
    name: string;
    animType: "cocosAni";
    anim: {
      type: number;
      animName: string; // 需要播放的cocos动画名称
      timeScale: number; // 播放速率
    };
  };
  // 结束后的属性
  after: {
    loop: boolean;
    animList: string[];
    endTimeScale: number;
    type: AfterType;
  };
}

export type AnimationActionList = AnimationAction[];

export interface AnimationsState {
  animationVal: string;
  fragmentId: string;
  activeActionId: string;
  animations: Animations;
  isPlayingAnimation: boolean;
  isPlayingFragment: boolean;
}

export interface Animations {
  [animationVal: string]: {
    audio: {
      url: string;
      delay?: number; // 延迟播放，单位秒
    };
    // 动画片段
    fragments: {
      [uuid: string]: AnimationActionList;
    };
    // 片段播放时间点
    points: {
      [uuid: string]: {
        startTime: number; // 开始播放时间，单位秒
        fragmentId: string;
      };
    };
  };
}
