import { Module } from "vuex";
import keyMirror from "@/common/utils/keyMirror";
import mutations from "./mutations";
import { state } from "./state";
import { AnimationAction, AnimationsState } from "./constants";

export * from "./constants";

export const moduleName = "moduleAnimations";
export const prefix = moduleName + "/";

export const types = {
  mutations: keyMirror(mutations, prefix)
};

export const module: Module<AnimationsState, State> = {
  namespaced: true,
  state,
  getters: {
    activeAction(state) {
      const { animationVal, fragmentId, activeActionId } = state;
      if (!animationVal || !fragmentId || !activeActionId) {
        return;
      }
      const actions = state.animations[animationVal].fragments[fragmentId];
      return actions.find(i => i.id === activeActionId);
    },
    flatActions(state) {
      const { animations } = state;
      const flatActions: (AnimationAction & { fragmentId: string })[] = [];
      const animationKeys = Object.keys(animations);
      animationKeys.forEach(animationKey => {
        const fragments = animations[animationKey].fragments;
        const fragmentKeys = Object.keys(fragments);
        fragmentKeys.forEach(fragmentKey => {
          const fragment = fragments[fragmentKey];
          fragment.forEach(action => {
            flatActions.push({ ...action, fragmentId: fragmentKey });
          });
        });
      });
      return flatActions;
    },
    currentComponentsActions(state, getters, rootState) {
      const { currentComponentIds } = rootState;
      return getters.flatActions.filter((action: AnimationAction) =>
        currentComponentIds.includes(action.componentId)
      );
    }
  },
  mutations,
  actions: {
    removeActionByComponentId({ state, commit }, id) {
      const { animations } = state;
      const animationKeys = Object.keys(animations);
      animationKeys.forEach(animationKey => {
        const fragments = animations[animationKey].fragments;
        const fragmentKeys = Object.keys(fragments);
        fragmentKeys.forEach(fragmentKey => {
          const fragment = fragments[fragmentKey];
          let index = fragment.length - 1;
          while (index >= 0) {
            const action = fragment[index];
            if (action.componentId === id) {
              commit("removeAction", {
                animationVal: animationKey,
                fragmentId: fragmentKey,
                index
              });
            }
            index -= 1;
          }
        });
      });
    }
  }
};

export default module;
