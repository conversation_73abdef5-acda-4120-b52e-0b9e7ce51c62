import { GetterTree } from "vuex";
import { Mode } from "./constants";
import { AnimationAction } from "./modules/animations";
let addNewId = 0;
export const getters: GetterTree<State, State> = {
  // 生成新的组件id
  newId: state => {
    const currentIds = state.componentIds.reduce((prev: string[], curr) => {
      if (typeof curr === "object") {
        return [...prev, curr.id, ...curr.subIds];
      }
      return [...prev, curr];
    }, []);
    addNewId++;
    if (!currentIds.length) {
      return String(addNewId);
    }
    const nextId = Math.max(...currentIds.map(id => Number(id))) + 1;
    if (addNewId < nextId) {
      addNewId = nextId;
    }
    return String(addNewId);
  },
  currentComponents: state => {
    const { currentComponentIds, componentMap } = state;
    const currentComponents: Components = currentComponentIds.map((id: string) => componentMap[id]);
    return currentComponents;
  },
  componentIdsAllObjectType: state =>
    state.componentIds.map(idOrGroup => {
      if (typeof idOrGroup === "string") {
        return { id: idOrGroup, subIds: [] };
      }
      return idOrGroup;
    }),
  isNormalMode: state => state.mode === Mode.NORMAL,
  isAnimationMode: state => state.mode === Mode.ANIMATION,

  // 当前编辑动画的组件对象
  editingAnimationsComponent: state => {
    return state.componentMap[state.editingAnimationsComponentId];
  },
  activeComponentAction: state => {
    const {
      editingAnimationsComponentId,
      componentAnimationVal,
      componentFragmentId,
    } = state;

    if (
      !editingAnimationsComponentId ||
      !componentAnimationVal ||
      !componentFragmentId
    )
      return undefined;

    return state.componentMap[
      state.editingAnimationsComponentId
    ].extra.animations[state.componentAnimationVal].fragments[
      state.componentFragmentId
    ].find((action: any) => {
      return action.id === state.componentActiveActionId;
    });
  },
  flatComponentActions: state => {
    const flatActions: (AnimationAction & { fragmentId: string })[] = [];
    const componentIds = Object.keys(state.componentMap);

    componentIds.forEach(componentId => {
      const component = state.componentMap[componentId];
      const { animations } = component.extra;
      if (!animations) return;

      const animationKeys = Object.keys(animations);
      animationKeys.forEach(animationKey => {
        const fragments = animations[animationKey].fragments;
        const fragmentKeys = Object.keys(fragments);
        fragmentKeys.forEach(fragmentKey => {
          const fragment = fragments[fragmentKey];
          fragment.forEach((action: any) => {
            flatActions.push({ ...action, fragmentId: fragmentKey });
          });
        });
      });
    });

    return flatActions;
  },
};
export const updateId = (state: any) => {
  const currentIds = state.componentIds.reduce((prev: string[], curr: { id: any; subIds: any; }) => {
    if (typeof curr === "object") {
      return [...prev, curr.id, ...curr.subIds];
    }
    return [...prev, curr];
  }, []);
  if (currentIds.length) {
    addNewId = Math.max(...currentIds.map((id: any) => Number(id)));
  } else {
    addNewId = currentIds.length;
  }
}



export const preGetId = (): number => {
  return addNewId;
}

export default getters;
