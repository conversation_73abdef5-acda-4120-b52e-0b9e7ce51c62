/* eslint-disable @typescript-eslint/no-explicit-any */
import { cloneDeep, isEqual, merge } from "lodash-es";
import Vue from "vue";
import { MutationTree } from "vuex";
import { v4 as uuidv4 } from "uuid";
import { AnimationAction, Animations } from "./modules/animations";
import { PixelToPicUrlQuence } from "@/common/utils/renderTextureToPicture";
import { updateId } from "./getters";
import { formSchemaToFlat } from "../common/utils/tools";
import { CATEGORY } from "@/common/constants/category";
import { Message } from "element-ui";

export enum UpdateLevelType {
  TOP = 0,
  BOTTOM = 1,
  FORWARD = 3,
  BACKWARD = 4,
}

/**
 * 交换数组中的两个元素
 * @param arr 数组
 * @param index1 下标1
 * @param index2 下标2
 */
function swapArray<T = string>(arr: T[], index1: number, index2: number) {
  Vue.set(arr, index1, arr.splice(index2, 1, arr[index1])[0]);
}

/**
 * 数组元素上移、下移、置顶、置底
 * @param arr 数组
 * @param index 元素下标
 * @param type 操作类型
 */
function updateLevel<T = number>(arr: T[], index: number, type: UpdateLevelType) {
  switch (type) {
    case UpdateLevelType.BACKWARD:
      if (index - 1 < 0) {
        return;
      }
      swapArray(arr, index, index - 1);
      break;
    case UpdateLevelType.FORWARD:
      if (index + 1 >= arr.length) {
        return;
      }
      swapArray(arr, index, index + 1);
      break;
    case UpdateLevelType.TOP:
      arr.push(arr.splice(index, 1)[0]);
      break;
    case UpdateLevelType.BOTTOM:
      arr.unshift(arr.splice(index, 1)[0]);
      break;
    default:
      break;
  }
}


const mutations: MutationTree<State> = {
  setUserInfo(state, payload) {
    Vue.set(state, "userInfo", { ...payload });
  },
  setEditFocus(state, payload) {
    state.editFocus = payload;
  },
  setClient(state, payload) {
    state.client = payload;
  },
  initState(
    state,
    payload: {
      name: string;
      template: Template;
      templateId: number;
      components?: Components;
      animations?: Record<string, any>;
      extraStageData?: {
        [key: string]: any;
      };
      stageData?: {
        width: number;
        height: number;
        safeWidth: number;
        safeHeight: number;
      };
      tagsData?: string;
      status?: number;
      subjectId: number;
      gradeId: number;
      parentVersion: number;
      extData: ExtData;
    },
  ) {
    const { template, components, animations = {}, extraStageData = {}, stageData = {}, name = "", templateId, tagsData = "", status, subjectId, gradeId, parentVersion, extData } = payload;
    const componentIds =
      components?.map((i: Component) => {
        if (i.type === "group") {
          const data = {
            id: String(i.id),
            subIds: (i as GroupComponent).subComponents.map(sub => String(sub.id)),
          };
          if (i.childComponents && i.childComponents.length > 0) {
            const subIds = (i as any).childComponents.map((sub: any) => String(sub.id));
            data.subIds = data.subIds.concat(subIds);

          }
          return data;
        } else if (i.childComponents && i.childComponents.length > 0) {
          return {
            id: String(i.id),
            subIds: (i as any).childComponents.map((sub: any) => String(sub.id)),
          };
        }
        return String(i.id);
      }) || [];

    const componentMap: State["componentMap"] = {};
    components?.forEach(i => {
      i.id = String(i.id);
      if (i.type === "group") {
        (i as GroupComponent).subComponents.forEach(sub => {
          componentMap[sub.id] = sub;
        });
      }
      if (i.childComponents && i.childComponents.length > 0) {
        (i as any).childComponents.forEach((child: any) => {
          componentMap[child.id] = child;
        });
      }
      componentMap[i.id] = i;
    });

    state.name = name;
    state.templateId = templateId;
    Vue.set(state, "extData", extData);
    Vue.set(state, "template", template);
    Vue.set(state, "componentIds", componentIds);
    Vue.set(state, "componentMap", componentMap);
    Vue.set(state, "animations", animations);
    Vue.set(state, "extraStageData", extraStageData);
    Vue.set(state, "stageData", Object.assign(cloneDeep(template.stage), cloneDeep(stageData)));
    Vue.set(state, "gradeId", gradeId);
    Vue.set(state, "subjectId", subjectId);
    Vue.set(state, "tagsData", tagsData);
    Vue.set(state, "status", status);
    if (parentVersion) {
      Vue.set(state, "parentVersion", parentVersion);
    }
  },
  initialDataLoaded(state) {
    state.initialDataLoaded = true;
  },
  cocosInitFinished(state) {
    state.cocosInitFinished = true;
    updateId(state);
  },
  cocosLoadQuestionBundleFinished(state) {
    state.cocosLoadQuestionBundleFinished = true;
  },
  cocosLoadGameSceneFinished(state) {
    state.cocosLoadGameSceneFinished = true;
  },
  cocosLoadStageRootNodeFinished(state) {
    state.cocosLoadStageRootNodeFinished = true;
  },
  cocosLoadCompListStart(state) {
    state.cocosLoadCompListStart = true;
  },
  cocosLoadCompListFinished(state) {
    state.cocosLoadCompListFinished = true;
  },
  setQuestionName(state, payload) {
    state.name = payload;
  },
  setStateProperty(state, payload) {
    Vue.set(payload.target, payload.key, payload.value);
  },
  addComponent(state, component: Component) {
    if (!component.extra) {
      Vue.set(component, "extra", {});
    }
    if (component.type !== "group") {
      state.componentIds.push(component.id);
    }
    Vue.set(state.componentMap, component.id, component);
  },
  refreshComponentLevels(state, payload: { ids: string[] }) {
    Vue.set(state, "componentIds", payload.ids);
  },
  resetOtherCompomentData(state, id: string) {
    const category = state.template.category;
    if (category == CATEGORY.LINE) {
      for (const key in state.componentMap) {
        const item = state.componentMap[key];
        if (item.tag === "dragArea") {
          const extraData = item.extra;
          if (extraData.pairingObject.includes(id)) {
            const updateData = {
              pairingObject: []
            }
            updateData.pairingObject = JSON.parse(JSON.stringify(extraData.pairingObject));
            updateData.pairingObject = updateData.pairingObject.filter(val => val != id);
            this.updateComponentExtra(state, {
              id: item.id,
              newExtra: updateData,
            });
          }
        }
      }
    }
  },

  removeComponent(state, id: string) {
    mutations.resetOtherCompomentData(state, id);
    state.currentComponentIds.indexOf(id) !== -1 && (state.currentComponentIds = []);
    state.componentIds.some((i, index) => {
      if (i === id) {
        state.componentIds.splice(index, 1);
        return true;
      }
      console.log(i, "removeComponent");
      if (typeof i === "object") {
        if (i.id === id) {
          i.subIds.forEach(subId => {
            Vue.delete(state.componentMap, subId);
          });
          state.componentIds.splice(index, 1);
          return true;
        }

        const hasFoundComponent = i.subIds.some((subId, index) => {
          if (subId === id) {
            i.subIds.splice(index, 1);
            return true;
          }
        });
        return hasFoundComponent;
      }
    });
    Vue.delete(state.componentMap, id);
  },
  updateComponentProperties(state, payload: { id: string; newProperties: Component["properties"] }) {
    // (window as any)._$store.isUpdateCmdIng=true;
    const { id, newProperties } = payload;
    const component = state.componentMap[id];
    if (!component) return;
    // 只处理值不同的
    for (const key in newProperties) {
      Vue.set(component.properties, key, newProperties[key as keyof Component["properties"]]);
    }
  },

  updateComponentExtra(state, payload: { id: string; newExtra: Component["extra"] }) {
    const { id, newExtra } = payload;
    const component = state.componentMap[id];
    console.log(id, newExtra, component);
    for (const key in newExtra) {
      Vue.set(component.extra, key, newExtra[key]);
    }
  },
  updateComponentExtraClear(state, payload: { id: string; newExtra: Component["extra"] }) {
    const { id, newExtra } = payload;
    const component = state.componentMap[id];
    console.log(id, newExtra, component);
    for (const key in newExtra) {
      Vue.delete(component.extra, key);
    }
  },
  addChildComponent(state, payload: { id: string; childId: string }) {
    const { id, childId } = payload;
    console.log(id, childId);
    // state.componentMap[id].childComponents.push(childComponent);
    // state.componentIds.push(childComponent.id);
  },

  removeChildComponent(state, payload: { id: string; childId: string }) {
    // if (state.componentMap[payload.id].type !== "group" && state.componentMap[payload.id].childComponents) {
    if (state.componentMap[payload.id].childComponents) {
      state.componentMap[payload.id].childComponents.forEach((childComponent: any, index: number) => {
        if (childComponent.id === payload.childId) {
          state.componentMap[payload.id].childComponents.splice(index, 1);
        }
      });
    }
    state.componentIds.forEach((id: string | { id: string; subIds: string[] }, index: number) => {
      if (typeof id === "object" && id.id === payload.id) {
        id.subIds = id.subIds.filter(subId => subId !== payload.childId);
        if (id.subIds.length === 0) {
          state.componentIds[index] = payload.id;
        }
      }
    });
    mutations.resetOtherCompomentData(state, payload.id);
  },
  updateChildComponentProperties(
    state,
    payload: {
      id: string;
      childId: string;
      newProperties: Component["properties"];
    },
  ) {
    const { id, childId, newProperties } = payload;
    const component = state.componentMap[id];
    component.childComponents.forEach((child: any) => {
      if (child.id === childId) {
        for (const key in newProperties) {
          child.properties[key] = newProperties[key as keyof Component["properties"]];
        }
      }
    });
    console.log("updateChildComponentProperties", component.childComponents);
  },
  replaceCurrentComponentIds(state, ids: string[]) {
    // console.log('commit replaceCurrentComponentIds', state.currentComponentIds, ids);
    // if(isEqual(state.currentComponentIds,ids)) {
    //   console.log('重复选中');
    // } else {
    //   // 取消选中
    //   mutations.unSelectComponent(state, state.currentComponentIds);
    //   // 选中
    //   mutations.selectComponent(state, ids);
    //   // 任何组件都没有选中  
    //   console.log('取消选中');
    // }
    Vue.set(state, "currentComponentIds", [...ids]);
  },
  startEditLabel(state, ids: string[]) {
    Vue.set(state, "currentComponentIds", [...ids]);
  },
  // selectComponent(state, ids: string[]) {
  //   console.log("selectComponent", ids);
  // },
  // unSelectComponent(state, ids: string[]) {
  //   console.log("UnSelectComponent", ids);
  // },
  updateComponentTag(state, payload: { id: string; tag: Tag }) {
    const { id, tag } = payload;
    if (tag.name === state.componentMap[id].tag) {
      return;
    }
    Vue.set(state.componentMap[id], "tag", tag.name);
    // 修改类型后清空extra
    Vue.set(state.componentMap[id], "extra", {});
  },
  updateComponentLevel(state, payload: { id: string; type: UpdateLevelType }) {
    const { id, type } = payload;
    state.componentIds.some((i, index) => {
      if (typeof i === "object") {
        if (i.id === id) {
          updateLevel(state.componentIds, index, type);
          return true;
        }
        const componentIndex = i.subIds.findIndex(subId => subId === id);
        if (componentIndex > -1) {
          updateLevel(i.subIds, componentIndex, type);
          return true;
        }
      }
      if (i === id) {
        updateLevel(state.componentIds, index, type);
        return true;
      }
    });
  },
  // 通过传入的 oldIndex 和 newIndex 来直接改变 componentIds
  updateComponentOrder(state, payload: { id: string; newIndex: number; oldIndex: number }[]) {
    const originalComponentIds = [...state.componentIds]; // 复制原始 componentIds
    const n = originalComponentIds.length; // 数组长度

    // 处理空数组或空 payload 的边界情况
    if (n === 0 && payload.length > 0 || n === 0 && payload.length === 0) {
      console.error("无法处理 payload：componentIds 为空但 payload 不为空。");
      return;
    }

    // 创建结果数组，类型与 originalComponentIds 的元素类型一致
    const resultArray: (typeof originalComponentIds[number] | undefined)[] = new Array(n).fill(undefined);
    const movedOriginalIndices = new Set<number>(); // 存储被移动元素的原始索引

    // 预检查：确保 payload 中各项的 newIndex 是唯一的
    const payloadTargetIndices = payload.map(p => p.newIndex);
    if (new Set(payloadTargetIndices).size !== payloadTargetIndices.length) {
      console.error("无效的 payload：多个移动项指向了相同的 newIndex。");
      return;
    }

    // --- 阶段一：放置被移动的元素 ---
    for (const move of payload) {
      // 验证索引边界
      if (move.oldIndex < 0 || move.oldIndex >= n || move.newIndex < 0 || move.newIndex >= n) {
        console.error(`移动操作中的索引无效: ${JSON.stringify(move)}。数组长度为 ${n}。`);
        return; // 若 payload 无效则中止
      }

      const currentItem = originalComponentIds[move.oldIndex]; // 获取原始位置的项
      let currentItemId: string;

      // 获取当前项的 ID，兼容字符串和对象两种类型
      if (typeof currentItem === 'string') {
        currentItemId = currentItem;
      } else if (currentItem && typeof currentItem === 'object' && 'id' in currentItem) {
        // 断言 currentItem 具有 id 属性，这是基于之前对 state.componentIds 元素类型的推断
        currentItemId = (currentItem as { id: string }).id;
      } else {
        console.error(`在 oldIndex ${move.oldIndex} 处遇到意外的项类型:`, currentItem);
        return; // 类型不一致则中止
      }

      // 比较 payload 中的 id 与实际项的 id
      if (currentItemId !== move.id) {
        console.warn(`移动操作的 ID 不匹配: ${JSON.stringify(move)}。期望在 oldIndex ${move.oldIndex} 的项的 ID 为 '${currentItemId}'，但 payload 中的 ID 为 '${move.id}'。将继续使用 oldIndex 处的项进行操作。`);
      }

      // 检查目标 newIndex 是否已被占用 (此检查部分冗余于顶部的 payloadTargetIndices 唯一性检查，但能捕获此阶段的逻辑错误)
      if (resultArray[move.newIndex] !== undefined) {
        console.error(`逻辑错误或 payload 问题：newIndex ${move.newIndex} 已被另一个 payload 项分配。当前项: ${JSON.stringify(currentItem)}, 当前占用者: ${JSON.stringify(resultArray[move.newIndex])}`);
        return;
      }

      resultArray[move.newIndex] = currentItem; // 将项放置到新位置
      movedOriginalIndices.add(move.oldIndex); // 记录此原始索引已被处理
    }
    // console.log("resultArray 1", JSON.stringify(resultArray));

    // --- 阶段二：填充未被移动的（静止的）元素 ---
    let fillPointer = 0; // 用于在 resultArray 中查找下一个可用空位的指针
    for (let originalIdx = 0; originalIdx < n; ++originalIdx) {
      if (!movedOriginalIndices.has(originalIdx)) { // 如果 originalComponentIds[originalIdx] 处的项是静止的
        const stationaryItem = originalComponentIds[originalIdx]; // 获取静止项

        // 为此静止项在 resultArray 中查找下一个空位
        while (fillPointer < n && resultArray[fillPointer] !== undefined) {
          fillPointer++;
        }

        if (fillPointer < n) { // 如果找到空位
          resultArray[fillPointer] = stationaryItem; // 放置静止项
          fillPointer++; // 移动指针，为下一个静止项做准备
        } else {
          // 如果逻辑和数组大小都正确，则不应发生此情况
          console.error(`逻辑错误：在 resultArray 中没有足够的空位给静止项 '${JSON.stringify(stationaryItem)}' (原始索引 ${originalIdx})。当前的 resultArray: ${JSON.stringify(resultArray)}`);
          return; // 中止
        }
      }
    }
    // console.log("resultArray 2", JSON.stringify(resultArray));

    // --- 最终验证：resultArray 中的所有位置都必须被填充 ---
    if (resultArray.some(item => item === undefined)) {
      console.error("错误：最终重排的数组包含未分配的空位。中止更新。", JSON.stringify(resultArray));
      return; // 中止
    }

    // --- 响应式地更新状态 ---
    // 断言 resultArray 的元素类型与 state.componentIds 的元素类型匹配
    state.componentIds.splice(0, n, ...(resultArray as (typeof originalComponentIds[number])[]));
  },
  /**
   * @description 更新子组件的排序
   */
  updateSubComponentOrder(state, payload: { id: string; oldIndex: number; newIndex: number}[]) {
    const { id, oldIndex, newIndex } = payload[0];
    
    // 根据子节点查找到父节点
    const parentId = state.componentIds.find(item => typeof item === "object" && item.subIds.includes(id)) as { id: string; subIds: string[] };
    console.log("%c Line:470 🍏 parentId", "color:#6ec1c2", parentId);

    // 1. 查找父组件
    const parent = state.componentIds.find(item => typeof item === "object" && item.id === parentId?.id);

    // 2. 检查父组件是否存在且为对象
    if (!parent || typeof parent !== "object") {
      console.error(`updateSubComponentOrder: 未找到ID为 ${parentId} 的父组件。`);
      return;
    }

    // 3. 复制子组件ID数组以进行操作
    const subIds = [...parent.subIds];
    
    // 4. 检查索引是否有效
    if (oldIndex < 0 || oldIndex >= subIds.length || newIndex < 0 || newIndex > subIds.length) {
      console.error(`updateSubComponentOrder: 无效的索引。 oldIndex: ${oldIndex}, newIndex: ${newIndex}, 数组长度: ${subIds.length}`);
      return;
    }
    
    // 5. 执行重排序
    const [movedItem] = subIds.splice(oldIndex, 1);
    subIds.splice(newIndex, 0, movedItem);

    // 6. 响应式地更新 subIds 数组
    Vue.set(parent, "subIds", subIds);
  },
  /**
   * @desc AddChildComponent后更新componentIds
   */
  updateComponentIdsWhenAddChildComponent(state, { childId, id }: { childId: string; id: string }) {
    state.componentIds.forEach((_id, index) => {
      if (typeof _id === "string" && _id === id) {
        const group = {
          id,
          subIds: [childId],
        };
        state.componentIds[index] = group;
      } else if (typeof _id === "object" && _id.id === id) {
        _id.subIds.push(childId);
      } else if (typeof _id === "string" && _id === childId) {
        state.componentIds.splice(index, 1);
        index--;
      }
      console.log(state.componentIds);
    });
  },
  /**
   * @desc 组合操作后更新componentIds
   */
  updateComponentIdsWhenCombineComponents(state, { ids, id }: { ids: string[]; id: string }) {
    const group = {
      id,
      subIds: ids,
    };
    ids.forEach(id => {
      const index = state.componentIds.findIndex(i => {
        if (typeof i === "string" && id === i) {
          return true;
        }
      });
      if (index > -1) {
        state.componentIds.splice(index, 1);
      }
    });
    state.componentIds.push(group);
  },
  /**
   * @desc 取消组合后更新componentIds
   */
  updateComponentIdsWhenSeparateGroupComponent(state, id: string) {
    // 在componentIds中找到group，并将subIds移到上层
    const index = state.componentIds.findIndex(i => {
      if (typeof i === "object" && i.id === id) {
        return true;
      }
    });
    if (index < 0) {
      console.error("separateGroupComponent: 没有这个id ", id);
      return;
    }
    const { subIds } = state.componentIds[index] as {
      id: string;
      subIds: string[];
    };
    state.componentIds.splice(index, 1, ...subIds);
  },
  showContextMenu(state) {
    // state.contextMenu.componentId = payload.id;
    // state.contextMenu.top = payload.top;
    // state.contextMenu.left = payload.left;
    state.contextMenu.visible = true;
  },
  doubleClick(state, payload: { id: string }) {
    console.log("doubleClick", payload);
  },
  hideContextMenu(state) {
    state.contextMenu.visible = false;
  },
  setContextMenuPosition(state, payload: { top: number; left: number }) {
    state.contextMenu.top = payload.top;
    state.contextMenu.left = payload.left;
  },
  replaceClipboard(state, paylod: State["clipboard"]) {
    state.clipboard.type = paylod.type;
    state.clipboard.components = paylod.components;
  },
  updateMode(state, mode) {
    state.mode = mode;
  },
  /**
   * @description 更新舞台数据
   */
  updateStage(state, newStageData) {
    for (const key in newStageData) {
      Vue.set(state.stageData, key, newStageData[key]);
    }
  },
  /**
   * @description 更新模板业务属性
   */
  updateExtraStageData(state, newExtra) {
    for (const key in newExtra) {
      Vue.set(state.extraStageData, key, newExtra[key]);
    }
  },

  /**
   * @description 更新解析
   */
  updateExtraStageAnalysis(state, newExtra) {
    for (const key in newExtra) {
      console.log("xuu-key", key, newExtra[key]);
      Vue.set(state.extraStageData.analysis, key, newExtra[key]);
    }
  },

  /**
   * @description 更新说明
   */
  updateExtraStageIntroduction(state, newExtra) {
    for (const key in newExtra) {
      Vue.set(state.extraStageData.introduction, key, newExtra[key]);
    }
  },

  /**
   * @description 批量设置组件是否可以拖拽移动
   * @param ids 组件 id 数组
   * @param dragable 是否可以拖拽移动
   */
  setComponentsDragable(state, payload: { ids: string[]; dragable: boolean }) {
    const { ids, dragable } = payload;

    ids.forEach(id => {
      Vue.set(state.componentMap[id], "dragable", dragable);
    });
  },
  /**
   * @description 设置切割图形组件坐标点数据
   */
  setCutShapePointsData(
    state,
    {
      componentId,
      pointsData,
    }: {
      componentId: BaseComponent["id"];
      pointsData: CutShapeComponent["properties"]["pointsData"];
    },
  ) {
    const component = state.componentMap[componentId];
    if (!component || component.type !== "cutShape") return;

    Vue.set(component.properties, "pointsData", pointsData);
  },
  /**
   * @description 设置切割图形组件线段数据
   */
  setCutShapeLinesData(
    state,
    {
      componentId,
      linesData,
    }: {
      componentId: BaseComponent["id"];
      linesData: CutShapeComponent["properties"]["linesData"];
    },
  ) {
    const component = state.componentMap[componentId];
    if (!component || component.type !== "cutShape") return;

    Vue.set(component.properties, "linesData", linesData);
  },
  /**
   * @description 添加切割图形组件坐标点
   */
  addCutShapePoint(
    state,
    {
      componentId,
      point,
    }: {
      componentId: BaseComponent["id"];
      point: CutShapePropertiesPoint;
    },
  ) {
    const component = state.componentMap[componentId];
    if (!component || component.type !== "cutShape") return;

    component.properties.pointsData.push(point);
  },
  /**
   * @description 删除切割图形组件坐标点
   */
  removeCutShapePoint(
    state,
    {
      componentId,
      pointId,
    }: {
      componentId: BaseComponent["id"];
      pointId: CutShapePropertiesPoint["id"];
    },
  ) {
    const component = state.componentMap[componentId];
    if (!component || component.type !== "cutShape") return;

    const index = component.properties.pointsData.findIndex((point: { id: string }) => point.id === pointId);
    if (index !== -1) {
      component.properties.pointsData.splice(index, 1);
    }
  },
  /**
   * @description 更新切割图形组件坐标点位置
   */
  updateCutShapePointPosition(
    state,
    {
      componentId,
      pointId,
      position,
    }: {
      componentId: BaseComponent["id"];
      pointId: CutShapePropertiesPoint["id"];
      position: {
        x?: CutShapePropertiesPoint["x"];
        y?: CutShapePropertiesPoint["y"];
      };
    },
  ) {
    const component = state.componentMap[componentId];
    if (!component || component.type !== "cutShape") return;

    const point = component.properties.pointsData.find((point: { id: string }) => point.id === pointId);
    if (!point) return;

    const { x, y } = position;
    if (x !== undefined) point.x = x;
    if (y !== undefined) point.y = y;
  },
  /**
   * @description 组件动画编辑相关
   */
  setIsEditingComponentAnimations(state, payload: boolean) {
    state.isEditingComponentAnimations = payload;
  },
  setEditingAnimationsComponentId(state, payload: string[]) {
    Vue.set(state, "editingAnimationsComponentId", payload);
  },
  setComponentAnimationConfig(state, componentAnimationConfig) {
    state.componentAnimationConfig = componentAnimationConfig;
  },
  setComponentAnimationVal(state, componentAnimationVal: string) {
    state.componentAnimationVal = componentAnimationVal;
  },
  setComponentFragmentId(state, componentFragmentId) {
    state.componentFragmentId = componentFragmentId;
  },
  setComponentActiveActionId(state, componentActiveActionId) {
    state.componentActiveActionId = componentActiveActionId;
  },
  setActiveComponentAction(state, payload) {
    const activeComponentAction = state.componentMap[state.editingAnimationsComponentId].extra.animations[state.componentAnimationVal].fragments[state.componentFragmentId].find((action: any) => {
      return action.id === state.componentActiveActionId;
    });

    Object.keys(payload).forEach(key => {
      Vue.set(activeComponentAction, key, payload[key]);
    });
  },
  addComponentAnimationFragment(state, payload: string) {
    const fragmentId = payload;
    const pointId = uuidv4();
    const { editingAnimationsComponentId, componentAnimationVal } = state;
    const animations = cloneDeep(state.componentMap[editingAnimationsComponentId].extra.animations);

    // 添加 fragment
    animations[componentAnimationVal].fragments[fragmentId] = [];
    // 添加开始时间
    animations[componentAnimationVal].points[pointId] = {
      fragmentId,
      startTime: 0,
    };

    const component = state.componentMap[editingAnimationsComponentId];
    Vue.set(component.extra, "animations", animations);
  },
  removeComponentAnimationFragment(state, payload: string) {
    const { componentAnimationVal, componentMap, editingAnimationsComponentId } = state;

    const { fragments, points } = componentMap[editingAnimationsComponentId].extra.animations[componentAnimationVal];

    Vue.delete(fragments, payload);

    // fix: 删除对应片段的 point
    const pointKeys = Object.keys(points);
    const pointId = pointKeys.find(id => points[id].fragmentId === payload);
    if (pointId) {
      Vue.delete(points, pointId);
    }
  },
  addComponentAnimationAction(
    state,
    payload: {
      action: AnimationAction;
    },
  ) {
    const { componentMap, editingAnimationsComponentId, componentAnimationVal, componentFragmentId } = state;

    componentMap[editingAnimationsComponentId].extra.animations[componentAnimationVal].fragments[componentFragmentId].push(payload.action);
  },
  removeComponentAnimationAction(
    state,
    payload: {
      animationVal: string;
      fragmentId: string;
      index: number;
    },
  ) {
    const { animationVal, fragmentId, index } = payload;
    const { componentMap, editingAnimationsComponentId } = state;

    componentMap[editingAnimationsComponentId].extra.animations[animationVal].fragments[fragmentId].splice(index, 1);
  },
  updateComponentActionConfigData(
    state,
    payload: {
      type: "tween" | "animation" | "spine" | "cocosAni";
      data: any;
      name: string;
    },
  ) {
    const { data, name, type } = payload;
    const action = state.componentMap[state.editingAnimationsComponentId].extra.animations[state.componentAnimationVal].fragments[state.componentFragmentId].find((action: any) => {
      return action.id === state.componentActiveActionId;
    });

    if (!action) {
      return;
    }
    action.value.name = name;
    Vue.set(action.value, "anim", data);
    action.value.animType = type;
  },
  /**
   * @description 修改组件 editable 属性
   */
  updateComponentEditable(
    state,
    payload: {
      componentIds: BaseComponent["id"][];
      newEditable: BaseComponent["editable"];
    },
  ) {
    const { componentIds, newEditable } = payload;
    componentIds.forEach(componentId => {
      Vue.set(state.componentMap[componentId], "editable", merge(cloneDeep(state.componentMap[componentId].editable), newEditable));
    });
  },
  setComponentActionSpeed(
    state,
    payload: {
      speed: number;
    },
  ) {
    const { speed } = payload;
    const action = state.componentMap[state.editingAnimationsComponentId].extra.animations[state.componentAnimationVal].fragments[state.componentFragmentId].find((action: any) => {
      return action.id === state.componentActiveActionId;
    });

    if (!action) {
      return;
    }
    action.value.speed = speed;
  },
  setComponentActionAnimOption(
    state,
    payload: {
      id?: string;
      option: any;
    },
  ) {
    const { componentMap, componentActiveActionId } = state;
    const { option, id = componentActiveActionId } = payload;
    const componentIds = Object.keys(componentMap);

    componentIds.forEach(componentId => {
      const component = componentMap[componentId];
      const { animations } = component.extra;
      if (!animations) return;

      Object.keys(animations).forEach(componentAnimationVal => {
        const { fragments } = animations[componentAnimationVal];

        Object.keys(fragments).forEach(fragmentId => {
          const action = fragments[fragmentId].find((action: any) => action.id === id);

          if (action) {
            Vue.set(action.value.anim, "option", option);
          }
        });
      });
    });
  },
  setComponentActionAnimRelative(
    state,
    payload: {
      relative: boolean;
    },
  ) {
    const { relative } = payload;
    const action = state.componentMap[state.editingAnimationsComponentId].extra.animations[state.componentAnimationVal].fragments[state.componentFragmentId].find((action: any) => {
      return action.id === state.componentActiveActionId;
    });
    if (!action) {
      return;
    }

    Vue.set(action.value.anim, "relative", relative);
  },
  removeAllComponentActionByComponentId(state, componentId: string) {
    const { componentMap } = state;

    Object.keys(componentMap).forEach(id => {
      const component = componentMap[id];
      const animations: Animations | undefined = component.extra.animations;

      if (!animations) return;

      const animationKeys = Object.keys(animations);
      animationKeys.forEach(animationKey => {
        const fragments = animations[animationKey].fragments;
        const fragmentKeys = Object.keys(fragments);
        fragmentKeys.forEach(fragmentKey => {
          const fragment = fragments[fragmentKey];

          const newFragment = fragment.filter(action => action.componentId !== componentId);

          Vue.set(fragments, fragmentKey, newFragment);
        });
      });
    });
  },
  updateCocosAniClips(state, payload: { id: string; clips: [] }) {
    const component = state.componentMap[payload.id];
    (component.properties as CocosAniProperties).clips = [];
    payload.clips.forEach((cl: any) => {
      (component.properties as CocosAniProperties).clips.push(cl.name);
    });
    console.log("vuex更新clips---》", component);
  },
  textToImg(state, payload: { selectId: string; texture: cc.RenderTexture }) {
    const component = state.componentMap[payload.selectId];
    if (payload.texture) {
      PixelToPicUrlQuence.Instance.addQueuePicData(component, "textureArray", payload.texture, "image/png", 0.92, "textImg.png");
    } else {
      console.log("vuex更新文本图片：texture无效");
    }
  },
  // 更新extData中的一级字段
  updateExtProps(state, payload: { key: string, value: any }) {
    const { key, value } = payload;
    Vue.set(state.extData, key, value);
  },
  // 更新组件配置表单schema
  updateExtFormSchema(state, payload: { formSchema: FormSchema; subType: string }) {
    const newFormConfig = { ...state.extData.formConfig };
    newFormConfig[payload.subType] = payload.formSchema;
    Vue.set(state.extData, "formConfig", newFormConfig);
  },
  // 更新组件下指定表单配置项
  updateFormItemProps(state, payload: { props: Record<string, any>; key: string; subType: string }) {
    const formItemList = formSchemaToFlat(state.extData.formConfig[payload.subType] as FormSchema);
    const formConfigs = formItemList.find(item => item.key === payload.key);
    if (formConfigs) {
      Object.assign(formConfigs, payload.props);
    }
  },
  initFormTemplateData(state, payload: { formTemplateData: FormTemplateData }) {
    state.extData.formTemplateData = payload.formTemplateData;
  },
  updateReferenceAnswer(state, payload: { referenceAnswer: any }) {
    state.template.referenceAnswer = payload.referenceAnswer;
  },
  updateTemplateProps(state, payload: { props: Record<string, any> }) {
    Object.assign(state.template, payload.props);
  },
  setContainerSize(state, payload: { width: number, height: number }) {
    state.containerWidth = payload.width;
    state.containerHeight = payload.height;
  },
  setGlobalMessage(state, payload: { type: any, message: string }) {
    console.log('payload.messageParams', payload);
    Message({
      type: payload.type,
      message: payload.message
    });
  },
};

// 复制所有mutation并增加cocos前缀，供cocos中调用
const cocosMutations = (() => {
  const cocosMutations = {} as MutationTree<State>;
  for (const key in mutations) {
    if (Object.prototype.hasOwnProperty.call(mutations, key)) {
      cocosMutations["cocos/" + key] = function (...args) {
        mutations[key](...args);
      };
    }
  }
  return cocosMutations;
})();

export default { ...mutations, ...cocosMutations };
