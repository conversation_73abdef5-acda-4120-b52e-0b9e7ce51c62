import { debounce, cloneDeep } from "lodash-es";
import { Store, StoreOptions } from "vuex";
import { types, prefix as timeTravelPrefix } from "./modules/timeTravel";

const DEBOUNCE_TIME = 500;

// 不需要撤销的mutation type集合
const excludeMutations = [
  "setUserInfo",
  "moduleAnimations/playAnimation",
  "moduleAnimations/stopAnimation",
  "moduleAnimations/cocos/stopAnimation",
  "moduleAnimations/playFragment",
  "moduleAnimations/stopFragment",
  "moduleAnimations/cocos/stopFragment",
  "setContextMenuPosition",
  "showContextMenu",
  "setGlobalMessage",
  "componentManagerCard/setActiveNames",
  "componentManagerCard/setVisible",
  "cocosLoadQuestionBundleFinished",
  "cocosLoadGameSceneFinished",
  "cocosLoadStageRootNodeFinished",
  "cocosLoadCompListStart",
  "cocosLoadCompListFinished",
];

class TimeTravel {
  historyStates: State[] = [];
  _cb: any = null;;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  constructor(public store: Store<any>) { }

  subscribeMutations() {
    this.historyStates.push(cloneDeep(this.store.state));
    const cb = debounce((mutation, state) => {
      const isTimeTravelMutation = mutation.type.startsWith(timeTravelPrefix);
      const isExcludeMutation = excludeMutations.includes(mutation.type);
      let ignoreHistory = false;
      if (mutation.payload) {
        ignoreHistory = mutation.payload.ignoreHistory
      }
      if (isTimeTravelMutation || isExcludeMutation || ignoreHistory) {
        return;
      }
      this.add(state);
    }, DEBOUNCE_TIME);
    this._cb = this.store.subscribe(cb);
  }

  add(state: State) {
    const cIndex: number = this.store.state.timeTravel.currentIndex;
    this.store.commit(types.mutations.splice, cIndex + 1);
    this.historyStates.splice(cIndex + 1);
    this.store.commit(types.mutations.push);
    this.historyStates.push(cloneDeep(state));
    this.store.commit(types.mutations.updateCurrentIndex, cIndex + 1);
    this.store.commit(types.mutations.updateTotal, this.historyStates.length);
  }

  undo() {
    const cIndex: number = this.store.state.timeTravel.currentIndex;
    if (cIndex < 1) {
      return;
    }
    this.store.commit(types.mutations.updateCurrentIndex, cIndex - 1);
    this.replaceState();
    this.store.commit(types.mutations.undo);
  }

  redo() {
    const cIndex: number = this.store.state.timeTravel.currentIndex;
    if (cIndex === this.historyStates.length - 1) {
      return;
    }

    this.store.commit(types.mutations.updateCurrentIndex, cIndex + 1);
    this.replaceState();
    this.store.commit(types.mutations.redo);
  }

  replaceState() {
    const currentIndex = this.store.state.timeTravel.currentIndex;
    const historyState = cloneDeep(this.historyStates[currentIndex]);
    this.store.replaceState(historyState);
    this.store.commit(types.mutations.updateCurrentIndex, currentIndex);
    this.store.commit(types.mutations.updateTotal, this.historyStates.length);
  }
  resetState(state: StoreOptions<State>) {
    this.store.commit("timeTravel/resetCommandFactory");
    if (this._cb) {
      this._cb();
    }
    if ((window as any).storeUnSubscribe) {
      (window as any).storeUnSubscribe();
    }
    this.store.replaceState(state);
    this.historyStates = [];

  }
}

export default TimeTravel;
