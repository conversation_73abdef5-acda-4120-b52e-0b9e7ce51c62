/* eslint-disable @typescript-eslint/no-explicit-any */
<template>
  <div id="app" v-loading="pageLoading">
    <top-bar />
    <div class="main">
      <CocosStagePanel />
      <EditAreaPanel v-if="initialDataLoaded" />
      <ComponentManagerCard />
      <AsyncComponentsPanel />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import "@/assets/svg/output";
import { translate } from "@/pages/index/common/hdkt2Shark/translate";
import { h5Switch } from "@/pages/index/common/hdkt2Shark/h5Switch";
import { resetStore } from "./store/index";
/** 监听跟读单词编辑事件 */
import "./common/utils/group";
import { clearCacheData, reCacheConfirm } from "@/common/utils/autoCache";
import SubScribe from "@/pages/index/common/utils/subscribe";
import { getNavigationStartTime, TimeMonitorType } from "@/common/utils/monitorUtil";
import { commitPageData2Store } from "./common/utils/intiPageData";
import { snapId, id, cchdId, hdkt2Shark, h5Switch as h5ReadCom, fromGroup } from "@/pages/index/common/utils/getQueryParams";

// 必须初始化时进行渲染
import CocosStagePanel from "./components/CocosStagePanel/index.vue";
@Component({
  components: {
    CocosStagePanel,
    EditAreaPanel: () => import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/EditAreaPanel/index.vue"),
    TopBar: () => import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/TopBar/index.vue"),
    ComponentManagerCard: () => import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/ComponentManagerCard/index.vue"),
    AsyncComponentsPanel: () => import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/AsyncComponentsPanel/index.vue"),
  },
})
export default class App extends Vue {
  /** 外部事件通知全局loading时为true */
  showLoading = false;

  subScriber: SubScribe | undefined;

  /**
   * 页面loading的时机：
   * 1. cocos初始化未完成（!cocosInitFinished）
   * 2. 初始数据加载未完成（!initialDataLoaded）
   * 3. 外部因素产生的全局loading未完成(showLoading)
   * 4. 非题组内部（题组小题的loading在题组页面）
   */
  get pageLoading() {
    const { cocosInitFinished, initialDataLoaded } = this.$store.state;
    return !this.isGroupInner && (!initialDataLoaded || !cocosInitFinished || this.showLoading);
  }

  get initialDataLoaded() {
    return this.$store.state.initialDataLoaded;
  }

  // 互动题板id
  get cchdId() {
    return id || cchdId;
  }

  get isGroupInner() {
    return fromGroup && !this.cchdId;
  }

  handleMessage(message: any) {
    if (!message.data || !message.data.action) return;
    if (message.data && message.data.action === "group-question-change") {
      if (snapId || this.cchdId) {
        console.log("snapId:", snapId, "cchdId:", this.cchdId, "已经存在不可重置");
        return;
      }
      (window as any).subQuestionChangeStart = Date.now() - getNavigationStartTime();
      this.resetMessage(message);
      return;
    }
    if (h5ReadCom && message.data.action == "newquestion2cocos") {
      this.receiveH5Switch(message.data.data);
      return;
    }

    if (message.data && message.data.action === "h5toCocos") {
      this.receiveHdktPage(message);
    }
  }

  receiveH5Switch = async (message: any) => {
    const data = await h5Switch(message);
    commitPageData2Store(data);
  };

  receiveHdktPage = async (message: any) => {
    if (!hdkt2Shark) return;
    // 研发开发 自测数据时接收的数据时message
    // 例如：this.receiveHdktPage(h5Puzzle); // h5Puzzle数据时mock数据
    let tempData = message;
    if (message.data && message.data.action === "h5toCocos") {
      tempData = message.data.data;
    }
    const data = await translate(tempData);
    const tagsData = tempData.tagsData;
    (data as any).tagsData = tagsData;
    (data as any).subjectId = tagsData.subjectId;
    (data as any).gradeId = tagsData.gradeId;
    commitPageData2Store(data);
  };

  beforeCreate() {
    this.$nextTick(() => {
      this.startEntry();
    });
  }

  async created() {
    window.addEventListener("message", this.handleMessage, false);
    window.oncontextmenu = function(e: any) {
      //取消默认的浏览器自带右键 很重要！！
      e.preventDefault();
    };
    this.$bus.$on("showPageLoading", this.handleShowLoading);
  }

  async mounted() {
    await this.$nextTick();
    this.subScriber = new SubScribe();
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("message", this.handleMessage, false);
      clearInterval(this.cacheTimer);
      this.subScriber && this.subScriber.remove();
      this.$bus.$off("showPageLoading", this.handleShowLoading);
    });
  }

  handleShowLoading(val: boolean) {
    this.showLoading = val;
  }

  public resetMessage(e: { data: { action: string; data: string } }) {
    const data = e.data;
    if (data && data.action === "group-question-change") {
      resetStore();
      this.startEntry();
      setTimeout(() => {
        commitPageData2Store(e?.data?.data);
      }, 20);
    }
  }

  // start入口
  async startEntry() {
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.DataInit);
    try {
      this.resetGroupCategory();
      if (fromGroup && !this.cchdId) {
        return;
      } else if (h5ReadCom) {
        // 阅读理解
        console.log("阅读理解");
        return;
      } else {
        // 请求题板数据
        // fetchPageData()
        this.$getPageConfigByKey("fetchPageData")().then((res: any) => {
          // console.log('fetch res', res);
          reCacheConfirm(res).then((data: any) => {
            if (data) {
              this.clearCacheData();
              // console.log('fetch data', data)
              commitPageData2Store(data);
            }
          });
        });
      }
    } catch (errData) {
      const err = errData as any;
      showErrorMessage(err);
    }
  }

  resetGroupCategory() {
    if (fromGroup && !this.cchdId) return;
    localStorage.setItem("groupCategory", "");
    // TODO: 整理setItem
  }

  cacheTimer: any = null;
  /**
   * 清除缓存数据
   * 每隔3分钟清除一次
   */
  clearCacheData() {
    clearCacheData();
    this.cacheTimer = setInterval(() => {
      this.$store.dispatch("cacheState");
    }, 3 * 60 * 1000);
  }
}
</script>
<style lang="less">
@import "./index.less";
</style>
