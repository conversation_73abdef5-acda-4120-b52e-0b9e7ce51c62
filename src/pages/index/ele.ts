import Vue from "vue";
import {
  Autocomplete,
  Button,
  ButtonGroup,
  Cascader,
  Checkbox,
  Collapse,
  CollapseItem,
  Col,
  ColorPicker,
  Dialog,
  Drawer,
  Loading,
  Input,
  InputNumber,
  Message,
  MessageBox,
  Option,
  Pagination,
  Popover,
  Row,
  Switch,
  Select,
  Slider,
  Upload,
  Tabs,
  TabPane,
  Tooltip,
  Form,
  FormItem,
  Tag,
  Radio,
  RadioButton,
  RadioGroup,
  CheckboxGroup,
  CheckboxButton,
  Avatar,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  Progress,
  Table,
  TableColumn,
  Image,
} from "element-ui";
import "zyb-pc-ui/lib/theme-chalk/index.css";
import "zyb-pc-ui/lib/theme-chalk/dropdown-item.css";
import "zyb-pc-ui/lib/theme-chalk/dropdown.css";
import "zyb-pc-ui/lib/theme-chalk/dropdown-menu.css";
import "zyb-pc-ui/lib/theme-chalk/checkbox-button.css";

Vue.use(Autocomplete);
Vue.use(Button);
Vue.use(ButtonGroup);
Vue.use(Cascader);
Vue.use(Checkbox);
Vue.use(CheckboxGroup);
Vue.use(CheckboxButton);
Vue.use(ColorPicker);
Vue.use(CollapseItem);
Vue.use(Collapse);
Vue.use(Col);
Vue.use(Dialog);
Vue.use(Drawer);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Loading);
Vue.use(Input);
Vue.use(InputNumber);
Vue.use(Option);
Vue.use(Pagination);
Vue.use(Popover);
Vue.use(Radio);
Vue.use(RadioButton);
Vue.use(RadioGroup);
Vue.use(Row);
Vue.use(Switch);
Vue.use(Select);
Vue.use(Slider);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(Upload);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.use(Tag);
Vue.use(Tooltip);
Vue.use(Avatar);
Vue.use(Dropdown);
Vue.use(DropdownItem);
Vue.use(DropdownMenu);
Vue.use(Progress);
Vue.use(Table);
Vue.use(TableColumn);
Vue.use(Image);

Vue.component(Message.name, Message);
Vue.component(MessageBox.name, MessageBox);
Vue.prototype.$message = Message;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
