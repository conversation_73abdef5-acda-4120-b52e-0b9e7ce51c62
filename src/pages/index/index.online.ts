import Vue from "vue";
import "@/common/utils/initComp.ts";
import { getPhpHost } from "@/common/utils/url";
import { Monitor } from "@/common/utils/monitorUtil";
import pageConfig from "@/common/utils/pageConfig/pageConfig.online";
import './index'

(window as MyWindow).monitorManager = new Monitor();
Vue.prototype.$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};
(window as MyWindow).$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};
getPhpHost();


