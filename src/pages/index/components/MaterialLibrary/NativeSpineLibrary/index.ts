import Vue from "vue";
import { CombinedVueInstance } from "vue/types/vue";
import store from "@/pages/index/store";
import SpineLibraryVue from "./index.vue";

const SpineLibraryConstructor = Vue.extend(SpineLibraryVue);

type InstanceMethods = {
  onClose?: () => void;
  onConfirm: (spines: Array<SpineItem>) => void;
};

type InstanceData = {
  visible: boolean;
  isMultiple: boolean;
  classify: string;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceData,
  InstanceMethods,
  object,
  Record<never, any>
>;

export const createInstance = () => {
  if (!instance) {
    instance = new SpineLibraryConstructor({
      store,
      el: document.createElement("div"),
    });
  }
  return instance;
};

type SpineLibraryOptions = {
  isMultiple?: boolean;
  classify: string;
} & InstanceMethods;

export const SpineLibrary = ({
  onClose,
  onConfirm,
  isMultiple,
  classify,
}: SpineLibraryOptions) => {
  createInstance();
  document.body.appendChild(instance.$el);
  instance.visible = true;
  instance.isMultiple = !!isMultiple;
  instance.classify = classify;
  if (onClose) {
    instance.onClose = onClose;
  }
  instance.onConfirm = onConfirm;
};
