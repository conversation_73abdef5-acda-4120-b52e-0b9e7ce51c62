<template>
  <el-dialog :visible.sync="dialogVisible" title="添加动效" width="500px">
    <el-form
      :model="formData"
      :rules="rules"
      ref="form"
      label-width="100px"
      class="add-spine-form"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="formData.name"
          size="mini"
          placeholder="请输入名称"
        />
      </el-form-item>
      <!-- <el-form-item
        label="标签"
        prop="tags"
        v-show="classify === 'spine' ? true : false"
      >
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入标签"
          size="mini"
        >
          <el-option
            v-for="tag in tagList"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
          </el-option>
        </el-select>
      </el-form-item> -->
      <el-form-item label="动效文件" prop="file">
        <el-upload
          ref="upload"
          action=""
          :file-list="formData.file"
          :on-change="onFileChange"
          :show-file-list="false"
          :auto-upload="false"
          accept=".zip"
        >
          <el-button
            v-if="!formData.file.length"
            slot="trigger"
            size="mini"
            type="primary"
            >选取文件</el-button
          >
          <div v-else class="add-spine-form-file-name">
            {{ formData.file[0].name }}
          </div>
        </el-upload>
      </el-form-item>
      <el-form-item label="封面图" prop="cover">
        <el-upload
          ref="upload"
          action=""
          :file-list="formData.cover"
          :on-change="onCoverChange"
          :show-file-list="false"
          :auto-upload="false"
          accept="image/*"
        >
          <el-button
            v-if="!formData.cover.length"
            slot="trigger"
            size="mini"
            type="primary"
            >选取文件</el-button
          >
          <div v-else class="add-spine-form-cover">
            <img
              v-if="coverUrl"
              :src="coverUrl"
              class="add-spine-form-cover-preview"
            />
            <i class="el-icon-close" @click.stop="coverUrl = ''"></i>
          </div>
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="add-spine-footer">
      <el-button @click="onClickClose" size="small">取 消</el-button>
      <el-button
        @click="onClickConfirm"
        type="primary"
        class="add-spine-footer-btn"
        size="small"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { addSpine } from "@/common/api/native-spine";
import { addCocosAni } from "@/common/api/cocosAni";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { ElForm } from "element-ui/types/form";
import { Component, Prop, Vue } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";

@Component
export default class AddSpineDialog extends Vue {
  @Prop()
  visible!: boolean;

  @Prop()
  tagList!: { name: string; id: number }[];

  @Prop()
  classify!: string;

  formData: {
    name: string;
    tags: string[];
    file: { raw: File }[];
    cover: { raw: File }[];
  } = { name: "", tags: [], file: [], cover: [] };

  rules = {
    name: [
      { required: true, message: "请输入动效名称", trigger: "blur" },
      { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
    ],
    tags: [
      {
        type: "array",
        required: true,
        message: "请至少输入一个标签",
        trigger: "change",
      },
    ],
    file: [
      {
        type: "array",
        required: true,
        trigger: "blur",
      },
    ],
    cover: [
      {
        type: "array",
        required: true,
        trigger: "blur",
      },
    ],
  };

  get userInfo() {
    return this.$store.state.userInfo;
  }

  get dialogVisible() {
    if (this.visible) {
      this.$nextTick(() => {
        (this.$refs.form as ElForm).clearValidate();
      });
    }
    return this.visible;
  }

  set dialogVisible(val) {
    (this.$refs.form as ElForm).resetFields();
    this.$emit("update:visible", val);
    this.$emit("close");
    bus.$emit('dialogVisible', val)
  }

  get coverUrl() {
    if (!this.formData.cover.length) {
      return undefined;
    }
    return URL.createObjectURL(this.formData.cover[0].raw);
  }

  set coverUrl(val) {
    this.formData.cover = [];
  }

  onClickClose() {
    this.dialogVisible = false;
  }

  onClickConfirm() {
    // 兼容 cocosAni 校验规则
    if (this.classify === "cocosAni") {
      this.formData.tags = ["12"];
    }
    (this.$refs.form as ElForm).validate(valid => {
      if (!valid) {
        return;
      }
      if (this.classify === "spine") {
        this.onCLickSpineConfirm();
      }
      if (this.classify === "cocosAni") {
        this.onClickCocosAniConfirm();
      }
    });
  }

  onCLickSpineConfirm() {
    const formData = new FormData();
    formData.append("name", this.formData.name);
    formData.append("zip", this.formData.file[0].raw);
    if (this.formData.cover.length) {
      formData.append("cover", this.formData.cover[0].raw);
    }
    return addSpine(formData)
      .then(() => {
        this.$message.success("上传成功");
        this.dialogVisible = false;
      })
      .catch(showErrorMessage);
  }

  onClickCocosAniConfirm() {
    const fd = new FormData();
    fd.append("title", this.formData.name);
    fd.append("file", this.formData.file[0].raw);
    if (this.formData.cover.length) {
      fd.append("thumbnail", this.formData.cover[0].raw);
    }
    return addCocosAni(fd)
      .then(() => {
        this.$message.success("上传成功");
        this.dialogVisible = false;
      })
      .catch(showErrorMessage);
  }

  onFileChange($event: any) {
    const { formData } = this;
    formData.file.pop();
    formData.file.push($event);
  }

  onCoverChange($event: any) {
    const { formData } = this;
    formData.cover.pop();
    formData.cover.push($event);
  }
}
</script>

<style lang="less" scoped>
.add-spine {
  &-form {
    width: 300px;

    &-file-name {
      &:hover {
        color: #409eff;
      }
    }

    &-cover {
      position: relative;
      i {
        position: absolute;
        right: 0;
        width: 16px;
        height: 16px;
        font-size: 16px;
        color: red;
      }

      &-preview {
        width: 80px;
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: center;

    &-btn {
      margin-left: 30px;
    }
  }
}
</style>
