<template>
  <li class="spine-item">
    <div
      :class="[
        'spine-item-cover',
        { selected },
        Number(isSyns) === 0 ? 'spine-item-cover-isSyns' : '',
      ]"
    >
      <span class="cover-selected"
        ><svg
          viewBox="0 0 24 24"
          id="coloration-selected"
          xmlns="http://www.w3.org/2000/svg"
          width="100%"
          height="100%"
        >
          <path
            d="M15.724 8l-5.038 5.96-2.535-2.41L7 12.765l3.819 3.628L17 9.078z"
            fill-rule="evenodd"
          ></path></svg
      ></span>
      <img class="spine-item-cover-img" :src="imgUrl" />
      <!-- <div
        class="spine-item-edit"
        @click.stop="onClickEdit"
        v-show="classify === 'spine' ? true : false"
      >
        编辑
      </div> -->
    </div>
    <span class="spine-item-name">{{ name }}</span>
  </li>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class SpineItem extends Vue {
  @Prop()
  name!: string;

  @Prop()
  imgUrl!: string;

  @Prop()
  selected!: boolean;

  @Prop()
  isSyns!: number;

  @Prop()
  classify!: string;

  onClickEdit() {
    this.$emit("clickEdit");
  }
}
</script>

<style lang="less" scoped>
.spine-item {
  position: relative;
  margin: 0px 10px 10px;
  box-sizing: border-box;
  width: calc(100% / 6 - 20px);
  flex-shrink: 0;
  text-align: center;
  cursor: pointer;

  :hover {
    .spine-item-edit {
      display: block;
    }
  }

  &-edit {
    display: none;
    width: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    height: 24px;
    background: rgba(0, 0, 0, 0.75);
    font-size: 14px;
    color: #409eff;
    line-height: 24px;
  }

  &-cover {
    position: relative;
    border: 1px solid #ececec;
    font-size: 0;

    &-img {
      padding: 5px;
      background: url("~@/assets/img/transparent.png") repeat;
      box-sizing: border-box;
      background-size: 10px;
      width: 100%;
      height: 120px;
      object-fit: contain;
    }
    &-isSyns {
      opacity: 0.5;
    }
  }

  &-name {
    // padding: 0 5px;
    display: inline-block;
    color: #333;
    font-size: 13px;
    line-height: 24px;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  //   svg {
  //   fill: white;
  // }

  .cover-selected {
    display: none;
    position: absolute;
    width: 16px;
    height: 16px;
    right: -8px;
    top: -8px;
    border-radius: 10px;
    background-color: #409eff;
    color: #fff;
    z-index: 2;
  }

  svg {
    fill: white;
  }
}

.selected {
  .cover-selected {
    display: block;
  }

  border: 1px solid #409eff;
}
</style>
