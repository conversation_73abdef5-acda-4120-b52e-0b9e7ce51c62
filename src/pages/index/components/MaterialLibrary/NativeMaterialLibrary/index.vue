<template>
  <div class="audio-library native">
    <el-dialog title="提示" :visible.sync="visible" :before-close="beforeClose" width="880px" top="0vh" :modal="false" :modal-append-to-body="false">
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="图片库" name="my" v-loading="loading">
          <div class="audio-container">
            <div class="audio-item" style="background: #fff;">
              <el-upload :show-file-list="false" :http-request="request" action="" accept="image/png, image/jpeg" :before-upload="beforeUpload" :on-success="onSuccess" class="audio-uploader">
                <i class="el-icon-plus audio-uploader-icon"></i>
              </el-upload>
            </div>
            <div v-for="audio in myAudios" :key="audio.id" class="audio-item" :title="audio.name">
              <div class="audio-item-box" @click="handleSelectedAudiosChange(audio)">
                <img :src="audio.url" />
              </div>
              <div @click.stop>
                <el-checkbox :value="mySelectedImgs.findIndex(i => i.id === audio.id) === -1 ? false : true" @change="handleSelectedAudiosChange(audio)"></el-checkbox>
              </div>
              <div class="audio-item-name">
                {{ audio.name }}
              </div>
            </div>
          </div>
          <el-pagination layout="prev, pager, next" :page-size="myPageSize" :current-page="myPageNumber" :total="myImgTotal" @current-change="handleMyPageNumberChange"></el-pagination>
        </el-tab-pane>
      </el-tabs>
      <span slot="footer" class="dialog-footer">
        <el-button type="danger" @click="onClickDelete" :disabled="isDeleteBtnDisabled" v-if="activeTabName === 'my'">删 除</el-button>
        <el-button type="primary" @click="onClickDownload" :disabled="isDeleteBtnDisabled" v-if="activeTabName === 'my'">下 载</el-button>
        <el-button @click="onClickClose">取 消</el-button>
        <el-button type="primary" @click="onClickConfirm" :disabled="isConfirmBtnDisabled">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import downloadByUrl from "@/common/utils/downloadByUrl";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import bus from "@/pages/index/common/utils/bus";
import { nativeRequestServer } from "@/common/nativeUtils";

@Component
export default class NativeMaterialLibrary extends Vue {
  loading = false;
  visible = false;
  // 当前选中tab
  activeTabName: "my" = "my";
  // 是否支持多选
  isMultiple = false;

  myPageNumber = 1;
  myPageSize = 11;
  myImgTotal = 0;
  myAudios: Array<AudioItem> = [];
  mySelectedImgs: Array<AudioItem> = [];

  callback: any;

  @Watch("visible", { immediate: true })
  visibleWatcher(visible: boolean) {
    if (visible) {
      this.getMyImgs();
    }
    bus.$emit("dialogVisible", visible);
  }

  get isConfirmBtnDisabled() {
    const { mySelectedImgs } = this;

    return !mySelectedImgs.length;
  }

  get isDeleteBtnDisabled() {
    return !this.mySelectedImgs.length;
  }

  async request(params: { file: File }) {
    const { file } = params;
    const formData = new FormData();
    formData.append("file", file);
    try {
      const uploadFileRes = await nativeRequestServer.post("/editorApi/uploadImg", formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      });
      console.log("uploadFileRes: ", uploadFileRes);
    } catch (err) {
      showErrorMessage(err);
      return Promise.reject();
    }
  }

  onClickConfirm() {
    this.visible = false;
    const { mySelectedImgs } = this;
    this.onConfirm(mySelectedImgs);
    this.mySelectedImgs = [];
  }

  async onClickDelete() {
    this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        await nativeRequestServer.get("/editorApi/delImg", { params: { id: this.mySelectedImgs[0].id } });
        this.mySelectedImgs = [];
        this.getMyImgs();
      })
      .catch(() => {
        // void
      });
  }

  beforeUpload(file: File) {
    const { size } = file;
    const kb = 1024;
    const mb = 1024 * kb;
    const SIZE_LIMI = 2 * mb;
    const isOutOfSize = size > SIZE_LIMI;
    if (isOutOfSize) {
      this.$message.error("文件大小不可超过2MB");
      return false;
    }
  }

  beforeClose(done: () => void) {
    this.onClickClose();
    done();
  }

  onClickClose() {
    this.visible = false;
  }

  onConfirm(audios: Array<AudioItem>) {
    console.log("onConfirm", { type: 'img', data: audios });
    this.callback({ type: 'img', data: audios });
  }

  /**
   * @description 选中图片逻辑
   * 只选一个
   */
  handleSelectedAudiosChange(audio: any) {
    console.log("handleSelectedAudiosChange", audio);
    this.mySelectedImgs.push(audio);
  }

  /**
   * @description 获取我的上传图片
   */
  async getMyImgs() {
    this.loading = true;
    try {
      const {
        data: { data },
      } = await nativeRequestServer.get("/editorApi/getImgList");
      this.myImgTotal = data.length;
      this.myAudios = data
        .map((item: any) => ({
          ...item,
          url: `${origin}${item.path}`,
          fileUrl: `${origin}${item.path}`,
          remoteUrl: item.urlContent,
        }))
        .filter((item: { name: string }) => item.name !== "textImg.png");
    } catch (err) {
      showErrorMessage(err);
    }
    this.loading = false;
  }

  handleMyPageNumberChange(pageNum: number) {
    this.myPageNumber = pageNum;
    this.getMyImgs();
  }

  onSuccess() {
    this.$message.success("上传成功");
    this.getMyImgs();
  }

  onClickDownload() {
    const { mySelectedImgs } = this;
    mySelectedImgs.forEach(audio => {
      downloadByUrl(audio.url);
    });
  }

  created() {
    bus.$on("material-library", (params: { callback: () => void }) => {
      console.log("material-library on", params);
      this.visible = true;
      this.callback = params.callback;
    });
  }
}
</script>

<style lang="less" scoped>
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.audio-library {
  /deep/ .el-dialog__header {
    height: 0;
    padding: 0;
    display: none;
  }

  /deep/ .el-dialog__body {
    padding: 20px 15px;

    .audio-container {
      display: flex;
      flex-wrap: wrap;

      .audio-item {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 10px 10px;
        cursor: pointer;

        &-box {
          width: 100px;
          height: 100px;
          background-size: 100%;
          border-radius: 50%;
        }

        &-play {
          animation: rotate 2s linear infinite;
        }

        &-name {
          text-align: center;
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .circle {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 40px;
          height: 40px;
          border-radius: 40px;
          transform: translate(-50%, -50%);
        }

        .el-checkbox {
          position: absolute;
          top: 10px;
          right: 10px;
        }

        .audio-uploader {
          .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }

          .el-upload:hover {
            border-color: #409eff;
          }

          .audio-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 98px;
            height: 98px;
            line-height: 98px;
            text-align: center;
          }
        }

        img {
          border-radius: 6px;
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
        .audio-item-box {
          display: 1;
        }
      }
    }

    .el-pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
}
</style>
