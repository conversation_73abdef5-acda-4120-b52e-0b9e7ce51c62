import Vue from "vue";
import AudioLibraryVue from "./index.vue";
import { CombinedVueInstance } from "vue/types/vue";

import store from "@/pages/index/store";

const AudioLibraryConstructor = Vue.extend(AudioLibraryVue);

type InstanceMethods = {
  onClose: () => void;
  onConfirm: (audios: AudioItem[]) => void;
};

type InstanceData = {
  visible: boolean;
  isMultiple: boolean;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceData,
  InstanceMethods,
  object,
  Record<never, any>
>;

export const createInstance = () => {
  if (!instance) {
    instance = new AudioLibraryConstructor({
      store,
      el: document.createElement("div")
    });
  }
  return instance;
};

type AudioLibraryOptions = {
  isMultiple?: boolean;
} & InstanceMethods;

export const AudioLibrary = ({
  onClose,
  onConfirm,
  isMultiple
}: AudioLibraryOptions) => {
  createInstance();
  document.body.appendChild(instance.$el);
  instance.visible = true;
  instance.isMultiple = !!isMultiple;
  instance.onClose = onClose;
  instance.onConfirm = onConfirm;
};
