<template>
  <div class="audio-library">
    <el-dialog
      title="提示"
      :visible.sync="visible"
      :before-close="beforeClose"
      width="1010px"
      top="0vh"
      :modal="false"
      :modal-append-to-body="false"
      v-loading="loading"
      element-loading-fullscreen="false"
      element-loading-background="rgba(0, 0, 0, 0.05)"
    >
      <div>
        <div class="audio-title">音频库</div>
        <div class="ops-wrapper">
          <div class="ops-left">
            <div class="ops-item">
              音频名称
              <el-input placeholder="请输入音频名称" size="small" v-model="searchParams.name" />
            </div>
            <div class="ops-item">
              创建人
              <el-input placeholder="请输入上传人名称" size="small" v-model="searchParams.creatorName" />
            </div>

            <el-button class="search-ops" type="primary" size="small" @click="handleSearch">
              查询
            </el-button>
          </div>
          <div class="ops-right">
            <el-upload :show-file-list="false" :http-request="request" action="" accept=".mp3" :before-upload="beforeUpload" :on-success="onSuccess" class="audio-uploader">
              <el-button size="small" class="upload-ops">
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-iconcloudUpload-o"></use>
                </svg>
                上传
              </el-button>
            </el-upload>
          </div>
        </div>
        <div class="audio-container-outer">
          <div class="audio-container" :class="{ 'is-none': !myAudios.length }">
            <template v-if="!myAudios.length">
              <div class="none"></div>
            </template>
            <template v-else>
              <div
                v-for="(audio, index) in myAudios"
                :key="audio.id"
                class="audio-item-wrapper"
                :title="audio.name"
                :class="{ playing: isPlaying(audio.url), selected: getSelected(audio.id) }"
                @click="handleSelectedAudiosChange(audio)"
              >
                <div class="audio-bg">
                  <div class="hover" @click="handleClickPlayOrPause(index, 'myAudios')">
                    <em v-if="!isPlaying(audio.url)">
                      <svg class="cocosicon" aria-hidden="true">
                        <use xlink:href="#cocos-iconic-play"></use>
                      </svg>
                    </em>
                    <em style="transform: translate(-13px, -50%) !important;" v-else>
                      <svg class="cocosicon" aria-hidden="true">
                        <use xlink:href="#cocos-iconic-audio"></use>
                      </svg>
                    </em>
                  </div>
                </div>
                <div class="audio-info">
                  <div class="title">{{ audio.name.substring(0, audio.name.lastIndexOf(".")) }}</div>
                  <div class="time">
                    <span>{{ audio.createName }}</span> 更新于{{ timestampToCustomFormat(audio.updateTime) }}
                  </div>
                </div>
                <div class="audio-ops">
                  <div class="del-ops" @click="onClickDelete(audio)">
                    <em>
                      <svg class="cocosicon" aria-hidden="true">
                        <use xlink:href="#cocos-iconic-deleted2"></use>
                      </svg>
                    </em>
                  </div>
                  <div class="down-ops" @click="onClickDownload(audio)">
                    <em>
                      <svg class="cocosicon" aria-hidden="true">
                        <use xlink:href="#cocos-icona-ic-down-o1"></use>
                      </svg>
                    </em>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>

        <el-pagination
          layout="sizes, prev, pager, next"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="searchParams.pageSize"
          :current-page="searchParams.pageNum"
          :total="myAudioTotal"
          @current-change="handleMyPageNumberChange"
          @size-change="handleSizeChange"
          v-if="myAudios.length"
        ></el-pagination>
      </div>

      <span slot="footer" class="dialog-footer">
        <div class="footer-tips">
          单次只能插入1个<span v-if="mySelectedAudios.length"
            >，已选 <span class="num">{{ mySelectedAudios.length }}</span> 个</span
          >
        </div>
        <el-button @click="onClickClose">取 消</el-button>
        <el-button type="primary" @click="onClickConfirm" :disabled="isConfirmBtnDisabled">
          插 入
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { deleteMyAudio, getMyAudios, GetMyAudiosParams, uploadAudio } from "@/common/api/audioLibrary";
import APIS from "@/common/api/constants";
import audioPlayer from "@/common/utils/audioPlayer";
import downloadByUrl from "@/common/utils/downloadByUrl";
import { requestPhpServer } from "@/common/utils/request";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import bus from "@/pages/index/common/utils/bus";
import { Component, Vue, Watch } from "vue-property-decorator";

@Component
export default class AudioLibrary extends Vue {
  loading = false;
  visible = false;

  searchParams: GetMyAudiosParams = {
    pageNum: 1,
    pageSize: 10,
    name: "",
    creatorName: "",
  };

  myAudioTotal = 0;
  myAudios: Array<AudioItem> = [];
  mySelectedAudios: Array<AudioItem> = [];

  playingAudioSrcMap: string[] = [];

  handleSearch() {
    this.searchParams.pageNum = 1;
    this.getAudios();
  }

  timestampToCustomFormat(timestamp: number) {
    const date = new Date(timestamp * 1000);
    const year = date.getFullYear();
    const month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的
    const day = ("0" + date.getDate()).slice(-2);
    const hours = ("0" + date.getHours()).slice(-2);
    const minutes = ("0" + date.getMinutes()).slice(-2);
    const seconds = ("0" + date.getSeconds()).slice(-2);
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  @Watch("visible", { immediate: true })
  visibleWatcher(visible: boolean) {
    if (visible) {
      this.searchParams = {
        pageNum: 1,
        pageSize: 10,
        name: "",
        creatorName: "",
      };
      this.getAudios();
    }
    bus.$emit("dialogVisible", visible);
  }

  isPlaying(src: string) {
    return this.playingAudioSrcMap.includes(src);
  }

  handleClickPlayOrPause(index: number, type: "myAudios" | "libraryAudios") {
    const { url: src } = this[type][index];
    const index1 = this.playingAudioSrcMap.findIndex(i => i === src);

    if (index1 !== -1) {
      audioPlayer.pause(src);
      this.playingAudioSrcMap.splice(index1, 1);
    } else {
      // 停止其它所有音频
      this.playingAudioSrcMap.forEach(src => {
        audioPlayer.pause(src);
      });
      this.playingAudioSrcMap = [];

      audioPlayer.play(
        src,
        () => {
          const index2 = this.playingAudioSrcMap.findIndex(i => i === src);
          this.playingAudioSrcMap.splice(index2, 1);
        },
        () => {
          const index2 = this.playingAudioSrcMap.findIndex(i => i === src);
          this.playingAudioSrcMap.splice(index2, 1);
        },
      );
      this.playingAudioSrcMap.push(src);
    }
  }

  get isConfirmBtnDisabled() {
    const { mySelectedAudios } = this;

    return !mySelectedAudios.length;
  }

  async request(params: { file: File }) {
    const { file } = params;
    const formData = new FormData();
    const { name } = file;
    formData.append("file", file);
    try {
      const uploadFileRes = await requestPhpServer.post(APIS.PHP_UPLOAD_AUDIO, formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      });
      console.log("uploadFileRes: ", uploadFileRes);
      const url = uploadFileRes.data.data;
      const uploadLibraryRes = await uploadAudio({
        name,
        urlContent: url,
      });
      console.log("uploadLibraryRes: ", uploadLibraryRes);
      this.getAudios();
    } catch (err) {
      if ((err as any).data.errNo && (err as any).data.data && (err as any).data.data.extStr) {
        this.$message.error((err as any).data.data.extStr);
      } else {
        showErrorMessage(err as Error);
      }
      return Promise.reject();
    }
  }

  onClickConfirm() {
    this.visible = false;
    const { mySelectedAudios } = this;
    this.onConfirm(mySelectedAudios);
    this.mySelectedAudios = [];
    this.stopAllPlayingAudio();
  }

  async onClickDelete(audio: { id: number }) {
    this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(async () => {
        await deleteMyAudio(audio.id);
        this.mySelectedAudios = [];
        this.getAudios();
      })
      .catch(() => {
        // void
      });
  }

  beforeUpload(file: File) {
    const { size } = file;
    const kb = 1024;
    const mb = 1024 * kb;
    const SIZE_LIMI = 2 * mb;
    const isOutOfSize = size > SIZE_LIMI;
    if (file.name.endsWith(".MP3")) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    const isLower = file.name.split(".")[1].toLowerCase() === file.name.split(".")[1];
    if (!isLower) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    if (isOutOfSize) {
      this.$message.error("文件大小不可超过2MB");
      return false;
    }
  }

  beforeClose(done: () => void) {
    this.searchParams = {
      pageNum: 1,
      pageSize: 10,
      name: "",
      creatorName: "",
    };
    this.onClickClose();
    done();
  }

  stopAllPlayingAudio() {
    // 停止其它所有音频
    this.playingAudioSrcMap.forEach(src => {
      audioPlayer.pause(src);
    });
    this.playingAudioSrcMap = [];
  }

  onClickClose() {
    this.stopAllPlayingAudio();
    this.visible = false;
    this.onClose();
  }

  onConfirm(audios: Array<AudioItem>) {
    console.log(audios);
  }

  onClose() {
    //
  }

  /**
   * @description 选中逻辑
   */
  handleSelectedAudiosChange(audio: AudioItem) {
    if (this["mySelectedAudios"].find(item => item.id === audio.id)) {
      this.mySelectedAudios = [];
    } else {
      this.mySelectedAudios = [];
      this["mySelectedAudios"].push(audio);
    }
  }

  getSelected(id: number) {
    return this["mySelectedAudios"].find(item => item.id === id);
  }

  /**
   * @description 获取音频列表
   */
  async getAudios() {
    this.loading = true;
    try {
      const {
        data: { data },
      } = await getMyAudios(this.searchParams);
      data.resourceList = data.resourceList || (data as any).list;
      const { resourceList, total } = data;
      this.myAudioTotal = total;
      this.myAudios = resourceList.map((item: any) => ({
        ...item,
        url: item.urlContent,
      }));
    } catch (err) {
      showErrorMessage(err);
    }
    this.loading = false;
  }
  handleMyPageNumberChange(pageNum: number) {
    this.searchParams.pageNum = pageNum;
    this.getAudios();
  }
  handleSizeChange(pageSize: number) {
    this.searchParams.pageSize = pageSize;
    this.getAudios();
  }

  onSuccess() {
    this.$message.success("上传成功");
  }

  onClickDownload(audio: { url: string }) {
    downloadByUrl(audio.url);
  }
}
</script>

<style lang="less" scoped>
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.audio-title {
  font-family: PingFangSC-Medium;
  font-size: 16px;
  color: #333333;
  line-height: 16px;
  height: 48px;
  font-weight: 500;
}
.ops-wrapper {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
  margin-right: 8px;
  font-size: 12px;
  color: #777777;
  letter-spacing: 0;
  text-align: justify;
  line-height: 12px;
  font-weight: 400;
  /deep/ button {
    height: 32px;
    line-height: 32px;
  }
  .ops-left {
    display: flex;
    flex: 1;
    // gap: 16px;
    .ops-item {
      // 第一个元素右边距16px
      margin-right: 16px;

      padding-left: 8px;
      display: flex;
      align-items: center;
      // gap: 8px;
      white-space: nowrap;
      /deep/ input {
        width: 200px;
        height: 32px;
        line-height: 32px;
        margin-left: 8px;
      }
    }
  }
}

.audio-item-wrapper {
  // 基数行 右间距24
  &:nth-child(2n + 1) {
    margin-right: 24px;
  }
  // 最后两个 margin-bottom: 0
  &:nth-last-child(1),
  &:nth-last-child(2) {
    margin-bottom: 0;
  }
  // 不是最后一个子div
  > div:not(:last-child) {
    margin-right: 16px;
  }
  margin-bottom: 16px;
  display: flex;
  background: #f5f7fa;
  border-radius: 8px;
  width: 467px;
  height: 62px;
  overflow: hidden;
  cursor: pointer;
  // gap: 16px;
  border: 1px solid rgba(0, 0, 0, 0);
  &.selected {
    border-color: #2abd90;
    background: #ecfcff;
  }
  &.playing {
    .audio-bg {
      .hover {
        display: block;
        svg {
          animation: rotate 1s linear infinite;
        }
      }
    }
  }
  &:hover {
    background: #ecfcff;
  }
  .audio-bg {
    border-radius: 8px 0 0 8px;
    width: 64px;
    min-width: 64px;
    height: 64px;
    background: url("../../../assets/<EMAIL>") no-repeat;
    background-size: 100% 100%;
    position: relative;
    .hover {
      display: none;
      transition: all 0.5s;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      font-size: 24px;
      // svg {
      //     animation: rotate 2s linear infinite;
      //   }
      em {
        color: #fff;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-7px, -50%);
        svg {
          width: 24px;
          height: 24px;
        }
      }
    }
  }
  .audio-info {
    max-width: calc(100% - 176px);
    flex: 1;
    .title {
      font-size: 14px;
      color: #333333;
      letter-spacing: 0;
      font-weight: 500;
      margin-top: 13px;
      margin-bottom: 12px;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      overflow: hidden;
    }
    .time {
      font-size: 12px;
      color: #999999;
      letter-spacing: 0;
      line-height: 12px;
      font-weight: 400;
      max-width: 277px;
      white-space: nowrap;
      display: flex;
      align-items: center;
      white-space: pre;
      span {
        display: block;
        max-width: 118px;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  &:hover {
    .audio-bg .hover {
      display: block;
    }
    .del-ops {
      display: flex !important;
    }
  }

  .audio-ops {
    padding: 16px 16px 16px 0;
    display: flex;
    width: 80px;
    // gap: 16px;
    justify-content: flex-end;
    > div {
      // 不是最后一个元素
      &:not(:last-child) {
        margin-right: 16px;
      }
      width: 32px;
      height: 32px;
      display: flex;
      align-content: center;
      align-items: center;
      background: #ffffff;
      border-radius: 8px;
      justify-content: center;
      cursor: pointer;
    }
    .down-ops:hover {
      svg {
        color: #2abd90;
      }
    }
    .del-ops {
      display: none;
      transition: all 0.5s;
      &:hover {
        svg {
          color: #f05348;
        }
      }
    }
  }
}
.audio-library {
  /deep/ .upload-ops {
    border: 1px solid #2abd90;
    border-radius: 4px;
    color: #2abd90;
    &:hover {
      background: rgba(45, 204, 156, 0.1);
      border: 1px solid #2dcc9c;
    }
    &:active {
      background: rgba(39, 173, 132, 0.1);
      border: 1px solid #27ad84;
    }
  }
  /deep/ .search-ops {
    background-color: 1px solid #2abd90;
    &:hover {
      background-color: #2dcc9c;
    }
    &:active {
      background: #27ad84;
    }
  }
  /deep/ .el-dialog__header {
    height: 0;
    padding: 0;
    display: none;
  }
  /deep/ .el-button--primary {
    background-color: #2abd90;
    // border-color: #2ABD90;
    &:hover {
      background: #2dcc9c;
    }
    &:disabled {
      pointer-events: none;
      background: rgba(42, 189, 144, 0.6);
    }
  }
  /deep/ .dialog-footer {
    margin-right: 8px;
    button {
      height: 32px;
      border-radius: 4px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 16px;
    .audio-container-outer {
      max-height: 384px;
      min-height: 384px;
      overflow-y: auto;
      &::-webkit-scrollbar-track-piece {
        background-color: #fff;
        border-radius: 2em;
      }
      &::-webkit-scrollbar {
        width: 0px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: rgba(95, 101, 113, 1);
        background-clip: padding-box;
        border-radius: 2em;
        border: 4px solid rgba(255, 255, 255, 1);
        width: 0px;
      }
      &:hover &::-webkit-scrollbar {
        width: 6px;
      }
    }
    .audio-container {
      display: flex;
      flex-wrap: wrap;
      // gap: 16px 24px;
      margin-left: 8px;

      overflow: auto;
      ::-webkit-scrollbar-track-piece {
        background-color: #fff;
        border-radius: 2em;
      }
      ::-webkit-scrollbar {
        width: 0px;
      }
      ::-webkit-scrollbar-thumb {
        background-color: rgba(95, 101, 113, 1);
        background-clip: padding-box;
        border-radius: 2em;
        border: 4px solid rgba(255, 255, 255, 1);
        width: 0px;
      }
      &.is-none {
        height: 432px;
        justify-content: center;
        align-content: center;
      }
      .none {
        background-size: 100% 100%;
        width: 80px;
        height: 80px;
        background-image: url("~@/pages/index/assets/<EMAIL>");
        &::after {
          content: "暂无内容";
          display: block;
          text-align: center;
          position: relative;
          top: 100%;
          margin-top: 16px;
          font-size: 14px;
          color: #999999;
          letter-spacing: 0;
          line-height: 14px;
          font-weight: 400;
        }
      }

      .audio-item {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 10px 10px;
        cursor: pointer;

        &-box {
          width: 100px;
          height: 100px;
          background-size: 100%;
          border-radius: 50%;
          background-image: url("~@/assets/img/audio_bg.png");
        }

        &-play {
          animation: rotate 2s linear infinite;
        }

        &-name {
          text-align: center;
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .circle {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 40px;
          height: 40px;
          border-radius: 40px;
          transform: translate(-50%, -50%);
        }

        .el-checkbox {
          position: absolute;
          top: 10px;
          right: 10px;
        }

        .audio-uploader {
          .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }

          .el-upload:hover {
            border-color: #409eff;
          }

          .audio-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 98px;
            height: 98px;
            line-height: 98px;
            text-align: center;
          }
        }

        img {
          border-radius: 6px;
          max-width: 100px;
          height: 100px;
        }
      }
    }

    .el-pagination {
      display: flex;
      justify-content: center;
      margin-top: 16px;
      .el-pager li.active {
        background-color: #2abd90;
        border-color: #2abd90;
      }
      .el-pager li,
      .btn-prev,
      .btn-next {
        height: 32px;
        line-height: 32px;
        width: 32px;
        margin-right: 8px;
        padding: 0;
      }
      .el-pager li.active + li {
        border-left: 1px solid #e1e2e6;
      }
      .el-pager li.active + li:hover {
        border-color: #2abd90;
      }
      .el-pager {
        display: flex;
        // gap: 8px;
      }
      .el-select--mini {
        width: unset;
      }
    }
    input {
      height: 32px !important;
      line-height: 32px !important;
    }
  }
  .footer-tips {
    position: absolute;
    left: 16px;
    font-size: 12px;
    color: #555555;
    line-height: 12px;
    font-weight: 400;
    .num {
      color: #2abd90;
    }
  }
}
</style>
