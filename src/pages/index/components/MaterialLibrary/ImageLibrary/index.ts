import Vue from "vue";
import { CombinedVueInstance } from "vue/types/vue";
import bus from "@/pages/index/common/utils/bus";
import { cloneDeep } from "lodash-es";
import { Message } from "element-ui";
import { checkPicturesSize } from "@/common/utils/resizePic";

type InstanceMethods = {
  onClose?: () => void;
  onConfirm: (images: Array<ImageItem>) => void;
};

type InstanceData = {
  visible: boolean;
  isMultiple: boolean;
  maxHeight?: number;
  maxWidth?: number;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceData,
  InstanceMethods,
  object,
  Record<never, any>
>;

export type ImageLibraryOptions = {
  isMultiple?: boolean;
  maxWidth?: number;
  maxHeight?: number;
  checkSize?: boolean;
} & InstanceMethods;

interface Data {
  compressUrl: string; // gif抽帧后url
  fileUrl: string; // 文件url
  id: string;
  menuId: string | number; //素材库分类记录
  parentId: string | number; //素材库分类记录
  childId: string | number; //素材库分类记录
  picHeight: number; // 高
  picWidth: number; // 宽
  name: string;
  m_id: string;
}

export const ImageLibrary = ({
  onClose,
  onConfirm,
  isMultiple,
  maxWidth,
  maxHeight,
  checkSize
}: ImageLibraryOptions) => {
  if (!(window as MyWindow).materialLibraryReady) {
    Message.info("素材库正在加载...，请稍后再试");
    return;
  }
  bus.$emit("material-library", {
    callback: async ({ type, data }: { type: any, data: Data[] }) => {
      if (!data.length) {
        return;
      }
      if (!['img', 'album'].includes(type)) {
        Message.error(`编辑器不支持${type}类型的资源`);
        return;
      }
      const materialPicList = cloneDeep(data);
      const urlList: string[] = [];
      materialPicList.forEach(async item => {
        const {
          fileUrl: content,
          picHeight: height,
          picWidth: width
        } = item;
        if ((content as string).indexOf(".gif") === -1) {
          // 判断大小
          if (checkSize && (width > Number(maxWidth) || height > Number(maxHeight))) {
            Message.error("图片支持最大尺寸为" + maxWidth + "*" + maxHeight + "");
          } else {
            urlList.push(content);
          }
        } else {
          Message.error("编辑器不支持gif");
        }

      });
      if (urlList.length) {
        const newUrls = await checkPicturesSize(urlList);
        const imgItems = newUrls.map(url => {
          return { url: url };
        });
        newUrls.length > 0 && onConfirm(imgItems);
      }
    },
  });
};
