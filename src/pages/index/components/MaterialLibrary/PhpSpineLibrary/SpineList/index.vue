<template>
  <ul class="spine-list">
    <spine-item
      v-for="(item, index) in list"
      :key="index"
      :name="classify === 'spine' ? item.name : item.title"
      :img-url="classify === 'spine' ? item.cover : item.thumbnail"
      :selected="item.selected"
      :isSyns="item.isSyns"
      :classify="classify"
      @click.native="onClick(item)"
      @clickEdit="onClickEdit(item)"
      @clickDelete="onClickDelete(item)"
    />
  </ul>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import SpineItem from "./SpinItem.vue";

@Component({
  components: {
    SpineItem,
  },
})
export default class SpineList extends Vue {
  @Prop()
  list!: SpineItem[];

  @Prop()
  classify!: string;

  onClick(spine: SpineItem) {
    const { isSyns = 1 } = spine;
    if (isSyns === 0) {
      this.$message.error("没有上传成功，需要重新上传");
    }
    if (isSyns !== 0) {
      this.$emit("click", spine);
    }
  }

  onClickEdit(spine: SpineItem) {
    this.$emit("clickEdit", spine);
  }
  onClickDelete(spine: SpineItem) {
    this.$emit("clickDelete", spine);
  }
}
</script>

<style lang="less" scoped>
.spine-list {
  margin: 10px 0;
  padding: 0;
  list-style: none;
  display: flex;
  flex-wrap: wrap;
}
</style>
