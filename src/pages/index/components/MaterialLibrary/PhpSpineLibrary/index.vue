<template>
  <div class="spine-library">
    <el-dialog
      title="动效库"
      :visible.sync="visible"
      width="880px"
      top="0vh"
      @close="onClose"
    >
      <div class="spine-library-ops">
        <el-input
          class="spine-library-ops-search"
          placeholder="请输入内容"
          prefix-icon="el-icon-search"
          v-model="searchName"
          size="small"
        />
        <el-select
          v-show="classify === 'spine' ? true : false"
          :loading="isFetchingTagList"
          v-model="searchTags"
          multiple
          filterable
          default-first-option
          placeholder="请输入标签"
          size="small"
          class="spine-library-ops-tags"
        >
          <el-option
            v-for="tag in tagList"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
          </el-option>
        </el-select>
        <el-button
          class="spine-library-ops-btn"
          @click="onClickSearch"
          size="small"
        >
          搜索
        </el-button>
        <el-button
          class="spine-library-ops-btn"
          @click="onClickAdd"
          size="small"
        >
          添加
        </el-button>
      </div>
      <div class="spine-library-spine-list">
        <spine-list
          :list="
            classify === 'spine'
              ? spineListWithSelectedStatus
              : cocosAniListWithSelectedStatus
          "
          :classify="classify"
          @click="onClickAni"
          @clickEdit="onClickSpineEdit"
          @clickDelete="onClickSpineDelete"
          v-loading="isSearching"
        />
        <el-pagination
          :total="total"
          :page-size="pageSize"
          :current-page="pageNum"
          @current-change="onPageChange"
          class="spine-library-paginaton"
          layout="prev, pager, next"
          hide-on-single-page
          small
        >
        </el-pagination>
      </div>
      <div slot="footer" class="spine-library-footer">
        <el-button @click="onClickClose" size="small">取 消</el-button>
        <el-button
          @click="onClickConfirm"
          type="primary"
          class="spine-library-footer-btn"
          size="small"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <add-spine-dialog
      :visible.sync="addSpineVisible"
      :tagList="tagList"
      :classify="classify"
      @close="refresh"
      @refresh="refresh"
    />
    <edit-spine-dialog
      v-if="editSpine"
      :visible.sync="editSpineVisible"
      :id="editSpine.id"
      :name="editSpine.name"
      :tags="editSpine.tags"
      :cover="editSpine.cover"
      :tagList="tagList"
      @close="refresh"
      @refresh="refresh"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { deleteSpine, getTagList, searchSpine } from "@/common/api/spine";
import { getCocosAniList } from "@/common/api/cocosAni";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import SpineList from "./SpineList/index.vue";
import AddSpineDialog from "./AddSpineDialog/index.vue";
import EditSpineDialog from "./EditSpineDialog/index.vue";
import bus from "@/pages/index/common/utils/bus";

enum STATUS {
  offline = 0,
  online = 1,
}

@Component({
  components: { SpineList, AddSpineDialog, EditSpineDialog },
})
export default class SpineLibrary extends Vue {
  visible = false;
  addSpineVisible = false;
  isMultiple = false;
  isSearching = false;
  isFetchingTagList = false;

  // search bar
  searchName = "";
  searchTags = [];

  // classify bar
  classify = "";

  //cocos ani list
  cocosAniList: CocosAniItem[] = [];
  selectedCocosAni: CocosAniItem[] = [];

  // spine list
  selectedSpines: SpineItem[] = [];
  spineList: SpineItem[] = [];
  total = 0;
  pageNum = 1;
  pageSize = 12;

  // edit
  editSpineVisible = false;
  editSpine: SpineItem | null = null;

  tagList: { name: string; id: number }[] = [];

  get spineListWithSelectedStatus() {
    return this.spineList.map(spine => ({
      ...spine,
      selected: this.selectedSpines.findIndex(i => i.id === spine.id) > -1,
    }));
  }

  get cocosAniListWithSelectedStatus() {
    return this.cocosAniList.map(spine => ({
      ...spine,
      selected: this.selectedCocosAni.findIndex(i => i.id === spine.id) > -1,
    }));
  }

  @Watch("visible") visibleWatcher(val: boolean) {
    if (val) {
      this.init();
    }
    bus.$emit('dialogVisible', val)
  }

  init() {
    this.searchName = "";
    this.searchTags = [];
    this.tagList = [];
    this.selectedSpines = [];
    this.selectedCocosAni = [];
    const { classify } = this;
    this.classify = classify;
    this.refresh();
  }

  refresh() {
    if (this.classify === "spine") {
      this.getTagList();
      this.search(1);
    }
    if (this.classify === "cocosAni") {
      this.getCocosAniList();
    }
  }

  search(pageNum?: number) {
    if (pageNum) {
      this.pageNum = pageNum;
    }

    const { searchName: name, searchTags: tags } = this;
    this.isSearching = true;
    return searchSpine({
      name,
      tags,
      pageNum: this.pageNum,
      pageSize: this.pageSize,
    })
      .then(res => {
        const { total, list } = res.data.data;
        this.total = total;
        this.spineList = list;
      })
      .catch(showErrorMessage)
      .finally(() => {
        this.isSearching = false;
      });
  }

  getCocosAniList(title?: string) {
    this.isFetchingTagList = true;
    const status = STATUS.online;
    const data = title ? { status, title } : { status };
    const params = Object.assign({}, data, {
      pn: this.pageNum,
      rn: this.pageSize,
    });
    return getCocosAniList({ params })
      .then(res => {
        const { total, list } = res.data.data;
        this.cocosAniList = list;
        this.total = total;
      })
      .catch(showErrorMessage)
      .finally(() => {
        this.isFetchingTagList = false;
      });
  }

  getTagList() {
    this.isFetchingTagList = true;
    return getTagList()
      .then(res => {
        this.tagList = res.data.data;
      })
      .catch(showErrorMessage)
      .finally(() => {
        this.isFetchingTagList = false;
      });
  }

  onClickSearch() {
    if (this.classify === "spine") {
      this.search(1);
    }
    if (this.classify === "cocosAni") {
      this.getCocosAniList(this.searchName);
    }
  }

  onClickAdd() {
    this.addSpineVisible = true;
  }

  onClose() {
    bus.$emit('closeSpine');
  }

  onClickClose() {
    console.log("onClickClose");
    this.visible = false;
  }

  onConfirm(selected: SpineItem[] | CocosAniItem[]) {
    console.log("confirm", selected);
  }

  onClickConfirm() {
    this.visible = false;
    const select =
      this.classify === "spine" ? this.selectedSpines : this.selectedCocosAni;
    this.onConfirm(select);
  }

  onPageChange(currentPage: number) {
    this.pageNum = currentPage;
    if (this.classify === "spine") {
      this.search();
    }
    if (this.classify === "cocosAni") {
      this.getCocosAniList();
    }
  }

  onClickAni(spine: SpineItem) {
    switch (this.classify) {
      case "spine":
        this.onClickSpine(spine);
        break;
      case "cocosAni":
        this.onClickCocosAni(spine);
        break;
    }
  }

  onClickSpine(spine: SpineItem) {
    const { selectedSpines, isMultiple, classify } = this;
    const index = selectedSpines.findIndex(item => item.id === spine.id);
    const isExisting = index > -1;
    if (!isMultiple) {
      this.selectedSpines = [spine];
      return;
    }
    if (isExisting) {
      selectedSpines.splice(index, 1);
      return;
    }
    selectedSpines.push(spine);
  }

  onClickCocosAni(spine: SpineItem) {
    const { selectedCocosAni, isMultiple, classify } = this;
    const index = selectedCocosAni.findIndex(item => item.id === spine.id);
    const isExisting = index > -1;
    if (!isMultiple) {
      this.selectedCocosAni = [spine];
      return;
    }
    if (isExisting) {
      selectedCocosAni.splice(index, 1);
      return;
    }
    selectedCocosAni.push(spine);
  }

  onClickSpineEdit(spine: SpineItem) {
    this.editSpine = spine;
    this.editSpineVisible = true;
  }

  async onClickSpineDelete(spine: SpineItem) {
    await deleteSpine(spine.id)
      .then(res => {
        console.log('code', res);
        this.$message.success("删除成功");
        this.refresh();
      })
      .catch(showErrorMessage);
  }
}
</script>

<style lang="less" scoped>
.spine-library {
  &-ops {
    &-search {
      width: 200px;
    }

    &-tags {
      margin-left: 16px;
    }

    &-btn {
      margin-left: 20px;
    }
  }

  &-spine-list {
    padding-top: 10px;
  }

  &-paginaton {
    display: flex;
    justify-content: flex-end;
  }

  &-footer {
    display: flex;
    justify-content: center;

    &-btn {
      margin-left: 40px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 10px 40px;
  }
}
</style>
