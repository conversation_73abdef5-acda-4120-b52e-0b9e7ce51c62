<template>
  <el-dialog :visible.sync="dialogVisible" title="编辑动效" width="500px">
    <el-form
      :model="formData"
      :rules="rules"
      ref="form"
      label-width="100px"
      class="add-spine-form"
    >
      <el-form-item label="名称" prop="name">
        <el-input
          v-model="formData.name"
          size="mini"
          placeholder="请输入名称"
        />
      </el-form-item>
      <el-form-item label="标签" prop="tags">
        <el-select
          v-model="formData.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="请输入标签"
          size="mini"
        >
          <el-option
            v-for="tag in tagList"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="封面图" prop="cover">
        <el-upload
          ref="upload"
          action=""
          :file-list="formData.cover"
          :on-change="onCoverChange"
          :show-file-list="false"
          :auto-upload="false"
          accept="image/*"
        >
          <div
            v-if="formData.cover.length || cover"
            class="add-spine-form-cover"
          >
            <img
              :src="coverUrl || cover"
              class="add-spine-form-cover-preview"
            />
          </div>
          <el-button v-else slot="trigger" size="mini" type="primary"
            >选取文件</el-button
          >
        </el-upload>
      </el-form-item>
    </el-form>
    <div slot="footer" class="add-spine-footer">
      <el-button @click="onClickClose" size="small">取 消</el-button>
      <el-button
        @click="onClickConfirm"
        type="primary"
        class="add-spine-footer-btn"
        size="small"
        >确 定</el-button
      >
    </div>
  </el-dialog>
</template>
<script lang="ts">
import { editSpine } from "@/common/api/spine";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { ElForm } from "element-ui/types/form";
import { Component, Prop, Vue } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";

@Component
export default class EditSpineDialog extends Vue {
  @Prop()
  visible!: boolean;

  @Prop()
  id!: number;

  @Prop()
  name!: string;

  @Prop()
  cover!: string;

  @Prop()
  tags!: { id: number; name: string }[];

  @Prop()
  tagList!: { name: string; id: number }[];

  formData: {
    name: string;
    tags: number[];
    cover: { raw: File }[];
  } = { name: "", tags: [], cover: [] };

  rules = {
    name: [
      { required: true, message: "请输入动效名称", trigger: "blur" },
      { min: 2, max: 20, message: "长度在 2 到 20 个字符", trigger: "blur" },
    ],
    tags: [
      {
        type: "array",
        required: true,
        message: "请至少输入一个标签",
        trigger: "change",
      },
    ],
  };

  get dialogVisible() {
    this.formData.name = this.name;
    this.formData.tags = this.tags.map(tag => tag.id);
    return this.visible;
  }

  set dialogVisible(val) {
    (this.$refs.form as ElForm).resetFields();
    this.$emit("update:visible", val);
    this.$emit("close");
    bus.$emit('dialogVisible', val)

  }

  get coverUrl() {
    if (!this.formData.cover.length) {
      return undefined;
    }
    return URL.createObjectURL(this.formData.cover[0].raw);
  }

  mounted() {
    this.formData.name = this.name;
    this.formData.tags = this.tags.map(tag => tag.id);
  }

  onClickClose() {
    this.dialogVisible = false;
  }

  async onClickConfirm() {
    const valid = await (this.$refs.form as ElForm).validate();
    if (!valid) {
      return;
    }
    const formData = new FormData();

    formData.append("id", String(this.id));
    formData.append("name", this.formData.name);
    formData.append("tags", JSON.stringify(this.formData.tags));
    if (this.formData.cover.length) {
      formData.append("cover", this.formData.cover[0].raw);
    }
    return editSpine(formData)
      .then(() => {
        this.$message.success("修改成功");
        this.dialogVisible = false;
      })
      .catch(showErrorMessage);
  }

  onCoverChange($event: any) {
    const { formData } = this;
    formData.cover.pop();
    formData.cover.push($event);
  }
}
</script>

<style lang="less" scoped>
.add-spine {
  &-form {
    width: 300px;

    &-cover {
      &-preview {
        width: 80px;
      }
    }
  }

  &-footer {
    display: flex;
    justify-content: center;

    &-btn {
      margin-left: 30px;
    }
  }
}
</style>
