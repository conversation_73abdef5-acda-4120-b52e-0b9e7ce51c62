import {
  Direction,
  ShakeDirections,
} from "@/pages/index/store/modules/animations";

export enum AnimType {
  FLY_INTO = 1, // 飞入
  FLY_OUT = 2, // 飞出
  APPEAR = 3, // 出现
  DISAPPEAR = 4, // 消失
  SCALE = 5, // 放大缩小
  ROTATION = 6, // 旋转
  MOVE_LINE = 7, // 直线移动
  MOVE_BEZIRE = 8, // 贝塞尔曲线移动
  CUSTOM_PATH = 9, // 自定义路径
  CUSTOM_CURVE = 10, // 自定义曲线
  APPEAR_OPACITY = 11, // 进入（透明度）
  DISAPPEAR_OPACITY = 12, // 退出（透明度）
  SHAKE = 13, // 抖动
  BLINK = 14, // 闪烁
  SPINE = 15, // spine 动画
  COCOSANI = 16, // cocos 动画

  // 思维
  SIWEI_SIZE_CHANGE = 100, // 思维 元素大小变化
  SIWEI_SHAKE_X = 101, // 思维 左右抖动
  SIWEI_SHAKE_Y = 102, // 思维 上下抖动
  SIWEI_REPEATEDLY = 103, // 思维 出现又消失
  SIWEI_AFTER_APPEAR = 104, // 思维 出现后左右抖动
  SIWEI_STAGNATION = 105, // 思维 停滞
  SIWEI_APPEAR = 106, // 思维 出现
  SIWEI_DISAPPEAR = 107, // 思维 消失
  SIWEI_MOVE_LINE = 108, // 思维 直线移动
}

export const siweiAnims = {
  siweiSizeChange: {
    name: "大小变化",
    type: AnimType.SIWEI_SIZE_CHANGE,
    relative: true,
  },
  siweiShakeX: {
    name: "左右抖动",
    type: AnimType.SIWEI_SHAKE_X,
    relative: true,
  },
  siweiShakeY: {
    name: "上下抖动",
    type: AnimType.SIWEI_SHAKE_Y,
    relative: true,
  },
  siweiRepeatedly: {
    name: "出现又消失",
    type: AnimType.SIWEI_REPEATEDLY,
    relative: true,
  },
  siweiAfterAppear: {
    name: "出现后左右抖动",
    type: AnimType.SIWEI_AFTER_APPEAR,
    relative: true,
  },
  siweiStagnation: {
    name: "停滞",
    type: AnimType.SIWEI_STAGNATION,
    relative: true,
  },
  siweiAppear: {
    name: "出现",
    type: AnimType.SIWEI_APPEAR,
    relative: true,
  },
  siweiDisappear: {
    name: "消失",
    type: AnimType.SIWEI_DISAPPEAR,
    relative: true,
  },
  siweiMoveLine: {
    name: "直线移动",
    type: AnimType.SIWEI_MOVE_LINE,
    relative: false,
  },
};

export const anims = {
  ...siweiAnims,
  appear: {
    name: "出现（放大）",
    type: AnimType.APPEAR,
    relative: true,
  },
  disappear: {
    name: "消失（缩小）",
    type: AnimType.DISAPPEAR,
    relative: true,
  },
  flyIn: {
    name: "飞入",
    type: AnimType.FLY_INTO,
    option: Direction.TOP_TO_BOTTOM,
    relative: true,
  },
  moveLine: {
    name: "直线移动",
    type: AnimType.MOVE_LINE,
    relative: true,
  },
  scale: {
    name: "缩放",
    type: AnimType.SCALE,
    relative: true,
    option: {
      start: 0.5, // 开始
      end: 0.5, // 结束
    },
  },
  flyOut: {
    name: "飞出",
    option: 0,
    type: AnimType.FLY_OUT,
    relative: true,
  },
  moveBezire: {
    name: "贝塞尔曲线",
    type: AnimType.MOVE_BEZIRE,
    relative: true,
  },
  customPath: {
    name: "自定义路径",
    type: AnimType.CUSTOM_PATH,
    relative: true,
  },
  customCurve: {
    name: "自定义曲线",
    type: AnimType.CUSTOM_CURVE,
    relative: true,
  },
  rotate: {
    name: "旋转",
    type: AnimType.ROTATION,
    option: {
      start: 0, // 开始
      end: 360, // 结束
    },
    relative: true,
  },
  appearOpacity: {
    name: "出现（透明度）",
    type: AnimType.APPEAR_OPACITY,
    relative: true,
  },
  disappearOpacity: {
    name: "消失（透明度）",
    type: AnimType.DISAPPEAR_OPACITY,
    relative: true,
  },
  shake: {
    name: "抖动",
    type: AnimType.SHAKE,
    option: ShakeDirections.VERTICAL,
    relative: true,
  },
  blink: {
    name: "闪烁",
    type: AnimType.BLINK,
    relative: true,
    option: {
      start: 0, // 开始
      end: 255, // 结束
    },
  },
  spine: {
    name: "骨骼动画",
    type: AnimType.SPINE,
    animName: "", // 需要播放的骨骼动画名称
    timeScale: 1, // 播放速率
  },

  cocosAni: {
    name: "cocos动画",
    type: AnimType.COCOSANI,
    animName: "", // 需要播放的cocos动画名称
    timeScale: 1, // 播放速率
  },
};

export const enterOptions = {
  value: "enter",
  label: "进入",
  animType: "tween",
  children: [
    {
      value: "flyIn",
      label: anims.flyIn.name,
      data: anims.flyIn,
    },
    {
      value: "appear",
      label: anims.appear.name,
      data: anims.appear,
    },
    {
      value: "appearOpacity",
      label: anims.appearOpacity.name,
      data: anims.appearOpacity,
    },
  ],
};

export const exitOptions = {
  value: "exit",
  label: "退出",
  animType: "tween",
  children: [
    {
      value: "flyOut",
      label: anims.flyOut.name,
      data: anims.flyOut,
    },
    {
      value: "disappear",
      label: anims.disappear.name,
      data: anims.disappear,
    },
    {
      value: "disappearOpacity",
      label: anims.disappearOpacity.name,
      data: anims.disappearOpacity,
    },
  ],
}

export const moveOptions = {
  value: "move",
  label: "移动",
  animType: "tween",
  children: [
    {
      value: "moveLine",
      label: anims.moveLine.name,
      data: anims.moveLine,
    },
    // {
    //   value: "moveBezire",
    //   label: anims.moveBezire.name,
    //   data: anims.moveBezire,
    // },
    {
      value: "customPath",
      label: anims.customPath.name,
      data: anims.customPath,
    },
    {
      value: "customCurve",
      label: anims.customCurve.name,
      data: anims.customCurve,
    },
  ],
};

// 思维动画选项
export const siweiOptions = {
  value: "siwei",
  label: "思维",
  animType: "tween",
  children: (() => {
    const result: Record<string, any> = [];
    for (const key in siweiAnims) {
      if (Object.prototype.hasOwnProperty.call(siweiAnims, key)) {
        result.push({
          value: key,
          label: siweiAnims[key as keyof typeof siweiAnims].name,
          data: siweiAnims[key as keyof typeof siweiAnims],
        });
      }
    }
    return result;
  })(),
};

// spine 动画选项
export const spineOptions = {
  value: "spine",
  label: "骨骼动画",
  animType: "spine",
  children: [
    {
      value: "spine",
      label: anims.spine.name,
      data: anims.spine,
    },
  ],
};

// cocos 动画选项
export const cocosAniOptions = {
  value: "cocosAni",
  label: "cocos动画",
  animType: "cocosAni",
  children: [
    {
      value: "cocosAni",
      label: anims.cocosAni.name,
      data: anims.cocosAni,
    },
  ],
};

export const siweiTypeAnimTypes = siweiOptions.children.map(
  (i: any) => i.data.type,
);

export const isSiweiTypeAnim = (type: AnimType) =>
  siweiTypeAnimTypes.includes(type);

// 进入类型的 action type 数组
export const enterTypeAnimTypes = [
  ...enterOptions.children.map(i => i.data.type),
  AnimType.SIWEI_APPEAR,
  AnimType.SIWEI_REPEATEDLY,
  AnimType.SIWEI_AFTER_APPEAR,
];

export const isEnterTypeAnim = (type: AnimType) =>
  enterTypeAnimTypes.includes(type);

// 退出类型的 action type 数组
export const exitTypeAnimTypes = [
  ...exitOptions.children.map(i => i.data.type)
];
export const isExitTypeAnim = (type: AnimType) =>
  exitTypeAnimTypes.includes(type);

// 允许编辑relative属性的action type
export const canEditRelativeAnimTypes = [
  AnimType.ROTATION,
  AnimType.MOVE_LINE,
  AnimType.MOVE_BEZIRE,
  AnimType.CUSTOM_PATH,
  AnimType.CUSTOM_CURVE,
];

export const options = [
  enterOptions,
  {
    value: "emphasis",
    label: "强调",
    animType: "tween",
    children: [
      {
        value: "scale",
        label: anims.scale.name,
        data: anims.scale,
      },
      {
        value: "rotate",
        label: anims.rotate.name,
        data: anims.rotate,
      },
      {
        value: "blink",
        label: anims.blink.name,
        data: anims.blink,
      },
      {
        value: "shake",
        label: anims.shake.name,
        data: anims.shake,
      },
    ],
  },
  exitOptions,
  moveOptions,
  // siweiOptions,
  spineOptions,
  cocosAniOptions,
];

export const CustomSpeeds: Partial<Record<AnimType, number>> = {
  [AnimType.SIWEI_SIZE_CHANGE]: 0.27,
  [AnimType.SIWEI_SHAKE_X]: 0.4,
  [AnimType.SIWEI_REPEATEDLY]: 0.83,
  [AnimType.SIWEI_AFTER_APPEAR]: 1.03,
  [AnimType.SIWEI_SHAKE_Y]: 0.83,
  [AnimType.SIWEI_STAGNATION]: 0.33,
  [AnimType.SIWEI_APPEAR]: 0.33,
  [AnimType.SIWEI_DISAPPEAR]: 0.33,
};

export default options;
