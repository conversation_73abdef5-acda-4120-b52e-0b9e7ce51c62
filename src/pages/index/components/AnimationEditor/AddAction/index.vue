<template>
  <div class="add-action">
    <div class="add-action-effect">
      <el-row class="add-action-effect-row">
        <el-col v-if="activeActionId && activeAction" :span="10" class="add-action-effect-row-effect-name"> 动画效果：{{ activeAction.value.anim.name }} </el-col>
        <el-col :span="activeActionId ? 5 : 10" style="position: relative;">
          <el-button :disabled="!currentIds.length" size="mini" @click="actionlistVisible = !actionlistVisible">{{ activeActionId ? "更改" : "添加动画效果" }}</el-button>
          <ul v-if="actionlistVisible" class="add-action-list" ref="addActionList">
            <li v-for="item in options" :key="item.value" :class="['add-action-list-item', { 'has-child': item.children && item.children.length }]">
              <div>{{ item.label }}</div>
              <ul v-if="item.children && item.children.length" class="add-action-sub-list">
                <li v-for="subItem in item.children" :key="subItem.value" class="add-action-sub-list-item" @click="onClickAction(item, subItem)">
                  <div>{{ subItem.label }}</div>
                </li>
              </ul>
            </li>
          </ul>
        </el-col>
        <el-col v-if="activeActionId" :span="5">
          <el-button v-if="activeActionId" size="mini" @click="removeAction(activeActionId)">删除</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { v4 as uuidv4 } from "uuid";
import { cloneDeep } from "lodash-es";
import { Component, Vue } from "vue-property-decorator";
import draggable from "vuedraggable";
import { AfterType, AnimationAction, Moment, SpineAnimationAction, CocosAnimationAction, types } from "@/pages/index/store/modules/animations";
import { AnimType, CustomSpeeds, options } from "./options";

@Component({
  components: {
    draggable,
  },
})
export default class AddAction extends Vue {
  actionlistVisible = false;

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get options() {
    const { currentIds } = this;
    const { componentMap } = this.$store.state;
    const { activeAction } = this;
    let optionsNew = options;

    if (activeAction) {
      const { animType } = activeAction.value;

      // 不允许不同 animType 之间的动画互相切换，降低 updateAction 的复杂度
      return options.filter(option => option.animType === animType);
    }

    // 非 spine 组件 过滤掉 spine 动画选项
    if (
      currentIds.find(id => {
        return componentMap[id].type !== "spine";
      })
    ) {
      optionsNew = optionsNew.filter(option => option.animType !== "spine");
    }

    // 非 cocos 组件 过滤掉 cocos 动画选项
    if (
      currentIds.find(id => {
        return componentMap[id].type !== "cocosAni";
      })
    ) {
      optionsNew = optionsNew.filter(option => option.animType !== "cocosAni");
    }
    return optionsNew;
  }

  get currentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get animationVal(): string {
    return this.$store.state.moduleAnimations.animationVal;
  }

  get fragmentId(): string {
    return this.$store.state.moduleAnimations.fragmentId;
  }

  get activeActionId(): string {
    return this.$store.state.moduleAnimations.activeActionId;
  }

  set activeActionId(val) {
    this.$store.commit(types.mutations.setActiveActionId, val);
  }

  get activeAction(): AnimationAction | SpineAnimationAction | CocosAnimationAction | undefined {
    return this.$store.getters["moduleAnimations/activeAction"];
  }

  get componentIdsAllObjectType(): { id: string; subIds: string[] }[] {
    return this.$store.getters.componentIdsAllObjectType;
  }

  mounted() {
    document.addEventListener("mousedown", this.onClickAway, true);
  }

  destroyed() {
    document.removeEventListener("mousedown", this.onClickAway), true;
  }

  onClickAway(e: MouseEvent) {
    if (!this.actionlistVisible || !this.$refs.addActionList) {
      return;
    }
    if (!(this.$refs.addActionList as HTMLElement).contains(e.target as Node)) {
      this.actionlistVisible = false;
    }
  }

  onClickAction(
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    const copySubItem = cloneDeep(subItem);
    this.actionlistVisible = false;
    if (this.activeActionId) {
      this.updateActionConfigData(item, copySubItem);
      return;
    }
    const { currentIds } = this;
    currentIds.forEach(id => {
      const valid = this.componentIdsAllObjectType.some(obj => obj.id === id);
      if (valid) {
        this.addAction(id, item, copySubItem);
      } else {
        this.$message.error("子组件不允许添加动画");
      }
    });
  }

  updateActionConfigData(
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    this.$store.commit(types.mutations.updateActionConfigData, {
      type: item.animType,
      data: subItem.data,
      name: item.label,
    });
    const customSpeed = CustomSpeeds[subItem.data.type as AnimType];
    if (customSpeed) {
      this.$store.commit(types.mutations.setActionSpeed, {
        speed: customSpeed,
      });
    }
  }

  addAction(
    componentId: string,
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    const { fragmentId, animationVal } = this;

    switch (item.animType) {
      case "tween": {
        const action: AnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            speed: CustomSpeeds[subItem.data.type as AnimType] || 2,
            repeat: 0,
            anim: cloneDeep(subItem.data),
          },
        };
        this.$store.commit(types.mutations.addAction, {
          animationVal,
          fragmentId,
          action,
        });
        this.activeActionId = action.id;
        break;
      }

      case "spine": {
        const action: SpineAnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          delay: 0,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            loop: false,
            animList: [],
            endTimeScale: 1,
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            anim: cloneDeep(subItem.data),
          },
        };
        this.$store.commit(types.mutations.addAction, {
          animationVal,
          fragmentId,
          action,
        });
        this.activeActionId = action.id;
        break;
      }

      case "cocosAni": {
        const action: CocosAnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          delay: 0,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            loop: false,
            animList: [],
            endTimeScale: 1,
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            anim: cloneDeep(subItem.data),
          },
        };
        this.$store.commit(types.mutations.addAction, {
          animationVal,
          fragmentId,
          action,
        });
        this.activeActionId = action.id;
        break;
      }

      default: {
        break;
      }
    }
  }

  removeAction(actionId: string) {
    const { animationVal, fragmentId } = this;
    const animation = this.$store.state.moduleAnimations.animations[animationVal];
    const fragment = animation.fragments[fragmentId];
    const index = fragment.findIndex((action: AnimationAction) => action.id === actionId);
    this.activeActionId = "";
    this.$store.commit(types.mutations.removeAction, {
      animationVal,
      fragmentId,
      index,
    });
  }
}
</script>

<style scoped lang="less">
.add-action {
  position: relative;

  &-effect-row {
    margin-bottom: 10px;
    font-size: 14px;
    text-align: left;
    display: flex;
    align-items: center;

    &-effect-name {
      font-weight: bold;
      font-size: 15px;
    }
  }

  &-list {
    margin: 0;
    position: absolute;
    top: 100%;
    left: 20%;
    z-index: 10;
    padding: 5px 0;
    list-style: none;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    user-select: none;

    &-item {
      position: relative;
      padding: 0px 20px 0 10px;
      white-space: nowrap;
      line-height: 20px;
      min-width: 40px;

      &:hover {
        background: #f9f9f9;
      }

      &:hover .add-action-sub-list {
        display: block;
        bottom: -5px;
        left: 100%;
      }
    }
  }

  &-sub-list {
    background: #fff;
    position: absolute;
    display: none;
    list-style: none;
    padding: 5px 0;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    &-item {
      padding: 0 10px;
      white-space: nowrap;
      line-height: 20px;
      min-width: 40px;
      cursor: pointer;

      &:hover {
        background: #f9f9f9;
      }
    }
  }
}

.has-child::after {
  content: "";
  width: 0;
  height: 0;
  border-width: 4px 7px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.75);
  text-shadow: 0 4px 9px rgba(0, 0, 0, 0.34);
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
</style>
