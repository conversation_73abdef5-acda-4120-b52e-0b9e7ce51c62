export const easingOptions = [
  { value: "quadIn", label: "平方曲线缓入" },
  { value: "quadOut", label: "平方曲线缓出" },
  { value: "quadInOut", label: "平方曲线缓入缓出" },
  { value: "cubicIn", label: "立方曲线缓入" },
  { value: "cubicOut", label: "立方曲线缓出" },
  { value: "cubicInOut", label: "立方曲线缓入缓出" },
  { value: "quartIn", label: "四次方曲线缓入" },
  { value: "quartOut", label: "四次方曲线缓出" },
  { value: "quartInOut", label: "四次方曲线缓入缓出" },
  { value: "quintIn", label: "五次方曲线缓入" },
  { value: "quintOut", label: "五次方曲线缓出" },
  { value: "quintInOut", label: "五次方曲线缓入缓出" },
  { value: "sineIn", label: "正弦曲线缓入" },
  { value: "sineOut", label: "正弦曲线缓出" },
  { value: "sineInOut", label: "正弦曲线缓入缓出" },
  { value: "expoIn", label: "指数曲线缓入" },
  { value: "expoOut", label: "指数曲线缓出" },
  { value: "expoInOut", label: "指数曲线缓入和缓出" },
  { value: "circIn", label: "循环公式缓入" },
  { value: "circOut", label: "循环公式缓出" },
  { value: "circInOut", label: "指数曲线缓入缓出" },
  { value: "elasticIn", label: "弹簧回震效果的缓入" },
  { value: "elasticOut", label: "弹簧回震效果的缓出" },
  { value: "elasticInOut", label: "弹簧回震效果的缓入缓出" },
  { value: "backIn", label: "回退效果的缓入" },
  { value: "backOut", label: "回退效果的缓出" },
  { value: "backInOut", label: "回退效果的缓入缓出" },
  { value: "bounceIn", label: "弹跳效果的缓入" },
  { value: "bounceOut", label: "弹跳效果的缓出" },
  { value: "bounceInOut", label: "弹跳效果的缓入缓出" },
  { value: "smooth", label: "平滑效果" },
  { value: "fade", label: "渐褪效果" }
];
