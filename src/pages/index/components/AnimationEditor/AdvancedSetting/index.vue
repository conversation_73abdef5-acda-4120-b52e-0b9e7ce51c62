<template>
  <el-dialog
    title="高级设置"
    :visible.sync="dialogVisible"
    class="advanced-setting"
    width="460px"
  >
    <template v-if="activeAction">
      <spine-action-advanced-setting
        v-if="activeAction.value.animType === 'spine'"
      />
      <cocos-ani-action-advanced-setting
        v-if="activeAction.value.animType === 'cocosAni'"
      />
      <tween-action-advanced-setting
        v-if="activeAction.value.animType === 'tween'"
      />
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import {
  AnimationAction,
  SpineAnimationAction,
  CocosAnimationAction,
} from "@/pages/index/store/modules/animations";
import TweenActionAdvancedSetting from "./TweenActionAdvanceSetting/index.vue";
import SpineActionAdvancedSetting from "./SpineActionAdvanceSetting/index.vue";
import CocosAniActionAdvancedSetting from "./CocosAniActionAdvanceSetting/index.vue";
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: {
    TweenActionAdvancedSetting,
    SpineActionAdvancedSetting,
    CocosAniActionAdvancedSetting,
  },
})
export default class AdvancedSetting extends Vue {
  @Prop({ default: true })
  visible!: boolean;

  @Watch('visible')
  dialogVisibleChanged(val: boolean) {
    bus.$emit('dialogVisible', val)
  }

  get dialogVisible() {
    return this.visible;
  }

  set dialogVisible(val) {
    this.$emit("update:visible", val);
  }

  get activeAction():
    | AnimationAction
    | SpineAnimationAction
    | CocosAnimationAction {
    return this.$store.getters["moduleAnimations/activeAction"];
  }
}
</script>

<style scoped lang="less">
.advanced-setting {
  /deep/ .el-dialog__body {
    padding-top: 0;
  }
}
</style>
