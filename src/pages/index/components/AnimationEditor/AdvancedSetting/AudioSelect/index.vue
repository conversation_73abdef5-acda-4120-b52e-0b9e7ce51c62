<template>
  <audio-select :value.sync="v" />
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { types, AnimationAction } from "@/pages/index/store/modules/animations";

@Component({
  components: { AudioSelect },
})
export default class AdvancedSettingAudioSelect extends Vue {
  get activeAction(): AnimationAction {
    return this.$store.getters["moduleAnimations/activeAction"];
  }

  get v() {
    return this.activeAction.audio.url;
  }

  set v(val) {
    this.$store.commit(types.mutations.setActionAudioUrl, { url: val });
  }
}
</script>
