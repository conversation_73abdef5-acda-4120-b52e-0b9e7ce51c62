<template>
  <div class="advanced-setting">
    <div class="advanced-setting-title">音效</div>
    <div>
      <el-row class="advanced-setting-row" style="margin-bottom: 0">
        <el-col :span="5" class="advanced-setting-row-label">
          音频
        </el-col>
        <el-col :span="16" :offset="1">
          <advanced-setting-audio-select />
        </el-col>
      </el-row>
      <el-row class="advanced-setting-row">
        <el-col :span="5" class="advanced-setting-row-label">
          播放时机
        </el-col>
        <el-col :span="12" :offset="1">
          <el-select v-model="audioMoment" size="mini">
            <el-option
              v-for="item in momentOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row class="advanced-setting-row">
        <el-col :span="5" class="advanced-setting-row-label">
          延迟
        </el-col>
        <el-col :span="8" :offset="1">
          <el-input-number
            v-model="audioDelay"
            controls-position="right"
            type="number"
            :min="0"
            :step="0.1"
            size="mini"
          />
        </el-col>
        秒
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import {
  AnimationAction,
  momentOptions,
  types,
} from "@/pages/index/store/modules/animations";
import { Component, Vue } from "vue-property-decorator";
import AdvancedSettingAudioSelect from "../AudioSelect/index.vue";

@Component({
  components: {
    AdvancedSettingAudioSelect,
  },
})
export default class AudioAdvanceSetting extends Vue {
  momentOptions = momentOptions;

  get activeAction(): AnimationAction {
    return this.$store.getters["moduleAnimations/activeAction"];
  }

  /**
   * @description 延迟播放时间
   */
  get audioDelay() {
    return this.activeAction.audio.delay;
  }
  set audioDelay(s) {
    if (Number(s) === this.audioDelay) return;
    this.$store.commit(types.mutations.setActionAudioDelay, {
      delay: Number(s),
    });
  }

  /**
   * @description 音频播放时机
   */
  get audioMoment() {
    return this.activeAction.audio.moment;
  }
  set audioMoment(val) {
    this.$store.commit(types.mutations.setActionAudioMoment, { moment: val });
  }
}
</script>

<style lang="less" scoped>
.advanced-setting {
  &-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
    margin-bottom: 10px;
  }

  &-row {
    line-height: 30px;
    font-size: 14px;
    margin-bottom: 10px;

    &-label {
      text-align: right;
    }
  }
}
</style>
