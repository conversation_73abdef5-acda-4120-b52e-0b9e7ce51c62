<template>
  <div class="spine-action-editor">
    <el-row class="spine-action-editor-row">
      <el-col :span="6">
        播放时机
      </el-col>
      <el-col :span="12">
        <el-select v-model="moment" size="mini">
          <el-option
            v-for="item in momentOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>

    <el-row class="spine-action-editor-row">
      <el-col :span="6">
        延迟
      </el-col>
      <el-input-number
        controls-position="right"
        v-model="delay"
        type="number"
        :min="0"
        :step="0.1"
        size="mini"
      />
      秒
    </el-row>

    <el-row class="spine-action-editor-row">
      <el-col :span="6">
        动画名称
      </el-col>

      <el-select
        :value="animName"
        size="mini"
        :loading="isFetching"
        @change="animName = $event"
      >
        <el-option key="void" value="">
          无动画
        </el-option>
        <el-option v-for="i in animationOptions" :key="i" :value="i">
          {{ i }}
        </el-option>
      </el-select>
    </el-row>

    <el-row class="spine-action-editor-row">
      <el-col :span="6">
        播放速率
      </el-col>
      <el-input-number
        v-model="timeScale"
        size="small"
        type="number"
        controls-position="right"
        :min="0.1"
        :step="0.1"
      />
    </el-row>

    <el-row class="spine-action-editor-row">
      <el-col :span="6">
        结束效果
      </el-col>
      <el-col :span="7">
        <el-select v-model="afterType" size="mini">
          <el-option
            v-for="item in afterEffectOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import {
  AfterType,
  SpineAnimationAction,
} from "@/pages/index/store/modules/animations";
import { types, momentOptions } from "@/pages/index/store/modules/animations";

/**
 * @description spine 动画属性编辑
 */
@Component
export default class SpineActionEditor extends Vue {
  get activeAction(): SpineAnimationAction {
    return this.$store.getters["moduleAnimations/activeAction"];
  }

  get momentOptions() {
    return momentOptions;
  }

  get component(): SpineComponent {
    return this.$store.state.componentMap[this.activeAction.componentId];
  }

  get afterEffectOptions() {
    return [
      {
        label: "无",
        value: AfterType.NONE,
      },
      {
        label: "隐藏",
        value: AfterType.HIDE,
      },
    ];
  }

  get afterType() {
    return this.activeAction?.after.type;
  }

  set afterType(val) {
    this.$store.commit(types.mutations.setActionAfterType, {
      afterType: val,
    });
  }

  /**
   * @description 获取骨骼动画选项逻辑
   */
  animationOptions: string[] = []; // 骨骼动画选项
  isFetching = false;
  get skeletonUrl() {
    return this.component.spineData.skeleton;
  }
  @Watch("skeletonUrl", {
    immediate: true,
  })
  skeletonUrlWatcher(url: string) {
    url && this.fetchAnimation(url);
  }
  // 获取动画队列选项
  fetchAnimation(url: string) {
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations);
        this.animationOptions = animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }

  /**
   * @description 播放时机
   */
  get moment() {
    return this.activeAction.moment;
  }
  set moment(val) {
    this.$store.commit(types.mutations.setActionMoment, {
      moment: val,
    });
  }

  /**
   * @description 延迟时长
   */
  get delay() {
    return this.activeAction.delay;
  }
  set delay(s) {
    if (Number(s) === this.delay) return;
    this.$store.commit(types.mutations.setActionDelay, {
      delay: Number(s),
    });
  }

  /**
   * @description 动画名称
   */
  get animName() {
    return this.activeAction.value.anim.animName;
  }
  set animName(animName: string) {
    if (animName === this.animName) return;

    this.$store.commit(types.mutations.setSpineAnimName, animName);
  }

  /**
   * @description 播放速率
   */
  get timeScale() {
    return this.activeAction.value.anim.timeScale;
  }
  set timeScale(timeScale: number) {
    if (timeScale === this.timeScale) return;

    this.$store.commit(types.mutations.setSpineTimeScale, timeScale);
  }
}
</script>

<style lang="less" scoped>
.spine-action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}
</style>
