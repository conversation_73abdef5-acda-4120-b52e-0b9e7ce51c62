<template>
  <el-row :class="['move-editor-row', { disabled: !currPoint }]">
    <el-col :span="6">
      坐标
    </el-col>
    <template v-if="currPoint">
      <el-col :span="8" style="display:flex; align-items: center">
        x
        <el-input-number
          v-model="x"
          type="number"
          size="mini"
          controls-position="right"
        />
      </el-col>
      <el-col :span="8" :offset="1" style="display:flex; align-items: center">
        y
        <el-input-number
          v-model="y"
          type="number"
          size="mini"
          controls-position="right"
        />
      </el-col>
    </template>
    <template v-else>
      <el-col :span="8" style="display:flex; align-items: center">
        x
        <el-input-number type="number" size="mini" controls-position="right" />
      </el-col>
      <el-col :span="8" :offset="1" style="display:flex; align-items: center">
        y
        <el-input-number type="number" size="mini" controls-position="right" />
      </el-col>
    </template>
  </el-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";
import { AnimationAction, types } from "@/pages/index/store/modules/animations";
import {
  convertToLeftTopCoordinate,
  convertToCenterCoordinate
} from "@/pages/index/common/utils/coordinate";

@Component
export default class MoveEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get option() {
    return this.action?.value.anim.option || {};
  }

  get stageWidth() {
    return this.$store.state.template.stage.safeWidth;
  }

  get stageHeight() {
    return this.$store.state.template.stage.safeHeight;
  }

  get currIndex() {
    return this.option.currIndex;
  }

  get currPoint() {
    if (this.currIndex === undefined) {
      return null;
    }
    return this.option.points[this.currIndex];
  }

  get x() {
    const point = this.currPoint;
    const x = this.convertToLeftTopCoordinate(point.x, 0).x;
    return x;
  }

  set x(val) {
    if (!this.action?.value.anim.option) {
      return;
    }
    if (this.x === val) return;

    const newPoints = cloneDeep(this.option.points);
    newPoints[this.currIndex].x = this.convertToCenterCoordinate(val, 0).x;
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        ...this.option,
        points: newPoints
      }
    });
  }

  get y() {
    const point = this.currPoint;
    const y = this.convertToLeftTopCoordinate(0, point.y).y;
    return y;
  }

  set y(val) {
    if (!this.action?.value.anim.option) {
      return;
    }
    if (this.y === val) return;

    const newPoints = cloneDeep(this.option.points);
    newPoints[this.currIndex].y = this.convertToCenterCoordinate(0, val).y;
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        ...this.option,
        points: newPoints
      }
    });
  }

  convertToLeftTopCoordinate(
    x: number,
    y: number,
    stageWidth: number = this.stageWidth,
    stageHeight: number = this.stageHeight
  ) {
    return convertToLeftTopCoordinate(x, y, stageWidth, stageHeight);
  }

  convertToCenterCoordinate(
    x: number,
    y: number,
    stageWidth: number = this.stageWidth,
    stageHeight: number = this.stageHeight
  ) {
    return convertToCenterCoordinate(x, y, stageWidth, stageHeight);
  }
}
</script>


<style scoped lang="less">
.move-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
