<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      终点坐标
    </el-col>
    <el-col :span="8" style="display:flex; align-items: center">
      x
      <el-input-number
        v-model="x"
        type="number"
        size="mini"
        controls-position="right"
      />
    </el-col>
    <el-col :span="8" :offset="1" style="display:flex; align-items: center">
      y
      <el-input-number
        v-model="y"
        type="number"
        size="mini"
        controls-position="right"
      />
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { AnimationAction, types } from "@/pages/index/store/modules/animations";
import {
  convertToLeftTopCoordinate,
  convertToCenterCoordinate,
} from "@/pages/index/common/utils/coordinate";

@Component
export default class ShakeDirectionEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  currIndex = 1;

  get points() {
    return (
      this.action?.value.anim.option.points || [
        { x: 0, y: 0 },
        { x: 0, y: 0 },
      ]
    );
  }

  get stageWidth() {
    return this.$store.state.template.stage.safeWidth;
  }

  get stageHeight() {
    return this.$store.state.template.stage.safeHeight;
  }

  get x() {
    return this.convertToLeftTopCoordinate(this.points[this.currIndex].x, 0).x;
  }

  set x(val) {
    if (!this.action?.value.anim.option) {
      return;
    }
    if (this.x === val) return;

    const { offset } = this.action?.value.anim.option;
    const points = [
      { x: this.points[0].x, y: this.points[0].y },
      { x: this.convertToCenterCoordinate(val, 0).x, y: this.points[1].y },
    ];
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        // currIndex 表示要改的坐标点在数组中的索引
        currIndex: this.currIndex,
        offset,
        points,
      },
    });
  }

  get y() {
    return this.convertToLeftTopCoordinate(0, this.points[this.currIndex].y).y;
  }

  set y(val) {
    if (!this.action?.value.anim.option) {
      return;
    }
    if (this.y === val) return;

    const { offset } = this.action?.value.anim.option;
    const points = [
      { x: this.points[0].x, y: this.points[0].y },
      { x: this.points[1].x, y: this.convertToCenterCoordinate(0, val).y },
    ];
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        points,
        currIndex: this.currIndex,
        offset,
      },
    });
  }

  convertToLeftTopCoordinate(
    x: number,
    y: number,
    stageWidth: number = this.stageWidth,
    stageHeight: number = this.stageHeight,
  ) {
    return convertToLeftTopCoordinate(x, y, stageWidth, stageHeight);
  }

  convertToCenterCoordinate(
    x: number,
    y: number,
    stageWidth: number = this.stageWidth,
    stageHeight: number = this.stageHeight,
  ) {
    return convertToCenterCoordinate(x, y, stageWidth, stageHeight);
  }
}
</script>

<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
