<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      属性
    </el-col>
    <el-col :span="12">
      <el-select v-model="v" size="mini" disabled placeholder="">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { AnimationAction } from "@/pages/index/store/modules/animations";

@Component
export default class PlaceholderEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get v(): string {
    return "";
  }

  set v(val) {
    // noop
  }

  get options() {
    return [];
  }
}
</script>


<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
