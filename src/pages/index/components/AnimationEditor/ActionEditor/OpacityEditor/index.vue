<template>
  <el-row class="opacity-editor-row">
    <el-col :span="6">
      透明度
    </el-col>
    <el-col :span="7">
      <el-input-number
        :max="255"
        :min="0"
        :step="1"
        type="number"
        v-model="startOpacity"
        size="mini"
        controls-position="right"
      ></el-input-number>
    </el-col>
    <el-col :span="2" style="text-align:center">~</el-col>
    <el-col :span="7">
      <el-input-number
        :max="255"
        :min="0"
        :step="1"
        type="number"
        v-model="endOpacity"
        size="mini"
        controls-position="right"
      ></el-input-number>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { AnimationAction, types } from "@/pages/index/store/modules/animations";

@Component
export default class OpacityEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get startOpacity(): number {
    return this.action?.value.anim.option.start;
  }

  set startOpacity(val) {
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        start: val,
        end: this.endOpacity,
      },
    });
  }

  get endOpacity(): number {
    return this.action?.value.anim.option.end;
  }

  set endOpacity(val) {
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: {
        start: this.startOpacity,
        end: val,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.opacity-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;

    /deep/ .el-input-number--mini {
      width: 100%;
    }
  }
}
</style>
