<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      方向
    </el-col>
    <el-col :span="12">
      <el-select v-model="v" size="mini">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { isNumber } from "lodash-es";
import { Component, Prop, Vue } from "vue-property-decorator";
import {
  AnimationAction,
  ShakeDirections,
  types,
} from "@/pages/index/store/modules/animations";

const labels = {
  [ShakeDirections.VERTICAL]: "上下",
  [ShakeDirections.HORIZONTAL]: "左右",
};

@Component
export default class ShakeDirectionEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get v(): ShakeDirections | undefined {
    return this.action?.value.anim.option;
  }

  set v(val) {
    this.$store.commit(types.mutations.setActionAnimOption, {
      option: val,
    });
  }

  get options() {
    return (Object.values(ShakeDirections).filter(v =>
      isNumber(v),
    ) as ShakeDirections[]).map(v => ({
      label: labels[v],
      value: v,
    }));
  }
}
</script>


<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
