<template>
  <!-- spine 动画编辑组件 -->
  <spine-action-editor
    v-if="activeAction && activeAction.value.animType === 'spine'"
  />

  <!-- cocos 动画编辑组件 -->
  <cocos-ani-action-editor
    v-else-if="activeAction && activeAction.value.animType === 'cocosAni'"
  />

  <!-- tween 动画编辑组件 -->
  <tween-action-editor v-else />
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import {
  AnimationAction,
  SpineAnimationAction,
  CocosAnimationAction,
} from "@/pages/index/store/modules/animations";
import TweenActionEditor from "./TweenActionEditor/index.vue";
import SpineActionEditor from "./SpineActionEditor/index.vue";
import CocosAniActionEditor from "./CocosAniActionEditor/index.vue";

/**
 * @description 动画片段属性编辑组件
 */
@Component({
  components: {
    TweenActionEditor,
    SpineActionEditor,
    CocosAniActionEditor,
  },
})
export default class ActionEditor extends Vue {
  get activeAction():
    | AnimationAction
    | SpineAnimationAction
    | CocosAnimationAction
    | undefined {
    return this.$store.getters["moduleAnimations/activeAction"];
  }
}
</script>
