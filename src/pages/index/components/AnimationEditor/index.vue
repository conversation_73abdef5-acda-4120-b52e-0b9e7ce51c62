<template>
  <div class="animation-editor">
    <animation-selector />
    <div v-if="fragmentId" class="animation-editor-action-area">
      <add-action />
      <action-editor />
      <action-list />
      <el-button
        size="small"
        @click="onClickBtn"
        :disabled="!actionList.length"
        >{{ isPlayingFragment ? "停止" : "预览" }}</el-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { AnimationAction } from "@/pages/index/store/modules/animations";
import ActionList from "./ActionList/index.vue";
import AddAction from "./AddAction/index.vue";
import ActionEditor from "./ActionEditor/index.vue";
import AnimationSelector from "./AnimationSelector/index.vue";
import { types } from "../../store/modules/animations";

@Component({
  components: {
    ActionEditor,
    ActionList,
    AddAction,
    AnimationSelector,
  },
})
export default class AnimationEditor extends Vue {
  get animationVal(): string {
    return this.$store.state.moduleAnimations.animationVal;
  }

  get fragmentId(): string {
    return this.$store.state.moduleAnimations.fragmentId;
  }

  get actionList(): AnimationAction[] {
    const animation = this.$store.state.moduleAnimations.animations[
      this.animationVal
    ];
    return (animation && animation.fragments[this.fragmentId]) ?? [];
  }

  get activeAction(): AnimationAction | undefined {
    return this.$store.getters["moduleAnimations/activeAction"];
  }

  get isPlayingFragment() {
    return this.$store.state.moduleAnimations.isPlayingFragment;
  }

  playFragment() {
    this.$store.commit(types.mutations.playFragment, this.actionList);
  }

  stopFragment() {
    this.$store.commit(types.mutations.stopFragment);
  }

  onClickBtn() {
    if (this.isPlayingFragment) {
      this.stopFragment();
    } else {
      this.playFragment();
    }
  }
}
</script>

<style scoped lang="less">
.animation-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }

  .show-component-btn {
    text-align: left;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;

    &:hover {
      color: #888;
    }
  }

  &-choose-area {
    padding: 20px 20px 5px;
  }

  &-action-area {
    background: #f9f9f9;
    padding: 10px 20px;
  }
}
</style>
