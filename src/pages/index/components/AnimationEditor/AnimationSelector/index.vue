<template>
  <div class="animation-editor-choose-area">
    <component-id-selector />
    <el-row class="animation-editor-row">
      <el-col :span="5"> 启动时机 </el-col>
      <el-col :span="14">
        <el-select v-model="animationVal" @change="fragmentId = ''" size="mini">
          <el-option
            v-for="item in animationConfig"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="5">
        <el-button size="small" @click="onClickPreviewBtn">{{
          isPlayingAnimation ? "停止" : "预览"
        }}</el-button>
      </el-col>
    </el-row>
    <el-row class="animation-editor-row">
      <el-col :span="5"> 音频 </el-col>
      <el-col v-if="audioUrl" :span="14">
        <audio-player :src="audioUrl" />
      </el-col>
      <el-col :span="4">
        <animation-audio />
      </el-col>
    </el-row>
    <div class="animation-editor-choose-area-fragments">
      <div class="animation-editor-choose-area-fragments-label">
        片段 <span class="add el-icon-plus" @click="onClickAdd"></span>
        <span
          v-if="fragmentId"
          class="remove el-icon-minus"
          @click="onClickDel"
        ></span>
      </div>
      <div
        v-for="(key, index) in fragmentKeys"
        :key="key"
        @click="fragmentId = key"
        :class="[
          'animation-editor-choose-area-fragments-fragment',
          { active: fragmentId === key },
        ]"
      >
        {{ index + 1 }}
      </div>
      <div v-if="fragmentId">
        <el-row class="animation-editor-row">
          <el-col :span="6"> 开始时间 </el-col>
          <el-input-number
            v-model="startTime"
            controls-position="right"
            type="number"
            :min="0"
            :step="0.1"
            size="mini"
          />
          秒
        </el-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { v4 as uuidv4 } from "uuid";
import { Component, Vue } from "vue-property-decorator";
import { types } from "@/pages/index/store/modules/animations";
import { ActiveNames } from "@/pages/index/store/modules/componentManagerCard/constants";
import { types as componentManagerCardTypes } from "@/pages/index/store/modules/componentManagerCard";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import ComponentIdSelector from "../../ComponentIdSelector/index.vue";
import AnimationAudio from "../AnimationAudio/index.vue";

@Component({
  components: {
    AnimationAudio,
    AudioPlayer,
    ComponentIdSelector,
  },
})
export default class AnimationSelector extends Vue {
  get animationVal(): string {
    return this.$store.state.moduleAnimations.animationVal;
  }

  set animationVal(val) {
    this.$store.commit(types.mutations.setAnimationVal, val);
  }

  get fragmentId(): string {
    return this.$store.state.moduleAnimations.fragmentId;
  }

  set fragmentId(val) {
    this.$store.commit(types.mutations.setFragmentId, val);
  }

  get animationConfig(): AnimationConfig {
    const config = this.$store.state.template.animationConfig;
    // if (this.$store.state.extraStageData?.isAutoSubmit === true) {
    //   // 自动提交过滤选项 错误后，错误提示1，错误提示2，错误提示3
    //   const newConfig = config.filter((item: any) => {
    //     return item.label.indexOf("错") === -1;
    //   });
    //   let animValState = false;
    //   newConfig.forEach((item: any) => {
    //     if (item.value === this.animationVal) {
    //       animValState = true;
    //     }
    //   });
    //   if (!animValState) {
    //     this.animationVal = newConfig[0].value;
    //   }
    //   return newConfig;
    // }
    return config;
  }

  get animation() {
    return this.$store.state.moduleAnimations.animations[this.animationVal];
  }

  get audioUrl() {
    return this.animation.audio.url;
  }

  get fragments() {
    return this.animation?.fragments;
  }

  get fragmentKeys() {
    if (!this.fragments) {
      return [];
    }
    return Object.keys(this.fragments);
  }

  get pointId() {
    const points = this.animation.points;
    const pointKeys = Object.keys(this.animation.points);
    const pointId = pointKeys.find(
      id => points[id].fragmentId === this.fragmentId,
    );
    return pointId;
  }

  get point() {
    if (!this.pointId) return undefined;
    const points = this.animation.points;
    return points[this.pointId];
  }

  get startTime() {
    return this.point?.startTime;
  }

  set startTime(val) {
    if (val === this.startTime) return;
    this.$store.commit(types.mutations.updatePointStartTime, {
      id: this.pointId,
      startTime: Number(val),
    });
  }

  get isPlayingAnimation() {
    return this.$store.state.moduleAnimations.isPlayingAnimation;
  }

  onClickPreviewBtn() {
    if (this.isPlayingAnimation) {
      this.stopAnimation();
    } else {
      this.playAnimation();
    }
  }

  showComponentManager() {
    this.$store.commit(
      componentManagerCardTypes.mutations.setActiveNames,
      ActiveNames.MANAGER,
    );
    this.$store.commit(
      componentManagerCardTypes.mutations.setVisible,
      !this.$store.state.componentManagerCard.visible,
    );
  }

  onClickAdd() {
    const fragmentId = uuidv4();
    this.$store.commit(types.mutations.addFragment, fragmentId);
    this.$store.commit(types.mutations.addPoint, {
      id: uuidv4(),
      fragmentId,
      startTime: 0,
    });
  }

  onClickDel() {
    const points = this.animation.points;
    const pointKeys = Object.keys(this.animation.points);
    const pointId = pointKeys.find(
      id => points[id].fragmentId === this.fragmentId,
    );
    this.$store.commit(types.mutations.removePoint, pointId);
    this.$store.commit(types.mutations.removeFragment, this.fragmentId);
  }

  playAnimation() {
    this.$store.commit(types.mutations.playAnimation, this.animation);
  }

  stopAnimation() {
    this.$store.commit(types.mutations.stopAnimation);
  }
}
</script>

<style scoped lang="less">
.animation-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }

  &-choose-area {
    padding: 20px 20px 5px;

    &-fragments {
      text-align: left;

      &-label {
        font-size: 14px;
        margin-bottom: 5px;
      }

      &-fragment {
        display: inline-block;
        padding: 0 10px;
        line-height: 20px;
        margin-right: 10px;
        margin-bottom: 5px;
        border: 1px solid #ccc;
        cursor: pointer;
      }

      .add,
      .remove {
        display: inline-block;
        cursor: pointer;
        padding: 0 5px;
        line-height: 20px;
        margin-right: 2px;

        &:hover {
          background: #eee;
        }
      }
    }

    .active {
      background: #64b5f6;
      color: #fff;
    }
  }
}
.el-input-number {
  margin-right: 10px;
}
</style>
