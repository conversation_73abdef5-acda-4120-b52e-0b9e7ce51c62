<template>
  <div class="animation-editor-audio">
    <el-button @click="dialogVisible = true" size="small">{{
      audioUrl ? "编辑" : "添加"
    }}</el-button>
    <el-dialog
      title="编辑音频"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
    >
      <template v-if="dialogVisible">
        <el-row class="animation-editor-audio-row" style="margin-bottom: 0">
          <el-col :span="4" class="animation-editor-audio-row-label">
            音频
          </el-col>
          <el-col :span="16" :offset="1">
            <audio-select />
          </el-col>
        </el-row>
        <el-row class="animation-editor-audio-row">
          <el-col :span="4" class="animation-editor-audio-row-label">
            延迟
          </el-col>
          <el-col :span="7" :offset="1">
            <el-input-number
              v-model="audioDelay"
              controls-position="right"
              type="number"
              :min="0"
              :step="0.1"
              size="mini"
            />
            秒
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { types } from "@/pages/index/store/modules/animations";
import AudioSelect from "./audioSelect.vue";
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: { AudioSelect },
})
export default class AnimationAudio extends Vue {
  dialogVisible = false;

  @Watch('dialogVisible')
  dialogVisibleChanged(val: boolean) {
    bus.$emit('dialogVisible', val)
  }

  get animationVal(): string {
    return this.$store.state.moduleAnimations.animationVal;
  }

  get fragmentId(): string {
    return this.$store.state.moduleAnimations.fragmentId;
  }

  get animation() {
    return this.$store.state.moduleAnimations.animations[this.animationVal];
  }

  get audioUrl() {
    return this.animation.audio.url;
  }

  get audioDelay() {
    return this.animation.audio.delay;
  }

  set audioDelay(val) {
    this.$store.commit(types.mutations.setAnimationAudioDelay, Number(val));
  }
}
</script>

<style scoped lang="less">
.animation-editor-audio {
  &-row {
    line-height: 30px;
    font-size: 14px;
    margin-bottom: 10px;

    &-label {
      text-align: right;
    }
  }
}
</style>
