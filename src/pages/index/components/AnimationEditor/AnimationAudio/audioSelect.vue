<template>
  <audio-select :value.sync="v" style="width: 300px" />
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { types } from "@/pages/index/store/modules/animations";

@Component({
  components: { AudioSelect },
})
export default class AnimationAudioSelect extends Vue {
  get animation() {
    const { animationVal, animations } = this.$store.state.moduleAnimations;
    return animations[animationVal];
  }

  get v() {
    return this.animation.audio.url;
  }

  set v(val) {
    this.$store.commit(types.mutations.setAnimationAudioUrl, val);
  }
}
</script>
