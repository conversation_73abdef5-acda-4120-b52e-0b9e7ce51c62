<template>
  <div
    :key="action.id"
    @click="onClickAction(action)"
    :class="[
      'action-list-item',
      {
        active: action.id === activeActionId,
        'queue-header': !drag && action.isQueueHeader,
      },
    ]"
    :data-queueNum="action.queueNum"
  >
    <span v-if="conflict" class="conflict-icon">
      <el-tooltip content="该动画可能存在冲突，请预览查看效果" placement="left">
        <span>
          <i class="el-icon-warning"></i>
        </span>
      </el-tooltip>
    </span>

    <span class="action-list-item-component-id">
      组件ID:{{ action.componentId }}
    </span>
    <span v-if="component.type === 'label'" class="action-list-item-brief">
      {{ component.properties.string }}
    </span>
    <span
      v-else-if="component.type === 'sprite'"
      class="action-list-item-brief"
    >
      <img
        class="action-list-item-brief-img"
        :src="component.properties.texture"
      />
    </span>
    <span v-else-if="component.type === 'spine'">
      <img
        class="action-list-item-brief-img"
        :src="component.spineData.cover || component.spineData.images[0]"
      />
    </span>
    <span v-else-if="component.type === 'cocosAni'">
      <img
        class="action-list-item-brief-img"
        :src="component.cocosAniData.cover || component.cocosAniData.images[0]"
      />
    </span>
    <span class="action-list-item-name">{{
      action.value.name + " " + action.value.anim.name
    }}</span>
    <span class="action-list-item-btns" v-if="!drag">
      <i class="el-icon-setting" @click.stop="openAdvancedSetting(action)" />
      <i class="el-icon-delete" @click.stop="removeAction(action.id)" />
    </span>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import draggable from "vuedraggable";
import { types, AnimationAction } from "@/pages/index/store/modules/animations";

@Component({
  components: {
    draggable,
  },
})
export default class ActionList extends Vue {
  @Prop({
    required: true,
  })
  drag!: boolean;

  @Prop({
    required: true,
  })
  conflict!: boolean;

  @Prop({
    required: true,
  })
  action!: AnimationAction & { isQueueHeader: boolean; queueNum: number };

  @Prop({
    required: true,
  })
  component!: Component;

  get activeActionId(): string {
    return this.$store.state.moduleAnimations.activeActionId;
  }

  set activeActionId(val) {
    this.$store.commit(types.mutations.setActiveActionId, val);
  }
  get animationVal(): string {
    return this.$store.state.moduleAnimations.animationVal;
  }

  get fragmentId(): string {
    return this.$store.state.moduleAnimations.fragmentId;
  }

  onClickAction(action: AnimationAction): void {
    this.$store.commit("replaceCurrentComponentIds", [action.componentId]);
    this.$nextTick(() => (this.activeActionId = action.id));
  }

  removeAction(actionId: string) {
    const { animationVal, fragmentId } = this;
    const animation = this.$store.state.moduleAnimations.animations[
      animationVal
    ];
    const fragment = animation.fragments[fragmentId];
    const index = fragment.findIndex(
      (action: AnimationAction) => action.id === actionId,
    );
    this.activeActionId = "";
    this.$store.commit(types.mutations.removeAction, {
      animationVal,
      fragmentId,
      index,
    });
  }

  openAdvancedSetting(action: AnimationAction): void {
    if (this.activeActionId === action.id) {
      this.$emit("openAdvancedSetting");
      return;
    }
    this.$store.commit("replaceCurrentComponentIds", [action.componentId]);
    this.$nextTick(() => {
      this.activeActionId = action.id;
      this.$emit("openAdvancedSetting");
    });
  }
}
</script>

<style scoped lang="less">
.action-list {
  &-item {
    position: relative;
    display: flex;
    justify-content: space-between;
    cursor: move;
    user-select: none;
    background: #fff;
    padding: 0 10px 0 20px;
    font-size: 13px;
    line-height: 30px;
    text-align: left;
    border: 1px solid #bbb;
    margin-bottom: 3px;
    margin-right: 40px;

    &-component-id {
      display: inline-block;
      // width: 75px;
      white-space: nowrap;
      flex-shrink: 0;
      padding-right: 6px;
    }

    &-name {
      display: inline-block;
      color: #999;
      font-size: 12px;
      white-space: nowrap;
      flex-shrink: 0;
      padding-left: 6px;
    }

    &-brief {
      display: inline-block;
      color: #999;
      font-size: 12px;
      flex-grow: 1;
      margin-right: 10px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;

      &-img {
        padding: 5px 0;
        box-sizing: border-box;
        display: inline-block;
        height: 30px;
        vertical-align: bottom;
      }
    }

    .conflict-icon {
      cursor: pointer;
      display: block;
      position: absolute;
      left: 3px;
      top: 0;
      bottom: 0;
      color: red;
    }

    &-btns {
      display: none;
      position: absolute;
      line-height: 30px;
      text-align: center;
      cursor: pointer;
      width: 40px;
      left: 100%;
      top: 0;
      font-size: 16px;

      .el-icon-setting,
      .el-icon-delete {
        line-height: 20px;
        width: 20px;

        &:hover {
          background: #ddd;
        }
      }
    }

    &:hover {
      background: #f6f6f6;

      .action-list-item-btns {
        display: inline-block;
      }
    }
  }

  .active {
    background: #d3eefd;
  }

  .queue-header {
    margin-top: 22px;
    position: relative;

    &::before {
      content: attr(data-queueNum);
      font-weight: bold;
      pointer-events: none;
      position: absolute;
      top: -22px;
      left: 0;
      color: #666;
      line-height: 22px;
    }
  }

  .ghost {
    opacity: 0.5;
    background: #eee;
  }

  .drag-item {
    &::before {
      content: none;
    }

    .action-list-item-btns {
      display: none;
    }
  }
}
</style>
