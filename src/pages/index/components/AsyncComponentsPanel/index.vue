<template>
  <div v-if="cocosInitFinished">
    <component :is="asyncComp.ScopedComponent" :scopedComponentData="scopedComponentData" v-if="scopedComponentData.state" />
    <component :is="asyncComp.Material" ref="materailComponent" />
    <component :is="asyncComp.NativeMaterial" ref="materailComponent" />
    <component :is="asyncComp.FormQuestion" :category="category" v-if="isRenderFormQuestion" />
    <component :is="asyncComp.PreviewModal" v-if="asyncComp.PreviewModal" v-show="showPreview" :visible="showPreview" />
  </div>
</template>

<script lang="ts">
import { isRenderFormEdit } from "@/common/utils/isRenderFormEdit";
import { parse } from "query-string";
import { Component, Vue, Watch } from "vue-property-decorator";
import { ExtendedVue } from "vue/types/vue";
import bus from "../../common/utils/bus";
// import { Mode } from "@/pages/index/store/constants";
import { listenQuestionToCocos, renderFormQuestionOriginData } from "../../common/utils/listenQuestionToCocos";
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { PixelToPicUrlQuence } from "@/common/utils/renderTextureToPicture";
import { ThisTypedComponentOptionsWithArrayProps, ComponentOptions } from "vue/types/options";
import { setMaterialLibraryDialogZIndex } from "@/common/utils/utils";

@Component
export default class AsyncComponentsPanel extends Vue {
  scopedComponentData = { id: "", state: 0 };
  showPreview: any = 0; // 展示设置答案的预览弹窗
  get cocosInitFinished() {
    return this.$store.state.cocosInitFinished;
  }
  get category() {
    return this.$store.state.template.category;
  }
  get isRenderFormQuestion() {
    return isRenderFormEdit(this.category);
  }
  get fromGroup() {
    const query = parse(window.location.search);
    return Number(query.fromGroup);
  }
  asyncComp: {
    [x: string]: ExtendedVue<Vue, unknown, unknown, unknown, Record<never, any>> | null;
  } = {
    Material: null,
    NativeMaterial: null,
    // ENPKQuestionForm: null,
    WordsDialog: null,
    FormQuestion: null,
    PreviewModal: null,
    ScopedComponent: null,
  };
  asyncCompImports: any = {
    ScopedComponent: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/ComponentManagerCard/ComponentLibrary/ScopedComponent/index.vue").then(component => {
          this.asyncComp.ScopedComponent = Vue.extend(component.default);
        });
      });
    },
    Material: () => {
      return Promise.resolve().then(() => {
        this.$getPageConfigByKey("Material")().then((component: { default: ComponentOptions<Vue> | undefined }) => {
          if (component.default) {
            this.asyncComp.Material = Vue.extend(component.default);
          }
        });
      });
    },
    NativeMaterial: () => {
      return Promise.resolve().then(() => {
        this.$getPageConfigByKey("NativeMaterial")().then((component: { default: ComponentOptions<Vue> | undefined }) => {
          if (component.default) {
            this.asyncComp.NativeMaterial = Vue.extend(component.default);
          }
        });
      });
    },
    PreviewModal: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "PreviewModal" */ "@/pages/index/components/EditArea/StageEditor/ExtraEditor/SetAnswer/PreviewModal.vue").then(component => {
          this.asyncComp.PreviewModal = Vue.extend(component.default);
        });
      });
    },
    FormQuestion: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "FormQuestion" */ "@/components/form-question").then(component => {
          this.asyncComp.FormQuestion = Vue.extend(component.default);
        });
      });
    },
  };
  renderAsyncComp(type: "cocosLoaded" | "userHandle", key = "") {
    const keys = {
      cocosLoaded: this.$getPageConfigByKey("asyncCompKeys") as string[],
      // TODO feat/feat_refactor_20241121
      // cocosLoaded: ["Material",  "ScopedComponent"],
      userHandle: ["PreviewModal"],
    };
    console.log("renderAsyncComp");
    if (key) {
      this.asyncCompImports[key] && (this.asyncCompImports[key] as any)();
      return;
    }

    keys[type] &&
      keys[type].forEach((key: string | number) => {
        this.asyncCompImports[key] && (this.asyncCompImports[key] as any)();
      });
  }

  @Watch("isRenderFormQuestion", { immediate: true })
  async isRenderFormQuestionChange(val: boolean) {
    if (val) {
      this.$nextTick().then(() => {
        this.renderAsyncComp("userHandle", "FormQuestion");
        this.$nextTick().then(() => {
          renderFormQuestionOriginData();
        });
      });
    }
  }
  @Watch("showPreview")
  async showPreviewChange(val: boolean) {
    if (val) {
      this.renderAsyncComp("userHandle", "PreviewModal");
    }
  }

  @Watch("cocosInitFinished")
  async onCocosInitFinished(val: boolean) {
    if (val) {
      try {
        (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosInit);
        (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.TTI);
      } catch (error) {
        console.log("monitorManager error", error);
      }

      console.log("cocosInitFinished");
      PixelToPicUrlQuence.Instance.starUploadPic();
      if (this.fromGroup) {
        window["cocos"].setResetAndSubmitShow(false);
        window.parent.postMessage(
          {
            action: "edit-loaded",
          },
          "*",
        );
      }
    }
  }

  postMessageToGroup(val: boolean) {
    this.fromGroup &&
      window.parent.postMessage(
        {
          action: "global-modal-visible",
          data: { show: val },
        },
        "*",
      );
  }

  async created() {
    bus.$on("scopedComponentState", (data: { id: string; state: number; callBack: Function }) => {
      this.scopedComponentData = data;
    });

    bus.$on("cocosResourcesLoaded", (val: string) => {
      if (val === "cocosLoadStageRootNodeFinished") {
        if (localStorage.getItem("delayLoadedMaterialLibrary") && !isNaN(Number(localStorage.getItem("delayLoadedMaterialLibrary")))) {
          const timer = setTimeout(() => {
            this.renderAsyncComp("cocosLoaded");
            clearTimeout(timer);
          }, Number(localStorage.getItem("delayLoadedMaterialLibrary")));
        } else {
          this.renderAsyncComp("cocosLoaded");
        }
      }
    });

    bus.$on("showDialogPreview", () => {
      this.showPreview = true;
      this.postMessageToGroup(true);
    });

    bus.$on("closeDialogPreview", () => {
      this.showPreview = false;
      this.postMessageToGroup(false);
    });

    bus.$on("material-close", (val: boolean) => {
      console.log("xu-material-close", val);
      this.postMessageToGroup(false);
    });
    bus.$on("material-show", (val: boolean) => {
      console.log("xu-post-material-show", val);
      this.postMessageToGroup(true);
      this.$nextTick(() => {
        // 当素材库显示时，动态更新zIndex
        setMaterialLibraryDialogZIndex();
      })
    });
    bus.$on("closeSpine", (val: boolean) => {
      console.log("xu-post-closeSpine", val);
      this.postMessageToGroup(false);
    });
    bus.$on("dialogVisible", (val: boolean) => {
      console.log("xu-post-dialogVisible", val);
      this.postMessageToGroup(val);
    });
    bus.$on("form-question-insert", (data: any) => {
      console.log("xu-form-question-insert");
      if (this.isRenderFormQuestion) {
        listenQuestionToCocos(data);
      }
    });
    // h5Label显示对题组的影响
    bus.$on("h5Label-dialog-toggle", this.postMessageToGroup);
  }
}
</script>

<style scoped lang="less">
.edit-area {
  position: relative;
  overflow: scroll;
  background: #fff;
  flex-shrink: 0;
  font-size: 12px;

  .resizer-left {
    position: absolute;
    z-index: 101;
    left: -4px;
    top: 0;
    width: 8px;
    height: 100%;
    cursor: col-resize;
  }

  /deep/ .el-drawer__wrapper {
    width: 380px;
    left: auto;
    overflow: visible;
    z-index: 100 !important;
  }
}
</style>
