<template>
  <div :class="['action-editor', { disabled: !activeActionId }]">
    <el-row class="action-editor-row">
      <el-col :span="6">
        播放时机
      </el-col>
      <el-col :span="12">
        <el-select v-model="moment" size="mini">
          <el-option v-for="item in momentOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-col>
    </el-row>
    <div :class="{ disabled: disabled }">
      <component :is="extraAttrEditor" :action="activeAction" />
      <el-row class="action-editor-row">
        <el-col :span="6">
          延迟
        </el-col>
        <el-input-number controls-position="right" v-model="delay" type="number" :min="0" :step="0.1" size="mini" />
        秒
      </el-row>
      <el-row class="action-editor-row">
        <el-col :span="6">
          速度
          <el-tooltip content="动画持续的时间，单位：秒" placement="bottom"> <i class="el-icon-info"/></el-tooltip>
        </el-col>
        <el-col :span="7">
          <el-select v-model="speed" size="mini">
            <el-popover placement="top" width="220" v-model="customSpeedPopoverVisible" :visible-arrow="false">
              <div style="text-align: right; margin: 0">
                速度：
                <el-input-number size="mini" v-model="speed" controls-position="right" :step="0.1" />秒
              </div>
              <div slot="reference" class="el-select-dropdown__item" style="border-bottom: 1px solid #eee">
                自定义
              </div>
            </el-popover>
            <el-option v-for="item in speedOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row class="action-editor-row">
        <el-col :span="6">
          结束效果
        </el-col>
        <el-col :span="7">
          <el-select v-model="afterType" size="mini">
            <el-option v-for="item in afterEffectOptions" :key="item.value" :label="item.label" :value="item.value" :disabled="hideDisabled && item.value === 1"> </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row class="action-editor-row">
        <el-col :span="6"> </el-col>
        <el-col :span="24" style="margin-left: -25%;">
          <span style="color: red;font-size: 12px;" v-if="afterType === 1 && hideDisabled">请将结束效果设置为无</span>
        </el-col>
      </el-row>
    </div>
    <hr />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { AfterType, AnimationAction, momentOptions } from "@/pages/index/store/modules/animations";
import AngleEditor from "./components/AngleEditor/index.vue";
import DirectionEditor from "./components/DirectionEditor/index.vue";
import ShakeDirectionEditor from "./components/ShakeDirectionEditor/index.vue";
import SiweiMoveLineEditor from "./components/SiweiMoveLineEditor/index.vue";
import PlaceholderEditor from "./components/PlaceholderEditor/index.vue";
import SizeEditor from "./components/SizeEditor/index.vue";
import MoveEditor from "./components/MoveEditor/index.vue";
import OpacityEditor from "./components/OpacityEditor/index.vue";
import { AnimType, isSiweiTypeAnim } from "../../../AnimationEditor/AddAction/options";
import { cloneDeep } from "lodash-es";

@Component
export default class TweenActionEditor extends Vue {
  customSpeedPopoverVisible = false;

  get extraAttrEditor() {
    const extraAttrEditorMap: Partial<Record<AnimType, any>> = {
      [AnimType.FLY_INTO]: DirectionEditor,
      [AnimType.FLY_OUT]: DirectionEditor,
      [AnimType.SCALE]: SizeEditor,
      [AnimType.ROTATION]: AngleEditor,
      [AnimType.SHAKE]: ShakeDirectionEditor,
      [AnimType.BLINK]: OpacityEditor,
      [AnimType.SIWEI_MOVE_LINE]: SiweiMoveLineEditor,
      [AnimType.MOVE_LINE]: MoveEditor,
      [AnimType.MOVE_BEZIRE]: MoveEditor,
      [AnimType.CUSTOM_PATH]: MoveEditor,
      [AnimType.CUSTOM_CURVE]: MoveEditor,
    };
    const key = this.activeAction?.value.anim.type;
    if (!key || !extraAttrEditorMap[key]) {
      return PlaceholderEditor;
    }
    return extraAttrEditorMap[key];
  }

  get activeActionId(): string {
    return this.$store.state.componentActiveActionId;
  }

  get activeAction(): AnimationAction {
    return this.$store.getters.activeComponentAction;
  }

  /**
   * @description 播放时机
   */
  get moment() {
    return this.activeAction?.moment;
  }
  set moment(val) {
    this.$store.commit("setActiveComponentAction", {
      moment: val,
    });
  }

  /**
   * @description 延迟时间
   */
  get delay() {
    return this.activeAction?.delay ?? 0;
  }
  set delay(s) {
    if (Number(s) === this.delay) return;

    this.$store.commit("setActiveComponentAction", {
      delay: Number(s),
    });
  }

  /**
   * @description 速度
   */
  get speed() {
    return this.activeAction?.value.speed;
  }
  set speed(val: number) {
    if (val === this.speed) return;

    const cloneValue = cloneDeep(this.activeAction?.value);
    cloneValue.speed = val;

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  /**
   * @description 结束效果
   */
  get afterType() {
    return this.activeAction?.after.type;
  }
  set afterType(val) {
    const cloneValue = cloneDeep(this.activeAction?.after);
    cloneValue.type = val;

    this.$store.commit("setActiveComponentAction", {
      after: cloneValue,
    });
  }

  get momentOptions() {
    return momentOptions;
  }

  get speedOptions() {
    return [
      {
        label: "非常慢",
        value: 5,
      },
      {
        label: "慢速",
        value: 3,
      },
      {
        label: "中速",
        value: 2,
      },
      {
        label: "快速",
        value: 1,
      },
      {
        label: "非常快",
        value: 0.5,
      },
    ];
  }

  get afterEffectOptions() {
    return [
      {
        label: "无",
        value: AfterType.NONE,
      },
      {
        label: "隐藏",
        value: AfterType.HIDE,
      },
    ];
  }

  get isSiweiAction() {
    return this.activeAction && isSiweiTypeAnim(this.activeAction.value.anim.type);
  }

  get currentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get hideDisabled() {
    // return this.isSiweiAction && this.activeAction?.value.anim.type !== AnimType.SIWEI_MOVE_LINE;
    const { currentIds } = this;
    const { componentMap } = this.$store.state;
    console.log("hideDisabled", this.componentAnimationVal, componentMap, currentIds);
    if (
      currentIds.find(id => {
        return componentMap[id].tag === "dragArea";
      }) &&
      ["afterWrong", "afterDragAreaWrong"].includes(this.componentAnimationVal)
    ) {
      return true;
    }
    return false;
  }

  get disabled() {
    return this.isSiweiAction && this.activeAction?.value.anim.type !== AnimType.SIWEI_MOVE_LINE;
  }
}
</script>

<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;

  /deep/ .el-input__suffix {
    pointer-events: none;

    .el-input__suffix-inner {
      pointer-events: none;
    }
  }
}

.el-input-number {
  margin-right: 10px;
}
</style>
