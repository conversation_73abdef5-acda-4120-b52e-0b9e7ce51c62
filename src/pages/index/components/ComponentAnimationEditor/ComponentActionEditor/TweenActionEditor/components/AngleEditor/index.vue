<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      旋转角度
    </el-col>
    <el-col :span="7">
      <el-autocomplete
        class="inline-input"
        v-model="startAngle"
        type="number"
        :fetch-suggestions="querySearch"
        placeholder=""
        size="mini"
      ></el-autocomplete>
    </el-col>
    <el-col :span="2" style="text-align:center">~</el-col>
    <el-col :span="7">
      <el-autocomplete
        class="inline-input"
        v-model="endAngle"
        type="number"
        :fetch-suggestions="querySearch"
        placeholder=""
        size="mini"
      ></el-autocomplete>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { AnimationAction } from "@/pages/index/store/modules/animations";
import { cloneDeep } from "lodash-es";

const options = [
  {
    label: "四分之一旋转",
    value: "90",
  },
  {
    label: "半旋转",
    value: "180",
  },
  {
    label: "完全旋转",
    value: "360",
  },
  {
    label: "旋转两周",
    value: "720",
  },
  {
    label: "逆-四分之一旋转",
    value: "-90",
  },
  {
    label: "逆-半旋转",
    value: "-180",
  },
  {
    label: "逆-完全旋转",
    value: "-360",
  },
  {
    label: "逆-旋转两周",
    value: "-720",
  },
];

@Component
export default class AngleEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get startAngle(): string {
    return String(this.action?.value.anim.option.start ?? "");
  }

  set startAngle(val) {
    const cloneValue = cloneDeep(this.action?.value);
    cloneValue.anim.option.start = Number(val);

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get endAngle(): string {
    return String(this.action?.value.anim.option.end ?? "");
  }

  set endAngle(val) {
    const cloneValue = cloneDeep(this.action?.value);
    cloneValue.anim.option.end = Number(val);

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get options() {
    return options;
  }

  querySearch(_: string, cb: (options: any[]) => void) {
    cb(options);
  }
}
</script>

<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
