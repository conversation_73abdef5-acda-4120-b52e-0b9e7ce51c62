<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      方向
    </el-col>
    <el-col :span="12">
      <el-select v-model="v" size="mini">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { cloneDeep, isNumber } from "lodash-es";
import { Component, Prop, Vue } from "vue-property-decorator";
import {
  AnimationAction,
  Direction,
} from "@/pages/index/store/modules/animations";

const labels = {
  [Direction.TOP_TO_BOTTOM]: "从上到下",
  [Direction.RIGHT_TOP_TO_LEFT_BOTTOM]: "从右上到左下",
  [Direction.RIGHT_TO_LEFT]: "从右到左",
  [Direction.RIGHT_BOTTOM_TO_LEFT_TOP]: "从右下到左上",
  [Direction.BOTTOM_TO_TOP]: "从下到上",
  [Direction.LEFT_BOTTOM_TO_RIGHT_TOP]: "从左下到右上",
  [Direction.LEFT_TO_RIGHT]: "从左到右",
  [Direction.LEFT_TOP_TO_RIGHT_BOTTOM]: "从左上到右下",
};

@Component
export default class DirectionEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get v(): Direction | undefined {
    return this.action?.value.anim.option;
  }

  set v(val) {
    const cloneValue = cloneDeep(this.action?.value);
    cloneValue.anim.option = val;

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get options() {
    return (Object.values(Direction).filter(v =>
      isNumber(v),
    ) as Direction[]).map(v => ({
      label: labels[v],
      value: v,
    }));
  }
}
</script>

<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
