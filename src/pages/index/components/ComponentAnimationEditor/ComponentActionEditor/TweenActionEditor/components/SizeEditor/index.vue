<template>
  <el-row class="action-editor-row">
    <el-col :span="6">
      尺寸
    </el-col>
    <el-col :span="7">
      <el-select v-model="startSize" size="mini">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-col>
    <el-col :span="2" style="text-align:center">~</el-col>
    <el-col :span="7">
      <el-select v-model="endSize" size="mini">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { cloneDeep, isNumber } from "lodash-es";
import { Component, Prop, Vue } from "vue-property-decorator";
import { AnimationAction, Sizes } from "@/pages/index/store/modules/animations";

const labels = {
  [Sizes.VERY_SMALL]: "微小",
  [Sizes.SMALL]: "小",
  [Sizes.LITTLE_SMALL]: "较小",
  [Sizes.NORMAL]: "正常",
  [Sizes.LITTLE_BIG]: "较大",
  [Sizes.BIG]: "大",
  [Sizes.VERY_BIG]: "巨大",
};

@Component
export default class SizeEditor extends Vue {
  @Prop()
  action!: AnimationAction;

  get startSize(): Sizes | undefined {
    return this.action?.value.anim.option.start;
  }

  set startSize(val) {
    const cloneValue = cloneDeep(this.action?.value);
    cloneValue.anim.option.start = Number(val);

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get endSize(): Sizes | undefined {
    return this.action?.value.anim.option.end;
  }

  set endSize(val) {
    const cloneValue = cloneDeep(this.action?.value);
    cloneValue.anim.option.end = Number(val);

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get options() {
    return (Object.values(Sizes).filter(v => isNumber(v)) as Sizes[]).map(
      v => ({
        label: labels[v],
        value: v,
      }),
    );
  }
}
</script>

<style scoped lang="less">
.action-editor {
  &-row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}

.disabled {
  opacity: 0.3;
  pointer-events: none;
}
</style>
