<template>
  <div v-if="componentFragmentId">
    <!-- spine 动画编辑组件 -->
    <spine-action-editor
      v-if="
        activeComponentAction &&
          activeComponentAction.value.animType === 'spine'
      "
    />

    <cocos-ani-action-editor
      v-else-if="
        activeComponentAction &&
          activeComponentAction.value.animType === 'cocosAni'
      "
    />

    <!-- tween 动画编辑组件 -->
    <tween-action-editor v-else />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import SpineActionEditor from "./SpineActionEditor/index.vue";
import CocosAniActionEditor from "./CocosAniActionEditor/index.vue";
import TweenActionEditor from "./TweenActionEditor/index.vue";

@Component({
  components: {
    SpineActionEditor,
    CocosAniActionEditor,
    TweenActionEditor,
  },
})
export default class ComponentActionEditor extends Vue {
  get componentFragmentId() {
    return this.$store.state.componentFragmentId;
  }

  get activeComponentAction() {
    return this.$store.getters.activeComponentAction;
  }
}
</script>
