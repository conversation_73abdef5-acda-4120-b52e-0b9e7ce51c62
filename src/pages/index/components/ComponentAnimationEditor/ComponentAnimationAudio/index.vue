<template>
  <div class="component-animation-audio" v-if="componentAnimationVal">
    <el-row class="row">
      <el-col :span="5">
        音频
      </el-col>
      <el-col v-if="audioUrl" :span="14">
        <audio-player :src="audioUrl" />
      </el-col>
      <el-col :span="4">
        <el-button @click="dialogVisible = true" size="small">{{
          audioUrl ? "编辑" : "添加"
        }}</el-button>
      </el-col>
    </el-row>

    <el-dialog
      title="编辑音频2"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
    >
      <el-row class="row" style="margin-bottom: 0">
        <el-col :span="4" style="text-align:right">
          音频
        </el-col>
        <el-col :span="16" :offset="1">
          <audio-select :value.sync="audioUrl" />
        </el-col>
      </el-row>
      <el-row class="row">
        <el-col :span="4" style="text-align:right">
          延迟
        </el-col>
        <el-col :span="7" :offset="1">
          <el-input-number
            v-model="audioDelay"
            controls-position="right"
            type="number"
            :min="0"
            :step="0.1"
            size="mini"
          />
          秒
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Watch } from "vue-property-decorator";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { cloneDeep } from "lodash-es";
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: {
    AudioPlayer,
    AudioSelect,
  },
})
export default class ComponentAnimationAudio extends Vue {
  dialogVisible = false;

  @Watch('dialogVisible')
  dialogVisibleChanged(val: boolean) {
    bus.$emit('dialogVisible', val)
  }

  get editingAnimationsComponentId(): string {
    return this.$store.state.editingAnimationsComponentId;
  }

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get editingAnimationsComponent(): Component {
    return this.$store.getters.editingAnimationsComponent;
  }

  get audioUrl(): string {
    return this.editingAnimationsComponent.extra["animations"][
      this.componentAnimationVal
    ].audio.url;
  }

  set audioUrl(val: string) {
    const newAnimations = cloneDeep(
      this.editingAnimationsComponent.extra.animations,
    );
    newAnimations[this.componentAnimationVal].audio.url = val;
    this.$store.commit("updateComponentExtra", {
      id: this.editingAnimationsComponentId,
      newExtra: {
        animations: newAnimations,
      },
    });
  }

  get audioDelay(): number {
    return this.editingAnimationsComponent.extra["animations"][
      this.componentAnimationVal
    ].audio.delay;
  }

  set audioDelay(val: number) {
    const newAnimations = cloneDeep(
      this.editingAnimationsComponent.extra.animations,
    );
    newAnimations[this.componentAnimationVal].audio.delay = val;
    this.$store.commit("updateComponentExtra", {
      id: this.editingAnimationsComponentId,
      newExtra: {
        animations: newAnimations,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.component-animation-audio {
  .row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}
</style>
