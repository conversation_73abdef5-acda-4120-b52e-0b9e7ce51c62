<template>
  <div v-if="fragmentId && actionList.length" class="action-list-container">
    <div class="action-list-title">播放顺序1</div>
    <draggable class="action-list" v-model="actionList" ghost-class="ghost" drag-class="drag-item" animation="200" @start="drag = true" @end="drag = false">
      <transition-group type="transition" :name="!drag ? 'flip-list' : null">
        <item
          v-for="(action, index) in actionList"
          :key="action.id"
          :action="action"
          :drag="drag"
          :conflict="actionConflictList[index]"
          :component="getComponentByAction(action)"
          @openAdvancedSetting="advancedSettingVisible = true"
        />
      </transition-group>
    </draggable>
    <advanced-setting :visible.sync="advancedSettingVisible" />
  </div>
</template>

<script lang="ts">
import { AnimationAction, Moment } from "@/pages/index/store/modules/animations";
import { cloneDeep } from "lodash-es";
import { Component, Vue } from "vue-property-decorator";
import draggable from "vuedraggable";
import { AnimType } from "../../AnimationEditor/AddAction/options";
import Item from "./Item.vue";
import AdvancedSetting from "./AdvancedSetting/index.vue";

type AnimationActionWithQueueData = AnimationAction & {
  isQueueHeader: boolean;
  queueNum: number;
};

@Component({
  components: {
    draggable,
    Item,
    AdvancedSetting,
  },
})
export default class ComponentActionList extends Vue {
  drag = false;
  advancedSettingVisible = false;

  get animationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get fragmentId(): string {
    return this.$store.state.componentFragmentId;
  }

  get actionList(): AnimationActionWithQueueData[] {
    const animation = this.$store.getters.editingAnimationsComponent.extra.animations[this.animationVal];
    const rawActionList: AnimationAction[] = (animation && animation.fragments[this.fragmentId]) ?? [];
    let queueNum = 0;
    const actionListWithQueueData = rawActionList.map((action, index) => {
      const isQueueHeader = index === 0 || action.moment === Moment.AFTER;
      if (isQueueHeader) {
        queueNum++;
      }
      return { ...action, queueNum, isQueueHeader };
    });

    return actionListWithQueueData;
  }

  set actionList(val) {
    const newAnimations = cloneDeep(this.editingAnimationsComponent.extra.animations);

    newAnimations[this.animationVal].fragments[this.fragmentId] = val;

    this.$store.commit("updateComponentExtra", {
      id: this.editingAnimationsComponent.id,
      newExtra: {
        animations: newAnimations,
      },
    });
  }

  get editingAnimationsComponent(): Component {
    return this.$store.getters.editingAnimationsComponent;
  }

  getComponentByAction(action: AnimationAction): Component {
    const { componentId } = action;
    const component = this.$store.state.componentMap[componentId];
    return component;
  }

  /**
   * @desc 获取 actionList中每一个action是否存在冲突的list
   *
   * @returns {boolean[]}
   */
  get actionConflictList(): boolean[] {
    const animUsePropsMap: Map<AnimType, string[]> = (window as MyWindow).cocos.getAnimaPropMap();
    console.log("1014-animUsePropsMap", animUsePropsMap);
    const queueAllPropsList = this.actionList.reduce((prev: Record<string, Record<string, number>>[], curr) => {
      const { isQueueHeader, queueNum, componentId } = curr;
      const animType = curr.value.anim.type;
      const useProps = animUsePropsMap.get(animType) || [];
      if (isQueueHeader) {
        prev[queueNum - 1] = {};
      }
      if (!prev[queueNum - 1][componentId]) {
        prev[queueNum - 1][componentId] = {};
      }
      const queueAllPropsMap = prev[queueNum - 1][componentId];
      useProps.forEach(prop => {
        if (!queueAllPropsMap[prop]) {
          queueAllPropsMap[prop] = 1;
        } else {
          queueAllPropsMap[prop] += 1;
        }
      });
      return prev;
    }, []);
    console.log("1014-queueAllPropsList", queueAllPropsList);
    const actionConflictList = this.actionList.map(action => {
      const { queueNum, componentId } = action;
      const queueAllProps = queueAllPropsList[queueNum - 1];
      const animType = action.value.anim.type;
      const useProps = animUsePropsMap.get(animType) || [];
      const hasConflict = useProps.some(prop => queueAllProps[componentId][prop] > 1);
      console.log("1014-hasConflict", hasConflict);
      return hasConflict;
    });

    return actionConflictList;
  }
}
</script>

<style lang="less" scoped>
.action-list-container {
  padding: 5px 0;
  font-size: 14px;
  text-align: left;

  .action-list-title {
    font-weight: bold;
    font-size: 15px;
  }

  .action-list {
    margin-bottom: 10px;
    padding: 5px 0;
    overflow: scroll;
    max-height: 300px;

    .flip-list-move {
      transition: transform 0.5s;
    }

    .flip-list-leave {
      transition: transform 0.2s;
    }

    .no-move {
      transition: transform 0s;
    }
  }
}
</style>
