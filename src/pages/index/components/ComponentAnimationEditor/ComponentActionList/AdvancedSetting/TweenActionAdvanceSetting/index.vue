<template>
  <div class="advanced-setting">
    <!-- 音频设置 -->
    <audio-advance-setting />
    <div v-if="canRepeat || canEditRelative" class="advanced-setting-title">
      动效
    </div>
    <div>
      <el-row v-if="canRepeat" class="advanced-setting-row">
        <el-col :span="5" class="advanced-setting-row-label">
          重复
        </el-col>
        <el-col :span="7" :offset="1">
          <el-input-number
            v-model="repeat"
            :min="0"
            :max="99"
            :step="1"
            size="mini"
            controls-position="right"
          />
        </el-col>
        <el-col :span="4" :offset="1">
          次
        </el-col>
      </el-row>
      <el-row v-if="canEditRelative" class="advanced-setting-row">
        <el-col :span="5" class="advanced-setting-row-label">
          相对效果
          <el-tooltip
            content="动画效果非绝对值，相对于组件本身属性而变化"
            placement="bottom"
          >
            <i class="el-icon-info"
          /></el-tooltip>
        </el-col>
        <el-col :span="6" :offset="1">
          <el-switch v-model="relative" />
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { AnimationAction } from "@/pages/index/store/modules/animations";
import AudioAdvanceSetting from "../AudioAdvanceSetting/index.vue";
import {
  AnimType,
  canEditRelativeAnimTypes,
} from "@/pages/index/components/AnimationEditor/AddAction/options";
import { cloneDeep } from "lodash-es";

@Component({
  components: { AudioAdvanceSetting },
})
export default class TweenActionAdvancedSetting extends Vue {
  get activeAction(): AnimationAction {
    return this.$store.getters.activeComponentAction;
  }

  get repeat() {
    return this.activeAction.value.repeat;
  }

  set repeat(val) {
    const cloneValue = cloneDeep(this.activeAction.value);
    cloneValue.repeat = val;

    this.$store.commit("setActiveComponentAction", {
      value: cloneValue,
    });
  }

  get relative() {
    return this.activeAction.value.anim.relative;
  }

  set relative(val) {
    this.$store.commit("setComponentActionAnimRelative", {
      relative: val,
    });
  }

  get canEditRelative() {
    return canEditRelativeAnimTypes.includes(this.activeAction.value.anim.type);
  }

  get canRepeat() {
    return this.activeAction.value.anim.type !== AnimType.SIWEI_MOVE_LINE;
  }
}
</script>

<style scoped lang="less">
.advanced-setting {
  &-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
    margin-bottom: 10px;
  }

  &-row {
    line-height: 30px;
    font-size: 14px;
    margin-bottom: 10px;

    &-label {
      text-align: right;
    }
  }
}
</style>
