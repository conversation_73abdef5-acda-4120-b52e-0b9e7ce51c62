<template>
  <audio-select :value.sync="v" />
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { AnimationAction } from "@/pages/index/store/modules/animations";
import { cloneDeep } from "lodash-es";

@Component({
  components: { AudioSelect },
})
export default class AdvancedSettingAudioSelect extends Vue {
  get activeAction(): AnimationAction {
    return this.$store.getters.activeComponentAction;
  }

  get v() {
    return this.activeAction.audio.url;
  }

  set v(val) {
    const cloneValue = cloneDeep(this.activeAction.audio);
    cloneValue.url = val;

    this.$store.commit("setActiveComponentAction", {
      audio: cloneValue,
    });
  }
}
</script>
