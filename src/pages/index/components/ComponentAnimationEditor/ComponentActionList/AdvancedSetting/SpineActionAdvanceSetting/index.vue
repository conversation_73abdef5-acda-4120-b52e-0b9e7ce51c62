<template>
  <div class="advanced-setting">
    <audio-advance-setting />

    <div class="advanced-setting-title">
      结束效果
    </div>

    <el-row class="advanced-setting-row">
      <el-col :span="5" class="advanced-setting-row-label">
        是否循环
      </el-col>
      <el-col :span="12" :offset="1">
        <el-switch v-model="loop" />
      </el-col>
    </el-row>

    <el-row class="advanced-setting-row">
      <el-col :span="5" class="advanced-setting-row-label">
        播放队列
      </el-col>
      <el-col :span="16" :offset="1">
        <ul class="animation-list-list">
          <i
            class="el-icon-circle-plus-outline icon"
            v-if="!animList.length"
            @click="handleAddFirstAnim"
          />
          <li v-for="(item, index) in animList" :key="index" v-else>
            <el-select
              :value="item"
              size="mini"
              :loading="isFetching"
              @change="onAnimationItemChange(index, $event)"
            >
              <el-option key="void" value="">
                无动画
              </el-option>
              <el-option v-for="i in animationOptions" :key="i" :value="i">
                {{ i }}
              </el-option>
            </el-select>
            <i
              class="el-icon-circle-plus-outline icon"
              @click="onClickAddAnimation(index)"
            />
            <i
              @click="onClickRemoveAnimation(index)"
              class="el-icon-remove-outline icon"
            />
          </li>
        </ul>
      </el-col>
    </el-row>

    <el-row class="advanced-setting-row">
      <el-col :span="5" class="advanced-setting-row-label">
        速率
      </el-col>
      <el-col :span="12" :offset="1">
        <el-input-number
          v-model="endTimeScale"
          size="small"
          type="number"
          controls-position="right"
          :min="0.1"
          :step="0.1"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { SpineAnimationAction } from "@/pages/index/store/modules/animations";
import { cloneDeep } from "lodash-es";
import { Component, Vue, Watch } from "vue-property-decorator";
import AudioAdvanceSetting from "../AudioAdvanceSetting/index.vue";

@Component({
  components: {
    AudioAdvanceSetting,
  },
})
export default class SpineActionAdvancedSetting extends Vue {
  isFetching = false;
  animationOptions: string[] = [];

  get activeAction(): SpineAnimationAction {
    return this.$store.getters.activeComponentAction;
  }

  get component(): SpineComponent {
    return this.$store.state.componentMap[this.activeAction.componentId];
  }

  get skeletonUrl() {
    return this.component.spineData.skeleton;
  }

  @Watch("skeletonUrl", {
    immediate: true,
  })
  skeletonUrlWatcher(url: string) {
    this.fetchAnimation(url);
  }

  // 获取动画队列选项
  fetchAnimation(url: string) {
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations);
        this.animationOptions = animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }

  /**
   * @description 结束后循环
   */
  get loop() {
    return this.activeAction.after.loop;
  }
  set loop(loop: boolean) {
    if (loop === this.loop) return;

    const cloneValue = cloneDeep(this.activeAction.after);
    cloneValue.loop = loop;

    this.$store.commit("setActiveComponentAction", {
      after: cloneValue,
    });
  }

  /**
   * @description 结束后播放速率
   */
  get endTimeScale() {
    return this.activeAction.after.endTimeScale;
  }
  set endTimeScale(endTimeScale: number) {
    if (endTimeScale === this.endTimeScale) return;

    const cloneValue = cloneDeep(this.activeAction.after);
    cloneValue.endTimeScale = endTimeScale;

    this.$store.commit("setActiveComponentAction", {
      after: cloneValue,
    });
  }

  /**
   * @description 结束后播放队列
   */
  get animList() {
    return this.activeAction.after.animList;
  }
  set animList(animList: string[]) {
    const cloneValue = cloneDeep(this.activeAction.after);
    cloneValue.animList = animList;

    this.$store.commit("setActiveComponentAction", {
      after: cloneValue,
    });
  }

  handleAddFirstAnim() {
    this.animList = [""];
  }
  onAnimationItemChange(index: number, value: string) {
    const newAnimationList = this.animList.concat();
    newAnimationList[index] = value;
    this.animList = newAnimationList;
  }

  onClickAddAnimation(index: number) {
    const newAnimationList = this.animList.concat();
    newAnimationList.splice(index + 1, 0, "");
    this.animList = newAnimationList;
  }

  onClickRemoveAnimation(index: number) {
    const newAnimationList = this.animList.concat();
    newAnimationList.splice(index, 1);
    this.animList = newAnimationList;
  }
}
</script>

<style lang="less" scoped>
.advanced-setting {
  .animation-list-list {
    list-style: none;
    margin: 0;
    padding: 0;
    text-align: left;
    line-height: 28px;
  }

  .icon {
    display: inline-block;
    width: 24px;
    height: 28px;
    line-height: 28px;
    font-size: 22px;
    vertical-align: bottom;
    cursor: pointer;

    &:first-of-type {
      margin-left: 5px;
    }
  }

  &-title {
    font-size: 16px;
    font-weight: bold;
    line-height: 40px;
    margin-bottom: 10px;
  }

  &-row {
    line-height: 30px;
    font-size: 14px;
    margin-bottom: 10px;

    &-label {
      text-align: right;
    }
  }
}
</style>
