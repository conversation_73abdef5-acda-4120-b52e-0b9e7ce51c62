<template>
  <div class="editing-animations-components">
    {{ `组件ID: ${editingAnimationsComponentId}` }}
    <span class="back" @click="handleQuite">
      <i class="el-icon-close" />
    </span>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";

@Component
export default class EditingAnimationsComponents extends Vue {
  get editingAnimationsComponentId(): string {
    return this.$store.state.editingAnimationsComponentId;
  }

  handleQuite() {
    this.$store.dispatch("closeComponentAnimationEditor");
  }
}
</script>

<style lang="less" scoped>
.editing-animations-components {
  text-align: left;
  font-size: 16px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .back {
    display: inline-block;
    height: 22px;
    margin-right: 10px;
    line-height: 22px;
    text-align: center;
    cursor: pointer;

    &:hover {
      color: #999;
    }
  }
}
</style>
