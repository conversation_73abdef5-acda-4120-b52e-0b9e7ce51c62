<template>
  <div class="component-fragment-list" v-if="componentAnimationVal">
    <el-row class="row">
      <el-col :span="5">
        片段
      </el-col>
      <span class="add el-icon-plus" @click="onClickAdd"></span>
      <span
        v-if="componentFragmentId"
        class="remove el-icon-minus"
        @click="onClickDel"
      ></span>
    </el-row>

    <div class="fragment-list">
      <div
        v-for="(key, index) in fragmentKeys"
        :key="key"
        @click="handleChangeFragment(key)"
        :class="['fragment', { active: componentFragmentId === key }]"
      >
        {{ index + 1 }}
      </div>
    </div>

    <el-row class="row" v-if="componentFragmentId">
      <el-col :span="6">
        开始时间
      </el-col>
      <el-input-number
        v-model="startTime"
        controls-position="right"
        type="number"
        :min="0"
        :step="0.1"
        size="mini"
      />
      秒
    </el-row>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";
import { v4 as uuidv4 } from "uuid";

@Component
export default class ComponentFragmentList extends Vue {
  get editingAnimationsComponent(): Component {
    return this.$store.getters.editingAnimationsComponent;
  }

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get editingAnimationsComponentId(): string {
    return this.$store.state.editingAnimationsComponentId;
  }

  get componentFragmentId(): string {
    return this.$store.state.componentFragmentId;
  }

  set componentFragmentId(val) {
    this.$store.commit("setComponentFragmentId", val);
  }

  get fragments() {
    return this.editingAnimationsComponent.extra.animations[
      this.componentAnimationVal
    ].fragments;
  }

  get fragmentKeys() {
    return Object.keys(this.fragments);
  }

  onClickAdd() {
    this.$store.commit("addComponentAnimationFragment", uuidv4());
  }

  get pointId() {
    const { points } = this.editingAnimationsComponent.extra.animations[
      this.componentAnimationVal
    ];
    const pointKeys = Object.keys(points);
    const pointId = pointKeys.find(
      id => points[id].fragmentId === this.componentFragmentId,
    );

    return pointId || "";
  }

  onClickDel() {
    this.$store.commit(
      "removeComponentAnimationFragment",
      this.componentFragmentId,
    );

    this.$store.commit("setComponentFragmentId", "");
  }

  get startTime(): number {
    const { points } = this.editingAnimationsComponent.extra.animations[
      this.componentAnimationVal
    ];

    return this.pointId && points[this.pointId].startTime;
  }

  set startTime(val) {
    const newAnimations = cloneDeep(
      this.editingAnimationsComponent.extra.animations,
    );

    newAnimations[this.componentAnimationVal].points[
      this.pointId
    ].startTime = Number(val);

    this.$store.commit("updateComponentExtra", {
      id: this.editingAnimationsComponentId,
      newExtra: {
        animations: newAnimations,
      },
    });
  }

  handleChangeFragment(key: string) {
    this.componentFragmentId = key;
    this.$store.commit("setComponentActiveActionId", "");
  }
}
</script>

<style lang="less" scoped>
.component-fragment-list {
  .row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }

  .add,
  .remove {
    display: inline-block;
    cursor: pointer;
    padding: 0 5px;
    line-height: 20px;
    margin-right: 2px;

    &:hover {
      background: #eee;
    }
  }

  .fragment-list {
    display: flex;
    align-items: center;
  }

  .fragment {
    display: inline-block;
    padding: 0 10px;
    line-height: 20px;
    margin-right: 10px;
    margin-bottom: 5px;
    border: 1px solid #ccc;
    cursor: pointer;
  }

  .active {
    background: #64b5f6;
    color: #fff;
  }
}
</style>
