<template>
  <div v-if="fragmentId">
    <el-button
      size="small"
      @click="onClickBtn"
      :disabled="!actionList.length"
      >{{ isPlayingFragment ? "停止" : "预览" }}</el-button
    >
  </div>
</template>

<script lang="ts">
import { AnimationAction, types } from "@/pages/index/store/modules/animations";
import { Message } from "element-ui";
import { Vue, Component } from "vue-property-decorator";

@Component
export default class ComponentFragmentPlay extends Vue {
  get fragmentId() {
    return this.$store.state.componentFragmentId;
  }

  get animationVal() {
    return this.$store.state.componentAnimationVal;
  }

  get editingAnimationsComponent() {
    return this.$store.getters.editingAnimationsComponent;
  }

  get actionList(): AnimationAction[] {
    const animation = this.editingAnimationsComponent.extra.animations[
      this.animationVal
    ];
    return (animation && animation.fragments[this.fragmentId]) ?? [];
  }

  get isPlayingFragment() {
    return this.$store.state.moduleAnimations.isPlayingFragment;
  }

  playFragment() {
    this.$store.commit(types.mutations.playFragment, this.actionList);
  }

  stopFragment() {
    this.$store.commit(types.mutations.stopFragment);
  }

  onClickBtn() {
    if (this.isPlayingFragment) {
      this.stopFragment();
    } else {
      this.playFragment();
    }
  }
}
</script>

<style lang="less" scoped></style>
