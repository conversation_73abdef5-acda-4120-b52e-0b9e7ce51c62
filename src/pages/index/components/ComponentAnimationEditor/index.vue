<template>
  <div class="component-animation-editor">
    <editing-animations-components />
    <component-animation-val-selector />
    <component-animation-audio />
    <component-fragment-list />
    <component-add-action />
    <component-action-editor />
    <component-action-list />
    <component-fragment-play />
  </div>
</template>

<script lang="ts">
import { Vue, Component } from "vue-property-decorator";
import EditingAnimationsComponents from "./EditingAnimationsComponents/index.vue";
import ComponentAnimationValSelector from "./ComponentAnimationValSelector/index.vue";
import ComponentAnimationAudio from "./ComponentAnimationAudio/index.vue";
import ComponentFragmentList from "./ComponentFragmentList/index.vue";
import ComponentAddAction from "./ComponentAddAction/index.vue";
import ComponentActionEditor from "./ComponentActionEditor/index.vue";
import ComponentActionList from "./ComponentActionList/index.vue";
import ComponentFragmentPlay from "./ComponentFragmentPlay/index.vue";

@Component({
  components: {
    EditingAnimationsComponents,
    ComponentAnimationValSelector,
    ComponentAnimationAudio,
    ComponentFragmentList,
    ComponentAddAction,
    ComponentActionEditor,
    ComponentActionList,
    ComponentFragmentPlay,
  },
})
export default class ComponentAnimationEditor extends Vue {}
</script>

<style lang="less" scoped>
.component-animation-editor {
  padding: 20px 20px 5px;
}
</style>
