<template>
  <div class="animation-selector">
    <el-row class="row">
      <el-col :span="5">
        启动时机
      </el-col>
      <el-col :span="14">
        <el-select
          v-model="componentAnimationVal"
          @change="handleComponentAnimationVal"
          size="mini"
        >
          <el-option
            v-for="item in componentAnimationConfig"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-col>
      <el-col :span="5">
        <el-button size="small" @click="onClickPreviewBtn">{{
          isPlayingAnimation ? "停止" : "预览"
        }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { types } from "@/pages/index/store/modules/animations";
import { Message } from "element-ui";
import { Vue, Component } from "vue-property-decorator";

@Component
export default class ComponentAnimationValSelector extends Vue {
  get isPlayingAnimation() {
    return this.$store.state.moduleAnimations.isPlayingAnimation;
  }

  onClickPreviewBtn() {
    if (this.isPlayingAnimation) {
      this.stopAnimation();
    } else {
      this.playAnimation();
    }
  }

  playAnimation() {
    if (!this.componentAnimationVal) {
      Message.warning("请先选择启动时机");
      return;
    }
    this.$store.commit(
      types.mutations.playAnimation,
      this.editingAnimationsComponent.extra.animations[
        this.componentAnimationVal
      ],
    );
  }

  stopAnimation() {
    this.$store.commit(types.mutations.stopAnimation);
  }

  get editingAnimationsComponentId(): string {
    return this.$store.state.editingAnimationsComponentId;
  }

  get editingAnimationsComponent(): Component {
    return this.$store.getters.editingAnimationsComponent;
  }

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  set componentAnimationVal(val) {
    const { animations } = this.editingAnimationsComponent.extra;
    const initAnimationData = {
      audio: {
        url: "",
        delay: 0,
      },
      fragments: {},
      points: {},
    };

    /**
     * @description 初始化 animations 数据
     */
    if (!animations) {
      this.$store.commit("updateComponentExtra", {
        id: this.editingAnimationsComponentId,
        newExtra: {
          animations: {
            [val]: initAnimationData,
          },
        },
      });
    }
    if (animations && !animations[val]) {
      this.$store.commit("updateComponentExtra", {
        id: this.editingAnimationsComponentId,
        newExtra: {
          animations: Object.assign(animations, {
            [val]: initAnimationData,
          }),
        },
      });
    }

    this.$store.commit("setComponentAnimationVal", val);
  }

  get componentAnimationConfig() {
    return this.$store.state.componentAnimationConfig;
  }

  handleComponentAnimationVal() {
    this.$store.commit("setComponentFragmentId", "");
    this.$store.commit("setComponentActiveActionId", "");
  }
}
</script>

<style lang="less" scoped>
.animation-selector {
  .row {
    font-size: 14px;
    margin-bottom: 10px;
    text-align: left;
    display: flex;
    align-items: center;
  }
}
</style>
