<template>
  <div v-if="componentAnimationVal && componentFragmentId" class="add-action">
    <div class="add-action-effect">
      <el-row class="add-action-effect-row">
        <el-col v-if="componentActiveActionId && activeAction" :span="10" class="add-action-effect-row-effect-name"> 动画效果：{{ activeAction.value.anim.name }} </el-col>
        <el-col :span="componentActiveActionId ? 5 : 10" style="position: relative">
          <el-button :disabled="!currentIds.length" size="mini" @click="actionlistVisible = !actionlistVisible">{{ componentActiveActionId ? "更改" : "添加动画效果" }}</el-button>
          <ul v-if="actionlistVisible" class="add-action-list" ref="addActionList">
            <li v-for="item in options" :key="item.value" :class="['add-action-list-item', { 'has-child': item.children && item.children.length }]">
              <div>{{ item.label }}</div>
              <ul v-if="item.children && item.children.length" class="add-action-sub-list">
                <li v-for="subItem in item.children" :key="subItem.value" class="add-action-sub-list-item" @click="onClickAction(item, subItem)">
                  <div>{{ subItem.label }}</div>
                </li>
              </ul>
            </li>
          </ul>
        </el-col>
        <el-col v-if="componentActiveActionId" :span="5">
          <el-button v-if="componentActiveActionId" size="mini" @click="removeAction(componentActiveActionId)">删除</el-button>
        </el-col>
      </el-row>
      <el-row style="color: red;" v-if="componentActiveActionId && activeAction && getShowErrorMsg">
        <el-col :span="20" style="text-align: left;">
          <span>{{ getShowErrorMsg }}</span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script lang="ts">
import { AfterType, AnimationAction, Moment, SpineAnimationAction, CocosAnimationAction } from "@/pages/index/store/modules/animations";
import { v4 as uuidv4 } from "uuid";
import { cloneDeep } from "lodash-es";
import { Vue, Component } from "vue-property-decorator";
import { AnimType, CustomSpeeds, options } from "../../AnimationEditor/AddAction/options";

@Component
export default class ComponentAddAction extends Vue {
  actionlistVisible = false;

  removeSubscribeFn!: () => void;

  subscribeReplaceCurrentComponentIds() {
    return this.$store.subscribe(mutation => {
      if (mutation.type === "replaceCurrentComponentIds" || mutation.type === "cocos/replaceCurrentComponentIds") {
        this.$store.commit("setComponentActiveActionId", "");
        // unselectedComponents
      }
    });
  }

  mounted() {
    document.addEventListener("mousedown", this.onClickAway, true);
    this.removeSubscribeFn = this.subscribeReplaceCurrentComponentIds();
  }

  destroyed() {
    document.removeEventListener("mousedown", this.onClickAway), true;
    this.removeSubscribeFn();
  }

  onClickAway(e: MouseEvent) {
    if (!this.actionlistVisible || !this.$refs.addActionList) {
      return;
    }
    if (!(this.$refs.addActionList as HTMLElement).contains(e.target as Node)) {
      this.actionlistVisible = false;
    }
  }

  get editingAnimationsComponent(): Component {
    return this.$store.getters.editingAnimationsComponent;
  }

  get componentAnimationVal(): string {
    return this.$store.state.componentAnimationVal;
  }

  get editingAnimationsComponentId(): string {
    return this.$store.state.editingAnimationsComponentId;
  }

  get componentFragmentId(): string {
    return this.$store.state.componentFragmentId;
  }

  get componentActiveActionId(): string {
    return this.$store.state.componentActiveActionId;
  }

  get currentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get activeAction(): AnimationAction | SpineAnimationAction | CocosAnimationAction | undefined {
    const actions = this.editingAnimationsComponent.extra.animations[this.componentAnimationVal].fragments[this.componentFragmentId];

    return actions.find((i: any) => i.id === this.componentActiveActionId);
  }

  get getShowErrorMsg() {
    if (!this.componentActiveActionId || !this.activeAction) return "";
    const { componentMap } = this.$store.state;
    const { activeAction, componentAnimationVal } = this;
    // console.log("...componentActiveActionId", activeAction, componentActiveActionId, activeAction, componentAnimationVal);
    const {
      componentId,
      value: {
        anim: { type },
      },
    } = activeAction as AnimationAction;
    if (componentMap[componentId].tag === "dragArea" && ["afterWrong", "afterDragAreaWrong"].includes(componentAnimationVal) && [2, 4, 12].includes(type)) {
      return `错误后答题区【${componentId}】不可消失，请更改或删除`;
    }
    return "";
  }

  get options() {
    const { currentIds, activeAction } = this;
    const { componentMap } = this.$store.state;
    let optionsNew = options;
    // 拖拽题 拖拽元素 答错后(afterWrong) 答题区不可以设置为消失动画
    // 拖拽题 答题区 答错后(afterDragAreaWrong) 答题区不可以设置为消失动画
    console.log(
      this.componentAnimationVal,
      this.editingAnimationsComponentId,
      currentIds,
      componentMap,
      currentIds.find(id => {
        return componentMap[id].tag === "dragArea";
      }),
      ["afterWrong", "afterDragAreaWrong"].includes(this.componentAnimationVal),
      optionsNew,
    );
    if (
      currentIds.find(id => {
        return componentMap[id].tag === "dragArea";
      }) &&
      ["afterWrong", "afterDragAreaWrong"].includes(this.componentAnimationVal)
    ) {
      optionsNew = optionsNew.filter(option => option.value !== "exit");
    }

    if (activeAction) {
      const { animType } = activeAction.value;

      // 不允许不同 animType 之间的动画互相切换，降低 updateAction 的复杂度
      return optionsNew.filter(option => option.animType === animType);
    }

    // 非 spine 组件 过滤掉 spine 动画选项
    if (
      currentIds.find(id => {
        return componentMap[id].type !== "spine";
      })
    ) {
      optionsNew = optionsNew.filter(option => option.animType !== "spine");
    }

    // 非 cocos 组件 过滤掉 cocos 动画选项
    if (
      currentIds.find(id => {
        return componentMap[id].type !== "cocosAni";
      })
    ) {
      optionsNew = optionsNew.filter(option => option.animType !== "cocosAni");
    }

    return optionsNew;
  }

  get componentIdsAllObjectType(): { id: string; subIds: string[] }[] {
    return this.$store.getters.componentIdsAllObjectType;
  }

  onClickAction(
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    this.actionlistVisible = false;
    const copySubItem = cloneDeep(subItem);

    // 更新组件动画
    if (this.componentActiveActionId) {
      this.updateActionConfigData(item, copySubItem);
      return;
    }

    this.currentIds.forEach(id => {
      const valid = this.componentIdsAllObjectType.some(obj => obj.id === id);
      if (valid) {
        this.addAction(id, item, copySubItem);
      } else {
        this.$message.error("子组件不允许添加动画");
      }
    });
  }

  updateActionConfigData(
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    this.$store.commit("updateComponentActionConfigData", {
      type: item.animType,
      data: subItem.data,
      name: item.label,
    });
    const customSpeed = CustomSpeeds[subItem.data.type as AnimType];
    if (customSpeed) {
      this.$store.commit("setComponentActionSpeed", {
        speed: customSpeed,
      });
    }
  }

  addAction(
    componentId: string,
    item: {
      label: string;
      animType: "tween" | "animation" | "spine" | "cocosAni";
    },
    subItem: { label: string; value: string; data: any },
  ) {
    switch (item.animType) {
      case "tween": {
        const action: AnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            speed: CustomSpeeds[subItem.data.type as AnimType] || 2,
            repeat: 0,
            anim: cloneDeep(subItem.data),
          },
        };

        this.$store.commit("addComponentAnimationAction", { action });
        this.$store.commit("setComponentActiveActionId", action.id);
        break;
      }

      case "spine": {
        const action: SpineAnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          delay: 0,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            loop: false,
            animList: [],
            endTimeScale: 1,
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            anim: cloneDeep(subItem.data),
          },
        };
        this.$store.commit("addComponentAnimationAction", { action });
        this.$store.commit("setComponentActiveActionId", action.id);
        break;
      }
      case "cocosAni": {
        const action: CocosAnimationAction = {
          id: uuidv4(),
          componentId,
          moment: Moment.BEFORE,
          delay: 0,
          audio: {
            url: "",
            moment: Moment.BEFORE,
            delay: 0,
          },
          after: {
            loop: false,
            animList: [],
            endTimeScale: 1,
            type: AfterType.NONE,
          },
          value: {
            name: item.label,
            animType: item.animType,
            anim: cloneDeep(subItem.data),
          },
        };

        this.$store.commit("addComponentAnimationAction", { action });
        this.$store.commit("setComponentActiveActionId", action.id);
        break;
      }

      default: {
        break;
      }
    }
  }

  removeAction(actionId: string) {
    const index = this.editingAnimationsComponent.extra.animations[this.componentAnimationVal].fragments[this.componentFragmentId].findIndex((action: AnimationAction) => action.id === actionId);

    this.$store.commit("removeComponentAnimationAction", {
      animationVal: this.componentAnimationVal,
      fragmentId: this.componentFragmentId,
      index,
    });

    this.$store.commit("setComponentActiveActionId", "");
  }
}
</script>

<style lang="less" scoped>
.add-action {
  position: relative;

  &-effect-row {
    margin-bottom: 10px;
    font-size: 14px;
    text-align: left;
    display: flex;
    align-items: center;

    &-effect-name {
      font-weight: bold;
      font-size: 15px;
    }
  }

  &-list {
    margin: 0;
    position: absolute;
    top: 100%;
    left: 20%;
    z-index: 10;
    padding: 5px 0;
    list-style: none;
    background: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    user-select: none;

    &-item {
      position: relative;
      padding: 0px 20px 0 10px;
      white-space: nowrap;
      line-height: 20px;
      min-width: 40px;

      &:hover {
        background: #f9f9f9;
      }

      &:hover .add-action-sub-list {
        display: block;
        bottom: -5px;
        left: 100%;
      }
    }
  }

  &-sub-list {
    background: #fff;
    position: absolute;
    display: none;
    list-style: none;
    padding: 5px 0;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    &-item {
      padding: 0 10px;
      white-space: nowrap;
      line-height: 20px;
      min-width: 40px;
      cursor: pointer;

      &:hover {
        background: #f9f9f9;
      }
    }
  }
}

.has-child::after {
  content: "";
  width: 0;
  height: 0;
  border-width: 4px 7px;
  border-style: solid;
  border-color: transparent transparent transparent rgba(0, 0, 0, 0.75);
  text-shadow: 0 4px 9px rgba(0, 0, 0, 0.34);
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
}
</style>
