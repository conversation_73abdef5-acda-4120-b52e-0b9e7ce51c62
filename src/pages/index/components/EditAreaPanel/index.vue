<template>
  <div class="edit-area" :style="{ width: editorAreaWidth + 'px' }">
        <div class="resizer-left" :style="{ background: isResizerActive ? '#298df8' : '' }" @mousedown="resizerActive" />
        <!-- 编辑组件属性 -->
        <component :is="asyncComp.EditArea" v-if="isNormalMode" />
        <!-- 编辑全局动画 可以异步 -->
        <component :is="asyncComp.AnimationEditor" v-if="isAnimationMode" />
        <!-- 编辑组件动画 可以异步 -->
        <el-drawer :modal="false" :visible="isEditingComponentAnimations" :with-header="false" :wrapper-closable="false" size="360px" v-if="asyncComp.ComponentAnimationEditor">
          <component :is="asyncComp.ComponentAnimationEditor" v-if="isEditingComponentAnimations && asyncComp.ComponentAnimationEditor" />
        </el-drawer>
      </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { ExtendedVue } from "vue/types/vue";

@Component
export default class EditAreaPanel extends Vue {
  editorAreaWidth = 360;

  isResizerActive = false;

  get isNormalMode() {
    return this.$store.getters.isNormalMode;
  }

  get isAnimationMode() {
    return this.$store.getters.isAnimationMode;
  }

  get isEditingComponentAnimations() {
    return this.$store.state.isEditingComponentAnimations;
  }

  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  @Watch("isEditingComponentAnimations")
  async onEditingComponentAnimationsChange(val: boolean) {
    if (val) {
      this.renderAsyncComp("ComponentAnimationEditor");
    }
  }

  @Watch("isAnimationMode")
  async onAnimationModeChange(val: boolean) {
    console.log('isAnimationMode')
    if (val) {
      this.renderAsyncComp("AnimationEditor");
    }
  }
  @Watch("initialDataLoaded")
  async onDataLoadedChange(val: boolean) {
    if (val) {
      this.renderAsyncComp("EditArea");
    }
  }

  created() {
    this.renderAsyncComp("EditArea");
  }

  asyncComp: {
    [x: string]: ExtendedVue<Vue, unknown, unknown, unknown, Record<never, any>> | null;
  } = {
    AnimationEditor: null,
    EditArea: null,
    ComponentAnimationEditor: null,
  };

  asyncCompImports = {
    EditArea: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/EditArea/index.vue").then(component => {
          this.asyncComp.EditArea = Vue.extend(component.default);
        });
      });
    },
    AnimationEditor: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "AnimationEditor" */ "@/pages/index/components/AnimationEditor/index.vue").then(component => {
          this.asyncComp.AnimationEditor = Vue.extend(component.default);
        });
      });
    },
    ComponentAnimationEditor: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "ComponentAnimationEditor" */ "@/pages/index/components/ComponentAnimationEditor/index.vue").then(component => {
          this.asyncComp.ComponentAnimationEditor = Vue.extend(component.default);
        });
      });
    }
  };

  renderAsyncComp(key = "") {
    if (key) {
      this.asyncCompImports[key] && (this.asyncCompImports[key] as any)();
      return;
    }
  }

  resizerActive() {
    this.isResizerActive = true;
    document.documentElement.style.cursor = "col-resize";
    document.addEventListener("mousemove", this.onResizerMove);
    document.addEventListener("mouseleave", this.resizerInactive);
    document.addEventListener("mouseup", this.resizerInactive, true);
  }

  resizerInactive() {
    this.isResizerActive = false;
    document.documentElement.style.cursor = "auto";
    document.removeEventListener("mousemove", this.onResizerMove);
    document.removeEventListener("mouseleave", this.resizerInactive);
    document.removeEventListener("mouseup", this.resizerInactive);
  }

  onResizerMove(e: MouseEvent) {
    window.dispatchEvent(new Event("resize"));
    this.editorAreaWidth = Math.max(this.editorAreaWidth - e.movementX, 360);
  }
}
</script>

<style scoped lang="less">
.edit-area {
  position: relative;
  overflow: scroll;
  background: #fff;
  flex-shrink: 0;
  font-size: 12px;

  .resizer-left {
    position: absolute;
    z-index: 101;
    left: -4px;
    top: 0;
    width: 8px;
    height: 100%;
    cursor: col-resize;
  }

  /deep/ .el-drawer__wrapper {
    width: 380px;
    left: auto;
    overflow: visible;
    z-index: 100 !important;
  }
}
</style>
