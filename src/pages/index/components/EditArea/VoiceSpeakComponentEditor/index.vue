<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="麦克风属性" name="sprite-properties">
      <div class="flex">
        <ya-input-number label="答题时长" property="answerDuration" type="number" :componentIds="currentComponentIds" :options="{
            min: 1,
            step: 1,
            max: 100,
            type: 'positive integer',
          }" />
      </div>
    </el-collapse-item>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";
@Component({
  components: {
    BaseProperties,
    YaInputNumber,
  },
})
export default class VoiceComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;
  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }


  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
