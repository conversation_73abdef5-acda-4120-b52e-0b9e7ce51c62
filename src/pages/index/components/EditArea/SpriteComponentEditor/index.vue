<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="图片属性" name="sprite-properties">
      <ImageSelect
        :src.sync="componentTexture"
        :componentId=" componentId"
        :isShowDeleteBtn="false"
        :disableReplace="!textureEditable"
        :disableCrop="!textureEditable"
        :isChangePropertiesSize="true"/>
      <!-- ek-sign="left90" class="btn-flex" -->
      <flip-editor />
    </el-collapse-item>

    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import FlipEditor from "../BaseProperties/FlipEditor/index.vue";
import YaInput from "../EditComponents/Input/index.vue";
import ImageUpload from "../ImageUpload/index.vue";
import ImageSelect from "@/components/ImageSelect/index.vue";
@Component({
  components: {
    ExtraEditor,
    BaseProperties,
    YaInput,
    ImageUpload,
    ImageSelect,
    FlipEditor,
  },
})
export default class SpriteComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get componentTexture() {
    return this.$store.state.componentMap[this.componentId].properties.texture;
  }

  set componentTexture(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        texture: val,
      },
    });
  }

  get textureEditable() {
    let tempValue = true;
    const { editable = { properties: {} }} = this.$store.state.componentMap[this.componentId];

    if(!editable || (editable.properties && editable.properties.texture === false)) {
      tempValue = false;
    }
    return tempValue;
  }
}
</script>
<style scoped lang="less"></style>
