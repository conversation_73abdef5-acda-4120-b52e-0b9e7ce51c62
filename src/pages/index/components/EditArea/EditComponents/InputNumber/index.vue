<template>
  <div class="container">
    <span class="label" :class="options.required ? 'required' : ''">
      {{ label }}
    </span>
    <el-input
      :value="displayValue"
      @input="validate($event)"
      @change="onChange"
      size="small"
      type="number"
      v-bind="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker
      firstLevelProperty="properties"
      :secondLevelProperty="property"
      :componentIds="componentIds"
      :required="options.required"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";
import { Message } from "element-ui";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaInputNumber extends Mixins(
  GetComponentPropertiesDisabledMixin,
) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  userInput = null;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property] !==
        firstVal,
    );
    if (hasDifferentVal) {
      return "";
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: Number(val),
      },
    });
  }

  get displayValue() {
    if (this.userInput !== null) {
      return this.userInput;
    }

    return this.v;
  }
  validate(val: any) {
    //正整数
    if (this.options.type === "positive integer") {
      val = val.replace(/[^0-9]+/g, "");
    }
    if (this.options.maxLength && val.length > this.options.maxLength) {
      val = val.slice(0, this.options.maxLength);
    }
    this.userInput = val;
  }

  onChange(val: string) {
    // if (this.options.type === "positive integer") {
    //   if (this.options.max && val > this.options.max) {
    //     val = this.options.max;
    //     Message.error("限制输入为最大值");
    //   }
    //   if (this.options.min && val < this.options.min) {
    //     Message.error("限制输入为最小值");
    //     val = this.options.min;
    //   }
    // }
    if (this.options.max && val > this.options.max) {
      val = this.options.max;
      Message.error("限制输入为最大值");
    }
    if (this.options.min && val < this.options.min) {
      Message.error("限制输入为最小值");
      val = this.options.min;
    }
    this.v = val;
    this.userInput = null;
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  flex-grow: 1;

  .el-input {
    margin-left: 5px;
    flex-grow: 1;
  }

  /deep/ .el-input__inner {
    padding-right: 0;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 30px;
    flex-shrink: 0;
  }
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
