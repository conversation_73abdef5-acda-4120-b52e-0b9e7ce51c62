<template>
  <div class="container">
    <span class="label" :class="options.required ? 'required' : ''">
      {{ label }}
    </span>
    <audio-select
      style="margin-left:8px"
      :value="v"
      @change="onChange($event)"
      :options="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker
      firstLevelProperty="properties"
      :secondLevelProperty="property"
      :componentIds="componentIds"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    AudioSelect,
    ComponentPropertiesEditableChecker,
  },
})
export default class YaAudioSelect extends Mixins(
  GetComponentPropertiesDisabledMixin,
) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property] !==
        firstVal,
    );
    if (hasDifferentVal) {
      return "";
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val,
      },
    });
  }

  onChange(val: string) {
    this.v = val;
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  flex-grow: 1;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
