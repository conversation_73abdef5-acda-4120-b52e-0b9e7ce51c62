<template>
  <div class="container">
    <span class="label" :class="option.required ? 'required' : ''">{{ label }}</span>
    <el-select v-model="v" multiple :placeholder="placeholder" size="small" :disabled="getComponentPropertiesDisabled('properties', property)">
      <el-option v-for="item in showOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
    </el-select>
    <component-properties-editable-checker firstLevelProperty="properties" :secondLevelProperty="property" :componentIds="componentIds" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";
import { Message } from "element-ui";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaSelect extends Mixins(GetComponentPropertiesDisabledMixin) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  @Prop({
    default: () => ({}),
  })
  option!: any;

  get currentId() {
    return this.$store.state.currentComponentIds[0];
  }
  get currentIds() {
    return this.$store.state.currentComponentIds;
  }
  get showOptions() {
    return this.options;
  }
  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]].properties[this.property];
    const hasDifferentVal = this.componentIds.some(id => this.$store.state.componentMap[id].properties[this.property] !== firstVal);
    if (hasDifferentVal) {
      return this.defaultValue;
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return JSON.parse(JSON.stringify(firstVal));
  }

  set v(val) {
    const category = this.$store.state.template.category;
    if (category == 1100) {
         Message.warning("当前题型不支持更换");
      return;
    }
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;

  .el-select {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
