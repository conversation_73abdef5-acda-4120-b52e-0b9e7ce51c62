<template>
  <div class="container">
    <span class="label" :class="options.required ? 'required' : ''">{{ label }}</span>
    <el-input
      @blur="blurHandler"
      :maxlength="options.maxlength ? options.maxlength : 100"
      @input="validate($event)"
      @change="onChange"
      :value="displayValue"
      size="small"
      :type="type"
      v-bind="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker firstLevelProperty="properties" :secondLevelProperty="property" :componentIds="componentIds" :required="options.required" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaInput extends Mixins(GetComponentPropertiesDisabledMixin) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  userInput = null;

  englishReg = /[^a-zA-Z0-9\s-\\.!@#\\$%\\\\^&\\*\\)\\(\\+=\\{\\}\\[\]\\/",'<>~\\·`\\?:;|]+/g;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]].properties[this.property];
    const hasDifferentVal = this.componentIds.some(id => this.$store.state.componentMap[id].properties[this.property] !== firstVal);
    if (hasDifferentVal) {
      return "";
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val,
      },
    });
  }

  get displayValue() {
    if (this.userInput !== null) {
      return this.userInput;
    }

    return this.v;
  }

  blurHandler() {
    this.$emit("blur", this.displayValue);
    console.log("blur", this.displayValue);
  }

  validate(val: any) {
    //正整数
    if (this.options.type === "positive integer") {
      val = val.replace(/[^0-9]+/g, "");
    } else if (this.options.type === "english") {
      val = val.replace(this.englishReg, "");
    }
    if (this.options.maxLength && val.length > this.options.maxLength) {
      val = val.slice(0, this.options.maxLength);
    }
    this.userInput = val;
  }
  onChange(val: string) {
    this.v = val;
    this.userInput = null;
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
