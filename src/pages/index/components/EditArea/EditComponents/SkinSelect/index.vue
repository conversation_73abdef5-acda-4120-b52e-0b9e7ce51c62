<template>
  <div class="skin-select-library">
    <!-- <el-dialog
      width="300px"
      :visible="visible"
      @update:visible="handleUpdateVisible"
      title="更改样式"
      @closed="handleClosed"
    >  -->
    <div class="title" v-if="!disSelsectAll">
      <label class="name"> {{ checkTitle }} </label>
      <el-checkbox v-model="changeAll" class="skin-change-all">全部更改 </el-checkbox>
    </div>
    <div class="skin-select-list">
      <div v-for="(item, index) in skinSelectList" :key="item.type" @click="handleClickShapeItem(item)" class="skin-select-list--item">
        <div class="item">
          <img :src="item.icon" />
          <span v-if="curIndex == index" class="check transition-delay"
            ><svg version="1.1" viewBox="0 0 26 26" class="svg-icon svg-fill" style="width: 18px; height: 18px">
              <g transform="translate(2 2)" fill-rule="nonzero" stroke="#FFF" stroke-width="2" fill="none">
                <rect pid="0" fill="#35CC67" x="-1" y="-1" width="24" height="24" rx="12"></rect>
                <path pid="1" stroke-linecap="round" stroke-linejoin="round" d="M7 12l3 3 6.116-7"></path>
              </g></svg></span>
        </div>

        <!-- <label>{{ item.label }}</label> -->
      </div>
    </div>
    <!-- </el-dialog> -->
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";

export enum selectType {
  yellow = 1,
  purple = 2,
  green = 3,
}

export interface SkinSelectLibraryItem {
  type: selectType;
  label: string;
  icon: string;
}

@Component
export default class SkinSelect extends Vue {
  @Prop({
    required: true,
  })
  skinSelectList!: SkinSelectLibraryItem[];
  @Prop({
    required: true,
  })
  disSelsectAll!: boolean;

  @Prop({
    required: false,
  })
  title!: string;

  @Prop({
    required: false,
  })
  curIndex!:number ;
  /** 是否全部生效 */
  changeAll = false;

  get checkTitle() {
    let title = "样式更改";
    if (this.title) {
      title = this.title;
    }
    return title;
  }
  handleUpdateVisible(visible: boolean) {
    this.$emit("showSkin", visible);
  }

  handleClickShapeItem(shapeItem: SkinSelectLibraryItem) {
    console.log("#------>", this.changeAll);
    const data = {
      type: cloneDeep(shapeItem.type),
      changeAll: this.changeAll,
    };
    this.$emit("selectSkin", data);
    this.handleUpdateVisible(false);
  }
}
</script>

<style lang="less" scoped>
.title {
  display: flex;
  position: relative;
  align-items: center;
  .name {
    margin-left: 0%;
    font-size: 15px;
  }
  .skin-check-box {
    margin-right: 0%;
  }
  .skin-change-all {
    position: absolute;
    right: 0%;
  }
}
.skin-select-library {
  .skin-select-list {
    display: flex;
    // justify-content: space-around;
    align-items: center;
    width: 285px;
    flex-wrap: wrap;

    .skin-select-list--item {
      // border: 1px solid #409eff;
      background-color: #f3f3f7;
      margin-right: 5px;
      margin-top: 5px;
      box-sizing: border-box;
      cursor: pointer;
      min-width: 90px;
      max-width: 90px;
      min-height: 90px;
      max-height: 90px;

      display: flex;
      align-items: center;
      flex-direction: column;

      img {
        box-sizing: border-box;
        width: 90px;
        height: 90px;
        margin: auto;
      }

      .item {
        width: 90px;
        height: 90px;
        position: relative;
      }

      // label {
      //   height: 20px;
      //   line-height: 20px;
      //   margin-top: 10px;
      // }
    }

    .selected {
      img {
        border: 1px solid #409eff;
      }
    }
  }
}
.check {
  top: 1px;
  right: 1px;
  width: 18px;
  height: 18px;
  position: absolute;
  text-align: center;
}
.svg-fill {
  fill: currentColor;
  stroke: none;
}
.transition-delay {
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.svg-icon {
  display: inline-block;
  width: 18px;
  height: 18px;
  color: inherit;
  vertical-align: middle;
  fill: none;
  stroke: currentColor;
}
</style>
