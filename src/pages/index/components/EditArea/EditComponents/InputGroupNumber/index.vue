<template>
  <div class="container">
    <span class="label" :class="options.required ? 'required' : ''">{{
      label
    }}</span>
    <el-input
      v-model="v1"
      size="small"
      :type="type"
      v-bind="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <div>-</div>
    <el-input
      v-model="v2"
      size="small"
      :type="type"
      v-bind="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker
      firstLevelProperty="properties"
      :secondLevelProperty="property"
      :componentIds="componentIds"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaInputGroupNumber extends Mixins(
  GetComponentPropertiesDisabledMixin,
) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "number",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  @Prop({
    required: false,
  })
  content!: string;

  get v1() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property][0];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property][0] !==
        firstVal,
    );
    if (hasDifferentVal) {
      return "";
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v1(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: [+val, this.v2],
      },
    });
  }

  get v2() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property][1];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property][1] !==
        firstVal,
    );
    if (hasDifferentVal) {
      return "";
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v2(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: [this.v1, +val],
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .required {
    &::before {
      content: "*";
      color: red;
    }
  }
  .el-input {
    margin-left: 10px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
