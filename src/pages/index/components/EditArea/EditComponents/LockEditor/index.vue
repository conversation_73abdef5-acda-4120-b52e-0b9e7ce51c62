<template >
  <div>
    <span class="label">锁定</span>
    <div class="container1">
      <el-button :disabled="v" class="zyb-btn zyb-lg-btn" @click="handleChange(false)">
        {{ "锁定" }}
      </el-button>
      <el-button :disabled="!v" class="zyb-btn zyb-lg-btn" @click="handleChange(true)">
        {{ "取消锁定" }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue } from "vue-property-decorator";
export default class LockEditor extends Vue {
  get componentIds() {
    return this.$store.state.currentComponentIds;
  }
  get v() {
    let isLock = false; // 不锁
    const componentIds = this.componentIds;
    const firstVal = this.$store.state.componentMap[componentIds[0]].dragable;
    if (firstVal == false) {
      isLock = true;
    } else {
      isLock = false;
    }
    for (let i = 1; i < componentIds.length; i++) {
      const firstVal = this.$store.state.componentMap[componentIds[i]].dragable;
      if (firstVal == false) {
        isLock = isLock && true;
      } else {
        isLock = isLock && false;
      }
    }
    return isLock;
  }
  set v(val) {
    const componentIds = this.componentIds;
    this.$store.commit("setComponentsDragable", {
      ids: componentIds,
      dragable: val,
    });
  }

  handleChange(val: any) {
    this.v = val;
  }
}
</script>

<style scoped lang="less">
.label {
  display: flex;
  font-size: 15px;
  font-weight: unset;
}
.container1 {
  display: flex;
  align-items: center;
  margin-top: 5px;
}
.required {
  &::before {
    content: "*";
    color: red;
  }
}
.zyb-lg-btn {
  width: 114px;
  height: 28px;
  margin-bottom: 5px;
}
.zyb-btn.disabled,
.zyb-btn[disabled] {
  outline: none;
  opacity: 0.5;
  cursor: no-drop;
}
.zyb-btn {
  color:#333 !important;
  font-weight: 430 !important;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  border-radius: 2px;
  color: var(--font-gray-darker);
  background-color: var(--btn-background);
  border: 1px solid var(--btn-border-color);
}
</style>
