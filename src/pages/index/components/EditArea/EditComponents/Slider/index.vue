<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-slider
      v-model="v"
      v-bind="options"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker
      firstLevelProperty="properties"
      :secondLevelProperty="property"
      :componentIds="componentIds"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class Input extends Mixins(GetComponentPropertiesDisabledMixin) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property] !==
        firstVal,
    );
    if (hasDifferentVal) {
      return this.defaultValue;
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    // el-slider经常会触发set，当值一样的时候忽略掉
    if (val === this.v) {
      return;
    }
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
  }

  .el-slider {
    flex-grow: 1;
    padding: 0 15px;
  }
}
</style>
