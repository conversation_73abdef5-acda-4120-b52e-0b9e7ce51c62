<!--
 * @Date: 2021-09-18 13:11:04
 * @LastEditors: chxu
 * @LastEditTime: 2021-11-29 10:31:02
 * @FilePath: /src/pages/index/components/EditArea/EditComponents/ColorPicker/index.vue
 * @Author: chxu
-->
<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-color-picker
      v-model="v"
      :disabled="getComponentPropertiesDisabled('properties', property)"
    />
    <component-properties-editable-checker
      firstLevelProperty="properties"
      :secondLevelProperty="property"
      :componentIds="componentIds"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

const COCOS_DEFAULT_COLOR = "#ffffff";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaColorPicker extends Mixins(
  GetComponentPropertiesDisabledMixin,
) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]]
      .properties[this.property];
    const hasDifferentVal = this.componentIds.some(
      id =>
        this.$store.state.componentMap[id].properties[this.property] !==
        firstVal,
    );

    if (hasDifferentVal) {
      return "";
    }
    if (!firstVal) {
      return "";
    }
    return firstVal;
  }

  set v(val) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val ?? COCOS_DEFAULT_COLOR,
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  flex-grow: 1;

  .label {
    display: inline-block;
    text-align: left;
    padding-right: 8px;
  }

  .el-color-picker {
    margin-right: 0px;
  }
}
</style>
