<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-switch v-model="v" :disabled="getComponentPropertiesDisabled('properties', property)" />
    <component-properties-editable-checker firstLevelProperty="properties" :secondLevelProperty="property" :componentIds="componentIds" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class YaSwitch extends Mixins(GetComponentPropertiesDisabledMixin) {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: false,
  })
  defaultValue!: string;

  @Prop({
    required: false,
    default: false,
  })
  disabled!: boolean;

  get tag() {
    //  特殊tag 获取
    const firstVal = this.$store.state.componentMap[this.componentIds[0]].tag;
    return firstVal || "";
  }
  get v() {
    const firstVal = this.$store.state.componentMap[this.componentIds[0]].properties[this.property];
    const hasDifferentVal = this.componentIds.some(id => this.$store.state.componentMap[id].properties[this.property] !== firstVal);
    if (hasDifferentVal) {
      return this.defaultValue;
    }
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    if (this.property === "isFixed" && this.tag === "oneDragableObject") {
      this.$message.error(`该文本组件已设置‘单个拖拽元素’，不可设置【固定宽高】`);
      return;
    } else if (this.property === "isFixed" && this.tag === "dragableObject") {
      this.$message.error(`该文本组件已设置‘拖拽元素’，不可设置【固定宽高】`);
      return;
    }
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        [this.property]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 30px;
    margin-right: 5px;
  }

  .el-switch {
    margin-right: 0;
  }
}
</style>
