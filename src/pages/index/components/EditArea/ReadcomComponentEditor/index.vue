<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <!-- <el-collapse-item v-if="currentComponents.length === 1" title="阅读理解组件" name="sprite-properties">
      <div class="flex">
        <ya-select label="组件风格：" property="style" :options="styleOptions" :defaultValue="0" :componentIds="currentComponentIds" :option="{ required: false }" />
      </div>
      <div class="flex">
        <mu-select label="挂载组件：" property="mountList" :options="options" :defaultValue="[]" :componentIds="currentComponentIds" :option="{ required: false }" />
      </div>
    </el-collapse-item> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import YaSelect from "../EditComponents/Select/index.vue";
import MuSelect from "../EditComponents/MultipleSelect/index.vue";
@Component({
  components: {
    BaseProperties,
    YaSelect,
    MuSelect,
  },
})
export default class CounterComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;
  styleOptions = [
    { label: "卡通", value: 0 },
    // { label: "简洁", value: 1 },
  ];
  get componentId() {
    return this.currentComponentIds[0];
  }
  get currentId() {
    return this.$store.state.currentComponentIds[0];
  }
  get options() {
    const itemId = [];
    const currentId = this.currentId;
    for (const key of this.$store.state.componentIds) {
      if (typeof key == "string") {
        if (key != currentId) {
          itemId.push({ label: "组件：" + key, value: key });
        }
      } else {
        if (key.id != currentId) {
          itemId.push({ label: "组件：" + key.id, value: key.id });
          for (const nKey of key.subIds) {
            itemId.push({ label: "组件：" + nKey, value: nKey });
          }
        }
      }
    }
    return itemId;
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.container /deep/ .el-select {
  width: 192px;
}
</style>
