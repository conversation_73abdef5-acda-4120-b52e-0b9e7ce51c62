<template>
  <div class="container" v-if="isSubTemplateEdit || isSubTemplateCreate">
    <label v-if="label">{{ label }}</label>
    <el-checkbox v-model="checked" style="margin-left: 5px" :disabled="_disabled" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { get } from "lodash-es";
import { isSubTemplateEdit, isSubTemplateCreate } from "@/pages/index/common/utils/isTemplateEdit";

const getEditable = (val: undefined | boolean) => {
  return val ? true : val === undefined ? true : false;
};

/**
 * @description 组件属性是否可编辑选择器
 */
@Component
export default class ComponentPropertiesEditableChecker extends Vue {
  isSubTemplateEdit = isSubTemplateEdit;
  isSubTemplateCreate = isSubTemplateCreate;

  // 一级属性名
  @Prop({
    required: true,
    validator: val => val === "properties" || val === "extra" || val === "tag",
  })
  firstLevelProperty!: string;

  // 二级属性名
  @Prop({
    required: true,
  })
  secondLevelProperty!: string;

  // 组件 id 数组
  @Prop({
    required: true,
  })
  componentIds!: string[];

  // 标签
  @Prop({
    required: false,
  })
  label!: string;

  // 该属性是否必填，当该属性为必填项时，没填的时候不能勾选
  @Prop({
    required: false,
    default: false,
  })
  required!: boolean;

  @Prop({
    required: false,
    default: undefined,
  })
  disabled!: boolean;

  get _disabled(): boolean {
    const { disabled, required, hasVal } = this;
    if (disabled !== undefined) return disabled;

    return required && !hasVal;
  }

  get hasVal(): boolean {
    const { firstLevelProperty, secondLevelProperty } = this;
    const propertyPath = `${firstLevelProperty}.${secondLevelProperty}`;
    const val = get(this.componentMap[this.componentIds[0]], propertyPath);

    if (val === "" || val === undefined) return false;

    // 下拉选择多选的情况
    if (Array.isArray(val)) {
      if (!val.length) return false;
    }

    return true;
  }

  get componentMap(): State["componentMap"] {
    return this.$store.state.componentMap;
  }

  get checked() {
    const { firstLevelProperty, secondLevelProperty } = this;
    const propertyPath = `${firstLevelProperty}.${secondLevelProperty}`;

    const firstVal = getEditable(get(this.componentMap[this.componentIds[0]].editable, propertyPath));

    let hasDifferent = false;

    this.componentIds.forEach(componentId => {
      const propertyEditable = getEditable(get(this.componentMap[componentId].editable, propertyPath));

      if (firstVal !== propertyEditable) {
        hasDifferent = true;
      }
    });

    if (hasDifferent) return false;

    return !firstVal;
  }

  set checked(val: boolean) {
    const { firstLevelProperty, secondLevelProperty } = this;
    this.$store.commit("updateComponentEditable", {
      componentIds: this.componentIds,
      newEditable: {
        [firstLevelProperty]: {
          [secondLevelProperty]: !val,
        },
      },
    });
  }
}
</script>

<style lang="less" scoped>
.container {
  display: inline-block;
}
</style>
