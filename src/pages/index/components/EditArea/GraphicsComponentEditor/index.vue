<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      v-if="currentComponents.length === 1"
      title="绘图组件属性"
      name="sprite-properties"
    >
      <div class="flex">
        <ya-select
          label="展示结构："
          property="displayType"
          :options="displayTypeOptions"
          :defaultValue="1"
          :componentIds="currentComponentIds"
          :option="{ required: true }"
        />
      </div>
      <div class="flex">
        <ya-select
          label="画布边框："
          property="boardType"
          :defaultValue="1"
          :componentIds="currentComponentIds"
          :options="boardTypeOptions"
          :option="{ required: true }"
        />
      </div>
      <div class="flex">
        <zybColorPicker
          label="画笔颜色:"
          class="color-select"
          property="lineColor"
          :componentIds="currentComponentIds"
        ></zybColorPicker>
        <!-- <ya-color-picker
          label="画笔颜色:"
          property="lineColor"
          :componentIds="currentComponentIds"
          :option="{ required: true }"
        /> -->
      </div>
      <div class="flex">
        <image-select
          label="背景图片:"
          property="background"
          :componentIds="currentComponentIds"
          :option="{ required: true }"
        />
      </div>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import YaColorPicker from "../EditComponents/ColorPicker/index.vue";
import YaSelect from "../EditComponents/Select/index.vue";
import ImageSelect from "../EditComponents/ImageSelect/index.vue";

// import AudioSelect from "@/components/AudioSelect/index.vue";

@Component({
  components: {
    BaseProperties,
    YaColorPicker,
    ImageSelect,
    YaSelect,
  },
})
export default class GraphicsComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  displayTypeOptions = [
    { label: "上下结构", value: 1 },
    { label: "左右结构", value: 2 },
  ];

  boardTypeOptions = [
    { label: "有边框", value: 1 },
    { label: "无边框", value: 2 },
  ];

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
