<template>
  <el-col :span="formItemConfigs.span" :offset="formItemConfigs.offset" class="common-atom-shell" :class="formItemConfigs.customClassName" :style="formStyle" v-if="isRender">
    <el-form-item :prop="formItemConfigs.key" :class="[`common-atom-shell--label-${formItemConfigs.labelPosition || 'left'}`]">
      <template slot="label">
        {{ formItemConfigs.label }}
        <el-tooltip class="item" effect="light" :content="formItemConfigs.labelTips" placement="top" v-if="formItemConfigs.labelTips" :style="{ left: formItemConfigs.labelTipsLeft || '58px' }">
          <div slot="content" v-html="formItemConfigs.labelTips"></div>
          <i class="el-icon-question button-tips"></i>
        </el-tooltip>
      </template>
      <div class="form-box">
        <div class="form-box-left">
          <component :is="formItemConfigs.formItemType" v-bind="$attrs" :formItemConfigs="formItemConfigs"></component>
        </div>
        <div class="form-box-right" v-if="formItemConfigs.unit">
          <span class="form-box-unit">{{ formItemConfigs.unit }}</span>
        </div>
      </div>
    </el-form-item>
  </el-col>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";

@Component({
  components: {},
})
export default class CommonAtomShell extends Vue {
  @Prop({
    required: true,
  })
  formItemConfigs!: FormItemConfigs;

  // @Prop({
  //   required: true,
  // })
  // currentComponents!: Components;

  get currentComponent() {
    return this.$attrs.currentComponents[0];
  }

  get isRender() {
    let val = true;
    const { renderDependOn } = this.formItemConfigs as any;
    const { properties } = this.$attrs.currentComponents[0] as any;
    // value 或 item 且
    if (renderDependOn) {
      renderDependOn.forEach((item: any) => {
        const keys = item.key.split(".");
        let temp = properties;
        while (keys.length) {
          let key = keys.shift();
          if (key === "subQuestionIndex") key = Number(this.$attrs.parentIndex) !== -1 ? this.$attrs.parentIndex : this.$attrs.formIndex;
          if (temp[key]) {
            temp = temp[key];
          }
        }
        // console.log("isRender...temp", temp);
        const propVal = temp;
        // const propVal = properties[item.key];
        if (!item.value.includes(propVal)) {
          val = false;
        }
      });
    }
    return val;
  }

  getSpecifyTypeCompArr(type: string, tag: string, extra?: any) {
    const componentMap = this.$store.state.componentMap;
    const ids: string[] = [];
    Object.keys(componentMap).forEach((key: string) => {
      const comp = componentMap[key];
      const compExtra = comp.extra;
      let extraEqual = true;
      if (comp.tag == tag) {
        extra &&
          Object.keys(extra).forEach((key: string) => {
            if (extra[key] && extra[key] !== compExtra[key]) {
              extraEqual = false;
            }
          });
      }
      if (comp.type === type && tag && comp.tag == tag && extraEqual) {
        ids.push(key);
      }
    });
    return ids.map((id: string) => componentMap[id]);
  }

  @Watch("isRender")
  isRenderChange(val: boolean) {
    this.$store.commit("updateComponentProperties", {
      id: (this.currentComponent as any).id,
      newProperties: {
        [this.formItemConfigs.key]: val ? this.formItemConfigs.value : undefined,
      },
    });
    // 判断是否需要触发change事件
    const actions = (this.formItemConfigs as any).actions;
    if (!val && actions && actions.delete) {
      // 删除组件...
      const { type, tag, extra } = actions.delete;
      const specifyTypeCompArr = this.getSpecifyTypeCompArr(type, tag, extra);
      const specifyTypeCompIds = specifyTypeCompArr.map((comp: { id: any }) => comp.id);
      if (specifyTypeCompArr.length) {
        this.$store.dispatch("removeComponents", specifyTypeCompIds);
      }
    }
  }

  get formStyle() {
    const { paddingTop, paddingLeft, paddingRight, paddingBottom } = this.formItemConfigs;
    return {
      padding: `${paddingTop || 0}px ${paddingRight || 0}px ${paddingBottom || 0}px ${paddingLeft || 0}px`,
    };
  }
}
</script>

<style lang="less" scoped>
.common-atom-shell {
  text-align: left;
  &.hide {
    display: none;
  }
  /deep/ .common-atom-shell--label-top > .el-form-item__label {
    float: none;
    display: inline-block;
    text-align: left;
  }
  /deep/ .common-atom-shell--label-left > .el-form-item__label {
    float: left;
    display: block;
    text-align: left;
    padding: 0 8px 0 0;
  }
  /deep/ .el-form-item__label {
    position: relative;
    z-index: 1;
  }
}
.form-box {
  display: flex;
}
.form-box-left {
  width: 100%;
  flex: auto;
}
.form-box-right {
  width: auto;
  flex: none;
  padding-left: 10px;
}
</style>
