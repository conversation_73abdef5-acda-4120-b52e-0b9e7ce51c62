export const getMyAudiosData = Promise.resolve({
  data: {
    duration: 4,
    data: {
      total: 1,
      resourceList: [
        {
          id: 190,
          name: "Duang.mp3",
          type: 1,
          urlContent: "./static/audio/duang.mp3",
          status: 0,
          createdBy: "未知",
          createdAt: "2023-01-04T09:07:02.000Z",
          updatedBy: "未知",
          updatedAt: "2023-01-04T09:07:02.000Z",
          deletedBy: null,
          deletedAt: null,
          client: 4,
        },
      ],
    },
    errNo: 0,
    errStr: "success",
  },
});

export const searchSpineData = Promise.resolve({
  data: {
    duration: 20,
    data: {
      list: [
        {
          id: 4,
          createdBy: "suyingtao",
          updatedBy: "gewulong_v",
          createdAt: "2021-01-20T04:01:17.000Z",
          updatedAt: "2023-01-18T10:16:10.000Z",
          deletedAt: null,
          name: "蚂蚁",
          cover: "./static/spine/ants/MAYI.png",
          md5: "fbf40ed6efcddcbc40cdd53e70441111",
          client: 1,
          atlas: "./static/spine/ants/MAYI.atlas",
          skeleton: "./static/spine/ants/MAYI.json",
          images: ["./static/spine/ants/MAYI.png"],
          tags: [
            { id: 4, createdBy: "suyingtao", updatedBy: "suyingtao", createdAt: "2021-01-20T04:01:16.000Z", updatedAt: "2021-01-20T04:01:16.000Z", deletedAt: null, type: 1, name: "mayi", client: 1 },
          ],
        },
      ],
      total: 1,
      time: { beforeGetList: 1674119038585, afterGetList: 1674119038589, afterGetTags: 1674119038605 },
    },
    errNo: 0,
    errStr: "success",
  },
});

export const getTagListData = Promise.resolve({
  data:{"duration":3,"data":[],"errNo":0,"errStr":"success"}
})
