<template>
  <div class="audio-library">
    <el-dialog title="提示" :visible.sync="visible" width="880px" top="0vh" :modal="false"
      :modal-append-to-body="false">
      <el-tabs v-model="activeTabName">
        <el-tab-pane label="图片库" name="my">
          <div class="audio-container">
            <div v-for="(audio) in myAudios" :key="audio.id" class="audio-item" :title="audio.name">
              <div class="audio-item-box" :style="{'background-image': 'url('+audio.url+')'}"
                @click="
                  handleSelectedAudiosChange(
                    mySelectImg === audio
                      ? false
                      : true,
                    audio
                  )
                ">
              </div>
              <div @click.stop>
                <el-checkbox :value="
                    mySelectImg === audio
                      ? true
                      : false
                  " @change="
                    handleSelectedAudiosChange(
                      $event,
                      audio
                    )
                  "></el-checkbox>
              </div>
              <div class="audio-item-name">
                {{ audio.name }}
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <span slot="footer" class="dialog-footer">
        <el-button @click="onClickClose">取 消</el-button>
        <el-button type="primary" @click="onClickConfirm" :disabled="isConfirmBtnDisabled">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component
export default class MockImgageLibrary extends Vue {
  visible = false;
  // 当前选中tab
  activeTabName = "my";

  myAudios: Array<AudioItem> = [{ url: "./static/img/desert.jpg", id: 1, name: "沙漠" }];

  mySelectImg: AudioItem | null = null;

  resolve: any = null;

  init() {
    this.visible = true;
    return new Promise(resolve => {
      this.resolve = resolve;
    });
  }

  get isConfirmBtnDisabled() {
    return !this.mySelectImg;
  }

  onClickConfirm() {
    this.visible = false;
    this.resolve(this.mySelectImg);
  }

  onClickClose() {
    this.mySelectImg = null;
    this.onClickConfirm();
  }

  onConfirm(audios: Array<AudioItem>) {
    console.log(audios);
  }

  /**
   * @description 选中图片逻辑
   */
  handleSelectedAudiosChange(e: boolean, audio: AudioItem) {
    if (e) {
      this.mySelectImg = audio;
    } else {
      this.mySelectImg = null;
    }
  }
}
</script>

<style lang="less" scoped>
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.audio-library {
  /deep/ .el-dialog__header {
    height: 0;
    padding: 0;
    display: none;
  }

  /deep/ .el-dialog__body {
    padding: 20px 15px;

    .audio-container {
      display: flex;
      flex-wrap: wrap;

      .audio-item {
        position: relative;
        width: 100px;
        height: 100px;
        margin: 10px 10px;
        cursor: pointer;

        &-box {
          width: 100px;
          height: 80px;
          background-size: 100%;
        }

        &-play {
          animation: rotate 2s linear infinite;
        }

        &-name {
          text-align: center;
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .circle {
          position: absolute;
          top: 50%;
          left: 50%;
          width: 40px;
          height: 40px;
          border-radius: 40px;
          transform: translate(-50%, -50%);
        }

        .el-checkbox {
          position: absolute;
          top: 10px;
          right: 10px;
        }

        .audio-uploader {
          .el-upload {
            border: 1px dashed #d9d9d9;
            border-radius: 6px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
          }

          .el-upload:hover {
            border-color: #409eff;
          }

          .audio-uploader-icon {
            font-size: 28px;
            color: #8c939d;
            width: 98px;
            height: 98px;
            line-height: 98px;
            text-align: center;
          }
        }

        img {
          border-radius: 6px;
          max-width: 100px;
          height: 100px;
        }
      }
    }

    .el-pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 16px;
    }
  }
}
</style>
