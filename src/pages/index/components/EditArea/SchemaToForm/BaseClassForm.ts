import { Vue, Prop, Watch, Component } from "vue-property-decorator";
import { updateFormList, getCompDefaultConfig, UPDATE_DATA_OBJ_DEFAULT, translateChangeProps } from "@/pages/index/common/utils/tools";
import bus from "@/pages/index/common/utils/bus";
import { merge } from "lodash-es";
import { toHexString } from "@/components/zyb-color-picker/colorPipette/utils";

const EXTRA_PROPS = [
  "formItemType",
  "key",
  "value",
  "label",
  "labelTips",
  "labelPosition",
  "paddingTop",
  "paddingLeft",
  "paddingRight",
  "paddingBottom",
  "span",
  "offset",
  "rule",
  "validation",
  "unit",
  "customClassName"
];

@Component({})
export default class BaseClassForm<T> extends Vue {
  @Prop({
    required: true,
  })
  formItemConfigs!: FormItemConfigs;

  @Prop({
    required: true,
  })
  currentComponents!: Components;

  // 动态列表子原子组件时,传入的数据
  @Prop({
    required: false,
    default: UPDATE_DATA_OBJ_DEFAULT,
  })
  updateDataObj!: any;

  // 动态列表组件子原子组件更新数据时触发方法
  @Prop({
    required: false,
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    default: () => () => { },
  })
  baseUpdateStoreData!: Function;

  // 动态列表组件子原子组件组下标
  @Prop({
    required: false,
    default: -1,
  })
  formIndex!: number;

  isJustChangeComponent = false;

  // 当前表单对应key
  get propertiesKey() {
    return this.formItemConfigs.key;
  }

  get currentComponent() {
    return this.currentComponents[0];
  }

  get val() {
    return this.getVal();
  }
  set val(val) {
    this.setVal(val);
  }

  getVal() {
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      return this.currentComponent?.properties?.[this.propertiesKey];
    } else {
      return this.updateDataObj[this.propertiesKey];
    }
  }

  setVal(val: any) {
    // 增加拦截数据的逻辑 跟category和propertiesKey挂钩
    // 原则： 只影响val 不影响其他的属性值 beforeSetVal afterSetVal
    // 需要测试 是否所有的setVal逻辑都会由此分发
    // console.log('setVal...', val, this.propertiesKey);
    // 如果有关联表单，先清空关联表单的值 必须先清空，否则渲染出来的表单还会有原来的值（动态表单的实现产生的bug）
    // 判断是不是颜色更新
    if (['BaseColor', 'base-color'].includes(this.formItemConfigs.formItemType)) {
      val = toHexString(val);
    }
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      if (!this.currentComponent) return;
      this.$store.commit("updateComponentProperties", {
        id: this.currentComponent?.id,
        newProperties: {
          [this.propertiesKey]: val,
        },
      });
    } else {
      this.baseUpdateStoreData({ index: this.formIndex, key: this.propertiesKey, value: val });
    }
    this.$nextTick(() => {
      this.setAssociatedForm(val);
    })
  }

  getValByKey(key: string) {
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      return this.currentComponent?.properties?.[key];
    } else {
      return this.updateDataObj[key];
    }
  }

  setValByKey(val: any, key: string) {
    // 如果有关联表单，先清空关联表单的值 必须先清空，否则渲染出来的表单还会有原来的值（动态表单的实现产生的bug）
    // 判断是不是颜色更新
    if (['BaseColor', 'base-color'].includes(this.formItemConfigs.formItemType)) {
      val = toHexString(val);
    }
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      if (!this.currentComponent) return;
      this.$store.commit("updateComponentProperties", {
        id: this.currentComponent?.id,
        newProperties: {
          [key]: val,
        },
      });
    } else {
      this.baseUpdateStoreData({ index: this.formIndex, key: key, value: val });
    }
    this.$nextTick(() => {
      this.setAssociatedForm(val);
    })
  }

  setAssociatedForm(val: any) {
    const options = this.formItemConfigs.options;
    options?.forEach(item => {
      if (!item.associatedForm?.length) return;
      if (item.value === val) {
        item.associatedForm?.forEach((item) => {
          if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
            if (this.currentComponent.properties[item.key] === item.value) return;
            this.$store.commit("updateComponentProperties", {
              id: this.currentComponent?.id,
              newProperties: {
                [item.key]: item.value,
              },
            });
          } else {
            if (this.updateDataObj[item.key] === item.value) return;
            this.baseUpdateStoreData({ index: this.formIndex, key: item.key, value: item.value });
          }
        });
      } else {
        item.associatedForm?.forEach((item) => {
          if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
            if (this.currentComponent.properties[item.key] === item.value) return;
            this.$store.commit("updateComponentProperties", {
              id: this.currentComponent?.id,
              newProperties: {
                [item.key]: item.value,
              },
            });
          } else {
            if (this.updateDataObj[item.key] === item.value) return;
            this.baseUpdateStoreData({ index: this.formIndex, key: item.key, value: item.value });
          }
        });
      }
    });
  }

  // 表单的配置,除开了AbstractSchema 和 BaseRuleSchema
  get formConfig() {
    const formConfig = { ...this.formItemConfigs };
    EXTRA_PROPS.forEach(key => delete formConfig[key]);
    return (formConfig as unknown) as T;
  }
  @Watch("currentComponent.id", { immediate: true })
  watchCurrentComponentId() {
    this.isJustChangeComponent = true;
    this.$nextTick(() => {
      this.isJustChangeComponent = false;
    })
  }

  @Watch("val", { immediate: true })
  async watchVal(val: any, oldVal: any) {
    // console.log('isJustChangeComponent.la', this.formItemConfigs.key, this.isJustChangeComponent, val, oldVal);
    if (val == oldVal) return;
    // if (this.isJustChangeComponent) return;
    if (!this.isJustChangeComponent) {
      this.externalAction(val, oldVal);
    };
    // 处理changeProps联动
    if (this.formItemConfigs.changeProps?.length) {
      const tempChangeProps = translateChangeProps(this.formItemConfigs.changeProps, val);
      console.log('tempChangeProps', tempChangeProps);
      this.updateOtherProps(tempChangeProps, !this.isJustChangeComponent);
    };
    // 存在联动,且非动态列表
    if (!this.formItemConfigs.options?.length) return;
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      let option: FormOption = { label: "", value: "" };
      const cleanFormKey: string[] = [];

      this.formItemConfigs.options?.forEach(item => {
        if (item.value === this.val) {
          option = item;
        }
        // 收集其他选项表单key,用于隐藏对应表单
        item.associatedForm?.forEach(formItem => {
          cleanFormKey.push(formItem.key);
        });
      });
      // 存在类tabs操作其他表单
      if (cleanFormKey.length || option?.associatedForm?.length) {
        const formConfig = this.$store.state.extData?.formConfig?.[this.currentComponent?.subType];
        this.tabSwitchOtherForm(formConfig, cleanFormKey, option);
      }
      // 存在修改其他组件属性
      if (option?.changeProps?.length) {
        this.updateOtherProps(option.changeProps, !this.isJustChangeComponent);
      }
    } else {
      this.baseUpdateStoreData({ index: this.formIndex, key: this.propertiesKey, value: this.val });
    }
  }
  // tab切换联动表单逻辑处理
  tabSwitchOtherForm(formConfig: FormSchema, cleanFormKey: string[], option: FormOption) {
    const newFormConfig = updateFormList(formConfig, cleanFormKey, [option], [this.propertiesKey]);
    this.$store.commit("updateExtFormSchema", {
      formSchema: newFormConfig,
      subType: this.currentComponent?.subType,
    });
  }


  /**
   * @description 联动更新其他组件属性
   * @param changeProps 
   * @param changeValue 需要判断是否更新value(组件刚切换的是时候不更新)
   */
  updateOtherProps(changeProps: ChangeProps, changeValue: boolean) {
    changeProps.forEach(item => {
      // 浅克隆,避免value删除对原数据造成影响
      const props = { ...item.props };
      // 是否存在value属性
      if ("value" in props && changeValue) {
        this.$store.commit("updateComponentProperties", {
          id: this.currentComponent?.id,
          newProperties: {
            [item.targetKey]: props.value,
          },
        });
        delete props.value;
      }
      this.$store.commit("updateFormItemProps", {
        props: props,
        key: item.targetKey,
        subType: this.currentComponent?.subType,
      });
    });
  }
  // 获取画布指定类型组件集合
  getSpecifyTypeCompArr(type: string, tag: string, extra?: any) {
    const componentMap = this.$store.state.componentMap;
    const ids: string[] = [];
    Object.keys(componentMap).forEach((key: string) => {
      const comp = componentMap[key];
      const compExtra = comp.extra;
      let extraEqual = true;
      if (comp.tag == tag) {
        extra && Object.keys(extra).forEach((key: string) => {
          if (extra[key] && extra[key] !== compExtra[key]) {
            extraEqual = false;
          }
        })
      }
      if (comp.type === type && (tag && comp.tag == tag) && extraEqual) {
        ids.push(key);
      }
    })
    return ids.map((id: string) => componentMap[id]);
  }

  // 判断两个字段是否相等
  isEqual(a: any, b: any): boolean {
    if (a === b) return true;
    if (a instanceof Date && b instanceof Date) return a.getTime() === b.getTime();
    if (!a || !b || (typeof a !== "object" && typeof b !== "object")) return a === b;
    if (a.prototype !== b.prototype) return false;
    const keys = Object.keys(a);
    if (keys.length !== Object.keys(b).length) return false;
    return keys.every(k => this.isEqual(a[k], b[k]));
  };
  // 触发外部联动
  externalAction(val: any, oldVal: any) {
    const actions = this.formItemConfigs.actions;
    if (!actions) return;
    if (val < oldVal && actions.delete) {
      // 删除组件...
      const { type, tag, extra } = actions.delete;
      const specifyTypeCompArr = this.getSpecifyTypeCompArr(type, tag, extra);
      // console.log('del', val, oldVal, type, tag, specifyTypeCompArr);
      const specifyTypeCompIds = specifyTypeCompArr.map((comp: { id: any; }) => comp.id)
      if (specifyTypeCompArr.length && val < specifyTypeCompArr.length) {
        this.$store.dispatch("removeComponents", specifyTypeCompIds.slice(val - specifyTypeCompArr.length));
      }
    }
    if (oldVal === undefined) oldVal = 0;
    if (val > oldVal && actions.add) {
      const { type, tag, extra } = actions.add;
      const specifyTypeCompArr = this.getSpecifyTypeCompArr(type, tag, extra);
      console.log('specifyTypeCompArr', specifyTypeCompArr.length, extra);
      if (specifyTypeCompArr.length >= val) return;
      const delta = val - specifyTypeCompArr.length;
      for (let index = 0; index < delta; index++) {
        const defaultConfig = getCompDefaultConfig(actions.add.type);
        // tag添加tagIndex 用于区分同类型组件
        // extra.tagIndex = specifyTypeCompArr.length + index;
        const compConfig = merge(defaultConfig, actions.add);
        // 添加逻辑分段 位置不重复 最多支持15*8=120个
        const perMax = 15;
        const block = Math.floor((Number(specifyTypeCompArr.length) + index) / perMax);
        const remainder = Math.floor((Number(specifyTypeCompArr.length) + index) % perMax)
        const step = 30;
        const blockMap = new Map([
          [0, [1, -1]],
          [1, [0, -1]],
          [2, [-1, -1]],
          [3, [-1, 0]],
          [4, [-1, 1]],
          [5, [0, 1]],
          [6, [1, 1]],
          [7, [1, 0]],
        ])
        Object.assign(compConfig.properties, {
          x: (blockMap.get(block) || [1, -1])[0] * remainder * step + step,
          y: (blockMap.get(block) || [1, -1])[1] * remainder * step + step,
        });

        // x y 位置偏移 
        if (compConfig.extra) {
          Object.assign(compConfig.extra, {
            tagIndex: specifyTypeCompArr.length + index + 1
          });
        }
        this.$store.dispatch("addComponentNoFocus", merge(defaultConfig, actions.add));
      }
    }
    if (actions.change && this.currentComponent && !this.isEqual(val, oldVal)) {
      bus.$emit("schema-form-change", {
        componentId: this.currentComponent.id,
        propertiesKey: this.propertiesKey,
        subIndex: this.formIndex, // 动态表单的索引
        val,
        oldVal,
        changeConfig: actions.change === true ? undefined : actions.change
      });
    }
  }

  handleChangeByKey(val: any, oldVal: any, key: string) {
    if (this.currentComponent && !this.isEqual(val, oldVal)) {
      bus.$emit("schema-form-change", {
        componentId: this.currentComponent.id,
        propertiesKey: key,
        subIndex: this.formIndex, // 动态表单的索引
        val,
        oldVal
      });
    }
  }
}
