<template>
  <div class="special-keep-look-table">
    <div class="table-row" :class="{ 'first': index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :class="{ 'subfirst': sIndex === 0 }" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="cell-div">
          <el-select  :value="val[index][sIndex]" v-bind="formItemConfigs.optionsConfig.config" :multiple="false" @change="(val) => {handleChange(val, index, sIndex)}" clearable>
            <el-option v-show="!!item.label" v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import cloneDeep from "lodash-es/cloneDeep";
import isEqual from "lodash-es/isEqual";

@OverviewDataCollection({
  label: "连连看组件",
  "labelPosition":"top",
  value: [
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined]
  ],
  "optionsConfig": {
      "relativePropertiesKey": "StuImageList",
      "labelPrefix": "图案",
      "valueKey": "index",
      "config": {
        "placeholder": ""
      }
  },
  col: 5,
  row: 5,
  rowKey: 'stuRow',
  colKey: 'stuCol',
  min: 1,
  max: 5,
})

@Component
export default class SpecialKeepLookTable extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialKeepLookTable";
  init = false;
  public handleClick() {
    console.log("click");
    const timeStamp = new Date().getTime();
    this.setVal(timeStamp);
  }
  get row() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.rowKey] || this.formItemConfigs.row
  }
  
  get col() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.colKey] || this.formItemConfigs.col
  }

  get size() {
    return {
      row: this.row,
      col: this.col
    }
  }

  @Watch("size", { deep: true })
  onSizeChange(newVal: any, oldVal: any) {
    if(isEqual(newVal, oldVal)) return;
    // row col 变更时 重置数据
    const val: any[][] = [];
    for(let i = 0; i < this.row; i++) {
      const temp: any[] = [];
      for(let j = 0; j < this.col; j++) {
        temp.push(null);
      }
      val.push(temp);
    }
    this.setVal(val);
  }

  @Watch("val", { deep: true })
  onValChange() {
    if(this.init) return;
    // 题板规格变更时 不更新数据
    // 判断二维数组为空
    let isRefreshBlankData = true;
    [...this.val].forEach((item) => {
      item.forEach((subItem: any) => {
        if(subItem) isRefreshBlankData = false;
      })
    })
    if(!isRefreshBlankData) return;
    this.setVal(this.val);
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  created() {
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  get options() {
    if(this.formItemConfigs.optionsConfig) {
      const { optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, valueKey } } = this.formItemConfigs;
      const keys = relativePropertiesKey.split(".");
      
      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while(keys.length) {
        let key = keys.shift();
        if(key === 'subQuestionIndex') key = this.$attrs.parentIndex;
        if(temp[key]) {
          temp = temp[key];
        } else {
          temp = [];
        }
        if(!keys.length) {
          options = temp.map((option: any, index: number) => {
            return {
              ...option,
              label: labelKey ? option[labelKey] : `${labelPrefix}${index+1}`,
              value: valueKey === 'index' ? index : option[valueKey]
            }
          })
        }
      }
      return options;
    }
    return this.formItemConfigs.options;
  }

  @Watch("options")
  onOptionsChange(val: any[], oldVal: any[]) {
    this.$nextTick(() => {
      // 只处理删除的情况
      if(val.length >= oldVal.length) return;
      // 找出选项内容发生变化的option
      // const changedOptions = this.options.filter((option: any, index: number) => {
      //   const oldOption = oldVal[index];
      //   if(!oldOption) return true;
      //   return !isEqual(option, oldOption);
      // }).map((item: { value: any; }) => item.value);
      const optionsValue = this.options.map((option: any) => {

        return option.value;
      });
      // 判断二维数组是否都为空
      let isAllBlankData = true;
      let needChange = false;
      const temp = cloneDeep(this.val);
      [...this.val].forEach((item, index: number) => {
        item.forEach((subItem: any, subIndex: number) => {
          if(subItem || subItem === 0) {
            isAllBlankData = false;
            if(!optionsValue.includes(subItem)) {
              temp[index][subIndex] = "";
              needChange = true;
            }
          }
        })
      })
      if(isAllBlankData) return;
      if(needChange) {
        this.setVal(temp);
      }
    });
  }

  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex] = params;
    const valClone = cloneDeep(this.val);
    valClone[index][subIndex] = value;
    this.setVal(valClone);
  }
}
</script>

<style lang="less" scoped>
.special-keep-look-table {
  // border-left: 1px solid #e1e2e6;
  // border-top: 1px solid #e1e2e6;
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
  }
  .table-col {
    width: 50px;
    border-bottom: 1px solid #e1e2e6;
    border-right: 1px solid #e1e2e6;
    box-sizing: border-box;
  }
  .first .inner-item {
    border-top: 1px solid #e1e2e6;
  }
  // 每一行的第一个单元格
  .inner-item:first-child {
    border-left: 1px solid #e1e2e6;
  }
  .subfirst {
    border-left: 1px solid #e1e2e6;
  }
  
  /deep/ .el-input-number--mini {
    width: 100%;
    display: block;
    height: 100%;
    line-height: 30px;
  }
  /deep/ .el-input--mini {
    height: 100%;
  }
  /deep/ .el-input input {
    padding: 0;
    border: 0px;
    // height: 100%;
  }
}
</style>
