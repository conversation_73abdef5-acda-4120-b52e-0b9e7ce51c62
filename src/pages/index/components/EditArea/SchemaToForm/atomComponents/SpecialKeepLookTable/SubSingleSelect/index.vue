<template>
  <el-select  v-model="formItemValue[formItemConfigs.key]" v-bind="formItemConfigs" :multiple="false">
    <el-option v-show="!!item.label" v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
  </el-select>
</template>

<script lang="ts">
import { Component,Prop,Vue, Watch, } from "vue-property-decorator";
@Component({})
export default class SubSingleSelect extends Vue {
  @Prop({
    required: true,
  })
  formItemConfigs!: any;

  @Prop({
    required: true,
  })
  formItemValue!: any;
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get currentComponent() {
    return this.currentComponents[0];
  }

  get options() {
    if(this.formItemConfigs.optionsConfig) {
      const { optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, valueKey } } = this.formItemConfigs;
      const keys = relativePropertiesKey.split(".");
      
      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while(keys.length) {
        let key = keys.shift();
        if(key === 'subQuestionIndex') key = this.$attrs.parentIndex;
        if(temp[key]) {
          temp = temp[key];
        } else {
          temp = [];
        }
        if(!keys.length) {
          options = temp.map((option: any, index: number) => {
            return {
              label: labelKey ? option[labelKey] : `${labelPrefix}${index+1}`,
              value: valueKey === 'index' ? index : option[valueKey]
            }
          })
        }
      }
      return options;
    }
    return this.formItemConfigs.options;
  }
  @Watch("options.length")
  onOptionsChange(val: number, oldVal: number) {
    if(val > oldVal) return;
    const value = this.formItemValue[this.formItemConfigs.key];
    if(value === "") return;
    if(typeof this.formItemValue[this.formItemConfigs.key] === 'undefined') return;
    const optionsValue = this.options.map((option: any) => option.value);
    if(!optionsValue.includes(value) && typeof value !== 'undefined') {
      this.formItemValue[this.formItemConfigs.key] = "";
    }
  }
}
</script>