<template>
  
  <div class="base-tag-table">
    <div class="table-row inner-row" :class="{ 'first': index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :style="cellStyle" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="base-tag cell-div">
          <el-tag :color="val[index][sIndex] === 1 ? formItemConfigs['active'].color : formItemConfigs.color" v-bind="formItemConfigs" @click="handleClick(index, sIndex)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep } from "lodash-es";

@OverviewDataCollection({
  label: "标签列表",
  labelPosition: "top",
  color: '#909399',
  active: {
    color: '#ffffff'
  },
  col: 3,
  row: 3,
  span: 8,
  width: '60px',
  height: '60px',
  value: [
    [0,0,0,0],
    [0,0,0,0],
    [0,0,0,0]
  ] //  0-color  1-active.color
})

@Component({})
export default class BaseTagList extends BaseClassForm<BaseTagListType> {
  static componentName = "BaseTagList";
  handleClick(index: number, subIndex: number) {
    const valClone = cloneDeep(this.val);
    valClone[index][subIndex] = valClone[index][subIndex] === 1 ? 0 : 1;
    this.setVal(valClone);
  }
  // 1 active 0 normal
  // color(index: string|number, subIndex: string|number) {
  //   return this.val[index][subIndex] === 1 ? this.formItemConfigs['active'].color : this.formItemConfigs.color
  // }

  get row() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.rowKey] || this.formItemConfigs.row
  }
  
  get col() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.colKey] || this.formItemConfigs.col
  }

  get cellStyle() {
    return {
      maxWidth: this.formItemConfigs.width,
      height: this.formItemConfigs.height
    }
  }
}
</script>



<style lang="less" scoped>
.base-tag-table {
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
    overflow: hidden;
    width: 100%;
    /deep/ .el-tag {
      width: 100%;
      height: 100%;
      border-color: #e9e9eb;
    }
  }
  .table-col {
    box-sizing: border-box;
    position: relative;
    flex: 1;
  }
}
</style>
