<template>
  <div class="special-option-config">
    <el-form size="small" label-position="left" ref="ruleForm" :model="ruleForm" :rules="rules">
      <el-form-item v-if="isShowType" label="选项类型">
        <el-radio-group v-model="optionType">
          <el-radio label="text">文本</el-radio>
          <el-radio label="img">图片</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="option-list-title">{{ listConfig.label }}
        <el-tooltip :content="listConfig.labelTips" placement="top" effect="light" v-if="listConfig.labelTips">
          <i class="el-icon-question"></i>
        </el-tooltip></div>
      <div v-if="options.length" class="option-list-box">
        <el-form-item v-for="(item, index) in options" :label="getOrder(orderConfig, index)"
          :prop="'input' + index" :key="index">
          <div class="option-box">
            <div class="option-box-input" v-if="optionType === 'text'">
              <el-input v-for="(option, i) in item" :key="i" placeholder="请输入内容"
                :maxlength="textMaxLength" v-model="option.text">
              </el-input>
            </div>
            <div class="option-box-input" v-if="optionType === 'img'">
              <ImageSelect v-for="(option, i) in item" :key="i" :src.sync="option.imgUrl"
                :is-show-delete-btn="false" :max-width="stageSafeWidth"
                :max-height="stageSafeHeight" class="custom-option-image"/>
            </div>
            <div class="option-box-operations">
              <i v-if="options.length < max" class="el-icon-circle-plus-outline"
                @click="add(index)"></i>
              <i v-if="options.length > min" class="el-icon-remove-outline" @click="del(index)"></i>
            </div>
          </div>
        </el-form-item>
      </div>
      <i v-else class="el-icon-circle-plus-outline" @click="add(0)"></i>
    </el-form>
  </div>
</template>
  
<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep, debounce, isEqual } from "lodash-es";
import store from "@/pages/index/store";
import { DEBOUNCE_TIME } from "@/pages/index/common/utils/tools";

interface CustomOptionListAbstract extends SpecialOptionConfigAbstract {
  optionLength: number;
  listConfig?: {
    label: string;
    labelTips: string;
  };
}

type OptionsItem = Array<{ text: string; imgUrl: string }>;

const ORDER_SEAT = "{{$}}";

@OverviewDataCollection({
  label: "选项列表",
  labelTips: "一个选项多个输入框",
  value: {
    type: "text", // text:文本, img:图片
    options: [], // 选项列表
  },
})
@Component({ components: { ImageSelect } })
export default class CustomOptionList extends BaseClassForm<Record<string, any>> {
  static componentName = "CustomOptionList";
  created() {
    if (this.val?.options?.length) {
      this.options = cloneDeep(this.val.options);
    }
    if (this.customOptionConfig.optionLength) {
      this.optionsItem = new Array(this.customOptionConfig.optionLength).fill(null).map(() => ({ text: "", imgUrl: "" }));
    }
  }
  get stageSafeWidth() {
    return store.state.stageData.safeWidth;
  }
  get stageSafeHeight() {
    return store.state.stageData.safeHeight;
  }
  get textMaxLength() {
    return this.customOptionConfig?.textConfig?.maxLength || Infinity;
  }

  get ruleForm() {
    if (!this.customOptionConfig?.textConfig?.validation) return null;
    return this.options.reduce((obj: any, item: any, index: number) => {
      obj[`input${index}`] = item;
      return obj;
    }, {});
  }

   get listConfig() {
    return (
      this.customOptionConfig.listConfig || {
        label: "选项列表",
        labelTips: "",
      }
    );
  }

  get orderConfig() {
    return (
      this.customOptionConfig.orderConfig || {
        show: true,
        type: "letter",
        decorate: "选项{{$}}",
      }
    );
  }

  get decorateArrays() {
    const { orderConfig } = this;
    if (orderConfig && orderConfig?.type === "function") {
      console.log("orderConfig", [...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || ""]);
      const decorateFunc = new Function(...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || "");
      // eslint-disable-next-line prefer-spread
      return decorateFunc.apply(null, Object.values(orderConfig?.args || {}));
    }
    return [];
  }

  getOrder(orderConfig: OrderConfig | undefined, index: number) {
    if (!orderConfig) return index + 1;
    if (orderConfig?.type === "letter") {
      const letter = String.fromCharCode(65 + index);
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, letter) : letter;
    } else if (orderConfig?.type === "function") {
      return this.decorateArrays[index];
    } else {
      const i = (index + 1).toString();
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, i) : i;
    }
  }

  get rules() {
    const validation = this.customOptionConfig?.textConfig?.validation;
    if (!validation) return null;
    
    return this.options.reduce((obj: any, item: any, index: number) => {
      obj[`input${index}`] = [
        { required: true, type: 'array', message: "请输入内容", trigger: ["blur", "change"] },
        {
          trigger: ["blur", "change"],
          validator: (rule: { message: string }, value: any, callback: any) => {
            try {
              const reg = new RegExp(validation as string);
              let valid = true;
              if(this.optionType === 'text') {
                value.forEach((item: any) => {
                  if(!reg.test(item.text)) valid = false;
                })
              }
              if (!valid) {
                callback(new Error(this.customOptionConfig?.textConfig?.validationMessage || "输入内容校验不通过"));
              } else {
                callback();
              }
            } catch (error) {
              console.error(error);
              callback();
            }
          },
        },
      ];
      return obj;
    }, {});
  }
  get isShowType() {
    const typeConfig = this.customOptionConfig.typeConfig;
    if (!typeConfig) return true;
    if (!typeConfig.show) {
      // 不显示类型配置表单时,需保证当前value的类型与配置一致
      const typeVal = typeConfig.type || "text";
      if (typeVal !== this.val?.type) {
        const cloneVal = cloneDeep(this.val) || {};
        this.setVal({
          options: [], // 选项列表
          ...cloneVal,
          type: typeVal,
        });
      }
    }
    return typeConfig.show;
  }

  get customOptionConfig() {
    return this.formItemConfigs as unknown as CustomOptionListAbstract;
  }
  get optionType() {
    return this.val?.type;
  }
  set optionType(type) {
    if (type === this.val?.type) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.type = type;
    this.options = cloneVal.options?.map(() => cloneDeep(this.optionsItem)) || [];
    cloneVal.options = cloneDeep(this.options);
    this.setVal(cloneVal);
    this.$nextTick(() => {
      (this.$refs.ruleForm as any)?.resetFields();
    });
  }
  optionsItem: OptionsItem = [{ text: "", imgUrl: "" }];
  options: Array<OptionsItem> = [];
  @Watch("options", { deep: true })
  watchOptions() {
    this.debounceFun(this.val, this.options);
  }

  @Watch("val.options.length")
  watchOptionsLength(val: number) {
    if(val !== this.options.length) {
      // console.log('外部原因触发的更改');
      this.options = cloneDeep(this.val.options);
    }
  }
  // 防抖函数
  debounceFun = debounce((val, options) => {
    let cloneVal = cloneDeep(val);
    if (isEqual(cloneVal?.options, options)) return;
    if (cloneVal) {
      cloneVal.options = cloneDeep(options);
    } else {
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: [], // 选项列表
      };
    }
    this.setVal(cloneVal);
  }, DEBOUNCE_TIME);
  get min() {
    return this.customOptionConfig.min || 0;
  }
  get max() {
    return this.customOptionConfig.max || Infinity;
  }
  add(index: number) {
    let cloneVal = cloneDeep(this.val);
    if (cloneVal) {
      this.options.splice(index + 1, 0, cloneDeep(this.optionsItem));
      cloneVal.options = cloneDeep(this.options);
    } else {
      this.options = [cloneDeep(this.optionsItem)];
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: cloneDeep(this.options), // 选项列表
      };
    }
    this.setVal(cloneVal);
  }
  // 删除子项
  del(index: number) {
    const cloneVal = cloneDeep(this.val);
    this.options.splice(index, 1);
    cloneVal.options = cloneDeep(this.options);
    this.setVal(cloneVal);
  }
}
</script>
  
<style lang="less" scoped>
.option-list-title {
  line-height: 36px;
  font-size: 12px;
  color: #777;
}
.option-list-box {
  padding-left: 5px;
}
.el-icon-circle-plus-outline,
.el-icon-remove-outline {
  cursor: pointer;
}
.option-box {
  display: flex;
  .option-box-input {
    width: 100%;
    flex: auto;
    .image-select,
    .el-input {
      margin: 5px 0;
    }
  }
  .option-box-operations {
    text-align: right;
    padding-left: 5px;
    width: 35px;
    flex: none;
  }
}
</style>

<style lang="less">
@CSS_PREFIX: custom-option-image;
@WIDTH: 120px;
.@{CSS_PREFIX} {
  .image-select {
    width: @WIDTH;
    min-height: 32px;
    max-height: 200px;
  }
  .edit-btn-container {
    width: @WIDTH !important;
  }
  .image-container {
    width: @WIDTH !important;
  }
}
</style>
  