<template>
  <div class="special-sudo-config">
    <el-form size="small" label-position="left">
      <el-form-item :label="sudoConfigs.type.label" :required="true">
        <el-radio-group v-model="sudoType" style="width: 98%;">
          <el-row class="base-radio-row">
            <el-col class="base-radio-col" v-for="item in sudoConfigs.type.options" :key="item.value" :span="8" style="margin-bottom: 6px;">
              <el-radio :label="item.value">{{ item.label }}</el-radio>
            </el-col>
          </el-row>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <el-form size="small" label-position="left">
      <el-form-item :label="sudoConfigs.grid.label" label-position="left" :required="true">
        <el-radio-group v-model="sudoGrid">
          <el-radio v-for="item in sudoConfigs.grid.options" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item  v-if="['0', '4'].includes(sudoType)">
        <template slot="label">
          {{sudoConfigs.mark.label}}
          <el-tooltip class="item" effect="light" content="标准、杀手、不等号题板下，5*5为区域配置" placement="top" :style="{ left: '58px' }">
            <i class="el-icon-question button-tips"></i>
          </el-tooltip>
        </template>
        <el-radio-group v-model="isMark" v-if="['0', '4'].includes(sudoType)">
          <el-radio v-for="item in sudoConfigs.mark.options" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
        </el-radio-group>
        <TableConfig :col="size.col" :val="val" :row="size.row" type="color" @change="handleChange" v-if="isMark === '1'" />
      </el-form-item>
      <el-form-item label="" v-if="sudoType === '2'">
        <el-radio-group v-model="isMark">
          <el-radio label="1">标记宫<el-tooltip class="item" effect="light" content="标准、杀手、不等号题板下，5*5为区域配置" placement="top" :style="{ left: '58px',color: '#777', marginLeft: '5px'}">
            <i class="el-icon-question button-tips"></i>
          </el-tooltip></el-radio>
          <el-radio label="0">标记区域</el-radio>
        </el-radio-group>
        <TableConfig :col="size.col" :val="val" :row="size.row" type="color" @change="handleChange" @setGroup="handleSetGroup" />
      </el-form-item>
      <el-form-item label="对角线" v-if="sudoType === '1'">
        <el-switch v-model="hasDiagonal" active-value="1" inactive-value="0">>标记宫</el-switch>
      </el-form-item>
    </el-form>
    <el-form size="small" label-position="top" hide-required-asterisk>
      <el-form-item v-if="['1', '3'].includes(sudoType)">
        <template slot="label">
          区域配置
          <el-tooltip class="item" effect="light" content="相同颜色的格子自定义为一个区域" placement="top" :style="{ left: '58px' }">
            <i class="el-icon-question button-tips"></i>
          </el-tooltip>
        </template>
        <TableConfig :col="size.col" :val="val" :row="size.row" type="color" @change="handleChange" @setGroup="handleSetGroup" />
      </el-form-item>
      <el-form-item v-if="['0', '1', '4'].includes(sudoType)">
        <template slot="label">
          题板内容配置
          <el-tooltip class="item" effect="light" placement="top" :style="{ left: '58px' }">
            <div slot="content" v-if="sudoType !== '4'" v-html="'点击格子即可输入数字'"></div>
            <div style="width: 310px;" slot="content" v-else v-html="'1、点击格子输入数字。</br>2、鼠标hover格子之间的线条并点击线条区域出现符号，再次点击变换符号，第3次点击后清空，可循环操作。'"></div>
            <i class="el-icon-question button-tips"></i>
          </el-tooltip>
        </template>
        <TableConfig :col="size.col" :val="val" :row="size.row" type="number" @change="handleChange" />
      </el-form-item>
    </el-form>
    <el-form size="small" label-position="left" hide-required-asterisk v-if="['2', '3'].includes(sudoType)">
      <el-form-item label="虚线框颜色">
        <ColorPicker :value="val.dashAreaColor" :clearable="true" @input="handleColorChange"></ColorPicker>
      </el-form-item>
    </el-form>
    <el-form size="small" label-position="top" hide-required-asterisk>
      <template v-if="['2', '3'].includes(sudoType)">
        <el-form-item>
          <template slot="label">
            虚线框配置
            <el-tooltip class="item" effect="light" content="" placement="top" :style="{ left: '58px' }">
              <!-- 杀手 宫 -->
              <div slot="content" v-if="sudoType === '2' && isMark === '1'">选择好相邻的格子后，点击“确认”按钮即可将选择的格子自动合成为一个虚线框</div>
              <!-- 杀手 区域 -->
              <div slot="content" v-if="sudoType === '2' && isMark === '0'">此处虚线框为自动生成，上个环节中可删除，此处不可删除</div>
              <!-- 运算 -->
              <div slot="content" v-if="sudoType === '3'">此处虚线框为自动生成，上个环节中可删除，此处不可删除</div>
              <i class="el-icon-question button-tips"></i>
            </el-tooltip>
          </template>
          <TableConfig :col="size.col" :val="val" :row="size.row" type="dashCell" @change="handleChange" @setGroup="handleSetGroup" />
        </el-form-item>
        <el-form-item>
          <template slot="label">
            题板内容配置
            <el-tooltip class="item" effect="light" content="" placement="top" :style="{ left: '58px' }">
              <!-- 杀手 -->
              <div slot="content" v-if="sudoType === '2'">虚线框内选择任意一个格子都可设置结果（左上角数字），不会重复设置</div>
              <!-- 运算 -->
              <div slot="content" v-if="sudoType === '3'">结果&符号为必填，题板内容不必填</div>
              <i class="el-icon-question button-tips"></i>
            </el-tooltip>
          </template>
          <TableConfig :col="size.col" :val="val" :row="size.row" type="dashNumber" @change="handleChange" @setGroup="handleSetGroup" @setGroupNumber="handleSetGroupNumber" />
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep } from "lodash-es";
import store from "@/pages/index/store";
import ColorPicker from "@/components/zyb-color-picker/ColorPicker.vue";
import TableConfig from "./TableConfig.vue";

const logPreFix = "数独组件";

@OverviewDataCollection({
  label: "数独表单组件",
  labelTips: "一个选项一个输入框",
  labelPosition: "top",

  value: {
    dashAreaColor: "#ff0000",
    type: "0",
    grid: "4*4",
    isMark: "1",
    groups: [],
    cells: [],
    col: 4,
    row: 4,
    colors: ["#E5E5E5", "#88DDFF", "#FDBBD8", "#AFF392", "#FFE27E", "#DFDEFF", "#ABECF6", "#FFC896", "#9CF1CA", "#E8D7FF"],
  },
})
@Component({ components: { ColorPicker, TableConfig } })
export default class SpecialSudoConfig extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialSudoConfig";
  log(...params: any[]) {
    console.log(logPreFix, ...params);
  }
  initCells(row: number, col: number) {
    const cells = Array(row)
      .fill(0)
      .map(() => {
        return Array(col)
          .fill(0)
          .map(() => {
            return {
              color: "",
              text: "",
              groupId: "",
            };
          });
      });
    return cells;
  }
  created() {
    this.log("created");
    if (!this.val.cells || !this.val.cells.length) {
      const cells = this.initCells(this.val.row || 4, this.val.col || 4);
      const valClone = cloneDeep(this.val);
      valClone.cells = cells;
      this.setVal(valClone);
    }
  }
  sudoConfigs = {
    type: {
      label: "题板",
      options: [
        { label: "标准数独", value: "0" },
        { label: "异形数独", value: "1" },
        { label: "杀手数独", value: "2" },
        { label: "运算数独", value: "3" },
        { label: "不等号数独", value: "4" },
      ],
    },
    grid: {
      label: "排版类型",
      options: [
        { label: "4*4", value: "4*4", col: 4, row: 4 },
        { label: "5*5", value: "5*5", col: 5, row: 5 },
        { label: "6*6", value: "6*6", col: 6, row: 6 },
      ],
    },
    mark: {
      label: "标记宫",
      options: [
        { label: "是", value: "1" },
        { label: "否", value: "0" },
      ],
    },
  };
  get stageSafeWidth() {
    return store.state.stageData.safeWidth;
  }
  get stageSafeHeight() {
    return store.state.stageData.safeHeight;
  }
  // 题板类型
  get sudoType() {
    return this.val?.type;
  }
  set sudoType(type) {
    if (type === this.val?.type) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.type = type;
    const cells = this.initCells(this.val.row || 4, this.val.col || 4);
    cloneVal.cells = cells;
    const sizeOpt = this.sudoConfigs["grid"].options[0];
    cloneVal.grid = sizeOpt.value;
    cloneVal.col = sizeOpt?.col;
    cloneVal.row = sizeOpt?.row;
    cloneVal.dashAreaColor = "#ff0000";
    cloneVal.isMark = ["0", "2", "4"].includes(type) ? "1" : "0";
    cloneVal.groups = [];
    // 对角线设置为默认值
    cloneVal.hasDiagonal = "0";
    this.setVal(cloneVal);
  }
  get sudoGrid() {
    return this.val?.grid;
  }
  set sudoGrid(val: string) {
    // console.log('sudoGrid', params);
    const sizeOpt = this.sudoConfigs["grid"].options.find(item => item.value === val);
    if (val === this.val?.grid) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.grid = val;
    cloneVal.col = sizeOpt?.col || 4;
    cloneVal.row = sizeOpt?.row || 4;
    const cells = this.initCells(cloneVal.row || 4, cloneVal.col || 4);
    cloneVal.cells = cells;
    cloneVal.groups = [];
    cloneVal.dashAreaColor = "#ff0000";
    this.setVal(cloneVal);
  }
  get isMark() {
    return this.val?.isMark;
  }
  set isMark(val) {
    if (val === this.val?.isMark) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.isMark = val;
    const cells = this.initCells(cloneVal.row || 4, cloneVal.col || 4);
    cloneVal.cells = cells;
    cloneVal.groups = [];
    cloneVal.dashAreaColor = "#ff0000";
    this.setVal(cloneVal);
  }

  get hasDiagonal() {
    return this.val?.hasDiagonal;
  }
  set hasDiagonal(val) {
    if (val === this.val?.hasDiagonal) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.hasDiagonal = val;
    this.setVal(cloneVal);
  }

  get size() {
    const sizeOpt = this.sudoConfigs["grid"].options.find(item => item.value === this.sudoGrid);
    return sizeOpt
      ? {
          row: sizeOpt.row,
          col: sizeOpt.col,
        }
      : {
          row: 4,
          col: 4,
        };
  }
  get isShowMark() {
    return ["0", "2"].includes(this.sudoType);
  }
  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex, key] = params;
    const valClone = cloneDeep(this.val);
    this.log("handleChange", value, index, subIndex, key, valClone);
    const temp = value;
    // const oldVal = valClone["cells"][index][subIndex][key];
    valClone["cells"][index][subIndex][key] = temp;
    // 删除组时 同步将groups中的数据处理掉
    // if(key === 'groupId' && oldVal) {
    //   valClone["groups"].filter((item: { id: number; }) => item.id !== oldVal)
    // }
    this.setVal(valClone);
  }
  // 设置虚线框颜色
  handleColorChange(val: string) {
    const valClone = cloneDeep(this.val);
    valClone.dashAreaColor = val;
    this.setVal(valClone);
  }
  handleSetGroup(pos: number[][], groupId: number) {
    const valClone = cloneDeep(this.val);
    let preGroupId = 0;
    pos.forEach((pos: number[]) => {
      const [posX, posY] = pos;
      preGroupId = valClone["cells"][posX][posY]["groupId"] || 0;
      valClone["cells"][posX][posY]["groupId"] = groupId;
    });
    this.log("handleSetGroup", preGroupId);
    // groups同步更新 如果原有的groupId不存在了 需要清空
    if (!groupId && preGroupId) {
      // 查找是否还有相同groupId的cell
      let needClear = true;
      valClone["cells"].forEach((item: { groupId: any }[]) => {
        item.forEach((cell: { groupId: any }) => {
          if (cell.groupId === preGroupId) {
            return (needClear = false);
          }
        });
      });
      if (needClear) {
        valClone["groups"] = valClone["groups"].filter((item: { id: number }) => item.id !== preGroupId);
      }
    }

    this.setVal(valClone);
  }
  handleSetGroupNumber(groupId: number, params: { text: string; extraText: string; index: number; sIndex: number; isMinCell: boolean }) {
    const valClone = cloneDeep(this.val);
    this.log("handleSetGroup", valClone);
    valClone["cells"][params.index][params.sIndex].text = params.text;
    const findGroup = valClone["groups"].find((item: { id: number }) => item.id === groupId);
    if (findGroup) {
      // findGroup.text = params.text;
      findGroup.extraText = params.extraText;
      if (params.isMinCell) findGroup.text = params.text;
    } else {
      valClone["groups"].push({
        id: groupId,
        extraText: params.extraText,
        text: params.isMinCell ? params.text : "",
        // ...params,
      });
    }
    this.setVal(valClone);
  }
}
</script>

<style lang="less" scoped>
.option-list-title {
  line-height: 36px;
  font-size: 12px;
  color: #777;
}
.option-list-box {
  padding-left: 5px;
}
.el-icon-circle-plus-outline,
.el-icon-remove-outline {
  cursor: pointer;
}
.option-box {
  display: flex;
  .option-box-input {
    width: 100%;
    flex: auto;
  }
  .option-box-operations {
    text-align: right;
    padding-left: 5px;
    width: 35px;
    flex: none;
  }
}
.special-sudo-config {
  .table-wrapper {
    // border-left: 1px solid #e1e2e6;
    // border-top: 1px solid #e1e2e6;
    box-sizing: border-box;
    .table-row {
      display: flex;
    }
    .outside-row {
      display: flex;
    }
    .cell-div {
      width: 100%;
      height: 100%;
    }
    .table-col {
      width: 50px;
      border-bottom: 1px solid #e1e2e6;
      border-right: 1px solid #e1e2e6;
      box-sizing: border-box;
    }
    // 第一个inner-row中的 inner-item
    .first .inner-item {
      border-top: 1px solid #e1e2e6;
    }
    // 第一行的第一个单元格
    // .inner-row:first-child .inner-item {
    //   border-top: 1px solid #e1e2e6;
    // }
    // 每一行的第一个单元格
    .inner-item:first-child {
      border-left: 1px solid #e1e2e6;
    }
    .subfirst {
      border-left: 1px solid #e1e2e6;
    }
    .opacity {
      opacity: 0;
    }
    .out-item {
      border: 0px !important;
      // padding: 6px;
      .cell-div {
        border: 1px solid #e1e2e6;
      }
    }
    .first .out-item-left,
    .first .out-item-right {
      margin-top: 0px;
    }
    .first .out-item-top,
    .first .out-item-bottom {
      margin-left: 0px;
    }
    .out-item-left {
      padding-right: 6px;
      padding-bottom: 2px;
      padding-top: 0;
      padding-left: 0;
      margin-top: -1px;
    }
    .out-item-right {
      padding-left: 6px;
      padding-bottom: 2px;
      padding-top: 0;
      padding-right: 0;
      margin-top: -1px;
    }
    .out-item-top {
      padding-bottom: 6px;
      padding-left: 0px;
      padding-top: 0;
      padding-right: 2px;
      margin-left: -1px;
    }
    .out-item-bottom {
      padding-top: 6px;
      padding-left: 0px;
      padding-bottom: 0;
      padding-right: 2px;
      margin-left: -1px;
    }

    /deep/ .el-input-number--mini {
      width: 100%;
      display: block;
      height: 100%;
      line-height: 30px;
    }
    /deep/ .el-input--mini {
      height: 100%;
      display: block;
    }
    /deep/ .el-input input {
      padding: 0;
      border: 0px;
      height: 100%;
    }
    /deep/ #boxPanel {
      width: unset !important;
      left: 0px;
    }
    /deep/ .tColor {
      display: flex;
    }
    /deep/ .zyb-color-container {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
