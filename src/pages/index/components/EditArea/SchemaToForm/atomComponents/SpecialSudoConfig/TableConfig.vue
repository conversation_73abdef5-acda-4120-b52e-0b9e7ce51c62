<template>
  <div class="table-wrapper" v-if="row * col && val.cells" :class="{ 'group-set-wrapper': !!editTags, [type]: true, 'show-operator': showOperator }" v-click-outside="cancelGroupSet">
    <div class="table-row inner-row" :class="{ first: index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :class="{ subfirst: sIndex === 0 }" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="cell-div" v-if="val.cells[index] && val.cells[index][sIndex]">
          <el-input
            v-if="type === 'number'"
            size="mini"
            :style="{ background: val.cells[index][sIndex].color || '#fff' }"
            :value="val['cells'][index][sIndex].text ? val['cells'][index][sIndex].text : undefined"
            @input="
              val => {
                handleChange(val, index, sIndex, 'text');
              }
            "
          ></el-input>
          <color-picker
            v-if="type === 'color'"
            :simple="true"
            :value="val.cells[index][sIndex].color ? val.cells[index][sIndex].color : undefined"
            @input="
              val => {
                handleChange(val, index, sIndex, 'color');
              }
            "
            :clearable="true"
            :simpleColors="colors"
          ></color-picker>
          <el-tag
            v-if="type === 'dashCell'"
            :class="{
              disabled: !!val.cells[index][sIndex].groupId && editTags,
            }"
            :disable-transitions="true"
            :style="genBorderStyle(index, sIndex)"
            :disabled="!!val.cells[index][sIndex].groupId"
            size="small"
            style="padding:0;"
            :color="val.cells[index][sIndex].color || '#fff'"
            @click.native="handleTagClick(index, sIndex)"
          >
            <el-tag
              :class="{
                disabled: !!val.cells[index][sIndex].groupId && editTags,
              }"
              :disable-transitions="true"
              style="padding:0;"
              v-if="!val.cells[index][sIndex].groupId && editTags"
              :disabled="!!val.cells[index][sIndex].groupId"
              size="small"
              :color="editTags[index][sIndex] === 1 ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0)'"
            />
            <div class="cancel-opt" type="text" icon="el-icon-delete" size="mini" v-if="val.cells[index][sIndex].groupId && editTags" @click.stop="handleCancelGroup(val.cells[index][sIndex].groupId)">
              <i class="el-icon-delete"></i>
            </div>
          </el-tag>

          <el-popover v-if="type === 'dashNumber' && val.cells[index][sIndex].groupId" placement="top" width="386" :ref="`pop${index}${sIndex}`" trigger="click">
            <div>
              <template v-if="!!dashNumberRules">
                <el-form :ref="`form${index}${sIndex}`" :model="dashNumberForm" :rules="dashNumberRules" label-position="right" :label-width="sudoType === '3' ? '90px' : '48px'">
                  <template v-if="sudoType === '2'">
                    <el-form-item label="结果" prop="extraText">
                      <el-input v-model="dashNumberForm.extraText" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="数字" prop="text">
                      <el-input v-model="dashNumberForm.text" size="small"></el-input>
                    </el-form-item>
                  </template>
                  <template v-if="sudoType === '3'">
                    <el-form-item label="结果&符号" prop="extraText">
                      <el-input v-model="dashNumberForm.extraText" size="small"></el-input>
                    </el-form-item>
                    <el-form-item label="题板内数字" prop="text">
                      <el-input v-model="dashNumberForm.text" size="small"></el-input>
                    </el-form-item>
                  </template>
                </el-form>
              </template>

              <div style="text-align: right; margin: 0">
                <el-button size="mini" type="text" @click="handleCancelPopover(index, sIndex)">取消</el-button>
                <el-button type="primary" size="mini" @click="handleConfirmPopover(index, sIndex)">确定</el-button>
              </div>
            </div>

            <el-tag
              :class="{
                'el-tag-reference': true,
              }"
              :disable-transitions="true"
              :style="genBorderStyle(index, sIndex)"
              size="medium"
              slot="reference"
              @click="handleShowPopover(index, sIndex)"
              :color="editTags && editTags[index][sIndex] === 1 ? 'rgba(0,0,0,0.2)' : '#fff'"
            >
              <!-- v-if="isGroupMinCell(val.cells[index][sIndex].groupId, index, sIndex)" -->
              <template>
                <span class="lt" v-if="isGroupMinCell(val.cells[index][sIndex].groupId, index, sIndex)">{{ getGroupNumbers(val.cells[index][sIndex].groupId).extraText }}</span>
                <span class="center">{{ val.cells[index][sIndex].text }}</span>
              </template>
            </el-tag>
          </el-popover>
          <el-tag :disable-transitions="true" v-if="type === 'dashNumber' && !val.cells[index][sIndex].groupId" size="medium" :color="'#fff'"> </el-tag>
        </div>
        <!-- 设置不等号 右/下 -->
        <div class="operator-tag operator-right" v-if="showOperator && sIndex < col - 1" @click="handleOperatorClick(index, sIndex, 'operatorRightText')"><span>{{ val.cells[index][sIndex].operatorRightText }}</span></div>
        <div class="operator-tag operator-bottom" v-if="showOperator && index < row - 1" @click="handleOperatorClick(index, sIndex, 'operatorBottomText')"><span>{{val.cells[index][sIndex].operatorBottomText}}</span></div>
      </div>
    </div>
    <el-button @click="handleSetGroupChange()" v-show="editTags" size="mini" class="set-group-opt">确认</el-button>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import ColorPicker from "@/components/zyb-color-picker/ColorPicker.vue";
import clickOutside from "vue-click-outside";
import { cloneDeep } from "lodash-es";
import { groupAdjacentColors3 } from "@/common/utils/dataValidate/customValidates/sudokuQuestion";

@Component({
  components: { ColorPicker },
  directives: {
    "click-outside": clickOutside,
  },
})
export default class SubTableConfig extends Vue {
  @Prop({
    required: true,
  })
  type!: string;

  @Prop({
    required: true,
  })
  row!: number;

  @Prop({
    required: true,
  })
  col!: number;

  @Prop({
    required: true,
  })
  val!: any;

  editTags: number[][] | null = null;

  editGroupId = 0;

  isSettingGroup = false;

  dashNumberForm = {
    text: "", // 区域数字
    extraText: "", // 区域的运算规则
  };
  dashNumberRules: any = null;
  get sudoType() {
    return this.val?.type;
  }
  get showOperator() {
    return this.type === 'number' && this.val?.type === '4';
  }

  genDashNumberRules() {
    const numberStr = this.getColNumberStr();
    const textPattern = new RegExp(`^[${numberStr}]{1}$`, "g");
    if (this.sudoType === "3") {
      // 运算数独
      return {
        text: [{ pattern: textPattern, message: `只允许输入${numberStr.split("").join("、")}`, trigger: "change" }],
        extraText: [
          { required: true, message: "请输入结果&符号", trigger: "change" },
          { pattern: /^(([0-9]{1,3})([+\-*/]{0,1}))$/, message: `只允许输入1～3位数字和最多1位（+、-、*、/）符号`, trigger: "change" },
        ],
      };
    } else {
      // 杀手数独
      return {
        text: [{ pattern: textPattern, message: `只允许输入${numberStr.split("").join("、")}`, trigger: "change" }],
        extraText: [
          { required: true, message: "请输入结果", trigger: "change" },
          { pattern: /^([0-9]{1,2})$/, message: `只允许输入最多两位0-9的数字`, trigger: "change" },
        ],
      };
    }
  }

  get colors() {
    return this.val.colors;
  }

  get max() {
    return 9;
  }

  get validation() {
    const numberStr = this.getColNumberStr();

    return new RegExp(`[^${numberStr}]`, "g");
  }

  getColNumberStr() {
    return Array(this.row)
      .fill(0)
      .map((i, index) => {
        return index + 1;
      })
      .join("");
  }
  handleOperatorClick(index: number, sIndex: number, directionKey: string) {
    console.log('handleCellHover', index, sIndex, directionKey);
    const operatorOps = ['<', '>', ''];
    const theCell = this.val.cells[index][sIndex];
    let val = operatorOps[0];
    if(theCell[directionKey]) {
      const pre = operatorOps.indexOf(theCell[directionKey]);
      val = operatorOps[pre + 1];
    }
    this.handleChange(val, index, sIndex, directionKey);
  }

  genBorderStyle(index: number, sIndex: number) {
    const theCell = this.val.cells[index][sIndex];
    const lcell = this.val.cells[index][sIndex - 1];
    const rcell = this.val.cells[index][sIndex + 1];
    const tcell = this.val.cells[index - 1]?.[sIndex];
    const bcell = this.val.cells[index + 1]?.[sIndex];
    const lcellInSameGroup = lcell?.groupId === theCell.groupId;
    const rcellInSameGroup = rcell?.groupId === theCell.groupId;
    const tcellInSameGroup = tcell?.groupId === theCell.groupId;
    const bcellInSameGroup = bcell?.groupId === theCell.groupId;
    const style = {
      "max-height": "28px",
    };
    if (!theCell.groupId) return style;
    const borderStr = `dashed 1px ${this.val.dashAreaColor}`;
    if (!lcellInSameGroup) {
      style["border-left"] = borderStr;
    }
    if (!rcellInSameGroup) {
      style["border-right"] = borderStr;
    }
    if (!tcellInSameGroup) {
      style["border-top"] = borderStr;
    }
    if (!bcellInSameGroup) {
      style["border-bottom"] = borderStr;
    }
    return style;
  }

  genEditTags() {
    return Array(this.row)
      .fill(0)
      .map(() => {
        return Array(this.col)
          .fill(0)
          .map(() => {
            return 0;
          });
      });
  }
  // 验证宫的颜色配置
  validateMarkColor(color: string) {
    // 跳过颜色为空的验证
    if (!color) return true;
    // 异形数独 验证
    if (this.val.isMark === "1") {
      const maxLen = this.row;
      let total = 1;
      this.val.cells.forEach((item: { color: string }[]) => {
        item.forEach((cell: { color: string }) => {
          if (cell.color === color) {
            total++;
          }
        });
      });
    }
    return true;
  }

  handleChange(...params: any[]) {
    // console.log("handleChange", params, this.validation);
    const [value, i, si, key] = params;
    let temp = value;
    if (key === "text") {
      temp = temp.replace(this.validation, "")[0] || "";
    }
    if (key === "color") {
      // 清空颜色时 需要考虑连续性被破坏的问题-todo
      // 找出相同的颜色个cell的个数 如果超出row设置为空
      // 宫的验证逻辑 先验证数量 数量正好满足时验证位置 不满足条件给出提示
      const color = temp || this.val.cells[i][si].color;
      const valid = this.validateMarkColor(color);
      // console.log("valid", valid);
      if (!valid) return;
    }
    params[0] = temp;
    this.$emit("change", ...params);
    // 杀手数独 标记区域 自动设置组
    // 运算数独 自动设置组
    const autoSetGroup = (this.sudoType === "2" && this.val.isMark === "0") || this.sudoType === "3";
    if (autoSetGroup && key === "color") {
      if (!temp) {
        // 如果是删除 需要把groupId删除
        this.$emit("setGroup", [[i, si]], "");
      }
      const color = temp || this.val.cells[i][si].color;
      // 判断这个颜色是否跟别的颜色相连
      if (color) {
        const colorCells = this.val.cells.map((item: { color: string }[]) => {
          return item.map((cell: { color: string }) => {
            return cell.color === color ? 1 : 0;
          });
        });
        // 色值相同且相邻 同一组
        let curMaxGroupId = 1;
        (colorCells as number[][]).forEach((item, i) => {
          item.forEach((cell, si) => {
            if (this.val["cells"][i][si].groupId > curMaxGroupId) {
              curMaxGroupId = this.val.cells[i][si].groupId;
            }
          });
        });
        colorCells[i][si] = temp ? 1 : 0;
        // color的位置记录 如果相邻groupId为
        const copyCell = cloneDeep(this.val.cells);
        copyCell[i][si].color = temp;
        // console.log('groupAdjacentColors3', groupAdjacentColors3(copyCell), copyCell);
        // console.log('groupAdjacentColors', this.groupAdjacentColors(copyCell), copyCell);
        const { groups } = groupAdjacentColors3(copyCell);
        Object.values(groups).forEach((group: any) => {
          group.parts.forEach((part: any[], i: string | number) => {
            const theParts = part.map(subPart => {
              const [x1, y1] = subPart.split(",");
              return [Number(x1), Number(y1)];
            });
            // console.log('setGroup-theParts', theParts, group.groupIds[i] || curMaxGroupId + 1);
            this.$emit("setGroup", theParts, group.groupIds[i] || curMaxGroupId + 1);
            if (!group.groupIds[i]) curMaxGroupId++;
          });
        });
      }
    }
  }
  groupAdjacentColors(matrix: number[][]) {
    const groups = [];
    const groupIds: string[] = [];
    let curGroupIndex = 0;
    for (let i = 0; i < matrix.length; i++) {
      for (let j = 0; j < matrix[i].length; j++) {
        const color = matrix[i][j];
        if (color) {
          const lcell = matrix[i][j - 1];
          const rcell = matrix[i][j + 1];
          const tcell = matrix[i - 1]?.[j];
          const bcell = matrix[i + 1]?.[j];
          const groupId = this.val.cells[i][j].groupId;

          if (lcell || rcell || tcell || bcell) {
            if (!groups[curGroupIndex]) {
              groups[curGroupIndex] = [];
            }
            groups[curGroupIndex].push([i, j]);
          } else {
            curGroupIndex++;
            groups[curGroupIndex] = [[i, j]];
          }
          if (groupId && !groupIds[curGroupIndex] && !groupIds.includes(groupId)) {
            groupIds[curGroupIndex] = groupId;
          }
        }
      }
    }
    return { groups, groupIds };
  }
  // 设置虚线框-确认按钮的位置
  handlePositionConfirmButton() {
    this.$nextTick(() => {
      const tableColWidth = (this.$el.querySelector(".dashCell .table-col") as any).clientWidth;
      const confirmButton = this.$el.querySelector(".set-group-opt");
      console.log("tableColWidth", `${tableColWidth * this.col + 16}px`, tableColWidth, confirmButton);
      if (confirmButton) {
        (confirmButton as any).style.left = `${tableColWidth * this.col + 16}px`;
      }
    });
  }
  handleTagClick(index: string | number, sIndex: string | number) {
    // 杀手数独 配置区域不可以编辑
    if (this.sudoType === "2" && this.val.isMark === "0") return;
    // 杀手数独 配置区域不可以编辑
    if (this.sudoType === "3") return;
    if (this.type === "dashCell" && !this.editTags) {
      this.$set(this, "editTags", this.genEditTags());
      this.isSettingGroup = false;
      // 设置按钮的位置
      this.handlePositionConfirmButton();
    }
    if (this.val["cells"][index][sIndex].groupId) return;
    // (this.editTags as Array<[number]>)[index][sIndex] = (this.editTags as Array<[number]>)[index][sIndex] ? 0 : 1;
    this.$set((this.editTags as Array<[number]>)[index], sIndex, (this.editTags as Array<[number]>)[index][sIndex] ? 0 : 1);
    // console.log("handleTagClick", index, sIndex, (this.editTags as Array<[number]>)[index][sIndex]);
  }
  handleSetGroupChange(editTags = this.editTags) {
    // console.log("handleSetGroupChange", editTags);
    // 校验是否可以组成一个虚线框

    const isValid = this.isShapeValid(editTags as number[][]);
    if (isValid) {
      // 找出所有的下标
      const pos: number[][] = [];
      let curMaxGroupId = 0;
      let curGroupId = 0;
      (editTags as number[][]).forEach((item, i) => {
        item.forEach((cell, si) => {
          if (cell) {
            pos.push([i, si]);
            curGroupId = this.val.cells[i][si].groupId || curGroupId || 0;
          }
          if (this.val["cells"][i][si].groupId > curMaxGroupId) {
            curMaxGroupId = this.val.cells[i][si].groupId;
          }
        });
      });
      // console.log("setGroup", pos, curGroupId, curMaxGroupId + 1, editTags);
      this.$emit("setGroup", pos, curGroupId || curMaxGroupId + 1);
      this.isSettingGroup = false;
      this.editTags = null;
      this.$message.success("虚线框编辑成功");
    } else {
      this.isSettingGroup = true;
      this.$message.error("虚线框必须相互连接");
    }
    // console.log("isValid", isValid);
  }
  handleCancelGroup(groupId: number) {
    // console.log("handleCancelGroup");
    const pos: number[][] = [];
    (this.editTags as number[][]).forEach((item, i) => {
      item.forEach((cell, si) => {
        if (this.val["cells"][i][si].groupId === groupId) {
          pos.push([i, si]);
        }
      });
    });
    this.$emit("setGroup", pos, 0);
  }

  /**
   * 深度优先搜索（DFS）
   * 从给定的位置开始，访问所有连续的1，并返回访问过的1的数量
   */
  dfs([i, j]: [number, number], shape: number[][], visited: boolean[][]): number {
    // 如果位置无效或者已经被访问过，返回0
    if (i < 0 || i >= shape.length || j < 0 || j >= shape.length || shape[i][j] === 0 || visited[i][j]) {
      return 0;
    }

    // 标记当前位置已经被访问过
    visited[i][j] = true;

    // 访问当前位置的上、下、左、右四个位置，并返回访问过的1的数量
    return 1 + this.dfs([i - 1, j], shape, visited) + this.dfs([i + 1, j], shape, visited) + this.dfs([i, j - 1], shape, visited) + this.dfs([i, j + 1], shape, visited);
  }
  isShapeValid(shape: number[][]): boolean {
    if (!shape) {
      return false;
    }

    const shapeLength: number = shape.length;
    let oneCount = 0;
    // 创建一个二维数组记录每个位置是否被访问过
    const visited: boolean[][] = Array(shapeLength)
      .fill(false)
      .map(() => Array(shapeLength).fill(false));

    // 找到第一个1的位置并计算1的总数
    let start: [number, number] | null = null;
    for (let i = 0; i < shapeLength; i++) {
      for (let j = 0; j < shapeLength; j++) {
        if (shape[i][j] === 1) {
          oneCount++;
          if (start === null) {
            start = [i, j];
          }
        }
      }
    }

    // 如果没有1，返回false
    if (oneCount === 0) {
      return true;
    }

    // 如果只有一个1，返回true
    if (oneCount === 1) {
      return true;
    }

    // 使用深度优先搜索（DFS）访问所有连续的1
    const visitedCount: number = this.dfs(start!, shape, visited);

    // 如果所有的1都连续，返回true
    return visitedCount === oneCount;
  }
  handleShowPopover(index: number, sIndex: number) {
    console.log("handleShowPopover", index, sIndex);

    this.dashNumberRules = this.genDashNumberRules();
    const groupId = this.val.cells[index][sIndex].groupId;
    const findGroup = this.val["groups"].find((item: { id: number }) => item.id === groupId);
    this.$set(this, "dashNumberForm", {
      text: this.val.cells[index][sIndex].text, // 区域数字
      extraText: findGroup ? findGroup.extraText : "", // 区域的运算规则
    });
  }
  handleConfirmPopover(index: number, sIndex: number) {
    console.log("handleConfirmPopover", index, sIndex);
    const form = this.$refs[`form${index}${sIndex}`]?.[0];
    if (form) {
      form.validate((valid: any) => {
        // console.log("valid", valid);
        if (valid) {
          const isMinCell = this.isGroupMinCell(this.val.cells[index][sIndex].groupId, index, sIndex);
          this.$emit("setGroupNumber", this.val.cells[index][sIndex].groupId, { ...this.dashNumberForm, index, sIndex, isMinCell });
          (this.$refs[`pop${index}${sIndex}`] || [{ showPopper: false }])[0].showPopper = false;
          this.dashNumberForm = {
            text: "", // 区域数字
            extraText: "", // 区域的运算规则
          };
          this.dashNumberRules = null;
        } else {
          this.$message.error("输入有误，请检查后重试");
        }
      });
    }
  }
  handleCancelPopover(index: number, sIndex: number) {
    // console.log("handleCancelPopover", index, sIndex);
    (this.$refs[`pop${index}${sIndex}`] || [{ showPopper: false }])[0].showPopper = false;
    this.dashNumberForm = {
      text: "", // 区域数字
      extraText: "", // 区域的运算规则
    };
    this.dashNumberRules = null;
  }
  // 找到组中最小的cell
  isGroupMinCell(groupId: number, index: number, sIndex: number) {
    // const theGroup = this.val.groups.find((item: { id: any; }) => item.id === groupId);
    let findI = -1;
    let findSI = -1;
    this.val.cells.some((item: any[], i: number) => {
      item.some((cell: { groupId: number }, si: number) => {
        if (cell.groupId === groupId) {
          findI = i;
          findSI = si;
          return true;
        }
      });
      if (findI > -1) return true;
    });
    return findI === index && sIndex === findSI;
  }

  getGroupNumbers(groupId: number) {
    const theGroup = this.val.groups.find((item: { id: any }) => item.id === groupId);
    return theGroup || { text: "", extraText: "" };
  }
  cancelGroupSet() {
    this.isSettingGroup = false;
    this.editTags = null;
  }
}
</script>

<style lang="less" scoped>
.special-sudo-config {
  .table-wrapper {
    // border-left: 1px solid #e1e2e6;
    // border-top: 1px solid #e1e2e6;
    position: relative;
    &.color {
      .cell-div {
        max-height: 28px !important;
      }
    }
    &.group-set-wrapper {
      width: calc(100% - 60px);
    }
    &.show-operator {
      .cell-div {
        padding: 3px !important;
      }
    }
    .set-group-opt {
      position: absolute;
      top: 50%;
      transform: translate(0px, -50%);
      padding: 5px !important;
      left: 100%;
    }
    box-sizing: border-box;
    .table-row {
      display: flex;
    }
    .outside-row {
      display: flex;
    }
    .operator-right {
      // opacity: 0;
      // &:hover {
      //   opacity: 1;
      // }
      cursor: pointer;
      position: absolute;
      top: 0px;
      bottom: 0px;
      right: -3px;
      width: 6px;
      // border: 1px dashed #e9e9eb;
      // background-color: rgba(255,255,255,0.5);
      // background-color: rgba(0,0,0,0.5);
      // background-color: red;
      color: #909399;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
    }
    .operator-bottom {
      // opacity: 0;
      // &:hover {
      //   opacity: 1;
      // }
      cursor: pointer;
      position: absolute;
      bottom: -3px;
      right: 0px;
      left: 0;
      height: 6px;
      width: 100%;
      // border: 1px dashed #e9e9eb;
      // background-color: rgba(0,0,0,0.5);
      // background-color: green;
      color: #909399;
      z-index: 10;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      span {
        display: block;
        transform: rotate(90deg);
      }
    }
    .cell-div {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      // background: #fff;
      .lt {
        position: absolute;
        left: 2px;
        top: -8px;
        font-size: 10px;
        color: #333;
      }
      /deep/ input {
        text-align: center !important;
      }
      // 数字的背景色使用配置的色值
      /deep/ .el-input__inner {
        background: transparent !important;
      }
    }
    /deep/ .el-tag {
      width: 100%;
      height: 100%;
      border-color: rgba(0, 0, 0, 0);
      &:hover {
        .cancel-opt {
          cursor: pointer;
          pointer-events: all;
          color: #333;
          i {
            opacity: 1;
          }
        }
      }

      .cancel-opt {
        display: block;
        width: 100%;
        height: 100%;
        text-align: center;
        line-height: 26px;
        pointer-events: none;
        cursor: not-allowed;
        background: rgba(255, 255, 255, 0.3);
        i {
          opacity: 0;
        }
      }
      &.el-tag-reference {
        position: relative;
        // display: flex;
        // justify-content: center;
        // align-content: center;

        .center {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          font-size: 14px;
          margin-top: 4px;
          color: #333;
        }
      }
    }
    .table-col {
      width: 50px;
      border-bottom: 1px solid #e1e2e6;
      border-right: 1px solid #e1e2e6;
      box-sizing: border-box;
      position: relative;
    }
    // 第一个inner-row中的 inner-item
    .first .inner-item {
      border-top: 1px solid #e1e2e6;
    }
    // 第一行的第一个单元格
    // .inner-row:first-child .inner-item {
    //   border-top: 1px solid #e1e2e6;
    // }
    // 每一行的第一个单元格
    .inner-item:first-child {
      border-left: 1px solid #e1e2e6;
    }
    .subfirst {
      border-left: 1px solid #e1e2e6;
    }
    .opacity {
      opacity: 0;
    }
    .out-item {
      border: 0px !important;
      // padding: 6px;
      .cell-div {
        border: 1px solid #e1e2e6;
      }
    }
    .first .out-item-left,
    .first .out-item-right {
      margin-top: 0px;
    }
    .first .out-item-top,
    .first .out-item-bottom {
      margin-left: 0px;
    }
    .out-item-left {
      padding-right: 6px;
      padding-bottom: 2px;
      padding-top: 0;
      padding-left: 0;
      margin-top: -1px;
    }
    .out-item-right {
      padding-left: 6px;
      padding-bottom: 2px;
      padding-top: 0;
      padding-right: 0;
      margin-top: -1px;
    }
    .out-item-top {
      padding-bottom: 6px;
      padding-left: 0px;
      padding-top: 0;
      padding-right: 2px;
      margin-left: -1px;
    }
    .out-item-bottom {
      padding-top: 6px;
      padding-left: 0px;
      padding-bottom: 0;
      padding-right: 2px;
      margin-left: -1px;
    }

    /deep/ .el-input-number--mini {
      width: 100%;
      display: block;
      height: 100%;
      line-height: 30px;
    }
    /deep/ .el-input--mini {
      height: 100%;
      display: block;
    }
    /deep/ .el-input input {
      padding: 0;
      border: 0px;
      height: 100%;
    }
    /deep/ #boxPanel {
      width: unset !important;
      left: 0px;
    }
    /deep/ .tColor {
      display: flex;
    }
    /deep/ .zyb-color-container {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
