<template>
  <div class="special-option-config">
    <el-form size="small" label-position="left" hide-required-asterisk :model="ruleForm" :rules="rules" ref="ruleForm">
      <el-form-item v-if="isShowType" :label="optionTextConfig.label">
        <el-radio-group v-model="optionType">
          <el-radio label="text">文本</el-radio>
          <el-radio label="img">图片</el-radio>
        </el-radio-group>
      </el-form-item>
      <div class="option-list-title">
        {{ optionTextConfig.listTitle }}
        <el-tooltip :content="optionTextConfig.labelTips" placement="top" effect="light" v-if="optionTextConfig.labelTips">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div v-if="options.length" class="option-list-box">
        <el-form-item v-for="(item, index) in options" :label="getOrder(index)" :prop="'input' + index" :key="index">
          <div class="option-box">
            <div class="option-box-input">
              <el-input v-if="optionType === 'text'" placeholder="请输入内容" :maxlength="textMaxLength" v-model="item.text"> </el-input>
              <ImageSelect v-if="optionType === 'img'" :src.sync="item.imgUrl" :is-show-delete-btn="false" :max-width="stageSafeWidth" :max-height="stageSafeHeight" />
            </div>
            <div class="option-box-operations">
              <i v-if="options.length < max" class="el-icon-circle-plus-outline" @click="add(index)"></i>
              <i v-if="options.length > min" class="el-icon-remove-outline" @click="del(index)"></i>
            </div>
          </div>
        </el-form-item>
      </div>
      <i v-else class="el-icon-circle-plus-outline" @click="add(0)"></i>
      <el-form-item label="正确答案" v-if="isShowAnswer">
        <el-select v-model="answer" placeholder="请选择选项" :multiple="isMultipleAnswer">
          <el-option v-for="(item, index) in options" :key="index" :label="getOrder(index)" :value="index"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep, debounce, isEqual } from "lodash-es";
import store from "@/pages/index/store";
import { DEBOUNCE_TIME } from "@/pages/index/common/utils/tools";
const ORDER_SEAT = "{{$}}";

@OverviewDataCollection({
  label: "选项列表",
  labelTips: "一个选项一个输入框",
  optionTextConfig: {
    label: "",
    listTitle: "",
  },
  value: {
    type: "text", // text:文本, img:图片
    options: [], // 选项列表
    answer: "", // 选项下标,从0开始
  },
})
@Component({ components: { ImageSelect } })
export default class SpecialOptionConfig extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialOptionConfig";
  created() {
    if (this.val?.options?.length) {
      this.options = cloneDeep(this.val.options);
    }
  }
  get ruleForm() {
    if (!this.specialOptionConfig?.textConfig?.validation) return null;
    return this.options.reduce((obj: Record<string, string>, item: { text: string }, index: number) => {
      obj[`input${index}`] = item.text;
      return obj;
    }, {});
  }
  get rules() {
    const validation = this.specialOptionConfig?.textConfig?.validation;
    if (!validation) return null;
    if (this.optionType !== "text") return null;
    return this.options.reduce((obj: Record<string, any>, item: { text: string }, index: number) => {
      obj[`input${index}`] = [
        { required: true, message: this.specialOptionConfig?.textConfig?.validationMessage || "请输入内容", trigger: ["blur", "change"] },
        {
          trigger: ["blur", "change"],
          validator: (rule: { message: string }, value: string, callback: any) => {
            try {
              const reg = new RegExp(validation as string);
              if (!reg.test(value)) {
                // 重新赋值正则匹配的值
                this.options[index].text = reg.exec(value)?.[0] || "";
                callback(new Error(this.specialOptionConfig?.textConfig?.validationMessage || "输入内容校验不通过"));
              } else {
                callback();
              }
            } catch (error) {
              console.error(error);
              callback();
            }
          },
        },
      ];
      return obj;
    }, {});
  }
  get stageSafeWidth() {
    return store.state.stageData.safeWidth;
  }
  get stageSafeHeight() {
    return store.state.stageData.safeHeight;
  }
  get textMaxLength() {
    return this.specialOptionConfig?.textConfig?.maxLength || Infinity;
  }
  get isShowType() {
    const typeConfig = this.specialOptionConfig.typeConfig;
    if (!typeConfig) return true;
    if (!typeConfig.show) {
      // 不显示类型配置表单时,需保证当前value的类型与配置一致
      const typeVal = typeConfig.type || "text";
      if (typeVal !== this.val?.type) {
        const cloneVal = cloneDeep(this.val) || {};
        this.setVal({
          options: [], // 选项列表
          answer: "", // 选项下标,从0开始
          ...cloneVal,
          type: typeVal,
        });
      }
    }
    return typeConfig.show;
  }

  get isShowAnswer() {
    const answerConfig = this.specialOptionConfig.answerConfig;
    if (!answerConfig) return true;
    return answerConfig.show;
  }
  get isMultipleAnswer() {
    const answerConfig = this.specialOptionConfig.answerConfig;
    if (!answerConfig) return false;
    return answerConfig.multiple;
  }
  get optionTextConfig() {
    return {
      label: "选项列表",
      listTitle: "选项列表",
      ...this.specialOptionConfig.optionTextConfig,
    };
  }

  get specialOptionConfig() {
    return (this.formItemConfigs as unknown) as SpecialOptionConfigAbstract;
  }
  get optionType() {
    return this.val?.type;
  }
  set optionType(type) {
    if (type === this.val?.type) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.type = type;
    this.options = cloneVal.options?.map(() => ({ text: "", imgUrl: "" })) || [];
    cloneVal.options = cloneDeep(this.options);
    this.setVal(cloneVal);
    this.$nextTick(() => {
      (this.$refs.ruleForm as any)?.resetFields();
    });
  }
  get answer() {
    return this.val?.answer;
  }
  set answer(answer) {
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.answer = answer;
    this.setVal(cloneVal);
  }
  options: Array<{ text: string; imgUrl: string }> = [];
  @Watch("options", { deep: true })
  watchOptions() {
    this.debounceFun(this.val, this.options);
  }

  @Watch("val.options.length")
  watchOptionsLength(val: number) {
    if (val !== this.options.length) {
      // console.log('外部原因触发的更改');
      this.options = cloneDeep(this.val.options);
    }
  }

  // 防抖函数
  debounceFun = debounce((val, options) => {
    let cloneVal = cloneDeep(val);
    if (isEqual(cloneVal?.options, options)) return;
    if (cloneVal) {
      cloneVal.options = cloneDeep(options);
      if (cloneVal.answer >= cloneVal.options.length) {
        cloneVal.answer = "";
      }
    } else {
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: [], // 选项列表
        answer: "", // 选项下标,从0开始
      };
    }
    this.setVal(cloneVal);
  }, DEBOUNCE_TIME);
  get min() {
    return this.specialOptionConfig.min || 0;
  }
  get max() {
    return this.specialOptionConfig.max || Infinity;
  }
  get orderConfig() {
    return (
      this.specialOptionConfig?.orderConfig || {
        type: "letter",
        decorate: "选项{{$}}",
      }
    );
  }
  add(index: number) {
    let cloneVal = cloneDeep(this.val);
    if (cloneVal) {
      this.options.splice(index + 1, 0, { text: "", imgUrl: "" });
      cloneVal.options = cloneDeep(this.options);
    } else {
      this.options = [{ text: "", imgUrl: "" }];
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: cloneDeep(this.options), // 选项列表
        answer: "", // 选项下标,从0开始
      };
    }
    this.setVal(cloneVal);
  }
  // 删除子项
  del(index: number) {
    const cloneVal = cloneDeep(this.val);
    this.options.splice(index, 1);
    cloneVal.options = cloneDeep(this.options);
    // 多选
    if (this.isMultipleAnswer) {
      const answer = cloneVal.answer as Array<number>;
      if (!answer || !answer.length) return;
      const delIndex = answer && answer.findIndex(item => item === index);
      if (delIndex > -1) {
        answer.splice(delIndex, 1);
      }
      answer.forEach((item, i) => {
        if (item > index) {
          answer[i] = item - 1;
        }
      });
    } else if (this.answer >= this.options.length) {
      // 单选
      cloneVal.answer = "";
    }
    this.setVal(cloneVal);
  }
  // 转换列表项序号
  getOrder(index: number) {
    const { orderConfig } = this;
    if (!orderConfig) return `选项${String.fromCharCode(65 + index)}`;
    if (orderConfig?.type === "letter") {
      const letter = String.fromCharCode(65 + index);
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, letter) : letter;
    } else {
      const i = (index + 1).toString();
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, i) : i;
    }
  }
}
</script>

<style lang="less" scoped>
.option-list-title {
  line-height: 36px;
  font-size: 12px;
  color: #777;
}
.option-list-box {
  padding-left: 5px;
}
.el-icon-circle-plus-outline,
.el-icon-remove-outline {
  cursor: pointer;
}
.option-box {
  display: flex;
  .option-box-input {
    width: 100%;
    flex: auto;
  }
  .option-box-operations {
    text-align: right;
    padding-left: 5px;
    width: 35px;
    flex: none;
  }
}
</style>
