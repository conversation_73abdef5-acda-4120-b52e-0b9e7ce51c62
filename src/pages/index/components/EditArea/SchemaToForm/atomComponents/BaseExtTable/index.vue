<template>
  <div class="base-ext-table">
    <div class="table-row out-row" v-if="formConfig.hasOutsideGrid">
      <div class="table-col out-item out-item-left opacity">左</div>
      <div class="table-col out-item out-item-top" v-for="(sitem, sIndex) in col" :key="`${sitem}`">
        <CellItem
          :currentComponent="currentComponent"
          :cellConfigs="formConfig.cellConfigs.top"
          :value="(val['outerTables'][0] || [])[sIndex]"
           @change="(val) => {handleChange(val, 0, sIndex, 'outerTables', formConfig.cellConfigs.top.type)}"
        />
      </div>
      <div class="table-col out-item out-item-right opacity"></div>
    </div>
    <div class="table-row inner-row" :class="{ 'first': index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col out-item out-item-left" v-if="formConfig.hasOutsideGrid">
        <!-- 左 3-->
        <CellItem
          :currentComponent="currentComponent"
          :cellConfigs="formConfig.cellConfigs.left"
          :value="(val['outerTables'][3] || [])[index]"
          @change="(val) => {handleChange(val, 3, index, 'outerTables', formConfig.cellConfigs.left.type)}"
        />
      </div>
      <div class="table-col inner-item" :class="{ 'subfirst': sIndex === 0 }" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <CellItem
          :currentComponent="currentComponent"
          :cellConfigs="formConfig.cellConfigs.center"
          :colors="formConfig.colors"
          :value="(val['innerTables'][index] || [])[sIndex]"
          @change="(val) => {handleChange(val, index, sIndex, 'innerTables', formConfig.cellConfigs.center.type)}"
        />
      </div>
      <div class="table-col out-item out-item-right" v-if="formConfig.hasOutsideGrid">
        <!-- 右 1-->
        <CellItem
          :currentComponent="currentComponent"
          :cellConfigs="formConfig.cellConfigs.right"
          :value="(val['outerTables'][1] || [])[index]"
          @change="(val) => {handleChange(val, 1, index, 'outerTables', formConfig.cellConfigs.right.type)}"
        />
      </div>
    </div>
    <div class="table-row out-row" v-if="formConfig.hasOutsideGrid">
      <div class="table-col out-item out-item-left opacity">
        <div class="cell-div"></div>
      </div>
      <div class="table-col out-item out-item-bottom" v-for="(sitem, sIndex) in col" :key="`${sitem}`">
        <!-- 下2-->
        <CellItem
          :currentComponent="currentComponent"
          :cellConfigs="formConfig.cellConfigs.bottom"
          :value="(val['outerTables'][2] || [])[sIndex]"
          @change="(val) => {handleChange(val, 2, sIndex, 'outerTables', formConfig.cellConfigs.bottom.type)}"
        />
      </div>
      <div class="table-col out-item out-item-right opacity">
        <div class="cell-div">
          右
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import CellItem from './CellItem.vue';
import cloneDeep from "lodash-es/cloneDeep";
import { isEqual } from "lodash-es";
// 支持的类型有 select color input
@OverviewDataCollection({
  label: "题板配置",
  text: "点击生成",
  labelTips: `outerTables: 二维数组，是外层格子的数据<br/>
      innerTables: 二维数组，是内部格子的数据<br/>
      hasOutsideGrid?<boolean>: true 有外部格子 false 无外部格子<br/>
      cellDefaultValue<number|string>: 格子的默认值<br/>
      cellConfigs: 格子上下左右和中心（内部格子的所有单元格）的配置<br/>
      cellConfigs的配置参考： type是number, 支持额外的参数max<br/>
      cellConfigs的配置参考： type是color, 支持额外的参数"colors": ['red', 'blue']<br/>
      row<number>: 单元格横向个数<br/>
      col<number>: 单元格的纵向个数<br/>
      rowKey<string>: 单元格横向个数关联的key, 必须放在properties的第一级数据中，不然找不到<br/>
      colKey<string>: 单元格纵向个数关联的key, 必须放在properties的第一级数据中，不然找不到<br/>`
      ,
  value: {
    outerTables: [
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined]
    ],
    innerTables: [
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined]
    ],
  },
  hasOutsideGrid: true,
  "cellDefaultValue": "",
  "cellConfigs": {
    "left": {
      "type": "number",
      "max": 3
    },
    "top": {
      "type": "select",
      "max": 3
    },
    "right": {
      "type": "color",
      "colors": ['red', 'blue']
    },
    "bottom": {
      "type": "text",
      "colors": ['red', 'blue']
    },
    "center": {
      "type": "number",
      "max": 3
    },
  },  // 'color' | ｜ 'number' ｜ 'text' ｜ 'select'
  row: 5,
  col: 5,
  "rowKey": "stuRow",
  "colKey": "stuCol",
})

@Component({
  components: {
    CellItem
  }
})
export default class BaseExtTable extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseExtTable";
  init = false;

  @Watch("val", { deep: true })
  onValChange() {
    if(this.init) return;
    // 题板规格变更时 不更新数据
    // 判断二维数组为空
    let isRefreshBlankData = true;
    [...this.val['innerTables'], ...(this.val['outerTables'] || [])].forEach((item) => {
      item.forEach((subItem: any) => {
        if(subItem) isRefreshBlankData = false;
      })
    })
    if(!isRefreshBlankData) return;
    // 空数据 清空input .base-ext-table input
    this.setVal(this.val);
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  get row() {
    return this.updateDataObj[this.formItemConfigs.rowKey] || this.currentComponents[0]['properties'][this.formItemConfigs.rowKey] || this.formItemConfigs.row
  }
  
  get col() {
    return this.updateDataObj[this.formItemConfigs.colKey] || this.currentComponents[0]['properties'][this.formItemConfigs.colKey] || this.formItemConfigs.col
  }

  get size() {
    return {
      row: this.row,
      col: this.col
    }
  }

  @Watch("size", { deep: true, immediate: true })
  onSizeChange(newVal: any, oldVal: any) {
    if(isEqual(newVal, oldVal)) return;
    console.log('onSizeChange', newVal, oldVal);
    // row col 变更时 重置数据
    const val: { outerTables: any[][], innerTables: any[][] } = {
      outerTables: [],
      innerTables: []
    };
    // outerTables 生成上（0）右（1）下（2）左（3）4个数组，其中上下的个数是col， 左右的个数是row
    // 是否需要生成外部的
    if(this.formConfig.hasOutsideGrid) {
      val.outerTables = Array.from({ length: 4 }, (item, index) => {
      let len = Number(this.col);
      if (index % 2) {
        len = Number(this.row);
      }
      return Array.from({ length: len }, (sItem, sIndex) => {
        if(!oldVal && typeof (this.val.outerTables[index] || [])[sIndex] !== 'undefined') {
          return (this.val.outerTables[index] || [])[sIndex] ?? this.formConfig.cellDefaultValue;
        } else {
          return this.formConfig.cellDefaultValue;
        }
      });
    })
    }
    
    for(let i = 0; i < this.row; i++) {
      const innerTemp: any[] = [];
      for(let j = 0; j < this.col; j++) {
        if(!oldVal && typeof (this.val.innerTables[i] || [])[j]!== 'undefined') {
          innerTemp.push((this.val.innerTables[i] || [])[j] ?? this.formConfig.cellDefaultValue);
        } else {
          innerTemp.push(this.formConfig.cellDefaultValue);
        }
      }
      val.innerTables.push(innerTemp);
    }
    if(!isEqual(val, this.val)) {
      this.setVal(val);
    }
  }

  @Watch("formConfig.cellConfigs", { deep: true, immediate: true })
  onCellConfigChange(newVal: any, oldVal: any) {
    if(isEqual(newVal, oldVal)) return;
    if(!oldVal) return;
    const tempVal = cloneDeep(this.val);
    // const diff = difference(Object.values(newVal), Object.values(oldVal));
    const indexMap = {
      'top': 0,
      'right': 1,
      'bottom': 2,
      'left': 3,
    }
    Object.keys(newVal).forEach((key: string) => {
      const config = newVal[key]
      if(!isEqual(config, oldVal[key])) {
        // outerTables 生成上（0）右（1）下（2）左（3）
        if(key === 'center') {
          tempVal.innerTables.forEach((item: any[]) => {
            item.forEach((_sItem: any, sIndex: string|number) => {
              item[sIndex] = this.formConfig.cellDefaultValue
            })
          })
        } else {
          const index = indexMap[key];
          tempVal.outerTables[index].forEach((_item: any, sIndex: string|number) => {
            tempVal.outerTables[index][sIndex] = this.formConfig.cellDefaultValue
          })
        }
      }
    })
    if(!isEqual(tempVal, this.val)) {
      // console.log('onCellConfigChange', tempVal);
      this.setVal(tempVal);
    }
  }

  created() {
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }
  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex, key, type] = params;
    let temp = value;
    if(['text', 'number'].includes(type as string) && value) {
      temp = Math.round(Number(value));
    }
    const valClone = cloneDeep(this.val);
    valClone[key][index][subIndex] = temp;
    this.setVal(valClone);
  }
}
</script>

<style lang="less" scoped>
.base-ext-table {
  width: calc(100% - 2px);
  // border-left: 1px solid #e1e2e6;
  // border-top: 1px solid #e1e2e6;
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .outside-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
  }
  .table-col {
    max-width: 50px;
    flex: 1;
    border-bottom: 1px solid #e1e2e6;
    border-right: 1px solid #e1e2e6;
    box-sizing: border-box;
  }
  // 第一个inner-row中的 inner-item
  .first .inner-item {
    border-top: 1px solid #e1e2e6;
  }
  // 第一行的第一个单元格
  // .inner-row:first-child .inner-item {
  //   border-top: 1px solid #e1e2e6;
  // }
  // 每一行的第一个单元格
  .inner-item:first-child {
    border-left: 1px solid #e1e2e6;
  }
  .subfirst {
    border-left: 1px solid #e1e2e6;
  }
  .opacity {
    opacity: 0;
    flex: 1;
  }
  .out-item {
    border: 0px !important;
    // padding: 6px;
    .cell-div {
      border: 1px solid #e1e2e6;
    }
  }
  .first .out-item-left,  .first .out-item-right {
    margin-top: 0px;
  }
  .first .out-item-top,  .first .out-item-bottom {
    margin-left: 0px;
  }
  .out-item-left {
    padding-right: 6px;
    padding-bottom: 2px;
    padding-top: 0;
    padding-left: 0;
    margin-top: -1px;
  }
  .out-item-right {
    padding-left: 6px;
    padding-bottom: 2px;
    padding-top: 0;
    padding-right: 0;
    margin-top: -1px;
  }
  .out-item-top {
    padding-bottom: 6px;
    padding-left: 0px;
    padding-top: 0;
    padding-right: 2px;
    margin-left: -1px;
  }
  .out-item-bottom {
    padding-top: 6px;
    padding-left: 0px;
    padding-bottom: 0;
    padding-right: 2px;
    margin-left: -1px;
  }
  
  /deep/ .el-input-number--mini {
    width: 100%;
    display: block;
    height: 100%;
    line-height: 30px;
  }
  /deep/ .el-input--mini {
    height: 100%;
  }
  /deep/ .el-input input {
    padding: 0;
    border: 0px;
    height: 100%;
  }
  /deep/ .el-select, /deep/ .el-select .el-input {
    padding: 0;
    border: 0px;
    height: 100%;
  }
  /deep/ .el-select {
    display: block;
  }
  /deep/ #boxPanel {
    width: unset !important;
    left: 0px;
  }
  /deep/ .tColor {
    display: flex;
  }
  /deep/ .zyb-color-container {
    width: 100%;
    height: 100%;
  }
  /deep/ .zyb-color-wrapper {
    width: 100%;
    height: 100%;
  }
}
</style>
