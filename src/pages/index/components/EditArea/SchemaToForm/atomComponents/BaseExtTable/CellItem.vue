<template>
  <div class="cell-div">
    <el-input-number
      v-if="cellConfigs.type === 'number'"
      :min="min"
      :max="max"
      :step="1"
      :step-strictly="true"
      type="number"
      size="mini"
      controls-position="right"
      :controls="false"
      :value="!value && value !== 0 ? undefined : value"
      @change="
        val => {
          handleChange(val);
        }
      "
    ></el-input-number>
    <color-picker
      v-if="cellConfigs.type === 'color'"
      :simple="true"
      :simpleColors="cellConfigs.colors"
      :value="value"
      @input="
        val => {
          handleChange(val);
        }
      "
    ></color-picker>
    <el-select
      v-if="cellConfigs.type === 'select'"
      :value="value"
      v-bind="(cellConfigs.optionsConfig || {}).config"
      :multiple="false"
      @change="
        val => {
          handleChange(val);
        }
      "
      clearable
    >
      <el-option v-show="!!item.label" v-for="item in options" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Watch, Prop, Vue } from "vue-property-decorator";
import ColorPicker from "@/components/zyb-color-picker/ColorPicker.vue";
// 支持的类型有 select color input
@Component({
  components: {
    ColorPicker,
  },
})

// todo-prop 添加强校验
export default class CellItem extends Vue {
  @Prop({
    required: true,
  })
  value!: string | number;

  @Prop({
    required: true,
  })
  currentComponent!: any;

  @Prop({
    required: true,
  })
  cellConfigs!: any;

  static componentName = "BaseExtTable";

  handleChange(val: any) {
    this.$emit("change", val);
  }

  get max() {
    return this.currentComponent["properties"][this.cellConfigs.maxKey] || this.cellConfigs.max;
  }

  get min() {
    return (this.currentComponent["properties"][this.cellConfigs.minKey] || this.cellConfigs.min) ?? 1;
  }

  get options() {
    if (this.cellConfigs.optionsConfig) {
      const {
        optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, disabledKey, valueKey },
      } = this.cellConfigs;
      const keys = relativePropertiesKey.split(".");

      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while (keys.length) {
        let key = keys.shift();
        if (key === "subQuestionIndex") key = this.$attrs.parentIndex;
        if (temp[key]) {
          temp = temp[key];
        } else {
          temp = [];
        }
        if (!keys.length) {
          options = temp.map((option: any, index: number) => {
            return {
              ...option,
              disabled: disabledKey ? option[disabledKey] : option['disabled'],
              label: labelKey ? option[labelKey] : `${labelPrefix}${index + 1}`,
              value: valueKey === "index" ? index : option[valueKey],
            };
          });
        }
      }
      // console.log('options.....', options);
      return options;
    }
    return this.cellConfigs.options;
  }

  @Watch("options")
  onOptionsChange(val: any[], oldVal: any[]) {
    this.$nextTick(() => {
      // 只处理删除的情况
      if ((val || []).length >= (oldVal || []).length) return;
      this.$emit("optionsdelete", val, oldVal);
      // const optionsValue = this.options.map((option: any) => {

      //   return option.value;
      // });
      // // 判断二维数组是否都为空
      // let isAllBlankData = true;
      // let needChange = false;
      // const temp = cloneDeep(this.val);
      // [...this.val].forEach((item, index: number) => {
      //   item.forEach((subItem: any, subIndex: number) => {
      //     if(subItem || subItem === 0) {
      //       isAllBlankData = false;
      //       if(!optionsValue.includes(subItem)) {
      //         temp[index][subIndex] = "";
      //         needChange = true;
      //       }
      //     }
      //   })
      // })
      // if(isAllBlankData) return;
      // if(needChange) {
      //   this.setVal(temp);
      // }
    });
  }
}
</script>
