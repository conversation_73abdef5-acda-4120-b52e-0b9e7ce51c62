
<template>
  <div class="base-checkbox">
    <el-checkbox v-model="val">{{ formItemConfigs.checkboxLabel }}</el-checkbox>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "复选框",
  value: true,
  checkboxLabel: "选项"
})
@Component({})
export default class BaseCheckbox extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseCheckbox";
}
</script>

<style lang="less" scoped>
</style>
