<template>
  <div class="special-architecture-master">
    <el-dialog :visible.sync="visible" top="0vh" class="dialog-sam">
      <div class="base-tag-table" v-if="visible">
        <div class="table-row inner-row" :class="{ first: index === 0 }" v-for="(item, index) in row" :key="item">
          <div class="table-col inner-item" :style="cellStyle" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
            <div class="base-tag cell-div">
              <el-tag :color="tempVal[index][sIndex] === 1 ? formItemConfigs['active'].color : formItemConfigs.color" v-bind="formItemConfigs" 
              @click="handleClick(index, sIndex)"
               ></el-tag>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClickClose">取 消</el-button>
        <el-button type="primary" @click="onClickConfirm">
          确 定
        </el-button>
      </span>
    </el-dialog>
    <el-button @click="handleShowClick" size="small" type="primary">{{ formItemConfigs.text }}</el-button>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import { checkMaterialList } from "@/common/utils/eventListener/architectureMasterQuestion";
import { cloneDeep } from "lodash-es";

@OverviewDataCollection({
  label: "",
  text: "自动生成题面",
  labelPosition: "top",
  color: "#909399",
  active: {
    color: "#ffffff",
  },
  col: 3,
  row: 3,
  span: 8,
  rowKey: "stuRow",
  colKey: "stuCol",
  width: "30px",
  height: "30px",
  value: [
    [0, 0, 0, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 0],
  ],
})
@Component({})
export default class SpecialArchitectureMasterTable extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialArchitectureMasterTable";
  visible = false;
  tempVal = [
    [0, 0, 0, 0],
    [0, 0, 1, 0],
    [0, 0, 0, 0],
    [0, 0, 0, 1],
  ];
  onClickClose() {
    this.visible = false;
  }

   get row() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.rowKey] || this.formItemConfigs.row
  }
  
  get col() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.colKey] || this.formItemConfigs.col
  }

  get cellStyle() {
    return {
      maxWidth: this.formItemConfigs.width,
      height: this.formItemConfigs.height,
    };
  }

  handleClick(index: number, subIndex: number) {
    this.$set(this.tempVal[index], subIndex, this.tempVal[index][subIndex] === 1 ? 0 : 1);
  }

  public handleShowClick() {
    console.log("click");
    // 1. 验证筑材是否配置是否正确--change
    const { properties } = this.currentComponents[0];
    const { stuMaterialList, stuGenQuestion } = properties;
    const checkRes = checkMaterialList(stuMaterialList);
    // stuGenQuestion
    if (checkRes !== true) {
      return;
    }
    // 2. 获取数据
    this.visible = true;
    // 保留自动/手动生成的结果
    this.tempVal = Array.from({ length: Number(this.row) }, (item, index) => {
      return Array.from({ length: Number(this.col) }, 
      (sItem, sIndex) => {
        return stuGenQuestion?.[index]?.[sIndex] === 0 ? 0 : 1
      })
    })
  }

  onClickConfirm() {
    // 校验-未配置题面（如果没有一个空白格）
     let isAllBlank = true;
    [...this.tempVal].forEach((item) => {
      item.forEach((subItem: any) => {
        if(subItem) isAllBlank = false;
      })
    })
    if(isAllBlank) {
      this.$message.error('未配置题面');
      return;
    }
    this.setVal(cloneDeep(this.tempVal))
    this.visible = false;
  }
}
</script>

<style lang="less" scoped>
.base-tag-table {
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
    overflow: hidden;
    width: 100%;
    /deep/ .el-tag {
      width: 100%;
      height: 100%;
      border-color: #e9e9eb;
    }
  }
  .table-col {
    box-sizing: border-box;
    position: relative;
    flex: 1;
  }
}
.special-architecture-master {
  /deep/ .el-dialog__body {
    display: flex;
    justify-content: center;
  }
}
</style>
