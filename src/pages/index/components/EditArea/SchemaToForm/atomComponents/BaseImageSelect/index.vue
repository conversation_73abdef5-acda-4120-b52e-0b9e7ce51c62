
<template>
  <div :class="`${cssPrefix}`">
    <ImageSelect
      :src.sync="val"
      :is-show-delete-btn="clearable"
      :max-width="stageSafeWidth"
      :max-height="stageSafeHeight"
    />
    <el-input v-model="val" style="display: none;"></el-input>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";
import store from "@/pages/index/store";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "图片选择",
  value: "",
  rule: [
    {
      required: true,
      message: "请选择XX图片",
      trigger: "change" //  这里必须为change, 否则不会触发由空变为有颜色的校验
    },
  ]
})
@Component({ components: { ImageSelect } })
export default class BaseImageSelect extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseImageSelect";
  cssPrefix = "base-image-select";

  get stageSafeWidth() {
    return store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return store.state.stageData.safeHeight;
  }

  get clearable() {
    // 默认无删除按钮
    return this.formItemConfigs.clearable;
  }
}
</script>

<style lang="less">
@CSS_PREFIX: base-image-select;
@WIDTH: 120px;
.@{CSS_PREFIX} {
  .image-select {
    width: @WIDTH;
    min-height: 32px;
    max-height: 200px;
  }
  .edit-btn-container {
    width: @WIDTH !important;
  }
  .image-container {
    width: @WIDTH !important;
  }
}
</style>
