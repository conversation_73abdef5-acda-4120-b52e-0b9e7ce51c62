<template>
  <div class="base-input">
    <el-input :value="val" v-bind="formItemConfigs" @input="validate($event)"/>
  </div>
</template>

<script lang="ts">
import DebounceClassForm from "../../DebounceClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "文本输入框",
  value: "",
})
@Component({})
export default class BaseInput extends DebounceClassForm<BaseInputType> {
  static componentName = "BaseInput";
  // forceUpdate在4/11新增，用来解决非手动输入时表单的变更
  get forceUpdateVal() {
    return (this.formItemConfigs as any).forceUpdate;
  }
  validate(val: any) {
    const { validation } = this.formItemConfigs;

    let temp = this.val;
    if (validation) {
      const reg = new RegExp(validation as string);
      if(reg.test(val as string)) {
        temp = val;
      }
      this.val = temp;
    } else {
      this.val = val;
    }
  }
  @Watch("forceUpdateVal")
  onForceChange(val: string) {
    this.validate(val);
  }
}
</script>

<style lang="less" scoped>
.base-input {
  min-height: 30px;
}
</style>
