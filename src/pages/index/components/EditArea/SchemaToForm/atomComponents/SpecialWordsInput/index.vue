<template>
  <div class="special-words-input">
    <el-input :value="val" v-bind="formItemConfigs" @input="validate($event)" @blur="blurHandler" />
    <div class="word-check" v-if="errWordsList.length">
      <p class="error-word">{{errWordsList.map(item => item.word).join('，')}}无法测评，请联系产运老师处理<span></span></p>
    </div>
    <div class="word-check" v-if="wordCheckRight">
      <span class="right-tips">
        单词校验正确
      </span>
    </div>
    <div class="word-check" v-if="chineseCheckWrong">
      <span class="chinese-tips">
        不能包含中文！
      </span>
    </div>
    <div class="word-check" v-if="!chineseCheckWrong && !wordCheckRight && !errWordsList.length" style="min-height: 20px;"></div>
  </div>
</template>

<script lang="ts">
import DebounceClassForm from "../../DebounceClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import { isEqual } from "lodash-es";
import { checkWordIsInASR } from "@/common/utils/asrRequest";

@OverviewDataCollection({
  label: "单词输入框",
  value: "",
  hasEvaluate: true,
  evaluateKey: "phonetics",
})
@Component
export default class SpecialWordsInput extends DebounceClassForm<SpecialWordsInput> {
  static componentName = "SpecialWordsInput";

  englishReg = /[^a-zA-Z0-9\s-\\.!@#\\$%\\\\^&\\*\\)\\(\\+=\\{\\}\\[\]\\/",'<>~\\·`\\?:;|]+/g;

  wordReplace = /[^a-zA-Z0-9'\s-]+/g;

  errWordsList: any[] = [];

  wordCheckRight = false;

  chineseCheckWrong = false;

  // forceUpdate在4/11新增，用来解决非手动输入时表单的变更
  phoneticsCache: any = null;
  get forceUpdateVal() {
    return (this.formItemConfigs as any).forceUpdate;
  }
  validate(val: any) {
    const { validation } = this.formItemConfigs;

    let temp = this.val;
    if (validation) {
      const reg = new RegExp(validation as string);
      if (reg.test(val as string)) {
        temp = val;
      }
      this.val = temp;
    } else {
      this.val = val;
    }
  }

  get evaluateVal() {
    return this.getEvaluateVal();
  }
  set evaluateVal(val) {
    this.setEvaluateVal(val);
  }

  get phoneticConfig() {
    return this.formItemConfigs.phoneticConfig;
  }

  get phoneticKey() {
    return this.phoneticConfig?.relativePropertiesKey;
  }
  get phoneticOptionsKey() {
    return this.phoneticConfig?.optionsRelativeKey;
  }

  get showPhoneticKey() {
    return this.getValByKey(this.phoneticConfig?.showRelativeKey) === this.phoneticConfig?.showRelativeValue;
  }

  async handlePhonetic() {
    await this.loadPhoneticsData();
    if (this.showPhoneticKey) {
      const data = this.phoneticsCache || {};
      const evaluatePhoneticsOptions = ((data as any)[this.val as string] || []).map((item: any) => {
        return {
          // label: item,
          value: item,
        };
      });
      if (isEqual(this.getValByKey(this.phoneticOptionsKey), evaluatePhoneticsOptions)) return;
      this.setEvaluateVal("");
      this.setValByKey(evaluatePhoneticsOptions, this.phoneticOptionsKey);
    } else {
      this.setEvaluateVal("");
      if (this.phoneticOptionsKey) {
        this.setValByKey([], this.phoneticOptionsKey);
      }
    }
  }

  @Watch("val")
  onValChange() {
    if (this.forceUpdateVal) {
      this.$emit("update:forceUpdate", false);
    }
    this.errWordsList = [];
    this.wordCheckRight = false;
    this.chineseCheckWrong = false;
    // 如果显示音标，则清空
    this.handlePhonetic();
  }

  getEvaluateVal() {
    if (!this.phoneticKey) {
      return "";
    }
    return this.getValByKey(this.phoneticKey);
  }

  setEvaluateVal(val: any) {
    console.log("43-setEvaluateVal...", val);
    const { relativePropertiesKey: phoneticKey } = this.phoneticConfig || {};
    if (phoneticKey) {
      this.setValByKey(val, phoneticKey);
    }
  }

  async loadPhoneticsData() {
    if (!this.phoneticsCache) {
      const module = await import("@/pages/index/components/EditArea/VoiceComponentEditor/phoneticsList.json");
      this.phoneticsCache = module.default || module;
    }
    return this.phoneticsCache;
  }

  get evaluatePhoneticsOptions() {
    const data = this.phoneticsCache || {};
    console.log("evaluatePhoneticsOptions...", data);
    return ((data as any)[this.val as string] || []).map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });
  }

  blurHandler() {
    console.log("单词输入框-blurHandler", this.val);
    const val = (this.val as string) || "";
    if (val.match(this.englishReg)) {
      this.wordCheckRight = false;
      this.chineseCheckWrong = true;
      return;
    } else {
      this.chineseCheckWrong = false;
    }
    const words = val.replace(this.wordReplace, " ");
    this.errWordsList = [];
    if (!words) {
      this.wordCheckRight = false;
      return;
    }
    checkWordIsInASR(words)
      .then(res => {
        const data = res.data.data;
        const WordsList: any[] = data.WordsList || [];
        WordsList.forEach((item: any) => {
          if (item.in_dict !== 0) {
            this.errWordsList.push(item);
          }
        });
        if (this.errWordsList.length === 0) {
          this.wordCheckRight = true;
        } else {
          this.wordCheckRight = false;
        }
        console.warn(this.errWordsList, "this.errWordsLis");
      });
  }

  // 监听hasEvaluate变化
  @Watch("showPhoneticKey", { immediate: true })
  onHasEvaluateChange() {
    this.handlePhonetic();
  }
}
</script>

<style lang="less" scoped>
.base-input {
  min-height: 30px;
}
.word-check {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  .error-word {
    color: rgba(240, 83, 72, 1);
    margin: 0;
    line-height: 25px;
  }
  .error-tips {
    color: rgba(240, 83, 72, 1);
  }
  .right-tips {
    color: #42c57a;
    // margin-left: -124px;
  }
  .chinese-tips {
    color: rgba(240, 83, 72, 1);
    // margin-left: -124px;
  }
  .add-words {
    float: right;
    color: #42c57a;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
