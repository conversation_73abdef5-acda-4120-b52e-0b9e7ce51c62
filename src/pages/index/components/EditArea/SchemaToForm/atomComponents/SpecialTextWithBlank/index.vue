<!-- eslint-disable @typescript-eslint/camelcase -->
<template>
  <div class="special-text-with-blank-wrapper">
    <vue-tinymce ref="tinymce" :value="val" :setting="setting" :setup="setup"></vue-tinymce>
    <span class="words-count">{{ `${valueLen}/${formConfig.maxLength}` }}</span>
    <div class="el-form-item__error words-error-wrapper" v-if="valueLen > formConfig.maxLength">最多输入{{ formConfig.maxLength }}个字</div>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import "@/pages/index/tinymce";
import contentCss from "!!raw-loader!tinymce/skins/content/default/content.min.css";
import contentUiCss from "!!raw-loader!tinymce/skins/ui/oxide/content.min.css";
import isEqual from "lodash-es/isEqual";
import { cloneDeep, debounce } from "lodash-es";
import { Message } from "element-ui";

@OverviewDataCollection({
  label: "富文本带填空",
  // eslint-disable-next-line no-useless-escape
  labelTips: "",
  labelPosition: "top",
  maxLength: 150,
  blankMin: 1,
  blankMax: 5,
  blankOptionsKey: "stuBlankOptions",
  // eslint-disable-next-line no-useless-escape
  value: '空的标识：<img class="editor-blank-img" data-time="1687227862738" />',
})
@Component({
  name: "SpecialTextWithBlank",
})
export default class SpecialTextWithBlank extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialTextWithBlank";
  debounceFun = debounce(() => {
    this.contentChange();
  }, 300);
  oldContent = "";
  public setting: any = {
    menubar: false,
    skin: false,
    // eslint-disable-next-line @typescript-eslint/camelcase
    content_css: false,
    toolbar: ["myBlank"],
    plugins: "wordcount paste",
    // eslint-disable-next-line @typescript-eslint/camelcase
    fontsize_formats: "14px",
    branding: false,
    elementpath: false,
    language: "zh_CN",
    height: 200,
    // eslint-disable-next-line @typescript-eslint/camelcase
    min_height: 200,
    // eslint-disable-next-line @typescript-eslint/camelcase
    max_height: 450,
    // eslint-disable-next-line @typescript-eslint/camelcase
    forced_root_block: false,
    // eslint-disable-next-line @typescript-eslint/camelcase
    content_style:
      [contentCss, contentUiCss].join("\n") +
      `body {font-size: 14px}
      .editor-blank-img {
        width: 80px;
        height: 32px;
        vertical-align: middle;
        display: inline;
        background: #cdcdcd
      }
      .tox-statusbar__wordcount {
        display: none !important;
      }
      .mce-resizehandle {
        display: none !important;
      }
    `, // 初始化赋值
    // eslint-disable-next-line @typescript-eslint/camelcase
    paste_data_images: false,
    placeholder: "输入内容",
    // eslint-disable-next-line @typescript-eslint/camelcase
    paste_as_text: true,
    // eslint-disable-next-line @typescript-eslint/camelcase
    paste_preprocess: function(plugin: any, args: any) {
      console.log("paste_postprocess", args.content);
      // eslint-disable-next-line no-useless-escape
      if (args.content.indexOf('<img class="editor-blank-img" data-time="') > -1) {
        // eslint-disable-next-line no-useless-escape
        args.content = args.content.replace(/<img class=\"editor-blank-img\" data-time=\"\d+\" \/>/g, "");
      }
    },
    // eslint-disable-next-line @typescript-eslint/camelcase
    init_instance_callback: (editor: any) => {
      console.log("0 init_instance_callback");
      this.resetPlaceholderStyle();
      this.initEditorVal();
      editor.on("input paste undo redo Keyup ", () => {
        this.checkVal();
      });
      editor.on("Change SetContent KeyUp", () => {
        this.debounceFun();
      });
    },
  };
  editor: any;
  valueLen = 0;
  get blankOptions() {
    const key = this.formItemConfigs.blankOptionsKey || "stuBlankOptions";
    return this.updateDataObj[key] || this.currentComponents[0].properties[key] || [];
  }
  initEditorVal() {
    if (this.val) {
      this.editor.setContent(this.val);
      this.valueLen = this.getValueLen();
      this.oldContent = this.val;
    }
  }

  getValueLen() {
    const wordcount = (this as any).$tinymce.activeEditor.plugins.wordcount;
    return wordcount.body.getCharacterCount();
  }

  checkVal() {
    const wordcount = (this as any).$tinymce.activeEditor.plugins.wordcount;
    if (this.valueLen != wordcount.body.getCharacterCount()) {
      this.valueLen = wordcount.body.getCharacterCount();
    }
    // 不截取 还原之前的数据
    if (this.valueLen > this.formConfig.maxLength) {
      Message.error(`最多输入${this.formConfig.maxLength}个字`);
      // 还原之前的数据
      this.editor.setContent(this.oldContent);
      return;
    } else {
      this.oldContent = this.editor.getContent();
    }
  }
  resetPlaceholderStyle() {
    const iframeDocument = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument;
    // console.log("is null?", iframeDocument, document.querySelector(".tox-edit-area__iframe"));
    if (!iframeDocument) return;
    const style = document.createElement("style");
    style.type = "text/css";
    style.innerText = `
    #tinymce {
      font-size: 14px;
      line-height: 1.5;
      margin: 0 !important;
    }
    #tinymce .editor-blank-img {
      width: 60px;
      height: 24px;
      vertical-align: middle;
      display: inline;
      margin: 6px;
    }
    #tinymce::before {
      font-size: 14px;
      color: #C0C4CC;
      font-family: Arial,Avenir, Helvetica, sans-serif;
      font-weight: 300;
    }
    ::-webkit-scrollbar-track-piece {
      background-color :#fff;
      border-radius: 2em;
    }
    ::-webkit-scrollbar {
      width: 0px;
    }
    ::-webkit-scrollbar-thumb {
      background-color:rgba(95,101,113,1);
      background-clip:padding-box;
      border-radius: 2em;
      border: 4px solid rgba(255,255,255,1);
      width:0px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background-color: red;
    }
    #tinymce:hover ::-webkit-scrollbar {
      width: 6px;
    }
    `;
    iframeDocument.head.appendChild(style);
  }
  decodeHtml(str: string) {
    const HTML_ENCODE = {
      "&ldquo;": "“",
      "&rdquo;": "”",
      "&lsquo;": "‘",
      "&rsquo;": "’",
      "&mdash;": "—",
      "&middot;": "·",
      "&nbsp;": " ",
      "&hellip;":"…",
      "&lt;":"<",
      "&gt;":">"
    };
    const REGX_HTML_DECODE = /&\w+;|&#(\d+);/g;
    return typeof str != "string"
      ? str
      : str.replace(REGX_HTML_DECODE, function($0, $1) {
          let c = HTML_ENCODE[$0]; // 尝试查表
          if (c === undefined) {
            // Maybe is Entity Number
            if (!isNaN($1)) {
              c = String.fromCharCode($1 == 160 ? 32 : $1);
            } else {
              // Not Entity Number
              c = $0;
            }
          }
          return c;
        });
  }
  contentChange() {
    if (!(this as any).$tinymce || !(this as any).$tinymce.activeEditor) return;
    const wordcount = (this as any).$tinymce.activeEditor.plugins.wordcount;
    const content = this.editor.getContent();
    if (this.valueLen != wordcount.body.getCharacterCount()) {
      this.valueLen = wordcount.body.getCharacterCount();
    }
    this.val = this.decodeHtml(content);
    this.oldContent = this.val;

    this.setBlankOptions(this.val);
  }
  public setup(editor: any) {
    this.editor = editor;
    editor.ui.registry.addIcon(
      "editor-blank",
      '<svg t="1686541572072" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="23449" width="32" height="32"><path d="M32 242l0 540c0 16.79999999 13.2 30 30 30l900 0c16.79999999 0 30-13.2 30-30l0-540c0-16.79999999-13.2-30-30-30L62 212c-16.79999999 0-30 13.2-30 30z" p-id="23450" fill="#cdcdcd"></path></svg>',
    );
    editor.ui.registry.addToggleButton("myBlank", {
      icon: "editor-blank",
      tooltip: "插入空格",
      onAction: () => {
        const tinymce = this.$refs.tinymce;
        const content = (tinymce as any).editor.getContent();
        // eslint-disable-next-line no-useless-escape
        const blankTag = `<img class=\"editor-blank-img\" data-time=\"`;
        // 查找字符串出现的次数
        if (content.split(blankTag).length > this.formConfig.blankMax) {
          this.$message.error(`最多插入${this.formConfig.blankMax}个空格`);
          return;
        }
        const timeStamp = Date.now();
        (tinymce as any).editor.selection.setContent(
          (tinymce as any).editor.dom.createHTML("img", {
            class: "editor-blank-img",
            "data-time": timeStamp,
          }),
        );
      },
    });
  }

  public setBlankOptions(content: string) {
    console.log("call setBlankOptions", content);
    // 添加拦截 如果填空没有发生变化 不处理
    // eslint-disable-next-line no-useless-escape
    const reg = /data-time=\"(\d+)\"/g;
    const arr = content.match(reg);
    console.log("arr", arr);
    const blankOptions = arr?.map((item: string, index: number) => {
      return {
        label: item.slice(12, -1),
        value: index + 1,
      };
    });
    if (isEqual(blankOptions, this.blankOptions)) return;
    const oldVal = cloneDeep(this.blankOptions);
    this.setValByKey(blankOptions, this.formItemConfigs.blankOptionsKey || "stuBlankOptions");
    this.handleChangeByKey(blankOptions, oldVal, this.formItemConfigs.blankOptionsKey || "stuBlankOptions");
  }
  beforeDestroy() {
    console.log('test-content', 'beforeDestroy');
    this.editor = null;
    // 解决频繁切换组件导致的富文本数据为空的问题
    (this.debounceFun as any) = null;
  }
}
</script>

<style lang="less" scoped>
.special-text-with-blank-wrapper {
  position: relative;
  font-size: 12px;
  .words-count {
    position: absolute;
    border: 0;
    z-index: 10000;
    bottom: 1px;
    right: 16px;
    font-size: 12px;
    color: #999;
    background: #fff;
    height: 16px;
    line-height: 16px;
    text-align: center;
  }
}
</style>
