<template>
  <div class="base-slider">
    <el-slider v-model="val" v-bind="formConfig" />
  </div>
</template>
  
<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "滑块",
  value: 10,
})
@Component({})
export default class BaseSlder extends BaseClassForm<BaseSliderType> {
  static componentName = "BaseSlider";
}
</script>
  
<style lang="less" scoped>
.base-slider /deep/ .el-slider__runway {
  margin: 12px 0;
}
</style>
  