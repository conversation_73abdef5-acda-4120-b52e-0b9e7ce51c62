<template>
  <el-radio-group class="base-radio-group" v-model="val" v-bind="radioGroupConfig">
    <el-row class="base-radio-row">
      <el-col class="base-radio-col" v-for="radioConfig in formConfig.options"
        :key="radioConfig.label" :span="radioConfig.span" :offset="radioConfig.offset || 0">
        <el-radio v-bind="radioConfig" :label="radioConfig.value">{{ radioConfig.label }}</el-radio>
      </el-col>
    </el-row>
  </el-radio-group>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "单选框组",
  value: "1",
  options: [
    { label: "选项1", value: "1" },
    { label: "选项2", value: "2" },
    { label: "选项3", value: "3" },
  ],
})
@Component({})
export default class BaseRadioGroup extends BaseClassForm<BaseRadioGroupType> {
  static componentName = "BaseRadioGroup";

  get radioGroupConfig() {
    const radioGroupConfig = { ...this.formConfig };
    delete radioGroupConfig.options;
    return radioGroupConfig;
  }
}
</script>

<style lang="less" scoped>
.base-radio-group {
  width: 100%;
  .base-radio-col {
    & > label {
      line-height: 28px;
    }
  }
}
</style>
