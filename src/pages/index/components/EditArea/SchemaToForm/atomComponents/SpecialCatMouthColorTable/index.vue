<template>
  <div class="special-cat-mouth-table">
    <div class="table-row" :class="{ first: index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :class="{ subfirst: sIndex === 0 }" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="cell-div">
          <el-tag :color="val[index][sIndex] === 1 ? formItemConfigs['active'].color : formItemConfigs.color" 
          :class="{ disabled:  val[index][sIndex] === -1}"
          v-bind="formItemConfigs" @click="handleClick(index, sIndex)" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import cloneDeep from "lodash-es/cloneDeep";

// 4*4=》 5 5*5 =》 6
@OverviewDataCollection({
  label: "猫鼠游戏-答案题板",
  labelPosition: "top",
  labelTips: "",
  value: [
    [-1,0,0,0],
    [0,0,0,0],
    [0,0,0,0]
  ],
  color: '#ffffff',
  active: {
    color: '#42c57a'
  },
  disabled: {
    color: '#f5f7fa'
  },
  rowKey: "row",
  colKey: "col",
  row: 3,
  col: 3,
})
// 0 默认 1 active -1 disabled
@Component
export default class SpecialCatMouthColorTable extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialCatMouthColorTable";
  init = false;
  get row() {
    return this.currentComponents[0]["properties"][this.formItemConfigs.rowKey] || this.formItemConfigs.row;
  }

  get col() {
    return this.currentComponents[0]["properties"][this.formItemConfigs.colKey] || this.formItemConfigs.col;
  }

  handleClick(index: number, subIndex: number) {
    if(this.val[index][subIndex] === -1) return;
    const valClone = cloneDeep(this.val);
    valClone[index][subIndex] = valClone[index][subIndex] === 1 ? 0 : 1;
    this.setVal(valClone);
  }

  @Watch("val", { deep: true })
  onValChange() {
    if (this.init) return;
    // 题板规格变更时 不更新数据
    // 判断二维数组为空
    let isRefreshBlankData = true;
    [...this.val].forEach(item => {
      item.forEach((subItem: any) => {
        if (subItem) isRefreshBlankData = false;
      });
    });
    if (!isRefreshBlankData) return;
    this.setVal(this.val);
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  created() {
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex] = params;
    const valClone = cloneDeep(this.val);
    valClone[index][subIndex] = value;
    this.setVal(valClone);
  }
  get options() {
    if (this.formItemConfigs.optionsConfig) {
      const {
        optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, valueKey },
      } = this.formItemConfigs;
      const keys = relativePropertiesKey.split(".");

      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while (keys.length) {
        let key = keys.shift();
        if (key === "subQuestionIndex") key = this.$attrs.parentIndex;
        if (temp[key]) {
          temp = temp[key];
        } else {
          temp = [];
        }
        if (!keys.length) {
          options = temp.map((option: any, index: number) => {
            return {
              ...option,
              label: labelKey ? option[labelKey] : `${labelPrefix}${index + 1}`,
              value: valueKey === "index" ? index : option[valueKey],
            };
          });
        }
      }
      return options;
    }
    return this.formItemConfigs.options;
  }

  @Watch("options")
  onOptionsChange(val: any[], oldVal: any[]) {
    this.$nextTick(() => {
      // 只处理删除的情况
      if (val.length >= oldVal.length) return;
      // 找出选项内容发生变化的option
      // const changedOptions = this.options.filter((option: any, index: number) => {
      //   const oldOption = oldVal[index];
      //   if(!oldOption) return true;
      //   return !isEqual(option, oldOption);
      // }).map((item: { value: any; }) => item.value);
      const optionsValue = this.options.map((option: any) => {
        return option.value;
      });
      // 判断二维数组是否都为空
      let isAllBlankData = true;
      let needChange = false;
      const temp = cloneDeep(this.val);
      [...this.val].forEach((item, index: number) => {
        item.forEach((subItem: any, subIndex: number) => {
          if (subItem || subItem === 0) {
            isAllBlankData = false;
            if (!optionsValue.includes(subItem)) {
              temp[index][subIndex] = null;
              needChange = true;
            }
          }
        });
      });
      if (isAllBlankData) return;
      if (needChange) {
        this.setVal(temp);
      }
    });
  }
}
</script>

<style lang="less" scoped>
.special-cat-mouth-table {
  box-sizing: border-box;
  margin-top: 20px;
  margin-left: 20px;
  .table-row {
    display: flex;
    .table-col:last-child {
      border-width: 0px;
      width: 0px;
      &::before {
        display: none;
      }
      &::after {
        display: none;
      }
    }
    &:last-child {
      .table-col {
        border-width: 0px;
        &::before {
          display: none;
        }
        &::after {
          display: none;
        }
      }
    }
  }
  .cell-div {
    width: 100%;
    height: 100%;
    border: 1px solid #bbb;
    height: 26px;
    line-height: 26px;
    width: 50px;
    overflow: hidden;
    position: absolute;
    transform: translate(-50%, -50%);
    background: #fff;
    z-index: 10;
     /deep/ .el-tag {
      width: 100%;
      height: 100%;
      border-radius: 0px;
      border: 0px solid #bbb;
      cursor: pointer;
      &.disabled {
        cursor: not-allowed;
        background-color: #f4f4f5 !important;
        border-color: #e9e9eb;
      }
    }
  }
  .table-col {
    width: 66px;
    border-bottom: 1px solid #bbb;
    border-right: 1px solid #bbb;
    box-sizing: border-box;
    position: relative;
    height: 66px;
    &::before {
      content: "";
      width: 1px;
      height: 141%;
      border-left: 1px #bbb dashed;
      transform: rotate(45deg);
      position: absolute;
      right: 0;
      top: 1px;
      transform-origin: top right;
    }
    &::after {
      content: "";
      width: 1px;
      height: 141%;
      border-left: 1px #bbb dashed;
      transform: rotate(-45deg);
      position: absolute;
      position: absolute;
      left: 0%;
      transform-origin: top left;
      top: 1px;
    }
  }
  .first .inner-item {
    border-top: 1px solid #bbb;
  }
  // 每一行的第一个单元格
  .inner-item:first-child {
    border-left: 1px solid #bbb;
  }
  .subfirst {
    border-left: 1px solid #bbb;
  }

  /deep/ .el-input-number--mini {
    width: 100%;
    display: block;
    height: 100%;
    line-height: 30px;
  }
  /deep/ .el-input--mini {
    height: 100%;
  }
  /deep/ .el-input input {
    padding: 0;
    border: 0px;
    height: 100%;
  }
  /deep/ input {
    position: absolute;
  }
}
</style>
