<template>
  <div class="base-tag">
    <el-tag :color="color" v-bind="formItemConfigs" @click="handleClick" />
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "标签",
  color: '#909399',
  active: {
    color: '#ffffff'
  },
  span: 8,
  value: 0 //  0-color  1-active.color
})
@Component({})
export default class BaseTag extends BaseClassForm<BaseTagType> {
  static componentName = "BaseTag";
  handleClick() {
    this.setVal(this.val === 1 ? 0 : 1)
  }
  // 1 active 0 normal
  get color() {
    return this.val === 1 ? this.formItemConfigs['active'].color : this.formItemConfigs.color
  }
}
</script>

<style lang="less" scoped>
.base-tag {
  width: 100%;
  /deep/ .el-tag {
    width: 100%;
    border-color: #e9e9eb;
  }
}
</style>
