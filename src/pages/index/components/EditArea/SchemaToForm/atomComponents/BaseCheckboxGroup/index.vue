
<template>
  <div class="base-checkbox-list">
    <el-checkbox-group v-model="val" v-bind="checkboxGroupConfig">
      <el-row>
        <el-col v-for="item in formItemConfigs.options" :key="item.value" :span="item.span"
          :offset="item.offset">
          <el-checkbox :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-col>
      </el-row>
    </el-checkbox-group>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "复选框组",
  value: ["1"],
  options: [
    { value: "1", label: "武汉" },
    { value: "2", label: "合肥" },
    { value: "3", label: "北京" },
  ],
})
@Component({})
export default class BaseCheckboxGroup extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseCheckboxGroup";
  get val() {
    return this.getVal() || [];
  }
  set val(val) {
    this.setVal(val);
  }
  get checkboxGroupConfig() {
    const checkboxGroupConfig = { ...this.formConfig };
    delete checkboxGroupConfig.options;
    return checkboxGroupConfig;
  }
}
</script>

<style lang="less" scoped>
.el-checkbox {
  line-height: 28px;
}
</style>
