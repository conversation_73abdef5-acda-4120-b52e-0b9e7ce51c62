<template>
  <div class="base-input-number">
    <el-input
      ref="input"
      v-model.number="val"
      type="number"
      :step="formConfig.step"
      :placeholder="formConfig.placeholder"
      :min="formConfig.min"
      :max="formConfig.max"
      :disabled="formConfig.disabled"
      :step-strictly="formConfig.stepStrictly"
      @input="handleChange"
      @blur="handleBlur"
      @keyup.enter.native="handleBlur"
      :key="propertiesKey"
    />
  </div>
</template>

<script lang="ts">
import DebounceClassForm from "../../DebounceClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
// 外部因素导致的更新数据时 需要使用forceUpdate

@OverviewDataCollection({
  label: "数字输入框",
  value: 0,
})
@Component({})
export default class BaseInputNumber extends DebounceClassForm<Record<string, any>> {
  static componentName = "BaseInputNumber";
  isInit = true;
  handleBlur() {
    const { val } = this;
    let temp = val;
    const { max, min, step = 1, stepStrictly } = this.formConfig;
    // 只能输入step的倍数 否则向上取整
    if (stepStrictly && step) {
      // 根据step确定小数的位数
      const stepStr = String(step);
      const stepStrArr = stepStr.split('.');
      const stepDecimal = stepStrArr[1] ? stepStrArr[1].length : 0;
      // 如果最后一位是小数点
      if(String(temp).at(-1) === '.' && !String(step).includes('.')) {
        temp = Number(temp)
      }
      if (Number(val) % step !== 0) {
        temp = Math.round(Number(val) / step) * step;
      }
      // 如果最后一位是小数点
      if(String(temp).at(-1) === '.' && !String(step).includes('.')) {
        temp = Number(temp)
      }
      if (Number(val) % step !== 0) {
        // 保留stepDecimal位小数
        temp = Number(val).toFixed(stepDecimal);
      }
    }
    if (typeof max !== 'undefined' && val > max) temp = max;
    if (typeof min !== 'undefined' && val < min) temp = min;
    const inputDom = this.$el.querySelector('.base-input-number input');
    if(inputDom) {
      (inputDom as any).value = temp;
    }
    this.val = (Number(temp));
  }
  handleChange(val: any) {
    console.log('handleChange...', val)
    const { max, min, step = 1, stepStrictly } = this.formConfig;
    let temp = val;
    if(val === "" || val < min) {
      //  this.setVal(temp);
       this.val = (temp);
       return;
    };
    let stepDecimal = 0;
    // 只能输入step的倍数 否则向上取整
    if (stepStrictly && step) {
      // 根据step确定小数的位数
      const stepStr = String(step);
      const stepStrArr = stepStr.split('.');
      stepDecimal = stepStrArr[1] ? stepStrArr[1].length : 0;
    // 如果有小数点，但是输入的位数不足， 先不处理这一次change
    if(String(val).split('.').length > 1 && stepDecimal > String(val).split('.')[1].length) return;

      // 如果最后一位是小数点
      if(String(temp).at(-1) === '.' && !String(step).includes('.')) {
        temp = Number(temp)
      } else if (val % step !== 0) {
        // 保留stepDecimal位小数
        temp = Number(val).toFixed(stepDecimal);
      }
    }
    if (typeof max !== 'undefined' && val > max) temp = max;
    if (typeof min !== 'undefined' && val < min) temp = min;
    this.val = Number(temp);
    // this.setVal(Number(temp));
  }
  forceChangeInput(val: any) {
    const inputDom = this.$el.querySelector('.base-input-number input');
    if((inputDom as any).value !== val ) {
      (inputDom as any).value = val;
      // this.bindVal = val;
      // this.val = val;
    }
  }
  get compVal() {
    return this.currentComponent.properties?.[this.propertiesKey] ?? this.updateDataObj[this.propertiesKey]
  }

  get forceUpdateVal() {
    return (this.formItemConfigs as any).forceUpdate;
  }

  get max() {
    return (this.formItemConfigs as any).max;
  }

  get min() {
    return (this.formItemConfigs as any).min;
  }

  created() {
    if((typeof this.formItemConfigs as any).forceUpdate === 'undefined') {
      this.$set(this.formItemConfigs, 'forceUpdate', this.val);
    }
  }

  // 最大值和最小值发生变化时 如果
  @Watch("max")
  onMaxChange(val: string) {
    console.log('onMaxChange---', val, this.propertiesKey);
    if(typeof val === 'undefined') {
      return;
    }
    if(this.compVal > val) {
      this.handleChange(this.max || "");
      this.$nextTick(() => {
        this.forceChangeInput(val);
      })
    }
  }

  @Watch("min")
  onMinChange(val: string) {
    console.log('onMinChange---', val, this.propertiesKey, this.compVal);
    if(typeof val === 'undefined') {
      return;
    }
    // 最大值和最小值发生变化时
    if(this.compVal < val) {
      this.handleChange(this.min || "");
      this.$nextTick(() => {
        this.forceChangeInput(val);
      })
    }
  }

  @Watch("forceUpdateVal")
  onForceChange(val: string) {
    console.log('onForceChange---', val, this.propertiesKey);
    
    if(this.isInit) {
      this.isInit = false;
      return false;
    }
    if(typeof val === 'undefined') {
      return;
    }
    if(this.val !== val) {
      this.handleChange(val);
    }
    this.$nextTick(() => {
      this.forceChangeInput(val);
    })
  }
  @Watch("compVal")
  onCompValChange(val: string) {
    console.log('onCompValChange---', val, this.propertiesKey);
    if(typeof val === 'number' && val !== this.val) {
      this.handleChange(val);
      this.$nextTick(() => {
        this.forceChangeInput(val);
      })
    }
  }
}
</script>

<style lang="less" scoped>
.base-input-number {
  // max-width: 100px;
  /deep/ .el-input__inner {
    padding-right: 0;
  }
}
</style>
