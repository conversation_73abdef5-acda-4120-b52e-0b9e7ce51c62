
<template>
  <div class="base-color">
    <color-picker v-model="val" :clearable="true"></color-picker>
    <el-input type="text" v-model="val" style="display: none;"></el-input>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import ColorPicker from "@/components/zyb-color-picker/ColorPicker.vue";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "色板",
  value: "#00ff00",
  rule: [
    {
      required: true,
      message: "请选择XX颜色",
      trigger: "change" //  这里需要是change, 否则不会触发由空变为有颜色的校验
    },
  ]
})
@Component({ components: { ColorPicker } })
export default class BaseColor extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseColor";
}
</script>

<style lang="less" scoped>
</style>