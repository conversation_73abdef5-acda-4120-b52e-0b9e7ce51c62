<template>
  <div class="base-input">
    <el-button @click="handleClick" :size="formItemConfigs.size || 'small'" type="primary">{{ formItemConfigs.text }}</el-button>
  </div>
</template>

<script lang="ts">
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";

@OverviewDataCollection({
  label: "生成题面",
  text: "点击生成",
  value: "",
})
@Component({})
export default class BaseRandomButton extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseRandomButton";
  public handleClick() {
    console.log("click");
    const timeStamp = new Date().getTime();
    this.setVal(timeStamp);
  }
}
</script>

<style lang="less" scoped></style>
