
<template>
  <div class="special-dynamic-list">
    <div v-show="val.length === 0 && (specialDynamicList.min || specialDynamicList.max)">
      <i class="el-icon-circle-plus-outline" @click="add(0)"></i>
    </div>
    <div class="focus-config-box">
      <el-form size="small" :label-position="specialDynamicList.labelPosition">
        <el-form-item v-if="specialDynamicList.focusConfig&&specialDynamicList.focusConfig.show">
          <template slot="label">
            {{ listConfig.label }}
            <el-tooltip :content="listConfig.labelTips" placement="top" effect="light" v-if="listConfig.labelTips">
              <i class="el-icon-question"></i>
            </el-tooltip>
          </template>
          <el-pagination
            background
            layout="pager"
            :page-size="1"
            :current-page="focusIndex + 1"
            @current-change="handleCurrentChange"
            :total="val.length">
          </el-pagination>
        </el-form-item>
        <el-form-item v-if="specialDynamicList.answerConfig && specialDynamicList.answerConfig.show"
          :label="specialDynamicList.answerConfig.label">
          <el-select v-model="answerIndex"
            :placeholder="specialDynamicList.answerConfig.placeholder || '请选择'">
            <el-option v-for="(item, i) in val" :key="i"
              :label="getOrder(specialDynamicList.orderConfig, i)" :value="i">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="form-list" :class="{
        'label-left': orderLabelConfig === 'left',
        'label-top': orderLabelConfig === 'top',
        'is-bottom': specialDynamicList.operationsPosition === 'bottom',
        'is-top': specialDynamicList.operationsPosition === 'top' }" v-for="(item, index) in val"
      :key="item._id" v-show="isFormListShow(index)">
      <div v-if="specialDynamicList.orderConfig && specialDynamicList.orderConfig.show"
        class="order">
        {{ getOrder(specialDynamicList.orderConfig, index) }}
      </div>
      <div class="operations operations-top">
        <i v-if="(specialDynamicList.operations || ['add']).includes('add') && val.length < max"
          class="el-icon-circle-plus-outline" @click="add(index)"></i>
        <i v-if="(specialDynamicList.operations || ['del']).includes('del') && val.length > min"
          class="el-icon-remove-outline" @click="del(index)"></i>
      </div>
      <div class="content">
        <el-form size="small" :rules="rules(index)" :model="item" @submit.native.prevent>
          <el-row>
            <common-atom-shell
              v-for="formItem in runtimeSubFormConfigsArr[index]"
              :key="formItem.key"
              :formItemConfigs="formItem"
              :currentComponents="currentComponents"
              :updateDataObj="item"
              :baseUpdateStoreData="updateStoreData"
              :parentIndex="formIndex"
              :formIndex="index">
            </common-atom-shell>
          </el-row>
        </el-form>
      </div>
      <div class="operations">
        <i v-if="(specialDynamicList.operations || ['add']).includes('add') && val.length < max"
          class="el-icon-circle-plus-outline" @click="add(index)"></i>
        <i v-if="(specialDynamicList.operations || ['del']).includes('del') && val.length > min"
          class="el-icon-remove-outline" @click="del(index)"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import { cloneDeep, isEqual } from "lodash-es";
import { configsToRules, getCleanFormKeyAndOption, getRuntimeFormConfigs, updateConfigsProps, updateFormList, UPDATE_DATA_OBJ_DEFAULT } from "@/pages/index/common/utils/tools";
import CommonAtomShell from "./../../CommonAtomShell.vue";
import { OverviewDataCollection } from "../../OverviewCollection";

const ORDER_SEAT = "{{$}}";

@OverviewDataCollection({
  label: "动态列表",
  labelTips: "focusConfig中的key, 推荐使用subQuestionIndex",
  "focusConfig": {
      "show": false,
      "key": "subQuestionIndex"
  },
  "orderConfig": {
      "show": true,
      "labelPosition": "left"
  },
  "max": 5,
  "min": 1,
  "value": [
      {
          "isShow": true,
          "s": "2"
      }
  ],
  subFormConfigs: [
    {
      key: "isShow",
      paddingLeft: 20,
      value: true,
      formItemType: "BaseCheckbox",
      label: "显示",
      span: 8,
    },
    {
      key: "s",
      value: "1",
      formItemType: "BaseSingleSelect",
      options: [
        { label: "选项2", value: "2" },
        { label: "选项1", value: "1" },
      ],
      span: 8,
    },
  ],
})
@Component({
  components: {
    CommonAtomShell,
  },
})
export default class SpecialDynamicList extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialDynamicList";
  get specialDynamicList() {
    return this.formItemConfigs as unknown as SpecialDynamicListAbstract;
  }
  rules(index: number) {
    return configsToRules(this.specialDynamicList.subFormConfigs, {val: this.val[index]});
  }
  get subFormConfigs() {
    return this.formItemConfigs.subFormConfigs
  }
  @Watch("subFormConfigs")
  subFormConfigsChange() {
    this.$nextTick(() => {
      this.runtimeSubFormConfigsArr = [];
      const newVal = cloneDeep(this.val);
      newVal.forEach((valItem: Record<string, any>) => {
        const { changeValData, newFormConfigs } = getRuntimeFormConfigs(valItem, this.specialDynamicList.subFormConfigs);
        Object.assign(valItem, changeValData);
        this.runtimeSubFormConfigsArr.push(newFormConfigs as FormItemConfigs[]);
      });
    })
  }
  get val() {
    const val = this.getVal() || [];
    // 存在value值,但对应表单未初始化
    if (val?.length && !this.runtimeSubFormConfigsArr.length) {
      const newVal = cloneDeep(val);
      newVal.forEach((valItem: Record<string, any>) => {
        const { changeValData, newFormConfigs } = getRuntimeFormConfigs(valItem, this.specialDynamicList.subFormConfigs);
        Object.assign(valItem, changeValData);
        this.runtimeSubFormConfigsArr.push(newFormConfigs as FormItemConfigs[]);
      });
      if (!isEqual(val, newVal)) this.setVal(newVal);
    }
    if (val.length > this.runtimeSubFormConfigsArr.length) {
      // 动态列表组件外添加动态列表项
      const valItem = this.getValItem();
      const { newFormConfigs } = getRuntimeFormConfigs(valItem, this.specialDynamicList.subFormConfigs);
      for (let index = this.runtimeSubFormConfigsArr.length; index < val.length; index++) {
        this.runtimeSubFormConfigsArr.push(newFormConfigs as FormItemConfigs[]);
      }
    }
    return val.map((item: Record<string, any>, index: number) => {
      // 是否存在内置id
      if (item._id) {
        return item;
      } else {
        return {
          _id: `${+new Date()}-${index}`,
          ...item,
        };
      }
    });
  }
  set val(val) {
    this.setVal(val);
  }

  get focusIndex() {
    return this.getSelectedIndex("focusConfig");
  }
  set focusIndex(val) {
    this.setSelectedIndex(val, "focusConfig");
  }
  get answerIndex() {
    return this.getSelectedIndex("answerConfig");
  }
  set answerIndex(val) {
    this.setSelectedIndex(val, "answerConfig");
  }
  get listConfig() {
    return (
      this.specialDynamicList?.listConfig || {
        label: "小题",
        labelTips: "点击小题的题号，切换不同小题的配置",
      }
    );
  }
  isFormListShow(i: number) {
    if (this.specialDynamicList?.focusConfig?.show) {
      return i === this.focusIndex;
    } else {
      return true;
    }
  }
  created() {
    const focusConfig = this.specialDynamicList?.focusConfig;
    if(focusConfig && focusConfig.show) {
      const canUseKeys: string[] = ['stu_chosenQuestionId', 'stuFocus', 'subQuestionIndex']
      if(focusConfig.key && !canUseKeys.includes(focusConfig.key)) {
        this.$message.error(`focusConfig中的字段key不可以为${focusConfig.key}，请使用stuFocus 或 subQuestionIndex`)
      }
      if(!focusConfig.key) {
        focusConfig.key = 'subQuestionIndex'
        this.$store.commit("updateComponentProperties", {
          id: this.currentComponent?.id,
          newProperties: {
            [focusConfig.key]: 0
          },
        });
      }
    }
  }
  getSelectedIndex(type: string) {
    const key = this.specialDynamicList[type]?.key;
    if (!key) return 0;
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      return this.currentComponent?.properties?.[key];
    } else {
      return this.updateDataObj[key];
    }
  }
  setSelectedIndex(val: number, type: string) {
    const key = this.specialDynamicList[type]?.key;
    if (!key) return;
    if (this.updateDataObj === UPDATE_DATA_OBJ_DEFAULT) {
      if (!this.currentComponent) return;
      this.$store.commit("updateComponentProperties", {
        id: this.currentComponent?.id,
        newProperties: {
          [key]: val,
        },
      });
    } else {
      this.baseUpdateStoreData({ key, value: val });
    }
  }
  get min() {
    return this.specialDynamicList.min || 0;
  }
  get max() {
    return this.specialDynamicList.max || Infinity;
  }

  @Watch("max")
  onMaxChange(val: number) {
    if(typeof val === 'undefined') {
      return;
    }
    if(this.val.length > val) {
      this.del(val, this.val.length - val)
    }
  }

  get decorateArrays() {
    const { orderConfig } = this.specialDynamicList;
    if (orderConfig && orderConfig?.type === "function") {
      console.log("orderConfig", [...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || ""]);
      const decorateFunc = new Function(...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || "");
      // eslint-disable-next-line prefer-spread
      return decorateFunc.apply(null, Object.values(orderConfig?.args || {}));
    }
    return [];
  }

  get orderLabelConfig() {
    return this.specialDynamicList.orderConfig?.labelPosition || 'left'
  }

  runtimeSubFormConfigsArr: Array<FormItemConfigs[]> = [];
  // 转换列表项序号
  getOrder(orderConfig: OrderConfig | undefined, index: number) {
    if (!orderConfig) return index + 1;
    if (orderConfig?.type === "letter") {
      const letter = String.fromCharCode(65 + index);
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, letter) : letter;
    } else if (orderConfig?.type === "function") {
      return this.decorateArrays[index];
    } else {
      const i = (index + 1).toString();
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, i) : i;
    }
    //
  }
  // 子原子组件数据更新触发方法
  updateStoreData({ index, key, value }: { index: number; key: string; value: any }) {
    const val = cloneDeep(this.val);
    const oldRuntimeSubFormConfigs = this.runtimeSubFormConfigsArr[index] || this.specialDynamicList.subFormConfigs;
    const formItemConfigs = oldRuntimeSubFormConfigs.find(item => item.key === key);
    // 是否存在关联属性options
    if (!formItemConfigs?.options?.length) {
      // 值有变化则set
      if (!isEqual(val[index][key], value)) {
        val[index][key] = value;
        this.setVal(val);
      }
      return;
    }
    let newFormSchema: FormItemConfigs[] = oldRuntimeSubFormConfigs;
    // 只要存在options,就需要遍历,避免当前项没有option,其他项有,这时需要删除其他项optin表单
    const { cleanFormKey, option } = getCleanFormKeyAndOption(formItemConfigs?.options, value);
    // 存在需要清除的表单key,则更新formSchema
    if (cleanFormKey.length) {
      newFormSchema = updateFormList(oldRuntimeSubFormConfigs, cleanFormKey, [option], [key]) as FormItemConfigs[];
    }
    const resData = updateConfigsProps({ key, value }, newFormSchema);
    // 存在更新其他表单属性
    if (resData) {
      const { changeValData, newFormConfigs } = resData;
      newFormSchema = newFormConfigs;
      // 待属性赋值生效后,再进行value赋值
      Object.keys(changeValData).forEach(key => {
        val[index][key] = changeValData[key];
      });
    }
    // 初始值未改变,则只setVal
    if (isEqual(newFormSchema,oldRuntimeSubFormConfigs)) {
      // 值有变化则set
      if (!isEqual(val[index][key], value)) {
        val[index][key] = value;
        this.setVal(val);
      }
    } else {
      this.runtimeSubFormConfigsArr[index] = newFormSchema;
      val[index][key] = value;
      this.$nextTick(() => {
        // 待属性赋值生效后,再进行value赋值
        if (!isEqual(this.val, val)) this.setVal(val);
      });
    }
  }
  // 获取子项
  getValItem() {
    const valItem: Record<string, any> = {};
    // 生成默认值对象
    this.specialDynamicList.subFormConfigs.forEach(item => {
      // 未设置默认值则默认值取空字符串,避免表单数据绑定失败
      valItem[item.key] = cloneDeep("value" in item ? item.value : "");
      if (item.options?.length) {
        item.options.forEach(option => {
          option?.associatedForm?.forEach(formItem => {
            // 占位符不新增属性
            if (formItem.formItemType === "BaseStaticText") return;
            valItem[formItem.key] = cloneDeep("value" in formItem ? formItem.value : "");
          });
        });
      }
    });
    return valItem;
  }
  // 添加子项
  add(index: number) {
    const val = [...this.val];
    const valItem = this.getValItem();
    // 获取运行时表单
    const { changeValData, newFormConfigs } = getRuntimeFormConfigs(valItem, this.specialDynamicList.subFormConfigs);
    Object.assign(valItem, changeValData, { _id: +new Date() });
    val.splice(index + 1, 0, valItem);
    this.runtimeSubFormConfigsArr.splice(index + 1, 0, newFormConfigs as FormItemConfigs[]);
    this.setVal(val);
    this.focusIndex = index + 1;
  }
  // 删除子项
  del(index: number, count = 1) {
    const val = [...this.val];
    val.splice(index, count);
    this.runtimeSubFormConfigsArr.splice(index, count);
    this.setVal(val);
    // 找当前 找不到的话找最后一个
    this.focusIndex = val.length === 0 ? "" : Math.min(this.focusIndex, val.length - 1)
    // if (index === this.focusIndex) {
    //   this.focusIndex = val.length === 0 ? "" : 0;
    // } else if (index < this.focusIndex) {
    //   this.focusIndex = this.focusIndex - 1;
    // }
    if (index <= this.answerIndex) {
      this.answerIndex = "";
    }
  }

  handleCurrentChange(page: number) {
    this.focusIndex = page - 1;
  }
}
</script>

<style lang="less" scoped>
.special-dynamic-list {
  /deep/ .el-radio-button--small .el-radio-button__inner {
    padding: 7px 10px;
  }
}
.form-list {
  display: flex;
  flex-flow: nowrap;
  position: relative;
  &.label-top {
    display: block;
  }
  .operations-top {
    display: none;
  }
  &.is-bottom {
    flex-flow: wrap;
    .operations {
      width: 100%;
      text-align: right;
      padding-left: 0;
      padding-right: 10px;
    }
  }
  &.is-top {
    flex-flow: wrap;
    .operations {
      display: none;
    }
    .operations-top {
      display: block;
      width: auto;
      position: absolute;
      z-index: 10;
      top: 0px;
      right: 0px;
      padding-right: 0px;
    }
  }
}
.order {
  flex: none;
  padding: 0 5px 0 10px;
}
.content {
  flex: auto;
}
.el-icon-circle-plus-outline,
.el-icon-remove-outline {
  font-size: 18px;
  line-height: 28px;
  cursor: pointer;
}
.operations {
  box-sizing: border-box;
  flex: none;
  padding-left: 5px;
}
.form-list {
  margin-bottom: 10px;
  border-bottom: 1px dashed #ccc;
}
.focus-config-box {
  box-sizing: border-box;
}
</style>

<style lang="less">
.focus-config-box {
  .el-pager {
    li {
      border: 1px solid #e1e2e6 !important;
      border-left: none !important;
      border-radius: 0 !important;
      margin: 0 !important;
      background-color: #fff !important;
    }
    li:first-child {
      border-left: 1px solid #e1e2e6 !important;
      border-radius: 2px 0 0 2px !important;
      box-shadow: none !important;
    }
    li:last-child {
      border-left: 1px solid #e1e2e6 !important;
      border-radius: 0 2px 2px 0 !important;
      box-shadow: none !important;
    }
    li.active {
      background-color: #42c57a !important;
      color: #fff !important;
      &:not(.disabled):hover {
        color: #fff !important;
      }
    }
    li:not(.active):hover, li:not(.disabled):hover {
      color: #333 !important;
    }
  }
}
</style>