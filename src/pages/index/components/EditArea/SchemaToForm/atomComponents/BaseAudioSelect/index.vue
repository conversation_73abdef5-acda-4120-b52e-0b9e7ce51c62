
<template>
  <div :class="`${cssPrefix}`">
    <AudioSelect
      :value.sync="val"
      label=""
      :property="formItemConfigs.key"
      defaultValue=""
    />
    <el-input v-model="val" style="display: none;" />
  </div>
</template>
<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "音频选择",
  value: ""
})
@Component({ components: { AudioSelect } })
export default class BaseAudioSelect extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseAudioSelect";
  cssPrefix = "base-audio-select";
}
</script>

<style lang="less">
@CSS_PREFIX: base-audio-select;
@WIDTH: 160px;
// .@{CSS_PREFIX} {}
</style>
