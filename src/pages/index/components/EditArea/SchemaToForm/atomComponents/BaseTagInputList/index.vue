<template>
  <div class="base-tag-table">
    <div class="table-row inner-row" :class="{ first: index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :style="cellStyle" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="base-tag cell-div">
          <!-- <el-tag :color="val[index][sIndex][activeKey] === 1 ? formItemConfigs['active'].color : formItemConfigs.color" v-bind="formItemConfigs" @click="handleClick(index, sIndex)" @dblclick.native="handleDblclick(index, sIndex)">
            <el-input v-if="val[index][sIndex]._show" readonly="true"/>
          </el-tag> -->
          <el-input
            :ref="`${index}${sIndex}inp`"
            :style="{ 'background-color': val[index][sIndex][activeKey] === 1 ? formItemConfigs['active'].color : formItemConfigs.color }"
            v-bind="formItemConfigs"
            :readonly="val[index][sIndex]._show ? false : true"
            @click.native="handleClick(index, sIndex)"
            @dblclick.native="handleDblclick(index, sIndex)"
            @blur="handleBlur(index, sIndex)"
            @input="
              $event => {
                validate($event, index, sIndex);
              }
            "
            :value="val[index][sIndex][textKey]"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep, debounce, isEqual } from "lodash-es";

@OverviewDataCollection({
  label: "标签列表",
  labelPosition: "top",
  color: "#909399",
  active: {
    color: "#ffffff",
  },
  validation: "^[0-4]{0,1}$",
  activeKey: "active",
  textKey: "count",
  col: 3,
  row: 3,
  span: 8,
  width: "60px",
  height: "60px",
  value: [
    [{ active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }],
    [{ active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }],
    [{ active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }],
    [{ active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }, { active: 0 }]
  ], //  0-color  1-active.color
})
@Component({})
export default class BaseTagInputList extends BaseClassForm<BaseTagListType> {
  static componentName = "BaseTagInputList";

  get activeKey() {
    return this.formItemConfigs["activeKey"] || "active";
  }
  // textKey "" 或undefined都表示没有值
  get textKey() {
    return this.formItemConfigs["textKey"] || "value";
  }


  get row() {
    return this.currentComponents[0]["properties"][this.formItemConfigs.rowKey] || this.formItemConfigs.row;
  }

  get col() {
    return this.currentComponents[0]["properties"][this.formItemConfigs.colKey] || this.formItemConfigs.col;
  }

  get rowcol() {
    return `${this.row}*${this.col}`
  }

  get size() {
    return {
      row: this.row,
      col: this.col
    }
  }

  @Watch("size", { deep: true })
  onSizeChange(newVal: any, oldVal: any) {
    if(isEqual(newVal, oldVal)) return;
    // row col 变更时 重置数据
    const val: any[][] = [];
    for(let i = 0; i < this.row; i++) {
      const temp: any[] = [];
      for(let j = 0; j < this.col; j++) {
        temp.push({
          [this.textKey]: '',
          [this.activeKey]: 0
        });
      }
      val.push(temp);
    }
    this.setVal(val);
  }

  get cellStyle() {
    return {
      maxWidth: this.formItemConfigs.width,
      height: this.formItemConfigs.height,
    };
  }
  

  handleClick(index: number, subIndex: number) {
    // console.log("xu1-click", index, subIndex);
    // 如果没有dblclick 就改颜色
    debounce(() => {
      if (this.val[index][subIndex]._show) {
        return;
      }
      // console.log("xu1-handleclick", index, subIndex);
      const valClone = cloneDeep(this.val);
      valClone[index][subIndex][this.activeKey] = valClone[index][subIndex][this.activeKey] === 1 ? 0 : 1;
      this.setVal(valClone);
      this.toggleShow(index, subIndex, 0);
    }, 200)();
    // 单击 显示input&改颜色 显示input时 再点击 聚焦 聚焦的时候 把active改正原来的
  }

  toggleShow(index: number, subIndex: number, val: 0 | 1) {
    if(val) {
      this.$set(this.val[index][subIndex], "_show", val);
    } else {
      this.$delete(this.val[index][subIndex], "_show")
    }
  }

  handleDblclick(index: number, subIndex: number) {
    console.log("xu2-handleDblclick", index, subIndex);
    // 聚焦
    this.toggleShow(index, subIndex, 1);
    // 清空内容 如果不清空 双击会选中文本
    this.$set(this.val[index][subIndex], this.textKey, "");
  }

  handleBlur(index: number, subIndex: number) {
    console.log("xu2-handleBlur", index, subIndex);
    this.toggleShow(index, subIndex, 0);
    const valClone = cloneDeep(this.val);
    this.setVal(valClone);
  }

  validate(val: any, index: number, subIndex: number) {
    const { validation } = this.formItemConfigs;
    const valClone = cloneDeep(this.val);
    let temp = valClone[index][subIndex][this.textKey];
    if (validation) {
      const reg = new RegExp(validation as string);
      if (reg.test(val as string)) {
        temp = val;
      }
    }
    valClone[index][subIndex][this.textKey] = temp;
    this.setVal(valClone);
  }

}
</script>

<style lang="less" scoped>
.base-tag-table {
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
    overflow: hidden;
    width: 100%;
    /deep/ .el-tag {
      width: 100%;
      height: 100%;
      border-color: #e9e9eb;
      padding: 0 !important;
    }
    /deep/ .el-input {
      border: 0px !important;
      width: 100%;
      height: 100%;
      input {
        border-color: #e1e2e6 !important;
        width: 100%;
        height: 100%;
        // 居中
        padding: 15px;
        background: transparent;
      }
    }
  }
  .table-col {
    box-sizing: border-box;
    position: relative;
    flex: 1;
  }
}
</style>
