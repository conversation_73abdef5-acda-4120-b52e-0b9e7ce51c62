<template>
  <div class="base-static-text" :style="contentStyle">
    <pre class="pre" :style="labelStyle">{{ val || formConfig.text }}</pre>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "占位符",
  text: "大标题",
})
@Component({})
export default class BaseStaticText extends BaseClassForm<BaseStaticTextType> {
  static componentName = "BaseStaticText";

  get contentStyle() {
    return {
      display: "flex",
      justifyContent: this.formConfig.align === "center" ? "center" : this.formConfig.align === "right" ? "flex-end" : "flex-start",
    };
  }

  get labelStyle() {
    return {
      fontSize: `${this.formConfig.fontSize || 14}px`,
      fontWeight: `${this.formConfig.bold ? 500 : 400}`,
      color: this.formConfig.color,
      whiteSpace: this.formConfig.whiteSpace || "break-spaces",
    };
  }
}
</script>

<style lang="less" scoped>
.base-static-text {
  width: 100%;
}
</style>
