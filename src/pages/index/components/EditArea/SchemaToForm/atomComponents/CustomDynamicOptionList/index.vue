<template>
  <div class="custom-dynamic-option-config">
    <el-form size="small" :label-position="customOptionConfig.labelPosition || 'left'">
      <el-form-item v-if="isShowType" label="选项类型">
        <el-radio-group v-model="optionType">
          <el-radio label="text">文本</el-radio>
          <el-radio label="img">图片</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div>
      <div class="option-list-title">
        {{ listConfig.label }}
        <el-tooltip :content="listConfig.labelTips" placement="top" effect="light" v-if="listConfig.labelTips">
          <i class="el-icon-question"></i>
        </el-tooltip>
      </div>
      <div v-if="options.length" class="option-list-box">
        <div v-for="(item, index) in options" :key="index">
          <div class="option-item-title" :class="{ 'has-checkbox': answerConfig.formItemType === 'checkbox' }">
            <el-checkbox class="answer-checkbox" v-model="item[answerConfig.key]" v-if="answerConfig.formItemType === 'checkbox'"></el-checkbox>
            <el-form-item :label="getOrder(orderConfig, index)"></el-form-item>
            <div class="option-box-operations">
              <i v-if="options.length < max" class="el-icon-circle-plus-outline" @click="add(index)"></i>
              <i v-if="options.length > min" class="el-icon-remove-outline" @click="del(index)"></i>
            </div>
          </div>
          <el-form size="small" label-position="left" ref="ruleForm" :model="item" :rules="rules">
            <div v-for="formItem in customOptionConfig.optionConfigs" :key="formItem.key">
              <el-form-item :label="formItem.label" v-if="formItem.formItemType === 'BaseRadioGroup'" :prop="formItem.key">
                <el-radio-group class="base-radio-group" v-model="item[formItem.key]" v-bind="formItem">
                  <el-row class="base-radio-row">
                    <el-col class="base-radio-col" v-for="radioConfig in formItem.options" :key="radioConfig.label" :span="radioConfig.span" :offset="radioConfig.offset || 0">
                      <el-radio v-bind="radioConfig" :label="radioConfig.value">{{ radioConfig.label }}</el-radio>
                    </el-col>
                  </el-row>
                </el-radio-group>
              </el-form-item>
              <el-form-item :label="formItem.label" v-if="formItem.formItemType === 'BaseSingleSelect'" :prop="formItem.key" :required="formItem.rule && formItem.rule[0] && formItem.rule[0].required" :rules="formItem.rule || []">
                <SubSingleSelect 
                  :formItemConfigs="formItem"
                  :formItemValue="item"
                  :currentComponents="currentComponents"
                  :parentIndex="formIndex"
                />
              </el-form-item>
              <el-form-item :label="formItem.label" v-if="formItem.formItemType === 'optionItem'" :prop="optionType === 'text' ? formItem.textKey : formItem.imgKey">
                <div class="option-box">
                  <div class="option-box-input" v-if="optionType === 'text'">
                    <el-input placeholder="请输入内容" v-bind="formItem" :maxlength="textMaxLength" :value="item[formItem.textKey]" @input="validate($event, item[formItem.textKey],formItem, item)"> </el-input>
                  </div>
                  <div class="option-box-input" v-if="optionType === 'img'">
                    <ImageSelect :src.sync="item[formItem.imgKey]" :is-show-delete-btn="false" :max-width="stageSafeWidth" :max-height="stageSafeHeight" class="custom-option-image" />
                  </div>
                </div>
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>
      <i v-else class="el-icon-circle-plus-outline" @click="add(0)"></i>
    </div>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";
import { OverviewDataCollection } from "../../OverviewCollection";
import { cloneDeep, debounce, isEqual } from "lodash-es";
import store from "@/pages/index/store";
import { DEBOUNCE_TIME } from "@/pages/index/common/utils/tools";
import CommonAtomShell from "./../../CommonAtomShell.vue";
// import SubSingleSelect from "./SubSingleSelect.vue";
import SubSingleSelect from './SubSingleSelect/index.vue';
import { ElForm } from "element-ui/types/form";

interface CustomOptionListAbstract extends SpecialOptionConfigAbstract {
  optionLength: number;
  listConfig?: {
    label: string;
    labelTips: string;
  };
  orderConfig?: OrderConfig;
  optionConfigs: FormItemConfigs[];
  answerConfig: {
    show: boolean;
    mutiple?: boolean;
    formItemType: string;
    key: string;
  };
}

interface CustomOptionListValue {
  options: OptionsItem[];
  type: string;
}

type OptionsItem = {
  [x: string]: any;
  text: string;
  imgUrl: string;
};

const ORDER_SEAT = "{{$}}";

@OverviewDataCollection({
  label: "选项列表",
  labelTips: "支持radio/input/image",
  formItemType: "CustomDynamicOptionList",
  listConfig: {
    label: "选项列表",
    labelTips: "laaa"
  },
  rule: [
    {
      required: true,
      trigger: "blur",
    },
  ],
  answerConfig: {
    show: true,
    formItemType: "checkbox",
    key: "isCorrect",
  },
  textConfig: {
    maxLength: 6,
    validation: "^.{1,6}$",
    validationMessage: "请输入1～6个字",
  },
  optionConfigs: [
    {
      formItemType: "BaseRadioGroup",
      key: "mouseType",
      label: "目标类型",
      rule: [
        {
          required: true,
          trigger: "blur",
        },
      ],
      options: [
        {
          label: "普通鼠",
          value: 1,
          span: 6,
        },
        {
          label: "闪电鼠",
          value: 2,
          offset: 2,
          span: 6,
        },
        {
          label: "头盔鼠",
          value: 3,
          offset: 2,
          span: 6,
        },
      ],
    },
    {
      formItemType: "optionItem",
      key: "text",
      textKey: "text",
      imgKey: "imgUrl",
      label: "选项",
      relativeTypeKey: "type",
      rule: [
        {
          required: true,
          trigger: "blur",
          message: "选项不能为空",
        },
      ],
    },
  ],
  max: 6,
  min: 2,
  value: {
    type: "text",
    options: [
      {
        text: "",
        imgUrl: "",
        mouseType: "",
        isCorrect: false,
      },
      {
        text: "",
        imgUrl: "",
        mouseType: "",
        isCorrect: false,
      },
    ],
  },
})

@OverviewDataCollection({
  label: "绕过文本为空校验",
  labelTips: "绕过input/image的校验，需要在rule中设置required=false绕过校验",
  formItemType: "CustomDynamicOptionList",
  listConfig: {
    label: "绕过文本为空校验",
    labelTips: "绕过input/image的校验，需要在rule中设置required=false绕过校验"
  },
  rule: [
    {
      required: true,
      trigger: "blur",
    },
  ],
  answerConfig: {
    show: true,
    formItemType: "checkbox",
    key: "isCorrect",
  },
  textConfig: {
    maxLength: 6,
    validation: "^.{1,6}$",
    validationMessage: "请输入1～6个字",
  },
  optionConfigs: [
    {
      formItemType: "optionItem",
      key: "text",
      textKey: "text",
      imgKey: "imgUrl",
      label: "选项",
      relativeTypeKey: "type",
      rule: [
        {
          required: false
        },
      ],
    },
  ],
  max: 6,
  min: 2,
  value: {
    type: "text",
    options: [
      {
        text: "",
        imgUrl: "",
        isCorrect: false,
      },
      {
        text: "",
        imgUrl: "",
        isCorrect: false,
      },
    ],
  },
})

@Component({ components: { ImageSelect, CommonAtomShell, SubSingleSelect } })
export default class CustomDynamicOptionList extends BaseClassForm<Record<string, any>> {
  static componentName = "CustomDynamicOptionList";
  created() {
    if (this.val?.options?.length) {
      this.options = cloneDeep(this.val.options);
      const defaultValue = this.formItemConfigs.value as CustomOptionListValue;
      this.optionsItem = cloneDeep(defaultValue?.options || [])[0] || {
        text: "",
        imgUrl: "",
      };
    }
  }
  get stageSafeWidth() {
    return store.state.stageData.safeWidth;
  }
  get stageSafeHeight() {
    return store.state.stageData.safeHeight;
  }
  get textMaxLength() {
    return this.customOptionConfig?.textConfig?.maxLength || Infinity;
  }

  get ruleForm() {
    if (!this.customOptionConfig?.textConfig?.validation) return null;
    return this.options.reduce((obj: any, item: any, index: number) => {
      obj[`input${index}`] = item;
      return obj;
    }, {});
  }

  get rules() {
    const rules = {};
    this.customOptionConfig.optionConfigs.forEach(optionConfig => {
      if (optionConfig.rule) {
        if (optionConfig.relativeTypeKey === "type") {
          rules[optionConfig.textKey] = optionConfig.rule;
          rules[optionConfig.imgKey] = optionConfig.rule;
        } else {
          rules[optionConfig.key] = optionConfig.rule;
        }
      }
    });

    return rules;
  }

  get isShowType() {
    const typeConfig = this.customOptionConfig.typeConfig;
    if (!typeConfig) return true;
    if (!typeConfig.show) {
      // 不显示类型配置表单时,需保证当前value的类型与配置一致
      const typeVal = typeConfig.type || "text";
      if (typeVal !== this.val?.type) {
        const cloneVal = cloneDeep(this.val) || {};
        this.setVal({
          options: [], // 选项列表
          ...cloneVal,
          type: typeVal,
        });
      }
    }
    return typeConfig.show;
  }

  get customOptionConfig() {
    return (this.formItemConfigs as unknown) as CustomOptionListAbstract;
  }
  get optionType() {
    return this.val?.type;
  }
  set optionType(type) {
    if (type === this.val?.type) return;
    const cloneVal = cloneDeep(this.val) || {};
    cloneVal.type = type;
    this.options =
      cloneVal.options?.map((opt: any) => {
        // merge
        const optionItem = opt;
        const refreshKeys: string[] = [];
        this.formItemConfigs.optionConfigs.forEach((item: { relativeTypeKey: any; textKey: string; imgKey: string }) => {
          if (item.relativeTypeKey) {
            refreshKeys.push(item.textKey);
            refreshKeys.push(item.imgKey);
          }
        });
        const defaultOptionItem = cloneDeep(this.optionsItem);
        const refreshObjects = {};
        refreshKeys.forEach((key: string) => {
          refreshObjects[key] = defaultOptionItem[key];
        });
        return {
          ...optionItem,
          ...refreshObjects,
        };
      }) || [];
    cloneVal.options = cloneDeep(this.options);
    // 遍历Formconfig
    this.setVal(cloneVal);
    this.$nextTick(() => {
      if(this.$refs.ruleForm && (this.$refs.ruleForm as any).length){
        (this.$refs.ruleForm as any).forEach((form: ElForm) => {
          form.clearValidate();
        });
      } else {
        (this.$refs.ruleForm as ElForm)?.clearValidate();
      }
    });
  }
  optionsItem: OptionsItem = { text: "", imgUrl: "" };
  options: Array<OptionsItem> = [];
  @Watch("options", { deep: true })
  watchOptions() {
    this.debounceFun(this.val, this.options);
  }
  // 防抖函数
  debounceFun = debounce((val, options) => {
    let cloneVal = cloneDeep(val);
    if (isEqual(cloneVal?.options, options)) return;
    if (cloneVal) {
      cloneVal.options = cloneDeep(options);
    } else {
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: [], // 选项列表
      };
    }
    this.setVal(cloneVal);
  }, DEBOUNCE_TIME);
  get min() {
    return this.customOptionConfig.min || 0;
  }
  get max() {
    return this.customOptionConfig.max || Infinity;
  }
  get listConfig() {
    return (
      this.customOptionConfig.listConfig || {
        label: "选项列表",
        labelTips: "",
      }
    );
  }
  get answerConfig() {
    return (
      this.customOptionConfig.answerConfig || {
        show: true,
        lable: "正确答案",
        formItemType: "select",
      }
    );
  }
  add(index: number) {
    let cloneVal = cloneDeep(this.val);
    if (cloneVal) {
      this.options.splice(index + 1, 0, cloneDeep(this.optionsItem));
      cloneVal.options = cloneDeep(this.options);
    } else {
      this.options = [cloneDeep(this.optionsItem)];
      cloneVal = {
        type: "text", // text:文本, img:图片
        options: cloneDeep(this.options), // 选项列表
      };
    }
    this.setVal(cloneVal);
  }
  // 删除子项
  del(index: number) {
    const cloneVal = cloneDeep(this.val);
    this.options.splice(index, 1);
    cloneVal.options = cloneDeep(this.options);
    this.setVal(cloneVal);
  }
  get orderConfig() {
    return (
      this.customOptionConfig.orderConfig || {
        show: true,
        decorate: "选项{{$}}",
      }
    );
  }

  get decorateArrays() {
    const { orderConfig } = this;
    if (orderConfig && orderConfig?.type === "function") {
      console.log("orderConfig", [...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || ""]);
      const decorateFunc = new Function(...Object.keys(orderConfig?.args || {}), orderConfig?.decorate || "");
      // eslint-disable-next-line prefer-spread
      return decorateFunc.apply(null, Object.values(orderConfig?.args || {}));
    }
    return [];
  }

  getOrder(orderConfig: OrderConfig | undefined, index: number) {
    if (!orderConfig) return index + 1;
    if (orderConfig?.type === "letter") {
      const letter = String.fromCharCode(65 + index);
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, letter) : letter;
    } else if (orderConfig?.type === "function") {
      return this.decorateArrays[index];
    } else {
      const i = (index + 1).toString();
      return orderConfig?.decorate ? orderConfig.decorate.replace(ORDER_SEAT, i) : i;
    }
  }

  validate(val: string, oldVal: string, formItem: any, item: any) {
    const { validation } = formItem;

    let temp = oldVal;
    if (validation) {
      const reg = new RegExp(validation as string);
      if(reg.test(val as string)) {
        temp = val;
      }
    } else {
      temp = val;
    }
    item[formItem.textKey] = temp;
  }
}
</script>

<style lang="less" scoped>
.option-list-title {
  line-height: 36px;
  font-size: 12px;
  color: #777;
}
.option-list-box {
  padding-left: 5px;
}
.el-icon-circle-plus-outline,
.el-icon-remove-outline {
  cursor: pointer;
}
.option-box {
  display: flex;
  .option-box-input {
    width: 100%;
    flex: auto;
    .image-select,
    .el-input {
      margin: 5px 0;
    }
  }
  .option-box-operations {
    text-align: right;
    padding-left: 5px;
    width: 35px;
    flex: none;
  }
}
</style>

<style lang="less">
@CSS_PREFIX: custom-dynamic-option-config;
@WIDTH: 120px;
.@{CSS_PREFIX} {
  .image-select {
    width: @WIDTH;
    min-height: 32px;
    max-height: 200px;
  }
  .edit-btn-container {
    width: @WIDTH !important;
  }
  .image-container {
    width: @WIDTH !important;
  }
  .option-item-title {
    display: flex;
    justify-content: space-between;
    &.has-checkbox {
      position: relative;
      padding-left: 12px;
      .el-checkbox {
        position: absolute;
        left: -12px;
        top: 6px;
      }
      .answer-checkbox {
        position: absolute;
        left: -12px;
        top: 6px;
      }
    }
  }
  /deep/ .el-form-item__label {
    line-height: 38px;
  }
  /deep/ .el-form-item {
    display: flex;
    align-items: center;
  }
}
</style>
