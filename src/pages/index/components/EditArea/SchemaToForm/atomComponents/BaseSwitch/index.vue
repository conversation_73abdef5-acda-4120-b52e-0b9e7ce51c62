<template>
  <div class="base-switch">
    <el-switch v-model="val" v-bind="formConfig" />
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "开关",
  value: false,
})
@Component({})
export default class BaseSwitch extends BaseClassForm<BaseSwitchType> {
  static componentName = "BaseSwitch";
}
</script>

<style lang="less" scoped></style>
