<template>
  <div class="special-treebrjdge-table">
    <div class="table-row inner-row" :class="{ 'first': index === 0 }" v-for="(item, index) in row" :key="item">
      <div class="table-col inner-item" :class="{ 'subfirst': sIndex === 0 }" v-for="(sitem, sIndex) in col" :key="`${item}-${sitem}`">
        <div class="cell-div">
          <el-input-number
            :min="1"
            :max="formConfig.max"
            :step="1"
            type="number"
            size="mini"
            controls-position="right"
            :value="val && val[index] && val[index][sIndex] ? val[index][sIndex] : undefined"
            @change="(val) => {handleChange(val, index, sIndex)}"
            :controls="false"
          ></el-input-number>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import cloneDeep from "lodash-es/cloneDeep";

@OverviewDataCollection({
  label: "树桥",
  labelTips: '',
  value: [
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined],
    [undefined,undefined,undefined,undefined,undefined]
  ],
  rowKey: 'row',
  colKey: 'col',
  row: 6,
  col: 6,
  min: 1,
  max: 8,
  desc: `
    <div>rowKey: 关联的组件属性的行的key;</div>
    <div>colKey: 关联的组件属性的列的key;</div>
    <div>min: 数字的最小值;</div>
    <div>max： 数字的最大值;</div>
    <div>空值为undefined或null, null是因为数据初始化时，模板数据只能是null不能是undefined。 undefined是因为el组件中，只有值为undefined 表单中才会显示空，否则会显示最小值"</div>
  `
})

@Component
export default class SpecialTreeBridgeTable extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialTreeBridgeTable";
  init = false;
  get row() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.rowKey] || this.formItemConfigs.row
  }
  
  get col() {
    return this.currentComponents[0]['properties'][this.formItemConfigs.colKey] || this.formItemConfigs.col
  }
  
  public handleClick() {
    const timeStamp = new Date().getTime();
    this.setVal(timeStamp);
  }

  @Watch("val", { deep: true })
  onValChange() {
    if(this.init) return;
    // 题板规格变更时 不更新数据
    // 判断二维数组为空
    let isRefreshBlankData = true;
    [...this.val].forEach((item) => {
      item.forEach((subItem: any) => {
        if(subItem) isRefreshBlankData = false;
      })
    })
    if(!isRefreshBlankData) return;
    this.setVal(this.val);
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  created() {
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }

  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex] = params;
    const valClone = cloneDeep(this.val);
    valClone[index][subIndex] = value;
    this.setVal(valClone);
  }
}
</script>

<style lang="less" scoped>
.special-treebrjdge-table {
  box-sizing: border-box;
  margin-top: 20px;
  margin-left: 20px;
  .table-row {
    display: flex;
    .table-col:last-child {
      border-width: 0px;
        width: 0px;

    }
    &:last-child {
      .table-col {
        border-width: 0px;
      }
    }
  }
  .cell-div {
    width: 100%;
    height: 100%;
    border: 1px dashed #bbb;
    height: 26px;
    line-height: 26px;
    width: 26px;
    border-radius: 50%;
    overflow: hidden;
    position: absolute;
    transform: translate(-50%, -50%);
  }
  .table-col {
    width: 50px;
    border-bottom: 1px dashed #bbb;
    border-right: 1px dashed #bbb;
    box-sizing: border-box;
    position: relative;
    height: 40px;
  }
  // 第一个inner-row中的 inner-item
  .first .inner-item {
    border-top: 1px dashed #bbb;
  }
  // 第一行的第一个单元格
  // .inner-row:first-child .inner-item {
  //   border-top: 1px solid #bbb;
  // }
  // 每一行的第一个单元格
  .inner-item:first-child {
    border-left: 1px dashed #bbb;
  }
  .subfirst {
    border-left: 1px dashed #bbb;
  }
  
  /deep/ .el-input-number--mini {
    width: 100%;
    display: block;
    height: 100%;
    line-height: 30px;
  }
  /deep/ .el-input--mini {
    height: 100%;
  }
  /deep/ .el-input input {
    padding: 0;
    border: 0px;
    height: 100%;
  }
  /deep/ input {
    position: absolute;
  }
}
</style>
