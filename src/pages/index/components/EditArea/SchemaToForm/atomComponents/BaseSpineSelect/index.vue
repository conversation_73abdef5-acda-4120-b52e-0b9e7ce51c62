<template>
  <div class="spine-wrapper" :class="`${cssPrefix}`">
    <div class="no-spine" v-if="!(val && val.skeleton)">
      <el-button size="small" type="primary" class="upload-handler" @click="handleSelect">添加动效 </el-button>
    </div>
    <div class="has-spine" v-if="val && val.cover">
      <div class="spine-cover"><img :src="val.cover" /></div>
      <!-- <i class="el-icon-error" @click="handleDelete()"></i> -->
      <div class="button-wrapper">
        <el-button size="small" type="primary" @click="handleSelect">替换</el-button>
        <el-button size="small" type="danger" v-if="clearable" @click="handleDelete()">删除</el-button>
      </div>
    </div>
    <div class="is-required" v-if="val && val.skeleton">
      <div class="animation-list" :style="{ marginLeft: `-${animationOffset - 15}px` }">
        <div class="animation-item" v-for="key in animationKeys" :key="key.value">
          <label class="el-form-item__label">{{ key.label }}</label>
          <el-select
            size="small"
            placeholder=""
            :value="val[key.value]"
            @change="
              val => {
                handleChange(key.value, val);
              }
            "
          >
            <el-option v-for="item in animationOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";

@OverviewDataCollection({
  label: "动效组件",
  hasDefaultOption: true,
  value: {
    atlas: "",
    images: [],
    skeleton: "",
    cover: "",
  },
  rule: [
    {
      required: true,
      message: "请上传xx动画",
      trigger: "blur",
    },
  ],
  validation: [], // 自定义校验规则 不这么传的话 校验会有错误提示，且上传动效后不会消失
})
@Component
export default class BaseSpineSelect extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseSpineSelect";
  cssPrefix = "base-spine-select";

  animationOptions: Array<{ label: string; value: string }> = [];

  isFetching = false;

  animationOffset = 0;

  get animationKeys() {
    // 自定义动画字段需要在options中添加 label和value, value对应字段名
    return this.formItemConfigs["options"] || [{ label: "播放队列", value: "animation" }];
  }

  get clearable() {
    // 默认无删除按钮
    return this.formItemConfigs.clearable;
  }

  get defaultOptionItem() {
    if (this.formItemConfigs.hasDefaultOption) {
      return { label: "无动画", value: "" };
    }
    return null;
  }

  getAnimationOffset() {
    const animationDom = this.$el;
    this.animationOffset = animationDom ? (animationDom as any).offsetLeft : 0;
  }

  mounted() {
    this.getAnimationOffset();
    this.getAnimationOptions();
  }

  getAnimationOptions() {
    if (this.val?.skeleton) {
      this.fetchAnimation(this.val?.skeleton);
    }
  }

  handleSelect() {
    this.$getPageConfigByKey("SpineLibrary")().then((comp: { SpineLibrary: (arg0: { isMultiple: boolean; classify: string; onClose: () => void; onConfirm: (spines: any) => void }) => void }) => {
      comp.SpineLibrary({
        isMultiple: true,
        classify: "spine",
        onClose: () => {
          // bus.$emit("resourceLibraryToggle", false);
        },
        onConfirm: spines => {
          const [{ atlas, images, skeleton, cover }] = spines;
          this.setVal({
            atlas,
            images,
            skeleton,
            cover,
          });
          this.fetchAnimation(skeleton);
        },
      });
    });
  }

  checkAudio = (...params: any) => {
    const value = params[1];
    const callback = params[2];
    if (!value.skeleton) {
      return callback(new Error(`请添加${this.formItemConfigs.label}动效`));
    }
    if (!value.animation) {
      return callback(new Error(`请选择${this.formItemConfigs.label}播放队列`));
    }
    return callback();
  };

  rules = [{ validator: this.checkAudio, trigger: "change" }];

  handleDelete() {
    this.setVal({
      atlas: "",
      images: [],
      skeleton: "",
      cover: "",
    });
  }

  fetchAnimation(url: string) {
    if (process.env.VUE_APP_IS_OVERVIEW) {
      this.animationOptions = [
        { label: "idle", value: "idle" },
        { label: "move", value: "move" },
      ];
      if (this.defaultOptionItem) {
        this.animationOptions.unshift(this.defaultOptionItem);
      }
      return;
    }
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations).map(ani => {
          return {
            label: ani,
            value: ani,
          };
        });
        this.animationOptions = [...animationsKeys];
        if (this.defaultOptionItem) {
          this.animationOptions.unshift(this.defaultOptionItem);
        }
        this.$nextTick(() => {
          this.getAnimationOffset();
        });
      })
      .finally(() => {
        this.isFetching = false;
      });
  }

  handleChange(key: string, val: string) {
    this.setVal({ ...this.val, [key]: val });
  }
}
</script>

<style lang="less">
.spine-wrapper {
  .no-spine {
    display: flex;
    .media-upload {
      display: inline-block;
      line-height: 28px;
    }
    .tip {
      margin-left: 16px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .has-spine {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    padding: 0 8px;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
    &:hover {
      .button-wrapper {
        bottom: 0px;
      }
    }
    .button-wrapper {
      position: absolute;
      bottom: -28px;
      display: flex;
      justify-content: space-between;
      left: 10px;
      width: 120px;
      height: 28px;
      align-items: flex-end;
      transition: all 0.3s;
      button {
        margin-left: 0;
        // width: 60px;
        flex: 1;
      }
      &:hover {
        bottom: 0;
      }
    }
    .spine-cover {
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      background: url("~@/assets/img/transparent.png") repeat;
      background-size: 10px;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .el-icon-success {
      color: #42c57a;
    }
    .el-icon-error {
      display: none;
      color: #95a1ad;
      cursor: pointer;
    }
    .el-input__count {
      line-height: 15px;
    }
    .el-icon-success,
    .el-icon-error {
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
    &:hover {
      border-radius: 2px;
      // background: #f0f1f5;
      .el-icon-error {
        display: inline-block;
      }
      .el-icon-success {
        display: none;
      }
    }
  }
  .audio-control {
    margin-bottom: 10px;
  }
  .el-textarea {
    margin-bottom: 6px;
    /deep/ .el-input__count {
      line-height: 15px;
    }
  }
  .upload-handler {
    > span {
      display: flex;
      align-items: center;
    }
  }
}
.animation-item {
  margin-bottom: 12px;
}
.animation-list {
  margin-top: 12px;
}
.opacity-label label {
  opacity: 0 !important;
}
</style>
