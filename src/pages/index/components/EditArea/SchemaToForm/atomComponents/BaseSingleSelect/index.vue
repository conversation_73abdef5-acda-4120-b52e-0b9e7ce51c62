<template>
  <div class="base-single-select">
    <el-select v-model="val" v-bind="formConfig" :multiple="false">
      <el-option v-show="!!item.label" v-for="item in options" :key="item.value" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import { isEqual } from "lodash-es";

@OverviewDataCollection({
  label: "单选下拉框",
  value: "1",
  options: [
    { label: "选项1", value: "1" },
    { label: "选项2", value: "2" },
    { label: "选项3", value: "3" },
  ],
})
@Component({})
export default class BaseSingleSelect extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseSingleSelect";
  get options() {
    if (this.formItemConfigs.optionsConfig) {
      const {
        optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, valueKey, disabledValues },
      } = this.formItemConfigs;
      const keys = relativePropertiesKey.split(".");

      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while (keys.length) {
        let key = keys.shift();
        if (key === "subQuestionIndex") key = Number(this.$attrs.parentIndex) !== -1 ? this.$attrs.parentIndex : this.formIndex;
        if (temp[key]) {
          temp = temp[key];
        }
      }
      if (!keys.length && temp && temp.length) {
        options = temp.map((option: any, index: number) => {
          const val = valueKey === "index" ? index : option[valueKey];
          return {
            label: labelKey ? option[labelKey] : `${labelPrefix}${index + 1}`,
            value: val,
            disabled: Boolean(disabledValues && disabledValues.length && disabledValues.includes(val)),
          };
        });
      }
      return options || [];
    }
    return this.formItemConfigs.options;
  }
  @Watch("options")
  onOptionsChange(val: any[], oldVal: any[]) {
    this.$nextTick(() => {
      const { options } = this;
      // 如果当前值不在options中 则将当前值设置为options中的第一个值
      const optionsValue = options.map((option: any) => option.value);
      // 选中的项内容发生了变化 长度也发生了变化，更新
      if (oldVal && val.length !== oldVal.length) {
        const curOption = val.find((option: any) => option.value === this.val);
        const oldOption = oldVal.find((option: any) => option.value === this.val);
        // 判断curOption和oldOption是否相同
        if (!isEqual(curOption, oldOption)) {
          this.setVal("");
        }
      }
      if (!optionsValue.includes(this.val) && typeof this.val !== "undefined") {
        this.setVal("");
      }
    });
  }
}
</script>

<style lang="less" scoped>
.el-select {
  width: 100%;
}
</style>
