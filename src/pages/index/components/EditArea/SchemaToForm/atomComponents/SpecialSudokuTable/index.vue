<template>
  <div class="special-sudoku-table">
    <div class="table-row out-row" v-if="formConfig.hasOutsideGrid">
      <div class="table-col out-item out-item-left opacity">左</div>
      <div class="table-col out-item out-item-top" v-for="(sitem, sIndex) in formConfig.col" :key="`${sitem}`">
        <div class="cell-div">
          <!-- 上 0-->
          <el-input-number
            :min="1"
            :max="formConfig.max"
            :step="1"
            :step-strictly="true"
            type="number"
            size="mini"
            controls-position="right"
            :controls="false"
            :value="val['outerTables'][0][sIndex] ? val['outerTables'][0][sIndex] : undefined"
            @change="(val) => {handleChange(val, 0, sIndex, 'outerTables')}"
          ></el-input-number>
        </div>
      </div>
      <div class="table-col out-item opacity"></div>
    </div>
    <div class="table-row inner-row" :class="{ 'first': index === 0 }" v-for="(item, index) in formConfig.row" :key="item">
      <div class="table-col out-item out-item-left" v-if="formConfig.hasOutsideGrid">
        <div class="cell-div">
          <!-- 左 3-->
          <el-input-number
            :min="1"
            :max="formConfig.max"
            :step="1"
            type="number"
            size="mini"
            controls-position="right"
            :step-strictly="true"
            :controls="false"
            :value="val['outerTables'][3][index] ? val['outerTables'][3][index] : undefined"
            @change="(val) => {handleChange(val, 3, index, 'outerTables')}"
          ></el-input-number>
        </div>
      </div>
      <div class="table-col inner-item" :class="{ 'subfirst': sIndex === 0 }" v-for="(sitem, sIndex) in formConfig.col" :key="`${item}-${sitem}`">
        <div class="cell-div">
          <el-input-number
            :step-strictly="true"
            v-if="formConfig.fillMode === 'text'"
            :min="1"
            :max="formConfig.max"
            :step="1"
            type="number"
            size="mini"
            controls-position="right"
            :value="val['innerTables'][index][sIndex] ? val['innerTables'][index][sIndex] : undefined"
            @change="(val) => {handleChange(val, index, sIndex, 'innerTables')}"
            :controls="false"
          ></el-input-number>
          <color-picker
            v-if="formConfig.fillMode === 'color'"
            :simple="true"
            :value="val.innerTables[index][sIndex] ? val.innerTables[index][sIndex] : undefined"
            @input="(val) => {handleChange(val, index, sIndex, 'innerTables')}"
            :simpleColors="formConfig.colors"></color-picker>
        </div>
      </div>
      <div class="table-col out-item out-item-right" v-if="formConfig.hasOutsideGrid">
        <div class="cell-div">
          <!-- 右 1-->
          <el-input-number
            :min="1"
            :max="formConfig.max"
            :step="1"
            type="number"
            size="mini"
            controls-position="right"
            :controls="false"
            :step-strictly="true"
            :value="val['outerTables'][1][index] ? val['outerTables'][1][index] : undefined"
            @change="(val) => {handleChange(val, 1, index, 'outerTables')}"
          ></el-input-number>
        </div>
      </div>
    </div>
    <div class="table-row out-row" v-if="formConfig.hasOutsideGrid">
      <div class="table-col out-item opacity">
        <div class="cell-div"></div>
      </div>
      <div class="table-col out-item out-item-bottom" v-for="(sitem, sIndex) in formConfig.col" :key="`${sitem}`">
        <div class="cell-div">
          <!-- 下2-->
          <el-input-number
            :min="1"
            :max="formConfig.max"
            :step="1"
            type="number"
            size="mini"
            controls-position="right"
            :step-strictly="true"
            :controls="false"
            :value="val['outerTables'][2][sIndex] ? val['outerTables'][2][sIndex] : undefined"
            @change="(val) => {handleChange(val, 2, sIndex, 'outerTables')}"
          ></el-input-number>
        </div>
      </div>
      <div class="table-col out-item opacity">
        <div class="cell-div">
          右
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import BaseClassForm from "../../BaseClassForm";
import ColorPicker from "@/components/zyb-color-picker/ColorPicker.vue";
import cloneDeep from "lodash-es/cloneDeep";
import { Message } from "element-ui";

@OverviewDataCollection({
  label: "标记宫",
  text: "点击生成",
  value: {
    outerTables: [
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined]
    ],
    innerTables: [
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined],
      [undefined,undefined,undefined,undefined,undefined]
    ],
  },
  hasOutsideGrid: true,
  fillMode: "color", // 'color' | 'text'
  row: 5,
  col: 5,
  colors: [],
  sameColorCellNumber: 5,
  min: 1,
  cellHeight: "20px",
  max: 5,
})
// 1*3（）3*1（） 1*4（） 4*1（） 3*3（） 4*4（） 5*5（span: 20 width: 30）
@Component({ components: { ColorPicker } })
export default class SpecialSudokuTable extends BaseClassForm<Record<string, any>> {
  static componentName = "SpecialSudokuTable";
  init = false;
  public handleClick() {
    console.log("click");
    const timeStamp = new Date().getTime();
    this.setVal(timeStamp);
  }

  @Watch("val", { deep: true })
  onValChange() {
    if(this.init) return;
    // 题板规格变更时 不更新数据
    // 判断二维数组为空
    let isRefreshBlankData = true;
    [...this.val['innerTables'], ...(this.val['outerTables'] || [])].forEach((item) => {
      item.forEach((subItem: any) => {
        if(subItem) isRefreshBlankData = false;
      })
    })
    if(!isRefreshBlankData) return;
    // 空数据 清空input .special-sudoku-table input
    this.setVal(this.val);
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
    // const domTimer = setTimeout(() => {
    //   clearTimeout(domTimer);
    //   const inputDoms = document.querySelectorAll('.special-sudoku-table input');
    //   inputDoms.forEach((item: any) => {
    //     item.value = '';
    //   })
    // }, 500);
    // this.$nextTick(() => {
    //   const inputDoms = document.querySelectorAll('.special-sudoku-table input');
    //   inputDoms.forEach((item: any) => {
    //     item.value = '';
    //   })
    // })
  }

  created() {
    this.init = true;
    const timer = setTimeout(() => {
      this.init = false;
      clearTimeout(timer);
    }, 1000);
  }
  handleChange(...params: (string | number)[]) {
    const [value, index, subIndex, key] = params;
    // 如果颜色相同的单元格数量超过了sameColorCellNumber 则不允许再填入
    const sameColorCellCount = this.val.innerTables.reduce((prev: number, cur: any[]) => {
      return prev + cur.filter((item: any) => item === value).length;
    }, 0);
    const { fillMode, sameColorCellNumber } = this.formConfig;
    if(fillMode === 'color' && sameColorCellNumber && sameColorCellCount >= sameColorCellNumber) {
      Message({
        message: `颜色相同的单元格数量不能超过${sameColorCellNumber}`,
        type: "error"
      });
      return;
    }
    let temp = value;
    if(fillMode === 'text' && value) {
      temp = Math.round(Number(value));
    }
    const valClone = cloneDeep(this.val);
    valClone[key][index][subIndex] = temp;
    this.setVal(valClone);
  }
}
</script>

<style lang="less" scoped>
.special-sudoku-table {
  // border-left: 1px solid #e1e2e6;
  // border-top: 1px solid #e1e2e6;
  box-sizing: border-box;
  .table-row {
    display: flex;
  }
  .outside-row {
    display: flex;
  }
  .cell-div {
    width: 100%;
    height: 100%;
  }
  .table-col {
    width: 50px;
    border-bottom: 1px solid #e1e2e6;
    border-right: 1px solid #e1e2e6;
    box-sizing: border-box;
  }
  // 第一个inner-row中的 inner-item
  .first .inner-item {
    border-top: 1px solid #e1e2e6;
  }
  // 第一行的第一个单元格
  // .inner-row:first-child .inner-item {
  //   border-top: 1px solid #e1e2e6;
  // }
  // 每一行的第一个单元格
  .inner-item:first-child {
    border-left: 1px solid #e1e2e6;
  }
  .subfirst {
    border-left: 1px solid #e1e2e6;
  }
  .opacity {
    opacity: 0;
  }
  .out-item {
    border: 0px !important;
    // padding: 6px;
    .cell-div {
      border: 1px solid #e1e2e6;
    }
  }
  .first .out-item-left,  .first .out-item-right {
    margin-top: 0px;
  }
  .first .out-item-top,  .first .out-item-bottom {
    margin-left: 0px;
  }
  .out-item-left {
    padding-right: 6px;
    padding-bottom: 2px;
    padding-top: 0;
    padding-left: 0;
    margin-top: -1px;
  }
  .out-item-right {
    padding-left: 6px;
    padding-bottom: 2px;
    padding-top: 0;
    padding-right: 0;
    margin-top: -1px;
  }
  .out-item-top {
    padding-bottom: 6px;
    padding-left: 0px;
    padding-top: 0;
    padding-right: 2px;
    margin-left: -1px;
  }
  .out-item-bottom {
    padding-top: 6px;
    padding-left: 0px;
    padding-bottom: 0;
    padding-right: 2px;
    margin-left: -1px;
  }
  
  /deep/ .el-input-number--mini {
    width: 100%;
    display: block;
    height: 100%;
    line-height: 30px;
  }
  /deep/ .el-input--mini {
    height: 100%;
  }
  /deep/ .el-input input {
    padding: 0;
    border: 0px;
    height: 100%;
  }
  /deep/ #boxPanel {
    width: unset !important;
    left: 0px;
  }
  /deep/ .tColor {
    display: flex;
  }
  /deep/ .zyb-color-container {
    width: 100%;
    height: 100%;
  }
}
</style>
