
<template>
  <div class="base-multiple-select">
    <el-select v-model="val" v-bind="formConfig" multiple popper-class="virtualselect" filterable :filter-method="filterMethod" ref="select" @hook:mounted="refreshGetOption" @visible-change="isVisibleChange">
      <virtual-list
        v-if="formItemConfigs.useVirtual"
        class="virtualselect-list"
        :data-key="'value'"
        :data-sources="selectArr"
        :data-component="itemComponent"
        :keeps="10"
        ref="virtualList"
        :extra-props="{
            label: 'label',
            value: 'value'
        }"></virtual-list>
      <template v-if="!formItemConfigs.useVirtual">
        <el-option v-for="item in options" :key="item.value" :label="item.label"
        :value="item.value" :disabled="item.disabled"></el-option>
      </template>
    </el-select>
  </div>
</template>
  
  <script lang="ts">
import BaseClassForm from "../../BaseClassForm";
import { Component, Watch } from "vue-property-decorator";
import { OverviewDataCollection } from "../../OverviewCollection";
import virtualList from "vue-virtual-scroll-list";
import ElOptionNode from './el-option-node.vue'

@OverviewDataCollection({
  label: "多选下拉框",
  value: ["1"],
  useVirtual: false,
  options: [
    { label: "选项1", value: "1" },
    { label: "选项2", value: "2" },
    { label: "选项3", value: "3" },
  ],
})
@Component({
  components: {
    virtualList,
  },
})
export default class BaseMultipleSelect extends BaseClassForm<Record<string, any>> {
  static componentName = "BaseMultipleSelect";
  itemComponent = ElOptionNode;
  query = '';
  get val() {
    return this.getVal() || [];
  }
  set val(val) {
    this.setVal(val);
  }
  get options() {
    if(this.formItemConfigs.optionsConfig) {
      const { optionsConfig: { relativePropertiesKey, labelKey, labelPrefix, valueKey, disabledValues } } = this.formItemConfigs;
      const keys = relativePropertiesKey.split(".");
      
      const { properties } = this.currentComponent;
      let temp = properties;
      let options = [];
      while(keys.length) {
        let key = keys.shift();
        if(key === 'subQuestionIndex') key = this.$attrs.parentIndex;
        if(temp[key]) {
          temp = temp[key];
        }
        if(!keys.length && temp && temp.length) {
          options = temp.map((option: any, index: number) => {
            const val = valueKey === 'index' ? index : option[valueKey];
            return {
              label: labelKey ? option[labelKey] : `${labelPrefix}${index+1}`,
              value: val,
              disabled: disabledValues && disabledValues.length && disabledValues.includes(val)
            }
          })
        }
      }
      return options || [];
    }
    return this.formItemConfigs.options;
  }
  @Watch("options")
  onOptionsChange() {
    if(this.val.length) {
      const { options } = this;
      const newVal = this.val.filter((v: any) => options.find((opt: { value: any; }) => opt.value === v));
      this.setVal(newVal);
    };
    // (this.$refs as any).virtualList && (this.$refs as any).virtualList.reset();
  }

  get selectArr() {
    if(!this.query) {
      return this.options;
    }
    return this.options.filter((item: { label: string; value: string; }) => {
      return item.label.toLowerCase().indexOf(this.query.toLowerCase()) > -1 || item.value.toLowerCase().indexOf(this.query.toLowerCase()) > -1;
    });
  }

  filterMethod(query: string) {
    console.log('query', query);
    this.query = query;
  }

  isVisibleChange(bool: boolean) {
    if(bool && this.formItemConfigs.useVirtual) {
      this.query = '';
      (this.$refs as any).virtualList && (this.$refs as any).virtualList.reset();
    }
  }

  refreshGetOption() {
    if(!this.formItemConfigs.useVirtual) return;
    this.$nextTick(() => {
      (this.$refs.select as any).getOption = (val: any) => {
        const item = this.options.find((item: { value: any; }) => item.value === val);
        return {
          currentLabel: item.label,
          value: item.value,
          hitState: false
        }
      }
      (this.$refs.select as any).setSelected();
    });
  }

  mounted() {
    setTimeout(() => {
      (window as any).vl = (this.$refs as any).virtualList;
    }, 2000);
  }
}
</script>
  
<style lang="less">
  .virtualselect-list {
    max-height: 245px;
    overflow-y: auto;
  }
  .base-multiple-select {
    .el-select {
      width: 100%;
    }
    .el-select__tags {
      width: calc(100% - 7px);
      max-width: calc(100% - 7px);
    }
    .el-tag {
      white-space: break-spaces;
      word-break: break-all;
      height: auto;
    }
  }
</style>
