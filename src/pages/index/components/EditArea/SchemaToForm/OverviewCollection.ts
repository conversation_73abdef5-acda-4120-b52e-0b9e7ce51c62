export const overviewTableData: Array<FormItemConfigs> = [];

export function OverviewDataCollection(params: { label: string; value?: any; options?: FormOption[]; checkboxLabel?: string; text?: string; subFormConfigs?: FormItemConfigs[];[x: string]: any }) {
  return function(targetClass: any) {
    overviewTableData.push({
      formItemType: targetClass.componentName,
      key: "propertiesKey",
      ...params,
    });
  };
}
