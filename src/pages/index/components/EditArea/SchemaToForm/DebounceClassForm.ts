import { Component } from "vue-property-decorator";
import { debounce } from "lodash-es";
import BaseClassForm from "./BaseClassForm";
import { DEBOUNCE_TIME } from "@/pages/index/common/utils/tools";

@Component({})
export default class DebounceClassForm<T> extends BaseClassForm<T> {
  // 表单绑定值
  bindVal: number | string | boolean = "";
  created() {
    this.bindVal = this.getVal();
  }
  // 防抖函数
  debounceFun = debounce(val => {
    this.setVal(val);
  }, DEBOUNCE_TIME);
  get val() {
    return this.bindVal;
  }
  set val(val) {
    this.bindVal = val;
    this.debounceFun(val);
  }
  get nativeVal() {
    return this.val;
  }
  beforeDestroy() {
    this.debounceFun.cancel();
  }
}
