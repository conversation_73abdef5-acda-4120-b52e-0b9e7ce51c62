<template>
  <div class="schema-to-form-list">
    <el-collapse :value="collapseValue">
      <div class="user-tips">配置不完整会导致用户无法作答，切记预览查看效果哦~</div>
      <el-form size="small" :rules="rules" :model="properties">
        <el-row>
          <common-atom-shell v-for="formItem in formData.formList" :key="formItem.key" v-bind="overviewBind" :formItemConfigs="formItem" :currentComponents="currentComponents"> </common-atom-shell>
        </el-row>
        <el-collapse-item v-for="item in formData.collapseList" :key="item.encodeId" :name="item.encodeId" :title="item.collapseName">
          <el-row>
            <common-atom-shell
              v-for="formItem in item.formList"
              :key="formItem.key" v-bind="overviewBind"
              :formItemConfigs="formItem"
              :currentComponents="currentComponents"> </common-atom-shell>
          </el-row>
        </el-collapse-item>
      </el-form>
    </el-collapse>
  </div>
</template>

<script lang="ts">
import { configsToRules, exportVue, formSchemaToFlat, strToUnicode } from "@/pages/index/common/utils/tools";
import { Component, Vue, Prop } from "vue-property-decorator";
import CommonAtomShell from "./CommonAtomShell.vue";
import "@/common/utils/eventListener";

@Component({
  components: {
    CommonAtomShell,
  },
})
export default class SchemaToFormList extends Vue {
  @Prop({
    required: false,
    default: () => [],
  })
  currentComponents!: Components;

  @Prop({
    required: false,
    default: () => null,
  })
  overviewData!: {
    formSchema: FormSchema;
    properties: Record<string, any>;
  } | null;

  @Prop({
    required: false,
    default: () => null,
  })
  baseUpdateStoreData!: Function;

  collapseValue: string[] = [];

  get properties() {
    if (this.overviewData) {
      return this.overviewData.properties;
    } else {
      return this.currentComponents[0]?.properties;
    }
  }

  get formSchema() {
    if (this.overviewData) {
      return this.overviewData.formSchema;
    } else {
      return this.$store.state.extData?.formConfig?.[this.currentComponents[0]?.subType] || [];
    }
  }

  get overviewBind() {
    if (this.overviewData) {
      return {
        baseUpdateStoreData: this.baseUpdateStoreData,
        updateDataObj: this.overviewData.properties,
      };
    } else {
      return {};
    }
  }

  formData: {
    collapseList: CollapseFormRuntime[];
    formList: FormItemConfigs[];
  } = {
    collapseList: [],
    formList: [],
  };

  get formSchemaRuntime() {
    this.formData = {
      collapseList: [],
      formList: [],
    };
    const formSchemaRuntime: Array<FormItemConfigs | CollapseFormRuntime> = [];
    const encodeIds: string[] = [];
    this.formSchema.forEach((item: FormItemConfigs | CollapseForm) => {
      if (item.formItemType === "collapse") {
        const encodeId = strToUnicode((item as CollapseForm).collapseName);
        encodeIds.push(encodeId);
        const collapseForm = { ...(item as CollapseForm), encodeId };
        formSchemaRuntime.push(collapseForm);
        this.formData.collapseList.push(collapseForm);
      } else {
        formSchemaRuntime.push(item as FormItemConfigs);
        this.formData.formList.push(item as FormItemConfigs);
      }
    });
    this.collapseValue = encodeIds;
    return formSchemaRuntime;
  }

  get rules() {
    return configsToRules(formSchemaToFlat(this.formSchemaRuntime));
  }

  // 批量注册表单原子组件
  registerAtomComponent() {
    const modules = exportVue(require.context("@/pages/index/components/EditArea/SchemaToForm/atomComponents", true, /index\.vue$/));
    Object.keys(modules).forEach(key => {
      Vue.component(key, modules[key]);
    });
  }

  created() {
    this.registerAtomComponent();
  }
}
</script>

<style lang="less" scoped>
.user-tips {
  color: #fa574b;
  margin-top: 12px;
  margin-bottom: 6px;
  text-align: left;
}
/deep/ .table-tag-shell {
  .el-form-item {
    margin-bottom: 0 !important;
  }
  .base-tag {
    height: 32px;
    margin-right: 1px !important;
    margin-bottom: 1px !important;
    span {
      box-sizing: border-box;
      width: calc(100% - 1px);
    }
  }
  .is-required label{
    display: none !important;
  }
  .form-list {
    padding-bottom: 6px;
  }
  input {
    margin-right: 1px !important;
    padding: 0px 2px !important;
    width: calc(100% - 1px);
  }
}
/deep/ .unspeakabel-answer-table {
  .el-form-item {
    margin-bottom: 1px !important;
  }
  .el-form-item__content {
    margin-left: -5px;
    padding-left: 0px;
  }
  .form-list {
    padding-bottom: 6px;
  }
  input {
    margin-right: 1px !important;
    padding: 0px 2px !important;
    width: calc(100% - 6px);
  }
  .is-required label{
    display: none !important;
  }
}
/deep/ .el-collapse-item.is-active {
  overflow: hidden !important;
}
</style>
