
<template>
  <div class="overview">
    <el-tabs v-model="activeName">
      <el-tab-pane label="组件总览" name="over">
        <div class="filter-area">
          <el-input v-model="compName" clearable placeholder="请输入组件名称搜索" @input="compNameSearch">
          </el-input>
        </div>
        <el-table :data="overviewTableData" style="width: 100%" height="calc(100vh - 100px)"
          :cell-class-name="cellClassName">
          <el-table-column type="index" width="50">
          </el-table-column>
          <el-table-column label="表单组件">
            <template slot-scope="scope">
              <el-form size="small">
                <common-atom-shell :formItemConfigs="scope.row" :currentComponents="currentComponents"
                  :updateDataObj="{propertiesKey: scope.row.value}" :formIndex="scope.$index"
                  :baseUpdateStoreData="updateOverviewData">
                </common-atom-shell>
              </el-form>
            </template>
          </el-table-column>
          <el-table-column label="value" width="100">
            <template slot-scope="scope">
              {{ getValue(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="value类型" width="120">
            <template slot-scope="scope">
              {{getValueType(scope.row)}}
            </template>
          </el-table-column>
          <el-table-column label="表单schema">
            <template slot-scope="scope">
              <pre :title="JSON.stringify(scope.row, null, 4)">{{ scope.row }}</pre>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template slot-scope="scope">
              <div class="action-cell">
                <el-button size="mini" @click="copyToClipboard(scope.row)" style="margin-bottom: 10px;">复制schema</el-button>
                <el-button size="mini" @click="copyToClipboard(scope.row.formItemType)" style="margin-bottom: 10px;">复制formItemType
                </el-button>
                <el-button size="mini" v-if="scope.row.desc" type="danger" style="margin-bottom: 10px;" @click="handleAlert(scope.row)">注意事项
                </el-button>
                <el-button size="mini" @click="handleDebug(scope.row)" type="success">调试
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="调试" name="debug">
        <div class="debug-area">
          <div class="debug-area-left">
            <div class="textarea-box schema">
              <div class="textarea-title">schema</div>
              <el-input type="textarea" :rows="20" placeholder="请输入schema" v-model="schemaVal" @change="handleSchemaConfigChange">
              </el-input>
            </div>
            <div class="textarea-box properties">
              <div class="textarea-title">properties</div>
              <el-input type="textarea" :rows="20" placeholder="请输入properties"
                v-model="propertiesVal">
              </el-input>
            </div>
            <div class="btn-box">
              <el-button @click="generateForm" type="primary">生成表单</el-button>
              <el-button @click="jsonFormat" type="primary">json格式化</el-button>
              <el-button v-if="overviewData" @click="formValidate" type="primary">表单校验</el-button>
            </div>
          </div>
          <div class="debug-area-form">
            <SchemaToFormList v-if="overviewData" :baseUpdateStoreData="updateStoreData"
            :currentComponents="currentComponents"
              :overviewData="overviewData"></SchemaToFormList>
            <div v-else>请输入schema和properties生成表单~</div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
    <a class="document-link" target="_blank"
      href="https://alidocs.dingtalk.com/i/nodes/qXomz1wAyjKVXRxAQrlg83Y9pRBx5OrE">表单组件详细配置</a>
  </div>
</template>

<script lang="ts">
interface OverviewData {
  formSchema: FormItemConfigs[];
  properties: Record<string, any>;
}

import { Component, Vue } from "vue-property-decorator";
import { overviewTableData } from "./OverviewCollection";
import CommonAtomShell from "./CommonAtomShell.vue";
import copyToClipboard from "@/common/utils/copyToClipboard";
import SchemaToFormList from "./SchemaToFormList.vue";
import { configsToRules, getCleanFormKeyAndOption, updateConfigsProps, updateFormList } from "@/pages/index/common/utils/tools";
import { schemaValidate } from "@/common/utils/dataValidate/schemaValidate";

@Component({
  components: {
    CommonAtomShell,
    SchemaToFormList,
  },
})
export default class Overview extends Vue {
  [x: string]: any;
  get rules() {
    if (this.overviewData) {
      return configsToRules(this.overviewData.formSchema);
    } else {
      return [];
    }
  }
  overviewTableData = overviewTableData;
  activeName = localStorage.getItem('activeName') || "over";
  schemaVal = localStorage.getItem('schemaVal') || JSON.stringify([]);
  propertiesVal = localStorage.getItem('propertiesVal') || JSON.stringify({});
  overviewData: OverviewData | null = null;
  compName = "";
  get currentComponents() {
    const properties = this.overviewData?.properties || {};
    return [{
      id: '1',
      properties
    }]
  }
  compNameSearch() {
    if (this.compName === "") {
      this.overviewTableData = overviewTableData;
    } else {
      const filterList = overviewTableData.filter(item => item.label?.indexOf(this.compName) !== -1);
      this.overviewTableData = filterList;
    }
  }
  cellClassName(data: any) {
    if (data.row.formItemType === "BaseColor" && data.columnIndex === 1) {
      return "cellVisible";
    }
  }
  copyToClipboard(json: Record<string, any>) {
    const isOK = copyToClipboard(JSON.stringify(json, function (k: any, v: any) {
    // 忽略文本组件的内容
    if (k === 'desc') {
      return undefined
    }
    return v;
  }, 4));
    if (isOK) {
      this.$message.success("复制成功");
    } else {
      this.$message.error("复制失败");
    }
  }

  handleDebug(row: any) {
    console.log('...handleDebug...', row);
    this.activeName = "debug";
    this.schemaVal = JSON.stringify([row], function (k: any, v: any) {
    // 忽略文本组件的内容
    if (k === 'desc') {
      return undefined;
    }
    return v;
  }, 4);
    this.propertiesVal =  JSON.stringify({
      [row.key]: row.value
    }, null, 4);
    this.$nextTick(() => {
      this.generateForm();
    })
  }

  handleAlert(row: any) {
    console.log('...handleAlert...', row);
    // this.$message(`<pre>${row.desc}</pre>`, '组件注意事项', {
    //   dangerouslyUseHTMLString: true
    // });
    this.$alert(`${row.desc}`, '组件注意事项', {
      dangerouslyUseHTMLString: true
    });
  }
  updateOverviewData({ index, key, value }: { index: number; key: string; value: any }) {
    this.overviewTableData[index].value = value;
  }
  jsonFormat() {
    try {
      const schemaVal = JSON.parse(this.schemaVal);
      if (Array.isArray(schemaVal)) {
        this.schemaVal = JSON.stringify(schemaVal, null, 4);
      } else {
        this.$message.error(`schema定义类型需要是Array`);
      }
    } catch (error) {
      this.$message.error(`输入schema格式异常:${error}`);
    }
    try {
      const properties = JSON.parse(this.propertiesVal);
      this.propertiesVal = JSON.stringify(properties, null, 4);
    } catch (error) {
      this.$message.error(`输入properties格式异常:${error}`);
    }
  }
  generateForm() {
    const overviewData: Partial<OverviewData> = {};
    try {
      const schemaVal = JSON.parse(this.schemaVal);
      if (Array.isArray(schemaVal)) {
        overviewData.formSchema = schemaVal;
        localStorage.setItem("schemaVal", this.schemaVal);
        localStorage.setItem("activeName", this.activeName);
      } else {
        this.$message.error(`schema定义类型需要是Array`);
      }
    } catch (error) {
      this.$message.error(`输入schema格式异常:${error}`);
    }
    try {
      overviewData.properties = JSON.parse(this.propertiesVal);
      localStorage.setItem("propertiesVal", this.propertiesVal);
    } catch (error) {
      this.$message.error(`输入properties格式异常:${error}`);
    }
    if (overviewData.properties && overviewData.formSchema) {
      this.overviewData = null;
      this.$nextTick(() => {
        this.overviewData = overviewData as OverviewData;
      });
    }
  }

  getValueType(row: any) {
    if (!("value" in row)) return "/";
    const val = row.value;
    if (typeof val === "object") {
      if (!val) {
        return "null";
      } else if (Array.isArray(val)) {
        return `Array<${typeof val[0]}>`;
      } else {
        return "object";
      }
    } else {
      return typeof val;
    }
  }
  getValue(row: any) {
    if (!("value" in row)) return "/";
    return typeof row.value === "string" ? `"${row.value}"` : row.value;
  }

  updateStoreData({ index, key, value }: { index: number; key: string; value: any }) {
    if (!this.overviewData) return;
    this.$set(this.overviewData.properties, key, value);
    this.propertiesVal = JSON.stringify(this.overviewData.properties, null, 2);
    let formItemConfigs = this.overviewData.formSchema.find(item => {
      // 折叠面板
      if (item.formItemType === 'collapse') {
        return item.formList.find((child:FormItemConfigs)  => child.key === key)
      }
      return item.key === key
    });
    if (formItemConfigs?.formItemType === 'collapse') {
      formItemConfigs = formItemConfigs.formList.find((child:FormItemConfigs)  => child.key === key)
    }
    // 是否存在关联属性options
    if (!formItemConfigs?.options?.length) return;
    // let newFormSchema: FormItemConfigs[] = this.overviewData.formSchema;
    let newFormSchema: FormItemConfigs[] = [];
    this.overviewData.formSchema.forEach((item) => {
      if(item.formItemType === 'collapse') {
        newFormSchema = [...newFormSchema, ...item.formList]
      } else {
        newFormSchema = [...newFormSchema, item]
      }
    })
    // 只要存在options,就需要遍历,避免当前项没有option,其他项有,这时需要删除其他项optin表单
    const { cleanFormKey, option } = getCleanFormKeyAndOption(formItemConfigs?.options, value);
    // 存在需要清除的表单key,则更新formSchema
    if (cleanFormKey.length) {
      newFormSchema = updateFormList(this.overviewData.formSchema, cleanFormKey, [option], [key]) as FormItemConfigs[];
    }
    const resData = updateConfigsProps({ key, value }, newFormSchema);
    // 存在更新其他表单属性
    if (resData) {
      const { changeValData, newFormConfigs } = resData;
      newFormSchema = newFormConfigs;
      this.$nextTick(() => {
        // 待属性赋值生效后,再进行value赋值
        const properties = (this.overviewData as OverviewData).properties;
        Object.assign(properties, changeValData);
        this.propertiesVal = JSON.stringify(properties, null, 2);
      });
    }
    // 不等于初始值则进行赋值
    if (newFormSchema !== this.overviewData.formSchema) {
      this.overviewData.formSchema = newFormSchema;
    }
  }
  formValidate() {
    if (!this.overviewData) return;
    const { properties, formSchema } = this.overviewData;
    if (properties && formSchema) {
      const res = schemaValidate(formSchema, properties);
      if (res) {
        this.$message.error(`"${res.label || res.validateLabel || "存在"}" 表单为必填项`);
      } else {
        this.$message.success("校验通过");
      }
    }
  }
  handleSchemaConfigChange() {
    localStorage.setItem("schemaVal", this.schemaVal);
  }
}
</script>

<style lang="less" scoped>
.overview {
  height: 100vh;
  overflow: auto;
  box-sizing: border-box;
  padding: 0 10px;
  .document-link {
    position: fixed;
    top: 20px;
    right: 35px;
  }
  .filter-area {
    width: 300px;
    padding-bottom: 10px;
    padding-left: 10px;
  }
  .el-table {
    max-height: calc(100vh - 115px);
    overflow: auto;
    &::before {
      display: none;
    }
    /deep/ .cell {
      max-height: max-content;
    }
    /deep/ .cellVisible .cell {
      overflow: visible;
    }
  }
  .header {
    height: 50px;
    line-height: 30px;
    text-align: center;
    color: #909399;
    font-weight: bold;
    border-bottom: 2px solid #ebeef5;
  }
  .el-row {
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    &:hover {
      background-color: #f5f7fa;
    }
  }
  .debug-area {
    display: flex;
  }
  .debug-area-form {
    height: calc(100vh - 65px);
    overflow: auto;
    width: 360px;
    flex: none;
    background-color: #f1f1f1;
  }
  .debug-area-left {
    padding-right: 30px;
    width: 100%;
    flex: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .textarea-box {
      flex: none;
      &.properties {
        width: calc(30% - 10px);
      }
      &.schema {
        width: calc(70% - 10px);
      }
      .el-textarea {
        height: calc(100vh - 160px);
        /deep/ textarea {
          height: 100%;
        }
      }
    }
    .textarea-title {
      height: 30px;
      line-height: 30px;
      text-align: center;
    }
    .btn-box {
      width: 100%;
      text-align: center;
      padding-top: 10px;
    }
  }
  .action-cell {
    display: flex;
    align-items: baseline;
    justify-content: left;
    flex-direction: column;
  }     
}
</style>

