<template>
  <div class="container">
    <el-button-group class="btn-group">
      <el-button icon="element-icon el-iconbring-to-front" size="small" @click="toTop" :disabled="disabled"></el-button>
      <el-button icon="element-icon el-iconsend-to-back" size="small" @click="toBottom" :disabled="disabled"></el-button>
      <el-button icon="element-icon el-iconbring-forward" size="small" @click="forward" :disabled="disabled"></el-button>
      <el-button icon="element-icon el-iconsend-backward" size="small" @click="backward" :disabled="disabled"></el-button>
    </el-button-group>
    <div class="desc-group">
      <span class="desc">置顶</span>
      <span class="desc">置底</span>
      <span class="desc">上移</span>
      <span class="desc">下移</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { UpdateLevelType } from "@/pages/index/store";

@Component
export default class LevelEditor extends Vue {
  @Prop({
    required: true,
  })
  componentIds!: string[];
  @Prop({
    required: true,
  })
  disabled!: false;

  updateLevel(type: UpdateLevelType) {
    this.$store.dispatch("updateComponentsLevel", {
      ids: this.componentIds,
      type,
    });
  }

  toBottom() {
    this.updateLevel(UpdateLevelType.BOTTOM);
  }

  toTop() {
    this.updateLevel(UpdateLevelType.TOP);
  }

  backward() {
    this.updateLevel(UpdateLevelType.BACKWARD);
  }

  forward() {
    this.updateLevel(UpdateLevelType.FORWARD);
  }
}
</script>


<style scoped lang="less">
.container {
  margin-bottom: 10px;

  .btn-group,
  .desc-group {
    display: flex;

    .el-button {
      flex-grow: 1;
    }

    .desc {
      display: inline-block;
      flex-grow: 1;
      font-size: 12px;
      font-weight: 500;
    }
  }
}
</style>
