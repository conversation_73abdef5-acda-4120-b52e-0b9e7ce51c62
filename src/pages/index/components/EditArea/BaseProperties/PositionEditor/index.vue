<template>
  <div class="position-editor">
    <div class="flex">
      <span class="label">X</span>
      <el-input
        :value="displayValueX"
        :disabled="getComponentPropertiesDisabled('properties', 'x')"
        size="small"
        type="number"
        @input="userInputX = $event"
        @change="onChangeX"
        @focus="handleFocus"
      />
      <component-properties-editable-checker
        firstLevelProperty="properties"
        secondLevelProperty="x"
        :componentIds="componentIds"
      />
    </div>
    <div class="flex">
      <span class="label">Y</span>
      <el-input
        :value="displayValueY"
        :disabled="getComponentPropertiesDisabled('properties', 'y')"
        size="small"
        type="number"
        @input="userInputY = $event"
        @change="onChangeY"
        @focus="handleFocus"
      />
      <component-properties-editable-checker
        firstLevelProperty="properties"
        secondLevelProperty="y"
        :componentIds="componentIds"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class PositionEditor extends Mixins(
  GetComponentPropertiesDisabledMixin,
) {
  get stageWidth() {
    return this.$store.state.stageData.width;
  }

  get stageHeight() {
    return this.$store.state.stageData.height;
  }

  get stageSafeWidth() {
    return this.$store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return this.$store.state.stageData.safeHeight;
  }

  userInputX = null;
  get displayValueX() {
    if (this.userInputX !== null) {
      return this.userInputX;
    }

    return this.x;
  }
  onChangeX(val: string) {
    this.x = Number(val);
    this.userInputX = null;
  }

  userInputY = null;
  get displayValueY() {
    if (this.userInputY !== null) {
      return this.userInputY;
    }

    return this.y;
  }

  onChangeY(val: string) {
    this.y = Number(val);
    this.userInputY = null;
  }

  get x() {
    const firstId = this.componentIds[0];
    const firstComponent: Component = this.$store.state.componentMap[firstId];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    const { x: firstVal } = window.cocos.getWorldPos(
      firstComponent.id,
      firstComponent.properties.angle,
      firstComponent.properties.x,
    );

    const hasDifferentVal = this.componentIds.some(id => {
      const component: Component = this.$store.state.componentMap[id];
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      const { x } = window.cocos.getWorldPos(
        id,
        component.properties.angle,
        component.properties.x,
      );

      return x !== firstVal;
    });

    if (hasDifferentVal) return "";

    return firstVal;
  }

  set x(x) {
    this.componentIds.forEach(id => {
      this.$store.dispatch("updateComponentsProperties", {
        ids: [id],
        newProperties: {
          x,
        },
      });
    });
  }

  get y() {
    const firstId = this.componentIds[0];
    const firstComponent: Component = this.$store.state.componentMap[firstId];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    const { y: firstVal } = window.cocos.getWorldPos(
      firstId,
      firstComponent.properties.angle,
      firstComponent.properties.y,
    );

    const hasDifferentVal = this.componentIds.some(id => {
      const component: Component = this.$store.state.componentMap[id];
      // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
      // @ts-ignore
      const { y } = window.cocos.getWorldPos(
        id,
        component.properties.angle,
        component.properties.y,
      );

      return y !== firstVal;
    });
    if (hasDifferentVal) {
      return "";
    }
    return firstVal;
  }

  set y(y) {
    this.componentIds.forEach(id => {
      this.$store.dispatch("updateComponentsProperties", {
        ids: [id],
        newProperties: {
          y,
        },
      });
    });
  }

  convertToLeftTopCoordinate(
    x: number,
    y: number,
    width: number,
    height: number,
    stageWidth: number,
    stageHeight: number,
  ) {
    const newX = stageWidth / 2 + x - width / 2;
    const newY = stageHeight / 2 - y - height / 2;
    return { x: this.toFixed(newX, 1), y: this.toFixed(newY, 1) };
  }

  convertToCenterCoordinate(
    x: number,
    y: number,
    width: number,
    height: number,
    stageWidth: number,
    stageHeight: number,
  ) {
    const newX = -stageWidth / 2 + x + width / 2;
    const newY = stageHeight / 2 - y - height / 2;
    return { x: this.toFixed(newX, 1), y: this.toFixed(newY, 1) };
  }

  toFixed(num: number, n: number) {
    return Math.round(num * Math.pow(10, n)) / Math.pow(10, n);
  }

  handleFocus(e: FocusEvent) {
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    e.target?.select();
  }
}
</script>

<style scoped lang="less">
.position-editor {
  display: flex;
}

.flex {
  display: flex;
  align-items: center;

  &:first-child {
    margin-right: 16px;
  }

  .el-input {
    margin-left: 5px;
    flex-grow: 1;
  }

  /deep/ .el-input__inner {
    padding-right: 0;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 30px;
    flex-shrink: 0;
  }
}
</style>
