<template>
  <div>
    <span class="title">旋转</span>
    <div class="flex">
      <button @click="rotate(0)" class="btn-rotate">
        <svg version="1.1" viewBox="0 0 16 16" class="svg-icon svg-fill">
          <g fill-rule="nonzero" fill="none">
            <path pid="0" fill="#C0F0DB" d="M1 13.52l4.09-9.13v9.13"></path>
            <path pid="1" d="M5 13.333H1.676L5 5.883v7.45zm-4.351 1h5.018c.184 0 .333-.149.333-.333V2.751a.333.333 0 00-.638-.135L.345 13.864a.333.333 0 00.304.47z" fill="#00B266"></path>
            <path pid="2" d="M8.5 13.346v-2.559l4.674 2.559H8.5zm-1-3.509v4.071c0 .242.196.438.437.438h7.437a.437.437 0 00.21-.821L8.147 9.454a.437.437 0 00-.647.383z" fill="#525354"></path>
            <path
              pid="3"
              d="M10.1 3.5l.53-.555a.5.5 0 00-.725-.69l-1.333 1.4a.5.5 0 000 .69l1.333 1.4a.5.5 0 10.724-.69l-.527-.554A4.5 4.5 0 0114.5 9v1a.5.5 0 001 0V9a5.5 5.5 0 00-5.4-5.5z"
              fill="#00B266"
            ></path>
          </g>
        </svg>
        <span>左转90°</span>
      </button>

      <button @click="rotate(1)" class="btn-rotate">
        <svg version="1.1" viewBox="0 0 16 16" class="svg-icon svg-fill">
          <g fill-rule="nonzero" fill="none">
            <path pid="0" fill="#C0F0DB" d="M10.415 13.727V4.621l4.494 9.106z"></path>
            <path pid="1" d="M11 13.333h3.324L11 5.883v7.45zm4.351 1h-5.018A.333.333 0 0110 14V2.751a.333.333 0 01.638-.135l5.017 11.248a.333.333 0 01-.304.47z" fill="#00B266"></path>
            <path pid="2" d="M7.768 13.33v-2.503l-4.677 2.502h4.677zm1-3.616v4.282c0 .184-.149.333-.333.333H.431a.333.333 0 01-.157-.627L8.278 9.42a.333.333 0 01.49.294z" fill="#525354"></path>
            <path
              pid="3"
              d="M5.9 3.5l-.53-.555a.5.5 0 01.725-.69l1.333 1.4a.5.5 0 010 .69l-1.333 1.4a.5.5 0 11-.724-.69l.527-.554A4.5 4.5 0 001.5 9v1a.5.5 0 01-1 0V9a5.5 5.5 0 015.4-5.5z"
              fill="#00B266"
            ></path>
          </g></svg
        ><span>右转90°</span>
      </button>
      <button @click="rotate(2)" class="btn-rotate">
        <svg version="1.1" viewBox="0 0 16 16" class="svg-icon svg-fill">
          <g fill-rule="nonzero" fill="none">
            <path pid="0" fill="#C0F0DB" d="M7 14.755V2.553L2.137 14.755z"></path>
            <path pid="1" d="M12.898 14H9.667V5.257L12.897 14zm1.27.551L9.312 1.415a.333.333 0 00-.646.115v13.137c0 .184.149.333.333.333h4.855a.333.333 0 00.312-.449z" fill="#525354"></path>
            <path pid="2" d="M6.333 14h-3.23l3.23-8.743V14zm-4.188 1H7c.184 0 .333-.15.333-.333V1.53a.333.333 0 00-.646-.115L1.833 14.55a.333.333 0 00.312.449z" fill="#00B266"></path>
          </g></svg
        ><span>水平翻转</span>
      </button>

      <button @click="rotate(3)" class="btn-rotate">
        <svg data-v-763a258a="" data-v-5905fda0="" version="1.1" viewBox="0 0 16 16" class="svg-icon svg-fill">
          <g fill-rule="nonzero" fill="none">
            <path pid="0" fill="#C0F0DB" d="M1.667 8.94V14L13.75 8.94z"></path>
            <path pid="1" d="M2 3.102v3.231h8.743L2 3.103zm-.551-1.27l13.136 4.855a.333.333 0 01-.115.646H1.333A.333.333 0 011 7V2.145a.333.333 0 01.449-.312z" fill="#525354"></path>
            <path pid="2" d="M10.743 9.667H2v3.23l8.743-3.23zM1 13.855V9c0-.184.15-.333.333-.333H14.47a.333.333 0 01.115.646L1.45 14.167A.333.333 0 011 13.855z" fill="#00B266"></path>
          </g></svg
        ><span>垂直翻转</span>
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Mixins } from "vue-property-decorator";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";
import { FlipType } from "@/common/constants/flipTypes";

@Component({
  components: {
    ComponentPropertiesEditableChecker,
  },
})
export default class FlipEditor extends Mixins(GetComponentPropertiesDisabledMixin) {
  get componentId() {
    return this.componentIds[0];
  }
  rotate(e: any) {
    switch (e) {
      case 0:
        {
          let culAngle = this.$store.state.componentMap[this.componentId].properties.angle || 0;
          culAngle += 90;
          const rate = parseInt(culAngle / 360 + "");
          if (rate > 0) {
            culAngle = culAngle - rate * 360;
          }

          this.$store.dispatch("updateComponentsProperties", {
            ids: this.componentIds,
            newProperties: {
              angle: culAngle,
            },
          });
        }
        break;
      case 1:
        {
          let culAngle = this.$store.state.componentMap[this.componentId].properties.angle || 0;
          culAngle -= 90;
          if (culAngle < 0) {
            const rate = Math.ceil(Math.abs(culAngle) / 360);
            culAngle = rate * 360 + culAngle;
          }
          this.$store.dispatch("updateComponentsProperties", {
            ids: this.componentIds,
            newProperties: {
              angle: culAngle,
            },
          });
        }
        break;
      case 2:
        {
          // let flipx = this.$store.state.componentMap[this.componentId].properties.flipX || false;
          // flipx = Boolean(!flipx);
          // this.$store.dispatch("updateComponentsProperties", {
          //   ids: this.componentIds,
          //   newProperties: {
          //     flipX: flipx,
          //   },
          // });
          let flip: FlipType = this.$store.state.componentMap[this.componentId].properties.flipType || 0;
          switch (flip) {
            case FlipType.FNORMAL:
              flip = FlipType.FLIPX;
              break;
            case FlipType.FLIPX:
              flip = FlipType.FNORMAL;
              break;
            case FlipType.FLIPY:
              flip = FlipType.FLIPALL;
              break;
            case FlipType.FLIPALL:
              flip = FlipType.FLIPY;
              break;
            default:
              break;
          }
          this.$store.dispatch("updateComponentsProperties", {
            ids: this.componentIds,
            newProperties: {
              flipType: flip,
            },
          });
        }

        break;
      case 3:
        {
          // let flipy = this.$store.state.componentMap[this.componentId].properties.flipY || false;
          // flipy = Boolean(!flipy);
          // this.$store.dispatch("updateComponentsProperties", {
          //   ids: this.componentIds,
          //   newProperties: {
          //     flipY: flipy,
          //   },
          // });
          let flip: FlipType = this.$store.state.componentMap[this.componentId].properties.flipType || 0;

          switch (flip) {
            case FlipType.FNORMAL:
              flip = FlipType.FLIPY;
              break;
            case FlipType.FLIPX:
              flip = FlipType.FLIPALL;
              break;
            case FlipType.FLIPY:
              flip = FlipType.FNORMAL;
              break;
            case FlipType.FLIPALL:
              flip = FlipType.FLIPX;
              break;
            default:
              break;
          }
          this.$store.dispatch("updateComponentsProperties", {
            ids: this.componentIds,
            newProperties: {
              flipType: flip,
            },
          });
        }
        break;
      default:
        break;
    }
  }
}
</script>

<style scoped lang="less">
.btn-rotate {
  border: none;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-grow: 1;
  font-size: 12px;
  color: #757678;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.title {
  display: flex;
  font-size: 14px;
  font-weight: 500;
  margin-top: 10px;
}
.svg-fill {
  fill: currentColor;
  stroke: none;
  margin-bottom: 5px;
  height: 20px;
}
</style>
