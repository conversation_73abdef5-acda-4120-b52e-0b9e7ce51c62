<template>
  <el-collapse-item title="基础属性" name="base-properties">
    <!-- 组件层级编辑器 -->
    <div class="flex pd-bt-16">
      <div style="flex: 1">
        <level-editor :componentIds="currentComponentIds" :disabled="getComponentPropertiesDisabled('properties', 'zIndex')" />
      </div>
      <component-properties-editable-checker firstLevelProperty="properties" secondLevelProperty="zIndex" :componentIds="componentIds" />
    </div>

    <div class="flex pd-bt-16">
      <div class="flex" style="margin-right: 16px">
        <ya-switch label="显示" property="active" :default-value="true" />
      </div>

      <div v-if="isShowColorPicker" class="flex">
        <zybColorPicker class="color-select" property="color" label="颜色" :componentIds="currentComponentIds"></zybColorPicker>
      </div>
    </div>

    <!-- 位置编辑器 -->
    <div class="pd-bt-16">
      <position-editor :componentIds="currentComponentIds" />
    </div>

    <div class="flex pd-bt-16">
      <div class="flex" style="margin-right: 16px">
        <ya-input-number label="宽度" property="width" type="number" />
      </div>

      <div class="flex">
        <ya-input-number label="高度" property="height" type="number" />
      </div>
    </div>

    <div class="flex">
      <div style="flex: 1">
        <ya-slider-and-input
          label="旋转角度"
          property="angle"
          :defaultValue="0"
          :options="{
            min: 0,
            max: 360,
          }"
        />
      </div>
    </div>

    <div class="flex">
      <div v-show="!isGroup" style="flex: 1">
        <ya-slider-and-input
          label="透明度"
          property="opacity"
          :defaultValue="255"
          :options="{
            min: 1,
            max: 255,
          }"
        />
      </div>
    </div>
    <lock-editor />
  </el-collapse-item>
</template>

<script lang="ts">
import { Component, Prop, Mixins } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import YaInput from "../EditComponents/Input/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import YaColorPicker from "../EditComponents/ColorPicker/index.vue";
import YaSlider from "../EditComponents/Slider/index.vue";
import YaSliderAndInput from "../EditComponents/SliderAndInput/index.vue";
import LevelEditor from "./LevelEditor/index.vue";
import PositionEditor from "./PositionEditor/index.vue";
import GetComponentPropertiesDisabledMixin from "@/pages/index/components/EditArea/getComponentPropertiesDisabledMixin";
import ComponentPropertiesEditableChecker from "@/pages/index/components/EditArea/ComponentPropertiesEditableChecker.vue";
import { ComponentTypes } from "@/common/constants/componentTypes";
import zybColorPicker from "@/components/zyb-color-picker/index.vue";
import LockEditor from "../EditComponents/LockEditor/index.vue";
// 不展示颜色基本属性选择器的组件类型
const NOT_SHOW_COLOR_PICKER_COMPONENT_TYPES = [
  ComponentTypes.SPINE,
  ComponentTypes.CUT_SHAPE,
  ComponentTypes.GROUP,
  ComponentTypes.SPECIAL_COMPONENT,
  ComponentTypes.SVG_SHAPE,
  ComponentTypes.RICH_EXT_SPRITE
];

@Component({
  components: {
    ExtraEditor,
    LevelEditor,
    PositionEditor,
    YaInput,
    YaInputNumber,
    YaSwitch,
    YaColorPicker,
    YaSlider,
    YaSliderAndInput,
    ComponentPropertiesEditableChecker,
    zybColorPicker,
    LockEditor,
  },
})
export default class LabelComponentEditor extends Mixins(GetComponentPropertiesDisabledMixin) {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get isGroup() {
    let isGroup = false;
    this.currentComponents.forEach(item => {
      if (item.type === ComponentTypes.GROUP) {
        isGroup = true;
      }
    });
    return isGroup;
  }

  /**
   * @description 某些组件没有 color 属性，所以不展示在基础属性当中
   */
  get isShowColorPicker(): boolean {
    const id = this.componentIds.find(id => {
      return NOT_SHOW_COLOR_PICKER_COMPONENT_TYPES.find(type => type === this.$store.state.componentMap[id].type);
    });

    return id === undefined;
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
}

.pd-bt-16 {
  padding-bottom: 16px;
}

.title {
  text-align: left;
  font-weight: bold;
  font-size: 12px;
  flex-shrink: 0;
  padding-bottom: 10px;
}
</style>
