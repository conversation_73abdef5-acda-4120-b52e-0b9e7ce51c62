<template>
  <div>
    <BaseFormTemplate
    :currentComponents="currentComponents"
    :formConfigs="formConfigs"
    :formData="formData"
    ></BaseFormTemplate>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseFormTemplate from "../BaseFormTemplate/index.vue";
import { formConfigs } from './FormModel';

@Component({
  components: {
    BaseFormTemplate
  },
})
export default class CutPictureComponentEditor  extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get formData() {
    return this.currentComponents[0].properties;
  }

  formConfigs = formConfigs;
}
</script>

<style lang="less">
.cut-triangle-item {
  .el-radio-group {
    margin-top: 28px;
    margin-left: -140px;
  }
}
</style>
