import store from "@/pages/index/store";

export const defaultFormData = {
  cutLineNum: 4,
  cutLineColor: '#F16240',
  cutGraphicNum: 2,
  isCutTriangle: false,
  bottomGraphic: 1,
  bottomTextureUrl: ''
}

const formItemConfigs = {
  cutGraphicNum: {
    config: {
      key: 'cutGraphicNum',
      type: 'input-number',
      label: '需分割图形数量',
      min: 2,
      max: 10,
      step: 1,
      'step-strictly': true,
      required: false,
      defaultValue: 4,
      onChange: (id: number, val: number) => {
        // row line
        console.log('onChange', val);
        const { componentMap } = store.state;
        const oldVal = Object.keys(componentMap).filter((key: string) => {
          const { tag, type } = componentMap[key];
          return tag === 'cutGraphic' && type === 'sprite'
        }).length;
        console.log('onChange',id, val, oldVal);

        if(oldVal === val) {
          return;
        }

        if(oldVal > val) {
          const cutGraphicIds: string[] = [];
          Object.keys(componentMap).forEach((key: string) => {
            const { tag, type } = componentMap[key];
            if(tag === 'cutGraphic' && type === 'sprite') {
              cutGraphicIds.push(key)
            }
          })
          store.dispatch("removeComponents", cutGraphicIds.slice(val-oldVal));
          // 删除组件
        } else {
          // 新增组件 个数不对
          const delta = val - oldVal;
          for (let index = 0; index < delta; index++) {
            const component: Omit<SpriteComponent, "id"> = {
              tag: "cutGraphic",
              type: "sprite",
              dragable: true,
              deletable: false,
              canCombine: false,
              properties: {
                active: true,
                width: 240,
                height: 200,
                x: 0 + (oldVal + index + 1) * 20,
                y: 0 - (oldVal + index + 1) * 20,
                angle: 0,
                texture: 'https://jiaoyanbos.cdnjtzy.com/cw_12e64254aa041f3afedbaf96c3c198ee.png',
              },
              extra: { tag: "cutGraphic" },
            };
            store.dispatch("addComponentNoFocus", component);
          }
        }
      }
    },
    rules:[]
  },
  cutLineNum: {
    config: {
      key: 'cutLineNum',
      type: 'input-number',
      label: '分割线数量',
      min: 1,
      max: 10,
      step: 1,
      'step-strictly': true,
      required: false,
      defaultValue: 4,
      onChange: (id: number, val: number) => {
        // row line
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['cutLineNum']: val,
          },
        });
      }
    },
    rules:[]
  },
  cutLineColor: {
    config: {
      key: 'cutLineColor',
      type: 'color',
      label: '分割线的颜色',
      required: true,
      defaultValue: '#F16240',
      onChange: (id: number, val: number) => {
        // row line
        console.log('onChange', val);
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['cutLineColor']: val,
          },
        });
      }
    },
  },
  isCutTriangle: {
    config: {
      key: 'isCutTriangle',
      type: 'radio',
      label: '判断分割线是否交于一点',
      labelTips: `是：判断分割线和底板图的交点，当某个交点处的分割线数量=1，判断为错误；<br/>判断分割线和底板图的交点，当每个交点处的分割线数量≥2，判断为正确<br/>
      否：无需判断分割线，在底板图的边框上，是否会相交`,
      labelTipsLeft: '135px',
      customClass: 'cut-triangle-item',
      value: true,
      defaultValue: false,
      options: [
        { label: '是', value: true },
        { label: '否', value: false },
      ],
      onChange: (id: number, value: boolean) => {
        // row line
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['isCutTriangle']: value
          }
        });
      }
    },
    rules:[]
  },
  bottomGraphic: {
    config: {
      key: 'bottomGraphic',
      type: 'radio',
      label: '底板形状',
      labelTips: '矩形：按照上传的底板图UI，完整呈现为底板区;<br/>正/倒三角形：以上传的图形，顶部/底部线中间为中点，形成正/倒三角形',
      value: true,
      defaultValue: 0,
      options: [
        { label: '矩形', value: 0 },
        { label: '正三角形', value: 1 },
        { label: '倒三角形', value: 2 },
      ],
      onChange: (id: number, value: boolean) => {
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['bottomGraphic']: value
          }
        });
      }
    },
    rules:[]
  },
  bottomTextureUrl: {
    config: {
      key: 'bottomTextureUrl',
      type: 'image',
      label: '底板区UI(最大768*520px)',
      required: true,
      defaultValue: '',
      showDelete: false,
      options: [],
      onChange: (id: number, value: string) => {
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['bottomTextureUrl']: value,
          },
        });
      }
    },
    rules:[]
  },
}

export const formConfigs = Object.values(formItemConfigs).map(item => item.config);
