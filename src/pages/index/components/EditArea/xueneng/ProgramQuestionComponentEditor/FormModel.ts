import store from "@/pages/index/store"

const gridOptions = [
  { label: '空', value: -1 },
  { label: '汽车', value: 0, limit:1 },
  { label: '障碍物', value: 1 },
  { label: '小鱼干', value: 2 },
  { label: '宝箱', value: 3 },
  { label: '空宝箱', value: 4 },

]

const cmdOptions = [
  { label: '前进', value: 0 },
  { label: '左转', value: 1 },
  { label: '右转', value: 2 },
  { label: '拿取', value: 3 },
  { label: '循环', value: 4 },
  { label: '问号', value: 5 },
  { label: 'if', value: 6 },
  { label: '鱼干', value: 7 },
]

const selectOptions = [
  { label: '上', value: 0 },
  { label: '下', value: 1 },
  { label: '左', value: 2 },
  { label: '右', value: 3 },
]

const requiredImageKeyList = ['carImg', 'barrierImg', 'fishImg', 'chestImg', 'forwardIcon', 'turnLeftIcon', 'turnRightIcon', 'getIcon', 'loopIcon', 'questionIcon', 'ifIcon', 'fishIcon' ]

const requiredImageLableList = ['汽车图片', '障碍物图片', '鱼干图片', '宝箱图片', '前进icon', '左转icon', '右转icon', '获取icon', '循环icon', '问号icon', 'if Icon', '鱼干icon']

// 新增的图片为非必填项
const imageKeyList = ['showBgImg', 'showGridImg', 'willDargBgImg', 'willDargGridImg', 'willDargTagImg', 'cmdBgImg']

const imageLableList = ['演示区背景', '演示区格子', '吸附区背景', '吸附区格子', '吸附区标签背景', '指令区背景']

export const formConfigs = [
  {
    key: 'type',
    type: 'radio',
    label: '题目类型',
    defaultValue: '5*5',
    options: [
      { label: '3*3', value: '3*3' },
      { label: '4*4', value: '4*4' },
      { label: '5*5', value: '5*5' }
    ],
    onChange: (id: number, value: string) => {
      // row line
      console.log('PQ:::onChange', value.split('*'));
      const [line, row] = value.split('*');
      console.log('PQ:::line,row', line, row);
      const grid = Array.from({ length: Number(line) }, () => Array.from({ length: Number(row) }, () => (-1)));
      // 产品（liulijia_v）要求 5*5时有默认值 其他的情况是空的
      if (Number(line) === 5) {
        grid[2] = [
          0,
          1,
          2,
          3,
          4
        ]
      }
      store.commit("updateComponentProperties", {
        id,
        newProperties: {
          ['line']: Number(line),
          ['row']: Number(row),
          ['grid']: grid
        },
      });
      (formConfigs[1] as any).row = Number(row);
      (formConfigs[1] as any).line = Number(line);
    }
  },
  {
    key:'grid',
    type:'TwoDTable',
    label:'演示区控制',
    labelTips:'必须有且只有1个「汽车」，至少有1个「鱼干」/「宝箱」/「空宝箱」',
    cellWidth: '60px',
    celleHeight: '40px',
    tableCellOptions:gridOptions,
    row:5,
    line:5,
  }, 
  {
    key: 'cmds',
    type:'checkbox',
    label:'待拖拽指令区',
    options: cmdOptions
  },
  {
    title:'指令输入区',
    labelTips:'最少有1行最多有3行，if：只能有1行，loop：最多有2行',
    type: 'inputGroup',
    min:1,
    max:3,
    inputKey:'mainCmdRows',
    label:'main',
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'mainCmdCols',
    label: '',
    subLabel: '列数',
    min: 1,
    max: 6,
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'loopCmdRows',
    checkboxKey: 'loopCmdVisible',
    label:'loop',
    min:1,
    max:2,
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'loopCmdCols',
    label: '',
    subLabel: '列数',
    min: 1,
    max: 6,
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'ifCmdRows',
    checkboxKey: 'ifCmdVisible',
    label:'if',
    min:1,
    max:1,
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'ifCmdCols',
    subLabel: '列数',
    label:'',
    min:1,
    max:6,
    'step-strictly': true,
    step: 1,
  },
  {
    type: 'inputGroup',
    inputKey: 'mainCmdPosX',
    label: 'main位置坐标',
    subLabel: 'X',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'mainCmdPosY',
    label: '',
    min: 0,
    subLabel: 'Y',
  },
  {
    type: 'inputGroup',
    inputKey: 'loopCmdPosX',
    label: 'loop位置坐标',
    subLabel: 'X',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'loopCmdPosY',
    label: '',
    subLabel: 'Y',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'ifCmdPosX',
    label: 'if位置坐标',
    subLabel: 'X',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'ifCmdPosY',
    label: '',
    subLabel: 'Y',
    min: 0,
  },

  {
    type: 'inputGroup',
    inputKey: 'cmdListPosX',
    label: '指令区位置坐标',
    subLabel: 'X',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'cmdListPosY',
    label: '',
    subLabel: 'Y',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'showCmdPosX',
    label: '演示区位置坐标',
    subLabel: 'X',
    min: 0,
  },
  {
    type: 'inputGroup',
    inputKey: 'showCmdPosY',
    label: '',
    subLabel: 'Y',
    min: 0,
  },
  {
    type:'select',
    key:'startDirection',
    label:'车头初始位置',
    labelTips:'上传的图片，需车头的初始位置朝向右侧',
    labelWidth:'100px',
    labelTipsLeft:'80px',
    options:selectOptions,
  },
  {
    key: 'dragAudioUrl',
    type: 'audio',
    label: '拖拽音效',
    required: true,
    defaultValue: '',
    showDelete: false,
    options: []
  },
  {
    key: 'pickAudioUrl',
    type: 'audio',
    label: '拿取音效',
    required: true,
    defaultValue: '',
    showDelete: false,
    options: []
  },
  ...imageKeyList.map((key:any, i:number) => {
    return {
        key: key,
        type: 'image',
        label: imageLableList[i],
        defaultValue: '',
        options: []
    }
  }),
  ...requiredImageKeyList.map((key: any, i: number) => {
    return {
      key: key,
      type: 'image',
      label: requiredImageLableList[i],
      required: true,
      defaultValue: '',
      showDelete: false,
      options: []
    }
  })
]
