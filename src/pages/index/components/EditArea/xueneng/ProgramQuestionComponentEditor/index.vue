<template>
  <div>
    <BaseFormTemplate :currentComponents="currentComponents" :formConfigs="formConfigs" :formData="formData"></BaseFormTemplate>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import BaseFormTemplate from "../BaseFormTemplate/index.vue";
import { formConfigs } from "./FormModel";

@Component({
  components: {
    BaseFormTemplate,
  },
})
export default class ProgramQuestionComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get formData() {
    return this.currentComponents[0].properties;
  }

  formConfigs = formConfigs;

  @Watch("formData.line", { immediate: true })
  lineWatcher(val: number) {
    console.log('formData.line val', val);
    if(!val) return;
    this.formConfigs.forEach(conf => {
      if (["grid"].includes(conf.key)) {
        (conf as any).line = Number(val);
        (conf as any).row = Number(this.formData.row);
      }
    });
  }
}
</script>

<style scoped lang="less">
.match-board-panel {
  text-align: left !important;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
