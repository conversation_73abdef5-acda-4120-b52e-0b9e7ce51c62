<template>
  <el-form-item
  :label="config.label"
  :label-width="config.labelWidth"
  :required="config.required">
  <el-tooltip
    class="item"
    effect="dark"
    :content="config.labelTips"
    placement="top"
    v-if="config.labelTips"
  >
    <i class="el-icon-question button-tips"></i>
  </el-tooltip>
  <div class="table-cell-wrapper" v-if="form[config.key].length">
    <el-row class="table-row" v-for="(item, i) in config.line" :key="item">
      <el-col :span="24/config.row" v-for="(sitem, j) in config.row" :key="`${item}-${sitem}`">
        <div :style="{ height: config.cellHeight }">
          <el-select
            size="small"
            placeholder=""
            :value="form[config.key][i][j]"
            @change="(val) => handleChange(val, i, j)">
            <el-option
              v-for="opt in config.tableCellOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value">
            </el-option>
          </el-select>
        </div>
      </el-col>
    </el-row>
  </div>
  </el-form-item>
</template>

<script lang="ts">
import { cloneDeep } from "lodash-es";
import { Component, Vue, Prop } from "vue-property-decorator";


@Component
export default class XNTwoDTable extends Vue {
[x: string]: any;
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;

  get val(): number {
    return this.form[this.config.key];
  }

  set val(val) {
    this.form[this.config.key] = val;
  }

  handleChange(val: any, x: number, y:number) {
    const cloneVal = cloneDeep(this.form[this.config.key]);

    const cloneOpt = cloneDeep(this.config.tableCellOptions)

    cloneVal.forEach((row:number[]) => {
      row.forEach((col:number) => {
        const opt = cloneOpt.find((opt:{label:string,value:number}) => opt.value === col)
        opt.times = (opt.times || 0) + 1
      })
    })

    const curOpt = cloneOpt.find((opt:{label:string,value:number}) => opt.value === val)

    if(curOpt.limit && curOpt.times >= curOpt.limit){
      return
    }
    
    cloneVal[x][y] = val;
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: cloneVal,
      },
    });
    // this.$nextTick(() => {
    //   this.config.onChange(this.componentId, this.form[this.config.key]);
    // })
  }
}
</script>

<style scoped lang="less">
.table-cell-wrapper {
  border-left: 1px solid transparent;
  border-top: 1px solid transparent;
  margin-top: 28px;
  .table-row{
    display: flex;
    flex-wrap: nowrap;
  }
  .el-col {
    border-right: 1px solid transparent;
    border-bottom: 1px solid transparent;
    /deep/ .el-input__inner{
      padding-right:20px!important;
    }
  } 
}
.button-tips {
  position: absolute;
  left: 68px;
  top: 8px;
}
</style>
