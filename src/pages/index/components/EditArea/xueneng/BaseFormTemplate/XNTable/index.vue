<template>
  <el-form-item
  :label="config.label"
  :label-width="config.labelWidth"
  :required="config.required">
  <el-tooltip
    class="item"
    effect="dark"
    :content="config.labelTips"
    placement="top"
    v-if="config.labelTips"
  >
    <div slot="content" v-html="config.labelTips"></div>
    <i class="el-icon-question button-tips"></i>
  </el-tooltip>
  <div class="table-cell-wrapper">
    <el-row v-for="item in config.line" :key="item">
      <el-col :span="24/config.row" v-for="sitem in config.row" :key="`${item}-${sitem}`">
        <div :style="{ height: config.cellHeight }">
          <!-- {{item}}-{{sitem}} {{ (item-1)*row + sitem }} -->
          <el-select
            size="small"
            placeholder=""
            :value="form[config.key][(item-1)*config.row + sitem - 1]"
            @change="(val) => handleChange(val, (item-1)*config.row + sitem - 1)">
            <el-option
              v-for="opt in config.tableCellOptions"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value">
            </el-option>
          </el-select>
        </div>
      </el-col>
    </el-row>
  </div>
  </el-form-item>
</template>

<script lang="ts">
import { cloneDeep } from "lodash-es";
import { Component, Vue, Prop } from "vue-property-decorator";


@Component
export default class XNTable extends Vue {
[x: string]: any;
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;

  get val(): number {
    return this.form[this.config.key];
  }

  set val(val) {
    this.form[this.config.key] = val;
  }

  handleChange(val: any, index: number) {
    const cloneVal = cloneDeep(this.form[this.config.key]);
    cloneVal[index] = val;
    console.log('handleChange', val);
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: cloneVal,
      },
    });
  }
}
</script>

<style scoped lang="less">
.table-cell-wrapper {
  border-left: 1px solid transparent;
  border-top: 1px solid transparent;
  margin-top: 28px;
  .el-col {
    border-right: 1px solid transparent;
    border-bottom: 1px solid transparent;
  } 
}
.button-tips {
  position: absolute;
  left: 58px;
  top: 8px;
}
</style>
