<template>
  <zybColorPicker
    class="color-select"
    :property="config.key"
    :label="config.label"
    :clearable="true"
    :componentIds="[componentId]"></zybColorPicker>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import zybColorPicker from "@/components/zyb-color-picker/index.vue";

@Component({
  components: {
    zybColorPicker
  },
})
export default class XNColorSelect extends Vue {
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;
}
</script>

<style lang="less" scoped>
.color-select {
  display: flex;
  align-items: center;
  /deep/ .color-label {
    margin-right: 10px;
    font-size: 12px;
    color: #777;
    line-height: 28px;
  }
  margin-bottom: 18px;
}
</style>
