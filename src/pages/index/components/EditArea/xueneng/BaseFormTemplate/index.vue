<template>
  <div>
    <el-collapse-item
      title="题目属性"
      name="stage-properties"
      class="xn-base-form"
      :class="customClass"
    >
      <el-form :label-position="labelPosition" size="small">
        <div
          :key="item.key"
          v-for="(item) in formConfigs"
        >
        <template v-if="item.type === 'custom'" >
          <slot name="custom" :data="{componentId, config: item, form: formData }">custom</slot>
        </template>
        <template v-else>
          <component
            :is="findComp(item.type)"
            :componentId="componentId"
            :config="item"
            :form="formData"
          />
        </template>
      </div>
      </el-form>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraImageSelect from "./XNImageSelect/index.vue";
import ExtraAudioSelect from "./XNAudioSelect/index.vue";
import ExtraRadio from './XNRadio/index.vue';
import ExtraTable from "./XNTable/index.vue";
import XNColorSelect from "./XNColorSelect/index.vue";
import ExtraInputNumber from "./XNInputNumber/index.vue";
import TwoDTable from "./XNTwoDTable/index.vue";
import ExtraCheckbox from './XNCheckbox/index.vue'
import InputGroup from './XNInputGroup/index.vue'
import ExtraSelect from "./XNSelect/index.vue";

@Component({
  components: {
    ExtraRadio,
    ExtraTable,
    ExtraImageSelect,
    ExtraAudioSelect,
    XNColorSelect,
    ExtraInputNumber,
    TwoDTable,
    ExtraCheckbox,
    InputGroup,
    ExtraSelect
  },
})
export default class BaseFormTemplate extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  @Prop({})
  customClass!: string;

  @Prop({
    required: true,
  })
  formConfigs!: any[];

  @Prop({
    required: true,
  })
  formData!: any;

  @Prop({
    default: 'left'
  })
  labelPosition!: 'left' | 'top' | 'right';

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  findComp(type: any) {
    const compMap = {
      ['radio']: ExtraRadio,
      ['table']: ExtraTable,
      ['image']: ExtraImageSelect,
      ['audio']: ExtraAudioSelect,
      ['input-number']: ExtraInputNumber,
      ['color']: XNColorSelect,
      ['TwoDTable']: TwoDTable,
      ['checkbox']:ExtraCheckbox,
      ['inputGroup']: InputGroup,
      ['select']: ExtraSelect
    };
    return compMap[type];
  }
}
</script>

<style scoped lang="less">
.xn-base-form {
  text-align: left !important;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
