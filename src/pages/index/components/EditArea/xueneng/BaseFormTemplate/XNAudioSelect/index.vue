<template>
  <el-form-item :label="config.label" :label-width="config.labelWidth">
    <AudioSelect :value.sync="v" label="" :property="config.key" defaultValue="" />
  </el-form-item>
</template>
<!-- :property="config.key" -->

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";

@Component({
  components: {
    AudioSelect,
  },
})
export default class XNImageSelect extends Vue {
  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    required: true,
  })
  form!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  get v() {
    return this.form[this.config.key];
  }

  set v(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: val,
      },
    });
  }

  get stageSafeWidth() {
    return this.$store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return this.$store.state.stageData.safeHeight;
  }

  @Watch("val")
  srcWacher(src: string) {
    this.config.onChange && this.config.onChange(this.componentId, src);
  }
}
</script>
