<template>
  <div class="input-group-box">
    <el-tooltip class="item" effect="dark" :content="config.labelTips" placement="top" v-if="config.labelTips">
      <i class="el-icon-question button-tips"></i>
    </el-tooltip>
    <el-form-item :label="config.title">
      <div class="input-row" :key="config.inputKey">
        <el-checkbox v-if="config.checkboxKey" :label="config.label" v-model="v"></el-checkbox>
        <span v-else>{{ config.label }}</span>
        <div class="input-row-number">
          <span>{{ typeof config.subLabel !== "undefined" ? config.subLabel : "行数" }}</span>
          <el-input-number v-model="c" @blur="() => onBlur()" controls-position="right"  :step="config.step" :step-strictly="config['step-strictly']" :min="config.min" :max="config.max"></el-input-number>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class ExtraInputNumber extends Vue {
  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    required: true,
  })
  form!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  get v(): number {
    return this.form[this.config.checkboxKey];
  }

  set v(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.checkboxKey]: val,
      },
    });
  }

  get c(): number {
    return this.form[this.config.inputKey];
  }

  set c(val) {
    console.log('xu-c', val);
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.inputKey]: val,
      },
    });
  }

  onBlur(){
    if(!this.c && typeof this.config.min !== 'undefined'){
      this.c = this.config.min
    }
  }
}
</script>

<style scoped lang="less">
/deep/ .el-form-item__label {
  width: 100%;
}

/deep/ .el-form-item__content {
  display: inline-block;
  width: 100%;
}

.input-row {
  display: flex;
  justify-content: space-between;
  .input-row-number {
    span {
      margin-right: 12px;
    }
  }
}
.input-group-box {
  position: relative;
  .button-tips {
    position: absolute;
    left: 68px;
    top: 8px;
  }
}
</style>
