<template>
  <el-form-item
  :label="config.label"
  :label-width="config.labelWidth"
  class="xn-radio-item"
  :class="config.customClass"
  :required="config.required">
    <el-tooltip
      class="item"
      effect="dark"
      :content="config.labelTips"
      placement="top"
      v-if="config.labelTips"
      :style="{ left: config.labelTipsLeft || '58px' }"
    >
    <div slot="content" v-html="config.labelTips"></div>
      <i class="el-icon-question button-tips"></i>
    </el-tooltip>
      <el-radio-group
        v-model="val"
        @input="handleInput">
        <el-radio
          v-for="opt in config.options"
          :key="opt.value"
          :label="opt.value"
          >{{ opt.label }}</el-radio>
      </el-radio-group>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";


@Component
export default class XNRadio extends Vue {
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;

  get val(): number {
    return typeof this.form[this.config.key] !== 'undefined' ? this.form[this.config.key] : this.config.defaultValue;
  }

  set val(val) {
    // this.form[this.config.key] = val;
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: val,
      },
    });
  }

  handleInput(val: any) {
    console.log('handleInput', val);
    this.config.onChange && this.config.onChange(this.componentId, val);
  }
}
</script>

<style scoped lang="less">
.button-tips {
  position: absolute;
  left: 58px;
  top: 8px;
}
</style>
