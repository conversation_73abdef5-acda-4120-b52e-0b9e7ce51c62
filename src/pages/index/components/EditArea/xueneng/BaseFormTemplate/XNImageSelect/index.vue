<template>
  <el-form-item :label="config.label" :label-width="config.labelWidth">
    <image-select :src.sync="v" :is-show-delete-btn="config.showDelete" :max-width="stageSafeWidth" :max-height="stageSafeHeight" class="xn-image-select" />
  </el-form-item>
</template>
<!-- :property="config.key" -->

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";

@Component({
  components: {
    ImageSelect,
  },
})
export default class XNImageSelect extends Vue {
  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    required: true,
  })
  form!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  get v() {
    return this.form[this.config.key];
  }

  set v(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: val,
      },
    });
  }

  get stageSafeWidth() {
    return this.$store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return this.$store.state.stageData.safeHeight;
  }

  @Watch("val")
  srcWacher(src: string) {
    this.config.onChange && this.config.onChange(this.componentId, src);
  }
}
</script>

<style lang="less">
.xn-image-select {
  .image-container .image {
    max-width: 160px !important;
  }
  .edit-btn-container {
    width: 160px !important;
    justify-content: space-around !important;
  }
  .image-container {
    width: 160px !important;
    display: flex;
    justify-content: center;
  }
}
</style>
