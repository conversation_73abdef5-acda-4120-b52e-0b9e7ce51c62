<template>
    <el-form-item
    :label="config.label"
    class="xn-select-item"
    :class="config.customClass"
    :required="config.required">
    <template #label>
      <span>{{ config.label }}</span>
      <el-tooltip
        class="item"
        effect="dark"
        :content="config.labelTips"
        placement="top"
        v-if="config.labelTips"
        :style="{ left: config.labelTipsLeft || '58px' }"
      >
      <div slot="content" v-html="config.labelTips"></div>
        <i class="el-icon-question button-tips"></i>
      </el-tooltip>
    </template>
        <el-select
          v-model="val"
          @input="handleInput">
          <el-option
            v-for="opt in config.options"
            :key="opt.value"
            :label="opt.label"
            :value="opt.value"
            ></el-option>
        </el-select>
    </el-form-item>
  </template>
  
  <script lang="ts">
  import { Component, Vue, Prop } from "vue-property-decorator";
  
  
  @Component
  export default class XNRadio extends Vue {
    @Prop({
      required: true
    })
    config!: any;
  
    @Prop({
      required: true
    })
    form!: any;
  
    @Prop({
      required: true
    })
    componentId!: number;
  
    get val(): number {
      return this.form[this.config.key];
    }
  
    set val(val) {
      // this.form[this.config.key] = val;
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          [this.config.key]: val,
        },
      });
    }
  
    handleInput(val: any) {
      console.log('handleInput', val);
      if(this.config.onChange){
        this.config.onChange(this.componentId, val);
      }
    }
  }
  </script>
  
  <style scoped lang="less">
  .xn-select-item{    /deep/ .el-form-item__label {
    position: relative;
    z-index: 1;
  }
    .button-tips {
      color:#333;
      margin-left:3px;
  }
  }

  </style>
  