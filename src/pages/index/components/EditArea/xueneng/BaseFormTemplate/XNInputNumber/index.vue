<template>
  <el-form-item
  :label="config.label"
  :label-width="config.labelWidth"
  :required="config.required">
    <el-input
      v-model="v" 
      size="small"
      type="number"
      :min="config.min"
      :max="config.max"
      :step="config.step"
      :style="{ width: config.width ?  config.width  : '112px' }"
      :step-strictly="config['step-strictly']"
      @change="handleChange"
    />
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";


@Component
export default class ExtraInputNumber extends Vue {
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;

  get v() {
    return this.form[this.config.key];
  }

  set v(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: val,
      },
    });
  }

  handleChange(val: any) {
    console.log('handleInput', val, this.v);
    let temp = val;
    const { max, min } = this.config;
    if(max && val > max) temp = max;
    if(min && val < min) temp = min;
    if(val !== temp) {
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          [this.config.key]: temp,
        },
      });
    }
    this.config.onChange && this.config.onChange(this.componentId, temp);
  }
}
</script>

<style scoped lang="less">

</style>
