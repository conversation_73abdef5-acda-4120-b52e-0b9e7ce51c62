<template>
  <el-form-item
  :label="config.label"
  :label-width="config.labelWidth"
  :required="config.required">
    <div class="discs-wrapper">
      <div class="discs-item" v-for="(disc, index) in cloneDiscs" :key="index">
        <el-input
          v-model="disc.number"
          size="small"
          type="number"
          :max="max"
          :min="1"
          :style="{ width: config.width ?  config.width  : '112px' }"
          @change="val => numberChange(val, index)"
        />
          <color-picker
            ek-sign="bgcolor"
            class="color-select"
            :value="disc.color"
            :clearable="true"
            @change="val => colorChange(val, index)"
            ></color-picker>
        <i
          class="el-icon-plus operation-icon"
          :class="{ disabled: form[config.key].length >= max }"
          @click.prevent="changeLength('add', index)"
        ></i>
        <i
          class="el-icon-minus operation-icon"
          :class="{ disabled: form[config.key].length < 2 }"
          @click.prevent="changeLength('delete', index)"
        ></i>
      </div>
    </div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import zybColorPicker from "@/components/zyb-color-picker/index.vue";
import color from "@/components/zyb-color-picker/ColorPicker.vue";
import { toHexString } from "@/components/zyb-color-picker/colorPipette/utils";
import cloneDeep from "lodash-es/cloneDeep";

@Component({
  components: {
    zybColorPicker,
    colorPicker: color,
  },
})
export default class DiscsFormItem extends Vue {
  @Prop({
    required: true
  })
  config!: any;

  @Prop({
    required: true
  })
  form!: any;

  @Prop({
    required: true
  })
  componentId!: number;

  get max() {
    // 初代汉诺塔，最多支持4个圆盘； 二代最多支持8个
    const { tempVersion } = this.$store.state.template;
    if(tempVersion) return 8;
    return 4;
  }

  cloneDiscs = cloneDeep(this.form.discs);

  @Watch('formData.discs', { immediate: true })
  discsWatcher(val: number) {
    if(JSON.stringify(val) !== JSON.stringify(this.cloneDiscs)) {
      this.cloneDiscs = cloneDeep(this.form.discs);
    }
  }

  handleUpdate() {
    if(JSON.stringify(this.form.discs) === JSON.stringify(this.cloneDiscs)) return;
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.config.key]: cloneDeep(this.cloneDiscs),
      },
    });
  }

  changeLength(type: 'add' | 'delete', index: number) {
    if(type == 'add') {
      this.cloneDiscs.splice(index+1, 0, { number: '', color: '#B45AB4' })
    } else {
      this.cloneDiscs.splice(index, 1)
    }
    this.handleUpdate();
  }
  colorChange(val: string, index: number) {
    this.cloneDiscs[index].color = toHexString(val);
    this.handleUpdate();
  }
  numberChange(val: string, index: number) {
    let temp = val;
    const { max = this.max, min = 1 } = this.config;
    if(max && val > max) temp = max;
    if(min && val < min) temp = min;
    if(val !== temp) {
      this.cloneDiscs[index].number = temp;
    }
    this.handleUpdate();
  }
}
</script>

<style lang="less" scoped>
.color-select {
  display: flex;
  align-items: center;
  /deep/ .color-label {
    margin-right: 10px;
    font-size: 12px;
    color: #777;
    line-height: 28px;
  }
  // margin-bottom: 18px;
}
.discs-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.color-select {
  margin-left: 16px;
}
.operation-icon {
  width: 16px;
  height: 16px;
  margin-left: 16px;
  &.el-icon-plus {
      &:before {
        background: rgba(66, 197, 122, 1);
        color: #fff;
        border-radius: 50%;
        border: 2px solid rgba(66, 197, 122, 1);
        cursor: pointer;
      }
    }
    &.el-icon-minus {
      &:before {
        background: rgba(66, 197, 122, 1);
        color: #fff;
        border-radius: 50%;
        border: 2px solid rgba(66, 197, 122, 1);
        cursor: pointer;
      }
    }
    &.disabled {
      cursor: none;
      pointer-events: none;
      &:before {
        border: 2px solid rgba(175, 176, 179, 1) !important;
        background: rgba(175, 176, 179, 1) !important;
        cursor: not-allowed !important;
      }
    }
}
</style>
