<template>
  <div>
    <BaseFormTemplate
    :currentComponents="currentComponents"
    :formConfigs="formConfigs"
    :formData="formData"
    labelPosition="top"
    >
    <template slot="custom" slot-scope="{ data: $data }">
      <DiscsFormItem :componentId="$data.componentId" :config="$data.config" :form="$data.form"></DiscsFormItem> 
    </template>
  </BaseFormTemplate>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseFormTemplate from "../BaseFormTemplate/index.vue";
import DiscsFormItem from "./DiscsFormItem/index.vue";
import { formConfigs } from './FormModel';

@Component({
  components: {
    BaseFormTemplate,
    DiscsFormItem
  },
})

export default class HanoiTowerComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get formData() {
    return this.currentComponents[0].properties;
  }

  formConfigs = formConfigs;
}
</script>

<style scoped lang="less">
.match-board-panel {
  text-align: left !important;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
