import store from "@/pages/index/store";

const formItemConfigs = {
  texture: {
    config: {
      key: 'baseTexture',
      type: 'image',
      label: '底座',
      defaultValue: ''
    },
    rules:[]
  },
  discs: {
    config: {
      key: 'discs',
      type: 'custom',
      label: '圆盘顺序和颜色',
    },
    rules:[]
  },
  answerColunm: {
    config: {
      key: 'answerColunm',
      type: 'checkbox',
      label: '正确答案',
      defaultValue: 'B',
      options: [
        { label: 'B柱', value: 'B' },
        { label: 'C柱', value: 'C' }
      ]
    },
    rules: []
  },
}

export const formConfigs = [
  {
    key: 'baseTexture',
    type: 'image',
    label: '底座',
    defaultValue: '',
    showDelete: false,
  },
  {
    key: 'colunmTexture',
    type: 'image',
    label: '柱子',
    defaultValue: '',
    showDelete: false,
  },
  {
    key: 'colunmActiveTexture',
    type: 'image',
    label: '点亮柱子',
    defaultValue: '',
    showDelete: false,
  },
  {
    key: 'discs',
    type: 'custom',
    label: '圆盘顺序和颜色',
  },
  {
    key: 'answerColunm',
    type: 'checkbox',
    label: '正确答案',
    defaultValue: 'B',
    options: [
      { label: 'B柱', value: 'B' },
      { label: 'C柱', value: 'C' }
    ]
  }
];
