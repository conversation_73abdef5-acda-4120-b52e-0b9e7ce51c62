import store from "@/pages/index/store";

export const defaultFormData = {
  type: JSON.stringify({ line: 3, row: 3 }),
  targetList: {
    "0": "",
    "1": "2",
    "2": "1",
    "3": "3",
    "4": "4",
    "5": "7",
    "6": "空",
    "7": "5",
    "8": "6"
  },
  questionList: {
    "0": "",
    "1": "2",
    "2": "1",
    "3": "3",
    "4": "4",
    "5": "7",
    "6": "空",
    "7": "5",
    "8": "6"
  },
  isShowTarget: true,
  questionListTexture: '',
  cellTexture: '',
}

const lineConfigs = {
  2: {
    questionList: {
      "0": "1",
      "1": "2",
      "2": "空",
      "3": "",
      "4": "空",
      "5": "3",
    },
    targetList: {
      "0": "1",
      "1": "2",
      "2": "3",
      "3": "",
      "4": "空",
      "5": "空",
    },
    tableCellOptions: [
      { label: '0', value: '0' },
      { label: '1', value: '1' },
      { label: '2', value: '2' },
      { label: '3', value: '3' },
      { label: '4', value: '4' },
      { label: '5', value: '5' },
      { label: '6', value: '6' },
      { label: '7', value: '7' },
      { label: '8', value: '8' },
      { label: '9', value: '9' },
      { label: '空格子', value: '' },
      { label: '干扰项', value: '空' },
      { label: '干扰项0', value: '空0' },
      { label: '干扰项1', value: '空1' },
      { label: '干扰项2', value: '空2' },
      { label: '干扰项3', value: '空3' },
      { label: '干扰项4', value: '空4' },
      { label: '干扰项5', value: '空5' },
      { label: '干扰项6', value: '空6' },
      { label: '干扰项7', value: '空7' },
      { label: '干扰项8', value: '空8' },
      { label: '干扰项9', value: '空9' },
    ]
  },
  3: {
    questionList: {
      "0": "1",
      "1": "2",
      "2": "3",
      "3": "4",
      "4": "6",
      "5": "7",
      "6": "",
      "7": "5",
      "8": "8"
    },
    targetList: {
      "0": "1",
      "1": "2",
      "2": "3",
      "3": "4",
      "4": "5",
      "5": "6",
      "6": "7",
      "7": "8",
      "8": ""
    },
    tableCellOptions: [
      { label: '0', value: '0' },
      { label: '1', value: '1' },
      { label: '2', value: '2' },
      { label: '3', value: '3' },
      { label: '4', value: '4' },
      { label: '5', value: '5' },
      { label: '6', value: '6' },
      { label: '7', value: '7' },
      { label: '8', value: '8' },
      { label: '9', value: '9' },
      { label: '空格子', value: '' },
      { label: '干扰项', value: '空' },
      { label: '干扰项0', value: '空0' },
      { label: '干扰项1', value: '空1' },
      { label: '干扰项2', value: '空2' },
      { label: '干扰项3', value: '空3' },
      { label: '干扰项4', value: '空4' },
      { label: '干扰项5', value: '空5' },
      { label: '干扰项6', value: '空6' },
      { label: '干扰项7', value: '空7' },
      { label: '干扰项8', value: '空8' },
      { label: '干扰项9', value: '空9' },
    ]
  }
}

const formItemConfigs = {
  type: {
    config: {
      key: 'type',
      type: 'radio',
      label: '题目类型',
      defaultValue: '{ line: 3, row: 3 }',
      options: [
        { label: '2*3', value: '2*3' },
        { label: '3*3', value: '3*3' }
      ],
      onChange: (id: number, value: string) => {
        // row line
        console.log('onChange', value.split('*'));
        const [ line, row ] = value.split('*');
        // lineConfigs
        store.commit("updateComponentProperties", {
          id,
          newProperties: {
            ['line']: Number(line),
            ['row']: Number(row),
            ['questionList']: lineConfigs[line]['questionList'],
            ['targetList']: lineConfigs[line]['targetList']
          },
        });
        formItemConfigs['questionList'].config.tableCellOptions = lineConfigs[line]['tableCellOptions'];
        formItemConfigs['questionList'].config.row = Number(row);
        formItemConfigs['questionList'].config.line = Number(line);
        formItemConfigs['targetList'].config.row = Number(row);
        formItemConfigs['targetList'].config.line = Number(line);
        formItemConfigs['targetList'].config.tableCellOptions = lineConfigs[line]['tableCellOptions'];
        defaultFormData['targetList'] = (lineConfigs[line]['targetList']as any);
        defaultFormData['questionList'] = (lineConfigs[line]['questionList'] as any)
      }
    },
    rules:[]
  },
  targetList: {
    config: {
      key: 'targetList',
      type: 'table',
      label: '目标图形',
      labelTips: `「干扰格子」代表格子可移动，但格子上不带文案<br/>
      「空格子」代表格子不可点击移动，位置空置`,
      cellWidth: '60px',
      celleHeight: '40px',
      defaultValue: lineConfigs['3']['targetList'],
      tableCellOptions: lineConfigs['3']['tableCellOptions'],
      row: 3,
      line: 3
    },
    rules:[]
  },
  questionList: {
    config: {
      key: 'questionList',
      type: 'table',
      label: '题目图形',
      labelTips: `「干扰格子」代表格子可移动，但格子上不带文案<br/>
      「空格子」代表格子不可点击移动，位置空置`,
      cellWidth: '60px',
      celleHeight: '40px',
      defaultValue: lineConfigs['3']['targetList'],
      tableCellOptions: lineConfigs['3']['tableCellOptions'],
      row: 3,
      line: 3
    },
    rules:[]
  },
  isShowTarget: {
    config: {
      key: 'isShowTarget',
      type: 'radio',
      label: '目标图形隐藏',
      defaultValue: true,
      options: [
        { label: '否', value: true },
        { label: '是', value: false },
      ]
    },
    rules:[]
  },
  questionListTexture: {
    config: {
      key: 'questionListTexture',
      type: 'image',
      label: '底板UI（490*490px）',
      required: true,
      defaultValue: '',
      showDelete: false,
      options: []
    },
    rules:[]
  },
  cellTexture: {
    config: {
      key: 'cellTexture',
      type: 'image',
      label: '格子UI(150*150px)',
      required: true,
      defaultValue: '',
      showDelete: false,
      options: []
    },
    rules:[]
  },
}

export const formConfigs = Object.values(formItemConfigs).map(item => item.config);
