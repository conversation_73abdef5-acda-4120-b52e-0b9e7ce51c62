<template>
  <div>
    <BaseFormTemplate
    :currentComponents="currentComponents"
    :formConfigs="formConfigs"
    :formData="formData"
    ></BaseFormTemplate>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import BaseFormTemplate from "../BaseFormTemplate/index.vue";
import { formConfigs } from './FormModel';

@Component({
  components: {
    BaseFormTemplate
  },
})
export default class KlotskiQuestionComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get formData() {
    return this.currentComponents[0].properties;
  }

  formConfigs = formConfigs;

  @Watch('formData.line', { immediate: true })
  lineWatcher(val: number) {
    this.formConfigs.forEach((conf) => {
      if(['targetList', 'questionList'].includes(conf.key)) {
        (conf as any).line = Number(val);
      }
    })
  }
}
</script>

<style scoped lang="less">
.match-board-panel {
  text-align: left !important;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
