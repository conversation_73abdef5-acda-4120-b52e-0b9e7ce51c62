<template>
  <div class="edit-area-normal-mode">
    <!-- 组件id的展示 -->
    <component-id-selector v-if="currentIds.length" />
    <!-- 组件属性 -->
    <div v-if="currentComponents.length" class="comp-editor-wrapper">
      <content-placeholders v-show="componentEditorLoading">
        <content-placeholders-heading :img="false" />
        <content-placeholders-text :lines="20" />
      </content-placeholders>
      <el-collapse :value="collapseValue">
        <component
          :is="componentEditor"
          :currentComponents="currentComponents"
          v-show="!hideProperty"
          ref="asyncComp" />
        <composite-extra-editor />
      </el-collapse>
      <combine-editor :currentComponents="currentComponents" v-if="showCombineEditor" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { ActiveNames } from "@/pages/index/store/modules/componentManagerCard/constants";
import { types } from "@/pages/index/store/modules/componentManagerCard";
import { isEqual } from "lodash-es";
@Component({
  components: {
    CombineEditor: () => import(/* webpackChunkName: "CombineEditor" */ "../CombineEditor/index.vue"),
    ComponentIdSelector: () => import(/* webpackChunkName: "ComponentIdSelector" */ "../../ComponentIdSelector/index.vue"),
    CompositeExtraEditor: () => import(/* webpackChunkName: "CompositeExtraEditor" */ "../CompositeExtraEditor/index.vue"),
  },
})
export default class CompEditor extends Vue {
  @Prop({
    required: true,
  })
  hideProperty!: string;

  collapseValue = [
    "stage-properties",
    "base-properties",
    "label-properties",
    "sprite-properties",
    "shape-text-properties",
    "formula-properties",
    "spine-properties",
    "extra-properties",
    "cutShape-properties",
    "special-component-properties",
    "realia-component-properties",
    "shape-component-properties",
  ];

  componentEditor: any = null;

  componentEditorLoading = false;

  get showCombineEditor() {
    // 选中多个组件或者单个组合组件
    return this.currentComponents.length > 1 || (this.currentComponents.length === 1 && this.currentComponents[0].type === "group");
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  @Watch("currentIds", {
    immediate: true,
  })
  async currentIdsChange(val: any, oldVal: any) {
    if (isEqual(val, oldVal)) return;
    console.log("currentIdsChange..getComponentEditor", val, oldVal);
    if (val.length) {
      this.componentEditor = await this.getComponentEditor();
    } else {
      this.componentEditor = null;
    }
  }

  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get category() {
    return this.$store.state.template.category;
  }

  async getComponentEditor() {
    let timer = null;
    this.componentEditorLoading = true;
    const loop = () => {
      timer = requestAnimationFrame(loop);
      // 判断app是否有子元素
      if (this.componentEditor && this.$refs.asyncComp) {
        this.componentEditorLoading = false;
        // 根据app里是否有内容来定义白屏时间
        cancelAnimationFrame(timer);
      }
    };
    loop();
    const subType = this.currentComponents?.[0]?.subType;
    const type = this.currentComponents?.[0]?.type;
    const hasDifferentType = this.currentComponents.some(({ type }: { type: any }) => type !== this.currentComponents[0].type);
    const { getCompByType } = await import("./compConfigs");
    const curComp = getCompByType(type, subType);
    if (this.currentComponents.length > 0 && !hasDifferentType) {
      if (!curComp) {
        this.componentEditorLoading = false;
        return null;
      }
    }
    return curComp;
    // return this.componentEditors.composite;
  }

  showComponentManager() {
    this.$store.commit(types.mutations.setActiveNames, ActiveNames.MANAGER);
    this.$store.commit(types.mutations.setVisible, !this.$store.state.componentManagerCard.visible);
  }
}
</script>

<style scoped lang="less">
.edit-area-normal-mode {
  padding: 20px 20px;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 5px;
  /* 移除 overflow: unset，保持Element UI的默认动画机制 */
}
/deep/ .el-collapse-item__wrap {
  /* 添加动画优化 */
  will-change: height;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
/deep/ .el-collapse-item__wrap.is-active {
  /* 展开状态下才允许overflow visible，避免内容被裁剪 */
  overflow: visible !important;
}
/deep/ .el-collapse-item__header {
  font-size: 15px;
  font-weight: unset;
}
/deep/ .el-collapse-item__arrow {
  /* 优化箭头动画 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}
</style>
