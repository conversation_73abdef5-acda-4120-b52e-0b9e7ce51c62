/* eslint-disable @typescript-eslint/no-var-requires */
import store from "@/pages/index/store";

const componentEditors: any = {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  label: (r: (arg0: any) => void) => require.ensure([], () => r(require("../LabelComponentEditor/index.vue")), "LabelComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  spine: (r: (arg0: any) => void) => require.ensure([], () => r(require("../SpineComponentEditor/index.vue")), "SpineComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  sprite: (r: (arg0: any) => void) => require.ensure([], () => r(require("../SpriteComponentEditor/index.vue")), "SpriteComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  group: (r: (arg0: any) => void) => require.ensure([], () => r(require("../GroupComponentEditor/index.vue")), "GroupComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  composite: (r: (arg0: any) => void) => require.ensure([], () => r(require("../CompositeEditor/index.vue")), "CompositeEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  formula: (r: (arg0: any) => void) => require.ensure([], () => r(require("../FormulaComponentEditor/index.vue")), "FormulaComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  svgShape: (r: (arg0: any) => void) => require.ensure([], () => r(require("../ShapeComponentEditor/index.vue")), "ShapeComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  shape: (r: (arg0: any) => void) => require.ensure([], () => r(require("../H5ShapeComponentEditor/index.vue")), "H5ShapeComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  richTextSprite : (r: (arg0: any) => void) => require.ensure([], () => r(require("../NewH5LabelComponentEditor/index.vue")), "NewH5LabelComponentEditor"),
};

const specialComponentList: any = {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  voice: (r: (arg0: any) => void) => require.ensure([], () => r(require("../VoiceComponentEditor/index.vue")), "VoiceComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  audioHistoryAnswer: (r: (arg0: any) => void) => require.ensure([], () => r(require("../AudioHistoryAnswerEditor/index.vue")), "AudioHistoryAnswerEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  graphics: (r: (arg0: any) => void) => require.ensure([], () => r(require("../GraphicsComponentEditor/index.vue")), "GraphicsEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  keyboard: (r: (arg0: any) => void) => require.ensure([], () => r(require("../KeyboardEditor/index.vue")), "KeyboardEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  keyboardEnglish: (r: (arg0: any) => void) => require.ensure([], () => r(require("../KeyboardEnglishEditor/index.vue")), "KeyboardEnglishEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  matchboard: (r: (arg0: any) => void) => require.ensure([], () => r(require("../MatchBoardComponentEditor/index.vue")), "MatchBoardComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  counter: (r: (arg0: any) => void) => require.ensure([], () => r(require("../CounterComponentEditor/index.vue")), "CounterComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  write: (r: (arg0: any) => void) => require.ensure([], () => r(require("../WriteBoardComponentEditor/index.vue")), "WriteBoardComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  clock: (r: (arg0: any) => void) => require.ensure([], () => r(require("../ClockComponentEditor/index.vue")), "ClockComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  speaker: (r: (arg0: any) => void) => require.ensure([], () => r(require("../SpeakerComponentEditor/index.vue")), "SpeakerComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  brush: (r: (arg0: any) => void) => require.ensure([], () => r(require("../BrushComponentEditor/index.vue")), "BrushComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  readcom: (r: (arg0: any) => void) => require.ensure([], () => r(require("../ReadcomComponentEditor/index.vue")), "ReadcomComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  voiceSpeak: (r: (arg0: any) => void) => require.ensure([], () => r(require("../VoiceSpeakComponentEditor/index.vue")), "VoiceSpeakComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  h5Label: (r: (arg0: any) => void) => require.ensure([], () => r(require("../H5LabelComponentEditor/index.vue")), "H5LabelComponentEditor"),
};

const optionComponentList: any = {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  klotskiQuestion: (r: (arg0: any) => void) => require.ensure([], () => r(require("../xueneng/KlotskiQuestionComponentEditor/index.vue")), "KlotskiQuestionComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  cutPicture: (r: (arg0: any) => void) => require.ensure([], () => r(require("../xueneng/CutPictureComponentEditor/index.vue")), "CutPictureComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  programQuestion: (r: (arg0: any) => void) => require.ensure([], () => r(require("../xueneng/ProgramQuestionComponentEditor/index.vue")), "ProgramQuestionComponentEditor"),
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  hanoiTower: (r: (arg0: any) => void) => require.ensure([], () => r(require("../xueneng/HanoiTowerComponentEditor/index.vue")), "HanoiTowerComponentEditor"),
};

export const compConfigs = {
  ...componentEditors,
  ...specialComponentList,
  ...optionComponentList,
}

export const getCompByType = (type: string, subType: string) => {
  if(type === "specialComponent") {
    return specialComponentList[subType] || componentEditors.composite; 
  }
  else if(type === "optionComponent") {
    if((store.state.extData?.formConfig as any)?.[subType]) {
      return (r: (arg0: any) => void) => require.ensure([], () => r(require("../SchemaToForm/SchemaToFormList.vue")), "SchemaToFormList");
    }
    return optionComponentList[subType] || componentEditors.composite; 
  }
  else if (compConfigs[type]) {
    return compConfigs[type]
  }
  return componentEditors.composite;
}