<template>
  <div class="edit-area-normal-mode">
    <!-- 全局属性 -->
    <template v-if="!currentComponents.length">
      <el-collapse :value="collapseValue">
        <StageEditor :currentComponents="currentComponents" v-show="!hideProperty" />
        <EditButtonComp v-if="renderEditButton" />
      </el-collapse>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { isRenderFormEditButton } from "@/common/utils/isRenderFormEdit";
import StageEditor from "./StageEditor/index.vue";
@Component({
  components: {
    EditButtonComp: () => import(/* webpackChunkName: "EditButtonComp" */ "./EditButtonComp/index.vue"),
    StageEditor,
  },
})
export default class GlobalEditor extends Vue {
  @Prop({
    required: true,
  })
  hideProperty!: string;

  collapseValue = [
    "stage-properties",
    "base-properties",
    "label-properties",
    "sprite-properties",
    "shape-text-properties",
    "formula-properties",
    "spine-properties",
    "extra-properties",
    "cutShape-properties",
    "special-component-properties",
    "realia-component-properties",
    "shape-component-properties"
  ];

  get renderEditButton() {
    return isRenderFormEditButton(this.category);
  }

  componentEditor: any = null;

  componentEditorLoading = false;

  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get category() {
    return this.$store.state.template.category;
  }
}
</script>

<style scoped lang="less">
.edit-area-normal-mode {
  padding: 20px 20px;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 5px;
  /* 移除 overflow: unset，保持Element UI的默认动画机制 */
}
/deep/ .el-collapse-item__wrap {
  /* 添加动画优化 */
  will-change: height;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
/deep/ .el-collapse-item__wrap.is-active {
  /* 展开状态下才允许overflow visible，避免内容被裁剪 */
  overflow: visible !important;
}
/deep/ .el-collapse-item__header {
  font-size: 15px;
  font-weight: unset;
}
/deep/ .el-collapse-item__arrow {
  /* 优化箭头动画 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}
</style>
