<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      v-if="currentComponents.length === 1"
      title="键盘组件属性"
      name="sprite-properties"
    >
      <!-- <div class="flex">
        <ya-select
          label="键盘样式："
          property="keyboardType"
          :options="keyboardTypeOptions"
          :defaultValue="1"
          :componentIds="currentComponentIds"
          :option="{ required: true }"
        />
      </div> -->
         <ya-switch
            label="支持收起键盘"
            property="isPackUp"
            :componentIds="currentComponentIds"
          />
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import YaSelect from "../EditComponents/Select/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
@Component({
  components: {
    YaSelect,
    BaseProperties,
    YaSwitch
  },
})
export default class KeyboardEnglishEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;



  // get borad() {
  //   const firstVal = this.$store.state.componentMap[this.currentComponentIds[0]]
  //     .properties["keyboardType"];
  //   const hasDifferentVal = this.currentComponentIds.some(
  //     id =>
  //       this.$store.state.componentMap[id].properties["keyboardType"] !==
  //       firstVal,
  //   );
  //   if (hasDifferentVal) {
  //     return "";
  //   }
  //   if (firstVal === undefined) {
  //     return 1;
  //   }
  //   return firstVal;
  // }

  // changeBorad(val: number) {
  //   this.$store.dispatch("updateComponentsProperties", {
  //     ids: this.currentComponentIds,
  //     newProperties: {
  //       ["keyboardType"]: val,
  //     },
  //   });
  // }

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.keyboard-editor {
  padding: 10px;
}
</style>
