<template>
  <div>
    <el-button class="add-button" type="success" round style="width: 100%; margin: 10px 0;" @click="showEditModel"
      v-if="renderEditButton && !currentIds.length">编辑题目</el-button>
    <component :is="contextualAnswerForm" :category="category" v-if="contextualAnswerForm"></component>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { isRenderFormEditButton } from "@/common/utils/isRenderFormEdit";
import { CATEGORY } from "@/common/constants";
import bus from "@/pages/index/common/utils/bus";
import { renderFormQuestionOriginData } from "@/pages/index/common/utils/listenQuestionToCocos";
import { ExtendedVue } from "vue/types/vue";
import { autoShowFormDialog } from "@/common/utils/FormTemplateManager";
@Component
export default class EditButtonComp extends Vue {
  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  get category() {
    return this.$store.state.template.category;
  }

  get renderEditButton() {
    return isRenderFormEditButton(this.category);
  }

  contextualAnswerForm: ExtendedVue<Vue, unknown, unknown, unknown, Record<never, any>> | null = null;

  mounted() {
    console.log("EditButtonComp-mounted");
    // 如果是情景对话 页面第一次渲染时 需要展示
    const isPageInit = this.$store.state.timeTravel.total === 0;
    const initShowCategory = [CATEGORY.ENPK, CATEGORY.ENGROUPPK, CATEGORY.CONTEXTUALANSWERQUESTION];
    if (initShowCategory.includes(this.category) && isPageInit) {
      this.showEditModel();
    }
  }
  // 获取情景对话组件并渲染
  registerContextualAnswer() {
    if (this.contextualAnswerForm) {
      bus.$emit("form-dialog-toggle", { toggle: true, category: this.category });
    } else {
      import(/* webpackChunkName: "contextualAnswerForm" */ "@/components/form-question/contextual-answer-form").then(component => {
        this.contextualAnswerForm = Vue.extend(component.default);
        this.$nextTick(() => {
          bus.$emit("form-dialog-toggle", { toggle: true, category: this.category });
        })
      });
    }
  }

  showEditModel() {
    switch (this.category) {
      case CATEGORY.FOLLOWWORDS:
        // 抛事件给题组 题组显示modal edit-form category
        bus.$emit("edit-form", this.category);
        break;
      case CATEGORY.ENPK:
      case CATEGORY.ENGROUPPK:
        autoShowFormDialog();
        break;
      case CATEGORY.CONTEXTUALANSWERQUESTION:
        // 情景对话
        this.registerContextualAnswer();
        break;
      default:
        // 跟读单词
        renderFormQuestionOriginData();
        break;
    }
  }
}
</script>

<style scoped lang="less"></style>
