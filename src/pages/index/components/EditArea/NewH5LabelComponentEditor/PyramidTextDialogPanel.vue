<template>
  <div
    :class="['pyramid-container']"
    v-show="visible"
    v-loading="iframeLoading"
    element-loading-fullscreen="false"
    element-loading-text="请稍后，文本编辑组件加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.3)">
    <iframe :src="pyramidUrl" v-if="pyramidUrl" title="pyramid" ref="editorFrame"></iframe>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import clickOutside from "vue-click-outside";
import { isNotOnline } from "@/common/utils/env";
import { getShipName } from "@/common/utils/devHelper";
import { postMessage } from "@/common/utils/postMessage";
import { blobToUrl } from "@/common/utils/uploadBlob";

@Component({
  directives: {
    "click-outside": clickOutside,
  },
})
export default class EditDialogPanel extends Vue {
  @Prop({
    required: true,
  })
  visible!: boolean;

  pyramidUrl = "";

  iframeLoading = true;

  getPyramidUrl() {
    let hostName = "kejian.zuoyebang.cc";
    if (isNotOnline) {
      hostName = `kejian-${getShipName()}-cc.suanshubang.cc`;
    }
    return `//${hostName}/static/pyramid/#/text-editor`;
    // return `http://localhost:8081/#/text-editor`;
    // return 'https://kejian-ccs-cc.suanshubang.cc/static/pyramid/#/text-editor';
  }

  handleParse(data: unknown) {
    try {
      return typeof data === "string" ? JSON.parse(data) : data;
    } catch (error) {
      return data;
    }
  }

  getIframeWindow(): Promise<Window> {
    return new Promise((resolve) => {
      const timer = requestAnimationFrame(() => {
        const iframeDom = document.querySelector(".pyramid-container iframe");
        if (iframeDom) {
          cancelAnimationFrame(timer);
          resolve((iframeDom as any).contentWindow);
        }
      });
    })
  }

  async updateWidget(widgetData: any) {
    const iframeWindow = await this.getIframeWindow();
    postMessage(iframeWindow, "text-editor-update", { widget: widgetData });
  }

  async renderNewWidget() {
    const iframeWindow = await this.getIframeWindow();
    postMessage(iframeWindow, "text-editor-update", { new: true });
  }

  /**
   * 将以base64的图片url数据转换为Blob
   * @param urlData 用url方式表示的base64图片数据
   */
  convertBase64UrlToBlob(urlData: string) {
    const temp = urlData || '';
    const arr: any = temp.split(",") || [''];
    const  mime = arr[0].match(/:(.*?);/)[1];
    const  bstr = atob(arr[1]);
    let  n = bstr.length;
    const  u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }

  async handleMessage(message: { data: any }) {
    // console.log("message", message);
    const { action, type, data } = message.data && typeof message.data === "string" && this.handleParse(message.data);
    // console.log("action", action, "data", data);
    const tempAction = action || type;
    if (tempAction === "text-editor-ready") {
      this.iframeLoading = false;
      this.$emit('rich-text-edit-ready');
    }
    if (tempAction === "text-editor-confirm") {
      // 有时返回的url是data:, 属于无效数据；添加了按钮的重复点击拦截后没有浮现。 所以先过滤掉，如果复现再排查
      if(!data.urlstr.includes('data:image/')) {
        console.error('返回的是无效数据，需要排查产生的影响和原因');
      }
      const blob = this.convertBase64UrlToBlob(data.urlstr);
      // blob widget
      blobToUrl(blob, "text.png", true).then(url => {
        console.log("url...", url);
        data.widget.props.rotate = 0;
        this.$emit("rich-text-edit-done", {
          url,
          left: data.widget.props.left,
          top: data.widget.props.top,
          width: data.widget.props.width === 'auto' ? data.widget.props.clientWidth : data.widget.props.width,
          height: data.widget.props.clientHeight,
          content: JSON.stringify(data.widget),
        });
        this.$emit("update:visible", false);
        // 将数据外抛
        // this.$store.dispatch("updateComponentsProperties", {
        //   ids: [compId],
        //   newProperties: {
        //     url: url,
        //     latex: latex,
        //     width: img.width * 2,
        //     height: img.height * 2,
        //   },
        // });
      });
    }
    if (tempAction === "text-editor-cancel") {
      this.$emit("update:visible", false);
      this.$emit("rich-text-edit-cancel");
    }
  }

  mounted() {
    this.pyramidUrl = this.getPyramidUrl();
    // console.log("mounted..pyramid text editor");
    window.addEventListener("message", this.handleMessage, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("message", this.handleMessage, false);
    });
  }
}
</script>

<style scoped lang="less">
.pyramid-container {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  // background: rgba(0, 0, 0, 0.3);
  z-index: 100;
  iframe {
    width: 100%;
    height: 100%;
  }
}
.editor-dialog-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  // background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  flex-direction: column;
  transform-origin: left top;
  .action-wrapper {
    height: 60px;
    position: fixed;
    bottom: 0px;
    right: 16px;
    bottom: 0px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    z-index: 10;
    align-items: center;
  }
}
.editor-form-wrapper {
  position: fixed;
  right: 0;
  width: 334px;
  top: 52px;
  bottom: 0;
  background: #fff;
  padding: 20px;
}
</style>

<style lang="less">
.action-wrapper {
  span {
    font-size: 14px !important;
  }
}
.draggable-wrapper {
  border: 1px solid #0092ed;
  min-width: 100px;
  min-height: 100px;
  &.active {
    .handle {
      pointer-events: unset !important;
      display: block !important;
    }
  }
  &.active.editing {
    border-style: dashed;
    .handle {
      pointer-events: none !important;
      display: none !important;
    }
  }
  .tinymce-editor-wrapper {
    min-width: 100px;
    min-height: 40px;
  }
  .handle {
    border-radius: 6px;
    background: #fff;
    border: 1px solid #0092ed;
  }
  .tinymce-editor-wrapper {
    height: 100% !important;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
  .handle-tl {
    top: 0px;
    left: 0px;
    cursor: nw-resize;
  }
  .handle-tm {
    top: 0px;
    left: 50%;
    margin-left: -5px;
    cursor: n-resize;
  }
  .handle-tr {
    top: 0px;
    right: -10px;
    cursor: ne-resize;
  }
  .handle-ml {
    top: 50%;
    margin-top: -5px;
    left: 0px;
    cursor: w-resize;
  }
  .handle-mr {
    top: 50%;
    margin-top: -5px;
    right: -10px;
    cursor: e-resize;
  }
  .handle-bl {
    bottom: -10px;
    left: 0px;
    cursor: sw-resize;
  }
  .handle-bm {
    bottom: -10px;
    left: 50%;
    margin-left: -5px;
    cursor: s-resize;
  }
  .handle-br {
    bottom: -10px;
    right: -10px;
    cursor: se-resize;
  }
  .drag-handle {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0);
    z-index: 1;
  }

  @media only screen and (max-width: 768px) {
    [class*="handle-"]:before {
      content: "";
      left: -10px;
      right: -10px;
      bottom: -10px;
      top: -10px;
      position: absolute;
    }
  }
}
</style>
