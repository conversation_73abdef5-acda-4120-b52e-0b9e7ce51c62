<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
    <el-button :disabled="currentComponents.length > 1" type="primary" size="small" class="add-option" @click="handleClick">编辑富文本</el-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import BaseProperties from "@/pages/index/components/EditArea/BaseProperties/index.vue";
import ExtraEditor from "../ExtraEditor/index.vue";
import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";
import { ComponentTypes } from "@/common/constants";

@Component({
  components: {
    BaseProperties,
    ExtraEditor,
  },
})
export default class RichTextComponentLibrary extends Vue {
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }
  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get componentId(): string {
    return this.componentIds[0];
  }

  handleClick() {
    compManagerUtil.editComponent(ComponentTypes.RICH_EXT_SPRITE);
  }
}
</script>

<style scoped lang="less">
.shape-content {
  width: 520px;
  min-height: 380px;
  display: flex;
  // display: flex !important;
  position: fixed;
  top: 0 !important;
  left: 50% !important;
  transform: translate(-50%, 52px);

  z-index: 9999;
  background: rgba(255, 255, 255, 1);
  box-shadow: 2px 2px 8px 0px rgba(131, 134, 143, 0.2);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
}
</style>

<style lang="less">
.dialog-fade-enter-active {
  animation: unset !important;
}
.dialog-fade-leave-active {
  animation: unset !important;
}
</style>
