<template>
  <PyramidTextDialogPanel :visible.sync="visible" @rich-text-edit-done="handleDone" @rich-text-edit-cancel="handleCancel" @rich-text-edit-ready="handleReady" ref="dialogEditor" />
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import PyramidTextDialogPanel from "./PyramidTextDialogPanel.vue";
import { types } from "@/pages/index/store/modules/componentManagerCard";

@Component({
  components: {
    PyramidTextDialogPanel,
  },
})
export default class RichTextEditModal extends Vue {
  status: "create" | "edit" = "edit";
  
  visible = true;

  richTextKey = "texture";
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }
  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get componentId(): string {
    return this.componentIds[0];
  }
  
  setStatus(status: "create" | "edit") {
    this.status = status;
    console.log('setStatus...', status);
    (this.$refs.dialogEditor as any).renderNewWidget();
  }

  handleSetContent() {
    // console.log("handleSetContent", this.currentComponents);
    const htmlContent = this.getRichTextContent({ compId: this.componentId, propKey: this.richTextKey });
    // console.log("handleSetContent", htmlContent);
    if (this.$refs.dialogEditor && htmlContent) {
      const widgetJson = JSON.parse(htmlContent);
      const firstId = this.componentIds[0];
      const firstComponent: Component = this.$store.state.componentMap[firstId];
      const { x } = (window as any).cocos.getWorldPos(firstComponent.id, firstComponent.properties.angle, firstComponent.properties.x);
      const { y } = (window as any).cocos.getWorldPos(firstComponent.id, firstComponent.properties.angle, firstComponent.properties.y);
      widgetJson.props.left = x;
      widgetJson.props.top = y;
      (this.$refs.dialogEditor as any).updateWidget(widgetJson);
    }
    if (this.$refs.dialogEditor && !htmlContent) {
      (this.$refs.dialogEditor as any).renderNewWidget();
    }
  }

  handleReady() {
    this.handleClick();
    if (this.compIsNew()) {
      (this.$refs.dialogEditor as any).renderNewWidget();
      return;
    }
  }

  handleClick() {
    if (this.status === "edit") {
      // 将该组件在画布中隐藏
      this.$store.commit("updateComponentProperties", {
        id: this.componentIds[0],
        newProperties: {
          active: false,
        },
        ignoreHistory: true, // 有该字段 不会记录到历史记录中
      });
      // 收起组件管理面板
      this.$store.commit(types.mutations.setVisible, false);
      // 允许编辑宽高
      this.$store.commit("updateComponentEditable", {
        componentIds: this.componentIds,
        newEditable: {
          ["properties"]: {
            ["width"]: true,
            ["height"]: true,
            ["color"]: false,
            ["scaleX"]: true,
            ["scaleY"]: true,
          },
        },
        ignoreHistory: true,
      });
      this.handleSetContent();
    }
    // 显示编辑组件
    this.visible = true;
  }

  handleDone(params: any) {
    // console.log("handleDone...params", params);
    this.$emit("done", params);

    // 使用公式渲染进行测试
    this.visible = false;
    if (this.status === "edit") {
      this.storeRichTextData({
        compId: this.componentId,
        propKey: this.richTextKey,
        ...params,
      });
    }
    // (window as any).getd = this.getRichTextContent;
  }

  handleCancel() {
    // console.log("handleCancel");
    // 判断是不是新创建的组件 是的话就删除组件
    this.$emit("cancel");

    if (this.compIsNew()) {
      console.log('创建新组件时，不再先创建组件，不需要删除');
      // this.$store.dispatch("removeComponents", this.componentIds);
    } else {
      this.$store.commit("updateComponentProperties", {
        id: this.componentIds[0],
        newProperties: {
          active: true,
        },
        ignoreHistory: true, // 有该字段 不会记录到历史记录中
      });
      this.$store.commit("updateComponentEditable", {
        componentIds: this.componentIds,
        newEditable: {
          ["properties"]: {
            ["width"]: false,
            ["height"]: false,
            ["color"]: false,
            ["scaleX"]: false,
            ["scaleY"]: false,
          },
        },
        ignoreHistory: true,
      });
    }
  }

  storeRichTextData(params: { compId: string; propKey: string; url: string; content: string; suffix?: string }) {
    const { compId, propKey, url, content, suffix = "RichTextKey" } = params;
    const uniqueKey = `${propKey}${suffix}`;
    const uniqueKeyValue = `${propKey}${Date.now()}`;
    // console.log("storeRichTextData...params", this.compIsNew(), params, {
    //     [propKey]: url,
    //     [uniqueKey]: uniqueKeyValue,
    //     active: true,
    //   });
    const idNewEdit = this.compIsNew();
    this.$store.commit("updateComponentProperties", {
      id: compId,
      newProperties: {
        [propKey]: url,
        [uniqueKey]: uniqueKeyValue,
        width: (params as any).width || 300,
        height: (params as any).height || 300,
        active: true,
      },
      ignoreHistory: idNewEdit, // 如果是新增的时候本次更新不计入到历史记录 这样撤销一次就直接删除这个组件
    });
    this.$store.commit("updateComponentProperties", {
      id: compId,
      newProperties: {
        x: (params as any).left,
        y: (params as any).top,
      },
      ignoreHistory: idNewEdit,
    });
    this.$store.commit("updateExtProps", {
      key: uniqueKeyValue,
      value: [encodeURIComponent(content)],
      ignoreHistory: idNewEdit,
    });

    this.$store.commit("updateComponentEditable", {
      componentIds: this.componentIds,
      newEditable: {
        ["properties"]: {
          ["width"]: false,
          ["height"]: false,
          ["color"]: false,
          ["scaleX"]: true,
          ["scaleY"]: true,
        },
        ignoreHistory: idNewEdit,
      },
    });
  }

  getRichTextContent(params: { compId: string; propKey: string; suffix?: string }) {
    const { compId, propKey, suffix = "RichTextKey" } = params;
    const { componentMap, extData } = this.$store.state;
    const theComp = componentMap[compId];
    const contentKey = theComp.properties[`${propKey}${suffix}`];
    console.log("getRichTextContent-contentKey", contentKey);
    return extData[contentKey] ? decodeURIComponent(extData[contentKey][0]) : "";
  }

  getIsEditable() {
    const { componentMap } = this.$store.state;
    const theComp = componentMap[this.componentId];
    // console.log("theComp", theComp);
    return !theComp.properties.active && theComp.editable.properties.width;
  }

  compIsNew() {
    if(this.status === "create") {
      return true;
    }
    const { componentMap } = this.$store.state;
    const theComp = componentMap[this.componentId];
    return !theComp.properties[this.richTextKey];
  }

  created() {
    console.log("RichTextEditModal created");
  }
}
</script>

<style scoped lang="less">
.shape-content {
  width: 520px;
  min-height: 380px;
  display: flex;
  // display: flex !important;
  position: fixed;
  top: 0 !important;
  left: 50% !important;
  transform: translate(-50%, 52px);

  z-index: 9999;
  background: rgba(255, 255, 255, 1);
  box-shadow: 2px 2px 8px 0px rgba(131, 134, 143, 0.2);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
}
</style>

<style lang="less">
.dialog-fade-enter-active {
  animation: unset !important;
}
.dialog-fade-leave-active {
  animation: unset !important;
}
</style>
