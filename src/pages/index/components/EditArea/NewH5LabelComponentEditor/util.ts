import { CreateComp } from "@/pages/index/common/utils/createComp";
import RichTextEditModal from "./RichTextEditModal.vue";
import store from "@/pages/index/store";

const handleCreate = (params: { url: string; content: string; }) => {
  console.log('handleCreate', params);
  const { url, content,  } = params;
  const propKey = `texture`;
  // const suffix = "RichTextKey";
  // const uniqueKey = `${propKey}${suffix}`;
  const uniqueKeyValue = `${propKey}${Date.now()}`;
  const component: Omit<RichTextSpriteComponent, "id"> = {
    type: "richTextSprite",
    tag: "",
    dragable: true,
    canCombine: true,
    properties: {
      active: true,
      x: (params as any).left,
      y: (params as any).top,
      textureRichTextKey: uniqueKeyValue,
      [propKey]: url,
      width: (params as any).width || 300,
      height: (params as any).height || 300,
    },
  };
  store.dispatch("addComponentAndFocus", component);
  const compId = store.state.currentComponentIds[0];
  console.log('compId...', compId);
  store.commit("updateComponentProperties", {
    id: compId,
    newProperties: {
      x: (params as any).left,
      y: (params as any).top,
    },
    ignoreHistory: true,
  });
  store.commit("updateExtProps", {
    key: uniqueKeyValue,
    value: [encodeURIComponent(content)],
    ignoreHistory: true,
  });

  store.commit("updateComponentEditable", {
    componentIds: [compId],
    newEditable: {
      ["properties"]: {
        ["width"]: false,
        ["height"]: false,
        ["color"]: false,
        ["scaleX"]: true,
        ["scaleY"]: true,
      },
      ignoreHistory: true,
    },
  });
}

function create() {
  new CreateComp({
    component: RichTextEditModal,
    idPrefix:'richtext-editor',
    keepLive:  true
  }).confirm((instance) => {
    instance.setStatus('create');
    instance.handleClick();
  }).then((res: any) => {
    handleCreate(res.data);
  });
  console.log('create');
}

function edit() {
  new CreateComp({
    component: RichTextEditModal,
    idPrefix:'richtext-editor',
    keepLive:  true,
  }).confirm((instance) => {
    instance.setStatus('edit');
    instance.handleClick();
  })
}

export default {
  create,
  edit
}