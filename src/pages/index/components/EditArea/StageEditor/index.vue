<template>
  <div class="stage-editor">
    <el-collapse :value="['stage-properties', 'stage-extra-properties']">
      <CommonPanel />
      <ExtraEditor />
      <AnalysisEditor v-if="isRenderAnalysis" />
      <IntroductionEditor v-if="cocosInitFinished"/>
    </el-collapse>
  </div>
</template>

<script lang="ts">
import { isSupportAnalysis } from "@/common/utils/initAnalysisData";
import { Component, Vue } from "vue-property-decorator";
import CommonPanel from "./CommonPanel/index.vue";
import ExtraEditor from "./ExtraEditor/index.vue";
const COCOS_DEFAULT_COLOR = "#ffffff";

@Component({
  components: {
    // CommonPanel: () => import(/* webpackChunkName: "CommonPanel" */ "./CommonPanel/index.vue"),
    AnalysisEditor: () => import(/* webpackChunkName: "edit-init" */ "./AnalysisEditor/index.vue"),
    // ExtraEditor: () => import(/* webpackChunkName: "ExtraEditor" */ "./ExtraEditor/index.vue"),
    CommonPanel,
    ExtraEditor,
    IntroductionEditor: () => import(/* webpackChunkName: "IntroductionEditor" */ "./IntroductionEditor/index.vue"),
  },
})
export default class StageEditor extends Vue {
  get value() {
    const arr = ["stage-properties", "stage-extra-properties"];
    return arr;
  }
  get cocosInitFinished() {
    return this.$store.state.cocosInitFinished;
  }
  get stageData() {
    return this.$store.state.stageData;
  }

  get stageWidth() {
    return this.$store.state.stageData.width;
  }

  get stageHeight() {
    return this.$store.state.stageData.height;
  }

  get stageSafeWidth() {
    return this.$store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return this.$store.state.stageData.safeHeight;
  }

  get stageBackgroundColor() {
    return this.$store.state.stageData.backgroundColor;
  }

  set stageBackgroundColor(value) {
    if (value === this.stageBackgroundColor) return;

    this.$store.commit("updateStage", {
      ...this.stageData,
      backgroundColor: value || COCOS_DEFAULT_COLOR,
    });
  }

  get stageTexture() {
    return this.$store.state.stageData.texture;
  }

  set stageTexture(value) {
    if (value === this.stageTexture) return;
    this.$store.commit("updateStage", { ...this.stageData, texture: value });
  }

  get stageTextureType() {
    return this.$store.state.stageData.textureType;
  }

  set stageTextureType(value) {
    if (value === this.stageTextureType) return;
    this.$store.commit("updateStage", {
      ...this.stageData,
      textureType: value,
    });
  }

  get isRenderAnalysis() {
    return isSupportAnalysis() && this.$store.state.extraStageData.analysis;
  }
}
</script>

<style lang="less" scoped>
.stage-editor {
  text-align: left;

  .el-form-item {
    margin-bottom: 6px;
  }
}
</style>
