<template>
  <el-collapse-item
    title="全局属性"
    name="stage-properties"
    class="common-panel"
  >
    <el-form label-width="100px" size="small">
      <el-form-item label="宽度：">
        {{ stageWidth }} /
        {{ stageSafeWidth }}
      </el-form-item>

      <el-form-item label="高度：">
        {{ stageHeight }} /
        {{ stageSafeHeight }}
      </el-form-item>

      <el-form-item label="背景图片：">
        <image-select
          :src.sync="stageTexture"
          :is-show-delete-btn="true"
          :max-width="stageSafeWidth"
          :max-height="stageSafeHeight"
          class="tooltip-next"
        />
      </el-form-item>

      <el-form-item label="背景颜色：">
        <color-picker
        ek-sign="bgcolor"
        class="color-select"
        :clearable="true"
        :property="bgColor"
        v-model="bgColor"
        ></color-picker>
        <span class="button-tips">背景图和背景色只能二选一哦~</span>
      </el-form-item>
    </el-form>
  </el-collapse-item>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import ImageSelect from "@/components/ImageSelect/index.vue";
import color from "@/components/zyb-color-picker/ColorPicker.vue";
import { toHexString } from "@/components/zyb-color-picker/colorPipette/utils";

@Component({
  components: {
    ImageSelect,
    colorPicker: color,
  },
})
export default class CommonPanel extends Vue {
  get stageData() {
    return this.$store.state.stageData;
  }

  get stageWidth() {
    return this.$store.state.stageData.width;
  }

  get stageHeight() {
    return this.$store.state.stageData.height;
  }

  get stageSafeWidth() {
    return this.$store.state.stageData.safeWidth;
  }

  get stageSafeHeight() {
    return this.$store.state.stageData.safeHeight;
  }

  get stageTexture() {
    return this.$store.state.stageData.texture;
  }

  set stageTexture(value) {
    if (value === this.stageTexture) return;
    this.$store.commit("updateStage", { ...this.stageData, texture: value, bgColor: '' });
  }

  get bgColor() {
    return this.$store.state.stageData.bgColor;
  }

  set bgColor(value) {
    this.$store.commit("updateStage", {
      ...this.stageData,
      bgColor: toHexString(value),
      texture: '',
    });
  }
}
</script>

<style lang="less" scoped>
.common-editor {
  text-align: left;

  .el-form-item {
    margin-bottom: 6px;
  }
}
.button-tips {
  position: absolute;
  left: 40px;
  top: 0px;
  font-size: 12px;
  margin-left: 16px;
  color: #999999;
  line-height: 13px;
}
</style>
