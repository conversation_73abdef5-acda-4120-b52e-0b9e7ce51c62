<template>
  <div class="question-analysis-wrapper" v-if="analysis && isRender">
    <div class="question-analysis-text">
      <span class="label">解析</span>
      <el-radio-group class="form-content" v-model="type" @change="handleTypeChange">
        <el-radio label="text" value="text">文本</el-radio>
        <el-radio label="image" value="image">图片</el-radio>
      </el-radio-group>
      <template v-if="type === 'text'">
        <el-input v-model="analysisText" rows="5" size="small" placeholder="请输入解析内容" type="textarea"> </el-input>
        <span class="el-input__count textarea-count">
          <span class="el-input__count-inner"> {{ analysisText.length }}/{{ 1000 }} </span>
        </span>
      </template>
      <template v-if="type === 'image'">
        <div class="question-analysis-image">
          <image-select :src.sync="imageUrl" :maxWidth="932" :maxHeight="524" :checkSize="true" :isMultiple="false" />
          <span v-if="!imageUrl">图片支持最大尺寸932*524</span>
        </div>
      </template>
    </div>
    <div class="question-analysis-audio">
      <el-dropdown @command="handleCommand" trigger="click" size="small">
        <el-button type="primary" size="small">
          上传解析音频
          <i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown" class="dropdown-audio">
          <el-dropdown-item command="upload">
            <el-upload
              :show-file-list="false"
              :http-request="request"
              action=""
              accept=".mp3"
              :before-upload="beforeUpload"
              :on-success="onSuccess"
              :on-error="handleUploadError"
              class="audio-uploader"
            >
              <el-button size="small" type="text" class="upload-handler">本地音频</el-button>
            </el-upload>
          </el-dropdown-item>
          <el-dropdown-item command="combine" class="combine-button">音频合成</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <span>不超过600kb的mp3文件</span>
    </div>
    <el-dialog title="文本录入" :visible.sync="visible" width="880px" height="360px" top="0vh" :close-on-click-modal="false" @close="onDialogClose">
      <el-form :model="formData" label-position="left" ref="form" class="form-question-wrapper">
        <el-form-item label="" class="q-form-item" prop="text" :rules="rules">
          <el-input class="q-pure-input" type="textarea" autocomplete="off" rows="10" v-model="formData['text']" placeholder="请输入内容">
            <span class="el-input__count" slot="suffix">
              <span class="el-input__count-inner"> {{ formData["text"].length }}/{{ getMax }} </span>
            </span>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleAudioPlay" :disabled="!formData.audio.url">播放</el-button>
        <el-button @click="onCombine" :loading="loading">合成</el-button>
        <el-button type="primary" @click="onDialogConfirm" :disabled="!formData.audio.url">
          插入题目
        </el-button>
      </div>
    </el-dialog>
    <div class="audio-wrapper">
      <div class="has-mp3" v-if="analysisAudio.url">
        <span class="mp3-name">{{ analysisAudio.fileName }}</span>
        <em class="el-icon-success"></em>
        <em class="el-icon-error" @click="handleAudioDelete()"></em>
      </div>
      <AudioControls
        v-if="analysisAudio.url || formData.audio.url"
        v-show="analysisAudio.url"
        class="audio-control"
        :audioUrl="formData.audio.url || analysisAudio.url"
        @loaded="handleAudioLoad"
      ></AudioControls>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { requestPhpServer } from "@/common/utils/request";
import { requestSpeechServer } from "@/common/utils/speechRequest";
import APIS from "@/common/api/constants";
import AudioControls from "@/components/form-question/q-audio/audio-controls";
import { isSupportAnalysis } from "@/common/utils/initAnalysisData";
import ImageSelect from "@/components/ImageSelect/index.vue";
@Component({
  components: {
    AudioControls,
    ImageSelect,
  },
})
// todo
// 解析字符超了 怎么处理？ 提示 还是直接截断 zhengbo
export default class AnalysisEditor extends Vue {
  visible = false;
  loading = false;
  formData = {
    text: "",
    audio: {
      url: "",
      fileName: "",
    },
  };
  fileName = "";
  rules = [
    { required: true, message: `请输入内容`, trigger: "change" },
    { required: true, message: `请输入内容`, trigger: "blur" },
    { type: "string", message: `请输入内容`, trigger: "change" },
    { type: "string", message: `请输入内容`, trigger: "blur" },
    { max: this.getMax, message: `内容不得超过${this.getMax}个字符`, trigger: "blur" },
    { max: this.getMax, message: `内容不得超过${this.getMax}个字符`, trigger: "change" },
  ];
  type: "text" | "image" = this.$store.state.extraStageData.analysis?.imageUrl ? "image" : "text";

  get isRender() {
    return isSupportAnalysis();
  }

  get getMax() {
    return 300;
  }

  get analysis() {
    return this.$store.state.extraStageData.analysis;
  }

  get analysisText() {
    const val = this.$store.state.extraStageData.analysis.text;
    return val ?? "";
  }

  set analysisText(val) {
    if (val === this.analysisText) return;
    if (val.length > 1000) {
      this.$message.error("解析不得超过1000个字符");
    }
    this.$store.commit("updateExtraStageAnalysis", {
      ["text"]: val.slice(0, 1000),
    });
  }
  get imageUrl() {
    const val = this.$store.state.extraStageData.analysis.imageUrl;
    return val ?? "";
  }

  set imageUrl(val) {
    console.log("imageUrl", val, this.imageUrl);
    if (val === this.imageUrl) return;
    this.$store.commit("updateExtraStageAnalysis", {
      ["imageUrl"]: val,
    });
  }

  get analysisAudio() {
    const val = this.$store.state.extraStageData["analysis"]["audio"];
    return val ?? { url: "", fileName: "" };
  }

  public handleTypeChange(val: "text" | "image") {
    console.log("handleTypeChange", val);
    if (val === "image") {
      this.$store.commit("updateExtraStageAnalysis", {
        ["text"]: "",
      });
    }
    if (val === "text") {
      this.$store.commit("updateExtraStageAnalysis", {
        ["imageUrl"]: "",
      });
    }
  }

  public handleCommand(command: string) {
    console.log("handleCommand", command);
    if (command === "combine") {
      this.visible = true;
    }
  }

  onDialogClose() {
    this.visible = false;
    (this.$refs.form as any).clearValidate();
    this.formData = {
      text: "",
      audio: {
        url: "",
        fileName: "",
      },
    };
  }

  onDialogConfirm() {
    this.visible = false;
    this.$store.commit("updateExtraStageAnalysis", {
      ["audio"]: {
        ...this.analysisAudio,
        ...this.formData.audio,
      },
    });
    this.formData = {
      text: "",
      audio: {
        url: "",
        fileName: "",
      },
    };
  }

  async onCombine() {
    console.log("onCombine");
    (this.$refs.form as any).validate(async (valid: boolean, data: any[]) => {
      console.log("validate.params", valid, data);
      if (!valid) return;
      this.loading = true;
      // requestSpeechServer.
      try {
        const url = "//speech.zuoyebang.com/v1/tts";
        const uploadFileRes = await requestSpeechServer.post<Response<{ audio_url: string }>>(url, {
          format: "mp3",
          // eslint-disable-next-line @typescript-eslint/camelcase
          result_type: "url",
          voice: "Aitong",
          text: this.formData.text,
        });
        const data = uploadFileRes.data.data || {};
        console.log("speech-data", data);
        this.formData.audio.fileName = "合成音频";
        this.formData.audio.url = data.audio_url;
        this.loading = false;
        this.$message.success("合成成功");
        return uploadFileRes;
      } catch (err) {
        this.$message.error("遇到点问题，请稍后再试");
        // this.$message.error((err as Error).message || '遇到点问题，请稍后再试');
        return Promise.reject();
      }
    });
  }

  async request(params: { file: File }) {
    const { file } = params;
    const formData = new FormData();
    formData.append("file", file);
    // const { name } = file;
    try {
      const uploadFileRes = await requestPhpServer.post(APIS.PHP_UPLOAD_AUDIO, formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      });
      return uploadFileRes;
    } catch (err) {
      this.$message.error((err as Error).message);
      return Promise.reject();
    }
  }

  beforeUpload(file: File) {
    const isType = file.type === "audio/mp3" || file.type === "audio/mpeg";
    const { size } = file;
    this.fileName = file.name;
    if (!isType) {
      this.$message.error("上传视频只支持mp3格式!");
      return false;
    }
    if (file.name.endsWith(".MP3")) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    // 获取文件的后缀名 并判断是否是小写
    const isLower = file.name.split(".")[1].toLowerCase() === file.name.split(".")[1];
    if (!isLower) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    const isOutOfSize = size / 1024 > 600;
    if (isOutOfSize) {
      console.log(size);
      this.$message.error("音频不能超过600KB");
      return false;
    }
  }

  handleUploadError() {
    this.$message.error("该MP3音频存在问题，请使用其他音频");
  }

  onSuccess(res: any) {
    if (res.data.errNo === 0) {
      const url = res.data.data; // artcw
      // const { url } = res.data.data; // tihu
      this.$store.commit("updateExtraStageAnalysis", {
        ["audio"]: {
          url: url,
          fileName: this.fileName,
        },
      });
      this.fileName = "";
    } else {
      this.$message.error("该MP3音频存在问题，请使用其他音频");
    }
  }

  handleAudioDelete() {
    this.$store.commit("updateExtraStageAnalysis", {
      ["audio"]: {
        url: "",
        fileName: "",
        duration: 0,
      },
    });
  }

  handleAudioLoad(duration: number) {
    this.$store.commit("updateExtraStageAnalysis", {
      ["audio"]: {
        url: this.analysisAudio.url,
        fileName: this.analysisAudio.fileName,
        duration: duration,
      },
    });
  }

  handleAudioPlay() {
    const audioDom = document.querySelector(".audio-control audio");
    if (audioDom) (audioDom as HTMLAudioElement).play();
  }
}
</script>

<style lang="less" scoped>
.question-analysis-wrapper {
  margin-top: 10px;
  margin-bottom: 10px;
}
.textarea-count {
  position: absolute;
  bottom: 4px;
  right: 8px;
  color: #999;
  font-size: 12px;
}
.question-analysis-audio {
  margin-top: 10px;
  margin-bottom: 10px;
  > span {
    margin-left: 10px;
  }
}
.question-analysis-image {
  display: flex;
  align-items: center;
  > span {
    margin-left: 10px;
  }
}
.label {
  display: inline-block;
  text-align: left;
  min-width: 40px;
  flex-shrink: 0;
  height: 24px;
}
.has-mp3 {
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  width: 306px;
  height: 28px;
  padding: 0 8px;
  vertical-align: middle;
  .mp3-name {
    display: inline-block;
    vertical-align: top;
    font-size: 12px;
    line-height: 28px;
    color: #555;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    width: 280px;
  }
  .el-icon-success {
    color: #42c57a;
  }
  .el-icon-error {
    display: none;
    color: #95a1ad;
    cursor: pointer;
  }
  .el-input__count {
    line-height: 15px;
  }
  .el-icon-success,
  .el-icon-error {
    position: absolute;
    right: 8px;
    top: 6px;
    font-size: 16px;
  }
  &:hover {
    border-radius: 2px;
    background: #f0f1f5;
    .el-icon-error {
      display: inline-block;
    }
    .el-icon-success {
      display: none;
    }
  }
}
.audio-control {
  margin-bottom: 10px;
}
.audio-wrapper {
  /deep/ .el-slider__runway {
    margin: 3px 0 !important;
    height: 4px;
  }
  .audio-controls-container {
    width: 309px !important;
    /deep/ .control-progress {
      width: 146px !important;
    }
  }
}
.upload-handler {
  width: 128px;
  text-align: center;
}
.combine-button {
  text-align: center;
}
.dropdown-audio {
  /deep/ .el-dropdown-menu__item {
    text-align: center;
    padding: 0 !important;
  }
}
.question-analysis-text {
  position: relative;
}
</style>
