/*
 * @Date: 2025-07-11 16:12:34
 * @LastEditors: chxu
 * @LastEditTime: 2025-07-11 18:49:37
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/StageEditor/IntroductionEditor/IntroductionBtnManager.ts
 * @Author: chxu
 */

import Vue from 'vue'
import IntroductionBtn from './IntroductionBtn.vue'

class IntroductionBtnManager {
  private static instance: IntroductionBtnManager | null = null
  private componentInstance: Vue | null = null
  private container: HTMLElement | null = null

  constructor() {
    if (IntroductionBtnManager.instance) {
      return IntroductionBtnManager.instance
    }
    IntroductionBtnManager.instance = this
  }

  private createContainer(): HTMLElement {
    if (!this.container) {
      this.container = document.createElement('div')
      this.container.id = 'introduction-btn-container'
      const parentDom = document.querySelector('.canvas-container');
      (parentDom as HTMLElement).appendChild(this.container)
    }
    return this.container
  }

  private createComponent(): Vue {
    if (!this.componentInstance) {
      const container = this.createContainer()
      
      // // 创建 Vue 实例
      const ComponentClass = Vue.extend(IntroductionBtn)
      this.componentInstance = new ComponentClass({
        propsData: {
          text: '查看原文',
          x: 0,
          y: 280
        }
      })

      // 挂载到容器
      this.componentInstance.$mount();
      
      container.appendChild(this.componentInstance.$el)
    }
    return this.componentInstance
  }

  show(): void {
    const component = this.createComponent()
    ;
    const timer = setTimeout(() => {
      clearTimeout(timer);
      // 等待组件挂载完成
      (component as any).show()
    }, 300)
  }

  hide(): void {
    if (this.componentInstance) {
      ;(this.componentInstance as any).hide()
    }
  }

  setText(text: string): void {
    if (this.componentInstance) {
      ;(this.componentInstance as any).setText(text)
    }
  }

  setPosition(x: number, y: number): void {
    if (this.componentInstance) {
      ;(this.componentInstance as any).setPosition(x, y)
    }
  }

  // 获取当前缩放比例
  getScale(): number {
    if (this.componentInstance) {
      return (this.componentInstance as any).scale
    }
    return 1
  }

  // 根据原始尺寸获取缩放后的尺寸
  getScaleSize(size: number): number {
    const scale = this.getScale()
    return Math.round(size * scale * 100) / 100
  }

  setShow(visible: boolean): void {
    if (this.componentInstance) {
      ;(this.componentInstance as any).setShow(visible)
    }
  }

  getVisible(): boolean {
    if (this.componentInstance) {
      return (this.componentInstance as any).getVisible()
    }
    return false
  }

  destroy(): void {
    if (this.componentInstance) {
      ;(this.componentInstance as any).destroy()
      this.componentInstance.$destroy()
      this.componentInstance = null
    }
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
      this.container = null
    }
    IntroductionBtnManager.instance = null
  }

  // 静态方法，获取单例实例
  static getInstance(): IntroductionBtnManager {
    if (!IntroductionBtnManager.instance) {
      IntroductionBtnManager.instance = new IntroductionBtnManager()
    }
    return IntroductionBtnManager.instance
  }

  // 添加事件监听
  on(event: string, callback: Function): void {
    if (this.componentInstance) {
      this.componentInstance.$on(event, callback)
    }
  }

  // 移除事件监听
  off(event: string, callback?: Function): void {
    if (this.componentInstance) {
      this.componentInstance.$off(event, callback)
    }
  }


}

// 导出单例实例
export default IntroductionBtnManager.getInstance() 