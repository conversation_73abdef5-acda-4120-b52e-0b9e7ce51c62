<template>
  <div class="question-introduction-wrapper">
    <!-- <span class="label">题目说明</span> -->
    <ya-label label="题目说明" description="此部分内容只展示在错题本的“查看原文”功能和Cocos“预览”功能中，不在课中直播间展示" />
      <el-switch class="form-switch" v-model="isSet" @change="handleToggle"></el-switch>
      <div class="introduction-content" v-if="isSet">
        <el-button
          size="mini"
          @click="handleEdit(0)"
          v-if="introduction.textUrls.length === 0"
        >添加说明</el-button>
        <div class="preview" v-else>
          <div class="modal" @click="handleEdit(1)">
            <i :class="['cursor']">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-iconedit-o"></use>
              </svg>
            </i>
            <span>更改题目说明</span>
          </div>
          <img
          :src="introduction.textUrls[0]"
          alt=""
           />
        </div>
        
      </div>
  </div>
</template>

<script lang="ts">
import Label from "@/components/Label/index.vue";
import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";
import { Component, Vue, Watch } from "vue-property-decorator";
import IntroductionBtn from "./drawBtn";

@Component({
  components: {
    "ya-label": Label,
  }
})
export default class IntroductionEditor extends Vue {
  get introduction() {
    return this.$store.state.extraStageData.introduction;
  }

  get isSet() {
    const val = this.$store.state.extraStageData.introduction.isSet;
    return val ?? false;
  }

  set isSet(val) {
    console.log(val);
    if (val === this.isSet) return;
    if (!val) {
      this.$store.commit("updateExtraStageIntroduction", {
        ["isSet"]: val,
        textUrls: [],
      });
    } else {
      this.$store.commit("updateExtraStageIntroduction", {
        ["isSet"]: val,
      });
    }
  }
  
  @Watch("isSet", { immediate: true })
  onIsSetChange(val: any) {
    if (val) {
      IntroductionBtn.show();
    } else {
      IntroductionBtn.hide();
      this.$store.commit("updateExtraStageIntroduction", {
        ["textUrls"]: [],
      });
      this.$store.commit("updateExtProps", {
        key: 'introductionH5LabelDomStr',
        value: undefined,
        ignoreHistory: true,
      });
    }
  }

  rules = [
    { required: true, message: `请输入内容`, trigger: "change" }
  ];

  handleToggle() {
    console.log("handleToggle");
  }

  async handleEdit(isEdit: number) {
    let content = undefined
    if (isEdit === 1) {
      content = decodeURIComponent(this.$store.state.extData.introductionH5LabelDomStr);
    }
    console.log('handleEdit...', isEdit, content);
    const H5LabelComponentEditor = await compManagerUtil.registerComponent('h5Label');
    (H5LabelComponentEditor as any).dialog(content, {
      width: '862px',
    }).then((res: { data: { formData: string; labelPicList: string[]; }; }) => {
      this.$store.commit("updateExtraStageIntroduction", {
        ["textUrls"]: res.data.labelPicList,
      });
      this.$store.commit("updateExtProps", {
        key: 'introductionH5LabelDomStr',
        value: [encodeURIComponent(res.data.formData)],
        ignoreHistory: true,
      });
    }).catch((err: any) => {
      content = undefined;
    });
  }
}
</script>

<style scoped lang="less">
.question-introduction-wrapper {
  margin-bottom: 10px;
  .form-switch {
    margin-left: 10px;
  }
  .introduction-content {
    margin-top: 10px;
    margin-left: 73px;
  }
  .preview {
    max-width: 100%;
    position: relative;
    min-height: 100px;
    .modal {
      display: none;
      transition: all 0.3s;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      cursor: pointer;
      justify-content: center;
      align-items: center;
      color: #fff;
      // display: flex;
      flex-direction: column;
      svg {
        font-size: 16px;
        color: #fff;
        margin: 10px;
      }
    }
    &:hover .modal {
      display: flex;
    }
    .cursor {
      cursor: pointer;
    }
    img {
      width: 100%;
    }
  }
}
</style>
