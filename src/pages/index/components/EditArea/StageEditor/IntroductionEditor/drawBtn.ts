/*
 * @Date: 2025-07-11 16:12:34
 * @LastEditors: chxu
 * @LastEditTime: 2025-07-11 18:39:05
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/StageEditor/IntroductionEditor/drawBtn.ts
 * @Author: chxu
 */

// 导入 Vue 组件管理器
import IntroductionBtnManager from './IntroductionBtnManager'

// 为了保持 API 兼容性，重新导出管理器的方法
class IntroductionBtn {
    static getInstance() {
        return IntroductionBtnManager
    }
}

// 导出管理器实例，保持原有 API
export default IntroductionBtnManager
