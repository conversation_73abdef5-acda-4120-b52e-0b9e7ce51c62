<template>
  <div
    v-show="visible"
    id="introductionBtn"
    class="introduction-btn"
    :class="{ collapsed }"
    :style="buttonStyle"
  >
    <span class="btn-text" v-show="!collapsed" @click="handleShowIntroduction">{{ text }}</span>
    <!-- 编辑器测 不展示收起状态 @click.stop="toggleCollapse" -->
    <div class="btn-arrow">
      <img
        :class="{ 'arrow-collapsed': collapsed }"
        src="/static/img/introduction_arrow.png"
        alt="arrow"
      />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({
  name: 'IntroductionBtn'
})
export default class IntroductionBtn extends Vue {
  @Prop({ default: '查看原文' }) readonly text!: string;
  @Prop({ default: 0 }) readonly x!: number;
  @Prop({ default: 280 }) readonly y!: number;

  visible = false;
  collapsed = false;
  bottom = 92;
  scale = 1;

  get containerWidth() {
    try {
      if (this.$store && (this.$store as any).state) {
        return (this.$store as any).state.containerWidth || 0;
      }
    } catch (error) {
      console.log('error get containerWidth', error);
    }
    return document.querySelector(".canvas-container")?.clientWidth || 0;
  }

  get buttonStyle() {
    const base = {
      left: '-400px',
      transformOrigin: 'left bottom',
    } as any;
    if (this.collapsed) {
      base.justifyContent = 'flex-end';
    }
    return base;
  }

  getScaleSize = (size: number, scale = this.scale) => {
    return Math.round(size * scale * 100) / 100;
  };

  getBottom = () => {
    // 获取y
    const canvasDom = document.querySelector("#GameCanvas") as HTMLElement;
    const canvasRect = canvasDom.getBoundingClientRect();
    const scale = canvasRect.width / 1280;
    this.bottom = canvasRect.bottom - Math.round((120+92) * scale);
    this.scale = Math.round(scale * 10000) / 10000;
    const theEl = document.querySelector("#introductionBtn") as HTMLElement;
    if (theEl) {
      theEl.style.bottom = Math.round((120+92) * scale) + 'px';
      theEl.style.transform = `scale(${this.scale})`;
      theEl.style.left = `${0}px`;
    } else {
      const timer = setInterval(() => {
        const theEl = document.querySelector("#introductionBtn") as HTMLElement;
        if (theEl) {
          clearInterval(timer);
          theEl.style.bottom = Math.round((120+92) * scale) + 'px';
          theEl.style.transform = `scale(${this.scale})`;
          theEl.style.left = `${0}px`;
        }
      }, 300)
    }
  }

  show() {
    this.$nextTick(() => {
      this.getBottom();
      window.addEventListener("resize", this.getBottom);
      this.visible = true;
    })
  }
  hide() {
    this.visible = false;
    window.removeEventListener("resize", this.getBottom);
  }
  setText(newText: string) { this.$emit('update:text', newText); }
  setPosition(x: number, y: number) { this.$emit('update:position', { x, y }); }
  destroy() { this.hide(); }
  setShow(visible: boolean) { visible ? this.show() : this.hide(); }
  getVisible(): boolean { return this.visible; }

  toggleCollapse() {
    this.collapsed = !this.collapsed;
  }
  handleShowIntroduction() {
    this.$emit('showIntroduction');
  }
}
</script>

<style scoped>
.introduction-btn {
  position: absolute;
  left:0;
  /* bottom: 212px; */
  width: 172px;
  height: 72px;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 0 36px 36px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  cursor: pointer;
  transition: width 0.3s ease;
  user-select: none;
  box-sizing: border-box;
  overflow: hidden;
}
.introduction-btn.collapsed {
  width: 48px; /* 只显示半圆部分 */
  /* padding: 0; */
  justify-content: flex-end;
}
.btn-arrow {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}
.arrow-collapsed {
  transform: rotate(180deg);
  transition: transform 0.2s;
}
.btn-arrow img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}
.btn-text {
  color: white;
  font-size: 24px;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;
  flex-shrink: 0;
  transition: opacity 0.2s;
}
</style> 