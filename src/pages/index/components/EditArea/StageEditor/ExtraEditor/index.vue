<template>
  <el-collapse-item title="业务属性" name="stage-extra-properties">
    <div class="question-name">
      <span class="label required" v-if="!isTemplateEdit">题目名称</span>
      <el-input v-model="questionName" size="small" />
    </div>
    <SetAnswer v-if="isRenderSetAnswer" />
    <template v-for="item in extraConfig">
      <component
        :class="{
          required: item.required,
        }"
        :is="components[item.type] || item.type"
        :key="item.key"
        :label="item.label"
        :property="item.key"
        :params="item.params"
        :config="item"
      ></component>

      <!-- 动态显示子组件 -->
      <template v-for="itemChild in item.child">
        <div v-if="isShowChild(item, itemChild)" :key="itemChild.key + 'child'">
          <component
            :class="{
              required: itemChild.required,
            }"
            :is="components[itemChild.type] || itemChild.type"
            :key="itemChild.key"
            :label="itemChild.label"
            :property="itemChild.key"
            :params="itemChild.params"
            :config="itemChild"
          ></component>
        </div>
      </template>
    </template>
  </el-collapse-item>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { isTemplateEdit } from "@/pages/index/common/utils/isTemplateEdit";
import StageExtraAudioSelect from "./AudioSelect/index.vue";
import ExtraInput from "./Input/index.vue";
import ExtraSelect from "./Select/index.vue";
import ExtraSwitch from "./Switch/index.vue";
// import ExtraSwitchInput from "./SwitchInput/index.vue";
import ExtraDragJudge from "./DragJudge/index.vue";
import ExtraRadio from "./Radio/index.vue";
import ColorPicker from "./ColorPicker/index.vue";
import Options from "./Options";
import DragPintuEditor from "./DragPintuEditor/index.vue";
import KeyboardSelect from "./KeyboardSelect/index.vue";
@Component({
  components: {
    SetAnswer: () => import(/* webpackChunkName: "SetAnswer" */ "@/pages/index/components/EditArea/StageEditor/ExtraEditor/SetAnswer/index.vue")
  }
})
export default class GlobalExtraEditor extends Vue {
  components = {
    input: ExtraInput,
    select: ExtraSelect,
    switch: ExtraSwitch,
    switchInput: ExtraDragJudge,
    radio: ExtraRadio,
    audioSelect: StageExtraAudioSelect,
    colorPicker: ColorPicker,
    custom: Options,
    DragPintuEditor: DragPintuEditor,
    keyboardSelect:KeyboardSelect,
  };


  totalConfig = {
    "key": "total",
    "label": "总数值判定",
    "required": false,
    "type": "radio",
    "params": {
      "relative": {
        "key": "dragSum",
        "label": "",
        "required": true,
        "type": "switchInput",
        "params": {
          "min": 1,
          "max": 100
        }
      }
    }
  }
  get extraConfig() {
    return this.$store.state.template && this.$store.state.template.extraConfig;
  }

  get isTemplateEdit() {
    return isTemplateEdit;
  }

  get questionName() {
    return this.$store.state.name;
  }

  set questionName(val: string) {
    this.$store.commit("setQuestionName", val);
  }

  get isRenderSetAnswer() {
    return this.$store.state.template.supportSetReferenceAnswer;
  }

  public isShowChild(item: any, itemChild: any) {
    if (!item.child) return false;
    const show = this.$store.state.extraStageData[item.key] === itemChild.fathLabel;
    return show;
  }
}
</script>

<style lang="less" scoped>
.question-name {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}

.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
