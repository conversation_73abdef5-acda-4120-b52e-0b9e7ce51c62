<template>
  <audio-select
    :value.sync="v"
    :label="label"
    :property="property"
    :defaultValue="defaultValue"
    :params="params"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";

@Component({
  components: { AudioSelect },
})
export default class StageExtraAudioSelect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
}
</script>
