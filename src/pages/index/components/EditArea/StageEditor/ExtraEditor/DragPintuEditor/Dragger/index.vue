<template>
  <div ref="dragItem" class="widget-draggable" style="position: relative; left: 0; top: 0;">
    <slot></slot>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
export interface DragPosition {
  mouseStartPosX: number;
  mouseStartPosY: number;
  dragStartX: number;
  dragStartY: number;
}
/**
 * @description: 拖拽组件
 * @property {number} scale 缩放参数
 * @property {HTMLElement} wholePageDom 页面容器
 * @property {boolean} needTransitionWhenDragOver 拖拽事件结束是否需要过渡，默认为true
 * @property {number} transitionTime 过渡时间，默认为100
 * @property {Function} checkIfBreakDrag 需要阻断拖拽事件时要传，返回false为阻断，返回true为不阻断
 * @event dragEventTriggered  mousedown或touchstart事件触发
 * @event beforeInitDrag  当前拖拽元素符合可拖动条件，还没有初始化拖拽元素
 * @event afterInitDrag  拖拽元素已初始化完成
 * @event beforeInitDragPosition  初始化拖拽所需位置数据之前
 * @event afterInitDragPosition  初始化拖拽所需位置数据之后
 * @event moveEventTriggered  mousemove或touchmove事件触发
 * @event beforeMove  元素开始移动之前
 * @event afterMove  元素移动之后
 * @event releaseEventTriggered  mouseup或touchend事件触发
 * @event dragHasMovedEnded  元素被拖动过并松开了
 * @event dragNoMoveEnded 元素没有被拖动过就松开了
 * @event beforeResetDragInfo 重置拖拽信息之前
 * @event afterResetDragInfo 清空拖拽信息之后
 */
@Component
export default class Dragger extends Vue {
  @Prop({ type: Number, default: 1 }) private scale!: number;
  @Prop({ type: Boolean, default: true }) private needTransitionWhenDragOver!: boolean;
  @Prop({ type: Number, default: 100 }) private transitionTime!: number;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  @Prop({ type: Function, default: () => {} }) private checkIfBreakDrag!: (e?: any) => boolean;

  private isDragDown = false; // 是否触发mousedown事件
  private isMoved = false; // 是否触发mousemove事件
  private wholePageDom!: HTMLElement;

  private drag: any = null; // 当前拖拽元素
  private pos: DragPosition = {
    // 记录拖起时的位置
    mouseStartPosX: 0, // 鼠标初始位置
    mouseStartPosY: 0,
    dragStartX: 0, // 拖拽元素初始化定位
    dragStartY: 0,
  };
  private containerLeft = 0; // 容器坐标
  private containerTop = 0; // 容器坐标

  private moveTimer = 0; // 节流计时器

  private isMove = false; // 是否被拖动
  private isUp = false; // 是否拖拽抬起
  private isDown = false; // 是否按下
  public created() {
    // 当三只触摸时会触发touchcancel事件，需要重置拖拽元素
    document.addEventListener("touchcancel", this.handleTouchCancel);
    this.isMove = false;
    this.isUp = false;
  }
  public mounted() {
    if (this.$refs.dragItem) {
      (this.$refs.dragItem as HTMLElement).addEventListener("mousedown", this.dragDown);
    }
    this.$nextTick(() => {
      this.wholePageDom = document.querySelector(".puzzle-cut-select") as any;
    });
  }
  public beforeDestroy() {
    document.removeEventListener("mousemove", this.moveFn);
    document.removeEventListener("mouseup", this.upFn);
    document.removeEventListener("", this.upFn);
    if (this.$refs.dragItem) {
      (this.$refs.dragItem as HTMLElement).removeEventListener("mousedown", this.dragDown);
    }
  }
  public handleTouchCancel(e: TouchEvent) {
    this.drag = null;
  }

  private dragDown(e: any) {
    this.$emit("dragEventTriggered", e);
    if (!this.isDown) {
      this.isDown = true;
    }
    if (!this.drag) {
      const beforeinitDragResult = this.checkIfBreakDrag(e);
      if (beforeinitDragResult === false) {
        return;
      }
      this.$emit("beforeInitDrag", e);
      this.isDragDown = true;
      this.drag = e.target as any;
      this.$emit("afterInitDrag", this.drag);
      this.$emit("beforeInitDragPosition", this.drag);
      // 获取卡片和鼠标初始位置
      const dragRect = this.drag.getBoundingClientRect();
      const containerRect = this.wholePageDom.getBoundingClientRect();
      this.containerLeft = containerRect.left;
      this.containerTop = containerRect.top;
      this.pos.mouseStartPosX = !e.touches ? e.clientX : e.touches[0].clientX;
      this.pos.mouseStartPosY = !e.touches ? e.clientY : e.touches[0].clientY;
      this.drag.style.transition = "";
      this.pos.dragStartX = dragRect.left - this.containerLeft;
      this.pos.dragStartY = dragRect.top - this.containerTop;
      // this.afterInitDragPosition();
      this.$emit("afterInitDragPosition", this.drag);
      document.addEventListener("mousemove", this.moveFn);
      document.addEventListener("mouseup", this.upFn);
      document.addEventListener("touchmove", this.moveFn);
      document.addEventListener("touchend", this.upFn);
    }
    return false;
  }
  private moveFn(e: any) {
    e.preventDefault();
    if (!this.isMove) {
      this.isMove = true;
    }

    // this.moveEventTriggered(e);
    this.$emit("moveEventTriggered", e);
    this.isMoved = true;
    if (!this.isDragDown || !this.drag) {
      return;
    }
    if (e.touches) {
      // 两指触摸 不能移动
      if (e.touches.length !== 1) {
        return;
      }
    }
    // this.beforeMove();
    this.$emit("beforeMove", e);
    const clientX = !e.touches ? e.clientX : e.touches[0].clientX;
    const clientY = !e.touches ? e.clientY : e.touches[0].clientY;
    const dragItemRect = (this.$refs.dragItem as HTMLElement).getBoundingClientRect();
    const dragItemLeft = dragItemRect.left - this.containerLeft;
    const dragItemTop = dragItemRect.top - this.containerTop;
    let left = (clientX - this.pos.mouseStartPosX + this.pos.dragStartX - dragItemLeft) / this.scale;
    let top = (clientY - this.pos.mouseStartPosY + this.pos.dragStartY - dragItemTop) / this.scale;
    const containerWidth = this.wholePageDom.clientWidth;
    const containerHeight = this.wholePageDom.clientHeight;
    const minLeft = -dragItemLeft / this.scale;
    const maxLeft = containerWidth - this.drag.clientWidth - dragItemLeft / this.scale;
    const minTop = -dragItemTop / this.scale;
    const maxTop = containerHeight - this.drag.clientHeight - dragItemTop / this.scale;
    if (left <= minLeft) {
      left = minLeft;
    } else if (left >= maxLeft) {
      left = maxLeft;
    }
    if (top <= minTop) {
      top = minTop;
    } else if (top >= maxTop) {
      top = maxTop;
    }
    this.drag.style.left = left + "px";
    this.drag.style.top = top + "px";
    // this.afterMove({left, top});
    this.$emit("afterMove", this.drag, { left, top });
  }
  private upFn(e: any) {
    e.preventDefault();
    if (!this.isUp) {
      this.isUp = true;
    }

    this.$emit("releaseEventTriggered", e);
    if (!this.isDragDown || !this.drag) {
      return;
    }
    this.isDragDown = false;
    if (this.isMoved) {
      this.isMoved = false;
      if (this.needTransitionWhenDragOver) {
        this.drag.style.transition = `all ${this.transitionTime}ms ease-in-out`;
      }
      this.$emit("dragHasMovedEnded", this.drag);
    } else {
      this.$emit("dragNoMoveEnded", this.drag);
    }
    this.$emit("beforeResetDragInfo", this.drag);
    this.drag = null;
    this.pos.mouseStartPosX = 0;
    this.pos.mouseStartPosY = 0;
    this.pos.dragStartX = 0;
    this.pos.dragStartY = 0;
    this.pos.dragStartX = 0;
    this.pos.dragStartY = 0;
    this.$emit("afterResetDragInfo");
    document.removeEventListener("mousemove", this.moveFn);
    document.removeEventListener("mouseup", this.upFn);
    document.removeEventListener("touchmove", this.moveFn);
    document.removeEventListener("touchend", this.upFn);
  }
}
</script>
<style scoped lang="less"></style>
