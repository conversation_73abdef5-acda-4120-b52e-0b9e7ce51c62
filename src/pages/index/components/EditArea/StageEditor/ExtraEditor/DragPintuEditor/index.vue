<template>
  <div class="puzzle-cut-container" ek-box="pageBg">
    <div class="prop-item-title-top">
      <span class="title">拼图属性</span>
    </div>
    <div class="container">
      <ya-label label="是否开启" />
      <el-switch v-model="showPuzzle" size="small"/>
    </div>

    <div v-if="showPuzzle">
      <div class="prop-item-title-between">
        <span class="title">目标图片</span>
      </div>
      <div class="prop-item-content">
        <div class="puzzle-preview">
          <span class="puzzle-preview-box" @click="changeCutRange">
            <span class="puzzle-preview-box-inner" :style="`background: url(${picUrl}) no-repeat ${-postion.left * preViewScale}px ${-postion.top * preViewScale}px/cover`"></span>
            <span :class="['puzzle-preview-line', cutClass]"></span>
            <span class="puzzle-preview-box-message">修改选区</span>
          </span>
          <div class="puzzle-button-container">
            <el-button class=" puzzle-opacity" @click="uploadPic" style="height:28px">
              <span style="font-size:12px">选择图片</span>
            </el-button>
            <el-select class="puzzle-select" v-model="cutType" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="puzzle-tips">小提示：可点击图片修改选区</div>
      <el-button class=" puzzle-btn" @click="handleCut" style="height:28px">
        <span style="font-size:12px">确认切割</span>
      </el-button>
      <div class="prop-item-title-between prop-item-title-between-tips">
        <!-- <span class="title">拼图块配置</span> -->
        <!-- <el-tooltip content="拼图块随机打散排列" placement="bottom"> <i class="el-icon-info"/></el-tooltip> -->
      </div>
      <el-button class=" puzzle-btn" style="height:28px" @click="handleRadom">
        <span style="font-size:12px">重新排位</span>
      </el-button>
      <div class="puzzle-cut-select" :style="selectStyle" ref="select" v-if="showCutRange && picData.width">
        <Dragger @afterMove="changePos" :scale="imgScale">
          <div :class="['puzzle-cut-select-box', cutClass]" :style="boxStyle"></div>
        </Dragger>
        <img id="puzzle-pic" class="puzzle-cut-select-pic" :src="picUrl" />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
interface PicSize {
  width: number;
  height: number;
}
interface Postion {
  left: number;
  top: number;
}
interface PuzzleInfo {
  url: string;
  top: number;
  left: number;
  containerLeft: number;
  containerTop: number;
  containerWidth: number;
  containerHeight: number;
  itemMidSplit: number;
  itemBorderSplit: number;
  width: number;
  height: number;
  cutType: number;
  cutNum: number[];
  imgList: string[];
}
import bus from "@/pages/index/common/utils/bus";
import { Component, Vue, Prop } from "vue-property-decorator";
import Dragger from "./Dragger/index.vue";
import { getImgFile } from "@/common/utils/imgUrl2File";
import { PixelToPicUrlQuence } from "@/common/utils/renderTextureToPicture";
import { getRadomArr } from "@/common/utils/utils";
import Label from "@/components/Label/index.vue";
import { UpdateLevelType } from "@/pages/index/store";
import { Message } from "element-ui";

@Component({
  components: { Dragger, YaLabel: Label },
})
export default class DragPintuEditor extends Vue {
  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  private readonly options = [
    {
      label: "4*4",
      value: 1,
      cutNum: [4, 4],
    },
    {
      label: "4*3",
      value: 2,
      cutNum: [4, 3],
    },
    {
      label: "3*3",
      value: 3,
      cutNum: [3, 3],
    },
  ];

  // 图片区域的最大尺寸
  private readonly maxSize: number = 350;
  private showCutRange = false;

  private cutSize: PicSize = {
    width: 0,
    height: 0,
  };

  private postion: Postion = {
    left: 0,
    top: 0,
  };
  private selectStyle: any = {};
  private imgScale = 1;
  private preViewScale = 1;
  private debouceTime = 0.5;
  private resetTime = 0;
  private cutType = 1;

  private picUrl = "";
  private picData: PicSize = {
    width: 0,
    height: 0,
  };
  private boxStyle = {};
  // 切割后的图片
  private imgList: any[] = [];
  private answer = {
    left: 667,
    top: 102,
    url: 'https://yaya.cdnjtzy.com/dragAreaImg-709de6.png',
  };
  private canForceCut = false;

  private otherDragToPintu = false;
  private get cutNum() {
    return this.options[this.cutType - 1].cutNum || [4, 4];
  }

  private get puzzleInfo(): PuzzleInfo | null {
    return JSON.stringify(this.params.puzzleInfo) !== "{}" ? this.params.puzzleInfo : null;
  }

  private get showPuzzle(): boolean {
    return this.params.showPuzzle;
  }

  private set showPuzzle(val) {
    this.setStateProperty(this.params, "showPuzzle", val);
    this.setStateProperty(this.params, "puzzleInfo", {});
    this.cutSize = {
      width: 0,
      height: 0,
    };
    this.imgScale = 1;
    this.preViewScale = 1;
    this.picUrl = "";
    this.picData = {
      width: 0,
      height: 0,
    };
    this.selectStyle = {};
    this.postion = {
      left: 0,
      top: 0,
    };
  }

  public get cutClass() {
    const cutNum = this.options[this.cutType - 1].cutNum || [4, 4];
    return `puzzle-${cutNum[0]}-${cutNum[1]}`;
  }

  get extraConfig() {
    return this.$store.state.template && this.$store.state.template.extraConfig;
  }

  get dragableObject() {
    let config = {};
    this.extraConfig.forEach((cfg: any) => {
      if (cfg.key === "dragableObject") {
        config = cfg;
      }
    });
    return config;
  }

  private get componentMap() {
    return this.$store.state.componentMap;
  }

  public mounted() {
    if (this.puzzleInfo) {
      const { left, top, width, height, url, cutType, imgList } = this.puzzleInfo;
      this.addMaterialPic({
        type: 'img',
        data: [{ fileUrl: url, picHeight: height, picWidth: width }]
      });
      this.postion = {
        left,
        top,
      };
      this.cutType = cutType;
      this.imgList = imgList;
      // 校验h5拼图题的唯一性
      this.checkPairUnique();
    }
    this.otherDragToPintu = !this.showPuzzle;
  }

  private uploadPic() {
    this.showCutRange = false;
    bus.$emit("material-library", {
      limitSelect: true,
      callback: this.addMaterialPic,
    });
  }

  private addMaterialPic({ type, data }: { type: any, data: any[] }) {
    // 没有选中图片时 不做任何的处理
    if(!data.length) {
      return;
    }
    if (!['img', 'album'].includes(type)) {
      Message.error(`编辑器不支持${type}类型的资源`);
      return;
    }
    const { picHeight = 0, picWidth = 0, fileUrl = "" } = data[0] || {};
    if (fileUrl) {
      if (fileUrl.indexOf(".gif") !== -1) {
        Message.error("编辑器不支持gif");
        data[0].fileUrl = ''
        this.picUrl = '';
        if(this.puzzleInfo) {
          (this.puzzleInfo as any).url = ''
        }
        return;
      }
      const min = Math.min(picWidth, picHeight);
      const max = Math.max(picWidth, picHeight);
      const scale = Math.round((this.maxSize / max) * 100) / 100;
      //   const base = Math.round(((1 - scale) / 2) * 100) / 100;
      this.cutSize = {
        width: min,
        height: min,
      };
      this.imgScale = scale;
      this.preViewScale = Math.round((150 / min) * 100) / 100;
      this.picUrl = fileUrl;
      this.picData = {
        width: picWidth,
        height: picHeight,
      };
      this.selectStyle = {
        width: picWidth + "px",
        height: picHeight + "px",
        transform: ` scale(${this.imgScale})`,
      };
      this.postion = {
        left: 0,
        top: 0,
      };
    }
  }

  private changeCutRange() {
    if (this.cutSize.width === 0) {
      return;
    }
    this.boxStyle = {
      width: this.cutSize.width + "px",
      height: this.cutSize.height + "px",
      left: this.postion.left + "px",
      top: this.postion.top + "px",
    };
    this.showCutRange = !this.showCutRange;
  }

  private convertCanvasToImage() {
    const { width, height } = this.picData;
    const { left, top } = this.postion;
    const cutNum = this.cutNum;
    const cutWidth = Math.round((this.cutSize.width / cutNum[0]) * 100) / 100;
    const cutHeight = Math.round((this.cutSize.height / cutNum[1]) * 100) / 100;
    const img = new Image();
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    const imgList: any[] = [];
    canvas.width = width;
    canvas.height = height;
    img.setAttribute("crossOrigin", "anonymous");
    img.src = this.picUrl;
    img.width = width;
    img.height = height;
    img.onload = () => {
      ctx && ctx.drawImage(img, 0, 0);
      for (let j = 0; j < +cutNum[1]; j++) {
        for (let i = 0; i < +cutNum[0]; i++) {
          const canvas2 = document.createElement("canvas");
          const ctx2 = canvas2.getContext("2d") as CanvasRenderingContext2D;
          canvas2.width = cutWidth;
          canvas2.height = cutHeight;
          const data = ctx.getImageData(left + i * cutWidth, top + j * cutHeight, cutWidth, cutHeight);
          data && ctx2.putImageData(data, 0, 0);
          imgList.push(getImgFile(canvas2.toDataURL("image/png")));
        }
      }
      this.uploadFileList(imgList);
    };
  }

  private uploadFileList(fileList: any[]) {
    bus.$emit("showPageLoading", true);
    PixelToPicUrlQuence.Instance.starUploadPicList(fileList, (urls: string[]) => {
      if (urls.length === fileList.length) {
        this.imgList = urls;
        this.handleRadom(true);
        bus.$emit("showPageLoading", false);
      } else {
        this.$message("切割图片处理失败请重试！");
        bus.$emit("showPageLoading", false);
      }
    });
  }

  private handleRadom(needUpdate?: boolean) {
    if (needUpdate !== true && !this.puzzleInfo) {
      return;
    }
    const puzzleInfo = {
      url: this.picUrl,
      top: this.postion.top,
      left: this.postion.left,
      containerLeft: this.puzzleInfo?.containerLeft || 0,
      containerTop: this.puzzleInfo?.containerTop || 0,
      containerWidth:  this.puzzleInfo?.containerWidth || 0,
      containerHeight:  this.puzzleInfo?.containerHeight || 0,
      itemMidSplit:  this.puzzleInfo?.itemMidSplit || 14,
      itemBorderSplit:  this.puzzleInfo?.itemBorderSplit || 18,
      width: this.picData.width,
      height: this.picData.height,
      cutType: this.cutType,
      cutNum: this.cutNum,
      imgList: this.imgList,
    };
    if (needUpdate === true) {
      this.updatePuzzle(this.imgList, 'cut', puzzleInfo);
    } else {
      const deltaTime = new Date().getTime() / 1000 - this.resetTime;
      console.warn(deltaTime, "deltaTime");
      if (deltaTime <= this.debouceTime) {
        console.warn("快速点击");
      } else {
        this.updatePuzzle(this.imgList, 'sort');
      }
      this.resetTime = new Date().getTime() / 1000;
    }
  }

  private handleCut() {
    this.showCutRange = false;
    if(this.canForceCut) {
      this.canForceCut = false;
      bus.$emit("showPageLoading", true);
      this.convertCanvasToImage();
    }
    if (this.puzzleInfo) {
      const { left, top, url, cutType } = this.puzzleInfo;
      if (this.postion.left === left && this.postion.top === top && url === this.picUrl && cutType === this.cutType) {
        return;
      }
    } else if (!this.picUrl) {
      return;
    }
    this.$confirm("确认切割会清空所有拖拽元素和答题区哦～", "温馨提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      closeOnClickModal: false,
    })
      .then(() => {
        bus.$emit("showPageLoading", true);
        if (this.puzzleInfo && !this.puzzleInfo?.containerWidth) {
          this.setStateProperty(this.params, "puzzleInfo", {
            ...this.puzzleInfo,
            ...this.getDragContainerArea()
          });
        }
        this.convertCanvasToImage();
      })
      .catch(() => {
        this.$message({
          type: "info",
          message: "已取消",
        });
      });
  }

  private getDragContainerArea() {
    let minLeft = 0;
    let minTop = 0;
    let rowCount = 0;
    let itemWidth = 0;
    let itemSplit = 0;
    for (const key in this.componentMap) {
      const { tag, properties } = this.componentMap[key];
      if(tag === "dragableObject") {
        const tempX = properties.x + 640 - properties.width / 2;
        const tempY = 360 - properties.y - properties.height / 2;
        minLeft = minLeft ? Math.min(tempX || 0, minLeft) : tempX;
        minTop = minTop ? Math.min(tempY || 0, minTop) : tempY;
        itemWidth = properties.width;
        if(minTop === tempY && tempX > minLeft) {
          console.log(tempX, minLeft);
          itemSplit = itemSplit ? Math.min(itemSplit || 0, tempX - minLeft - itemWidth) : tempX - minLeft - itemWidth;
        }
        if (!minTop || minTop === tempY) {
          rowCount++;
        }
      }
    }
    return {
      left: minLeft,
      top: minTop,
      containerWidth: itemWidth * rowCount + itemSplit * ( rowCount - 1 )
    }
  }

  private changePos(drag: HTMLElement, pos: { left: number; top: number }) {
    this.postion = {
      left: Math.round(pos.left * 100) / 100,
      top: Math.round(pos.top * 100) / 100,
    };
  }

  private updatePuzzle(imgList: string[], type: 'cut' | 'sort', puzzleInfo?: PuzzleInfo) {
    if (puzzleInfo) {
      //修改图片配置
      this.setStateProperty(this.params, "puzzleInfo", puzzleInfo);
    }
    if(type === 'cut') {
      // 判断是否重新切割了
      this.deleteDragComponents();
      this.insertOption(imgList); 
    } else {
      this.refreshSort(imgList)
    }
  }

  private refreshSort(imgList: string[]) {
    const tempImgList = imgList.slice(0);
    const randomData: any[] = [];
    Object.keys(this.componentMap).forEach(compId => {
      const { tag, extra } = this.componentMap[compId];
      // 拖拽元素
      if (tag === "dragableObject") {
        const random = Math.floor(Math.random() * tempImgList.length);
        const randomImg = tempImgList.splice(random, 1)[0];
        // find randomImg PreCompId
        const PrecompId = Object.keys(this.componentMap).find((id) => {
          const { tag, properties } = this.componentMap[id];
          return tag === "dragableObject" && properties.texture === randomImg;
        }) || ''
        const PrecompType = this.componentMap[PrecompId]?.extra.type;
        const PrecompAnswerId = Object.keys(this.componentMap).find((id) => {
          const { tag, extra = {} } = this.componentMap[id];
          return tag === "dragArea" && extra.type === PrecompType;
        })
        randomData.push({
          id: compId,
          img: randomImg
        })
        randomData.push({
          id: PrecompAnswerId,
          type: extra.type
        })
      }
    })
    randomData.forEach((item) => {
      const { id, img, type } = item;
      if(!id) return;
      
      if(img) {
        this.$store.commit("updateComponentProperties", {
          id,
          newProperties: {
            texture: img,
          },
        });
      } else {
        this.$store.commit("updateComponentExtra", {
          id,
          newExtra: {
            type: type,
          },
        });
      }
    })
  }

  private checkPairUnique() {
    const temp: Array<{ id: string, type: number, answerId: string }> = [];
    const types: string[] = [];
    let len = 0;
    Object.keys(this.componentMap).forEach(compId => {
      const { tag, extra } = this.componentMap[compId];
      // 拖拽元素
      if (tag === "dragableObject") {
        len++;
        const compType = extra.type;
        const compAnswerIds = Object.keys(this.componentMap).filter((id) => {
          const { tag, extra = {} } = this.componentMap[id];
          return tag === "dragArea" && extra.type === compType;
        })
        // 一个type 一个拖拽元素 一个答题区
        if(!types.includes(compType) && compAnswerIds.length ===1) {
          types.push(compType);
          temp.push({
            id: compId,
            type: compType,
            answerId: compAnswerIds[0]
          })
        }
      }
    })
    if(len > temp.length) {
      this.canForceCut = true;
      this.$message.error('当前题目数据存在问题，请点击【确认切割】进行数据修复');
    }
  }

  private insertOption(imgList: string[]) {
    this.setStateProperty((this.dragableObject as any).params, "options", []);
    const { cutNum = [4, 4], containerLeft = 113, containerTop = 96, itemMidSplit = 14, containerWidth = 0 } = this.puzzleInfo || {};
    const top = containerTop || 96;
    const left = containerLeft || 113;
    const basketTop = this.answer.top || 102;
    const basketLeft = this.answer.left || 667;
    const boxSize = containerWidth || 420; // 区域实际尺寸
    const margin = itemMidSplit || 14; // 间距
    const width = Math.round((boxSize - (cutNum[0] -1) * margin)/cutNum[0] * 10) / 10;
    const height = Math.round((boxSize - (cutNum[1] -1) * margin)/cutNum[1] * 10) / 10;
    const radomArr = getRadomArr(cutNum[0] * cutNum[1]);
    for (let j = 0; j < +cutNum[1]; j++) {
      for (let i = 0; i < +cutNum[0]; i++) {
        const num = radomArr[i + j * cutNum[0]];
        const url = imgList[num - 1] || "";
        const dragComponent = {
          type: "sprite",
          dragable: true,
          deletable: true,
          tag: "dragableObject",
          properties: {
            active: true,
            width,
            height,
            texture: url,
            x: left + i * (width + margin) - 640 + width / 2,
            y: 360 - j * (height + margin) - height / 2 - top,
          },
          extra: {
            num: 1,
            type: String(num),
            tag: "dragableObject",
          },
        };
        this.$store.dispatch("addComponentNoFocus", dragComponent);
        const dragAreaUrl = this.answer.url;
        const dragAreaComponent = {
          type: "sprite",
          dragable: true,
          deletable: true,
          tag: "dragArea",
          properties: {
            active: true,
            width,
            height,
            texture: dragAreaUrl,
            x: basketLeft + i * width - 640 + width / 2,
            y: 360 - (basketTop + j * height) - height / 2,
          },
          extra: {
            position: [
              {
                endX: basketLeft + i * width + "",
                endY: 120 + basketTop + j * height,
              },
            ],
            type: String(i + j * cutNum[0] + 1),
            tag: "dragArea",
          },
        };
        this.$store.dispatch("addComponentNoFocus", dragAreaComponent);
      }
      this.sortDragComponents();
      const dragOptions = [];
      for (let i = 0; i < cutNum[0] * cutNum[1]; i++) {
        dragOptions.push({ label: JSON.stringify(i + 1), value: JSON.stringify(i + 1) });
      }
      this.setStateProperty((this.dragableObject as any).params, "options", dragOptions);
    }
  }

  private setAnswerOptions({ left, top, url }: {
    left: number,
    top: number,
    url: string,
  }) {
    this.answer.left = left;
    this.answer.top = top;
    this.answer.url = url || this.answer.url;
  }

  private deleteDragComponents() {
    const deleteComponentIds = [];
    let minLeft = 0;
    let minTop = 0;
    let url = '';
    for (const key in this.componentMap) {
      const { tag, properties } = this.componentMap[key];
      if (tag === "dragableObject" || tag === "dragArea") {
        deleteComponentIds.push(key);
      }
      if(tag === "dragArea") {
        const tempX = properties.x + 640 - properties.width / 2;
        const tempY = 360 - properties.y - properties.height / 2;
        minLeft = minLeft ? Math.min(tempX || 0, minLeft) : tempX;
        minTop = minTop ? Math.min(tempY || 0, minTop) : tempY;
        url = properties.texture;
      }
    }
    if(this.otherDragToPintu) {
      this.otherDragToPintu = false;
    } else {
      this.setAnswerOptions({
        left: minLeft,
        top: minTop,
        url
      });
    }
    deleteComponentIds.length > 0 && this.$store.dispatch("removeComponents", deleteComponentIds);
  }

  private sortDragComponents() {
    const ids = [];
    for (const key in this.componentMap) {
      const tag = this.componentMap[key].tag;
      if (tag === "dragableObject") {
        ids.push(key);
      }
    }
    this.$store.dispatch("updateComponentsLevel", {
      ids: ids,
      type: UpdateLevelType.TOP,
    });
  }

  private setStateProperty(target: object, key: string, value: any) {
    this.$store.commit("setStateProperty", {
      target,
      key,
      value,
    });
  }
}
</script>
<style scoped lang="less">
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 10px;
  }

  .el-switch {
    margin-right: 0;
  }
}
.puzzle-cut-container {
  // width: 328px;
  padding: 0px 16px 0;

  .puzzle-cut-select {
    position: fixed;
    right: 360px;
    top: 52px;
    overflow: hidden;
    transform-origin: 100% 0;
    &-pic {
      width: 100%;
      height: 100%;
    }
    &-box {
      width: 50px;
      height: 50px;
      position: absolute;
      cursor: move;
      opacity: 0.7;
      &::after {
        content: "";
        display: inline-block;
        width: 100%;
        height: 100%;
        border: 2000px solid rgba(0, 0, 0, 1);
        position: absolute;
        top: -2000px;
        left: -2000px;
        // cursor: default;
      }
    }
  }
  .puzzle-preview {
    flex: 1;
    display: flex;
    // position: relative;
    // justify-content: space-between;
    &-line {
      width: 150px;
      height: 150px;
      position: absolute;
      top: 0px;
      left: 0px;
    }
    &-box {
      border-radius: 2px;
      box-sizing: border-box;
      border: 1px solid rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      overflow: hidden;
      &-inner {
        // position: absolute;
        width: 150px;
        height: 150px;
        // background-size: 100% 100% !important;
      }
      &-message {
        display: none;
      }
      &:hover {
        .puzzle-preview-box-inner {
          opacity: 0.7;
        }
        .puzzle-preview-box-message {
          display: inline-block;
          position: absolute;
        }
      }
    }

    .puzzle-button-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      flex: 1;
    }
    .puzzle-opacity {
      position: relative;
      width: 110px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      color: var(--font-gray-darker);
      cursor: pointer;
    }
    .puzzle-select {
      width: 110px;
      margin-top: 20px;
    }
  }
  .puzzle-tips {
    font-size: 12px;
    margin: 10px 0;
  }
  .puzzle-btn {
    width: 100%;
    // margin-top: 10px;
  }
  .prop-item-title-top {
    margin-bottom: 10px;
    justify-content: left;
    align-items: center;
    &-tips {
      margin-top: 20px;
    }
  }
  .prop-item-title-between {
    margin-bottom: 10px;
    justify-content: left;
    align-items: center;
    &-tips {
      margin-top: 20px;
    }
  }

  .puzzle-4-4 {
    background-image: url("https://img.zuoyebang.cc/cw_67e3bff142e9a1c5ed3ba81814ed8da3.png");
    background-size: contain;
  }
  .puzzle-3-3 {
    background-image: url("https://img.zuoyebang.cc/cw_2767940a7323de14d53270524e929fb7.png");
    background-size: contain;
  }
  .puzzle-4-3 {
    background-image: url("https://img.zuoyebang.cc/cw_d06a7a03ca60a12a2a2ebd80c8bcd5f3.png");
    background-size: contain;
  }

  .puzzle-cut-container .puzzle-btn {
    width: 100%;
  }
}
</style>
