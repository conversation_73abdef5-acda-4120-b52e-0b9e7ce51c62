<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-select v-model="v" :placeholder="placeholder" v-bind="params" size="small">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.label"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import { specialAddComponent } from "@/pages/index/common/utils/componentOperation";
import { MessageBox } from "element-ui";
@Component
export default class KeyboardSelect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;
  subType = "";
  get options() {
    const optionsList = [];
    for (const item of this.params.options) {
      optionsList.push({ label: item["label"], value: item["value"] });
    }

    return optionsList;
  }

  addKeyboard(label: string) {
    let subType = "";
    for (const item of this.options) {
      if (item.label == label) {
        subType = item.value;
        break;
      }
    }
    if (subType) {
      specialAddComponent(subType);
    }
  }
  updateCompomentExtraStageData(tag: string, tagType:string, newExtra: any) {
    for (const key in this.$store.state.componentMap) {
      const item = this.$store.state.componentMap[key];
      if (item.tag === tag && typeof item.extra[tagType] != "undefined") {
        this.$store.commit("updateComponentExtra", {
          id: item.id,
          newExtra:newExtra,
        });
      }
    }
  }
  nextSetV(val: string) {
    this.updateCompomentExtraStageData("blankModule","characterLimit", {"characterLimit": "1"});
    this.updateCompomentExtraStageData("blankModule","correctArray", {"correctArray": [""],hasAllCorrect:false});
    const subType = this.subType;
    for (const key in this.$store.state.componentMap) {
      const item = this.$store.state.componentMap[key];
      if (item.type === "specialComponent") {
        switch (item.subType) {
          case subType:
            this.$store.dispatch("removeComponents", [item.id]);
            break;
        }
      }
    }
    this.addKeyboard(val);
    this.v = val;
  }
  findTagCompomentList(tag: string) {
    const list = [];
    for (const key in this.$store.state.componentMap) {
      const item = this.$store.state.componentMap[key];
      if (item.tag === tag) {
        list.push(item);
      }
    }
    return list;
  }
  get v() {
    let subTypeLabel = "";
    for (const key in this.$store.state.componentMap) {
      const item = this.$store.state.componentMap[key];
      if (item.type === "specialComponent") {
        switch (item.subType) {
          case SpecialComponentSubTypes.KEYBOARD_ENGLISH:
            subTypeLabel = "英文键盘";
            this.subType = SpecialComponentSubTypes.KEYBOARD_ENGLISH;
            break;
          case SpecialComponentSubTypes.KEYBOARD:
            subTypeLabel = "数学键盘";
            this.subType = SpecialComponentSubTypes.KEYBOARD;
            break;
        }
      }
    }
    return subTypeLabel;
  }

  set v(val) {
    if (val === this.v) return; // 更换键盘
    const list = this.findTagCompomentList("blankModule");
    if (list.length>0) {
      MessageBox.confirm("切换键盘后将重置答题区‘字符限制’，‘答案’", "温馨提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        this.nextSetV(val);
      });
    } else {
      this.nextSetV(val);
    }
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-select {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
</style>
