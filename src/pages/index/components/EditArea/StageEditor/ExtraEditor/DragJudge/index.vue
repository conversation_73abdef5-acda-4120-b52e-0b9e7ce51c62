<template>
  <div class="container drag-judge">
    <ya-label :label="label" :description="description" />
    <el-radio-group v-model="switchVal" size="small">
      <el-radio v-for="(item, index) in radioOptions" :key="index" :label="item.value" size="small" style="margin: 0 6px;">{{ item.label }}</el-radio>
    </el-radio-group>
    <!-- <el-switch v-model="switchVal" size="small" /> -->
    <div v-if="switchVal === true || switchVal === 1" style="margin: 12px 0px; display: flex;">
      <ya-label label="总数值" />
      <el-input v-model="numberVal" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,1})?).*$/g, '$1')" size="small" type="text" style="width: 90px; height: 24px; line-height: 24px;margin: 0 12px;" @blur="handleBlur" />
      <el-select v-model="signVal" placeholder="运算规则" size="small" style="width: 100px;margin-left: 12px;">
        <el-option :key="0" label="加法" :value="0"></el-option>
        <el-option :key="1" label="乘法" :value="1"></el-option>
      </el-select>
    </div>
    <!-- dragSumArray -->
    <template v-if="switchVal === 2">
      <div v-for="(item, index) in dragSumArray" style="padding: 12px 0px; display: flex;border-bottom: 1px solid #f0f1f5;" :key="index">
        <ya-label :label="`分区${index + 1}`" />
        <div>
          <div style="display: flex;">
            <el-input v-model.number="item.dragSum" size="small" onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,1})?).*$/g, '$1')" type="text" style="width: 100px; height: 24px; line-height: 24px;" @blur="handleBlur" placeholder="请输入数值" />
            <el-select v-model="item.dragSign" placeholder="运行规则" size="small" style="width: 100px;margin-left: 12px;">
              <el-option :key="0" label="加法" :value="0"></el-option>
              <el-option :key="1" label="乘法" :value="1"></el-option>
            </el-select>
          </div>
          <el-select v-model="item.dragAreaId" placeholder="答题区选择" size="small" style="width: calc(100%);margin-top: 10px;" :multiple="true">
            <el-option :label="item.label" v-for="(item, index) in dragAreaOptions" :key="index" :value="item.value"></el-option>
            <!-- <el-option :key="1" label="乘法" :value="1"></el-option> -->
          </el-select>
        </div>
        <el-button
          type="danger"
          icon="el-icon-delete"
          circle
          style="width: 20px; height: 20px; margin-left: 15px;"
          @click="
            () => {
              handleDelete(index);
            }
          "
          v-show="dragSumArray.length > 1"
        ></el-button>
      </div>
      <el-button class="add-button" :disabled="dragSumArray.length >= 20" type="success" round @click="handleAdd">增加分区</el-button>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import { cloneDeep } from "lodash-es";
// dragNumberAnswerJudge undefined true false
// 无 dragNumberAnswerJudge false || undefined
// 总数值判定 dragNumberAnswerJudge true || 1  dragSum "" dragSign (undefined || 0 // 加法) 1 // 乘法
// 区域数值判定 dragNumberAnswerJudge  2  dragSumArray [{...}]
@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraDragJudge extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: false,
  })
  defaultValue!: string;

  @Prop({
    default: {
      params: {
        relative: {
          key: "dragSum",
          label: "",
          required: true,
          type: "input",
          params: {},
        },
      },
    },
  })
  config!: any;

  radioOptions = [
    {
      label: "无",
      value: 0,
    },
    {
      label: "总数值",
      value: 1,
    },
    {
      label: "分区数值",
      value: 2,
    },
  ];

  description = "1.总/分区数值可输入小于10000的整数；2.判定正确时，需要答案区吸附的元素数值等于总/分区数值";

  get inputProperty() {
    return this.config.params.relative.key;
  }
  get min() {
    return this.config.params.relative.params.min;
  }
  get max() {
    return this.config.params.relative.params.max || 10000;
  }
  // get dragArea
  get dragAreaOptions() {
    const dragAreaComp = Object.values(this.$store.state.componentMap).filter((comp: any) => comp.tag === "dragArea");
    return dragAreaComp.map((item: any) => {
      return {
        label: `答题区ID:${item.id}`,
        value: item.id,
      };
    });
  }

  handleAdd() {
    if(this.dragSumArray.length >= 20) return;
    // dragSumArray
    this.dragSumArray = [
      ...this.dragSumArray,
      {
        dragSum: "",
        dragSign: undefined,
        dragAreaId: [],
      },
    ];
  }

  handleDelete(index: number) {
    this.dragSumArray.splice(index, 1);
  }

  // 运算符号 0 加法 1 乘法
  get signVal() {
    const val = this.$store.state.extraStageData["dragSign"];
    return val;
  }

  set signVal(val) {
    if (val === this.signVal) return;
    this.$store.commit("updateExtraStageData", {
      ["dragSign"]: val,
    });
  }

  dragSumArray = this.$store.state.extraStageData["dragSumArray"]
    ? cloneDeep(this.$store.state.extraStageData["dragSumArray"])
    : [
        {
          dragSum: "",
          dragSign: undefined,
          dragAreaId: [],
        },
      ];

  @Watch("dragSumArray", { deep: true })
  dragSumArrayChange() {
    // const tempArr = cloneDeep(this.dragSumArray);
    this.dragSumArray.forEach((item: { dragSum: any; }) => {
      let tempSum = item.dragSum;
      if(typeof (tempSum as any) === 'undefined' || tempSum === '') {
        console.log('不处理');
      } else {
        const { max, min } = this;
        if (max && tempSum > max) tempSum = max;
        if (min && tempSum < min) tempSum = min;
        item.dragSum = tempSum;
      }
    })
    this.$store.commit("updateExtraStageData", {
      ["dragSumArray"]: cloneDeep(this.dragSumArray),
    });
  }

  get switchVal() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set switchVal(val) {
    if (val === this.switchVal) return;
    this.dragSumArray = val === 2 ? [{
      dragSum: "",
      dragSign: undefined,
      dragAreaId: [],
    }] : [];
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
      ["dragSumArray"]: this.dragSumArray,
      [this.inputProperty]: undefined,
      ["dragSign"]: undefined,
    });
  }

  get numberVal() {
    const val = this.$store.state.extraStageData[this.inputProperty];
    return val;
  }

  set numberVal(val) {
    val = String(val);
    const t = val.charAt(0);
    let temp = val.replace(/[^\d]/g, "");
    if (t === "-" && temp) {
      val = "-" + temp;
    }
    const { max, min } = this;
    if (max && val > max) temp = max;
    if (min && val < min) temp = min;
    if (val === "") temp = "";
    if (String(temp).at(-1) !== ".") {
      temp = Number(temp);
    }
    // 正则匹配 数值
    this.$store.commit("updateExtraStageData", {
      [this.inputProperty]: temp,
    });
  }

  handleBlur() {
    if (String(this.numberVal).at(-1) === ".") {
      this.numberVal = String(Number(this.numberVal));
    }
  }
  mounted() {
    this.dragSumArray.forEach((item: any) => {
      item.dragAreaId.forEach((id: any) => {
        if(!this.dragAreaOptions.find(opt => opt.value === id)) {
          item.dragAreaId = item.dragAreaId.filter((tempId: any) => tempId !== id)
        }
      })
    })
  }
}
</script>

<style scoped lang="less">
@DANGER_COLOR: #f56c6c;
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;
  &.drag-judge {
    display: block !important;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 10px;
  }

  .el-switch {
    margin-right: 0;
  }
  /deep/ .add-button {
    width: 100%;
    cursor: pointer;
    margin-top: 12px;
  }
  /deep/ .el-button--danger {
    margin-top: 6px;
    margin-left: 15px;
    height: 22px;
    width: 22px;
    i {
      font-size: 12px;
    }
    &:hover {
      color: @DANGER_COLOR;
      border-color: @DANGER_COLOR;
      background-color: #fff;
    }
  }
}
</style>
