<template>
  <el-dialog
    title="添加答案"
    width="602px"
    height="80vh"
    top="0vh"
    :custom-class="`set-answer-dialog`"
    :close-on-click-modal="false"
    :visible="visible"
    @close="onDialogClose">
      <span class="red-tips">请先作答，正确后的作答结果，将自动设置为答案</span>
      <div :class="['preview-container']">
        <iframe ref="previewFrame" :src="previewUrl" v-if="previewUrl" title="previewPage" :onLoad="onLoad()"></iframe>
      </div>
    </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";

@Component
export default class PreviewModal extends Vue {
  @Prop({
    required: false,
  })
  visible!: boolean
  
  showPreview = false; // 是否渲染该组件
  previewUrl = ""; // 是否渲染该组件

  created() {
    this.previewUrl = this.$getPageConfigByKey('getAnswerPreviewUrl')();
    bus.$on("showDialogPreview", () => {
      this.showPreview = true;
    });

    bus.$on("closeDialogPreview", () => {
      this.showPreview = false;
    });
  }

  onDialogClose() {
    console.log("onDialogClose");
    bus.$emit("closeDialogPreview");
  }

  /**
   * 初始化该组件和打开关闭弹窗时都会触发onload事件，只在showPreview处理数据的同步
   */
  onLoad() {
    console.log("preview.page---onLoad", this.showPreview);
    // 通知setAnswer组件， 发消息
    bus.$emit("dialogPreviewLoaded");
  }
}
</script>

<style scoped lang="less">
.question-answer {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    margin-left: 5px;
  }
  .required {
    &::before {
      content: "*";
      color: red;
    }
  }
  .tips {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
  }
}


</style>
<style lang="less">
.set-answer-dialog {
  .el-dialog__body {
    // height: 300px !important;
    padding: 0 !important;
    margin-bottom: 10px;
    .red-tips {
      padding-left: 10px;
      margin-bottom: 10px;
      color: #fa574b;
      display: block;
      width: 100%;
      text-align: left;
      box-sizing: border-box;
    }
  }
  .preview-container {
    width: 600px;
    height: 440px;
    display: flex;
    iframe {
      flex: 1;
    }
  }
}
</style>
