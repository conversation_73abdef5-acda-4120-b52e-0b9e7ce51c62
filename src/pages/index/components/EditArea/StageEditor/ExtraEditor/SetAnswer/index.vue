<template>
  <div class="set-answer-contanier">
    <div class="question-answer">
      <span class="label required">答案</span>
      <el-button size="small" type="primary" @click="handleClick">{{ hasSetAnswer ? "修改答案" : "添加答案" }}</el-button>
      <span class="tips" v-if="hasSetAnswer">已添加完答案</span>
    </div>
  </div>
</template>

<script lang="ts">
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { postMessage, PREVIEW_DATA } from "@/common/utils/postMessage";
import bus from "@/pages/index/common/utils/bus";
import { cloneDeep, isEqual } from "lodash-es";
import { Component, Vue, Watch } from "vue-property-decorator";
import { timeTravel } from "@/pages/index/store";

@Component
export default class SetAnswer extends Vue {
  showPreview = false; // 是否渲染该组件
  hasPostMessage = false;
  isJustChangeComp = false;
  removeSubscribeFn!: () => void;
  get isRender() {
    return this.$store.state.template.supportSetReferenceAnswer;
  }
  get referenceAnswer() {
    return this.$store.state.template.referenceAnswer;
  }
  get currentComponents() {
    return this.$store.getters.currentComponents;
  }
  get hasSetAnswer() {
    const { referenceAnswer } = this.$store.state.template;
    this.log("referenceAnswer", referenceAnswer, referenceAnswer && Object.keys(this.$store.state.template.referenceAnswer).length);
    return !!(this.referenceAnswer && Object.keys(this.referenceAnswer).length);
  }
  get componentMap() {
    return this.$store.state.componentMap;
  }
  log(...params: any) {
    console.log('FE-SetAnswer:::', ...params);
  }
  postMessage() {
    if (!this.showPreview) return;
    this.hasPostMessage = true;
    (window as MyWindow).getPreivewContent().then(async res => {
      const iframeWindow = await this.getIframeWindow();
      postMessage(iframeWindow, PREVIEW_DATA, {
        content: res,
        parentVersion: this.$store.state.parentVersion,
        id: Date.now(),
      });
    });
  }
  handleMessage(message: { data: { action: any; data: any; actionType: any } }) {
    if (!message.data) return;
    const { action } = message.data;
    if (!action) return;
    if (action === "preview-loaded") {
      console.log('preciew loaded down');
      this.postMessage();
    }
    if (action === "preview-reference-answer") {
      this.$store.commit("updateReferenceAnswer", {
        referenceAnswer: message.data.data,
      });
      bus.$emit("closeDialogPreview");
      // 自动关闭弹窗-待确认
      this.showPreview = false;
      (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.SetAnswer);
    }
  }
  @Watch('componentMap')
  onComponentMapChange(val: Record<string, Record<string, any>>, oldVal: Record<string, Record<string, any>>) {
    if(!this.isRender) return;
    if(!this.hasSetAnswer) return;
    this.log('onComponentMapChange', val, oldVal);
    let needClearReferenceAnswer = false;
    const oldIds = Object.keys(oldVal);
    const newIds = Object.keys(val);
    if(!oldIds.length && newIds.length) {
      return;
    }
    // 数据初始化时， componentMap为空对象
    // 对比val oldVal的变化 增删 需要判断增删组件的业务属性 学能组件的properties
    if(Object.keys(oldVal).length !== Object.keys(oldVal).length) {
      // 找到增 或 删的组件 
      // 对比两个数组 找出两个数组中不同的元素
      const addComps = newIds.filter(v => !oldIds.includes(v));
      const delComps = oldIds.filter(v => !newIds.includes(v));
      // merge两个对象
      const mergeComps = Object.assign({}, val, oldVal);
      // merge两个数组
      const  mergeChangeCompIds = [...addComps, ...delComps];

      // merge两个数组中的对象
      const mergeChangeComps = mergeChangeCompIds.map(v => mergeComps[v]);
      console.log('mergeChangeComps', mergeChangeComps);
      mergeChangeComps.forEach((comp) => {
        if(comp.tag) {
          needClearReferenceAnswer = true;
          // tag组件tag 对比extra
        } else if(comp.type === 'optionComponent') {
          needClearReferenceAnswer = true;
        }
      })
    }
    if(needClearReferenceAnswer) {
      this.clearReferenceAnswer()
    }
  }
  clearReferenceAnswer() {
    console.log('clearReferenceAnswer');
    this.$store.commit("updateReferenceAnswer", {
      referenceAnswer: {}
    });
  }
  created() {
    console.log('created-renderDialogPreview');
    // 接收父窗口消息
    window.addEventListener("message", this.handleMessage, false);
    // 接收预览弹窗的消息
    bus.$on("dialogPreviewLoaded", () => {
      if (this.hasPostMessage) return;
      this.postMessage();
    });
    bus.$on("closeDialogPreview", () => {
      this.showPreview = false;
      this.hasPostMessage = false;
    });

    // 通知渲染预览组件
    bus.$emit("renderDialogPreview");
  }
  subscribeReplaceCurrentComponentIds() {
    
    return this.$store.subscribe((mutation, state) => {
      if(!this.isRender) return;
      if(!this.hasSetAnswer) return;
      if(!this.currentComponents || !this.currentComponents[0]) return;
      this.log('mutation.type', mutation.type);
      // 过滤切换组件时更新cocos/updateComponentProperties
      if(mutation.type === 'cocos/replaceCurrentComponentIds') {
        this.isJustChangeComp = true;
      }
      if(['cocos/updateComponentProperties', 'updateComponentExtra'].includes(mutation.type)) {
        if(this.isJustChangeComp === true) {
          const timer = setTimeout(() => {
            this.isJustChangeComp = false;
            clearTimeout(timer);
          }, 500);
          return;
        }
        if(!this.currentComponents[0].tag && this.currentComponents[0].type !== 'optionComponent')  return;
        if(timeTravel.historyStates.length < 1) return;
        const preComp = cloneDeep((timeTravel.historyStates.at(-1) || { componentMap: {
          [mutation.payload.id]: {
            properties: {}
          }
        } } ).componentMap[mutation.payload.id]);
        const curComp = this.currentComponents[0];
        // 如果只是切换小题 不处理
        console.log('preComp', preComp, );
        console.log('curComp', curComp, );
        preComp.properties.subQuestionIndex = curComp.properties.subQuestionIndex
        const isSame = isEqual(preComp.properties, curComp.properties);
        console.log('isSame', isSame ? '不会清空' : '应该清空');
        if(!isSame) {
          this.clearReferenceAnswer()
          this.log('clearReferenceAnswer done', mutation.type, mutation, state);
        };
      }
    });
  }
  async mounted() {
    await this.$nextTick();
    this.removeSubscribeFn = this.subscribeReplaceCurrentComponentIds();
  }
  destroyed() {
    // this.removeSubscribeFn();
  }
  handleClick() {
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.SetAnswer);
    (window as MyWindow).getPreivewContent().then(async res => {
      this.log("getPreivewContent", res);
      if(res) {
        // 通知打开预览弹窗
        bus.$emit("showDialogPreview");
        this.showPreview = true;
        this.hasPostMessage = false;
      }
    });
    
  }
  getIframeWindow(): Promise<Window> {
    return new Promise((resolve) => {
      const timer = requestAnimationFrame(() => {
        const iframeDom = document.querySelector(".preview-container iframe");
        if (iframeDom) {
          cancelAnimationFrame(timer);
          resolve((iframeDom as any).contentWindow);
        }
      });
    })
  }
}
</script>

<style scoped lang="less">
.question-answer {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    margin-left: 5px;
  }
  .required {
    &::before {
      content: "*";
      color: red;
    }
  }
  .tips {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
  }
}
.preview-container {
  display: flex;
  iframe {
    flex: 1;
    min-width: 540px;
    width: 60vw;
    height: 60vw;
    max-height: 80vh;
    max-width: 100vh;
    min-height: 400px;
  }
}
</style>
