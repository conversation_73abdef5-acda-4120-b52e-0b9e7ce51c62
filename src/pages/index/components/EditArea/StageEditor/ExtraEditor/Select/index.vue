<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-select
      v-model="v"
      :placeholder="placeholder"
      v-bind="params"
      size="small"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class ExtraSelect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  get options() {
    return this.params.options;
  }

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-select {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
</style>
