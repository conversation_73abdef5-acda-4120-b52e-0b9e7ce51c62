<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-input v-model="v" size="small" v-bind="params" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class ExtraInput extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
</style>
