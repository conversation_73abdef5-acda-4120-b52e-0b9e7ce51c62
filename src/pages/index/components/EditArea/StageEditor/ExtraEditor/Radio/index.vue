<!--
 * @Date: 2021-08-12 16:47:28
 * @LastEditors: chxu
 * @LastEditTime: 2021-09-22 15:43:46
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/StageEditor/ExtraEditor/Radio/index.vue
 * @Author: chxu
-->
<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-radio-group v-model="v" size="small">
      <el-radio
        v-for="(item, index) in params.options"
        :key="index"
        :label="item.value"
        size="small"
      >
        {{ item.label }}</el-radio
      >
    </el-radio-group>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    yaLabel: Label,
  },
})
export default class ExtraRadio extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  @Prop({
    default: () => ({}),
  })
  config!: Record<string, any>;

  get options() {
    return this.params.options;
  }

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-radio-group {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;

    /deep/ .el-radio__label {
      padding-left: 2px;
      font-size: 13px;
    }
  }
}
</style>
