<template>
  <div :class="`${cssPrefix}`">
    <span class="label">{{ label }}</span>
    <div :class="`${cssPrefix}-wrapper`">
      <div
        :class="`${cssPrefix}-item`"
        v-for="(opt, index) in options"
        :key="opt.value"
      >
        <div>{{ opt.label }}</div>
        <el-button
          type="danger"
          icon="el-icon-delete"
          circle
          @click="
            () => {
              handleDelete(index);
            }
          "
        ></el-button>
      </div>
      <div :class="`${cssPrefix}-item`" v-show="isAdd">
        <div>
          <el-input
            v-model="tempValue"
            size="small"
            @input="handleInput"
            @keyup.enter.native="confirmAdd"
          />
        </div>
        <el-button
          icon="el-icon-check"
          circle
          plain
          @click="confirmAdd"
        ></el-button>
        <el-button
          class="danger"
          icon="el-icon-close"
          circle
          @click="cancelAdd"
        ></el-button>
      </div>
      <el-button
        class="add-button"
        type="success"
        round
        @click="handleAdd"
        :disabled="isAdd"
        >增加分类</el-button
      >
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class Options extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  cssPrefix = "options";

  isAdd = false;

  tempValue = "";

  get options() {
    return this.params.options;
  }

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }

  handleInput(val: string) {
    this.tempValue = val.replace(/[^a-zA-Z0-9]/g,'')
  }

  handleAdd() {
    this.tempValue = "";
    this.isAdd = true;
    console.log("handleAdd");
  }
  handleDelete(index: number) {
    // 验证是否被使用 已经使用的话 需要提示
    console.log("handleDelete", index);
    // componentMap extra.type
    const isUsedIds: string[] = [];
    Object.values(this.$store.state.componentMap).forEach((component: any) => {
      if (component.extra.type === this.params.options[index].value) {
        isUsedIds.push(component.id);
      }
    });
    this.$store.commit("setStateProperty", {
      target: this.params,
      key: "options",
      value: this.params.options.filter(
        (opt: { label: string; value: string }, idx: number) => index !== idx,
      ),
    });
    if (isUsedIds.length) {
      this.$message.error("分类名称已被删除，请修改该分类下的元素至其他分类下");
      // 处理extra
      this.$store.dispatch("updateComponentsExtra", {
        ids: isUsedIds,
        newExtra: { type: "" },
      });
    }
  }

  confirmAdd() {
    // 去重
    if (
      this.options.find(
        (item: { label: string; value: string }) =>
          item.value === this.tempValue,
      )
    ) {
      // 提示重复
      this.$message.error("分类名称已经存在，请重新输入");
      return;
    }
    this.isAdd = false;
    if (!this.tempValue) return;
    this.$store.commit("setStateProperty", {
      target: this.options,
      key: this.options.length,
      value: { label: this.tempValue, value: this.tempValue },
    });
  }

  cancelAdd() {
    this.isAdd = false;
  }
}
</script>

<style scoped lang="less">
@CSS_PREFIX: options;
@BORDER_COLOR: #f0f1f5;
@MAIN_COLOR: #42c57a;
@DANGER_COLOR: #f56c6c;
.@{CSS_PREFIX} {
  align-items: center;
  padding-bottom: 10px;
  font-size: 13px;
  .label {
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
  &-wrapper {
    display: block;
    margin-top: 12px;
  }
  &-item {
    display: flex;
    height: 24px;
    border-bottom: 1px solid @BORDER_COLOR;
    margin-bottom: 12px;
    padding-bottom: 12px;
    align-items: center;
    font-size: 14px;
    > div {
      flex: 1;
      max-width: calc(100% - 29px);
      word-break: break-all;
    }
    /deep/ .el-button {
      margin-left: 15px;
      height: 22px;
      width: 22px;
      i {
        font-size: 12px;
      }
      &.danger:hover {
        color: @DANGER_COLOR;
        border-color: @DANGER_COLOR;
        background-color: #fff;
      }
    }

    &:last-of-type {
      border-bottom: 0px solid @BORDER_COLOR;
    }
  }
  /deep/ .add-button {
    width: 100%;
    cursor: pointer;
  }
}
</style>
