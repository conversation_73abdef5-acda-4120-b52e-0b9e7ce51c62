<template>
  <div class="container">
    <ya-label :label="label" :description="description" />
    <el-switch v-model="switchVal" size="small" />
    <el-input
      v-model="numberVal"
      size="small"
      type="text"
      style="width: 120px;margin: 0 12px;"
      :disabled="!switchVal"
      @blur="handleBlur"
      />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraSwitchInput extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: false,
  })
  defaultValue!: string;

  @Prop({
    default: {
      "params": {
        "relative": {
            "key": "dragSum",
            "label": "",
            "required": true,
            "type": "input",
            "params": {}
        }
      }
    },
  })
  config!: any;

  description = '可填入小于10000的数字。判定正确时，需要答案区吸附的元素数值，等于总数值'

  get inputProperty() {
    return this.config.params.relative.key
  }
  get min() {
    return this.config.params.relative.params.min
  }
  get max() {
    return this.config.params.relative.params.max
  }

  get switchVal() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.defaultValue;
  }

  set switchVal(val) {
    if (val === this.switchVal) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
  get numberVal() {
    const val = this.$store.state.extraStageData[this.inputProperty];
    return val;
  }

  set numberVal(val) {
    val = String(val);
    const t = val.charAt(0);
    let temp = val.replace(/[^\d]/g, '')
    if(t === '-' && temp) {
      val = '-' + temp
    }
    const { max, min } = this;
    if(max && val > max) temp = max;
    if(min && val < min) temp = min;
    if(val === '') temp = '';
    if(String(temp).at(-1) !== '.') {
      temp = Number(temp)
    }
    // 正则匹配 数值
    this.$store.commit("updateExtraStageData", {
      [this.inputProperty]: temp,
    });
  }

  handleBlur() {
    if(String(this.numberVal).at(-1) === '.') {
      this.numberVal =String(Number(this.numberVal));
    }
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 10px;
  }

  .el-switch {
    margin-right: 0;
  }
}
</style>
