<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-color-picker v-model="v" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class ColorPicker extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: true,
  })
  config!: any;

  get v() {
    const val = this.$store.state.extraStageData[this.property];
    return val ?? this.config.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$store.commit("updateExtraStageData", {
      [this.property]: val,
    });
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  align-items: center;
  .label {
    margin-right: 10px;
  }
}
</style>
