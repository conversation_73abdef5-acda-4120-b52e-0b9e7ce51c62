<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="麦克风属性" name="sprite-properties" class="voice-form-config">
      <div class="flex">
        <ya-select label="评测类型" property="wordType" :defaultValue="0" :options="wordTypeOptions" :componentIds="currentComponentIds" :option="{ required: true }" />
      </div>
      <div class="flex" v-if="componentProperties.wordType === 1 && category === 1020">
        <ya-switch label="音标测评" property="isUsePhonetics" :componentIds="currentComponentIds" />
      </div>
      <div class="flex">
        <ya-input @blur="blurHandler" label="评测内容" property="evaluatingText" :componentIds="currentComponentIds" :options="{ required: true, maxlength: maxlength, type: 'english' }" />
      </div>
      <div class="word-check" v-if="errWordsList.length">
        <p class="error-word">{{errWordsList.map(item => item.word).join('，')}}<span>无法测评，请联系产运老师处理</span></p>
      </div>
      <div class="word-check" v-if="wordCheckRight">
        <span class="right-tips">
          单词校验正确
        </span>
      </div>
      <div class="word-check" v-if="chineseCheckWrong">
        <span class="chinese-tips">
          不能包含中文！
        </span>
      </div>
      <div class="flex" v-if="componentProperties.isUsePhonetics">
        <ya-select label="评测音标" property="evaluatePhonetics"
        placeholder="请选择音标"
        :options="evaluatePhoneticsOptions" :componentIds="currentComponentIds" :option="{ required: true }" />
      </div>

      <div class="flex">
        <ya-switch label="自动开始" property="autoBegin" :componentIds="currentComponentIds" :option="{ required: true }" />
      </div>
      <div class="flex" style="width: 60%;">
        <ya-input-number label="答题时长" property="answerDuration" type="number" :componentIds="currentComponentIds" :options="{ required: true, max: 100 }" />
      </div>
      <div class="flex">
        <ya-input label="及格线  " property="accuracy" type="number" :componentIds="currentComponentIds" :options="accuracyOptions" />
      </div>
      <div class="flex" v-if="category === 1020">
        <ya-switch label="星星反馈" property="isUseStarSpine" :componentIds="currentComponentIds" />
      </div>
      <div class="spine-wrapper" v-if="componentProperties.isUseStarSpine && componentProperties.starSpine">
        <div class="flex">
          <ya-label :label="`星星反馈动画`" :required="true" />
          <div class="no-spine" v-if="!componentProperties.starSpine.skeleton">
            <el-button size="small" type="primary" class="upload-handler" @click="handleSelect">添加动效</el-button>
          </div>
          <div class="has-spine" v-if="componentProperties.starSpine.cover">
            <div class="spine-cover"><img :src="componentProperties.starSpine.cover" /></div>
            <i class="el-icon-error" @click="handleDelete()"></i>
          </div>
        </div>
      </div>
      <div v-for="(item, index) in componentProperties.scoreLevels" style="padding: 12px 0px; border-bottom: 1px solid #f0f1f5;" :key="index" class="feedback-item">
        <div class="flex" style="justify-content:space-between;width: 100%;">
          <ya-label :label="`${index + 1}星属性`" :required="true" />
          <div class="flex" style="margin-bottom:0;gap: 10px">
            <i class="el-icon-circle-plus-outline" @click="add(index)" v-if="componentProperties.scoreLevels.length < 5"></i>
            <i v-if="componentProperties.scoreLevels.length > 3" class="el-icon-remove-outline" @click="del(index)"></i>
          </div>
        </div>

        <div class="flex">
          <ya-label label="分数极值" />
          <el-input
            v-model.number="item.score"
            size="small"
            onkeyup="value=value.replace(/^\D*(\d*(?:\.\d{0,1})?).*$/g, '$1')"
            type="text"
            style="width: 100px; height: 24px; line-height: 24px;"
            placeholder="请输入数值"
          />
        </div>
        <div class="flex">
          <ya-label label="动画队列" />
          <el-select v-model="item.animation" placeholder="答题区选择" size="small" style="margin-top: 10px;">
            <el-option :label="item.label" v-for="(item, index) in animationOps" :key="index" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="flex"><AudioSelect :value.sync="item.audio" class="form-content" label="上传音效" @change="handleAudioChange" /></div>
      </div>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";
import { checkWordIsInASR } from "@/common/utils/asrRequest";
// import phoneticsList from "./phoneticsList.json";
// import { SpineLibrary } from "@/pages/index/components/MaterialLibrary/PhpSpineLibrary";

@Component({
  components: {
    ExtraEditor: () => import("../ExtraEditor/index.vue"),
    BaseProperties: () => import("../BaseProperties/index.vue"),
    YaInputNumber: () => import("../EditComponents/InputNumber/index.vue"),
    YaInput: () => import("../EditComponents/Input/index.vue"),
    YaColorPicker: () => import("../EditComponents/ColorPicker/index.vue"),
    YaSwitch: () => import("../EditComponents/Switch/index.vue"),
    YaSelect: () => import("../EditComponents/Select/index.vue"),
    YaInputGroupNumber: () => import("../EditComponents/InputGroupNumber/index.vue"),
    YaAudioSelect: () => import("../EditComponents/AudioSelect/index.vue"),
    YaLabel: () => import("@/components/Label/index.vue"),
    AudioSelect: () => import("@/components/AudioSelect/index.vue"),
  },
})
export default class VoiceComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  wordTypeOptions = [
    { label: "单词", value: 1 },
    { label: "句子", value: 2 },
    { label: "段落", value: 3 },
    { label: "问题问答", value: 4 },
    { label: "复述", value: 5 },
    // { label: "音标", value: 6 },
  ];

  accuracyOptions = {
    required: true,
    min: 0,
    max: 100,
  };

  animationOps: any[] = []; // 动画列表

  isFetching = false; // 动画列表

  showText = false;

  wordReplace = /[^a-zA-Z0-9'\s-]+/g;

  englishReg = /[^a-zA-Z0-9\s-\\.!@#\\$%\\\\^&\\*\\)\\(\\+=\\{\\}\\[\]\\/",'<>~\\·`\\?:;|]+/g;

  errWordsList: any[] = [];

  wordCheckRight = false;

  chineseCheckWrong = false;

  phoneticsList = {};

  get maxlength() {
    if (this.currentComponents[0].properties.wordType === 1) {
      return 25;
    } else if (this.currentComponents[0].properties.wordType === 2) {
      return 70;
    } else {
      return 100;
    }
  }

  get evaluatingTextComponentId() {
    return this.currentComponents[0].properties.evaluatingTextComponentId || "";
  }

  get componentProp() {
    return this.currentComponents[0].properties;
  }

  get category() {
    return this.$store.state.template.category;
  }

  get evaluatingText() {
    return this.currentComponents[0].properties.evaluatingText;
  }

  get componentProperties() {
    return this.currentComponents[0].properties;
  }

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get evaluatePhoneticsOptions() {
    return ((this.phoneticsList as any)[this.evaluatingText] || []).map((item: any) => {
      return {
        label: item,
        value: item,
      };
    });
  }

  get evaluatePhonetics() {
    return this.componentProperties.evaluatePhonetics;
  }

  get skeletonUrl() {
    return (this.componentProperties.starSpine || {}).skeleton;
  }

  async mounted() {
    if (this.evaluatingText) {
      this.blurHandler(this.evaluatingText);
    }
    if (this.componentProperties.isUsePhonetics) {
      this.phoneticsList = await import('./phoneticsList.json').then(res => res.default);
    }
  }

  blurHandler(val: string) {
    if (val.match(this.englishReg)) {
      this.wordCheckRight = false;
      this.chineseCheckWrong = true;
      return;
    } else {
      this.chineseCheckWrong = false;
    }
    const words = val.replace(this.wordReplace, " ");
    this.errWordsList = [];
    if (!words) {
      this.wordCheckRight = false;
      return;
    }
    // 增加判断，如果是开启音标评测，则不进行单词校验
    if (this.componentProperties.isUsePhonetics) {
      this.wordCheckRight = false;
      return;
    }
    checkWordIsInASR(words)
      .then(res => {
        const data = res.data.data;
        const WordsList: any[] = data.WordsList || [];
        WordsList.forEach((item: any) => {
          if (item.in_dict !== 0) {
            this.errWordsList.push(item);
          }
        });
        if (this.errWordsList.length === 0) {
          this.wordCheckRight = true;
        } else {
          this.wordCheckRight = false;
        }
        console.warn(this.errWordsList, "this.errWordsLis");
      });
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }

  showTextChange() {
    if (this.showText) {
      const labelComponent = {
        type: "label",
        tag: "",
        dragable: true,
        deletable: false,
        canCopy: false,
        editable: {
          properties: {
            focus: false,
          },
        },
        properties: {
          active: true,
          fontSize: 32,
          lineHeight: 32,
          width: 100,
          height: 101,
          x: 0,
          y: 0,
          string: "单击添加文本",
          str: `<size=32><color=#000000>${this.evaluatingText}</c></s>`,
          color: "#000000",
          cusorIndex: 1,
          selectArr: [],
          isLabelRight: true,
          isFixed: false,
          rowSpacing: 1.0,
        },
      };
      this.$store.dispatch("addComponentNoFocus", labelComponent);
    } else {
      this.$store.dispatch("removeComponent", this.evaluatingTextComponentId);
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          evaluatingTextComponentId: "",
        },
      });
    }
  }

  handleAnimationChange(val: string) {
    this.updateProperties("starSpine", {
      animation: val,
    });
  }

  handleSelect() {
    import('@/pages/index/components/MaterialLibrary/PhpSpineLibrary').then(({ SpineLibrary }) => {
      SpineLibrary({
      isMultiple: true,
      classify: "spine",
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      onClose: () => {},
      onConfirm: spines => {
        const [{ atlas, images, skeleton, cover }] = spines;
        this.updateProperties("starSpine", {
          atlas,
          images,
          skeleton,
          cover,
          animation: "",
        });
        this.updateProperties("scoreLevels", [
          {
            score: "",
            animation: "",
          },
          {
            score: "",
            animation: "",
          },
          {
            score: "",
            animation: "",
          },
        ]);
      },
    });
    })
  }

  handleDelete() {
    this.updateProperties("starSpine", {
      atlas: "",
      images: [],
      skeleton: "",
      cover: "",
      animation: "",
    });
    this.updateProperties("scoreLevels", []);
  }

  del(index: number) {
    this.componentProperties.scoreLevels.splice(index, 1);
  }

  add(index: number) {
    this.componentProperties.scoreLevels.splice(index + 1, 0, {
      score: "",
      animation: "",
    });
  }

  handleScoreAnimationChange(index: number, val: string) {
    this.componentProperties.scoreLevels[index].animation = val;
  }

  fetchAnimation(url: string) {
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations).map(ani => {
          return {
            label: ani,
            value: ani,
          };
        });
        return animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }

  handleAudioChange(val: any) {
    console.log("handleAudioChange", val);
    bus.$emit("audioDelete");
  }

  @Watch("skeletonUrl", { immediate: true })
  onSkeletonUrlChange(val: string) {
    if (val) {
      this.fetchAnimation(val).then(animations => {
        this.animationOps = animations;
      });
    } else {
      this.animationOps = [];
    }
  }

  @Watch("componentProperties.isUseStarSpine")
  onIsUseStarSpineChange(val: boolean) {
    if (val) {
      this.updateProperties("starSpine", {
        atlas: "",
        images: [],
        skeleton: "",
        cover: "",
        animation: "",
      });
    } else {
      this.updateProperties("starSpine", {});
      this.updateProperties("scoreLevels", []);
    }
  }

  @Watch("componentProperties.wordType")
  onWordTypeChange(val: number) {
    console.log('onWordTypeChange',val);
    if (val !== 1) {
      this.updateProperties("isUsePhonetics", 0);
      this.updateProperties("evaluatePhonetics", "");
    }
  }

  @Watch("componentProperties.isUsePhonetics")
  async onIsUsePhoneticsChange() {
    this.updateProperties("evaluatePhonetics", "");
    this.updateProperties("evaluatingText","");
    this.wordCheckRight = false;
    this.errWordsList = [];
    if (this.componentProperties.isUsePhonetics) {
      this.phoneticsList = await import('./phoneticsList.json').then(res => res.default);
    }
  }

  @Watch("evaluatingText")
  onEvaluatingTextChange() {
    this.updateProperties("evaluatePhonetics", "");
  }
}
</script>

<style scoped lang="less">
.voice-form-config {
  font-size: 13px;
  /deep/ .label {
    font-size: 13px;
    margin-right: 5px;
  }
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.word-check {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  .error-word {
    color: rgba(240, 83, 72, 1);
    margin: 0;
    // height: 25px;
    line-height: 25px;
    text-align: left;
  }
  .right-tips {
    color: #42c57a;
    margin-left: -124px;
  }
  .chinese-tips {
    color: rgba(240, 83, 72, 1);
    margin-left: -124px;
  }
}

.spine-wrapper {
  .no-spine {
    display: flex;
    .media-upload {
      display: inline-block;
      line-height: 28px;
    }
    .tip {
      margin-left: 16px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .has-spine {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    padding: 0 8px;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
    &:hover {
      .button-wrapper {
        bottom: 0px;
      }
    }
    .button-wrapper {
      position: absolute;
      bottom: -28px;
      display: flex;
      justify-content: space-between;
      left: 10px;
      width: 120px;
      height: 28px;
      align-items: flex-end;
      transition: all 0.3s;
      button {
        margin-left: 0;
        // width: 60px;
        flex: 1;
      }
      &:hover {
        bottom: 0;
      }
    }
    .spine-cover {
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 5px;
      background: url("~@/assets/img/transparent.png") repeat;
      background-size: 10px;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
    .el-icon-success {
      color: #42c57a;
    }
    .el-icon-error {
      display: none;
      color: #95a1ad;
      cursor: pointer;
    }
    .el-input__count {
      line-height: 15px;
    }
    .el-icon-success,
    .el-icon-error {
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
    &:hover {
      border-radius: 2px;
      // background: #f0f1f5;
      .el-icon-error {
        display: inline-block;
      }
      .el-icon-success {
        display: none;
      }
    }
  }
  .audio-control {
    margin-bottom: 10px;
  }
  .el-textarea {
    margin-bottom: 6px;
    /deep/ .el-input__count {
      line-height: 15px;
    }
  }
  .upload-handler {
    > span {
      display: flex;
      align-items: center;
    }
  }
}
.feedback-item {
  display: flex;
  flex-direction: column;
  display: flex;
  align-items: baseline;
  flex-direction: column;
  justify-content: flex-start;
  i {
    font-size: 16px;
  }
}
</style>
