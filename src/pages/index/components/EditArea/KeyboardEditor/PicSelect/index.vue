<template>
  <div class="demo-fit">
    <div
      v-for="val in boardList"
      :key="val[0]"
      :class="`block ${chooseBoard === val[0] ? 'active' : ''}`"
      @click="changeBlock(val[0])"
    >
      <el-avatar
        shape="square"
        :size="60"
        :fit="`样式${val[0]}`"
        :src="val[1]"
      ></el-avatar>
      <span class="title">{{ `样式${val[0]}` }}</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class PicSelect extends Vue {
  @Prop() chooseBoard!: number;
  get boardList() {
    return [
      [1, "https://img.zuoyebang.cc/cw_364657f8de9549fcbd4d89d4245d2447.png"],
      [2, "https://img.zuoyebang.cc/cw_13ab0e655a59126bbc25d11a3318d732.png"],
      [3, "https://img.zuoyebang.cc/cw_76bdedfb8f30213270be0adeae4596f4.png"],
    ];
  }
  changeBlock(value: number) {
    this.$emit("boradSetting", value);
  }
}
</script>
<style scoped lang="less">
.demo-fit {
  display: flex;
  text-align: center;
  justify-content: space-between;
  .block {
    flex: 1;
    display: flex;
    flex-direction: column;
    flex-grow: 0;
    padding: 5px 5px 0 5px;
    border: 1px solid #ffffff;
    border-radius: 5px;
    .title {
      margin-top: 10px;
      font-size: 14px;
      color: #8492a6;
    }
    .el-avatar {
      background: none;
    }
  }
  .block.active {
    border: 1px solid #c8ccd6;
  }
  .block:hover {
    background: #f1f4f6;
    cursor: pointer;
  }
}
</style>
