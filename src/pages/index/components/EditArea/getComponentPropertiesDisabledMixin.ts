import { Vue, Component } from "vue-property-decorator";
import { get } from "lodash-es";

@Component
export default class GetComponentPropertiesDisabledMixin extends Vue {
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get componentMap(): State["componentMap"] {
    return this.$store.state.componentMap;
  }

  get isSuperManagerMode() {
    return this.$store.getters.isSuperManagerMode;
  }

  getComponentPropertiesDisabled(
    firstLevelProperty: string,
    secondLevelProperty: string,
  ) {
    if (this.isSuperManagerMode) return false;

    const { componentIds, componentMap } = this;
    let disabled = false;

    componentIds.forEach((componentId: string) => {
      const editable = get(
        componentMap[componentId].editable,
        `${firstLevelProperty}.${secondLevelProperty}`,
      );
      if (secondLevelProperty == "angle") { // angle group 禁止使用
        if (componentMap[componentId].type == "group") {
          disabled = true;
        }
      }

      if (editable === false) {
        disabled = true;
      }
    });

    return disabled;
  }
}
