<template>
  <div class="spine-editor">
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="动效属性" name="spine-properties">
      <el-row>
        <el-col :span="12">
          <ya-switch :componentIds="currentComponentIds" label="循环" property="loop" />
        </el-col>
        <el-col :span="12">
          <ya-input-number
            :componentIds="currentComponentIds"
            :options="{
              min: 0.1,
              max: 10,
              step: 0.1,
            }"
            class="spine-editor-time-scale"
            label="速率"
            property="timeScale"
          />
        </el-col>
      </el-row>
      <el-row class="spine-editor-animation-list">
        <el-col :span="6">
          播放队列
        </el-col>
        <el-col :span="18">
          <ul class="spine-editor-animation-list-list">
            <li v-for="(item, index) in animationList" :key="index">
              <el-select :value="item" @change="onAnimationItemChange(index, $event)" size="mini" :loading="isFetching">
                <el-option key="void" value="">
                  无动画
                </el-option>
                <el-option v-for="i in animationOptions" :key="i" :value="i">
                  {{ i }}
                </el-option>
              </el-select>
              <i @click="onClickAddAnimation(index)" class="el-icon-circle-plus-outline icon" />
              <i v-if="animationList.length > 1" @click="onClickRemoveAnimation(index)" class="el-icon-remove-outline icon" />
            </li>
          </ul>
        </el-col>
      </el-row>
    </el-collapse-item>
    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaInput from "../EditComponents/Input/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import YaColorPicker from "../EditComponents/ColorPicker/index.vue";
import YaSlider from "../EditComponents/Slider/index.vue";
import YaSliderAndInput from "../EditComponents/SliderAndInput/index.vue";

@Component({
  components: {
    BaseProperties,
    ExtraEditor,
    YaInput,
    YaInputNumber,
    YaSwitch,
    YaColorPicker,
    YaSlider,
    YaSliderAndInput,
  },
})
export default class SpineComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: SpineComponent[];

  animationOptions: string[] = [];

  isFetching = false;

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get skeletonUrl() {
    return this.currentComponents[0].spineData?.skeleton || '';
  }

  get animationList() {
    const animationList = this.currentComponents[0].properties.animationList;
    return animationList;
  }

  set animationList(animationList) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.currentComponentIds,
      newProperties: { animationList },
    });
  }

  onAnimationItemChange(index: number, value: string) {
    const newAnimationList = this.animationList.concat();
    newAnimationList[index] = value;
    this.animationList = newAnimationList;
  }

  onClickAddAnimation(index: number) {
    const newAnimationList = this.animationList.concat();
    newAnimationList.splice(index + 1, 0, "");
    this.animationList = newAnimationList;
  }

  onClickRemoveAnimation(index: number) {
    const newAnimationList = this.animationList.concat();
    newAnimationList.splice(index, 1);
    this.animationList = newAnimationList;
  }

  @Watch("skeletonUrl", {
    immediate: true,
  })
  skeletonUrlWatcher(url: string) {
    if(url) {
      this.fetchAnimation(url);
    }
  }

  fetchAnimation(url: string) {
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations);
        this.animationOptions = animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }
}
</script>

<style scoped lang="less">
.spine-editor {
  &-time-scale {
    width: 140px;
    flex-grow: 1;
    box-sizing: border-box;
  }

  &-animation-list {
    text-align: left;
    line-height: 28px;

    &-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
  }

  .icon {
    display: inline-block;
    width: 24px;
    height: 28px;
    line-height: 28px;
    font-size: 22px;
    vertical-align: bottom;
    cursor: pointer;

    &:first-of-type {
      margin-left: 5px;
    }
  }
}
</style>
