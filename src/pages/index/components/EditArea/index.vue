<template>
  <component :is="asyncComp.CompEditor" v-if="currentIds.length && asyncComp.CompEditor" :hideProperty="hideProperty" />
  <component :is="asyncComp.GlobalEditor" :hideProperty="hideProperty" v-else />
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { isEqual } from "lodash-es";
import { ExtendedVue } from "vue/types/vue";
@Component
export default class EditArea extends Vue {
  collapseValue = [
    "stage-properties",
    "base-properties",
    "label-properties",
    "sprite-properties",
    "shape-text-properties",
    "formula-properties",
    "spine-properties",
    "extra-properties",
    "cutShape-properties",
    "special-component-properties",
    "realia-component-properties",
    "shape-component-properties"
  ];

  hideProperty = false;
  asyncComp: {
    [x: string]: ExtendedVue<Vue, unknown, unknown, unknown, Record<never, any>> | null;
  } = {
    CompEditor: null,
    GlobalEditor: null,
  };

  get currentIds() {
    if (this.$store.state.currentComponentIds.length) {
      const { isShowProperty = "" } = this.$store.state.componentMap[this.$store.state.currentComponentIds[0]];
      this.hideProperty = isShowProperty === "hide";
    }
    return this.$store.state.currentComponentIds;
  }

  @Watch("currentIds", {
    immediate: true,
  })
  currentIdsChange(val: any, oldVal: any) {
    console.log("currentIdsChange..getComponentEditor", val, oldVal);
    if (isEqual(val, oldVal)) return;
    if (val.length) {
      this.renderAsyncComp("CompEditor");
    } else {
      this.renderAsyncComp("GlobalEditor");
    }
  }

  asyncCompImports = {
    CompEditor: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "CompEditor" */ "./CompEditor/index.vue").then(component => {
          this.asyncComp.CompEditor = Vue.extend(component.default);
        });
      });
    },
    GlobalEditor: () => {
      return Promise.resolve().then(() => {
        import(/* webpackChunkName: "edit-init" */ "./GlobalEditor.vue").then(component => {
          this.asyncComp.GlobalEditor = Vue.extend(component.default);
        });
      });
    },
  };

  renderAsyncComp(key = "") {
    console.log("renderAsyncComp", key);
    if (key) {
      this.asyncCompImports[key] && (this.asyncCompImports[key] as any)();
      return;
    }
  }

  mounted() {
    // 记录焦点状态 解决撤销与撤销的互斥问题
    this.$nextTick(() => {
      const dom = document.querySelector(".edit-area");
      if (dom) {
        const setFocus = () => {
          console.log("xuu-setFocus");
          (window as MyWindow).vueFocus = true;
          (window as MyWindow).editFocus = true;
          this.$bus.$emit("editFocusChange", (window as MyWindow).editFocus);
        };
        const setBlur = () => {
          (window as MyWindow).vueFocus = false;
          const timer = setTimeout(() => {
            clearTimeout(timer);
            (window as MyWindow).editFocus = false;
            this.$bus.$emit("editFocusChange", (window as MyWindow).editFocus);
          }, 500);
        };
        dom.addEventListener("focus", setFocus, true);
        dom.addEventListener("blur", setBlur, true);
        this.$once("hook:beforeDestroy", function() {
          window.removeEventListener("focus", setFocus, true);
          window.removeEventListener("blur", setBlur, true);
        });
      }
    });
  }
}
</script>

<style scoped lang="less">
.edit-area-normal-mode {
  padding: 20px 20px;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 5px;
  /* 移除 overflow: unset，保持Element UI的默认动画机制 */
}
/deep/ .el-collapse-item__wrap {
  /* 添加动画优化 */
  will-change: height;
  transition: height 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
/deep/ .el-collapse-item__wrap.is-active {
  /* 展开状态下才允许overflow visible，避免内容被裁剪 */
  overflow: visible !important;
}
/deep/ .el-collapse-item__header {
  font-size: 15px;
  font-weight: unset;
}
/deep/ .el-collapse-item__arrow {
  /* 优化箭头动画 */
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}
</style>
