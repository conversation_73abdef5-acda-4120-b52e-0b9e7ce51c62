<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      v-if="currentComponents.length === 1"
      title="时钟属性"
      name="sprite-properties"
    >
      <div class="flex">
        <span class="timeLable">显示时刻:</span>
        <el-tooltip placement="top">
          <div slot="content">
            时：仅支持1-12的数字<br />分：仅支持0-59的数字
          </div>
          <i class="el-icon-question" />
        </el-tooltip>
      </div>

      <div class="flex">
        <ya-input-number
          label=""
          property="hour"
          :componentIds="currentComponentIds"
          :options="{
            min: 1,
            step: 1,
            max: 12,
            maxLength: 2,
            type: 'positive integer',
          }"
          type="number"
        />
        <span class="lable1">时</span>
        <ya-input-number
          label=""
          property="minute"
          :componentIds="currentComponentIds"
          :options="{
            min: 0,
            step: 1,
            max: 59,
            maxLength: 2,
            type: 'positive integer',
          }"
          type="number"
        />
        <span class="lable1">分</span>
      </div>

      <div class="flex">
        <span class="timeLable">正确时刻:</span>
        <el-tooltip placement="top">
          <div slot="content">
            时：仅支持1-12的数字<br />分：仅支持0-59的数字
          </div>
          <i class="el-icon-question" />
        </el-tooltip>
      </div>

      <div class="flex">
        <ya-input-number
          label=""
          property="correctHour"
          :componentIds="currentComponentIds"
          :options="{
            min: 1,
            step: 1,
            max: 12,
            maxLength: 2,
            type: 'positive integer',
          }"
          type="number"
        />
        <span class="lable1">时</span>
        <ya-input-number
          label=""
          property="correctMinute"
          :componentIds="currentComponentIds"
          :options="{
            min: 0,
            step: 1,
            max: 59,
            maxLength: 2,
            type: 'positive integer',
          }"
          type="number"
        />
        <span class="lable1">分</span>
      </div>

      <div class="flex_1">
        <el-row>
          <el-col>
            <ya-switch
              label="联动:"
              property="linkage"
              :componentIds="currentComponentIds"
            />
          </el-col>
        </el-row>
      </div>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";

// import AudioSelect from "@/components/AudioSelect/index.vue";

@Component({
  components: {
    BaseProperties,
    YaInputNumber,
    YaSwitch,
  },
})
export default class ClockComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.el-tooltip {
  font-size: 18px;
  cursor: pointer;
  margin-bottom: 2px;
}
.timeLable {
  font-size: 14px;
  margin-right: 5px;
}

.lable1 {
  margin-left: 5px;
  margin-right: 5px;
}
.container {
  display: flex;
  align-items: center;
  flex-grow: 0;
}

.container /deep/ .label {
  display: inline-block;
  text-align: left;
  flex-shrink: 0;
  min-width: 0px;
}
.container /deep/ .el-input {
  flex-grow: 1;
  width: 80px;
  margin-left: 0px;
}
.flex_1 /deep/ .el-switch__core {
  margin-bottom: 4px;
}
</style>
