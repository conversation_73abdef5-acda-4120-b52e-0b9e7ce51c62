<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
    <el-button :disabled="currentComponents.length > 1" icon="zyb-icon-plus-o" type="primary" size="small" class="add-option" @click="editorHandler">编辑公式</el-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";
import { ComponentTypes } from "@/common/constants/componentTypes";

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
  },
})
export default class FormulaComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;
  unSubscribe: any;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map((i: any) => i.id);
  }
  editorHandler() {
    compManagerUtil.editComponent(ComponentTypes.FORMULA);
  }
}
</script>

<style scoped lang="less">
.add-option {
  width: 100px;
  font-size: 12px;
  margin: 10px 0;
  font-weight: 400;
  line-height: 12px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
}
.formula-editor-dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  .formula-editor-relative {
    position: relative;
  }
  .formula-editor-close {
    span {
      position: absolute;
      font-size: 60px;
      font-weight: 700;
      color: #fff;
      top: 0;
      right: -48px;
      cursor: pointer;
      font-family: "Symbola,Times New Roman,PingFang-Medium";
    }
  }
  .formula-iframe {
    height: 70vh;
    width: 50vw;
    min-height: 400px;
    min-width: 700px;
    background-color: #fff;
  }
}
</style>
