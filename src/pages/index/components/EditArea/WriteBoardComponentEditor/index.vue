<!--
 * @Date: 2021-09-10 16:44:09
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-13 19:06:21
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/WriteBoardComponentEditor/index.vue
 * @Author: chxu
 * 运算分类 移动 增加 删除
-->
<template>
  <div class="match-path-panel">
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      title="题目属性"
      name="stage-properties"
      class="match-board-panel"
    >
      <el-form label-position="top" size="small">
        <el-form-item label="题目类型">
          <el-radio-group v-model="writeType">
            <el-radio
              v-for="type in writeTypes"
              :key="type.value"
              :label="type.value"
              >{{ type.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="汉字">
          <el-tooltip
            class="item"
            effect="dark"
            content="最多可添加4个汉字，每空只能添加一个汉字"
            placement="top"
          >
            <i class="el-icon-question button-tips"></i>
          </el-tooltip>
          <div
            class="option-setting"
            v-for="(item, index) in writeList"
            :key="index"
          >
            <span class="sort-label">{{ index + 1 }}</span>
            <el-input
              v-model="item.hanzi"
              placeholder="限1汉字以内"
              maxlength="1"
              size="small"
              @input="changeHanzi(index, $event)"
            ></el-input>
            <el-select
              v-model="item.pinyin"
              class="pinyin-select"
              @change="updateAnswer(index)"
            >
              <div class="pinyin-select__option">
                <el-option
                  v-for="pinyin in item.pinyinList"
                  :key="pinyin"
                  :label="pinyin"
                  :value="pinyin"
                ></el-option>
              </div>
            </el-select>
            <el-checkbox
              v-model="item.hasAnswer"
              class="pinyin-checkbox"
              @change="updateHasAnswer(index)"
              >显示汉字</el-checkbox
            >
            <el-button
              v-if="writeList.length > 1"
              type="text"
              icon="el-icon-delete"
              circle
              @click="
                () => {
                  openConfirm(index);
                }
              "
            ></el-button>
          </div>
          <el-button
            icon="zyb-icon-plus-o"
            type="primary"
            size="small"
            class="add-option"
            :disabled="writeList.length >= 4"
            @click="addOption"
            >添加汉字</el-button
          >
        </el-form-item>
      </el-form>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import pinyin from "pinyin";

@Component({
  components: {
    BaseProperties,
  },
})
export default class WriteBoardComponent extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  writeTypes: Array<{ label: string; value: number }> = [
    {
      label: "根据拼音写汉字",
      value: 0,
    },
    {
      label: "根据汉字写拼音",
      value: 1,
    },
  ];
  writeList: any[] = [];
  beforeMount() {
    setTimeout(() => {
      this.writeList = this.writeArray.map((item: any, index: number) => {
        const hanzi = item.hanzi.replace(/[^\u4E00-\u9FA5]/g, "");
        return {
          hanzi,
          pinyin: item.pinyin,
          hasAnswer: item.hasAnswer ? true : false,
          pinyinList: pinyin(hanzi, { heteronym: true })[0],
        };
      });
      console.log(this.writeList, "this.writeList");
    }, 400);
  }
  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get writeType(): number {
    return this.currentComponents[0].properties.writeType;
  }

  set writeType(val) {
    if (val === this.writeType) return;
    this.updateProperties("writeType", val);
  }

  get writeArray(): Array<{ hanzi: string; pinyin: string }> {
    return this.currentComponents[0].properties.writeArray;
  }
  @Watch("writeList", { immediate: true, deep: true })
  private updateWriteList(newVal: any[], oldVal: any[]) {
    console.log("newVal:", newVal, oldVal);
    if (newVal && oldVal) {
      const writeArray: any[] = [];
      newVal.forEach(write => {
        writeArray.push({
          hanzi: write.hanzi,
          pinyin: write.pinyin,
          hasAnswer: write.hasAnswer,
        });
      });
      this.updateProperties("writeArray", writeArray);
    }
  }
  changeHanzi(index: number) {
    this.writeList[index].hanzi = this.writeList[index].hanzi.replace(
      /[^\u4e00-\u9fa5]/,
      "",
    );
    if (this.writeList[index].hanzi !== "") {
      this.writeList[index].pinyinList = pinyin(this.writeList[index].hanzi, {
        heteronym: true,
      })[0];
      this.writeList[index].pinyin = this.writeList[index].pinyinList[0];
    } else {
      this.writeList[index].pinyinList = [];
      this.writeList[index].pinyin = "";
    }
  }
  addOption() {
    this.writeList.push({
      hanzi: "",
      pinyin: "",
      hasAnswer: false,
      pinyinList: [],
    });
  }
  updateHasAnswer(index: number) {
    console.log(index, "int");
  }
  updateAnswer(int: number) {
    console.log(int, "int");
  }
  openConfirm(index: number) {
    if (this.writeList.length <= 1) {
      return;
    }
    this.$confirm("确定删除该选项？", "", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(action => {
      this.delWrite(index);
    });
  }
  delWrite(index: number) {
    this.writeList.splice(index, 1);
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.match-path-panel {
  text-align: left;
  .el-form-item {
    margin-bottom: 6px;
  }
  .math-model-wrapper {
    display: flex;
    justify-content: space-between;
  }
  .option-setting {
    margin-bottom: 10px;
    .sort-label {
      display: inline-block;
      width: 8px;
      margin-right: 12px;
    }
    .el-input {
      height: 28px;
      width: 100px;
    }
    .el-input__inner {
      height: 28px;
    }
    .pinyin-select,
    .el-select {
      margin-left: 8px;
      width: 80px;

      .el-input {
        height: 28px;
        width: 80px;
        .pinyin-select__option {
          width: 80px;
        }
      }
    }
    .pinyin-checkbox {
      margin-left: 10px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(51, 51, 51, 1);
      line-height: 12px;
      margin-right: 0;
    }

    .right-tag {
      display: inline-block;
      width: 48px;
      height: 28px;
      margin-left: 15px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(66, 197, 122, 1);
      line-height: 28px;
    }
  }
  .add-option {
    width: 100px;
    font-size: 12px;
    margin: 10px 0;
    font-weight: 400;
    line-height: 12px;
    text-align: center;
    border-radius: 2px;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.match-path-panel {
  .math-model-wrapper {
    .el-form-item {
      max-width: 46px !important;
      input {
        padding: 5px !important;
      }
      .equal {
        text-align: center;
      }
    }
  }
  .button-tips {
    position: absolute;
    left: 58px;
    top: -31px;
  }
  .button-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-button {
      margin-top: 10px;
      width: 90%;
    }
  }
}
</style>
