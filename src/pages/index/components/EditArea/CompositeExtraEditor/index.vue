<!--
 * @Date: 2021-09-18 13:11:04
 * @LastEditors: chxu
 * @LastEditTime: 2021-11-01 10:12:30
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/CompositeExtraEditor/index.vue
 * @Author: chxu
-->
<template>
  <el-collapse-item v-if="currentIds.length > 1" title="业务属性" name="extra-properties">
    <tag-select :componentId="-1" />
    <div v-for="(item, index) in editorConfig" :key="index">
      <component
        :class="{
          required: item.required,
        }"
        :is="components[item.type]"
        :label="item.label"
        :property="item.key"
        :params="item.params"
        :config="item"
        :componentId="-1"
      />
    </div>
  </el-collapse-item>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import TagSelect from "../ExtraEditor/TagSelect/index.vue";
import ExtraSelect from "../ExtraEditor/Select/index.vue";
import ExtraSwitch from "../ExtraEditor/Switch/index.vue";

/**
 * @description 多选组件同时修改业务属性组件
 */
@Component({
  components: {
    TagSelect,
  },
})
export default class CompositeExtraEditor extends Vue {
  components = {
    select: ExtraSelect,
    switch: ExtraSwitch,
  };

  get editorConfig(): [] {
    if (!this.tag) {
      return [];
    }

    /**
     * 筛选出类别选项和是否为正确答案选项
     * 多选组件时，只允许同时设置类别和正确选项这两项
     */
    return this.$store.state.template.tags.find((tag: Tag) => tag.name === this.tag).editorConfig.filter((i: { key: string }) => i.key === "isCorrect" || i.key === "type");
  }

  get tag() {
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].tag;

    const hasDifferentVal = this.currentIds.some((id: string | number) => this.$store.state.componentMap[id].tag !== firstVal);

    if (hasDifferentVal) {
      return undefined;
    }

    return firstVal;
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }
}
</script>

<style lang="less" scoped></style>
