<!-- eslint-disable no-case-declarations -->
<template>
  <div class="combine-editor">
    <template v-if="currentComponents.length > 1">
      <div class="label">对齐</div>
      <div class="align-actions">
        <el-button size="small" v-for="(item, index) in alignmentList" :key="index" @click="handleClick(item.dir)">
          <svgicon :name="item.value" :ref="item" width="16" height="16" original />
          {{item.label}}
        </el-button>
      </div>
      <div class="align-actions grid">
        <el-button size="small" v-for="(item, index) in gridList" :key="index" @click="handleClick(item.dir)" :disabled="currentComponents.length < 3">
          <svgicon :name="item.value" :ref="item" width="16" height="16" original />
          {{item.label}}
        </el-button>
      </div>
      <!-- <svgicon :name="item" :ref="item" width="40" height="40" original /> -->
      <el-button @click="combine">组合</el-button>
    </template>
    <template v-if="currentComponents.length === 1 && currentComponents[0].type === 'group'">
      <el-button @click="separate">取消组合</el-button>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { combineHandler } from "../../TopBar/Operations";

@Component
export default class CombineEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  alignmentList = [
    {
      label: "左对齐",
      value: "arrangement/doms-align-left",
      dir: 'left'
    },
    {
      label: "水平居中",
      value: "arrangement/doms-align-center",
      dir: 'LCenter'
    },
    {
      label: "右对齐",
      value: "arrangement/doms-align-right",
      dir: 'right'
    },
    {
      label: "顶部对齐",
      value: "arrangement/doms-align-top",
      dir: 'top'
    },
    {
      label: "垂直居中",
      value: "arrangement/doms-align-middle",
      dir: 'VCenter'
    },
    {
      label: "底部对齐",
      value: "arrangement/doms-align-bottom",
      dir: 'bottom'
    }
  ];
  gridList = [
    {
      label: "水平分布",
      value: "arrangement/doms-distribution-level",
      dir: 'level'
    },
    {
      label: "垂直分布",
      value: "arrangement/doms-distribution-vertical",
      dir: 'vertical'
    }
  ]
    

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }
  // 保留两位小数点
  fixed2 (num: number) {
    return Math.round(num * 100) / 100;
  }

  rotatePosSelf (p: { x: number,y:number }, yaw: number): { x: number,y:number } {
    const rad = -yaw *Math.PI / 180;
    const sin = Math.sin(rad);
    const cos = Math.cos(rad);
    const x = p.x;
    const y = p.y;
    // p.x = x * cos - y * sin;
    // p.y = x * sin + y * cos;
    return { x: x * cos - y * sin, y: x * sin + y * cos }
  }

  handleClick(dir: string) {
    // console.log('dir', dir);
    const currentComponentsSize = this.currentComponents.map((item: any) => {
      const { x, y, width,  height } = (window as any).cocos.getRotateEWordRect(item.id);
      
      return {
        id: item.id,
        x: this.fixed2(x),
        y: this.fixed2(y),
        width: this.fixed2(width),
        height: this.fixed2(height)
      }
    });
    // console.log('duiqi-currentComponentsSize', currentComponentsSize);
    const left = Math.min(...currentComponentsSize.map((item: any) => item.x));
    const top = Math.min(...currentComponentsSize.map((item: any) => item.y));
    const bottom = this.fixed2(Math.max(...currentComponentsSize.map((item: any) => {
      const {  y, height } = item;
      return y + height;
    })));
    const right = Math.max(...currentComponentsSize.map((item: any) => {
      const {  x, width } = item;
      return x + width;
    }));
    // console.log('duiqi-left。。。', left, 'top', top, 'bottom', bottom, 'right', right);
    const maxX = Math.max(...currentComponentsSize.map((item: any) => item.x));
    // 最左侧的组件
    const leftComponent: any = currentComponentsSize.find((item: any) => item.x === left);
    // 最右侧的组件
    const rightComponent: any = currentComponentsSize.find((item: any) => (item.x + item.width) === right);
    // 最上的组件
    const topComponent: any = currentComponentsSize.find((item: any) => item.y === top);
    // 最底的组件
    const bottomComponent: any = currentComponentsSize.find((item: any) => this.fixed2(item.y + item.height) === bottom);
    // console.log('duiqi-bottom', bottom, bottomComponent, rightComponent);
    // return;
    switch(dir) {
      case 'left': {
        // done
        // 最小left对应的组件
        currentComponentsSize.forEach((comp) => {
          if(comp.id !== leftComponent.id) {
            // 获取目标x
            const { x: selfX } = (window as any).cocos.convertEditorWorldPos(comp.id,{ x: leftComponent.x });
            // 获取当前组件的x
            // console.log('duiqi-左对齐,targetX', leftComponent.x, 'selfX', selfX);
            this.$store.dispatch("updateComponentsProperties", {
              ids: [comp.id],
              newProperties: {
                ['x']: this.fixed2(selfX)
              },
            });
          }
        })
        break;
      } 
      case 'LCenter': {
        // 中心点对齐 done
        const centerX = left + (right - left) / 2;
        currentComponentsSize.forEach((comp) => {
          const { x: selfX } = (window as any).cocos.convertEditorWorldPos(comp.id,{ x: centerX - comp.width / 2 });
          this.$store.dispatch("updateComponentsProperties", {
            ids: [comp.id],
            newProperties: {
              ['x']: this.fixed2(selfX),
            },
          });
        })
        break;
      } 
      case 'right':
        // done
        currentComponentsSize.forEach((comp) => {
          // 最右的不处理 right
          if(Math.floor((comp.x + comp.width)) !== Math.floor(right)) {
            const { width: theWidth } = (window as any).cocos.getRotateEWordRect(comp.id);
            // console.log('duiqi-right=> theWidth', theWidth, 'right', right);
            const { x: selfX } = (window as any).cocos.convertEditorWorldPos(comp.id,{ x: right - theWidth });
            this.$store.dispatch("updateComponentsProperties", {
              ids: [comp.id],
              newProperties: {
                ['x']: this.fixed2(selfX),
              },
            });
          }
        })
        break;
      case 'top': {
        // done
        currentComponentsSize.forEach((comp) => {
          if(comp.id !== topComponent.id) {
            // 获取目标x
            const { y: selfY } = (window as any).cocos.convertEditorWorldPos(comp.id,{ y: topComponent.y });
            // 获取当前组件的x
            // console.log('duiqi-顶对齐,targetY', topComponent.y, 'selfX', selfY);
            this.$store.dispatch("updateComponentsProperties", {
              ids: [comp.id],
              newProperties: {
                ['y']: this.fixed2(selfY)
              },
            });
          }
        })
        break;
      }
      case 'VCenter': {
        // 中心点对齐 done
        const centerY = this.fixed2(top + (bottom - top) / 2);
        currentComponentsSize.forEach((comp) => {
          const { y: selfY } = (window as any).cocos.convertEditorWorldPos(comp.id,{ y: this.fixed2(centerY - comp.height / 2) });
          // console.log('duiqi-centerY', centerY, selfY, top, bottom);
          this.$store.dispatch("updateComponentsProperties", {
            ids: [comp.id],
            newProperties: {
              ['y']: this.fixed2(selfY),
            },
          });
        })
        break;
      }
      case 'bottom': {
        // done
        currentComponentsSize.forEach((comp) => {
          // 最右的不处理 right
          if(Math.floor((comp.y + comp.height)) !== Math.floor(bottom)) {
            // console.log('duiqi-bottom=> comp.height', comp.height, 'bottom', bottom, comp.y + comp.height);
            const { y: selfY } = (window as any).cocos.convertEditorWorldPos(comp.id,{ y: bottom - comp.height });
            this.$store.dispatch("updateComponentsProperties", {
              ids: [comp.id],
              newProperties: {
                ['y']: this.fixed2(selfY),
              },
            });
          }
        })
        break;
      }
      case 'level': {
        // 算出最小外接四边形的宽度
        const rectWidth = right - left;
        // 计算每个组件的宽度和
        const totalWidth = currentComponentsSize.reduce((pre: number, cur: any) => {
          return pre + Number(cur.width);
        }, 0);
        // 将差值平均分配成n-1份 gap
        const gap = this.fixed2((rectWidth - totalWidth) / (currentComponentsSize.length - 1));
        // currentComponentsSize.sort((a,b) => a.x - b.x);
        // 第一个 最后一个 第二个
        const temp: any[] = [];
        currentComponentsSize.forEach((comp) => {
          const { id } = comp;
          if(![leftComponent.id, rightComponent.id].includes(id)) {
            temp.push(comp);
          }
        })
        temp.push(rightComponent)
        temp.unshift(leftComponent)
        // console.log('duiqi-temp', temp, leftComponent, rightComponent);
        temp.forEach((comp, index) => {
          const { id } = comp;
          if(id !== leftComponent.id && id !== rightComponent.id) {
            const { x: preX, width: preWidth } = temp[index-1];
            const { x: selfX } = (window as any).cocos.convertEditorWorldPos(comp.id,{ x: preX + preWidth + gap});
            comp.x = selfX;
            // console.log('duiqi..水平分布，preX', preX, 'preWidth', preWidth, 'gap', gap, 'comp.x', comp.x, leftComponent.id, rightComponent.id, 'get-w', preX + preWidth + gap, 'selfX', selfX);
            this.$store.dispatch("updateComponentsProperties", {
              ids: [id],
              newProperties: {
                ['x']: selfX,
              },
            });
          }
        })
        break;
      } 
      case 'vertical': {
        // 算出最小外接四边形的宽度
        const rectHeight = bottom - top;
        // 计算每个组件的宽度和
        const totalHeight= currentComponentsSize.reduce((pre: number, cur: any) => {
          return pre + Number(cur.height);
        }, 0);
        // 将差值平均分配成n-1份 gap
        const gap = this.fixed2((rectHeight - totalHeight) / (currentComponentsSize.length - 1));
        // currentComponentsSize.sort((a,b) => a.y - b.y);
        // 第一个 最后一个 第二个
        // console.log('duiqi-topComponent', topComponent, 'bottomComponent', bottomComponent);
        const temp: any[] = [];
        currentComponentsSize.forEach((comp) => {
          const { id } = comp;
          // console.log('duiqi-id', id);
          if(![topComponent.id, bottomComponent.id].includes(id)) {
            temp.push(comp);
          }
        })
        temp.push(bottomComponent)
        temp.unshift(topComponent)
        // console.log('duiqi-temp', temp);
        temp.forEach((comp, index) => {
          const { id } = comp;
          if(id !== topComponent.id && id !== bottomComponent.id) {
            const { y: preY, height: preHeight } = temp[index-1];
            const { y: selfY } = (window as any).cocos.convertEditorWorldPos(comp.id,{ y: preY + preHeight + gap});
            comp.y = selfY;
            // console.log('duiqi..preY', preY, 'preHeight', preHeight, 'gap', gap, 'comp.y', comp.y, topComponent.id, bottomComponent.id);
            this.$store.dispatch("updateComponentsProperties", {
              ids: [id],
              newProperties: {
                ['y']: comp.y,
              },
            });
          }
        })
        break;
      } 
    }
  }

  combine() {
    combineHandler(this.currentIds);
  }

  separate() {
    this.$store.dispatch("separateGroupComponent", this.currentIds[0]);
  }
}
</script>

<style scoped lang="less">
.combine-editor {
  padding: 10px;
  .label {
    text-align: left;
    min-width: 40px;
    margin: 0 0 10px -6px;
    font-size: 14px;
  }
  .align-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
     /deep/ .el-button {
      width: 33%;
      margin-bottom: 12px;
      span {
        display: flex;
        align-items: center;
      }
    }
    /deep/ .el-button+.el-button {
        margin-left: 0px;
    }
    &.grid {
      /deep/ .el-button {
        width: 45%;
      }
    }
  }
}

</style>
