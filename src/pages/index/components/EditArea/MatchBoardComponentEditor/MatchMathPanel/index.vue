<!--
 * @Date: 2021-09-10 16:44:09
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-13 19:08:11
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/MatchBoardComponentEditor/MatchMathPanel/index.vue
 * @Author: chxu
 * 运算分类 移动 增加 删除
-->
<template>
  <div class="match-path-panel">
    <el-form-item label="运算分类">
      <el-tooltip
        class="item"
        effect="dark"
        content="数的范围是0～99"
        placement="top"
      >
        <i class="el-icon-question button-tips"></i>
      </el-tooltip>
      <el-radio-group v-model="mathType" @click.native="handleRadioClick">
        <el-radio
          v-for="type in mathTypes"
          :key="type.value"
          :label="type.value"
          >{{ type.label }}</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <MathModel
      :matchSymbols="matchSymbols"
      @change="handleSymbolChange"
      @blur="handleSymbolBlur"
      @enter="handleSymbolBlur"
      @selectClick="handleSelectSymbolChange"
    />
    <MatchAction
      :currentComponents="currentComponents"
      :componentId="componentId"
    />
    <div v-for="(answerSymbol, index) in answerSymbols" :key="index">
      <div class="answer-title">
        <div>正确答案{{ index + 1 }}</div>
        <el-button
          v-if="answerSymbols.length > 1"
          type="text"
          icon="el-icon-delete"
          circle
          @click="
            () => {
              handleDelete(index);
            }
          "
        ></el-button>
      </div>
      <MathModel
        :matchSymbols="answerSymbol"
        @change="handleSymbolChange"
        @selectClick="handleSelectSymbolChange"
        @blur="handleSymbolBlur"
        @enter="handleSymbolBlur"
      />
    </div>
    <div class="button-wrapper">
      <el-button
        class="add-button"
        type="primary"
        round
        @click="handleAdd"
        style="100%"
        >增加答案</el-button
      >
      <el-tooltip
        class="item"
        effect="dark"
        content="答案只比对最终形状，与位置、实现过程无关；最终形状一样，位置不同也算对"
        placement="top"
      >
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import MathModel from "./MathModel.vue";
import MatchAction from "../MatchAction.vue";
import { MatchData2NumberMap } from "./MatchData2NumberMap";
import { isEqual } from "lodash-es";

interface MatchSymbol {
  type: string;
  value: string | undefined;
}

@Component({
  components: {
    MathModel,
    MatchAction,
  },
})
export default class MatchMathPanel extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  @Prop({
    required: true,
  })
  componentId!: string;

  public mathTypes: Array<{ label: string; value: number }> = [
    {
      label: "两个数运算",
      value: 1,
    },
    {
      label: "三个数运算",
      value: 2,
    },
  ];

  public actionTypes: Array<{ label: string; value: number }> = [
    {
      label: "移动",
      value: 0,
    },
    {
      label: "增加",
      value: 1,
    },
    {
      label: "删除",
      value: 2,
    },
  ];

  private isMounted = false;

  public symbolInputChanged = false;

  public symbolBlur = true;

  public mathTypeClickChanged = false;

  // 初始化
  public matchSymbols: MatchSymbol[] = [];

  // 答案
  public answerSymbols: MatchSymbol[][] = [];

  public blurTimer: unknown = null;

  @Watch("pattern", { deep: true })
  async onPatternChanged(val: number[][]) {
    if (this.symbolInputChanged || this.mathTypeClickChanged) {
      this.symbolInputChanged = false;
      this.symbolBlur = true;
      this.mathTypeClickChanged = false;
      return;
    }
    this.matchSymbols = this.patternToSymbol(val);
  }

  @Watch("rightAnswer", { deep: true })
  async onRightAnswerChanged(val: number[][][]) {
    if (this.symbolInputChanged || this.mathTypeClickChanged) {
      this.symbolInputChanged = false;
      this.symbolBlur = true;
      this.mathTypeClickChanged = false;
      return;
    }
    this.answerSymbols = val.map((item: number[][]) =>
      this.patternToSymbol(item),
    );
  }

  @Watch("matchSymbols", { deep: true })
  async onMatchSymbolsChanged(val: MatchSymbol[]) {
    if (!this.isMounted) return;
    if (this.blurTimer) return;
    if (this.symbolInputChanged || this.mathTypeClickChanged) {
      await new Promise(resolve => {
        this.blurTimer = setInterval(() => {
          if (this.symbolBlur) {
            this.blurTimer = null;
            resolve(true);
          }
        }, 200);
      });
      if (this.matchType === 1) return;
      const pattern = this.symbolToPattern(val);
      this.updateProperties("pattern", pattern);
    }
  }

  @Watch("answerSymbols", { deep: true })
  async onAnswerSymbolsChanged(val: MatchSymbol[][], oldVal: MatchSymbol[][]) {
    console.log("answerSymbols watch-oldVal", val, oldVal, this.isMounted, this.blurTimer, this.symbolInputChanged, this.mathTypeClickChanged);
    if (!this.isMounted) return;
    if (this.blurTimer) return;
    if (this.symbolInputChanged || this.mathTypeClickChanged) {
      this.symbolInputChanged = false;
      this.mathTypeClickChanged = false;
      const mathType = this.mathType;
      await new Promise(resolve => {
        this.blurTimer = setInterval(() => {
          if (this.symbolBlur) {
            if(this.blurTimer) {
              clearInterval((this.blurTimer as any));
            }
            this.blurTimer = null;
            resolve(true);
          }
        }, 200);
      });
      if (this.matchType === 1) return;
      if (this.mathType !== mathType) return;
      const patterns = val.map((item: MatchSymbol[]) =>
        this.symbolToPattern(item),
      );
      this.updateProperties("rightAnswer", patterns);
    }
  }

  @Watch("mathType")
  async onmathTypeChanged(val: number) {
    if (this.mathTypeClickChanged) {
      this.matchSymbols = this.initMatchSymbols(val, false);
      this.answerSymbols = [];
      const pattern = this.symbolToPattern(this.matchSymbols);
      this.updateProperties("pattern", pattern);
      this.updateProperties("rightAnswer", []);
      await this.$nextTick();
      this.mathTypeClickChanged = false;
      this.symbolInputChanged = false;
    }
  }

  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  get matchConfig(): [] {
    return this.currentComponents[0].properties;
  }

  get matchType(): number {
    return this.currentComponents[0].properties.matchType;
  }

  get mathType(): number {
    return this.currentComponents[0].properties.mathType;
  }

  set mathType(val) {
    if (val === this.mathType) return;
    this.updateProperties("mathType", val);
  }

  get pattern(): number[][] {
    return this.currentComponents[0].properties.pattern;
  }

  get rightAnswer(): number[][][] {
    return this.currentComponents[0].properties.rightAnswer;
  }

  get actionType(): number {
    return this.currentComponents[0].properties.actionType;
  }

  set actionType(val) {
    if (val === this.actionType) return;
    this.updateProperties("actionType", val);
  }

  get actionNumbers(): number {
    return this.currentComponents[0].properties.actionNumbers;
  }

  set actionNumbers(val) {
    if (val === this.actionNumbers) return;
    this.updateProperties("actionNumbers", val);
  }

  get actionTypeTip(): string {
    const currentAction = this.actionTypes.find(
      item => item.value === this.actionType,
    );
    return currentAction ? currentAction.label : "";
  }

  initMatchSymbols(mathType: number, isAnswer: boolean) {
    if (mathType === 1) {
      return [
        {
          type: "number",
          value: isAnswer ? " " : "1",
        },
        {
          type: "symbol",
          value: "+",
        },
        {
          type: "number",
          value: isAnswer ? " " : "7",
        },
        {
          type: "equal",
          value: "=",
        },
        {
          type: "number",
          value: isAnswer ? " " : "8",
        },
      ];
    } else {
      return [
        {
          type: "number",
          value: isAnswer ? " " : "1",
        },
        {
          type: "symbol",
          value: "+",
        },
        {
          type: "number",
          value: isAnswer ? " " : "7",
        },
        {
          type: "symbol",
          value: "-",
        },
        {
          type: "number",
          value: isAnswer ? " " : "1",
        },
        {
          type: "equal",
          value: "=",
        },
        {
          type: "number",
          value: isAnswer ? " " : "8",
        },
      ];
    }
  }

  handleAdd() {
    if (this.answerSymbols.length >= 5) {
      this.$message({
        message: "最多只能添加5个答案哦～",
        type: "warning",
      });
      return;
    }
    console.log("handleAdd");
    this.answerSymbols.splice(
      this.answerSymbols.length,
      0,
      this.initMatchSymbols(this.mathType, true),
    );
  }

  handleDelete(index: number) {
    this.symbolInputChanged = true;
    this.answerSymbols.splice(index, 1);
  }

  handleSymbolChange() {
    this.symbolInputChanged = true;
    this.symbolBlur = false;
  }

  handleSelectSymbolChange() {
    this.symbolInputChanged = true;
    this.symbolBlur = true;
  }

  handleSymbolBlur() {
    console.log("handleSymbolBlur");
    this.symbolBlur = true;
  }

  handleRadioClick() {
    console.log("handleRadioClick");
    this.mathTypeClickChanged = true;
  }

  mounted() {
    console.log("mounted math panel");
    // 转 answerSymbol
    this.matchSymbols = this.patternToSymbol(this.pattern);
    this.$set(
      this,
      "answerSymbols",
      this.rightAnswer.map((item: number[][]) => this.patternToSymbol(item)),
    );
    this.$nextTick(() => {
      this.isMounted = true;
    });
  }
  updateProperties(key: string, val: unknown) {
    console.log('updateProperties', key, val);
    if(key === 'pattern') {
      console.log('updateProperties pattern', val)
    }
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }

  patternToSymbol(patt: number[][]) {
    const temp = [];
    const symbolStr = patt.map(item => MatchData2NumberMap.get(item.join("")));

    while (symbolStr && symbolStr.length) {
      const nextIndex = symbolStr.findIndex(item =>
        ["+", "-", "="].includes(item as string),
      );
      if (nextIndex > -1) {
        temp.push(symbolStr.splice(0, nextIndex));
        temp.push(symbolStr.shift());
      } else {
        temp.push(symbolStr.splice(0, symbolStr.length));
      }
    }

    return temp.map(item => {
      if (Array.isArray(item)) {
        return {
          type: "number",
          value: item.join("") || "",
        };
      } else if (item === "=") {
        return {
          type: "equal",
          value: item,
        };
      } else if (["+", "-"].includes(item as string)) {
        return {
          type: "symbol",
          value: item,
        };
      } else {
        return {
          type: "number",
          value: item || "",
        };
      }
    });
  }

  symbolToPattern(symbols: MatchSymbol[]) {
    // 不存在的话 " "
    console.log(
      symbols
        .map(item => item.value || " ")
        .join("")
        .split(""),
      "symbols str",
    );
    const tempSymbols: string[] = symbols
      .map(item => item.value || " ")
      .join("")
      .split("");
    return tempSymbols.map(item => {
      const temp =
        MatchData2NumberMap.get(item ? item : " ") ||
        MatchData2NumberMap.get(" ");
      return (temp as string).split("").map(numStr => Number(numStr));
    });
  }
}
</script>

<style lang="less" scoped>
.match-path-panel {
  text-align: left;
  .el-form-item {
    margin-bottom: 6px;
  }
  .math-model-wrapper {
    display: flex;
    justify-content: space-between;
  }
}
</style>

<style lang="less">
.match-path-panel {
  .math-model-wrapper {
    .el-form-item {
      max-width: 46px !important;
      input {
        padding: 5px !important;
      }
      .equal {
        text-align: center;
      }
    }
  }
  .button-tips {
    position: absolute;
    left: 64px;
    top: -30px;
  }
  .button-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-button {
      margin-top: 10px;
      width: 90%;
    }
  }
}
</style>
