<!--
 * @Date: 2021-09-10 16:44:09
 * @LastEditors: chxu
 * @LastEditTime: 2021-10-09 14:52:27
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/MatchBoardComponentEditor/MatchMathPanel/MathModel.vue
 * @Author: chxu
 * 运算分类 移动 增加 删除
-->
<template>
  <div class="math-model-wrapper">
    <el-form-item
      label=""
      v-for="(matchSymbol, index) in matchSymbols"
      :key="index"
    >
      <el-input
        type="age"
        autocomplete="off"
        v-if="matchSymbol.type === 'number'"
        :value="matchSymbol.value"
        @blur="handleBlur"
        @keyup.enter.native="handleEnter"
        @input="
          val => {
            handleInput(val, index);
          }
        "
      ></el-input>

      <template v-if="matchSymbol.type === 'symbol'">
        <el-select
          size="small"
          placeholder=""
          v-model="matchSymbol.value"
          @change="handleSelectClick"
        >
          <el-option
            v-for="symbolType in symbolTypes"
            :key="symbolType.value"
            :label="symbolType.label"
            :value="symbolType.value"
          >
          </el-option>
        </el-select>
      </template>
      <div v-if="matchSymbol.type === 'equal'" class="equal">=</div>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
  components: {
    // ImageSelect,
  },
})
export default class MatchPanel extends Vue {
  @Prop({
    default: () => ({}),
  })
  matchSymbols!: Array<{ label: string; value: string }>;

  public symbolTypes: Array<{ label: string; value: string }> = [
    {
      label: "+",
      value: "+",
    },
    {
      label: "-",
      value: "-",
    },
  ];

  handleInput(val: string, index: number) {
    const re = /[^0-9]/g;
    if (re.test(val)) {
      let temp = parseFloat(val);
      if (!isNaN(temp) && String(temp).length > 2) {
        temp = Number(String(temp).slice(0, 2));
      }
      console.log(isNaN(temp) ? null : temp, "temp");
      this.matchSymbols[index].value = isNaN(temp) ? "" : String(temp);
    } else {
      // 如果是0+... 需要去掉0
      this.matchSymbols[index].value =
        val[0] === "0" && val[1] ? val.substr(1, 3) : val.substr(0, 2);
    }
    this.$emit("change", this.matchSymbols[index].value);
  }

  handleSelectClick() {
    this.$emit("selectClick");
  }

  handleBlur(e: { target: { value: string } }) {
    this.$emit("blur", e.target.value);
  }

  handleEnter(e: { target: { blur: () => void } }) {
    e.target.blur();
  }
}
</script>

<style lang="less" scoped>
.math-model-wrapper {
  display: flex;
  justify-content: space-between;
}
</style>

<style lang="less">
.math-model-wrapper {
  .el-form-item {
    max-width: 46px !important;
    input {
      padding: 5px !important;
    }
    .equal {
      text-align: center;
    }
  }
}
</style>
