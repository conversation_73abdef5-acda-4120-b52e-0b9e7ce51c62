<!--
 * @Date: 2021-09-10 16:44:09
 * @LastEditors: chxu
 * @LastEditTime: 2021-09-21 18:01:08
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/MatchBoardComponentEditor/MatchMathPanel/MatchAction.vue
 * @Author: chxu
 * 运算分类 移动 增加 删除
-->
<template>
  <div class="match-path-panel">
    <el-form-item label="">
      <el-radio-group v-model="actionType">
        <el-radio
          v-for="atype in actionTypes"
          :key="atype.value"
          :label="atype.value"
          >{{ atype.label }}</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="`可${actionTypeTip}的火柴根数`">
      <el-select size="small" placeholder="" v-model="actionNumbers">
        <el-option v-for="num in 5" :key="num" :label="num" :value="num">
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import MathModel from "./MatchMathPanel/MathModel.vue";

@Component({
  components: {
    MathModel,
  },
})
export default class MatchAction extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  @Prop({
    required: true,
  })
  componentId!: string;

  public actionTypes: Array<{ label: string; value: number }> = [
    {
      label: "移动",
      value: 0,
    },
    {
      label: "增加",
      value: 1,
    },
    {
      label: "删除",
      value: 2,
    },
  ];
  get matchConfig(): [] {
    return this.currentComponents[0].properties;
  }

  get actionType(): number {
    return this.currentComponents[0].properties.actionType;
  }

  set actionType(val) {
    if (val === this.actionType) return;
    this.updateProperties("actionType", val);
  }

  get actionNumbers(): number {
    return this.currentComponents[0].properties.actionNumbers;
  }

  set actionNumbers(val) {
    if (val === this.actionNumbers) return;
    this.updateProperties("actionNumbers", val);
  }

  get actionTypeTip(): string {
    const currentAction = this.actionTypes.find(
      item => item.value === this.actionType,
    );
    return currentAction ? currentAction.label : "";
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.match-path-panel {
  text-align: left;
  .el-form-item {
    margin-bottom: 6px;
  }
  .math-model-wrapper {
    display: flex;
    justify-content: space-between;
  }
}
</style>

<style lang="less">
.match-path-panel {
  .math-model-wrapper {
    .el-form-item {
      max-width: 46px !important;
      input {
        padding: 5px !important;
      }
      .equal {
        text-align: center;
      }
    }
  }
  .answer-title {
    display: flex;
    justify-content: space-between;
  }
}
</style>
