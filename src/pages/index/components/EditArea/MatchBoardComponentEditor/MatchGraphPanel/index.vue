<template>
  <el-form label-position="left" size="small" label="题目属性" class="match-graph-panel">
    <div class="flex-wrapper">
      <el-form-item label="行">
        <el-select size="small" placeholder="" v-model="row">
          <el-option v-for="num in 5" :key="num" :label="num" :value="num"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="列">
        <el-select size="small" placeholder="" v-model="col">
          <el-option v-for="num in 7" :key="num" :label="num" :value="num"> </el-option>
        </el-select>
      </el-form-item>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="是否需要斜向火柴" style="maxWidth: 100% !important;">
        <el-switch v-model="obliqueMatch" />
      </el-form-item>
    </div>
    <el-form-item label="初始图形">
      <el-tooltip
        class="item"
        effect="dark"
        content="点击网格的边可选中或取消选中
          "
        placement="top"
      >
        <i class="el-icon-question button-tips"></i>
      </el-tooltip>
      <GraphItem :pattern="pattern" :col="col" :row="row" @changeStatus="handlePatternChange" />
    </el-form-item>
    <MatchAction :currentComponents="currentComponents" :componentId="componentId" />
    <div class="flex-wrapper">
      <el-form-item label="答案展示是否居中" style="maxWidth: 100% !important;">
        <el-switch v-model="answerDisplayCenter" />
      </el-form-item>
    </div>
    <div v-for="(answerItem, index) in rightAnswer" :key="index">
      <div class="answer-title">
        <div>正确答案{{ index + 1 }}</div>
        <el-button
          v-if="rightAnswer.length > 1"
          type="text"
          icon="el-icon-delete"
          circle
          @click="
            () => {
              handleDelete(index);
            }
          "
        ></el-button>
      </div>
      <GraphItem
        :col="col"
        :row="row"
        :pattern="answerItem"
        @changeStatus="
          pos => {
            handleAnswerChange(pos, index);
          }
        "
      />
    </div>
    <div class="button-wrapper">
      <el-button type="primary" round @click="handleAdd" class="add-button">增加答案</el-button>
      <el-tooltip class="item" effect="dark" content="答案只比对最终形状，与位置、实现过程无关；最终形状一样，位置不同也算对" placement="top">
        <i class="el-icon-question"></i>
      </el-tooltip>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="是否展示目标区" style="maxWidth: 100% !important;">
        <el-switch v-model="showTargetArea" />
      </el-form-item>
    </div>
    <template v-if="showTargetArea">
      <el-form-item label="选择目标区">
        <el-select size="small" placeholder="" v-model="selectTargetArea">
          <el-option v-for="num in rightAnswer.length" :key="num" :label="`正确答案${num}`" :value="num-1"> </el-option>
        </el-select>
      </el-form-item>
      <div class="flex-wrapper">
        <el-form-item label="在C端是否可以拖拽" style="maxWidth: 100% !important;">
          <el-switch v-model="isCanDrag" />
        </el-form-item>
      </div>
      <div class="target-property-wrapper">
        <div class="label">目标区属性</div>
        <div class="flex-wrapper">
          <ya-input-number label="位置    X" property="targetAreaX" type="number" :options="{ required: true }" />
          <ya-input-number label="Y" property="targetAreaY" type="number" :options="{ required: true }" />
        </div>
        <div class="flex-wrapper">
          <ya-input-number label="缩放倍数" property="targetAreaScale" type="number" :options="{ required: true, min: 0.5, max: 5 }" />
          <ya-input-number label="角度" property="targetAreaAngle" type="number" :options="{ required: true }" />
        </div>
      </div>
    </template>
    <div class="flex-wrapper">
      <el-form-item label="目标区火柴" style="maxWidth: 100% !important;" class="target-form-tips-wrapper">
        <el-tooltip
          class="item"
          effect="dark"
          content="请按照95*14px大小上传火柴"
          placement="top"
        >
          <i class="el-icon-question"></i>
        </el-tooltip>
        <image-select :src.sync="targetAreaMatchTexture" />
        <el-input v-model="targetAreaMatchTexture" style="display: none;" />
      </el-form-item>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="操作区火柴底" style="maxWidth: 100% !important;">
        <image-select :src.sync="operationAreaBgTexture" />
        <el-input v-model="operationAreaBgTexture" style="display: none;" />
      </el-form-item>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="透明度" style="maxWidth: 100% !important;" class="target-form-tips-wrapper">
        <el-tooltip
          class="item"
          effect="dark"
          content="范围：0～255"
          placement="top"
        >
          <i class="el-icon-question"></i>
        </el-tooltip>
        <ya-input-number label="" property="operationAreaBgOpacity" type="number" :options="{ min: 0, max: 255, type: 'positive integer' }" class="input-number-opacity"/>
      </el-form-item>
    </div>
    
    <div class="flex-wrapper">
      <el-form-item label="操作区火柴" style="maxWidth: 100% !important;">
        <image-select :src.sync="operationAreaMatchTexture" />
        <el-input v-model="operationAreaMatchTexture" style="display: none;" />
      </el-form-item>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="操作区变色火柴" style="maxWidth: 100% !important;">
        <image-select :src.sync="operationAreaColorfulMatchTexture" />
        <el-input v-model="operationAreaColorfulMatchTexture" style="display: none;" />
      </el-form-item>
    </div>
    <div class="flex-wrapper">
      <el-form-item label="操作区火柴召唤态" style="maxWidth: 100% !important;">
        <image-select :src.sync="operationAreaMatchStatusTexture" />
        <el-input v-model="operationAreaMatchStatusTexture" style="display: none;" />
      </el-form-item>
    </div>
  </el-form>
</template>

<script lang="ts">
import { cloneDeep, isEqual } from "lodash-es";
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import MatchAction from "../MatchAction.vue";
import GraphItem from "./GraphItem.vue";
import horActiveSvg from "./resource/hor-active.svg";
import horStaticSvg from "./resource/hor-static.svg";
import verActiveSvg from "./resource/ver-active.svg";
import verStaticSvg from "./resource/ver-static.svg";
import Label from "@/components/Label/index.vue";
import YaInputNumber from "@/pages/index/components/EditArea/EditComponents/InputNumber/index.vue";
import ImageSelect from "@/components/ImageSelect/index.vue";
interface Grid {
  row: number;
  col: number;
}

@Component({
  components: {
    GraphItem,
    MatchAction,
    YaLabel: Label,
    YaInputNumber,
    ImageSelect,
  },
})
export default class MatchGraphPanel extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  @Prop({
    required: true,
  })
  componentId!: string;

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  public actionTypes: Array<{ label: string; value: number }> = [
    {
      label: "移动",
      value: 0,
    },
    {
      label: "增加",
      value: 1,
    },
    {
      label: "删除",
      value: 2,
    },
  ];

  getSrc(rowIndex: number, colIndex: number) {
    const svgs = {
      hor: {
        active: horActiveSvg,
        default: horStaticSvg,
      },
      ver: {
        active: verActiveSvg,
        default: verStaticSvg,
      },
    };
    const dir = rowIndex % 2 === 0 ? "hor" : "ver";
    const status = this.pattern[rowIndex][colIndex] ? "active" : "default";
    return svgs[dir][status];
  }

  @Watch("grid")
  async onGidChanged(val: Grid, oldVal: any) {
    if(isEqual(val, oldVal)) return;
    this.updateProperties("pattern", this.getPatternByGrid(this.pattern, val));
    this.updateProperties(
      "rightAnswer",
      this.rightAnswer.map((item: number[][]) => this.getPatternByGrid(item, val)),
    );
  }

  getPatternByGrid(pattern: number[][], grid: Grid) {
    const newPattern = [];

    const getRowItem = (col: number) => {
      const rowItem = [];
      for (let i = 0; i < col + 1; i++) {
        rowItem[i] = 0;
      }
      return rowItem;
    };
    // const len = this.obliqueMatch ? 3 * grid.row + 1 : 2 * grid.row + 1;
    // pattern 为旧的数据 斜向和正向的数据
    const prePatternRow = this.obliqueMatch ? (pattern.length - 1 ) / 3 : (pattern.length - 1 ) / 2;
    const prePart1 = pattern.slice(0, 2 * prePatternRow + 1);
    const prePart2 = pattern.slice(2 * prePatternRow + 1);
    console.log('prePatternRow', pattern, prePatternRow, prePatternRow, prePart1, prePart2);

    for (let i = 0; i < 2 * grid.row + 1; i++) {
      if (prePart1[i]) {
        const rowItem = [];
        
        for (let j = 0; j < grid.col + 1; j++) {
          // i为基数 j是最后一个 为0
          if(i % 2 === 0 && j === grid.col) {
            rowItem[j] = 0;
          } else {
            rowItem[j] = prePart1[i][j] ? prePart1[i][j] : 0;
          }
        }
        newPattern[i] = rowItem;
      } else {
        newPattern[i] = getRowItem(grid.col);
      }
    }
    // 斜向火柴
    if(this.obliqueMatch) {

      for (let i = 0; i < grid.row; i++) {
        if (prePart2[i]) {
          const rowItem = [];
          for (let j = 0; j < 2 * grid.col; j++) {
            rowItem[j] = prePart2[i][j] ? prePart2[i][j] : 0;
          }
          newPattern.push(rowItem);
        } else {
          newPattern.push(getRowItem(2 * grid.col - 1));
        }
      }
    }
    return newPattern;
  }

  get matchConfig() {
    return this.currentComponents[0].properties;
  }

  get grid(): Grid {
    return this.currentComponents[0].properties.grid;
  }

  get row(): number {
    return this.grid.row;
  }

  set row(val) {
    this.updateProperties("grid", {
      ...this.grid,
      row: val,
    });
  }

  get col(): number {
    return this.grid.col;
  }

  set col(val) {
    this.updateProperties("grid", {
      ...this.grid,
      col: val,
    });
  }

  get obliqueMatch() {
    return this.matchConfig.obliqueMatch;
  }

  set obliqueMatch(obliqueMatch: boolean) {
    if (obliqueMatch === this.obliqueMatch) return;
    let copyPattern = cloneDeep(this.matchConfig.pattern);
    const copyRightAnswer = cloneDeep(this.matchConfig.rightAnswer);
    console.log('xu-copyRightAnswer', copyRightAnswer);
    if (!obliqueMatch) {
      copyPattern = copyPattern.slice(0, 2 * this.row + 1);
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      copyRightAnswer.forEach((answer: any) => {
        answer.splice(2 * this.row + 1, this.row);
      });
    } else {
      copyPattern = copyPattern.slice(0, 2 * this.row + 1);
      if (copyPattern.length === 2 * this.row + 1) {
        copyPattern.push(...Array(this.row).fill(Array(2 * this.col).fill(0)));
        console.log("xu-copyPattern", copyPattern);
      }
      copyRightAnswer.forEach((answer: any) => {
        if (answer.length === 2 * this.row + 1) {
          answer.push(...Array(this.row).fill(Array(2 * this.col).fill(0)));
        }
        // answer.forEach((item: any) => {
        //   if (item.length === 2 * this.row + 1) {
        //     item.push(...Array(this.row).fill(Array(2 * this.col).fill(0)));
        //   }
        // });
      });
    }
    // 决定了斜向火柴的数据
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        ["obliqueMatch"]: obliqueMatch,
        ["pattern"]: copyPattern,
        ["rightAnswer"]: copyRightAnswer,
      },
    });
  }

  get selectTargetArea() {
    return this.matchConfig.selectTargetArea;
  }

  set selectTargetArea(selectTargetArea: number) {
    if (selectTargetArea === this.selectTargetArea) return;
    this.updateProperties("selectTargetArea", selectTargetArea);
  }

  get targetAreaMatchTexture() {
    return this.matchConfig.targetAreaMatchTexture;
  }

  set targetAreaMatchTexture(targetAreaMatchTexture: string) {
    this.updateProperties("targetAreaMatchTexture", targetAreaMatchTexture);
  }

  get operationAreaBgTexture() {
    return this.matchConfig.operationAreaBgTexture;
  }

  set operationAreaBgTexture(operationAreaBgTexture: string) {
    this.updateProperties("operationAreaBgTexture", operationAreaBgTexture);
  }

  get operationAreaBgOpacity() {
    return this.matchConfig.operationAreaBgOpacity;
  }

  set operationAreaBgOpacity(operationAreaBgOpacity: string) {
    this.updateProperties("operationAreaBgOpacity", operationAreaBgOpacity);
  }

  get operationAreaMatchTexture() {
    return this.matchConfig.operationAreaMatchTexture;
  }

  set operationAreaMatchTexture(operationAreaMatchTexture: string) {
    this.updateProperties("operationAreaMatchTexture", operationAreaMatchTexture);
  }

  get operationAreaColorfulMatchTexture() {
    return this.matchConfig.operationAreaColorfulMatchTexture;
  }

  set operationAreaColorfulMatchTexture(operationAreaColorfulMatchTexture: string) {
    this.updateProperties("operationAreaColorfulMatchTexture", operationAreaColorfulMatchTexture);
  }

  get operationAreaMatchStatusTexture() {
    return this.matchConfig.operationAreaMatchStatusTexture;
  }

  set operationAreaMatchStatusTexture(operationAreaMatchStatusTexture: string) {
    this.updateProperties("operationAreaMatchStatusTexture", operationAreaMatchStatusTexture);
  }

  get isCanDrag() {
    return this.matchConfig.isCanDrag;
  }

  set isCanDrag(isCanDrag: number) {
    if (isCanDrag === this.isCanDrag) return;
    this.updateProperties("isCanDrag", isCanDrag);
  }

  get targetAreaX() {
    return this.matchConfig.targetAreaX;
  }

  set targetAreaX(targetAreaX: number) {
    if (targetAreaX === this.targetAreaX) return;
    this.updateProperties("targetAreaX", targetAreaX);
  }

  get targetAreaY() {
    return this.matchConfig.targetAreaY;
  }

  set targetAreaY(targetAreaY: number) {
    if (targetAreaY === this.targetAreaY) return;
    this.updateProperties("targetAreaY", targetAreaY);
  }

  get targetAreaScale() {
    return this.matchConfig.targetAreaScale;
  }

  set targetAreaScale(targetAreaScale: number) {
    if (targetAreaScale === this.targetAreaScale) return;
    this.updateProperties("targetAreaScale", targetAreaScale);
  }

  get targetAreaAngle() {
    return this.matchConfig.targetAreaAngle;
  }

  set targetAreaAngle(targetAreaAngle: number) {
    if (targetAreaAngle === this.targetAreaAngle) return;
    this.updateProperties("targetAreaAngle", targetAreaAngle);
  }

  get answerDisplayCenter() {
    return this.matchConfig.answerDisplayCenter;
  }

  set answerDisplayCenter(val: boolean) {
    if (val === this.answerDisplayCenter) return;
    this.updateProperties("answerDisplayCenter", val);
  }

  get showTargetArea() {
    return this.matchConfig.showTargetArea;
  }

  set showTargetArea(showTargetArea: boolean) {
    if (showTargetArea === this.showTargetArea) return;
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        ["showTargetArea"]: showTargetArea,
        ["selectTargetArea"]: undefined,
        ["targetAreaX"]: 300,
        ["targetAreaY"]: 0,
        ["targetAreaScale"]: 1,
        ["targetAreaAngle"]: 0,
        ["isCanDrag"]: showTargetArea,
      },
    });
    // this.updateProperties("showTargetArea", showTargetArea);
  }

  get pattern(): number[][] {
    return this.matchConfig.pattern;
  }

  get rightAnswer(): number[][][] {
    return this.matchConfig.rightAnswer;
  }

  handleAdd() {
    console.log("handleAdd");
    if (this.rightAnswer.length >= 5) {
      this.$message({
        message: "最多只能添加5个答案哦～",
        type: "warning",
      });
      return;
    }
    const copyAnswer = cloneDeep(this.rightAnswer);
    copyAnswer.splice(copyAnswer.length, 0, this.getPatternByGrid([], this.grid));
    this.updateProperties("rightAnswer", copyAnswer);
  }

  handleDelete(index: number) {
    console.log("handleDelete");
    const copyAnswer = cloneDeep(this.rightAnswer);
    copyAnswer.splice(index, 1);
    this.updateProperties("rightAnswer", copyAnswer);
    // 如果目标区选择的是删除的数据 需要更新目标区的值
    if (this.selectTargetArea === index) {
      this.updateProperties("selectTargetArea", undefined);
    }
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }

  handlePatternChange({ rowIndex, colIndex }: { rowIndex: number; colIndex: number }) {
    console.log("rowIndex", colIndex);
    const copyPattern = cloneDeep(this.pattern);
    const oldVal = copyPattern[rowIndex][colIndex];
    copyPattern[rowIndex][colIndex] = Number(!oldVal);
    this.updateProperties("pattern", copyPattern);
  }

  handleAnswerChange(
    {
      rowIndex,
      colIndex,
    }: {
      rowIndex: number;
      colIndex: number;
    },
    index: number,
  ) {
    const copyAnswer = cloneDeep(this.rightAnswer);
    const answerItem = copyAnswer[index];
    const oldVal = answerItem[rowIndex][colIndex];
    answerItem[rowIndex][colIndex] = Number(!oldVal);
    this.updateProperties("rightAnswer", copyAnswer);
  }
}
</script>

<style lang="less" scoped>
.match-graph-panel {
  text-align: left;
  .el-form-item {
    margin-bottom: 6px;
  }
  .flex-wrapper {
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
  }
}
</style>

<style lang="less">
.match-graph-panel {
  .flex-wrapper {
    .el-form-item {
      display: flex;
      max-width: 120px !important;
      input {
        padding: 5px !important;
      }
    }
    .el-form-item__label {
      padding-right: 10px;
    }
  }
  .answer-title {
    display: flex;
    justify-content: space-between;
  }
  .button-tips {
    position: absolute;
    left: 64px;
    top: -30px;
  }
  .button-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    .add-button {
      margin-top: 10px;
      width: 90%;
    }
    i {
      font-size: 16px;
    }
  }
  .target-property-wrapper {
    .label {
      font-size: 12px;
      color: #777;
      white-space: pre;
    }
    .flex-wrapper {
      .container:first-child {
        margin-right: 10px;
      }
      .container {
        width: 50%;
      }
    }
  }
}
</style>

<style lang="less">
.target-form-tips-wrapper {
  .el-form-item__content {
    display: flex;
    // margin-top: 8px;
    .el-icon-question {
      margin-right: 12px;
      margin-top: 8px;
    }
  }
}
.input-number-opacity {
  .label {
    min-width: 0px !important;
  }
}
</style>
