<!--
 * @Date: 2021-09-10 16:44:09
 * @LastEditors: chxu
 * @LastEditTime: 2021-09-22 11:31:43
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/MatchBoardComponentEditor/MatchGraphPanel/GraphItem.vue
 * @Author: chxu
 * 运算分类 移动 增加 删除
-->
<template>
  <div class="match-graph-item">
    <div
      v-for="(rowItem, rowIndex) in patternPart1"
      :key="rowIndex"
      :class="['row-item', `${rowIndex % 2 === 0 ? 'odd-row' : 'even-row'}`, `row-${rowIndex}`]"
    >
      <div
        v-for="(colItem, colIndex) in rowItem"
        :key="colIndex"
        :class="['col-item', `${colIndex % 2 === 0 ? 'odd-col' : 'even-col'}`, `col-${colIndex}`]"
        @click="handleClick(rowIndex, colIndex)"
      >
        <img :src="getSrc(rowIndex, colIndex)" />
      </div>
    </div>
    <!-- // 对角线 m行n列 m行 2n个 -->
    <div class="diagonal-wrapper">
      <div
      v-for="(rowItem, rowIndex) in patternPart2"
      :key="rowIndex"
      :class="['diagonal-item', `diagonal-${rowIndex}`]"
    >
      <div
        v-for="(colItem, colIndex) in col"
        :key="colIndex"
        :class="['col-item', `${colIndex % 2 === 0 ? 'odd-col' : 'even-col'}`, `col-${colIndex}`]"
        :style="{
          left: `${8 + colIndex * 38}px`,
          top: `${8 + rowIndex * 38}px`,
        }"
      >
      <!-- @click="handleClick(row + rowIndex, colIndex * 2)" -->
        <img class="left" :class="{active: !!patternPart2[rowIndex][2*colIndex]}" :src="getDiagonalSrc(rowIndex, 2*colIndex)" @click="handleClick(2 * row + 1 + rowIndex, colIndex * 2)" />
        <img class="right" :class="{active: !!patternPart2[rowIndex][2*colIndex + 1]}" :src="getDiagonalSrc(rowIndex, 2*colIndex + 1)" @click="handleClick(2 * row + 1 + rowIndex, colIndex * 2 + 1)" />
      </div>
    </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import MathModel from "./GraphItem.vue";
import horActiveSvg from "./resource/hor-active.svg";
import horStaticSvg from "./resource/hor-static.svg";
import verActiveSvg from "./resource/ver-active.svg";
import verStaticSvg from "./resource/ver-static.svg";

@Component({
  components: {
    MathModel,
  },
})
export default class MatchGraphPanel extends Vue {
  @Prop({
    required: true,
  })
  pattern!: number[][];

  @Prop({
    required: true,
  })
  col!: number;

  @Prop({
    required: true,
  })
  row!: number;

  get patternPart1() {
    return this.pattern.slice(0, 2 * this.row + 1);
  }

  get patternPart2() {
    return this.pattern.slice(2 * this.row + 1);
  }
  getSrc(rowIndex: number, colIndex: number) {
    const svgs = {
      hor: {
        active: horActiveSvg,
        default: horStaticSvg,
      },
      ver: {
        active: verActiveSvg,
        default: verStaticSvg,
      },
    };
    const dir = rowIndex % 2 === 0 ? "hor" : "ver";
    const status = this.pattern[rowIndex][colIndex] ? "active" : "default";
    return svgs[dir][status];
  }

  getDiagonalSrc(rowIndex: number, colIndex: number) {
    const svgs = {
        active: verActiveSvg,
        default: verStaticSvg,
      };
    const status = this.patternPart2[rowIndex][colIndex] ? "active" : "default";
    return svgs[status];
  }

  handleClick(rowIndex: number, colIndex: number) {
    this.$emit("changeStatus", { rowIndex, colIndex });
  }
}
</script>

<style lang="less" scoped>
.match-graph-item {
  position: relative;
}
.diagonal-wrapper {
  position: absolute;
  top: 0px;
  left: 0;
  background: rgba(0,0,0,0.2);
}
.diagonal-item {
  display: flex;
  margin-left: 8px;
  .col-item {
    width: 30px;
    height: 30px;
    // margin-right: 8px;
    // margin-top: 8px;
    display: flex;
    position: absolute;
    // 第一个col-item
    &:first-child {
      margin-left: 0;
    }
    // justify-content: center;
    // transform: rotate(-45deg);
    // transform-origin: center;
    .left {
      width: 8px;
      height: 36px;
      position: absolute;
      top: -2px;
      left: 50%;
      margin-left: -5px;
      transform: rotate(-45deg);
      transform-origin: center;
      &.active {
        z-index: 1;
      }
    }
    .right {
      width: 8px;
      height: 36px;
      position: absolute;
      top: -2px;
      left: 50%;
      margin-left: -5px;
      transform: rotate(45deg);
      transform-origin: center;
      &.active {
        z-index: 1;
      }
    }
    // &.odd-col {
    //   // width: 38px;
    //   // transform: rotate(45deg);
    // }
  }
}
.row-item {
  display: flex;
  &.odd-row {
    .col-item {
      margin-left: 8px;
      width: 30px;
      height: 8px;
    }
  }
  &.even-row {
    .col-item {
      width: 8px;
      height: 30px;
      margin-right: 30px;
    }
  }
  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
  &.odd-row div:last-child {
    display: none !important;
  }
}
.col-item {
  display: flex;
}
</style>
