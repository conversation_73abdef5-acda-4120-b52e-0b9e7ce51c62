<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      title="题目属性"
      name="stage-properties"
      class="match-board-panel"
    >
      <el-form label-position="top" size="small">
        <el-form-item label="题目类型">
          <el-radio-group v-model="matchType" @click.native="handleRadioClick">
            <el-radio
              v-for="type in matchTypes"
              :key="type.value"
              :label="type.value"
              >{{ type.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <MatchMathPanel
          v-if="matchType === 0"
          :currentComponents="currentComponents"
          :componentId="componentId"
        />
        <MatchGraphPanel
          v-if="matchType === 1"
          :currentComponents="currentComponents"
          :componentId="componentId"
        />
      </el-form>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import MatchMathPanel from "./MatchMathPanel/index.vue";
import MatchGraphPanel from "./MatchGraphPanel/index.vue";
import { cloneDeep } from "lodash-es";

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
    MatchMathPanel,
    MatchGraphPanel,
  },
})
export default class MatchBoardComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  matchTypes: Array<{ label: string; value: number }> = [
    {
      label: "公式题",
      value: 0,
    },
    {
      label: "图形题",
      value: 1,
    },
  ];

  matchTypeFormChange = false;

  @Watch("matchType")
  async onMatchTypeChanged(val: number) {
    if (this.matchTypeFormChange) {
      if (val === 0) {
        this.initMath();
      } else {
        this.initGraph();
      }
      await this.$nextTick();
      this.matchTypeFormChange = false;
    }
  }

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  get matchType(): number {
    return this.currentComponents[0].properties.matchType;
  }

  set matchType(val) {
    if (val === this.matchType) return;
    this.updateProperties("matchType", val);
  }

  handleRadioClick() {
    console.log("handleRadioClick", this.matchType);
    this.matchTypeFormChange = true;
  }

  initMath() {
    console.log("initMath");
    const pattern = [
      [0, 0, 1, 0, 0, 1, 0],
      [0, 1, 0, 1],
      [1, 0, 1, 0, 0, 1, 0],
      [1, 0, 1, 0],
      [1, 1, 1, 1, 1, 1, 1],
    ];
    // matchType
    this.updateProperties("pattern", cloneDeep(pattern));
    this.updateProperties("rightAnswer", []);
  }

  initGraph() {
    console.log("initGraph");
    const pattern = [
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
      [0, 0, 0, 0, 0, 0, 0],
    ];
    // matchType
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        mathType: 1,
        pattern: cloneDeep(pattern),
        rightAnswer: [],
        grid: {
          row: 3,
          col: 6,
        },
        obliqueMatch: false,
        showTargetArea: false,
        selectTargetArea: undefined,
        targetAreaX: undefined,
        targetAreaY: undefined,
        targetAreaAngle: 0,
        targetAreaScale: 1,
        isCanDrag: true,
        operationAreaBgOpacity: undefined,
        operationAreaBgTexture: '',
        targetAreaMatchTexture: '',
        operationAreaMatchTexture: '',
        operationAreaColorfulMatchTexture: '',
        operationAreaMatchStatusTexture: ''
      },
    });
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.match-board-panel {
  text-align: left !important;
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
