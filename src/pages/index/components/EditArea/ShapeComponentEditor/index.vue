<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="形状属性" name="sprite-properties">
      <div class="flex">
        <ya-select label="边框宽度" property="lineWidth" :defaultValue="1" :options="lineWidthOptions" :componentIds="currentComponentIds" :option="{ required: true }" />
      </div>
      <div class="flex">
        <zybColorPicker class="color-select" property="strokeColor" label="边框颜色" :componentIds="currentComponentIds"></zybColorPicker>
      </div>
      <div class="flex">
        <zybColorPicker class="color-select" property="fillColor" label="填充颜色" :componentIds="currentComponentIds"></zybColorPicker>
      </div>
    </el-collapse-item>
    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaSelect from "../EditComponents/Select/index.vue";
import zybColorPicker from "@/components/zyb-color-picker/index.vue";
import FlipEditor from "../BaseProperties/FlipEditor/index.vue";

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
    YaSelect,
    zybColorPicker,
    FlipEditor,
  },
})
export default class ShapeComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  lineWidthOptions = [
    { label: "0", value: 0 },
    { label: "1", value: 2 },
    { label: "2", value: 3 },
    { label: "3", value: 4 },
    { label: "4", value: 5 },
    { label: "5", value: 6 },
  ];

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
