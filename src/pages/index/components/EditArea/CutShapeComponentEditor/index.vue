<!--
 * @Date: 2021-11-01 16:35:45
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-01 19:46:10
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/CutShapeComponentEditor/index.vue
 * @Author: chxu
-->
<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      v-if="currentComponents.length === 1"
      title="切割图形属性"
      name="sprite-properties"
    >
      <div class="flex">
        <!-- <ya-color-picker
          label="填充颜色"
          property="fillColor"
          :componentIds="currentComponentIds"
        /> -->
        <zybColorPicker
          class="color-select"
          property="fillColor"
          label="填充颜色"
          :componentIds="currentComponentIds"
        ></zybColorPicker>
        <zybColorPicker
          class="color-select"
          property="strokeColor"
          label="线段颜色"
          :componentIds="currentComponentIds"
        ></zybColorPicker>
        <!-- <ya-color-picker
          label="线段颜色"
          property="strokeColor"
          :componentIds="currentComponentIds"
        /> -->
      </div>
      <div class="flex">
        <ya-input-number
          label="线段宽度"
          style="width:100px"
          property="lineWidth"
          type="number"
          :componentIds="currentComponentIds"
        />
        <ya-switch
          label="隐藏点线"
          property="isHidePointLine"
          :componentIds="currentComponentIds"
        />
      </div>

      <cut-shape-points-editor :componentId="componentId" />
    </el-collapse-item>
    <extra-editor
      v-if="currentComponents.length === 1"
      :component="currentComponents[0]"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";
import YaColorPicker from "../EditComponents/ColorPicker/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import CutShapePointsEditor from "./CutShapePointsEditor.vue";

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
    YaInputNumber,
    YaColorPicker,
    CutShapePointsEditor,
    YaSwitch,
  },
})
export default class CutShapeComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  justify-content: space-between;
}
</style>
