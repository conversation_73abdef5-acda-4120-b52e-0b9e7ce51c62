<template>
  <div class="cut-shape-points-editor">
    <div class="label-container">
      <label>坐标点</label>
      <el-button type="primary" size="mini" @click="handleAddPoint"
        >添加</el-button
      >
    </div>
    <div class="point-list">
      <div class="point-list--item" v-for="point in _points" :key="point.id">
        <label style="color:red;font-weight:bold">{{ point.label }}：</label>
        <label style="margin-right:5px">x</label>
        <el-input-number
          style="width:100px"
          :value="getEditPointWorldPos(point.id).x"
          size="mini"
          controls-position="right"
          @change="handleUpdatePointPosition($event, 'x', point.id)"
        />
        <div class="split"></div>
        <label style="margin-right:5px">y</label>
        <el-input-number
          style="width:100px"
          :value="getEditPointWorldPos(point.id).y"
          size="mini"
          controls-position="right"
          @change="handleUpdatePointPosition($event, 'y', point.id)"
        />
        <i class="el-icon-delete" @click="handleDeletePoint(point.id)" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { v4 as uuidv4 } from "uuid";
import { uniq, cloneDeep } from "lodash-es";

@Component
export default class CutShapePointsEditor extends Vue {
  @Prop({
    required: true,
  })
  componentId!: BaseComponent["id"];

  handleAddPoint() {
    const point: CutShapePropertiesPoint = {
      id: uuidv4(),
      x: 100,
      y: 100,
      label: this.getNextPointLabel(),
      editable: true,
    };

    this.$store.commit("addCutShapePoint", {
      componentId: this.componentId,
      point,
    });
  }

  getEditPointWorldPos(pointId: CutShapePropertiesPoint["id"]) {
    const component: Component = this.$store.state.componentMap[
      this.componentId
    ];
    // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
    // @ts-ignore
    return window.cocos.getEditPointWorldPos(
      this.componentId,
      pointId,
      component.properties.x,
      component.properties.y,
      component.properties.angle,
    );
  }

  // 点 label 生成规则
  getNextPointLabel(): string {
    const { pointsData } = this.component.properties;

    // 获取所有点后面的数字
    const pointNums = uniq<number>(
      pointsData
        .map(point => parseInt(point.label.split("点")[1]))
        .filter(i => !Number.isNaN(i)),
    );
    // 升序排列
    pointNums.sort();

    let i = 1;
    // eslint-disable-next-line no-constant-condition
    while (true) {
      if (!pointNums.find(num => num === i)) return `点${i}`;
      i++;
    }
  }

  handleUpdatePointPosition(
    val: number,
    prop: "x" | "y",
    pointId: CutShapePropertiesPoint["id"],
  ) {
    this.$store.commit("updateCutShapePointPosition", {
      componentId: this.componentId,
      pointId,
      position: {
        [prop]: val,
      },
    });
  }

  handleDeletePoint(pointId: CutShapePropertiesPoint["id"]) {
    this.$store.commit("removeCutShapePoint", {
      componentId: this.componentId,
      pointId,
    });
  }

  get component(): CutShapeComponent {
    return this.$store.state.componentMap[this.componentId];
  }

  get points(): CutShapePropertiesPoint[] {
    return this.component.properties.pointsData.filter(point => point.editable);
  }

  get _points(): CutShapePropertiesPoint[] {
    const pointsList = cloneDeep(this.points);

    return pointsList.sort(this.sortRule);
  }

  sortRule(a: CutShapePropertiesPoint, b: CutShapePropertiesPoint) {
    return +a.label.split("点")[1] - +b.label.split("点")[1];
  }
}
</script>

<style lang="less" scoped>
.cut-shape-points-editor {
  text-align: left;
  padding-bottom: 10px;

  .label-container {
    label {
      min-width: 40px;
      margin-right: 10px;
    }
  }

  .point-list {
    &--item {
      display: flex;
      margin: 5px 0;
      align-items: center;

      .split {
        margin: 0 10px;
      }

      i {
        margin-left: 5px;
        cursor: pointer;
        font-size: 16px;
      }
    }
  }
}
</style>
