<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item
      v-if="currentComponents.length === 1"
      title="计数器组件属性"
      name="sprite-properties"
    >
      <div class="flex">
        <ya-input-number
          label="计数器数字"
          property="countNum"
          :componentIds="currentComponentIds"
          :options="{ required: true, maxLength: 10, type: 'positive integer' }"
        />
      </div>
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import YaInputNumber from "../EditComponents/InputNumber/index.vue";

@Component({
  components: {
    BaseProperties,
    YaInputNumber,
  },
})
export default class CounterComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
</style>
