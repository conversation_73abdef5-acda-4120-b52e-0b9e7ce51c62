<template>
  <div class="container">
    <el-button-group class="btn-group">
      <el-button
        icon="el-iconalign-left element-icon"
        size="small"
        @click="updateHorizontalAlign(HorizontalAlign.LEFT)"
      ></el-button>
      <el-button
        icon="el-iconalign-center element-icon"
        size="small"
        @click="updateHorizontalAlign(HorizontalAlign.CENTER)"
      ></el-button>
      <el-button
        icon="el-iconalign-right element-icon"
        size="small"
        @click="updateHorizontalAlign(HorizontalAlign.RIGHT)"
      ></el-button>
    </el-button-group>
    <el-button-group class="btn-group" v-if="showVertical">
      <el-button
        icon="el-iconalign-top element-icon"
        size="small"
        @click="updateVerticalAlign(VerticalAlign.TOP)"
      ></el-button>
      <el-button
        icon="el-iconalign-vertically element-icon"
        size="small"
        @click="updateVerticalAlign(VerticalAlign.CENTER)"
      ></el-button>
      <el-button
        icon="el-iconalign-bottom element-icon"
        size="small"
        @click="updateVerticalAlign(VerticalAlign.BOTTOM)"
      ></el-button>
    </el-button-group>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class LevelEditor extends Vue {
  @Prop({
    required: true,
  })
  componentIds!: string[];

  @Prop({
    default: false,
  })
  showVertical!: boolean;

  VerticalAlign = window.cc.Label.VerticalAlign;
  HorizontalAlign = window.cc.Label.HorizontalAlign;

  updateHorizontalAlign(type: cc.Label.HorizontalAlign) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        horizontalAlign: type,
      },
    });
  }

  updateVerticalAlign(type: cc.Label.VerticalAlign) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: this.componentIds,
      newProperties: {
        verticalAlign: type,
      },
    });
  }
}
</script>

<style scoped lang="less">
.container {
  margin-bottom: 10px;

  .btn-group {
    display: flex;

    &:not(:last-of-type) {
      margin-bottom: 10px;
    }

    .el-button {
      flex-grow: 1;
    }
  }
}
</style>
