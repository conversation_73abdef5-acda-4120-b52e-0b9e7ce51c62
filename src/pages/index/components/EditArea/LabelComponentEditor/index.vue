<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item title="文本属性" name="label-properties">
      <align-editor :componentIds="currentComponentIds" />
      <el-row>
        <el-col :span="12">
          <ya-switch
            label="粗体"
            property="enableBold"
            :componentIds="currentComponentIds"
          />
        </el-col>
        <el-col :span="12">
          <ya-switch
            label="斜体"
            property="enableItalic"
            :componentIds="currentComponentIds"
          />
        </el-col>
        <el-col :span="12">
          <ya-switch
            label="下划线"
            property="enableUnderline"
            :componentIds="currentComponentIds"
          />
        </el-col>

        <el-col :span="12">
          <ya-switch
            label="固定宽高"
            property="isFixed"
            :componentIds="currentComponentIds"
          />
        </el-col>
      </el-row>
      <!-- <ya-input
        label="文本"
        property="string"
        type="textarea"
        :componentIds="currentComponentIds"
      /> -->
      <el-row>
        <el-col :span="24">
          <ya-slider-and-input
            label="字号"
            :defaultValue="32"
            property="fontSize"
            :options="{ min: 12, max: 64 }"
            :componentIds="currentComponentIds"
          />
        </el-col>
      </el-row>
      <el-row>
        <!-- <el-col :span="24">
          <ya-slider-and-input
            label="行高"
            property="lineHeight"
            :options="{ min: 12, max: 100 }"
            :componentIds="currentComponentIds"
          />
        </el-col> -->
        <!-- 
        <el-col :span="24">
          <ya-slider-and-input
            label="行距"
            property="rowSpacing"
            :options="{ min: 1.0, step: 0.01, max: 3.0 }"
            :componentIds="currentComponentIds"
          />
        </el-col> -->
      </el-row>
      <div class="flex">
        <ya-select
          label="行距"
          property="rowSpacing"
          :defaultValue="1"
          :options="rowSpacingOptions"
          :componentIds="currentComponentIds"
        />
      </div>
      <!-- <ya-select
        label="排版"
        property="overflow"
        :defaultValue="0"
        :options="overflowOptions"
        :componentIds="currentComponentIds"
      /> -->
      <!-- <ya-select
        label="字体"
        property="fontFamily"
        :options="[{ label: '1', value: 1 }]"
        :componentIds="currentComponentIds"
      /> -->
    </el-collapse-item>
    <extra-editor
      v-if="currentComponents.length === 1"
      :component="currentComponents[0]"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaInput from "../EditComponents/Input/index.vue";
import YaSelect from "../EditComponents/Select/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import YaSliderAndInput from "../EditComponents/SliderAndInput/index.vue";
import AlignEditor from "./AlignEditor/index.vue";

@Component({
  components: {
    ExtraEditor,
    YaInput,
    YaSelect,
    YaSwitch,
    YaSliderAndInput,
    BaseProperties,
    AlignEditor,
  },
})
export default class LabelComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  rowSpacingOptions = [
    { label: "1", value: 1 },
    { label: "1.2", value: 1.2 },
    { label: "1.5", value: 1.5 },
    { label: "2", value: 2 },
  ];

  overflowOptions = [
    { label: "自适应宽度", value: window.cc.Label.Overflow.NONE },
    { label: "截断", value: window.cc.Label.Overflow.CLAMP },
    { label: "缩放", value: window.cc.Label.Overflow.SHRINK },
    { label: "自适应高度", value: window.cc.Label.Overflow.RESIZE_HEIGHT },
  ];

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.flex {
  margin-bottom: 10px;
}
.flex /deep/ .container {
  justify-content: space-between;
  display: flex;
  align-items: center;
}
.container /deep/ .el-select {
  margin-right: 0px;
  flex-grow: 0;
  width: 150px;
}
</style>
