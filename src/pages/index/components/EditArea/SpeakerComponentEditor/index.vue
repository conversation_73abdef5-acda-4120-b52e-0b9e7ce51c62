<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <el-collapse-item v-if="currentComponents.length === 1" title="音频组件属性" name="sprite-properties">
      <div class="icon-div">
        <img :src="typeImg" class="icon" />
        <el-popover placement="top" v-model="customSpeedPopoverVisible" :visible-arrow="false">
          <skin-select @selectSkin="handleSelectedShapes" @showSkin="showSkin" :skinSelectList.sync="skinList" :disSelsectAll="false" />
          <el-button class="btSkin" size="mini" slot="reference"> 更多样式 </el-button>
        </el-popover>
      </div>

      <div class="autoPlay">
        <el-col :span="4" style="text-align: center">
          <div class="yinpin">
            <div class="xinghao">*</div>
            <div>音频</div>
          </div>
        </el-col>
        <el-col :span="16" :offset="1" v-if="isShowAudioSelect">
          <audio-select :value.sync="audioUrl" />
        </el-col>
        <el-col :span="16" :offset="1" v-else>
          <audio-player :value.sync="audioUrl" />
        </el-col>
      </div>

      <div class="repeat">
        <ya-select label="总播放次数:" property="count" :options="countOptions" :defaultValue="-1" :componentIds="currentComponentIds" :option="{ required: true }" />
        <el-tooltip placement="top">
          <div slot="content">播放总次数</div>
          <i class="el-icon-question" />
        </el-tooltip>
      </div>

      <div class="autoPlay">
        <span class="label"> 自动开始 </span>
        <el-switch v-model="handleAutoPlay" />
      </div>

      <div v-show="handleAutoPlay" class="autoPlay">
        <ya-select label="自动播放次数" property="autoRepeatCount" :defaultValue="1" :options="autoRepeatCountOptions" :componentIds="currentComponentIds" />
      </div>

      <div v-show="handleAutoPlay" class="autoPlay">
        <span class="label1">倒计时</span>
        <el-radio-group class="form-content" v-model="countdownPlay">
          <el-radio :label="true">显示</el-radio>
          <el-radio :label="false">不显示</el-radio>
        </el-radio-group>
        <!-- <span class="label"> 显示倒计时开始</span>
        <el-switch v-model="countdownPlay" /> -->
      </div>

      <!-- <div v-show="countdownPlay" class="autoPlay">
        <ya-select label="倒计时样式" property="countdownSkin" :defaultValue="0" :options="countdownSkinOptions" :componentIds="currentComponentIds" />
      </div> -->

      <div v-show="countdownPlay" class="icon-div">
        <img :src="countdownImg" class="icon" />
        <el-popover placement="top" v-model="customCountdownSkinVisible" :visible-arrow="false">
          <skin-select @selectSkin="handleCountdownSelectedShapes" @showSkin="showCountdownSkin" :skinSelectList.sync="countdownSkinList" :disSelsectAll="true" />
          <el-button class="btSkin" size="mini" slot="reference"> 更多样式 </el-button>
        </el-popover>
      </div>

      <div class="autoPlay">
        <span class="label1">播放中允许中断</span>
        <el-radio-group class="form-content" v-model="notStopPlaying">
          <el-radio :label="false">允许</el-radio>
          <el-radio :label="true">不允许</el-radio>
        </el-radio-group>
        <!-- <span class="label"> 显示倒计时开始</span>
        <el-switch v-model="countdownPlay" /> -->
      </div>
      <!-- <ya-switch label="不允许播放时中断" property="notStopPlaying" :default-value="false" /> -->
    </el-collapse-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import YaSelect from "../EditComponents/Select/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import AudioSelect from "@/components/AudioSelect/index.vue";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import { SkinSelectLibraryItem } from "../EditComponents/SkinSelect/index.vue";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import SkinSelect from "../EditComponents/SkinSelect/index.vue";
import { Howl } from "howler";

@Component({
  components: {
    YaSelect,
    YaSwitch,
    SkinSelect,
    AudioSelect,
    AudioPlayer,
    BaseProperties,
  },
})
export default class SpeakerComponentEditor extends Vue {
  @Prop({
    required: true,
    default: "",
  })
  currentComponents!: Components;

  SkinSelectLibraryVisible = false;
  customSpeedPopoverVisible = false;
  customCountdownSkinVisible = false;
  // 是否是自动开始
  autoPlay = false;
  countdownSkinList: SkinSelectLibraryItem[] = [
    {
      type: 0,
      label: "小学",
      icon: require("./countdown_0.png"),
    },
    {
      type: 1,
      label: "初高",
      icon: require("./countdown_1.png"),
    },
  ];

  skinList: SkinSelectLibraryItem[] = [
    {
      type: 1,
      label: "黄色",
      icon: require("./speaker_1.png"),
    },
    {
      type: 2,
      label: "紫色",
      icon: require("./speaker_2.png"),
    },
    {
      type: 3,
      label: "绿色",
      icon: require("./speaker_3.png"),
    },
    {
      type: 4,
      label: "绿色",
      icon: require("./speaker_4.png"),
    },
    // {
    //   type: 5,
    //   label: "绿色",
    //   icon: require("./speaker_5.png"),
    // },
  ];

  countOptions = [
    { label: "无限制", value: -1 },
    // { label: "0次", value: 0 },
    { label: "1次", value: 1 },
    { label: "2次", value: 2 },
    { label: "3次", value: 3 },
    { label: "4次", value: 4 },
    { label: "5次", value: 5 },
  ];

  get countdownSkinOptions() {
    const skin = [
      { label: "小学", value: 0 },
      { label: "初高", value: 1 },
    ];
    return skin;
  }

  get autoRepeatCountOptions() {
    let autoRepeatCountOptio = [
      { label: "1次", value: 1 },
      { label: "2次", value: 2 },
      { label: "3次", value: 3 },
      { label: "4次", value: 4 },
      { label: "5次", value: 5 },
    ];
    if (this.$store.state.componentMap[this.componentId].properties.count == -1) {
      autoRepeatCountOptio;
    } else {
      autoRepeatCountOptio = [];
      const count = this.$store.state.componentMap[this.componentId].properties.count;
      for (let i = 1; i <= count; i++) {
        autoRepeatCountOptio.push({ label: i + "次", value: i });
      }
    }
    return autoRepeatCountOptio;
  }
  get countdownImg() {
    const speakerType = this.$store.state.componentMap[this.componentId].properties.countdownSkin || 0;
    const value = this.countdownSkinList.find((value: SkinSelectLibraryItem) => {
      if (value.type == speakerType) {
        return value;
      }
    });
    return value?.icon;
  }
  get typeImg() {
    const speakerType = this.$store.state.componentMap[this.componentId].properties.speakerType;
    const value = this.skinList.find((value: SkinSelectLibraryItem) => {
      if (value.type == speakerType) {
        return value;
      }
    });
    return value?.icon;
  }
  showSkin(show: boolean) {
    this.customSpeedPopoverVisible = show;
  }
  showCountdownSkin(show: boolean) {
    this.customCountdownSkinVisible = show;
  }

  set notStopPlaying(v) {
    this.$store.dispatch("updateComponentsProperties", {
      ids: [this.componentId],
      newProperties: {
        notStopPlaying: v,
      },
    });
  }
  get notStopPlaying() {
    return this.$store.state.componentMap[this.componentId].properties.notStopPlaying || false;
  }

  set countdownPlay(v) {
    let bIsCountdown = v;

    let bHasCountdown = false;
    this.$store.state.componentIds.forEach((Id: any) => {
      const element = this.$store.state.componentMap[Id];
      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.SPEAKER) {
          if (element.properties.countdown) {
            if (bIsCountdown) {
              bHasCountdown = true;
            }
          }
        }
      }
    });
    if (bHasCountdown) {
      this.$message.error("一道题目中仅能存在一个倒计时属性");
      bIsCountdown = true;
      return;
    }
    this.$store.dispatch("updateComponentsProperties", {
      ids: [this.componentId],
      newProperties: {
        countdown: bIsCountdown,
      },
    });
  }
  get countdownPlay() {
    return this.$store.state.componentMap[this.componentId].properties.countdown || false;
  }
  set handleAutoPlay(v) {
    let bIsAutoPlay = v;

    let bHasAutoPlay = false;
    this.$store.state.componentIds.forEach((Id: any) => {
      const element = this.$store.state.componentMap[Id];
      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.SPEAKER) {
          if (element.properties.autoPlay) {
            if (bIsAutoPlay) {
              bHasAutoPlay = true;
            }
          }
        }
      }
    });
    if (bHasAutoPlay) {
      this.$message.error("一道题目中仅能存在一个自动播放音频");
      bIsAutoPlay = true;
      return;
    }
    if (bIsAutoPlay == false) {
      this.$store.dispatch("updateComponentsProperties", {
        ids: [this.componentId],
        newProperties: {
          autoPlay: bIsAutoPlay,
          countdown: bIsAutoPlay,
        },
      });
    } else {
      this.$store.dispatch("updateComponentsProperties", {
        ids: [this.componentId],
        newProperties: {
          autoPlay: bIsAutoPlay,
        },
      });
    }

    // return !bIsAutoPlay;
    this.autoPlay = bIsAutoPlay;
  }

  get handleAutoPlay() {
    return this.$store.state.componentMap[this.componentId].properties.autoPlay;
  }

  handleCountdownSelectedShapes(data: { type: number; changeAll: boolean }) {
    console.log("#-----shapeSubTypes--->", data.type);
    if (data.changeAll) {
      this.$store.state.componentIds.forEach((Id: any) => {
        const element = this.$store.state.componentMap[Id];
        if (element) {
          if (element.subType && element.subType == SpecialComponentSubTypes.SPEAKER) {
            this.$store.commit("updateComponentProperties", {
              id: Id,
              newProperties: {
                countdownSkin: data.type,
              },
            });
          }
        }
      });
    } else {
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          countdownSkin: data.type,
        },
      });
    }
  }

  handleSelectedShapes(data: { type: number; changeAll: boolean }) {
    console.log("#-----shapeSubTypes--->", data.type);
    if (data.changeAll) {
      this.$store.state.componentIds.forEach((Id: any) => {
        const element = this.$store.state.componentMap[Id];
        if (element) {
          if (element.subType && element.subType == SpecialComponentSubTypes.SPEAKER) {
            this.$store.commit("updateComponentProperties", {
              id: Id,
              newProperties: {
                speakerType: data.type,
              },
            });
          }
        }
      });
    } else {
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          speakerType: data.type,
        },
      });
    }
  }

  handleSelect() {
    this.SkinSelectLibraryVisible = true;
  }

  isShowAudioSelect(): boolean {
    if (this.audioUrl == "") {
      return false;
    }
    return true;
  }

  setDuration(audioUrl: string) {
    if (!audioUrl) {
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          duration: 0,
        },
      });
      return;
    }
    const sound = new Howl({
      src: [audioUrl],
      onplay: () => {
        console.log("onplay");
      },
      onpause: () => {
        console.log("onpause");
      },
      onend: () => {
        console.log("onend");
      },
      onload: () => {
        this.$store.commit("updateComponentProperties", {
          id: this.componentId,
          newProperties: {
            duration: sound.duration(),
          },
        });
      },
    });
  }
  get audioUrl(): string {
    return this.$store.state.componentMap[this.componentId].properties.audioUrl;
  }
  set audioUrl(val: string) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        audioUrl: val,
        // duration:
      },
    });
    this.setDuration(val);
  }

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>

<style scoped lang="less">
.repeat {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
}
.keyboard-editor {
  padding: 10px;
}
.icon {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
  background-color: #f3f3f7;
}

.icon-div {
  display: flex;
  align-items: center;
}
.xinghao {
  color: rgba(255, 0, 0, 255);
}
.yinpin {
  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 5px;
}

.autoPlay {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }
}

.label1 {
  text-align: left;
  min-width: 30px;
  margin-right: 5px;
}

.label {
  display: inline-block;
  text-align: left;
  min-width: 30px;
  margin-right: 5px;
}

.el-switch {
  margin-right: 0;
}
</style>
