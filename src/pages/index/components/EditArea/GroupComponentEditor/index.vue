<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <extra-editor
      v-if="currentComponents.length === 1"
      :component="currentComponents[0]"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaInput from "../EditComponents/Input/index.vue";

@Component({
  components: {
    ExtraEditor,
    YaInput,
    BaseProperties,
  },
})
export default class GroupComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }
}
</script>


<style scoped lang="less"></style>
