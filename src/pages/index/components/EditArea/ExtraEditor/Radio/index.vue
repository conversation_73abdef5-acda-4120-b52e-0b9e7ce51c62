<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-radio-group v-model="v" size="small">
      <el-radio
        v-for="(item, index) in params.options"
        :key="index"
        :label="item.value"
      >
        {{ item.label }}</el-radio
      >
    </el-radio-group>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraRadio extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: false,
  })
  defaultValue!: string;

  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  get v() {
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[
      this.property
    ];
    const hasDifferentVal = this.currentIds.some(
      (id: string | number) =>
        this.$store.state.componentMap[id].extra[this.property] !== firstVal,
    );

    if (hasDifferentVal) {
      return undefined;
    }

    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    if (val === this.v) return;

    this.currentIds.forEach((id: string) => {
      this.$store.commit("updateComponentExtra", {
        id,
        newExtra: {
          [this.property]: val,
        },
      });
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;

  .el-radio-group {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;

    /deep/ .el-radio__label {
      padding-left: 2px;
      font-size: 13px;
    }
  }
}
</style>
