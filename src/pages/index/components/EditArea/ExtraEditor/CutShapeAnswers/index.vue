<template>
  <div class="cut-shape-answers">
    <div class="label-container">
      <label>{{ label }}</label>
      <el-button type="primary" size="mini" @click="handleAddAnswer"
        >添加</el-button
      >
    </div>
    <div class="answer-list">
      <div class="answer-list--item" v-for="(answer, index) in v" :key="index">
        <el-select
          :value="answer[0]"
          size="mini"
          @change="handleAnswerChange($event, 0, index)"
        >
          <el-option
            v-for="option in options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          ></el-option>
        </el-select>
        <div class="split">&</div>
        <el-select
          :value="answer[1]"
          size="mini"
          @change="handleAnswerChange($event, 1, index)"
        >
          <el-option
            v-for="option in options"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          ></el-option>
        </el-select>
        <i class="el-icon-delete" @click="handleDeleteAnswer(index)" />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";

type CutShapeAnswer = [string, string];
type CutShapeAnswers = CutShapeAnswer[];
type Option = { label: string; value: string };
type Options = Option[];

/**
 * @description 切割元素答案配置组件
 */
@Component
export default class ExtraCutShapeAnswers extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: true,
  })
  componentId!: number;

  handleAddAnswer() {
    const clonedValue = cloneDeep(this.v);
    clonedValue.push(["", ""]);
    this.v = clonedValue;
  }

  handleAnswerChange(val: string, prop: number, index: number) {
    const clonedValue = cloneDeep(this.v);
    clonedValue[index][prop] = val;
    this.v = clonedValue;
  }

  handleDeleteAnswer(index: number) {
    const clonedValue = cloneDeep(this.v);
    clonedValue.splice(index, 1);

    this.v = clonedValue;
  }

  get component(): Component {
    return this.$store.state.componentMap[this.componentId];
  }

  get options(): Options {
    if (this.component.type === "cutShape") {
      const {
        properties: { pointsData, linesData },
      } = this.component;

      const options: Options = [];

      pointsData.forEach((point: { id: any; label: any }) => {
        const { id, label } = point;

        options.push({
          value: id,
          label,
        });
      });
      linesData.forEach((line: { id: any; label: any }) => {
        const { id, label } = line;

        options.push({
          value: id,
          label,
        });
      });

      return options;
    } else {
      return [];
    }
  }

  @Watch("options", {
    immediate: true,
  })
  optionsWacher(options: Options) {
    const clonedValue = cloneDeep(this.v).filter(answer => {
      const [answer1, answer2] = answer;

      // 如果两个选项都没选择，直接返回
      if (!answer1 && !answer2) return true;

      // 任意一个答案不在现存选项中，删除该答案
      if (answer1 && !options.find(option => option.value === answer1))
        return false;
      if (answer2 && !options.find(option => option.value === answer2))
        return false;

      // 皆不满足说明该答案不需要删除
      return true;
    });

    this.v = clonedValue;
  }

  get v(): CutShapeAnswers {
    const firstVal = this.component.extra[this.property];

    return firstVal || [];
  }
  set v(val) {
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.cut-shape-answers {
  text-align: left;

  .label-container {
    label {
      min-width: 40px;
      margin-right: 10px;
    }
  }

  .answer-list {
    &--item {
      display: flex;
      margin: 5px 0;
      align-items: center;

      .split {
        margin: 0 10px;
      }

      i {
        margin-left: 5px;
        cursor: pointer;
        font-size: 16px;
      }
    }
  }
}
</style>
