<template >
  <div v-show="!selectEffect" class="container">
    <template>
      <ya-label :label="label" :description="config.description" />
      <div class="config-area">
        <div class="icon-div">
          <img :src="effectTypeImg" class="icon" />
          <el-popover placement="top" v-model="customSpeedPopoverVisible" :visible-arrow="false">
            <skin-select @selectSkin="handleSelectedShapes" @showSkin="showSkin" :curIndex="v" :title="title"
              :skinSelectList.sync="defaultEffetList" :disSelsectAll="false" />
            <el-button class="btSkin" size="mini" slot="reference"> 更多样式 </el-button>
          </el-popover>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import SkinSelect from "../../EditComponents/SkinSelect/index.vue";
@Component({
  components: {
    YaLabel: Label,
    SkinSelect,
  },
})
export default class DefaultEffect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: true,
  })
  config!: any;

  // 是否从编辑器插入
  @Prop({
    required: false,
  })
  isEditorAdd!: boolean;
  // effect展示的选择
  @Prop({
    default: "",
  })
  effectShowType!: string;

  customSpeedPopoverVisible = false;
  title = "选中样式更改";
  defaultEffetList: { type: number, label: string, icon: string }[] = [
    {
      type: 0,
      label: "黄色",
      icon: require("./answer_border_new.png"),
    },
    {
      type: 1,
      label: "蓝色",
      icon: require("./answer_border_new1.png"),
    },
    {
      type: 2,
      label: "绿色",
      icon: require("./answer_border_new2.png"),
    },
    {
      type: 3,
      label: "小鹿",
      icon: require("./answer_border_new3.png"),
    },
    {
      type: 4,
      label: "绿色云朵",
      icon: require("./answer_border_new4.png"),
    },
    {
      type: 5,
      label: "耳朵",
      icon: require("./answer_border_new5.png"),
    },
    {
      type: 6,
      label: "令",
      icon: require("./answer_border_new6.png"),
    },
    {
      type: 7,
      label: "粉色脚印",
      icon: require("./answer_border_new7.png"),
    },
    {
      type: 8,
      label: "白色脚印",
      icon: require("./answer_border_new8.png"),
    },
    {
      type: 9,
      label: "黄色脚印",
      icon: require("./answer_border_new9.png"),
    },
    {
      type: 10,
      label: "绿色脚印",
      icon: require("./answer_border_new10.png"),
    },
  ];

  get component(): Component {
    return this.$store.state.componentMap[this.componentId];
  }

  get effectTypeImg() {
    const typeIndex = this.v;
    const value = this.defaultEffetList.find((value: { type: number }) => {
      if (value.type == typeIndex) {
        return value;
      }
    });
    return value?.icon;
  }
  get selectEffect() {
    const selectEff = this.$store.state.componentMap[this.componentId].extra['selectEffect'];
    if (selectEff) {
      return true;
    } else {
      return false;
    }
  }

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[this.property];
    if (firstVal === undefined) {
      return this.config.defaultValue;
    }
    return firstVal;
  }

  showSkin(show: boolean) {
    this.customSpeedPopoverVisible = show;
  }

  handleSelectedShapes(data: { type: number; changeAll: boolean }) {
    console.log("#-----shapeSubTypes--->", data.type);
    const tagName = this.$store.state.componentMap[this.componentId].tag;
    if (data.changeAll) {
      this.$store.state.componentIds.forEach((Id: any) => {
        let currId = Id;
        if (typeof currId !== "string") {
          currId = currId.id;
        }
        const element = this.$store.state.componentMap[currId];
        if (element && element.tag == tagName) {
          this.$store.commit("updateComponentExtra", {
            id: currId,
            newExtra: {
              [this.property]: data.type,
              defaultEffectPosX: 0,
              defaultEffectPosY: 0,
            },
          });
        }
      });
    } else {
      this.$store.commit("updateComponentExtra", {
        id: this.componentId,
        newExtra: {
          [this.property]: data.type,
          defaultEffectPosX: 0,
          defaultEffectPosY: 0,
        },
      });
    }
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  padding-bottom: 10px;

  .config-area {
    margin-left: 5px;

    /deep/ .image-select .image-container .image {
      width: 100px;
    }
  }

  .spine-editor {
    width: 100%;
    text-align: left;

    .spine-config {
      margin: 10px 0 0 15px;

      .label {
        display: inline-block;
        min-width: 40px;
        margin-right: 10px;
      }
    }

    &-loop {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-time-scale {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-animation-list {
      margin-top: 10px;
      text-align: left;
      line-height: 28px;

      &-list {
        list-style: none;
        padding: 0;
        margin: 0;

        &-item {
          margin-bottom: 5px;
        }
      }
    }
  }

  .icon {
    width: 80px;
    height: 80px;
    margin-right: 5px;
    margin-bottom: 5px;
    background-color: #f3f3f7;
  }

  .icon-div {
    display: flex;
    align-items: center;
  }
}
</style>
