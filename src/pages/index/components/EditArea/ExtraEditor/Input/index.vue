<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-input v-model="v" size="small" v-bind="params" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraInput extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: true,
  })
  config!: any;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[
      this.property
    ];
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }
}
</script>


<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
</style>
