<template>
  <audio-select
    :value.sync="v"
    :label="label"
    :property="property"
    :defaultValue="defaultValue"
    :params="params"
  />
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";

@Component({
  components: { AudioSelect },
})
export default class ComponentExtraAudioSelect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[
      this.property
    ];
    return firstVal ?? this.defaultValue;
  }

  set v(val) {
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }
}
</script>
