<!--
 * @Author: your name
 * @Date: 2021-04-20 14:50:13
 * @LastEditTime: 2021-04-23 12:06:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/ExtraEditor/Text/index.vue
-->
<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <text-color
      v-if="property === 'labelColor'"
      :currentIds="currentIds"
      @changeVal="val => change(val)"
    />
    <text-font
      v-if="property === 'fontSize'"
      :currentIds="currentIds"
      :options="fontOptions"
      @changeVal="val => change(val)"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import TextColor from "./TextColor/index.vue";
import TextFont from "./TextFont/index.vue";

@Component({
  components: {
    YaLabel: Label,
    TextColor: TextColor,
    TextFont: TextFont,
  },
})
export default class ExtraText extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  beforeMount() {    
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[
      this.property
    ];

    if (firstVal === undefined) {
      this.change(this.config.default);
    }
  }
  get fontOptions() {
    const interval = [];
    for (let i = 12; i <= 64; i += 1) {
      interval.push(i);
    }
    return interval;
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  change(val: string | number) {
    if (this[this.property] === val) return;
    this[this.property] = val;
    this.currentIds.forEach((id: string) => {
      this.$store.commit("updateComponentExtra", {
        id,
        newExtra: {
          [this.property]: val,
        },
      });
    });
    console.log("store", this.$store.state.componentMap[this.currentIds[0]]);
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;
}
</style>
