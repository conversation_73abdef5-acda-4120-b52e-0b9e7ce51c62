<template>
  <div class="color-container">
    <el-color-picker
      class="color-picker"
      v-model="realColor"
      size="mini"
    ></el-color-picker>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class TextColor extends Vue {
  @Prop({ default: "#000" }) defaultColor!: string;
  @Prop({ default: () => ({}) }) currentIds!: any;

  get realColor() {
    return this.color ? this.color : this.defaultColor;
  }
  set realColor(val) {
    val = val ? val : "#000";
    this.$emit("changeVal", val);
  }
  get color() {
    const labelColor = this.$store.state.componentMap[this.currentIds[0]].extra
      .labelColor;
    return labelColor;
  }
}
</script>

<style scoped lang="less">
.color-container {
  margin-left: 5px;
  .label,
  .color-picker {
    vertical-align: middle;
  }
}
</style>
