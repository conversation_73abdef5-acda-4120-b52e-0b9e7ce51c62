<!--
 * @Author: your name
 * @Date: 2021-04-20 14:56:59
 * @LastEditTime: 2021-04-23 12:12:56
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/ExtraEditor/Text/TextFont/index.vue
-->
<template>
  <div class="font-container">
    <el-select
      class="font-input"
      :value="displayValue"
      @change="onChange"
      v-bind="options"
      size="small"
    >
      <el-option
        v-for="item in options"
        :key="item"
        :label="item"
        :value="item"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import YaInput from "../../../EditComponents/Input/index.vue";

@Component({
  components: {
    YaLabel: Label,
    YaInput,
  },
})
export default class TextFont extends Vue {
  @Prop({ default: () => ({}) }) options!: any;

  @Prop({ default: () => ({}) }) currentIds!: any;
  @Prop({ required: false }) defaultValue!: number;

  userInput = null;
  get displayValue() {
    if (this.userInput !== null) {
      return this.userInput;
    }
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra
      .fontSize;

    if (firstVal === undefined) {
      return this.defaultValue;
    }

    return firstVal;
  }
  onChange(val: number) {
    this.$emit("changeVal", Number(val));
    this.userInput = null;
  }
}
</script>

<style scoped lang="less">
.font-container {
  margin-left: 5px;
  .font-input {
    width: 70px;
  }
}
</style>
