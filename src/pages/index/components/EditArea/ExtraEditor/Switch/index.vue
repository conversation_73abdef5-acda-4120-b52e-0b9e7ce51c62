<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-switch v-model="v" size="small" :disabled="selectDisabled" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class YaSwitch extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: false,
  })
  defaultValue!: string;

  @Prop({
    required: true,
  })
  config!: any;
  beforeMount() {
    if (this.config.defaultValue !== undefined) {
      const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
      if (firstVal === undefined) {
        this.v = this.config.defaultValue;
      }
    }
  }
  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  get selectDisabled() {
    if (this.currentIds.length > 1) {
      return true;
    }
    return false;
  }
  get v() {
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
    const hasDifferentVal = this.currentIds.some((id: string | number) => this.$store.state.componentMap[id].extra[this.property] !== firstVal);

    if (hasDifferentVal) {
      return undefined;
    }

    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    if (val === this.v) return;

    this.currentIds.forEach((id: string) => {
      this.$store.commit("updateComponentExtra", {
        id,
        newExtra: {
          [this.property]: val,
        },
      });
    });
  }
}
</script>


<style scoped lang="less">
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 10px;
  }

  .el-switch {
    margin-right: 0;
  }
}
</style>
