<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-select v-model="v" :placeholder="placeholder" :multiple="params.multiple" v-bind="params" size="small"
      :disabled="selectDisabled || params.disabled">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { generateOptions } from "./generateOptions";
import Label from "@/components/Label/index.vue";
import { isEqual } from "lodash-es";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraSelect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  @Prop({
    required: true,
  })
  componentId!: number;

  beforeMount() {
    if (this.config.defaultValue !== undefined) {
      const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
      if (firstVal === undefined) {
        this.v = this.config.defaultValue;
      }
    }
  }
  get selectDisabled() {
    if (this.currentIds.length > 1) {
      return true;
    }
    return false;
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  get options() {
    return generateOptions(this.$store, this.params.options);
  }

  get v() {
    let firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];

    const hasDifferentVal = this.currentIds.some((id: string | number) => !isEqual(this.$store.state.componentMap[id].extra[this.property], firstVal));

    if (hasDifferentVal) {
      return Array.isArray(firstVal) ? [] : undefined;
    }

    // TODO 多测试 多选新增，当cavan渲染区域删除选项同步更新删除
    if (firstVal instanceof Array) {
      const secondVal: any = [];
      firstVal &&
        firstVal.forEach((select: number) => {
          this.options &&
            this.options.forEach((options: any) => {
              if (Number(options.value) === Number(select)) {
                secondVal.push(select);
              }
            });
        });
      firstVal = secondVal;
    }

    if (firstVal === undefined) {
      return this.defaultValue;
    }

    return firstVal;
  }

  set v(val) {
    if (val === this.v) return;
    this.currentIds.forEach((id: string) => {
      this.$store.commit("updateComponentExtra", {
        id,
        newExtra: {
          [this.property]: val,
        },
      });
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-select {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }

  .required {
    &::before {
      content: "*";
      color: red;
    }
  }
}
</style>
