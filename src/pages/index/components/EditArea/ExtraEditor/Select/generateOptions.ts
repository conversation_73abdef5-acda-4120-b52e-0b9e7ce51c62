import { Store } from "vuex";

export const generateOptions = (
  $store: Store<State>,
  options: string | any[] | undefined
) => {
  if (!options) {
    return [];
  }

  if (typeof options !== "string") {
    return options;
  }

  if (typeof options === "string") {
    const requirement = options.split("$")[1].split(";");
    const result = [];
    for (const id in $store.state.componentMap) {
      const component = $store.state.componentMap[id];
      const fail = requirement.some(require => {
        const [key, v] = require.split("=");
        if (component[key as keyof Component] !== v) {
          return true;
        }
      });
      if (!fail) {
        result.push({ label: id, name: id, value: id });
      }
    }
    return result;
  }
};
