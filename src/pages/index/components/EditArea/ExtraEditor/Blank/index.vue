<!--
 * @Author: your name
 * @Date: 2021-04-20 15:18:56
 * @LastEditTime: 2022-01-24 18:45:35
 * @LastEditors: wind
 * @Description: In User Settings Edit
 * @FilePath: /interactive-question-editor/src/pages/index/components/EditArea/ExtraEditor/Blank/index.vue
-->
<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <div>
      <div class="inner" v-for="(value, idx) in answers" :key="idx">
        <el-input v-model="answers[idx]" :placeholder="placeholder" @input="val => changeVal(idx, val)" :maxlength="maxlength" size="small"></el-input>
        <el-button type="danger" :disabled="answers.length === 1" icon="el-icon-delete" circle @click="handleBtn('subtract', idx)"></el-button>
      </div>
      <div class="flex">
        <el-button class="add-button" type="success" round @click="handleBtn('add')">增加</el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraBlank extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请输入",
  })
  placeholder!: string;

  @Prop({
    default: "",
  })
  defaultValue!: string;

  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  answers: string[] = [];
  isOrganinChange = false;
  beforeMount() {
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
    if (firstVal === undefined) {
      this.answers = [""];
    }
  }
  mounted() {
    this.$nextTick(() => {
      this.updateAnswers();
    });
  }

  get keyboardSubType() {
    let subType = "";
    for (const key in this.$store.state.componentMap) {
      const item = this.$store.state.componentMap[key];
      if (item.type === "specialComponent") {
        switch (item.subType) {
          case SpecialComponentSubTypes.KEYBOARD_ENGLISH:
          case SpecialComponentSubTypes.KEYBOARD:
            subType = item.subType;
            break;
        }
      }
    }
    return subType;
  }
  @Watch("currentId", { deep: true })
  async currentIdChanged(val: string, oldVal: string) {
    // if (val !== oldVal) {
    this.updateAnswers();
    // }
  }

  updateAnswers() {
    const temp = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
    if (!temp) {
      return;
    }
    if (JSON.stringify(temp) === JSON.stringify(this.answers)) return;
    this.isOrganinChange = true;
    this.answers = JSON.parse(JSON.stringify(temp)) || [""];
  }
  get currentId() {
    return this.$store.state.currentComponentIds[0];
  }
  get currentIds() {
    return this.$store.state.currentComponentIds;
  }
  get maxlength() {
    const characterLimit = this.$store.state.componentMap[this.currentIds[0]].extra.characterLimit;
    const length = characterLimit ? characterLimit : "";
    return length;
  }
  get hasAllCorrect() {
    const correct = this.answers.some((val: string) => val === "");
    // correct 有为‘’的答案
    return !correct;
  }

  @Watch("answers", { deep: true })
  async onAnswersChanged(val: string[]) {
    console.log("onAnswersChanged", val);
    if (this.isOrganinChange) {
      this.isOrganinChange = false;
      return;
    }
    this.$store.dispatch("updateComponentsExtra", {
      ids: this.currentIds,
      newExtra: {
        [this.property]: JSON.parse(JSON.stringify(val)),
        hasAllCorrect: this.hasAllCorrect,
      },
    });
  }

  handleBtn(type: string, idx: number) {
    if (type === "add") {
      this.answers.push("");
    }
    if (type === "subtract") {
      this.answers.splice(idx, 1);
    }
  }

  changeVal(idx: number, val: string) {
    console.log("changeVal", idx, val);
    const subtype = this.keyboardSubType;
    let currVal = val;
    if (subtype === SpecialComponentSubTypes.KEYBOARD_ENGLISH) {
      currVal = currVal.replace(/[^\w-":',!?. ]|_/gi, "");
    } else {
      currVal = currVal
        .replace(/[^\d.]/g, "")
        .replace(/\.{2,}/g, ".")
        .replace(".", "$#$")
        .replace(/\./g, "")
        .replace("$#$", ".")
        .replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3")
        .replace(/^\./g, "");
      console.log(currVal[idx]);
      if (currVal.length > 1 && currVal[0] === "0" && currVal[1] !== ".") {
        currVal = currVal.slice(1);
      }
    }
    this.answers[idx] = currVal;
  }
}
</script>

<style scoped lang="less">
@BORDER_COLOR: #f0f1f5;
@MAIN_COLOR: #42c57a;
@DANGER_COLOR: #f56c6c;
.container {
  padding-bottom: 10px;
  display: flex;
  // align-items: center;
  padding-bottom: 10px;
  .inner {
    margin-bottom: 8px;
    .el-input {
      width: 200px;
      vertical-align: middle;
      margin-right: 10px;
    }
    .icon {
      vertical-align: middle;
      font-size: 20px;
      margin-left: 10px;
      cursor: pointer;
    }
  }
  /deep/ .el-button {
    margin-left: 15px;
    height: 22px;
    width: 22px;
    i {
      font-size: 12px;
    }
    &.danger:hover {
      color: @DANGER_COLOR;
      border-color: @DANGER_COLOR;
      background-color: #fff;
    }
  }
  /deep/ .add-button {
    width: 200px;
    cursor: pointer;
    height: 40px;
    margin-left: 0px;
  }
  .flex {
    display: flex;
  }
}
</style>
