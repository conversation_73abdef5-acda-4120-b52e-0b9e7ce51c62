<template>
  <el-collapse-item title="业务属性" name="extra-properties">
    <tag-select :componentId="componentId" />
    <div v-for="(item, index) in editorConfig" :key="index + item.label">
      <component
        :class="{
          required: item.required,
        }"
        :is="components[item.type]"
        :label="item.label"
        :property="item.key"
        :isEditorAdd="item.isEditorAdd"
        :effectShowType="item.effectShowType"
        :params="!item.paramsfromGlobal ? item.params : getTypeParams(item)"
        :config="item"
        :componentId="componentId"
      />
    </div>
  </el-collapse-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import ExtraInput from "./Input/index.vue";
import ExtraInputNumber from "./InputNumber/index.vue";
import ExtraSelect from "./Select/index.vue";
import ExtratText from "./Text/index.vue";
import ExtratBlank from "./Blank/index.vue";
import ExtraEffect from "./Effect/index.vue";
import DefaultEffect from "./DefaultEffect/index.vue";
import ExtraSwitch from "./Switch/index.vue";
import ExtraRadio from "./Radio/index.vue";
import TagSelect from "./TagSelect/index.vue";
import ComponentExtraAudioSelect from "./AudioSelect/index.vue";
import ExtraGroup from "./Group/index.vue";
import CutShapeAnswers from "./CutShapeAnswers/index.vue";
import ExtraAnimations from "./Animations/index.vue";
import ExtraChildEditor from "./Child/index.vue";
import ExtraSelectFromStep from "./ExtraSelectFromStep/index.vue";
// import { getComponentTagConfig } from "../../../common/tagEdtiorConfig/componentTagConfig";
@Component({
  components: { TagSelect },
})
export default class ExtraEditor extends Vue {
  components = {
    audioSelect: ComponentExtraAudioSelect,
    group: ExtraGroup,
    input: ExtraInput,
    inputNumber: ExtraInputNumber,
    select: ExtraSelect,
    custom: ExtraSelect,
    effect: ExtraEffect,
    switch: ExtraSwitch,
    radio: ExtraRadio,
    cutShapeAnswers: CutShapeAnswers,
    animations: ExtraAnimations,
    text: ExtratText,
    blank: ExtratBlank,
    childComponent: ExtraChildEditor,
    selectFromStep: ExtraSelectFromStep,
    defaultEffect: DefaultEffect,
  };

  @Prop({
    required: true,
  })
  component!: Component;

  get componentId() {
    return this.$store.state.currentComponentIds[0];
  }

  get editorConfig(): [] {
    if (!this.component.tag) {
      return [];
    }
    const editorCfg = this.$store.state.template.tags.find((tag: Tag) => tag.name === this.component.tag).editorConfig;
    // 1230-todo.测试
    // editorCfg 编辑器配置本地化 
    // const tags = getComponentTagConfig(this.$store.state.template.category);
    // if (tags && tags.length > 0) {
    //   editorCfg = tags.find((tag: Tag) => tag.name === this.component.tag).editorConfig;
    // }
    return editorCfg;
  }

  get globalConfigs(): [] {
    return this.$store.state.template.extraConfig.filter((item: { relatedComponentProperties?: string }) => {
      return !!item.relatedComponentProperties;
    });
  }

  getTypeParams(item: any) {
    const filterItem: any = this.globalConfigs.find((config: any) => {
      return config.relatedComponentProperties === item.key;
    });
    return filterItem ? filterItem.params : item.params;
  }
}
</script>

<style scoped lang="less">
.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
