<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-select filterable v-model="v" :placeholder="placeholder" :multiple="params.multiple" v-bind="params" size="small" :disabled="selectDisabled">
      <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"> </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import { isEqual } from "lodash-es";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraSelectFromStep extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "text",
  })
  type!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
    default: "请选择",
  })
  placeholder!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number;

  @Prop({
    required: true,
  })
  config!: any;

  @Prop({
    default: () => ({}),
  })
  params!: Record<string, any>;

  @Prop({
    required: true,
  })
  componentId!: number;

  beforeMount() {
    if (this.config.defaultValue !== undefined) {
      const firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];
      if (firstVal === undefined) {
        this.v = this.config.defaultValue;
      }
    }
  }
  get selectDisabled() {
    if (this.currentIds.length > 1) {
      return true;
    }
    return false;
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }
  globExtraConfig(type: string): any {
    let data: any = null;
    for (const item of this.$store.state.template.extraConfig) {
      if (item.type === type) {
        data = item;
        break;
      }
    }
    return data;
  }
  get options() {
    const optionList: any[] = [];
    let globalParamsData = null;
    if (this.config.editorConfigfromGlobalSwKeyboard) {
      let subType = "";
      for (const key in this.$store.state.componentMap) {
        const item = this.$store.state.componentMap[key];
        if (item.type === "specialComponent") {
          switch (item.subType) {
            case SpecialComponentSubTypes.KEYBOARD_ENGLISH:
            case SpecialComponentSubTypes.KEYBOARD:
              subType = item.subType;
              break;
          }
        }
      }

      if (subType) {
        const globData = this.globExtraConfig("keyboardSelect");
        if (globData && globData["params"]["options"]) {
          for (const item of globData["params"]["options"]) {
            if (item.value === subType && item["editorConfig"]) {
              for (const ite of item["editorConfig"]) {
                if (ite.key === this.config.key && ite.params) {
                  globalParamsData = ite.params;
                }
              }
            }
          }
        }
      }
    }
    if (globalParamsData) {
      for (let i = globalParamsData.min; i <= globalParamsData.max; i += globalParamsData.step) {
        optionList.push({ label: i, value: i + "" });
      }
    } else {
      for (let i = this.params.min; i <= this.params.max; i += this.params.step) {
        optionList.push({ label: i, value: i + "" });
      }
    }

    return optionList;
  }

  get v() {
    let firstVal = this.$store.state.componentMap[this.currentIds[0]].extra[this.property];

    const hasDifferentVal = this.currentIds.some((id: string | number) => !isEqual(this.$store.state.componentMap[id].extra[this.property], firstVal));

    if (hasDifferentVal) {
      return Array.isArray(firstVal) ? [] : undefined;
    }

    // TODO 多测试 多选新增，当cavan渲染区域删除选项同步更新删除
    if (firstVal instanceof Array) {
      const secondVal: any = [];
      firstVal &&
        firstVal.forEach((select: number) => {
          this.options &&
            this.options.forEach((options: any) => {
              if (Number(options.value) === Number(select)) {
                secondVal.push(select);
              }
            });
        });
      firstVal = secondVal;
    }

    if (firstVal === undefined) {
      return this.defaultValue;
    }

    return firstVal;
  }

  set v(val) {
    if (val === this.v) return;
    this.currentIds.forEach((id: string) => {
      this.$store.commit("updateComponentExtra", {
        id,
        newExtra: {
          [this.property]: val,
        },
      });
    });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-select {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }

  .required {
    &::before {
      content: "*";
      color: red;
    }
  }
}
</style>
