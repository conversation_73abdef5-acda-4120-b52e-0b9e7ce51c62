<template >
  <div class="container">
    <!-- <el-select v-model="modleId">
      <el-option v-for="item1 in options" :key="item1.value" :label="item1.label" :value="item1.value"></el-option>
    </el-select> -->
    <el-select v-model="modleId" style="width: 144px; height: 24px" size="small" :placeholder="cfg.placeholder" @change="onChangePosId()">
      <el-option v-for="item1 in options" :key="item1.value" :label="item1.label" :value="item1.value"></el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import Label from "@/components/Label/index.vue";
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class SimpleSelect extends Vue {
  @Prop({
    required: true,
  })
  options!: any;

  @Prop({
    required: true,
  })
  cfg!: any;

  modleId = "";

  onChangePosId() {
    this.cfg.callBack(this.cfg, this.modleId);
  }
}
</script>

<style scoped lang="less">
</style>
