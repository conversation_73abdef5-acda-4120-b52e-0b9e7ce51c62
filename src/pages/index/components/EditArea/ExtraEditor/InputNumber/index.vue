<template>
  <div class="container">
    <ya-label :label="label" :description="config.description" />
    <el-input
      v-model="v"
      size="small"
      type="text"
      v-bind="params"
      @blur="handleBlur"
    />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class YaInputNumber extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: string;

  @Prop({
    required: true,
  })
  config!: any;

  userInput = null;

  set v(val) {
    val = String(val);
    const t = val.charAt(0);
    let temp = val.replace(/[^\d]/g, '')
    console.log('set v', temp);
    // 如果第一个是- add
    if(t === '-' && temp) {
      temp = '-' + temp
    }
    const { max, min } = this.params;
    if(max && temp > max) temp = max;
    if(min && temp < min) temp = min;
    if(val === '') temp = '';
    if(String(temp).at(-1) !== '.') {
      temp = Number(temp)
    }
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: temp,
      },
    });
  }

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[
      this.property
    ];
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  get displayValue() {
    if (this.userInput !== null) {
      return this.userInput;
    }

    return this.v;
  }

  handleBlur() {
    if(String(this.v).at(-1) === '.') {
      this.v =String(Number(this.v));
    }
  }

  onChange(val: string) {
    let temp = val;
    const { max, min } = this.params;
    if(max && Number(val) > Number(max)) temp = max;
    if(min && Number(val) < Number(min)) temp = min;
    this.v = temp === "" ? temp : Number(temp);
    this.userInput = null;
  }
}
</script>


<style scoped lang="less">
.container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
  }
}
</style>
