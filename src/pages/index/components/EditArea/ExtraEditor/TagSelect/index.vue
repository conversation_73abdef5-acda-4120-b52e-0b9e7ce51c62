<template>
  <div class="tag-select">
    <span class="label"> 类型 </span>
    <el-select v-model="tag" size="small" :disabled="selectDisabled">
      <!-- 如果是需切割图形 不展示普通元素 -->
      <!-- <el-option key="void" label="普通元素" value="" v-if="tag !== 'cutGraphic'"></el-option> -->
      <el-option v-for="item in tags" :key="item.name" :label="item.label" :value="item.name" :disabled="currSubComoentDisable(item) || item.selectable == false"></el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { CATEGORY } from "@/common/constants";
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class TagSelect extends Vue {
  @Prop({
    required: true,
  })
  componentId!: number;

  get currentComponent() {
    return this.$store.state.componentMap[this.currentIds[0]];
  }

  currSubComoentDisable(item: any) {
    // 子组件不允许设置业务属性
    const currid = this.currentIds[0];
    let culTagNum = 0;
    for (let i = 0; i < this.componentIds.length; i++) {
      if (typeof this.componentIds[i] === "object") {
        const tagName = this.$store.state.componentMap[this.componentIds[i].id].tag;
        if (item.settingNum && tagName === item.name) {
          culTagNum++;
        }
        for (const ite of this.componentIds[i].subIds) {
          if (ite == currid) {
            return true;
          }
        }
      } else {
        const tagName = this.$store.state.componentMap[this.componentIds[i]].tag;
        if (item.settingNum && tagName === item.name) {
          culTagNum++;
        }
      }
    }
    if (culTagNum >= item.settingNum) {
      return true;
    }
    return false;
  }

  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  get componentIds() {
    return this.$store.state.componentIds;
  }

  get tags(): Tag[] {
    // selectable 表示是否在业务属性中出现
    const tag: any = [];
    // 业务属性中都不可出现 selectable都为false
    let isAllDisabled = true;
    if (!this.$store.state.template.tags.length) isAllDisabled = false;
    this.$store.state.template.tags.forEach((item: any) => {
      const rule = typeof item.selectable !== "undefined" && item.selectable === false;
      if (!rule) isAllDisabled = false;
      if (!rule || this.currentComponent.tag == item.name) {
        tag.push(item);
      }
    });
    // 是否展示普通元素
    let showDefault = true;
    // 需切割图形 不展示普通元素
    if (this.tag === "cutGraphic") showDefault = false;
    // 如果所有的业务属性都不可选择 则不展示普通元素
    if (isAllDisabled) showDefault = false;
    // 如果当前的组件无tag 需要展示
    const tagName = this.currentComponent.tag;
    if (!tagName) showDefault = true;
    if (showDefault) {
      tag.push({ name: "", label: "普通元素" });
    }
    return tag;
  }

  get selectDisabled() {
    if (this.currentIds.length > 1) {
      return true;
    }
    if ((this.bundleName === "sharkBlankQuestion" && this.currentComponent.type === "spine") || this.currentComponent.isNoChangeTag) {
      return true;
    }
    return false;
  }
  get bundleName() {
    return this.$store.state.template.bundleName;
  }

  get tag() {
    const firstVal = this.$store.state.componentMap[this.currentIds[0]].tag;

    const hasDifferentVal = this.currentIds.some((id: string | number) => this.$store.state.componentMap[id].tag !== firstVal);

    if (hasDifferentVal) {
      return undefined;
    }

    return firstVal;
  }

  set tag(val) {
    if (val === this.tag) {
      return;
    }
    if (this.currentComponent.isNoChangeTag) {
      this.$message.error(`该组件不支持更改tag`);
      return;
    }
    const tag: Tag = (() => {
      if (val) {
        return this.tags.find(i => i.name === val) as Tag;
      }
      return { name: "", label: "", editorConfig: [] };
    })();
    if (this.currentComponent.type === "group") {
      const hasChildComponent = false;

      // tag.editorConfig.forEach(config => {
      //   if (config.type === "childComponent") {
      //     hasChildComponent = true;
      //   }
      // });
      if (hasChildComponent) {
        this.$message.error(`组合不支持设置为${tag.label}`);
        return;
      }
    } else if (this.fixLabelTag(tag)) {
      this.$message.error(`该文本组件已设置【固定宽高】，不可更改为‘${tag.label}’`);
      return;
    }
    //todoss 角标的修改
    // todoss 修改写法
    this.updateTagSelect(tag);
  }
  resetOtherCompomentData() {
    const category = this.$store.state.template.category;
    if (category == CATEGORY.LINE) {
      for (const key in this.$store.state.componentMap) {
        const item = this.$store.state.componentMap[key];
        if (item.tag === "dragArea") {
          const extraData = item.extra;
          if (extraData.pairingObject.includes(this.componentId)) {
            const updateData = {
              pairingObject: [],
            };
            updateData.pairingObject = JSON.parse(JSON.stringify(extraData.pairingObject));
            updateData.pairingObject=updateData.pairingObject.filter(val => val != this.componentId);
            this.$store.commit("updateComponentExtra", {
              id: item.id,
              newExtra: updateData,
            });
          }
        }
      }
    }
  }
  updateTagSelect(tag: any) {
    let isUpdateOtherComponent = false;
    const category = this.$store.state.template.category;
    if (this.tag == "ligature" && tag.name == "" && category == CATEGORY.LINE) {
      isUpdateOtherComponent = true;
    }
    if (this.componentId) {
      const componentId = this.$store.state.componentMap[this.componentId].extra;
      if (componentId[componentId.getParams]) {
        this.$store.dispatch("removeComponent", componentId[componentId.getParams].id);
        this.$store.commit("updateComponentExtra", {
          id: this.componentId,
          newExtra: {
            [componentId.property]: "",
            [componentId.getParams]: null,
          },
        });
      }
    }
    this.$store.dispatch("updateComponentsTag", {
      ids: this.currentIds,
      tag,
    });
    if (tag.name === "") {
      this.$store.dispatch("removeChildComponents", {
        ids: this.currentIds,
      });
    }

    tag.editorConfig.forEach((config: { initialValue: any; key: any }) => {
      const { initialValue, key } = config;
      if (initialValue !== undefined) {
        this.currentIds.forEach((id: any) => {
          this.$store.commit("updateComponentExtra", {
            id,
            newExtra: {
              [key]: this.addDefaultVale(initialValue, key) || initialValue,
            },
          });
        });
      }
    });

    if (isUpdateOtherComponent) {
      this.resetOtherCompomentData();
    }
  }

  fixLabelTag(tag: any) {
    // 文本组件 固定宽高将不支持  拖拽元素
    if ((tag.name == "oneDragableObject" || tag.name == "dragableObject") && this.currentComponent.type === "label" && this.currentComponent.properties.isFixed) {
      return true;
    }
    return false;
  }
  addDefaultVale(initialValue: Record<string, any>, key: string): any {
    const properi = this.$store.state.componentMap[this.componentId].properties;
    const target = initialValue[0];
    if (target && key === "position" && properi) {
      if (typeof target.endX != "undefined" && typeof target.endY != "undefined") {
        const initialValue1 = JSON.parse(JSON.stringify(initialValue));
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        const pos = window.cocos.getWorldPos(this.componentId, properi.angle, properi.x);
        initialValue1[0].endX = pos.x;
        initialValue1[0].endY = pos.y;
        return initialValue1;
      }
      return null;
    }
    return null;
  }
}
</script>

<style scoped lang="less">
.tag-select {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 5px;
  }
}
</style>
