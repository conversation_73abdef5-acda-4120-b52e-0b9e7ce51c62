<template>
  <div class="container">
    <template v-if="componentType === 'sprite' || effectShowType === 'images' || (isAnim && config.key === 'linkPoint')">
      <ya-label :label="label" :description="config.description" />
      <div class="config-area">
        <image-select v-if="!isEditorAdd" :src.sync="v" />
        <edtior-image-select v-else :src.sync="v" :componentId="componentId" :isEditorAdd="isEditorAdd" :property="property" :params="params" />
      </div>
    </template>
    <template v-else-if="isAnim && config.key !== 'linkPoint'">
      <div class="spine-editor">
        <div style="display: flex; justify-content: space-between">
          <div>
            <ya-label :label="label" :description="config.description" />
          </div>
          <el-button v-if="showClear" @click="clear" class="btSkin" size="mini" slot="reference"> 清空 </el-button>
        </div>
        <div class="spine-config">
          <el-row>
            <el-col :span="12" class="spine-editor-loop">
              <span class="label">循环</span>
              <el-switch v-model="loop" />
            </el-col>
            <el-col :span="12" class="spine-editor-time-scale">
              <span class="label">速率</span>
              <el-input
                v-model="timeScale"
                v-bind="{
                  min: 0.1,
                  max: 10,
                  step: 0.1,
                }"
                type="number"
                size="mini"
              />
            </el-col>
          </el-row>
          <el-row class="spine-editor-animation-list">
            <el-col :span="6"> 播放队列 </el-col>
            <el-col :span="18">
              <ul class="spine-editor-animation-list-list">
                <li v-for="(item, index) in animationList" :key="index" class="spine-editor-animation-list-list-item">
                  <el-select :value="item" @change="onAnimationItemChange(index, $event)" size="mini" :loading="isFetching">
                    <el-option key="void" value=""> 无动画 </el-option>
                    <el-option v-for="i in animationOptions" :key="i" :value="i">
                      {{ i }}
                    </el-option>
                  </el-select>
                  <i @click="onClickAddAnimation(index)" class="el-icon-circle-plus-outline icon" />
                  <i v-if="animationList.length > 1" @click="onClickRemoveAnimation(index)" class="el-icon-remove-outline icon" />
                </li>
              </ul>
            </el-col>
          </el-row>
        </div>
      </div>
    </template>
    <template v-else> 当前组件不支持配置{{ label }} </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import ImageSelect from "@/components/ImageSelect/index.vue";
import EdtiorImageSelect from "@/components/EditorImageSelect/index.vue";
import { CATEGORY } from "@/common/constants";
@Component({
  components: {
    YaLabel: Label,
    ImageSelect,
    EdtiorImageSelect,
  },
})
export default class ExtraEffect extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: true,
  })
  config!: any;

  // 是否从编辑器插入
  @Prop({
    required: false,
  })
  isEditorAdd!: boolean;

  // effect展示的选择
  @Prop({
    default: "",
  })
  effectShowType!: string;

  animationOptions: string[] = [];

  isFetching = false;

  get component(): Component {
    return this.$store.state.componentMap[this.componentId];
  }

  get showClear(): boolean {
    const { category } = this.$store.state.template;
    if (category == CATEGORY.CHOICE) {
      return true;
    }
    return false;
  }
  get componentType() {
    return this.component.type;
  }

  get isAnim() {
    return this.componentType === "spine" || this.componentType === "cocosAni";
  }

  get isLinkPoint() {
    return this.config.key === "linkPoint";
  }

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[this.property];
    if (firstVal === undefined) {
      if (this.isAnim && !this.isLinkPoint) {
        return {
          loop: false,
          animationList: [""],
          timeScale: 1,
        };
      }
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    if (this.isEditorAdd) return;
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }

  get skeletonUrl() {
    return this.componentType === "spine" && this.effectShowType !== "images"
      ? (this.component as SpineComponent).spineData.skeleton
      : this.componentType === "cocosAni"
      ? (this.component as CocosAniComponent).cocosAniData.url
      : undefined;
  }

  get animationList() {
    return this.v.animationList || [""];
  }

  set animationList(animationList) {
    this.v = { ...this.v, animationList };
  }

  get timeScale() {
    return this.v.timeScale;
  }

  set timeScale(value) {
    this.v = { ...this.v, timeScale: value };
  }

  get loop() {
    return this.v.loop;
  }

  set loop(value) {
    this.v = { ...this.v, loop: value };
  }

  @Watch("skeletonUrl", {
    immediate: true,
  })
  skeletonUrlWatcher(url: string) {
    url && this.fetchAnimation(url);
  }

  onAnimationItemChange(index: number, value: string) {
    const newAnimationList = this.animationList.concat();
    newAnimationList[index] = value;
    this.animationList = newAnimationList;
  }

  onClickAddAnimation(index: number) {
    const newAnimationList = this.animationList.concat();
    newAnimationList.splice(index + 1, 0, "");
    this.animationList = newAnimationList;
  }

  onClickRemoveAnimation(index: number) {
    const newAnimationList = this.animationList.concat();
    newAnimationList.splice(index, 1);
    this.animationList = newAnimationList;
  }

  clear() {
    this.$store.commit("updateComponentExtraClear", {
      id: this.componentId,
      newExtra: {
        [this.property]: "",
      },
    });  
  }

  fetchAnimation(url: string) {
    if (this.componentType === "cocosAni") {
      // 获取动画队列选项
      this.animationOptions = (this.component as CocosAniComponent).properties.clips;
      return;
    }

    if (this.componentType !== "spine") {
      return;
    }
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations);
        this.animationOptions = animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  padding-bottom: 10px;

  .config-area {
    margin-left: 5px;

    /deep/ .image-select .image-container .image {
      width: 100px;
    }
  }

  .spine-editor {
    width: 100%;
    text-align: left;

    .spine-config {
      margin: 10px 0 0 15px;

      .label {
        display: inline-block;
        min-width: 40px;
        margin-right: 10px;
      }
    }

    &-loop {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-time-scale {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-animation-list {
      margin-top: 10px;
      text-align: left;
      line-height: 28px;

      &-list {
        list-style: none;
        padding: 0;
        margin: 0;

        &-item {
          margin-bottom: 5px;
        }
      }
    }
  }

  .icon {
    display: inline-block;
    width: 24px;
    height: 28px;
    line-height: 28px;
    font-size: 22px;
    vertical-align: bottom;
    cursor: pointer;

    &:first-of-type {
      margin-left: 5px;
    }
  }
}
</style>
