<template>
  <div class="extra-editor-group">
    <div style="display: inline-block">
      {{ label }}:
      <el-button v-if="v.length < params.max" @click="onClickAdd" size="mini">添加</el-button>
    </div>
    <div v-for="(item, index) in v" :key="index" class="extra-editor-group-item-root">
      <div class="extra-editor-group-item">
        <div class="extra-editor-group-item-form">
          <div v-for="(formItem, i) in children" :key="i" class="extra-editor-group-item-form-item">
            <ya-label style="width: 55px" :label="formItem.label" :description="formItem.description" />
            <el-input :value="children.length === 1 ? item : item[formItem.key]" @input="onChange(index, formItem.key, $event)" v-if="formItem.type === 'input'" v-bind="formItem.params" size="mini" />
            <el-input-number
              v-else-if="formItem.type === 'inputNumber'"
              :value="children.length === 1 ? item : item[formItem.key]"
              @input="onChange(index, formItem.key, $event)"
              v-bind="formItem.params"
              size="mini"
              controls-position="right"
            />
            <el-select v-else-if="formItem.type === 'select'" :value="children.length === 1 ? item : item[formItem.key]" size="mini" @change="onChange(index, formItem.key, $event)" />
            <audio-select v-else-if="formItem.type === 'audioSelect'" :value="children.length === 1 ? item : item[formItem.key]" @change="onChange(index, formItem.key, $event)"> </audio-select>
          </div>
        </div>
        <el-button v-if="v.length > params.min" @click="onClickDel(index)" size="mini"> 移除 </el-button>
      </div>

      <div v-if ="property === 'position'"  class="extra-editor-group-positem">
        <ya-label style="width: 55px" :label="posCfg.posLable" :description="posCfg.posLableDescription" />
        <simple-select :cfg="{ placeholder: posCfg.posPlaceholder, callBack: onChangePosChoose, index: index }" :options="posComponentIdOptions"></simple-select>
        <el-button :key="reshNum" :disabled="getPosDataDisabled(index)" style="margin-left: 10px" @click="onClickGetPos(index)" size="mini">确定 </el-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { cloneDeep } from "lodash-es";
import SimpleSelect from "../SimpleSelect/index.vue";
import bus from "@/pages/index/common/utils/bus";
@Component({
  components: {
    AudioSelect,
    YaLabel: Label,
    SimpleSelect: SimpleSelect,
  },
})
export default class ExtraGroup extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: true,
  })
  config!: any;

  posComponentId = "";
  posCfg = {
    posLable: "参考元素",
    posPlaceholder: "请选择拖拽元素id",
    posLableDescription: "",
    posComponentId: {},
  };
  reshNum = 0;

  getPosDataDisabled(index: number) {
    if (this.posCfg.posComponentId[index]) {
      return false;
    }
    return true;
  }

  get posComponentIdOptions() {
    const idList: { label: string; value: string }[] = [];
    this.$store.state.componentIds.forEach((Id: any) => {
      let currId = Id;
      if (typeof currId !== "string") {
        currId = currId.id;
      }
      const element = this.$store.state.componentMap[currId];
      if (element && element.tag == "dragableObject") {
        idList.push({ label: currId + "", value: currId + "" });
      }
    });
    return idList;
  }
  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[this.property];
    return firstVal;
  }

  set v(val) {
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }

  get children() {
    return this.params.children;
  }

  onChangePosChoose(data: any, id: number) {
    const index = data.index;
    this.posCfg.posComponentId[index] = id + "";
    this.reshNum++;
  }

  onChange(index: number, key: string, value: any) {
    const cloneV = cloneDeep(this.v);
    if (this.children.length === 1) {
      cloneV[index] = value;
    } else {
      cloneV[index][key] = value;
    }
    this.v = cloneV;
  }

  onChangeChoose(index: number, pro: { key: string; value: any }[], isAll: boolean) {
    const cloneV = cloneDeep(this.v);
    if (isAll) {
      for (let i = 0; i < cloneV.length; i++) {
        for (let j = 0; j < pro.length; j++) {
          cloneV[i][pro[j].key] = pro[j].value;
        }
      }
    } else {
      for (let j = 0; j < pro.length; j++) {
        cloneV[index][pro[j].key] = pro[j].value;
      }
    }
    this.v = cloneV;
  }

  onClickAdd() {
    const cloneV = cloneDeep(this.v);
    if (this.children.length === 1) {
      cloneV.push(this.children[0].initialValue);
    } else {
      const initialValue: Record<string, any> = {};
      this.children.forEach((formItem: any) => {
        initialValue[formItem.key] = formItem.initialValue;
      });
      this.addDefaultVale(initialValue);
      cloneV.push(initialValue);
    }
    this.v = cloneV;
  }
  onGetPoscallBack(data: { id: string; index: number; chooseAll: boolean; x: number; y: number }) {
    // this.$store.dispatch("updateComponentsProperties", {
    //   ids: [data.id],
    //   newProperties: {
    //     x: data.x,
    //     y: data.y,
    //   },
    // });
    this.onChangeChoose(
      data.index,
      [
        { key: "endX", value: data.x },
        { key: "endY", value: data.y },
      ],
      data.chooseAll,
    );
  }
  onClickGetPos(index: number) {
    bus.$emit("scopedComponentState", { state: 1, index: index, id: this.posCfg.posComponentId[index], type:"dragQuestion", callBack: this.onGetPoscallBack });
  }

  onClickDel(index: number) {
    const cloneV = cloneDeep(this.v);
    cloneV.splice(index, 1);
    this.v = cloneV;
  }
  addDefaultVale(initialValue: Record<string, any>) {
    const curV = this.v;
    if (this.property === "position" && curV.length >0 && curV[curV.length - 1]) {
      initialValue.endX = curV[curV.length - 1].endX;
      initialValue.endY = curV[curV.length - 1].endY;
      return;
    }
    const properi = this.$store.state.componentMap[this.componentId].properties;
    if (this.property === "position" && properi) {
      if (typeof initialValue.endX != "undefined" && typeof initialValue.endY != "undefined") {
        // eslint-disable-next-line @typescript-eslint/ban-ts-ignore
        // @ts-ignore
        const pos = window.cocos.getWorldPos(this.componentId, properi.angle, properi.x);
        initialValue.endX = pos.x;
        initialValue.endY = pos.y;
      }
    }
  }
}
</script>

<style scoped lang="less">
.extra-editor-group {
  text-align: left;
  &-item-root {
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin: 10px 0 10px 15px;
  }
  &-positem {
    display: flex;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 5px;
  }
  &-item {
    display: flex;
    align-items: center;
    // border-bottom: 1px solid #eee;

    &-form {
      &-item {
        margin-bottom: 10px;
        margin-right: 10px;
        display: flex;

        &:only-child,
        &:last-child {
          margin-bottom: 0;
        }

        .audio-select-container {
          padding-bottom: 0;
        }
      }
    }
  }
}
</style>
