/* eslint-disable @typescript-eslint/no-explicit-any */
<template>
  <div class="container">
    <template>
      <ya-label :label="label" :description="config.description" />
      <div class="spine-editor">
        <div class="spine-config" v-if="subParamsValue">
          <div v-for="(item, index) in subParams" :key="index">
            <ya-label :label="item.label" :description="item.description" />
            <div v-if="item.type === 'effect'">
              <image-select :src.sync="subParamsValue[item.key]" @update:src="val => imageChangeHandler(item.key, val)" :isShowDeleteBtn="false" />
            </div>
            <div v-else-if="item.type === 'select'">
              <el-select :value="subParamsValue[item.key]" @change="val => selectChangeHandler(item.key, val)" :placeholder="item.label" size="small">
                <el-option v-for="item1 in subParamsOptions[item.key]" :key="item1.value" :label="item1.label" :value="item1.value"> </el-option>
              </el-select>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import ImageSelect from "@/components/ImageSelect/index.vue";
import { cloneDeep } from "lodash-es";
import getImageSize from "@/common/utils/getImageSize";

// todoss cocos动画的实现
@Component({
  components: {
    YaLabel: Label,
    ImageSelect,
  },
})
export default class ExtraChildEditor extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: true,
  })
  property!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: true,
  })
  config!: any;
  subParamsValue: any = null;
  mainSubParam: any = null;
  lastId?: number;
  created() {
    if (!this.lastId) {
      this.lastId = this.componentId;
    }
    this.initSubParams();
  }

  @Watch("componentId")
  componentIdChange(val: number) {
    console.log(val);
    if (this.lastId !== val) {
      this.initSubParams();
      this.lastId = val;
    }
  }

  get subParams() {
    return this.config.subParams;
  }

  get component(): Component {
    return this.$store.state.componentMap[this.componentId];
  }

  get componentType() {
    return this.component.type;
  }

  get v() {
    const firstVal = this.$store.state.componentMap[this.componentId].extra[this.property];
    if (firstVal === undefined) {
      return this.defaultValue;
    }
    return firstVal;
  }

  set v(val) {
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: val,
      },
    });
  }

  get childComponentIds() {
    //todo
    let childIds: string[] = [];
    const isGroup = this.$store.state.componentMap[this.componentId].type == "group";
    this.$store.getters.componentIdsAllObjectType.forEach((id: any) => {
      if (typeof id === "object" && id.id === this.componentId) {
        if (isGroup) {
          for (let i = 0; i < id.subIds.length; i++) {
            if (this.$store.state.componentMap[id.subIds[i]].cName) {
              childIds.push(id.subIds[i]);
            }
          }
        } else {
          childIds = id.subIds;
        }
      }
    });
    return childIds;
  }

  initSubParams() {
    const valMap = {};
    this.subParams.forEach((param: any) => {
      if (this.v && this.v[param.key]) {
        valMap[param.key] = this.v[param.key];
      } else if (param.defaultValue) {
        valMap[param.key] = param.defaultValue;
      }
      if (param.main) {
        this.mainSubParam = param.type;
      }
    });
    this.subParamsValue = valMap;
    if (this.childComponentIds.length === 0) {
      let _type = "";
      if (this.mainSubParam === "effect") {
        _type = "sprite";
      }
      const { x, y, width, height } = this.$store.state.componentMap[this.componentId].properties;
      const childComponent = [
        {
          cName: "linePoint",
          tag: "",
          isFocus: false,
          type: _type,
          editable: false,
          deletable: false,
          tagName: "",
          properties: {
            active: true,
            width: 30,
            height: 30,
            x: 0 + width / 2 + 20,
            y: 0,
            texture: "https://testimg.zuoyebang.cc/cw_db4a5e73e0a9c5ebc3264fbf92efbe07.png",
          },
        },
      ];
      Object.keys(childComponent[0].properties).forEach(key => {
        valMap[key] && (childComponent[0].properties[key] = valMap[key]);
      });
      this.$store.dispatch("addChildComponent", {
        id: this.componentId,
        childComponent: childComponent[0],
      });
    }
  }

  imageChangeHandler(key: string, value: any) {
    if (value) {
      getImageSize(value).then(({ width, height }) => {
        const componentParams = {
          tag: "",
          isFocus: false,
          type: "sprite",
          editable: false,
          deletable: false,
          tagName: "",
          properties: {
            active: true,
            width,
            height,
            x: 0,
            y: 0,
            texture: value,
          },
        };
        console.warn(componentParams, "componentParams");
        this.$store.dispatch("updateComponentsProperties", {
          ids: [this.childComponentIds[0]],
          newProperties: {
            texture: value,
            width,
            height,
          },
        });
      });
    }
    this.propertyChangeHandler(key, value);
  }

  selectChangeHandler(key: string, value: any) {
    const position = { x: 0, y: 0 };
    const { width, height } = this.component.properties;
    if (value === "up") {
      position.x = 0;
      position.y = 0 + height / 2 + 20;
    } else if (value === "down") {
      position.x = 0;
      position.y = 0 - height / 2 - 20;
    } else if (value === "left") {
      position.x = 0 - width / 2 - 20;
      position.y = 0;
    } else if (value === "right") {
      position.x = 0 + width / 2 + 20;
      position.y = 0;
    }
    this.$store.dispatch("updateComponentsProperties", {
      ids: [this.childComponentIds[0]],
      newProperties: {
        x: position.x,
        y: position.y,
      },
    });
    console.log(width, height, position);

    this.propertyChangeHandler(key, value);
  }

  propertyChangeHandler(key: string, value: any) {
    console.warn(key, value, this.property, this.subParamsValue);
    this.subParamsValue[key] = value;
    this.subParamsValue = cloneDeep(this.subParamsValue);
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        [this.property]: cloneDeep(this.subParamsValue),
      },
    });
  }

  get subParamsOptions() {
    const optionMap = {};
    this.subParams.forEach((param: any) => {
      if (param.params.options) {
        optionMap[param.key] = param.params.options;
      }
    });
    return optionMap;
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  padding-bottom: 10px;

  .config-area {
    margin-left: 5px;

    /deep/ .image-select .image-container .image {
      width: 100px;
    }
  }

  .spine-editor {
    width: 100%;
    text-align: left;

    .spine-config {
      margin: 10px 0 0 15px;

      .label {
        display: inline-block;
        min-width: 40px;
        margin-right: 10px;
      }
    }

    &-loop {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-time-scale {
      display: flex;
      align-items: center;
      flex-grow: 1;
      box-sizing: border-box;
    }

    &-animation-list {
      margin-top: 10px;
      text-align: left;
      line-height: 28px;

      &-list {
        list-style: none;
        padding: 0;
        margin: 0;

        &-item {
          margin-bottom: 5px;
        }
      }
    }
  }

  .icon {
    display: inline-block;
    width: 24px;
    height: 28px;
    line-height: 28px;
    font-size: 22px;
    vertical-align: bottom;
    cursor: pointer;

    &:first-of-type {
      margin-left: 5px;
    }
  }
}
</style>
