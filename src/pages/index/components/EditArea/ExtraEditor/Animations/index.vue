<template>
  <div class="container">
    <ya-label label="编辑动画" />
    <el-button size="mini" @click="handleClick">去编辑</el-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import Label from "@/components/Label/index.vue";
import { cloneDeep } from "lodash-es";

@Component({
  components: {
    YaLabel: Label,
  },
})
export default class ExtraAnimations extends Vue {
  @Prop({
    default: () => ({}),
  })
  params!: any;

  get currentComponentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  handleClick() {
    this.$store.commit("setIsEditingComponentAnimations", true);
    this.$store.commit(
      "setEditingAnimationsComponentId",
      this.currentComponentIds[0],
    );
    this.$store.commit(
      "setComponentAnimationConfig",
      cloneDeep(this.params.options),
    );
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  flex-grow: 1;
  align-items: center;
  padding-bottom: 10px;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    margin-right: 10px;
  }
}
</style>
