<template>
  <div class="container">
    <div class="label">{{ label }}</div>
    <img @click="onClick" class="image" :src="imageUrl" />
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import getImageSize from "@/common/utils/getImageSize";
import bus from "@/pages/index/common/utils/bus";

@Component
export default class ImageUpload extends Vue {
  @Prop({
    default: "图片",
  })
  label!: string;

  @Prop({
    required: true,
  })
  componentId!: number;

  @Prop({
    required: false,
    default: "texture",
  })
  property!: string;

  get imageUrl() {
    return this.$store.state.componentMap[this.componentId].properties[
      this.property
    ];
  }

  set imageUrl(val) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [this.property]: val,
      },
    });
  }

  get componentWidth() {
    return this.$store.state.componentMap[this.componentId].properties.width;
  }

  onClick() {
    bus.$emit("material-show");
    ImageLibrary({
      onClose: () => {
        //
      },
      onConfirm: async images => {
        const imageSize = await getImageSize(images[0].url);
        const { componentWidth } = this;
        // 保持图片原始比例
        this.$store.commit("updateComponentProperties", {
          id: this.componentId,
          newProperties: {
            width: componentWidth,
            height: (imageSize.height / imageSize.width) * componentWidth,
          },
        });
        this.imageUrl = images[0].url;
      },
    });
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  align-items: center;
  margin-bottom: 10px;

  .label {
    margin-right: 10px;
  }

  .image {
    width: 120px;
    margin-right: 10px;
    cursor: pointer;
    &:hover {
      outline: blue;
    }
  }
}
</style>
