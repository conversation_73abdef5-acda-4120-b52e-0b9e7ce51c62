import { CreateComp } from "@/pages/index/common/utils/createComp";
import EditModal from "./EditDialog.vue";
import store from "@/pages/index/store";
import { SpecialComponentSubTypes } from "@/common/constants";

const handleCreate = (params: { formData
  : any; labelPicList: string[]; }) => {
  console.log('handleCreate..', params);
  const component: Omit<H5LabelComponent, "id"> = {
    type: "specialComponent",
    subType: SpecialComponentSubTypes.H5LABEl,
    tag: "",
    dragable: true,
    canCombine: false,
    properties: {
      active: true,
      width: 1000,
      height: 640,
      x: 0,
      y: 0,
      customH5label: params.formData,
      labelPicList: params.labelPicList,
    },
  };
  store.dispatch("addComponentAndFocus", component);
}

const handleEdit = (params: { formData
  : any; labelPicList: string[]; }) => {
  store.commit("updateComponentProperties", {
    id: store.state.currentComponentIds[0],
    newProperties: {
      customH5label: params.formData,
      labelPicList: params.labelPicList,
    },
  });
}
const idPrefix = "h5label-editor"

function create() {
  new CreateComp({
    component: EditModal,
    idPrefix,
  }).confirm((instance) => {
    instance.open('create', '');
  }).then((res: any) => {
    handleCreate(res.data);
  });
  console.log('create');
}

function edit() {
  const content = store.getters.currentComponents[0].properties.customH5label;
  new CreateComp({
    component: EditModal,
    idPrefix,
  }).confirm((instance) => {
    instance.open('edit', content);
  }).then((res: any) => {
    handleEdit(res.data);
  })
}

export default {
  create,
  edit
}