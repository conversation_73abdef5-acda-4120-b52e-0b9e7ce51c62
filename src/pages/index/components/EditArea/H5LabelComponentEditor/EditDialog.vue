<template>
  <el-dialog title="富文本" :visible.sync="visible" :width="width" top="0vh" custom-class="h5label-form-dialog" :close-on-click-modal="false" @close="onDialogClose">
    <el-form :model="formData" label-position="top" ref="form" class="form-content-wrapper">
      <QEditor :config="config" :form="formData" ref="QEditor" @itemValidate="itemValidate"></QEditor>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="onDialogClose">取 消</el-button>
      <el-button size="small" type="primary" @click="onDialogConfirm" :loading="loading"> 插入 </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { getIsUploadEnable } from "@/common/utils/renderTextureToPicture";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import QEditor from "@/components/form-question/q-editor/index.vue";
import { picReSize } from "@/pages/index/common/hdkt2Shark/h5Switch";
import { ElForm } from "element-ui/types/form";
import { Component, Prop, Vue } from "vue-property-decorator";
// 弹窗的显示逻辑
// 1. 单击编辑富文本按钮
// 2. 单击组件-富文本
// 3. 双击画布中的富文本组件
// h5Label-dialog-toggle 新增富文本组件和双击cocos组件时true，关闭弹窗时false

@Component({
  components: {
    QEditor,
  },
})
export default class H5LabelComponentEditor extends Vue {
  @Prop({
    default: "1032px",
  })
  width!: string;

  status: "edit" | "create" = "edit";

  visible = false;
  loading = false;
  stopInsert = false;
  formData = {
    content: "",
  };
  initContent = "";
  config = {
    key: "content",
    label: "内容",
    type: "text",
    cocosKey: "questionTitle",
    props: {
      type: "text",
      required: true,
      max: 6000,
      placeholder: "内容最多不超过6000个字符",
    },
  };
  itemValidate(key: string) {
    (this.$refs.form as any).validateField(key, (...params: any[]) => {
      console.log("validateField.params", params);
    });
  }

  setFormDataContent(content: string) {
    this.formData.content = content;
    this.resetEditor();
    this.$refs.form && (this.$refs.form as ElForm).clearValidate();
  }

  resetEditor() {
    const qEditor = this.$refs.QEditor ? (this.$refs.QEditor as QEditor) : null;
    if (qEditor) qEditor.setContent(this.formData.content);
  }

  open(status: "edit" | "create", content: string) {
    this.status = status;
    this.visible = true;
    // console.log("open...", status, content);
    this.formData.content = content;
    this.initContent = content;
    this.$nextTick(() => {
      this.resetEditor();
    });
  }

  onDialogClose() {
    this.visible = false;
    console.log("onDialogClose");
    this.$emit("cancel");
  }
  
  onDialogConfirm() {
    console.log("onDialogConfirm");
    (this.$refs.form as any).validate(async (valid: boolean, data: any[]) => {
      const dom = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument.querySelector("#tinymce");
      if (!valid) {
        showErrorMessage(new Error("题目信息有误，请修改后再插入"));
      } else {
        // 判断是否全部是空格
        if (!dom.innerText.trim()) {
          showErrorMessage(new Error("题目信息有误，请修改后再插入"));
          this.formData.content = "";
          return;
        }
        const messageInstance = this.$message({
          message: "富文本渲染中，请稍等···",
          type: "info",
          duration: 0,
        });
        this.visible = true;
        this.loading = true;
        let imgBlob: any = null;
        let content = this.formData.content;
        if (this.$refs.QEditor) {
          // default 1000
          // 只有一个dom的时候，获取dom的宽度
          // 滚动条的出现的时候，占用宽度导致错位
          content = (this.$refs.QEditor as QEditor).getContent();
          // 判断content是否变化
          if (content === this.initContent) {
            console.log("content 没有变化 不重新截图");
            this.$emit("cancel");
            this.stopInsert = false;
            this.loading = false;
            this.initContent = "";
            messageInstance.close();
            return;
          }

          const dom = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument.querySelector("#tinymce");
          // 解决翻页阅读题的,图片和编辑态文字位置有gap的问题
          const width = 1000;
          [...dom.querySelectorAll("p")].forEach(item => {
            item.style.margin = "0";
          });
          // 如果是多个dom 需要获取最后一个dom的
          const options = {
            height: dom.offsetHeight + 36,
            width: dom.offsetWidth > width ? width : dom.offsetWidth,
            bgcolor: "transparent",
            style: { margin: "0", fontSize: "24px", lineHeight: 1.5 },
          };
          imgBlob = await (this.$refs.QEditor as QEditor).genBlob(options);
        }
        if (this.stopInsert) {
          this.stopInsert = false;
          this.loading = false;
          this.initContent = "";
          messageInstance.close();
          return;
        }
        const cocosData = {
          formData: content,
          imgBlob: imgBlob,
        };
        console.log("h5label-cocosData", cocosData);

        const resizeData = await this.InsertLargeTextToCocos(cocosData);
        this.$emit("done", resizeData);
        this.loading = false;
        this.visible = false;
        messageInstance.close();
      }
    });
  }

  async InsertLargeTextToCocos(data: any) {
    console.log("InsertLargeTextToCocos", data);
    getIsUploadEnable(true);
    const list = await picReSize(data.imgBlob);
    getIsUploadEnable(false);
    return {
      formData: data.formData,
      labelPicList: list,
    };
  }

  public mounted() {
    console.log("H5 label mounted");
    window.addEventListener("unhandledrejection", this.handleEnhandledrejection, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("unhandledrejection", this.handleEnhandledrejection, false);
    });
  }

  handleEnhandledrejection(event: any) {
    if (event.reason && event.reason.message && event.reason.message.includes("dom-to-image-img-load-error")) {
      console.warn(`UNHANDLED PROMISE REJECTION:`, event.reason.message);
      const [key, value] = "dom-to-image-img-load-error: https://yaya.cdnjtzy.com/cw_2f086ea4ca10a3d3fe9adaa65e4015e8-0224d5.png".split(" ");
      const str = this.formData["content"];
      console.log(key, value);
      if (str.includes(value)) {
        this.stopInsert = true;
        showErrorMessage(new Error("题目信息可能有丢失，请再次点击【插入题目】进行重试"));
      }
    }
    event.preventDefault(); // 增加阻止默认事件，阻止页面报错
    // 通过addEventListener绑定的事件，不能通过return false来阻止默认行为
  }
}
</script>

<style scoped lang="less">
.add-option {
  width: 100px;
  font-size: 12px;
  margin: 10px 0;
  font-weight: 400;
  line-height: 12px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
}
.formula-editor-dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  .formula-editor-relative {
    position: relative;
  }
  .formula-editor-close {
    span {
      position: absolute;
      font-size: 60px;
      font-weight: 700;
      color: #fff;
      top: 0;
      right: -48px;
      cursor: pointer;
      font-family: "Symbola,Times New Roman,PingFang-Medium";
    }
  }
  .formula-iframe {
    height: 70vh;
    width: 50vw;
    min-height: 400px;
    min-width: 700px;
    background-color: #fff;
  }
}
</style>

<style lang="less">
.h5label-form-dialog {
  height: 540px;
  .dialog-footer {
    margin-top: 16px;
  }
  .tox-tinymce {
    min-height: 414px;
  }
  .el-form-item__label {
    display: none;
  }
  .form-content-wrapper {
    padding: 0px;
  }
  .el-dialog__body {
    padding: 0px 16px;
  }
  .el-form-item {
    margin-bottom: 0px;
  }
}
</style>
