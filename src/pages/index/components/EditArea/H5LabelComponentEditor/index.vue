<template>
  <div>
    <base-properties :currentComponents="currentComponents" />
    <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
    <el-button :disabled="currentComponents.length > 1" type="primary" size="small" class="add-option" @click="handleClick"> 编辑富文本 </el-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import BaseProperties from "../BaseProperties/index.vue";
import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";
import { SpecialComponentSubTypes } from "@/common/constants";
import ExtraEditor from "../ExtraEditor/index.vue";

@Component({
  components: {
    BaseProperties,
    ExtraEditor,
  },
})
export default class H5LabelComponentEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  handleClick() {
    console.log("edit");
    compManagerUtil.editComponent(SpecialComponentSubTypes.H5LABEl);
  }

  /**
   * 当富文本的内容的图片也是空的时候，则认为这是一个无效的组件，留在题目中会对真正有效的组件造成干扰
   */
  isValidComp() {
    if (!this.currentComponents[0].properties.labelPicList.length) {
      return false;
    }
    return true;
  }
}
</script>

<style scoped lang="less">
.add-option {
  width: 100px;
  font-size: 12px;
  margin: 10px 0;
  font-weight: 400;
  line-height: 12px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
}
.formula-editor-dialog {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
  .formula-editor-relative {
    position: relative;
  }
  .formula-editor-close {
    span {
      position: absolute;
      font-size: 60px;
      font-weight: 700;
      color: #fff;
      top: 0;
      right: -48px;
      cursor: pointer;
      font-family: "Symbola,Times New Roman,PingFang-Medium";
    }
  }
  .formula-iframe {
    height: 70vh;
    width: 50vw;
    min-height: 400px;
    min-width: 700px;
    background-color: #fff;
  }
}
</style>

<style lang="less">
.h5label-form-dialog {
  height: 540px;
  .dialog-footer {
    margin-top: 16px;
  }
  .tox-tinymce {
    min-height: 414px;
  }
  .el-form-item__label {
    display: none;
  }
  .form-content-wrapper {
    padding: 0px;
  }
  .el-dialog__body {
    padding: 0px 16px;
  }
  .el-form-item {
    margin-bottom: 0px;
  }
}
</style>
