<template>
  <div class="container">
    <el-button-group class="btn-group">
      <el-button
        icon="el-iconalign-left element-icon"
        size="small"
        :class="{ active: horizontal === HorizontalAlign.LEFT }"
        @click="updateHorizontalAlign(HorizontalAlign.LEFT)"
      ></el-button>
      <el-button
        icon="el-iconalign-center element-icon"
        size="small"
        :class="{ active: horizontal === HorizontalAlign.CENTER }"
        @click="updateHorizontalAlign(HorizontalAlign.CENTER)"
      ></el-button>
      <el-button
        icon="el-iconalign-right element-icon"
        size="small"
        :class="{ active: horizontal === HorizontalAlign.RIGHT }"
        @click="updateHorizontalAlign(HorizontalAlign.RIGHT)"
      ></el-button>
    </el-button-group>
    <el-button-group class="btn-group">
      <el-button
        icon="el-iconalign-top element-icon"
        size="small"
        :class="{ active: vertical === VerticalAlign.TOP }"
        @click="updateVerticalAlign(VerticalAlign.TOP)"
      ></el-button>
      <el-button
        icon="el-iconalign-vertically element-icon"
        size="small"
        :class="{ active: vertical === VerticalAlign.CENTER }"
        @click="updateVerticalAlign(VerticalAlign.CENTER)"
      ></el-button>
      <el-button
        icon="el-iconalign-bottom element-icon"
        size="small"
        :class="{ active: vertical === VerticalAlign.BOTTOM }"
        @click="updateVerticalAlign(VerticalAlign.BOTTOM)"
      ></el-button>
    </el-button-group>
  </div>
</template>
<!-- <align-editor @change="handleAlign" :horizontal="shapeData.textAlign" :vertical="shapeData.tbAlign"/> -->

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

enum VerticalAlign {
  TOP = 'flex-start',
  CENTER = 'center',
  BOTTOM = 'flex-end',
}

enum HorizontalAlign {
  LEFT = 'left',
  CENTER = 'center',
  RIGHT = 'right',
}

@Component
export default class LevelEditor extends Vue {
  @Prop({
    required: true,
  })
  horizontal!: HorizontalAlign;
  @Prop({
    required: true,
  })
  vertical!: VerticalAlign;

  VerticalAlign = VerticalAlign;
  HorizontalAlign = HorizontalAlign;

  updateHorizontalAlign(type: any) {
    this.$emit("change", { type: 'horizontal', value: type })
  }

  updateVerticalAlign(type: any) {
    this.$emit("change", { type: 'vertical', value: type })
  }
}
</script>

<style scoped lang="less">
.container {
  margin-bottom: 10px;

  .btn-group {
    display: flex;

    &:not(:last-of-type) {
      margin-bottom: 10px;
    }

    .el-button {
      flex-grow: 1;
    }
  }
  .active {
    background: #e6e8eb;
  }
}
</style>
