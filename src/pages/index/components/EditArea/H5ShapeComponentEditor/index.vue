<template>
    <el-collapse :value="collapseValue" v-if="currentComponents.length">
      <base-properties :currentComponents="currentComponents" v-if="currentComponents.length === 1"/>
      <extra-editor v-if="currentComponents.length === 1" :component="currentComponents[0]" />
      <!-- 编辑形状 -->
      <el-button :disabled="currentComponents.length > 1" type="primary" size="small" class="add-option" @click="handleEdit"> 编辑形状 </el-button>
    </el-collapse>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
  },
})
export default class ShapeComponentEditor extends Vue {
  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  @Watch("componentIds")
  onComponentIdsChange(val: string[], oldVal: string[]) {
    console.log("onComponentIdsChange", val, oldVal);
    this.$emit('destroy');
  }

  collapseValue = [
    "stage-properties",
    "base-properties",
    "label-properties",
    "sprite-properties",
    "shape-text-properties",
    "formula-properties",
    "spine-properties",
    "extra-properties",
    "cutShape-properties",
    "special-component-properties",
    "realia-component-properties",
    "shape-component-properties"
  ];

  componentEditorLoading = false;

  async handleEdit() {
    compManagerUtil.editComponent('shape');
  }
  mounted() {
    console.log('shape form mounted');
  }
}
</script>

<style scoped lang="less">
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.add-option {
  width: 100px;
  font-size: 12px;
  margin: 10px 0;
  font-weight: 400;
  line-height: 12px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    font-size: 13px;
  }
  .border-color {
    margin-bottom: 10px;
    margin-left: 10px;
  }
}
</style>
