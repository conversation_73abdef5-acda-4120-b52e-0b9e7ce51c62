<template>
  <div class="container">
    <span class="label">{{ label }}</span>
    <el-slider
      v-model="value"
      v-bind="options"
    />
    <el-input
      v-model="value"
      @input="validate($event)"
      @change="onChange"
      stlye="width: auto;"
      size="small"
      type="number"
      v-bind="options"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { Message } from "element-ui";

@Component({
  components: {
  },
})
export default class Input extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    required: false,
  })
  value!: number;

  @Prop({
    default: () => ({}),
  })
  options!: any;

  userInput = null;

  validate(val: any) {
    //正整数
    console.log("validate", val);

    this.userInput = val;
  }
  onChange(val: string) {
    if (this.options.max && val > this.options.max) {
      val = this.options.max;
      Message.error("限制输入为最大值");
    }
    if (this.options.min && val < this.options.min) {
      val = this.options.min;
      Message.error("限制输入为最小值");
    }
    this.$emit("input", val);
    this.userInput = null;
  }
}
</script>

<style scoped lang="less">
.container {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
  }

  .el-slider {
    flex-grow: 1;
    padding: 0 15px;
  }

  .el-input {
    width: auto;
  }
}
</style>
