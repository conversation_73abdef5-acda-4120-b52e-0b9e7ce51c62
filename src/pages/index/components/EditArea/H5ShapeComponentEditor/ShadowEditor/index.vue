<template>
  <div class="container">
    <span class="label" :class="option.required ? 'required' : ''" v-if="label">{{ label }}</span>
    <el-popover
      placement="bottom"
      width="200"
      trigger="click"
      @show="
        () => {
          hide = false;
        }
      "
      @hide="
        () => {
          hide = true;
        }
      "
    >
      <div class="shadow-tooltip-flex">
        <template v-if="showTip">
          <el-tooltip class="item" effect="dark" :content="item.label" placement="top-start" v-for="item in optionsList[type]" :key="item.value">
          <el-button @click.stop="handleClick(item.type)" style="margin-bottom: 16px;" size="small">
            <template v-if="item.value">
              <svgicon :name="item.value" :ref="item" width="32" height="32" original />
            </template>
            <template v-else>无</template>
          </el-button>
        </el-tooltip>
        </template>
        <template v-else>
          <el-button v-for="item in optionsList[type]" :key="item.value" @click.stop="handleClick(item.type)" style="margin-bottom: 16px;" size="small">
            <template v-if="item.value">
              <svgicon :name="item.value" :ref="item" width="32" height="32" original />
            </template>
            <template v-else>无</template>
          </el-button>
        </template>
        
      </div>
      <el-button slot="reference" size="small" class="action-button">
        <template v-if="activeItem.value">
          <svgicon :name="activeItem.value" :ref="activeItem" width="32" height="32" original />
        </template>
        <template v-else>无</template>
        <i class="el-icon-arrow-down" :class="{ reverse: hide }"></i>
      </el-button>

    </el-popover>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component({})
export default class ShadowEditor extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: () => ({}),
  })
  option!: any;

  @Prop({
    required: true,
  })
  value!: string;

  @Prop({
    default: "shadow",
  })
  type!: "shadow" | "arrow";

  @Prop({
    default: true,
  })
  showTip!: boolean;

  hide = true;

  optionsList = {
    shadow: [
      {
        label: "无",
        value: "",
        type: "",
      },
      {
        label: "右下",
        value: "shadow/shadow-right-bottom",
        type: "5px 5px 5px",
      },
      {
        label: "下",
        value: "shadow/shadow-bottom",
        type: "0px 5px 5px",
      },
      {
        label: "左下",
        value: "shadow/shadow-left-bottom",
        type: "-5px 5px 5px",
      },
      {
        label: "右",
        value: "shadow/shadow-right",
        type: "5px 0px 5px",
      },
      {
        label: "左",
        value: "shadow/shadow-left",
        type: "-5px 0px 5px",
      },
      {
        label: "右上",
        value: "shadow/shadow-right-top",
        type: "5px -5px 5px",
      },
      {
        label: "上",
        value: "shadow/shadow-top",
        type: "0px -5px 5px",
      },
      {
        label: "左上",
        value: "shadow/shadow-left-top",
        type: "-5px -5px 5px",
      },
    ],
    // arrow?: '-' | '-arrow' | 'arrow-' | 'arrow-arrow' | '-triangle' | 'triangle-' | 'triangle-triangle' | 'diamond-triangle' | 'circle-triangle' | 'diamond-diamond' | 'circle-circle' 
    // arrow-zhixian  arrow-youjiantou-shixin  arrow-zuojiantou-shixin  arrow-shuangjiantou 
    arrow: [
      {
        label: "",
        value: "shapeEditor/arrow-zhixian",
        type: "-",
      },
      {
        label: "",
        value: "shapeEditor/arrow-youjiantou",
        type: "-arrow",
      },
      {
        label: "",
        value: "shapeEditor/arrow-zuojiantou",
        type: "arrow-",
      },
      {
        label: "",
        value: "shapeEditor/arrow-shuangjiantou",
        type: "arrow-arrow",
      },
      {
        label: "",
        value: "shapeEditor/arrow-youjiantou-shixin",
        type: "-triangle",
      },
      {
        label: "",
        value: "shapeEditor/arrow-zuojiantou-shixin",
        type: "triangle-",
      },
      {
        label: "",
        value: "shapeEditor/arrow-shuangjiantou-shixin",
        type: "triangle-triangle",
      },
      {
        label: "",
        value: "shapeEditor/arrow-lingxing-jiantou",
        type: "diamond-triangle",
      },
      {
        label: "",
        value: "shapeEditor/arrow-yuan-jiantou",
        type: "circle-triangle",
      },
      {
        label: "",
        value: "shapeEditor/arrow-shuanglingxing",
        type: "diamond-diamond",
      },
      {
        label: "",
        value: "shapeEditor/arrow-shuangyuan",
        type: "circle-circle",
      },
    ],
    // ['','','border/border-dotted','','','','','',]
    border: [
      {
        label: "无",
        value: "",
        type: "",
      },
      {
        label: "",
        value: "border/border-solid",
        type: "solid",
      },
      {
        label: "",
        value: "border/border-dotted",
        type: "dotted",
      },
      {
        label: "",
        value: "border/border-dashed-1",
        type: "dashed",
      },
      {
        label: "",
        value: "border/border-double",
        type: "double",
      },
      {
        label: "",
        value: "border/border-groove",
        type: "groove",
      },
      {
        label: "",
        value: "border/border-ridge",
        type: "ridge",
      },
      {
        label: "",
        value: "border/border-insert",
        type: "triangle-",
      },
      {
        label: "",
        value: "border/border-outset",
        type: "triangle-triangle",
      }
    ],
    // InsertOrderedList InsertUnorderedList
    ul: [
      {
        label: "无",
        value: "",
        type: "false",
      },
      {
        label: "无",
        value: "style/list-square",
        type: "check",
      },
      {
        label: "circle",
        value: "style/list-circle",
        type: "disc",
      },
      {
        label: "right",
        value: "style/list-right",
        type: "duigou",
      },
      {
        label: "rect",
        value: "style/list-rect",
        type: "square",
      },
      {
        label: "arrow",
        value: "style/list-arrow",
        type: "arrow",
      },
      {
        label: "diamond",
        value: "style/list-diamond",
        type: "diamond",
      },
      {
        label: "dot",
        value: "style/list-dot",
        type: "subCircle",
      }
      
    ],
    ol: [
      {
        label: "无",
        value: "",
        type: 'false',
      },
      {
        label: "decimal",
        value: "style/list-decimal",
        type: "decimal",
      },
      {
        label: "decimal01",
        value: "style/list-decimal01",
        type: "number-right",
      },
      {
        label: "circlenumber",
        value: "style/list-lower-roman",
        type: "lower-roman",
      },
      {
        label: "upperroman",
        value: "style/list-upper-roman",
        type: "upper-roman",
      },
      {
        label: "lowerlatin",
        value: "style/list-lower-latin",
        type: "lower-alpha",
      },
      {
        label: "upperlatin",
        value: "style/list-upper-latin",
        type: "upper-alpha",
      },
    ]
  };
  get activeItem() {
    return this.optionsList[this.type].find((item: any) => item.type === this.value) || this.optionsList[this.type][0];
  }

  handleClick(type: string) {
    // this.v = type;
    this.$emit("input", type);
    this.hide = true;
  }
}
</script>

<style scoped lang="less">
.container {
  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    margin-right: 5px;
  }
  margin-bottom: 10px;

  .btn-group {
    display: flex;

    &:not(:last-of-type) {
      margin-bottom: 10px;
    }

    .el-button {
      flex-grow: 1;
    }
  }
  .el-icon-arrow-down {
    transform: rotateZ(180deg);
    transition: all 0.3s;
    &.reverse {
      transform: rotateZ(0deg);
    }
  }
}
</style>

<style lang="less">
.shadow-tooltip-flex {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
  .el-button {
    margin-top: 0px;
    margin-bottom: 0px !important;
    margin-left: 0px !important;
    width: 44px;
    padding: 0 8px;
    height: 32px !important;
  }
  .action-button {
    height: 28px;
    padding: 0 8px;
    span {
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-between;
      align-items: center;
    }
  }

}
</style>
