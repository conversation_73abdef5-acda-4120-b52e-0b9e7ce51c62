<template>
  <div class="shape-wrapper">
    <ShapeToolbar :visible="visible" :max-shap-count="1" @changeVisible="changeVisible" />
    <div class="shape-dialog-wrapper" v-show="showEditor" v-if="renderShapeCanvas">
      <div class="shape-dialog" :style="nativeStyle">
        <div class="shape-dialog-title"></div>
        <div class="shape-editor-wrapper">
          <ShapeCanvas ref="shapeCanvasRef" :page-data="editShapeData" @hook:mounted="mountedShapeEditor" />
        </div>
        <div class="shape-dialog-foot">
        </div>
      </div>
      <div class="action-wrapper">
        <el-button size="small" plain @click.stop="handleCancel" :disabled="confirming">取消</el-button>
        <el-button size="small" type="success" plain @click="handleConfirm" :loading="confirming">确定</el-button>
      </div>
      <div class="shape-form-wrapper">
        <el-collapse>
          <ShapeFormEditor :currentComponents="currentComponents" />
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import bus from "@/pages/index/common/utils/bus";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import { ShapeCanvas, ShapeToolbar, ShapeEditorEvent, ShapeEditorBus } from "zyb-shape-editor";
import "zyb-shape-editor/dist/style.css";
import domtoimage from "@/components/zyb-color-picker/colorPipette/dom-to-image.js";
import { blobToUrl } from "@/common/utils/uploadBlob";

import { ShapeEventType } from "@/pages/index/components/EditArea/H5ShapeComponentEditor/index";
import { cloneDeep } from "lodash-es";
import ShapeFormEditor from "@/pages/index/components/EditArea/H5ShapeComponentEditor/ShapeFormEditor.vue";

export enum ShapeStatus {
  Default = 0,
  Create = 1,
  Edit = 2,
}

@Component({
  components: {
    ShapeToolbar,
    ShapeCanvas,
    ShapeFormEditor,
  },
})
export default class ShapeComponentLibrary extends Vue {
  @Prop({
    required: true,
  })
  status!: ShapeStatus;

  visible = false;
  showEditor = false;
  confirming = false;

  boundingClientRect = {
    width: 500,
    height: 500,
    left: 100,
    top: 100,
  };
  editShapeData: any = {
    widgetCanDrag: true,
    widgetCanResize: true,
    widgetCanRotate: false,
    maxWidgetCount: 1,
    addListenerGlobal: false,
  };
  defaultShapeData: any = {
    widgetCanDrag: true,
    widgetCanResize: true,
    widgetCanRotate: false,
    maxWidgetCount: 1,
    addListenerGlobal: false,
  };
  get scale() {
    return this.containerWidth / 1280;
  }
  get nativeStyle() {
    return {
      width: `${1280 * this.scale}px`,
      height: `${960 * this.scale}px`,
      left: `${this.boundingClientRect.left}px`,
      top: `${this.boundingClientRect.top}px`,
    };
  }
  get containerStyle() {
    return {
      width: `${this.boundingClientRect.width}px`,
      height: `${this.boundingClientRect.height}px`,
      left: `${this.boundingClientRect.left}px`,
      top: `${this.boundingClientRect.top}px`,
    };
  }
  get containerWidth() {
    return this.$store.state.containerWidth;
  }
  get containerHeight() {
    return this.$store.state.containerHeight;
  }
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }
  get renderShapeCanvas() {
    return this.visible || this.editShapeData.id || this.editShapeData.type === "custom_line";
  }
  get currentComponents() {
    return this.$store.getters.currentComponents;
  }
  get shapeData() {
    if (this.currentComponents[0] && this.currentComponents[0].type === "shape") {
      return this.currentComponents[0].properties.shapeData;
    }
    return { id: null };
  }

  @Watch("shapeData.id")
  onShapeDataChange(val: any, oldVal: any) {
    console.log("onShapeDataChange", val, oldVal);
    // 形状组件切换
    if (val && oldVal && val !== oldVal) {
      bus.$emit(ShapeEventType.EndEditShape);
    }
    // 形状切别的组件
    if (!val) {
      bus.$emit(ShapeEventType.EndEditShape);
    }
  }

  @Watch("scale")
  onScaleChange() {
    const canvasRect = (document.querySelector("#Cocos2dGameContainer") as any).getBoundingClientRect();
    this.boundingClientRect = canvasRect;
  }

  @Watch("showEditor")
  onShowEditorChange(val: any, oldVal: any) {
    console.log("onShowEditorChange", val, oldVal);
    if (val) {
      this.$store.commit("updateComponentProperties", {
        id: this.componentIds[0],
        newProperties: {
          active: false,
          // active: true,
        },
        ignoreHistory: true, // 有该字段 不会记录到历史记录中
      });
    }
  }

  created() {
    console.log("created..shape");
    const canvasRect = (document.querySelector("#Cocos2dGameContainer") as any).getBoundingClientRect();
    this.boundingClientRect = canvasRect;
    if (this.status === ShapeStatus.Create) {
      this.handleOpenShapeSelector();
    }
    if (this.status === ShapeStatus.Edit) {
      this.handleEditShape();
    }
  }

  handleEditShape() {
    this.visible = false;
    if (!this.currentComponents || !this.currentComponents[0]) return;
    const temp = cloneDeep(this.currentComponents[0].properties.shapeData);
    const sizeData = this.getShapeSizeFromCocos(this.currentComponents[0].properties);
    temp.detail[0].props = {
      ...temp.detail[0].props,
      ...sizeData,
    };
    this.editShapeData = temp;
    this.editShapeData.width = 1280 * 1;
    this.editShapeData.height = 720 * 1;
    // 修改shape props detail[0].props
    this.showEditor = true;
  }

  handleOpenShapeSelector() {
    this.visible = true;
    this.showEditor = false;
    this.editShapeData = cloneDeep(this.defaultShapeData);
    this.editShapeData.width = 1280 * 1;
    this.editShapeData.height = 720 * 1;
  }

  changeVisible(val: boolean) {
    this.visible = val;
    // 如果是选择了形状后关闭 则this.showEditor为true;所以关闭形状选择且未进入编辑模式视为点击空白处触发的取消 需要出发cancel事件 否则形状组件不会销毁
    if(!val && !this.showEditor) {
      // 选择形状弹窗关闭，且没有进入编辑模式
      this.$emit("cancel");
    }
  }

  updateShape(data: any) {
    console.log("shape==> 更新shape属性", data);
    ShapeEditorBus.$emit(ShapeEditorEvent.WIDGET_PROP_CHANGE, data);
  }

  startEditShape(data: any) {
    console.log("shape==> startEditShape: 进入形状编辑模式", data);
    this.editShapeData = data;
    this.showEditor = true;
  }
  openShapeSelect() {
    console.log("shape==> openShapeSelect: 进入形状编辑模式");
    this.handleOpenShapeSelector();
  }

  mounted() {
    ShapeEditorBus.$on(ShapeEditorEvent.ACTIVE_WIDGET_CHANGE, this.widgetSelected);
    ShapeEditorBus.$on(ShapeEditorEvent.ADD_WIDGET, this.addWidget);
    bus.$on("updateShape", this.updateShape);
    bus.$on("startEditShape", this.startEditShape);
    bus.$on("openShapeSelect", this.openShapeSelect);
    this.$once("hook:beforeDestroy", () => {
      ShapeEditorBus.$off(ShapeEditorEvent.ACTIVE_WIDGET_CHANGE, this.widgetSelected);
      bus.$off("updateShape", this.updateShape);
      bus.$off("startEditShape", this.startEditShape);
      bus.$off("openShapeSelect", this.openShapeSelect);
      ShapeEditorBus.$off(ShapeEditorEvent.ADD_WIDGET, this.addWidget);
    });
  }

  async cancelShapeEditStatus() {
    // 如果图形处于编辑状态 需要恢复到非编辑状态
    const editShapeDom = (document.querySelector(".svg-shape-edit-area") as any) || (document.querySelector(".svg-line-edit-area") as any);
    console.log("editShapeDom", editShapeDom);
    if (editShapeDom) {
      editShapeDom.click();
      const myEventToDispatch = new MouseEvent("mousedown", {
        bubbles: true, // 表示该事件是否应该冒泡。默认为 false。
        cancelable: true, // 表示该事件是否可以取消。默认为 false。
        view: window, // 与事件关联的视图。默认为当前窗口。
      }); //or "mousedown",whichever you need
      editShapeDom.dispatchEvent(myEventToDispatch);
    } else {
      return true;
    }
    await this.$nextTick();
    return true;
  }

  async handleConfirm() {
    await this.cancelShapeEditStatus();
    this.confirming = true;
    const widgetDom = document.querySelector(".vdrr") as any;
    // 线条和空的编辑形状无widgetDom， 需要特殊处理
    if (widgetDom) {
      // 确定时 文字必须取消选中和失去焦点
      const textEditorDom = document.querySelector(".ql-container") as any;
      if(textEditorDom && textEditorDom.style.display === 'block') {
        console.log('active_widget', widgetDom.id);
        ShapeEditorBus.$emit("active_widget", { active: false, widgetId: widgetDom.id });

        await Promise.resolve(() => {
          return new Promise(resolve => {
            setTimeout(() => {
              resolve(true);
            }, 500);
          });
        });
      }
    }

    // 线条的编辑按钮要隐藏
    const lineControlDom = document.querySelector(".control-area") as any;
    if (lineControlDom) {
      lineControlDom.style.display = "none";
    }
    console.log("handleConfirm", (this.$refs.shapeCanvasRef as any).toJSON());
    const shapeData = cloneDeep((this.$refs.shapeCanvasRef as any).toJSON());
    // 空的编辑形状 直接关闭
    if (!shapeData.detail[0]) {
      this.showEditor = false;
      ShapeEditorBus.$emit(ShapeEditorEvent.DELETE_WIDGET);
      bus.$emit(ShapeEventType.EndEditShape);
      this.deleteTempShape();
      this.confirming = false;
      this.$emit("cancel");
      return;
    }

    const dom = document.querySelector(".vdrr-content") as any;
    const textDom = document.querySelector(".ql-editor div") as any;
    const svgDom = document.querySelector(".vdrr-content svg") as any;
    const h = widgetDom.offsetHeight;
    const w = widgetDom.offsetWidth;
    const textHeight = textDom.scrollHeight || 0;
    const gap = 10;
    widgetDom.style.width = `${w + 2 * gap}px`;
    widgetDom.style.height = `${h + 2 * gap}px`;
    widgetDom.style.left = `${widgetDom.offsetLeft - gap}px`;
    widgetDom.style.top = `${widgetDom.offsetTop - gap}px`;
    dom.style.width = `${w}px`;
    dom.style.height = `${h}px`;
    dom.style.padding = `${gap}px`;
    const options = {
      bgcolor: "transparent",
      style: {
        transform: "rotate(0deg)",
      },
    };
    if(textDom) {
      textDom.style.margin = `0 ${gap}px 0 ${gap}px`;
      textDom.style.width = `${w}px`;
    }
    if (textHeight > h) {
      svgDom.style.width = `${w}px`;
      (options.style as any).marginTop = `${(textHeight - dom.offsetHeight) / 2}px`;
      (options as any).height = Math.max(textHeight);
      // console.log("textHeight", textHeight, "h", h, `${(textHeight - dom.offsetHeight) / 2}px`);
    }

    // 确定是 有概率一直loading 需要处理容错 shape xuchunhong
    // console.log("...options", options, textDom, textHeight);
    const res = await domtoimage.toBlob(dom as Element, options);
    const url: string = await blobToUrl(res, "img.png");
    // console.log("shape => shape url：", url);
    // window.open(url);
    // return;

    // const shapeData = cloneDeep((this.$refs.shapeCanvasRef as any).toJSON());
    const { props } = shapeData.detail[0];
    // 添加textHeight
    if (textHeight > h) {
      props.textHeight = textHeight;
      // console.log("set textHeight", props.textHeight);
    } else {
      props.textHeight = 0;
    }

    if (props) {
      if (props.shadowObj) {
        // 阴影的位置和文字 字段设置和存储不一致，需要特殊处理
        props.shadowPosition = props.shadowObj.position;
        props.shadowColor = props.shadowObj.color;
      }
      // 文字阴影+1 fontShadowPosition fontShadowColor
      if (props.shadow) {
        const [fontShadowColor, ...rest] = props.shadow.split(" ");
        props.fontShadowColor = fontShadowColor;
        props.fontShadowPosition = rest.join(" ");
      }
    }
    console.log("shape => ..shapeData", shapeData, this.getShapeSize(shapeData.detail[0].props));
    const { x, y, ...rest } = this.getShapeSize(shapeData.detail[0].props);
    this.$store.commit("updateComponentProperties", {
      id: this.componentIds[0],
      newProperties: {
        ...rest,
        shapeData,
        texture: url,
        active: true,
      },
    });
    this.$store.commit("updateComponentProperties", {
      id: this.componentIds[0],
      newProperties: {
        x,
        y,
      },
    });
    this.showEditor = false;
    ShapeEditorBus.$emit(ShapeEditorEvent.DELETE_WIDGET);
    bus.$emit(ShapeEventType.EndEditShape);
    this.confirming = false;
    this.$emit("done", { shapeData, url });
  }
  // 数字 只保留两位小数
  fixedNumber(num: number) {
    return Math.floor(num * 100) / 100;
  }

  getShapeSize(props: any) {
    const { width = 80, height = 80, left = 640, top = 360, rotate = 0, textHeight = 0 } = props;
    // console.log("...getShapeSize", width, textHeight, height, left, top, rotate, "scale", this.scale, "gap", textHeight);
    // const { scale } = this;
    const gap = 10;
    const topGap = 120; // 形状的渲染区域在4/3的画布中，所以需要加上顶部的高度
    const x = this.fixedNumber((left - gap) / 1);
    const y = this.fixedNumber(textHeight ? (top - (textHeight - height) / 2) / 1 + topGap : (top - gap) / 1 + topGap);
    let selfX = 0;
    let selfY = 0;
    if (this.componentIds[0]) {
      const { x: tempX, y: tempY } = (window as any).cocos.convertEditorWorldPos(this.componentIds[0], { x, y });
      selfX = tempX;
      selfY = tempY;
    }
    const size = {
      width: this.fixedNumber((width + 2 * gap) / 1),
      height: this.fixedNumber(Math.max(textHeight, height + 2 * gap) / 1),
      // x: x,
      // y: y,
      x: this.componentIds[0] ? selfX : x,
      y: this.componentIds[0] ? selfY : y,
      shapeScale: this.scale,
      shapeTop: 0,
      shapeLeft: 0,
    };
    size.shapeTop = this.fixedNumber((size.height - height) / 2 / size.height);
    size.shapeLeft = this.fixedNumber((size.width - width) / 2 / size.width);
    return size;
  }

  getShapeSizeFromCocos(props: any) {
    const { width = 80, height = 80, shapeTop = 0, shapeLeft = 0, texture = "" } = props;
    const { x: cocosX, y: cocosY, width: cocosW, height: cocosH } = (window as any).cocos.getRotateEWordRect(this.componentIds[0]);
    const topGap = 120;

    const w = this.fixedNumber(width * (1 - shapeLeft * 2));
    const h = this.fixedNumber(height * (1 - shapeTop * 2));

    const size = {
      width: w,
      height: h,
      // left: this.fixedNumber((cocosX + this.fixedNumber(shapeLeft * width)) / 1 + (cocosW - w) / 2),
      // top: this.fixedNumber(cocosY + this.fixedNumber(shapeTop * height) - topGap + (cocosH - h) / 2),
      left: this.fixedNumber(cocosX + (cocosW - w) / 2),
      top: this.fixedNumber(cocosY - topGap + (cocosH - h) / 2),
    };
    // console.log("size.getShapeSizeFromCocos", size, texture);
    if (!texture) return {};
    return size;
  }

  deleteTempShape() {
    // 如果是新增的组件，则删除这个组件
    if (this.currentComponents[0].type === "shape" && !this.currentComponents[0].properties.texture) {
      this.$store.dispatch("removeComponent", this.currentComponents[0].id);
    } else {
      this.$store.commit("updateComponentProperties", {
        id: this.componentIds[0],
        newProperties: {
          active: true,
        },
      });
    }
  }

  handleCancel() {
    console.log("取消形状编辑");
    this.showEditor = false;
    bus.$emit(ShapeEventType.EndEditShape);
    this.deleteTempShape();
    this.$emit("cancel");
  }

  mountedShapeEditor() {
    console.log("mountedShapeEditor", this.editShapeData.id);
    if (!this.editShapeData.id) {
      console.log("新增组件，没有id");
      // 展示编辑形状表单
      bus.$emit(ShapeEventType.AddShape);
      return;
    }
    this.$nextTick(() => {
      // 编辑的时候没有选中组件 需要手动选中
      ShapeEditorBus.$emit((ShapeEditorEvent as any).ACTIVE_WIDGET, { active: true, widgetId: this.editShapeData.detail[0].id });
    });
  }

  getCustomLineData() {
    return {
      name: "page",
      id: `page-${Date.now()}-1`,
      type: "custom_line",
      width: 780,
      height: 438.75,
      background: "",
      remark: "",
      animation: "",
      others: {},
      themeDetailId: 0,
      lockPage: 0,
      detail: [{
        type: "custom_line",
        props: {
          stroke: "#37a2b8",
          strokeWidth: 1,
          strokeDasharray: "",
          shadowColor: "#666666",
          fontShadowColor: "#666666",
        }
      }],
      maxWidgetCount: 1,
      widgetCanDrag: true,
      widgetCanResize: true,
      widgetCanRotate: false,
      addListenerGlobal: false,
    };
  }

  addWidget(shape: any, type: any) {
    console.log("..addWidget", shape, type);
    if (type.type === "custom_line") {
      this.showEditor = true;
      const temp = this.getCustomLineData();
      temp.detail.push(shape as never);
      this.editShapeData = temp;
      this.$nextTick(() => {
        const temp = (this.$refs.shapeCanvasRef as any).toJSON();
        if (!temp.detail[0]) {
          const props = {
            stroke: "#37a2b8",
            strokeWidth: 1,
            strokeDasharray: "",
            shadowColor: "#666666",
            fontShadowColor: "#666666",
          }
          temp.detail.push({
            type: "custom_line",
            props: props,
          });
          this.updateShape({
            strokeWidth: props.strokeWidth,
            strokeDasharray: props.strokeDasharray,
            stroke: props.stroke,
            shadowColor: props.shadowColor,
          })
        }
        this.editShapeData = temp;
        this.addShape(this.editShapeData);
        const timer = setTimeout(() => {
          clearTimeout(timer);
          // 展示编辑形状表单
          bus.$emit(ShapeEventType.StartEditShape, this.editShapeData);
        }, 500);
        
      });
    }
  }

  refreshShapeData(temp: any) {
    if (temp.detail[0] && temp.detail[0].props) {
      const { props } = temp.detail[0]
      if(props.strokeDasharray === "none") {
        // 边框
        props.strokeWidth = 1;
        props.strokeDasharray = "";
        props.stroke = "#37a2b8";
      }
      // 阴影
      if(!props.shadowColor) {
        props.shadowColor = "#666666";
      }
      // 文字阴影
      if(!props.fontShadowColor) {
        props.fontShadowColor = "#666666";
      }
      
      this.updateShape({
        strokeWidth: props.strokeWidth,
        strokeDasharray: props.strokeDasharray,
        stroke: props.stroke,
        shadowColor: props.shadowColor,
        fontShadowColor: props.fontShadowColor,
      })
    }

      
      
      // shadowPosition shadowColor
      // 文字阴影
      // 处理新增组件的默认值
      // 形状 
      // 填充（#4fc4db） 边框（- 1 #37a2b8) 
      // 阴影 
      // 无 #666666
      // 文本 字号 32 颜色 文字阴影 无/#666666
      // 线条 
      // 图形阴影 无 #666666
      // 边框 - 1 #37a2b8
      // 自定义线条
      // 边框 - 1 #37a2b8
  }

  widgetSelected(active: boolean, widgetData: any) {
    if (this.confirming) return;
    // console.log("widgetSelected..", active, widgetData, this.visible, this.editShapeData);
    if (!Object.keys(widgetData).length) {
      // 始终保持组件的选中状态-不然的话修改表单的数据无效
      ShapeEditorBus.$emit((ShapeEditorEvent as any).ACTIVE_WIDGET, { active: true, widgetId: widgetData.id || this.editShapeData.detail[0].id });
      return;
    }
    // widgetData.props
    if (active) {
      this.showEditor = true;
    }
    if (this.visible) {
      this.visible = false;
      const temp = (this.$refs.shapeCanvasRef as any).toJSON();
      this.refreshShapeData(temp);
      this.editShapeData = temp;
      // 修改默认值
      // 新增组件
      this.addShape(this.editShapeData);
      const timer = setTimeout(() => {
        clearTimeout(timer);
        // 展示编辑形状表单
        bus.$emit(ShapeEventType.StartEditShape, this.editShapeData);
      }, 500);
    }
  }

  public addShape(shapeData: any = {}) {
    // console.log("addShape...", shapeData, this.getShapeSize(shapeData.detail[0].props));
    const shapeComponent: Omit<H5ShapeComponent, "id"> = {
      tag: "",
      type: "shape",
      dragable: true,
      properties: {
        active: false,
        // active: true,
        ...this.getShapeSize(shapeData.detail[0].props),
        shapeData,
        texture: "",
      },
    };
    this.$store.dispatch("addComponentAndFocus", shapeComponent);
  }
}
</script>

<style scoped lang="less">
.shape-content {
  width: 520px;
  min-height: 380px;
  display: flex;
  // display: flex !important;
  position: fixed;
  top: 0 !important;
  left: 50% !important;
  transform: translate(-50%, 52px);

  z-index: 9999;
  background: rgba(255, 255, 255, 1);
  box-shadow: 2px 2px 8px 0px rgba(131, 134, 143, 0.2);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
}
.shape-dialog-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 100;
  display: flex;
  flex-direction: column;
  .shape-dialog-title {
    height: 12.5%;
  }
  .shape-dialog-foot {
    height: 12.5%;
  }
  .action-wrapper {
    height: 60px;
    position: fixed;
    bottom: 0px;
    right: 16px;
    position: fixed;
    bottom: 0px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    z-index: 10;
    // background: red;
  }
}
.shape-form-wrapper {
  position: fixed;
  right: 0;
  width: 358px;
  top: 52px;
  bottom: 0;
  background: #fff;
  padding: 20px;
}
.shape-dialog {
  position: fixed;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  // background: rgba(0,0,0,0.3);

  .oc-editor {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
  }
  .oc-editor-zoom {
    flex: 1;
    height: 100%;
    .page {
      width: 100% !important;
      height: 100% !important;
    }
  }
  .shape-dialog-title {
    height: 12.5%;
  }
  .shape-editor-wrapper {
    position: relative;
    height: 75%;
    text-align: center;
  }
}
</style>

<style lang="less">
.dialog-fade-enter-active {
  animation: unset !important;
}
.dialog-fade-leave-active {
  animation: unset !important;
}
.action-wrapper {
  span {
    font-size: 14px !important;
  }
}
.shape-dialog {
  .oc-editor-zoom {
    height: 100% !important;
  }
  .editor-zoom-wrap {
    height: 100% !important;
  }

  .editor-zoom {
    background: unset !important;
  }
}
</style>
