<template>
  <div class="shape-form-editor">
    <!-- <el-collapse-item v-if="currentComponents.length === 1" title="形状属性" name="sprite-properties"> -->
    <template v-if="isLine">
      <div class="title">线条</div>
      <div class="form-item">
        <ShadowEditor label="图形阴影" v-model="shapeData.shadowPosition" @input="handleChange('shadowPosition')" />
        <colorPicker class="color-select border-color" label="" v-model="shapeData.shadowColor" :clearable="true" @change="handleChange('shadowColor')"></colorPicker>
        
      </div>
      <div class="form-item">
        <span class="label">边框</span>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.strokeDasharray" @change="handleChange('strokeDasharray')">
          <el-option class="border-type-option" v-for="item in lineTypeWidthOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.strokeWidth" @change="handleChange('strokeWidth')">
          <el-option :label="0" :value="0" v-if="shapeData.strokeDasharray === 'none'"> </el-option>
          <template v-else>
            <el-option v-for="item in lineWidthOptions.slice(1)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </template>
        </el-select>
        <colorPicker class="color-select" :clearable="true" label="" v-model="shapeData.stroke" :componentIds="currentComponentIds" @change="handleChange('stroke')"></colorPicker>
      </div>
      <div class="form-item">
        <ShadowEditor label="箭头" type="arrow" :showTip="false" v-model="shapeData.arrow" @input="handleChange('arrow')" />
      </div>
    </template>
    <template v-else>
      <div class="title">形状属性</div>
      <div class="form-item">
        <span class="label">填充</span>
        <colorPicker class="color-select" :clearable="true" v-model="shapeData.fill" @change="handleChange('fill')"></colorPicker>
      </div>
      <div class="form-item">
        <span class="label">边框</span>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.strokeDasharray" @change="handleChange('strokeDasharray')">
          <el-option class="border-type-option" v-for="item in lineTypeWidthOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.strokeWidth" @change="handleChange('strokeWidth')">
          <el-option :label="0" :value="0" v-if="shapeData.strokeDasharray === 'none'"> </el-option>
          <template v-else>
            <el-option v-for="item in lineWidthOptions.slice(1)" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </template>
        </el-select>
        <colorPicker class="color-select" :clearable="true" label="" v-model="shapeData.stroke" :componentIds="currentComponentIds" @change="handleChange('stroke')"></colorPicker>
      </div>
      <div class="form-item">
        <ShadowEditor label="阴影" v-model="shapeData.shadowPosition" @input="handleChange('shadowPosition')" />
        <colorPicker class="color-select border-color" :clearable="true" label="" v-model="shapeData.shadowColor" @change="handleChange('shadowColor')"></colorPicker>
      </div>
      <div class="title">文本属性</div>
      <div class="form-item">
        <span class="label">字号</span>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.fontSize" @change="handleChange('fontSize')">
          <el-option v-for="item in fontSizeOptions" :key="item.value" :label="item.value" :value="item.value"> </el-option>
        </el-select>
      </div>
      <div class="form-item">
        <span class="label">行间距</span>
        <el-select placeholder="请选择" class="form-content" size="small" v-model="shapeData.lineHeight" @change="handleChange('lineHeight')">
          <el-option v-for="item in lineHeightOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </div>
      <div class="form-item">
        <ShadowEditor label="文字阴影" v-model="shapeData.fontShadowPosition" @input="handleChange('fontShadowPosition')" />
        <colorPicker class="color-select border-color" :clearable="true" v-model="shapeData.fontShadowColor" @change="handleChange('fontShadowColor')"></colorPicker>
      </div>
      <el-row>
        <el-col :span="12">
          <div class="form-item">
            <span class="label">粗体</span>
            <el-switch v-model="shapeData.bold" @change="handleChange('bold')"></el-switch>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="form-item">
            <span class="label">斜体</span>
            <el-switch v-model="shapeData.italic" @change="handleChange('italic')"></el-switch>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="form-item">
            <span class="label">下划线</span>
            <el-switch v-model="shapeData.underline" @change="handleChange('underline')"></el-switch>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="form-item">
            <span class="label">文字颜色</span>
            <colorPicker class="color-select" :clearable="true" v-model="shapeData.color" @change="handleChange('color')"></colorPicker>
          </div>
        </el-col>
      </el-row>
      <align-editor @change="handleAlign" :horizontal="shapeData.textAlign" :vertical="shapeData.tbAlign" />
    </template>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import ExtraEditor from "../ExtraEditor/index.vue";
import BaseProperties from "../BaseProperties/index.vue";
import YaSelect from "../EditComponents/Select/index.vue";
import FlipEditor from "../BaseProperties/FlipEditor/index.vue";
import SliderAndInput from "./SliderAndInput/index.vue";
import YaSwitch from "../EditComponents/Switch/index.vue";
import AlignEditor from "./AlignEditor/index.vue";
import ShadowEditor from "./ShadowEditor/index.vue";
import color from "@/components/zyb-color-picker/ColorPicker.vue";
import { cloneDeep } from "lodash-es";
import bus from "@/pages/index/common/utils/bus";
import { ShapeEventType } from "./index";
// svg_zdyquxian 需要liujian同步如何使用

@Component({
  components: {
    ExtraEditor,
    BaseProperties,
    YaSelect,
    colorPicker: color,
    FlipEditor,
    SliderAndInput,
    YaSwitch,
    AlignEditor,
    ShadowEditor,
  },
})
export default class ShapeFormEditor extends Vue {
  @Prop({
    required: true,
  })
  currentComponents!: Components;

  isEdit = false;
  shapeData: H5ShapeProperties["shapeData"] = {};

  get shapeType() {
    if (this.currentComponents[0] && this.currentComponents[0].type === "shape") {
      return this.currentComponents[0].properties.shapeData.detail[0].type;
    }
    return "";
  }

  get isLine() {
    return ["line-zhixian", "line-djiantou", "line-sjiantou", "line-zhexian", "line-quxian", "svg-zdyquxian", "custom_line"].includes(this.shapeType);
  }

  lineWidthOptions = [
    { label: "0", value: 0 },
    { label: "1", value: 1 },
    { label: "2", value: 2 },
    { label: "3", value: 3 },
    { label: "4", value: 4 },
    { label: "5", value: 5 },
  ];

  lineTypeWidthOptions = [
    { label: "无", value: "none" },
    { label: "—————", value: "" },
    { label: "- - - - - -", value: "5" },
    { label: "...................", value: "1" },
  ];

  rowSpacingOptions = [
    { label: "0", value: 0 },
    { label: "2", value: 2 },
    { label: "4", value: 4 },
    { label: "6", value: 6 },
    { label: "8", value: 8 },
    { label: "10", value: 10 },
    { label: "12", value: 12 },
    { label: "14", value: 14 },
    { label: "16", value: 16 },
    { label: "18", value: 18 },
    { label: "20", value: 20 },
  ];

  lineHeightOptions = [
    { label: "1", value: 1 },
    { label: "1.2", value: 1.2 },
    { label: "1.4", value: 1.4 },
    { label: "1.5", value: 1.5 },
    { label: "1.6", value: 1.6 },
    { label: "1.8", value: 1.8 },
    { label: "2", value: 2 },
    { label: "2.5", value: 2.5 },
    { label: "3", value: 3 },
    { label: "3.5", value: 3.5 },
    { label: "4", value: 4 },
    { label: "5", value: 5 },
    { label: "6", value: 6 },
    { label: "7", value: 7 },
    { label: "8", value: 8 },
    { label: "9", value: 9 },
    { label: "10", value: 10 },
  ];

  fontSizeOptions = [
    {  value: 12 },
    { value: 14 },
    { value: 16 },
    { value: 18 },
    { value: 20 },
    { value: 22 },
    { value: 24 },
    { value: 28 },
    { value: 32 },
    { value: 36 },
    { value: 48 },
    { value: 64 },
    { value: 72 }
  ];
  get componentId() {
    return this.currentComponentIds[0];
  }

  get currentComponentIds() {
    return this.currentComponents.map(i => i.id);
  }

  updateProperties(key: string, val: any) {
    this.$store.commit("updateComponentProperties", {
      id: this.componentId,
      newProperties: {
        [key]: val,
      },
    });
  }

  handleEdit() {
    // 双击组件 this.isEdit = true; shape-todo
    this.isEdit = true;
    console.log("handleEdit", this.currentComponents[0].properties.shapeData);
    // this.shapeData = cloneDeep(this.currentComponents[0].properties.shapeData.props);
    this.$set(this, "shapeData", cloneDeep(this.currentComponents[0].properties.shapeData.detail[0].props));
    bus.$emit("startEditShape", cloneDeep(this.currentComponents[0].properties.shapeData));
    // 先保存当前组件的属性 点击取消的时候用于数据的复原
  }

  handleAlign({ type, value }: { type: string; value: string }) {
    console.log("handleAlign..type", type, value);
    // textAlign tbAlign
    if (type === "horizontal") {
      (this.shapeData.textAlign as any) = value;
      this.handleChange("textAlign");
    } else {
      (this.shapeData.tbAlign as any) = value;
      this.handleChange("tbAlign");
    }
  }

  handleChange(key: string) {
    // fill/color 如果填充色/文字颜色为'' 则设置为rgba(0,0,0,0), 否则会设置为默认色 于预期不符'
    if (['fill', 'color', 'shadowColor'].includes(key) && this.shapeData[key] === "") {
      this.shapeData[key] = "rgba(0,0,0,0)";
    }
    if (key === "strokeDasharray") {
      if (this.shapeData[key] === "none") {
        this.shapeData["strokeWidth"] = 0;
      } else if (this.shapeData["strokeWidth"] === 0) {
        this.shapeData["strokeWidth"] = 1;
      }

      bus.$emit("updateShape", {
        ["strokeWidth"]: this.shapeData["strokeWidth"],
      });
    }
    // fontShadowPosition 如果是阴影位置变更 强制修改一下阴影颜色
    if (key === "fontShadowPosition") {
      bus.$emit("updateShape", {
        ["fontShadowColor"]: this.shapeData["fontShadowColor"],
      });
    }
    bus.$emit("updateShape", {
      [key]: this.shapeData[key],
    });
    // fontShadowPosition 如果是阴影位置变更 强制修改一下阴影颜色
    if (key === "fontShadowPosition") {
      bus.$emit("updateShape", {
        ["fontShadowColor"]: this.shapeData["fontShadowColor"],
      });
    }
  }

  handleStartEditShape(data: any) {
    console.log("StartEditShape..", data);
    this.$set(this, "shapeData", cloneDeep(data.detail[0].props));
  }
  handleEndEditShape() {
    this.isEdit = false;
  }
  handleAddShape() {
    this.isEdit = true;
    console.log("ShapeEventType.AddShape");
  }

  created() {
    // 编辑的时候 还未渲染组件，监听不到bus
    if (this.currentComponents[0] && this.currentComponents[0].type === "shape") {
      this.$set(this, "shapeData", cloneDeep(this.currentComponents[0].properties.shapeData.detail[0].props));
    }
    bus.$on(ShapeEventType.StartEditShape, this.handleStartEditShape);
    bus.$on(ShapeEventType.EndEditShape, this.handleEndEditShape);
    bus.$on(ShapeEventType.AddShape, this.handleAddShape);
    this.$once("hook:beforeDestroy", () => {
      bus.$off(ShapeEventType.StartEditShape, this.handleStartEditShape);
      bus.$off(ShapeEventType.EndEditShape, this.handleEndEditShape);
      bus.$off(ShapeEventType.AddShape, this.handleAddShape);
    });
  }
}
</script>

<style scoped lang="less">
.shape-form-editor {
  height: calc(100vh - 140px);
  overflow-y: auto;
  .title {
    display: flex;
    align-items: center;
    height: 48px;
    line-height: 48px;
    background-color: #fff;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    font-size: 13px;
    font-weight: 500;
    transition: border-bottom-color 0.3s;
    outline: none;
    margin-bottom: 12px;
  }
}
.flex {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.add-option {
  width: 100px;
  font-size: 12px;
  margin: 10px 0;
  font-weight: 400;
  line-height: 12px;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-right: 10px;
    padding-bottom: 0px !important;
    width: 80px !important;
  }

  .el-input {
    margin-left: 5px;
    margin-right: 10px;
    flex-grow: 1;
  }
  .border-color {
    margin-bottom: 10px;
    margin-left: 10px;
  }
}
</style>

<style lang="less">
.shape-form-editor {
  .label {
    display: inline-block;
    text-align: left;
    min-width: 56px;
    flex-shrink: 0;
    font-size: 14px;
    margin-right: 5px;
  }
  .container .label {
    min-width: 56px;
  }
}
.border-type-option {
  width: 60px;
  overflow: hidden;
}
</style>
