import Vue from "vue";
import { CombinedVueInstance } from "vue/types/vue";
import store from "@/pages/index/store";
import FormulaModalFN from "./formulaModal.vue";

const FormulaModalConstructor = Vue.extend(FormulaModalFN);

type InstanceMethods = {
  onClose?: () => void;
  onConfirm?: (params: {
    url: string,
    width: number,
    height: number,
    latex: string
  }) => void;
  onGetEquationInfo?: (latexStr: string) => void;
};

type InstanceData = {
  showFormula: boolean;
  type: string;
  show: () => void;
  getCompLatex: () => void;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceData,
  InstanceMethods,
  object,
  Record<never, any>
>;

export const createInstance = (latex: string, type: OutputType) => {
  if (!instance) {
    instance = new FormulaModalConstructor({
      store,
      el: document.createElement("div"),
      propsData: {
        latex,
        type
      }
    });
  } else {
    (instance as any).$props.latex = latex;
    (instance as any).$props.type = type;
    (instance as any).latex = latex;
    // const timer = setTimeout(() => {
    //   clearTimeout(timer);
    //   (instance as any).changeFormula(latex);
    // }, 3000);
  }
  console.log('instance...', instance);
  return instance;
};

export const enum OutputType {
  SVG = 'svg',
  HTML='html'
}

export type FormulaOptions = {
  latex?: string;
  type?: OutputType;
} & InstanceMethods;

export const FormulaModal = ({
  onClose,
  onConfirm,
  onGetEquationInfo,
  latex,
  type
}: FormulaOptions) => {
  createInstance(latex || '', type || OutputType.SVG);
  instance.show();
  instance.onConfirm = onConfirm;
  instance.onGetEquationInfo = onGetEquationInfo;
  instance.onClose = onClose;
  document.body.appendChild(instance.$el);
  
};
export default FormulaModal;
