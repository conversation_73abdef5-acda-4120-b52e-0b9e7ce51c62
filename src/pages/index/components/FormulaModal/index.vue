<template>
  <el-dialog
    title="编辑公式"
    :visible="showFormula"
    top="0vh"
    v-loading="iframeLoading"
    element-loading-fullscreen="false"
    element-loading-text="加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.3)"
    @close="close"
    class="formula-editor-dialog">
    <iframe
      title="formula"
      name="formula"
      allow="clipboard-read; clipboard-write" id="formulaIframe"
      class="formula-iframe"
      ref="formulaIframe"
      :src="src"
      :onLoad="onLoad()"
    />
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import bus from "../../common/utils/bus";
import { image2Canvas } from "@/common/utils/resizePic";
import { blobToUrl } from "@/common/utils/uploadBlob";
import { cloneDeep } from "lodash-es";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
@Component({})
export default class FormulaModal extends Vue {
  showFormula = true;
  unSubscribe: any;
  iframeLoading = true;
  maxWidth = 1280;
  maxHeight = 960;

  public src = "https://latex.cdnjtzy.com/latexeditor/index.html";

  get componentId() {
    return this.currentComponentIds[0];
  }

  get componentLatex() {
    const { componentId, $store: { state: {componentMap } }}  = this;
    const { properties = { latex: '' } } = componentMap[componentId] || {};
    return properties.latex;
  }

  get currentComponents() {
    return this.$store.getters.currentComponents;
  }

  get currentComponentIds() {
    return this.currentComponents.map((i: any) => i.id);
  }

  public close() {
    this.showFormula = false;
    this.changeFormula("");
    bus.$emit("showFormulaModal", false)
  }

  public getCompLatex() {
    const { componentId, $store: { state: {componentMap } }}  = this;
    const { properties = { latex: '' } } = componentMap[componentId] || {};
    return properties.latex;
  }

  public changeFormula(latex?: string) {
    const paramsLatex = typeof latex === 'string' ? latex : this.getCompLatex();
    const formulaIframe = this.$refs.formulaIframe;
    formulaIframe && (formulaIframe as any).contentWindow.postMessage(
      {
        type: "getEquationInfo",
        data: paramsLatex || '',
      },
      "*",
    );
  }

  public loadMathJax() {
    if(!(window as any).MathJax) {
      console.log('call loadMathJax');
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.crossOrigin = "anonymous";
      script.src = "https://yy-s.zuoyebang.cc/static/mathjax_274/MathJax.js?config=svgsnippet-update200113";
      document.body.appendChild(script);
    }
  }

  // 公式转为图片
  public transformFormula(latexStr: string) {
    // todo: 公式转为图片时有耗时，需要优化
    const latex = latexStr.replace(/^\$|\$$/g, "");
    const div = document.createElement("div");
    const compId = this.componentId;
      div.innerText = latexStr;
      div.style.display = "none";
      document.body.appendChild(div);
      (window as any).MathJax.Hub.Typeset([
        div,
        () => {
          const svgEl = div.querySelector("svg");
          [
            ["version", 1.1],
            ["xmlns", "http://www.w3.org/2000/svg"],
          ].forEach((item: any) => {
            svgEl?.setAttribute(item[0], item[1]);
          });
          (svgEl as any).style.color = "#ffffff";
          const svg = svgEl ? svgEl.outerHTML : "";
          document.body.removeChild(div);

          if (svg) {
            const img = new Image();
            img.crossOrigin = "Anonymous";
            img.onload = async () => {
              console.log('img size', img.width, img.height);
              // 超出限制给出提示
              if(img.width > this.maxWidth || img.height > this.maxHeight) {
                showErrorMessage(new Error('公式组件的宽不可超过1280,高不可超过960'));
                return;
              }
              const canvas = image2Canvas(img, this.maxWidth, this.maxHeight, 2);
              // (canvas as any).style.position = "fixed";
              // (canvas as any).style.top = "725px";
              // document.body.appendChild(canvas);
              canvas.toBlob(
                blob => {
                  blobToUrl(blob, "text.png",true).then(url => {
                    this.$store.dispatch("updateComponentsProperties", {
                      ids: [compId],
                      newProperties: {
                        url: url,
                        latex: latex,
                        width: img.width * 2,
                        height: img.height * 2,
                      },
                    });
                  });
                },
                "image/png",
                1,
              );
            };
            img.src = "data:image/svg+xml;base64," + window.btoa(unescape(encodeURIComponent(svg))); //svg内容中可以有中文字符
          }
        },
      ]);
  }

  public handlerMessage(e: { data: { type: string; data: string } }) {
    const data = e.data;
    if (data && data.type === "sendEquationInfo") {
      this.transformFormula(data.data);
      this.showFormula = false;
    }
  }

  public show() {
    this.showFormula = true;
    bus.$emit("showFormulaModal", true);
  }

  created() {
    console.log('created FormulaModal');
    window.addEventListener("message", this.handlerMessage);
    this.unSubscribe = this.$store.subscribe(mutation => {
      const copyMutation = cloneDeep(mutation);
      if (copyMutation.type === "doubleClick") {
        if(this.currentComponents.length) {
          const { type = "" } = this.currentComponents?.[0];
          if (type === 'formula') {
            setTimeout(() => {
              this.show();
              this.changeFormula();
            }, 100);
          }
        };
      } else if (copyMutation.type === "addComponent") {
        const { type, properties: { latex }} = copyMutation.payload
        if (type === "formula" && latex === "") {
          console.log('changeFormula.addComponent')
          this.show();
          this.changeFormula("");
        }
      }
    });
    bus.$on("showFormulaModal", (val: boolean) => {
      // 点击编辑公式按钮 手动调用changeFormula
      if(val && !this.showFormula) {
        this.changeFormula();
      }
      // 判断是否已经渲染 没有渲染的话 渲染
      this.showFormula = val;
    });
  }

  mounted() {
    this.loadMathJax();
  }

  onLoad() {
    if(this.iframeLoading) {
      const timer = setTimeout(() => {
        this.iframeLoading = false;
        clearTimeout(timer);
        if (this.componentLatex!="") {
          console.log('changeFormula.4');
          this.changeFormula();
        }
      }, 2000);
    }
  }

  public destroyed() {
    this.unSubscribe && this.unSubscribe();
    window.removeEventListener("message", this.handlerMessage);
  }

}
</script>

<style lang="less">
.formula-editor-dialog {
  .el-dialog {
    width: 60vw;
    min-width: 745px;
  }
  .formula-iframe {
    height: 470px;
    width: 100%;
    background-color: #fff;
  }
}
</style>
