<template>
  <el-dialog
    title="编辑公式"
    :visible="showFormula"
    top="0vh"
    v-loading="iframeLoading"
    element-loading-fullscreen="false"
    element-loading-text="加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.3)"
    @close="close"
    class="formula-editor-dialog"
  >
    <iframe title="formula" name="formula" allow="clipboard-read; clipboard-write" id="formulaIframe" class="formula-iframe" ref="formulaIframe" :src="src" @load="onLoad" />
  </el-dialog>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { image2Canvas } from "@/common/utils/resizePic";
import { blobToUrl } from "@/common/utils/uploadBlob";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { OutputType } from ".";
import getImageSize from "@/common/utils/getImageSize";
@Component({})
export default class FormulaModalFN extends Vue {
  @Prop({
    default: "",
  })
  latex!: string;

  @Prop({
    default: "svg",
  })
  type!: OutputType;

  showFormula = true;
  iframeLoading = true;
  maxWidth = 1280;
  maxHeight = 960;

  public src = "https://latex.cdnjtzy.com/latexeditor/index.html";

  public close() {
    this.showFormula = false;
    this.changeFormula("");
    this.onClose();
  }

  public changeFormula(latex?: string) {
    const paramsLatex = typeof latex === "string" ? latex : this.latex;
    const formulaIframe = this.$refs.formulaIframe;
    // console.log("paramsLatex", paramsLatex, formulaIframe);
    console.log("paramsLatex", paramsLatex);
    formulaIframe &&
      (formulaIframe as any).contentWindow.postMessage(
        {
          type: "getEquationInfo",
          data: paramsLatex || "",
        },
        "*",
      );
  }

  public loadMathJax() {
    if (!(window as any).MathJax) {
      console.log("call loadMathJax");
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.src = "https://yy-s.zuoyebang.cc/static/mathjax_274/MathJax.js?config=svgsnippet-update200113";
      document.body.appendChild(script);
    }
  }

  // 公式转为图片
  public transformFormula(latexStr: string) {
    // todo: 公式转为图片时有耗时，需要优化
    const latex = latexStr.replace(/^\$|\$$/g, "");
    const div = document.createElement("div");
    div.innerText = latexStr;
    console.log("innerText", latexStr);
    div.style.display = "none";
    document.body.appendChild(div);
    (window as any).MathJax.Hub.Typeset([
      div,
      () => {
        const svgEl = div.querySelector("svg");
        [
          ["version", 1.1],
          ["xmlns", "http://www.w3.org/2000/svg"],
        ].forEach((item: any) => {
          svgEl?.setAttribute(item[0], item[1]);
        });
        (svgEl as any).style.color = "#ffffff";
        // (svgEl as any).style.color = "#000000";
        const svg = svgEl ? svgEl.outerHTML : "";
        document.body.removeChild(div);
        if (svg) {
          const img = new Image();
          img.crossOrigin = "Anonymous";
          img.onload = async () => {
            console.log("img size", img.width, img.height);
            // 超出限制给出提示
            if (img.width > this.maxWidth || img.height > this.maxHeight) {
              showErrorMessage(new Error("公式组件的宽不可超过1280,高不可超过960"));
              return;
            }
            const canvas = image2Canvas(img, this.maxWidth, this.maxHeight, 2);
            // (canvas as any).style.position = "fixed";
            // (canvas as any).style.top = "725px";
            // document.body.appendChild(canvas);
            canvas.toBlob(
              blob => {
                blobToUrl(blob, "text.png", true).then(async url => {
                  const imageSize = await getImageSize(url);
                  console.log("imageSize...", imageSize);
                  this.onConfirm({
                    url,
                    width: imageSize.width,
                    height: imageSize.height,
                    latex: latex,
                  });
                });
              },
              "image/png",
              1,
            );
          };
          // console.log('公式-base64', "data:image/svg+xml;base64," + window.btoa(unescape(encodeURIComponent(svg))));
          img.src = "data:image/svg+xml;base64," + window.btoa(unescape(encodeURIComponent(svg))); //svg内容中可以有中文字符
        }
      },
    ]);
  }

  public handlerMessage(e: { data: { type: string; data: string } }) {
    const data = e.data;
    if (data && data.type === "sendEquationInfo") {
      console.log("sendEquationInfo", data.data, this.type);
      if (this.type === "html") {
        this.onGetEquationInfo(data.data);
      }
      if (this.type === "svg") {
        console.log("sendEquationInfo--nani");
        this.transformFormula(data.data);
      }
      this.showFormula = false;
    }
  }

  public show() {
    this.showFormula = true;
    this.changeFormula();
  }

  created() {
    console.log("created FormulaModal");
    window.addEventListener("message", this.handlerMessage);
  }

  mounted() {
    console.log("...mounted...");
    this.loadMathJax();
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars
  onConfirm(_params: any) {}
  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars
  onClose() {}
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  onGetEquationInfo(latexStr: string) {}
  onLoad() {
    console.log("onLoad");
    this.iframeLoading = false;
    if (this.latex != "") {
      this.changeFormula();
    }
  }

  public destroyed() {
    window.removeEventListener("message", this.handlerMessage);
  }
}
</script>

<style lang="less">
.formula-editor-dialog {
  .el-dialog {
    width: 60vw;
    min-width: 745px;
  }
  .formula-iframe {
    height: 470px;
    width: 100%;
    background-color: #fff;
  }
}
</style>
