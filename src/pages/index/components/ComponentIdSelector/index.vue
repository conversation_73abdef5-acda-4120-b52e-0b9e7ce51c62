<template>
  <div class="show-component-btn" @click="showComponentManager">
    {{ currentIds.length ? `组件ID: ${currentIds.join("、")}` : "请选择组件" }}
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { ActiveNames } from "@/pages/index/store/modules/componentManagerCard/constants";
import { types } from "@/pages/index/store/modules/componentManagerCard";

@Component
export default class ComponentIdSelector extends Vue {
  get currentIds() {
    return this.$store.state.currentComponentIds;
  }

  showComponentManager() {
    this.$store.commit(types.mutations.setActiveNames, ActiveNames.MANAGER);
    this.$store.commit(
      types.mutations.setVisible,
      !this.$store.state.componentManagerCard.visible,
    );
  }
}
</script>

<style scoped lang="less">
.show-component-btn {
  text-align: left;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 20px;

  &:hover {
    color: #888;
  }
}
</style>
