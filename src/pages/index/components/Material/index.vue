<template>
  <Material :bus="bus" pd="zyb_hudong" token="deG!34fk1@s" pagesource="kejian" :host="hostParam" :accept="['img', 'album']"></Material>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { Material } from "material-library";
import "material-library/dist/material.css";
import "material-library/src/assets/svg/output";
import { REQUESTBASEURLPHP } from "@/common/utils/devHelper";
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: {
    Material,
  },
})
export default class MaterialLibrary extends Vue {
  get hostParam() {
    return REQUESTBASEURLPHP;
  }
  get bus() {
    return bus;
  }
  mounted() {
    this.$nextTick(() => {
      (window as MyWindow).materialLibraryReady = true;
    });
  }
}
</script>

<style lang="less"></style>
