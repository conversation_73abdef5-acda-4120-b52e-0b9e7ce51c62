<template>
  <div class="ruler-tick">
    <div class="ruler-tick-horizontal" @click="onClick($event, 'horizontal')">
      <div
        class="ruler-tick-horizontal-inner"
        :style="{
          transform: `translateX(${-NEGATIVE_COUNT * itemLength + originPointLeft - cocosContainerWidth / 2 + 'px'})`,
        }"
      >
        <div
          class="ruler-tick-horizontal-inner-item"
          :style="{
            width: itemLength + 'px',
          }"
          v-for="i in reverseTickNumber"
          :key="i"
        >
          <span class="ruler-tick-horizontal-inner-item-number">
            {{ i }}
          </span>
        </div>
      </div>
    </div>
    <div class="ruler-tick-vertical" @click="onClick($event, 'vertical')">
      <div
        class="ruler-tick-vertical-inner"
        :style="{
          transform: `translateY(${-NEGATIVE_COUNT * itemLength + originPointTop - cocosContainerHeight / 2 + 'px'})`,
        }"
      >
        <div class="ruler-tick-vertical-inner-item" :style="{ height: itemLength + 'px' }" v-for="i in reverseTickNumber" :key="i">
          <span class="ruler-tick-vertical-inner-item-number">
            {{ i }}
          </span>
        </div>
      </div>
    </div>
    <div>
      <div
        v-for="(left, index) in verticalLines"
        :style="{
          left: left + 'px',
        }"
        :key="index"
        class="vertical-line"
      >
        <span class="handle" @mousedown="onMouseDown($event, left, 'vertical', index)" />
      </div>
    </div>
    <div>
      <div
        v-for="(top, index) in horizontalLines"
        :style="{
          top: top + 'px',
        }"
        :key="index"
        class="horizontal-line"
      >
        <span class="handle" @mousedown="onMouseDown($event, top, 'horizontal', index)" />
      </div>
    </div>
    <div
      v-if="activeLineInfo"
      class="line-info"
      :style="{
        bottom: activeLineInfo.bottom + 'px',
        left: activeLineInfo.left + 'px',
      }"
    >
      {{ activeLineInfo.number + "px" }}
      <i class="el-icon-delete-solid" @click.stop="onClickDelete" />
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import getCocosContainerSize from "../CocosStagePanel/getCocosContainerSize";
import { types } from "@/pages/index/store/modules/rulerTick";

// 正数数量
const POSITIVE_COUNT = 30;
// 负数数量
const NEGATIVE_COUNT = 5;
const COUNT = POSITIVE_COUNT + NEGATIVE_COUNT + 1;
// 每一个tick item的单位
const UNIT = 100;

@Component
export default class RulerTick extends Vue {
  cocosContainerWidth = 0;
  cocosContainerHeight = 0;
  parentElWidth = 0;
  parentElHeight = 0;
  POSITIVE_COUNT = POSITIVE_COUNT;
  NEGATIVE_COUNT = NEGATIVE_COUNT;
  COUNT = COUNT;
  UNIT = UNIT;
  activeLine: {
    type: "vertical" | "horizontal";
    index: number;
  } = {
    type: "vertical",
    index: -1,
  };

  get tickNumber() {
    return Array(COUNT)
      .fill(0)
      .map((_, i) => (POSITIVE_COUNT - i) * UNIT);
  }

  get reverseTickNumber() {
    return [...this.tickNumber].reverse();
  }

  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  get stageSize(): State["template"]["stage"] {
    return (this.$store.state as State).template.stage;
  }

  get originPointLeft() {
    // cocos以中心点为原点
    return this.transCocosPointPositionToScreenPointPosition(0, 0).x;
  }

  get originPointTop() {
    // cocos以中心点为原点
    return this.transCocosPointPositionToScreenPointPosition(0, 0).y;
  }

  /**
   * @desc ratio = 屏幕像素 / cocos像素
   */
  get ratio() {
    return this.cocosContainerWidth / this.stageSize.safeWidth;
  }

  get itemLength() {
    return this.ratio * UNIT;
  }

  get verticalLines() {
    return this.$store.state.rulerTick.verticalLines.map((x: number) => this.transCocosPointPositionToScreenPointPosition(x, 0).x);
  }

  get horizontalLines() {
    return this.$store.state.rulerTick.horizontalLines.map((y: number) => this.parentElHeight - this.transCocosPointPositionToScreenPointPosition(0, y).y);
  }

  get activeLineInfo() {
    const { type, index } = this.activeLine;
    if (index < 0) {
      return undefined;
    }
    if (this.activeLine.type === "vertical") {
      return {
        left: this.transCocosPointPositionToScreenPointPosition(this.$store.state.rulerTick.verticalLines[index] + 10, 0).x,
        bottom: 30,
        number: this.$store.state.rulerTick.verticalLines[index] + this.stageSize.safeWidth / 2,
        type,
        index,
      };
    }
    return {
      left: 10,
      bottom: this.parentElHeight - this.horizontalLines[index] + 10,
      number: -this.$store.state.rulerTick.horizontalLines[index] + this.stageSize.safeHeight / 2,
      type,
      index,
    };
  }

  @Watch("initialDataLoaded")
  async onInitialDataLoaded(val: boolean) {
    if (val) {
      this.getCocosContainerSize();
    }
  }

  mounted() {
    this.getCocosContainerSize();
    window.addEventListener("resize", this.getCocosContainerSize);
    window.addEventListener("click", this.onClickAway, true);
  }

  destroyed() {
    window.removeEventListener("click", this.onClickAway, true);
  }

  onClickAway(e: MouseEvent) {
    if (!this.$el.contains(e.target as Node)) {
      this.activeLine.index = -1;
    }
  }

  getCocosContainerSize() {
    const parentEl = this.$el.parentElement;
    if (!parentEl) {
      return;
    }
    const heightWidthRatio = this.stageSize.safeHeight / this.stageSize.safeWidth;
    const { width, height } = getCocosContainerSize(parentEl, heightWidthRatio);
    this.parentElWidth = parentEl.clientWidth;
    this.parentElHeight = parentEl.clientHeight;
    this.cocosContainerWidth = width;
    this.cocosContainerHeight = height;
  }

  transCocosPointPositionToScreenPointPosition(x: number, y: number) {
    return {
      x: Number((this.parentElWidth / 2 + x * this.ratio).toFixed(0)),
      y: Number((this.parentElHeight / 2 + y * this.ratio).toFixed(0)),
    };
  }

  transScreenPointPositionToCocosPointPosition(x: number, y: number) {
    return {
      x: Number(((x - this.parentElWidth / 2) / this.ratio).toFixed(0)),
      y: Number(((y - this.parentElHeight / 2) / this.ratio).toFixed(0)),
    };
  }

  onClick(e: MouseEvent, type: "vertical" | "horizontal") {
    const { clientX, clientY } = e;
    const { x: parentX, y: parentY } = (this.$el.parentElement as HTMLElement).getBoundingClientRect();
    const { x, y } = this.transScreenPointPositionToCocosPointPosition(clientX - parentX, this.parentElHeight - (clientY - parentY));
    if (type === "vertical") {
      this.addHorizontalLine(y);
      return;
    }
    this.addVerticalLine(x);
  }

  addVerticalLine(x: number) {
    this.$store.commit(types.mutations.addVerticalLine, x);
  }

  addHorizontalLine(y: number) {
    this.$store.commit(types.mutations.addHorizontalLine, y);
  }

  onMouseDown(e: MouseEvent, number: number, type: "vertical" | "horizontal", index: number) {
    const startX = e.x;
    const startY = e.y;

    document.onmouseup = () => {
      document.onmousemove = null;
      document.onmouseup = null;
    };

    document.onmousemove = e => {
      if (type === "vertical") {
        const offsetX = e.x - startX;
        const value = this.transScreenPointPositionToCocosPointPosition(number + offsetX, 0).x;
        this.$store.commit(types.mutations.updateVerticalLine, {
          index,
          value,
        });
      } else {
        const offsetY = e.y - startY;
        const value = this.transScreenPointPositionToCocosPointPosition(0, this.parentElHeight - (number + offsetY)).y;
        this.$store.commit(types.mutations.updateHorizontalLine, {
          index,
          value,
        });
      }
    };
    this.activeLine = {
      type,
      index,
    };
  }

  onClickDelete() {
    if (!this.activeLineInfo) {
      return;
    }
    if (this.activeLineInfo.type === "vertical") {
      this.$store.commit(types.mutations.removeVerticalLine, this.activeLineInfo.index);
    }
    if (this.activeLineInfo.type === "horizontal") {
      this.$store.commit(types.mutations.removeHorizontalLine, this.activeLineInfo.index);
    }
    this.activeLine.index = -1;
  }
}
</script>

<style scoped lang="less">
.ruler-tick {
  &-horizontal {
    cursor: copy;
    position: absolute;
    bottom: 2px;
    left: 0;
    right: 0;
    overflow: hidden;
    text-align: left;

    &-inner {
      white-space: nowrap;

      &-item {
        display: inline-block;
        width: 100px;
        text-align: left;
        background: url("../../assets/ruler_ticks.png") no-repeat;
        background-position: bottom;
        background-size: contain;
        padding-bottom: 10px;

        &-number {
          user-select: none;
          display: inline-block;
          font-size: 12px;
          color: #777;
        }
      }
    }
  }

  &-vertical {
    cursor: copy;
    position: absolute;
    top: 0;
    left: 2px;
    bottom: 0;
    overflow: hidden;

    &-inner {
      &-item {
        height: 100px;
        text-align: left;
        background: url("../../assets/ruler_ticks_vertical.png") no-repeat;
        background-position: left;
        background-size: contain;
        padding-left: 10px;

        &-number {
          user-select: none;
          display: inline-block;
          font-size: 12px;
          color: #777;
          position: relative;
          top: -8px;
        }
      }
    }
  }

  .vertical-line {
    position: absolute;
    top: 0;
    bottom: 0;
    border-right: 1px solid #8bf6ff;
    opacity: 0.5;

    .handle {
      cursor: ew-resize;
      position: absolute;
      bottom: 0px;
      left: -6px;
      display: inline-block;
      background: #000;
      width: 13px;
      height: 13px;
      border-radius: 3px;
    }
  }

  .horizontal-line {
    position: absolute;
    left: 0;
    right: 0;
    border-bottom: 1px solid #8bf6ff;
    opacity: 0.5;

    .handle {
      cursor: ns-resize;
      position: absolute;
      left: 0px;
      top: -6px;
      display: inline-block;
      background: #000;
      width: 13px;
      height: 13px;
      border-radius: 3px;
    }
  }

  .line-info {
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 8px;
    position: absolute;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 10px 0 rgba(22, 45, 61, 0.36);

    .el-icon-delete-solid {
      cursor: pointer;
    }
  }
}
</style>
