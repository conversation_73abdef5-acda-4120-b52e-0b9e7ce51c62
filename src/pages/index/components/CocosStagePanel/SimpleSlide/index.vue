<template>
  <div id="group-slide" :class="`${theme}`">
    <div :class="['slide-wrapper']" :style="slidesStyle">
      <div @click="handleSelect(index)" v-for="(item, index) in orders" :key="item"
        :class="['slide-item', { active: index === selectIndex }]">{{ index + 1 }}</div>
    </div>
    <div class="slide-handles" :style="scaleStyle" v-if="theme === 'chugao'">
      <div :class="['slide-prev', { disable: selectIndex === 0 }]" @click="handleSelect(selectIndex - 1)"></div>
      <div :class="['slide-next', { disable: selectIndex === orders.length - 1 }]"
        @click="handleSelect(selectIndex + 1)"></div>
    </div>
    <div class="slide-handles" v-if="theme === 'xiaoxue'">
      <div :style="scaleStyle" :class="['slide-prev', { disable: selectIndex === 0 }]"
        @click="handleSelect(selectIndex - 1)"></div>
      <div :style="scaleStyle" :class="['slide-next', { disable: selectIndex === orders.length - 1 }]"
        @click="handleSelect(selectIndex + 1)"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import "./index.less";
import store from "@/pages/index/store";
import { CATEGORY, SpecialComponentSubTypes } from "@/common/constants";

@Component({
  components: {},
})
export default class SimpleSlide extends Vue {
  @Prop({
    default: 1,
  })
  scale!: number;

  @Prop({
    default: 1280,
  })
  parentWidth!: number;

  @Prop({
    default: 960,
  })
  length!: number;

  @Prop({
    default: 'xiaoxue',
  })
  theme!: string;

  selectIndex = 0;

  isFromGroup = false;

  get scaleStyle() {
    return [
      {
        transform: `scale(${this.scale})`,
      },
    ];
  }

  get category() {
    return this.$store.state.template.category;
  }

  get slidesStyle() {
    const h = this.theme === "xiaoxue" ? 66 : 0;
    const y = 120;
    const top = (y + h / 2) * this.scale;
    return [
      {
        transform: `scale(${this.scale})`,
        top: `${top}px`,
      },
    ];
  }

  get orders() {
    return Array.from({ length: this.length }, (v, i) => i);
  }

  async handleSelect(index: number) {
    console.log("handleSelect", index);
    this.selectIndex = index;
    if (this.category === CATEGORY.ENGROUPPK) {
      this.updateENGROUPPKProperties(index);
    } else if (this.category === CATEGORY.ENPK) {
      this.updateENPKProperties(index);
    }
    if (this.category === CATEGORY.CONTEXTUALANSWERQUESTION) {
      this.updateContextualAnswerProperties(index);
    }
  }

  get componentIndex() {
    const components = Object.values(store.state.componentMap);
    const subType :any = {
      [CATEGORY.ENPK]: SpecialComponentSubTypes.ENPK,
      [CATEGORY.ENGROUPPK]: SpecialComponentSubTypes.ENGROUPPK,
      [CATEGORY.CONTEXTUALANSWERQUESTION]: 'contextualAnswer'
    }
    const quesIndexKey :any = {
      [CATEGORY.ENPK]: 'pkData.questionIndex',
      [CATEGORY.ENGROUPPK]: 'pkData.questionIndex',
      [CATEGORY.CONTEXTUALANSWERQUESTION]: 'stuQuestionIndex'
    }
    const keys = quesIndexKey[this.category].split(".");
    const theComp = components.find(item => item.subType === subType[this.category])
    const { properties = {} } = theComp;
    let temp = properties;
    let value = 0;
    while (keys.length) {
      const key = keys.shift();
      console.log('key...', key);
      if (typeof temp[key] !== 'undefined') {
        temp = temp[key];
      } else {
        temp = {};
      }
      if (!keys.length) {
        value = temp;
      }
    }
    
    return value;
  }

  updateContextualAnswerProperties(index: number) {
    const components = Object.values(store.state.componentMap);
    components.forEach((comp: any) => {
      const {
        id,
        subType,
      } = comp;
      if (subType && subType == 'contextualAnswer') {
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            stuQuestionIndex: index,
          },
        });
      }
    });
  }

  updateENGROUPPKProperties(index: number) {
    const components = Object.values(store.state.componentMap);
    components.forEach((comp: any) => {
      const {
        id,
        subType,
        properties: { pkData },
      } = comp;
      if (subType && subType == SpecialComponentSubTypes.ENGROUPPK) {
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ["pkData"]: {
              ...pkData,
              questionIndex: index,
            },
          },
        });
      }
    });
  }

  updateENPKProperties(index: number) {
    const components = Object.values(store.state.componentMap);
    components.forEach((comp: any) => {
      const {
        id,
        subType,
        properties: { pkData },
      } = comp;
      if (subType && subType == SpecialComponentSubTypes.ENPK) {
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ["pkData"]: {
              ...pkData,
              questionIndex: index,
            },
          },
        });
      }
    });
  }

  @Watch('componentIndex')
  componentIndexChange(val: number) {
    console.log("componentIndexChange", val);
    if (!val && val !== this.selectIndex) {
      this.selectIndex = 0;
    }
  }
}
</script>
