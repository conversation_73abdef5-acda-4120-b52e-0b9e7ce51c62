#group-slide {
  width: 0;
  height: 0;
  .slide-gray-top {
    position: absolute;
    left: 1px;
    top: 12.5%;
    background-color: #fff;
    border-bottom: 4px solid #F9FAFC;
    margin-top: 1px;
  }
  .slide-gray-bottom {
    position: absolute;
    left: 1px;
    bottom: 12.5%;
    background-color: #F9FAFC;
    margin-bottom: 1px;
  }
  .slide-wrapper {
    position: absolute;
    // left: 24px;
    top: 20px;
    display: flex;
    align-items: center;
    transform-origin: left top;
    // &::after {
    //   content: '';
    //   position: absolute;
    //   left: 0;
    //   height: 100%;
    //   width: 100000px;
    //   background-color: #9E9E9E;
    //   top: 0;
    //   z-index: 0;
    // }
  }
  &.xiaoxue {
    .slide-wrapper {
      padding-left: 24px;
    }
    .slide-item {
      cursor: pointer;
      width: 66px;
      height: 66px;
      background: url("~@/assets/img/ti-circle.png") repeat;
      font-weight: 800;
      font-size: 28px;
      color: #9E9E9E;
      text-align: center;
      line-height: 66px;
      margin-right: 16px;
      position: relative;
      &.active {
        &:after {
          content: '';
          background: url("~@/assets/img/ti-star.png") repeat;
          position: absolute;
          left: -16px;
          top: -11px;
          width: 98px;
          height: 34px;
        }
      }
    }
    .slide-prev, .slide-next {
      cursor: pointer;
      width: 80px;
      height: 80px;
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      &.disable {
        pointer-events: none;
        cursor: none;
      }
    }
    .slide-prev {
      left: 10px;
      background: url("~@/assets/img/prev.png") repeat; 
      transform-origin: left top;
      &.disable {
        background: url("~@/assets/img/prev_disabled.png") repeat; 
      }
    }
    .slide-next {
      right: 10px;
      transform-origin: right top;
      background: url("~@/assets/img/next.png") repeat; 
      &.disable {
        background: url("~@/assets/img/next_disabled.png") repeat; 
      }
    }
    .submit {
      width: 92px;
      height: 92px;
      background: url("~@/assets/img/tijiao.png") repeat; 
      position: absolute;
    }
  }
  &.chugao {
    .slide-wrapper {
      height: 80px;
      width: 100%;
      left: 2%;
    }
    .slide-item {
      cursor: pointer;
      width: 48px;
      height: 48px;
      line-height: 48px;
      background: #E6E6E6;
      font-weight: 600;
      color: #141414;
      text-align: center;
      margin-right: 24px;
      position: relative;
      border-radius: 50%;
      font-size: 26px;
      opacity: 0.5;
      &.active {
        // width: 60px;
        // height: 60px;
        // line-height: 60px;
        // font-size: 32px;
        opacity: 1;
      }
    }
    .slide-handles {
      position: absolute;
      bottom: 12.5%;
      right: 0px;
      display: flex;
      justify-content: flex-end;
      height: 112px;
      align-items: center;
      transform-origin: right bottom;
    }
    .slide-prev {
      cursor: pointer;
      width: 64px;
      height: 64px;
      margin-right: 32px;
      background: url("~@/assets/img/btn_prev.png") no-repeat; 
      &.disable {
        display: none;
        pointer-events: none;
        cursor: none;
      }
    }
    .slide-next {
      cursor: pointer;
      height: 64px;
      background: #2ED1A0;
      border-radius: 36px;
      padding: 0 46px;
      margin-right: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      &.disable {
        pointer-events: none;
        cursor: none;
      }
      &:after {
        content: '下一题';
        font-size: 28px;
        font-weight: 500;
        color: #FFFFFF;
        display: block;
      }
    }
  }
  .group-audio-wrapper {
    position: absolute;
    height: 64px;
    background: #FFFFFF;
    box-shadow: 0px 0px 4px 0px #E6EBF5;
    border-radius: 32px;
    display: flex;
    align-items: center;
    // position: fixed;
    top: 0px;
    // margin-left: -53px;
    // margin-top: -16px;
    // margin-top: -14px;
    // margin-left: -54px;
    padding: 16px;
    box-sizing: border-box;
    justify-content: center;
    transform-origin: left top;
    .button-icon {
      cursor: pointer;
      width: 44px;
      height: 44px;
      border-radius: 2px;
      // right: 368px;
      background-color: green;
      display: flex;
      align-items: center;
      flex-direction: column;
      box-sizing: border-box;
      justify-content: space-between;
      background: url("~@/assets/img/<EMAIL>") repeat;
      background-size: 44px 44px;
      margin-right: 10px;
    }
    .extra {
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #777777;
      line-height: 24px;
      display: flex;
      align-items: center;
      .init-time {
        color: #333333;
        font-weight: 500;
      }
      .split {
        // split@2x
        width: 8px;
        height: 24px;
        background: url("~@/assets/img/<EMAIL>");
        background-size: 8px 24px;
        margin: 0 6px;
      }
    }
  }
}