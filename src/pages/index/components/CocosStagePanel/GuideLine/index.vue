<template>
  <div>
    <span :class="className" :style="style[0]" />
    <span :class="className" :style="style[1]" />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

export enum GuideLineType {
  vertical = 1,
  horizontal = 2,
}

@Component
export default class GuideLine extends Vue {
  @Prop()
  type!: GuideLineType;

  @Prop()
  stageSize!: StageSize;

  @Prop()
  containerWidth!: number;

  @Prop()
  containerHeight!: number;

  get className() {
    if (this.type === GuideLineType.vertical) {
      return "guide-line-verticle";
    }
    return "guide-line-horizontal";
  }

  get style() {
    if (this.type === GuideLineType.vertical) {
      const ratio = this.containerWidth / this.stageSize.safeWidth;
      const distance =
        ((this.stageSize.safeWidth - this.stageSize.width) / 2) * ratio;
      return [{ left: distance + "px" }, { right: distance + "px" }];
    }
    const ratio = this.containerHeight / this.stageSize.safeHeight;
    const distance =
      ((this.stageSize.safeHeight - this.stageSize.height) / 2) * ratio;
    return [{ top: distance + "px" }, { bottom: distance + "px" }];
  }
}
</script>

<style lang="less" scoped>
@charset 'utf-8';

.guide-line-verticle {
  position: absolute;
  top: -300px;
  width: 0;
  height: calc(100% + 600px);
  border-left: 1px dashed #ccc;
}

.guide-line-horizontal {
  position: absolute;
  left: -300px;
  height: 0;
  width: calc(100% + 600px);
  border-top: 1px dashed #ccc;
}

.guide-line-horizontal {
  &:last-child {
    &::before {
      position: absolute;
      top: 5px;
      right: 235px;
      content: "设计分辨率";
      font-size: 12px;
      color: #aaa;
    }
  }
}
</style>
