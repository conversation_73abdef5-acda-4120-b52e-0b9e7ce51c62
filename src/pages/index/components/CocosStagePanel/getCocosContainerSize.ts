export default function getCocosContainerSize(
  parentEl: HTMLElement,
  ratio = 0.75,
  space = 140,
): { width: number; height: number } {
  if (parentEl) {
    const w1 = Math.floor(parentEl.clientWidth - space);
    const h1 = Math.floor(w1 * ratio);
    const h2 = Math.floor(parentEl.clientHeight - space);
    const w2 = Math.floor(h2 / ratio);
    if (h1 < h2) {
      return { width: w1, height: h1 };
    }
    return { width: w2, height: h2 };
  }
  return { width: 0, height: 0 };
}
