<template>
  <div id="group-slide" v-if="isFromGroup" :class="`${theme}`">
    <template>
      <div :class="['slide-wrapper']" :style="slidesStyle">
        <div @click="handleSelect(index)" v-for="(item, index) in orders" :key="item" :class="['slide-item', { active: index === selectIndex }]">{{ index + 1 }}</div>
      </div>
      <div class="slide-handles" :style="scaleStyle" v-if="theme === 'chugao'">
        <div :class="['slide-prev', { disable: selectIndex === 0 }]" @click="handleSelect(selectIndex - 1)"></div>
        <div :class="['slide-next', { disable: selectIndex === orders.length - 1 }]" @click="handleSelect(selectIndex + 1)"></div>
      </div>
      <div class="slide-handles" v-if="theme === 'xiaoxue'">
        <div :style="scaleStyle" :class="['slide-prev', { disable: selectIndex === 0 }]" @click="handleSelect(selectIndex - 1)"></div>
        <div :style="scaleStyle" :class="['slide-next', { disable: selectIndex === orders.length - 1 }]" @click="handleSelect(selectIndex + 1)"></div>
      </div>
      <div class="submit" :style="submitStyle" v-if="theme === 'xiaoxue'" v-show="selectIndex === orders.length - 1"></div>
    </template>
    <div class="group-audio-wrapper" v-if="groupCategory === 1102" :style="groupAudioStyle" @click="handleAudio">
      <div class="button-icon"></div>
      <div class="extra">
        <div class="init-time">{{ "00:00" }}</div>
        <div class="split"></div>
        <div class="total-time">{{ totalTime }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { postMessage, GROUP_SLIDES, EDIT_SLIDES_CHANGE, SHOW_AUDIO_SETTING, GROUP_AUDIO_TIME } from "@/common/utils/postMessage";
import { parse } from "query-string";
import "./index.less";

@Component
export default class GroupSlide extends Vue {
  @Prop({
    default: 1,
  })
  scale!: number;
  @Prop({
    default: 1280,
  })
  parentWidth!: number;
  @Prop({
    default: 960,
  })
  parentHeight!: number;
  orders: number[] = [];
  currentQstValue = -1;
  selectIndex = -1;
  isFromGroup = false;
  theme = "xiaoxue"; // xiaoxue chugao
  isChangeing = false;
  totalTime = "00:00";
  groupCategory = 1027;
  get scaleStyle() {
    return [
      {
        transform: `scale(${this.scale})`,
      },
    ];
  }

  get slidesStyle() {
    const h = this.theme === "xiaoxue" ? 66 : 0;
    const y = 120;
    const top = (y + h / 2) * this.scale;
    return [
      {
        transform: `scale(${this.scale})`,
        top: `${top}px`,
      },
    ];
  }

  get groupAudioStyle() {
    const x = this.theme === "xiaoxue" ? 0 : 372;
    const y = this.theme === "xiaoxue" ? 294 : 202;
    const h = 64;
    const w = 234;
    const top = (960 / 2 - h / 2 - y) * this.scale;
    const left = (1280 / 2 - w / 2 + x) * this.scale;
    return [
      {
        transform: `scale(${this.scale})`,
        height: `${h}px`,
        width: `${w}px`,
        top: `${top}px`,
        left: `${left}px`,
      },
    ];
  }

  get grayStyle() {
    return [
      {
        width: `${this.parentWidth - 2}px`,
        height: `${80 * this.scale}px`,
        borderWidth: `${16 * this.scale}px`,
      },
    ];
  }

  get submitStyle() {
    return [
      {
        transform: `scale(${this.scale})`,
        right: `${36 * this.scale}px`,
        bottom: "12.5%",
        transformOrigin: "right bottom",
        marginBottom: `${35 * this.scale}px`,
      },
    ];
  }

  get grayBottomStyle() {
    return [
      {
        width: `${this.parentWidth - 2}px`,
        height: `${112 * this.scale}px`,
      },
    ];
  }

  async handleSelect(index: number) {
    if (!(await (window as any).getCanSwitchQuestion())) {
      return;
    }
    // 先保存数据 再切题
    if (this.isChangeing) {
      return;
    }
    this.isChangeing = true;
    try {
      await (window as any).getQuestionDetail(true).then((data: never) => {
        if ((data as any).category !== 0) {
          postMessage(window.parent, EDIT_SLIDES_CHANGE, { currentQstIndex: this.orders[index], preDetail: data });
        }
        this.isChangeing = false;
      });
    } catch (err) {
      console.log(err);
      this.isChangeing = false;
    }
  }
  handleMessage(message: { data: { action: any; data: { indexArray: number[]; currIndex: number; theme: string; formatTime?: string; groupCategory: number } } }) {
    const { action, data } = message.data;
    if (action === GROUP_SLIDES) {
      console.log("xu-GROUP_SLIDES", JSON.stringify(data), action, message);
      this.orders = data.indexArray;
      this.currentQstValue = data.currIndex;
      this.groupCategory = data.groupCategory;
      this.selectIndex = this.orders.findIndex(item => {
        return item === this.currentQstValue;
      });
      this.theme = data.theme || "xiaoxue";
    }
    if (action === GROUP_AUDIO_TIME) {
      console.log("xu-GROUP_AUDIO_TIME", data, action, message);
      this.totalTime = data.formatTime || "00:00";
    }
  }

  handleAudio() {
    postMessage(window.parent, SHOW_AUDIO_SETTING, {});
  }

  created() {
    window.addEventListener("message", this.handleMessage, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("message", this.handleMessage, false);
    });
    const query = parse(window.location.search);
    this.isFromGroup = Boolean(Number(query.fromGroup)) && !query.plat;
  }
}
</script>
