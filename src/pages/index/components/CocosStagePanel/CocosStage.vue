<template>
  <div class="canvas-container" :style="containerStyle" ref="container">
    <canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="0"></canvas>
    <context-menu />
    <div id="splash">
      <div class="progress-bar stripes">
        <span style="width: 0%"></span>
      </div>
    </div>
    <guide-line :type="GuideLineType.vertical" :stageSize="stageSize" :containerWidth="containerWidth" :containerHeight="containerHeight" />
    <guide-line :type="GuideLineType.horizontal" :stageSize="stageSize" :containerWidth="containerWidth" :containerHeight="containerHeight" />
    <!-- 题组类型的题(跟读单词等) 切题控件 -->
    <GroupSlide :scale="scale" :parentWidth="containerWidth" :parentHeight="containerHeight"></GroupSlide>
    <!-- 小英pk/情景对话 切题控件 -->
    <SimpleSlide
      v-if="[1109, 1192, 1167].includes(category)"
      :scale="scale"
      :parentWidth="containerWidth"
      :parentHeight="containerHeight"
      :length="slideConfig.total || 0"
      :theme="slideConfig.theme"
    ></SimpleSlide>
    <ImageScale v-if="showScaleModal" @close="showScaleModal = false" :src="imageScaleSrc" :containerScale="scale"> </ImageScale>
    <!-- <ImgBgRemoveView :scale="scale"> </ImgBgRemoveView> -->
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import ContextMenu from "../ContextMenu/index.vue";
import getCocosContainerSize from "./getCocosContainerSize";
import GuideLine, { GuideLineType } from "./GuideLine/index.vue";
import GroupSlide from "./GroupSlide/index.vue";
import SimpleSlide from "./SimpleSlide/index.vue";
import { isNotOnline } from "@/common/utils/env";
import { getNavigationStartTime, TimeMonitorType } from "@/common/utils/monitorUtil";
import { CATEGORY } from "@/common/constants";
import ImageScale from "@/components/ImageScale/index.vue";
// import ImgBgRemoveView from "@/components/ImgBgRemoveView/ImgBgRemoveView.vue";
import bus from "@/pages/index/common/utils/bus";
import { ShowImageScaleModalEvtData } from "@/types/imageScale";
import { isNative } from "@/common/utils/pageConfig";

@Component({
  components: {
    ContextMenu,
    GuideLine,
    GroupSlide,
    SimpleSlide,
    ImageScale,
    // ImgBgRemoveView,
  },
})
export default class CocosStage extends Vue {
  containerWidth = document.body.clientWidth - 440;
  containerHeight = 0;
  havaCocos = false;
  parentVersion = Number(localStorage.getItem("parentVersion")) || 1;
  // 控制图片缩放弹窗显隐
  showScaleModal = false;
  // 需要缩放的图片地址
  imageScaleSrc = "";
  /** 容器的透明度 在游戏渲染完毕前将canvas的透明度设置为0 解决页面初始化时canvas黑屏和resize效果*/
  containerOpacity = 0;

  get GuideLineType() {
    return GuideLineType;
  }

  get containerStyle() {
    return [
      {
        width: this.containerWidth + "px",
        height: this.containerHeight + "px",
        opacity: this.containerOpacity,
      },
    ];
  }

  get scale() {
    return this.containerWidth / 1280;
  }

  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  get stageSize() {
    return this.$store.state.template.stage;
  }

  get slideLen() {
    const { category } = this.$store.state.template;
    if (category === CATEGORY.ENPK || category === CATEGORY.ENGROUPPK) {
      return this.formTemplateData.components.length;
    }
    if (category === CATEGORY.CONTEXTUALANSWERQUESTION) {
      const { componentMap } = this.$store.state;
      const theComp: any = Object.values(componentMap).find((item: any) => item.subType === "contextualAnswer");
      return theComp.properties.stuQuestion.length;
    }
    return 0;
  }

  get slideConfig() {
    const config = {
      total: 0,
      theme: "xiaoxue",
    };
    const { category } = this.$store.state.template;
    if (category === CATEGORY.ENPK || category === CATEGORY.ENGROUPPK) {
      config.total = this.formTemplateData.components.length;
    }
    if (category === CATEGORY.CONTEXTUALANSWERQUESTION) {
      config.theme = "chugao";
      const { componentMap } = this.$store.state;
      const theComp: any = Object.values(componentMap).find((item: any) => item.subType === "contextualAnswer");
      config.total = theComp.properties.stuQuestion.length;
    }

    return config;
  }

  get formTemplateData() {
    return this.$store.state.extData.formTemplateData;
  }

  get category() {
    return this.$store.state.template.category;
  }

  get cocosInitFinished() {
    return this.$store.state.cocosInitFinished;
  }

  @Watch("initialDataLoaded")
  async oninitialDataLoaded(val: boolean) {
    console.log("oninitialDataLoaded");
    if (val) {
      // window.cc是否ready
      this.initStage();
    }
  }
  @Watch("cocosInitFinished")
  async onCocosInitFinished(val: boolean) {
    if (val) {
      // 防止cocosResourcesLoaded没有执行 在这里做一个兜底的处理
      this.containerOpacity = 1;
      // 获取包管理的地址 保存到缓存中
      const timer = setTimeout(() => {
        clearTimeout(timer);
        this.storagePkgUrl();
      }, 2000);
    }
    window.cc.game.resume();
  }
  @Watch("showScaleModal")
  async onShowScaleModalChanged(val: boolean) {
    /**
     * 打开图片弹窗时，需要禁用快捷键，不禁用的话，上下左右键被触发时，图片组件的位移会发生变化
     */
    (window as MyWindow).cocos.showSocpedComponent = val;
  }

  storagePkgUrl() {
    const { bundleName } = this.$store.state.template;
    this.$getPageConfigByKey("getBundleUrl")(bundleName, 4).then((res: string) => {
      if (res) {
        localStorage.setItem(bundleName, res);
      }
    });
  }

  getCCReady() {
    return new Promise(resolve => {
      let theTimer = -1;
      const loop = () => {
        theTimer = requestAnimationFrame(loop);
        if ((window as any).cc) {
          resolve(true);
          cancelAnimationFrame(theTimer);
        }
      };
      loop();
    });
  }

  getEditInitScene() {
    return new Promise(resolve => {
      let theTimer = -1;
      const loop = () => {
        theTimer = requestAnimationFrame(loop);
        if ((window as any)._$editorInitScene) {
          (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosBoot);
          window.cc.director.runSceneImmediate((window as any)._$editorInitScene);
          resolve((window as any)._$editorInitScene);
          // 根据app里是否有内容来定义白屏时间
          cancelAnimationFrame(theTimer);
        }
      };
      loop();
    });
  }

  async initStage() {
    await this.getCCReady();
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosInit);
    if (this.havaCocos) {
      window.cc.assetManager.main.loadScene("init", function(err: any, scene: any) {
        window.cc.director.runScene(scene);
      });
      return;
    }
    console.log("开始创建cocos舞台", new Date().getTime());
    this.havaCocos = true;
    this.getContainerSize();
    (window as MyWindow)._$store = this.$store;
    (window as MyWindow)._vue = Vue;
    // 快速预览
    if ((window as MyWindow).__quick_compile_project__) {
      (window as MyWindow).__quick_compile_project__.load(() => {
        window.cc.macro.ENABLE_WEBGL_ANTIALIAS = true;
        window.cc.macro.ENABLE_TRANSPARENT_CANVAS = true;
        (window as MyWindow).boot();
        window.cc.game.on(window.cc.game.EVENT_GAME_INITED, () => {
          window.cc.game.canvas.addEventListener("mousedown", this.setContextMenuPosition);
        });
      });
    } else {
      window.cc.macro.ENABLE_WEBGL_ANTIALIAS = true;
      window.cc.macro.ENABLE_TRANSPARENT_CANVAS = true;
      const { bundleName } = this.$store.state.template;
      let url = "";
      const cacheUrl = localStorage.getItem(`my${bundleName}`) || localStorage.getItem(bundleName);
      // storagePkgUrl
      if (cacheUrl) {
        url = isNative() ? cacheUrl : cacheUrl + "/" + bundleName ?? "";
        console.log(`%c ${bundleName}系localStorage中指定，请根据需要到localStorage中删除或更改my${bundleName}的值`, "color: #409eff; font-size: 20px;");
      } else {
        if (isNotOnline) {
          console.log(`%c 可在localStorage中将my${bundleName}的值设置为指定的地址`, "color: #409eff; font-size: 20px;");
        }
        // 默认从缓存中读取 页面完成之后 再获取一次最新的地址，存到本地。
        url = isNative() ? await this.$getPageConfigByKey("getBundleUrl")(bundleName, 4) : (await this.$getPageConfigByKey("getBundleUrl")(bundleName, 4)) + "/" + bundleName;
      }
      await this.getEditInitScene();
      this.collectCocosTimeStamp();
      window.cc
        .find("Canvas")
        .getComponent("InitSceneCmpt")
        .initData({
          resourceMap: {
            [bundleName]: url,
          },
        });
      window.cc.game.on(window.cc.game.EVENT_GAME_INITED, () => {
        window.cc.game.canvas.addEventListener("mousedown", this.setContextMenuPosition);
      });
      try {
        // 设置缓存信息
        const editorPreLoadedTimes = JSON.parse(localStorage.getItem("editorPreLoadedTimes") || "{}");
        const { category } = this.$store.state.template;
        if (editorPreLoadedTimes[category] && editorPreLoadedTimes[category].bundleUrl === url) {
          return;
        } else {
          editorPreLoadedTimes[category] = {
            time: Date.now().toString(),
            bundleUrl: url,
          };
          localStorage.setItem(`editorPreLoadedTimes`, JSON.stringify(editorPreLoadedTimes));
          console.log("cache.编辑器缓存了", category, "的地址", url);
        }
      } catch (error) {
        console.log("error", error);
      }
    }
  }

  mounted() {
    this.getContainerSize();
    window.addEventListener("resize", this.getContainerSize);
  }

  async created() {
    await (window as MyWindow).loadJsPromise;
    // startLoadGameJsTime 和 endLoadGameJsTime在加载js时种下
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosScriptInit, (window as any).startLoadGameJsTime);
    (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosScriptInit, (window as any).endLoadGameJsTime);
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosBoot, (window as any).endLoadGameJsTime);

    window.addEventListener("error", this.handleError, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("error", this.handleError, false);
    });
    bus.$on("show-image-scale-modal", (data: ShowImageScaleModalEvtData) => {
      this.showScaleModal = true;
      this.imageScaleSrc = data.url;
    });
  }
  // 收集cocos各个环节的加载时机
  async collectCocosTimeStamp() {
    const start = Date.now(); // 开始加载bundle的时间
    // 加载的时长
    const times = {
      loadBundleStart: start,
      loadBundleEnd: 0,
      loadBundleDuration: 0,
      loadGameSceneStart: 0,
      loadGameSceneEnd: 0,
      loadGameSceneDuration: 0,
      loadStageRootNodeStart: 0,
      loadStageRootNodeEnd: 0,
      loadStageRootNodeDuration: 0,
      loadCompListStart: 0,
      loadCompListEnd: 0,
      loadCompListDuration: 0,
    };
    const resourcesLoadDurations: any[] = []; // 收集资源加载的时长
    // 自定义观察者 收集资源加载的时长
    (window as any).yy.loader.addObserver({
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      beforeLoadBundle: () => {},
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      beforeLoadRes: () => {},
      afterLoadRes: (params: any) => {
        // 直接打点
        // console.log("yy.loader.afterLoadRes", params);
        resourcesLoadDurations.push({ url: params.url, duration: params.time });
      },
      afterLoadBundle: (params: any) => {
        // 直接打点
        console.log("yy.loader.afterLoadBundle", params);
      },
    });
    bus.$on("cocosResourcesLoaded", (val: string) => {
      // console.log("yy.loader.val...cocosResourcesLoaded", val);
      if (val === "cocosLoadQuestionBundleFinished") {
        times.loadBundleEnd = Date.now();
        times.loadBundleDuration = times.loadBundleEnd - times.loadBundleStart;
      }
      if (val === "cocosLoadGameSceneFinished") {
        times.loadGameSceneStart = times.loadBundleEnd;
        times.loadGameSceneEnd = Date.now();
        times.loadGameSceneDuration = times.loadGameSceneEnd - times.loadGameSceneStart;
      }
      if (val === "cocosLoadStageRootNodeFinished") {
        times.loadStageRootNodeStart = times.loadGameSceneEnd;
        times.loadStageRootNodeEnd = Date.now();
        times.loadStageRootNodeDuration = times.loadStageRootNodeEnd - times.loadStageRootNodeStart;
        const timer = setTimeout(() => {
          clearTimeout(timer);
          this.containerOpacity = 1;
        }, 200);
      }
      if (val === "cocosLoadCompListStart") {
        times.loadCompListStart = Date.now();
      }
      if (val === "cocosLoadCompListFinished") {
        times.loadCompListEnd = Date.now();
        times.loadCompListDuration = times.loadCompListEnd - times.loadCompListStart;
        // 打点
        // resourcesLoadDurations 收集资源加载的时长
        // loadCompListDuration
        // 距离页面打开的时间
        (window as MyWindow).monitorManager.reportLongTTILog({
          resourcesLoadDurations,
          loadCompListDuration: times.loadCompListDuration,
          loadGameSceneDuration: times.loadGameSceneDuration,
          loadStageRootNodeDuration: times.loadStageRootNodeDuration,
          loadBundleDuration: times.loadBundleDuration,
          loadPageDuration: times.loadCompListEnd - (getNavigationStartTime() || 0),
        });
      }
      if (val === "cocosInitFinished") {
        // this.$nextTick(() => {
        //   window.dispatchEvent(new Event("resize"));
        //   const timer = setTimeout(() => {
        //     console.log("300ms触发resize");
        //     clearTimeout(timer);
        //     window.dispatchEvent(new Event("resize"));
        //   }, 300);
        // });
        navigator.serviceWorker.ready.then(registration => {
          registration.active && registration.active.postMessage({ type: "cocosInitFinished" });
          // 页面销毁时 清空缓存
          this.$once("hook:beforeDestroy", () => {
            registration.active && registration.active.postMessage({ type: "pageDestroy" });
          });
        });
      }
    });
  }

  handleError(event: any) {
    // 捕获cocos的异常 抛出异常后清空包地址 再次刷新后会重新获取包地址
    // console.log('handleError', event);
    // 如果是cocos抛出了异常 将对应的包地址清空
    if (event && event.filename.includes("/cocos/")) {
      const { bundleName } = this.$store.state.template;
      bundleName && localStorage.setItem(bundleName, "");
    }
    event.preventDefault(); // 增加阻止默认事件，阻止页面报错
    // 通过addEventListener绑定的事件，不能通过return false来阻止默认行为
  }

  getContainerSize() {
    console.log("没获取到-parentEl0");
    const parentEl = (this.$refs.container as HTMLElement).parentElement;
    if (!parentEl) {
      this.containerWidth = document.body.clientWidth - 440;
      this.containerHeight = ((document.body.clientWidth - 440) * 3) / 4;
      this.$store.commit("setContainerSize", {
        width: this.containerWidth,
        height: this.containerHeight,
      });
      console.log("没获取到-parentEl");
      return;
    }
    const ratio = this.stageSize.safeHeight / this.stageSize.safeWidth;
    const { width, height } = getCocosContainerSize(parentEl, ratio, 80);
    this.containerWidth = width;
    this.containerHeight = height;
    this.$store.commit("setContainerSize", {
      width,
      height,
    });
    window.parent.postMessage(
      {
        action: "stage-size",
        data: {
          width: this.containerWidth,
          height: this.containerHeight,
          left: (parentEl.offsetWidth - this.containerWidth) / 2,
          top: (parentEl.offsetHeight - this.containerHeight) / 2,
        },
      },
      "*",
    );
  }

  setContextMenuPosition(e: MouseEvent) {
    const isButtonRight = e.button === 2;
    if (!isButtonRight) {
      return;
    }
    this.$store.commit("setContextMenuPosition", {
      top: e.clientY,
      left: e.clientX,
    });
  }

  destroyed() {
    window.removeEventListener("resize", this.getContainerSize);
  }
}
</script>

<style scoped lang="less">
.canvas-container {
  position: relative;
  font-family: Arial !important;
  font-weight: normal;
  background-color: #ffffff;
  canvas {
    background-color: #ffffff;
  }
}
</style>
