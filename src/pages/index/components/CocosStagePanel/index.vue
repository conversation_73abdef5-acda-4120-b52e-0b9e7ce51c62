<template>
  <div class="stage" ref="stage" @click="onClickStage">
    <CocosStage />
    <RulerTick />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import CocosStage from "./CocosStage.vue";
import { Mode } from "@/pages/index/store/constants";
import { parse } from "query-string";
import RulerTick from "@/pages/index/components/RulerTick/index.vue";

@Component({
  components: {
    CocosStage,
    RulerTick
  },
})
export default class CocosStagePanel extends Vue {
  get fromGroup() {
    const query = parse(window.location.search);
    return Number(query.fromGroup);
  }

  onClickStage(e: Event) {
    if (e.target === this.$refs.stage && this.$store.state.currentComponentIds.length && this.$store.state.mode === Mode.NORMAL) {
      this.$store.commit("replaceCurrentComponentIds", []);
      this.$store.dispatch("closeComponentAnimationEditor");
    }
    if (e.target === this.$refs.stage && this.$store.state.mode === Mode.NORMAL) {
      this.fromGroup &&
        window.parent.postMessage(
          {
            action: "unselectedComponents",
          },
          "*",
        );
    }
  }
}
</script>

<style scoped lang="less">
.stage {
  position: relative;
  overflow: hidden;
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: calc(100% - 360px);
}
</style>
