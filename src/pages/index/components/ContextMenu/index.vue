<template>
  <ul v-if="visible" class="context-menu" :style="menuStyle" ref="contextMenu">
    <li v-for="item in baseMenu" :key="item.label"
      :class="['item', { disabled: item.disabled }, { 'has-child': item.children && item.children.length }]"
      @click="item.onClick">
      {{ item.label }}
      <ul v-if="item.children" class="sub-menu">
        <li v-for="subItem in item.children" :key="subItem.label" :class="['item', { disabled: subItem.disabled }]"
          @click="subItem.onClick">
          {{ subItem.label }}
        </li>
      </ul>
    </li>
    <div v-if="(specialMenu.length || businessMenu.length)" class="separator" />
    <li v-for="item in specialMenu" :key="item.label" class="item" @click="item.onClick">
      {{ item.label }}
    </li>
    <li v-for="item in businessMenu" :key="item.label" class="item" @click="item.onClick">
      {{ item.label }}
    </li>
  </ul>
</template>


<script lang="ts">
import { v4 as uuidv4 } from "uuid";
import { Component, Vue, Watch } from "vue-property-decorator";
import { ClipboardTypes } from "@/pages/index/store/constants";
import copyToClipboard from "@/common/utils/copyToClipboard";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import { UpdateLevelType } from "../../store";
import { Message } from "element-ui";
import { copyHandler, pasteHandler } from "../TopBar/Operations";
import * as clipboard from "clipboard-polyfill";
import bus from "@/pages/index/common/utils/bus";
import { cloneDeep } from "lodash-es";
import { preGetId, updateId } from "../../store/getters";
@Component({
  name: "ContextMenu",
})
export default class ContextMenu extends Vue {
  canPaste = true;

  // @Watch("visible")
  // async updateCanpaste(val: any) {
  //   console.log(val);
  //   if (val) {
  //     const data = await clipboard.read();
  //     console.log(data, "11111");
  //     this.canPaste = data.length > 0;
  //   }
  // }

  get baseMenu() {
    const paste = {
      label: "粘贴",
      disabled: !this.canPaste,
      onClick: () => {
        pasteHandler();
        // this.$store.dispatch("pasteComponent");
        this.$store.commit("hideContextMenu");
      },
    };
    const copy = {
      label: "复制",
      onClick: () => {
        copyHandler(this.components);
        // this.$store.dispatch("copyComponents", this.componentIds);
        this.$store.commit("hideContextMenu");
      },
    };

    const unDeletIds = this.components.filter(component => component.deletable === false).map(i => i.id);
    const deleteOp = {
      label: "删除",
      onClick: () => {
        const { componentIds } = this;

        if (componentIds.find(id => id === this.$store.state.editingAnimationsComponentId)) {
          Message.error("请先退出组件动画编辑模式");
          return;
        }

        this.$store.dispatch("removeComponents", componentIds);
        this.$store.commit("hideContextMenu");
      },
      disabled: unDeletIds.length != 0,
    };

    const updateLevel = (type: UpdateLevelType) => {
      this.$store.dispatch("updateComponentsLevel", {
        ids: this.componentIds,
        type,
      });
      this.$store.commit("hideContextMenu");
    };

    const dragableIds = this.components.filter(component => component.dragable !== false).map(i => i.id);
    const setUnDragable = {
      label: "锁定",
      onClick: () => {
        this.$store.commit("setComponentsDragable", {
          ids: dragableIds,
          dragable: false,
        });
        this.$store.commit("hideContextMenu");
      },
      disabled: dragableIds.length === 0,
    };

    const unDragableIds = this.components.filter(component => component.dragable === false).map(i => i.id);
    const setDragable = {
      label: "取消锁定",
      onClick: () => {
        this.$store.commit("setComponentsDragable", {
          ids: unDragableIds,
          dragable: true,
        });
        this.$store.commit("hideContextMenu");
      },
      disabled: unDragableIds.length === 0,
    };

    const move = {
      label: "移动",
      children: [
        {
          label: "上移",
          onClick: () => updateLevel(UpdateLevelType.FORWARD),
        },
        {
          label: "下移",
          onClick: () => updateLevel(UpdateLevelType.BACKWARD),
        },
        {
          label: "置顶",
          onClick: () => updateLevel(UpdateLevelType.TOP),
        },
        {
          label: "置底",
          onClick: () => updateLevel(UpdateLevelType.BOTTOM),
        },
      ],
      onClick: () => {
        // noop
      },
    };

    if (this.componentsLength === 0) {
      return [paste];
    }

    const menuMap = {
      label: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      sprite: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      spine: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      cocosAni: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      group: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      default: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      cutShape: [copy, paste, deleteOp, setUnDragable, setDragable, move],
      svgShape: [copy, paste, deleteOp, move],
      specialComponent: [],
      formula: [copy, paste, deleteOp, move],
      optionComponent: [],
    };

    return (this.componentsLength === 1 && menuMap[this.components[0].type]) ? menuMap[this.components[0].type] : menuMap.default;
  }

  get businessMenu() {

    const categorySupMap: any = {
      1011: 1,
      1010: 1
    }
    const { category } = this.$store.state.template;
    if (this.componentsLength != 1 || !categorySupMap[category] ) {
      return [];
    }

    const businessMenuList = []
    const haveSubIdInAction: any = {};
    let curId = preGetId() + 1;
    const cloneBus = {
      label: "克隆",
      onClick: () => {
        console.info("克隆前:", this.components[0]);
        const cloneData = cloneDeep(this.components[0]);
        const idPre = cloneData.id;
        const newId = curId + "";

        cloneData.id = newId;
        cloneData.isCloneComp = 1;
        const idMap: any = { [idPre]: cloneData.id };
        if (cloneData.type == "group") {
          this.$store.state.componentIds.forEach((id: any) => {
            if (typeof id === "object" && id.id === idPre) {
              for (let i = 0; i < id.subIds.length; i++) {
                curId += 1;
                idMap[id.subIds[i]] = curId + "";
              }
            }
          });
        }
        delete cloneData.extra.animationsForClient;
        let isSucc = true;
        let isChangeCompId = false;

        if (cloneData.extra.animations) {
          const animations = cloneDeep(cloneData.extra.animations);
          for (const key in cloneData.extra.animations) {
            const points = cloneData.extra.animations[key].points;
            const fragments = cloneData.extra.animations[key].fragments;
            animations[key].points = {};
            animations[key].fragments = {};
            if (points) {
              for (const key1 in points) {
                const uuid = uuidv4();
                animations[key].points[uuid] = points[key1];
                if (animations[key].points[uuid].fragmentId) {
                  const preFragmentId = animations[key].points[uuid].fragmentId;
                  const uuid1 = uuidv4();
                  animations[key].points[uuid].fragmentId = uuid1;
                  if (fragments[preFragmentId]) {
                    animations[key].fragments[uuid1] = fragments[preFragmentId];
                  } else {
                    console.error("没找到preFragmentId:", preFragmentId, "异常了")
                    isSucc = false;
                  }
                } else {
                  console.error("没找到points[uuid].fragmentId:", points[uuid].fragmentId);
                  isSucc = false;
                }
              }
            }
          }
          cloneData.extra.animations = animations;
          for (const key in cloneData.extra.animations) {
            const fragments = cloneData.extra.animations[key].fragments;
            if (fragments) {
              for (const key1 in fragments) {
                if (fragments[key1] instanceof Array) {
                  for (let i = 0; i < fragments[key1].length; i++) {
                    if (fragments[key1][i].id) {
                      fragments[key1][i].id = uuidv4();
                    } else {
                      console.error("没找到id:", fragments[key1][i].id, "更换uuid了");
                      isSucc = false;
                    }
                    if (fragments[key1][i].componentId && idMap[fragments[key1][i].componentId]) {
                      haveSubIdInAction[fragments[key1][i].componentId] = 1;
                      fragments[key1][i].componentId = idMap[fragments[key1][i].componentId];
                      isChangeCompId = true;
                    }
                  }
                }
              }

            }
          }
        }
        if (!isSucc) {
          this.$message({
            type: "error",
            message: `数据异常暂无法克隆`,
            duration: 1000,
            showClose: true,
          });
          this.$store.commit("hideContextMenu");
          return;
        }
        let tipsNum = 1;
        let succStr = "<div> " + tipsNum + ". 即将克隆组件：" + idPre + "，生成组件：" + cloneData.id + '</div>';
        // for (const idOne in idMap) {
        //   if (idOne != idPre) {
        //     tipsNum += 1;
        //     succStr += "<div> " + tipsNum + ". 子组件：" + idOne + " -> " + "子组件：" + idMap[idOne] + '</div>';

        //   }
        // }
        if (isChangeCompId) {
          tipsNum += 1;
          succStr += "<div> " + tipsNum + ". 新组件中编辑动画，组件：" + idPre + "升级为组件：" + cloneData.id + "</div>";
        }

        // for (const idOSub in haveSubIdInAction) {
        //   if (idOSub != idPre) {
        //     tipsNum += 1;
        //     succStr += "<div> " + tipsNum + ". [请注意] 编辑动画中子组件id：" + idOSub + " -> 组件：" + idMap[idOSub] + "</div>";
        //   }
        // }

        const strExitId = this.getGlobalHaveTips(idMap);
        if (strExitId[idPre]) {
          tipsNum += 1;
          succStr += "<div> " + tipsNum + ".动画模式：「" + strExitId[idPre].join(",") + "」中，不添加新组件：" + cloneData.id + "</div>";
        }
        if (this.components[0].tag == "dragArea") {
          tipsNum += 1;
          succStr += "<div> " + tipsNum + ". 组件：" + cloneData.id + "为答题区，请注意调整落位坐标</div>";
        }

        // if (strExitId) {
        //   for (const strExitIdOne in strExitId) {
        //     if (strExitIdOne != idPre) {
        //       tipsNum += 1;
        //       succStr += "<div> " + tipsNum + ". [请注意] " + "原子组件id：" + strExitIdOne + " 存在全局动画模式：「" + strExitId[strExitIdOne].join(",") + "」中,新克隆子组件：" + idMap[strExitIdOne] + "将不带入</div>";
        //     }
        //   }
        // }

        this.$alert(succStr, "克隆提示", {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true,
          // showClose: false
          callback: action => {
            if (action == "confirm") {
              if (cloneData.type != "group") {
                this.$store.dispatch("addComponentNoFocus", cloneData);
              } else {
                // todo
                const subComponents: any[] = [];
                for (const key in idMap) {
                  if (key != idPre) {
                    const cData = cloneDeep(this.$store.state.componentMap[key]);
                    cData.id = idMap[key];
                    subComponents.push(cData);
                  }
                }
                this.$store.dispatch("addGroupComponentAndBus", { components: subComponents, groupComponent: cloneData });
              }
              this.$message({
                type: "success",
                message: "克隆成功",
                duration: 1000,
              });
              console.info("克隆后:", cloneData);
              updateId(this.$store.state);
            } else {
              this.$message({
                type: "info",
                message: "取消克隆",
                duration: 1000,
              });
            }
          }
        });
        this.$store.commit("hideContextMenu");
      },
    };

    const haveTagMap: any = {
      answer: 1,
      dragArea: 1,
      dragableObject: 1
    }

    // 无法同时克隆多个组件
    if (haveTagMap[this.components[0].tag]) {
      businessMenuList.push(cloneBus);
    }
    return businessMenuList;

  }
  getGlobalHaveTips(idMap: any): any {
    const timeStr: any = {};
    const config = this.$store.state.template.animationConfig;
    const labelMap: any = {};
    for (let i = 0; i < config.length; i++) {
      labelMap[config[i].value] = config[i].label;
    }
    for (const key in labelMap) {
      if (this.$store.state.moduleAnimations && this.$store.state.moduleAnimations.animations) {
        const animData = this.$store.state.moduleAnimations.animations[key];
        if (animData) {
          const fragments = animData.fragments;
          if (fragments) {
            for (const key1 in fragments) {
              if (fragments[key1] instanceof Array) {
                for (let i = 0; i < fragments[key1].length; i++) {
                  if (fragments[key1][i].componentId && idMap[fragments[key1][i].componentId]) {
                    if (!timeStr[fragments[key1][i].componentId]) {
                      timeStr[fragments[key1][i].componentId] = [];
                    }
                    timeStr[fragments[key1][i].componentId].push("'" + labelMap[key] + "'");
                  }
                }
              }
            }
          }

        }
      }
    }
    return timeStr;
  }

  get specialMenu() {
    if (this.componentsLength === 0) {
      return [];
    }

    const copyTextureUrl = {
      label: "复制图片链接",
      onClick: () => {
        const url = (this.components[0] as SpriteComponent).properties.texture;
        const success = copyToClipboard(url);
        if (success) {
          this.$message({
            type: "success",
            message: "复制成功",
            duration: 1000,
          });
        } else {
          this.$message({
            type: "error",
            message: `复制失败，请手动复制: ${url}`,
            duration: 1000,
            showClose: true,
          });
        }
        this.$store.commit("hideContextMenu");
      },
    };

    const replaceTextureUrl = {
      label: "替换图片",
      onClick: () => {
        bus.$emit("material-show");
        ImageLibrary({
          onClose: () => {
            //
          },
          onConfirm: images => {
            this.$store.commit("updateComponentProperties", {
              id: this.componentIds[0],
              newProperties: {
                texture: images[0].url,
              },
            });
          },
        });
        this.$store.commit("hideContextMenu");
      },
    };

    const copyLabelString = {
      label: "复制文本",
      onClick: () => {
        const string = (this.components[0] as LabelComponent).properties.string;
        const success = copyToClipboard(string);
        if (success) {
          this.$message({
            type: "success",
            message: "复制成功",
            duration: 1000,
          });
        } else {
          this.$message({
            type: "error",
            message: `复制失败，请手动复制: ${string}`,
            duration: 1000,
            showClose: true,
          });
        }
        this.$store.commit("hideContextMenu");
      },
    };

    const specialMenuMap = {
      label: [copyLabelString],
      sprite: [replaceTextureUrl, copyTextureUrl],
      spine: [],
      cocosAni: [],
      group: [],
      default: [],
      cutShape: [],
      svgShape: [],
      specialComponent: [],
      formula: [],
      optionComponent: []
    };

    return (this.componentsLength === 1 && specialMenuMap[this.components[0].type]) ? specialMenuMap[this.components[0].type] : specialMenuMap.default;
  }

  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get components(): Components {
    return this.componentIds.map((componentId: string) => this.$store.state.componentMap[componentId]);
  }

  get componentsLength(): number {
    return this.componentIds.length;
  }

  get menuStyle() {
    let top = this.$store.state.contextMenu.top;
    let len = (this.specialMenu.length + this.businessMenu.length + this.baseMenu.length) || 0;
    len = len * 24 + 20;
    if (top > window.innerHeight - len) {
      top = window.innerHeight - len;
    }
    return `top:${top}px;left:${this.$store.state.contextMenu.left}px`;
  }

  get visible() {
    return this.$store.state.contextMenu.visible;
  }

  // async canPaste() {
  //   const data = await clipboard.read();
  //   console.log(data, "11111");
  //   return data.length;
  //   // return this.$store.state.clipboard.type !== ClipboardTypes.NONE;
  // }

  mounted() {
    document.addEventListener("mousedown", this.onClickAway, true);
  }

  destroyed() {
    document.removeEventListener("mousedown", this.onClickAway), true;
  }

  onClickAway(e: MouseEvent) {
    console.log(e, "onClickAway");
    if (!this.visible) {
      return;
    }
    if (!(this.$refs.contextMenu as Element).contains(e.target as Element)) {
      this.$store.commit("hideContextMenu");
    }
  }
}
</script>

<style scoped lang="less">
.context-menu {
  position: fixed;
  list-style: none;
  z-index: 1000;
  background: #fff;
  padding: 5px 0;
  margin: 0;
  border-radius: 4px;
  box-shadow: 0 1px 7px rgba(0, 0, 0, 0.4);
  user-select: none;

  .item {
    position: relative;
    width: 140px;
    padding: 0 12px;
    font-size: 12px;
    color: #333;
    line-height: 22px;
    text-align: left;
    border-top: 1px solid transparent;
    border-bottom: 1px solid transparent;

    &:hover {
      background: #f4f4f4;
      border-top: 1px solid #fff;
      border-bottom: 1px solid #fff;

      .sub-menu {
        display: block;
        top: -5px;
        left: 100%;
      }
    }
  }

  .has-child::after {
    content: "";
    width: 0;
    height: 0;
    border-width: 4px 7px;
    border-style: solid;
    border-color: transparent transparent transparent rgba(0, 0, 0, 0.75);
    text-shadow: 0 4px 9px rgba(0, 0, 0, 0.34);
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
  }

  .sub-menu {
    position: absolute;
    display: none;
    list-style: none;
    background: #fff;
    padding: 5px 0;
    margin: 0;
    border-radius: 4px;
    box-shadow: 0 1px 7px rgba(0, 0, 0, 0.4);
    user-select: none;
  }

  .disabled {
    color: #aaa;
    pointer-events: none;
  }

  .separator {
    border-bottom: 1px solid #e1e1e1;
    margin: 3px 5px;
  }
}
</style>
