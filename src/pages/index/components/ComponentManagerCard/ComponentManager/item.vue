<template>
  <div class="component-manager-item" :class="[{ active: isActive }]" @mousedown.stop="handleMouseDown(item.id, isSub)">
    <div
      :class="[
        'component-manager-item-self',
        {
          dim: isDim,
          'non-editable': !isEditable,
          'sub-padding': isSub,
        },
      ]"
      @click="replaceId"
    >
      <span v-if="!isSub" class="component-manager-item-prefix">
        <i v-if="item.subIds.length" @click="subsVisible = !subsVisible" :class="subsVisible ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" />
      </span>
      <i class="" />
      <span class="component-manager-item-self-info">ID: {{ item.id }} </span>
      <span class="component-manager-item-self-type">{{ componentType }}</span>
      <span v-if="component.type === 'sprite'" class="component-manager-item-self-sprite">
        <img class="component-manager-item-self-sprite-img" :src="component.properties.texture" />
      </span>
      <span v-else-if="component.type === 'spine'" class="component-manager-item-self-spine">
        <img class="component-manager-item-self-spine-img" :src="component.spineData.cover" />
      </span>
      <span v-else-if="component.type === 'formula'" class="component-manager-item-self-formula">
        <img class="component-manager-item-self-formula-img" :src="component.properties.url" />
      </span>
      <span v-else-if="component.type === 'richTextSprite'" class="component-manager-item-self-formula">
        <img class="component-manager-item-self-formula-img" :src="component.properties.texture" />
      </span>
      <!-- && component.properties.textureArray && component.properties.textureArray.length > 0 -->
      <span v-else-if="component.type === 'label'" class="component-manager-item-self-label">
        <!-- <img
          class="component-manager-item-self-label-img"
          :src="component.properties.textureArray[0]"
        /> -->
        {{ labelStr }}
      </span>
      <span v-else-if="component.type === 'cocosAni'" class="component-manager-item-self-cocosAni">
        <img class="component-manager-item-self-cocosAni-img" :src="component.cocosAniData.cover" />
      </span>
      <div class="ops">
        <i v-if="!isEditable" class="el-icon-lock op" />
        <el-tooltip effect="dark" content="删除" placement="right" v-if="component.deletable !== false">
          <i :class="['el-icon-delete', 'op']" @click.stop="remove" />
        </el-tooltip>
      </div>
    </div>
    <div v-if="subsVisible">
      <div
        v-for="(subId, index) in reversedSubIds"
        :key="subId"
        class="sub-item-wrapper"
        :class="{
          'drag-hover': subDropIndex === index && !!draggingSubId,
          up: isSubMoveUp,
        }"
        draggable="true"
        @dragstart="handleSubDragStart($event, subId, index)"
        @dragend="handleSubDragEnd"
        @dragenter="handleSubDragEnter($event, index)"
        @dragover="handleSubDragOver($event)"
      >
        <component-manager-item :is-sub="true" :item="{ id: subId, subIds: [] }" @mousedownItem="handleMouseDown" />
        <div class="drag-line"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Message } from "element-ui";
import { Component, Prop, Vue } from "vue-property-decorator";
import { hotkeysIsMul, hotkeysIsShift } from "../../TopBar/Operations";
@Component({
  name: "component-manager-item",
})
export default class ComponentManagerItem extends Vue {
  subsVisible = true;
  private draggingSubId: string | null = null;
  private subDropIndex = -1;
  private subDragSourceIndex = -1;

  @Prop({
    required: true,
  })
  item!: { id: string; subIds: string[] };

  @Prop({
    default: false,
  })
  isSub!: boolean;

  get isActive() {
    return this.$store.state.currentComponentIds.includes(this.item.id);
  }
  get component(): Component {
    return this.$store.state.componentMap[this.item.id];
  }
  get componentType() {
    const types: any = {
      label: "文本",
      h5Label: "富文本",
      richTextSprite: "新文本",
      sprite: "图片",
      spine: "动效",
      cocosAni: "cocos动画",
      group: "组合",
      cutShape: "图形",
      keyboard: "数字键盘",
      graphics: "绘图",
      counter: "计数器",
      voice: "语音",
      matchboard: "火柴",
      clock: "时钟",
      formula: "公式",
      keyboardEnglish: "英文键盘",
      speaker: "音频组件",
      brush: "画笔组件",
      svgShape: "形状",
      shape: "形状(新)",
      readcom: "阅读理解",
      voiceSpeak: "语音说话",
      listenRetell: "听后转述",
      listenAnswer: "听后回答",
      microp: "麦克风",
      enPK: "小英pk题组",
      enGroupPK: "小英全员pk题组",
      hanoiTower: "汉诺塔",
      programQuestion: "编程操控",
      cutPicture: "切割题",
      klotskiQuestion: "华容道",
    };
    if (this.component.subType) {
      return types[this.component.subType] || this.component.componentLabel;
    }
    return types[this.component.type] || this.component.componentLabel;
  }

  get labelStr() {
    const str = (this.component.properties && this.component.properties.str) || "";
    let subStr = "";
    if (str) {
      const re = /(<.+?>)|[^>]+(?=<)/gi;
      const ff = str.match(re);
      for (let i = 0; i < ff.length; i++) {
        if (ff[i].indexOf("<") == -1) {
          subStr += ff[i];
          if (subStr.length >= 5) {
            break;
          }
        }
      }
      if (subStr.length > 5) {
        subStr = subStr.substring(0, 5);
      }
    }
    return subStr;
  }
  get isDim() {
    return this.component.properties.active === false;
  }
  get isEditable() {
    return !(this.component.editable === false);
  }

  get reversedSubIds(): string[] {
    return [...this.item.subIds].reverse();
  }

  get isSubMoveUp(): boolean {
    return this.subDropIndex < this.subDragSourceIndex;
  }

  handleSubDragStart(e: DragEvent, subId: string, index: number) {
    this.draggingSubId = subId;
    this.subDragSourceIndex = index;
    // 阻止事件冒泡，以免触发父级的拖拽
    e.stopPropagation();

    // --- 创建自定义拖拽图像 ---
    const dragImage = document.createElement("div");
    const dragImageParent = document.createElement("div");
    dragImage.style.padding = "10px 8px";
    dragImage.style.background = "#333";
    dragImage.style.borderRadius = "4px";
    dragImage.style.display = "flex";
    dragImage.style.fontSize = "14px";
    dragImage.style.color = "#fff";
    dragImage.style.alignItems = "center";
    dragImage.style.justifyContent = "center";
    dragImage.textContent = `1 个组件`; // 子组件总是单个拖拽
    dragImageParent.style.border = "12px solid transparent";
    dragImageParent.appendChild(dragImage);
    dragImageParent.style.position = "absolute";
    dragImageParent.style.top = "-1000px";
    dragImageParent.style.left = "-1000px";
    document.body.appendChild(dragImageParent);

    if (e.dataTransfer) {
      e.dataTransfer.setDragImage(dragImageParent, 0, 0);
      e.dataTransfer.dropEffect = 'move';
      e.dataTransfer.effectAllowed = 'move';
    }

    requestAnimationFrame(() => {
      document.body.removeChild(dragImageParent);
    });

    // 添加 dragging class
    (e.currentTarget as HTMLElement).classList.add("dragging");
  }

  handleSubDragOver(e: DragEvent) {
    e.preventDefault();
    if (e.dataTransfer) {
      e.dataTransfer.dropEffect = 'move';
    }
  }

  handleSubDragEnter(e: DragEvent, index: number) {
    e.preventDefault();
    this.subDropIndex = index;
  }

  handleSubDragEnd(e: DragEvent) {
    if (this.draggingSubId === null || this.subDropIndex === -1) {
      return;
    }

    // 移除 dragging class
    (e.currentTarget as HTMLElement).classList.remove("dragging");

    const subIds = [...this.item.subIds];
    const reversedSubIds = [...subIds].reverse();

    const sourceId = this.draggingSubId;
    const targetId = reversedSubIds[this.subDropIndex];

    if (sourceId && targetId && sourceId !== targetId) {
      const sourceIndex = subIds.indexOf(sourceId);
      console.log("%c Line:232 🌰 sourceIndex", "color:#fca650", sourceIndex);
      const targetIndex = subIds.indexOf(targetId);
      console.log("%c Line:234 🥛 targetIndex", "color:#e41a6a", targetIndex);

      this.$store.commit("updateSubComponentOrder", [{ id: this.draggingSubId, newIndex: targetIndex, oldIndex: sourceIndex }]);
    }

    // 重置状态
    this.draggingSubId = null;
    this.subDropIndex = -1;
    this.subDragSourceIndex = -1;
  }

  handleMouseDown(id: string, isSub: boolean) {
    // e.preventDefault();
    // e.stopPropagation();
    console.log("mousedownItem", id, isSub);
    this.$emit("mousedownItem", id, isSub);
  }

  replaceId() {
    if (!this.isEditable) {
      return;
    }
    // 多选
    const isMul = hotkeysIsMul();
    // 按住shift
    const isShift = hotkeysIsShift();
    if (isMul) {
      // 获取当前选中的组件id
      const arr = [];
      for (let i = 0; i < this.$store.state.currentComponentIds.length; i++) {
        arr.push(this.$store.state.currentComponentIds[i]);
      }
      // 获取所有组件id
      const delItem = [];
      // 获取所有组件id的父级id
      const deItemParent: any = {};
      for (const key in this.$store.state.componentIds) {
        if (typeof this.$store.state.componentIds[key] != "string") {
          for (const nKey of this.$store.state.componentIds[key].subIds) {
            deItemParent[nKey] = this.$store.state.componentIds[key].id;
            delItem.push(nKey);
          }
        }
      }
      // 过滤所有子组件id的 ，选出过滤父级id

      const delItemParentList = [];
      for (let i = 0; i < arr.length; i++) {
        if (delItem.includes(arr[i])) {
          delItemParentList.push(deItemParent[arr[i]]);
          arr.splice(i, 1);
          i--;
        }
      }
      // 过滤所有组件id的父级id
      if (delItemParentList.length > 0) {
        for (let i = 0; i < arr.length; i++) {
          if (delItemParentList.includes(arr[i])) {
            arr.splice(i, 1);
            i--;
          }
        }
      }

      // 如果当前选中的组件id是子组件id，则提示
      if (delItem.includes(this.item.id)) {
        Message.error("多选时，不可选中子组件");
        return;
      }

      const arr1 = [];

      // 按住shift
      if (isShift) {
        let isHad = false;
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] == this.item.id) {
            isHad = true;
          }
          arr1.push(arr[i]);
        }

        let lastIndex = -1;
        let currentIndex = -1;
        // 获取当前选中的组件id的索引
        if (arr.length > 0) {
          for (let i = 0; i < this.$store.state.componentIds.length; i++) {
            let id = this.$store.state.componentIds[i];
            if (typeof id !== "string") {
              id = id.id;
            }
            if (id == arr[arr.length - 1]) {
              lastIndex = i;
            }
            if (id == this.item.id) {
              currentIndex = i;
            }
          }
        }
        // 如果当前选中的组件id的索引不为-1，则获取当前选中的组件id的索引和最后一个组件id的索引
        if (lastIndex != -1 && currentIndex != -1) {
          if (lastIndex <= currentIndex) {
            for (let i = lastIndex; i <= currentIndex; i++) {
              let id = this.$store.state.componentIds[i];
              if (typeof id !== "string") {
                id = id.id;
              }
              if (!arr1.includes(id)) {
                arr1.push(id);
              }
            }
          } else {
            for (let i = lastIndex; i >= currentIndex; i--) {
              let id = this.$store.state.componentIds[i];
              if (typeof id !== "string") {
                id = id.id;
              }
              if (!arr1.includes(id)) {
                arr1.push(id);
              }
            }
          }
        } else {
          // 如果当前选中的组件id的索引为-1，则添加当前选中的组件id
          if (!isHad) {
            arr1.push(this.item.id);
          }
        }
        this.$store.commit("replaceCurrentComponentIds", arr1);
      } else {
        let isHad = false;
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] == this.item.id) {
            isHad = true;
          } else {
            arr1.push(arr[i]);
          }
        }

        if (!isHad) {
          arr1.push(this.item.id);
        }
      }
      this.$store.commit("replaceCurrentComponentIds", arr1);
    } else {
      this.$store.commit("replaceCurrentComponentIds", [this.item.id]);
    }
  }
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }
  remove() {
    const { componentIds } = this;
    if (componentIds.find(id => id === this.$store.state.editingAnimationsComponentId)) {
      Message.error("请先退出组件动画编辑模式");
      return;
    }
    this.$store.dispatch("removeComponent", this.item.id);
  }
}
</script>

<style scoped lang="less">
.component-manager-item {
  background: #fff;
  position: relative;

  .sub-item-wrapper {
    position: relative;
    &.up {
      .drag-line {
        top: 2px;
        bottom: unset;
      }
    }
    &.dragging {
      opacity: 0.8;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  .sub-item-wrapper.drag-hover {
    .drag-line {
      display: block;
    }
  }

  .drag-line {
    display: none;
    position: absolute;
    width: calc(100% - 32px);
    background: rgba(0, 0, 0, 0.3);
    left: 16px;
    bottom: 2px;
    height: 2px;
    background: #24b366;
    &::before {
      content: "";
      position: absolute;
      width: 2px;
      height: 2px;
      background: #fff;
      border: 2px solid #4ec08d;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translate(0, -50%);
      border-radius: 50%;
      transition: all 0.2s ease;
    }
  }

  &:hover {
    background: #e9f7ef5a;

    .ops {
      display: block;
    }
  }

  &.active {
    background: #e9f7ef;
    // 子节点的active样式
    > .component-manager-item-self .component-manager-item-self-type {
      color: #42c57a !important;
    }
    > .component-manager-item-self .component-manager-item-self-info {
      color: #42c57a !important;
    }
  }

  &-self {
    height: 30px;
    line-height: 30px;

    cursor: pointer;
    border: 1px solid transparent;

    .ops {
      display: none;
      position: absolute;
      right: 10px;
      top: 0;
      font-size: 14px;
      height: 32px;

      .op {
        // cursor: pointer;
        line-height: 32px;
        padding: 0 5px;
      }
    }

    &-info {
      font-size: 14px;
    }

    &-type {
      font-size: 12px;
      // color: #666;
    }

    &-sprite {
      &-img {
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        height: 28px;
        vertical-align: bottom;
      }
    }

    &-spine {
      &-img {
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        height: 28px;
        vertical-align: bottom;
      }
    }

    &-formula {
      &-img {
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        height: 28px;
        vertical-align: bottom;
      }
    }

    &-label {
      &-img {
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        height: 28px;
        vertical-align: bottom;
        background-color: #8d92966c;
      }
    }

    &-cocosAni {
      &-img {
        padding: 5px;
        box-sizing: border-box;
        display: inline-block;
        height: 28px;
        vertical-align: bottom;
      }
    }
  }

  &-prefix {
    display: inline-block;
    width: 24px;
    height: 16px;
    text-align: center;
    font-size: 12px;
    line-height: 16px;
  }

  .dim {
    opacity: 0.3;
  }

  .non-editable {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .sub-padding {
    padding-left: 36px;
  }

  .component-manager-item-self-formula-img {
    filter: brightness(0%);
  }

  .component-manager-item-self {
    width: calc(100% - 32px);
    overflow: hidden;
    white-space: nowrap;
  }
}
</style>
