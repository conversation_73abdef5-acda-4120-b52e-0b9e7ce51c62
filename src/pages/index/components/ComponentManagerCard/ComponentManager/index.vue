<!--
 * @FilePath: /interactive-question-editor/src/pages/index/components/ComponentManagerCard/ComponentManager/index.vue
 * <AUTHOR> wanghuan
 * @description  : 
 * @warn         : 
-->
<template>
  <div class="component-manager" v-click-outside="handleClickOutside">
    <div
      v-for="(itemInReversedList, index) in componentIds"
      :key="itemInReversedList.id"
      class="component-item"
      :class="{
        dragging: isDragging(itemInReversedList.id),
        'drag-hover': dropIndex === index && hasDraggingItems,
        up: isMoveUp,
      }"
      :data-id="itemInReversedList.id"
      @dragenter.stop="draglineEnter($event, index)"
      @dragleave.stop="draglineLeave($event, index)"
      @dragstart.stop="handleDragStart($event, itemInReversedList.id)"
      @drop.stop="draglineDrop($event, index)"
      @dragend.stop="handleDragEnd($event, index)"
    >
      <Item :item="itemInReversedList" :data-id="itemInReversedList.id" @mousedownItem="handleMousedown" :draggable="!!dragHandlerId" />
      <div class="drag-line" v-show="isCanDragTo(itemInReversedList.id, index)"></div>
    </div>
    <span v-if="!componentIds.length" class="component-manager-empty">空</span>
  </div>
</template>

<script lang="ts">
import { cloneDeep } from "lodash";
import { reverse } from "lodash-es";
import clickOutside from "vue-click-outside";
import { Component, Vue, Watch } from "vue-property-decorator";
import Item from "./item.vue";

@Component({
  directives: {
    "click-outside": clickOutside,
  },
  components: {
    Item,
  },
})
export default class ComponentManager extends Vue {
  private draggingIds: string[] = [];
  dropIndex = -1;
  dragHandlerId = "";

  get draggable() {
    const { currentComponentIds, componentIds } = this.$store.state;
    let dragIds = [...currentComponentIds];
    if (this.dragHandlerId) {
      dragIds.push(this.dragHandlerId);
    }
    dragIds = [...new Set(dragIds)];
    // 判断选中的组件数量是否大于1 是否是子组件
    const hasSubComponent = dragIds.some((id: string) => {
      return !componentIds.find((item: any) => item === id || item.id === id);
    });
    // console.log("zujianguanli--draggable", hasSubComponent, currentComponentIds.length >= 1 && !hasSubComponent);
    // 选中的组件数量大于1 且不是子组件
    return dragIds.length >= 1 && !hasSubComponent;
  }

  get componentIds() {
    return [...this.$store.getters.componentIdsAllObjectType].reverse();
  }

  isDragging(id: string): boolean {
    return this.draggingIds.includes(id);
  }

  isSubComponent(id: string): boolean {
    const { componentIds } = this.$store.state;
    return !componentIds.find((item: any) => item === id || item.id === id);
  }

  handleClickOutside() {
    this.clearDragData();
  }
  isCanDragTo(id: string, index: number) {
    // console.log("zujianguanli--isCanDragTo", id, index);
    // id是否被选中
    // 拖拽的元素是否被选中
    const dragHandlerIdIsSelected = this.$store.state.currentComponentIds.includes(this.dragHandlerId);
    if (!dragHandlerIdIsSelected) {
      return true;
    }
    // console.log("zujianguanli--isCanDragTo-2", id, index);
    const isSelected = this.$store.state.currentComponentIds.includes(id);
    // if (this.draggingIds.includes(id)) return false;
    const maxIndex = Math.max(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    const minIndex = Math.min(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    // console.log("zujianguanli--isCanDragTo-index", index, isSelected, id, maxIndex, minIndex);
    if (index === maxIndex) {
      return false;
    }
    if (isSelected) {
      // 上一个组件是否被选中
      const prevComponentId = this.componentIds[index - 1]?.id;
      const isPrevSelected = this.$store.state.currentComponentIds.includes(prevComponentId);
      // 下一个组件是否被选中
      const nextComponentId = this.componentIds[index + 1]?.id;
      const isNextSelected = this.$store.state.currentComponentIds.includes(nextComponentId);
      // console.log("zujianguanli--isCanDragTo", isSelected, isPrevSelected, isNextSelected, id, prevComponentId, nextComponentId);
      // 上移 isPrevSelected || 下移 isNextSelected
      const isMoveUp = index > maxIndex;
      if ((isMoveUp && isPrevSelected) || (!isMoveUp && isNextSelected)) {
        return false;
      } else {
        return true;
      }
    }
    return true;
  }

  get hasDraggingItems(): boolean {
    // return this.$store.state.currentComponentIds.length > 0 || !!this.dragHandlerId;
    return !!this.dragHandlerId;
  }

  @Watch("hasDraggingItems")
  onComponentIdsChange(newVal: AnalyserOptions) {
    // console.log("zujianguanli--onComponentIdsChange", newVal);
    if (!newVal) {
      this.clearDragData();
    }
  }

  handleDragStart(e: DragEvent) {
    const { currentComponentIds } = this.$store.state;
    // console.log("zujianguanli--handleDragStart-dragHandlerId", this.dragHandlerId);
    if (!this.dragHandlerId) return;
    if (!currentComponentIds.includes(this.dragHandlerId) && this.dragHandlerId) {
      this.draggingIds = [this.dragHandlerId];
    } else {
      this.draggingIds = cloneDeep(this.$store.state.currentComponentIds || []);
    }
    // console.log("zujianguanli--handleDragStart", this.draggingIds);
    if (!this.draggingIds.length) return;
    // 创建一个自定义的拖拽图像
    const dragImage = document.createElement("div");
    const dragImageParent = document.createElement("div");
    dragImage.style.padding = "10px 8px";
    dragImage.style.background = "#333";
    dragImage.style.borderRadius = "4px";
    dragImage.style.display = "flex";
    dragImage.style.fontSize = "14px";
    dragImage.style.color = "#fff";
    dragImage.style.alignItems = "center";
    dragImage.style.justifyContent = "center";
    dragImage.textContent = `${this.draggingIds.length} 个组件`;
    dragImageParent.style.border = "12px solid transparent";
    dragImageParent.appendChild(dragImage);
    dragImageParent.style.position = "absolute";
    dragImageParent.style.top = "-1000px";
    dragImageParent.style.left = "-1000px";
    document.body.appendChild(dragImageParent);

    // 设置拖拽图像
    e.dataTransfer?.setDragImage(dragImageParent, 0, 0);

    // 在下一帧移除临时元素
    requestAnimationFrame(() => {
      document.body.removeChild(dragImageParent);
    });
  }

  draglineEnter(e: DragEvent, index: number) {
    if (!this.hasDraggingItems) return;
    this.dropIndex = index;
    // console.log("zujianguanli--draglineEnter-dropIndex", index);
    // this.dropIndex = -1;
    e.preventDefault();
  }

  isOutSide(e: DragEvent) {
    const { clientX, clientY } = e;
    // console.log("zujianguanli--draglineEnter", clientX, clientY);
    // 判断是否离开了组件
    // this.$el的边界
    const elRect = this.$el.getBoundingClientRect();
    const isOutside = clientX < elRect.left || clientX > elRect.right || clientY < elRect.top || clientY > elRect.bottom;
    // console.log("zujianguanli--draglineLeave-isOutside", isOutside);
    return isOutside;
  }

  draglineLeave(e: DragEvent, index: number) {
    if (!this.hasDraggingItems) return;
    if (index === this.dropIndex) return;
    // this.dropIndex = -1;
    e.preventDefault();
  }

  handleMousedown(id: string, isSub: any) {
    // console.log("zujianguanli--handleMousedown", id, isSub, "dragHandlerId", this.dragHandlerId);
    // 判断是不是子组件
    if (isSub) {
      this.clearDragData();
      // return;
    } else {
      this.dragHandlerId = id;
    }
    // console.log("zujianguanli--handleMousedown", id, isSub, "dragHandlerId", this.dragHandlerId);
  }

  clearDragData() {
    this.dragHandlerId = "";
    this.draggingIds = [];
    this.dropIndex = -1;
  }

  get isMoveUp() {
    // 选中的元素
    // 获取this.draggingIds的最大和最小的index
    const maxIndex = Math.max(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    const minIndex = Math.min(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    // dropIndex在maxIndex和minIndex之间
    // if (this.dropIndex > maxIndex && this.dropIndex < minIndex) {
    //   console.log("zujianguanli--handleDragEnd--dropIndex在maxIndex和minIndex之间");
    // }
    let isMoveToUp = false;
    if (this.dropIndex < minIndex) {
      isMoveToUp = true;
    }
    return isMoveToUp;
  }

  handleDragEnd(e: DragEvent) {
    if (!this.hasDraggingItems) return;
    // console.log("zujianguanli--handleDragEnd", this.draggingIds, this.dropIndex);
    //dropIndex对应的id
    const dropId = this.componentIds[this.dropIndex]?.id;
    // 如果离开了组件 则不执行 如果不允许拖拽到该位置 则不执行
    if(this.isOutSide(e) || !this.isCanDragTo(dropId, this.dropIndex)) {
      this.clearDragData();
      return;
    }

    // if (this.dropIndex === -1) return;
    // 选中的元素
    let tempComponentIds = JSON.parse(JSON.stringify(this.componentIds));
    // console.log("zujianguanli--handleDragEnd", JSON.stringify(tempComponentIds), this.dropIndex);
    // console.log("zujianguanli--handleDragEnd-dropIndex", this.dropIndex, this.draggingIds);
    const idIndexArray: any[] = [];
    // 获取this.draggingIds的最大和最小的index
    // const maxIndex = Math.max(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    const minIndex = Math.min(...this.draggingIds.map(id => this.componentIds.findIndex((item: any) => item.id === id)));
    // console.log("zujianguanli--handleDragEnd--maxIndex", maxIndex, minIndex);
    // dropIndex在maxIndex和minIndex之间
    // if (this.dropIndex > minIndex && this.dropIndex < maxIndex) {
    //   console.log("zujianguanli--handleDragEnd--dropIndex在maxIndex和minIndex之间");
    //   // return;
    // }
    let isMoveToUp = false;
    if (this.dropIndex < minIndex) {
      isMoveToUp = true;
    }
    // 先移除this.draggingIds中的元素，然后再插入
    // const tempRemoveIds = tempComponentIds.filter((item: any) => this.draggingIds.includes(item.id) && item.id !== dropId);
    const willRemoveIds = tempComponentIds.filter((item: any) => this.draggingIds.includes(item.id));
    tempComponentIds = tempComponentIds.filter((item: any) => !this.draggingIds.includes(item.id) || item.id === dropId);
    
    // console.log("zujianguanli--handleDragEnd--tempComponentIds", JSON.stringify(tempComponentIds), JSON.stringify(tempRemoveIds));
    // 获取target顺序
    let targetIndex = tempComponentIds.findIndex((item: any) => item.id === dropId);
    // console.log("zujianguanli--handleDragEnd--targetIndex", targetIndex, isMoveToUp);
    // 解决间隔选中 移到最小索引位置的问题 [9，8，7，6，5，4，3，2，1] 选中8、3、2，移动到8的位置 应该是[9，8，3，2，7，6，5，4，1] 不加这个判断逻辑移动到8则targetIndex为-1导致拖拽失败
    // if (this.dropIndex === minIndex) {
    //   targetIndex = minIndex - 1;
    // }
    // targetIndex如果为-1 有可能是因为targetIndex对应的被选中了

    if (targetIndex === -1) {
      this.clearDragData();
      return;
    }
    if (isMoveToUp) {
      targetIndex--;
    }

    willRemoveIds.forEach((idItem: any, index: number) => {
      if (idItem === dropId) {
        console.log('zujianguanli--handleDragEnd--idItem === dropId', '不用移动');
      } else {
        // 在目标位置插入元素
        tempComponentIds.splice(targetIndex + index + 1, 0, idItem);
      }
    });
    // console.log("zujianguanli--handleDragEnd--tempComponentIds", JSON.stringify(tempComponentIds));
    // 重新获取顺序
    // console.log("zujianguanli--handleDragEnd--componentIdsAllObjectType", JSON.stringify(this.$store.getters.componentIdsAllObjectType));
    const reverseTempComponentIds = reverse(tempComponentIds);
    // console.log("zujianguanli--handleDragEnd--reverseTempComponentIds", JSON.stringify(reverseTempComponentIds));
    this.$store.getters.componentIdsAllObjectType.forEach((item: { id: any }, index: any) => {
      const id = item.id;
      const newIndex = reverseTempComponentIds.findIndex((item: any) => item.id === id);
      const sourceIndex = index;
      idIndexArray.push({
        id,
        oldIndex: sourceIndex,
        newIndex
      });
    });
    // console.log(
    //   "zujianguanli--idIndexArray",
    //   idIndexArray,
    //   "拖拽的idIndexArray",
    //   idIndexArray.filter((item: any) => this.draggingIds.includes(item.id)),
    // );
    // this.$store.commit("updateComponentOrder", [{ id: draggedItemId, oldIndex: originalOldIndex, newIndex: originalNewIndex }]);
    // 只传this.draggingIds中的元素
    this.$store.commit(
      "updateComponentOrder",
      idIndexArray.filter((item: any) => this.draggingIds.includes(item.id)),
    );
    this.clearDragData();
  }
  draglineDrop(_e: DragEvent, index: number) {
    if (!this.hasDraggingItems) return;
    // console.log("zujianguanli--draglineDrop");
    if (this.dropIndex === index) return;
    this.clearDragData();
  }
}
</script>

<style scoped lang="less">
.component-manager {
  max-height: 300px;
  text-align: left;
  overflow-x: hidden;
  overflow-y: auto;
  &-empty {
    color: #aaa;
    font-size: 12px;
  }
}

.component-item {
  cursor: move;
  user-select: none;
  transition: transform 0.2s ease;
  position: relative;
  &.drag-hover {
    .drag-line {
      display: block;
    }
  }
  &.up {
    .drag-line {
      top: 2px;
      bottom: unset;
    }
  }

  &.dragging {
    opacity: 0.8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  .drag-line {
    display: none;
    position: absolute;
    width: calc(100% - 32px);
    background: rgba(0, 0, 0, 0.3);
    left: 16px;
    bottom: 2px;
    height: 2px;
    background: #24b366;
    &::before {
      content: "";
      position: absolute;
      width: 2px;
      height: 2px;
      background: #fff;
      border: 2px solid #4ec08d;
      position: absolute;
      left: 0px;
      top: 50%;
      transform: translate(0, -50%);
      border-radius: 50%;
      transition: all 0.2s ease;
    }
  }
}
</style>
