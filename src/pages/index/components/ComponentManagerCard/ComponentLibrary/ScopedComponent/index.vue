<template>
  <div class="shape-wrapper" @keydown.prevent>
    <!-- <ShapeToolbar :visible="visible" :max-shap-count="1" @changeVisible="changeVisible" /> -->
    <div class="shape-dialog-wrapper">
      <div class="shape-dialog" :style="nativeStyle">
        <img :style="imageStyle" :src="imageStyle.blobUrl" alt="" @mousedown.stop="startDrag" @mousemove.stop="drag" @mouseup.stop="stopDrag" @mouseleave.stop="stopDrag" />
      </div>
      <div class="action-wrapper">
        <el-button size="small" plain @click.stop="handleCancel" :disabled="confirming">取消</el-button>
        <el-button size="small" type="success" plain @click.stop="handleConfirm" :loading="confirming">确定</el-button>
      </div>
      <div class="shape-form-wrapper">
        <div class="show-component-btn">
          {{ `参考选项组件ID: ${scopedComponentData.id}` }}
        </div>
        <el-collapse :value="collapseValue">
          <el-collapse-item title="基础属性" name="base-properties1">
            <span class="tipsPos" v-if="imageStyle.angle != 0 && scopedComponentData.type == 'dragQuestion'"> 当前元素存在旋转角度，按照非旋转状态计算 </span>
            <div class="position-editor">
              <div class="flex">
                <span class="label">X</span>
                <div class="inputDiv" @keydown.up.prevent="handleUpArrow('x')" @keydown.down.prevent="handleDownArrow('x')">
                  <el-input :value="X" :readonly="true" size="small" type="number" />
                  <svg
                    @click.stop="handleUpArrow('x')"
                    class="arrow-up"
                    style="width: 0.85em; height: 0.85em; vertical-align: middle; fill: currentColor; overflow: hidden"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3336"
                  >
                    <path d="M65.582671 288.791335l446.417329 446.41733 446.417329-446.41733z" p-id="3337"></path>
                  </svg>
                  <svg
                    @click.stop="handleDownArrow('x')"
                    class="arrow-down"
                    style="width: 0.85em; height: 0.85em; vertical-align: middle; fill: currentColor; overflow: hidden"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3336"
                  >
                    <path d="M65.582671 288.791335l446.417329 446.41733 446.417329-446.41733z" p-id="3337"></path>
                  </svg>
                </div>
              </div>
              <div class="flex">
                <span class="label">Y</span>
                <div class="inputDiv" @keydown.up.prevent="handleUpArrow('y')" @keydown.down.prevent="handleDownArrow('y')">
                  <el-input :value="Y" :readonly="true" size="small" type="number" />
                  <svg
                    @click.stop="handleUpArrow('y')"
                    class="arrow-up"
                    style="width: 0.85em; height: 0.85em; vertical-align: middle; fill: currentColor; overflow: hidden"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3336"
                  >
                    <path d="M65.582671 288.791335l446.417329 446.41733 446.417329-446.41733z" p-id="3337"></path>
                  </svg>
                  <svg
                    @click.stop="handleDownArrow('y')"
                    class="arrow-down"
                    style="width: 0.85em; height: 0.85em; vertical-align: middle; fill: currentColor; overflow: hidden"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="3336"
                  >
                    <path d="M65.582671 288.791335l446.417329 446.41733 446.417329-446.41733z" p-id="3337"></path>
                  </svg>
                </div>
              </div>
            </div>
          </el-collapse-item>
          <el-collapse-item title="操作" name="oper-properties">
            <div class="flex" style="margin-right: 16px">
              <span class="label">全部更改</span>
              <el-switch style="margin-left: 8px" v-model="chooseAll" />
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import bus from "@/pages/index/common/utils/bus";
import { Component, Prop, Vue } from "vue-property-decorator";
import { cloneDeep } from "lodash-es";
import { types } from "@/pages/index/store/modules/componentManagerCard";
@Component({
  components: {},
})
export default class ScopedComponent extends Vue {
  @Prop({
    required: true,
  })
  scopedComponentData!: { callBack: Function; id: string; state: number; index: number; type: string };

  confirming = false;

  collapseValue = ["base-properties1", "oper-properties"];
  chooseAll = false;
  boundingClientRect = {
    width: 500,
    height: 500,
    left: 100,
    top: 100,
  };
  editShapeData: any = {
    widgetCanDrag: true,
    widgetCanResize: true,
    widgetCanRotate: false,
    maxWidgetCount: 1,
    addListenerGlobal: false,
  };
  defaultShapeData: any = {
    widgetCanDrag: true,
    widgetCanResize: true,
    widgetCanRotate: false,
    maxWidgetCount: 1,
    addListenerGlobal: false,
  };

  imageStyle = {
    width: `0px`,
    height: `0px`,
    left: `0px`,
    top: `0px`,
    position: "absolute",
    w: 0,
    h: 0,
    x: 0,
    y: 0,
    offectX: 0,
    offectY: 0,
    orginW: 0,
    orginH: 0,
    blobUrl: "",
    angle: 0,
  };

  startPosition = {
    x: 0.0,
    y: 0.0,
  };
  magDistance = 100;
  get scale() {
    return this.containerWidth / 1280;
  }

  get X(): number {
    return Number(((this.imageStyle.x + this.imageStyle.offectX) / this.scale).toFixed(2));
  }
  get Y(): number {
    return Number(((this.imageStyle.y + this.imageStyle.offectY) / this.scale).toFixed(2));
  }

  hasDecimal(num: number) {
    return num % 1 !== 0;
  }

  handleUpArrow(key: string) {
    let offsetX = 0;
    let offsetY = 0;
    const X = this.X;
    const Y = this.Y;
    if (key == "x") {
      offsetX = 1;
      if (this.hasDecimal(X)) {
        offsetX = Math.ceil(X) - X;
      }
    } else {
      offsetY = 1;
      if (this.hasDecimal(Y)) {
        offsetY = Math.ceil(Y) - Y;
      }
    }

    const nextX = (this.X + offsetX) * this.scale - this.imageStyle.offectX - this.imageStyle.x;
    const nextY = (this.Y + offsetY) * this.scale - this.imageStyle.offectY - this.imageStyle.y;
    this.onMove(nextX, nextY);
  }
  handleDownArrow(key: string) {
    let offsetX = 0;
    let offsetY = 0;
    const X = this.X;
    const Y = this.Y;
    if (key == "x") {
      offsetX = -1;
      if (this.hasDecimal(X)) {
        offsetX = parseInt(X+"") - X;
      }
    } else {
      offsetY = -1;
      if (this.hasDecimal(Y)) {
        offsetY = parseInt(Y+"")  - Y;
      }
    }
    const nextX = (this.X + offsetX) * this.scale - this.imageStyle.offectX - this.imageStyle.x;
    const nextY = (this.Y + offsetY) * this.scale - this.imageStyle.offectY - this.imageStyle.y;
    this.onMove(nextX, nextY);
  }

  get nativeStyle() {
    return {
      width: `${1280 * this.scale}px`,
      height: `${960 * this.scale}px`,
      left: `${this.boundingClientRect.left}px`,
      top: `${this.boundingClientRect.top}px`,
      transform: "rotate(0deg)",
    };
  }
  get containerStyle() {
    return {
      width: `${this.boundingClientRect.width}px`,
      height: `${this.boundingClientRect.height}px`,
      left: `${this.boundingClientRect.left}px`,
      top: `${this.boundingClientRect.top}px`,
    };
  }

  get containerWidth() {
    if (!this.$store.state) {
      return 0;
    }
    return this.$store.state.containerWidth;
  }
  get containerHeight() {
    return this.$store.state.containerHeight;
  }
  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  onScaleChange() {
    this.initBoundingClientRect();
    if (this.imageStyle.orginW && this.imageStyle.orginH) {
      this.resetProp(this.imageStyle.orginW * this.scale, this.imageStyle.orginH * this.scale);
    }
  }
  initBoundingClientRect() {
    const canvasRect = (document.querySelector("#Cocos2dGameContainer") as any).getBoundingClientRect();
    this.boundingClientRect = canvasRect;
  }

  startDrag(event: any) {
    this.confirming = true;
    this.startPosition.x = event.pageX;
    this.startPosition.y = event.pageY;
    event.preventDefault();
  }
  drag(event: any) {
    if (this.confirming) {
      const offsetX = event.pageX - this.startPosition.x;
      const offsetY = event.pageY - this.startPosition.y;
      this.onMove(offsetX, offsetY);
      this.startPosition.x = event.clientX;
      this.startPosition.y = event.clientY;
    }
  }
  onMove(offsetX: number, offsetY: number) {
    if (
      this.imageStyle.x + offsetX > -this.magDistance &&
      this.imageStyle.y + offsetY > -this.magDistance &&
      this.imageStyle.x + this.imageStyle.w + offsetX < 1280 * this.scale + this.magDistance &&
      this.imageStyle.y + this.imageStyle.h + offsetY < 960 * this.scale + this.magDistance
    ) {
      this.imageStyle.x += offsetX;
      this.imageStyle.y += offsetY;
      this.imageStyle.left = this.imageStyle.x + "px";
      this.imageStyle.top = this.imageStyle.y + "px";
    }
  }
  stopDrag() {
    this.confirming = false;
  }

  created() {
    this.initBoundingClientRect();
  }

  handleEditShape() {
    this.editShapeData.width = 1280 * 1;
    this.editShapeData.height = 720 * 1;
  }

  handleOpenShapeSelector() {
    this.editShapeData = cloneDeep(this.defaultShapeData);
    this.editShapeData.width = 1280 * 1;
    this.editShapeData.height = 720 * 1;
  }

  mounted() {
    this.$store.commit(types.mutations.setVisible, false);
    this.initImageData();
    window.addEventListener("resize", this.onScaleChange);
  }
  async initImageData() {
    (window as MyWindow).cocos.showSocpedComponent = true;
    const id = this.scopedComponentData.id;
    const texture = await (window as MyWindow).cocos.getCaptureByNode(id);
    const data = texture.readPixels();
    const width = texture.width;
    const height = texture.height;
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    canvas.width = width;
    canvas.height = height;
    const rowBytes = width * 4;
    for (let row = 0; row < height; row++) {
      const srow = height - 1 - row;
      const imageData = ctx.createImageData(width, 1);
      const start = srow * width * 4;
      for (let i = 0; i < rowBytes; i++) {
        imageData.data[i] = data[start + i];
      }
      ctx.putImageData(imageData, 0, row);
    }

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        async blob => {
          if (!blob) {
            reject(new Error("生成组件分身失败"));
            return;
          }
          this.imageStyle.blobUrl = URL.createObjectURL(blob);
          this.imageStyle.orginW = width;
          this.imageStyle.orginH = height;
          this.resetProp(width * this.scale, height * this.scale);
        },
        "image/png",
        1,
      );
    });
  }
  resetProp(orWidth: number, orHeight: number) {
    const id = this.scopedComponentData.id;
    const properi = this.$store.state.componentMap[id].properties;
    const pos = window["cocos"].getWorldPos(id, properi.angle, properi.x);
    const width = orWidth;
    const height = orHeight;
    this.imageStyle.width = width + `px`;
    this.imageStyle.height = height + `px`;
    this.imageStyle.w = width;
    this.imageStyle.h = height;
    const centPos = window["cocos"].getNodeCentEdtiorPos(id);
    this.imageStyle.x = centPos.x * this.scale - this.imageStyle.w / 2;
    this.imageStyle.y = centPos.y * this.scale - this.imageStyle.h / 2;

    this.imageStyle.left = this.imageStyle.x + `px`;
    this.imageStyle.top = this.imageStyle.y + `px`;
    this.imageStyle.angle = properi.angle;
    if (this.scopedComponentData.type == "dragQuestion") {
      this.imageStyle.offectX = centPos.x * this.scale - (properi.width * this.scale) / 2 - this.imageStyle.x;
      this.imageStyle.offectY = centPos.y * this.scale - (properi.height * this.scale) / 2 - this.imageStyle.y;
    } else {
      this.imageStyle.offectX = pos.x * this.scale - this.imageStyle.x;
      this.imageStyle.offectY = pos.y * this.scale - this.imageStyle.y;
    }
  }

  async handleConfirm() {
    this.confirming = true;
    this.scopedComponentData.callBack &&
      this.scopedComponentData.callBack({
        id: this.scopedComponentData.id,
        index: this.scopedComponentData.index,
        chooseAll: this.chooseAll,
        x: this.X,
        y: this.Y,
      });
    this.handleCancel();
  }
  // 数字 只保留两位小数
  fixedNumber(num: number) {
    return Math.floor(num * 100) / 100;
  }

  handleCancel() {
    (window as MyWindow).cocos.showSocpedComponent = false;
    bus.$emit("scopedComponentState", { state: 0, id: "" });
  }
  beforeDestroy() {
    // 组件销毁前记得释放Blob URL
    window.removeEventListener("resize", this.onScaleChange);
    this.imageStyle.blobUrl && URL.revokeObjectURL(this.imageStyle.blobUrl);
  }
}
</script>

<style scoped lang="less">
.arrow-up {
  position: absolute;
  // cursor: pointer;
  transform: scaleY(-1);
  top: 0px;
  right: 1px;
}
.arrow-down {
  position: absolute;
  bottom: 0px;
  right: 1px;
}
.tipsPos {
  color: sandybrown;
  margin-bottom: 8px;
  display: flex;
}
/deep/ .el-collapse-item__header.is-active {
  border-bottom-color: transparent;
}
/deep/ .el-collapse-item__header {
  font-size: 15px;
  font-weight: unset;
}
// /deep/ .el-collapse-item__content {
//   padding-bottom: 5px;
//   overflow: unset !important;
// }
// /deep/ .el-collapse-item {
//   overflow: unset !important;
// }
// /deep/ .el-collapse-item__wrap {
//   overflow: unset !important;
// }
// /deep/ .el-collapse-item__header {
//   font-size: 15px;
//   font-weight: unset;
// }
.position-editor {
  display: flex;
}

.flex {
  display: flex;
  align-items: center;

  &:first-child {
    margin-right: 16px;
  }

  .el-input {
    // margin-left: 5px;
    flex-grow: 1;
  }

  /deep/ .el-input__inner {
    padding-right: 0;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 30px;
    flex-shrink: 0;
  }
}
.show-component-root {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.show-component-btn {
  text-align: left;
  font-size: 16px;
  cursor: pointer;
  margin-bottom: 20px;

  &:hover {
    color: #888;
  }
}
.shape-content {
  width: 520px;
  min-height: 380px;
  display: flex;
  // display: flex !important;
  position: fixed;
  top: 0 !important;
  left: 50% !important;
  transform: translate(-50%, 52px);

  z-index: 9999;
  background: rgba(255, 255, 255, 1);
  box-shadow: 2px 2px 8px 0px rgba(131, 134, 143, 0.2);
  border-radius: 4px;
  border: 1px solid #e6ebf5;
}

.inputDiv {
  width: 141px;
  position: relative;
  display: block;
}
.shape-dialog-wrapper {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  z-index: 105;
  .action-wrapper {
    height: 60px;
    position: fixed;
    bottom: 0px;
    right: 16px;
    position: fixed;
    bottom: 0px;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    z-index: 10;
    // background: red;
  }
}
.shape-form-wrapper {
  position: fixed;
  right: 0;
  width: 358px;
  top: 52px;
  bottom: 0;
  background: #fff;
  padding: 20px;
}
.shape-dialog {
  position: absolute;
}
</style>
<style scoped lang="less">
.dialog-fade-enter-active {
  animation: unset !important;
}
.dialog-fade-leave-active {
  animation: unset !important;
}
.action-wrapper {
  span {
    font-size: 14px !important;
  }
}
.shape-dialog {
  .oc-editor-zoom {
    height: 100% !important;
  }
  .editor-zoom-wrap {
    height: 100% !important;
  }

  .editor-zoom {
    background: unset !important;
  }
  .shape-wrapper {
    width: 100%;
    height: 100%;
    pointer-events: none;
  }
}
</style>
