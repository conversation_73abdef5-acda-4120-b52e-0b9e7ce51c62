<template>
  <div class="component-list-wrapper">
    <div class="component-list">
      <div v-for="i in componentList" :key="i.type" class="component-box">
        <div :class="['component', { disabled: i.disabled }]" @click="!i.disabled && debounceClickFun(i)">
          <i :class="[i.icon, 'icon']"></i>
          {{ i.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";
import questionMangerConfig from "../../../questionMangerConfig.json";
import { compManagerUtil, ComponentLibraryItem } from "@/pages/index/common/utils/compManagerUtil";
import { debounce } from "lodash-es";

@Component
export default class ComponentLibrary extends Vue {
  groupCategoryId: any = 0;

  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  @Watch("initialDataLoaded")
  async oninitialDataLoaded() {
    this.groupCategory();
  }
  groupCategory() {
    this.groupCategoryId = localStorage.getItem("groupCategory") || 0;
  }
  get brushDisable() {
    let isDis = false;
    let category = this.$store.state.template.category;
    if (this.groupCategoryId) {
      category = this.groupCategoryId;
    }
    if ((questionMangerConfig as any)[category]) {
      isDis = (questionMangerConfig as any)[category].addBrushDisable || false;
    }
    return isDis;
  }

  get voiceDisable() {
    let isDis = false;
    let category = this.$store.state.template.category;
    if (this.groupCategoryId) {
      category = this.groupCategoryId;
    }
    if ((questionMangerConfig as any)[category]) {
      isDis = (questionMangerConfig as any)[category].addSpeakerDisable || false;
    }

    return isDis;
  }
  get componentList() {
    let brushDisable = false;
    this.$store.state.componentIds.forEach((Id: any) => {
      const element = this.$store.state.componentMap[Id];
      if (element) {
        if (element.subType && element.subType == SpecialComponentSubTypes.BRUSH) {
          brushDisable = true;
        }
      }
      if (brushDisable == false) {
        brushDisable = this.brushDisable;
      }
    });
    const componentLibraryItem: ComponentLibraryItem[] = [
      {
        type: "label",
        label: "文本",
        icon: "el-icon-document",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      {
        type: "richTextSprite",
        label: "新文本",
        icon: "el-icon-document new-text",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      {
        type: "h5Label",
        label: "富文本",
        icon: "el-icon-document rich-text-icon",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      {
        type: "formula",
        label: "公式",
        icon: "el-icon-data-analysis",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      {
        type: "sprite",
        label: "图片",
        icon: "el-icon-picture",
      },
      {
        type: "spine",
        label: "动效",
        icon: "el-icon-magic-stick",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      // 形状升级，svgShape入口屏蔽掉
      // {
      //   type: "svgShape",
      //   label: "形状",
      //   icon: "el-icon-s-flag",
      //   disabled: false,
      //   forbiddenText: "暂未开放",
      // },
      {
        type: "shape",
        label: "形状",
        icon: "el-icon-s-flag",
        disabled: false,
        forbiddenText: "暂未开放",
      },
      {
        type: "speaker",
        label: "音频组件",
        icon: "el-icon-headset",
        disabled: this.voiceDisable, //false, //
        forbiddenText: "暂未开放",
      },
      {
        type: "brush",
        label: "画笔",
        icon: "el-icon-edit",
        disabled: brushDisable,
        forbiddenText: "画笔组件只能添加一个",
      },
    ];
    return this.$getPageConfigByKey("componentLibraries")
      ? this.$getPageConfigByKey("componentLibraries").map((key: string) => componentLibraryItem.find(item => item.type === key))
      : componentLibraryItem;
  }

  async onComponentClick(componentLibraryItem: ComponentLibraryItem) {
    compManagerUtil.addComponent(componentLibraryItem.type);
  }

  debounceClickFun = debounce(componentLibraryItem => {
    this.onComponentClick(componentLibraryItem);
  }, 300);

  async handleDoubleClickComponent(data: any) {
    if (data.id === "-1") return;
      const theComp = this.$store.state.componentMap[data.id];

      if (theComp) {
        const { type, subType } = theComp;
        compManagerUtil.editComponent(subType || type);
      }
  }

  created() {
    bus.$on("doubleClickComponent", this.handleDoubleClickComponent);
    this.$once("hook:beforeDestroy", () => {
      bus.$off("doubleClickComponent", this.handleDoubleClickComponent);
      this.debounceClickFun.cancel();
    });
  }
}
</script>

<style scoped lang="less">
.component-list {
  padding: 17px;
  display: flex;
  flex-wrap: wrap;
  position: relative;

  .icon {
    font-size: 40px;
    display: block;
    margin-bottom: 5px;
  }

  .component-box {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-size: 12px;
    width: 48px;
    height: 60px;
    padding: 10px;
    transition: all 0.3s;
    &:hover {
      background: #f0f2f5;
    }
    .component {
      cursor: pointer;
    }

    .disabled {
      cursor: not-allowed;
      opacity: 0.3;
    }
    .new-text {
      position: relative;
      &::after {
        content: "";
        width: 16px;
        height: 16px;
        position: absolute;
        // src/pages/index/assets/<EMAIL>
        background: url("../../../assets/<EMAIL>");
        top: -7px;
        right: -12px;
        background-size: 100% 100%;
      }
    }
  }
  .backBtn {
    position: absolute;
    top: 8px;
    left: 8px;
  }
  &.special {
    width: 100%;
  }
  .rich-text-icon {
    background: url(../../../../../assets/rich_text_icon.png);
    background-size: 40px 40px;
    width: 40px;
    height: 40px;
    &::before {
      content: "" !important;
    }
  }
}
</style>
