<template>
  <div :class="['complentsContainerRoot', { hide: !visible }]" ref="container" @mouseenter="handleMouseEnter">
    <i  :class="['toggle-btn', !visible ? 'el-icon-s-unfold' : 'el-icon-s-fold']" @click="handleToggleVisible"></i>
    <el-tabs v-model="activeName" @tab-click="handleTabClick">
      <el-tab-pane label="组件库" :name="ActiveNames.LIBRARY" v-if="showAddComponent">
        <component-library />
      </el-tab-pane>
      <el-tab-pane label="组件管理" :name="ActiveNames.MANAGER">
        <component-manager v-if="isFirstClick" v-show="activeName === ActiveNames.MANAGER" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import ComponentManager from "./ComponentManager/index.vue";
import ComponentLibrary from "./ComponentLibrary/index.vue";
import { ActiveNames } from "@/pages/index/store/modules/componentManagerCard/constants";
import { types } from "@/pages/index/store/modules/componentManagerCard";
import questionMangerConfig from "../../questionMangerConfig.json";
import { CATEGORY } from "@/common/constants";
import { EventName } from "@/common/api/eventName";
@Component({
  components: {
    ComponentManager,
    ComponentLibrary,
  },
})
export default class ComponentManagerCard extends Vue {
  isFirstClick = false;
  get visible() {
    return this.$store.state.componentManagerCard.visible;
  }

  set visible(v) {
    this.$store.commit(types.mutations.setVisible, v);
  }

  get activeName() {
    return this.$store.state.componentManagerCard.activeName;
  }

  set activeName(v) {
    if (v !== this.activeName) {
      this.isFirstClick = true;
      this.$store.commit(types.mutations.setActiveNames, v);
    }
  }

  get ActiveNames() {
    return ActiveNames;
  }
  get showAddComponent() {
    let isShow = true;
    const category = this.$store.state.template.category;
    if (questionMangerConfig[category]) {
      if (typeof questionMangerConfig[category].addComponent != "undefined") {
        isShow = questionMangerConfig[category].addComponent;
      }
      if (isShow == false) {
        this.activeName = ActiveNames.MANAGER;
      }
    }
    return isShow;
  }

  get isAnimationMode() {
    return this.$store.getters.isAnimationMode;
  }

  get componentLength(): number {
    return this.$store.state.componentIds.length;
  }

  get componentSignalIdLength(): number {
    return Object.values(this.$store.state.componentMap).filter((comp: Component) => {
      if (this.category === CATEGORY.CHOICE && comp.tag && comp.tag === 'answer') return true;
      if (this.category === CATEGORY.BLANK && comp.tag && comp.tag === 'blankModule') return true;
      return false;
    }).length;
  }

  get category(): number {
    return this.$store.state.template.category;
  }

  @Watch("componentSignalIdLength")
  async onComponentsChanged() {
    // console.log('onComponentsChanged...watch', val);
    const { componentMap } = this.$store.state;
    let tempSignalId = 1;
    Object.keys(componentMap).forEach((id: string) => {
      const currId = String(id);
      const tempComp = componentMap[currId];
      if (tempComp && this.category === CATEGORY.CHOICE && tempComp.tag && tempComp.tag === 'answer') {
        this.$store.commit("updateComponentExtra", {
          id: currId,
          newExtra: {
            signalId: tempSignalId,
          },
        });
        tempSignalId++;
      } else if (tempComp && this.category === CATEGORY.BLANK && tempComp.tag && tempComp.tag === 'blankModule') {
        // console.log('onComponentsChanged...watch2', tempComp, tempSignalId);
        this.$store.commit("updateComponentExtra", {
          id: currId,
          newExtra: {
            signalId: tempSignalId,
          },
        });
        tempSignalId++;
      } else if (tempComp && tempComp.extra && tempComp.extra.signalId) {
        // console.log('onComponentsChanged...watch3', tempComp, tempSignalId);
        this.$store.commit("updateComponentExtra", {
          id: currId,
          newExtra: {
            signalId: undefined,
          },
        });
      }
    })
    // console.log('componentMap', componentMap);
  }
  handleMouseEnter() {
    // @ts-ignore
    if (window.cocos) {
      // @ts-ignore
      window.cocos.cancleStageMouse();
    }
  }


  handleToggleVisible() {
    this.visible = !this.visible;
    (window as MyWindow).monitorManager.liveLog(EventName.CLICK_COMPONENT_COLLAPSE, {
      visible: this.visible,
    });
  }

  handleTabClick(tab: { name: ActiveNames; }) {
    console.log('handleTabClick-name', tab.name);
    if (tab.name === ActiveNames.LIBRARY) {
      (window as MyWindow).monitorManager.liveLog(EventName.CLICK_COMPONENT_LIBRARY_TAB, {});
    } else if (tab.name === ActiveNames.MANAGER) {
      (window as MyWindow).monitorManager.liveLog(EventName.CLICK_COMPONENT_MANAGE_TAB, {});
    }
    }
  }
</script>

<style scoped lang="less">
.complentsContainerRoot {
  position: absolute;
  left: 0;
  top: 0;
  padding: 10px 0px 10px 10px;
  flex-shrink: 0;
  width: 240px;
  word-break: break-all;
  transition: all 0.5s;

  .toggle-btn {
    position: absolute;
    padding: 10px;
    padding-left: 18px;
    border-radius: 0 6px 6px 0;
    right: -44px;
    top: 10px;
    font-size: 26px;
    cursor: pointer;
    background: #fff;
    box-shadow: 5px 3px 10px -10px rgb(0 0 0 / 30%);
  }
}

.hide {
  transform: translateX(calc(-100%));
}

/deep/ .el-tabs__content {
  padding: 0;
}

/deep/ .el-tabs__nav {
  display: flex;
  width: 100%;
  z-index: unset !important;

  .el-tabs__item {
    flex: 1;
    padding: 0px;
  }
}

/deep/ .el-tabs {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, .12), 0 0 6px 0 rgba(0, 0, 0, .04);
}

/deep/ .el-tabs__header {
  margin: 0;
}

.component-manager {
  padding: 8px 0;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: rgba(131, 134, 143, 0.4);
  }

  &::-webkit-scrollbar-track {
    border-radius: 6px;
    background: transparent;
  }
}
</style>
