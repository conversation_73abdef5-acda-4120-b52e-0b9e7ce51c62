<template>
  <div class="sketch">
    <a href="javascript:;" class="file">
      sketch上传
      <input
        class="inputSketch"
        id="fileFolderMore"
        type="file"
        name="file"
        webkitdirectory
        @change="checkFile"
      />
    </a>
    <div id="mask" v-loading="this.bLoading" v-if="this.bLoading"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({
  components: {},
})
export default class Sketch extends Vue {
  // 是否显示加载
  protected bLoading = false;
  // 读取json文件
  public fileReader: FileReader = new FileReader();
  // 文件夹中所有文件列表 Array<File>
  protected fileList: File[] = [];
  // 布局的json文件
  protected layoutJson: any = {};
  // json文件的elements列表  Array<cfgData>
  protected uploadElementList: any[] = [];
  // 图片名和cdn地址的映射 name-url
  protected imgUrlMap: Map<string, string> = new Map();

  /**
   * 重置变量
   */
  resetVar(): void {
    this.bLoading = false;
    this.fileList = [];
    this.layoutJson = {};
    this.uploadElementList = [];
    this.imgUrlMap = new Map();
  }

  /**
   * 检查sketch包
   */
  async checkFile(e: any) {
    this.resetVar();
    this.bLoading = true;
    this.$message.info("图片上传中...");
    // 所有文件
    const files = e.target.files;
    // layout.json配置文件
    let layoutFile;
    this.fileList = [];
    for (const file of files) {
      // tslint:disable-next-line:no-string-literal
      const fileName = file["name"];
      if (fileName.indexOf("json") > -1) {
        layoutFile = file;
      }
      this.fileList.push(file);
    }

    this.fileReader.removeEventListener("loadend", this.fileReaderLoadend);
    this.fileReader.onloadend = this.fileReaderLoadend;
    if (layoutFile) {
      this.fileReader.readAsText(layoutFile);
    } else {
      this.bLoading = false;
      this.$message.error("缺少配置文件：layout.json");
    }
  }

  /**
   * 读取json文件后
   */
  private async fileReaderLoadend() {
    const res = String(this.fileReader.result);
    this.layoutJson = JSON.parse(res);

    const elementsStr = "elements";
    // 所有元素的配置
    const elements = ((this.layoutJson as any)[elementsStr] as any) || [];

    this.uploadElementList = [];
    this.imgUrlMap.clear();

    // 用于去重
    const tmpMap = new Map();
    for (const element of elements) {
      if (!tmpMap.has(element.name)) {
        tmpMap.set(element.name, true);
        if (element.type === "Image") {
          // 只上传图片
          this.uploadElementList.push(element);
        }
      }
    }
    // console.log("需要上传的图片数量", this.uploadElementList.length);
    // console.log(this.uploadElementList);
    this.uploadImg(0);
  }

  /** 上传图片 */
  private async uploadImg(index: number) {
    const vo = this.uploadElementList[index];
    if (!vo || index >= this.uploadElementList.length) {
      if (this.checkImgUploadResult()) {
        this.$message.info("图片上传完毕，正在配置中...");
        this.bLoading = false;
        this.uploadImgFinished();
      } else {
        this.bLoading = false;
        this.$message.info("图片上传异常，请重新上传");
      }
      return;
    }

    /** 只上传图片类型 */
    if (vo.type !== "Image") {
      this.uploadImg(++index);
      return;
    }

    const file = this.findFileByName(vo.name + ".png");
    if (!file) {
      this.uploadImg(++index);
      return;
    }

    // 上传图片
    const res = await this.uploadImageToServer(file);
    if (res && res.name && res.url) {
      // 存储图片地址
      this.imgUrlMap.set(res.name, res.url);
    }

    this.uploadImg(++index);
  }

  /**
   * 通过文件名获取File
   */
  private findFileByName(name: string): File | null {
    for (const file of this.fileList) {
      if (file.name === name) {
        return file;
      }
    }
    return null;
  }

  /**
   * 上传图片到服务端
   */
  async uploadImageToServer(file: File) {
    const formData = new FormData();
    const { name } = file;
    formData.append("file", file);
    // 开启图片压缩
    formData.append("minify", "1");

    try {
      const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
      if (url && url !== "") {
        return Promise.resolve({ url, name });
      } else {
        return Promise.reject();
      }
    } catch (e) {
      return Promise.reject();
    } finally {
      //
    }
  }

  /**
   * 判定图片是否完全上传成功
   */
  checkImgUploadResult(): boolean {
    (document.querySelector("#fileFolderMore") as any).value = null;

    if (this.imgUrlMap.size === this.uploadElementList.length) {
      return true;
    }
    return false;
  }

  /**
   * 图片上传完成
   */
  uploadImgFinished() {
    const elements = (this.layoutJson as IlayoutJson).elements || [];

    for (const element of elements) {
      if (element.type === "Image") {
        this.addSprite(element);
      } else if (element.type === "Text") {
        this.addTextToStage(element);
      }
    }
  }

  addSprite(eleParam: any): void {
    const imgUrl = this.imgUrlMap.get(`${eleParam.name}.png`) || "";
    if (eleParam.name.indexOf("alice_bg") >= 0) {
      this.setBackgroundImg(imgUrl);
    } else {
      // TODO opacity
      const param = {
        url: imgUrl,
        width: eleParam.w,
        height: eleParam.h,
        x: eleParam.x,
        y: eleParam.y,
      };
      this.addImageToStage(param);
    }
  }

  /**
   * 设置背景图片
   */
  setBackgroundImg(value: string): void {
    this.$store.commit("updateStage", { ...this.stageData, texture: value });
  }

  /**
   * 添加图片到舞台
   */
  addImageToStage(param: {
    url: string;
    width: number;
    height: number;
    x: number;
    y: number;
  }) {
    const component: Omit<SpriteComponent, "id"> = {
      tag: "",
      type: "sprite",
      dragable: true,
      properties: {
        active: true,
        width: param.width,
        height: param.height,
        x: -this.stageData.width * 0.5 + param.width * 0.5 + param.x,
        y: this.stageData.height * 0.5 - param.height * 0.5 - param.y,
        texture: param.url,
      },
    };
    this.$store.dispatch("addComponentNoFocus", component);
  }

  /**
   * 添加文本到舞台
   */
  addTextToStage(param: any) {
    let horizontalAlignFlag = 1;
    const alignParam = param?.align || "center";
    if (alignParam === "left") {
      horizontalAlignFlag = 0;
    } else if (alignParam === "right") {
      horizontalAlignFlag = 2;
    }

    const component: Omit<LabelComponent, "id"> = {
      type: "label",
      tag: "",
      dragable: true,
      properties: {
        active: true,
        fontSize: param.size,
        lineHeight: param.vh,
        width: param.w + 4,
        height: param.h,
        x: -this.stageData.width * 0.5 + param.w * 0.5 + param.x,
        y: this.stageData.height * 0.5 - param.h * 0.5 - param.y,
        string: "请输入文本",
        str: `<size=${param.size}><color=${param.color}>${param.text}</c></s>`,
        color: param.color,
        cusorIndex: 0,
        selectArr: [],
        isLabelRight: true,
        horizontalAlign: horizontalAlignFlag,
      },
    };

    this.$store.dispatch("addComponentNoFocus", component);
  }

  /**
   * 舞台的配置数据
   */
  get stageData() {
    return this.$store.state.stageData;
  }
}

interface IlayoutJson {
  elements: any[];
  canvas: {};
  questionType: string;
  update_time: number;
}
</script>

<style lang="less" scoped>
.sketch {
  position: absolute;
  left: 250px;
  .file {
    position: relative;
    display: inline-block;
    // background: #d0eeff;
    // border: 1px solid #99d3f5;
    border-radius: 4px;
    padding: 4px 12px;
    overflow: hidden;
    color: #525e71;
    text-decoration: none;
    text-indent: 0;
    line-height: 20px;
    user-select: none;
    cursor: pointer;
    font-size: 16px;
  }
  .file input {
    position: absolute;
    font-size: 100px;
    right: 0;
    top: 0;
    opacity: 0;
  }
  .file:hover {
    background: #aadffd;
    border-color: #78c3f3;
    color: white;
    text-decoration: none;
  }
  #mask {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background-color: rgba(255, 255, 255, 0.2);
    z-index: 99;
  }
}
</style>
