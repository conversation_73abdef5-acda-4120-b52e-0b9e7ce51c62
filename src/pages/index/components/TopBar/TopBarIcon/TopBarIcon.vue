<template>
  <div
    class="top-bar-icon"
    :class="{ 'is-disabled': disabled, 'is-active': active }"
    @click="handleClick"
  >
    <div class="top-bar-icon-wrapper">
      <slot></slot>
    </div>
    <span class="tip">{{ text }}</span>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({
  props: {
    text: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      required: false,
      default: false,
    },
    active: {
      type: Boolean,
      required: false,
      default: false,
    },
  },
})
export default class TopBarIcon extends Vue {
  handleClick() {
    this.$emit("click");
  }
}
</script>

<style lang="less" scoped>
.top-bar-icon {
  position: relative;
  cursor: pointer;
  height: 100%;
  width: 44px;
  color: rgb(91, 107, 115);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  transition: all 0.3s ease;

  &:hover {
    background: rgb(242, 242, 242);
  }

  &-wrapper {
    width: 18px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tip {
    margin-top: 8px;
    font-size: 12px;
    line-height: 1;
    white-space: nowrap;
  }

  &.is-disabled {
    position: relative;
    pointer-events: none;
    color: rgb(200, 205, 208);

    &::after {
      content: "";
      position: absolute;
      top: 0px;
      left: 0px;
      width: 100%;
      height: 100%;
      cursor: not-allowed;
      pointer-events: auto;
    }
  }

  &.is-active {
    color: rgb(41, 141, 248);
    background: rgb(242, 248, 255);
  }
}
</style>
