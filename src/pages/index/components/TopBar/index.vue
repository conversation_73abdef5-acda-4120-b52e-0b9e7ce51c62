<template>
  <header class="header">
    <top-bar-left />
    <!-- <sketch v-if="$getPageConfigByKey('showSketchUpload')" /> -->
    <mode-selector />
    <operations v-if="cocosInitFinished" />
  </header>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import Operations from "./Operations/index.vue";
import ModeSelector from "./ModeSelector/index.vue";
import { timeTravel } from "../../store";
import TopBarLeft from "./TopBarLeft/index.vue";
// import Sketch from "./Sketch/index.vue";

@Component({
  components: {
    TopBarLeft,
    Operations,
    ModeSelector,
    // Sketch,
  },
})
export default class TopBar extends Vue {
  get cocosInitFinished() {
    return this.$store.state.cocosInitFinished;
  }

  @Watch("cocosInitFinished")
  async onCocosInitFinished(val: boolean) {
    if (val) {
      timeTravel.subscribeMutations();
    }
  }
}
</script>

<style scoped lang="less">
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 52px;
  background: rgb(255, 255, 255);
  box-shadow: rgba(100, 100, 100, 0.2) 0px 2px 3px 0px;
  z-index: 100;
  color: #555;
  font-weight: 400;
}
</style>
