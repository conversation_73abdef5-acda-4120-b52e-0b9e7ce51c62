<template>
  <div class="top-bar-left">
    <div class="back-icon" @click="handleClickBackBtn" v-if="$getPageConfigByKey('showBackBtn')">
      <left-outlined />
    </div>
    <div class="project-name" v-if="initialDataLoaded">
      <div class="name" :title="questionName">
        {{ questionName }}
      </div>
      <div class="tags">
        <el-tag size="mini" style="margin-left: 6px">{{ templateName }}</el-tag>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import LeftOutlined from "@/components/svgIcons/LeftOutlined.vue";
import { getQuestionListUrl } from "@/common/utils/url";
import getCombineQueryParams from "@/common/utils/getCombineQueryParams";
import { saveCacheState } from "@/pages/index/components/TopBar/Operations/index";
import { parse } from "query-string";

@Component({
  components: {
    LeftOutlined,
  },
})
export default class TopBarLeft extends Vue {
  get initialDataLoaded(): boolean {
    return this.$store.state.initialDataLoaded;
  }

  get questionName() {
    return this.$store.state.name;
  }

  get templateName() {
    return this.$store.state.template.name;
  }

  get templateType() {
    return this.$store.state.template.tempType;
  }

  get plat() {
    const query = parse(window.location.search);
    return query.plat;
  }

  get templateTypeLabels() {
    return {
      1: "选择题",
      2: "拖拽题",
      3: "切割题",
    };
  }

  get h5toCocos() {
    return window.location.href.indexOf("hdkt2Shark") !== -1;
  }

  get fromGroup() {
    const query = parse(window.location.search);
    return Number(query.fromGroup);
  }

  // 回退上一页
  async handleClickBackBtn() {
    console.log("xu-handleClickBackBtn");
    const { snapId, tiku, plat, fromGroup, isGroupQst, h5Switch } = getCombineQueryParams();
    const isYkt = plat === "ykt";
    const isCocosTiku = plat === "cocosTiku";
    const isEditQuestion = !!snapId;
    const isHalfProduct = this.$store.state.status;

    // TODO 返回的时候先加上，待确认
    // 题组插入题目时 不缓存
    if (this.fromGroup && this.plat) {
      console.log("题组 插入题目");
    } else {
      await saveCacheState();
    }

    // isHalfProduct
    if (isYkt) {
      console.log("xu-handleClickBackBtn1");
      // 题组 新增-cocos互动库/公共题库-编辑-返回
      if (Number(fromGroup) && !isGroupQst) {
        window.parent.postMessage(
          JSON.stringify({
            type: "cocos-edit-close",
          }),
          "*",
        );
        return;
      }
      // 编辑题目，直接返回易课堂
      if ((isEditQuestion && !tiku) || this.h5toCocos || h5Switch) {
        console.log("xu-handleClickBackBtn3");
        window.parent.postMessage(
          JSON.stringify({
            type: "cocos-close",
          }),
          "*",
        );
        return;
      }
      if (isYkt && isHalfProduct && !Number(fromGroup)) {
        console.log("xu-handleClickBackBtn4");
        // 通知yiketang退出编辑页面-ykt来更换iframe地址
        window.parent.postMessage(
          JSON.stringify({
            type: "cocosChangeUrl",
            url: "",
          }),
          "*",
        );
        return;
      } else if (isYkt && Number(fromGroup)) {
        window.parent.parent.postMessage(
          JSON.stringify({
            type: "cocosChangeUrl",
            url: "",
          }),
          "*",
        );
        return;
      } else {
        console.log("xu-handleClickBackBtn5");
        // 返回上一页
        window.history.back();
        return;
      }
    } else if (isCocosTiku) {
      console.log("xu-isCocosTiku");
      if (Number(fromGroup) && isGroupQst) {
        window.parent.postMessage(
          JSON.stringify({
            type: "cocos-close",
          }),
          "*",
        );
        return;
      }
      window.parent.postMessage(
        JSON.stringify({
          type: "cocosChangeUrl",
          url: "",
        }),
        "*",
      );
      return;
    }
    console.log("xu-handleClickBackBtn6");
    location.href = getQuestionListUrl();
  }

  created() {
    this.$bus.$on("logout", this.handleClickBackBtn);
  }
}
</script>

<style lang="less" scoped>
.top-bar-left {
  flex: 0 0 248px;
  padding-right: 10px;
  display: flex;
  align-items: center;
  height: 100%;

  .back-icon {
    display: flex;
    width: 44px;
    height: 100%;
    justify-content: center;
    align-items: center;
    margin-right: 7px;
    color: rgb(141, 158, 167);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.1s linear 0s;

    &:hover {
      color: rgb(65, 80, 88);
      background: rgb(242, 242, 242);
    }

    > svg {
      height: 12px;
      fill: currentColor;
      width: 1em;
    }
  }

  .project-name {
    height: 85%;
    font-size: 14px;
    color: rgb(82, 94, 113);
    line-height: unset;
    white-space: nowrap;
    overflow: hidden;
    max-width: 15em;

    .name {
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .tags {
      margin-top: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
