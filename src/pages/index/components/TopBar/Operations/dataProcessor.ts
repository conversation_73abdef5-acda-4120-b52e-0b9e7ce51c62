import { cloneDeep, uniq } from "lodash-es";
import { Animations, AnimationActionList, AnimationAction, Moment, AfterType, AnimationsState } from "@/pages/index/store/modules/animations";
import { isEnterTypeAnim } from "../../AnimationEditor/AddAction/options";
import request from "@/common/utils/request";
import APIS from "@/common/api/constants";
import { CATEGORY } from "@/common/constants/category";

export type ComponentWithSubs = Component & {
  subComponents?: Component[];
};

export type ComponentWithChild = Component & {
  childComponents?: Component[];
};

interface ActionForClient {
  time: number;
  type: string;
  componentId?: string;
  data?: any;
  audioUrl?: string;
  repeat?: number;
}

export const extractPicUrlFromJson = (data: object): string[] => {
  console.warn(data, "dengchao");
  const stringifyData = JSON.stringify(data);
  const urlPattern = /((http|https):\/\/)?([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?/g;
  const urls = stringifyData.match(urlPattern);
  console.log(urls, "urls");
  const uniqUrls = uniq(urls);
  return uniqUrls as string[];
}

/**
 * @desc 提取出json数据中的所有资源url
 * @param data json数据
 */
const extractResourceUrlFromJson = (data: Record<string, any>) => {
  //大纲截图不需要
  console.warn(data, "dengchao");
  const stringifyData = JSON.stringify(data, function (k: any, v: any) {
    // 忽略文本组件的内容
    if (k === 'str') {
      return ''
    }
    if (k === 'customH5') {
      console.log('customH5 ignore');
      return ''
    }
    if (k === 'customH5label') {
      console.log('customH5label ignore');
      return ''
    }
    return v;
  });
  const urlPattern = /((http|https):\/\/)([\w_-]+(?:(?:\.[\w_-]+)+))([\w.,@?^=%&:/~+#-]*[\w@?^=%&/~+#-])?/g;
  const urls = stringifyData.match(urlPattern);
  console.log(urls, "urls");
  const uniqUrls = uniq(urls);

  const resourceSuffixArr = [".MP3", ".mp3", ".wav", ".ogg", ".acc", ".mp4", ".jpg", ".gif", ".png", ".jpeg", ".bmp", ".atlas", ".json", ".zip"];
  // let resourceUrls = uniqUrls.filter(url => resourceSuffixPattern.test(url));
  let resourceUrls = uniqUrls.filter(url  => {
    let typeState = false;
    resourceSuffixArr.forEach(type => {
      if ((url as any).includes(type)) {
        typeState = true;
      }
    });
    return typeState;
  });

  resourceUrls = resourceUrls.filter(value => value !== data.template.bundleUrl && value !== data.thumbnail);

  return resourceUrls;
};

const getObjList = (data: any, list: any[], overList: any[]): any => {
  if (typeof (data) == "object") {
    if (data instanceof Object) {
      if (data.cover && data.atlas && data.images) {
        list = list.concat(data.images);
        overList = overList.concat([data.cover]);
      }
      for (const i in data) {
        const da = getObjList(data[i], [], []);
        list = list.concat(da.list);
        overList = overList.concat(da.overList);
      }

    }
    else if (data instanceof Array) {
      for (let i = 0; i < data.length; i++) {
        const da = getObjList(data[i], [], []);
        list = list.concat(da.list);
        overList = overList.concat(da.overList);

      }
    }
  }
  return { list, overList }

}
const getSpineOverList = (componentMap: any) => {
  let list: any[] = [];
  let overList: any[] = [];
  for (const i in componentMap) {
    const data = getObjList(componentMap[i], [], []);
    list = list.concat(data.list);
    overList = overList.concat(data.overList);
  }
  for (let i = 0; i < overList.length; i++) {
    for (let j = 0; j < list.length; j++) {
      if (overList[i] == list[j]) {
        overList.splice(i, 1);
        i--;
        break;
      }
    }
  }
  return overList;
}

/** 获取资源列表 */
export const getResourceList = (state: any) => {
  const { template: rawTemplate, componentMap, componentIds, stageData, moduleAnimations, extraStageData } = state as State & { moduleAnimations: AnimationsState };
  const { animations } = moduleAnimations;
  const template = cloneDeep(rawTemplate);
  const resourceList: string[] = extractResourceUrlFromJson({
    animations,
    componentMap,
    extraStageData,
    stageData,
    template,
  }) as string[];

  let subPicList: string[] = [];
  if (template.category == CATEGORY.READCOMQUESTION) {
    let coutomH5 = null;
    for (let i = 0; i < componentIds.length; i++) {
      const idOrGroup = componentIds[i];
      if (typeof idOrGroup != "object" && componentMap[idOrGroup].type == "specialComponent" && componentMap[idOrGroup].subType == "readcom") {
        //阅读理解组件
        if (componentMap[idOrGroup].properties.customH5) {
          coutomH5 = componentMap[idOrGroup].properties.customH5;
        }
      }
    }
    subPicList = extractPicUrlFromJson({ coutomH5: coutomH5 });
  } else if (template.category == CATEGORY.XIAOLUPRACTICE) {
    for (let i = 0; i < componentIds.length; i++) {
      const idOrGroup = componentIds[i];
      if (typeof idOrGroup != "object" && componentMap[idOrGroup].type == "optionComponent" && componentMap[idOrGroup].subType == "xiaoluPractice") {
        for (let j = 0; j < componentMap[idOrGroup].properties.stuQuestions.length; j++) {
          const question = componentMap[idOrGroup].properties.stuQuestions[j];
          for (let h = 0; h < question.hanziList.length; h++) {
            const hanzi = question.hanziList[h];
            hanzi.lianziPic && subPicList.push(hanzi.lianziPic);
            for (let k = 0; k < hanzi.bihuaList.length; k++) {
              const bihua = hanzi.bihuaList[k];
              bihua.rightPic && subPicList.push(bihua.rightPic);
              bihua.wrongPic && subPicList.push(bihua.wrongPic);
            }
          }
        }
      }
    }
  }
  for (let i = 0; i < subPicList.length; i++) {
    for (let j = 0; j < resourceList.length; j++) {
      if (subPicList[i] == resourceList[j]) {
        resourceList.splice(j, 1);
        j--;
      }
    }
  }
  // 动效封面剔除
  const coverSpineMap = {};
  const spineOverList = getSpineOverList(componentMap);
  // 去重 减少不必要的遍历
  for (let i = 0; i < spineOverList.length; i++) {
    if (!coverSpineMap[spineOverList[i]]) {
      coverSpineMap[spineOverList[i]] = 1;
    }
  }

  for (let l = 0; l < resourceList.length; l++) {
    if (coverSpineMap[resourceList[l]]) {
      console.log("spine 封面剔除:", resourceList[l]);
      resourceList.splice(l, 1);
      l--;
    }
  }
  return resourceList;
}


/**
 * @desc 获取需要在动画播放前隐藏的组件id
 *
 * @returns {string[]} ids
 */
export const getHideBeforeAnimationComponentIds = (animations: Animations) => {
  const hideBeforeAnimationComponentIds: string[] = [];
  const animationKeys = Object.keys(animations);
  animationKeys.forEach(animationKey => {
    const fragments = animations[animationKey].fragments;
    const fragmentKeys = Object.keys(fragments);
    fragmentKeys.forEach(fragmentKey => {
      const fragment = fragments[fragmentKey];
      fragment.forEach(action => {
        if (isEnterTypeAnim(action.value.anim.type)) {
          hideBeforeAnimationComponentIds.push(action.componentId);
        }
      });
    });
  });
  return hideBeforeAnimationComponentIds;
};

/**
 * @desc 遍历components，在component上添加hideBeforeAnimation属性
 *
 * @param hideBeforeAnimationComponentIds 需要添加的组件id
 * @param components 组件数组
 */
export const dirtyAddHideBeforeAnimationAttr = (hideBeforeAnimationComponentIds: string[], components: ComponentWithSubs[]) => {
  const helper = (component: Component) => {
    const { id } = component;
    if (hideBeforeAnimationComponentIds.includes(id)) {
      component.hideBeforeAnimation = true;
    } else {
      delete component.hideBeforeAnimation;
    }
  };
  components.forEach(component => {
    const { subComponents = [] } = component;
    helper(component);
    if (subComponents.length) {
      subComponents.forEach((comp: Component) => {
        helper(comp);
      });
    }
  });
};

/**
 * @desc 遍历components，调用cocos方法修改spine组件的properties为C端可用的
 *
 * @param components 组件数组
 */
export const dirtyModifySpineComponentProperties = (components: ComponentWithSubs[]) => {
  const helper = (component: Component) => {
    if (component.type === "spine") {
      component.properties = {
        ...component.properties,
        ...(window as MyWindow).cocos.getSpineProperties(component),
      };
    }
    if (component.type === "cocosAni") {
      component.properties = {
        ...component.properties,
        ...(window as MyWindow).cocos.getCocosAniProperties(component),
      };
    }
    if (component.type === "label") {
      component.properties = {
        ...component.properties,
        str: (window as MyWindow).cocos.getLableString(component.id),
      };
    }
  };
  components.forEach(component => {
    const { subComponents = [] } = component;
    helper(component);
    if (subComponents.length) {
      subComponents.forEach((comp: Component) => {
        helper(comp);
      });
    }
  });
};

// 隐藏动画数据
const hideActionData = [{ type: "to", time: 0, props: { scale: 0 }, repeat: 0 }];

/**
 * @desc 生成C端使用的action元素。action中的value、audio、after拆分成三个元素。
 *
 * @param action 动画元素
 * @param baseTime 基础时间
 */
export const genActionForClient = (action: AnimationAction, baseTime = 0) => {
  const result = [];
  const actionTime = Number(action.delay || 0) + baseTime;
  result.push({
    time: actionTime,
    componentId: action.componentId,
    repeat: Number(action.value.repeat),
    type: action.value.animType,
    data: action.value.anim.data,
  });
  if (action.audio.url) {
    const audioStartTime = action.audio.moment === Moment.BEFORE ? 0 : Number(action.value.speed);
    const time = actionTime + audioStartTime + Number(action.audio.delay || 0);
    result.push({
      time,
      type: "audio",
      audioUrl: action.audio.url,
    });
  }
  if (action.after.type === AfterType.HIDE) {
    const { after } = action;
    /**
     * @description fix action 重复+隐藏导出数据错误
     */
    const repeat = Number(action.value.repeat || 0);
    const speed = Number(action.value.speed === undefined ? action.value.anim.data.duration : action.value.speed);
    result.push({
      time: actionTime + speed * (repeat + 1) + Number(after.delay || 0),
      type: "tween",
      repeat: 0,
      componentId: action.componentId,
      data: hideActionData,
    });
  }
  return result;
};

/**
 * @desc fragment 由一维数组转二维数组，同一播放队列的放在一个数组里
 * @param fragment 片段
 */
export const fragmentProcessor = (fragment: AnimationActionList) => {
  const newFragment: AnimationAction[][] = [];
  let resultIndex = 0;

  fragment.forEach((action: AnimationAction, index: number) => {
    const tempData: any = (window as MyWindow).cocos.getActionData(action.componentId, action);
    delete tempData["curveData"];
    action.value.anim.data = tempData;

    if (index === 0) {
      newFragment[resultIndex] = [action];
      return;
    }

    if (action.moment === Moment.BEFORE) {
      console.warn("fragmentProcessor", action);
      newFragment[resultIndex].push(action);
      return;
    }

    newFragment[++resultIndex] = [action];
  });

  return newFragment;
};

/**
 * @desc 生成C端使用的fragment
 * @param {AnimationActionList} fragment 片段
 */
export const genFragmentForClient = (fragment: AnimationActionList) => {
  const fragmentForClient: ActionForClient[] = [];
  const actionsList = fragmentProcessor(fragment);

  // 播放队列中的动画最长持续时间
  const actionsMaxEndTime: number[] = [];
  actionsList.forEach((actions, index) => {
    actionsMaxEndTime[index] = 0;
    actions.forEach(action => {
      const delay = Number(action.delay) || 0;
      const repeat = Number(action.value.repeat || 0);
      // 骨骼动画取 duration
      console.warn(action.value);
      const speed = Number(action.value.speed === undefined ? action.value.anim.data.duration : action.value.speed);
      const endTime = delay + (repeat + 1) * speed;
      if (endTime > actionsMaxEndTime[index]) {
        actionsMaxEndTime[index] = endTime;
      }
    });
  });

  // 播放队列动画开始时间 = 上一播放队列动画结束时间
  let baseTime = 0;
  actionsList.forEach((actions, index) => {
    if (index > 0) baseTime += actionsMaxEndTime[index - 1];
    actions.forEach(action => {
      fragmentForClient.push(...genActionForClient(action, baseTime));
    });
  });

  return fragmentForClient;
};

/**
 * @desc 生成C端使用的动画数据
 * @param {Animations} animations Animations
 */
export const genAnimationsForClient = (animations: Animations) => {
  // 获取所有播放时机
  const animationKeys = Object.keys(animations);

  // 转为数组
  const animationArr = animationKeys.map(key => animations[key]);

  // C 端动画数据
  const animationsForClient: Record<string, ActionForClient[]> = {};

  animationArr.forEach((animation, index) => {
    const {
      // 启动时机音频
      audio,
      // 启动时机包含的动画片段
      fragments,
      // 启动时机开始时间
      points,
    } = animation;

    const pointKeys = Object.keys(points);

    // 当前播放时机
    const animationKey = animationKeys[index];

    // 当前播放时机对应的 C 端数据
    animationsForClient[animationKey] = [];
    const actionsForClient = animationsForClient[animationKey];

    // 遍历 fragments
    pointKeys.map(key => {
      const { startTime, fragmentId } = points[key];

      const fragment = fragments[fragmentId];
      const fragmentForClient = genFragmentForClient(fragment);

      fragmentForClient.forEach(action => (action.time += startTime));
      actionsForClient.push(...fragmentForClient);
    });

    if (audio.url) {
      actionsForClient.unshift({
        time: Number(audio.delay || 0),
        type: "audio",
        audioUrl: audio.url,
      });
    }

    // 根据time排序
    actionsForClient.sort((a, b) => a.time - b.time);
  });

  // 移除空动画
  for (const key in animationsForClient) {
    if (animationsForClient[key].length === 0) {
      delete animationsForClient[key];
    }
  }
  
  return animationsForClient;
};

/**
 * @desc 生成组件业务属性map
 *
 * @param components 组件
 * @returns {Record<string, any>} 组件业务属性map
 */
export const genExtraDataMap = (components: ComponentWithSubs[]) => {
  const extraDataMap: Record<string, any> = {};
  const helper = (component: Component) => {
    const { id, extra = {}, tag } = component;
    extraDataMap[id] = extra;
    extraDataMap[id].tag = tag;
  };
  components.forEach(component => {
    const { subComponents = [] } = component;
    helper(component);
    if (subComponents.length) {
      subComponents.forEach((comp: Component) => {
        helper(comp);
      });
    }
  });
  return extraDataMap;
};

/**
 * @description 组件生成 animationsForClient
 */
export const dirtyGenComponentAnimationsForClient = (components: ComponentWithSubs[]) => {
  components.forEach(component => {
    const { extra } = component;

    if (!extra) return;
    if (!extra.animations) return;

    extra.animationsForClient = genAnimationsForClient(cloneDeep(extra.animations));
  });
};

/**
 * @description 获取带 md5 的 bundle url
 * @param {string} bundleUrl 不带md5的bundle url
 */
export const getRealBundleUrl = async (bundleUrl: string) => {
  const urlWithoutSuffix = bundleUrl.split(".zip")[0];
  const bundleName = urlWithoutSuffix
    .split("/")
    .pop()
    ?.split("_")[0];
  try {
    const realBundleUrl: string = await request
      .get(APIS.GET_BUNDLE_URL, {
        params: { bundleName },
      })
      .then(res => {
        const { cdnUrl } = res.data.data;
        return cdnUrl;
      });
    return realBundleUrl;
  } catch (err) {
    console.log("getRealBundleUrl失败:", err);
    return bundleUrl;
  }
};
