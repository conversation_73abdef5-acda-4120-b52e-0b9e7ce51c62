/* eslint-disable @typescript-eslint/no-explicit-any */
<template>
  <div class="operations">
    <!-- 题组预览 -->
    <el-dropdown class="preview-dropdown" v-if="fromGroup && !plat && groupCategory === '1027'"
      :disabled="isPreviewing || isPlayingAnimation" split-button size="mini" @command="handleGroupPreview"
      trigger="click" @click.native="previewBtn">
      <span class="zyb-icon-btn" @click="handleGroupPreview('0')" style="padding: 0 3px">
        <svg version="1.1" viewBox="0 0 12 14" class="svg-icon svg-fill" style="width: 16px; height: 16px">
          <defs>
            <path pid="0"
              d="M9.708 3.08l5 8.67a1.25 1.25 0 01-1.082 1.875H3.624a1.25 1.25 0 01-1.083-1.875l5.001-8.67a1.25 1.25 0 012.166 0z"
              id="svgicon_editor-header_ic-menu-play_a"></path>
            <path pid="1"
              d="M9.708 3.08l5 8.67a1.25 1.25 0 01-1.082 1.875H3.624a1.25 1.25 0 01-1.083-1.875l5.001-8.67a1.25 1.25 0 012.166 0z"
              id="svgicon_editor-header_ic-menu-play_b"></path>
            <path pid="2" d="M6.49 1.26l3.084 5.355A1.25 1.25 0 018.49 8.49h-5l3-7.23z"
              id="svgicon_editor-header_ic-menu-play_c"></path>
          </defs>
          <g transform="translate(-3 -1)" fill-rule="nonzero" fill="none">
            <path pid="3" d="M0 0h16v16H0z"></path>
            <use fill="#00B266" transform="rotate(90 8.625 8)" xlink:href="#svgicon_editor-header_ic-menu-play_a"></use>
            <use fill="#2CEB91" transform="rotate(90 8.625 8)" xlink:href="#svgicon_editor-header_ic-menu-play_b"></use>
            <use fill="#03BBB3" transform="matrix(0 -1 -1 0 11.49 11.49)"
              xlink:href="#svgicon_editor-header_ic-menu-play_c"></use>
          </g>
        </svg>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="0">题组预览</el-dropdown-item>
        <el-dropdown-item command="1">单题预览</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 撤销 -->
    <top-bar-icon text="撤销" :disabled="!canUndo || editFocus" @click.native.prevent="undo($event)"
      v-if="$getPageConfigByKey('showUndoBtn')">
      <undo-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>

    <!-- 重做 -->
    <top-bar-icon text="重做" :disabled="!canRedo" @click="redo" v-if="$getPageConfigByKey('showRedoBtn')">
      <redo-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>
    <template>
      <top-bar-icon text="预览" :disabled="isPreviewing || isPlayingAnimation" v-if="!fromGroup || plat" @click="preview">
        <preview-icon :style="{ width: 18, height: 18, fill: 'currentColor' }" />
      </top-bar-icon>
      <top-bar-icon text="预览" :disabled="isPreviewing || isPlayingAnimation"
        v-if="fromGroup && !plat && groupCategory !== '1027'" @click="handleGroupPreview('0')">
        <preview-icon :style="{ width: 18, height: 18, fill: 'currentColor' }" />
      </top-bar-icon>
    </template>

    <top-bar-icon v-if="!isTemplateEdit" text="保存" :disabled="isSaving || isPlayingAnimation || isSnap"
      @click="handleSave(1)">
      <cloud-upload-outlined-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>
    <top-bar-icon v-if="!isTemplateEdit && $getPageConfigByKey('showCreateBtn')" :text="fromGroup && plat ? '插入' : '创建'"
      :disabled="isSaving || isPlayingAnimation" @click="handleSave(0)">
      <cloud-upload-outlined-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>

    <bind-tag :visible.sync="bindTagVisible" :saving.sync="bindTagSaving" :savingContiue.sync="bindTagSaving"
      @confirm="tagConfirm" @update:visible="handleVisible" @confirmContinue="handleContinue"
      @hook:mounted="handleBindTagCreated" ref="bindTag"></bind-tag>
  </div>
</template>

<script lang="ts">
import { parse } from "query-string";
import { cloneDeep, isEqual } from "lodash-es";
import { Message } from "element-ui";
import { Component, Vue, Watch } from "vue-property-decorator";
import { timeTravel } from "@/pages/index/store";
import { AnimationsState } from "@/pages/index/store/modules/animations";
import hotkeys from "@/common/utils/hotkeys";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { createVersion, updateQuestion, createPicTask } from "@/common/api/question";
import { isTemplateEdit } from "@/pages/index/common/utils/isTemplateEdit";
import { temporaryStorageId } from "@/pages/index/common/utils/getTemporaryStorageId";
import { addQuestion } from "@/common/api/question";
import RedoIcon from "@/components/svgIcons/Redo.vue";
import UndoIcon from "@/components/svgIcons/Undo.vue";
import CloudUploadOutlinedIcon from "@/components/svgIcons/CloudUploadOutlined.vue";
import PreviewIcon from "@/components/svgIcons/Preview.vue";
import TopBarIcon from "../TopBarIcon/TopBarIcon.vue";
import BindTag from "./bindTag.vue";
import bus from "@/pages/index/common/utils/bus";
import { ComponentWithSubs, dirtyModifySpineComponentProperties, dirtyGenComponentAnimationsForClient, genAnimationsForClient, genExtraDataMap, getResourceList } from "./dataProcessor";
import { validate } from "@/common/utils/dataValidate";
import { culResListSize } from "@/common/utils/getImgsTotalPixel";
import { combineHandler, copyHandler, pasteHandler } from "./index";
import { getIsUploadData } from "@/common/utils/renderTextureToPicture";
import { AIPicTask, genThumbnailNew } from "@/pages/index/common/utils/genThumbnail";
import { HideCompsBeforeAnimation } from "@/pages/index/common/utils/HideCompsBeforeAnimation";
import { getSaveAnalysisData } from "@/common/utils/initAnalysisData";
// plat='ykt' 从易课堂进入
// plat='list' 从题板列表进入
export type Plat = "ykt" | "list" | undefined;
import getCombineQueryParams from "@/common/utils/getCombineQueryParams";
import getStandardData, { StandardData } from "@/common/utils/standardDataManager/getStandardData";
import { saveCacheState } from "@/pages/index/components/TopBar/Operations/index";
import { clearCacheData } from "@/common/utils/autoCache";
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { slimExtData } from "@/common/utils/utils";
import { CATEGORY } from "@/common/constants";
import { isNative } from "@/common/utils/pageConfig";
import { nativeRequestServer } from "@/common/nativeUtils";
import { hotKeyMap } from "./index"
import { EventName } from "@/common/api/eventName";
import { getMutationCounter } from "@/pages/index/common/utils/subscribe";

const ShortKeys = {
  SAVE: "command+s,ctrl+s",
  REDO: "command+shift+z,ctrl+y,command+y",
  UNDO: "command+z,ctrl+z",
  PUBLISH: "command+p,ctrl+p",
  PREVIEW: "command+o,ctrl+o",
  DEL: "backspace,delete,del",
  COPY: "command+c,ctrl+c",
  PASTE: "command+v,ctrl+v",
  ALLCHOOSE: "command+a,ctrl+a",
  CUT: "command+x,ctrl+x",
  COPY_D: "command+d,ctrl+d",
  COMBINE_G: "command+g,ctrl+g", // 组合
  CANCEL_COMBINE_G: "command+shift+g,ctrl+shift+g", // 取消组合
};

@Component({
  components: {
    UndoIcon,
    RedoIcon,
    CloudUploadOutlinedIcon,
    TopBarIcon,
    PreviewIcon,
    BindTag,
  },
})
export default class Operations extends Vue {
  isSaving = false;
  isPreviewing = false;
  isBackAndContinue = false; // 创建并继续生产，返回cocos-tiku后，需要停留在pyramid页面
  temporaryStorageId: string = temporaryStorageId;

  bindTagVisible = false;
  // 打开标签模态框的次数 打开模态框的时间
  openTagModalCount = 0;
  openTagModalTime = 0;
  bindTagSaving = false;
  isSnap = false;
  currentQstIndex = -1;
  firstThumbnail = "";
  generalData: any = null;
  status = 0;
  repeatSeaveID = 0;
  timeTravel: unknown = timeTravel;
  editFocus = false;
  saveTimer: any = null;
  questionTypes: Array<{ desc: string; category: number }> = [];
  aiPicTask: any = null;
  aiTags: number[] = [];
  // 保存初始组件的ids
  initComponentIds: string[] = [];

  features = {
    isQuestion: 1, // 是否组卷、是否可发题
    hasMys: 0, // 是否包含神秘提示
    canInteract: 1, // 是否可交互
    isOral: 0, // 是否含有口述逻辑
    isGroup: 0, // 是否是题组
    demoPage: 0, // 讲解页
    hasVideo: 0, // 视频
    liveResources: 1, //小组直播是否打包
    groupData: {
      // 题组数据
      questionLength: 0,
    },
  };
  canDoTime = true;
  isGroupPreview = false;
  thumbnail = "";
  currentTimerTravelStartIndex = 0;
  groupCategory = localStorage.getItem("groupCategory") || "";
  mouseX = 0;
  mouseY = 0;
  get previewOptions() {
    return [{ label: 1, value: 1 }];
  }

  get canRedo() {
    return this.$store.getters["timeTravel/canRedo"] && this.canDoTime && !(window as MyWindow).lableEnditing;
  }

  get canUndo() {
    return this.$store.getters["timeTravel/canUndo"] && this.canDoTime && !(window as MyWindow).lableEnditing;
  }

  get isTemplateEdit() {
    return isTemplateEdit;
  }

  get componentIds(): string[] {
    return this.$store.state.currentComponentIds;
  }

  get templateId() {
    return this.$store.state.templateId;
  }

  get parentVersion() {
    return this.$store.state.parentVersion;
  }

  get storeStatus() {
    return this.$store.state.status;
  }

  get canSave() {
    const { plat, tiku } = parse(location.search);
    return (this.storeStatus !== 0 && !!plat) || tiku;
  }

  get questionName() {
    return this.$store.state.name;
  }

  get isPlayingAnimation() {
    const isPlayingFragment = this.$store.state.moduleAnimations.isPlayingFragment;
    const isPlayingAnimation = this.$store.state.moduleAnimations.isPlayingAnimation;
    return isPlayingFragment || isPlayingAnimation;
  }

  get components(): Components {
    return this.componentIds.map((componentId: string) => this.$store.state.componentMap[componentId]);
  }

  get fromGroup() {
    const query = parse(window.location.search);
    return Number(query.fromGroup);
  }

  get plat() {
    const query = parse(window.location.search);
    return query.plat;
  }

  get getGroupState() {
    let str = "";
    if (this.isGroupPreview) {
      str = "preview";
    } else if (this.status == 1) {
      str = "save";
    } else if (this.status == 0) {
      str = "create";
    }
    return str;
  }

  handleGroupPreview(data: string) {
    if (data == "0") {
      this.groupPreview();
    } else if (data == "1") {
      this.preview();
    }
  }

  editFocusChange(val: boolean) {
    this.editFocus = val;
  }

  async handleMessage(message: any) {
    if (message.data && message.data.action === "group-save-res") {
      this.bindTagVisible = false;
      this.bindTagSaving = false;
      this.isSaving = false;
    }
    if (message.data && message.data.action === "group-save-error") {
      this.bindTagVisible = message.data.data.bindTagVisible;
      this.bindTagSaving = false;
      this.isSaving = false;
    }
    if (message.data && message.data.action === "group-question-change") {
      const { currentQstIndex, firstThumbnail } = message.data.data;
      this.currentQstIndex = currentQstIndex;
      // 题组的第一题的第一张图
      this.firstThumbnail = firstThumbnail;
      console.log("截图-题组的第一小题", this.firstThumbnail);
    }
  }

  handleStorage(message: any) {
    if (message.key === "groupCategory") {
      this.groupCategory = message.newValue;
    }
  }

  handleBindTagCreated() {
    const operationTypes = this.getOperations(); // 获取操作类型
    if (operationTypes.messageType === "cchd-change" || !localStorage.getItem("tagsData")) {
      (this.$refs.bindTag as any)?.setTagsData("fromState");
    }
  }

  created() {
    const { id } = parse(location.search);
    const { snapId } = getCombineQueryParams();
    this.isSnap = !!snapId;
    this.repeatSeaveID = id;
    window.addEventListener("message", this.handleMessage, false);
    window.addEventListener("storage", this.handleStorage, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("message", this.handleMessage, false);
      window.removeEventListener("storage", this.handleStorage, false);
    });
  }

  get cocosInitFinished() {
    return this.$store.state.cocosInitFinished;
  }

  @Watch("cocosInitFinished", { immediate: true })
  async onCocosInitFinished(val: boolean) {
    console.log("cocosInitFinished----", val);
    if (val) {
      this.$getPageConfigByKey("getQuestionTypeList")().then((res: { data: { errNo: number; data: any[] } }) => {
        if (res.data.errNo === 0) {
          this.questionTypes = res.data.data.map((item: any) => {
            return {
              ...item,
              desc: item.desc || item.questionTypeDesc,
            };
          });
        }
      });
      try {
        this.initComponentIds = Object.keys(this.$store.state.componentMap);

      } catch (error) {
        console.log(error);
      }
    }
  }

  previewBtn() {
    window.parent.postMessage(
      {
        action: "expand-draw",
      },
      "*",
    );
  }

  mounted() {
    this.registerShortKeys();
    this.$bus.$on("editFocusChange", this.editFocusChange);
    (window as MyWindow).getPreivewContent = async () => {
      const clientContent = await this.genContent();
      const name = this.questionName;
      const content = JSON.stringify({
        ...clientContent,
        thumbnail: "",
      });
      const { id } = parse(location.search);
      const result = {
        name,
        id: Number(id),
        content,
        thumbnail: "",
        extData: JSON.stringify(this.$store.state.extData),
      };
      const valid = await validate(result, "setAnswer");
      return valid ? JSON.stringify(clientContent) : "";
    };
    (window as MyWindow).getQuestionDetail = async (autoCache: boolean) => {
      const data = await this.genData(autoCache);
      const con = JSON.parse(data.content);
      const features = con?.template?.features ? con?.template?.features : this.features;
      const standardData = await getStandardData({
        id: 0,
        name: data.name,
        thumbnail: data.thumbnail,
        con: con,
        features: features,
      });
      // console.log("getQuestionDetail", standardData);
      // console.log("getQuestionDetail-thumbnail", data.thumbnail);
      return {
        content: data.content,
        thumbnail: data.thumbnail,
        name: this.questionName,
        tempId: this.templateId,
        parentVersion: this.parentVersion,
        status: this.status,
        tempType: this.$store.state.template.tempType,
        category: this.$store.state.template.category,
        features: JSON.stringify(features),
        standardData: standardData,
      };
    };
    (window as MyWindow).getCanSwitchQuestion = async (): Promise<boolean> => {
      const isCheckOk = await culResListSize(this.$store.state);
      if (!isCheckOk) {
        return false;
      }
      if (getIsUploadData()) {
        Message.error("文本框图片正在上传中，请稍后");
        return false;
      }
      if (!this.checkLabelAndOther()) {
        return false;
      }
      return true;
    };

    bus.$on("getContent", (callback: any) => {
      this.genContent().then(data => {
        callback(data);
      });
    });

    bus.$on("getAIPicTags", (params: any) => {
      this.aiTags = params.AITags;
    });

    bus.$on("clearAIPicTags", () => {
      this.aiTags = [];
    });

    document.addEventListener('mousemove', this.globalMouseTracker);
  }

  globalMouseTracker(event: { clientX: any; clientY: any; }) {
    this.mouseX = event.clientX;
    this.mouseY = event.clientY;
  }


  destroyed() {
    this.unregisterShortKeys();
    this.$bus.$off("editFocusChange", this.editFocusChange);
    document.removeEventListener('mousemove', this.globalMouseTracker);
    document.removeEventListener("keydown", this.handleKeyDown);
    document.removeEventListener("keyup", this.handleKeyUp);
  }

  registerShortKeys() {
    const stageEl = document.getElementsByClassName("stage")[0];


    hotkeys(ShortKeys.DEL, event => {
      if (this.components.length > 0) {
        event.preventDefault();
        this.del();
      }
    });

    hotkeys(ShortKeys.COPY, event => {
      if (this.components.length > 0) {
        event.preventDefault();
        copyHandler(this.components);
        // this.copy();
      }
    });
    hotkeys(ShortKeys.PASTE, event => {
      const containerEl = document.getElementsByClassName("complentsContainerRoot")[0];
      const element = document.elementFromPoint(this.mouseX, this.mouseY);
      if (element && ((stageEl && stageEl.contains(element)) || (containerEl && containerEl.contains(element)))) {
        event.preventDefault();
        this.paste(event);
      }
    });
    hotkeys(ShortKeys.REDO, event => {
      event.preventDefault();
      console.log("REDO");
      if (this.canRedo) {
        this.redo();
      } else if (!this.canDoTime) {
        Message.error("操作过快，请稍后再试");
      }
    });
    hotkeys(ShortKeys.UNDO, event => {
      event.preventDefault();
      console.log("UNDO");
      if (this.canUndo) {
        this.undo();
      } else if (!this.canDoTime) {
        Message.error("操作过快，请稍后再试");
      }
    });
    hotkeys(ShortKeys.PREVIEW, event => {
      event.preventDefault();
      this.preview();
    });
    hotkeys(ShortKeys.ALLCHOOSE, event => {
      event.preventDefault();
      const itemId: any[] = [];
      // for (const key in this.$store.state.componentMap) {
      //   itemId.push(key);
      // }
      for (const key in this.$store.state.componentIds) {
        if (typeof this.$store.state.componentIds[key] == "string") {
          itemId.push(this.$store.state.componentIds[key]);
        } else {
          itemId.push(this.$store.state.componentIds[key].id);
        }
      }
      this.$store.commit("replaceCurrentComponentIds", itemId);
    });

    hotkeys(ShortKeys.SAVE, event => {
      event.preventDefault();
      this.handleSave(1);
    });
    hotkeys(ShortKeys.CUT, event => {
      event.preventDefault();
      copyHandler(this.components);
      this.del();
    });

    hotkeys(ShortKeys.COPY_D, event => {
      // +10 拷贝
      event.preventDefault();
      const ens = JSON.parse(JSON.stringify(this.components));
      copyHandler(ens);
      this.paste(event);
    });
    hotkeys(ShortKeys.COMBINE_G, event => {
      event.preventDefault();
      this.combineHandler();
    });

    hotkeys(ShortKeys.CANCEL_COMBINE_G, event => {
      event.preventDefault();
      this.cancelCombineHandler();
    });
    //todo

    document.addEventListener("keydown", this.handleKeyDown);
    document.addEventListener("keyup", this.handleKeyUp);
    const cocos: any = (window as any).cocos;
    cocos.vueHandeKeyDown = this.handleKeyDown;
    cocos.vuehandleKeyUp = this.handleKeyUp;

  }

  handleKeyDown(event: KeyboardEvent) {
    const { key } = event;
    if (key === "Shift") {
      hotKeyMap.shift = true;

    }
    if (key === "Control") {
      hotKeyMap.ctrl = true;
    }
    if (key === "Meta") {
      hotKeyMap.command = true;
    }
  };


  handleKeyUp(event: KeyboardEvent) {
    const { key } = event;
    if (key === "Shift") {
      hotKeyMap.shift = false;

    }
    if (key === "Control") {
      hotKeyMap.ctrl = false;
    }
    if (key === "Meta") {
      hotKeyMap.command = false;
    }
  };

  unregisterShortKeys() {
    const keys = Object.values(ShortKeys);
    keys.forEach(key => hotkeys.unbind(key));
  }

  undo() {
    if (!this.canUndo || this.editFocus) return;
    if (getIsUploadData()) {
      Message.error("文本框图片正在上传中，请稍后");
      return;
    }
    this.canDoTime = false;
    setTimeout(() => {
      this.canDoTime = true;
    }, 400);
    timeTravel.undo();
    (window as MyWindow).monitorManager.liveLog(EventName.CLICK_REVOKE_BUTTON, {});
  }

  redo() {
    if (getIsUploadData()) {
      Message.error("文本框图片正在上传中，请稍后");
      return;
    }
    this.canDoTime = false;
    setTimeout(() => {
      this.canDoTime = true;
    }, 400);
    timeTravel.redo();
    (window as MyWindow).monitorManager.liveLog(EventName.CLICK_REDO_BUTTON, {});
  }

  del() {
    const deleteOpDisabled = this.components.findIndex(component => component.deletable === false) !== -1;
    if (deleteOpDisabled) {
      Message.error("该元素不可删除");
      return;
    }
    const { componentIds } = this;

    if (componentIds.find(id => id === this.$store.state.editingAnimationsComponentId)) {
      Message.error("请先退出组件动画编辑模式");
      return;
    }

    this.$store.dispatch("removeComponents", componentIds);
  }

  combineHandler() {
    if (!this.components || this.components.length <= 0) {
      return;
    }
    const components = this.components;
    const indexList = [];
    for (let i = 0; i < components.length; i++) {
      if (typeof components[i].id != "undefined") {
        indexList.push(components[i].id);
      }
    }
    if (indexList.length > 1) {
      combineHandler(indexList);
    }
  }
  cancelCombineHandler() {
    const components = this.components;
    for (let i = 0; i < components.length; i++) {
      if (components[i].type === "group") {
        this.$store.dispatch("separateGroupComponent", components[i].id);
      }
    }
  }

  copy() {
    this.$store.dispatch("copyComponents", this.componentIds);
  }

  paste(event: any) {
    pasteHandler();
    event.preventDefault();
    // this.$store.dispatch("pasteComponent");
  }
  checkLabelTextArray(): boolean {
    const { componentMap } = this.$store.state;
    const errCompId = Object.keys(componentMap).find(id => {
      const { type, properties } = componentMap[id];
      return type === "label" && (!properties.textureArray || !properties.textureArray.length);
    });
    if (errCompId) {
      this.$store.commit("replaceCurrentComponentIds", [errCompId]);
      Message.error(`组件${errCompId}：文本组件生成图片失败，请重试`);
      return false;
    }
    return true;
  }
  checkLabelAndOther(): boolean {
    let statue = true;
    for (let i = 0; i < this.components.length; i++) {
      const data = this.components[i];
      if (data && data.type === "label") {
        statue = false;
        Message.error("文本框在选中状态");
      }
      if (!statue) {
        break;
      }
    }
    console.log("检查文本框状态:", statue);
    return statue;
  }
  checkCurrentComponentIdIsChangeIndex() {
    this.$store.commit("replaceCurrentComponentIds", []);
  }

  // 点击保存/创建按钮时 对题目和资源状态的校验，不满足条件将进行拦截
  async validateQuestionState() {
    if (this.isSaving || this.isPlayingAnimation) return false;
    this.isSaving = true;
    if (getIsUploadData()) {
      Message.error("文本框图片正在上传中，请稍后");
      return false;
    }
    if (!this.checkLabelAndOther()) return false;
    if (!this.checkLabelTextArray()) return false;
    // 增加检查当前选中组件 id 是否在最上层
    this.checkCurrentComponentIdIsChangeIndex();
    // 为什么要在culResListSize前保缓存
    if (this.fromGroup && this.plat) {
      console.log("题组 插入题目");
    } else {
      // if (status === 0) {
      //  (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CreateAction);
      // }
      await saveCacheState();
    }
    const isCheckOk = await culResListSize(this.$store.state);
    if (!isCheckOk) {
      return false;
    }
    return true;
  }

  saveStateLow() {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer);
    }
    this.saveTimer = setTimeout(() => {
      console.log("xu-isSaving-low");
      this.isSaving = false;
    }, 60 * 1000);
  }

  async saveHalf() {
    const { snapId } = parse(location.search);
    if (snapId) return;
    const data = await this.genData(true);
    this.generalData = data;
    this.confirm({});
    (window as MyWindow).monitorManager.liveLog(EventName.CLICK_SAVE_BUTTON, {});
  }

  async handleSave(status: number) {
    console.log("handleSave");
    // 清空 generalData
    this.generalData = null;

    const questionStateValid = await this.validateQuestionState();

    console.log("handleSave--questionStateValid", questionStateValid);
    if (!questionStateValid) {
      this.isSaving = false;
      return;
    }
    this.saveStateLow();
    // state:1 保存 不走校验逻辑，0:创建走校验逻辑（正常流程）
    this.status = status;
    if (status) {
      await this.saveHalf();
      return;
    }
    // 如果是题组-插入，不显示tag
    const { fromGroup, isGroupQst } = getCombineQueryParams();
    let valid = await this.genDataValidateWithoutTags();
    console.log("handleSave--valid", valid);
    if (Number(fromGroup) && !isGroupQst) {
      const { fromGroup } = getCombineQueryParams();
      if (Number(fromGroup) && this.groupCategory === "1102" && valid) {
        valid = (window.parent as any).validateAudio();
      }
      if (!valid) {
        this.isSaving = false;
        return;
      }
      this.confirm({});
    } else {
      if (!valid) {
        this.isSaving = false;
        return;
      } else {
        this.bindTagVisible = true;
        this.openTagModalCount++;
        this.openTagModalTime = Date.now();
        (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CreateAction);
      }
    }
  }

  compIsNoChange() {
    // true 没有变化 false 变化
    const preTimerTravel = timeTravel.historyStates.at(this.currentTimerTravelStartIndex) || { componentMap: {}, stageData: {} };
    const currTimerTravel = timeTravel.historyStates.at(-1) || { componentMap: {}, stageData: {} };
    return isEqual(preTimerTravel.componentMap, currTimerTravel.componentMap) && isEqual(preTimerTravel.stageData, currTimerTravel.stageData);
  }

  async genThumbnail() {
    // 先判断组件内容和上一次显示弹窗前有没有变化，有变化，截图，无变化不截图
    // 变化后截图 内容一致 不处理
    // const currThumbnail = await genThumbnailNew();
    // 点击按钮触发验证时截的图
    let currThumbnail = this.generalData.thumbnail;
    if (!currThumbnail) {
      currThumbnail = await genThumbnailNew();
    }
    if (this.thumbnail !== currThumbnail) {
      console.log("thumbnail is diff");
      this.thumbnail = currThumbnail;
      console.log("创建时-截图-ai识别使用", this.thumbnail);
      // this.generalData = data;
      // 开始智能检测-todo
      if (this.aiPicTask) {
        this.aiPicTask.setUrl(this.thumbnail);
      } else {
        this.aiPicTask = new AIPicTask(this.thumbnail);
      }
      // this.generalData = JSON.parse(
      //   JSON.stringify(
      //     this.generalData,
      //     function(k, v) {
      //       // 忽略文本组件的内容
      //       if (k === "thumbnail") {
      //         return currThumbnail;
      //       }
      //       if (k === "content") {
      //         const tempContent = JSON.parse(v);
      //         tempContent.thumbnail = currThumbnail;
      //         return JSON.stringify(tempContent);
      //       }
      //       // console.log("k", k);
      //       return v;
      //     },
      //     4,
      //   ),
      // );
      // console.log("generalData...2", this.generalData);
    }
  }

  async genDataValidateWithoutTags(needThumbnail = true) {
    console.log("genDataValidateWithoutTags....");
    const data = await this.genData(needThumbnail);
    const valid = await validate(data);
    if (!valid) {
      return false;
    }
    this.generalData = data;
    console.log("generalData...4", this.generalData);
    return true;
  }

  async handleVisible(visible: boolean) {
    console.log("xu-bindTagVisible", visible);
    if (!visible) {
      this.isSaving = false;
      if (this.aiPicTask && this.aiPicTask.token) {
        this.aiPicTask.clear();
        this.thumbnail = "";
      }
    }
    if (visible) {
      // 将题目序号切到第一题
      await this.forceChangeQuestionIndex();
      // 如果是题组的其他小题 不截图
      if (this.currentQstIndex > 0) {
        console.log("如果是题组的其他小题 不截图");
        if (this.aiPicTask) {
          this.aiPicTask.setUrl(this.firstThumbnail);
        } else {
          this.aiPicTask = new AIPicTask(this.firstThumbnail);
        }
      } else {
        if (!this.compIsNoChange() || !this.thumbnail) {
          this.genThumbnail();
        }
        const currentTimerTravelStartIndex = this.$store.state.timeTravel.currentIndex;
        const func = () => {
          //记录当前的时间切片的位置 下一次对比时使用
          this.currentTimerTravelStartIndex = currentTimerTravelStartIndex;
          bus.$off("getAIPicTags", func);
        };
        bus.$on("getAIPicTags", func);
      }
    }
  }

  handleContinue() {
    console.log("handleContinue");
    this.isBackAndContinue = true;
  }

  async tagConfirm(tagsData: any) {
    console.log("handleContinue-tagconfirm");
    this.bindTagSaving = true;
    this.confirm(tagsData);
  }

  getOperations() {
    // this.status 1 保存 2 创建
    // this.repeatSeaveID
    // 题组-小题的编辑
    const { id, snapId, temporaryStorageId, plat, tiku, h5Switch, fromGroup, hdkt2Shark } = parse(location.search);
    const saveId = id || this.repeatSeaveID;
    const isFromYkt = plat === "ykt";
    const { isGroupQst } = getCombineQueryParams();
    const temp: any = {};
    if (Number(fromGroup) && this.isGroupPreview) {
      temp.groupType = "groupPreview";
      return temp; // 无需点击保存和创建 就要交给题组
    }
    if (Number(fromGroup) && isGroupQst && this.status) {
      // ?fromGroup=1
      temp.groupType = "subQSave";
      return temp; // 点击保存和创建 都是交给题组处理
    }
    if (Number(fromGroup) && isGroupQst && !this.status) {
      // ?fromGroup=1
      temp.groupType = "subQCreate";
      return temp; // 点击保存和创建 都是交给题组处理
    }
    if (Number(fromGroup) && saveId && !this.status) {
      // id=1815&plat=ykt&fromGroup=1 题组-新增小题-cocos互动库-编辑
      // snapId=17806&id=1799&plat=ykt&tiku=1&&fromGroup=1 题组-新增小题-cocos互动库-编辑
      // 题组-新增小题-cocos互动库-编辑-保存--走小题的保存逻辑
      // 题组-新增小题-cocos互动库-编辑-创建--交给题组处理 insert
      // 题组-新增小题-cocos公共题库-编辑-保存--走小题的保存逻辑--不可保存
      // 题组-新增小题-cocos互动库-编辑-创建--交给题组处理 insert
      temp.groupType = "subQInsert";
      return temp; // 创建交给题组
    }
    // h5Switch // jiaoxue-tihu-ydlj-cc.suanshubang.cc/interactive-question-editor/?h5Switch=newquestion&plat=ykt&parentVersion= -----> h5Switch:(tiku-ykt-cocos) 1. 保存后停留在该页面 createshark/editshark 2. 创建createversion--回到ykt 3. 转换后，ykt-编辑题目-走正常的题目的编辑逻辑,不再有h5Switch
    if (h5Switch === "newquestion") {
      temp.shark = !saveId ? "createShark" : "editShark";
      if (!this.status && isFromYkt) {
        temp.createVersion = 1;
        temp.messageType = "cocosNewquestion-add";
      }
      return temp; // 阅读理解和完形填空转cocos
    }
    if (hdkt2Shark) {
      // hdkt2Shark=1&plat=ykt&parentVersion=1 -----> hdkt2Shark:(h5-ykt-cocos) 1. 保存后停留在该页面 createshark/editshark 2. 创建 createshark/editshark + createversion--回到ykt 3. 转换后，ykt-编辑题目-走正常的题目的编辑逻辑 不再有hdkt2Shark
      temp.shark = !saveId ? "createShark" : "editShark";
      if (!this.status && isFromYkt) {
        temp.createVersion = 1;
        temp.messageType = "cocos2H5";
      }
      return temp;
    }
    if (snapId && saveId && tiku) {
      // snapId=17806&id=1799&plat=ykt&tiku=1 ------> isEditSnapshot + isFromYkt +
      // isEditSnapshot 不可保存 创建走的的是 createVersion + 返回ykt(新增)
      temp.shark = "editShark";
      if (plat) {
        temp.createVersion = 1;
        temp.messageType = "cchd-add";
      }
      return temp;
    }
    if (snapId && saveId) {
      //jiaoxue-tihu-ydlj-cc.suanshubang.cc/interactive-question-editor/index.html?plat=ykt&id=1809&kjtype=2&uid=2032&parentVersion=&snapId=17814 ------> isEditSnapshot + isFromYkt +
      // isEditSnapshot 不可保存 创建走的的是 createVersion + 返回ykt（替换当前页）
      temp.shark = "editShark";
      if (plat) {
        temp.createVersion = 1;
        temp.messageType = "cchd-change";
      }
      return temp;
    }
    if (saveId) {
      // id=1036&plat=ykt -----> isEditQuestion+isFromYkt+isHalfProduct(ykt-pyramid-cocos互动库) 1. 保存 updateQuestion，保留在该页面 2. 创建 updateQuestion + createversion---回到ykt(新增)
      temp.shark = "editShark";
      if (!this.status && plat) {
        temp.createVersion = 1;
        temp.messageType = "cchd-add";
      }
      return temp;
    }
    if (temporaryStorageId) {
      // https://jiaoxue-tihu-tizu01-cc.suanshubang.cc/interactive-question-editor/?temporaryStorageId=11298 -----> isCreateQuestion  保存/创建都是 createshark
      temp.shark = "createShark";
      if (!this.status && plat) {
        temp.createVersion = 1;
        temp.messageType = "cchd-add";
      }
      return temp;
    }
    return temp;
  }

  async getQuestionData(tagsData: any) {
    const data = this.generalData;
    console.log("data.content", data.content);
    const con = JSON.parse(data.content);
    const features = con?.template?.features ? con?.template?.features : this.features;
    const standardData = await getStandardData({
      id: Number(this.repeatSeaveID),
      name: data.name,
      thumbnail: data.thumbnail,
      con: con,
      features: features,
      tagsData: Object.keys(tagsData).length ? tagsData : JSON.parse(this.$store.state.tagsData || "{}"),
      gradeId: Object.keys(tagsData).length ? tagsData.gradeId : this.$store.state.gradeId,
      subjectId: Object.keys(tagsData).length ? tagsData.subjectId : this.$store.state.subjectId,
      // 学能配置化后的数据有这个字段 formConfig
      extData: this.$store.state.extData,
    });
    console.log("standardData..res", standardData);
    console.log("slimExtData...", slimExtData(con.components, this.$store.state.extData));
    // throw new Error("please check standardData is right");
    // eslint-disable-next-line no-unreachable
    const questionData = {
      content: data.content,
      thumbnail: data.thumbnail,
      gradeId: tagsData.gradeId,
      subjectId: tagsData.subjectId,
      tags: JSON.stringify(tagsData),
      tagIds: Object.keys(tagsData).length && tagsData.tagIds ? JSON.stringify(tagsData.tagIds) : undefined,
      name: this.questionName,
      tempId: this.templateId,
      parentVersion: this.parentVersion,
      status: this.status,
      tempType: this.$store.state.template.tempType,
      category: this.$store.state.template.category,
      features: JSON.stringify(features),
      standardData: JSON.stringify(standardData),
      extData: JSON.stringify(slimExtData(con.components, this.$store.state.extData)),
    };
    return questionData;
  }

  // 将题板数据保存为快照
  // 将题板数据+快照同步给易课堂
  async confirm(tagsData: any) {
    const operationTypes = this.getOperations();
    console.log("operationTypes", operationTypes);
    this.bindTagSaving = true;
    try {
      const questionData = await this.getQuestionData(tagsData);
      console.log("...questionData", questionData);
      if (isNative()) {
        questionData.extData = JSON.parse(questionData.extData);
        const createRes = await nativeRequestServer.post("/editorApi/updateAndCreatedSubject", {
          data: JSON.stringify(questionData),
          name: questionData.name,
          id: operationTypes.id,
        });
        // 提示创建成功 并且页面自动跳转到题目页面
        console.log("...createRes", createRes.data.id);
        Message.success("保存成功");
        location.search = `?id=${createRes.data.id}`;
      } else {
        if (["subQSave", "subQCreate", "subQInsert", "groupPreview"].includes(operationTypes.groupType)) {
          const actionTypes = {
            subQSave: "save",
            subQCreate: "create",
            subQInsert: "insert",
            groupPreview: "preview", // save create
          };
          window.parent.postMessage(
            {
              action: "save-group-question",
              isContinueCreate: this.isBackAndContinue,
              data: questionData,
              actionType: actionTypes[operationTypes.groupType],
            },
            "*",
          );
          if (operationTypes.groupType === "groupPreview") {
            this.isGroupPreview = false;
            this.bindTagVisible = false;
            this.bindTagSaving = false;
            this.isSaving = false;
          }
          console.log("题组自己的创建逻辑", operationTypes.groupType);
          return;
        }
        // createShark
        if (operationTypes.shark === "createShark") {
          await addQuestion(questionData)
            .then(res => {
              if (res.data.errNo === 0) {
                if (!operationTypes.createVersion) {
                  Message.success({
                    message: "保存成功",
                  });
                }
                clearCacheData();
                this.repeatSeaveID = res.data.data.qid;
              } else {
                throw res;
              }
            })
            .catch(showErrorMessage);
        }
        if (operationTypes.shark === "editShark") {
          await updateQuestion({ ...questionData, qid: this.repeatSeaveID })
            .then(res => {
              if (res.data.errNo === 0) {
                if (!operationTypes.createVersion) {
                  Message.success({
                    message: "保存成功",
                  });
                  clearCacheData();
                }
              } else {
                throw res;
              }
            })
            .catch(showErrorMessage);
        }
        if (operationTypes.createVersion) {
          const { demoId, templateId } = JSON.parse(questionData.extData || "{}");
          const {
            data: { data: versionData },
          } = await createVersion({
            qid: this.repeatSeaveID,
            gradeId: questionData.gradeId,
            subjectId: questionData.subjectId,
            parentVersion: questionData.parentVersion,
            tags: questionData.tags,
            tagIds: questionData.tagIds,
            content: questionData.content,
            thumbnail: questionData.thumbnail,
            features: questionData.features,
            standardData: questionData.standardData,
            extData: questionData.extData,
            demoId: demoId ? Number(demoId) : undefined,
            templateId: templateId ? Number(templateId) : undefined,
            source: this.plat === "ykt" ? 0 : 1,
          });
          console.log("versionData:", versionData);
          if (!(versionData as any).snapId) {
            throw new Error("创建失败，请重试");
          } else {
            const { qid: cchdId, tId: tid, ...restData } = versionData as any;
            clearCacheData();
            // 拖拽题埋点
            if (questionData.category === CATEGORY.DRAG) {
              this.setDragLiveLog(questionData.content);
            }

            // 创建小题截图的任务
            this.handleStartScreenshotTask(JSON.parse(questionData.standardData), (versionData as any).snapId);
            // 克隆组件功能埋点
            if (questionData.content.indexOf("isCloneComp") !== -1) {
              (window as MyWindow).monitorManager.liveLog("I5C_007", {
                snapId: (versionData as any).snapId,
                isCloneComp: 1,
                category: questionData.category,
              });
              console.info("自测：", {
                snapId: (versionData as any).snapId,
                isCloneComp: 1,
                category: questionData.category,
              });
            }
            // 判断是不是cocosTiku，是的话传参给cocosTiku, 通知创建完成并关闭页面
            if (this.plat === "cocosTiku") {
              window.parent.postMessage(
                JSON.stringify({
                  ...restData,
                  tid,
                  cchdId,
                  isContinueCreate: this.isBackAndContinue,
                  type: "cocos-create-success",
                }),
                "*",
              );
            } else if (operationTypes.messageType) {
              console.log("postMessage", operationTypes.messageType);
              window.parent.postMessage(
                JSON.stringify({
                  ...restData,
                  tid,
                  customData: questionData.content,
                  standardData: questionData.standardData,
                  cchdId,
                  type: operationTypes.messageType,
                }),
                "*",
              );
            }

            // 只有从题型模版和公共题库过来的数据才会打点，互动库没有打点
            if ((temporaryStorageId || this.isSnap) && operationTypes.messageType !== "cchd-change") {
              const { snapId } = getCombineQueryParams();
              (window as MyWindow).monitorManager.liveLog("I5C_002", {
                uniId: temporaryStorageId ? Number(temporaryStorageId) : Number(snapId),
                pageType: temporaryStorageId ? "demo" : "common", // 区分公共题库和模版
                demoId: demoId ? Number(demoId) : undefined,
                templateId: templateId ? Number(templateId) : undefined,
                aiResponseTagIds: this.aiTags,
                sharkId: tagsData.questionTagId,
                aiTagIds: tagsData.AITagIds,
                contentTags: tagsData.contentTags,
                tasks: this.aiPicTask ? this.aiPicTask.tasks : [],
                tagModalShowDuration: Date.now() - this.openTagModalTime, // 最后一次打开模态框的用时
                tagModalShowTimes: this.openTagModalCount,
              });
            }
          }
          
          (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CreateQuestion, Date.now());
          (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CreateAction);
        }
        if (this.bindTagVisible) {
          const { demoId, templateId } = JSON.parse(questionData.extData || "{}");
          const { snapId } = getCombineQueryParams();
          // 只要创建成功就会打这个点位
          this.setAIUseLiveLog(tagsData, demoId, templateId);
          try {
            (window as MyWindow).monitorManager.liveLog(EventName.CLICK_CREATE_BUTTON, {
                pageType: operationTypes.messageType !== "cchd-change" ? temporaryStorageId ? "demo" : snapId ? "common" : "hdk"  : "edit", // 区分公共题库和模版
                demoId: demoId ? Number(demoId) : undefined,
                templateId: templateId ? Number(templateId) : undefined,
                snapId: Number(snapId || 0),
                componentsTotal: Object.keys(this.$store.state.componentMap).length,
                componentsId: Object.keys(this.$store.state.componentMap),
                initComponentsId: this.initComponentIds,
                duraiton: Date.now() - (window as MyWindow).monitorManager.timeManager.createQuestion.start,
                timeTravelTotal: this.$store.state.timeTravel.total,
                mutationCounter: getMutationCounter(), // 计算mutation的次数
              })
          } catch (error) {
            console.log(error);
          }
          
        }
      }
      this.bindTagVisible = false;
      this.bindTagSaving = false;
      this.isSaving = false;
    } catch (err) {
      this.isSaving = false;
      this.bindTagSaving = false;
      showErrorMessage(err as Error);
    }
  }

  /**
   * 13类型的题型，小题的截图采用异步任务生成后更新标准数据
   * 具体的流程为：创建题目时-生成截图任务-截图任务完成-更新至题目的标准数据中
   */

  handleStartScreenshotTask(standardData: StandardData, snapId: number) {
    try {
      // 执行小题截图的任务
      // 敲冰块所有小题截图都一样，不截图
      const blackCategorys = [1151];
      const {
        category,
        questionStructure: { questionType, children },
      } = standardData;
      if (questionType === 13 && children.length && !blackCategorys.includes(category)) {
        createPicTask(snapId);
      }
      // eslint-disable-next-line no-empty
    } catch (error) { }
  }

  /**
   * 题目创建成功时ai的保存数据打点
   */
  setAIUseLiveLog(tagsData: { AITagIds: any[] }, demoId: number | undefined, templateId: number | undefined) {
    const logParams = {
      pageType: temporaryStorageId ? "demo" : "common",
      aiResponseTagIds: this.aiTags, // 空数组说明没返回
      demoId: demoId ? Number(demoId) : undefined,
      templateId: templateId ? Number(templateId) : undefined,
      aiTagIds: tagsData.AITagIds,
      tasks: this.aiPicTask ? this.aiPicTask.tasks : [],
      tagModalShowDuration: Date.now() - this.openTagModalTime, // 最后一次打开模态框的用时
      tagModalShowTimes: this.openTagModalCount,
      callAIApiTimes: 0, // 调用ai识别的次数
      callAIApiResTimes: 0, // 调用ai识别返回结果的次数
      aiStatus: "", // '' 空 AI AI+USER USER ai字段保存的数据
      dotType: "aiSave",
      isWaitingForAI: this.aiTags.length ? 1 : 0,
    };
    logParams.callAIApiTimes = logParams.tasks.length;
    logParams.callAIApiResTimes = logParams.tasks.filter((item: { tags: string | any[] }) => item.tags.length).length;
    if (!logParams.aiTagIds.length) {
      logParams.aiStatus = "";
    } else if (isEqual(logParams.aiResponseTagIds, logParams.aiTagIds)) {
      logParams.aiStatus = "AI";
    } else if (logParams.aiTagIds.some(item => new Set(logParams.aiResponseTagIds).has(item))) {
      // 有交集
      logParams.aiStatus = "AI+USER";
    } else {
      logParams.aiStatus = "USER";
    }

    // console.log('logParams', logParams);

    (window as MyWindow).monitorManager.liveLog("I5C_006", logParams);
  }

  /**
   * 拖拽题-拖拽分区使用情况埋点
   */
  setDragLiveLog(questionContent: string) {
    const {
      extraStageData: { dragNumberAnswerJudge },
    } = JSON.parse(questionContent);
    (window as MyWindow).monitorManager.liveLog("I5C_031", {
      dragNumberAnswerJudge,
    });
  }
  groupPreview() {
    console.log("题组预览");
    this.isGroupPreview = true;
    this.handleSave(1);
  }

  async preview() {
    if (getIsUploadData()) {
      Message.error("文本框图片正在上传中，请稍后");
      return;
    }
    if (!this.checkLabelAndOther()) return;
    if (!this.checkLabelTextArray()) return;
    if (this.isPreviewing || this.isPlayingAnimation) return;
    const isCheckOk = await culResListSize(this.$store.state);
    if (!isCheckOk) {
      return;
    }

    const data = await this.genData(false);
    console.log("preview-data", data);
    const valid = await validate(data);
    if (!valid) {
      return;
    }
    this.isPreviewing = true;
    // const content = JSON.parse(data.content);
    this.$getPageConfigByKey("goPreview")({
      ...data,
      parentVersion: this.parentVersion,
    })
      .catch()
      .finally(() => {
        this.isPreviewing = false;
      });
      (window as MyWindow).monitorManager.liveLog(EventName.CLICK_PREVIEW_BUTTON, {});
  }

  async forceChangeQuestionIndex() {
    const { questionType } = this.$store.state.template;
    const sleep = (time: number) => {
      return new Promise(resolve => (time === 0 ? resolve(0) : setTimeout(resolve, time)));
    };
    let isValidOperate = false;
    // 判断是否需要更新
    const { componentMap } = this.$store.state as State & { moduleAnimations: AnimationsState };
    Object.values(componentMap).forEach((comp: Component) => {
      if ((comp.type === "specialComponent" && comp.subType === "enPK") || (comp.type === "optionComponent" && comp.subType === "enGroupPK")) {
        const { pkData } = comp.properties;
        if (pkData.questionIndex !== 0) {
          isValidOperate = true;
          this.$store.commit("updateComponentProperties", {
            id: comp.id,
            newProperties: {
              ["pkData"]: {
                ...pkData,
                questionIndex: 0,
              },
            },
          });
        }
      }

      // 13的题目 并且有stuQuestionIndex这个属性的 将属性改为0
      if (comp.type !== "optionComponent" && questionType === 13) {
        const canUseKeys: string[] = ["stu_chosenQuestionId", "stuFocus", "stuQuestionIndex"];
        canUseKeys.forEach(key => {
          if (typeof comp.properties[key] !== "undefined" && comp.properties[key] !== 0) {
            isValidOperate = true;
            this.$store.commit("updateComponentProperties", {
              id: comp.id,
              newProperties: {
                [key]: 0,
              },
            });
          }
        });
      }
    });
    const res = await sleep(isValidOperate ? 500 : 0);
    return res;
  }

  async fixPageReadLevels() {
    // 获取 category
    const category = this.$store.state.template.category;
    if (category === 1201) {
      // 判断是否需要更新
      const { componentMap } = this.$store.state as State & { moduleAnimations: AnimationsState };
      Object.values(componentMap).forEach((comp: Component) => {
        if (comp.subType === "pageRead") {
          // 需要重置层级的ids（放在最底）
          const needResetLevelsIds = [...comp.properties.stuStageCompIds.map((item: { relativeH5LabelId: any }) => item.relativeH5LabelId), comp.id].reverse();
          // 其他的ids
          const leftIds = this.$store.state.componentIds.filter((item: any) => !needResetLevelsIds.includes(item));
          const newLevelsIds = [...needResetLevelsIds, ...leftIds];
          this.$store.commit("refreshComponentLevels", { ids: newLevelsIds });
          this.$store.commit("updateComponentProperties", {
            id: comp.id,
            newProperties: {
              subQuestionIndex: 0,
            },
          });
        }
      });
      await this.$nextTick();
      return Promise.resolve();
    }
    return Promise.resolve();
  }

  /**
   * @desc 生成接口数据
   */
  async genData(
    needThumbnail = true,
  ): Promise<{
    name: string;
    id: number;
    content: string;
    thumbnail: string;
    extData: string;
  }> {
    try {
      const { id } = parse(location.search);
      const name = this.questionName;
      console.log("genData start");
      // 将题目序号切到第一题
      await this.forceChangeQuestionIndex();
      // 大阅读修正组件的层级
      await this.fixPageReadLevels();
      const clientContent = await this.genContent();
      let thumbnail = "";
      if (needThumbnail) {
        thumbnail = await genThumbnailNew();
        console.log("截图-封面", thumbnail);
        console.log("thumbnail-save", thumbnail);
        // if (!thumbnail) {
        //   thumbnail = await genThumbnailNew();
        //   console.log("截图-重新截取的图片", thumbnail);
        // } else {
        //   console.log("截图-使用验证时的结果");
        // }
      }
      const content = JSON.stringify({
        ...clientContent,
        thumbnail: thumbnail,
      });
      const result = {
        name,
        id: Number(id),
        content,
        thumbnail,
        extData: JSON.stringify(this.$store.state.extData),
      };
      return result;
    } catch (e) {
      this.isSaving = false;
      showErrorMessage(new Error("数据生成失败，请重试！"));
      throw Promise.reject();
    }
  }

  getIsMultipleAnswer({ template, extraDataMap, extraStageData, components }: { template: any; extraDataMap: any; extraStageData: any; components: any }) {
    switch (template.category) {
      case 1012: {
        // 填空题多个答案配置
        let isMultipleAnswer = false;
        for (const key in extraDataMap) {
          if (extraDataMap[key].tag == "blankModule" && extraDataMap[key].correctArray && extraDataMap[key].correctArray.length > 1) {
            isMultipleAnswer = true;
            break;
          }
        }
        extraStageData.isMultipleAnswer = isMultipleAnswer;
        break;
      }
      case 1011: {
        // 拖拽物体多个答案配置
        const findDragObjNum = (dargType: any) => {
          let num = 0;
          for (const key in extraDataMap) {
            if (extraDataMap[key].tag == "dragableObject" && extraDataMap[key].type == dargType) {
              num += extraDataMap[key].num;
            }
          }
          return num;
        };
        const dragAreaNum :any = {}; // 拖拽区域
        const drogObjNum :any = {}; // 拖拽物体
        for (const key in extraDataMap) {
          if (extraDataMap[key].tag == "dragArea" && findDragObjNum(extraDataMap[key].type) > 0) {
            if (typeof dragAreaNum[extraDataMap[key].type] == "undefined") {
              dragAreaNum[extraDataMap[key].type] = { rankNum: 1, num: extraDataMap[key].position.length };
            } else {
              dragAreaNum[extraDataMap[key].type].rankNum++;
              dragAreaNum[extraDataMap[key].type].num += extraDataMap[key].position.length;
            }
          } else if (extraDataMap[key].tag == "dragableObject") {
            if (typeof drogObjNum[extraDataMap[key].type] == "undefined") {
              drogObjNum[extraDataMap[key].type] = { rankNum: 1, num: extraDataMap[key].num };
            } else {
              drogObjNum[extraDataMap[key].type].rankNum++;
              drogObjNum[extraDataMap[key].type].num += extraDataMap[key].num;
            }
          }
        }
        let isMultipleAnswer = false;
        for (const key in dragAreaNum) {
          if (dragAreaNum[key].rankNum == 1 && drogObjNum[key].rankNum <= 1) {
            console.log("单选");
          } else if (dragAreaNum[key].rankNum == 1 && drogObjNum[key].rankNum > 1) {
            if (dragAreaNum[key].num >= drogObjNum[key].num) {
              // 单区拖满
              console.log("单选");
            } else {
              isMultipleAnswer = true;
              break;
            }
          } else if (dragAreaNum[key].rankNum > 1 && drogObjNum[key].rankNum == 1 && drogObjNum[key].num >= dragAreaNum[key].num) {
            console.log("单选");
          } else {
            isMultipleAnswer = true;
            break;
          }
        }
        // 如果开启了总数值判定，则isMultipleAnswer为true
        if (extraStageData.dragNumberAnswerJudge) {
          isMultipleAnswer = true;
        }
        extraStageData.isMultipleAnswer = isMultipleAnswer;
        break;
      }
      case 1019: {
        // 火柴题
        let isMultipleAnswer = false;
        const component = components.find((item: Component) => item.subType == "matchboard");
        if (component) {
          isMultipleAnswer = component.properties.rightAnswer.length > 1;
        }
        extraStageData.isMultipleAnswer = isMultipleAnswer;
        break;
      }
      case 1147: {
        // 妙笔生花
        let isMultipleAnswer = false;
        const component = components.find((item: Component) => item.subType == "magicPenFlowerQuestion");
        if (component) {
          isMultipleAnswer = component.properties.stuAltOptions.length > 1;
        }
        extraStageData.isMultipleAnswer = isMultipleAnswer;
        break;
      }
    }
    console.log("extraStageData.isMultipleAnswer", extraStageData.isMultipleAnswer);
  }

  /**
   * @desc 生成C端可直接使用的数据
   *
   * @returns content
   */
  async genContent() {
    const { template: rawTemplate, componentMap, componentIds, stageData, moduleAnimations, extraStageData } = this.$store.state as State & { moduleAnimations: AnimationsState };
    const _componentIds = cloneDeep(componentIds);
    const _componentMap = cloneDeep(componentMap);
    let components = cloneDeep(
      _componentIds.map(idOrGroup => {
        if (typeof idOrGroup === "object") {
          const component = _componentMap[idOrGroup.id] as Component & {
            subComponents?: Component[] & { childComponents?: Comment[] };
          };
          if (component.type === "group") {
            const childC = [];
            const subC = [];
            for (let i = 0; i < idOrGroup.subIds.length; i++) {
              const id = idOrGroup.subIds[i];
              if (_componentMap[id].cName) {
                childC.push(_componentMap[id]);
              } else {
                subC.push(_componentMap[id]);
              }
            }
            component.subComponents = subC;
            if (childC.length > 0) {
              component.childComponents = childC;
            }
          } else {
            component.childComponents = idOrGroup.subIds.map(id => _componentMap[id]);
          }
          if (component.childComponents && component.childComponents.length == 0) {
            delete component.childComponents;
          }
          return component;
        }
        if (_componentMap[idOrGroup].type == "specialComponent" && _componentMap[idOrGroup].subType == "enPK") {
          // 小英pk题-控制当前题目的索引值默认位0
          if (_componentMap[idOrGroup].properties.pkData) {
            _componentMap[idOrGroup].properties.pkData.questionIndex = 0;
          }
        }
        if (_componentMap[idOrGroup].type == "optionComponent" && _componentMap[idOrGroup].subType == "contextualAnswer") {
          // 情景回答-控制当前题目的索引值默认位0
          if (_componentMap[idOrGroup].properties.stuQuestionIndex) {
            _componentMap[idOrGroup].properties.stuQuestionIndex = 0;
          }
        }
        return _componentMap[idOrGroup];
      }) as ComponentWithSubs[],
    );
    const hideComp = new HideCompsBeforeAnimation();
    hideComp.addHide(true, components);
    dirtyModifySpineComponentProperties(components);
    dirtyGenComponentAnimationsForClient(components);
    const extraDataMap: Record<string, any> = genExtraDataMap(components);
    const { animations } = moduleAnimations;
    const animationsForClient = genAnimationsForClient(cloneDeep(animations));
    // template中新增categoryName字段 用于存储题型名称， 新的数据取categoryName，旧的数据取name
    const category = rawTemplate.category;
    const findQuestionType = this.questionTypes.find((item: any) => item.category == category);
    if (findQuestionType) {
      rawTemplate.categoryName = findQuestionType.desc;
    }
    const template = cloneDeep(rawTemplate);

    if (!template.features) {
      template.features = this.features;
    }
    const resourceList = getResourceList(this.$store.state);
    this.getIsMultipleAnswer({ template, extraDataMap, extraStageData, components });
    const questionType = template.questionType;
    let stringComponentsData = JSON.stringify(components);
    resourceList.forEach((url: string, index: number) => {
      if (url.indexOf(".png") !== -1) {
        if (!/\.(png)$/.test(url)) {
          const rightUrl = url.slice(0, url.indexOf(".png")) + ".png";
          const reg = new RegExp(url, "g");
          resourceList.splice(index, 1, rightUrl);
          stringComponentsData = stringComponentsData.replace(reg, rightUrl);
        }
      }
    });
    components = JSON.parse(stringComponentsData);
    return {
      animations,
      animationsForClient,
      components,
      extraDataMap,
      extraStageData: getSaveAnalysisData(extraStageData),
      resourceList,
      stageData,
      template,
      questionType,
      versionInfo: {
        v: this.parentVersion,
        timeStamp: parseInt(new Date().getTime() / 1000 + ""),
      },
    };
  }
}
</script>

<style scoped lang="less">
.previewClass {
  width: 80px;
  margin-right: 10px;
}

.zyb-icon-btn-group {
  display: flex;
  margin-left: 10px;
  margin-right: 10px;

  .zyb-icon-btn {
    width: 48px;
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--btn-color);
    background-color: var(--btn-background);
    border: 1px solid var(--header-btn-border-color);
    border-radius: 4px;
    box-sizing: border-box;

    &:hover {
      background-color: #f7f8f8;
    }

    &:active {
      background-color: #f0f0f0;
      border-color: var(--border-color) !important;
    }

    border-radius: 0;
    border-right: 0;

    &:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    &:nth-child(2) {
      width: 16px;
    }

    &:last-child {
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      border-right: 1px solid var(--header-btn-border-color);
    }
  }
}

.operations {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 14px;

  .preview-dropdown {
    margin-right: 16px;
  }

  /deep/ .el-dropdown__caret-button {
    width: 24px;
  }
}
</style>
