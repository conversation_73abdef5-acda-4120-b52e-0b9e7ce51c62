import getImageSize from "@/common/utils/getImageSize";
import { checkPicturesSize } from "@/common/utils/resizePic";
import { blobToUrl } from "@/common/utils/uploadBlob";
import { widgetToComponent } from "@/common/utils/widgetToComponent";
import store from "@/pages/index/store";
import * as clipboard from "clipboard-polyfill";
import { Message } from "element-ui";
import { cloneDeep } from "lodash-es";
import getCombineQueryParams from "@/common/utils/getCombineQueryParams";
const prefixWidgets = "::__courseware__::__widgets__::";
const prefixComponents = "::__courseware__::__components__::";
const prefixComponentsL = prefixComponents.length;
const prefixWidgetsL = prefixWidgets.length;
const prefixPages = "::__courseware__::__pages__::";
const prefixPagesL = prefixPages.length;
import md5 from 'md5';
export enum OtherComponentSubTypes { // 一些组件不支持复制
  READ_TEXT = "readText", // 阅读题
}

const findNewPasteComponentsIdAndFous = (data: any[]) => {
  const itemParent: any = {}
  for (const key in store.state.componentIds) {
    if (typeof store.state.componentIds[key] != "string") {
      const itemComponentId = store.state.componentIds[key] as { id: string, subIds: string[] }
      for (const nKey of itemComponentId.subIds) {
        itemParent[nKey] = itemComponentId.id;
      }

    }
  }
  const newComponentsIds: string[] = [];
  data.forEach((component: any) => {
    if (component.type === "group") {
      if (component.component.id && itemParent[component.component.id]) {
        newComponentsIds.push(itemParent[component.component.id]);
      }

    } else {
      if (component.component.id) {
        newComponentsIds.push(component.component.id);
      }
    }
  });
  store.commit("replaceCurrentComponentIds", newComponentsIds);
}



export const makeMd5ListByStore = () => {
  const md5List: any = [];
  for (const key in store.state.componentIds) {
    const subMd5List: any[] = [];
    let newComps = null;
    if (typeof store.state.componentIds[key] != "string") {
      const groupComId = store.state.componentIds[key] as { id: string, subIds: string[] };
      const id = groupComId.id;
      const subComponents: any[] = [];
      const comps = store.state.componentMap[id];
      for (const nKey of groupComId.subIds) {
        const subComponent = cloneDeep(store.state.componentMap[nKey]);
        const x = subComponent.properties.x + comps.properties.x;
        const y = subComponent.properties.y + comps.properties.y;

        subComponent.properties.x = 0;
        subComponent.properties.y = 0;

        delete subComponent.id;
        delete subComponent.deletable;
        delete subComponent.canCombine;
        delete subComponent.dragable;
        subComponent.tag = "";
        subComponent.extra = {};
        // 新的文本组件的编辑数据需要携带
        if (subComponent.properties.textureRichTextKey) {
          const tempKey = subComponent.properties.textureRichTextKey;
          // @ts-ignore
          const tempH5Text = store.state.extData[tempKey];
          subComponent['editRichText'] = tempH5Text;
        }
        subComponent.extra = {};

        const subMd5Str = md5(JSON.stringify(subComponent));
        subMd5List.push({ md5: subMd5Str, x, y });
        subComponents.push(subComponent);
      }
      const newGroupCompoent = cloneDeep(comps);
      newGroupCompoent.subComponents = subComponents;
      newComps = newGroupCompoent;
    } else {
      const id = store.state.componentIds[key] as string;
      newComps = cloneDeep(store.state.componentMap[id]);
    }

    if (newComps.childComponents) {
      delete newComps.childComponents;
    }
    delete newComps.id;
    delete newComps.deletable;
    delete newComps.canCombine;
    delete newComps.dragable;
    newComps.tag = "";
    newComps.extra = {};
    const x = newComps.properties.x;
    const y = newComps.properties.y;
    newComps.properties.x = 0;
    newComps.properties.y = 0;
    // 新的文本组件的编辑数据需要携带
    if (newComps.properties.textureRichTextKey) {
      const tempKey = newComps.properties.textureRichTextKey;
      // @ts-ignore
      const tempH5Text = store.state.extData[tempKey];
      newComps['editRichText'] = tempH5Text;
    }
    let md5Str = "";
    if (newComps.type == "group") {
      let tempStr = "";
      for (let i = 0; i < subMd5List.length; i++) {
        tempStr += subMd5List[i].md5;
      }
      md5Str = md5(tempStr);
    } else {
      md5Str = md5(JSON.stringify(newComps))
    }
    md5List.push({ md5: md5Str, x, y, subMd5List });
  }
  return md5List;
};


const findMd5ByList = (md5List: any[], md5: string, index: number, x: number, y: number) => {
  let data = null;
  let minMag = -100;
  for (let i = 0; i < md5List.length; i++) {
    if (md5List[i].md5 === md5) {
      if (index == i) {
        data = md5List[i];
        break;
      }
      const magX = md5List[i].x + 10 - x;
      const magY = md5List[i].y - 10 - y;
      if (magX >= - magY - 0.2 && magX <= -magY + 0.2 && magX > minMag) {
        minMag = magX;
        data = md5List[i];
      }
    }
  }
  return data;
}


export const pasteComponents = async (componentsStr: string) => {
  const componentsJson: any[] = JSON.parse(componentsStr);
  let haveComonentNotCopy = false;
  componentsJson.forEach(component => {
    if ((component.subType && component.subType === OtherComponentSubTypes.READ_TEXT) || component.canCopy === false) {
      haveComonentNotCopy = true;
    }
  });
  if (haveComonentNotCopy) {
    Message.error("组件不支持复制");
    return;
  }

  const md5List = makeMd5ListByStore();
  const newComponents: any[] = [];
  for (let i = 0; i < componentsJson.length; i++) {
    const component = componentsJson[i];
    let isPush = false;
    // 找出新文本组件的编辑内容 拆出来放入extData
    const x = component.properties.x;
    const y = component.properties.y;


    const md5Com = cloneDeep(component);

    md5Com.properties.x = 0;
    md5Com.properties.y = 0;
    let md5Str = "";
    if (component.type === "group") {
      let tempStr = "";
      for (let i = 0; i < md5Com.subComponents.length; i++) {
        md5Com.subComponents[i].properties.x = 0;
        md5Com.subComponents[i].properties.y = 0;
        tempStr += md5(JSON.stringify(md5Com.subComponents[i]));
      }
      md5Str = md5(tempStr);
    } else {
      md5Str = md5(JSON.stringify(md5Com));
    }

    const md5Cfg = findMd5ByList(md5List, md5Str, -1, x, y);
    let isCheckLine = false;
    if (md5Cfg) {
      component.properties.x = md5Cfg.x + 10;
      component.properties.y = md5Cfg.y - 10;
      isCheckLine = true;
    }

    if (component.editRichText) {
      store.commit("updateExtProps", {
        key: component.properties.textureRichTextKey,
        value: component.editRichText,
        ignoreHistory: true
      });
      component.editRichText = undefined;
    }
    if (component.type === "group") {
      await store.dispatch("addGroupComponent", component.subComponents.map((comp: { properties: { textureRichTextKey: any; x: number; y: number }; editRichText?: any; }, index: number) => {
        if (!isPush) {
          newComponents.push({ type: "group", component: comp });
          isPush = true;
        }
        if (isCheckLine && md5Cfg && md5Cfg.subMd5List) {

          const x = comp.properties.x;
          const y = comp.properties.y;
          comp.properties.x = 0;
          comp.properties.y = 0

          const subMd5Cfg = md5Cfg.subMd5List[index];
          if (subMd5Cfg) {
            comp.properties.x = subMd5Cfg.x + 10;
            comp.properties.y = subMd5Cfg.y - 10;
          } else {
            comp.properties.x = x;
            comp.properties.y = y;
          }
        }
        if (comp.editRichText) {
          store.commit("updateExtProps", {
            key: comp.properties.textureRichTextKey,
            value: comp.editRichText,
            ignoreHistory: true
          });
          comp.editRichText = undefined;
        }
        return comp;
      }));
    } else if (["optionComponent", "specialComponent"].includes(component.type)) {
      Message.error("教具不支持复制");
    } else {
      store.dispatch("addComponentNoFocus", component);
      if (!isPush) {
        newComponents.push({ type: "", component: component });
        isPush = true;
      }
    }
  }
  if (newComponents.length > 0) {
    findNewPasteComponentsIdAndFous(newComponents);
  }
};


export const pasteWidgets = async (widgetsJson: any[]) => {
  console.log(widgetsJson, "widgetsJson");
  for (const widget of widgetsJson) {
    if (widget.type === "group") {
      const groupWidgets = widget.widgets;
      const subComponents: (Omit<LabelComponent, "id"> | Omit<SpriteComponent, "id"> | null | undefined)[] = [];
      for (const groupWidget of groupWidgets) {
        const subComponent = await widgetToComponent(groupWidget);
        console.warn(subComponent);
        subComponent && subComponents.push(subComponent);
      }
      store.dispatch("addGroupComponent", subComponents);
    } else {
      const component = await widgetToComponent(widget);
      component && store.dispatch("addComponentNoFocus", component);
    }
  }
};

export const pastePages = (pagesJson: any[]) => {
  if (pagesJson.length > 1) {
    Message.error("不支持复制多页");
  } else {
    console.log(pagesJson, "pagesJson");
    const widgets = pagesJson[0]["detail"];
    widgets && pasteWidgets(widgets);
  }
};


export const pasteHandler = async () => {
  try {
    const clipboardStr = await clipboard.readText();


    console.warn("pasteHandler", clipboardStr);
    if (clipboardStr.indexOf(prefixWidgets) === 0) {
      pasteWidgets(JSON.parse(clipboardStr.substr(prefixWidgetsL)));
      return;
    } else if (clipboardStr.indexOf(prefixPages) === 0) {
      pastePages(JSON.parse(clipboardStr.substr(prefixPagesL)));
      return;
    } else if (clipboardStr.indexOf(prefixComponents) === 0) {
      pasteComponents((clipboardStr.substr(prefixComponentsL)));
      return;
    }
    clipboard.read().then(data => {
      for (let i = 0; i < data.length; i++) {
        console.log(data[i].types);
        if (data[i].types.includes("image/png")) {
          Message("正在为您上传图片...");
          data[i].getType("image/png").then(async blob => {
            console.log(JSON.stringify(blob));
            const url = await blobToUrl(blob, "image.png");
            const urls = await checkPicturesSize([url]);
            if (urls.length === 0) return;
            getImageSize(urls[0]).then(({ width, height }) => {
              const spriteComponent: Omit<SpriteComponent, "id"> = {
                tag: "",
                type: "sprite",
                dragable: true,
                properties: {
                  active: true,
                  width: width,
                  height: height,
                  x: 0,
                  y: 0,
                  texture: urls[0],
                },
              };
              store.dispatch("addComponentNoFocus", spriteComponent);
            });
          });
        } else if (data[i].types.includes("text/plain")) {
          if (!clipboardStr) return;
          const labelComponent: Omit<LabelComponent, "id"> = {
            type: "label",
            tag: "",
            dragable: true,
            properties: {
              active: true,
              fontSize: 32,
              lineHeight: 32,
              width: 600,
              height: 101,
              x: 0,
              y: 0,
              string: "单击添加文本",
              str: `<size=32><color=#000000>${clipboardStr}</c></s>`,
              color: "#000000",
              cusorIndex: 1,
              selectArr: [],
              isLabelRight: true,
              isFixed: false,
              rowSpacing: 1.0,
            },
          };
          store.dispatch("addComponentNoFocus", labelComponent);
        }
      }
    });
  } catch (e) {
    Message.error("请先授权浏览器访问剪切板");
    return;
  }
};

export const copyHandler = (components: any[]) => {
  const newComponents = cloneDeep(components);
  newComponents.forEach((comp: any, index) => {
    if (comp.type === "group") {
      store.state.componentIds.forEach((id: any) => {
        if (typeof id === "object" && id.id === comp.id) {
          const subComponents: any[] = [];
          id.subIds.forEach((subId: any) => {
            const subComponent = cloneDeep(store.state.componentMap[subId]);
            subComponent.properties.x += comp.properties.x;
            subComponent.properties.y += comp.properties.y;
            delete subComponent.id;
            delete subComponent.deletable;
            delete subComponent.canCombine;
            delete subComponent.dragable;
            subComponent.tag = "";
            subComponent.extra = {};
            // 新的文本组件的编辑数据需要携带
            if (subComponent.properties.textureRichTextKey) {
              const tempKey = subComponent.properties.textureRichTextKey;
              const tempH5Text = store.state.extData[tempKey];
              subComponent['editRichText'] = tempH5Text;
            }
            subComponent.extra = {};
            subComponents.push(subComponent);
          });

          comp.subComponents = subComponents;
          newComponents.splice(index, 1, comp);
        }
      });
    } else if (comp.childComponents) {
      delete comp.childComponents;
    }
    delete comp.id;
    delete comp.deletable;
    delete comp.canCombine;
    delete comp.dragable;
    comp.tag = "";
    comp.extra = {};
    // 新的文本组件的编辑数据需要携带
    if (comp.properties.textureRichTextKey) {
      const tempKey = comp.properties.textureRichTextKey;
      const tempH5Text = store.state.extData[tempKey];
      comp['editRichText'] = tempH5Text;
    }

  });

  clipboard.writeText(prefixComponents + JSON.stringify(newComponents));
};

export const combineHandler = (currentIds: any[]) => {
  let canCombine = true;
  currentIds.forEach((id: string) => {
    if (["specialComponent", "optionComponent"].includes(store.state.componentMap[id].type)) {
      canCombine = false;
    } else {
      if (store.state.componentMap[id].canCombine === false) {
        canCombine = false;
      }
    }
  });
  if (!canCombine) {
    Message.warning("选中的组件中有组件不支持组合");
    return;
  }
  const tag = { name: "", label: "", editorConfig: [] }; // 组合之后都为普通元素
  store.dispatch("updateComponentsTag", {
    ids: currentIds,
    tag,
  });
  store.dispatch("removeChildComponents", {
    ids: currentIds,
  });
  store.dispatch("combineComponents", currentIds);
};

export const saveCacheState = async () => {
  const { fromGroup } = getCombineQueryParams();
  if (Number(fromGroup)) {
    console.log((window.parent as any).cacheGroupState);
    await (window.parent as any).cacheGroupState();
  } else {
    await store.dispatch("cacheState");
  }
}
export const hotKeyMap = {
  ctrl: false,
  command: false,
  shift: false,
  cmd:false,
  control:false
}



export const hotkeysIsMul = () => {
  if (hotKeyMap.ctrl || hotKeyMap.command || hotKeyMap.cmd || hotKeyMap.shift || hotKeyMap.control) {
    return true;
  }
  return false;
}

export const hotkeysIsShift = () => {
  if (hotKeyMap.shift) {
    return true;
  }
  return false;
}

