/* eslint-disable @typescript-eslint/no-explicit-any */
<template>
  <el-dialog class="tag-modal" title="标签" :visible.sync="dialogVisible" width="50%" top="0vh" append-to-body>
    <el-form ref="form" :model="tagsData" :rules="tagRules" label-width="100px">
      <el-form-item label="年级" prop="gradeId">
        <el-select v-model="tagsData.gradeId" placeholder="请选择年级" @change="handleChange(...arguments, 'gradeId')">
          <el-option v-for="(item, index) in gradeOps" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="学科" prop="subjectId">
        <el-select v-model="tagsData.subjectId" placeholder="请选择学科" @change="handleChange(...arguments, 'subjectId')">
          <el-option v-for="(item, index) in subjectOps" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="考点" prop="pointList">
        <el-cascader
          ref="point"
          class="point-tag"
          expand-trigger="hover"
          :options="pointList"
          :show-all-levels="true"
          v-model="tagsData.pointList"
          @handleChange="cascaderChange"
          :filterable="true"
          :props="{
            multiple: true,
            value: 'pintId',
            label: 'title',
            emitPath: false,
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="题目标签" prop="contentTags" class="has-tooltip" v-show="showContentTags">
        <el-tooltip content="只有知识类标签的题目，才会被统计或展示在各类报告、错题本、及查阅口径中" placement="top-start">
          <i class="el-icon-info" />
        </el-tooltip>
        <el-checkbox-group @change="handleContentTagsChange" v-model="tagsData.contentTags" size="medium">
          <el-checkbox-button v-for="item in contentTagsOps" :label="item.value" :key="item.label" :disabled="tagsData.contentTags.includes(item.value)">{{ item.label }}</el-checkbox-button>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="业务属性" prop="questionTagId">
        <el-select v-model="tagsData.questionTagId" placeholder="请选择业务属性" @change="handleChange(...arguments, 'questionTagId')">
          <el-option v-for="(item, index) in questionTagOps" :key="index" :label="item.tagName" :value="item.tagId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="智能分类" prop="AITagIds">
        <el-cascader
          class="ai-tag"
          expand-trigger="hover"
          :options="AITagOps"
          :show-all-levels="false"
          v-model="tagsData.AITagIds"
          :filterable="true"
          :props="{
            multiple: true,
            value: 'tagId',
            label: 'tagName',
            emitPath: false,
          }"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="难度" prop="difficulty">
        <el-radio-group v-model="tagsData.difficulty">
          <el-radio v-for="item in zbdifficulty" :label="item.id" :key="item.id">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="confirm" :loading="saving || loading" v-if="plat !== 'cocosTiku'">保存</el-button>
      <template v-else>
        <el-button type="primary" @click="confirm" :loading="saving || loading">创建题目</el-button>
        <el-button type="primary" @click="confirmAndContinue" :loading="savingContiue || loading">创建并继续生产</el-button>
      </template>
    </span>
  </el-dialog>
</template>
<script lang="ts">
import { Vue, Component, Prop, Watch } from "vue-property-decorator";
import { getTagConditions, getPoint, getTagOptions, TagsItem, getTags } from "@/common/api/bindTag";
import { cloneDeep } from "lodash-es";
import { ElForm } from "element-ui/types/form";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { isNotOnline } from "@/common/utils/env";
import bus from "@/pages/index/common/utils/bus";
import getCombineQueryParams from "@/common/utils/getCombineQueryParams";
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { contentTagOps } from "@/common/api/constants";
import { CATEGORY } from "../../../../../common/constants/category";

// 新增业务属性和智能分类 保存新增 tagIds
// tagsData中新增 questionTagId AITagIds

const defaultTagData: any = localStorage.getItem("tagsData")
  ? { contentTags: [], ...JSON.parse(localStorage.getItem("tagsData") || "{}") }
  : {
      // 能力
      ability: [],
      // 年级
      gradeId: undefined,
      // 学科
      subjectId: undefined,
      // 业务属性
      questionTagId: undefined,
      // 智能分类
      AITagIds: [],
      // 难度
      difficulty: "",
      contentTags: [],
      // 考点
      pointList: [],
    };

@Component
export default class BindTag extends Vue {
  // 打开关闭
  @Prop({ type: Boolean, default: false }) readonly visible!: boolean;
  @Prop({ type: Boolean, default: false }) readonly saving!: boolean;
  @Prop({ type: Boolean, default: false }) readonly savingContiue!: boolean;

  dialogVisible = this.visible;

  tagsDataChanging = false;

  loading = false;

  // 年级下拉项
  gradeOps = [];
  // 学科下拉项
  subjectOps = [];
  // 考点下拉项
  pointList: any[] = [];
  pointListFlat = [];
  // 难度配置
  zbdifficulty = [];
  // 能力配置
  zbPower = [];

  // 业务属性下拉项
  questionTagOps: TagsItem[] = [];
  // 智能分类下拉项
  AITagOps: TagsItem[] = [];
  contentTagsOps = contentTagOps;
  observer: any = null;
  isUseGetTags = true;

  // 标签数据
  tagsData = cloneDeep(defaultTagData);

  get plat() {
    const { plat } = getCombineQueryParams();
    return plat;
  }

  get showContentTags() {
    return ![CATEGORY.ENGROUPPK, CATEGORY.ENPK, CATEGORY.AROUNDSPEAK].includes(this.$store.state.template.category);
  }

  tagRules = {
    gradeId: [
      { required: true, message: "请选择年级", trigger: "change" },
      {
        type: "number",
        min: 1,
        message: "请选择年级",
        trigger: "change",
      },
    ],
    subjectId: [
      { required: true, message: "请选择学科", trigger: "change" },
      {
        type: "number",
        min: 1,
        message: "请选择学科",
        trigger: "change",
      },
    ],
    pointList: [
      {
        required: true,
        message: "请选择考点",
        trigger: "change",
      },
      {
        type: "array",
        min: 1,
        message: "请选择考点",
        trigger: "change",
      },
      {
        type: "array",
        max: 10,
        message: "考点的数量不可超过10个",
        trigger: "change",
      },
    ],
    AITagIds: [
      {
        required: false,
        message: "请选择考点",
        trigger: "change",
      },
      {
        type: "array",
        max: 12,
        message: "智能分类的数量不可超过12个",
        trigger: "change",
      },
    ],
    contentTags: [
      {
        required: true,
        message: "请选择题目标签",
        trigger: "change",
      },
      {
        type: "array",
        min: 1,
        message: "请选择题目标签",
        trigger: "change",
      },
    ],
    questionTagId: [
      { required: true, message: "请选择业务属性", trigger: "change" },
      {
        type: "number",
        min: 1,
        message: "请选择业务属性",
        trigger: "change",
      },
    ],
    difficulty: [{ required: true, message: "请选择难度", trigger: "change" }],
  };

  get originTagsData() {
    return this.$store.state.tagsData;
  }

  setTagsData(type: "fromLocalStorage" | "fromState") {
    if (type === "fromState" && this.originTagsData) {
      try {
        const data = JSON.parse(this.originTagsData);
        if (data) {
          const { ability = [], difficulty = {}, pointList = [] } = data;
          if (pointList.length === 0 && this.pointIsRequired(this.$store.state.subjectId)) return;
          const getItemId = (list: { id: number }[]) => list.map(item => item.id);
          this.tagsData = {
            AITagIds: [],
            contentTags: [],
            ...data,
            gradeId: this.$store.state.gradeId,
            subjectId: this.$store.state.subjectId,
            ability: getItemId(ability),
            difficulty: difficulty.id,
            pointList: getItemId(pointList).filter(item => !!item),
          };

          this.tagRules.pointList[0].required = this.pointIsRequired();
          // validate
          this.validatePoint();
          console.error("tagsData-从server读取了");
        }
      } catch (error) {
        console.error(error);
      }
    } else {
      console.error("tagsData-使用了非缓存的默认值");
    }
  }

  open() {
    this.dialogVisible = true;
  }

  close() {
    // 清楚数据
    this.dialogVisible = false;
  }
  handleContentTagsChange(val: any) {
    if (val.length > 1) {
      this.tagsData.contentTags = [val.pop()];
    }
  }

  pointIsRequired(subjectId?: number) {
    const whiteMap = new Map([
      ["SCRATCH", 40],
      ["PYTHON", 41],
      ["YUNDONG", 79],
      ["AIGC", 100],
    ]);
    const whiteList = Array.from(whiteMap.values());
    return !whiteList.includes(subjectId || this.tagsData.subjectId);
  }

  async confirmAndContinue() {
    this.$emit("confirmContinue");
    await this.confirm();
  }

  async confirm() {
    if (((window as any).cc.game as any)._paused) (window as any).cc.game.resume();
    this.loading = true;
    // 如果是小英pk 则题目标签强制改为 非知识类
    if (!this.showContentTags) {
      this.tagsData.contentTags = [2];
    }
    if (isNotOnline && this.tagsData.pointList.length === 0 && this.pointIsRequired() && !this.pointList.length && !localStorage.getItem("tagsData")) {
      console.log("获取标签失败，给定测试用默认值");
      this.tagsData = {
        // 能力
        ability: [1],
        // 年级
        gradeId: 11,
        // 学科
        subjectId: 2,
        // 难度
        difficulty: 1,
        // 考点
        pointList: [{ id: 122181, name: "GUIYI" }],
      };
    } else {
      const valid = await new Promise(resolve => {
        (this.$refs["form"] as ElForm).validate(valid => {
          return resolve(valid);
        });
      });
      if (!valid) {
        this.loading = false;
        return;
      }
    }
    const { ability, difficulty, pointList } = this.tagsData;
    const filter = (condition: Array<any>, value: any) => {
      if (Array.isArray(value)) {
        return condition.filter(item => value.includes(item.id)) || [];
      }
      return condition.find(item => item.id === value);
    };
    // TODO: 关注tagIds值为无效数据（[null]）的时候
    /**
     * @description: 知识点添加pathNames字段 用于易课堂中展示
     * 如果getCheckedNodes获取失败，则使用之前的逻辑获取知识点
     */
    let tempPointList = [];
    try {
      tempPointList = (this.$refs.point as any).$refs.panel.getCheckedNodes().map((item: any) => {
        return {
          id: item.data.pintId,
          name: item.data.title,
          pathNames: item.pathLabels.slice(0, -1),
        };
      });
    } catch (error) {
      console.log("getCheckedNodes 出错了");
      tempPointList = filter(this.pointListFlat, pointList);
    }

    this.cacheTagsData(this.tagsData);

    // console.log("...tempPointList", tempPointList);
    const tagsData = {
      ...this.tagsData,
      ability: filter(this.zbPower, ability),
      difficulty: filter(this.zbdifficulty, difficulty),
      // pointList: filter(this.pointListFlat, pointList),
      pointList: tempPointList,
      // 易课堂要求 subject = subjectId
      subject: this.tagsData.subjectId,
      tagIds: [this.tagsData.questionTagId || null, ...(this.tagsData.AITagIds || [])],
    };
    this.$emit("confirm", tagsData);
  }

  /**
   * 缓存tagsData
   * @description:
   * 1. 保存tagsData到localStorage
   * 2. 当从公共题库和模版过来时，取出tagsData，作为题目的tagsData
   * */

  cacheTagsData(tagsData: any) {
    if (!tagsData.subjectId || !tagsData.gradeId) {
      return;
    }
    console.error(
      "tagsData-缓存了",
      JSON.stringify(tagsData, (k, v) => {
        if (["pointList", "AITagIds"].includes(k)) return [];
        return v;
      }),
    );
    localStorage.setItem(
      "tagsData",
      JSON.stringify(tagsData, (k, v) => {
        if (["pointList", "AITagIds"].includes(k)) return [];
        return v;
      }),
    );
  }

  @Watch("dialogVisible")
  onDialogVisibleChange(val: boolean) {
    this.setMonitorByVisible(val);
    this.$emit("update:visible", val);
    if (val) {
      this.tagRules.pointList[0].required = this.pointIsRequired();
      // validate
      this.validatePoint();
    }
  }

  // 监听年级/学科变动->初始化考点/难度/能力
  handleChange(value: any, type: string) {
    if (type === "gradeId") {
      (this.tagsData.subjectId as any) = "";
      const target = this.gradeOps.find((g: any) => g.id === this.tagsData.gradeId) || { subjectList: [] };

      this.subjectOps = target.subjectList;
    }

    if (["gradeId", "subjectId"].includes(type)) {
      // 解决element handleExpand level报错问题
      (this.$refs.point as any).$refs.panel.clearCheckedNodes();
      (this.$refs.point as any).$refs.panel.activePath = [];
      this.tagsData.pointList = [];
      this.tagsData.difficulty = 1;
      this.tagsData.ability = [];
    }

    if (type === "subjectId") {
      this.tagRules.pointList[0].required = this.pointIsRequired();
      // validate
      this.validatePoint();
    }
    this.tagsDataChanging = true;
  }

  cascaderChange() {
    this.tagsDataChanging = true;
  }

  // 获取参数配置项
  getTagConditions() {
    getTagConditions().then(tagCondition => {
      if (tagCondition) {
        const { difficulty = [], gradeAndSubject = [], zbPower } = tagCondition as any;
        // 难度
        this.zbdifficulty = difficulty;
        // 年级
        this.gradeOps = gradeAndSubject
          .map((grade: any) => {
            return grade.gradeList.map((g: any) => {
              g.subjectList = grade.subjectList || [];
              return g;
            });
          })
          .flat();
        // 能力
        this.zbPower = zbPower;
        // 学科
        const target = this.gradeOps.find((g: any) => g.id === this.tagsData.gradeId) || { subjectList: [] };

        this.subjectOps = target.subjectList;
      }
    });
    getTagOptions().then(res => {
      console.log("getTagOptions", res);
      if (res) {
        const { sharkTags, aiTags } = res as any;
        this.questionTagOps = sharkTags.children;
        const removeBlankChildren = (children: any[]): any[] => {
          return children.map(item => {
            return {
              ...item,
              children: item.children && item.children.length ? removeBlankChildren(item.children) : undefined,
            };
          });
        };
        this.AITagOps = removeBlankChildren(aiTags.children);
        const { snapId } = getCombineQueryParams();
        if (!this.isUseGetTags) return;
        if (!snapId) return;
        getTags(snapId).then(res => {
          // console.log("getTags", res);
          if (!this.isUseGetTags) return;
          if (res) {
            const tempAITagIds: number[] = [];
            res.forEach(element => {
              if (element.parentTagId === sharkTags.tagId) {
                this.tagsData.questionTagId = element.tagId;
              } else {
                tempAITagIds.push(element.tagId);
              }
            });
            this.$set(this.tagsData, "AITagIds", tempAITagIds);
          }
        });
      }
    });
  }

  // 获取考点配置项
  getPoint() {
    console.log("getPoint handle");
    getPoint({
      grade: this.tagsData.gradeId,
      subject: this.tagsData.subjectId,
    })
      .then(pointCondition => {
        if (pointCondition) {
          this.pointList = pointCondition.tree || ([] as any);
          // 扁平化pointList
          this.pointListFlat = this.flatPointList(this.pointList);
        }
      })
      .catch(err => {
        console.log(err);
        showErrorMessage(err);
      });
  }

  // 扁平化pointList
  flatPointList(list: Array<any> = []) {
    const res: any = [];
    this.walkPointList(list, (item: any) => {
      res.push({
        id: item.pintId,
        name: item.title,
      });
    });
    return res;
  }

  // 递归pointList
  walkPointList(list: Array<any>, cb: Function) {
    list.forEach((item, ...args) => {
      cb(item, ...args);
      if (item.children && item.children.length) {
        this.walkPointList(item.children, cb);
      }
    });
  }

  @Watch("visible")
  onVisibleChange(val: boolean) {
    this.dialogVisible = val;
    this.loading = false;
    // 第一次的打开弹框时获取年级学科数据、考点
    if (val && this.gradeOps.length === 0) {
      this.getTagConditions();
      this.getPoint();
    }
    bus.$emit("dialogVisible", val);
    if (val === true) {
      // resume pause
      (window as any).cc.game.pause();
      this.initObserver();
    } else {
      if (((window as any).cc.game as any)._paused) (window as any).cc.game.resume();
      this.observer && this.observer.disconnect();
    }
  }

  @Watch("savingContiue")
  onSavingContiueChange() {
    this.$nextTick(() => {
      this.loading = false;
    });
  }

  @Watch("saving")
  onSavingChange() {
    this.$nextTick(() => {
      this.loading = false;
    });
  }

  setMonitorByVisible(val: boolean) {
    if (val) {
      this.tagsDataChanging = false;
      (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.SetTag);
    } else {
      if (this.tagsDataChanging) {
        (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.SetTag);
      }
    }
  }

  validatePoint() {
    if (this.$refs["form"]) {
      (this.$refs["form"] as ElForm).validateField("pointList");
    }
  }

  created() {
    // 监听年级和学科变更
    this.$watch(
      () => [this.tagsData.gradeId, this.tagsData.subjectId],
      () => {
        // 打开弹框之后，修改年级学科再变动
        if (this.visible) {
          this.getPoint();
        }
      },
      {
        immediate: true,
      },
    );
    // getAIPicTags
    const handleGetAIPicTags = (params: any) => {
      const tags = [...new Set([...this.tagsData.AITagIds, ...params.AITags])].slice(0, 12);
      this.$set(this.tagsData, "AITagIds", [...tags]);
      // console.log("getAIPicTags...params", "params", JSON.stringify(params), "AITagIds", JSON.stringify(this.tagsData.AITagIds), this.tagsData);
    };
    const handleClearAIPicTags = () => {
      this.isUseGetTags = false;
      this.$set(this.tagsData, "AITagIds", []);
    };
    bus.$on("getAIPicTags", handleGetAIPicTags);
    bus.$on("clearAIPicTags", handleClearAIPicTags);

    this.$once("hook:beforeDestroy", () => {
      bus.$off("getAIPicTags", handleGetAIPicTags);
      bus.$off("clearAIPicTags", handleClearAIPicTags);
      // 销毁观察者实例
      this.observer && this.observer.disconnect();
    });
  }

  mounted() {
    this.$nextTick(() => {
      this.$refs["form"] && (this.$refs["form"] as ElForm).clearValidate();
    });
  }

  initObserver() {
    // 创建MutationObserver实例
    if (!this.observer) {
      this.observer = new MutationObserver(mutationsList => {
        // console.log("MutationObserver...", mutationsList);
        mutationsList.forEach(function(mutation) {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === 1) {
              const $el = (node as any).querySelectorAll(".el-cascader-panel .el-cascader-node[aria-owns]");
              if ($el.length) {
                console.log("$el....", $el.length);
                [...$el].map(item => (item as any).removeAttribute("aria-owns"));
              }
            }
          });
        });
      });
    }
    // 配置观察选项
    const config = {
      characterData: false,
      childList: true,
      attributes: false,
      subtree: true,
    };
    // 开始观察DOM变化
    this.observer.observe(document.querySelector(".tag-modal"), config);
  }
}
</script>

<style scoped lang="less">
.point-tag,
.ai-tag {
  width: calc(100% - 80px);
  max-width: 600px;
  /deep/ .el-cascader__search-input {
    margin-left: 10px !important;
  }
  /deep/ .el-cascader-menu {
    max-height: 300px;
  }
  /deep/ .el-cascader__tags input {
    margin-left: 9px;
  }
}
.has-tooltip {
  /deep/ .el-form-item__content {
    display: flex;
    margin-left: 80px;
    justify-items: center;
    align-items: center;
    // gap: 10px;
    left: -32px;
    .el-icon-info {
      margin: 0 10px;
    }
  }
  /deep/ .el-form-item__label {
    width: 80px !important;
  }
  /deep/ .el-checkbox-button.is-disabled .el-checkbox-button__inner {
    color: #fff !important;
    background-color: #42c57a !important;
    border-color: #42c57a !important;
    box-shadow: -1px 0 0 0 #8edcaf !important;
    cursor: pointer !important;
  }
}
</style>
