<template>
  <div class="mode-selector">
    <top-bar-icon style="width: 65px" text="普通模式" @click="updateMode(Mode.NORMAL)" :active="mode === Mode.NORMAL">
      <normal-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>


    <top-bar-icon style="width: 65px" text="动画模式" @click="updateMode(Mode.ANIMATION)" :active="mode === Mode.ANIMATION" v-if="animationConfig && animationConfig.length && showAddAnimation">
      <animation-icon :style="{ width: 16, height: 16, fill: 'currentColor' }" />
    </top-bar-icon>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import { Mode } from "@/pages/index/store/constants";
import AnimationIcon from "@/components/svgIcons/Animation.vue";
import NormalIcon from "@/components/svgIcons/Normal.vue";
import TopBarIcon from "../TopBarIcon/TopBarIcon.vue";
import questionMangerConfig from "../../../questionMangerConfig.json";
import { parse } from "query-string";
import { EventName } from "@/common/api/eventName";
@Component({
  components: {
    TopBarIcon,
    AnimationIcon,
    NormalIcon,
  },
})
export default class ModeSelector extends Vue {
  get Mode() {
    return Mode;
  }

  get mode() {
    return this.$store.state.mode;
  }
  get fromGroup() {
    const query = parse(window.location.search);
    return Number(query.fromGroup);
  }
  get showAddAnimation() {
    let isShow = true;
    const category = this.$store.state.template.category;
    if (questionMangerConfig[category]) {
      isShow = questionMangerConfig[category].addAnimation;
    }
    if (this.fromGroup) {
      isShow = false;
    }
    return isShow;
  }
  get animationConfig() {
    return this.$store.state.template && this.$store.state.template.animationConfig;
  }

  updateMode(mode: Mode) {
    if (this.mode === mode) {
      return;
    }
    this.$store.commit("updateMode", mode);
    try {
      if (mode === Mode.ANIMATION) {
        (window as MyWindow).monitorManager.liveLog(EventName.CLICK_COMPONENT_ANIMATION_MODE, {});
      } else {
        (window as MyWindow).monitorManager.liveLog(EventName.CLICK_COMPONENT_NORMAL_MODE, {});
      }
    } catch (error) {
      console.error(error); 
    }
  }
}
</script>

<style scoped lang="less">
.mode-selector {
  display: flex;
  height: 100%;
}
</style>
