import Vue from "vue";
import "@/common/utils/initComp.ts";
import pageConfig from "@/common/utils/pageConfig/pageConfig.native";
import './index'
import watermark from "@/common/nativeUtils/wartermark";

(window as MyWindow).monitorManager = {
  setStartByType: (type: string) => {
    console.log('setStartByType', type);
  },
  setEndByType: (type: string) => {
    console.log('setStartByType', type);
  },
  liveLog: (type: string) => {
    console.log('liveLog', type);
  }
};
Vue.prototype.$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};

(window as MyWindow).$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};

const timer = setTimeout(() => {
  watermark.set((window as any).user || `作业帮`)
  clearTimeout(timer);
}, 1000);
