import Vue from "vue";
import {
  <PERSON><PERSON>,
  <PERSON>alog,
  Drawer,
  Loading,
  Message,
  Tabs,
  TabPane,
} from "element-ui";
import App from "./App.vue";
import "./normalize.css";
import { Monitor } from "@/common/utils/monitorUtil";

(window as MyWindow).monitorManager = new Monitor();

Vue.config.productionTip = false;

Vue.use(Button);
Vue.use(Dialog);
Vue.use(Drawer);
Vue.use(Loading);
Vue.use(Tabs);
Vue.use(TabPane);
Vue.component(Message.name, Message);
Vue.prototype.$message = Message;

new Vue({
  render: h => h(App),
}).$mount("#app");
