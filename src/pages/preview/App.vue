<template>
  <div
    id="app"
    v-loading="isLoading"
    element-loading-text="加载中，请稍后"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(255,255,255, 0.8)"
    :class="{ 'mobile-container-wrapper': previewType !== 'web' }"
  >
    <div class="pc-container" v-if="previewType === 'web'">
      <div class="main">
        <div class="tabs" :style="{ width: `${containerWidth}px` }">
          <div class="tabs-header-wrapper">
            <div class="tabs-header">
              <div class="tab-nav" :class="{ active: activeName === tab.value }" v-for="tab in tabs" :key="tab.value" @click="handleClick(tab.value)">{{ tab.label }}</div>
            </div>
          </div>
          <div class="tabs-content">
            <div class="canvas-container-wrapper" ref="container">
              <div class="canvas-container" :style="containerStyle" ref="canvasContainer">
                <Feedback :showFeedbackRight="showFeedbackRight" :showFeedbackWrong="showFeedbackWrong" :scale="scale" />
                <canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="0"></canvas>
                <div v-if="cocosInstance && cocosInstance.showMask" class="screenshot-mask"></div>
                <div v-if="activeName === 'question' && showRestartButton" class="tip-btn-restart" ref="tipButton" @click="handleRestartQuestion" :style="buttonStyle">
                  <img :src="restartIcon" :style="iconStyle" alt="restart" />
                  <span :style="textStyle">再次作答</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <component :is="debuggerPanelComp" :cocosInstance="cocosInstance" v-if="debuggerPanelComp" />
    </div>
    <div class="canvas-container" :style="containerStyle" ref="container" v-else>
      <canvas id="GameCanvas" oncontextmenu="event.preventDefault()" tabindex="0"></canvas>
      <div
        v-if="cocosInstance.answerStatus === false && previewType === 'webmobile' && referenceAnswerConfigs.support"
        class="reset-btn-wrpper"
        :style="{ transform: `scale(${scale / 100})`, right: `${(scale / 100) * 30}px`, bottom: `${(scale / 100) * 288}px` }"
      >
        <div class="reset-btn" @click="cocosInstance.resetQuestion()"></div>
      </div>
      <Feedback :showFeedbackRight="showFeedbackRight" :showFeedbackWrong="showFeedbackWrong" :scale="scale" />
      <div v-if="cocosInstance && cocosInstance.showMask" class="screenshot-mask"></div>
      
    </div>
  </div>
</template>

<script lang="ts">
import { CATEGORY } from "@/common/constants";
import { isDevelopmentEnv } from "@/common/utils/env";
import hotkeys from "@/common/utils/hotkeys";
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { Message } from "element-ui";
import isMobile from "ismobilejs";
import { SnapType } from "qte-render";
import { parse } from "query-string";
import { Component, Mixins, Vue, Watch } from "vue-property-decorator";
import CocosCreator from "./cocosCreator";
import Feedback from "./components/Feedback.vue";
import ScreenshotMask from "./components/ScreenshotMask.vue";
import PreviewMixin from "./mixins";
import { processJsonCdnUrls } from "@/common/utils/imageUrlProcessor";

enum PreviewModes {
  MOBILE = "mobile",
  PC = "pc",
  SCREEN_SHOT = "screenshot",
}

// 易课堂播放快捷键
const YktShortKeys = {
  PREV_PAGE: "up,left",
  NEXT_PAGE: "down,right",
};

export interface BundleName {
  bundleName: string;
  category: number;
  parentVersion: number;
}
enum PreviewTypes {
  WEB = "web", // 包含二维码的预览界面
  MOBILE = "mobile", // 手机端
  WEBMOBILE = "webmobile", // 不包含二维码，目前只有内嵌的编辑器的场景在使用
}

enum TimeKeys {
  BeforeMount = "beforeMount",
  GetQuestionData = "getQuestionData",
  RenderQuestion = "renderQuestion",
  GetBundle = "getBundle",
  CallBoot = "callBoot",
  CreateQuestion = "createQuestion",
}

enum PageState {
  Loading = "loading", // 页面loading
  Booted = "booted", // cocos引擎初始化完成
  Rendered = "Rendered", // 题目渲染完成
}

@Component({
  components: {
    ScreenshotMask,
    Feedback
  },
})
export default class App extends Mixins(PreviewMixin) {
  [x: string]: any;
  containerWidth = 0;
  containerHeight = 0;
  isLoading = true;
  isPhone = false;
  previewType: PreviewTypes = PreviewTypes.WEB;
  safeWidth = 0;
  safeHeight = 0;
  category = 0;
  bundleName: BundleName[] = [];
  parentVersion = Number(localStorage.getItem("parentVersion")) || 1;
  questionData: any = null;
  jsonContent: any = null;
  showFeedbackRight = false;
  showFeedbackWrong = false;
  codeScale = Math.round((document.body.clientWidth / this.safeWidth) * 100);
  pageState: PageState = PageState.Loading;
  costTime = {
    default: Date.now(),
    beforeMount: {
      desc: "完成页面加载",
      duration: 0,
      start: Date.now(),
      end: 0,
    },
    getQuestionData: {
      desc: "获取题目数据",
      duration: 0,
      start: 0,
      end: 0,
    },
    renderQuestion: {
      desc: "渲染题目",
      duration: 0,
      start: 0,
      end: 0,
    },
    getBundle: {
      desc: "获取bundle",
      duration: 0,
      start: 0,
      end: 0,
    },
    callBoot: {
      desc: "调用boot",
      duration: 0,
      start: 0,
      end: 0,
    },
    createQuestion: {
      desc: "创建题目",
      duration: 0,
      start: 0,
      end: 0,
    },
  };
  cocosInstance: CocosCreator = new CocosCreator();
  restartIcon = require("./assets/alice_btn_1.png");
  showRestartButton = false;
  activeName: "question" | "analysis" | "answer" = "question";
  tabOps = [
    { label: "题目", value: "question" },
    { label: "解析", value: "analysis" },
    { label: "答案", value: "answer" },
  ];

  get tabs() {
    const { tabs: supportTabs } = parse(location.search);
    const supportTabsArr = supportTabs ? JSON.parse(supportTabs) : [];
    const temp = supportTabsArr && supportTabsArr.length ? this.tabOps.filter(tab => supportTabsArr.includes(tab.value)) : this.tabOps;
    return temp;
  }

  debuggerPanelComp: any = null;
  // 引入调试组件 是否引入取决于是否在缓存中加了debugger
  private registAsyncDebuggerComponent() {
    return import(/* webpackChunkName: "debuggerPanel" */ "./components/DebuggerPanel.vue").then(component => {
      this.debuggerPanelComp = Vue.extend(component.default);
      return this.$nextTick();
    });
  }

  setCostTime(key: string, { start, end }: { start?: number; end?: number }) {
    if (start) {
      this.costTime[key].start = start;
    }
    if (end) {
      this.costTime[key].end = end;
      this.costTime[key].duration = end - this.costTime[key].start;
    }
  }

  get containerStyle() {
    return [
      {
        width: this.containerWidth + "px",
        height: this.containerHeight + "px",
      },
    ];
  }

  get scale() {
    const scale = Math.round((this.containerWidth / this.safeWidth) * 100);
    return scale;
  }

  async handleClick(tab: string) {
    if (this.cocosInstance.isRendering) {
      Message.error("题目正在渲染中，请稍后再试");
      return;
    }
    if (this.activeName === tab) return;
    this.cocosInstance.showMask = false;
    switch (tab) {
      case "question":
        this.activeName = "question";
        // 渲染题目
        await this.handleRenderQuestion();
        this.cocosInstance.showQuestionContent();
        break;
      case "analysis":
        this.cocosInstance.hideQuestionContent();
        // 渲染解析
        this.activeName = "analysis";
        this.cocosInstance.renderAnalysis();
        this.showRestartButton = false;
        break;
      case "answer":
        this.cocosInstance.hideQuestionContent();
        // 渲染答案
        this.activeName = "answer";
        this.cocosInstance.renderRightAnswer();
        this.showRestartButton = false;
        break;
      default:
        break;
    }
  }

  referenceAnswerConfigs = {
    support: false,
    // 获取参考答案 getRefAn
    getResult: () => {
      if (!this.referenceAnswerConfigs.support) return;
      const res = this.cocosInstance.getReferenceAnswer();
      // 参考答案格式是数组， 如果不是数组的话，给出错误提示
      // 判断res是否是数组
      if (!Array.isArray(res)) {
        Message.error("参考答案必须是数组，请修改返回结果的格式后重试");
        return;
      }
      if (!res) {
        Message.error("获取参考答案失败");
      } else {
        // 将数据通过message发送给父窗口
        window.parent.postMessage(
          {
            action: "preview-reference-answer",
            data: JSON.parse(JSON.stringify(res)),
          },
          "*",
        );
      }
    },
  };

  async handleRenderQuestion(type?: SnapType | null) {
    (window as MyWindow).monitorManager.setStartByType("renderQuestion");
    this.setCostTime(TimeKeys.RenderQuestion, { start: Date.now() });
    await this.cocosInstance.renderQuestion(type);
    this.setCostTime(TimeKeys.RenderQuestion, { end: Date.now() });
    (window as MyWindow).monitorManager.setEndByType("renderQuestion");
    this.pageState = PageState.Rendered;
  }

  getContainerSize() {
    // 根据页面大小指定二维码的缩放策略
    let ratio = this.safeHeight / this.safeWidth;
    if (this.previewType === PreviewTypes.WEBMOBILE && !this.referenceAnswerConfigs.support) {
      // 判断宽高比接近4/3还是16/9
      ratio = 9 / 16;
      const appDom = document.querySelector("#app");
      if (appDom) {
        // 判断宽高比支持4/3的渲染就渲染4/3 +10是为了解决误差问题
        if (((appDom as any).offsetWidth * 3) / 4 < (appDom as any).offsetHeight + 10) {
          ratio = 3 / 4;
        }
      }
    }
    const heightPad = this.previewType === PreviewTypes.WEB ? 56 : 2;
    let widthPad = this.previewType === PreviewTypes.WEB ? 0 * 1 + 24 + 24 : 2;
    if (this.previewType === PreviewTypes.WEB) {
      this.codeScale = 1;
      if (document.body.clientHeight - heightPad < 300 || document.body.clientWidth < 700) {
        this.codeScale = Math.min((Math.round((document.body.clientWidth / this.safeWidth) * 100) / 100) * 2, 1);
      }
      widthPad = this.previewType === PreviewTypes.WEB ? 0 * this.codeScale + 24 + 24 : 2;
    } else {
      this.codeScale = 0;
    }

    const parentEl = {
      clientWidth: document.body.clientWidth - widthPad,
      clientHeight: document.body.clientHeight - heightPad,
    };
    if (this.isPhone) {
      this.containerWidth = parentEl.clientWidth;
      this.containerHeight = parentEl.clientHeight - 3;
    } else {
      if (parentEl.clientHeight / parentEl.clientWidth > ratio) {
        this.containerWidth = parentEl.clientWidth;
        this.containerHeight = this.containerWidth * ratio;
      } else {
        this.containerHeight = parentEl.clientHeight - 3;
        this.containerWidth = this.containerHeight / ratio;
      }
    }
  }

  beforeMount() {
    this.registerShortKeys();
    // answer 只有需要获取参考答案的时候才会有该参数 参数为1
    const { mode, answer } = parse(location.search);
    this.setCostTime("beforeMount", { end: Date.now() });
    // 告诉父页面已经加载完成
    if (answer) {
      window.parent.postMessage(
        {
          action: "preview-loaded",
        },
        "*",
      );
    }
    this.setCostTime(TimeKeys.GetQuestionData, { start: Date.now() });

    if (answer) {
      this.referenceAnswerConfigs.support = true;
      // 参考答案的数据由父级传入
      (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.DataInit);
    }

    const { phone: isPhone } = isMobile();
    this.isPhone = isPhone;
    if (isPhone) {
      this.previewType = PreviewTypes.MOBILE;
    } else if (mode === PreviewModes.MOBILE) {
      this.previewType = PreviewTypes.WEBMOBILE;
    } else {
      this.previewType = PreviewTypes.WEB;
    }
  }

  async initCocosAndRenderQuestion() {
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.GetBundleUrl);
    this.setCostTime(TimeKeys.GetBundle, { start: Date.now() });
    const bundleMapData = await this.getBundleMap();
    (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.GetBundleUrl);
    this.setCostTime(TimeKeys.GetBundle, { end: Date.now() });
    this.getContainerSize();
    this.setCostTime(TimeKeys.CallBoot, { start: Date.now() });
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosBoot);
    // 如果没有cocosAssetsDirName 等待 直到获取到cocosAssetsDirName 再继续执行
    const cocosAssetsDirName = await this.getCocosAssetsDirName();
    console.log("cocosAssetsDirName", cocosAssetsDirName);
    const cocosPath = isDevelopmentEnv ? window.location.origin + "/" + cocosAssetsDirName + "/" : window.location.origin + "/interactive-question-editor/" + cocosAssetsDirName + "/";
    await this.cocosInstance.initCocos({
      containerWidth: this.containerWidth,
      containerHeight: this.containerHeight,
      cocosPath,
      bundleMap: bundleMapData,
      callBack: {
        onShowResult: () => {
          // 非参考答案页面使用SetAnswer打点，从页面加载到开始作答的耗时
          if (!this.referenceAnswerConfigs.support) {
            (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.SetAnswer, performance.timing.navigationStart);
            (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.SetAnswer);
          }
          // 不显示通用反馈的题目类型
          const showFeedBackBlackList = [CATEGORY.XIAOLUPRACTICE];
          if (!showFeedBackBlackList.includes(this.category)) {
            this.cocosInstance.answerStatus === true ? (this.showFeedbackRight = true) : (this.showFeedbackWrong = true);
          }
          // 不显示遮罩的题目类型
          const showMaskBlackList = [
            CATEGORY.GROUP,
            CATEGORY.READCOMQUESTION,
            CATEGORY.VIDEOGROUP,
            CATEGORY.LISTENRETELL,
            CATEGORY.LISTENANSWER,
            CATEGORY.LOUDREADING,
            CATEGORY.FOLLOWWORDSGROUP,
            CATEGORY.XIAOLUPRACTICE,
            CATEGORY.CONTEXTUALANSWERQUESTION,
          ];
          if (!showMaskBlackList.includes(this.category)) {
            this.cocosInstance.showMask = true;
          }
          setTimeout(() => {
            if (this.cocosInstance.answerStatus === true) {
              this.referenceAnswerConfigs.getResult();
            }
            this.showFeedbackRight = false;
            this.showFeedbackWrong = false;
          }, 3000);
          this.showRestartButton = true;
        },
      },
    });
    this.setCostTime(TimeKeys.CallBoot, { end: Date.now() });
    (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosBoot);
    this.pageState = PageState.Booted;

    if (!this.tabs.length) {
      Message.error("tabs传参不支持，请检查后重试");
    } else {
      if (this.tabs[0].value === this.activeName) {
        await this.handleRenderQuestion();
      } else {
        this.handleClick(this.tabs[0].value);
      }
      console.log("handleRenderQuestion done");
      this.setCostTime(TimeKeys.CreateQuestion, { end: Date.now() });
      (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosInit);
      (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.TTI);
      (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CreateQuestion);
      this.isLoading = false;
      window.parent.postMessage(
        {
          action: "question-rendered",
        },
        "*",
      );
      this.cocosInstance.showQuestionContent();
    }
  }

  handleMessage(message: { data: { action: any; data: any; actionType: any } }) {
    if (!message.data) return;
    const { action, data } = message.data;
    if (!action) return;
    if (action === "preview-data") {
      this.log("::preview-data::", this.pageState);
      this.handlePreviewData(data);
    }
    if (action === "received-custom-answer") {
      this.log("::received-custom-answer::");
      const answerData = typeof data === "string" ? JSON.parse(data) : data;
      this.cocosInstance.renderReceivedAnswer(answerData);
    }
  }

  log(...params: any) {
    const style = "background: #e6faef;border: 1px solid #e6faef;color: #42c57a;font-size: 16px;margin: 3px;padding: 3px;";
    console.log("%cFE-Preview", style, ...params);
  }

  debugManager() {
    localStorage.setItem("pageTools", "预览工具箱, 在控制台输入pageTools即可使用，只有编辑器入口的预览有该功能");
    (window as any).pageTools = {
      desc: {
        name: "预览工具",
        getCostTime: "获取页面耗时，不需要传参数",
        getQuestionData: "获取题目数据，不需要传参数",
        setDebugger: "设置是否开启调试模式，参数为true/false",
        setBundleUrl: "设置bundleUrl，参数为bundleName和url",
        cocosInstance: "cocos在预览中的实例，可以调用cocosSDK的方法",
        setSpineMax: "设置题目允许的动效资源的最大数量",
      },
      getCostTime: () => {
        this.log((window as any).pageTools.desc.getCostTime);
        // 对象转数组
        const costTimeArray = Object.keys(this.costTime).map(key => {
          return {
            name: key,
            ...this.costTime[key],
          };
        });
        // 对象转表格
        console.table(costTimeArray);
        // console.table()
        // return this.costTime;
      },
      getPageState: () => {
        return this.pageState;
      },
      getQuestionData: () => {
        this.log((window as any).pageTools.desc.getQuestionData);
        return this.questionData;
      },
      setDebugger: (isDebugger: boolean) => {
        this.log((window as any).pageTools.desc.setDebugger);
        if (isDebugger) {
          this.registAsyncDebuggerComponent();
        } else {
          this.debuggerPanelComp = null;
        }
        this.cocosInstance.isDebugger = isDebugger;
      },
      setBundleUrl: (bundleName: string, url: string) => {
        this.log((window as any).pageTools.desc.setBundleUrl);
        localStorage.setItem(bundleName, url);
        this.log("设置bundleUrl成功", bundleName, url);
      },
      setSubmitDataMax: (num: string) => {
        this.log((window as any).pageTools.desc.setSubmitDataMax);
        localStorage.setItem("submitDataMax", num);
      },
      cocosInstance: this.cocosInstance,
    };
  }

  async created() {
    this.getContainerSize();
    try {
      this.getPreviewData(this.handlePreviewData);
    } catch (error) {
      showErrorMessage(error as Error);
    }
  }
  async mounted() {
    try {
      window.addEventListener("resize", this.getContainerSize);
      // 接收父窗口消息
      window.addEventListener("message", this.handleMessage, false);

      this.updateButtonPosition();
    } catch (err) {
      showErrorMessage(err as Error);
    }
  }

  destroyed() {
    this.unregisterShortKeys();
    window.removeEventListener("resize", this.getContainerSize);
    window.removeEventListener("message", this.handleMessage);
  }

  // async getPkgURLByBundleName(bundleName: string, status = 4): Promise<string> {
  //   const pkgUrl = await requestPhpServerPre
  //     .get(APIS.PHP_GET_PKGS, {
  //       params: {
  //         status, //2未启用 3 启用中 4已启用
  //       },
  //     })
  //     .then(res => {
  //       const pkg: any = res.data.data.pkgList ? res.data.data.pkgList[bundleName] : null;
  //       return pkg;
  //     });
  //   return pkgUrl;
  // }

  handlePreviewData(data: any) {
    this.questionData = data;
    // data.content = processJsonCdnUrls(data.content);
    // console.log("::handlePreviewData::", processJsonCdnUrls(data.content));
    const jsonContent = JSON.parse(processJsonCdnUrls(data.content));
    // console.log("::jsonContent::", jsonContent);
    this.jsonContent = jsonContent;
    const {
      template: { stage, category, bundleName },
    } = jsonContent;
    let isGroup = false;
    let subCategory = [];
    if (jsonContent.features && jsonContent.features.isGroup) {
      isGroup = true;
      subCategory = jsonContent.features.groupData.subCategory;
    }
    const { safeWidth, safeHeight } = stage;
    this.category = category;
    this.safeWidth = safeWidth;
    this.safeHeight = safeHeight;
    if (isGroup) {
      this.bundleName = subCategory;
    } else {
      this.bundleName = [
        {
          bundleName: bundleName,
          category,
          parentVersion: data.parentVersion ?? this.parentVersion,
        },
      ];
      this.log("::bundleName::", this.bundleName);
    }
    this.parentVersion = data.parentVersion ?? this.parentVersion;
    (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.DataInit);
    this.setCostTime(TimeKeys.GetQuestionData, { end: Date.now() });
  }

  @Watch("questionData")
  onQuestionDataChange() {
    const { tId } = parse(location.search);
    this.cocosInstance.initData(tId || this.questionData.id, this.jsonContent, this.questionData.standardData);
    (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosInit);
    this.setCostTime(TimeKeys.CreateQuestion, { start: Date.now() });
    this.$nextTick(async () => {
      if (this.pageState === PageState.Loading) {
        await this.initCocosAndRenderQuestion();
      } else {
        await this.handleRenderQuestion();
      }
      const allowDebugger = this.previewType === PreviewTypes.WEB && localStorage.getItem("debugger") === "1";
      if (allowDebugger) {
        this.registAsyncDebuggerComponent();
      }
      if (this.previewType === PreviewTypes.WEB) {
        this.debugManager();
      }
      this.updateButtonPosition();
    });
  }

  registerShortKeys() {
    hotkeys(YktShortKeys.PREV_PAGE, event => {
      event.preventDefault();
      window.parent.postMessage(
        {
          action: "cchd-playing",
          kjtype: "prev",
        },
        "*",
      );
    });
    hotkeys(YktShortKeys.NEXT_PAGE, event => {
      event.preventDefault();
      window.parent.postMessage(
        {
          action: "cchd-playing",
          kjtype: "next",
        },
        "*",
      );
    });
  }

  unregisterShortKeys() {
    const keys = Object.values(YktShortKeys);
    keys.forEach(key => hotkeys.unbind(key));
  }

  // 再次作答事件
  async handleRestartQuestion() {
    if (this.cocosInstance) {
      //this.cocosInstance.resetQuestion();
      if (this.showRestartButton == true) {
        this.showRestartButton = false;
        await this.handleRenderQuestion();
      }
    }
  }

  buttonPosition = { left: "0px", top: "0px" };

  updateButtonPosition() {
    this.$nextTick(() => {
      const container = this.$refs.canvasContainer as HTMLElement;
      if (container) {
        const containerRect = container.getBoundingClientRect();
        const calculatedLeft = containerRect.right - 104 - 16;
        const calculatedTop = containerRect.bottom - 32 - 16;

        console.log("更新按钮位置 - 容器:", containerRect);
        console.log("更新按钮位置 - 计算结果:", { left: calculatedLeft, top: calculatedTop });

        this.buttonPosition = {
          left: `${calculatedLeft}px`,
          top: `${calculatedTop}px`,
        };
      } else {
        console.log("updateButtonPosition: container ref不存在！");
      }
    });
  }

  get buttonStyle() {
    return {
      position: "fixed",
      left: this.buttonPosition.left,
      top: this.buttonPosition.top,
      width: "104px",
      height: "32px",
      backgroundColor: "#2ABD90",
      borderRadius: "4px",
      cursor: "pointer",
      zIndex: 99999,
      display: "block",
      boxShadow: "0 2px 8px rgba(29, 22, 22, 0.2)",
      pointerEvents: "auto",
      padding: "0",
    };
  }

  get iconStyle() {
    return {
      position: "absolute",
      left: "14px",
      top: "50%",
      transform: "translateY(-50%)",
      width: "16px",
      height: "16px",
      pointerEvents: "none",
    };
  }

  get textStyle() {
    return {
      position: "absolute",
      left: "34px",
      top: "50%",
      transform: "translateY(-50%)",
      color: "#ffffff",
      fontSize: "14px",
      fontWeight: "normal",
      whiteSpace: "nowrap",
      pointerEvents: "none",
    };
  }
}
</script>

<style lang="less">
:focus {
  outline: 0;
}

html {
  -ms-touch-action: none;
}

body,
canvas,
div {
  display: block;
  outline: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);

  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  -khtml-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

/* Remove spin of input type number */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0;
  /* <-- Apparently some margin are still there even though it's hidden */
}

html,
body,
#app {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
}

body {
  box-sizing: border-box;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 0;
  border: 0;
  margin: 0;

  cursor: default;
  // color: #888;
  // background-color: #333;

  text-align: center;
  font-family: Arial, Helvetica, Verdana, sans-serif;
  display: flex;
  flex-direction: column;
}

#Cocos2dGameContainer {
  // position: absolute;
  margin: 0;
  left: 0px;
  top: 0px;

  display: -webkit-box;
  -webkit-box-orient: horizontal;
  -webkit-box-align: center;
  -webkit-box-pack: center;
}

canvas {
  background-color: rgba(0, 0, 0, 0);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0;
  /* <-- Apparently some margin are still there even though it's hidden */
}

.stripes span {
  background-size: 30px 30px;
  background-image: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.15) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, 0.15) 50%,
    rgba(255, 255, 255, 0.15) 75%,
    transparent 75%,
    transparent
  );

  animation: animate-stripes 1s linear infinite;
}

@keyframes animate-stripes {
  0% {
    background-position: 0 0;
  }

  100% {
    background-position: 60px 0;
  }
}

.el-loading-spinner .path {
  stroke: #42c57a;
}

.el-loading-spinner .el-loading-text {
  color: #42c57a;
}

.el-loading-spinner i {
  color: #42c57a;
}
</style>
<style lang="less" scoped>
.mobile-container-wrapper {
  display: flex;
  align-items: center;
}

#app {
  overflow: hidden;

  .pc-container {
    .title {
      line-height: 40px;
      min-width: 100px;
      margin: 0;
      color: #000000d9;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
      word-wrap: break-word;
    }

    .main {
      display: flex;

      /deep/ .tabs {
        width: calc(100vw - 296px);
      }

      .tabs-header-wrapper {
        position: relative;
        height: 42px;
        font-size: 14px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 12px;
      }

      .tabs-header {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        border: 0;
        display: flex;
        transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      }

      .tabs-content {
        display: flex;
        position: relative;
        justify-content: center;
      }

      .tab-nav {
        position: relative;
        display: inline-block;
        box-sizing: border-box;
        height: 100%;
        margin: 0 32px 0 0;
        padding: 0px 16px;
        text-decoration: none;
        text-align: center;
        cursor: pointer;
        transition: color 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        color: #000000d9;
        text-shadow: 0 0 0.25px currentColor;
        line-height: 40px;
        border-bottom: 2px solid #fff;
        font-size: 14px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 60px;

        &.active {
          color: #2abd90;
          border-bottom: 2px solid #2abd90;
          top: 1px;
        }
      }

      .canvas-container-wrapper {
        height: 100%;
        flex: 1;
        position: absolute;
        left: 0;
        top: 0;
        left: 0;
        right: 0;
      }
    }
  }

  .qr-code-wrapper {
    position: relative;
    top: 56px;
    margin-left: 24px;
    background-color: #f3f3f7;
  }

  .mobile-container-wrapper {
    width: calc(100vh);
    padding-left: 33%;
    padding-top: 100%;
    position: relative;
  }

  .mobile-container {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  .reset-btn-wrpper {
    position: absolute;
    right: 0;
    bottom: 12.5%;
    z-index: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 114px;
    height: 114px;
    transform-origin: right bottom;
  }

  .reset-btn {
    background: url("./assets/reset.png") no-repeat;
    border: 1px solid #dcdfe6;
    border-radius: 50%;
    padding: 0 10px;
    cursor: pointer;
    width: 114px;
    height: 114px;
    background-size: 100% 100%;
    transform-origin: center;
  }

  .canvas-container {
    position: relative;
    box-shadow: inset 0 0 0px 1px #f0f0f0;
    font-family: Arial !important;
    font-weight: normal;
  }

  .screenshot-mask {
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}
</style>
