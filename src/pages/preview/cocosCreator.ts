import { Message } from "element-ui";
import cocosSDK, {
  QTE_RESET_TYPE,
  QTE_SUBMIT_TYPE,
  PageInfoOrigin,
  QTETemplateCommonParams,
  QTETemplateParams,
  QTETemplateJudgeParams,
  AdapterLinkType,
  RenderOption,
  CocosQuestion,
  IStandardSubmitData,
  SnapType,
} from "qte-render";
import { MessageBox } from 'element-ui';
import { CATEGORY } from "@/common/constants";
import { StandardData } from "@/common/utils/standardDataManager/getStandardData";
import { genTextureCdnUrl } from "../index/common/utils/genThumbnail";

const log = (params: any[]) => {
  console.log(...['CocosInstance-FE', ...params]);
}

class CocosCreator {
  public isDebugger = false;
  public isRendering = false;
  public answerStatus: boolean | undefined = undefined; // undefined 未作答 true正确 false错误
  public currentQuestion!: CocosQuestion;
  public data!: PageInfoOrigin;
  public qteKey!: string;
  public standardData!: StandardData;
  // 预览页面作答的数据
  public customAnswerMap: Record<string, IStandardSubmitData[]> = {};
  // 通过postMessage接收到的作答数据
  public receivedAnswerMap: Record<string, IStandardSubmitData[]> = {};
  public showMask = false;
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {
    log(['初始化实例成功']);
    this.isRendering = false;
    this.answerStatus = undefined;
  }

  private handleQuestionData(id: string | number | undefined, questionContent: { features?: any; template: any; questions?: any }) {
    const questionData = new PageInfoOrigin();
    questionData.id = id; // 题目唯一id
    questionData.category = questionContent.template.category; // 题目的category

    // 题组的数据和单题的数据不同 需要做处理--待确认
    const features = questionContent.features ? questionContent.features : questionContent.template.features;
    questionData.othersExt = {
      customData: JSON.stringify(questionContent),
      features: JSON.stringify(features),
    };
    if (questionContent.features) {
      const { template, questions } = questionContent;
      questionData.othersExt.customData = JSON.stringify({
        template: template,
        questions: questions.map((quesItem: { content: string; }) => {
          return JSON.parse(quesItem.content)
        })
      })
    }
    return questionData;
  }

  private handleRenderOption(type?: any) {
    const option: RenderOption = {
      readonly: false,
      qteKey: this.qteKey,
      recoverData: this.customAnswerMap[this.qteKey] || [],
    };
    if (type) {
      option.type = type as SnapType;
    }
    option.settings = { submit: QTE_SUBMIT_TYPE.DEFAULT, reset: QTE_RESET_TYPE.DEFAULT };
    if (option.type === SnapType.CUSTOMANSWER || option.type === SnapType.RIGHTANSWER) {
      option.settings = { submit: QTE_SUBMIT_TYPE.HIDE, reset: QTE_RESET_TYPE.HIDE };
    }
    return option;
  }

  private handleRenderReceivedAnswerOption() {
    const option: RenderOption = {
      readonly: false,
      qteKey: this.qteKey,
      recoverData: this.receivedAnswerMap[this.qteKey] || [],
    };
    option.type = SnapType.CUSTOMANSWER;
    option.settings = { submit: QTE_SUBMIT_TYPE.HIDE, reset: QTE_RESET_TYPE.HIDE };
    return option;
  }

  public initData(id: string | number | undefined, questionContent: { features?: any; template: any; questions?: any; }, standardData: StandardData) {
    this.data = this.handleQuestionData(id, questionContent)
    this.qteKey = String(id);
    this.standardData = standardData;
  }

  public async initCocos({ containerWidth, containerHeight, cocosPath, bundleMap, callBack: { onShowResult } }: {
    containerWidth: number,
    containerHeight: number,
    cocosPath: string,
    bundleMap: { [key: string]: string },
    callBack: {
      onShowResult: (result: any) => void;
    }
  }) {
    await cocosSDK.boot({
      isLowDevice: false,
      canvasId: "GameCanvas",
      w: containerWidth,
      h: containerHeight,
      cocosPath: cocosPath,
      bundleMap: bundleMap,
      cocosScript: "dynamic",
      adpater: {
        type: AdapterLinkType.PREVIEW,
        logCat(arg) {
          console.warn("arg", arg);
        },
      },
      hooks: {
        onCreate: async (vo: QTETemplateCommonParams) => {
          console.info("QTE", "onCreate =====> BEGIN", vo);
          console.info("QTE", "onCreate =====> END");
        },
        onStart: async (vo: QTETemplateCommonParams) => {
          console.info("QTE", "onStart =====> BEGIN", vo);
          console.info("QTE", "onStart =====> END");
        },
        onReset: async (vo: QTETemplateParams) => {
          console.info("QTE", "onReset =====> BEGIN", vo);
          console.info("QTE", "onReset =====> END");
          this.answerStatus = undefined;
        },
        onSubmit: async (vo: IStandardSubmitData[]) => {
          console.info("用户提交数据", vo);
          const isDragQuestion = this.data.category === CATEGORY.DRAG;
          let answerStatusMaxLen = 0;
          let submitStatusMaxLen = 0;
          const limit = localStorage.getItem('submitDataMax') ? Number(localStorage.getItem('submitDataMax')) : 15;
          vo.forEach(item => {
            const { answerStatus, submitStatus, children } = item.submitData;
            // features中isGroup为true的，提交数据在children中
            let tempSubmitStatusStatus = '';
            if (submitStatus && (submitStatus as any).status) {
              tempSubmitStatusStatus = (submitStatus as any).status;
            } else if (children && children.length) {
              tempSubmitStatusStatus = children.map((item) => (item as any).submitStatus && (item as any).submitStatus.status ? (item as any).submitStatus.status : '').join('');
            }
            console.log('submitStatusStatus...', tempSubmitStatusStatus);
            const tempAnswerStatus = children && children.length ? children.map((item) => (item as any).answerStatus) : answerStatus;
            const answerStatusLen = this.calcBiteLen(JSON.stringify(tempAnswerStatus));
            const submitStatusLen = this.calcBiteLen(JSON.stringify(tempSubmitStatusStatus));
            if (answerStatusLen > answerStatusMaxLen) {
              answerStatusMaxLen = answerStatusLen;
            }
            if (submitStatusLen > submitStatusMaxLen) {
              submitStatusMaxLen = submitStatusLen;
            }
          })
          if (answerStatusMaxLen > limit || submitStatusMaxLen > limit) {
            const lenMessage = `<p style="font-size: 12px;color: #999; margin-top: 10px;">
            ${answerStatusMaxLen > limit ? `answerStatus: （${answerStatusMaxLen} / ${limit}） ` : ''}
            ${submitStatusMaxLen > limit ? `submitStatus: （${submitStatusMaxLen} / ${limit}） ` : ''}
            </p>`
            const messageArr = [isDragQuestion ? '<div class="el-icon-warning" style="color:#E6A23C; font-size: 48px;margin-bottom: 10px;"></div><p style="font-size: 16px;"><strong>当前题目拖拽元素次数设置过多，请修改后重试.</strong></p><p style="font-size: 16px;"><strong>如有疑问请联系产品或研发老师进行排查～</strong><p>' : '<div class="el-icon-warning" style="color:#E6A23C; font-size: 48px;margin-bottom: 10px;"></div><p style="font-size: 16px;"><strong>题目数据异常，请联系产品或研发老师进行排查～</strong><p>', lenMessage]
            const title = `提交数据超出限制`;
            MessageBox.confirm(messageArr.join(''), title, {
              center: true,
              showClose: false,
              showCancelButton: false,
              showConfirmButton: false,
              closeOnClickModal: false,
              dangerouslyUseHTMLString: true
            })
          }
        },
        /** 题版提交后展示完成动画调用弹框 */
        onShowResult: async (res: any, vo: QTETemplateParams) => {
          console.log("反馈弹框数据:", res, vo);
          if (vo["isCorrect"] == 1) {
            this.answerStatus = true;
          } else {
            this.answerStatus = false;
          }
          onShowResult(vo);
        },
        onPause: async (vo: QTETemplateParams) => {
          console.info("QTE", "onPause =====> BEGIN", vo);
          console.info("QTE", "onPause =====> END");
        },
        onResume: async (vo: QTETemplateParams) => {
          console.info("QTE", "onResume =====> BEGIN", vo);
          console.info("QTE", "onResume =====> END");
        },
        onDestroy: async (vo: QTETemplateParams) => {
          console.info("QTE", "onDestroy =====> BEGIN", vo);
          console.info("QTE", "onDestroy =====> END");
        },
        onJudge: async (vo: QTETemplateJudgeParams) => {
          console.info("QTE", "onJudge =====> BEGIN", vo);
          console.info("QTE", "onJudge =====> END");
        },
      },
    });
  }

  public async renderQuestion(type?: SnapType | null) {
    this.showMask = false;
    const option = this.handleRenderOption(type);
    this.currentQuestion = await cocosSDK.createQuestion(this.data, option);
    return this.currentQuestion;
  }

  public async renderReceivedAnswer(data: any) {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    this.isRendering = true;
    this.showMask = false;
    this.receivedAnswerMap[data[0].tid + ""] = data;
    const option = this.handleRenderReceivedAnswerOption();
    this.currentQuestion = await cocosSDK.createQuestion(this.data, option);
    this.isRendering = false;
    return this.currentQuestion;
  }

  /** 渲染正确答案 */
  public async renderRightAnswer() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    this.isRendering = true;
    await this.renderQuestion(SnapType.RIGHTANSWER);
    this.isRendering = false;
  }

  /**渲染半道题 */
  public async renderHalfQuestion() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    this.isRendering = true;
    await this.renderQuestion();
    this.isRendering = false;
  }

  /** 渲染我的答案 */
  async renderCustomAnswer() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    this.isRendering = true;
    await this.renderQuestion(SnapType.CUSTOMANSWER);
    this.isRendering = false;
  }

  async renderSubQuestion(index: number) {
    console.log('renderSubQuestion-index', index);
    await (this.currentQuestion as any).switchQuestion(index, true);
  }
  async getTextureUrls(textures: cc.Texture2D[]) {
    // 对比texture
    // const texturesData = textures.map((texture) => {
    //   return ((texture as any)['texture'] as any).readPixels();
    // });
    // console.log('jietu-texturesData', texturesData);
    const urls: string[] = [];
    for (const i in textures) {
      const texture = textures[i];
      urls[i] = await genTextureCdnUrl(texture);
      // console.log('jietu-iii', i, urls[i]);
    }
    return urls;
  }
  async insertUrls2StandardData(urls: string[]) {
    this.standardData.questionStructure.children.forEach((child, i: number) => {
      child.questionInitScene = urls[i];
    })
    return this.standardData;
  }

  /** 渲染解析 */
  async renderAnalysis() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    if (!this.currentQuestion) {
      // Message.error("题目渲染完毕后，才可获取用户作答数据");
      await this.renderQuestion();
      // return;
    }
    this.isRendering = true;
    this.currentQuestion?.destroy();
    await this.currentQuestion?.renderAnalysis();
    this.isRendering = false;
  }

  getQuestionContent() {
    const parseOthersExt = JSON.parse(this.data.othersExt.customData);
    const introduction = parseOthersExt.extraStageData.introduction
    ;
    if(!introduction) return undefined;
    if(!introduction.textUrls || !introduction.textUrls.length) return undefined;
    if(!introduction.isSet) return undefined;
    return introduction.textUrls;
  }

  showQuestionContent() {
    const questionContent = this.getQuestionContent();
    if(!questionContent) return;
    console.log('showQuestionContent', questionContent);
    //@ts-ignore
    this.currentQuestion?.showQuestionContent(questionContent);
  }

  hideQuestionContent() {
    const questionContent = this.getQuestionContent();
    if(!questionContent) return;
    console.log('hideQuestionContent-todo');
  }

  /** 删除解析 */
  deleteAnalysis() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    if (!this.currentQuestion) {
      Message.error("题目渲染完毕后，才可获取用户作答数据");
      return;
    }
    this.isRendering = true;
    this.currentQuestion?.deleteAnalysis();
    this.isRendering = false;
  }
  /** 获取用户作答数据 */
  getReferenceAnswer(): any {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    if (!this.currentQuestion) {
      Message.error("题目渲染完毕后，才可获取用户作答数据");
      return;
    }
    this.isRendering = true;
    const data = this.currentQuestion?.setReferenceAnswer();
    this.isRendering = false;
    log(["获得参考答案", data]);
    return data;
  }

  /** 重置 */
  async resetQuestion() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    if (!this.currentQuestion) {
      Message.error("题目渲染完毕后，才可使用重置功能");
      return;
    }
    this.isRendering = true;
    this.showMask = false;
    await this.currentQuestion?.reset();
    this.isRendering = false;
  }
  /** 提交 */
  submitQuestion() {
    if (this.isRendering) {
      Message.error("题目正在渲染，请稍后再试");
      return;
    }
    if (!this.currentQuestion) {
      Message.error("题目渲染完毕后，才可使用重置功能");
      return;
    }
    this.isRendering = true;
    const data = this.currentQuestion?.getAnswerResult();
    this.customAnswerMap[data[0].tid + ""] = data;
    this.isRendering = false;
    log(["获得标准提交数据", data]);
  }

  setDebugMode(isDebug: boolean) {
    this.isDebugger = isDebug;
  }

  // 检查字符串长度，单位为字节
  private calcBiteLen(str: string) {
    return Number((str.length / 1024).toFixed(2));
  }
}

export default CocosCreator;

