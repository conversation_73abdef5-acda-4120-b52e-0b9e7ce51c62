<template>
  <div class="qr-code">
    <span class="code-title">预览本题</span>
    <canvas ref="canvas" />
    <span class="tips">注意：手机请连接公司内网后预览</span>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";
import qrcode from "qrcode";

@Component
export default class QrCode extends Vue {
  get url() {
    return location.href;
  }

  mounted() {
    const canvas = this.$refs.canvas;
    qrcode.toCanvas(canvas, this.url);
  }

  hide() {
    this.$emit("update:visible", false);
  }
}
</script>

<style scoped lang="less">
.qr-code {
  width: 248px;
  height: 300px;
  background-color: #f3f3f7;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center;
  .code-title {
    font-size: 14px;
    text-align: center;
    display: inline-block;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #2abd90;
    color: #fff;
  }
  canvas {
    height: 178px !important;
    width: 178px !important;
    margin-top: 32px;
  }

  .tips {
    margin: 16px 0;
    color: #777;
    font-size: 12px;
  }

  .el-icon-close {
    position: absolute;
    right: 0;
    top: 0;
    display: block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
  }
}
</style>
