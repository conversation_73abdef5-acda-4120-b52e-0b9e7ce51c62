<template>
  <div class="debugger-panel">
    <el-button @click="handleClick" type="text" class="debugger-handler" size="mini">点我调试</el-button>
    <el-drawer title="调试工具" :visible.sync="cocosInstance.isDebugger" :modal="false" direction="rtl" size="80">
      <div class="debugger-main">
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.resetQuestion()">重置</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.submitQuestion()">提交</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.renderHalfQuestion()">渲染半道题</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.renderCustomAnswer()">我的答案</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.renderRightAnswer()">正确答案</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.renderAnalysis()">渲染题目解析</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.deleteAnalysis()">删除题目解析</el-button>
        <el-button type="success" plain size="small" class="btn" @click="cocosInstance.getReferenceAnswer()">获取参考答案</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import CocosCreator from "../cocosCreator";
@Component
export default class DebuggerPanel extends Vue {
  @Prop() public cocosInstance!: CocosCreator;
  handleClick() {
    this.cocosInstance.isDebugger = true;
  }
}
</script>

<style lang="less" scoped>
.debugger-panel {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999;
  width: 0;
  height: 0;
}
.debugger-main {
  width: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .btn {
    display: block;
    margin-bottom: 12px;
    width: 160px;
    margin-left: 10px;
  }
}
.debugger-handler {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 999;
  color: #666;
}
</style>
