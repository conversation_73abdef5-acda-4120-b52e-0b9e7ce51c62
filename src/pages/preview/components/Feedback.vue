<template>
  <div
    v-if="showFeedbackRight || showFeedbackWrong"
    class="feedback"
    :class="{ 'right-feedback': showFeedbackRight, 'wrong-feedback': showFeedbackWrong}"
    :style="{ zoom: scale + '%' }">
    <template v-if="showFeedbackRight">
      <img alt="" class="toast" src="../assets/alice_asset_1.png" />
      <img alt="" class="feedback-title" src="../assets/alice_title_1.png" />
    </template>
    <template v-if="showFeedbackWrong">
      <img alt="" class="toast" src="../assets/alice_asset_2.png" />
      <img alt="" class="feedback-title" src="../assets/alice_title_2.png" />
    </template>
  </div>
</template>
<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
@Component
export default class Feedback extends Vue {
  @Prop({ default: false }) showFeedbackRight!: boolean;
  @Prop({ default: false }) showFeedbackWrong!: boolean;
  @Prop({ default: false }) scale!: number;
}
</script>
<style lang="less" scoped>
.feedback {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 400px;
  height: 400px;

  .toast {
    width: 100%;
  }

  &.right-feedback {
    width: 600px;
    height: 635px;

    .feedback-title {
      transform: translate(-50%, 38px);
    }
  }

  .feedback-title {
    position: absolute;
    width: 200px;
    bottom: 50%;
    left: 50%;
    transform: translate(-50%, 40px);
  }
}
</style>
