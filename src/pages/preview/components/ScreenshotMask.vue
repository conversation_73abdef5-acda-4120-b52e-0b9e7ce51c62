<template>
  <div v-if="visible" class="screenshot-mask">
    正在自动截图，请勿进行任何操作
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

@Component
export default class ScreenshotMask extends Vue {
  @Prop({
    default: false,
  })
  visible!: boolean;
}
</script>

<style scoped lang="less">
.screenshot-mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.9);
  font-size: 60px;
  color: #fff;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
