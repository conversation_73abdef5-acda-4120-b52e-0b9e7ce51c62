import { parse } from "query-string";
import { Component, Vue } from "vue-property-decorator";
import { nativeRequestServer } from "@/common/nativeUtils";
import { isDevelopmentEnv } from "@/common/utils/env";
@Component

export default class PreviewMixin extends Vue {
  handlePreviewData: any;
  parentVersion: any;
  bundleName: any;
  async getPreviewData(cb: (arg0: any) => void) {
    const { previewId } = parse(window.location.search);
    const res = await nativeRequestServer.get(`/editorApi/getSubjectDetails?id=${previewId}`);
    const { data: { data } } = res;
    console.log('data..pre', data);
    const tempData = JSON.parse(data)
    cb(tempData);
  }
  /**
 * 如果想要指定地址 可在localStorage添加,key为bundleName，value为指定的url
 */
  async getBundleMap() {
    console.log('PreviewMixin, getBundleMap');
    const { qte, bundlePath, bundleName } = parse(window.location.search);
    const cacheQte = localStorage.getItem("qte") || '';
    const qteUrl = qte || cacheQte || 'http://jiaoyanbos.cdnjtzy.com/cocos/dir/5d10f547fbf78c08fff3f4ffe2996a71';
    if(!bundlePath) {
      this.$message.error('页面参数中缺少bundlePath，请添加后再访问');
    }
    console.log('PreviewMixin, getBundleMap', {
      qte: `${qteUrl}/qte`,
      [bundleName]: bundlePath
    });
    return Promise.resolve({
      qte: `${qteUrl}/qte`,
      [bundleName]: bundlePath
    });
  }

  getCocosAssetsDirName() {
    console.log('PreviewMixin, getCocosAssetsDirName');
    return new Promise(resolve => {
      if ((window as MyWindow).cocosAssetsDirName) {
        resolve((window as MyWindow).cocosAssetsDirName);
      } else {
        const readyTimer = setInterval(() => {
          if ((window as MyWindow).cocosAssetsDirName) {
            clearInterval(readyTimer);
            resolve((window as MyWindow).cocosAssetsDirName);
          }
        }, 300);
      }
    });
  }
}