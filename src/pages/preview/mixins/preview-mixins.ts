import APIS from "@/common/api/constants";
import { CATEGORY } from "@/common/constants";
import { TimeMonitorType } from "@/common/utils/monitorUtil";
import { requestPhpServer, requestPhpServerPre } from "@/common/utils/request";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { parse } from "query-string";
import { Component, Vue } from "vue-property-decorator";
import { BundleName } from "../App.vue";
@Component

export default class PreviewMixin extends Vue {
  handlePreviewData: any;
  parentVersion: any;
  bundleName: any;
  getPreviewData(cb: any) {
    console.log('PreviewMixin, getPreviewData');
    const { previewId, snapId, qid, tId } = parse(location.search);
    if (!previewId && !snapId && !qid && !tId) return;
    const requestInstance = (() => {
      if (tId) {
        return requestPhpServer.post(APIS.PHP_GET_TID_INFO, {
          tid: tId,
        });
      }
      if (qid) {
        return requestPhpServer.post(APIS.PHP_GET_QUESTION_INFO, {
          qid,
        });
      }
      if (snapId) {
        return requestPhpServer.post(APIS.PHP_GET_VERSION_INFO, {
          snapId,
        });
      }
      return requestPhpServer.get(APIS.PHP_GET_PREVIEW_DATA, {
        params: { vid: previewId },
      });
    })();
    return requestInstance
      .then(({ data: { data } }) => {
        cb(data);
      })
      .catch(showErrorMessage);
  }

  async getPkgURLByBundleName(bundleName: string, status = 4): Promise<string> {
    console.log('PreviewMixin, getPkgURLByBundleName',);
    const pkgUrl = await requestPhpServerPre
      .get(APIS.PHP_GET_PKGS, {
        params: {
          status, //2未启用 3 启用中 4已启用
        },
      })
      .then(res => {
        const pkg: any = res.data.data.pkgList ? res.data.data.pkgList[bundleName] : null;
        return pkg;
      });
    return pkgUrl;
  }
  /**
 * 如果想要指定地址 可在localStorage添加,key为bundleName，value为指定的url
 */
  async getBundleMap() {
    console.log('PreviewMixin, getBundleMap');
    const promiseArray = [{ bundleName: "qte", category: CATEGORY.QTE, parentVersion: this.parentVersion }, ...this.bundleName].map(async (item: BundleName) => {
      let url = "";
      const cacheUrl = localStorage.getItem(`my${item.bundleName}`) || localStorage.getItem(item.bundleName);
      if (cacheUrl) {
        url = cacheUrl + "/" + item.bundleName;
        // 延时重新存储url
        const timer = setTimeout(() => {
          clearTimeout(timer);
          this.getPkgURLByBundleName(item.bundleName, 4).then((res: string) => {
            localStorage.setItem(item.bundleName, res);
          });
        }, 3000);
      } else {
        const pkgUrl = await this.getPkgURLByBundleName(item.bundleName, 4);
        url = pkgUrl + "/" + item.bundleName;
        localStorage.setItem(item.bundleName, pkgUrl);
      }
      if (!url) {
        throw Error("程序包地址获取失败，请确认程序包地址无误后再重试");
      }
      return { name: item.bundleName, url };
    });
    return Promise.all(promiseArray).then(res => {
      const bundleMap: Record<string, string> = {};
      res.forEach((item: { name: string; url: string }) => {
        if (!item.url.includes("null")) {
          bundleMap[item.name] = item.url;
        }
      });
      return Promise.resolve(bundleMap);
    });
  }

  getCocosAssetsDirName() {
    console.log('PreviewMixin, getCocosAssetsDirName');
    return new Promise(resolve => {
      if ((window as MyWindow).cocosAssetsDirName) {
        resolve((window as MyWindow).cocosAssetsDirName);
      } else {
        const readyTimer = setInterval(() => {
          if ((window as MyWindow).cocosAssetsDirName) {
            clearInterval(readyTimer);
            resolve((window as MyWindow).cocosAssetsDirName);
          }
        }, 300);
      }
    });
  }
}