/* eslint-disable @typescript-eslint/no-var-requires */
import { addQuestion } from "@/common/api/question";
import { createVersion } from "@/common/api/question";
export const mockCreateTizu = async () => {
  const data = require('./createShark.json');
  console.log('xu-mockCreateTizu', data);
  data.content = JSON.stringify(data.content);
  data.tags = JSON.stringify(data.tags);
  data.features = JSON.stringify(data.features);
  const qdata = await addQuestion(data);
  const qid = qdata.data.data.qid;
  const {
    data: { data: versionData },
  } = await createVersion({
    qid,
    gradeId: data.gradeId,
    subjectId: data.subjectId,
    parentVersion: data.parentVersion,
    tags: data.tags,
    content: data.content,
    thumbnail: data.thumbnail,
    features: data.features,
  });
  console.log('xu-tizu-versionData', versionData);
}



