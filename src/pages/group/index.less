@import "~@/assets/icon/iconfont.css";
.cocosicon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
:focus {
  outline: 0;
}

html,
body,
#app {
  height: 100%;
  overflow: hidden;
  user-select: none;
}

#app {
  font-family: Arial,Avenir, Helvetica,  sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
  background: #f5f5f5;
  position: relative;
}
.group-container {
  .cursor {
    transition: 0.5s all;
    cursor: pointer;
  }
  .disabled {
    cursor: none;
    opacity: 0.3;
    pointer-events: none;
  }
  .gray {
    cursor: default;
    opacity: 0.6;
    &:hover {
      color: rgba(51, 51, 51, 0.5) !important;
      border-color: #F0F1F5 !important;
      svg {
        color: currentColor !important;
      }
    }
    &.add{
      opacity: 1;
      color: rgba(51, 51, 51, 0.5) !important;
      svg {
        color: currentColor !important;
      }
      &:hover {
        color: rgba(51, 51, 51, 0.5) !important;
        border-color: #F0F1F5 !important;
        svg {
          color: currentColor !important;
          color: rgba(51, 51, 51, 0.5) !important;
        }
      }
    }
  }
  svg:hover {
    color: #25B864;
  }
  .cocosicon {
    font-size: 16px;
    color: #777777;
  }
  position: absolute;
  z-index: 10;
  height: 0vh;
  width: 0;
  background-color: #ccc;
  .question-wrapper {
    &.show {
      transform: translate(0%, 0px);
      margin-left: 0px;
    }
    transition: 0.1s transform;
    transform: translate(100%, 0px);
    position: fixed;
    width: 360px;
    height: 100vh;
    margin-left: 12px;
    background-color: rgba(255,255,255,1);
    top: 60px;
    right: -400px;
    padding-left: 16px;
    padding-right: 16px;
    box-sizing: border-box;
    .qst-handles {
      position: absolute;
      top: 50%;
      left: -12px;
      transform: translate(0, -50%);
      width: 12px;
      height: 56px;
      background: #FFFFFF;
      border-radius: 8px 0px 0px 8px;
      display: flex;
      justify-items: center;
      align-items: center;
      svg {
        font-size: 14px !important;
        color: #83868f;
      }
    }
    .title {
      height: 40px;
      line-height: 40px;
      font-weight: 500;
      color: #292E33;
      font-size: 14px;
    }
    .tabs-handle {
      display: flex;
      flex: 1;
    }
    .tabs-button {
      height: 40px;
      background: #F0F1F5;
      text-align: center;
      line-height: 40px;
      flex: 1;
      width: 48px;
      font-size: 14px;
      font-weight: 400;
      color: #777777;
      position: relative;
      &.active {
        font-weight: 500;
        color: #333333;
        &::after {
          content: '';
          display: block;
          position: absolute;
          width: 24px;
          height: 2px;
          background: #25B864;
          left: 50%;
          bottom: 0;
          transform: translate(-50%, 0);
        }
      }
    }
  }
  .group-handle {
    width: 40px;
    position: fixed;
    top: 60px;
    border-radius: 2px;
    background-color: #fff;
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 8px 4px 4px 4px;
    box-sizing: border-box;
    justify-content: space-between;
    .slide-item {
      border-radius: 4px;
      padding: 4px;
      div {
        width: 16px;
        height: 16px;
        border-radius: 2px;
        border: 1px solid #777777;
        color: #777777;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
      }
      &.active {
        background: #F0F2F5;
        div {
          background: #24B366;
          border: 1px solid #24B366;
          color: #fff;
        }
      }
      &:hover {
        background: #F0F2F5;
      }
      margin-bottom: 12px;
    }
    .handle-icon {
      display: block;
      margin-bottom: 12px;
      color: #777777;
      border-radius: 4px;
      margin-bottom: 12px;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      &.gray {
        &:hover {
          background: unset;
        }
        svg {
          color: #777777 !important;
        }
      }
      &:hover {
        background: #F0F2F5;
        svg {
          color: #777777 !important;
        }
      }
      &:last-of-type {
        margin-bottom: 4px;
      }
      svg {
        font-size: 20px !important;
      }
    }
  }

  .style-settings {
    display: flex;
    justify-content: space-between;
  }
  .style-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: 18px;
    &.active {
      .thumbnail {
        border-color: #24B366;
        i {
          display: block;
        }
      }
      span {
        color: #24B366;
      }
    }
    .thumbnail {
      width: 156px;
      height: 87.75px;
      background-color: #2c3e50;
      border: 1px solid #FFFFFF;
      position: relative;
      i {
        display: none;
        position: absolute;
        right: 5px;
        top: 5px;
        color: #24B366;
        background: url("~@/assets/img/<EMAIL>") repeat; 
        background-size: 18px 18px;
        width: 18px;
        height: 18px;
      }
      img {
        width: 100%;
        height: 100%;
      }
    }
    span {
      width: 99px;
      height: 11px;
      font-size: 12px;
      font-weight: 400;
      color: #555555;
      line-height: 12px;
      text-align: center;
      margin-top: 8px;
    }
  }
  .qst-settings {
    .add {
      svg {
        font-size: 12px;
      }
      &:hover {
        color: #24B366;
        border-color: #24B366;
        svg {
          color: #24B366;
        }
      }
      &.disabled {
        pointer-events: none;
      }
      transition: 0.5s all;
      height: 32px;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #F0F1F5;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      text-align: center;
      line-height: 32px;
      margin-top: 16px;
      svg {
        margin-right: 6px;
        font-size: 12px;
        color: #333333;
      }
    }
    .qst-list {
      margin-top: 8px;
    }
    .edit-name-wrapper {
      flex: 1;
      input {
        border-color: #24B366;
        height: 24px;
      }
      & + i {
        color: #24B366;
      }
      input {
        font-size: 14px !important;
      }
    }
    .qst-item {
      height: 40px;
      font-size: 12px;
      font-weight: 400;
      color: #333333;
      position: relative;
      &:hover {
        background-color: #F7F8FC;
      }
      // border: 1px solid rgba(0,0,0,0);
      &.active {
        background-color: #E9F7EF;
      }
      .qst-item-content {
        display: flex;
        align-items: center;
        height: 40px;
        padding-right: 8px;
      }
      &.drag-hover {
        // box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.2);
        // margin-bottom: 3px;
        // .dot-icon {
        //   color: #24B366;
        // }
        .drag-line {
          display: block;
        }
      }
      &.drag-curr {
        box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.2);
        margin-bottom: 3px;
        .dot-icon svg {
          color: #24B366;
        }
        // .drag-line {
        //   display: block;
        // }
      }
      .drag-line {
        display: none;
        position: absolute;
        width: calc(100% - 32px);
        height: 1px;
        background: rgba(0,0,0,.3);
        left: 16px;
        bottom: -3px;
        height: 1px;
        background: #24B366;
      }
      i {
        margin-left: 8px;
      }
      .dot-icon {
        position: relative;
        height: 20px;
        svg:hover {
          color: #777777 !important;
        }
        i {
          display: block;
          transform: rotate(90deg);
        }
        .dot2 {
          position: absolute;
          top: 0px;
          left: 4px;
        }
      }
      .qst-text {
        flex: 1;
        margin-left: 8px;
        font-size: 14px;
      }
    }
  }
}
.pyramid-container {
  position: absolute;
  z-index: 100;
  height: 100vh;
  width: 100%;
  left: 0;
  right: 0;
  transform: translate(-100%);
  transition: all 0.5s;
  &.show {
    transform: translate(0%);
  }
  iframe {
    height: 100vh;
    width: 100%;
  }
}
.editor-container {
  height: 100vh;
  width: 100%;
  iframe {
    height: 100vh;
    width: 100%;
  }
}