
import { CATEGORY } from "@/common/constants";
import bus from "@/pages/index/common/utils/bus";
interface FollowWordsForm {
  uniqueId: number,
  words: Array<{ value: string, error: string }>,
  explains: Array<{ value: string, error: string }>,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  answerTime: number
}
export const renderFormData = (data: { category: CATEGORY, curr: number, customH5: FollowWordsForm[] }) => {
  if (data.category !== CATEGORY.FOLLOWWORDSGROUP) return;
  bus.$emit('follow-word-group-render', data)
}




// edit-group/app.vue-comp