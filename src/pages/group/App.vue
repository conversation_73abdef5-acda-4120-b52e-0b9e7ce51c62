<template>
  <div id="app" v-loading="loading || !editReady">
    <div class="group-container" v-show="showSlideHandles && !loading && editReady">
      <div :class="['question-wrapper', { show: showSettings }]" :style="groupQuestionStyle">
        <div class="title">设置</div>
        <div class="tabs">
          <div class="tabs-handle">
            <div v-for="tab in tabsCofig" :key="tab.value" :class="['cursor', 'tabs-button', { active: activeTab === tab.value }]" @click="activeTab = tab.value">{{ tab.label }}</div>
          </div>
          <GroupStyle v-show="activeTab === 'style'" :theme="theme" :isPureGroup="isPureGroup" @selectTheme="selectTheme"></GroupStyle>
          <GroupQuestion
            v-show="activeTab === 'qst'"
            :isPureGroup="isPureGroup"
            :isAdding="isAdding"
            :orders="orders"
            :questions="questions"
            :currentQstIndex="currentQstIndex"
            @add="onAdd"
            @select="onSelect"
            @drag="onDrag"
            @copy="onCopy"
            @delete="onDelete"
          ></GroupQuestion>
          <div class="tabs-content qst-settings" v-show="activeTab === 'audio'">
            <GroupAudioEditor :data="detail.template.audioConfig" v-if="detail.template && detail.template.audioConfig" />
          </div>
        </div>
        <div class="qst-handles cursor" @click="onShowSettings">
          <em>
            <svg class="cocosicon" aria-hidden="true">
              <use xlink:href="#cocos-iconarrowRight-o2"></use>
            </svg>
          </em>
        </div>
      </div>
      <GroupHandle
        v-if="!isFollowWord"
        :stageSize="stageSize"
        :orders="orders"
        :questions="questions"
        :currentQstIndex="currentQstIndex"
        @select="onSelect"
        @copy="onCopy"
        @delete="onDelete"
        @showSettings="onShowSettings"
      />
    </div>
    <div :class="['pyramid-container', { show: showPyramid }]">
      <iframe :src="pyramidUrl" v-if="pyramidUrl" title="pyramid"></iframe>
    </div>
    <div class="editor-container" v-if="cocosURL">
      <iframe :src="cocosURL" ref="editorFrame" @load="iframeOnload" title="editor"></iframe>
    </div>
    <FollowWordGroupForm :category="detail.category" v-if="isFollowWord"></FollowWordGroupForm>
  </div>
</template>

<script lang="ts">
import APIS from "@/common/api/constants";
import { addQuestion, createVersion, getQuestionTemporaryStorage, getVersionInfo, updateQuestion } from "@/common/api/question";
import { CATEGORY } from "@/common/constants";
import { clearCacheData, getCacheData, setCacheData } from "@/common/utils/autoCache";
import { validateTizu } from "@/common/utils/dataValidate";
import { getShipName } from "@/common/utils/devHelper";
import { isDevelopmentEnv, isNotOnline } from "@/common/utils/env";
import { EDIT_SLIDES_CHANGE, GROUP_AUDIO_TIME, GROUP_EDIT_FORM, GROUP_ITEM_EDIT, GROUP_SAVE_ERROR, GROUP_SAVE_RES, GROUP_SLIDES, postMessage, SHOW_AUDIO_SETTING } from "@/common/utils/postMessage";
import { requestPhpServer } from "@/common/utils/request";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { getCategoryName, getSimpleQuestionStructure } from "@/common/utils/standardDataManager/getStandardData";
import { getQuestionListUrl } from "@/common/utils/url";
import FollowWordGroupForm from "@/components/form-question/follow-word-group-form";
import { renderFormData } from "@/pages/group/utils/GroupFollowGroup";
import bus from "@/pages/index/common/utils/bus";
import { Message } from "element-ui";
import { cloneDeep } from "lodash-es";
import { parse, stringify } from "query-string";
import clickOutside from "vue-click-outside";
import { Component, Vue, Watch } from "vue-property-decorator";
import "../index/ele";
import GroupAudioEditor from "./components/GroupAudioEditor/index.vue";
import GroupHandle from "./components/GroupHandle/index.vue";
import GroupQuestion from "./components/GroupQuestion/index.vue";
import GroupStyle from "./components/GroupStyle/index.vue";
const { plat, temporaryStorageId, snapId, id, tiku, hdkt2Shark, parentVersion } = parse(location.search);

enum Theme {
  XIAOXUE = "xiaoxue",
  CHUGAO = "chugao",
}

enum SaveStatus {
  CREATEVERSION = "createVersion",
  UPDATEQUESTION = "updateQuestion",
  CREATEQUESTION = "createQuestion",
}

enum MessageType {
  PYRAMIDBACK = "back",
  GROUPADD = "group-add",
  SAVE = "save-group-question",
  COCOSCHANGEURL = "cocosChangeUrl",
  EDITREADY = "edit-ready",
  EDITLOADED = "edit-loaded",
  UNSELECTEDCOMPONENTS = "unselectedComponents",
  EXPANDDRAW = "expand-draw",
  STAGESIZE = "stage-size",
  GLOBALMODALVISIBLE = "global-modal-visible",
  COCOSCLOSE = "cocos-close",
  COCOSEDITCLOSE = "cocos-edit-close", // 从cocos公共题库/互动库编辑态返回
}

enum BundleName {
  SharkSelectQuestion = "sharkSelectQuestion",
}

enum BooleanNumber {
  true = 1,
  false = 0,
}
type SubCategoryItem = {
  bundleName?: BundleName;
  category: CATEGORY;
  parentVersion: number;
};

interface Features {
  canInteract: BooleanNumber;
  demoPage: BooleanNumber;
  hasMys: BooleanNumber;
  hasVideo: BooleanNumber;
  isGroup: BooleanNumber;
  isOral: BooleanNumber;
  isQuestion: BooleanNumber;
  liveResources: BooleanNumber;
  newCocos: BooleanNumber;
  groupData: {
    questionLength: number;
    subCategory?: SubCategoryItem[];
  };
}

interface QuestionContent {
  animations: object;
  animationsForClient: object;
  components: Array<object>;
  extraDataMap: object;
  extraStageData: object;
  questionType: number;
  resourceList: string[];
  stageData: object;
  template: {
    bundleName: BundleName;
  };
  thumbnail: string;
  versionInfo: object;
}

export interface SubQuestionItem {
  standardData: any;
  id?: number;
  uniqueId?: number;
  category: CATEGORY;
  content: QuestionContent | string;
  features: Features;
  name: string;
  parentVersion: number;
  thumbnail: string;
  tempType: number;
}

interface AudioConfig {
  audioUrl: string;
  countdown: boolean;
  count: number;
  autoPlay: boolean;
  autoRepeatCount: number;
  formatTime: string;
  duration: number;
}

export interface Form {
  questionStem: string;
  answerStart?: string;
  readTime: number;
  audio: {
    url: string;
    name: string;
    duration: number;
  };
  playCount: number;
  readyTime: number;
  answerTime: number;
  answers: Array<{ value: string; error: string }>;
  keywords: Array<{ value: string; error: string }>;
}

interface FollowWordsForm {
  uniqueId: number;
  words: Array<{ value: string; error: string }>;
  explains: Array<{ value: string; error: string }>;
  audio: {
    url: string;
    name: string;
    duration: number;
  };
  answerTime: number;
}

interface Detail {
  category: CATEGORY;
  features: Features;
  gradeId: number;
  name: string;
  parentVersion: number;
  status: BooleanNumber;
  subjectId: number;
  tempId: number;
  tempType: number;
  template: {
    components: any[];
    audioConfig?: AudioConfig;
  };
  customH5: FollowWordsForm[];
}

interface EditWindow extends Window {
  getQuestionDetail: (autoCache: boolean) => Promise<SubQuestionItem>;
  updateFollowWordsQuesttionData: (params: { customH5: any[]; questions: SubQuestionItem[] }) => SubQuestionItem[];
  getCanSwitchQuestion: () => boolean;
}

@Component({
  components: {
    GroupAudioEditor,
    GroupHandle,
    GroupStyle,
    GroupQuestion,
    FollowWordGroupForm,
  },
  directives: {
    "click-outside": clickOutside,
  },
})
export default class App extends Vue {
  private qId = id; // 题目id
  loading = true;
  // userInfo = null;
  showSettings = true;
  showPyramid = false;
  activeTab = "qst"; // style qst audio
  currentQstIndex = -1;
  pyramidUrl = "";
  cocosEditUrl = "";
  defaultcocosUrl = "";
  cocosPath = "";
  questions: SubQuestionItem[] = [];
  orders: number[] = []; // number[] 初始化 增/删/拖拽/
  defaultQuestionTemp: SubQuestionItem = {
    category: CATEGORY.CHOICE,
    features: {
      newCocos: 1,
      isQuestion: 1,
      hasMys: 0,
      canInteract: 1,
      isOral: 0,
      isGroup: 0,
      demoPage: 0,
      hasVideo: 0,
      liveResources: 1,
      groupData: {
        questionLength: 0,
      },
    },
    name: "选择题",
    parentVersion: 1,
    thumbnail: "",
    tempType: 15,
    content: "",
    standardData: "",
  };
  // subCategory
  detail: Detail = {
    name: "题组",
    features: {
      newCocos: 1,
      isQuestion: 1,
      hasMys: 0,
      canInteract: 1,
      isOral: 0,
      isGroup: 1,
      demoPage: 0,
      hasVideo: 0,
      liveResources: 1,
      groupData: {
        questionLength: 2,
        subCategory: [
          {
            category: CATEGORY.CHOICE,
            parentVersion: 1,
          },
        ],
      },
    },
    gradeId: 0,
    subjectId: 0,
    tempId: 72,
    tempType: 15,
    status: 0,
    parentVersion: 1,
    category: CATEGORY.GROUP,
    template: {
      components: [],
      audioConfig: {
        audioUrl: "",
        countdown: true,
        count: 1,
        autoPlay: true,
        autoRepeatCount: 1,
        formatTime: "00:00",
        duration: 0,
      },
    },
    customH5: [],
  };
  tagsData = [];
  category = CATEGORY.GROUP; // todo
  questionType = 12;
  isAdd = false;
  readyTimer: number | null = null;
  isPreviewing = false;
  editReady = false;
  editLoaded = true;
  stageSize = { width: 9999, height: 1280, left: 0, top: 0 };
  currentOrder = -1;
  // theme: Theme = Theme.XIAOXUE
  theme: Theme = Theme.CHUGAO;
  showSlideHandles = true;
  isAdding = false;
  changeFlash = false;
  flashTimer!: NodeJS.Timeout;
  isBackAndContinue = false;
  get groupQuestionStyle() {
    return {
      left: `${this.stageSize.width + this.stageSize.left * 2}px`,
    };
  }
  get cocosURL() {
    return this.cocosEditUrl || this.defaultcocosUrl;
  }
  get currentQuestion() {
    return this.questions[this.currentQstIndex];
  }
  get audioConfig() {
    return this.detail.template.audioConfig;
  }
  @Watch("cocosURL")
  cocosURLChange(val: string, oldVal: string) {
    if (!oldVal) return;
    console.log("cocosURLChange", val, oldVal);
    this.editReady = false;
    this.editLoaded = false;
  }
  @Watch("orders.length")
  ordersLengthChange() {
    this.postOrdersMessageToEditor();
  }
  @Watch("currentQstIndex")
  currentQstIndexChange() {
    this.postOrdersMessageToEditor();
  }
  @Watch("cocosEditUrl")
  cocosEditUrlChange(val: string) {
    console.log("cocosEditUrlChange", val);
    if (!val) localStorage.setItem("groupCategory", String(this.detail.category));
  }
  getCocosPath() {
    if (isDevelopmentEnv) return location.origin;
    return `${location.origin}/interactive-question-editor`;
  }
  iframeOnload() {
    this.editReady = true;
    this.showSettings = false;
  }
  getCocosReady() {
    console.log("xu-getCocosReady", this.editReady);
    return new Promise(resolve => {
      if (this.editReady) {
        resolve(1);
      } else {
        const readyTimer = setInterval(() => {
          console.log("xu-getCocosReady-in", this.editReady);
          if (this.editReady) {
            clearInterval(readyTimer);
            resolve(1);
          }
        }, 300);
      }
    });
  }
  getEditFrameMounted() {
    return new Promise(resolve => {
      if (this.$refs.editorFrame) {
        resolve(1);
      } else {
        const readyTimer = setInterval(() => {
          if (this.$refs.editorFrame) {
            clearInterval(readyTimer);
            resolve(1);
          }
        }, 300);
      }
    });
  }
  getPyramidUrl() {
    let hostName = "kejian.zuoyebang.cc";
    if (isNotOnline) {
      hostName = `kejian-${getShipName()}-cc.suanshubang.cc`;
    }
    return `//${hostName}/static/cocos-tiku/#/questions-tabs?consumer=cocosTizu&cocosTab={"categories":1010}`;
    // return 'http://localhost:3000/cocos-tiku/#/questions-tabs?consumer=cocosTizu&cocosTab={"categories":1010}'
  }
  getSaveStatus(): SaveStatus {
    // createVersion-oc+编辑
    if (id && snapId && !tiku) {
      return SaveStatus.CREATEVERSION;
    } else if (this.qId) {
      return SaveStatus.UPDATEQUESTION;
    } else {
      return SaveStatus.CREATEQUESTION;
    }
  }
  getIframeWindow(): Window {
    const iFrame = (this.$refs.editorFrame as HTMLIFrameElement).contentWindow;
    return iFrame as Window;
  }
  handleParse(data: unknown) {
    try {
      return typeof data === "string" ? JSON.parse(data) : data;
    } catch (error) {
      return data;
    }
  }
  handleStringify(data: unknown) {
    try {
      return typeof data === "object" ? JSON.stringify(data) : data;
    } catch (error) {
      return data;
    }
  }
  get tabsCofig() {
    if (this.isVideoGroup) {
      return [
        {
          label: "页面管理",
          value: "qst",
        },
        {
          label: "题目样式",
          value: "style",
        },
        {
          label: "音频设置",
          value: "audio",
        },
      ];
    } else {
      return [
        {
          label: "页面管理",
          value: "qst",
        },
        {
          label: "题目样式",
          value: "style",
        },
      ];
    }
  }

  get isVideoGroup() {
    return this.detail.category === CATEGORY.VIDEOGROUP;
  }

  get isPureGroup() {
    return this.detail.category === CATEGORY.GROUP;
  }

  get isFollowWord() {
    return this.detail.category === CATEGORY.FOLLOWWORDSGROUP;
  }

  async postMessageToEditor(data: SubQuestionItem) {
    // console.log('this.questions', this.questions);
    const temp = {
      tagsData: this.tagsData,
      subjectId: this.detail.subjectId,
      gradeId: this.detail.gradeId,
      status: 0,
      tempId: 62,
      currentQstIndex: this.currentQstIndex,
      firstThumbnail: this.questions[0].thumbnail,
    };
    Object.keys(data).forEach((key: string) => {
      temp[key] = this.handleStringify(data[key]);
    });
    console.log("postMessageToEditor-editorFrame", temp, this.$refs.editorFrame);
    await this.getCocosReady();
    await this.getEditFrameMounted();
    postMessage(this.getIframeWindow(), GROUP_ITEM_EDIT, temp);
  }
  async postSaveMessageToEditor(status: 0 | 1) {
    await this.getCocosReady();
    await this.getEditFrameMounted();
    postMessage(this.getIframeWindow(), GROUP_SAVE_RES, { status });
  }

  async postSaveErrorMessageToEditor(bindTagVisible = false) {
    postMessage(this.getIframeWindow(), GROUP_SAVE_ERROR, { bindTagVisible });
  }
  async postOrdersMessageToEditor(indexArray: number[] = this.orders, currIndex: number = this.currentQstIndex, theme: Theme = this.theme) {
    console.log("xu-postOrdersMessageToEditor");
    if (currIndex === -1) return;
    await this.getCocosReady();
    await this.getEditFrameMounted();
    postMessage(this.getIframeWindow(), GROUP_SLIDES, { indexArray, currIndex, theme, groupCategory: this.detail.category });
  }
  async onAdd() {
    const getCanSwitch: any = (this.getIframeWindow() as EditWindow).getCanSwitchQuestion;
    if (getCanSwitch && !(await getCanSwitch())) {
      return;
    }

    await this.handleAutoSave(this.currentQstIndex);
    if (this.questions.length === 5) {
      showErrorMessage(new Error("最多创建5道题目"));
      return;
    }
    if (this.isAdding) {
      showErrorMessage(new Error("正在新增题目，不可复制"));
      return;
    }
    this.isAdding = true;
    this.showPyramid = true;
    this.showSettings = false;
  }
  selectTheme(type: Theme) {
    this.theme = type;
    this.translateAudioConfig();
    this.postOrdersMessageToEditor();
  }
  refreshAddStatus() {
    this.isAdd = false;
    if (this.cocosEditUrl) {
      this.cocosEditUrl = "";
      this.editReady = false;
      this.editLoaded = false;
    }
  }
  refreshOrders() {
    this.$set(
      this,
      "orders",
      Object.keys(this.questions).map((key: string) => Number(key)),
    );
  }
  // 自动缓存的时候，不需要thumbnail
  async handleAutoSave(index: number, autoCache = true) {
    if (this.getIframeWindow()) {
      // TODO
      if (!isDevelopmentEnv) {
        try {
          await (this.getIframeWindow() as EditWindow).getQuestionDetail(autoCache).then((data: SubQuestionItem) => {
            if ((data.category as number) !== 0) {
              this.questions[index] = {
                ...data,
                name: this.questions[index].name,
              };
            }
          });
        } catch (err) {
          console.log(err);
        }
      }
    }
  }
  async onCopy(index: number) {
    /**检查内容 */

    if (this.questions.length === 5) {
      showErrorMessage(new Error("最多创建5道题目"));
      return;
    }
    if (!this.editLoaded) {
      showErrorMessage(new Error("正在渲染当前题目，不可复制"));
      return;
    }
    const getCanSwitch: any = (this.getIframeWindow() as EditWindow).getCanSwitchQuestion;
    if (getCanSwitch && !(await getCanSwitch())) {
      return;
    }
    // 添加是异步的
    if (this.isAdding) {
      showErrorMessage(new Error("正在新增题目，不可复制"));
      return;
    } else {
      this.isAdding = true;
    }
    this.refreshAddStatus();
    if (this.currentQstIndex === index) {
      await this.handleAutoSave(index);
    }
    const data = this.questions[index];
    // 找到所有的副本 副本 2 副本 3
    let copyCurrent = "";
    let copyIndex: number[] = [];
    this.questions.forEach(({ name }: { name: string }) => {
      if (name.includes(`${data.name}_副本`)) {
        if (name === `${data.name}_副本`) {
          copyIndex.push(1);
        } else if (!isNaN(Number(name.slice(-1)))) {
          copyIndex.push(Number(name.slice(-1)));
        }
      }
    });
    copyIndex = copyIndex.sort((a, b) => a - b);
    copyIndex.forEach((item, index) => {
      if (item !== index + 1 && !copyCurrent) {
        copyCurrent = ` ${index + 1}`;
      }
    });
    if (copyIndex.length && !copyCurrent) {
      copyCurrent = ` ${copyIndex.length + 1}`;
    }
    if (copyCurrent === " 1") copyCurrent = "";
    this.questions.splice(this.questions.length, 0, {
      ...data,
      name: `${data.name}_副本${copyCurrent}`,
    });
    const addIndex = this.questions.length - 1;
    this.orders.splice(this.orders.length, 0, addIndex);
    await this.$nextTick();
    this.isAdding = false;
    this.handleSelect(addIndex);
    return Promise.resolve();
  }

  validateAudio() {
    // 音频验证
    if (this.isVideoGroup) {
      const { audioConfig } = this.detail.template;
      if (audioConfig && !audioConfig.audioUrl) {
        Message.error(`未设置音频，请进行设置`);
        return false;
      }
    }
    return true;
  }
  async handlePreview() {
    // content, id, name, thumbnail
    this.translateAudioConfig();
    if (this.isPreviewing) {
      return;
    }
    let valid = this.validateAudio();
    if (valid) {
      valid = await validateTizu(this.questions);
    }
    // console.log("validateTizu-2.22-res", valid);
    if (!valid) {
      return;
    }
    this.detail.features.groupData.subCategory = this.getSubCategory();
    const data = {
      content: JSON.stringify({
        customH5: this.detail.customH5,
        questions: this.orders.map(item => {
          return this.questions[item];
        }),
        resourceList: this.getResourceList(),
        questionType: this.questionType,
        template: {
          category: this.detail.category,
          questionType: this.questionType,
          theme: this.theme,
          stage: {
            width: 1280,
            height: 720,
            safeWidth: 1280,
            safeHeight: 960,
            backgroundColor: "#ffffff",
            texture: "",
            textureType: 0,
          },
          components: this.detail.template.components,
          audioConfig: this.detail.template.audioConfig,
        },
        features: this.detail.features,
      }),
      id: this.qId,
      name: this.detail.name,
      thumbnail: this.questions[0].thumbnail,
    };
    this.isPreviewing = true;
    this.detail.features.groupData.questionLength = this.questions.length;
    requestPhpServer
      .post(APIS.PHP_CREATE_PREVIEW, {
        content: data.content,
        features: JSON.stringify(this.detail.features),
        extData: JSON.stringify({ formConfig: {} }),
        parentVersion: this.detail.parentVersion,
      })
      .then(({ data }) => {
        if (data.errNo === 0) {
          const {
            data: { vid },
          } = data;
          const previewUrl = `${this.cocosPath}/preview.html?${stringify({
            previewId: vid,
          })}`;
          window.open(previewUrl);
          return;
        }
        throw data;
      })
      .catch(showErrorMessage)
      .finally(() => {
        this.isPreviewing = false;
      });
  }

  async onSelect(index: number) {
    if (this.changeFlash) {
      showErrorMessage(new Error("不可频繁切换题目"));
      return;
    }
    if (this.currentQstIndex === index) return;
    if (!this.editLoaded) {
      showErrorMessage(new Error("正在渲染当前题目，不可切换"));
      return;
    }
    /**检查内容 */
    const getCanSwitch: any = (this.getIframeWindow() as EditWindow).getCanSwitchQuestion;
    if (getCanSwitch && !(await getCanSwitch())) {
      return;
    }
    this.changeFlash = true;
    await this.handleAutoSave(this.currentQstIndex);
    await this.handleSelect(index);
  }

  async handleSelect(index: number, force?: boolean) {
    if (!this.editLoaded) {
      showErrorMessage(new Error("正在渲染当前题目，不可切换"));
      return;
    }
    if (this.currentQstIndex === index && !force) return;
    console.log("handleSelect--continue");
    this.refreshAddStatus();
    this.editLoaded = false;
    this.currentQstIndex = index;
    await this.getCocosReady();
    this.postMessageToEditor(this.questions[index]);
    if (this.flashTimer) {
      clearTimeout(this.flashTimer);
    }
    this.flashTimer = setTimeout(() => {
      clearTimeout(this.flashTimer);
      this.changeFlash = false;
    }, 1000);
  }
  async onDelete(index: number) {
    if (this.questions.length <= 1) {
      showErrorMessage(new Error("至少保留1道题目"));
      return;
    }
    if (this.isAdding) {
      showErrorMessage(new Error("正在新增题目，不可删除"));
      return;
    }
    // 删除当前的 需要重新渲染 需要拦截
    if (!this.editLoaded && this.currentQstIndex === index) {
      showErrorMessage(new Error("正在渲染当前题目，不可删除"));
      return;
    }
    console.log("onDelete", index, this.currentQstIndex);
    this.questions.splice(index, 1);
    const leftOrders = this.orders.filter(order => order !== index);
    this.orders = leftOrders.map(order => {
      return order < index ? order : order - 1;
    });
    // curr
    if (this.currentQstIndex === index) {
      this.handleSelect(this.questions[index] ? index : index - 1, true);
    } else if (this.currentQstIndex > index) {
      // 删除的是选中前面的
      this.currentQstIndex--;
    }
  }

  onDrag() {
    this.postOrdersMessageToEditor();
  }

  beforeMount() {
    window.oncontextmenu = function(e: MouseEvent) {
      //取消默认的浏览器自带右键 很重要！！
      e.preventDefault();
    };
    this.initData();
  }

  async fetchSnap(id: number) {
    if (!id) {
      this.$message.error({
        message: "页面参数错误",
        onClose: () => {
          window.history.back();
        },
      });
    }
    return getVersionInfo(id)
      .then(res => {
        return Promise.resolve(res?.data);
      })
      .catch(showErrorMessage);
  }
  // 根据id查找题板数据
  async fetchData(id: number) {
    if (!id && !temporaryStorageId) {
      this.$message.error({
        message: "页面参数错误",
        onClose: () => {
          if (!isDevelopmentEnv) {
            location.href = getQuestionListUrl();
          }
        },
      });
    }
    const request = (() => {
      if (id) {
        return requestPhpServer.post(APIS.PHP_GET_QUESTION_INFO, {
          qid: Number(id),
        });
      }
      if (temporaryStorageId) {
        return getQuestionTemporaryStorage(temporaryStorageId);
      }
    })();
    if (request) {
      return request
        .then(
          (res: {
            data: {
              data: {
                content: string;
                name: string;
                tempId: number;
                tempType: number;
                tempName: string;
                parentVersion: number;
              };
            };
          }) => {
            res.data.data.parentVersion = res.data.data.parentVersion ?? 1;
            return Promise.resolve(res?.data);
          },
        )
        .catch(showErrorMessage);
    }
  }
  async initData() {
    try {
      // 鉴权
      // if (!this.userInfo) {
      //   // 用户信息无缓存
      //   const {
      //     data: { data },
      //   } = await authLogin();

      //   // 课件会把旧权限系统 uid 挂在 url 上
      //   const uid = parse(window.location.search).uid;
      //   if (uid) {
      //     data.uid = Number(uid);
      //   }
      //   // uid 来自低幼权限，没有的话随便赋值一个
      //   if (!data.uid) {
      //     data.uid = 1;
      //   }
      //   this.userInfo = this.handleParse(JSON.stringify(data));
      // }

      const data = snapId ? await this.fetchSnap(snapId) : await this.fetchData(id);
      console.log("xu-tizu-data", data);
      const cacheData = await this.canRestore(data.data);
      clearCacheData();
      this.autoCache();
      console.log(cacheData, "autoSave");
      const renderData = cacheData ? cacheData : data.data;
      const { content, tagsData, features, gradeId, subjectId, name, tempId, tempType, status, parentVersion, category } = renderData;
      const { questions, template, customH5 } = this.handleParse(content);
      console.log(questions, "autoSave");
      console.log("init customH5", customH5);
      this.detail = {
        name,
        features: features || this.detail.features,
        gradeId,
        subjectId,
        tempId,
        tempType: tempType || this.detail.tempType,
        status,
        parentVersion,
        category,
        template,
        customH5: customH5
          ? customH5
          : [
              {
                uniqueId: Date.now(),
                words: [
                  {
                    value: "",
                    error: "",
                  },
                ],
                explains: [
                  {
                    value: "",
                    error: "",
                  },
                ],
                audio: {
                  url: "",
                  name: "",
                  duration: 0,
                },
                answerTime: 4,
              },
            ],
      };

      // 如果没有quesions 需要给一个默认的选择题
      if (!questions) {
        const uniqueId = this.detail.customH5[0].uniqueId;
        await this.getDefaultQuestionTemp();
        this.questions = [
          {
            ...this.defaultQuestionTemp,
            uniqueId,
          },
        ];
      } else {
        this.questions = questions.map((qst: SubQuestionItem) => {
          return {
            ...qst,
          };
        });
        this.getDefaultQuestionTemp();
      }

      this.tagsData = tagsData;
      this.refreshOrders();
      this.handleSelect(0);
      this.theme = template.theme || "xiaoxue";
      console.log("this.detail", this.detail);
      localStorage.setItem("groupCategory", category);
      await this.$nextTick();
      // render form
      renderFormData({
        category: this.detail.category,
        customH5: cloneDeep(this.detail.customH5),
        curr: this.currentQstIndex,
      });
    } catch (errData) {
      const err = errData as any;
      // 跳转登录
      // if (err.data?.data?.loginUrl) {
      //   window.location = err.data.data.loginUrl;
      //   return;
      // }
      showErrorMessage(err);
    }
  }

  async handleAddQuestion(data: any, cb: (data: any) => void) {
    const qdata = await addQuestion(data);
    clearCacheData();
    const qid = qdata.data.data.qid;
    cb(qid);
  }

  async handleCreateVersion(params: any) {
    try {
      const {
        data: { data: versionData },
      } = await createVersion(params);
      console.log("versionData", versionData);
      // 创建失败时 versionData为false
      if (!(versionData as any).snapId) {
        throw new Error("创建失败，请重试");
      } else {
        this.postSaveErrorMessageToEditor();
        clearCacheData();
        const { qid: cchdId, tId: tid, content, ...restData } = versionData as any;
        const isFromCocosTiku = plat === "cocosTiku";
        let type = hdkt2Shark ? "cocos2H5" : "cchd-add";
        if (snapId && !tiku && !hdkt2Shark && !isFromCocosTiku) type = "cchd-change";
        if (isFromCocosTiku) type = "cocos-create-success";
        // 需要判断是新增 还是编辑：snapId && !tiku
        // plat=ykt&id=1669&kjtype=2&uid=2032&parentVersion=&snapId=17583

        const tempContent = JSON.parse(content);
        tempContent.customH5 = undefined; // 不传customH5的数据给课件

        const conStr = JSON.stringify(tempContent);
        console.log(
          "xu-cv-data",
          JSON.stringify({
            ...restData,
            customData: conStr,
            cchdId,
            type,
          }),
        );

        // 克隆组件功能埋点
        if (conStr.indexOf("isCloneComp") !== -1) {
          (window as MyWindow).monitorManager.liveLog("I5C_007", {
            snapId: (versionData as any).snapId,
            isCloneComp: 1,
            category: this.detail.category,
          });
          console.info("自测题组：", {
            snapId: (versionData as any).snapId,
            isCloneComp: 1,
            category: this.detail.category,
          });
        }

        // 1103.todo isBackAndContinue
        window.parent &&
          window.parent.postMessage(
            JSON.stringify({
              ...restData,
              tid,
              isContinueCreate: this.isBackAndContinue,
              customData: conStr, // except customH5
              cchdId,
              type,
            }),
            "*",
          );
      }
    } catch (error) {
      this.postSaveErrorMessageToEditor(true);
      showErrorMessage(new Error("创建失败，请重试"));
      throw new Error("创建失败，请重试");
    }
  }

  translateAudioConfig() {
    if (this.isVideoGroup) {
      const { components, audioConfig } = this.detail.template;
      components.forEach((item: any) => {
        if (item.subType === "speaker") {
          item.properties = {
            ...item.properties,
            ...audioConfig,
          };
          if (this.theme === Theme.XIAOXUE) {
            item.properties.speakerType = 4;
            item.properties.countdownSkin = 0;
            item.properties.x = 0;
            item.properties.y = 294;
          }
          if (this.theme === Theme.CHUGAO) {
            item.properties.speakerType = 4;
            item.properties.countdownSkin = 1;
            item.properties.x = 372;
            item.properties.y = 202;
          }
        }
      });
    }
  }

  getSaveParams() {
    console.log("xu-components", this.questions);
    this.translateAudioConfig();
    this.detail.features.groupData.subCategory = this.getSubCategory();
    this.detail.features.groupData.questionLength = this.questions.length;
    const standardData = {
      id: snapId,
      tid: 0,
      questionType: this.questionType,
      source: "cocoshd",
      category: this.detail.category,
      desc: {
        categoryName: getCategoryName(this.category),
      },
      difficulty: this.tagsData && (this.tagsData as any).difficulty ? (this.tagsData as any).difficulty : [],
      pointList: this.tagsData && (this.tagsData as any).pointList ? (this.tagsData as any).pointList : [],
      subjectId: this.detail.subjectId,
      gradeId: this.detail.gradeId,
      questionModule: 2,
      questionStructure: {
        subTid: 0,
        questionType: this.questionType,
        category: this.detail.category,
        questionInitScene: this.questions[0].thumbnail, // 题目截图
        questionSignsScene: this.questions[0].thumbnail, // 带序号的 todo
        isEnumAnswer: false,
        isStandardAnswer: false,
        questionResource: [], // 本期为[]
        enumOptionList: [],
        enumStandardAnswer: [],
        standardAnswer: [],
        referenceAnswer: [], // 本期为[]
        questionAnswer: [], // 本期为[]
        expand: {}, // 本期为{}
        // 1.0.0 => 1.0.1 拉齐提交数据的版本 @wangcheng
        version: "1.0.1",
        children: this.questions.map((item, index) => {
          if (!item.standardData) {
            if (this.detail.category === 1107) {
              // 如果是跟读单词，不需要截图 直接生成结构话数据
              const questionStructure = getSimpleQuestionStructure({
                id: 0,
                name: item.name,
                thumbnail: item.thumbnail,
                con: JSON.parse(item.content as string),
                features: typeof item.features === "string" ? JSON.parse((item.features as unknown) as string) : item.features,
              });
              questionStructure.subTid = index + 1;
              return questionStructure;
            } else {
              Message.error(`第${index + 1}题数据结构不存在，请切到该小题后再点击创建`);
              this.postSaveErrorMessageToEditor();
              throw Error("数据结构不存在");
            }
          }
          const itemStandardData = this.handleParse((item as any).standardData);
          itemStandardData.questionStructure.subTid = index + 1;
          console.log("children..itemStandardData", itemStandardData.questionStructure);
          return itemStandardData.questionStructure;
        }),
      },
      // 1.0.0 => 1.0.1 拉齐提交数据的版本 @wangcheng
      version: "1.0.1",
      expand: {},
    };
    return {
      ...this.detail,
      content: JSON.stringify({
        customH5: this.detail.customH5,
        questions: this.orders.map(item => {
          return this.questions[item];
        }),
        questionType: this.questionType,
        resourceList: this.getResourceList(),
        template: {
          category: this.detail.category,
          questionType: this.questionType,
          theme: this.theme,
          stage: {
            width: 1280,
            height: 720,
            safeWidth: 1280,
            safeHeight: 960,
            backgroundColor: "#ffffff",
            texture: "",
            textureType: 0,
          },
          components: this.detail.template.components,
          audioConfig: this.detail.template.audioConfig,
        },
        features: this.detail.features,
      }),
      qid: this.qId,
      tags: JSON.stringify(this.tagsData) || "{}",
      thumbnail: this.questions[0].thumbnail,
      features: JSON.stringify(this.detail.features),
      standardData: JSON.stringify(standardData),
      extData: JSON.stringify({
        formConfig: {},
      }),
    };
  }
  async handleSave(status: 0 | 1) {
    const questionData = await this.cacheGroupState(true);
    questionData.status = status;
    questionData.tags = questionData.tags || "{}";

    const isFromPlatform = !["ykt", "cocosTiku"].includes(plat);
    const isFromYKTAndSave = ["ykt", "cocosTiku"].includes(plat) && status === 1;
    const isFromYKTAndCreate = ["ykt", "cocosTiku"].includes(plat) && status === 0;

    const versionParams = {
      qid: this.qId,
      gradeId: questionData.gradeId,
      subjectId: questionData.subjectId,
      parentVersion: questionData.parentVersion,
      tags: questionData.tags,
      content: questionData.content,
      thumbnail: questionData.thumbnail,
      features: questionData.features,
      standardData: questionData.standardData,
      source: plat === "ykt" ? 0 : 1,
    };

    console.log("versionParams", versionParams);

    try {
      // oc-pyramid-创建互动题-保存------addQuestion + 提示保存成功
      // oc-pyramid-创建互动题-创建------ addQuestion + createVersion
      // oc-pyramid-公共题库-编辑-创建------ addQuestion + createVersion
      // oc-h5-保存------addQuestion + 提示保存成功
      // oc-h5-创建------addQuestion + createVersion
      // platform-创建------addQuestion + 页面跳转
      // oc-编辑-创建------createVersion
      if (this.getSaveStatus() === SaveStatus.CREATEQUESTION) {
        if (isFromPlatform) {
          this.handleAddQuestion(questionData, (qid: number) => {
            Message.success({
              message: "创建成功",
              duration: 500,
              onClose: () => {
                location.href = `/interactive-question-editor/index.html?id=${qid}`;
              },
            });
          });
        } else if (isFromYKTAndSave) {
          this.handleAddQuestion(questionData, (qid: number) => {
            Message.success({
              message: "保存成功",
              duration: 500,
              onClose: () => {
                this.qId = qid;
                this.postSaveMessageToEditor(0);
              },
            });
          });
        } else if (isFromYKTAndCreate) {
          // ykt 创建
          this.handleAddQuestion(questionData, (qid: number) => {
            this.qId = qid;
            versionParams.qid = qid;
            this.handleCreateVersion(versionParams);
          });
        }
      } else if (this.getSaveStatus() === SaveStatus.UPDATEQUESTION) {
        // oc-pyramid-创建互动题-保存-保存------updateQuestion + 提示保存成功
        // oc-pyramid-创建互动题-保存-创建------updateQuestion + createVersion
        // oc-pyramid-编辑-保存------updateQuestion + 保存成功
        // oc-pyramid-编辑-创建------ updateQuestion + createVersion + 回到oc
        // oc-h5-保存-保存------updateQuestion + 提示保存成功
        // oc-h5-保存-创建------updateQuestion + createVersion
        // platform-编辑------updateQuestion + 提示保存成功
        await updateQuestion({ ...questionData, qid: this.qId })
          .then(res => {
            if (res.data.errNo === 0) {
              Message.success({
                message: "保存成功",
              });
              clearCacheData();
              if (isFromYKTAndCreate) {
                this.handleCreateVersion(versionParams);
              } else {
                this.postSaveMessageToEditor(0);
              }
            } else {
              throw res;
            }
          })
          .catch(showErrorMessage);
      } else if (this.getSaveStatus() === SaveStatus.CREATEVERSION) {
        // oc + 编辑
        this.handleCreateVersion(versionParams);
      }
    } catch (err) {
      showErrorMessage(err as Error);
      this.handleCreateVersion(1);
    }
  }

  getNameIndex() {
    let temp: number[] = [];
    // index + 1
    this.questions.forEach(({ name }: { name: string }) => {
      const reg = /^小题\s([0-9]{1})(\D*)$/;
      const getNameIndex = Number(name.replace(reg, "$1"));
      if (!temp.includes(getNameIndex) && !isNaN(getNameIndex)) temp.push(getNameIndex);
    });
    temp = temp.sort((a: number, b: number) => a - b);
    let number = temp.length + 1;
    const indexPad = temp.findIndex((item, index) => {
      return item !== index + 1;
    });
    if (indexPad !== -1) {
      number = indexPad + 1;
    }
    console.log("xu-getNameIndex", temp, indexPad, number);
    return number;
  }

  getResourceList() {
    const list: string[] = [];
    if (this.detail.template && this.detail.template.audioConfig && this.detail.template.audioConfig.audioUrl) {
      list.push(this.detail.template.audioConfig.audioUrl);
    }
    this.questions.forEach((qst: SubQuestionItem) => {
      const { content } = qst;
      const tempCon = this.handleParse(content);
      if (tempCon && tempCon.resourceList) {
        tempCon.resourceList.forEach((item: string) => {
          if (!list.includes(item)) {
            list.push(item);
          }
        });
      }
    });
    return list;
  }
  getSubCategory(): SubCategoryItem[] {
    const temp: Array<{ bundleName: BundleName; category: number; parentVersion: number }> = [];
    this.questions.forEach((qst: SubQuestionItem) => {
      const { category, parentVersion, content } = qst;
      const contentObject = this.handleParse(content) as QuestionContent;
      const {
        template: { bundleName },
      } = contentObject;
      if (!temp.filter(item => item.category === category).length) {
        temp.push({
          bundleName,
          category,
          parentVersion,
        });
      }
    });
    return temp;
  }
  async getDefaultQuestionTemp() {
    // Follow the words
    const tempMap = new Map([
      [CATEGORY.GROUP, 1],
      [CATEGORY.VIDEOGROUP, 1],
      [CATEGORY.FOLLOWWORDSGROUP, 22],
    ]);
    const { data } = await requestPhpServer.get(APIS.PHP_GET_QUESTION_TEMPLATE_LIST, {
      params: {
        pn: 1,
        rn: 10,
        parentVersion: 1,
        tempType: tempMap.get(this.detail.category), // 选择题 todo 根据category定义sub的tempType
      },
    });
    const { list } = data.data;
    if (list[0] && list[0].id) {
      const { data: res } = await requestPhpServer.get(APIS.PHP_GET_QUESTION_TEMPLATE_INFO, {
        params: {
          id: list[0].id,
        },
      });
      if (res.data) {
        console.log("xu-res.data", res.data);
        if (this.detail.category === CATEGORY.VIDEOGROUP) {
          // 听后选择 小题无题干音频
          const { content } = res.data;
          const jsonContent = this.handleParse(content);
          let {
            template: { extraConfig },
          } = jsonContent;
          if (extraConfig) {
            extraConfig = extraConfig.filter((item: { label: string }) => {
              return item.label !== "题干音频";
            });
          }
          jsonContent.template.extraConfig = extraConfig;
          console.log("jsonContent", jsonContent);
          res.data.content = this.handleStringify(jsonContent);
          this.defaultQuestionTemp = {
            ...res.data,
            content: this.handleStringify(jsonContent),
          };
        } else {
          this.defaultQuestionTemp = res.data;
        }
        this.defaultQuestionTemp.name = "小题 1";
      }
    }
  }
  handleMessage(message: object) {
    const messageCallbacks = {
      pyramidBack: (message: any) => {
        const { type } = message.data && typeof message.data === "string" && this.handleParse(message.data);

        if (type && type === MessageType.PYRAMIDBACK) {
          this.showPyramid = false;
          this.isAdding = false;
        }
      },
      editorBack: (message: any) => {
        const { type } = message.data && typeof message.data === "string" && this.handleParse(message.data);
        if (type && type === MessageType.COCOSCLOSE) {
          const isFromCocosTiku = plat === "cocosTiku";
          if (isFromCocosTiku) {
            console.log("tizu-cocosTiku-editorBack..");
            window.parent &&
              window.parent.postMessage(
                JSON.stringify({
                  type: "cocos-tizu-close",
                  url: "",
                }),
                "*",
              );
          } else {
            window.parent &&
              window.parent.postMessage(
                JSON.stringify({
                  type: "cocos-close",
                }),
                "*",
              );
          }
        }
      },
      pyramidEditorBack: (message: any) => {
        const { type } = message.data && typeof message.data === "string" && this.handleParse(message.data);
        if (type && type === MessageType.COCOSEDITCLOSE) {
          console.log(`xu-MessageType.COCOSEDITCLOSE`);
          this.cocosEditUrl = "";
          this.showSlideHandles = true;
          this.isAdding = false;
          this.editReady = false;
          this.handleSelect(this.currentQstIndex, true);
        }
      },
      pyramidAdd: async (message: any) => {
        // 新增-pyramid-创建互动题
        const { type, data } = message.data && typeof message.data === "string" && this.handleParse(message.data);
        if (type && type === MessageType.GROUPADD) {
          if (!data.snapId) {
            const { data: res } = await requestPhpServer.get(APIS.PHP_GET_QUESTION_TEMPLATE_INFO, {
              params: {
                id: data.id,
              },
            });
            data.content = res.data.content;
            data.content = res.data.content;
            data.parentVersion = this.detail.parentVersion;
            data.tempId = data.id;
          }
          this.questions.splice(this.questions.length, 0, {
            ...data,
            name: `小题 ${this.getNameIndex()}`,
          });
          const addIndex = this.questions.length - 1;
          this.orders.splice(this.orders.length, 0, addIndex);
          this.isAdding = false;
          this.handleSelect(addIndex);
          this.showPyramid = false;
        }
      },
      editQuestion: async (message: any) => {
        const { action, data, actionType } = message.data;
        let status: 0 | 1 = 0;
        // preview save create
        if (action === MessageType.SAVE) {
          this.isBackAndContinue = message.data.isContinueCreate;
          if (actionType === "save") {
            // 保存
            status = 1;
          } else if (actionType === "create") {
            // 创建 如果是插入进来的题，无gradeId  subjectId tags
            this.detail.gradeId = data.gradeId;
            this.detail.subjectId = data.subjectId;
            this.tagsData = this.handleParse(data.tags);
          }
          // 创建？编辑
          if (!this.isAdd) {
            // 编辑
            this.questions[this.currentQstIndex] = data;
          } else {
            this.isAdd = false;
            this.questions.splice(this.questions.length, 0, {
              ...data,
              name: `小题 ${this.getNameIndex()}`,
            });
            const addIndex = this.questions.length - 1;
            this.editReady = false;
            this.orders.splice(this.orders.length, 0, addIndex);
            this.currentQstIndex = addIndex;
            this.isAdding = false;
          }
          await this.$nextTick();
          if (actionType === "preview") {
            this.handlePreview();
          } else if (actionType === "insert") {
            this.cocosEditUrl = "";
            this.showSlideHandles = true;
            this.editReady = false;
            this.handleSelect(this.currentQstIndex, true);
          } else {
            this.handleSave(status);
          }
        }
      },
      cocosChangeUrl: (message: any) => {
        const { type, url } = this.handleParse(message.data);
        if (type === MessageType.COCOSCHANGEURL) {
          if (!url) {
            this.refreshAddStatus();
          } else {
            // 从pyramid公共题库和互动库来的编辑态，此时不显示题组的组件
            this.isAdd = true;
            this.cocosEditUrl = isDevelopmentEnv ? `${location.origin}/index.html?${url.split("?")[1]}` : url;
            this.editReady = false;
            this.editLoaded = false;
            this.showSlideHandles = false;
          }
          this.showPyramid = false;
        }
      },
      editReady: (message: any) => {
        const { action } = message.data;
        if (action === MessageType.EDITREADY) {
          console.log("xu-editReady");
          // this.editReady = true;
          // this.showSettings = false;
        }
      },
      expandDraw: (message: any) => {
        const { action } = message.data;
        if (action === MessageType.EXPANDDRAW) {
          this.showSettings = false;
        }
      },
      editLoaded: (message: any) => {
        const { action } = message.data;
        if (action === MessageType.EDITLOADED) {
          console.log("xu-loaded");
          this.editLoaded = true;
          this.showSettings = false;
          if (this.loading) this.loading = false;
        }
      },
      unselectedComponents: (message: any) => {
        const { action } = message.data;
        if (action === MessageType.UNSELECTEDCOMPONENTS) {
          console.log("xu-unselectedComponents");
          this.showSettings = false;
        }
      },
      stageSizeChange: (message: any) => {
        const { action, data } = message.data;
        if (action === MessageType.STAGESIZE) {
          console.log("xu-stageSizeChange", data);
          this.stageSize = data;
        }
      },
      modalVisibleChange: (message: any) => {
        // global-modal-visible
        const { action, data } = message.data;
        if (action === MessageType.GLOBALMODALVISIBLE) {
          console.log("xu-modalVisibleChange", data);
          this.showSlideHandles = !data.show;
        }
      },
      selectChange: (message: any) => {
        const { action, data } = message.data;
        if (action === EDIT_SLIDES_CHANGE) {
          console.log("xu-EDIT_SLIDES_CHANGE", data);
          this.questions[this.currentQstIndex] = {
            ...data.preDetail,
            name: this.questions[this.currentQstIndex].name,
          };
          this.handleSelect(data.currentQstIndex);
        }
      },
      showAudioSetting: (message: any) => {
        const { action } = message.data;
        if (action === SHOW_AUDIO_SETTING) {
          console.log("xu-SHOW_AUDIO_SETTING");
          if (this.activeTab === "audio" && this.showSettings) {
            this.showSettings = false;
          } else {
            this.activeTab = "audio";
            this.showSettings = true;
          }
        }
      },
      editForm: (message: any) => {
        const { action } = message.data;
        if (action === GROUP_EDIT_FORM) {
          console.log("xu-GROUP_EDIT_FORM");
          // render form
          renderFormData({
            category: this.detail.category,
            customH5: cloneDeep(this.detail.customH5),
            curr: this.currentQstIndex,
          });
        }
      },
    };
    Object.values(messageCallbacks).forEach((cb: (message: any) => void) => {
      cb(message);
    });
  }
  async mounted() {
    window.addEventListener("message", this.handleMessage, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("message", this.handleMessage, false);
    });
    (window as any).cacheGroupState = this.cacheGroupState;
    (window as any).validateAudio = this.validateAudio;
  }
  beforeDestroy() {
    clearInterval(this.cacheTimer);
  }
  created() {
    this.pyramidUrl = this.getPyramidUrl();
    this.cocosPath = this.getCocosPath();
    if (isDevelopmentEnv) {
      this.defaultcocosUrl = `${this.cocosPath}/index.html?fromGroup=1`;
    } else {
      this.defaultcocosUrl = `/interactive-question-editor/index.html?fromGroup=1`;
    }

    bus.$on("audioLoaded", ({ src, formatTime, duration }: { src: string; formatTime: string; duration: number }) => {
      if (this.audioConfig && this.audioConfig.audioUrl === src) {
        this.audioConfig.formatTime = formatTime;
        this.audioConfig.duration = duration;
        postMessage(this.getIframeWindow(), GROUP_AUDIO_TIME, { formatTime });
      }
    });
    bus.$on("audioDelete", () => {
      postMessage(this.getIframeWindow(), GROUP_AUDIO_TIME, { formatTime: "00:00" });
    });
    bus.$on("follow-word-group-insert", ({ category, flatData, customH5 }: { category: CATEGORY; flatData: any; customH5: FollowWordsForm[] }) => {
      console.log("insert", category);
      // saveCustomH5
      this.detail.customH5 = customH5;
      // this.questions
      const tempQuestions: SubQuestionItem[] = [];
      flatData.forEach((item: any) => {
        const tempItem = this.questions.find(qst => qst.uniqueId === item.uniqueId);
        if (tempItem) {
          tempQuestions.push(tempItem);
        } else {
          tempQuestions.push({
            ...this.defaultQuestionTemp,
            uniqueId: item.uniqueId,
          });
        }
      });
      // 数据转换 todo 方法调用
      const getQuestions = (this.getIframeWindow() as EditWindow).updateFollowWordsQuesttionData({ customH5: flatData, questions: tempQuestions });

      console.log("getQuestions", getQuestions);
      this.questions = getQuestions;
      // 渲染第一题
      this.refreshOrders();
      this.handleSelect(0, true);
      console.log("handleSelect");
    });
  }
  onShowSettings() {
    this.showSettings = !this.showSettings;
  }
  // 自动缓存数据到localstorage
  cacheTimer: any = null;

  autoCache() {
    this.cacheTimer = setInterval(() => {
      this.cacheGroupState();
    }, 60 * 1000);
  }
  async cacheGroupState(save = false) {
    // 自动缓存的时候需要获取一下当前题数据
    if (!save) {
      await this.handleAutoSave(this.currentQstIndex, false);
    }
    const questionData = this.getSaveParams();
    setCacheData(
      JSON.stringify({
        ...questionData,
        features: JSON.parse(questionData.features),
        standardData: JSON.parse(questionData.standardData),
        tagsData: JSON.parse(questionData.tags),
      }),
    );
    return questionData;
  }
  async canRestore(data: any) {
    return new Promise(resolve => {
      if (!data) {
        resolve();
        return;
      }
      const cacheData = getCacheData();
      if (cacheData && cacheData.category === data.category) {
        this.$confirm("是否恢复上次保存的试题？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            resolve(cacheData);
          })
          .catch(() => {
            resolve();
          });
      } else {
        resolve();
      }
    });
  }
}
</script>
<style lang="less">
@import "./index.less";
</style>
