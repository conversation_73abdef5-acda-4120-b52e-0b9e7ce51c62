<template>
  <div class="tabs-content qst-settings">
    <div
      v-if="isPureGroup"
      :class='["add", "cursor", { gray: questions.length >= 5 || isAdding }]' @click="handleAdd"
    >
      <em>
        <svg class="cocosicon" aria-hidden="true">
          <use xlink:href="#cocos-iconplus"></use>
        </svg>
      </em>
      <span>新增题目</span>
    </div>
    <div class="qst-list">
      <div
        :class="['qst-item cursor', {
          'drag-hover': dropIndex === index,
          'drag-curr': dropStartIndex === index,
          'active': item === currentQstIndex }]"
        v-for="(item, index) in orders"
        :key="index"
        @click.stop="handleSelect(item)"
        @drop.stop="draglineDrop($event, index)"
        @dragenter.stop="draglineEnter($event, index)"
        @dragleave.stop="draglineLeave($event, index)"
        @dragend.stop="draglineEnd($event, index)"
        @dragover.prevent="draglineOver($event, index)"
      >
        <div
        class="qst-item-content cursor"
        :draggable="draggable"
        @dragstart="onDragStart($event, index, item)">
          <div v-if="editIndex === item" class="edit-name-wrapper" v-click-outside="saveEditName" @click.stop>
            <el-input v-model="editName" @keyup.enter.native="saveEditName"/>
          </div>
          <template v-else>
            <span class="dot-icon">
              <i>
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-iconmoreHorizontal"></use>
                </svg>
              </i>
              <i class="dot2">
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-iconmoreHorizontal"></use>
                </svg>
              </i>
            </span>
            <span class="qst-text">{{questions[item].name}}</span>
          </template>
          <i @click.stop="handleEdit(item)" :class="['cursor']">
            <svg class="cocosicon" aria-hidden="true">
              <use xlink:href="#cocos-iconedit-o"></use>
            </svg>
          </i>
          <i :class="['cursor', { gray: questions.length >= 5 || isAdding }]" @click.stop="copyDebounce(item)">
            <svg class="cocosicon" aria-hidden="true">
              <use xlink:href="#cocos-iconcopy-o1"></use>
            </svg>
          </i>
          <i :class="['cursor', { gray: questions.length <= 1 }]" @click.stop="handleDelete(item)">
            <svg class="cocosicon" aria-hidden="true">
              <use xlink:href="#cocos-icondelete-o"></use>
            </svg>
          </i>
        </div>
        <div class="drag-line"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import { debounce } from "lodash-es";
import clickOutside from 'vue-click-outside';
import { SubQuestionItem } from '@/pages/group/App.vue'

@Component({
  directives: {
    'click-outside': clickOutside
  }
})
export default class GroupQuestion extends Vue {
  @Prop({
    required: true,
  })
  isPureGroup!: boolean;

  @Prop({
    required: true,
  })
  isAdding!: boolean;

  @Prop({
    required: true,
  })
  currentQstIndex!: number;

  @Prop({
    required: true,
  })
  orders!: number[];

  @Prop({
    required: true,
  })
  questions!: SubQuestionItem[];

  draggable = true
  dropIndex = -1
  dropStartIndex = -1
  editIndex = -1
  editName = ''

  debounce = debounce
  copyDebounce = debounce((index) => {
    this.handleCopy(index)
  }, 200)

  created() {
    console.log('isShow');
  }

  handleAdd() {
    this.$emit('add')
  }

  handleDelete(index: number) {
    this.$emit('delete', index)
  }

  handleCopy(index: number) {
    this.$emit('copy', index)
  }

  handleSelect(index: number) {
    this.$emit('select', index)
  }

  onDragStart (_e: any, index: number) {
    console.log('onDragStart',index);
    this.dropStartIndex = index;
  }

  draglineDrop (_e: DragEvent, index: number) {
    if (this.dropIndex === index) return;
    this.dropIndex = -1;
  }

  draglineEnter (e: DragEvent, index: number) {
    this.dropIndex = index;
    e.preventDefault();
  }

  draglineLeave (e: any, index: number) {
    if (e?.fromElement?.className !== 'qst-item-content') {
      this.dropStartIndex = -1;
      return;
    };
    if(this.dropIndex === index) this.dropIndex = -1;
    console.log('draglineLeave', e, e.fromElement.className, index);
  }

  async draglineEnd (e: DragEvent, index: number) {
    if (this.dropIndex === -1) return;
    // 排序  index => dropIndex dropIndex=>index
    [this.orders[index],this.orders[this.dropIndex]] = [this.orders[this.dropIndex],this.orders[index]];
    this.$emit('drag');
    await this.$nextTick();
    this.dropIndex = -1;
    this.dropStartIndex = -1;
    // todo

    // this.postOrdersMessageToEditor();
  }

  draglineOver (e: DragEvent, index: number) {
    console.log(index);
    e.preventDefault();
  }

  saveEditName() {
    this.questions[this.editIndex].name = this.editName;
    this.editIndex = -1;
  }

  handleEdit(index: number) {
    if (this.editIndex === index) return;
    const { name } = this.questions[index];
    this.editIndex = index;
    this.editName = name;
  }
}
</script>

<style lang="less" scoped>
.group-audio-config {
  margin-top: 18px;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }

  .el-input {
  margin-left: 5px;
  margin-right: 10px;
  flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    font-size: 13px;
  }
}

.required {
  &::before {
  content: "*";
  color: red;
  }
}
</style>
