<template>
  <div class="group-handle" :style="groupHandleStyle">
        <div>
          <div
            v-for="(item, index) in orders"
            :key="index"
            :class="['slide-item cursor', {active: currentQstIndex === item}]" @click="handleSelectClick(item)">
            <div>{{index+1}}</div>
          </div>
        </div>
        <div>
          <div :class="['handle-icon cursor', { gray: questions.length >= 5 }]">
            <em @click="copyDebounce(currentQstIndex)">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-iconcopy-o1"></use>
              </svg>
            </em>
          </div>
          <div :class="['handle-icon cursor', { gray: questions.length <= 1 }]">
            <em @click="handleDelete(currentQstIndex)">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-icondelete-o"></use>
              </svg>
            </em>
          </div>
          <div :class="['handle-icon cursor']">
            <em class="cursor" @click="handleShowSettings">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-iconmoreHorizontal"></use>
              </svg>
            </em>
          </div>
        </div>
      </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import { debounce } from "lodash-es";
interface GroupStageSize {
  width: number,
  height: number,
  left: number,
  top: number
}

@Component({
  components: { AudioSelect },
})
export default class GroupHandle extends Vue {
  @Prop({
    required: true,
  })
  stageSize!: GroupStageSize;

  @Prop({
    required: true,
  })
  currentQstIndex!: number;

  @Prop({
    required: true,
  })
  orders!: any[];

  @Prop({
    required: true,
  })
  questions!: any[];

  debounce = debounce
  copyDebounce = debounce((index) => {
    this.handleCopy(index)
  }, 200)

  get groupHandleStyle() {
    return {
      left: `${this.stageSize.width + this.stageSize.left + 8}px`,
      top: `${this.stageSize.top + this.stageSize.height/12 * 1.5 +  54 + 2}px`
    }
  }

  created() {
    console.log('isShow');
  }

  handleSelectClick(index: number) {
    this.$emit('select', index)
  }

  handleDelete(index: number) {
    this.$emit('delete', index)
  }

  handleCopy(index: number) {
    this.$emit('copy', index)
  }

  handleShowSettings() {
    this.$emit('showSettings')
  }
}
</script>

<style lang="less" scoped>
.group-audio-config {
  margin-top: 18px;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }

  .el-input {
  margin-left: 5px;
  margin-right: 10px;
  flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    font-size: 13px;
  }
}

.required {
  &::before {
  content: "*";
  color: red;
  }
}
</style>
