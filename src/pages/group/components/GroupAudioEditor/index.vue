<template>
  <div class="group-audio-config">
    <div class="form-item">
      <span class="label required">题干音频</span>
      <AudioSelect :value.sync="data.audioUrl" class="form-content" @change="handleAudioChange"/>
    </div>
    <div class="form-item">
      <span class="label">播放总次数</span>
      <el-select placeholder="请选择" class="form-content" size="small" v-model="data.count" @change="handleCountChange">
        <el-option
          label="无限次"
          :value="-1">
        </el-option>
        <el-option
          v-for="item in 5"
          :key="item"
          :label="item"
          :value="item">
        </el-option>
      </el-select>
    </div>
    <div class="form-item">
      <span class="label">自动播放</span>
      <!--  v-model="value" -->
      <el-switch class="form-content" v-model="data.autoPlay" @change="hancleAutoPlayChange"></el-switch>
    </div>
    <div class="form-item" v-if="data.autoPlay">
      <span class="label">自动播放次数</span>
      <el-select placeholder="请选择" class="form-content" size="small" v-model="data.autoRepeatCount">
        <el-option
          v-for="item in data.count === -1 ? 5 : data.count"
          :key="item"
          :label="item"
          :value="item">
        </el-option>
      </el-select>
    </div>
    <div class="form-item" v-if="data.autoPlay">
      <span class="label">倒计时</span>
      <el-radio-group class="form-content" v-model="data.countdown">
        <el-radio :label="true">显示</el-radio>
        <el-radio :label="false">不显示</el-radio>
      </el-radio-group>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";
import AudioSelect from "@/components/AudioSelect/index.vue";
import bus from "@/pages/index/common/utils/bus";
interface AudioConfig {
  audioUrl: string,
  countdown: boolean,
  count: number,
  autoPlay: boolean,
  autoRepeatCount: number,
  formatTime: string,
  duration: number
}

@Component({
  components: { AudioSelect },
})
export default class GroupEditor extends Vue {
  @Prop({
    required: true,
  })
  data!: AudioConfig;
  public isRender = true;
  public extraConfig = [{
    key:'guide',
    label:'题干音频',
    type:'audioSelect',
    params:{}
  },
  {
    key:'showCountdown',
    label:'倒计时',
    required:true,
    type:'radio',
    params:{
      options:[
      {
        label:'显示',
        value:true
      },
      {
        label:'不显示',
        value:false
      }
      ]
    }
  },
  {
    key:'showCountdown',
    label:'播放总次数',
    required:true,
    type:'radio',
    params:{
      options:[
      {
        label:'无限次',
        value:-1
      },
      {
        label:'1',
        value:1
      },
      {
        label:'2',
        value:2
      },
      {
        label:'3',
        value:3
      },
      {
        label:'4',
        value:4
      },
      {
        label:'5',
        value:5
      }
      ]
    }
  },
  {
    key:'autoPlay',
    label:'自动播放',
    type:'radio',
    params:{
      options:[
      {
        label:'显示',
        value:true
      },
      {
        label:'不显示',
        value:false
      }]
    }
  },
  {
    key:'autoPlay',
    label:'自动重复播放次数',
    type:'radio',
    params:{
      options:[
        {
        label:'无限次',
          value:-1
        },
        {
          label:'1',
          value:1
        },
        {
          label:'2',
          value:2
        },
        {
          label:'3',
          value:3
        },
        {
          label:'4',
          value:4
        },
        {
          label:'5',
          value:5
        }
      ]
    }
  }]
  created() {
    console.log('isShow');
  }
  hancleAutoPlayChange(val: any) {
    console.log('hancleAutoPlayChange', val);
    this.data.autoRepeatCount = 1;
    this.data.countdown = false;
  }
  handleCountChange(val: any) {
    console.log('handleCountChange', val);
    this.data.autoRepeatCount = 1;
  }
  handleAudioChange(val: any) {
    console.log('handleAudioChange', val, this.data.audioUrl);
    this.data.audioUrl = val;
    bus.$emit('audioDelete');
  }
}
</script>

<style lang="less" scoped>
.group-audio-config {
  margin-top: 18px;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }

  .el-input {
  margin-left: 5px;
  margin-right: 10px;
  flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    font-size: 13px;
  }
}

.required {
  &::before {
  content: "*";
  color: red;
  }
}
</style>
