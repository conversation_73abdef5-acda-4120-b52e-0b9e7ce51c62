<template>
  <div class="tabs-content style-settings">
    <div :class="['style-item',{ active: theme === 'xiaoxue' }]" @click="selectTheme(Theme.XIAOXUE)">
      <div :class="['thumbnail']">
        <i class="icon-success"></i>
        <img src="~@/assets/img/group_style_xx.png" v-if="isPureGroup">
        <img src="~@/assets/img/audiogroup_style_xx.png" v-else>
      </div>
      <span>小学</span>
    </div>
    <div :class="['style-item',{ active: theme === 'chugao' }]" @click="selectTheme(Theme.CHUGAO)">
      <div class="thumbnail">
        <i class="icon-success"></i>
        <img src="~@/assets/img/group_style_cg.png" v-if="isPureGroup">
        <img src="~@/assets/img/audiogroup_style_cg.png" v-else>
      </div>
      <span>初高</span>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from "vue-property-decorator";

enum Theme {
  XIAOXUE = 'xiaoxue',
  CHUGAO = 'chugao'
}

@Component({})
export default class GroupStyle extends Vue {
  @Prop({
    required: true,
  })
  theme!: Theme;

  @Prop({
    required: true,
  })
  isPureGroup!: Theme;

  Theme = Theme

  selectTheme(theme: Theme) {
    this.$emit('selectTheme', theme)
  }

  created() {
    console.log('isShow');
  }
}
</script>

<style lang="less" scoped>
.group-audio-config {
  margin-top: 18px;
}
.form-item {
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  .form-content {
    margin-left: 5px;
    margin-right: 10px;
    padding-bottom: 0px !important;
  }

  .el-input {
  margin-left: 5px;
  margin-right: 10px;
  flex-grow: 1;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    font-size: 13px;
  }
}

.required {
  &::before {
  content: "*";
  color: red;
  }
}
</style>
