/*
 * @Date: 2021-10-09 10:20:02
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-07 16:01:54
 * @FilePath: /interactive-question-editor/src/pages/index/index.ts
 * @Author: chxu
 */
import Vue from "vue";
import App from "./App.vue";
import "@/assets/cocosicon/iconfont.js";
import "./normalize.css";
import "./reset.css";
import "zyb-pc-ui/lib/theme-chalk/index.css";
import pageConfig from "@/common/utils/pageConfig/pageConfig.online";

Vue.prototype.$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};
(window as MyWindow).$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};

Vue.config.productionTip = false;
import { Loading, Input } from "element-ui";
import { Monitor } from "@/common/utils/monitorUtil";

Vue.use(Loading);
Vue.use(Input);

// remove index.html loading
const loadingEl = document.getElementById("loading");
if (loadingEl) {
  document.body.removeChild(loadingEl);
}

new Vue({
  render: h => h(App),
}).$mount("#app");

(window as MyWindow).monitorManager = new Monitor();
