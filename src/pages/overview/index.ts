import Vue from "vue";
import svgicon from "vue-svgicon";
import "./../index/ele";
import App from "./App.vue";
import store from "./../index/store";
import "./../index/normalize.css";
import "./../index/reset.css";
import { exportVue } from "../index/common/utils/tools";
import { getMethodByKey } from "../../common/utils/pageConfig";

Vue.config.productionTip = false;
Vue.use(svgicon);
Vue.prototype.$getPageConfigByKey = getMethodByKey;

const aa = new Vue({
  store,
  render: h => h(App),
}).$mount("#app");

const loadingEl = document.getElementById("loading");
if (loadingEl) {
  document.body.removeChild(loadingEl);
}
// 注册表单原子组件
const modules = exportVue(require.context("@/pages/index/components/EditArea/SchemaToForm/atomComponents", true, /index\.vue$/));
Object.keys(modules).forEach(key => {
  Vue.component(key, modules[key]);
});

(window as any).aa = aa;
