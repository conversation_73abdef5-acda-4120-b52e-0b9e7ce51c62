import Vue from "vue";
import svgicon from "vue-svgicon";
import "../index/ele";
import App from "./App.vue";
import store from "../index/store";
import "./../index/normalize.css";
import "./../index/reset.css";
import { exportVue } from "../index/common/utils/tools";
import pageConfig from "@/common/utils/pageConfig/pageConfig.native";
import watermark from "@/common/nativeUtils/wartermark";

(window as MyWindow).monitorManager = {
  setStartByType: (type: string) => {
    console.log('setStartByType', type);
  },
  setEndByType: (type: string) => {
    console.log('setStartByType', type);
  },
  liveLog: (type: string) => {
    console.log('liveLog', type);
  },
  reportLog: (type: string) => {
    console.log('reportLog', type);
  },
  reportLongTTILog: (type: string) => {
    console.log('reportLongTTILog', type);
  }
};
Vue.prototype.$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};

(window as MyWindow).$getPageConfigByKey = (key: string) => {
  // console.log('pageConfig', pageConfig);
  return (pageConfig as any)[key]
};

const timer = setTimeout(() => {
  watermark.set((window as any).user || `作业帮`)
  clearTimeout(timer);
}, 1000);

Vue.config.productionTip = false;
Vue.use(svgicon);

const aa = new Vue({
  store,
  render: h => h(App),
}).$mount("#app");

const loadingEl = document.getElementById("loading");
if (loadingEl) {
  document.body.removeChild(loadingEl);
}
// 注册表单原子组件
const modules = exportVue(require.context("@/pages/index/components/EditArea/SchemaToForm/atomComponents", true, /index\.vue$/));
Object.keys(modules).forEach(key => {
  Vue.component(key, modules[key]);
});

(window as any).aa = aa;
