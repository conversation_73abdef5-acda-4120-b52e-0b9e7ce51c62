enum Theme {
  XIAOXUE = 'xiaoxue',
  CHUGAO = 'chugao'
}

enum SaveStatus {
  CREATEVERSION = 'createVersion',
  UPDATEQUESTION = 'updateQuestion',
  CREATEQUESTION = 'createQuestion'
}

enum MessageType {
  PYRAMIDBACK = 'back',
  GROUPADD = 'group-add',
  SAVE = 'save-group-question',
  COCOSCHANGEURL = 'cocosChangeUrl',
  EDITREADY = 'edit-ready',
  EDITLOADED = 'edit-loaded',
  EXPANDDRAW = 'expand-draw',
  STAGESIZE = 'stage-size',
  GLOBALMODALVISIBLE = 'global-modal-visible',
  COCOSCLOSE = 'cocos-close',
  COCOSEDITCLOSE = 'cocos-edit-close' // 从cocos公共题库/互动库编辑态返回
}

enum BundleName {
  SharkSelectQuestion = 'sharkSelectQuestion'
}

enum Category {
  sharkSelectQuestion = 1010,
  Group = 1027,
  VideoGroup = 1102,
  FollowWordsGroup = 1107,
}

enum BooleanNumber {
  true = 1,
  false = 0
}
type SubCategoryItem = {
  bundleName?: BundleName,
  category: Category,
  parentVersion: number
}

interface Features {
  canInteract: BooleanNumber,
  demoPage: BooleanNumber,
  hasMys: BooleanNumber,
  hasVideo: BooleanNumber,
  isGroup: BooleanNumber,
  isOral: BooleanNumber,
  isQuestion: BooleanNumber,
  liveResources: BooleanNumber,
  newCocos: BooleanNumber,
  groupData: {
    questionLength: number,
    subCategory?: SubCategoryItem[]
  }
}

interface QuestionContent {
  animations: object,
  animationsForClient: object,
  components: Array<object>,
  extraDataMap: object,
  extraStageData: object,
  questionType: number,
  resourceList: string[],
  stageData: object,
  template: {
    bundleName: BundleName
  },
  thumbnail: string,
  versionInfo: object
}

export interface SubQuestionItem {
  id?: number,
  uniqueId?: number,
  category: Category,
  content: QuestionContent | string,
  features: Features,
  name: string,
  parentVersion: number,
  thumbnail: string,
  tempType: number
}

interface AudioConfig {
  audioUrl: string,
  countdown: boolean,
  count: number,
  autoPlay: boolean,
  autoRepeatCount: number,
  formatTime: string,
  duration: number
}

export interface Form {
  questionStem: string,
  answerStart?: string,
  readTime: number,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  playCount: number,
  readyTime: number,
  answerTime: number,
  answers: Array<{ value: string, error: string }>,
  keywords: Array<{ value: string, error: string }>
}

interface FollowWordsForm {
  uniqueId: number,
  words: Array<{ value: string, error: string }>,
  explains: Array<{ value: string, error: string }>,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  answerTime: number
}

interface Detail {
  category: Category,
  features: Features,
  gradeId: number,
  name: string,
  parentVersion: number,
  status: BooleanNumber,
  subjectId: number,
  tempId: number,
  tempType: number,
  template: {
    components: any[],
    audioConfig?: AudioConfig
  },
  customH5: FollowWordsForm[]
}

// width: 9999, height: 1280,left: 0, top: 0
interface GroupStageSize {
  width: number,
  height: number,
  left: number,
  top: number
}