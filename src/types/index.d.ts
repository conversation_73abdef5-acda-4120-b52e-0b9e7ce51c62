interface Tag {
  name: string;
  label: string;
  isGlobal?: boolean;
  editorConfig: any[];
}

interface ResponseWrapper<T = any> {
  errStr: string;
  errNo: number;
  data: T;
}

interface AnimationConfigItem {
  label: string;
  value: string;
}

interface StageSize {
  width: number;
  height: number;
  safeWidth: number;
  safeHeight: number;
}

type AnimationConfig = AnimationConfigItem[];

interface Template {
  id: number;
  bundleUrl: string;
  category: number;
  categoryName?: string;
  bundleName: string;
  tempType: number;
  stage: StageSize & {
    backgroundColor: string;
    texture: string;
    textureType: number;
  };
  questionType?: number;
  tags?: Tag[];
  animationConfig?: AnimationConfig;
  extraConfig?: any[];
  features: any;
  supportSetReferenceAnswer?: boolean;
  referenceAnswer?: {
    [k in string]: any[];
  };
  tempVersion?: string // 模版的版本 用于题板较大变动的情形
}

interface ExtData {
  formConfig: {
    [P in OptionComponentSubTypes]?: FormSchema;
  }; // 表单配置
  formTemplateData?: FormTemplateData; // 小英pk编辑态数据
  compEditorData?: {
    [k in string]: any; // k为组件id
  }; // 组件编辑态数据-暂未使用
}

enum OptionComponentSubTypes {
  KLOTSKIQUESTION = "klotskiQuestion",
  CUTPICTURE = "cutPicture",
  RACINGCAR = "racingCar",
}

enum ClipboardTypes {
  NONE = 0,
  CUT = 1,
  COPY = 2,
}

enum Mode {
  NORMAL = 0,
  ANIMATION = 1,
}
interface SignalData {
  id: string;
  newExtra: { signalId: number; isCorrect?: boolean };
}

interface State {
  userInfo: any;
  client: number | undefined;
  mode: Mode;
  name: string;
  templateId: number;
  initialDataLoaded: boolean;
  cocosInitFinished: boolean;
  cocosLoadQuestionBundleFinished: boolean;
  cocosLoadGameSceneFinished: boolean;
  cocosLoadStageRootNodeFinished: boolean;
  cocosLoadCompListStart: boolean;
  cocosLoadCompListFinished: boolean;
  template: Template;
  extData: ExtData;
  componentMap: Record<string, Component>;
  componentIds: (string | { id: string; subIds: string[] })[];
  currentComponentIds: string[];
  animations: Record<string, any>;
  contextMenu: {
    top: number;
    left: number;
    visible: boolean;
    componentId?: string;
  };
  clipboard: {
    type: ClipboardTypes;
    components: Component[];
  };
  stageData: {
    width: number;
    height: number;
    safeWidth: number;
    safeHeight: number;
    backgroundColor: string;
  };
  extraStageData: {
    // 切割题切割线颜色
    cutLineColor?: string;
    [key: string]: any;
  };
  isEditingComponentAnimations: boolean;
  editingAnimationsComponentId: string;
  componentAnimationConfig: any[];
  componentAnimationVal: string;
  componentFragmentId: string;
  componentActiveActionId: string;
  tagsData?: string;
  gradeId?: number;
  subjectId?: number;
  parentVersion?: number;
  status?: number;
  editFocus: boolean;
  componentManagerCard?: { visible: boolean; activeName: string };
  moduleAnimations?: {
    animationVal: string;
    fragmentId: string;
    activeActionId: string;
    animations: {
      afterReadingQuestion: { audio: { url: string; delay: number }; fragments: {}; points: {} };
      afterSubmitCorrect: { audio: { url: string; delay: number }; fragments: {}; points: {} };
      afterSubmitWrong: { audio: { url: string; delay: number }; fragments: {}; points: {} };
    };
    isPlayingAnimation: boolean;
    isPlayingFragment: boolean;
  };
  timeTravel?: { currentIndex: number; total: number };
  rulerTick?: { verticalLines: never[]; horizontalLines: never[] };
  containerWidth: number;
  containerHeight: number;
}

type MyWindow = typeof window & {
  boot: (data?: string | { resourceMap?: { [x as string]: string } }) => void;
  _$store: any;
  _vue: VueConstructor<Vue>;
  cocos: {
    getActionData: (number, any) => any[];
    getSpineProperties: (component: Component) => SpineProperties;
    getCocosAniProperties: (component: Component) => CocosAniProperties;
    getLableString: (componentId: Component["id"]) => string;
    getAnimaPropMap: () => any;
    screenshot: () => cc.RenderTexture;
    hideComponentShot: (hideIds: number[]) => cc.RenderTexture;
    getSmallCaptureById: (compId: string, props: any) => cc.RenderTexture;
    hideComponentShotWithSignal: (signalData: SignalData[], hideIds: number[]) => cc.RenderTexture;
    screenSignalShot: (signalData: SignalData[]) => Promise<cc.RenderTexture>;
    getCaptureByNode: (id: string) => Promise<cc.RenderTexture>;
    getNodeCentEdtiorPos: (id: string) => { x: number, y: number };
    showSocpedComponent: boolean;
  };
  // 快速预览相关
  __quick_compile_project__: any;
  _cocos_bridge: Record<string, any>;
  editFocus: boolean;
  vueFocus: boolean;
  lableEnditing: boolean = false;
  getPreivewContent: () => Promise<string>;
  getQuestionDetail: (autoCache: boolean) => Promise<{ content: string; thumbnail: string; name: any; tempId: any; parentVersion: any; status: number; tempType: any; category: any; features: string; standardData: StandardData; }>;
  getCanSwitchQuestion: () => Promise<boolean>;
  cocosAssetsDirName: string; // qte-render中web-mobile文件夹名称
  monitorManager: Monitor;
  $getPageConfigByKey: Monitor;
  loadJsPromise: Promise<void>;
  pageTools: DevHelper;
  materialLibraryReady: boolean; // 素材库是否准备就绪

};

type Component =
  | LabelComponent
  | SpineComponent
  | CocosAniComponent
  | GroupComponent
  | InnerGroupComponent
  | SpriteComponent
  | SpecialVoiceComponent
  | SpecialAudioHistoryAnswerComponent
  | CutShapeComponent
  | RealiaTestComponent
  | SpecialGraphicsComponent
  | SpecialKeyboardComponent
  | SpecialCounterComponent
  | SpecialKeyboardEnglishComponent;
type Components = Component[];

// 基本组件
interface BaseComponent<T = BaseProperties> {
  id: string;
  type: "sprite" | "label" | "spine" | "group" | "cutShape" | "cocosAni" | "specialComponent" | "realiaComponent" | "formula" | "svgShape" | "brush" | "shape";
  editable?: boolean | T<T, boolean>;
  // Partial<Record<keyof T, boolean>>;
  hideBeforeAnimation?: boolean;
  deletable?: boolean;
  tag?: string;
  properties: T;
  extra?: any;
  dragable?: boolean;
  childComponents?: Component[];
  canCombine?: boolean;
  isNoChangeTag?: boolean;
}

// label组件
interface LabelComponent extends BaseComponent<LabelProperties> {
  type: "label";
}

interface SpineData {
  images: string[];
  // imageNames: string[];
  skeleton: string;
  atlas: string;
  cover: string;
}

interface CocosAniData {
  url: string;
  zip: string;
  cover: string;
}

interface SpineItem {
  id: number;
  name: string;
  tags: string[];
  skeleton: string;
  images: string[];
  atlas: string;
  cover: string;
  url: string;
}

interface CocosAniItem {
  id: number;
  name: string;
  tags: string[];
  url: string;
  cover: string;
}

// spine组件
interface SpineComponent extends BaseComponent<SpineProperties> {
  type: "spine";
  spineData: SpineData;
}

interface CocosAniComponent extends BaseComponent<CocosAniProperties> {
  type: "cocosAni";
  cocosAniData: CocosAniData;
}

interface SpriteComponent extends BaseComponent<SpriteProperties> {
  type: "sprite";
}

interface ShapeComponent extends BaseComponent<ShapeProperties> {
  type: "svgShape";
}
interface H5ShapeComponent extends BaseComponent<H5ShapeProperties> {
  type: "shape";
}

interface FormulaComponent extends BaseComponent<FormulaProperties> {
  type: "formula";
}

interface RichTextSpriteComponent extends BaseComponent<RichTextSpriteProperties> {
  type: "richTextSprite";
}

// group组件
interface GroupComponent extends BaseComponent<GroupProperties> {
  type: "group";
  subComponents: Component[];
}
// 特殊组件
enum SpecialComponentSubTypes {
  VOICE = "voice", // 音频话筒组件
  AUDIO_HISTORY_ANSWER = "audioHistoryAnswer", // 音频历史作答组件
  GRAPHICS = "graphics",
  KEYBOARD = "keyboard",
  MATCHBOARD = "matchboard",
  COUNTER = "counter",
  CLOCK = "clock",
  KEYBOARD_ENGLISH = "keyboardEnglish",
  SPEAKER = "speaker",
  BRUSH = "brush", //画笔
  READCOM = "readcom",
  VOICESPEAK = "voiceSpeak", // 看图音频组件
  LISTENRETELL = "listenRetell",
  MICROP = "microp",
  LISTENANSWER = "listenAnswer",
  LOUDREADING = "readPage", // 朗读短文
  FOLLOWWORDS = "followWords", // 跟读单词
  H5LABEl = "h5Label", // 富文本
  ENPK = "enPK", // 小英pk
  ENGROUPPK = "enGroupPK", //小英全组pk
}
interface SpecialComponent<T = BaseProperties> extends BaseComponent<T> {
  type: "specialComponent";
  subType: SpecialComponentSubTypes;
}
interface SpecialVoiceComponentProperties extends BaseProperties {
  // 答题时长
  answerDuration: number;
  // 评测关键词
  evaluatingText: string;

  // 正确率
  accuracy: number;
  //
  // overScore: number[];
  //超时
  overTimeNum: number;
  // 评测类型
  wordType: number;

  // 自动开始
  autoBegin: boolean;
}

interface SpeakerComponentProperties extends BaseProperties {
  /** 音频组件皮肤枚举 */
  speakerType: number;
  /** 音频 */
  audioUrl: string;
  /** 播放次数, -1代表不限次 */
  count: number;
  /** 是否自动播放 */
  autoPlay: boolean;
  /** 不允许播放中断 */
  notStopPlaying: false;
  /** 显示倒计时开始 */
  countdown: false;
  /** 自动开始重复次数 */
  autoRepeatCount: number;
  /** 倒计时皮肤 */
  countdownSkin: number;

  /** 音频时长 */
  duration: number;
}
interface BrushComponentProperties extends BaseProperties {
  style: number;
  mountList: string[];
}
interface H5LabelComponentProperties extends BaseProperties {
  customH5label: string;
  labelPicList: string[];
}
interface SpecialGraphicsComponentProperties extends BaseProperties {
  displayType: number;
  boardType: number;
  lineColor: string;
  background: string;
}
interface SpecialKeyBoardComponentProperties extends BaseProperties {
  keyboardType: number;
}
interface SpecialKeyBoardEnglishComponentProperties extends BaseProperties {
  isPackUp: boolean;
  keyboardType: number;
}

interface SpecialCounterComponentProperties extends BaseProperties {
  countNum: number;
}
interface SpecialClockComponentProperties extends BaseProperties {
  linkage: boolean;
  hour: number;
  minute: number;
  correctHour: number;
  correctMinute: number;
}

interface SpecialMatchBoardComponentProperties extends BaseProperties {
  matchType: number; // 0 算术 1 图形
  mathType: number; // 1 二等式 2 三等式
  grid: {
    row: number;
    col: number;
  };
  pattern: number[][];
  rightAnswer: number[][][];
  actionType: number;
  actionNumbers: number;
}
interface SpecialVoiceComponent extends SpecialComponent<SpecialVoiceComponentProperties> {
  subType: SpecialComponentSubTypes.VOICE;
}

interface SpeakerComponent extends SpecialComponent<SpeakerComponentProperties> {
  subType: SpecialComponentSubTypes.SPEAKER;
}
interface BrushComponent extends SpecialComponent<BrushComponentProperties> {
  subType: SpecialComponentSubTypes.BRUSH;
}

interface H5LabelComponent extends SpecialComponent<H5LabelComponentProperties> {
  subType: SpecialComponentSubTypes.H5LABEl;
}

interface SpecialAudioHistoryAnswerComponent extends SpecialComponent<> {
  subType: SpecialComponentSubTypes.AUDIO_HISTORY_ANSWER;
}
interface SpecialGraphicsComponent extends SpecialComponent<SpecialGraphicsComponentProperties> {
  subType: SpecialComponentSubTypes.GRAPHICS;
}

interface SpecialKeyboardComponent extends SpecialComponent<SpecialKeyboardComponentProperties> {
  subType: SpecialComponentSubTypes.KEYBOARD;
}

interface SpecialKeyboardEnglishComponent extends SpecialComponent<SpecialKeyBoardEnglishComponentProperties> {
  subType: SpecialComponentSubTypes.KEYBOARD_ENGLISH;
}
interface SpecialMatchBoardComponent extends SpecialComponent<SpecialMatchBoardComponentProperties> {
  subType: SpecialComponentSubTypes.MATCHBOARD;
}
interface SpecialCounterComponent extends SpecialComponent<SpecialCounterComponentProperties> {
  subType: SpecialComponentSubTypes.COUNTER;
}

interface SpecialClockComponent extends SpecialComponent<SpecialClockComponentProperties> {
  subType: SpecialComponentSubTypes.CLOCK;
}

// 编辑器内部使用的group
interface InnerGroupComponent extends BaseComponent<GroupProperties> {
  type: "group";
}

enum CutShapeSubTypes {
  TRIAGLE_1 = 0, // 等腰三角形
  TRIAGLE_2 = 1, // 等边三角形
  TRIAGLE_3 = 2, // 直角三角形
  TRIAGLE_3_1 = 3, // 镜像直角三角形2
  TRAPEZOID_1 = 4, // 梯形
  TRAPEZOID_2 = 5, // 直角梯形
  TRAPEZOID_2_1 = 6, // 镜像直角梯形1
  TRAPEZOID_3 = 7, // 等腰梯形
  RHOMBOID = 8, // 平行四边形1
  RHOMBOID_1 = 9, // 镜像平行四边形2
  SQUARE = 10, // 正方形
  PENTAGON = 11, // 五边形
  LIUBIANXING = 12, // 六边形
}

/** 翻转类型 */
enum FlipType {
  FNORMAL = 0, // 水平（不翻转）｜垂直（不翻转）
  FLIPX = 1, // 水平（翻转）｜垂直（不翻转）
  FLIPY = 2, // 水平（不翻转）｜垂直（翻转）
  FLIPALL = 3, // 水平（翻转）｜垂直（翻转）
}
interface CutShapeComponent extends BaseComponent<CutShapeProperties> {
  type: "cutShape";
  subType: CutShapeSubTypes;
}

// 组件properties定义
interface BaseProperties {
  active: boolean;
  width: number;
  height: number;
  x: number;
  y: number;
  color?: string;
  opacity?: number;
  zIndex?: number;
  angle?: number;
  scaleX?: number;
  scaleY?: number;
  flipType?: FlipType;
}

interface LabelProperties extends BaseProperties {
  string: string;
  str: string;
  cusorIndex?: number;
  fontSize?: number;
  lineHeight?: number;
  selectArr?: string[];
  isLabelRight?: boolean;
  overflow?: cc.Label.Overflow;
  enableUnderline?: boolean;
  enableItalic?: boolean;
  enableBold?: boolean;
  horizontalAlign?: number;
  texture?: string;
  isFixed?: boolean;
  rowSpacing?: number;
  textureArray?: string[];
}

interface SpriteProperties extends BaseProperties {
  texture: string;
}
interface ShapeProperties extends BaseProperties {
  fillColor: string;
  lineWidth: number;
  strokeColor: string;
  svgText: string;
  origin: boolean;
}

interface H5ShapeProperties extends BaseProperties {
  shapeData: {
    fill?: string // 填充色
    strokeWidth?: number // 边框宽度
    strokeDasharray?: string // 边框样式
    stroke?: string // 边框颜色
    shadowPosition?: string // 阴影位置
    shadowColor?: string // 阴影颜色
    opacity?: string // 透明度
    width?: number // 宽度
    height?: number // 高度
    fontFamily?: string // 字体
    fontSize?: number // 字号
    bold?: boolean // 加粗
    italic?: boolean // 斜体
    underline?: boolean // 下划线
    color?: string // 颜色
    deleteline?: boolean // 删除线
    subsup?: 'sub' | 'super' | '' // 上下标
    emphasis?: 'dot' | '' // 加点字
    textAlign?: 'left' | 'center' | 'right' | 'justify' // 左右对齐
    tbAlign?: 'flex-start' | 'center' | 'flex-end' // 上下
    lineHeight?: number // 行高
    letterSpacing?: number // 字间距
    fontShadowPosition?: string // 字体阴影位置
    fontShadowColor?: string // 字体阴影颜色
  },
  texture: string
}

interface FormulaProperties extends BaseProperties {
  url: string;
  latex: string;
}

interface RichTextSpriteProperties extends BaseProperties {
  texture: string;
  textureRichTextKey: string;
}
interface BrushProperties extends BaseProperties {
  style: number;
}
interface ReadcomProperties extends BaseProperties {
  questions: any = { };
  customH5: string;
}

type CocosAniProperties = Omit<BaseProperties, "width" | "height"> & {
  timeScale: number;
  animationList: string[];
  loop: boolean;
  clips: string[];
};

type SpineProperties = Omit<BaseProperties, "width" | "height"> & {
  timeScale: number;
  animationList: string[];
  loop: boolean;
};
type GroupProperties = BaseProperties;

type CutShapePropertiesPoint = {
  id: string;
  label: string;
  x: number;
  y: number;
  editable: boolean;
};

type CutShapePropertiesLine = {
  id: string;
  label: string;
  points: [CutShapePropertiesPoint["id"], CutShapePropertiesPoint["id"]];
};

interface CutShapeProperties extends BaseProperties {
  // 填充颜色
  fillColor: string;
  // 线段颜色
  strokeColor: string;
  // 线段宽度
  lineWidth: number;
  // 是否隐藏点线
  isHidePointLine: boolean;

  // 端点数据
  pointsData: CutShapePropertiesPoint[];

  // 线段数据
  linesData: CutShapePropertiesLine[];
}

// 通用分页请求参数
interface PageQueryParam {
  pageNum: number;
  pageSize: number;
  pn: number;
  rn: number;
}

// 通用分页请求返回数据格式
interface PageQueryRes<T> extends PageQueryParam {
  total: number;
  pn: number;
  resourceList: any;
  list: Array<T>;
}

// 图库图片项
interface ImageItem {
  url: string;
}

interface AudioItem {
  url: string;
  id: number;
  name: string;
  createName?: string;
  updateTime?: number;
}

/**
 * @description 互动题项
 */
interface QuestionItem {
  id: number;
  name: string;
  category: number;
  thumbnail: string;
  tempId: number;
  tempType: number;
  content: string;
  gradeId: number;
  subjectId: number;
  tags: string;
}

/**
 * @description 互动题模板项
 */
interface QuestionTemplateItem {
  id: number;
  tempName: string;
  tempType: number;
  content: string;
  thumbnail: string;
  client: number;
  name: string;
  category: number;
  createName?: string;
  createTime?: number;
  updateName?: string;
  updateTime?: number;
  // 0: 未删除，1: 已删除
  deleted?: number;
  template?: any;
}

interface QuestionTemplateTypeItem {
  id: number;
  name: string;
}

interface Response<T> {
  errNo: number;
  errStr: string;
  data: T;
}

interface FormTemplateData {
  /** 题目数据相关的表单数据 */
  question?: any;
  /** 组件相关的表单数据 */
  components?: {
    [number]: any;
  };
  /** 题目表单配置化数据 */
  // questionFormItemConfigs: any,
  /** 组件表单配置化数据 */
  // componentItemFormItemConfigs: any,
}

// 形状的事件
interface ShapeEvent {
  type: ShapeEventType;
  data: any;
}

// 形状事件枚举
enum ShapeEventType {
  OpenShapeSelect = 'openShapeSelect', // 单击形状icon触发
  StartEditShape = 'startEditShape', // 选择形状 点击编辑形状 双击形状组件
  EndEditShape = 'EndEditShape', // 点击形状编辑的确定 带数据 点击形状编辑的取消 不带数据
} 
