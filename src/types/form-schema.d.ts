import { ElInput } from "element-ui/types/input";
import { ElInputNumber } from "element-ui/types/input-number";
import { ElSwitch } from "element-ui/types/switch";
import { ElSlider } from "element-ui/types/slider";
import { ElSelect } from "element-ui/types/select";
import { ElRadioGroup } from "element-ui/types/radio-group";
import { ElTag } from "element-ui/types/tag";

declare global {
  /**
   * 组件jsonSchema自动生成表单抽象
   */
  type FormVal = string | number | boolean | Record<string, any> | Array<any>;

  type CollapseMark = "collapse";

  enum FileImageType {
    PNG = "png",
    JPEG = "jpeg",
  }

  // 原子表单组件功能抽象
  interface AbstractSchema {
    formItemType: string; // 表单组件类型,对应component is组件渲染
    key: string; // 组件动态属性key
    value?: FormVal; // 表单默认值
  }

  type FormRule = Array<Record<string, string | number | boolean | Function>>;

  // 原子表单组件配置基础描述
  interface BaseRuleSchema {
    label?: string; // 表单名称
    labelTips?: string; // 鼠标划过表单label时的提示, 表单相关功能描述
    labelPosition?: "left" | "top"; // 表单名称位置,默认left
    paddingTop?: number; // 顶部内边距
    paddingLeft?: number; // 左侧内边距
    paddingRight?: number; // 右侧内边距
    paddingBottom?: number; // 低部内边距
    span?: number; // 布局, 参考elementUI Layout布局,默认24
    offset?: number; // 布局, 参考elementUI Layout布局
    rule?: FormRule; // 参考elementUI表单校验
    validation?: string; // 字符串形式正则,待运行时通过new RegExp()转换为正则表达式
    unit?: string; // 单位
    customClassName?: string; // 自定义类名
  }

  interface FormOption {
    label: string | number; // 显示的文案
    value: string | number | boolean; //选中的值
    span?: number; // el-col布局属性
    offset?: number; // el-col布局属性
    // 联动其他组件表单属性和表单值
    changeProps?: ChangeProps;
    // 类tabs操作,控制强关联组件显隐
    associatedForm?: Array<FormItemConfigs>;
  }

  // BaseInput
  type BaseInputType = ElInput;
  // BaseInputNumber
  type BaseInputNumberType = ElInputNumber;
  // BaseSlider
  type BaseSliderType = ElSlider;

  //BaseSwitch
  interface BaseSwitchType extends ElSwitch {
    options?: FormOption[];
  }
  interface BaseTagType extends ElTag {
    active: {
      color: string
    }
  }
  interface BaseTagListType extends BaseTagType {
    row?: number;
    col?: number;
    rowKey?: string;
    colKey?: string;
  }

  // BaseploadFile
  interface BaseUploadFileType {
    fileType: {
      size?: number;
      width?: number;
      height?: number;
      widthRange?: [number, number];
      heightRange?: [number, number];
      filer?: Array<FileImageType>;
    };
  }

  // BaseSelect
  interface BaseSelectType extends ElSelect {
    options: FormOption[];
    /**
     * 下拉列表的构造规则，如果有构造规则优先使用构造规则，无构造规则使用options
     */
    optionsConfig?: {
      relativePropertiesKey: string;
      labelKey?: string;
      labelPrefix?: string; // 前缀 + (index + 1)
      valueKey: string;
    }
  }
  // BaseRadioGroup
  interface BaseRadioGroupType extends ElRadioGroup {
    options?: FormOption[];
  }
  // 占位符
  interface BaseStaticTextType {
    text?: string;
    align?: "center" | "left" | "right";
    bold?: boolean;
    color?: string;
    fontSize?: string;
    whiteSpace?: string;
  }

  // 表单项配置联合类型
  type FormItem = BaseInputType | BaseInputNumberType | BaseSwitchType | BaseSliderType | BaseploadFileType | BaseSelectType | BaseRadioGroupType | BaseStaticTextType | BaseAudioSelectType | SpecialWordsInput;

  interface FormItemConfigs extends BaseRuleSchema, AbstractSchema, FormItem {
    // BaseImageSelect
    clearable?: string;
    // BaseCheckbox
    checkboxLabel?: string;
    // Select | BaseCheckboxGroup | BaseRadio | Baseswitch
    options?: FormOption[];
    index?: number
  }

  type ChangeProps = Array<{
    // 目标组件属性,一个属性对应一个表单
    targetKey: string;
    // 要修改对应表单json的数,包括value,value为该表单对应值
    props: Record<string, FormVal>;
  }>;

  interface CollapseForm {
    formItemType: CollapseMark; // 折叠面板标识
    collapseName: string; // 折叠面板名称
    formList: FormItemConfigs[]; // 折叠面板下表单列表
  }

  interface CollapseFormRuntime extends CollapseForm {
    encodeId: string; // 转码id,通过collapseName转码而来
  }

  type Operation = "add" | "del";

  interface OrderConfig {
    show: boolean;
    labelPosition?: 'left' | 'top';
    type?: "number" | "letter" | "function";
    decorate?: string; // 字符串中包含 {{$}} 运行时会将该字段替换为对应项数字/字母下标
    args?: {
      [x: string]: any
    }, // type为function时必填
  }

  interface SpecialDynamicListAbstract extends BaseRuleSchema, AbstractSchema {
    formItemType: string;
    subFormConfigs: FormItemConfigs[];
    orderConfig?: OrderConfig;
    operations?: Operation[];
    operationsPosition?: "right" | "bottom" | "top";
    min?: number;
    max?: number;
    listConfig?: {
      label: string;
      labelTips: string;
    };
    focusConfig?: {
      show: boolean;
      key: string;
    };
    answerConfig?: {
      show: boolean;
      key: string;
      label: string;
      placeholder?: string;
      mutiple?: boolean;
      formItemType?: string;
    };
  }

  interface SpecialOptionConfigAbstract extends BaseRuleSchema, AbstractSchema {
    typeConfig?: {
      show: boolean; // 选项类型表单是否显示
      type: "img" | "text"; // show为false时生效且必须设置,未设置则取"text",否则以实际properties数据为准
    };
    answerConfig?: {
      show: boolean; // 答案是否显示
      multiple?: boolean; // 是否是多个答案
    };
    textConfig?: {
      maxLength?: number; // 文本框输入文本长度上限
      validation?: string; // 字符串形式正则,待运行时通过new RegExp()转换为正则表达式
      validationMessage?: string; // 验证不通过时提示文本,不设置则提示 "输入内容校验不通过"
    };
    orderConfig?: OrderConfig;
    optionTextConfig?: {
      label?: string,
      listTitle?: string,
      labelTips?: string
    }
    min?: number; // 选择个数最小值
    max?: number; // 选项个数最大值
  }

  interface SpecialWordsInput extends BaseInputType {
    // 音标 英文
    showPhonetic?: boolean; // 是否显示音标
    phoneticConfig?: {
      showRelativeKey: string; // 显示音标关联字段
      showRelativeValue: string; // 显示音标关联字段值
      // 音标关联的字段
      relativePropertiesKey: string; // 音标关联的字段
      optionsRelativeKey: string; // 音标下拉选项关联的字段
      labelKey?: string; // 显示的文案
      labelPrefix?: string; // 前缀 + (index + 1)
      valueKey: string; // 选中的值

    };
  }

  type FormSchema = Array<FormItemConfigs | CollapseForm>;
}
