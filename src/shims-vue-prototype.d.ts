import { IStorage } from '@/common/utils/storage'
import { ImageLibraryOptions } from '@/pages/index/components/MaterialLibrary/ImageLibrary'
import { ImageCropperOptions } from 'src/components/ImageCropper'

declare module "vue/types/vue" {
  interface Vue {
    $storage: IStorage;
    $bus: Vue;
    $ImageCropper: (params: ImageCropperOptions) => void;
    $ImageLibrary: (params: ImageLibraryOptions) => void;
    $getPageConfigByKey: (params: string) => any;
  }
}
