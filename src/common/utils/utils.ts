// 获取范围内随机数
export function getRadomArr(count: number) {
  const originalArr = []; // 原数组
  const randomArr = [];
  // 给原数组originalArray赋值
  for (let i = 0; i < count; i++) {
    originalArr[i] = i + 1;
  }
  originalArr.sort(() => 0.5 - Math.random());
  for (let i = 0; i < count; i++) {
    randomArr.push(originalArr[i]);
  }
  return randomArr;
}

export function generateRandomStr() {
  let text = "";
  const possible = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

  for (let i = 0; i < 10; i++) {
    text += possible.charAt(Math.floor(Math.random() * possible.length));
  }
  return text;
}

export function slimExtData(components: any, extData: any) {
  console.log("slimExtData", extData, components);
  const { formConfig, ...res } = extData;
  const validTexts: any = {};
  components.forEach((item: { type: any; properties: { textureRichTextKey: any; }; }) => {
    // if (item.type === 'richTextSprite') 
    const { type, properties: { textureRichTextKey } } = item;
    if (type === 'richTextSprite' && res[textureRichTextKey]) {
      validTexts[textureRichTextKey] = res[textureRichTextKey];
    }
    return false;
  })
  console.log('validKeys', validTexts, res);
  return {
    formConfig,
    ...validTexts,
    ...res
  }
}

export function setMaterialLibraryDialogZIndex() {
  try {
    const modals = document.querySelectorAll('[aria-modal="true"]');
    let maxZIndex = 0;
    for (let i = 0; i < modals.length; i++) {
      // const z = parseInt(window.getComputedStyle(modals[i]).zIndex);
      const parent = modals[i].parentElement;
      if (!parent) {
        continue;
      }
      const inlineStyle = parent.getAttribute('style');
      if (inlineStyle) {
        const zIndexMatch = inlineStyle.match(/z-index\s*:\s*([^;]+)/i);
        if (zIndexMatch) {
          const zIndex = parseInt(zIndexMatch[1]);
          maxZIndex = Math.max(maxZIndex, isNaN(zIndex) ? 0 : zIndex)
        }
      }
    }
    const materialLibraryDialog = document.querySelector(".material-library-dialog") as HTMLElement;
    console.log("maxZIndex", maxZIndex);
    if (materialLibraryDialog) {
      materialLibraryDialog.style.zIndex = String(Math.max(Number(materialLibraryDialog.style.zIndex), maxZIndex + 1));
    }
  } catch (error) {
    console.log("zIndex error", error);
  }
}
