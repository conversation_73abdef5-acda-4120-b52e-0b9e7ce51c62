import { CATEGORY } from "@/common/constants";

import configsMap, { FORMDATATYPE } from './config';

import { cloneDeep } from "lodash-es";
import store from "@/pages/index/store";
import enPkFormDialog from "@/components/form-question/en-pk-form";
import { listenQuestionToCocos } from "@/pages/index/common/utils/listenQuestionToCocos";

const {
  questionModelMap,
  questionFormItemConfigsMap,
  componentItemModelMap,
  componentItemFormItemConfigsMap
} = configsMap;

export const getComponentModelData = (formDataType: FORMDATATYPE) => {
  return cloneDeep(componentItemModelMap.get(formDataType));
}

export const getComponentFormItemConfigs = (formDataType: FORMDATATYPE) => {
  return cloneDeep(componentItemFormItemConfigsMap.get(formDataType));
}

export const getQuestionFormItemConfigs = (category: CATEGORY) => {
  return cloneDeep(questionFormItemConfigsMap.get(category));
}

export const genComponentsData = (components: Component[]) => {
  console.error("components::", components);
  return components.filter((comp) => {
    return componentItemFormItemConfigsMap.has(comp.formDataType)
  }).map((comp) => {
    return getComponentModelData(comp.formDataType);
  })
}
export const genQuestionData = (category: CATEGORY) => {
  return questionModelMap.has(category) ? cloneDeep(questionModelMap.get(category)) : undefined;
}

export const genFormTemplateData = (category: CATEGORY, data: any): FormTemplateData | undefined => {
  const { template: { hasFormData }, components } = data;
  // componentId subQuestions componentProperties
  if (!hasFormData) {
    return undefined;
  }
  return {
    question: hasFormData ? genQuestionData(category) : undefined,
    components: genComponentsData(components),
  }
}

/**
 * 小英pk/小英pk题组 显示表单
 * 使用场景： 题目初始化时 & 点击编辑按钮时
 */
export const autoShowFormDialog = () => {
  const { category } = store.state.template;
  if (category === CATEGORY.ENPK || category === CATEGORY.ENGROUPPK) {
    enPkFormDialog(category).then((res) => {
      console.log('enPkFormDialog-res', res);
      listenQuestionToCocos(res);
    }).catch((err) => {
      console.log('enPkFormDialog-cancel', err)
    });
  }
}
