
import { enGroupPKQuestionConfigs } from './enGroupPKQuestion';
import {  enPKQuestionConfigs } from './enPKQuestion';
import { enPKComponentItemConfigs } from './enPKComponentItem';
import { enGroupPKComponentItemConfigs } from './enGroupPKComponentItem';

import { CATEGORY } from "@/common/constants";

export enum FORMDATATYPE {
  ENPK = 'enPK',
  ENGROUPPK ="enGroupPK"
}
type MapConfigItem = {
  category: CATEGORY,
  formDataType: FORMDATATYPE,
  questionConfigs: {
    model: {
      [key: string]: any
    },
    configs: Array<any>
  },
  componentItemConfigs: {
    model: {
      [key: string]: any
    },
    configs: Array<any>
  },
}
const mapConfig = [{
  category: CATEGORY.ENPK,
  formDataType: FORMDATATYPE.ENPK,
  questionConfigs: enPKQuestionConfigs,
  componentItemConfigs: enPKComponentItemConfigs,
},{
  category: CATEGORY.ENGROUPPK,
  formDataType: FORMDATATYPE.ENGROUPPK,
  questionConfigs: enGroupPKQuestionConfigs,
  componentItemConfigs: enGroupPKComponentItemConfigs, 
}];
function getConfigsMap(mapConfig: MapConfigItem[]) {
  const questionModelMap = new Map();
  const questionFormItemConfigsMap = new Map();
  const componentItemModelMap = new Map();
  const componentItemFormItemConfigsMap = new Map()

  mapConfig.forEach((item) => {
    const { category, formDataType, questionConfigs, componentItemConfigs } = item;
    questionModelMap.set(category, questionConfigs.model);
    questionFormItemConfigsMap.set(category, questionConfigs.configs);
    componentItemModelMap.set(formDataType, componentItemConfigs.model);
    componentItemFormItemConfigsMap.set(formDataType, componentItemConfigs.configs);
  })

  return {
    questionModelMap,
    questionFormItemConfigsMap,
    componentItemModelMap,
    componentItemFormItemConfigsMap
  }
}

const configsMap = getConfigsMap(mapConfig);

export default configsMap;