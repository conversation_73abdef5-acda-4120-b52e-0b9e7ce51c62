const configs = [
  {
    key: 'description',
    type: 'text',
    label: '题目说明',
    cocosKey: 'name',
    props: {
      required: true,
      max: 100,
      placeholder: '请输入题目说明',
    }
  },
  {
    key: 'type',
    label: '类型',
    type: 'select',
    cocosKey: 'type',
    props: {
      options: [
        { label: '读单词', value: 1 },
        { label: '读句子', value: 2 },
        { label: '读段落', value: 3 },
        // { label: '读音标', value: 6 }
      ],
      optionType: 'l-v',
      required: true,
    }
  },
  {
    key: 'image',
    label: '题干图片',
    type: 'img',
    cocosKey: 'picUrl',
    props: {
      required: false,
    }
  },
  {
    key: 'qeustionEN',
    type: 'text-array',
    label: '题干',
    cocosKey: 'text0',
    props: {
      min: 1,
      max: 1,
      required: true,
      tips: '',
      hide: false,
    },
    itemProps: {
      type: 'text',
      required: true,
      max: 100,
      placeholder: '题干最多不超过100个字符',
    }
  },
  {
    key: 'qeustionCH',
    type: 'text',
    label: '翻译',
    cocosKey: 'text1',
    props: {
      required: true,
      max: 100,
      placeholder: '请输入翻译内容',
    }
  },
  {
    key: 'answerTime',
    label: '答题时长',
    type: 'select',
    cocosKey: 'time',
    props: {
      afterfix: '秒',
      options: Array.from({ length: 120 }, (v, i) => (i+1)),
      max: 120,
      required: true,
    }
  },
]

const model =  {
  description: '',
  image: '',
  qeustionEN: [{
    value: '',
    error: ''
  }],
  qeustionCH: '',
  type: 2,
  answerTime: 4,
}

export const enPKComponentItemConfigs = {
  configs,
  model
}
