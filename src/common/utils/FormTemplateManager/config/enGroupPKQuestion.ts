export const configs = [


  {
    key: 'teacherBgUrl',
    label: '主讲背景图',
    type: 'img',
    cocosKey: 'teacherBgUrl',
    props: {
      required: true,
    }
  },

  {
    key: 'pkStartSpine',
    label: '开始pk动画',
    type: 'spine',
    cocosKey: 'pkStartSpine',
    props: {
      required: true,
    }
  },

  {
    key: 'pkStartSound',
    label: '开始pk音频',
    type: 'audio',
    cocosKey: 'pkStartSound',
    props: {
      required: true,
    }
  },


  {
    key: 'countDownSpine',
    label: '倒计时动画',
    type: 'spine',
    cocosKey: 'countDownSpine',
    props: {
      required: true,
    }
  },
  {
    key: 'countDownSound',
    label: '倒计时音频',
    type: 'audio',
    cocosKey: 'countDownSound',
    props: {
      required: true,
    }
  },



  {
    key: 'changeNextSpine',
    label: '切题动画',
    type: 'spine',
    cocosKey: 'changeNextSpine',
    props: {
      required: true,
    }
  },

  {
    key: 'changeNextSound',
    label: '切题音频',
    type: 'audio',
    cocosKey: 'changeNextSound',
    props: {
      required: true,
    }
  },
  {
    key: 'waitScoreSound',
    label: '等待评分音频',
    type: 'audio',
    cocosKey: 'waitScoreSound',
    props: {
      required: true,
    }
  },


  {
    key: 'showRankBgUrl',
    label: '排行榜背景图',
    type: 'img',
    cocosKey: 'showRankBgUrl',
    props: {
      required: true,
    }
  },

  {
    key: 'showRankSpine',
    label: '排行榜动画',
    type: 'spine',
    cocosKey: 'showRankSpine',
    props: {
      required: true,
    }
  },
  {
    key: 'showRankSound',
    label: '排行榜音频',
    type: 'audio',
    cocosKey: 'showRankSound',
    props: {
      required: true,
    }
  }
]


const model = {
  teacherBgUrl: '',

  pkStartSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: ''
  },
  pkStartSound: {
    url: '',
    name: '',
    duration: 0,
  },

  countDownSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: ''
  },

  countDownSound: {
    url: '',
    name: '',
    duration: 0,
  },
  changeNextSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: ''
  },

  changeNextSound: {
    url: '',
    name: '',
    duration: 0,
  },
  waitScoreSound: {
    url: '',
    name: '',
    duration: 0,
  },

  showRankBgUrl: "",
  showRankSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: ''
  },
  showRankSound: {
    url: '',
    name: '',
    duration: 0,
  }
}

export const enGroupPKQuestionConfigs = {
  configs,
  model,
}
