export const configs = [
  {
    key: 'countDownImages',
    label: '倒计时',
    type: 'img-array',
    cocosKey: 'countDownUrl',
    props: {
      max: 3,
      min: 3,
      required: true,
    }
  },
  {
    key: 'countDownSound',
    label: '倒计时',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'countDownSound',
    props: {
      required: true,
    }
  },
  {
    key: 'answerRaceImage',
    label: '抢答按钮',
    type: 'img',
    cocosKey: 'countDownEndUrl',
    props: {
      required: true,
    }
  },
  {
    key: 'answerRaceSound',
    label: '抢答按钮',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'countDownEndSound',
    props: {
      required: true,
    }
  },
  {
    key: 'randomSound',
    label: '随机', // 
    type: 'audio',
    cocosKey: 'randomSound',
    props: {
      required: true,
    }
  },
  {
    key: 'pkImage',
    label: 'PK',
    type: 'img',
    cocosKey: 'pkUrl',
    props: {
      required: true,
    }
  },
  {
    key: 'pkSound',
    label: 'PK',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'pkSound',
    props: {
      required: true,
    }
  },
  {
    key: 'pkLeftImage',
    label: 'PK背景图-左',
    type: 'img',
    cocosKey: 'pkBgUrl0',
    props: {
      required: true,
    }
  },
  {
    key: 'pkRightImage',
    label: 'PK背景图-右',
    type: 'img',
    cocosKey: 'pkBgUrl1',
    props: {
      required: true,
    }
  },
  {
    key: 'questionItemImage', // 单个题目未作答的占位图
    label: '未作答图',
    type: 'img',
    cocosKey: 'starUrl0',
    props: {
      required: true,
    }
  },
  {
    key: 'questionItemDoneImage', // 单个题目已作答的占位图
    label: '已答题图',
    type: 'img',
    cocosKey: 'starUrl1',
    props: {
      required: true,
    }
  },
  {
    key: 'questionItemDoneAudio',
    label: '已答题图',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'addStarSound',
    props: {
      required: true,
    }
  },
  {
    key: 'winSpine',
    label: '作答结果-胜利',
    type: 'spine',
    cocosKey: 'succSpine',
    props: {
      required: true,
    }
  },
  {
    key: 'winAudio',
    label: '作答结果-胜利',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'succSound',
    props: {
      required: true,
    }
  },
  {
    key: 'drawSpine',
    label: '作答结果-平局',
    type: 'spine',
    cocosKey: 'equalSpine',
    props: {
      required: true,
    }
  },
  {
    key: 'drawAudio',
    label: '作答结果-平局',
    hideLabel: true,
    type: 'audio',
    cocosKey: 'equalSound',
    props: {
      required: true,
    }
  },
]


const model =  {
  countDownImages: [],
  countDownSound: {
    url: '',
    name: '',
    duration: 0,
  },
  answerRaceImage: '',
  answerRaceSound: {
    url: '',
    name: '',
    duration: 0,
  },
  randomSound: {
    url: '',
    name: '',
    duration: 0,
  },
  pkImage: '',
  pkSound: {
    url: '',
    name: '',
    duration: 0,
  },
  pkLeftImage: '',
  pkRightImage: '',
  questionItemImage: '',
  questionItemDoneImage: '',
  questionItemDoneAudio: {
    url: '',
    name: '',
    duration: 0,
  },
  winSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: ''
  },
  winAudio: {
    url: '',
    name: '',
    duration: 0,
  },
  drawSpine: {
    atlas: '',
    images: [],
    skeleton: '',
    animation: '',
    cover: ''
  },
  drawAudio: {
    url: '',
    name: '',
    duration: 0,
  },
}

export const enPKQuestionConfigs = {
  configs,
  model,
}

