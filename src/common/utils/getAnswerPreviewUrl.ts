import { isDevelopmentEnv } from "./env";

const getCocosPath = () => {
  if (isDevelopmentEnv) return location.origin;
  return `${location.origin}/interactive-question-editor`;
}

export const getAnswerPreviewUrl = () => {
  if (isDevelopmentEnv) {
    return `${getCocosPath()}/preview.html?answer=1&mode=mobile`;
  } else {
    return `/interactive-question-editor/preview.html?answer=1&mode=mobile`;
  }
}