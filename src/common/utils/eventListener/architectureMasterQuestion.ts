import store from "@/pages/index/store";
import { Message } from "element-ui";
import { isEqual } from "lodash-es";


/**
 * 深度优先搜索（DFS）
 * 从给定的位置开始，访问所有连续的1，并返回访问过的1的数量
 */
const dfs = ([i, j]: [number, number], shape: number[][], visited: boolean[][]): number => {
  // 如果位置无效或者已经被访问过，返回0
  if (i < 0 || i >= shape.length || j < 0 || j >= shape.length || shape[i][j] === 0 || visited[i][j]) {
    return 0;
  }

  // 标记当前位置已经被访问过
  visited[i][j] = true;

  // 访问当前位置的上、下、左、右四个位置，并返回访问过的1的数量
  return 1
    + dfs([i - 1, j], shape, visited)
    + dfs([i + 1, j], shape, visited)
    + dfs([i, j - 1], shape, visited)
    + dfs([i, j + 1], shape, visited);
}

const isShapeValid = (shape: number[][]): boolean => {
  if (!shape) {
    return false;
  }

  const shapeLength: number = shape.length;
  let oneCount = 0;
  // 创建一个二维数组记录每个位置是否被访问过
  const visited: boolean[][] = Array(shapeLength).fill(false).map(() => Array(shapeLength).fill(false));

  // 找到第一个1的位置并计算1的总数
  let start: [number, number] | null = null;
  for (let i = 0; i < shapeLength; i++) {
    for (let j = 0; j < shapeLength; j++) {
      if (shape[i][j] === 1) {
        oneCount++;
        if (start === null) {
          start = [i, j];
        }
      }
    }
  }

  // 如果没有1，返回false
  if (oneCount === 0) {
    return false;
  }

  // 如果只有一个1，返回true
  if (oneCount === 1) {
    return true;
  }

  // 使用深度优先搜索（DFS）访问所有连续的1
  const visitedCount: number = dfs(start!, shape, visited);

  // 如果所有的1都连续，返回true
  return visitedCount === oneCount;
}


export const checkMaterialList = (list: Array<{ stuMaterialTotalCount: string, stuMaterialUseCount: string, materialShapeConfigs: number[][] }>) => {
  const invalidConfigIndex: number[] = [];
  // stuMaterialUseCount stuMaterialTotalCount
  // 建材的数量
  const invalidCountIndex: number[] = [];
  let countIs0 = true;
  list.forEach((item, IIndex) => {
    const { materialShapeConfigs, stuMaterialUseCount, stuMaterialTotalCount } = item;
    if (stuMaterialTotalCount < stuMaterialUseCount) {
      invalidCountIndex.push(IIndex + 1);
    }
    if (Number(stuMaterialUseCount)) {
      countIs0 = false;
    }
    if (!isShapeValid(materialShapeConfigs)) {
      if (!invalidConfigIndex.includes(IIndex + 1)) invalidConfigIndex.push(IIndex + 1);
    }
  })
  if (invalidConfigIndex.length) {
    Message.error(`筑材${invalidConfigIndex.join("、")}形状有误，需要方块间上下左右相连，请更改配置`)
    return false;
  }
  if (invalidCountIndex.length) {
    Message.error(`筑材${invalidCountIndex.join('、')}中数量配置有误，请更改配置`);
    return false;
  } 
  if (countIs0) {
    Message.error(`生成题面的筑材数量不可都为0，请更改配置`);
    return false;
  } 
  return true;
}

// 自动生成题面-筑材
export const caseArchitectureMasterAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  if (store.state.template.category !== 1162) return;
  const SUBTYPE = "architectureMaster";
  console.log('表单联动-帮帮建筑大赛');

  const { propertiesKey, val, oldVal } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (val === oldVal) return;
  const { stuMaterialList } = properties;
  if (propertiesKey === 'stuMaterialList') {
    // 筑材数量减少时，需要清空题面
    if ((oldVal || []).length > (val || []).length) {
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuGenQuestion']: [],
        },
      });
      return;
    }
  }
  if (propertiesKey === 'stuGenRandom') {
    // 1 为通知cocos生成题面专用字段
    if (val === 1) return;
    // 校验
    const checkRes = checkMaterialList(stuMaterialList)
    console.log('checkRes', checkRes);
    if (checkRes) {
      // 校验成功，将stuGenRandom重置为1 题板监听到值的变化后生成题面
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuGenRandom']: 1,
        },
      });
    }
  }
  if (['materialShapeConfigs', 'stuMaterialTotalCount', 'stuMaterialUseCount'].includes(propertiesKey)) {
    if (isEqual(oldVal, val)) return;
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuGenQuestion']: [],
      },
    });
  }
}

export const targetCategory = 1162;

export default caseArchitectureMasterAssemble;
