import store from "@/pages/index/store";
export const caseParkourAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "parkour";
  console.log('表单联动-跑酷');

  const { propertiesKey, val } = data;
  const { subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseCutRopeAssemble');
  // connectPos goodPos
  if (['stuAccuracyA'].includes(propertiesKey)) {
    store.commit("updateFormItemProps", {
      props: {
        "min": val > 1 ? Number(val) + 1 : 1,
      },
      key: 'stuAccuracyB',
      subType: subType,
    });
    
  }
}


export default caseParkourAssemble;
