import store from "@/pages/index/store";
import { isEqual } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";

export const caseTomJerryAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "tomJerry";
  console.log('表单联动-猫鼠游戏');

  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseTomJerryAssemble');
  // stuGrid 题目规格
  // stuImageList val.length oldLength
  // stuQuestions 初始题板
  // stuShowLine 是否可以看到视线 stuLineColors min max需要改 生成的数量也要改
  // stuAnswerImageIndex 正确答案-选择图案
  // stuAnswerConfigs 正确答案-宫格-置灰/绿色
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuLineColorsFormConfigs = compFormConfigs.find((item: { key: string; }) => item.key === 'stuLineColors') || compFormConfigs.find((item: { key: string; }) => item.key === 'stuShowLine').options[0].associatedForm
[0];
  const stuShowLineFormConfigs = compFormConfigs.find((item: { key: string; }) => item.key === 'stuShowLine');
[0];
  const props: any = {
    stuGrid: properties.stuGrid,
    stuCol: properties.stuCol,
    stuRow: properties.stuRow,
    stuImageList: cloneDeep(properties.stuImageList),
    stuQuestions: cloneDeep(properties.stuQuestions),
    stuShowLine: cloneDeep(properties.stuShowLine),
    stuLineColors: cloneDeep(properties.stuLineColors),
    stuAnswerImageIndex: properties.stuAnswerImageIndex,
    stuAnswerConfigs: cloneDeep(properties.stuAnswerConfigs)
  }
  props[propertiesKey] = val;
  props.stuCol = parseInt(props.stuGrid);
  props.stuRow = parseInt(props.stuGrid);
  console.log('caseTomJerryAssemble-props', props);
  if (propertiesKey === 'stuGrid') {
    // n*n
    props.stuQuestions = Array.from({ length: Number(props.stuCol) + 1 }, () => Array.from({ length: Number(props.stuCol) + 1 }, () => (null)));
    props.stuAnswerConfigs = cloneDeep(props.stuQuestions);
    props.stuAnswerImageIndex = null;
    props.stuLineColors = [];
  }
  // 找出props中与properties不同的属性
  const diffProps: any = {};
  Object.keys(props).forEach((key) => {
    if (!isEqual(props[key], properties[key])) {
      diffProps[key] = props[key];
    }
  })
  if (diffProps['stuQuestions'] || ['stuShowLine', 'stuQuestions'].includes(propertiesKey)) {
    let colLen = 0;
    // 置灰
    props.stuAnswerConfigs.forEach((item: any[], index: number) => {
      item.forEach((sItem, subIndex) => {
        if (typeof props.stuQuestions[index][subIndex] === 'number') {
          console.log('index-初始题板有值，需要置灰', index, subIndex);
          item[subIndex] = -1;
          colLen++;
        } else if (sItem === -1) {
          console.log('index-初始题板无值，需要清除置灰', index, subIndex);
          item[subIndex] = 0;
        }
      })
    })
    const tempColors = Array.from({ length: colLen }, (item, index) => {
      console.log(item, index);
      return props.stuLineColors[index] || { lineColor: '#d83034' }
    })
    if (props['stuShowLine']) {
      // 视线数量
      props.stuLineColors = tempColors;
    } else {
      props.stuLineColors = [];
    }
    store.commit("updateFormItemProps", {
      props: {
        "max": tempColors.length,
        "min": tempColors.length,
        "value": tempColors
      },
      key: 'stuLineColors',
      subType: subType,
    });
    const options = cloneDeep(stuShowLineFormConfigs.options);
    console.log('stuShowLineFormConfigs', stuShowLineFormConfigs, options);
    if(options) {
      options[0].associatedForm[0].max = tempColors.length;
      options[0].associatedForm[0].min = tempColors.length;
      options[0].associatedForm[0].value = tempColors;
      store.commit("updateFormItemProps", {
        props: {
          "options": options
        },
        key: 'stuShowLine',
        subType: subType,
      });
    }
  }
  if (diffProps['stuAnswerImageIndex'] || propertiesKey === 'stuAnswerImageIndex') {
    // 空
    props.stuAnswerConfigs.forEach((item: any[], index: number) => {
      item.forEach((sItem, subIndex) => {
        if (sItem !== -1) {
          console.log('index-选择图案有变，需要清空已设置的答案', index, subIndex);
          item[subIndex] = 0;
        }
      })
    })
  }
  // 二次diff
  Object.keys(props).forEach((key) => {
    if (!isEqual(props[key], properties[key])) {
      diffProps[key] = props[key];
    }
  })
  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ...diffProps,
    }
  })
}

export const targetCategory = 1157;

export default caseTomJerryAssemble;
