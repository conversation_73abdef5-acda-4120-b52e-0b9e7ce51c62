/* eslint-disable @typescript-eslint/no-use-before-define */
import store from "@/pages/index/store";
import { cloneDeep } from "lodash-es";
export const prizeClawAssembly = async (data: { propertiesKey: string; val: any; oldVal: any, changeConfig: { tag: string, type: string, extra?: any, parentKey: string }; subIndex: number }) => {
  const SUBTYPE = "prizeClaw";
  console.log('表单联动-欢乐抓娃娃');
  const { propertiesKey, val, oldVal, subIndex, changeConfig = { parentKey: '' } } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('prizeClawAssembly', changeConfig.parentKey, propertiesKey, val, oldVal, subIndex);
  // 增删 数据删除 不用更新
  // propertiesKey==='url' &&  changeConfig.parentKey === 'stuOptionBodyList' subIndex
  // propertiesKey==='url' &&  changeConfig.parentKey === 'stuOtherBodyList' subIndex

  if (propertiesKey === 'url') {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const points = await genPointByUrl(val);
    const temp = cloneDeep(properties[changeConfig.parentKey]);
    temp[subIndex].points = points;
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        [changeConfig.parentKey]: temp
      },
    });
  }
  // stuPawLeft--stuPawLeftPoints  stuPawRight stuPawRightPoints
  if (propertiesKey.includes('stuPaw')) {
    const points = await genPointByUrl(val);
    const pointKey = `${propertiesKey}Points`;
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        [pointKey]: points
      },
    });
  }
}

const genPointByUrl = (url: string) => {
  const config = {
    threshold: 1,
    drawBobOutlinePointsDelay: 10,
    drawContourPointsDelay: 100,
    imageDataSize: 2
  };

  const genPoints = function (img: any) {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    if (!ctx) return;
    ctx.clearRect(0, 0, img.width, img.height);
    ctx.drawImage(img, 0, 0);
    const canvasImageData = ctx.getImageData(0, 0, img.width, img.height);
    const data = canvasImageData.data;
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    const blobOutlinePoints = marchingSquares.getBlobOutlinePoints(data, img.width, img.height, true);

    const imageData = ctx.createImageData(config.imageDataSize, config.imageDataSize);
    for (let i = 0; i < imageData.data.length; i += 4) {
      imageData.data[i + 0] = 255;  // R value
      imageData.data[i + 1] = 0;    // G value
      imageData.data[i + 2] = 0;  // B value
      imageData.data[i + 3] = 255;  // A value
    }

    const contourPoints = rdp(blobOutlinePoints, config.threshold);
    if (contourPoints[0] && contourPoints[contourPoints.length - 1] && contourPoints[0].x === contourPoints[contourPoints.length - 1].x && contourPoints[0].y === contourPoints[contourPoints.length - 1].y) {
      // 去掉重复点 
      contourPoints.length = contourPoints.length - 1;
    }
    return contourPoints;
  };

  const marchingSquares = {
    NONE: 0,
    UP: 1,
    LEFT: 2,
    DOWN: 3,
    RIGHT: 4,
    data: [],
    width: 0,
    height: 0,
    loop: 0,
    upLeft: false,
    upRight: false,
    downLeft: false,
    downRight: false,
    nextStep: 0,
    previousStep: 0,
    state: 0,
    getBlobOutlinePoints: (data: any, width: any, height: any, loop: any): any => {
      marchingSquares.data = data;
      marchingSquares.width = width;
      marchingSquares.height = height;
      marchingSquares.loop = loop;
      const p = marchingSquares.getFirstNonTransparentPixelTopDown() || { x: 0, y: 0 };
      const  result = marchingSquares.walkPerimeter(p.x, p.y);
      // marchingSquares.width = null,
      // marchingSquares.height = null,
      // marchingSquares.data = null,
      // marchingSquares.loop = null,
      return result;
    },
    getFirstNonTransparentPixelTopDown: function () {
      let t, a,s = 0;
      const { data, width, height } = marchingSquares;
      for (t = 0; t < height; t++) for (a = 0; a < width; a++, s += 4) if (data[s + 3] > 0) return {
        x: a,
        y: t
      };
      return null
    },
    walkPerimeter: function (posX: number, posY: number) {
      posX < 0 && (posX = 0),
        posX > marchingSquares.width && (posX = marchingSquares.width),
        posY < 0 && (posY = 0),
        posY > marchingSquares.height && (posY = marchingSquares.height);
      let x = posX,
        y = posY;
      const result = [{ x, y }];
      do {
        switch (marchingSquares.step(x, y, marchingSquares.data), marchingSquares.nextStep) {
          case marchingSquares.UP:
            y--;
            break;
          case marchingSquares.LEFT:
            x--;
            break;
          case marchingSquares.DOWN:
            y++;
            break;
          case marchingSquares.RIGHT:
            x++
        }
        x >= 0 && x <= marchingSquares.width && y >= 0 && y <= marchingSquares.height && result.push({ x, y })
      } while (x !== posX || y !== posY);
      return marchingSquares.loop && result.push({ x, y }),
        result
    },
    step: function (x: number, y: number, data: number[]) {
      const { width,  } = marchingSquares;
      const rowOffset = 4 * width;
      const upLeftIndex = (y - 1) * rowOffset + 4 * (x - 1);
      const isInLeft = x > 0;
      const isInRight = x < width;
      const isInDown = y < marchingSquares.height;
      const isInUp = y > 0;
      /*
      checking the 2x2 pixel grid, assigning these values to each pixel, if not transparent
      +---+---+
      | 1 | 2 |
      +---+---+
      | 4 | 8 | <- current pixel (curx,cury)
      +---+---+
      */
      marchingSquares.upLeft = isInUp && isInLeft && data[upLeftIndex + 3] > 0;
      marchingSquares.upRight = isInUp && isInRight && data[upLeftIndex + 7] > 0;
      marchingSquares.downLeft = isInDown && isInLeft && data[upLeftIndex + rowOffset + 3] > 0;
      marchingSquares.downRight = isInDown && isInRight && data[upLeftIndex + rowOffset + 7] > 0;
      marchingSquares.previousStep = marchingSquares.nextStep;
      marchingSquares.state = 0;
      marchingSquares.upLeft && (marchingSquares.state |= 1);
      marchingSquares.upRight && (marchingSquares.state |= 2);
      marchingSquares.downLeft && (marchingSquares.state |= 4);
      marchingSquares.downRight && (marchingSquares.state |= 8);
      /* going DOWN with these cases:
          8          10          11
          +---+---+  +---+---+   +---+---+
          |   |   |  |   | 2 |   | 1 | 2 |
          +---+---+  +---+---+   +---+---+
          |   | 8 |  |   | 8 |   |   | 8 |
          +---+---+  +---+---+  	+---+---+
      */

      /* going UP with these cases:
          1          5           13
          +---+---+  +---+---+  +---+---+ 
          | 1 |   |  | 1 |   |  | 1 |   | 
          +---+---+  +---+---+  +---+---+ 
          |   |   |  | 4 |   |  | 4 | 8 | 
          +---+---+  +---+---+  +---+---+
      */

      /* going LEFT with these cases:
          4          12          14
          +---+---+  +---+---+   +---+---+
          |   |   |  |   |   |   |   | 2 |
          +---+---+  +---+---+   +---+---+
          | 4 |   |  | 4 | 8 |   | 4 | 8 |
          +---+---+  +---+---+  	+---+---+
      */

      /* going RIGHT with these cases:
          2          3           7        
          +---+---+  +---+---+   +---+---+
          |   | 2 |  | 1 | 2 |   | 1 | 2 |
          +---+---+  +---+---+   +---+---+
          |   |   |  |   |   |   | 4 |   |
          +---+---+  +---+---+  	+---+---+
      */

      /*
          6
          +---+---+
          |   | 2 |
          +---+---+
          | 4 |   |
          +---+---+
          当上一步是往上 则这一步往右，否则往左
      */

      /*
          9
          +---+---+
          | 1 |   |
          +---+---+
          |   | 8 |
          +---+---+
          当上一步是往右 则这一步往下，否则往上
      */

      switch (marchingSquares.state) {
        case 1:
          marchingSquares.nextStep = marchingSquares.UP;
          break;
        case 2:
        case 3:
          marchingSquares.nextStep = marchingSquares.RIGHT;
          break;
        case 4:
          marchingSquares.nextStep = marchingSquares.LEFT;
          break;
        case 5:
          marchingSquares.nextStep = marchingSquares.UP;
          break;
        case 6:
          marchingSquares.previousStep == marchingSquares.UP ? marchingSquares.nextStep = marchingSquares.LEFT : marchingSquares.nextStep = marchingSquares.RIGHT;
          break;
        case 7:
          marchingSquares.nextStep = marchingSquares.RIGHT;
          break;
        case 8:
          marchingSquares.nextStep = marchingSquares.DOWN;
          break;
        case 9:
          marchingSquares.previousStep == marchingSquares.RIGHT ? marchingSquares.nextStep = marchingSquares.UP : marchingSquares.nextStep = marchingSquares.DOWN;
          break;
        case 10:
        case 11:
          marchingSquares.nextStep = marchingSquares.DOWN;
          break;
        case 12:
          marchingSquares.nextStep = marchingSquares.LEFT;
          break;
        case 13:
          marchingSquares.nextStep = marchingSquares.UP;
          break;
        case 14:
          marchingSquares.nextStep = marchingSquares.LEFT;
          break;
        default:
          marchingSquares.nextStep = marchingSquares.NONE
      }
    }
  };

  const pointLineDistance = function (point: { x: number; y: number; }, linePointBegin: { x: number; y: number; }, linePointEnd: { x: number; y: number; }) {
    let result, k, b;
    if (linePointBegin.x === linePointEnd.x) {
      result = Math.abs(point.x - linePointBegin.x)
    } else {
      // y = kx + b
      k = (linePointEnd.y - linePointBegin.y) / (linePointEnd.x - linePointBegin.x);
      b = linePointBegin.y - k * linePointBegin.x;
      // kx - y + b = 0
      result = Math.abs(k * point.x - point.y + b) / Math.sqrt(Math.pow(k, 2) + 1);
    }
    return result
  }

  const rdp = (points: string | any[], threshold: number):any => {
    const pointStart = points[0],
      pointEnd = points[points.length - 1];
    if (points.length < 3) return points;
    // eslint-disable-next-line no-var
    for (var sliceIndex = -1, maxDis = 0, index = 1; index < points.length - 1; index++) {
      const curDis = pointLineDistance(points[index], pointStart, pointEnd);
      curDis > maxDis && (maxDis = curDis, sliceIndex = index)
    }
    if (maxDis > threshold) {
      const leftPoints = points.slice(0, sliceIndex + 1),
        rightPoints = points.slice(sliceIndex),
        leftRdp = rdp(leftPoints, threshold),
        rightRdp = rdp(rightPoints, threshold);
      return leftRdp.slice(0, leftRdp.length - 1).concat(rightRdp)
    }
    return [pointStart, pointEnd]
  };
  
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.src = url;

    img.onload = () => {
      resolve(genPoints(img));
    };

    img.onerror = (error) => {
      reject(error)
    };
  })
}

export const targetCategory = 1142;

export default prizeClawAssembly;
