/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-09-02 10:08:27
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-09-09 16:16:43
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/pathPuzzleQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 路线规律 只能有一个起点
export const caseShapeStackerAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "shapeStacker";
  console.log('表单联动-图形曡中叠');

  const { propertiesKey, val, oldVal } = data;
  if (["stuAreaCount"].indexOf(propertiesKey) == -1) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const {stuAreaItems } = properties;
  const prevVal = cloneDeep(stuAreaItems);
  //输入的数小于0时，图形数量为1
  if(val<=0)
  {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuAreaCount']: 1,
      },
    });
    return;
  }
  //增加图形
  if(val>oldVal)
  {
    for (let i = prevVal.length; i < val; i++) {
        prevVal.push({ dashedBorderImage: "",colorImage: "" });
    }
  }
  else(val<oldVal)
  {
    for (let i = prevVal.length; i > val; i--) {
        prevVal.pop();
    }
  }
  if (!isEqual(stuAreaItems, prevVal)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuAreaItems']: prevVal,
      },
    });
    store.commit("updateFormItemProps", {
      props: {
        "min": val,
        "max": val
      },
      key: 'stuAreaItems',
      subType: subType,
    });
  }
}

export default caseShapeStackerAssemble;
