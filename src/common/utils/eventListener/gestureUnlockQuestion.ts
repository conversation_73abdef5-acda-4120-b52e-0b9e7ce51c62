/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-20 14:13:08
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-06-26 11:00:39
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/gestureUnlockQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 迷宫连线 只能有一个起点和一个终点 
export const caseGestureUnlockAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "gestureUnlock";
  console.log('表单联动-解锁手势密码');

  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey !== 'stuGameConfig') return;
  const { subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const checkList = ["1", "2", "3"]
  let newVal = null
  if (val.length !== oldVal.length) return false;
  for (let i = 0; i < val.length; i++) {
    for (let j = 0; j < val[i].length; j++) {
      if (val[i][j] !== oldVal[i][j]) {
        if (checkList.includes(val[i][j]))
          newVal = val[i][j];
      }
    }
  }
  if (newVal === null) return true;

  function searchValue(target: string) {
    for (let row = 0; row < oldVal.length; row++) {
      for (let value = 0; value < oldVal[row].length; value++) {
        if (oldVal[row][value] === target)
          return true
      }
    }
    return false
  }
  //旧数据中包含


  if (newVal === "1") {
    if (searchValue("3")) {
      Message.error("起终点重复，请重新配置");
      return false;
    }
    else if (searchValue("1")) {
      Message.error("起点重复，请重新配置");
      return false;
    }
  }
  else if (newVal === "2") {

    if (searchValue("3")) {
      Message.error("起终点重复，请重新配置");
      return false;
    }
    else if (searchValue("2")) {
      Message.error("终点重复，请重新配置");
      return false;
    }
  }
  else if (newVal === "3") {
    if (searchValue("3")) {
      Message.error("起终点重复，请重新配置");
      return false;
    }
    else if (searchValue("2")) {
      Message.error("终点重复，请重新配置");
      return false;
    }
    else if (searchValue("1")) {
      Message.error("起点重复，请重新配置");
      return false;
    }
  }
  return true;
}
export default caseGestureUnlockAssemble;
