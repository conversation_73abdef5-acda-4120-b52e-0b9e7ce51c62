import store from "@/pages/index/store";
import cloneDeep from "lodash-es/cloneDeep";
export const caseDragChange = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "swapPostions";
  console.log('表单联动-拖拽交换位置');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseDragChange');
  let totalCount = 0;
  properties.stuOperationConfigs.forEach((item: { count: number; }, index: number) => {
    if (index === subIndex && propertiesKey === 'count') {
      totalCount += val;
    } else if (item.count) {
      totalCount += item.count;
    }
  });
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuOperationFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuOperationConfigs');
  const stuShowTargetAreaFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuShowTargetArea');
  const stuTargetConfigsFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuTargetConfigs');
  const newStuShowTargetAreaFormConfigs = cloneDeep(stuShowTargetAreaFormConfigs);
  let stuTargetSubFormConfig: any = {};
  const associatedForm = newStuShowTargetAreaFormConfigs.options.find((item: { value: number; }) => item.value === 1)?.associatedForm;
  newStuShowTargetAreaFormConfigs.options.forEach((opt: {
    value: number; associatedForm: {
      subFormConfigs: any;
      key: string; optionsConfig: { disabledValues: number[]; };
    }[];
  }) => {
    if (opt.associatedForm) {
      opt.associatedForm.forEach((aItem) => {
        if (aItem.key === 'stuTargetConfigs') {
          aItem.subFormConfigs.forEach((sItem: { optionsConfig: { disabledValues: number[]; }; }) => {
            stuTargetSubFormConfig = sItem;
          });
        }
      })
    }
  })
  if (properties.stuShowTargetArea && properties.stuTargetConfigs && totalCount !== properties.stuTargetConfigs.length) {
    const newStuTargetConfigs = properties.stuTargetConfigs.slice(0, totalCount);
    if (totalCount > newStuTargetConfigs.length) {
      newStuTargetConfigs.push(...Array(totalCount - newStuTargetConfigs.length).fill({ imageIndex: '' }));
    }
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuTargetConfigs']: newStuTargetConfigs
      },
    });
  }
  associatedForm[0].max = totalCount;
  associatedForm[0].min = totalCount;
  associatedForm[0].value = Array(totalCount).fill({ imageIndex: '' });
  // stuShowTargetArea 0 否 1 是
  //  stuOperationConfigs
  if (propertiesKey === 'stuOperationConfigs') {
    if (!properties.stuShowTargetArea) return;
    if (val >= oldVal) return;
    if (totalCount !== properties.stuTargetConfigs.length) {
      // 删除后如何刷新
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuTargetConfigs']: properties.stuTargetConfigs.slice(0, totalCount) // 删除后面的几项
        },
      });
    }
  }

  // stuShowTargetArea
  if (propertiesKey === 'stuShowTargetArea') {
    const newStuTargetConfigs = val === 0 ? [] : Array(totalCount).fill({ imageIndex: '' });
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuTargetConfigs']: newStuTargetConfigs
      },
    });
  }
  // imageIndex变化 & stuImageConfigs 变化 影响disableValues
  // stuOperationConfigs 删除的时候
  // stuImageConfigs 删除的时候
  // imageIndex 变化的时候
  let tempOperationConfigs = cloneDeep(properties.stuOperationConfigs);
  let tempImageConfigs = cloneDeep(properties.stuImageConfigs);
  if (['stuOperationConfigs'].includes(propertiesKey)) {
    // stuOperationConfigs 删除
    tempOperationConfigs = val;
  }
  if (['stuImageConfigs'].includes(propertiesKey)) {
    // stuImageConfigs 删除
    tempImageConfigs = val;
  }
  // imageIndex change
  if (['imageIndex'].includes(propertiesKey)) {
    tempOperationConfigs[subIndex].imageIndex = val;
  }
  // stuOperationConfigs中没有 stuImageConfigs中有的imageIndex
  const disabledValues: number[] = [];
  // const allImageIndex = tempOperationConfigs.map((_item: any, index: number) => index);
  // const usefulImageIndex = tempImageConfigs.map((item: { imageIndex: number; }) => item.imageIndex);
  // const disabledValues = allImageIndex.filter((item: number) => !usefulImageIndex.includes(item));
  tempImageConfigs.forEach((item: any, index: number) => {
    if (!tempOperationConfigs.find((oItem: { imageIndex: number; }) => oItem.imageIndex === index)) {
      disabledValues.push(index);
    }
  });
  if (stuTargetSubFormConfig) {
    stuTargetSubFormConfig.optionsConfig.disabledValues = disabledValues;
  }
  store.commit("updateFormItemProps", {
    props: {
      "options": newStuShowTargetAreaFormConfigs.options
    },
    key: 'stuShowTargetArea',
    subType: subType,
  });
  if (stuTargetConfigsFormConfigs) {
    console.log('stuTargetConfigsFormConfigs', Array(totalCount).fill({ imageIndex: '' }));
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": cloneDeep(associatedForm[0].subFormConfigs),
        "min": associatedForm[0].min,
        "max": associatedForm[0].max,
        "value": associatedForm[0].value
      },
      key: 'stuTargetConfigs',
      subType: subType,
    });
  } else {
    // 不展示目标区域时， 需要更新目标区域的options
    store.commit("updateFormItemProps", {
      props: {
        "options": newStuShowTargetAreaFormConfigs.options
      },
      key: 'stuShowTargetArea',
      subType: subType,
    });
  }
  store.commit("updateFormItemProps", {
    props: {
      "subFormConfigs": stuOperationFormConfigs.subFormConfigs
    },
    key: 'stuOperationConfigs',
    subType: subType,
  });
  if (properties.stuTargetConfigs) {
    // 如果stuTargetConfigs中的imageIndex在disabledValues中,则清空
    const newStuTargetConfigs = cloneDeep(properties.stuTargetConfigs);
    newStuTargetConfigs.forEach((item: any) => {
      if (disabledValues.includes(item.imageIndex)) {
        item.imageIndex = '';
      }
    });
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuTargetConfigs']: newStuTargetConfigs
      },
    });
  }
}

export const targetCategory = 1143;

export default caseDragChange;
