/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-05-28 16:29:51
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-05-30 18:43:51
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/questQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { cloneDeep, isEqual } from "lodash-es";


export const caseQuestAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "quest";
  console.log('表单联动-大招收集');

  const { propertiesKey, val, oldVal } = data;
  console.log('大招收集',propertiesKey);
  if (propertiesKey !== "stuOption") return;
  if(val.options.length === oldVal.options.length) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const { stuOption } = properties;
  console.log("大招收集 stuOption", stuOption)
  const prevVal = cloneDeep(stuOption);

  if (prevVal) {
    prevVal.answer = [];
    for (let i = 0; i < prevVal.options.length; i++)
      prevVal.answer.push(i)
  }


  if (!isEqual(stuOption, prevVal)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuOption']: prevVal,
      },
    });
  }
}

export default caseQuestAssemble;
