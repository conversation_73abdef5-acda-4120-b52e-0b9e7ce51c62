import store from "@/pages/index/store";
import { Message } from "element-ui";
import cloneDeep from "lodash-es/cloneDeep";

export const caseKnockIceAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "knockIce";
  console.log('表单联动-敲冰块');
  const { propertiesKey, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseKnockIceAssembly', subIndex);
  // 题目数据生成逻辑
  // 1. stuIceNumber/stuIceColor变化时，stuIceData清空
  // 2. 冰块颜色不满足2种时，stuIceData清空
  // 3. random时，生成stuIceNumber长度的数组，数组中相邻的数据不能相同，数组中的数据从stuIceColor中随机取
  if (['stuIceNumber', 'stuIceColor'].includes(propertiesKey)) {
    const { stuCustomsPass } = properties;
    const temp = cloneDeep(stuCustomsPass);
    temp[subIndex].stuIceData = [];
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuCustomsPass']: temp,
      }
    })
  }
  if (propertiesKey === 'random') {
    const { stuCustomsPass } = properties;
    const temp = cloneDeep(stuCustomsPass);
    const { stuIceNumber, stuIceColor } = temp[subIndex];
    if (stuIceColor.length < 2) {
      Message.error('冰块颜色不能少于2种');
      temp[subIndex].stuIceData = [];
    } else {
      // 生成长度为stuIceNumber的数组 数组中相邻的数据不能相同，数组中的数据从stuIceColor中随机取
      let arr = cloneDeep(stuIceColor);
      arr.sort(() => Math.random() - 0.5);
      arr = arr.slice(0, stuIceNumber);
      const initLen = arr.length;
      for (let i = initLen; i < stuIceNumber; i++) {
        let color = stuIceColor[Math.floor(Math.random() * stuIceColor.length)];
        if (i > 0) {
          while (color === arr[i - 1]) {
            color = stuIceColor[Math.floor(Math.random() * stuIceColor.length)];
          }
        }
        arr.push(color);
      }

      temp[subIndex].stuIceData = arr;
    }
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuCustomsPass']: temp,
      }
    })
  }
}

export const targetCategory = 1155;

export default caseKnockIceAssembly;
