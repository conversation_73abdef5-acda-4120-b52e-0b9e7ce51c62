/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-20 14:13:08
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-07-19 15:02:47
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/gestureUnlockQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 迷宫连线 只能有一个起点和一个终点 
export const caseTapFrenzyAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "tapFrenzy";
  console.log('表单联动-连续点击');

  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey !== 'stuOptions') return;
  if (val.options.length === oldVal.options.length) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const { stuQuestionList } = properties;
  console.log("连续点击 stuOptions", stuQuestionList)
  const prevVal = cloneDeep(stuQuestionList);

  if (prevVal) {
    if (val.options.length > oldVal.options.length) {
      for (let i = oldVal.options.length; i < prevVal[properties.subQuestionIndex].stuOptions.options.length; i++)
        prevVal[properties.subQuestionIndex].stuOptions.answer.push(i)
      if (!isEqual(stuQuestionList, prevVal)) {
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuQuestionList']: prevVal,
          },
        });
      }
    }
  }
}
export default caseTapFrenzyAssemble;
