import store from "@/pages/index/store";
import { isEqual } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";
export const caseCutRopeAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "cutRope";

  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseCutRopeAssemble');
  console.log('表单联动-割绳子');
  const compFormConfigs = store.state.extData.formConfig[subType];
  const props: any = {
    stuStemType: properties.stuStemType, // "1":普通 "2":折叠
    stuConnectCount: properties.stuConnectCount, // 连接物数量
    stuGoodsCount: properties.stuGoodsCount,
    stuConnectConfigs: properties.stuConnectConfigs
  }
  // connectPos goodPos
  if (['stuConnectConfigs', 'connectPos', 'goodPos'].includes(propertiesKey)) {
    const stuConnectConfigs = propertiesKey === 'stuConnectConfigs' ? val : cloneDeep(properties.stuConnectConfigs);
    if (propertiesKey !== 'stuConnectConfigs') {
      stuConnectConfigs[subIndex][propertiesKey] = val;
    } else {
      props[propertiesKey] = val;
      // 如果是新增一条连接关系 return
      if(oldVal.length < val.length) return;
    }
    if (oldVal === undefined) return;
    if (isEqual(val, oldVal)) return;
    
    // formList
    const connectDisabledValues: any[] = [];
    stuConnectConfigs.forEach((item: any) => {
      connectDisabledValues.push(item.connectPos);
    });
    const goodDisabledValues: any[] = [];
    stuConnectConfigs.forEach((item: any) => {
      goodDisabledValues.push(item.goodPos);
    });
    const newSubFormConfigs = cloneDeep(compFormConfigs[0].formList.find((item: any) => item.key === 'stuConnectConfigs').subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === 'connectPos') {
        item.optionsConfig.disabledValues = connectDisabledValues;
      }
      if (item.key === 'goodPos') {
        item.optionsConfig.disabledValues = goodDisabledValues;
      }
    })
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": newSubFormConfigs,
      },
      key: 'stuConnectConfigs',
      subType: subType,
    });
  } else {
    props[propertiesKey] = val;
    const stuConnectOptions: { label: string; value: any; }[] = [];
    const stuGoodOptions: { label: string; value: any; }[] = [];
    const stuConnectIds: number[] = [];
    // 找到所有连接物(connector)组件 
    const componentMap = store.state.componentMap;
    Object.keys(componentMap).forEach((key) => {
      const comp = componentMap[key];
      const compExtra = comp.extra;
      if (comp.type === 'sprite' && (comp.tag == 'connector')) {
        stuConnectOptions.push({ label: `连接物${compExtra.tagIndex}-上`, value: `${compExtra.tagIndex}-1` }, { label: `连接物${compExtra.tagIndex}-下`, value: `${compExtra.tagIndex}-2` });
        stuConnectIds.push(comp.id);
      }
      if (comp.type === 'sprite' && (comp.tag == 'linkGood')) {
        stuGoodOptions.push({ label: `切割物${compExtra.tagIndex}-左`, value: `${compExtra.tagIndex}-1` }, { label: `切割物${compExtra.tagIndex}-右`, value: `${compExtra.tagIndex}-2` });
      }
    })
    // 游戏模式变化后: stuConnectOptions stuGoodOptions 清空
    // 连接物数量变化后: stuConnectOptions stuGoodOptions 重新生成
    // 切割数量变化后: stuGoodOptions 重新生成
    if (propertiesKey === 'stuStemType') {
      if (props['stuStemType'] === '1') {
        // 连接物组件清空
        stuConnectIds.length && store.dispatch("removeComponents", stuConnectIds);

        props['stuConnectCount'] = undefined;
        props['stuConnectConfigs'] = undefined;
        props['stuConnectOptions'] = undefined;
        props['stuGoodOptions'] = undefined;
      } else {
        props['stuConnectConfigs'] = [
          {
            'connectPos': '', 'goodPos': ''
          }
        ];
        props['stuConnectCount'] = undefined;
      }
    }

    props['stuConnectOptions'] = stuConnectOptions;
    props['stuGoodOptions'] = stuGoodOptions;
  }
  // 找出props中与properties不同的属性
  const diffProps: any = {};
  Object.keys(props).forEach((key) => {
    if (!isEqual(props[key], properties[key])) {
      diffProps[key] = props[key];
    }
  })
  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ...props,
    }
  })
}

export const targetCategory = 1174;

export default caseCutRopeAssemble;
