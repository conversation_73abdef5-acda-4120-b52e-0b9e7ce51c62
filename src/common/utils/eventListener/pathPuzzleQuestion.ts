/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-09-02 10:08:27
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-09-18 14:52:05
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/pathPuzzleQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 路线规律 只能有一个起点
export const casePathPuzzleAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "pathPuzzle";
  console.log('表单联动-路线规律');


  const { propertiesKey, val, oldVal } = data;
  if (["stuQuestionConfigs", "stuElementList", "stuDragNum", "stuOption"].indexOf(propertiesKey) == -1) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const { stuQuestionType, stuDragQuestionConfigs,stuDragOptions,stuAdsorptionZoneCorrectList, stuElementList, stuDragNum } = properties;
  const prevVal = cloneDeep(val);
  //stuQuestionType

  if (propertiesKey === "stuQuestionConfigs") {
    // 判断是否有多个起点
    const startIsMore = val.outerTables.flat().filter((item: string) => item === "start").length > 1;
    if (startIsMore) {
      val.outerTables.forEach((item: any[], index: string | number) => {
        item.forEach((subItem, subIndex) => {
          const targetVal = "start";
          if (subItem === targetVal && subItem === oldVal.outerTables[index][subIndex]) {
            console.log('startIsMore', startIsMore, subItem, targetVal);
            item[subIndex] = '';
          }
        });
      })
    }
    console.log('判断是否有多个起点', startIsMore, val);
    if (!isEqual(val, prevVal)) {
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuQuestionConfigs']: val,
        },
      });
    }
  }
  else if (propertiesKey === "stuElementList") {
    //判断是否删除数据
    if (val.length < oldVal.length) {
      let deleteIndex = "";
      oldVal.forEach((item: any) => {
        let isHave = false;
        for (let i = 0; i < val.length; i++) {
          if (val[i].stuName === item.stuName)
            isHave = true;
        }
        if (!isHave) {
          deleteIndex = item.stuName;
        }
      })
      if (deleteIndex !== "") {
        const { stuQuestionConfigs } = properties;
        const configVal = cloneDeep(stuQuestionConfigs);
        configVal.innerTables = configVal.innerTables.map((subArr: any[]) => subArr.map((item: string) => item === deleteIndex ? "" : item));
        console.log('删除后题干数据', configVal.innerTables);
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuQuestionConfigs']: configVal,
          },
        });
      }

    }
    else {
      let isRepeat = false;
      for (let i = 0; i < val.length; i++) {
        for (let j = i + 1; j < val.length; j++) {
          if (val[i].stuName === val[j].stuName && val[i].stuName !== "") {
            isRepeat = true;
          }
        }
      }
      if (isRepeat) {
        console.log("重名", val);
        Message.error('题板元素名称重复，请重新输入');
        return;
      }
    }
  }
  //拖拽题
  if (stuQuestionType === "2") {
    if (propertiesKey === "stuDragNum") {
      const newStuDragQuestionConfigs = [];
      for (let i = 0; i < stuElementList.length; i++) {
        newStuDragQuestionConfigs.push({
          "label": stuElementList[i].stuName,
          "value": stuElementList[i].stuName
        })
      }
      for (let i = 1; i <= val; i++) {
        newStuDragQuestionConfigs.push({
          "label": "吸附区" + i,
          "value": "吸附区" + i
        })
      }
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuDragQuestionConfigs']: newStuDragQuestionConfigs,
        },
      });
      //删除吸附区间数量配置
      if (val < oldVal) {
        const { stuQuestionConfigs } = properties;
        const configVal2 = cloneDeep(stuQuestionConfigs);
        for (let i = val; i <= oldVal; i++) {
          configVal2.innerTables = configVal2.innerTables.map((subArr: any[]) => subArr.map((item: string) => item === "吸附区" + i ? "" : item));
        }
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuQuestionConfigs']: configVal2,
          },
        });
      }
      //修改吸附区数量配置
      const newStuAdsorptionZoneCorrectList = []
      for(let i = 0;i<val;i++)
      {
        if(i<stuAdsorptionZoneCorrectList.length)
          newStuAdsorptionZoneCorrectList.push({answer:stuAdsorptionZoneCorrectList[i].asnwer});
        else
          newStuAdsorptionZoneCorrectList.push({answer:""});
      }
      console.log('修改吸附区数量配置',newStuAdsorptionZoneCorrectList);
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuAdsorptionZoneCorrectList']: newStuAdsorptionZoneCorrectList,
        },
      });
      store.commit("updateFormItemProps", {
        props: {
          "min": val,
          "max": val
        },
        key: 'stuAdsorptionZoneCorrectList',
        subType: subType,
      });
    }
    if (propertiesKey === "stuElementList") {
      const newStuDragQuestionConfigs = [];
      for (let i = 0; i < val.length; i++) {
        newStuDragQuestionConfigs.push({
          "label": stuElementList[i].stuName,
          "value": stuElementList[i].stuName
        })
      }
      for (let i = 1; i <= stuDragNum; i++) {
        newStuDragQuestionConfigs.push({
          "label": "吸附区" + i,
          "value": "吸附区" + i
        })
      }
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuDragQuestionConfigs']: newStuDragQuestionConfigs,
        },
      });
    }
    if (propertiesKey === 'stuOption') {
      const newStuDragOptions = [];
      const optionName = ["选项A", "选项B", "选项C", "选项D", "选项E"]
      for (let i = 0; i < val.options.length; i++) {
        newStuDragOptions.push({"label": optionName[i],"value": i+""})
      }
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuDragOptions']: newStuDragOptions,
        },
      });
    }
  }
}
  export default casePathPuzzleAssemble;
