/**
 * eventListener文件夹中，除了index外，其他的ts文件能且仅能为schema-form-change的回调函数
 * 在回调函数的文件中，必须声明targetCategory和默认的函数
 * index.ts负责根据category动态注册回调函数，并在监听schema-form-change时执行
 * 外部开发者开发的代码的导入-方案设计中
 */
import bus from "@/pages/index/common/utils/bus";
import store from "@/pages/index/store";

/**
 * 
 * @param filePath 文件路径  './xxx.ts'
 * @returns fileName
 */
const getFileName = (filePath: string) => {
  // eslint-disable-next-line no-useless-escape
  const regex = /\/([^.\/]+)\.ts$/;
  const match = filePath.match(regex);

  if (match) {
    // console.log('匹配成功:', match[1]);
    return match[1];
  } else {
    console.error('匹配失败');
    return '';
  }
}

// 批量注册change函数, 没有声明category的代码将不会被注册
const registerCallBackByBundleName = (bundleName: string) => {
  // step1. 获取文件
  const files = require.context("../eventListener", true, /^(?!.*index).*\.ts$/);
  // 注册外部的 todo 从指定的文件中读取
  // step2. 读取modules并根据category进行注册
  const modules: any = {};
  files.keys().forEach((key: any) => {
    // console.log('files item', key,);
    const fileName = getFileName(key);

    // 有没有 类型对不对
    if (fileName && fileName === bundleName) {
      const m = files(key).default;
      if (!m) {
        console.error('没有导出默认模块，请导出默认模块后重试');
      } else {
        modules[bundleName] = m;
      }
      modules[bundleName] = m;
    }
  });
  return modules;
}

const modules = registerCallBackByBundleName(store.state.template.bundleName);

// 监听表单数据变化 并执行对应的逻辑
bus.$on('schema-form-change', (data: any) => {
  console.log('schema-form-change', data);
  // console.log('modules', modules);
  Object.values(modules).forEach(cb => {
    typeof cb === 'function' && cb(data);
  })
})
