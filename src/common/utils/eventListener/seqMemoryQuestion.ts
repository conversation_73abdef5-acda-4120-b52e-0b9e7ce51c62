// import { DEBOUNCE_TIME } from "@/pages/index/common/utils/tools";
import store from "@/pages/index/store";
// import { debounce } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";
import isEqual from "lodash-es/isEqual";
const gridMap = new Map([
  [0, 9],
  [1, 12],
  [2, 16],
]);
const genQuestionLists = (questionCount: number, stuGameType: 0 | 1) => {
  // 生成小题列表
  // stuGameType 0 : { "timeRemember": 1, "timeAnswer": 1 }
  // stuGameType 1 : { "timeRemember": 1, "timeAnswer": 1, "imageConfigs": [{ "count": "","imageIndex": "" }] }
  const questionLists: any[] = [];
  const questionItem: any = {
    timeRemember: 1,
    timeAnswer: 1,
  };
  if (stuGameType) {
    questionItem.imageConfigs = [
      { "count": "", "imageIndex": "" }
    ];
  }
  Array.from({ length: questionCount }).forEach(() => {
    questionLists.push(cloneDeep(questionItem));
  }
  );
  return questionLists;
};


export const caseSeqMemoryAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "seqMemory";
  console.log('表单联动-有序记忆');
  const { propertiesKey, val } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseSeqMemoryAssembly');
  const compFormConfigs = store.state.extData.formConfig[subType];
  const props: any = {
    stuGrid: properties.stuGrid, // 0:3*3 1:4*3 2:4*4
    stuGameType: properties.stuGameType, // 0:数字 1:图形
    stuNumModeStartCnt: properties.stuNumModeStartCnt || 1,
    stuNumModeEndCnt: properties.stuNumModeEndCnt || 1,
    stuQuestionMax: properties.stuQuestionMax,
    subQuestionIndex: 0,
  }

  props[propertiesKey] = val;
  if (propertiesKey === 'stuNumModeStartCnt') {
    if (val === '') return;
    if (val < 1) return;
    if (val > props.stuNumModeEndCnt) {
      props[propertiesKey] = props.stuNumModeEndCnt;
      return;
    }
    if (val < 1) {
      props[propertiesKey] = 1;
      return;
    }
  }
  if (propertiesKey === 'stuNumModeEndCnt') {
    // 值为字符串时为编辑态 非最终的结果 所以过滤
    if (typeof val === 'string') return;
    if (val < 1) return;
    if (val > props.stuQuestionMax) {
      props[propertiesKey] = props.stuQuestionMax;
      return;
    }
    if (val < props.stuNumModeStartCnt) {
      props[propertiesKey] = props.stuNumModeStartCnt;
      return;
    }
  }
  // 规格切换时 开始/结束为 1和规格最大值 同时更新配置的value为规格的最大值
  if (propertiesKey === 'stuGrid') {
    // gridMap
    props['stuQuestionMax'] = gridMap.get(val);
    props['stuNumModeStartCnt'] = 1;
    props['stuNumModeEndCnt'] = gridMap.get(val);
  }
  if (propertiesKey === 'stuGameType') {
    // gridMap
    props['stuNumModeStartCnt'] = 1;
    props['stuNumModeEndCnt'] = gridMap.get(props['stuGrid']);
    props['stuQuestionMax'] = gridMap.get(props['stuGrid']);
  }
  // 数字
  const relKey = 'stuGameType';
  const stuGameTypeFormConfigs = compFormConfigs.find((item: any) => item.key === relKey);
  const stuRoundListFormConfigs = cloneDeep(compFormConfigs.find((item: any) => item.key === 'stuRoundList'));
  const imageListFormConfigs = cloneDeep(compFormConfigs.find((item: any) => item.key === 'imageList'));
  const stuNumModeStartCntFormConfigs = cloneDeep(compFormConfigs.find((item: any) => item.key === 'stuNumModeStartCnt'));
  const tempOptions = cloneDeep(stuGameTypeFormConfigs.options);

  const questionCount = props.stuGameType ? props.stuNumModeEndCnt : props.stuNumModeEndCnt - props.stuNumModeStartCnt + 1;
  console.log('questionCount', questionCount);
  // 如果是游戏模式的切换 需要延迟调用updateComponentProperties， 解决默认值覆盖的问题
  if (propertiesKey === 'stuGameType') {
    // 等待500s
    const timer = setTimeout(() => {
      clearTimeout(timer);
      // 找出props和properties不同的属性
      const diffProps: any = {};
      Object.keys(props).forEach((key) => {
        if (!isEqual(props[key], properties[key])) {
          diffProps[key] = props[key];
        }
      })

      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuRoundList']: genQuestionLists(questionCount, props.stuGameType),
          ['subQuestionIndex']: 0,
          ['imageList']: {
            type: "img",
            options: properties.imageList.options.slice(0, questionCount)
          },
          ...diffProps
        }
      })
    }, 300);
    // 300
  } else {
    const diffProps: any = {};
    Object.keys(props).forEach((key) => {
      if (!isEqual(props[key], properties[key])) {
        diffProps[key] = props[key];
      }
    })
    // console.log('...diffProps', diffProps);
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuRoundList']: genQuestionLists(questionCount, props.stuGameType),
        ...diffProps
      }
    })
  }
  // 数字
  tempOptions.forEach((item: any) => {
    if (props.stuGameType === item.value || propertiesKey === 'stuGrid') {
      item.associatedForm.forEach((formItem: any) => {
        if (formItem.key === 'stuRoundList') {
          formItem.min = props.stuGameType ? 1 : questionCount; // 数字模式不可以删除 图形模式可以删除
          formItem.max = questionCount;
          formItem.subFormConfigs.forEach((subFormItem: any) => {
            if (subFormItem.key === 'imageConfigs') {
              subFormItem.max = questionCount;
              // imageConfigs
              subFormItem.subFormConfigs.forEach((subSubFormItem: any) => {
                if (subSubFormItem.key === 'count') {
                  subSubFormItem.max = questionCount;
                }
              })
            }
          })
          store.commit("updateFormItemProps", {
            props: {
              "max": questionCount,
              "min": props.stuGameType ? 1 : questionCount,
              "subFormConfigs": formItem.subFormConfigs,
            },
            key: 'stuRoundList',
            subType: subType,
          });
        };
        if (stuRoundListFormConfigs) {
          stuRoundListFormConfigs.min = props.stuGameType ? 1 : questionCount; // 数字模式不可以删除 图形模式可以删除
          stuRoundListFormConfigs.max = questionCount;
          stuRoundListFormConfigs.subFormConfigs.forEach((subFormItem: any) => {
            if (subFormItem.key === 'imageConfigs') {
              subFormItem.max = questionCount;
              // imageConfigs
              subFormItem.subFormConfigs.forEach((subSubFormItem: any) => {
                if (subSubFormItem.key === 'count') {
                  subSubFormItem.max = questionCount;
                }
              })
            }
          })
          store.commit("updateFormItemProps", {
            props: {
              "min": stuRoundListFormConfigs.min,
              "max": stuRoundListFormConfigs.max,
              "subFormConfigs": stuRoundListFormConfigs.subFormConfigs,
            },
            key: 'stuRoundList',
            subType: subType,
          });
        }
        if (imageListFormConfigs) {
          store.commit("updateFormItemProps", {
            props: {
              "max": questionCount,
            },
            key: 'imageList',
            subType: subType,
          });
        }
        if (formItem.key === 'imageList') {
          formItem.max = questionCount;
        }
        if (formItem.key === 'stuNumModeStartCnt') {
          formItem.max = props.stuNumModeEndCnt || props.stuQuestionMax;
          if (stuNumModeStartCntFormConfigs) {
            store.commit("updateFormItemProps", {
              props: {
                "max": formItem.max,
              },
              key: 'stuNumModeStartCnt',
              subType: subType,
            });
          }
        };
        if (formItem.key === 'stuNumModeEndCnt') {
          formItem.min = Math.min(props.stuNumModeEndCnt || props.stuQuestionMax, props.stuNumModeStartCnt || 1);
          formItem.max = props.stuQuestionMax;
          formItem.value = props.stuQuestionMax;
          formItem.forceUpdate = props.stuQuestionMax;
          if (stuNumModeStartCntFormConfigs) {
            store.commit("updateFormItemProps", {
              props: {
                "min": formItem.min,
                "max": formItem.max,
                "value": formItem.max,
                "forceUpdate": props.stuQuestionMax,
              },
              key: 'stuNumModeEndCnt',
              subType: subType,
            });
          }
        };
      })
    }
  });
  // stuNumModeStartCnt: max imageList max
  store.commit("updateFormItemProps", {
    props: {
      "options": tempOptions
    },
    key: relKey,
    subType: subType,
  });
  console.log('relKey', tempOptions);
  if (props.stuGameType) {
    store.commit("updateFormItemProps", {
      props: {
        "max": questionCount
      },
      key: 'imageList',
      subType: subType,
    });
  }
}

// 添加debounce时 动态表单的校验有问题 所以不用延时
// const debounceFun = debounce((data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
//   caseSeqMemoryAssembly(data);
// }, 0);

export const targetCategory = 1151;

export default caseSeqMemoryAssembly;
