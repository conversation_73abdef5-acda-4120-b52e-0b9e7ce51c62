import store from "@/pages/index/store";

const colors = [
  "#E5E5E5", // 灰 题板默认格子灰     stuBaseGridUrl1
  "#88DDFF", // 蓝 题板默认格子蓝色   stuBaseGridUrl2
  "#FDBBD8", // 粉 题板默认格子粉色   stuBaseGridUrl3
  "#AFF392", // 绿 题板默认格子绿色   stuBaseGridUrl4
  "#FFE27E", // 黄 题板默认格子黄色   stuBaseGridUrl5
  "#DFDEFF", // 紫 题板默认格子紫色   stuBaseGridUrl6
  "#ABECF6", // 蓝 题板默认格子浅蓝色  stuBaseGridUrl7
  "#FFC896", // 橙 题板默认格子橙色   stuBaseGridUrl8
  "#9CF1CA", // 绿 题板默认格子浅绿色  stuBaseGridUrl9
  "#E8D7FF"  // 紫 题板默认格子粉紫色  stuBaseGridUrl10
]
export const caseSudokuQuestionAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "sudoku";
  console.log('表单联动-数独');
  const { propertiesKey, val, oldVal } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (val === oldVal) return;
  // stuBaseGridUrl
  // 颜色图片和宫格颜色的联动关系处理 当颜色的图片删除时，与此关联的宫格颜色及虚线框配置同步清空
  if (propertiesKey.includes('stuBaseGridUrl')) {
    const urlKeys = Array.from({ length: 10 }, (_, index) => `stuBaseGridUrl${index+1}`);
    const canUseColors: string[] = [];
    urlKeys.forEach((url, index) => {
      const theColor = colors[index];
      if (properties[url] || (url === 'propertiesKey' && val)) {
        canUseColors.push(theColor);
      } else {
        const { stuSoduku } = properties;
        stuSoduku.cells.forEach((item: any[]) => {
          item.forEach((cell) => {
            if (cell.color === theColor) {
              cell.color = '';
              cell.groupId = '';
              // 如果是根据颜色组成虚线框的 需要将虚线框的配置删除
              const groupId = cell.groupId;
              const { type, isMark } = stuSoduku;
              if ((type === "3") || (type === "2" && isMark === "0")) {
                stuSoduku.groups = stuSoduku.groups.filter((item: { id: number; }) => item.id === groupId);
              }
            }
          })
        })
      }
      store.commit("updateComponentProperties", {
        id,
        newProperties: {
          stuSoduku: {
            ...properties.stuSoduku,
            colors: canUseColors
          },
        }
      });
    });
    // 如果颜色消失怎么处理？
  }
}

export const targetCategory = 1184;

export default caseSudokuQuestionAssemble;
