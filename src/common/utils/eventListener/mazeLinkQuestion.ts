import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 迷宫连线 只能有一个起点和一个终点 
export const caseMazeLinkAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "mazeLink";
  console.log('表单联动-迷宫连线');


  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey !== 'stuQuestionConfigs') return;
  const { id, subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const prevVal = cloneDeep(val);
  // 判断是否有多个8 or 9 
  const startIsMore = val.outerTables.flat().filter((item: number) => item === 8).length > 1;
  const endIsMore = val.outerTables.flat().filter((item: number) => item === 9).length > 1;

  // 判断改动的是不是四个角
  // 将最新的改动抹掉
  if ([8, 9].includes(val.outerTables[2].at(0)) && [8, 9].includes(val.outerTables[3].at(-1))) {
    Message.error('起点/终点不可在同一格子上');
    if (val.outerTables[2].at(0) !== oldVal.outerTables[2].at(0)) {
      val.outerTables[2][0] = ''
    }
    if (val.outerTables[3].at(-1) !== oldVal.outerTables[3].at(-1)) {
      val.outerTables[3][val.outerTables[3].length - 1] = ''
    }
  }
  if ([8, 9].includes(val.outerTables[0].at(-1)) && [8, 9].includes(val.outerTables[1].at(0))) {
    Message.error('起点/终点不可在同一格子上');
    if (val.outerTables[0].at(-1) !== oldVal.outerTables[0].at(-1)) {
      val.outerTables[0][val.outerTables[3].length - 1] = ''
    }
    if (val.outerTables[1].at(0) !== oldVal.outerTables[1].at(0)) {
      val.outerTables[1][0] = ''
    }
  }
  
  if (startIsMore || endIsMore) {
    val.outerTables.forEach((item: any[], index: string | number) => {
      item.forEach((subItem, subIndex) => {
        const targetVal = startIsMore ? 8 : 9;
        if (subItem === targetVal && subItem === oldVal.outerTables[index][subIndex]) { 
          console.log('startIsMore', startIsMore, endIsMore, subItem, targetVal);
          item[subIndex] = '';
        }
      });
    })
  }
  if (!isEqual(val, prevVal)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuQuestionConfigs']: val,
      },
    });
  }
}

export default caseMazeLinkAssemble;
