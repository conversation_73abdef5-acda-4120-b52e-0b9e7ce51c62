import store from "@/pages/index/store";
import { isEqual } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";

export const caseMtConbnationQuestionAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "mtConbnationQuestion";
  console.log('表单联动-榫卯零件拼接');

  const { propertiesKey, val, oldVal } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (val === oldVal) return;
  console.log('caseMtConbnationQuestionAssemble');
  if (propertiesKey === 'stuImageBgCount') {
    store.commit("updateFormItemProps", {
      props: {
        "min": !val ? 2 : val,
        "max": !val ? 2 : val
      },
      key: 'stuPieces',
      subType: subType,
    });
    const defaultItem = {
      'stuAnimationV': {
        'atlas': '',
        'images': [],
        'skeleton': '',
        'cover': '',
        'stuAnimation1': '',
        'stuAnimation2': '',
        'stuAnimation3': '',
        'stuAnimation4': '',
        'stuAnimation5': '',
        'stuAnimation6': '',
        'stuAnimation7': '',
        'stuAnimation8': '',
        'stuAnimation9': '',
        'stuAnimation10': '',
        'stuAnimation11': '',
        'stuAnimation12': ''
      }
    }
    let tempStuPieces = cloneDeep(properties.stuPieces);
    const tempLen = !val ? 2 : val;
    if (tempLen > tempStuPieces.length) {
      // 新增
      tempStuPieces = [...tempStuPieces, ...Array.from({ length: tempLen - tempStuPieces.length }, (item, index) => {
        return cloneDeep(defaultItem);
      })]
    } else {
      tempStuPieces = tempStuPieces.slice(0, tempLen);
    }
    console.log('tempLen', tempLen, tempStuPieces);

    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        stuPieces: tempStuPieces,
      }
    })
  }
}

export const targetCategory = 1163;

export default caseMtConbnationQuestionAssemble;
