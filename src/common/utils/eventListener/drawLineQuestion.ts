import store from "@/pages/index/store";
import cloneDeep from "lodash-es/cloneDeep";
export const caseDrawLineAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "drawLineQuestion";
  console.log('表单联动-连点成画');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseDrawLineAssembly');
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuConnectPointConfigs = compFormConfigs.find((item: any) => item.key === 'stuConnectPointConfigs');
  if (propertiesKey === 'stuConnectPointCount') {
    // 初始化组件时 val:''  oldVal:undefined
    if (val == oldVal) return;
    if (val < 2) return; // 最小值为2, <2时不处理，等待下一次的变化
    // 1. 连接点组件变化
    // 2. 连接点下拉列表变化
    // 3. 动态表单的最大值发生变化 超过最大值的内容执行删除操作
    // 找到所有连接点组件
    const componentMap = store.state.componentMap;
    const options: { label: string; value: any; }[] = [];
    Object.keys(componentMap).forEach((key) => {
      const comp = componentMap[key];
      const compExtra = comp.extra;
      if (comp.type === 'sprite' && (comp.tag == 'linkPoint')) {
        options.push({ label: `连接点${compExtra.tagIndex}`, value: comp.id });
      }
    })
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuLinkPointOptions']: options,
        ['stuConnectPointConfigs']: properties.stuConnectPointConfigs.slice(0, val) // // 删除后面的几项
      },
    });

    // 修改动态表单的最大值
    store.commit("updateFormItemProps", {
      props: {
        "max": val,
      },
      key: 'stuConnectPointConfigs',
      subType: subType,
    });

  }
  // 连接点起点变化--影响disabledValues
  if (propertiesKey === 'start') {
    if (oldVal === undefined) return;
    if (val == oldVal) return;
    // 影响disabledValues
    const disabledValues: any[] = [];
    properties.stuConnectPointConfigs.forEach((item: any) => {
      disabledValues.push(item.start);
    });
    const newSubFormConfigs = cloneDeep(stuConnectPointConfigs.subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === 'start') {
        item.optionsConfig.disabledValues = disabledValues;
      }
    })
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": newSubFormConfigs,
      },
      key: 'stuConnectPointConfigs',
      subType: subType,
    });
    // 自身的终点
    const newEndPoints: any[] = [];
    properties.stuConnectPointConfigs.forEach((item: any) => {
      if (item.end.includes(val)) {
        newEndPoints.push(item.start);
      }
    })
    // 更新properties
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuConnectPointConfigs']: properties.stuConnectPointConfigs.map((item: any) => {
          if (item.start === val) {
            return {
              ...item,
              end: newEndPoints
            }
          }
          return item;
        })
      }
    })
  }
  // 连接点终点变化后，以该变化为起点的终点，也要变化
  if (propertiesKey === 'end') {
    // 新增一项 不处理
    if (oldVal === undefined) return;
    if (!val.length && !oldVal) return;
    console.log('index', subIndex);
    // 找到新增的点
    const addVal = val.filter((item: any) => !oldVal.includes(item));
    // 找到删除的点
    const delVal = oldVal.filter((item: any) => !val.includes(item));
    const currentPoint = properties.stuConnectPointConfigs[subIndex];
    if (addVal.length) {
      // 找到新增点为起点的终点
      const relativePoint = properties.stuConnectPointConfigs.find((item: { start: any; }) => item.start === addVal[0])
      if (!relativePoint) return;
      let newEndPoints = cloneDeep(relativePoint.end);
      newEndPoints = [...newEndPoints, currentPoint.start];
      // 更新properties
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuConnectPointConfigs']: properties.stuConnectPointConfigs.map((item: any) => {
            if (item.start === addVal[0]) {
              return {
                ...item,
                end: Array.from(new Set(newEndPoints))
              }
            }
            return item;
          })
        }
      });
    }
    if (delVal.length) {
      // 删除起点（delStart）是这个点的连接关系项中的终点里的delStart
      const relativePoint = properties.stuConnectPointConfigs.find((item: { start: any; }) => item.start === delVal[0])
      if (!relativePoint) return;
      let newEndPoints = cloneDeep(relativePoint.end);
      newEndPoints = newEndPoints.filter((item: any) => item !== currentPoint.start)
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuConnectPointConfigs']: properties.stuConnectPointConfigs.map((item: any) => {
            if (item.start === delVal[0]) {
              return {
                ...item,
                end: Array.from(new Set(newEndPoints))
              }
            }
            return item;
          })
        }
      });
    }

  }
  // 连接点配置的数量变少后 影响disabledValues
  if (propertiesKey === 'stuConnectPointConfigs') {
    if (val.length < oldVal.length) {
      const disabledValues: any[] = [];
      properties.stuConnectPointConfigs.forEach((item: any) => {
        disabledValues.push(item.start);
      });
      const newSubFormConfigs = cloneDeep(stuConnectPointConfigs.subFormConfigs);
      newSubFormConfigs.forEach((item: any) => {
        if (item.key === 'start') {
          item.optionsConfig.disabledValues = disabledValues;
        }
      })
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newSubFormConfigs,
        },
        key: 'stuConnectPointConfigs',
        subType: subType,
      });
      // 自身的终点
      const newEndPoints: any[] = [];
      properties.stuConnectPointConfigs.forEach((item: any) => {
        if (item.end.includes(val)) {
          newEndPoints.push(item.start);
        }
      })
    }
  }
  // 替换图片时，修改图片组件的w/h
  if (propertiesKey === "stuPointUrl") {
    if (!oldVal) return;
    const img = new Image();
    img.src = val;
    img.onload = function () {
      // 获取图片组件
      const componentMap = store.state.componentMap;
      Object.keys(componentMap).forEach((key) => {
        const comp = componentMap[key];
        if (comp.type === 'sprite' && (comp.tag == 'linkPoint')) {
          store.commit("updateComponentProperties", {
            id: comp.id,
            newProperties: {
              ['width']: img.width,
              ['height']: img.height,
              ['texture']: val
            }
          })
        }
      })
      // 找到这一项配置
      const stuConnectPointCountConfigs = compFormConfigs.find((item: any) => item.key === 'stuConnectPointCount');
      const copyStuConnectPointCountConfigs = cloneDeep(stuConnectPointCountConfigs);
      copyStuConnectPointCountConfigs.actions.add.properties.texture = val;
      copyStuConnectPointCountConfigs.actions.add.properties.width = img.width;
      copyStuConnectPointCountConfigs.actions.add.properties.height = img.height;
      // 更改配置
      store.commit("updateFormItemProps", {
        props: {
          "actions": copyStuConnectPointCountConfigs.actions
        },
        key: 'stuConnectPointCount',
        subType: subType,
      });
    }
  }
}

export const targetCategory = 1131;

export default caseDrawLineAssembly;
