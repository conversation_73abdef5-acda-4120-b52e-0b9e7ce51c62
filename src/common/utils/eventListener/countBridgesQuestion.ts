import store from "@/pages/index/store";
export const treeBridgeAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "countBridges";
  console.log('表单联动-数桥游戏');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('countBridges', propertiesKey, val, oldVal, subIndex);
  if (propertiesKey === 'stuGrid') {
    const [stuRow, stuCol] = val.split('*');
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuCol']: Number(stuCol) + 1,
        ['stuRow']: Number(stuRow) + 1,
        ['stuTreeData']: Array.from({ length: Number(stuRow) + 1 }, () => Array.from({ length: Number(stuCol) + 1 }, () => (null)))
      },
    });
  }
}

export const targetCategory = 1145;

export default treeBridgeAssemble;
