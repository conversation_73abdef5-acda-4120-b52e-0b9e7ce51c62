import store from "@/pages/index/store";
import { cloneDeep } from "lodash-es";
export const multipleEmptyAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  if (store.state.template.category !== 1164) return;
  const SUBTYPE = "multipleEmptyQuestion";
  console.log('表单联动-多空连答');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties: { stuOptionList } } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('multipleEmptyQuestion', propertiesKey, val, oldVal, subIndex);
  let newStuOptionList = cloneDeep(stuOptionList);
  const altOptions: any[] = [];
  // 二维数组
  if (propertiesKey === 'stuBlankOptions') {
    if (!val) {
      newStuOptionList = [];
    } else if (val.length > oldVal.length) {
      val.forEach((item: any, index: number) => {
        const oldIndex = oldVal.findIndex((oldItem: any) => oldItem.label === item.label);
        if (oldIndex === -1) {
          newStuOptionList.splice(index, 0, {
            "layoutType": "1",
            "questionOptions": { "type": "text", "options": [{ "text": "" }, { "text": "" }] }
          })
        }
      })
    } else if (val.length < oldVal.length) {
      // 题目文本的变化加了延时处理 导致删除的数据有可能是多个
      const delIndexes: number[] = [];
      oldVal.forEach((item: any, index: number) => {
        if (!val.find((oldItem: any) => oldItem.label === item.label)) {
          delIndexes.push(index);
        }
      })
      newStuOptionList = newStuOptionList.filter((item: any, index: number) => !delIndexes.includes(index));
    }
  }

  if (propertiesKey === 'questionOptions') {
    newStuOptionList[subIndex].questionOptions = val;
  }

  newStuOptionList.forEach((item: any, index: number) => {
    item.questionOptions.options.forEach((opt: any, sIndex: number) => {
      if (opt.text) {
        altOptions.push({
          value: `${index + 1}-${sIndex + 1}`,
          label: opt.text || `空格${index + 1}选项${sIndex + 1}`,
        })
      }
    })
  })
  // 删除合成关系
  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ['stuOptionList']: newStuOptionList,
    },
  });

  store.commit("updateFormItemProps", {
    props: {
      "min": newStuOptionList.length,
      "max": newStuOptionList.length
    },
    key: 'stuOptionList',
    subType: subType,
  });
}
export const targetCategory = 1164;

export default multipleEmptyAssemble;
