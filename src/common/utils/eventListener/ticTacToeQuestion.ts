/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-04-22 15:53:52
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-05-07 11:02:59
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/ticTacToeQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { cloneDeep, isEqual } from "lodash-es";


export const caseTicTacToeAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "ticTacToe";
  console.log('表单联动-圈叉游戏');

  const checkKetList = ["stuBaseCircleUrl", "stuBaseXUrl", "stuGreyGridUrl", "stuCircleUrl", "stuXUrl"]
  const { propertiesKey, val, oldVal } = data;
  if (checkKetList.indexOf(propertiesKey) < 0) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const { stuOptionsList } = properties;
  const prevVal = cloneDeep(stuOptionsList);

  if (val == undefined || val === "")
    prevVal[checkKetList.indexOf(propertiesKey)].disabled = true;
  else
    prevVal[checkKetList.indexOf(propertiesKey)].disabled = false;


  if (!isEqual(stuOptionsList, prevVal)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuOptionsList']: prevVal,
      },
    });
  }
}

export default caseTicTacToeAssemble;
