import store from "@/pages/index/store";
import { Message } from "element-ui";
import { isEqual } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";
export const caseRiverCrossingAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "riverCrossing";
  console.log('表单联动-安全过河');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuOptionConfigs = compFormConfigs[0].formList.find((item: any) => item.key === 'stuOptionConfigs');
  // 船长动画 stuCaptainList
  // 船员动画 stuCrewList
  // 元素/敌对关系 下拉列表 stuRoleOptions 根据 stuCaptainList 和 stuCrewList 生成
  // 初始位置需要这是disabled
  const props: any = {
    stuCaptainList: properties.stuCaptainList,
    stuCrewList: properties.stuCrewList,
    stuOptionConfigs: properties.stuOptionConfigs
  }
  if (['stuCaptainList', 'stuCrewList'].includes(propertiesKey)) {
    if (val && oldVal && val.length == oldVal.length) return;
    props[propertiesKey] = val;
    const newOptions = props.stuCaptainList.map((_captain: any, index: number) => {
      return {
        label: `船长动画${index + 1}`,
        value: `chapter-${index + 1}`
      }
    }).concat(props.stuCrewList.map((_captain: any, index: number) => {
      return {
        label: `船员动画${index + 1}`,
        value: `member-${index + 1}`
      }
    }));
    // 如果两者之和等于5了， 就把两个的最大/小值改成当前的值 否则最大值为需求的最大值5(船长)和4（船员）
    const sumMax = 5;
    if (newOptions.length === sumMax) {
      store.commit("updateFormItemProps", {
        props: {
          "max": props.stuCaptainList.length,
        },
        key: 'stuCaptainList',
        subType: subType,
      });
      store.commit("updateFormItemProps", {
        props: {
          "max": props.stuCrewList.length,
        },
        key: 'stuCrewList',
        subType: subType,
      });
    } else {
      store.commit("updateFormItemProps", {
        props: {
          "max": sumMax,
        },
        key: 'stuCaptainList',
        subType: subType,
      });
      store.commit("updateFormItemProps", {
        props: {
          "max": sumMax - 1,
        },
        key: 'stuCrewList',
        subType: subType,
      });
    }

    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuRoleOptions']: newOptions
      },
    });
  }
  //  stuCaptainList stuCrewList
  // 元素变化--影响disabledValues
  if (propertiesKey === 'item') {
    if (oldVal === undefined) return;
    if (val == oldVal) return;
    // 影响disabledValues
    const disabledValues: any[] = [];
    properties.stuOptionConfigs.forEach((item: any) => {
      disabledValues.push(item[propertiesKey]);
    });
    const newSubFormConfigs = cloneDeep(stuOptionConfigs.subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === 'item') {
        item.optionsConfig.disabledValues = disabledValues;
      }
    })
    // 判断是否需要更新
    if (!isEqual(newSubFormConfigs, stuOptionConfigs.subFormConfigs)) {
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newSubFormConfigs,
        },
        key: 'stuOptionConfigs',
        subType: subType,
      });
    }
    
    // 其他选项的敌对关系中包含val, 则将其他选项的元素加入到自己的敌对关系中
    const newEndPoints: any[] = [];
    properties.stuOptionConfigs.forEach((item: any) => {
      if (item.relation.includes(val) && item.item !== val) {
        newEndPoints.push(item.item);
      }
    })
    // 更新properties
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuOptionConfigs']: properties.stuOptionConfigs.map((item: any) => {
          if (item.item === val) {
            return {
              ...item,
              relation: newEndPoints
            }
          }
          return item;
        })
      }
    })
  }
  // 初始位置变化影响posDisabledValues
  if (propertiesKey === 'pos') {
    if (val == oldVal) return;
    // 影响disabledValues
    const posDisabledValues: any[] = [];
    properties.stuOptionConfigs.forEach((item: any) => {
      posDisabledValues.push(item[propertiesKey]);
    });
    const newSubFormConfigs = cloneDeep(stuOptionConfigs.subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === propertiesKey) {
        item.optionsConfig.disabledValues = posDisabledValues;
      }
    })
    // 判断是否需要更新
    if (!isEqual(newSubFormConfigs, stuOptionConfigs.subFormConfigs)) {
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newSubFormConfigs,
        },
        key: 'stuOptionConfigs',
        subType: subType,
      });
    }
  }
  // 敌对关系变化后，以该变化为元素的敌对关系，也要变化
  if (propertiesKey === 'relation') {
    // 新增一项 不处理
    if (oldVal === undefined) return;
    if (!val.length && !oldVal) return;
    // console.log('index', subIndex);
    // 找到新增的点
    const addVal = val.filter((item: any) => !oldVal.includes(item));
    // 找到删除的点
    const delVal = oldVal.filter((item: any) => !val.includes(item));
    const currentPoint = properties.stuOptionConfigs[subIndex];
    if (addVal.length) {
      // 如果addVal[0]和当前元素的值一致，需要取消这次更新
      if (addVal[0] === currentPoint.item) {
        Message.error('同一个选项的敌对关系中的值不可和元素重复');
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuOptionConfigs']: properties.stuOptionConfigs.map((item: any) => {
              if (item.item === addVal[0]) {
                return {
                  ...item,
                  relation: item.relation.filter((rel: any) => rel !== addVal[0])
                }
              }
              return item;
            })
          }
        });
        return;
      }
      // 找到新增点为起点的终点
      const relativePoint = properties.stuOptionConfigs.find((item: { item: any; }) => item.item === addVal[0])
      if (!relativePoint) return;
      let newEndPoints = cloneDeep(relativePoint.relation);
      newEndPoints = [...newEndPoints, currentPoint.item];
      // 更新properties
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuOptionConfigs']: properties.stuOptionConfigs.map((item: any) => {
            if (item.item === addVal[0]) {
              return {
                ...item,
                relation: Array.from(new Set(newEndPoints))
              }
            }
            return item;
          })
        }
      });
    }
    if (delVal.length) {
      // 删除起点（delStart）是这个点的连接关系项中的终点里的delStart
      const relativePoint = cloneDeep(properties.stuOptionConfigs).find((item: { item: any; }) => {
        return item.item === delVal[0];
      })
      // console.log('relativePoint', relativePoint);
      if (!relativePoint) return;
      let newEndPoints = cloneDeep(relativePoint.relation);
      newEndPoints = newEndPoints.filter((item: any) => item !== currentPoint.item)
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuOptionConfigs']: properties.stuOptionConfigs.map((item: any) => {
            if (item.item === delVal[0]) {
              return {
                ...item,
                relation: Array.from(new Set(newEndPoints))
              }
            }
            return item;
          })
        }
      });
    }
  }
  // 选项的数量变少后 影响disabledValues
  if (propertiesKey === 'stuOptionConfigs') {
    if (val.length < oldVal.length) {
      const disabledValues: any[] = [];
      const posDisabledValues: any[] = [];
      properties.stuOptionConfigs.forEach((item: any) => {
        disabledValues.push(item.item);
        posDisabledValues.push(item.pos);
      });
      const newSubFormConfigs = cloneDeep(stuOptionConfigs.subFormConfigs);
      newSubFormConfigs.forEach((item: any) => {
        if (item.key === 'item') {
          item.optionsConfig.disabledValues = disabledValues;
        }
        if (item.key === 'pos') {
          item.optionsConfig.disabledValues = posDisabledValues;
        }
      })
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newSubFormConfigs,
        },
        key: 'stuOptionConfigs',
        subType: subType,
      });
      // 自身的终点
      const newEndPoints: any[] = [];
      properties.stuOptionConfigs.forEach((item: any) => {
        if (item.relation.includes(val)) {
          newEndPoints.push(item.item);
        }
      })
    }
  }
}

export const targetCategory = 1173;

export default caseRiverCrossingAssembly;
