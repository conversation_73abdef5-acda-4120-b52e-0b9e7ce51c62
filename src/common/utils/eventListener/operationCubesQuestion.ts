import store from "@/pages/index/store";
export const caseOperationCubesAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "operationCubesQuestion";
  console.log('表单联动-立方体拖放');
  const { propertiesKey, val } = data;
  const { id, subType, properties: { stuCol, stuRow, stuHeight } } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('cube Change');
  const sizeConfigs = {
    stuCol,
    stuRow,
    stuHeight
  }
  sizeConfigs[propertiesKey] = val;
  const newValues = Array(sizeConfigs.stuHeight).fill(0).map(() => {
    return {
      layerConfigs: Array(sizeConfigs.stuRow).fill(0).map(() => {
        return Array(sizeConfigs.stuCol).fill(0).map(() => {
          return 0;
        })
      })
    }
  })

  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ['stuAnswers']: newValues,
      ['stuOperations']: newValues
    },
  });

  store.commit("updateFormItemProps", {
    props: {
      "min": sizeConfigs.stuHeight,
      "max": sizeConfigs.stuHeight
    },
    key: 'stuAnswers',
    subType: subType,
  });
  store.commit("updateFormItemProps", {
    props: {
      "min": sizeConfigs.stuHeight,
      "max": sizeConfigs.stuHeight
    },
    key: 'stuOperations',
    subType: subType,
  });
}

export const targetCategory = 1149;

export default caseOperationCubesAssemble;
