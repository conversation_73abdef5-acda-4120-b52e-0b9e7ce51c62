import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep } from "lodash-es";

const randomItemByWeight = (arr: any[], count: number) => {
  const totalWeight = arr.reduce((prev, curr) => {
    return prev + curr.itemProb;
  }, 0);
  // weight最大的下标
  const maxWeight = Math.max(...arr.map((item) => item.itemProb));
  const maxWeightIndex = arr.findIndex((item) => item.itemProb === maxWeight);
  const perCount = Math.ceil(count / totalWeight);
  const indexArray: number[] = [];
  arr.forEach((item, index) => {
    for (let i = 0; i < item.itemProb * perCount; i++) {
      indexArray.push(index);
    }
  });
  // indexArray中maxWeightIndex的个数
  const maxWeightCount = indexArray.filter((item) => item === maxWeightIndex).length;
  // 第一个maxWeightIndex的下标
  const firstMaxWeightIndex = indexArray.findIndex((item) => item === maxWeightIndex);

  // indexArray中maxWeightIndex的个数不能超过9个
  if (maxWeightCount > 9) {
    indexArray.splice(firstMaxWeightIndex, maxWeightCount - 9)
  }
  // 权重最大的索引值
  // const maxWeightIndex = arr.findIndex((item) => item.weight === maxWeight);

  // 取出n个 > count
  const delta = indexArray.length - count;
  // 从indexArray中取出delta个
  console.log('delta', delta);
  const delRandom = [];
  while (delRandom.length < delta) {
    const random = Math.floor(Math.random() * indexArray.length);
    if (delRandom.indexOf(random) == -1) {
      delRandom.push(random);
      indexArray.splice(random, 1);
    }
  }
  console.log('indexArray', indexArray);

  // 从indexArray中取出6个 至少包含三个数字
  const sliceIndexArray: number[] = [];
  while (sliceIndexArray.length < 6) {
    const tempIndex = indexArray.findIndex((item) => sliceIndexArray.filter((item2) => item2 === item).length < 3);
    sliceIndexArray.push(indexArray[tempIndex]);
    indexArray.splice(tempIndex, 1);
  }
  console.log('sliceIndexArray', sliceIndexArray);
  const res: number[][] = [];
  sliceIndexArray.forEach((item) => {
    // 找到与item不同的数字 随机取 1 or 2 个
    const ableItems = [item];
    const tempIndex1 = indexArray.findIndex((item2) => !ableItems.includes(item2));
    if (tempIndex1 !== -1) {
      ableItems.unshift(indexArray[tempIndex1]);
      indexArray.splice(tempIndex1, 1);
    } else {
      const findPre = res.find((item2) => !item2.includes(item));
      if (findPre) {
        const temp = findPre[0];
        findPre[0] = item;
        item = temp;
        ableItems.push(temp);
        const tempIndex1 = indexArray.findIndex((item2) => item2 === item);
        indexArray.splice(tempIndex1, 1);
      }
    }
    res.push(ableItems);
  });
  // 如果有的列是一个值 那就查找前面的列中，都不存在这个值的情况，换一个到这里 并且不一个当前值
  // 如果indexArray还有内容 
  while (indexArray.length) {
    sliceIndexArray.forEach((item, index) => {
      const tempIndex = indexArray.findIndex((item2) => item2 !== item);
      if (tempIndex !== -1 && res[index].length < 3) {
        res[index].push(indexArray[tempIndex]);
        indexArray.splice(tempIndex, 1);
      }
    })
  }
  res.forEach((item => {
    // item中的值都不相同
    const set = new Set(item);
    if (set.size === item.length) {
      item.sort(() => Math.random() - 0.5);
    }
  }))
  // 随机排序
  res.sort(() => Math.random() - 0.5);
  return res;
}

const caseConversionAssembly = (data: { propertiesKey: string; val: any; oldVal: any, subIndex: number, changeConfig?: { [x: string]: any, tag: string, type: string, extra?: any }; }) => {
  const { propertiesKey, val, oldVal, subIndex, changeConfig } = data;

  if (!val && !oldVal) return;
  const { id, subType, properties: { stuBaseItem, stuItems, stuTargetValue, stuBoomValue, stuItemMaxValue, stuBoomGrayValue } } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== 'conversion') return;
  console.log('...caseConversionAssembly', data);
  console.log('表单联动-换算关系');
  // stuBoomValue 炸开节点值
  // stuBaseItem 基础元素
  // stuItems 相对元素
  // stuBoomValue 炸开节点值
  let tempStuItemMaxValue: string | number = '';
  if (propertiesKey === "itemValue" || (propertiesKey === "stuItems" && val.length < oldVal.length)) {
    // === "" 是新增后第一次change
    if (val == oldVal) return;
    const baseCount = changeConfig && changeConfig.parentKey === 'stuBaseItem' ? val : stuBaseItem[0].itemValue;
    const stuItemsCopy = cloneDeep(stuItems);
    if (propertiesKey === "itemValue") stuItemsCopy[subIndex].itemValue = val;
    const maxCount = Math.max(...stuItemsCopy.map((item: { itemValue: number; }) => item.itemValue));
    if (baseCount && maxCount) {
      tempStuItemMaxValue = baseCount * maxCount;
    }
    if (tempStuItemMaxValue !== Number(stuItemMaxValue)) {
      store.commit("updateFormItemProps", {
        props: {
          "forceUpdate": `${tempStuItemMaxValue}`
        },
        key: 'stuItemMaxValue',
        subType: subType,
      });
      if (stuBoomValue) {
        store.commit("updateFormItemProps", {
          props: {
            "forceUpdate": `${Number(tempStuItemMaxValue) * stuBoomValue}`
          },
          key: 'stuBoomGrayValue',
          subType: subType,
        });
      }
    }
  }
  if (propertiesKey === "stuBoomValue" && val && val !== stuTargetValue) {
    const baseCount = stuBaseItem[0].itemValue;
    const maxCount = Math.max(...stuItems.map((item: { itemValue: number; }) => item.itemValue));
    if (!baseCount || !maxCount) {
      store.commit("updateComponentProperties", {
        id,
        newProperties: {
          stuItemMaxValue: '',
          stuBoomGrayValue: '',
          stuBoomValue: '',
          stuTargetValue: '',
        }
      });
      Message({
        type: "error",
        message: "请先设置基础元素和相对元素",
        duration: 3000,
      });
      return;
    }
    if (val && stuItemMaxValue && stuItemMaxValue * val !== stuBoomGrayValue) {
      store.commit("updateFormItemProps", {
        props: {
          "forceUpdate": `${stuItemMaxValue * val}`
        },
        key: 'stuBoomGrayValue',
        subType: subType,
      });
    }
  }
  // stuGenRandom 随机生成对应的字段
  if (propertiesKey === 'stuGenRandom') {
    // 动态修改正确答案的个数 配置已经value值
    console.log('...caseConversionAssembly', id, subType, stuBaseItem, stuItems);
    // 判断元素设置是否规范
    let valid = true;
    [...stuBaseItem, ...stuItems].forEach((item: any) => {
      console.log('..item', item);
      if (!item.itemValue) {
        valid = false;
      }
      if (!item.itemProb) {
        valid = false;
      }
      if (!item.itemImg) {
        valid = false;
      }
    });
    if (!valid) {
      Message({
        type: "error",
        message: "请先设置元素",
      });
      return;
    }
    // itemValue不可以重复 不可以不存在
    const itemValueArray = stuItems.map((item: { itemValue: any; }) => item.itemValue);
    const itemValueSet = new Set(itemValueArray);
    if (itemValueSet.size !== itemValueArray.length) {
      Message.error("相对数值不可以重复");
      return;
    }
    // stuGenRandom
    const stuPreDatas = randomItemByWeight([...stuBaseItem || [], ...stuItems], 15);
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuPreDatas']: stuPreDatas
      }
    });
  }
}

export const targetCategory = 1127;

export default caseConversionAssembly;
