import store from "@/pages/index/store";
import { cloneDeep, isArray } from "lodash-es";
export const magicPenFlowerAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "magicPenFlowerQuestion";
  console.log('表单联动-妙笔生花');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType, properties: { stuOptionList, stuImageList } } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('magicPenFlower', propertiesKey, val, oldVal, subIndex);
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuOptionListConfigs = compFormConfigs.find((item: any) => item.key === 'stuOptionList');
  const newStuOptionListConfigs = cloneDeep(stuOptionListConfigs);
  const stuImageListConfigs = compFormConfigs.find((item: any) => item.key === 'stuImageList');
  let newStuOptionList = cloneDeep(stuOptionList);
  const altOptions: any[] = [];
  // 二维数组
  const altOptions2Dimension: string[][] = [];
  if (propertiesKey === 'stuImageList' && val.length === oldVal.length) return;
  if (propertiesKey === 'stuBlankOptions') {
    if (!val) {
      newStuOptionList = [];
    } else if (val.length > oldVal.length) {
      val.forEach((item: any, index: number) => {
        const oldIndex = oldVal.findIndex((oldItem: any) => oldItem.label === item.label);
        if (oldIndex === -1) {
          newStuOptionList.splice(index, 0, { "optionList": { "type": "text", "options": [{ "text": "" }] } })
        }
      })
    } else if (val.length < oldVal.length) {
      // 题目文本的变化加了延时处理 导致删除的数据有可能是多个
      const delIndexes: number[] = [];
      oldVal.forEach((item: any, index: number) => {
        if (!val.find((oldItem: any) => oldItem.label === item.label)) {
          delIndexes.push(index);
        }
      })
      newStuOptionList = newStuOptionList.filter((item: any, index: number) => !delIndexes.includes(index));
    }
  }

  if (propertiesKey === 'optionList') {
    newStuOptionList[subIndex].optionList = val;
  }

  if (propertiesKey === 'blankValues' || (propertiesKey === 'stuImageList' && val.length < oldVal.length)) {
    const newStuImageList = cloneDeep(propertiesKey === 'blankValues' ? stuImageList : val);
    if (propertiesKey === 'blankValues') {
      newStuImageList[subIndex].blankValues = val;
    }
    // 获取所有的blankValues 组成dieableValues
    let disabledValues: any[] = [];
    newStuImageList.forEach((item: any) => {
      disabledValues.push(...item.blankValues);
    });
    disabledValues = [...new Set(disabledValues)];
    const newSubFormConfigs = cloneDeep(stuImageListConfigs.subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === 'blankValues') {
        item.optionsConfig.disabledValues = disabledValues;
      }
    })
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": newSubFormConfigs,
      },
      key: 'stuImageList',
      subType: subType,
    });
  }

  newStuOptionList.forEach((item: any, index: number) => {
    const getOption = item.optionList.options.filter((opt: any) => !!opt.text).map((opt: any, sIndex: number) => {
      return {
        value: `${index + 1}-${sIndex + 1}`,
        label: opt.text || `空格${index + 1}选项${sIndex + 1}`,
      }
    });

    altOptions2Dimension.push(getOption);
    item.optionList.options.forEach((opt: any, sIndex: number) => {
      if (opt.text) {
        altOptions.push({
          value: `${index + 1}-${sIndex + 1}`,
          label: opt.text || `空格${index + 1}选项${sIndex + 1}`,
        })
      }
    })
  })
  newStuOptionListConfigs.min = newStuOptionList.length;
  newStuOptionListConfigs.max = newStuOptionList.length;
  // 生成合成关系的备选项options
  // console.log('newStuOptionList', newStuOptionList);
  // console.log('altOptions2Dimension ', altOptions2Dimension);
  // console.log('altOptions', altOptions);
  const imageAltOptions = altOptions2Dimension.length ? altOptions2Dimension.reduce((a: any, b: any) => {
    return a.map((x: string | any[]) => {
      return b.map((y: ConcatArray<string>) => {
        return isArray(x) ? x.concat(y) : [x].concat(y);
      })
    }, []).reduce((a: any, b: any) => {
      return a.concat(b)
    }, []).map((item: any) => {
      return {
        value: item.map((x: any) => {
          return x.value
        }).join(','),
        label: item.map((x: any) => {
          return x.label
        }).join('、')
      }
    })
  }) : [];
  console.log('imageAltOptions', imageAltOptions);
  // 删除合成关系

  if (!['stuImageList', 'blankValues'].includes(propertiesKey)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuOptionList']: newStuOptionList,
        ['stuAltOptions']: imageAltOptions
      },
    });

    store.commit("updateFormItemProps", {
      props: {
        "min": newStuOptionList.length,
        "max": newStuOptionList.length
      },
      key: 'stuOptionList',
      subType: subType,
    });
  }
}

export const targetCategory = 1147;

export default magicPenFlowerAssemble;
