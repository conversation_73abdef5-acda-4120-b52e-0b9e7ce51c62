/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-20 14:13:08
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-08-16 11:10:02
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/gestureUnlockQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";
import { cloneDeep, isEqual } from "lodash-es";


// 迷宫连线 只能有一个起点和一个终点 
export const caseTapFrenzyAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "runningGame";
  console.log('表单联动-室内体育运动');

  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey !== 'stuLevelsList' ) return;
  const { subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const { stuLevelsList } = properties;
  const compFormConfigs = store.state.extData.formConfig[subType];
  const stuLevelsListConfigs = compFormConfigs[1].formList.find((item: any) => item.key === 'stuLevelsList');

  const disabledValues: any[] = [];
  stuLevelsList.forEach((item: any) => {
    disabledValues.push(...disabledValues.concat(item.stuOrder));
  }); 
  const newSubFormConfigs = cloneDeep(stuLevelsListConfigs.subFormConfigs);
  newSubFormConfigs.forEach((item: any) => {
    if (item.key === 'stuOrder') {
      item.optionsConfig.disabledValues = disabledValues;
    }
  })
  // 判断是否需要更新
  if (!isEqual(newSubFormConfigs, stuLevelsListConfigs.subFormConfigs)) {
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": newSubFormConfigs,
      },
      key: 'stuLevelsList',
      subType: subType,
    });
  }
}
export default caseTapFrenzyAssemble;
