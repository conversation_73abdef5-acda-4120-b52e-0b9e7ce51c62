import store from "@/pages/index/store";

const getFullAssembly = (optionLen: number) => {
  const str = [...Array(optionLen)].map((item, index) => String.fromCharCode(65 + index)).join('')
  const arr = [];
  const len = str.length;
  const nbits = 1 << len;
  for (let i = 0; i < nbits; ++i) {
    const temp = [];
    let t = 0;
    for (let j = 0; j < len; j++) {
      t = 1 << j;
      if ((t & i) != 0) {
        temp.push(str[j])
      }
    }
    const tempStr = temp.join('+')
    if (tempStr) {
      arr.push(tempStr)
    }
  }
  arr.sort((a, b) => {
    if (a.length !== b.length) {
      return a.length - b.length;
    } else {
      const aArr = a.split('+');
      const bArr = b.split('+');
      const diffIndex = aArr.findIndex((item, index) => bArr[index] !== item);
      return aArr[diffIndex].charCodeAt(0) - bArr[diffIndex].charCodeAt(0)
    }
  })
  return arr;
}

const casePipeLineAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; }) => {
  console.log('表单联动-组合传送带');
  const { id, subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== 'pipeLine') return;
  console.log('...casePipeLineAssembly', data);
  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey === 'stuOptionConfig') {
    if (oldVal && JSON.stringify(val) === JSON.stringify(oldVal)) return;
    // 动态修改正确答案的个数 配置已经value值
    // if (oldVal.options.length === val.options.length) return;

    const compFormConfigs = store.state.extData.formConfig[subType];
    // 更改formConfig
    if (compFormConfigs && compFormConfigs.length) {
      const answerFormConfigs = compFormConfigs.find((item: FormItemConfigs) => item.key === 'stuAnswerConfigs')
      if (answerFormConfigs) {
        const answerLabels = getFullAssembly(val.options.length);
        // console.log('pipe..', oldVal.options, val.options, answerLabels);
        // 更改formConfig
        store.commit("updateFormItemProps", {
          props: {
            "min": answerLabels.length,
            "max": answerLabels.length,
            "orderConfig": {
              ...answerFormConfigs.orderConfig,
              args: {
                optionLen: val.options.length
              }
            }
          },
          key: 'stuAnswerConfigs',
          subType: subType,
        });
        if (oldVal === undefined) return;
        // 更改val
        const newConfigs = answerLabels.map((item) => {
          const itemArr = item.split('+');
          // const findItem = stuAnswerConfigs.find((config: { label: string; }) => config.label === item) ;
          return {
            "label": item,
            "url": "",
            "option": itemArr.map((key: string) => key.charCodeAt(0) - 65)
          }
        })
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuAnswerConfigs']: newConfigs,
          },
        });
      }
    }
  }
}

export const targetCategory = 1119;

export default casePipeLineAssembly;
