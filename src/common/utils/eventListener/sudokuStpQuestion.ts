import store from "@/pages/index/store";
import { cloneDeep } from "lodash-es";
export const sudokuStpQuestionAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "sudokuStpQuestion";
  console.log('表单联动-摩天楼数独');
  const { propertiesKey, val, oldVal, subIndex } = data;
  const { id, subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('sudokuStpQuestionAssembly', propertiesKey, val, oldVal, subIndex);
  const compFormConfigs = store.state.extData.formConfig[subType];
  const [stuRow, stuCol] = val.split('*');
  const newTableDatas = {
    innerTables: Array.from({ length: Number(stuRow) }, () => Array.from({ length: Number(stuCol) }, () => (null))),
    outerTables: Array.from({ length: 4 }, (item, index) => {
      let len = Number(stuCol);
      if (index % 2) {
        len = Number(stuRow);
      }
      return Array.from({ length: len }, () => (null));
    })
  }
  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ['stuCol']: Number(stuCol),
      ['stuRow']: Number(stuRow),
      ['stuPalaceGridData']: newTableDatas,
      ['stuQuestion']: newTableDatas
    },
  });
  // min max sameColorCellNumber

  // const compFormConfigs = store.state.extData.formConfig[subType];
  // const stuPalaceGridFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuPalaceGridData');
  // const stuQuestionFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuQuestion');
  store.commit("updateFormItemProps", {
    props: {
      "col": Number(stuCol),
      "row": Number(stuRow),
      "sameColorCellNumber": Math.min(Number(stuCol), Number(stuRow))
    },
    key: 'stuPalaceGridData',
    subType: subType,
  });
  store.commit("updateFormItemProps", {
    props: {
      "col": Number(stuCol),
      "row": Number(stuRow),
      "max": Math.max(Number(stuCol), Number(stuRow))
    },
    key: 'stuQuestion',
    subType: subType,
  });
  // stuIsCheckGird
  const stuIsCheckGirdFormConfigs = compFormConfigs.find((item: any) => item.key === 'stuIsCheckGird');
  if (stuIsCheckGirdFormConfigs) {
    const copyStuIsCheckGirdFormConfigs = cloneDeep(stuIsCheckGirdFormConfigs);
    copyStuIsCheckGirdFormConfigs.options[0].associatedForm[0].value = {
      innerTables: newTableDatas.innerTables
    };
    copyStuIsCheckGirdFormConfigs.options[0].associatedForm[0].col = Number(stuCol)
    copyStuIsCheckGirdFormConfigs.options[0].associatedForm[0].row = Number(stuRow)
    copyStuIsCheckGirdFormConfigs.options[0].associatedForm[0].sameColorCellNumber = Math.min(Number(stuCol), Number(stuRow))
    store.commit("updateFormItemProps", {
      props: {
        "options": copyStuIsCheckGirdFormConfigs.options
      },
      key: 'stuIsCheckGird',
      subType: subType,
    });
  }

}

export const targetCategory = 1144;

export default sudokuStpQuestionAssembly;
