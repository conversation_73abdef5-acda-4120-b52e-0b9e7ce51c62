/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-05-28 16:29:51
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-06-26 11:04:16
 * @FilePath: /interactive-question-editor/src/common/utils/eventListener/questQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { cloneDeep, isEqual } from "lodash-es";


export const caseQuestAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "followRecordManyTimes";
  console.log('表单联动-单词读三遍');
  const { propertiesKey, val, oldVal } = data;
  if (propertiesKey !== "stuRecordTimes") return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (isEqual(val, oldVal)) return;
  const {stuMicropTip } = properties;
  const prevVal = cloneDeep(stuMicropTip);
  //当前数据长度小于新数据长度
  if (prevVal.length < val) {
    for (let i = prevVal.length; i < val; i++) {
      if (i != 0) {
        const item = cloneDeep(prevVal[i - 1])
        item.text = "";
        prevVal.push(item);
      }
      else {
        prevVal.push({ text: "" });
      }
    }
  }
  else {
    for (let i = prevVal.length - 1; i >= val; i--)
      prevVal.pop();
  }

  if (!isEqual(stuMicropTip, prevVal)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuMicropTip']: prevVal,
      },
    });
    store.commit("updateFormItemProps", {
      props: {
        "min": val,
        "max": val
      },
      key: 'stuMicropTip',
      subType: subType,
    });
  }
}

export default caseQuestAssemble;
