import store from "@/pages/index/store";
import { isEqual } from "lodash-es";
import cloneDeep from "lodash-es/cloneDeep";
export const caseMathLinkUp = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "mathLinkUp";

  const { propertiesKey, val, oldVal } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('表单联动-算式连连看');
  const compFormConfigs = store.state.extData.formConfig[subType];
  const props: any = {
    stuItemTextList: properties.stuItemTextList,
    stuRow: 3,
    stuCol: 4,
  }
  // oldVal val 题板规格发生变化时
  if (['stuBoardType'].includes(propertiesKey)) {
    const stuBoardTypeFormConfigsOptions = compFormConfigs[0].formList.find((item: any) => item.key === 'stuBoardType').options;
    stuBoardTypeFormConfigsOptions.forEach((opt: any) => {
      if (opt.value === val) {
        opt.changeProps.forEach((propConfig: any) => {
          if (['stuRow', 'stuCol'].includes(propConfig.targetKey)) {
            props[propConfig.targetKey] = propConfig.props.value;
          }
        })
      }
    })
    const newMax = props.stuRow * props.stuCol / 2;
    const newOptions = cloneDeep(compFormConfigs[0].formList.find((item: any) => item.key === 'stuGameMode').options);
    let textSubFormConfigs: any[] = [];
    let imgSubFormConfigs: any[] = [];
    newOptions.forEach((opt: any) => {
      opt.associatedForm.forEach((item: any) => {
        // stuItemTextList是文本类型的对数  stuItemImgList是图片类型的对数
        if (['stuItemTextList', 'stuItemImgList'].includes(item.key)) {
          if ('stuItemTextList' === item.key) {
            textSubFormConfigs = item.subFormConfigs;
          }
          if ('stuItemImgList' === item.key) {
            imgSubFormConfigs = item.subFormConfigs;
          }
          item.max = newMax;
          item.subFormConfigs.forEach((subItem: any) => {
            if (['count', 'textList'].includes(subItem.key)) {
              subItem.max = newMax;
            }
          })
        }
      })
    })
    store.commit("updateFormItemProps", {
      props: {
        "options": newOptions
      },
      key: 'stuGameMode',
      subType: subType,
    });
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": textSubFormConfigs
      },
      key: 'stuItemTextList',
      subType: subType,
    });
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": imgSubFormConfigs
      },
      key: 'stuItemImgList',
      subType: subType,
    });
    if (val === "0") {
      const newStuDynamicList = cloneDeep(props.stuItemTextList);
      newStuDynamicList.forEach((item: { textList: { options: string | any[]; }; }) => {
        if (item.textList.options.length > newMax) {
          item.textList.options = item.textList.options.slice(0, newMax)
        }
      });
      if (!isEqual(newStuDynamicList, properties['stuItemTextList'])) {
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            'stuItemTextList': newStuDynamicList
          }
        })
      }
    }
  }
  // 元素配置发生变化时 清空生成的题面
  if (['stuItemTextList', 'stuItemImgList'].includes(propertiesKey)) {
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        'stuGenQuestion': []
      }
    })
  }
}

export const targetCategory = 1166;

export default caseMathLinkUp;
