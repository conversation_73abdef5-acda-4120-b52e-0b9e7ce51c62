import store from "@/pages/index/store";
const caseCluesAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "clues";
  console.log('表单联动-见微知著');
  const { propertiesKey } = data;
  const { subType } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('caseCluesAssembly');
  if (propertiesKey === 'stuImageCount') {
    const { componentMap } = store.state;
    const url: string[] = [];
    let theComp: Component = null;
    Object.values(componentMap).forEach((item: any) => {
      if (item.tag === "hotImage" && item.extra) {
        url.push(item.extra.targerUrl || "")
      }
      if (item.subType === 'clues') {
        theComp = item;
      }
    });
    if (theComp) {
      store.commit("updateComponentProperties", {
        id: theComp.id,
        newProperties: {
          ['stuTargetItems']: url
        },
      });
    }

  }
}

export const targetCategory = 1134;

export default caseCluesAssembly;
