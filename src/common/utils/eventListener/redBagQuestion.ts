import store from "@/pages/index/store";

export const caseRedBagAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "redBag";
  console.log('表单联动-帮帮红包雨');
  const { propertiesKey, val, oldVal } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  if (val === oldVal) return;
  if (propertiesKey === 'stuStartElementCount') {
  // stuStartElementCount变化时，修改stuEndElementCount的最小值
    store.commit("updateFormItemProps", {
      props: {
        "min": !val ? 1 : val
      },
      key: 'stuEndElementCount',
      subType: subType,
    });
    if (val >= properties.stuEndElementCount) {
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          stuEndElementCount: '',
        }
      })
    }
  }
}

export const targetCategory = 1156;

export default caseRedBagAssemble;
