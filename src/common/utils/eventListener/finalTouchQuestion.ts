import bus from "@/pages/index/common/utils/bus";
import store from "@/pages/index/store";
import { cloneDeep, isEqual } from "lodash-es";

const getBlackComponentIds = (comp: any): string[] => {
  const { properties } = comp;
  // 存放组件id的属性名
  const relativeKey = 'stuStageCompIds';
  // 点睛时机对应的索引值
  const relativeIndex = 1;
  return Object.values(properties[relativeKey][relativeIndex])
}
const handleComponentLengthChange = (data: {
  components: {
    properties: any; id?: any; type?: any; subType?: any;
  }[];
}) => {
  console.log('on componentLengthChange', data);
  // 文本（label） 新文本（richTextSprite）富文本（h5Label） 图片（sprite） 动效（spine） 公式（formula） 形状shape 点睛阶段不包含
  const finalTouchComp = data.components.find(comp => comp.subType === 'finalTouch');

  const whiteComponentTypes = ['label', 'richTextSprite', 'h5Label', 'sprite', 'spine', 'formula', 'shape'];
  const blackComponentIds = getBlackComponentIds(finalTouchComp);

  const options: { label: string; value: any; }[] = [];
  data.components.forEach((comp: { id?: any; type?: any; subType?: any; }) => {
    const { type, subType, id } = comp;
    if ((whiteComponentTypes.includes(type) || whiteComponentTypes.includes(subType)) && !blackComponentIds.includes(id)) {
      options.push({
        label: `ID${comp.id}`,
        value: comp.id
      })
    }
  })
  
  // 判断是否需要更新 stuComponentOptions
  if (finalTouchComp && !isEqual(finalTouchComp?.properties.stuComponentOptions, options)) {
    const newProp = {
      stuComponentOptions: options,
      stuTouchId: finalTouchComp.properties.stuTouchId,
      stuTouchCompType: finalTouchComp.properties.stuTouchCompType,
      stuTouchSpine: finalTouchComp.properties.stuTouchSpine,
    }
    // 判断是否需要更新 stuTouchId 关联的组件被删除后才加载这个组件
    if (newProp.stuTouchId && !options.find(opt => opt.value === newProp.stuTouchId)) {
      newProp.stuTouchId = ''
      newProp.stuTouchCompType = ''
      newProp.stuTouchSpine = undefined
    }
    // console.log('options...stuComponentOptions', options);
    store.commit("updateComponentProperties", {
      id: finalTouchComp.id,
      newProperties: newProp
    })
  }
  
}
// 加载完毕时
handleComponentLengthChange({ components: Object.values(store.state.componentMap) });
// 监听到组件数量变化时 只有加载了学能组件才可以监听
bus.$on('componentLengthChange', (data: { components: {
  properties: any; id?: any; type?: any; subType?: any; 
}[]; }) => {
  handleComponentLengthChange(data);
})
// 迷宫连线 只能有一个起点和一个终点 
export const caseFinalTouchQuestionAssemble = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "finalTouch";
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('表单联动-点睛之笔');
  const { propertiesKey, val } = data;
  const cloneProps = cloneDeep(properties);
  const willChangeProps = {
    stuTouchSpine: cloneProps.stuTouchSpine,
    stuTouchCompType: cloneProps.stuTouchCompType
  }
  if (propertiesKey === 'stuTouchId') {
    // val 获取组件对应的数值 修改属性  stuTouchCompType stuTouchSpine {skeleton}
    const touchComp = store.state.componentMap[val] || { type: '' };
    willChangeProps.stuTouchCompType = touchComp.type;
    if (touchComp.type === 'spine') {
      willChangeProps.stuTouchSpine = {
        skeleton: touchComp.spineData.skeleton,
        atlas: touchComp.spineData.atlas
      }
    } else {
      willChangeProps.stuTouchSpine = undefined;
    }
  }
  const diffProps: any = {};
  Object.keys(willChangeProps).forEach((key) => {
    if (!isEqual(willChangeProps[key], properties[key])) {
      diffProps[key] = willChangeProps[key];
    }
  })
  // console.log('diffProps', diffProps, 'will commit updateComponentProperties');
  store.commit("updateComponentProperties", {
    id: id,
    newProperties: {
      ...diffProps,
    }
  })
}
export default caseFinalTouchQuestionAssemble;
