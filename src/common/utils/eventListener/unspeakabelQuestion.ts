import store from "@/pages/index/store";

// 旋转密码板
const caseUnspeakabelAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; }) => {
  const SUBTYPE = "Unspeakabel";
  console.log('表单联动-旋转密码板');
  const spanType = {
    1: 24,
    2: 12,
    3: 8,
    4: 6,
    5: 4,
    6: 4,
    7: 3,
  }
  const offsetType = {
    3: 0,
    4: 0,
    5: 2,
    7: 1,
  }
  const { propertiesKey, val, oldVal } = data;
  if (!oldVal) return;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  const { stuAnswerCol, stuAnswerRow } = properties;
  if (subType !== SUBTYPE) return;
  console.log('...caseUnspeakabelAssembly', data);

  // stuCol stuAnswerCol stuRow stuAnswerRow stuQuestion stuMask stuAnswer
  if (propertiesKey === 'stuCol') {
    const compFormConfigs = store.state.extData.formConfig[subType];
    // 更改formConfig
    if (compFormConfigs && compFormConfigs.length) {
      const newStuQuestionSubFormConfigs = [];
      const newStuMaskSubFormConfigs = [];
      const newStuQuestions = [{}];
      const newStuMask = [{}];
      for (let i = 0; i < val * val; i++) {
        i % val === 0
        newStuQuestionSubFormConfigs[i] = {
          "key": `${i}`,
          "value": "",
          "formItemType": "BaseInput",
          "span": spanType[val],
          "validation": "^[\u4e00-\u9fa5a-zA-Z0-9]{0,2}$",
          "offset": i % val === 0 ? offsetType[val] : 0,
          "rules": [
            {
              "required": true,
              "message": "请输入内容",
              "trigger": "blur"
            }
          ]
        }
        newStuMaskSubFormConfigs[i] = {
          "formItemType": "BaseTag",
          "key": `${i}`,
          "label": "",
          "color": "#ffffff",
          "active": {
            "color": "#909399"
          },
          "span": spanType[val],
          "offset": i % val === 0 ? offsetType[val] : 0,
          "value": ""
        }
        newStuQuestions[0][`${i}`] = "";
        newStuMask[0][`${i}`] = 1;
      }
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newStuQuestionSubFormConfigs,
        },
        key: 'stuQuestion',
        subType: subType,
      });
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newStuMaskSubFormConfigs,
        },
        key: 'stuMask',
        subType: subType,
      });
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuQuestion']: newStuQuestions,
          ['stuMask']: newStuMask,
          ['stuRow']: val,
        },
      });
    }
  }
  if (['stuAnswerCol', 'stuAnswerRow'].includes(propertiesKey)) {
    const compFormConfigs = store.state.extData.formConfig[subType];
    // 更改formConfig
    if (compFormConfigs && compFormConfigs.length) {
      const newAnswerSubFormConfigs = [];
      const newStuAnswer = [{}];
      for (let i = 0; i < stuAnswerCol * stuAnswerRow; i++) {
        i % val === 0
        newAnswerSubFormConfigs[i] = {
          "key": `${i}`,
          "value": "",
          "formItemType": "BaseInput",
          "span": spanType[stuAnswerRow],
          "validation": "^[\u4e00-\u9fa5a-zA-Z0-9]{0,2}$",
          "offset": i % stuAnswerRow === 0 ? offsetType[stuAnswerRow] : 0
        }
        newStuAnswer[0][`${i}`] = "";
      }
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newAnswerSubFormConfigs,
        },
        key: 'stuAnswer',
        subType: subType,
      });
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          ['stuAnswer']: newStuAnswer
        },
      });
    }
  }
}

export const targetCategory = 1135;

export default caseUnspeakabelAssembly;
