import store from "@/pages/index/store";
import { Message } from "element-ui";
import cloneDeep from "lodash-es/cloneDeep";
/**
 * 齿轮升级（2023/10/16上线）说明：
 * 1. 大中小齿轮的大小和尺寸发生了变化
 */

const { tempVersion } = store.state.template || "0";
const versionMap= {
  "0": new Map([
    [1, {
      "width": 94,
      "height": 94,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_507e8f521bf80bd36cd420721c8da697.png"
    }],
    [2, {
      "width": 132,
      "height": 132,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_fa203101c5af8780de6fff9855cd25e5.png"
    }],
    [3, {
      "width": 170,
      "height": 170,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_d9b03fe38e69b6a63dd03f00f6c7b62f.png",
    }]
  ]),
  "1": new Map([
    [1, {
      "width": 112,
      "height": 112,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_99efd81094001b12931d28ff1540a29b.png"
    }],
    [2, {
      "width": 192,
      "height": 192,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_5c105ae686031862dce540933ef5cc54.png"
    }],
    [3, {
      "width": 288,
      "height": 288,
      "texture": "https://jiaoyanbos.cdnjtzy.com/cw_61b1e24cd22f169774ac371aa24207a3.png",
    }]
  ])
}

const driveMap = versionMap[tempVersion || "0"];

function findComponentIds(changeConfig: { tag: string, type: string, extra?: any }) {
  const { tag, type, extra } = changeConfig;
  const componentMap = store.state.componentMap;
  const ids: string[] = [];
  Object.keys(store.state.componentMap).forEach((key: string) => {
    const comp = componentMap[key];
    const compExtra = comp.extra;
    let extraEqual = true;
    extra && Object.keys(extra).forEach((key: string) => {
      if (extra[key] !== compExtra[key]) {
        extraEqual = false;
      }
    })
    if (comp.type === type && (tag && comp.tag == tag) && extraEqual) {
      ids.push(key);
    }
  })
  return ids;
}

export const caseGearAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; }) => {
  console.log('表单联动-齿轮');
  const { propertiesKey, val, oldVal, changeConfig } = data;
  if (oldVal === undefined) return;

  if (changeConfig) {
    const ids = findComponentIds(changeConfig);
    // 删除 新增
    if (propertiesKey === 'driveGearType') {
      const preComp = cloneDeep(store.state.componentMap[ids[0]]);
      Object.assign(preComp.properties, driveMap.get(val));
      preComp.extra.gearType = val;
      // 删除
      store.dispatch("removeComponents", ids);
      // 新增
      store.dispatch("addComponentNoFocus", preComp);
    }
    if (propertiesKey === 'driveGearDirection') {
      store.commit("updateComponentExtra", {
        id: ids[0],
        newExtra: {
          direction: val
        },
      });
    }
  } else {
    // changeConfig不存在时 自定义
    if (propertiesKey.includes('movableGear')) {
      const comp = store.state.componentMap[store.state.currentComponentIds[0]];
      let total = 0;
      ['movableGear1', 'movableGear2', 'movableGear3'].forEach((key: string) => {
        total += key !== propertiesKey ? comp.properties[key] : val;
      })
      if(tempVersion !== "1") {
        const max = 6;
        if (total > max) {
          Message({
            type: "error",
            message: `待拖拽区齿轮数量不可超过${max}个`,
            duration: 3000,
          });
          store.commit("updateComponentProperties", {
            id: comp.id,
            newProperties: {
              [propertiesKey]: oldVal
            }
          });
        }
      }
      
      const min = 1;
      
      if (total < min) {
        Message({
          type: "error",
          message: "待拖拽区齿轮数量不可少于1个",
          duration: 3000,
        });
        store.commit("updateComponentProperties", {
          id: comp.id,
          newProperties: {
            [propertiesKey]: oldVal
          }
        });
      }
    }
  }
}

export const targetCategory = 1117;

export default caseGearAssembly;
