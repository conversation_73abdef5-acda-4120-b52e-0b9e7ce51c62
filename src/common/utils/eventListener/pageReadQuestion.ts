import { compManagerUtil } from "@/pages/index/common/utils/compManagerUtil";
import store from "@/pages/index/store";
import { cloneDeep } from "lodash-es";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";

export const casePageReadQuestionAssemble = async (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "pageRead";
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  console.log('表单联动-翻页阅读');
  const { propertiesKey, val, oldVal } = data;
  const cloneProps = cloneDeep(properties);
  if (!['stuStageCompIds', 'randomNum'].includes(propertiesKey)) return;
  const subQuestionIndex = cloneProps.subQuestionIndex
  let changeType = '';
  let relativeH5LabelId = '';
  if (propertiesKey === "stuStageCompIds" && val.length !== oldVal.length) {
    console.log('subQuestionIndex', subQuestionIndex);
    // 判断是新增还是删除还是修改
    changeType = val.length > oldVal.length ? 'add' : val.length < oldVal.length ? 'delete' : '';
  }
  if (propertiesKey === "randomNum") {
    console.log('subQuestionIndex', subQuestionIndex);
    // 判断是新增还是删除还是修改
    changeType = 'update';
  }
  if (!changeType) return;
  console.log('changeType', changeType);
  if (changeType === 'delete') {
    // 找到被删除的组件id
    const deleteItem = oldVal.find((item: any) => {
      return !val.find((item2: any) => {
        return item.relativeH5LabelId === item2.relativeH5LabelId;
      })
    });
    console.log('deleteItem', deleteItem);
    if (!deleteItem) return;
    // 删除关联的富文本组件
    const relativeH5LabelId = deleteItem.relativeH5LabelId;
    if (relativeH5LabelId) {
      store.dispatch("removeComponent", relativeH5LabelId);
    }
  } else if (changeType === 'update' && properties.stuStageCompIds[subQuestionIndex].relativeH5LabelId) {
    // 更新关联的富文本组件
    relativeH5LabelId = properties.stuStageCompIds[subQuestionIndex].relativeH5LabelId;
    if (relativeH5LabelId) {
      const H5LabelComponentEditor = await compManagerUtil.registerComponent('h5Label')
      console.log('H5LabelComponentEditor', H5LabelComponentEditor);
      (H5LabelComponentEditor as any).editById(relativeH5LabelId).then((res: { id: string; data: { formData: string; labelPicList: string[]; }; }) => {
        console.log('res', res);
        store.commit("updateComponentProperties", {
          id: relativeH5LabelId,
          newProperties: {
            customH5label: res.data.formData,
            labelPicList: res.data.labelPicList,
          }
        })
      })
    }
  } else {
    // 获取新增的组件id
    relativeH5LabelId = store.getters.newId; // 新增的组件id
    const H5LabelComponentEditor = await compManagerUtil.registerComponent('h5Label')
    console.log('H5LabelComponentEditor', H5LabelComponentEditor);
    (H5LabelComponentEditor as any).createById(relativeH5LabelId).then((res: { id: string; data: { formData: string; labelPicList: string[]; }; }) => {
      console.log('res', res);
      const component: H5LabelComponent = {
        id: res.id,
        type: "specialComponent",
        subType: SpecialComponentSubTypes.H5LABEl,
        tag: "subOptionComponent",
        "canCombine": false,
        "deletable": false,
        "dragable": false,
        "editable": {
          "properties": {
            "width": false,
            "height": false,
            "opacity": false,
            "x": false,
            "y": false,
            "angle": false
          }
        },
        extra: {
          tag: "subOptionComponent",
          relativeSubType: "pageRead"
        },
        properties: {
          active: true,
          width: 780,
          height: 420,
          x: -100,
          y: 20,
          customH5label: res.data.formData,
          labelPicList: res.data.labelPicList,
        },
      };
      store.dispatch("addComponentNoFocus", component);
      // 关联id
      const tempStuStageCompIds = cloneDeep(cloneProps.stuStageCompIds);
      tempStuStageCompIds[subQuestionIndex].relativeH5LabelId = res.id;
      console.log('tempStuStageCompIds', id, tempStuStageCompIds);
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          stuStageCompIds: tempStuStageCompIds,
        }
      })
    }).catch(() => {
      // 创建富文本失败时 删除这个小题
      const tempStuStageCompIds = cloneDeep(cloneProps.stuStageCompIds);
      tempStuStageCompIds.splice(subQuestionIndex, 1);
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          subQuestionIndex: subQuestionIndex - 1,
          stuStageCompIds: tempStuStageCompIds,
        }
      })
    });
  }
}
export default casePageReadQuestionAssemble;
