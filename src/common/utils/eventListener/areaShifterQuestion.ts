import store from "@/pages/index/store";
import cloneDeep from "lodash-es/cloneDeep";
export const caseDrawLineAssembly = (data: { propertiesKey: string; val: any; oldVal: any, changeConfig?: { tag: string, type: string, extra?: any }; subIndex: number }) => {
  const SUBTYPE = "areaShifter";
  console.log('表单联动-等积变形');
  const { propertiesKey, val, oldVal, subIndex, changeConfig } = data;
  const { id, subType, properties } = store.state.componentMap[store.state.currentComponentIds[0]];
  if (subType !== SUBTYPE) return;
  
  const compFormConfigs: FormSchema = (store.state.extData.formConfig as any)[subType];
  const stuConnectPointConfigs = (compFormConfigs[0] as CollapseForm).formList.find((item: any) => item.key === 'stuConnectPointConfigs');
  const stuFixedConnectPointConfigs = (compFormConfigs[0] as CollapseForm).formList.find((item: any) => item.key === 'stuFixedConnectPointConfigs');
  if (!stuConnectPointConfigs || !stuFixedConnectPointConfigs) return;
  if (['stuConnectPointCount', 'stuFixedConnectPointCount'].includes(propertiesKey) ) {
    const relativeOptionsKey = propertiesKey === 'stuConnectPointCount' ? 'stuLinkPointOptions' : 'stuFixedLinkPointOptions';
    const relativeConfigsKey = propertiesKey === 'stuConnectPointCount' ? 'stuConnectPointConfigs' : 'stuFixedConnectPointConfigs';
    const tagVal = propertiesKey === 'stuConnectPointCount' ? 'linkPoint' : 'fixedLinkPoint';
    const tagPrefix = propertiesKey === 'stuConnectPointCount' ? '可移动连接点' : '不可移动连接点';
    // 初始化组件时 val:''  oldVal:undefined
    if (val == oldVal) return;
    if (val < 1) return; // 最小值为1, <1时不处理，等待下一次的变化
    // 1. 连接点组件变化
    // 2. 连接点下拉列表变化
    // 3. 动态表单的最大值发生变化 超过最大值的内容执行删除操作
    // 找到所有连接点组件
    const componentMap = store.state.componentMap;
    const options: { label: string; value: any; }[] = [];
    Object.keys(componentMap).forEach((key) => {
      const comp = componentMap[key];
      const compExtra = comp.extra;
      if (comp.type === 'sprite' && (comp.tag == tagVal)) {
        options.push({ label: `${tagPrefix}${compExtra.tagIndex}`, value: comp.id });
      }
    })
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        [relativeOptionsKey]: options,
        ['stuAllLinkPointOptions']: [
          ...options,
          ...properties[propertiesKey === 'stuConnectPointCount' ? 'stuFixedLinkPointOptions' : 'stuLinkPointOptions'] || []
        ],
        [relativeConfigsKey]: properties[relativeConfigsKey].slice(0, val) // // 删除后面的几项
      },
    });

    // 修改动态表单的最大值
    store.commit("updateFormItemProps", {
      props: {
        "max": val,
        "min": val
      },
      key: relativeConfigsKey,
      subType: subType,
    });
    // 判断是新增还是删除
    if (val > properties[relativeConfigsKey].length) {
      // 新增
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          [relativeConfigsKey]: [...properties[relativeConfigsKey], ...new Array(val - properties[relativeConfigsKey].length).fill({
            "start": "",
            "end": [],
          })
          ]
        },
      });
    } else {
      // 删除
      store.commit("updateComponentProperties", {
        id: id,
        newProperties: {
          [relativeConfigsKey]: properties[relativeConfigsKey].slice(0, val) // 删除后面的几项
        },
      });
    }

  }
  // 连接点起点变化--影响disabledValues
  if (propertiesKey === 'start') {
    if (oldVal === undefined) return;
    if (val == oldVal) return;
    const relativeConfigsKey = changeConfig?.tag === 'linkPoint' ? 'stuConnectPointConfigs' : 'stuFixedConnectPointConfigs';
    // 影响disabledValues
    const disabledValues: any[] = [];
    properties[relativeConfigsKey].forEach((item: any) => {
      disabledValues.push(item.start);
    });
    const newSubFormConfigs = cloneDeep(changeConfig?.tag === 'linkPoint' ? stuConnectPointConfigs.subFormConfigs : stuFixedConnectPointConfigs.subFormConfigs);
    newSubFormConfigs.forEach((item: any) => {
      if (item.key === 'start') {
        item.optionsConfig.disabledValues = disabledValues;
      }
    })
    store.commit("updateFormItemProps", {
      props: {
        "subFormConfigs": newSubFormConfigs,
      },
      key: relativeConfigsKey,
      subType: subType,
    });
    // 自身的终点
    const newEndPoints: any[] = [];
    properties['stuConnectPointConfigs'].forEach((item: any) => {
      if (item.end.includes(val)) {
        newEndPoints.push(item.start);
      }
    })
    const newFixedEndPoints: any[] = [];
    properties['stuFixedConnectPointConfigs'].forEach((item: any) => {
      if (item.end.includes(val)) {
        newFixedEndPoints.push(item.start);
      }
    })
    // 更新properties
    store.commit("updateComponentProperties", {
      id: id,
      newProperties: {
        ['stuConnectPointConfigs']: properties['stuConnectPointConfigs'].map((item: any) => {
          if (item.start === val) {
            return {
              ...item,
              end: newEndPoints
            }
          }
          return item;
        }),
        ['stuFixedConnectPointConfigs']: properties['stuFixedConnectPointConfigs'].map((item: any) => {
          if (item.start === val) {
            return {
              ...item,
              end: newFixedEndPoints
            }
          }
          return item;
        })
      }
    })
  }
  // 连接点终点变化后，以该变化为起点的终点，也要变化
  if (propertiesKey === 'end') {
    // 新增一项 不处理
    if (oldVal === undefined) return;
    if (!val.length && !oldVal) return;
    console.log('index', subIndex);
    const relativeConfigsKey = changeConfig?.tag === 'linkPoint' ? 'stuConnectPointConfigs' : 'stuFixedConnectPointConfigs';
    // 找到新增的点
    const addVal = val.filter((item: any) => !oldVal.includes(item));
    // 找到删除的点
    const delVal = oldVal.filter((item: any) => !val.includes(item));
    const currentPoint = properties[relativeConfigsKey][subIndex];
    let targetVal = 0;
    if (addVal.length) {
      targetVal = addVal[0]
    }
    if (delVal.length) {
      targetVal = delVal[0]
    }
    if (targetVal) {
      // 找到新增点为起点的终点
      const relativePoint = properties['stuConnectPointConfigs'].find((item: { start: any; }) => item.start === targetVal)
      if (relativePoint) {
        let newEndPoints = cloneDeep(relativePoint.end);
        newEndPoints = addVal.length ? [...newEndPoints, currentPoint.start] : newEndPoints.filter((item: any) => item !== currentPoint.start);
        // 更新properties
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuConnectPointConfigs']: properties['stuConnectPointConfigs'].map((item: any) => {
              if (item.start === targetVal) {
                return {
                  ...item,
                  end: Array.from(new Set(newEndPoints))
                }
              }
              return item;
            })
          }
        });
      };


      const relativeFixedPoint = properties['stuFixedConnectPointConfigs'].find((item: { start: any; }) => item.start === targetVal)
      if (relativeFixedPoint) {
        let newFixedEndPoints = cloneDeep(relativeFixedPoint.end);
        newFixedEndPoints = addVal.length ? [...newFixedEndPoints, currentPoint.start] : newFixedEndPoints.filter((item: any) => item !== currentPoint.start);
        // 更新properties
        store.commit("updateComponentProperties", {
          id: id,
          newProperties: {
            ['stuFixedConnectPointConfigs']: properties['stuFixedConnectPointConfigs'].map((item: any) => {
              if (item.start === targetVal) {
                return {
                  ...item,
                  end: Array.from(new Set(newFixedEndPoints))
                }
              }
              return item;
            })
          }
        });
      };
    }

  }
  // 连接点配置的数量变少后 影响disabledValues
  if (['stuConnectPointConfigs', 'stuFixedConnectPointConfigs'].includes(propertiesKey)) {
    if (val.length < oldVal.length) {
      const disabledValues: any[] = [];
      properties[propertiesKey].forEach((item: any) => {
        disabledValues.push(item.start);
      });
      const newSubFormConfigs = cloneDeep(propertiesKey === 'stuConnectPointConfigs' ? stuConnectPointConfigs.subFormConfigs : stuFixedConnectPointConfigs.subFormConfigs);
      newSubFormConfigs.forEach((item: any) => {
        if (item.key === 'start') {
          item.optionsConfig.disabledValues = disabledValues;
        }
      })
      store.commit("updateFormItemProps", {
        props: {
          "subFormConfigs": newSubFormConfigs,
        },
        key: propertiesKey,
        subType: subType,
      });
    }
  }
}

export const targetCategory = 1191;

export default caseDrawLineAssembly;
