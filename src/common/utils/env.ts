export const isDevelopmentEnv = process.env.NODE_ENV === "development";
export const isProductionEnv = process.env.NODE_ENV === "production";
export const isDocker = window.location.host.indexOf("docker") > -1;
export const isLocal = isDevelopmentEnv || isDocker;
export const isOnline = !isLocal;

export const isTest = window.location.host.indexOf("suanshubang") > -1;
export const isNotOnline = isDevelopmentEnv || isTest;
