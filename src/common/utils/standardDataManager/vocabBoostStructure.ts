import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 单词大爆炸-个性化纠音 标准数据结构
 * @param originData 
 */
export const vocabBoostStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  // vocabBoostQuestion 
  const SUBTYPE = 'vocabBoost';
  const SUBCATEGORY = 1020;
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('单词大爆炸-个性化纠音组件不存在');
  }
  // stuQuestionList
  const { properties: { stuQuestionList: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: 3,
      category: SUBCATEGORY,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
