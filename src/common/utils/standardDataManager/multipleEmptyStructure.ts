import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 多空连答 标准数据结构
 * @param originData 
 */
export const multipleEmptyStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'multipleEmptyQuestion';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('多空连答组件不存在');
  }
  const { properties: { stuOptionList } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true, // true
    isStandardAnswer: true, //true
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuOptionList.map((item: any, index: number) => {
    const { questionOptions: { options, type, answer } } = item;
    const enumOptionList: AnswerItem[] = [];
    const enumStandardAnswer: AnswerItem[] = [];
    options.forEach((option: any, index: number) => {
      const { text } = option;
      const optionItem = {
        signalId: index + 1,
        type: 'text',
        content: [text],
      }
      enumOptionList.push(optionItem)
      if (answer.includes(index)) {
        enumStandardAnswer.push(optionItem)
      }
    });
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList: enumOptionList,
      enumStandardAnswer: enumStandardAnswer,
      standardAnswer: enumStandardAnswer,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}