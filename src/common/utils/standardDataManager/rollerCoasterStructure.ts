import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 小火车连续题板 标准数据结构
 * @param originData 
 */
export const rollerCoasterStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'rollerCoaster';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('小火车连续题板组件不存在');
  }
  const { properties: { stuCustomsPass: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true,
    isStandardAnswer: true,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    const { stuQuestionType, stuOption: { options, type, answer } } = item;
    const enumOptionList: AnswerItem[] = [];
    const enumStandardAnswer: AnswerItem[] = [];
    if (stuQuestionType === 'select') {
      options.forEach((option: any, index: number) => {
        const { imgUrl, text } = option;
        const optionItem = {
          signalId: index + 1,
          type: type === 'text' ? 'text' : 'image',
          content: [type === 'text' ? text : imgUrl],
        }
        enumOptionList.push(optionItem)
        if (answer.includes(index)) {
          enumStandardAnswer.push(optionItem)
        }
      });
    }
    
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: stuQuestionType === 'select' ? true : false,
      isStandardAnswer: stuQuestionType === 'select' ? true : false,
      enumOptionList: enumOptionList,
      enumStandardAnswer: enumStandardAnswer,
      standardAnswer: enumStandardAnswer,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
