import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 迷宫连线 标准数据结构
 * @param originData 
 */
export const gestureUnlockStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components
    },
    name
  } = originData;
  const SUBTYPE = 'gestureUnlock';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('解锁手势密码组件不存在');
  }
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false, // false
    isStandardAnswer: false, //false
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }
  return theQuestionStructureTemp;
}