import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 打地鼠 标准数据结构
 * @param originData 
 */
export const moleStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'moleQuestion';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('打地鼠组件不存在');
  }
  // stuQuestionList  stuOptionList.options  stuOptionList.type  === 'text' ? text : imgUrl
  const { properties: { stuQuestionList: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true,
    isStandardAnswer: true,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {
      comment: "2023-07-19上线，增补的结构，在此之前的数据中children是[]"
    },
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    const { stuOptionList: { options, type } } = item;
    const enumOptionList: AnswerItem[] = [];
    const enumStandardAnswer: AnswerItem[] = [];
    options.forEach((option: any, index: number) => {
      const { imgUrl, text, isCorrect } = option;
      const optionItem = {
        signalId: index + 1,
        type: type === 'text' ? 'text' : 'image',
        content: [type === 'text' ? text : imgUrl],
      }
      enumOptionList.push(optionItem)
      if (isCorrect) {
        enumStandardAnswer.push(optionItem)
      }
    });
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList: enumOptionList,
      enumStandardAnswer: enumStandardAnswer,
      standardAnswer: enumStandardAnswer,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
