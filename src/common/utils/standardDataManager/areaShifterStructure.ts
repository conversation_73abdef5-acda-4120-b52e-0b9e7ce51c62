/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-28 17:53:42
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-10-15 14:59:14
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/sudokuStructure.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 迷宫连线 标准数据结构
 * @param originData 
 */
export const areaShifterStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components
    },
    name
  } = originData;
  const SUBTYPE = 'areaShifter';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('等积变形组件不存在');
  }
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false, // false
    isStandardAnswer: false, //false
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }
  return theQuestionStructureTemp;
}