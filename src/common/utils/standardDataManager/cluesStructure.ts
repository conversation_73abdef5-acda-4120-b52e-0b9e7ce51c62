import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
import store from "@/pages/index/store";
/**
 * 密码盘 标准数据结构
 * @param originData 
 */
export const cluesStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'clues';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('见微知著组件不存在');
  }
  const standardAnswer: AnswerItem[] = [];
  const { componentMap } = store.state;
  Object.values(componentMap).forEach((item: any) => {
    if (item.tag === "hotImage" && item.extra) {
      standardAnswer.push({
        signalId: item.extra.tagIndex,
        type: 'image',
        content: [item.extra.targerUrl || ""],
      })
      
    }
  });
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false, // true
    isStandardAnswer: true, //true
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer,
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  return theQuestionStructureTemp;
}