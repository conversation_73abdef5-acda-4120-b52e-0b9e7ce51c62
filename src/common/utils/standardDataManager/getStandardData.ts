import { CATEGORY } from "@/common/constants";
import { questionStructureFuncMap } from "./questionStructureUtil";
import store from "@/pages/index/store";
import { getHideComponentIdsBeforeScreenShot } from "@/pages/index/common/utils/HideCompsBeforeAnimation";
export interface AnswerItem {
  signalId: number,
  type: string,
  content: string[],
}

interface ResourceItem {
  type: string,
  content: string[],
}

interface Difficulty {
  id: number,
  name: string
}

interface PointItem {
  id: number,
  name: string
}

export interface StandardData {
  /** required 创建的时候没有 主键 */
  id: number, // snapId
  /** required */
  tid: number,
  /** required template.questionType*/
  questionType: number,
  /** 对于cocos题目来说，值就是cocoshd */
  source: string,
  /** required template.category*/
  category: number,
  desc: { categoryName: string },
  /** 难度 JSON.parse(tagsData).difficulty */
  difficulty?: {                 // 
    id: number,
    name: string
  }
  /** 难度 JSON.parse(tagsData).pointList */
  pointList?: [{                 // 考点
    id: number,
    name: string,
    pathNames?: string[] // 新增，用于显示知识点的路径
  }]
  /** 学科 state.subjectId */
  subjectId?: string,
  /** 年级 state.gradeId */
  gradeId?: number,
  /** 题组类型
   * 阅读理解 1
   * 其他 Number(state.template.features.isGroup)
   * */
  questionModule?: number,
  questionStructure: QuestionStructureItem,
  version: string,
  expand: { [x: string]: any }
}

export interface QuestionStructureItem {
  /**
   * 0: 单题/阅读理解的题干
   * index+1: 题组[小题1，小题2, 小题3, 小题4] => [1,2,3,4]
   * 阅读理解[题干，A,B,C,D] => [0, 1, 2, 3, 4]
  */
  subTid: number,
  questionType: number,
  category: number,
  desc: { categoryName: string },
  questionInitScene: string // 题目截图
  questionSignsScene: string[] // 带序号的
  isEnumAnswer: boolean,
  isStandardAnswer: boolean,
  questionResource: ResourceItem[],
  enumOptionList: AnswerItem[],
  enumStandardAnswer: AnswerItem[],
  standardAnswer: AnswerItem[], // 本期为[]
  referenceAnswer: AnswerItem[], // 本期为[]
  questionAnswer: AnswerItem[], // 本期为[]
  expand: {}, // 本期为{}
  children: QuestionStructureItem[]
}

export const getCategoryName = (category: CATEGORY, name = ''): string => {
  const categorys = [
    { name: '选择题', category: 1010 },
    { name: '拖拽题', category: 1011 },
    { name: '填空题', category: 1012 },
    { name: '连线题', category: 1013 },
    { name: '火柴题', category: 1019 },
    { name: '跟读题', category: 1020 },
    { name: '计数题', category: 1021 },
    { name: '阅读题', category: 1022 },
    { name: '时钟题', category: 1023 },
    { name: '手写题', category: 1024 },
    { name: '七巧板题', category: 1025 },
    { name: '主观题', category: 1026 },
    { name: '题组', category: 1027 },
    { name: '阅读理解题', category: 1100 },
    { name: '看图说话题', category: 1101 },
    { name: '听后选择题', category: 1102 },
    { name: '听后回答题', category: 1104 },
    { name: '听后转述题', category: 1103 },
    { name: '朗读短文题', category: 1105 },
    { name: '跟读单词题', category: 1107 },
    { name: '小英上台题组', category: 1109 }
  ]
  return categorys.find((item) => item.category === category)?.name || store.state.template.categoryName || name
}

/**
  * @desc 生成缩略图
  */
const genThumbnail = async (components: Component[]): Promise<string> => {
  // console.log('components', components);
  const tempParams: any[] = [];

  components.forEach((comp: Component) => {
    const { id, extra } = comp;
    if (extra.signalId) {
      // [{id,newExtra: {signalId: number}}]
      tempParams.push({ id, newExtra: { signalId: extra.signalId, isCorrect: extra.isCorrect } })
    }
  })
  const hideCompIds = getHideComponentIdsBeforeScreenShot();
  const texture = await (window as MyWindow).cocos.hideComponentShotWithSignal(tempParams, hideCompIds.map(item => Number(item)));
  // const texture = await (window as MyWindow).cocos.screenSignalShot(tempParams, hideCompIds);
  const data = texture.readPixels();
  const width = texture.width;
  const height = texture.height;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
  canvas.width = width;
  canvas.height = height;
  const rowBytes = width * 4;
  for (let row = 0; row < height; row++) {
    const srow = height - 1 - row;
    const imageData = ctx.createImageData(width, 1);
    const start = srow * width * 4;
    for (let i = 0; i < rowBytes; i++) {
      imageData.data[i] = data[start + i];
    }
    ctx.putImageData(imageData, 0, row);
  }

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      async blob => {
        if (!blob) {
          reject(new Error("生成缩略图失败"));
          return;
        }
        const name = "thumbnail.jpeg";
        const file = new File([blob], name, { type: blob.type });
        const formData = new FormData();
        formData.append("file", file);
        try {
          const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
          // 图片地址
          if (url && url !== "") {
            console.log('thumbnail-standardData', url);
            return resolve(url);
          } else {
            return reject();
          }
        } catch (e) {
          return reject(e);
        }
      },
      "image/jpeg",
      0.92,
    );
  });
}

const getIsEnumAnswer = (category: CATEGORY): boolean => {
  return [CATEGORY.CHOICE, CATEGORY.BLANK].includes(category)
}

const getIsStandardAnswer = (questionType: number, category: CATEGORY): boolean => {
  // 摩天楼数独 拖拽交换位置 连连看 写字 立体拼搭 光塔游戏 法码拖拽 返回false
  if ([1144, 1143, 1150, 1153, 1154, 1158, 1171, 1172 ,1200].includes(category)) return false;
  return [3, 4, 5].includes(questionType)
}

/**
 * 选择题 optionList
 */
const getSelectOptionList = (components: Component[], isCorrect?: boolean) => {
  const options: AnswerItem[] = [];
  components.forEach((comp: Component) => {
    const { tag, extra } = comp;
    let isFind = tag === 'answer';
    if (isCorrect) {
      isFind = isFind && extra.isCorrect
    }
    if (isFind) {
      options.push({
        signalId: extra.signalId,
        type: '',
        content: []
      })
    }
  })
  return options;
}

const getBlankOptionList = (components: Component[], isCorrect?: boolean) => {
  // tag === 'blankModule'
  // correctArray 正确答案
  const options: AnswerItem[] = [];
  components.forEach((comp: Component) => {
    const { tag, extra } = comp;
    const isFind = tag === 'blankModule';
    if (isFind) {
      options.push({
        signalId: extra.signalId,
        type: 'text',
        content: isCorrect ? extra.correctArray : []
      })
    }
  })
  return options;
}

const getOptionList = (category: CATEGORY, components: Component[], isCorrect?: boolean) => {
  if (category === CATEGORY.CHOICE) {
    return getSelectOptionList(components, isCorrect);
  } else if (category === CATEGORY.BLANK) {
    return getBlankOptionList(components, isCorrect)
  } else {
    return []
  }
}

const getQuestionData = (extraStageData: any) => {
  const temp = [];
  const { analysis } = extraStageData;

  if (analysis && analysis.text) {
    temp.push({
      signalId: temp.length + 1,
      label: '', // 标签
      type: 'text',
      content: [analysis.text] // 内容
    })
  }
  if (analysis && analysis.audio && analysis.audio.url) {
    temp.push({
      signalId: temp.length + 1,
      label: '', // 标签
      type: 'audio',
      content: [analysis.audio.url] // 内容
    })
  }
  if (analysis && analysis.imageUrl) {
    temp.push({
      signalId: temp.length + 1,
      label: '', // 标签
      type: 'image',
      content: [analysis.imageUrl] // 内容
    })
  }
  return temp;
}


export const getQuestionStructure = async (originData: any): Promise<QuestionStructureItem> => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
      extraStageData,
    },
    extData: {
      formConfig,
    } = { formConfig: {} },
    name
  } = originData;
  let url = '';
  if (category === CATEGORY.BLANK || category === CATEGORY.CHOICE) {
    url = await genThumbnail(components);
  }

  const temp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    desc: { categoryName: getCategoryName(category, name) },
    questionInitScene: thumbnail, // 题目截图
    questionSignsScene: url ? [url] : [], // 带序号的 todo
    isEnumAnswer: getIsEnumAnswer(category),
    isStandardAnswer: getIsStandardAnswer(questionType, category),
    questionResource: [], // 本期为[]
    enumOptionList: getOptionList(category, components),
    enumStandardAnswer: getOptionList(category, components, true),
    standardAnswer: getOptionList(category, components, true),
    referenceAnswer: [], // 本期为[]
    questionAnswer: getQuestionData(extraStageData), // 本期为[]
    expand: {}, // 本期为{}
    children: []
  }
  // 阅读理解
  // console.log('re...category', category, CATEGORY.READCOMQUESTION);
  if (category === CATEGORY.READCOMQUESTION) {
    const readComponents = components.find((comp: Component) => comp.type === 'specialComponent' && comp.subType === 'readcom')
    const readTemp: QuestionStructureItem = {
      subTid: 0,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [thumbnail],
      isEnumAnswer: getIsEnumAnswer(category),
      isStandardAnswer: getIsStandardAnswer(questionType, category),
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: [],
      desc: {
        categoryName: getCategoryName(category, name)
      }
    }
    if (readComponents) {
      const { properties: { question } } = readComponents;
      readTemp.questionSignsScene = question.questionList;
      // console.log('re...category', question.optionsList);
      readTemp.children = question.optionsList.map((item: any, index: number) => {
        const optionList = item.options.map((oItem: any, oIndex: number) => {
          return {
            signalId: oIndex + 1,
            type: 'image',
            content: oItem.picList,
            isCorrect: oItem.isCorrect
          }
        })

        return {
          subTid: index + 1,
          questionType: questionType, // 跟阅读理解的questionType一致？
          category: category, // // 跟阅读理解的category一致？ 还是不要这个值
          questionInitScene: item.optionsQuestion[0], // 题目截图 -todo: 王欢确认具体内容
          questionSignsScene: item.optionsQuestion,
          isEnumAnswer: true,
          isStandardAnswer: true,
          enumOptionList: optionList.map((oItem: any) => {
            return {
              signalId: oItem.signalId,
              type: 'image',
              content: oItem.content
            }
          }),
          enumStandardAnswer: optionList.filter((oItem: any) => oItem.isCorrect).map((oItem: any) => {
            return {
              signalId: oItem.signalId,
              type: 'image',
              content: oItem.content
            }
          }),
          standardAnswer: optionList.filter((oItem: any) => oItem.isCorrect).map((oItem: any) => {
            return {
              signalId: oItem.signalId,
              type: 'image',
              content: oItem.content
            }
          }),
          questionResource: [], // 本期为[]
          referenceAnswer: [], // 本期为[]
          questionAnswer: [], // 本期为[]
          expand: {}, // 本期为{}
          children: []
        }
      })
    }
    return readTemp;
  }

  if (category === CATEGORY.ENGROUPPK) {
    const enGroupPKComponents = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === 'enGroupPK');
    const enPKTemp: QuestionStructureItem = {
      subTid: 0,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [thumbnail],
      isEnumAnswer: getIsEnumAnswer(category),
      isStandardAnswer: getIsStandardAnswer(questionType, category),
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: [],
      desc: {
        categoryName: getCategoryName(category, name)
      }
    }
    if (enGroupPKComponents) {
      const { properties: { pkData: { question } } } = enGroupPKComponents;
      enPKTemp.children = question.map((item: any, index: number) => {
        return {
          subTid: index + 1,
          questionType: 2,
          category: 1193,
          questionInitScene: thumbnail,
          questionSignsScene: [],
          isEnumAnswer: false,
          isStandardAnswer: false,
          enumOptionList: [],
          enumStandardAnswer: [],
          standardAnswer: [],
          questionResource: [],
          referenceAnswer: [],
          questionAnswer: [],
          expand: {},
          children: []
        }
      })
      return enPKTemp;
    }

  }
  else if (category === CATEGORY.ENPK) {
    const enPKComponents = components.find((comp: Component) => comp.type === 'specialComponent' && comp.subType === 'enPK');
    const enPKTemp: QuestionStructureItem = {
      subTid: 0,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [thumbnail],
      isEnumAnswer: getIsEnumAnswer(category),
      isStandardAnswer: getIsStandardAnswer(questionType, category),
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: [],
      desc: {
        categoryName: getCategoryName(category, name)
      }
    }
    if (enPKComponents) {
      const { properties: { pkData: { question } } } = enPKComponents;
      enPKTemp.children = question.map((item: any, index: number) => {
        return {
          subTid: index + 1,
          questionType: questionType, // 跟阅读理解的questionType一致？
          category: 1108, // wangcheng提供的数据结构的值就是这个
          questionInitScene: thumbnail, // 题目截图 -todo: 王欢确认具体内容
          questionSignsScene: [], // 跟wangcheng确认过，这个值取的都是第一题的截图
          isEnumAnswer: false,
          isStandardAnswer: false,
          enumOptionList: [],
          enumStandardAnswer: [],
          standardAnswer: [],
          questionResource: [],
          referenceAnswer: [],
          questionAnswer: [],
          expand: {},
          children: []
        }
      })
      return enPKTemp;
    }

  }
  // 判断是否是学能：类选择题
  // 先查学能组件
  const xnComponent = components.find((comp: Component) => comp.type === 'optionComponent');
  // 查找是否是类选择题
  if (xnComponent) {
    const { subType, properties } = xnComponent;
    // formItemType SpecialOptionConfig
    // 目前只有捕鱼/黄金矿工/弹珠游戏 在用
    if ([CATEGORY.FISH, CATEGORY.GOLDENMINER, CATEGORY.ZUMA].includes(category) && formConfig && formConfig[subType]) {
      const optionFormConfig = formConfig[subType].find((configItem: { formItemType: string; key: string; }) => configItem.formItemType === 'SpecialOptionConfig');
      if (optionFormConfig) {
        const { options, answer, type } = properties[optionFormConfig.key];
        const enumOptionList = options.map((opt: { [x: string]: any; }, index: number) => {
          return {
            signalId: index + 1,
            type: type,
            content: [opt[type === 'text' ? 'text' : 'imgUrl']]
          }
        })
        const xnTemp: QuestionStructureItem = {
          subTid: 0,
          questionType: questionType,
          category: category,
          desc: { categoryName: getCategoryName(category, name) },
          questionInitScene: thumbnail, // 题目截图
          questionSignsScene: [], // 学能为[]--待验证
          isEnumAnswer: true,
          isStandardAnswer: getIsStandardAnswer(questionType, category),
          questionResource: [], // 本期为[]
          enumOptionList, // --学能待验证
          enumStandardAnswer: [enumOptionList[answer]], // --学能待验证
          standardAnswer: [enumOptionList[answer]], // --学能待验证
          referenceAnswer: [], // 本期为[]
          questionAnswer: getQuestionData(extraStageData), // 本期为[]
          expand: {}, // 本期为{}
          children: []
        }
        // console.log('xnTemp');
        return xnTemp;
      }
    }
  }
  // 判断是否是赛车
  if (category === 1116) {
    const scComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === 'racingCar');
    if (scComponent) {
      // eslint-disable-next-line @typescript-eslint/camelcase
      const { properties: { stu_questions: stuQuestions } } = scComponent;
      const scTemp: QuestionStructureItem = {
        subTid: 0,
        questionType: questionType,
        category: category,
        questionInitScene: thumbnail,
        questionSignsScene: [thumbnail],
        isEnumAnswer: true, // true
        isStandardAnswer: true, //true
        enumOptionList: [],
        enumStandardAnswer: [],
        standardAnswer: [],
        questionResource: [],
        referenceAnswer: [],
        questionAnswer: [],
        expand: {},
        children: [],
        desc: {
          categoryName: getCategoryName(category, name)
        }
      }

      scTemp.children = stuQuestions.map((item: any, index: number) => {
        // optType "1" 文本 "2" 图片 item
        // optTextA
        // optUrlA
        // answer "1"
        const { answer, optType } = item;
        const optTypeMap = new Map([
          ['1', 'text'],
          ['2', 'image']
        ]);
        const tempOptions = [
          { text: item.optTextA, image: item.optUrlA },
          { text: item.optTextB, image: item.optUrlB },
          { text: item.optTextC, image: item.optUrlC },
          { text: item.optTextD, image: item.optUrlD }
        ];
        const enumOptionList = tempOptions.map((opt, index: number) => {
          return {
            signalId: index + 1,
            type: optTypeMap.get(optType),
            content: [opt[optTypeMap.get(optType) || 'text']]
          }
        })
        return {
          subTid: index + 1,
          questionType: questionType,
          category: category,
          questionInitScene: thumbnail,
          questionSignsScene: [],
          isEnumAnswer: true,
          isStandardAnswer: true,
          enumOptionList, // --学能待验证,
          enumStandardAnswer: [enumOptionList[answer]], // --学能待验证
          standardAnswer: [enumOptionList[answer]], // --学能待验证
          questionResource: [],
          referenceAnswer: [],
          questionAnswer: [],
          expand: {},
          children: []
        }
      })
      return scTemp;
    }
  }
  // 怪兽餐厅
  if (category === 1130) {
    const monsterRestComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === 'monsterRest');
    if (monsterRestComponent) {
      // eslint-disable-next-line @typescript-eslint/camelcase
      const { properties: { stuQuestions } } = monsterRestComponent;
      const monsterRestTemp: QuestionStructureItem = {
        subTid: 0,
        questionType: questionType,
        category: category,
        questionInitScene: thumbnail,
        questionSignsScene: [thumbnail],
        isEnumAnswer: true, // true
        isStandardAnswer: true, //true
        enumOptionList: [],
        enumStandardAnswer: [],
        standardAnswer: [],
        questionResource: [],
        referenceAnswer: [],
        questionAnswer: [],
        expand: {},
        children: [],
        desc: {
          categoryName: getCategoryName(category, name)
        }
      }

      monsterRestTemp.children = stuQuestions.map((item: any, index: number) => {
        // optType "1" 文本 "2" 图片 item
        // optTextA
        // optUrlA
        // answer "1"
        const { questionContent: { type, answer, options } } = item;
        const optTypeMap = new Map([
          ['text', 'text'],
          ['img', 'image']
        ]);
        const typeKeyMap = new Map([
          ["img", "imgUrl"],
          ["text", "text"],
        ]);
        const enumOptionList = options.map((opt: { [x: string]: any; }, index: number) => {
          return {
            signalId: index + 1,
            type: optTypeMap.get(type),
            content: [opt[typeKeyMap.get(type) || 'text']]
          }
        })
        return {
          subTid: index + 1,
          questionType: questionType,
          category: category,
          questionInitScene: thumbnail,
          questionSignsScene: [],
          isEnumAnswer: true,
          isStandardAnswer: true,
          enumOptionList, // --学能待验证,
          enumStandardAnswer: answer.map((item: number) => enumOptionList[item]), // --学能待验证
          standardAnswer: answer.map((item: number) => enumOptionList[item]), // --学能待验证
          questionResource: [],
          referenceAnswer: [],
          questionAnswer: [],
          expand: {},
          children: []
        }
      })
      return monsterRestTemp;
    }
  }
  // 密码盘
  if (questionStructureFuncMap && questionStructureFuncMap[category]) {
    return {
      ...questionStructureFuncMap[category](originData),
      questionAnswer: getQuestionData(extraStageData)
    };
  }
  return temp
}

export const getSimpleQuestionStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
      extraStageData,
    },
    name
  } = originData;
  const temp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    desc: { categoryName: getCategoryName(category, name) },
    questionInitScene: thumbnail, // 题目截图
    questionSignsScene: [], // 带序号的 todo
    isEnumAnswer: getIsEnumAnswer(category),
    isStandardAnswer: getIsStandardAnswer(questionType, category),
    questionResource: [], // 本期为[]
    enumOptionList: getOptionList(category, components),
    enumStandardAnswer: getOptionList(category, components, true),
    standardAnswer: getOptionList(category, components, true),
    referenceAnswer: [], // 本期为[]
    questionAnswer: getQuestionData(extraStageData), // 本期为[]
    expand: {}, // 本期为{}
    children: []
  }
  return temp
}

export const getQuestionModule = (category: CATEGORY, isGroup: 0 | 1, components: Components) => {
  if (category === CATEGORY.ENGROUPPK) return 0;
  // 测评题库有值
  if (category === CATEGORY.READCOMQUESTION) return 1;
  // 包含学能组件的题目
  const specialComponents = components.filter(item => item.type === 'optionComponent');
  if (specialComponents.length) {
    return 3;
  }
  if (isGroup) return 2
  return 0
}

// const texture = (window as MyWindow).cocos.screenshot();
export const getStandardData = async (originData: any): Promise<StandardData> => {
  const {
    id,
    tid,
    subjectId,
    gradeId,
    con: {
      template: {
        questionType,
        category,
        supportSetReferenceAnswer,
        referenceAnswer
      },
      components
    },
    features,
    tagsData,
    name
  } = originData;
  const res = {
    /** 创建的时候没有 主键 */
    id: id, // snapId
    /** required */
    tid: tid,
    /** required template.questionType*/
    questionType: questionType,
    /** 对于cocos题目来说，值就是cocoshd */
    source: 'cocoshd',
    /** required template.category*/
    category: category,
    desc: { categoryName: getCategoryName(category, name) },
    /** 难度 JSON.parse(tagsData).difficulty */
    difficulty: tagsData ? tagsData.difficulty : [],
    /** 难度 JSON.parse(tagsData).pointList */
    pointList: tagsData ? tagsData.pointList : [],
    /** 学科 state.subjectId */
    subjectId: subjectId,
    /** 年级 state.gradeId */
    gradeId: gradeId,
    /** 题组类型
     * 阅读理解 1
     * 其他 Number(state.template.features.isGroup)
     * */
    questionModule: getQuestionModule(category, features.isGroup, components),
    questionStructure: await getQuestionStructure(originData),
    version: "1.0.1",
    expand: {
      tempVersion: store.state.template.tempVersion
    }, // 本期为{}
  }

  // 参考答案
  if (supportSetReferenceAnswer && referenceAnswer) {
    res.questionStructure.referenceAnswer = referenceAnswer;
  }
  return res;
}

export default getStandardData;