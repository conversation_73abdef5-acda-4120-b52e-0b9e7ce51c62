
/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-07-18 17:05:29
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-07-18 17:11:36
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/tapFrenzyStructure.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 连续点击 标准数据结构
 * @param originData 
 */
export const tapFrenzyStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'tapFrenzy';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('连续点击组件不存在');
  }
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }
  return theQuestionStructureTemp;
}