import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 过目不忘 标准数据结构
 * @param originData 
 */
export const memoryStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'memoryCompent';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('过目不忘组件不存在');
  }
  // stuQuestionList  stuOptionList.options  stuOptionList.type  === 'text' ? text : imgUrl
  const { properties: { stuTopicList: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {
      comment: "2023-07-19上线，增补的结构, 在此之前的数据中children是[]"
    },
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    const { stuOptionsList: { options, type } } = item;
    const enumOptionList: AnswerItem[] = [];
    options.forEach((option: any, index: number) => {
      // const { imgUrl, text, isCorrect } = option;
      const optionItem = {
        signalId: index + 1,
        type: type === 'text' ? 'text' : 'image',
        // content: [type === 'text' ? text : imgUrl],
        content: option.map((item: any) => {
          return type === 'text' ? item.text : item.imgUrl
        }),
      }
      enumOptionList.push(optionItem)
    });
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: false,
      isStandardAnswer: false,
      enumOptionList: enumOptionList,
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
