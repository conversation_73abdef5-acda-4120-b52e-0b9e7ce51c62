import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 圈叉游戏 标准数据结构
 * @param originData 
 */
export const ticTacToeStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'ticTacToe';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('圈叉游戏组件不存在');
  }
  const { properties: { stuQuestionList: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: 3,
      category: category+1,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: false,
      isStandardAnswer: false,
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}