/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2025-01-10 15:47:21
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-01-10 15:51:25
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/stepUpStructure.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 爬台阶 标准数据结构
 * @param originData 
 */
export const stepUpStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'stepUp';
  const SUBCATEGORY = 1199;
  const SUBQUESTIONTYPE= 3;
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('爬台阶不存在');
  }
  // stuQuestionList  stuOptionList.options  stuOptionList.type  === 'text' ? text : imgUrl
  const { properties: { stuQuestionList: stuQuestionList } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestionList.map((item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: SUBCATEGORY,
      category: SUBQUESTIONTYPE,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: false,
      isStandardAnswer: false,
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
