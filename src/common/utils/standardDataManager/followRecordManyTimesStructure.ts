/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-19 11:16:54
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-06-19 11:23:04
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/followRecordManyTimesStructure.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 迷宫连线 标准数据结构
 * @param originData 
 */
export const followRecordManyTimesStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components
    },
    name
  } = originData;
  const SUBTYPE = 'followRecordManyTimes';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('单词读三遍组件不存在');
  }
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false, // false
    isStandardAnswer: false, //false
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }
  return theQuestionStructureTemp;
}