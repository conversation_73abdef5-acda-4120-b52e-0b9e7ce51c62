import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 连续选择过山崖 标准数据结构
 * @param originData 
 */
export const serialJumpStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'serialJump';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('连续选择过山崖组件不存在');
  }
  const { properties: { stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true, // true
    isStandardAnswer: true, //true
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    const { stuAnswer, stuOptType } = item;
    const optTypeMap = new Map([
      ['1', 'text'],
      ['2', 'image']
    ]);
    const tempOptions = [
      { text: item.stuOptTextA, image: item.stuOptUrlA },
      { text: item.stuOptTextB, image: item.stuOptUrlB },
      { text: item.stuOptTextC, image: item.stuOptUrlC }
    ];
    const enumOptionList = tempOptions.map((opt, index: number) => {
      return {
        signalId: index + 1,
        type: optTypeMap.get(stuOptType),
        content: [opt[optTypeMap.get(stuOptType) || 'text']]
      }
    });
    
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList,
      enumStandardAnswer: [enumOptionList[stuAnswer]],
      standardAnswer: [enumOptionList[stuAnswer]],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}