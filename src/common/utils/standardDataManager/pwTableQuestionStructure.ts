import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 密码盘 标准数据结构
 * @param originData 
 */
export const pwTableQuestionStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'pwTabel';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('密码盘组件不存在');
  }
  const { properties: { stuQuestionList } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true, // true
    isStandardAnswer: true, //true
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestionList.map((item: any, index: number) => {
    // 答案     stuQuestionAnswer
    const { stuQuestionAnswer } = item;
    // 每个题目的题干只有一个 所以enumOptionList只有一个
    const enumOptionList = [{
      signalId: 1,
      type: 'text',
      content: [stuQuestionAnswer]
    }];
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList,
      enumStandardAnswer: enumOptionList,
      standardAnswer: enumOptionList,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}