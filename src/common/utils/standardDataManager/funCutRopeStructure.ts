import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 想象力训练 标准数据结构
 * @param originData 
 */
export const funCutRopeStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'funCutRope';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('趣味割绳子组件不存在');
  }
  const { properties: { stuOption: stuQuestions } } = theComponent;
  const { options, type, answer } = stuQuestions;
  const enumOptionList: AnswerItem[] = [];
  const enumStandardAnswer: AnswerItem[] = [];
  options.forEach((option: any, index: number) => {
    const { imgUrl, text } = option;
    const optionItem = {
      signalId: index + 1,
      type: type === 'text' ? 'text' : 'image',
      content: [type === 'text' ? text : imgUrl],
    }
    enumOptionList.push(optionItem)
    if (answer.includes(index)) {
      enumStandardAnswer.push(optionItem)
    }
  });
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true,
    isStandardAnswer: true,
    enumOptionList: enumOptionList,
    enumStandardAnswer: enumStandardAnswer,
    standardAnswer: enumStandardAnswer,
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }
  return theQuestionStructureTemp;
}