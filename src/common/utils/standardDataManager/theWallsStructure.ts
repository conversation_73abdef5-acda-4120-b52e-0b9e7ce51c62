import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 墙来了标准数据结构
 * @param originData 
 */
export const theWallsStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'theWalls';
  const SUBCATEGORY = 1010;
  const SUBQUESTIONTYPE= 4;
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('墙来了组件不存在');
  }
  const { properties: { stuQuestionList: stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true,
    isStandardAnswer: true,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    const { stuOption: { options, type, answer } } = item;
    const enumOptionList: AnswerItem[] = [];
    const enumStandardAnswer: AnswerItem[] = [];
    options.forEach((option: any, index: number) => {
      const { imgUrl, text } = option;
      const optionItem = {
        signalId: index + 1,
        type: type === 'text' ? 'text' : 'image',
        content: [type === 'text' ? text : imgUrl],
      }
      enumOptionList.push(optionItem)
      if (answer === index) {
        enumStandardAnswer.push(optionItem)
      }
    });
    return {
      subTid: index + 1,
      questionType: SUBQUESTIONTYPE,
      category: SUBCATEGORY,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList: enumOptionList,
      enumStandardAnswer: enumStandardAnswer,
      standardAnswer: enumStandardAnswer,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}