import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 情景回答 标准数据结构
 * @param originData 
 */
export const contextualAnswerStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'contextualAnswer';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('情景回答组件不存在');
  }
  const { properties: { stuQuestion } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestion.map((_item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: 2,
      category: 1104,
      isEnumAnswer: false,
      isStandardAnswer: false,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      questionResource: [],
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}