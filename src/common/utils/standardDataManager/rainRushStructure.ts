/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-11-08 17:33:45
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-01-10 15:55:39
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/detectiveStructure.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { AnswerItem, getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 抢伞游戏 标准数据结构
 * @param originData 
 */
export const rainRushStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'rainRush';
  const SUBCATEGORY = 1010;
  const SUBQUESTIONTYPE= 4;
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('抢伞游戏组件不存在');
  }
  const { properties: { stuQuestionList: stuQuestionList } } = theComponent;
  console.log("rainRushQuestion stuQuestionList",stuQuestionList)
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: true,
    isStandardAnswer: true,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestionList.map((item: any, index: number) => {
    const { stuOptions: { options, type, answer } } = item;
    const enumOptionList: AnswerItem[] = [];
    const enumStandardAnswer: AnswerItem[] = [];
    options.forEach((option: any, index: number) => {
      const { imgUrl, text } = option;
      const optionItem = {
        signalId: index + 1,
        type: type === 'text' ? 'text' : 'image',
        content: [type === 'text' ? text : imgUrl],
      }
      enumOptionList.push(optionItem)
      if (answer === index) {
        enumStandardAnswer.push(optionItem)
      }
    });
    return {
      subTid: index + 1,
      questionType: SUBQUESTIONTYPE,
      category: SUBCATEGORY,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: true,
      isStandardAnswer: true,
      enumOptionList: enumOptionList,
      enumStandardAnswer: enumStandardAnswer,
      standardAnswer: enumStandardAnswer,
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}