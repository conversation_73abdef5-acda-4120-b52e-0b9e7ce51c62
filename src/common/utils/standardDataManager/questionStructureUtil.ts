/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-04-22 15:53:52
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-01-10 15:59:09
 * @FilePath: /interactive-question-editor/src/common/utils/standardDataManager/questionStructureUtil.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { CATEGORY } from "@/common/constants";
import { pwTableQuestionStructure } from "./pwTableQuestionStructure";
import { dynamicOptionsStructure } from "./dynamicOptionsStructure";
import { doubleRacingStructure } from "./doubleRacingStructure";
import { serialJumpStructure } from "./serialJumpStructure";
import { cluesStructure } from "./cluesStructure";
import { forestAdventureStructure } from "./forestAdventureStructure";
import { javelinStructure } from "./javelinStructure";
import { miracleGemStructure } from "./miracleGemStructure";
import { prizeClawStructure } from "./prizeClawStructure";
import { realFakeHostageStructure } from "./realFakeHostageStructure";
import { rollerCoasterStructure } from "./rollerCoasterStructure";
import { moleStructure } from "./moleStructure";
import { memoryStructure } from "./memoryStructure";
import { airControllerStructure } from "./airControllerStructure";
import { seqMemoryStructure } from "./seqMemoryStructure";
import { xiaoluPracticeStructure } from "./xiaoluPracticeStructure";
import { knockIceStructure } from "./knockIceStructure";
import { multipleEmptyStructure } from "./multipleEmptyStructure";
import { keenObserverStructure } from "./keenObserverStructure";
import { imaginationTrainingStructure } from "./imaginationTrainingStructure";
import { contextualAnswerStructure } from "./contextualAnswerStructure";
import { funCutRopeStructure } from "./funCutRopeStructure";
import { parkourStructure } from "./parkourStructure";
import { mazeLinkStructure } from "./mazeLinkStructure";
import { ticTacToeStructure } from "./ticTacToeStructure";
import { fruitNinjaStructure } from "./fruitNinjaStructure";
import { theWallsStructure } from "./theWallsStructure";
import { questStructure } from "./questStructure";
import { gestureUnlockStructure } from "./gestureUnlockStructure";
import { followRecordManyTimesStructure } from "./followRecordManyTimesStructure";
import { aroundSpeakStructure } from "./aroundSpeakStructure";

import { sudokuStructure } from "./sudokuStructure";
import { tapFrenzyStructure } from "./tapFrenzyStructure";
import { areaShifterStructure } from "./areaShifterStructure";
import { pathPuzzleStructure } from "./pathPuzzleStructure";
import { detectiveStructure } from "./detectiveStructure";
import { rainRushStructure } from "./rainRushStructure";
import { stepUpStructure } from "./stepUpStructure";
import { vocabBoostStructure } from "./vocabBoostStructure";

export const questionStructureFuncMap = {
  [CATEGORY.PWTABLE]: pwTableQuestionStructure,
  [CATEGORY.DYNAMICOPTIONS]: dynamicOptionsStructure,
  [CATEGORY.DOUBLERACING]: doubleRacingStructure,
  [CATEGORY.SERIALJUMP]: serialJumpStructure,
  [CATEGORY.CLUES]: cluesStructure,
  [CATEGORY.FORESTADVENTURE]: forestAdventureStructure,
  [CATEGORY.JAVELIN]: javelinStructure,
  [CATEGORY.MIRACLEGEM]: miracleGemStructure,
  [CATEGORY.PRIZECLAW]: prizeClawStructure,
  [CATEGORY.REALFAKEHOSTAGE]: realFakeHostageStructure,
  [CATEGORY.ROLLERCOASTER]: rollerCoasterStructure,
  [CATEGORY.MOLE]: moleStructure,
  [CATEGORY.MEMORY]: memoryStructure,
  [CATEGORY.SEQMEMORY]: seqMemoryStructure,
  [CATEGORY.AIRCONTROLLER]: airControllerStructure,
  [CATEGORY.XIAOLUPRACTICE]: xiaoluPracticeStructure,
  [CATEGORY.KNOCKICE]: knockIceStructure,
  [CATEGORY.MULTIPLEEMPTY]: multipleEmptyStructure,
  [CATEGORY.KEENOBSERVER]: keenObserverStructure,
  [CATEGORY.IMAGINTRAIN]: imaginationTrainingStructure,
  [CATEGORY.CONTEXTUALANSWERQUESTION]: contextualAnswerStructure,
  [CATEGORY.FUNCUTROPE]: funCutRopeStructure,
  [CATEGORY.PARKOUR]: parkourStructure,
  [CATEGORY.MAZELINK]: mazeLinkStructure,
  [CATEGORY.TICTACTOE]: ticTacToeStructure,
  [CATEGORY.FRUITNINJA]: fruitNinjaStructure,
  [CATEGORY.THEWALLS]: theWallsStructure,
  [CATEGORY.QUEST]: questStructure,
  [CATEGORY.GESTUREUNLOCK]: gestureUnlockStructure,
  [CATEGORY.FOLLOWRECORDMANYTIMES]: followRecordManyTimesStructure,
  [CATEGORY.SUDOKU]: sudokuStructure,
  [CATEGORY.TAPFRENZY]: tapFrenzyStructure,
  [CATEGORY.AREASHIFTER]: areaShifterStructure,
  [CATEGORY.PATHPUZZLE]: pathPuzzleStructure,
  [CATEGORY.AROUNDSPEAK]: aroundSpeakStructure,
  [CATEGORY.DETECTIVE]: detectiveStructure,
  [CATEGORY.RAINRUSH]: rainRushStructure,
  [CATEGORY.STEPUP]: stepUpStructure,
  [CATEGORY.WORDBOMB]: vocabBoostStructure,
}
