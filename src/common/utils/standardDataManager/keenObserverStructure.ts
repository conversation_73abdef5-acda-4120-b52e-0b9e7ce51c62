import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 观察力 标准数据结构
 * @param originData 
 */
export const keenObserverStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'keenObserver';
  const subCategory = 1170;
  const subQuestionType = 3;
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('观察力组件不存在');
  }
  const { properties: { stuStemList } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {},
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuStemList.map((item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: subQuestionType,
      category: subCategory,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      questionResource: [],
      isEnumAnswer: false,
      isStandardAnswer: false,
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}