import { getCategoryName, QuestionStructureItem } from "./getStandardData";
/**
 * 飞机操控 标准数据结构
 * @param originData 
 */
export const airControllerStructure = (originData: any): QuestionStructureItem => {
  const {
    thumbnail,
    con: {
      template: {
        questionType,
        category
      },
      components,
    },
    name
  } = originData;
  const SUBTYPE = 'airplaneController';
  const theComponent = components.find((comp: Component) => comp.type === 'optionComponent' && comp.subType === SUBTYPE);
  if (!theComponent) {
    throw Error('飞机操控组件不存在');
  }
  // stuQuestionList  stuOptionList.options  stuOptionList.type  === 'text' ? text : imgUrl
  const { properties: { stuQuestions } } = theComponent;
  const theQuestionStructureTemp: QuestionStructureItem = {
    subTid: 0,
    questionType: questionType,
    category: category,
    questionInitScene: thumbnail,
    questionSignsScene: [thumbnail],
    isEnumAnswer: false,
    isStandardAnswer: false,
    enumOptionList: [],
    enumStandardAnswer: [],
    standardAnswer: [],
    questionResource: [],
    referenceAnswer: [],
    questionAnswer: [],
    expand: {
      comment: "2023-09-04上线，增补的结构, 在此之前的数据中children是[]"
    },
    children: [],
    desc: {
      categoryName: getCategoryName(category, name)
    }
  }

  theQuestionStructureTemp.children = stuQuestions.map((item: any, index: number) => {
    return {
      subTid: index + 1,
      questionType: questionType,
      category: category,
      questionInitScene: thumbnail,
      questionSignsScene: [],
      isEnumAnswer: false,
      isStandardAnswer: true,
      enumOptionList: [],
      enumStandardAnswer: [],
      standardAnswer: [],
      questionResource: [],
      referenceAnswer: [],
      questionAnswer: [],
      expand: {},
      children: []
    }
  })
  return theQuestionStructureTemp;
}
