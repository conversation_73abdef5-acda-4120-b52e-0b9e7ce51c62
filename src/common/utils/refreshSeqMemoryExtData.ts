// 有序记忆-结束题目数字个数
export const refreshSeqMemoryExtData = (category: number, extData: any, components: Components): void => {
  if (category !== 1151) return;
  if (JSON.stringify(extData).includes('stuNumModeEndCnt')) return;
  const stuNumModeEndCntFormConfig = {
      "formItemType": "BaseInputNumber",
      "key": "stuNumModeEndCnt",
      "label": "结束题目数字个数",
      "forceUpdate": 2,
      "value": 9,
      "min": 1,
      "max": 9,
      "span": 16,
      "stepStrictly": true,
      "step": 1,
      "actions": {
        "change": true
      }
    }
  const theComp = components.find((comp: Component) => comp.subType === 'seqMemory');
  const { properties } = theComp;
  stuNumModeEndCntFormConfig.min = properties.stuNumModeStartCnt;

  properties.stuNumModeEndCnt = properties.stuQuestionMax;
  const stuGameTypeConfigs = extData.formConfig.seqMemory.find((item: { key: string; }) => item.key === 'stuGameType');
  const associatedForm = stuGameTypeConfigs.options[0].associatedForm;
  associatedForm[0].forceUpdate = 2;
  stuNumModeEndCntFormConfig.max = properties.stuQuestionMax;
  stuNumModeEndCntFormConfig.min = properties.stuNumModeStartCnt || 1;
  stuNumModeEndCntFormConfig.value = properties.stuQuestionMax;
  stuGameTypeConfigs.options[0].associatedForm.splice(1, 0, stuNumModeEndCntFormConfig);
}