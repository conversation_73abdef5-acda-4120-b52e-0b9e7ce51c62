const removeCdnParams = (url: string): string => {
  if (!url) return '';
  // 匹配http(s)://开头的图片URL中的参数
  return url.replace(/(https?:\/\/[^"]*\.(jpg|jpeg|png|webp))@[^"]+/gi, '$1');
};

/**
 * 处理JSON字符串中的CDN图片URL
 * @param jsonStr JSON字符串
 * @returns 处理后的JSON字符串
 */
export const processJsonCdnUrls = (jsonStr: string): string => {
  if (!jsonStr) return '';
  
  // 先尝试解析JSON
  try {
    const data = JSON.parse(jsonStr);
    
    // 递归处理对象或数组
    const processValue = (value: any): any => {
      if (typeof value === 'string') {
        // 如果是字符串，检查是否是图片URL
        if (value.match(/\.(jpg|jpeg|png|webp)@/i)) {
          return removeCdnParams(value);
        }
        return value;
      } else if (Array.isArray(value)) {
        // 如果是数组，递归处理每个元素
        return value.map(item => processValue(item));
      } else if (typeof value === 'object' && value !== null) {
        // 如果是对象，递归处理每个属性
        const result: Record<string, any> = {};
        for (const key in value) {
          result[key] = processValue(value[key]);
        }
        return result;
      }
      return value;
    };

    // 处理整个数据结构
    const processedData = processValue(data);
    return JSON.stringify(processedData);
  } catch (error) {
    console.error('处理JSON字符串失败:', error);
    return jsonStr;
  }
};