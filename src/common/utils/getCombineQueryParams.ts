import { parse } from "query-string";
/**
 *  获取题组中小题的参数
 * @returns 
 */
export function getCombineQueryParams() {
  const selfParams = parse(location.search);
  let params = selfParams;
  if (Number(selfParams.fromGroup) === 1 && window.parent) {
    params = {
      ...selfParams,
      ...parse(window.parent.location.search)
    }
  }
  params.fromGroup = selfParams.fromGroup === "1";
  // 题组内部的页面 自己不带plat
  if (Number(selfParams.fromGroup) && !selfParams.plat) {
    params.isGroupQst = 1;
  } else if(Number(selfParams.fromGroup) && selfParams.plat) {
    params.isGroupQst = 0;
  }
  return params;
}

export default getCombineQueryParams;
