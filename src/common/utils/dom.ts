export const on = (function() {
  // @ts-ignore
  if (document.addEventListener) {
    return function(element: any, event: any, handler: Function) {
      if (element && event && handler && element.addEventListener) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return function(element: any, event: any, handler: Function) {
      if (element && event && handler && element.attachEvent) {
        element.attachEvent("on" + event, handler);
      }
    };
  }
})();

export const off = (function() {
  // @ts-ignore
  if (document.removeEventListener) {
    return function(
      element: any,
      event: string,
      handler: EventListenerOrEventListenerObject,
    ) {
      if (element && event && element.removeEventListener) {
        element.removeEventListener(event, handler, false);
      }
    };
  } else {
    return function(
      element: any,
      event: string,
      handler: EventListenerOrEventListenerObject,
    ) {
      if (element && event && element.detachEvent) {
        element.detachEvent("on" + event, handler);
      }
    };
  }
})();
