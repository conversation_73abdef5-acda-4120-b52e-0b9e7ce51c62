export interface PageConfig {
  fetchPageData: () => Promise<any>,
  showBackBtn: boolean, // 是否显示左上角返回按钮
  showSketchUpload: boolean, // 是否显示Sketch上传按钮
  showUndoBtn: boolean, // 是否显示撤销按钮
  showRedoBtn: boolean, // 是否显示重做按钮
  showCreateBtn: boolean, // 是否显示创建按钮
  componentLibraries?: string[], // 组件库支持的类型集合 如果没有该字段，则展示全部
  asyncCompKeys: string[], // 是否加载了cocos
  SpineLibrary: () => Promise<any>, // spine组件库
  AudioLibrary: () => Promise<any>, // 音频组件库
  Material?: () => Promise<any>, // 素材库组件库
  NativeMaterial?: () => Promise<any>, // 素材库组件库
  getQuestionTypeList?: () => Promise<any>, // 获取题型列表
  getBundleUrl: (bundleName: string) => Promise<any>, // 获取bundle地址
  getPreviewData?: () => Promise<any>, // 获取预览数据,
  goPreview?: (data: any, bundleName?: string) => Promise<any>, // 跳转到预览
  uploadFile: (file: any) => Promise<any>, // 上传文件
  getAnswerPreviewUrl: () => string, // 获取设置答案的地址
}

enum PageType {
  online = 'online',
  native = 'native'
}


const getPageType = (): any => {
  if (process.env.VUE_APP_TYPE === 'native') {
    return 'native'
  }
  return 'online'
}

export const isNative = () => {
  return getPageType() === 'native'
}

export const getMethodByKey = async (pageConfig: PageConfig) => {
  console.log('getMethodByKey','pageConfig', pageConfig);
  return (key: string) => {
    (pageConfig as any)[key]
  };
}
