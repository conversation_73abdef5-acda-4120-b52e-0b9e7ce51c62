import { getQuestionTypeList } from "@/common/api/question";
import { fetchPageData } from "../../../pages/index/common/utils/intiPageData";
import { getPkgListNewByBundleName } from "../../../pages/index/common/utils/getPkgListNewByBundleName";
import { getPreviewData } from "../getPreviewData";
import { goPreview } from '../goPreview';
import { PageConfig } from "./index";
import { uploadFile } from "../request";
import { getAnswerPreviewUrl } from "../getAnswerPreviewUrl";
const pageConfig: PageConfig = {
  fetchPageData: fetchPageData,
  showBackBtn: true,
  showSketchUpload: false,
  showUndoBtn: true,
  showRedoBtn: true,
  showCreateBtn: true,
  asyncCompKeys: ["Material", "ScopedComponent"],
  SpineLibrary: async () => {
    const module = await import(/* webpackChunkName: "SpineLibrary" */ "@/pages/index/components/MaterialLibrary/PhpSpineLibrary");
    return module;
  },
  AudioLibrary: async () => {
    const module = await import(/* webpackChunkName: "AudioLibrary" */ "@/pages/index/components/MaterialLibrary/AudioLibrary");
    return module;
  },
  Material: async () => {
    const module = await import(/* webpackChunkName: "edit-init" */ "@/pages/index/components/Material/index.vue");
    return module;
  },
  getQuestionTypeList: getQuestionTypeList,
  getBundleUrl: getPkgListNewByBundleName, // 获取bundle地址
  getPreviewData: getPreviewData,
  goPreview: goPreview,
  uploadFile,
  getAnswerPreviewUrl
}
export default pageConfig;