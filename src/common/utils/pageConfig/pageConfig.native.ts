import { fetchQuestionData, getAnswerPreviewUrl, getProxyBundleUrl, uploadFile } from "../../nativeUtils";
import { getNativePreviewData } from "../../nativeUtils/getPreviewData";
import { nativeGoPreview } from "../../nativeUtils/goPreview";
import { PageConfig } from "./index";

const pageConfig: PageConfig = {
  fetchPageData: fetchQuestionData,
    showBackBtn: false,
    showSketchUpload: false,
    showUndoBtn: false,
    showRedoBtn: false,
    showCreateBtn: false,
    componentLibraries: ['label', 'sprite', 'spine', 'speaker'],
    asyncCompKeys: ["Material", "ScopedComponent"],
    SpineLibrary: async () => {
      const module = await import(/* webpackChunkName: "NativeSpineLibrary" */ "@/pages/index/components/MaterialLibrary/NativeSpineLibrary");
      return module;
    },
    AudioLibrary: async () => {
      const module = await import(/* webpackChunkName: "NativeAudioLibrary" */ "@/pages/index/components/MaterialLibrary/NativeAudioLibrary");
      return module;
    },
    Material: async () => {
      const module = await import(/* webpackChunkName: "NativeMaterialLibrary" */ "@/pages/index/components/MaterialLibrary/NativeMaterialLibrary/index.vue");
      return module;
    },
    getQuestionTypeList: async () => {
      return Promise.resolve({
        "errNo": 0,
        "errStr": "success",
        "data": []
    });
    },
    getBundleUrl: getProxyBundleUrl,
    getPreviewData: getNativePreviewData,
    goPreview:nativeGoPreview,
    getAnswerPreviewUrl,
    uploadFile
}

export default pageConfig;