// 加载cocos运行需要的js的方法 已经废弃 改成了插件注入js的方式
import { TimeMonitorType } from "./monitorUtil";
import jsNames from "@/../public/cocos/md5.json";

export function loadJs(inputUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const url = inputUrl;
    const script = document.createElement("script");
    script.charset = "utf-8";
    script.crossOrigin = "anonymous";
    document.head.appendChild(script);
    script.onload = function () {
      resolve();
    };
    script.onerror = function (e) {
      console.log(`fail load ${url}`);
      console.log(JSON.stringify(e));
      reject(e);
    };
    console.log("shark: 加载框架js：", url);
    script.src = url;
    document.getElementsByTagName("head")[0].appendChild(script);
  });
}

export const loadGameJs = async () => {
  (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.CocosScriptInit);
  const cocosPublicPath = Number(localStorage.getItem("__tips__")) !== 1 && process.env.VUE_APP_CDN_PATH ? `${process.env.VUE_APP_CDN_PATH}/interactive-question-editor/cocos/` : `cocos/`;
  const jsList = jsNames.map(name => cocosPublicPath + name);
  for (let i = 0; i < jsList.length; i++) {
    await loadJs(jsList[i]);
  }
  (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.CocosScriptInit);
}

export default loadJs;
