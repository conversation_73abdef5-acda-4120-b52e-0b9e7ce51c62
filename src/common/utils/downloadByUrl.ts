import axios from "axios";

const downloadByUrl = (url: string) => {
  axios({
    url,
    method: "GET",
    responseType: "blob"
  }).then(response => {
    const objectUrl = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = objectUrl;
    // h5导入的音频文件带权限的参数 需要先把参数去掉，再获取文件名
    let fileName = decodeURIComponent(url.split('?')[0])
      .split("/")
      .pop() || "temp.mp3";
    // 判断fileName是否有文件后缀名
    if (!fileName.includes(".")) {
      fileName += ".mp3";
    }
    link.setAttribute("download", fileName);
    document.body.appendChild(link);
    link.click();
  });
};

export default downloadByUrl;
