import { Message } from "element-ui";

const DEFAULT_MESSAGE = "接口错误，请重试";

export const showErrorMessage = (
  res:
    | {
        data: Response<{ errStr: string }>;
      }
    | Error,
) => {
  console.log('showErrorMessage', res);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (window as any).RangersSiteSDK && (window as any).RangersSiteSDK('captureException', res) 
  if (res instanceof Error) {
    Message({
      type: "error",
      message: res.message ?? DEFAULT_MESSAGE,
      duration: 3000,
    });
  } else if (res.data.errNo === -1001 && res.data.data && (res.data.data as any).includes('https://')) {
    // 如果是登录问题 打开登录页面
    Message({
      type: "error",
      message: '暂未登录，请在新标签页登陆',
      duration: 3000,
    });
    // window.open(res.data.data as any)
  } else {
    Message({
      type: "error",
      message: res.data.errStr ?? DEFAULT_MESSAGE,
      duration: 3000,
    });
  }
};
