import { isString } from "lodash-es";
import { off, on } from "./dom";
import { parse } from "query-string";

// 保存
export const CCHD_SAVE = "cchd-save";
// 返回上一页
export const CCHD_BACK = "cchd-back";
// 题组的单题编辑
export const GROUP_ITEM_EDIT = "group-question-change";
// 题组保存结果
export const GROUP_SAVE_RES = "group-save-res";
export const GROUP_SAVE_ERROR = "group-save-error";
// 题组小题数组和当前选中value
export const GROUP_SLIDES = "group-slides";
export const EDIT_SLIDES_CHANGE = "edit-slides-change";
export const SHOW_AUDIO_SETTING = "show-audio-setting";
export const GROUP_AUDIO_TIME = "group-audio-time";
export const GROUP_EDIT_FORM = "group-edit-form";
// 预览页面题目数据
export const PREVIEW_DATA = 'preview-data'


// 公参
const globalData = (() => {
  const { plat, kjtype } = parse(window.location.search);

  return {
    plat,
    kjtype,
  };
})();

/**
 * parentPostMessage
 * @param actionName
 * @param data
 * @param host
 */
export const parentPostMessage = (actionName: string, data: any, host = "*") => {
  // data = isString(data) ? data : JSON.stringify(data);
  console.log(`parentPostMessage: ${actionName}`, data);
  window.parent.postMessage(
    {
      action: actionName,
      data,
      ...globalData,
    },
    host,
  );
};

/**
 * postMessage
 * @param actionName
 * @param data
 * @param host
 */
 export const postMessage = (contentWindow: Window, actionName: string, data: any, host = "*") => {
  // data = isString(data) ? data : JSON.stringify(data);
  console.log(`postMessage: ${actionName}`, data);
  contentWindow.postMessage(
    {
      action: actionName,
      data,
      ...globalData,
    },
    host,
  );
};

/**
 * 监听指定action message
 * @param action
 * @param handler
 * @param prop
 * @param host
 */
export const onMessage = (
  action: string | string[] = "",
  handler: Function,
  prop = "action",
  host = "*",
) => {
  action = isString(action) ? [action] : action;
  const c = (e: any) =>
    e &&
    e.data &&
    e.data.action &&
    action.includes(e.data[prop]) &&
    handler(e.data);

  on(window, "message", c);

  return function offMessage() {
    if (c) {
      off(window, "message", c);
    }
  };
};

/**
 * 保存编辑数据
 * @param data
 */
export const ccSave = (data: any = {}) => {
  parentPostMessage(CCHD_SAVE, data);
};

/**
 * 返回上一页
 * @param data
 */
export const ccBack = (data: any = {}) => {
  parentPostMessage(CCHD_BACK, data);
};
