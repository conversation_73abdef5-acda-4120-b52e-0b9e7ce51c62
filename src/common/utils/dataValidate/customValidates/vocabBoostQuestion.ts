import { Message } from "element-ui";

/**
 * @description 单词大爆炸题校验
 */
const vocabBoostQuestion = (data: any) => {
  const { components = [] } = data;
  console.log('...vocabBoostQuestion...validate');
  const vocabBoostComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === 'vocabBoost');
  if (vocabBoostComponent) {
    const {
      properties: {
        stuLevelKills,
        stuLevelScores
      },
    } = vocabBoostComponent;
    // 判断stuLevelKills是否是递增的
    let isValid = true;
    // 比较stuLevelKills[0]和stuLevelScores[0]是否是递增的
    // 将stuLevelKills的key从小到大排序
    const stuLevelKillsKeys = Object.keys(stuLevelKills[0]).filter(key => key !== "_id").sort((a, b) => Number(a) - Number(b));
    const stuLevelScoresKeys = Object.keys(stuLevelScores[0]).filter(key => key !== "_id").sort((a, b) => Number(a) - Number(b));
    // 比较stuLevelKills和stuLevelScores的值是否是递增的
    const stuLevelKillsValues = stuLevelKillsKeys.map((key) => stuLevelKills[0][key]);
    const stuLevelScoresValues = stuLevelScoresKeys.map((key) => stuLevelScores[0][key]);
    const errors = [];
    if (stuLevelKillsValues.some((value, sindex) => value <= stuLevelKillsValues[sindex - 1])) {
      errors.push(`【怪兽】`);
      isValid = false;
    }
    if (stuLevelScoresValues.some((value, sindex) => value <= stuLevelScoresValues[sindex - 1])) {
      errors.push(`【分数】`);
      isValid = false;
    }
    if (errors.length > 0) {
      Message.error(`${errors.join('、')}的值不是递增的`);
    }
    return isValid;
  }
  return true;
};

export default vocabBoostQuestion;
