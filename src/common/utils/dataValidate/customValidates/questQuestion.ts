
import { Message } from "element-ui";

/**
 * @description 大招收集
 */
const questQuestion = (data: any) => {
  const { components = [] } = data;
  const questComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "quest");
  if (questComponent) {
    const {
      properties: { stuOption, stuSpineOptionList },
    } = questComponent;
    console.log("大招收集 stuOption", stuOption);
    console.log("大招收集 stuSpineOptionList", stuSpineOptionList);

    if (stuOption.options.length > stuSpineOptionList.length) {
      Message.error(`卡片动画数量不可少于选项数量`);
      return false;
    }
    return true;
  }
};

export default questQuestion
