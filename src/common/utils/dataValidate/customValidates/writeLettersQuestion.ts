/*
 * @FilePath     : /src/common/utils/dataValidate/customValidates/writeLettersQuestion.ts
 * <AUTHOR> wanghuan 
 * @description  : 
 * @warn         : 
 */
import { Message } from "element-ui";

/**
 * @description 写单词题校验
 */
const writeLettersQuestion = (data: any) => {
    const { components = [] } = data;

    const compData = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === 'writeLetters');
    if (compData) {
        const {
            properties: {
                stuWriteTime
            },
        } = compData;
        // 循环 stuWriteTime: [
        //   {
        //     "1": "0",
        //     "2": "15",
        //     "3": "30"
        //   }
        // ]
        // 判断stuWriteTime的值是否是递增的
        const errors: string[] = [];
        let isValid = true;
        const _checkData = stuWriteTime[0];
        if(Number(_checkData["2"]) <= Number(_checkData["3"])){
            errors.push(`设置完成时间不对`);
            isValid = false;
        }
        console.log('errors', errors);
        if (errors.length > 0) {
            Message.error(`${errors.join('、')}`);
        }
        return isValid;
    }
    return true;
};

export default writeLettersQuestion;
