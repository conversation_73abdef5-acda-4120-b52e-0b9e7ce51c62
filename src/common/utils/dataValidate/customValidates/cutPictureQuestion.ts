import { Message } from "element-ui";

/**
 * @description 切割题——单次直线滑动
 */
const cutPictureQuestionValidate = (data: any) => {
  const components: Components = data.components;
  const cutPictureQuestionComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "cutPicture");

  if (!cutPictureQuestionComponent) {
    Message.error("至少存在一个切割元素");
    return false;
  }
  return true;
};

export default cutPictureQuestionValidate;
