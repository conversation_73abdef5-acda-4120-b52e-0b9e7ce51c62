import { Message } from "element-ui";

/**
 * 拖拽交换位置
 */
const swapPositions = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "swapPositions");
  if (currComponent) {
    const {
      properties: { stuOperationConfigs },
    } = currComponent;
    let totalCount = 0;
    stuOperationConfigs.forEach((item: { count: number; }) => {
      totalCount += item.count;
    }
    )
    if (totalCount > 10) {
      Message.error('数量设置总和不能超过10.');
      return false;
    }
  }
  return true;
};

export default swapPositions;
