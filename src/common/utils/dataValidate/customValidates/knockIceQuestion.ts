import { Message } from "element-ui";

/**
 * 敲冰块题
 */
const knockIceQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "knockIce");
  const { properties: { stuCustomsPass, stuNice, niceCombo, stuGood, goodCombo, stuExcellent, excellentCombo, niceIntegralCoefficient, goodIntegralCoefficient, excellentIntegralCoefficient } } = currComponent;
  // 判断stuCustomsPass中的每一项中都存在stuIceData且长度大于0
  let hasBadConfiged = false;
  let hasBadCombo = false;
  const counts: number[] = [];
  const integralCoefficientCounts: number[] = [];
  if (stuNice === "1") {
    counts.push(niceCombo)
    integralCoefficientCounts.push(niceIntegralCoefficient)
  }
  if (stuGood === "1") {
    counts.push(goodCombo)
    integralCoefficientCounts.push(goodIntegralCoefficient)
  }
  if (stuExcellent === "1") {
    counts.push(excellentCombo)
    integralCoefficientCounts.push(excellentIntegralCoefficient)
  }
  // counts 是否是递增的
  const isIncrease = counts.every((count, index) => {
    if (index === 0) return true;
    return count > counts[index - 1];
  });
  console.log('integralCoefficientCounts', integralCoefficientCounts);
  const isICIncrease = integralCoefficientCounts.every((count, index) => {
    if (index === 0) return true;
    return count > integralCoefficientCounts[index - 1];
  });
  // niceIntegralCoefficient
  if (!isIncrease || !isICIncrease) {
    hasBadCombo = true;
  }
  stuCustomsPass.forEach((item: {
    excellentIntegralCoefficient: number;
    goodIntegralCoefficient: number;
    niceIntegralCoefficient: number;
    excellentCombo: number;
    stuExcellent: string;
    goodCombo: number;
    niceCombo: number;
    stuGood: string;
    stuNice: string; stuIceData: any[];
  }) => {
    console.log('item.stuIceData', item);
    if (!item.stuIceData || item.stuIceData.length < 1) {
      hasBadConfiged = true;
    }

  });
  if (hasBadConfiged) {
    Message.error(`存在未生成题目的小题，请检查`);
    return false;
  }
  if (hasBadCombo) {
    Message.error(`Combo机制的连击次数/积分系数必须是递增逻辑`);
    return false;
  }

  return true;
};

export default knockIceQuestion;
