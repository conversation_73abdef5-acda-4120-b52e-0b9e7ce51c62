import { Message } from "element-ui";

/**
 * 等积变形题校验
 */
const areaShifterQuestion = (data: any) => {
  const { components = [] } = data;
  const areaShifterComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "areaShifter");
  if (areaShifterComponent) {
    const {
      properties: { stuConnectPointConfigs, stuFixedConnectPointConfigs },
    } = areaShifterComponent;
    // stuConnectPointConfigs 可移动连接点关系配置
    // stuFixedConnectPointConfigs 不可移动连接点关系配置
    const pointConfigs = [...stuConnectPointConfigs, ...stuFixedConnectPointConfigs];
    let hasEndRepeatWithStart = false;
    pointConfigs.forEach((item: { start: string; end: string[]; }) => {
      const { start, end } = item;
      if (end.includes(start)) {
        hasEndRepeatWithStart = true;
      }
    })
    if (hasEndRepeatWithStart) {
      Message.error(`连接点起点与终点不可相同`);
      return false;
    }
  }
  return true;
};

export default areaShifterQuestion;
