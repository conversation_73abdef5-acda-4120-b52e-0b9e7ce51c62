import { Message } from "element-ui";

/**
 * 帮帮红包雨
 */
const redBagQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "redBag");
  const { properties: { stuSubsectionList } } = currComponent;
  const ratioList = stuSubsectionList.map((item: { ratio: number }) => item.ratio);
  // 判断ratioList 是否是递增的
  let errorIndex = -1;
  const isIncrease = ratioList.every((item: number, index: number) => {
    if (index === 0) return true;
    if (item <= ratioList[index - 1]) {
      errorIndex = index;
    }
    return item > ratioList[index - 1];
  });
  if (!isIncrease) {
    Message.error(`分段${errorIndex + 1}"比例极值"表单配置有误`);
    return false;
  }
  return true;
};

export default redBagQuestion;
