import { Message } from "element-ui";

/**
 * 立方体拖放
 */
const operationCubesQuestion = (data: any) => {
  const { components = [] } = data;
  console.log(components);
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "operationCubesQuestion");
  const { properties: { stuAnswers, stuOperations } } = currComponent;
  let hasMiddleEmpty = false;
  stuOperations.forEach((item: { layerConfigs: any; }, index: number) => {
    if (index !== 0) {
      const layerConfigs = item.layerConfigs;
      layerConfigs.forEach((layer: any[], layerIndex: string | number) => {
        layer.forEach((cell, cellIndex) => {
          // 找到cell的上一层cell
          const prevCell = stuOperations[index - 1].layerConfigs[layerIndex][cellIndex];
          if (cell === 1 && prevCell === 0) {
            hasMiddleEmpty = true;
          }
        });
      });
    }
  });

  if (hasMiddleEmpty) {
    Message.error(`操作区图形配置项有错误，请检查`);
    return false;
  }


  let answerIsValid = false;

  stuAnswers.forEach(({ layerConfigs }: { layerConfigs: number[][]; }) => {
    layerConfigs.forEach((layer: any) => {
      if (layer.includes(1)) answerIsValid = true;
    });
  });
  if (!answerIsValid) {
    Message.error(`正确答案表单为必填项`);
    return false;
  }
  return true;
};

export default operationCubesQuestion;
