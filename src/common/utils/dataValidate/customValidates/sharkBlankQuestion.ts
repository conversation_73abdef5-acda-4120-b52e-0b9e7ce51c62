import { Message } from "element-ui";

/**
 * @description 填空题——all有答案
 */
const siweiBlankFindAllValidate = (data: any) => {
  const { extraDataMap = {} } = data;
  let id;
  let haveBlock = false;
  const hasCurrectAnswer = (() => {
    for (const key in extraDataMap) {
      const item = extraDataMap[key];
      if (item.tag === "blankModule") {
        if (item.hasAllCorrect == false) {
          id = key;
          return 2; // 有空没答案
        }
        haveBlock = true;
      }
    }
    if (!haveBlock) {
      return 1; // 没空
    }
    return 0;
  })();

  if (hasCurrectAnswer) {
    const mes = hasCurrectAnswer === 2 ? `请填写完整组件【 ${id} 】的业务属性【答案】` : "请设置答题区";
    Message.error(mes);
    return false;
  }
  return true;
};

export default siweiBlankFindAllValidate;
