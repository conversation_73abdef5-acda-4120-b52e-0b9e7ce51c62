import { Message } from "element-ui";

/**
 * 翻杯子
 */
const cupTossQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "cupToss");
  const { properties: { stuImageConfigs, stuRows } } = currComponent;
  if (stuRows === 1 && stuImageConfigs.length > 11) {
    Message.error(`图形1行分布时，数量不可超过11`);
    return false;
  }
  return true;
};

export default cupTossQuestion;
