import { Message } from "element-ui";

/**
 * 多空连答
 */
const multipleEmptyQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "multipleEmptyQuestion");
  const { properties: { stuBlankOptions } } = currComponent;
  // 空格的个数 至少为1
  if (!stuBlankOptions.length) {
    Message.error(`请至少设置一个空格`);
    return false;
  }
  return true;
};

export default multipleEmptyQuestion;
