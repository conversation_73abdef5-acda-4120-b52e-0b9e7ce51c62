import { Message } from "element-ui";

/**
 * @description 填空题——all有答案
 */
const countQuestionValidate = (data: any) => {
  const { components = [] } = data;
  let state = true;
  components.forEach((cmpt: any) => {
    if (cmpt.type === "specialComponent" && cmpt.subType === "counter") {
      const properties = cmpt.properties;
      const countNum = properties.countNum;
      if (!countNum) {
        Message.error("请填写计数器数字");
        state = false;
        return;
      } else {
        if ((countNum as number).toString().indexOf(".") !== -1 || countNum <= 0 || (countNum as number).toString().length > 10) {
          Message.error("计数器数字需为1-9999999999的整数");
          state = false;
          return;
        }
      }
    }
  });

  return state;
};

export default countQuestionValidate;
