import { Message } from "element-ui";

/**
 * @description 主观题目数据校验
 */
const subjectiveQuestion = (data: any) => {
  const { extraDataMap = {} } = data;
  let havePicBtn = false;
  let havePic = false;
  let haveLabelBtn = false;
  let haveLable = false;
  let isFistNum = 0;
  for (const key in extraDataMap) {
    const item = extraDataMap[key];
    if (item.tag === "writingBtn") {
      haveLabelBtn = true;
      if (item.isFirst) {
        isFistNum++;
      }
    } else if (item.tag === "pictureBtn") {
      havePicBtn = true;
      if (item.isFirst) {
        isFistNum++;
      }
    } else if (item.tag === "pictureBlank") {
      havePic = true;
    } else if (item.tag === "writingBlank") {
      haveLable = true;
    }
  }

  if (havePicBtn && havePic && haveLabelBtn && haveLable && isFistNum == 1) {
    return true;
  } else if (havePicBtn && havePic && isFistNum == 1 && (haveLabelBtn || haveLable) == false) {
    return true;
  } else if (haveLabelBtn && haveLable && isFistNum == 1 && (havePicBtn || havePic) == false) {
    return true;
  }
  // 模式校验
  if (!haveLabelBtn && !haveLable && !havePicBtn && !havePic) {
    Message.error("打字模式和拍照模式必须设置1组");
    return false;
  } else if ((haveLabelBtn || haveLable) && (haveLabelBtn && haveLable) == false) {
    if (!haveLabelBtn) {
      Message.error("需要配置打字按钮");
    } else {
      Message.error("需要配置文字展示区");
    }
    return false;
  } else if ((havePicBtn || havePic) && (havePicBtn && havePic) == false) {
    if (!havePicBtn) {
      Message.error("需要配置拍照按钮");
    } else {
      Message.error("需要配置图片展示区");
    }
    return false;
  }
  // 优先级校验
  if (isFistNum == 0) {
    if (havePicBtn && havePic && haveLabelBtn && haveLable) {
      Message.error("打字模式和拍照模式必须设置1个为默认优先");
    } else if (havePicBtn && havePic) {
      Message.error("请设置拍照按钮‘优先展示拍照模式’");
    } else if (haveLabelBtn && haveLable) {
      Message.error("请设置打字按钮‘优先展示文本模式’");
    }
    return false;
  } else if (isFistNum > 1) {
    Message.error("打字模式和拍照模式只能设置1个为默认优先");
    return false;
  }
  return false;
};

export default subjectiveQuestion;
