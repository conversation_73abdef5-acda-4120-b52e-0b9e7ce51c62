import { Message } from "element-ui";

/**
 * 算式连连看
 */
const mathLinkUpQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "mathLinkUp");
  const { properties: { stuTimeMode, stuLevelConfigs, stuGameMode, stuItemTextList, stuItemImgList, stuGenQuestion, stuRow, stuCol } } = currComponent;
  if (!stuGenQuestion || !stuGenQuestion.length) {
    Message.error(`未生成题面`);
    return false;
  }
  // stuGameMode "0" 图片 "1" 文本 stuBoardType
  const totalUpLimit = stuRow * stuCol / 2;
  const tempList = stuGameMode === "0" ? stuItemImgList : stuItemTextList;
  const totalLen = tempList.reduce((prev: any, curr: any) => {
    if (stuGameMode === "0") return prev + curr.count;
    return prev + curr.textList?.options?.length;
  }, 0);
  console.log('totalLen', totalLen, stuGameMode);
  if (totalLen !== totalUpLimit) {
    Message.error(`元素中的图片或文本的对数总和必须等于${totalUpLimit}`);
    return false;
  }
  // 计时方式 stuTimeMode "0" 正计时 "1"倒计时
  const valueList = stuLevelConfigs.map((item: { levelValue: any; }) => item.levelValue);
  if (stuTimeMode === "1") {
    valueList.reverse();
  }
  // valueList 是否是递增的
  const isIncrease = valueList.every((item: number, index: number) => {
    if (index === 0) return true;
    return item > valueList[index - 1];
  });
  if (!isIncrease) {
    console.log('totalLen', valueList, isIncrease);
    Message.error(`等级配置不正确，请检查`);
    return false;
  }
  return true;
};

export default mathLinkUpQuestion;
