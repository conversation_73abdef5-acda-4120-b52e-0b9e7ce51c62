import { Message } from "element-ui";

/**
 * @description 连线题 至少包含一组可连接的元素和答案
 */
const connectionQuestionValidate = (data: any) => {
  const components: Components = data.components;
  const pairingObjectAll: any = [];
  const componentsIdAll: any = [];
  // let validateOne = true;
  components.forEach(component => {
    if (component.tag === "dragArea") {
      pairingObjectAll.push(...component.extra.pairingObject);
    }
    if (component.tag === "ligature") {
      componentsIdAll.push(component.id);
    }
  });
  // if (!validateOne) {
  //   Message.error("连线元素答题区必须配置连线点");
  //   return false;
  // }
  for (let i = 0; i < pairingObjectAll.length; i++) {
    let havePair = false;
    const pairingObjectId = pairingObjectAll[i];
    for (let j = 0; j < componentsIdAll.length; j++) {
      if (pairingObjectId == componentsIdAll[j]) {
        havePair = true;
      }
    }
    if (havePair == false) {
      Message.error("连线元素" + pairingObjectId + "未找到");
      return false;
    }
  }

  let validateTwo = false;
  pairingObjectAll.forEach((id: number) => {
    componentsIdAll.forEach((componentsId: number) => {
      if (id === componentsId) {
        validateTwo = true;
      }
    });
  });
  if (!validateTwo) {
    Message.error("至少包含一组可连接的元素和答案");
    return false;
  }
  return true;
};

export default connectionQuestionValidate;
