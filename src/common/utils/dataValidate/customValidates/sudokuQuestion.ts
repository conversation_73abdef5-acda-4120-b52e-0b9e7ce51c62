/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-28 18:07:33
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-07-02 15:39:00
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/sudokuQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";
import { cloneDeep } from "lodash-es";

const distance = (pos1: string, pos2: string) => {
  const [x1, y1] = pos1.split(',');
  const [x2, y2] = pos2.split(',');
  const dx = Math.abs(Number(x2) - Number(x1));
  const dy = Math.abs(Number(y2) - Number(y1));
  return Math.sqrt(dx * dx + dy * dy);
}
export const groupAdjacentColors2 = (matrix: any[]) => {
  const groups = {};
  let hasBlank = false;
  console.log('groupAdjacentColors', matrix);
  matrix.forEach((item, i) => {
    item.forEach((subItem: { color: any; }, j: any) => {
      // 按颜色分组
      const val = subItem.color;
      if (!val) hasBlank = true;

      if (val && groups[val]) {
        groups[val].pos.push(`${i},${j}`);
      } else {
        groups[val] = {
          pos: [`${i},${j}`],
          parts: []
        };
      }
    })
  })
  // groups中再次分组
  Object.keys(groups).forEach((color) => {
    const group = groups[color];
    const parts: any[] = [];
    let curPartIndex = 0;
    const visitedPos: any[] = [];
    // parts pos
    const tempPos = [...group.pos];
    const pos = group.pos;
    console.log(JSON.stringify(pos));
    while (pos.length >= 1) {
      const cur = pos.shift();
      if (visitedPos.includes(cur)) {
        console.log('visitedPos', visitedPos, 'cur', cur)
        continue;
      };
      // cur是否已经处理过 处理过忽略
      const findAdjacent = tempPos.filter(nextPos => distance(cur, nextPos) === 1);
      console.log('findAdjacent', color, findAdjacent);


      // 相邻个数为0 
      if (findAdjacent.length === 0) {
        if (parts[curPartIndex] && parts[curPartIndex].length) {
          curPartIndex = parts.length;
        }
      } else {
        // 相邻的元素都不在这个组里 另起一组
        // const thePartHasAdjacent = findAdjacent.find(item => parts[curPartIndex].includes(item));
        // 找到相邻元素所在的组
        const tempIndex = parts.findIndex((part) => {
          const thePartHasAdjacent = findAdjacent.find(item => part.includes(item));
          if (thePartHasAdjacent) return true;
        })
        console.log('tempIndex', tempIndex, parts);
        if (tempIndex > -1) {
          curPartIndex = tempIndex;
        } else {
          if (parts[curPartIndex] && parts[curPartIndex].length) {
            curPartIndex = parts.length;
          }
        }
      }

      console.log(color, 'pos', cur, '相邻个数', findAdjacent.length, '分组Index', curPartIndex, JSON.stringify(parts));
      if (!parts[curPartIndex]) parts[curPartIndex] = [];
      console.log('curPartIndex', curPartIndex, 'cur', cur, JSON.stringify(parts[curPartIndex]));
      if (!parts[curPartIndex].includes(cur)) {
        parts[curPartIndex].push(cur);
      }
      if (!visitedPos.includes(cur)) {
        visitedPos.push(cur);
      }
      if (findAdjacent.length) {
        findAdjacent.forEach(item => {
          if (!parts[curPartIndex].includes(item)) {
            parts[curPartIndex].push(item);
          }
          // if (!visitedPos.includes(item)) {
          //   visitedPos.push(item);
          // }
        })
      }
      console.log('parts[curPartIndex]', JSON.stringify(parts[curPartIndex]))
    }
    group.parts = parts;
  })

  console.log('groupAdjacentColors-groups2', groups);
  return { groups, hasBlank };
};

export const groupAdjacentColors3 = (matrix: any[]) => {
  const groups = {};
  const groupIds: string[] = [];
  // { color pos }
  let hasBlank = false;
  console.log('groupAdjacentColors', matrix);
  matrix.forEach((item, i) => {
    item.forEach((subItem: {
      groupId: any; color: any;
    }, j: any) => {
      // 按颜色分组
      // const groupId = subItem.groupId;
      const val = subItem.color;
      if (!val) hasBlank = true;

      // const pos = [`${i},${j}`];
      if (val && groups[val]) {
        groups[val].pos.push(`${i},${j}`);
      } else {
        if (val) {
          groups[val] = {
            pos: [`${i},${j}`],
            parts: [],
          };
        }
      }
    })
  })
  console.log('groupAdjacentColors3-groups', groups);
  // groups中再次分组
  Object.keys(groups).forEach((color) => {
    if (!color) return;
    const group = groups[color];
    const parts = [];
    let curPartIndex = 0;
    const visitedPos: any[] = [];
    // parts pos
    const pos = group.pos;
    console.log(JSON.stringify(pos));
    let curQueue = [pos.shift()];
    while (curQueue.length) {
      const cur = curQueue.shift();
      // console.log('cur-while-start', 'cur', cur, 'pos', JSON.stringify(pos), 'curQueue', curQueue)
      if (!cur) {
        break;
      };
      // cur是否已经处理过 处理过忽略
      if (visitedPos.includes(cur)) {
        console.log('visitedPos', visitedPos, 'cur', cur)
        continue;
      } else {
        visitedPos.push(cur);
      }
      // 查找相邻的
      const findAdjacent = pos.filter((nextPos: string) => distance(cur, nextPos) === 1);
      const visitedAdjacent = (visitedPos || []).filter(nextPos => distance(cur, nextPos) === 1);
      // console.log('findAdjacent.length', findAdjacent.length, 'visitedAdjacent.length', visitedAdjacent.length, 'cur', cur, 'pos', pos, 'parts[curPartIndex]', parts[curPartIndex]);
      // curr 无 pos无
      // 新起点？
      if (!visitedAdjacent.length) {
        curPartIndex = parts.length;
        // console.log('cur--起点', cur);
        if (!parts[curPartIndex]) {
          parts[curPartIndex] = ([cur]);
        } else if (!parts[curPartIndex].includes(cur)) {
          parts[curPartIndex].push(cur);
        }
      } else if (visitedAdjacent.length && !findAdjacent.length) {

        // 找到visitedAdjacent所在的parts
        const visitedAdj = visitedAdjacent[0];
        curPartIndex = parts.findIndex((part) => {
          return !!part.find(subPart => subPart === visitedAdj)
        })
        // 终点
        // console.log('cur--终点', cur, curPartIndex);
        if (!parts[curPartIndex]) {
          parts[curPartIndex] = ([cur]);
        } else if (!parts[curPartIndex].includes(cur)) {
          parts[curPartIndex].push(cur);
        }
      } else {
        // 中间点 有可能是多个
        // console.log('cur--中间', cur);
        if (!parts[curPartIndex]) {
          parts[curPartIndex] = ([cur]);
        } else if (!parts[curPartIndex].includes(cur)) {
          parts[curPartIndex].push(cur);
        }
      }

      const posIndex = pos.findIndex((nextPos: any) => cur === nextPos);
      if (posIndex >= 0) {
        pos.splice(posIndex, 1);
      }

      if (findAdjacent.length) {
        curQueue = [...curQueue, ...findAdjacent]
      }
      if (!curQueue.length) {
        curQueue.push(pos.shift())
      }
      // curQueue.forEach((item) => {
      //   const posIndex = pos.findIndex(nextPos => item === nextPos);
      //   pos.splice(posIndex, 1);

      // })
      // console.log('parts[curPartIndex]', JSON.stringify(parts[curPartIndex]))
      // console.log('cur--while-end', 'curQueue', curQueue, 'cur', cur, 'pos', JSON.stringify(pos))
      curQueue = [...new Set(curQueue)]
    }
    // curGroupIndex
    // const groupId = subItem.groupId;
    const groupIds: any[] = [];
    parts.forEach((part, i) => {
      let groupId = '';
      part.forEach((subPart) => {
        const [x1, y1] = subPart.split(',');
        if (!groupId && matrix[x1][y1].groupId) {
          groupId = matrix[x1][y1].groupId
        }
      });
      if (groupId) {
        groupIds[i] = groupId;
      } else {
        // curGroupIndex++;
        groupIds[i] = '';
      }
    })
    group.parts = parts;
    group.groupIds = groupIds;
  })

  console.log('groupAdjacentColors-groups3', groups);
  return { groups, hasBlank, groupIds };
};

const stuSodukuValid = (stuSoduku: { cells: any[]; type: string; isMark: string; row: number; groups: any[]; col: any; }) => {
  let isValid = true;
  // 标准/不等号数独+标记宫 验证 isMark === "1"  type === "0"
  const { groups, hasBlank } = groupAdjacentColors3(stuSoduku.cells);
  // 格子未配置满 标准/不等号数独+标记宫(否)之外都需要验证
  if (!(["0", "4"].includes(stuSoduku.type) && stuSoduku.isMark === "0")) {
    if (hasBlank) {
      Message.error('配置了颜色的格子才会形成宫或区域，不可存在未配置的格子');
      isValid = false;
      return isValid;
    }
  }
  // 相邻&相同数量限制 标准+宫 异性 杀手+宫
  if (stuSoduku.isMark === "1" || stuSoduku.type === "1") {
    console.log('标准+宫/异性/杀手+宫-颜色数量验证');
    let countRowValid = true;
    Object.values(groups).forEach(item => {
      (item as any).parts.forEach((part: string | any[]) => {
        if (part.length > stuSoduku.row) {
          countRowValid = false;
        }
      })
    })
    if (!countRowValid) {
      Message.error(`相邻格子相同的颜色数量不能超过${stuSoduku.row}个`);
      isValid = false;
      return isValid;
    }
  }
  // 题板内容配置-不能重复 
  // 行列不能重复
  // 宫/区域不能重复（标准+宫/异性/杀手+宫/）
  // 宫/区域内数字不能重复（标准+宫/异性/杀手+宫/）
  // 杀手+宫 找到组中的第一个 并将组的text复制给cell
  stuSoduku.groups.forEach((group) => {
    // group.text group.id
    let findFirst = false;
    stuSoduku.cells.find(item => {
      item.forEach((cell: { groupId: any; text: any; }) => {
        if (cell.groupId === group.id && !findFirst) {
          cell.text = group.text;
          findFirst = true;
        }
      })
    })
  })
  let numberNoRepeatValid = true;
  const groupIds: any[] = [];
  stuSoduku.cells.forEach((item, i) => {
    item.forEach((cell: { text: any; groupId: any; }, j: number) => {
      const val = cell.text;
      if (cell.groupId && !groupIds.includes(cell.groupId)) {
        groupIds.push(cell.groupId);
      }
      if (val) {
        const rowNumbers = item.map((item: { text: any; }) => item.text).filter((item: any) => item === val);
        const colNumbers = Array.from({ length: stuSoduku.col }, (_, index) => stuSoduku.cells[index][j].text).filter(item => item === val);
        // 找到组
        let groupNumbers: string | any[] = [];
        if (stuSoduku.isMark === "1" || stuSoduku.type === "1") {
          Object.values(groups).forEach(item => {
            (item as any).parts.forEach((part: string[]) => {
              if (part.includes(`${i},${j}`)) {
                console.log('找到组了，组是', part);
                groupNumbers = part.map((pItem) => {
                  const [x1, y1] = pItem.split(',');
                  return stuSoduku.cells[Number(x1)][Number(y1)].text;
                }).filter(item => item === val)
              }
            })
          })
        }
        // 找到组中的第一个
        if (rowNumbers.length > 1 || colNumbers.length > 1 || groupNumbers.length > 1) {
          numberNoRepeatValid = false;
          console.log('数字重复了', rowNumbers, colNumbers, groupNumbers);
        }
      }
    });
  })
  if (!numberNoRepeatValid) {
    Message.error(`请检查题板内容配置`);
    isValid = false;
    return isValid;
  }
  // 虚线框配置（运算/杀手数独） 
  if (stuSoduku.type === "2" || stuSoduku.type === "3") {
    // 至少需要配置1个虚线框
    if (!groupIds.length) {
      Message.error(`至少需配置1个虚线框`);
      isValid = false;
      return isValid;
    }
    // 结果校验
    const label = stuSoduku.type === "2" ? '结果' : '结果&符号'
    const findBlankExtraText = stuSoduku.groups.find(group => !group.extraText);
    if (findBlankExtraText || groupIds.length > stuSoduku.groups.length) {
      Message.error(`“虚线框内的${label}”为必填项`);
      isValid = false;
      return isValid;
    }
  }
  return isValid;
}

(window as any).stuSodukuValid = stuSodukuValid;

// console.log('stuSodukuValid', stuSodukuValid);

/**
 * 数独
 */
const sudokuQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "sudoku");
  const { properties: { stuTimingType, stuStarTime3, stuStarTime2, stuStarTime1, stuSoduku } } = currComponent;

  // 宫格的配置
  const cloneStuSoduku = cloneDeep(stuSoduku);
  const stuSodukuDataValid = stuSodukuValid(cloneStuSoduku);
  if (!stuSodukuDataValid) return stuSodukuDataValid;

  const isIncrease = parseInt(stuTimingType) === 2 ? true : stuStarTime3 < stuStarTime2 && stuStarTime2 < stuStarTime1;
  console.log("sudokuQuestion 时间极值校验结果",isIncrease)
  if (!isIncrease) {
    Message.error(`时间极值表单配置有误`);
    return false;
  }
  return true;
};

export default sudokuQuestion;

