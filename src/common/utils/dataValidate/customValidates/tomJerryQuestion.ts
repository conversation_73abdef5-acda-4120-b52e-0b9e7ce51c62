import { Message } from "element-ui";

/**
 * 猫鼠游戏
 */
const tomJerryQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "tomJerry");
  const { properties: { stuCol, stuRow, stuQuestions, stuAnswerConfigs } } = currComponent;
  // 初始题板”中不可不配置图案或全部格点都配置图案
  //  “正确答案”表单为必填项
  let colLen = 0;
  const colMax = stuCol * stuRow;
  stuQuestions.forEach((item: any[]) => {
    item.forEach((sItem) => {
      if (typeof sItem === 'number') {
        colLen++;
      }
    })
  })
  let answerColLen = 0;
  stuAnswerConfigs.forEach((item: any[]) => {
    item.forEach((sItem) => {
      if (sItem === 1) {
        answerColLen++;
      }
    })
  })
  if (colLen === 0 || colLen >= colMax) {
    Message.error(`初始题板”中不可不配置图案或全部格点都配置图案`);
    return false;
  }
  if (answerColLen === 0) {
    Message.error(`“正确答案”表单为必填项`);
    return false;
  }
  return true;
};

export default tomJerryQuestion;
