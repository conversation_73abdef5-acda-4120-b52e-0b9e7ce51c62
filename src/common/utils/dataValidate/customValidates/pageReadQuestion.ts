/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2025-02-27 16:15:53
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-02-27 18:01:49
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/pageReadQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import store from "@/pages/index/store";
import { Message } from "element-ui";

/**
 * 翻页阅读的校验逻辑
 * @description 1. 字段说明：stuStageCompIds 关联的富文本id，stuCountdown 倒计时设置，stuMinReadTime 最少阅读时间
 * 2. 层级关系的重置：关联的富文本组件的层级必须跟页码保持一致，第一页的富文本在最上，最后一页的富文本在最下；和gedengyang协商一致后，将重置层级的逻辑设置为：阅读组件在最底层，页码关联的富文本组件按照倒序进行排列（第一页放在最上），剩余的组件按照原来的层级进行排列
 * 3. 倒计时设置必须大于最少阅读时间
 */
const pageReadQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "pageRead");
  const { properties: { stuCountdown, stuMinReadTime, stuCountdownRed } } = currComponent;
  //倒计时变红时间必须小于倒计时时间
  if (stuCountdownRed >= stuCountdown) {
    Message.error(`倒计时变红时间必须小于倒计时时间`);
    return false;
  }
  // 倒计时设置必须大于最少阅读时间
  if (stuCountdown <= stuMinReadTime) {
    Message.error(`倒计时设置必须大于最少阅读时间`);
    return false;
  }
  return true;
};

export default pageReadQuestion;
