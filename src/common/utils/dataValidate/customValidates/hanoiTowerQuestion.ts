import { Message } from "element-ui";

/**
 * @description 汉诺塔
 */
const hanoiTowerQuestion = (data: any) => {
  const { components = [] } = data;
  const hanoiTowerComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "hanoiTower");
  if (hanoiTowerComponent) {
    const {
      properties: { baseTexture, colunmTexture, colunmActiveTexture, answerColunm, discs },
    } = hanoiTowerComponent;

    if (!baseTexture) {
      Message.error(`请上传底座图片`);
      return false;
    }
    if (!colunmTexture) {
      Message.error(`请上传柱子图片`);
      return false;
    }
    if (!colunmActiveTexture) {
      Message.error(`请上传点亮柱子图片`);
      return false;
    }
    if (!answerColunm.length) {
      Message.error(`正确答案不能为空`);
      return false;
    }
    const numbers = discs.map((_item: { number: number }) => _item.number).sort();
    const minNumber = numbers[0];
    // const findRandomNumber = numbers.find((num: number, index: number) => (num - minNumber) !== index);
    if (!minNumber) {
      Message.error(`圆盘顺序不能为空`);
      return false;
    }
  } else {
    Message.error(`必须存在一个汉诺塔元素`);
    return false;
  }
  return true;
};

export default hanoiTowerQuestion;
