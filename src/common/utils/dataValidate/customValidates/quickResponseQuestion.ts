import { Message } from "element-ui";

/** 
 * 快速回答 校验
 * @param data 
 */

const quickResponseQuestion = function (data: any) {
  const { components = [] } = data;

  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "quickResponse");
  const { properties: { stuOptionListLeft, stuOptionListRight, stuLevelConfigs } } = currComponent;

  const num = stuOptionListLeft.length + stuOptionListRight.length;
  // 校验选项数量
  if (num > 30) {
    Message.error(`选项相加的总数量≤30`);
    return false;
  }
  const isIncrease = stuLevelConfigs.every((rightCount: any, index: number) => {
    if (index === 0) return true;
    return rightCount.rightCount < stuLevelConfigs[index - 1].rightCount;
  });
  if (!isIncrease) {
    Message.error(`等级配置不正确，请检查`);
    return false;
  }
  // 循环stuLevelConfigs 查看每个rightCount 大小是否小于num
  const isEver = stuLevelConfigs.every((right: any, index: number) => {
    if (right.rightCount > num) {
      Message.error(`${3 - Number(index)} 星等级配置的正确数量不能大于总选项数`);
    }
    return right.rightCount <= num;
  });
  if (!isEver) {

    return false;
  }
  console.log("quickResponseQuestion  end");
  return true;
}

export default quickResponseQuestion;
