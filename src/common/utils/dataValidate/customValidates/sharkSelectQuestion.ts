import { Message } from "element-ui";

/**
 * @description 选择题——找出所有正确元素
 */
const siweiSelectFindAllValidate = (data: any, name?: string) => {
  const { components = [] } = data;
  let hasCorrectAnswer = false;
  let correctAnswerNumber = 0;
  components.forEach((compt: any) => {
    if (compt.tag === "answer" && compt.extra.isCorrect === true) {
      hasCorrectAnswer = true;
      correctAnswerNumber++;
    }
  });
  if (localStorage.getItem("groupCategory") === "1102") {
    // 听后选择只支持一个选项
    if (!hasCorrectAnswer) {
      Message.error(`${name ? name + ":" : ""}请设置一个正确选项`);
      return false;
    } else if (correctAnswerNumber > 1) {
      Message.error(`${name ? name + ":" : ""}该题型只能设置一个正确选项`);
      return false;
    }
  }
  if (!hasCorrectAnswer) {
    // Message.error("请至少设置一个正确选项");
    Message.error(`${name ? name + ":" : ""}请至少设置一个正确选项`);
    return false;
  }
  // 多选 不支持【自动提交】- 选中即提交
  if (correctAnswerNumber > 1 && data.extraStageData.autoSubmitMode === 1) {
    Message.error(`正确答案是一个时，才能使用自动提交-选中即提交`);
    return false;
  }
  return true;
};

export default siweiSelectFindAllValidate;
