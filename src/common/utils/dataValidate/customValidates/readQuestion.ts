import { Message } from "element-ui";

/**
 * @description 阅读题目数据校验
 */
const readQuestion = (data: any) => {
  const { components = [], extraDataMap = {} } = data;
  let haveRead = false;
  let haveCmbFinishNum = 0;
  components.forEach((cmpt: any) => {
    if (cmpt.type === "label" && cmpt.subType === "readText") {
      haveRead = true;
    }
    if (cmpt.type === "specialComponent" && cmpt.subType === "h5Label" && cmpt.tag == "readText") {
      haveRead = true;
    }

    if (cmpt["type"] === "group" && cmpt.subComponents) {
      cmpt.subComponents.forEach((subcmpt: any) => {
        if (subcmpt.tag === "finishReadBtn") {
          haveCmbFinishNum++;
        }
      });
    }
  });

  if (haveCmbFinishNum > 0) {
    Message.error("提交按钮不可组合");
    return false;
  }

  let num = 0;
  for (const key in extraDataMap) {
    const item = extraDataMap[key];
    if (item.tag === "finishReadBtn") {
      num++;
    }
  }
  if (haveRead === false) {
    Message.error("题目数据错误");
    return false;
  }
  if (num < 1) {
    Message.error("请配置提交按钮");
    return false;
  } else if (num > 1) {
    Message.error("只允许配置一个提交按钮");
    return false;
  }
  return haveRead;
};

export default readQuestion;
