import { Message } from "element-ui";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";

/**
 * @description 小英pk
 */
const enPKQuestion = (data: any) => {
  const { components = [] } = data;
  const enPKComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === SpecialComponentSubTypes.ENGROUPPK);
  if (enPKComponent) {
    const {
      properties: {
        pkData: { countDownSound },
      },
    } = enPKComponent;
    if (!countDownSound) {
      Message.error(`请先编辑题目再进行预览或创建`);
      return false;
    }
  }
  return true;
};

export default enPKQuestion;
