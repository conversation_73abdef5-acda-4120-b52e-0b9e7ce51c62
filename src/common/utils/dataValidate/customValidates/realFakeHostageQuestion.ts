import { Message } from "element-ui";

/**
 * 真假人质
 */
const realFakeHostage = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "realFakeHostage");
  let posIsRepeat = false;
  if (currComponent) {
    const {
      properties: { stuTopicList },
    } = currComponent;
    stuTopicList.forEach((item: {
      optionList: any;
    }, index: number) => {

      // 真假人质-位置选项不可重复
      const poss = item.optionList.options.map((opt: any) => opt.optionPos);
      // poss是否存在重复的数据
      if (poss.length !== Array.from(new Set(poss)).length) {
        Message.error(`第${index + 1}题位置选项存在重复， 请检查`);
        posIsRepeat = true;
      }
    })
  }
  if (posIsRepeat) return false;
  return true;
};

export default realFakeHostage;
