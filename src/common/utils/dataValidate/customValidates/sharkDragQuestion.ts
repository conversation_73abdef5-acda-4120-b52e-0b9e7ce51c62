import { AfterType } from "@/pages/index/store/modules/animations";
import { Message } from "element-ui";

/**
 * @description 拖拽题——元素分类
 */
const siweiDragClassifyValidate = (data: any) => {
  const {
    extraDataMap = {},
    extraStageData: { dragNumberAnswerJudge, dragSum, dragSumArray },
  } = data;
  console.log("..siweiDragClassifyValidate", dragNumberAnswerJudge, dragSumArray, dragSum, extraDataMap);
  // 是否有答题区在组件动画【错误后】的时机中设置为了动画效果【消失】和结束效果【隐藏】
  let dragAreaAniIsHide = false;
  let errorMessage = '';
  const exitAniTypes = [2, 4, 12];
  Object.keys(extraDataMap).forEach((key) => {
    const { animations } = extraDataMap[key];
    if (animations) {
      Object.keys(animations).forEach((aniKey) => {
        if (["afterWrong", "afterDragAreaWrong"].includes(aniKey)) {
          const { fragments } = animations[aniKey];
          Object.keys(fragments).forEach((fragmentId) => {
            // console.log("..fragments", fragments[fragmentId]);
            const fragmentList = fragments[fragmentId];
            fragmentList.forEach(({ value: { anim: { type } }, componentId, after: { type: afterType } }: any) => {
              // console.log("..componentId", componentId, type, afterType);
              if (extraDataMap[componentId] && extraDataMap[componentId].tag === "dragArea" && (exitAniTypes.includes(type) || afterType === AfterType.HIDE)) {
                errorMessage += `<div>请打开组件【${key}】的编辑动画，检查组件ID：【${componentId}】的动画配置<div>`;
                dragAreaAniIsHide = true;
              }
            })
          })
        }
      })
    }
  })
  if (dragAreaAniIsHide) {
    Message({
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      type: 'error'
    })
    return false;
  }
  // 2 是分区 0/undefined/false 是无 其他（true/1） 是总数值
  if (dragNumberAnswerJudge === 2) {
    // 分区 dragSum: "", dragSign:
    let isValid = true
    dragSumArray.forEach((element: any) => {
      if (!Number(element.dragSum) || typeof element.dragSign === 'undefined' || !element.dragAreaId.length) {
        isValid = false;
      }
    });
    if (!isValid) {
      Message.error("请检查题目内容后，再预览/插入到课件哦");
      return false;
    }
  } else if (dragNumberAnswerJudge) {
    if (!dragSum) {
      Message.error("请检查题目内容后，再预览/插入到课件哦");
      return false;
    }
  }
  let dragableObjectNumberSum = 0;
  for (const key in extraDataMap) {
    const item = extraDataMap[key];
    if (item.tag === "dragableObject" && item.dragNumber) {
      dragableObjectNumberSum += Number(item.dragNumber);
    }
  }
  if (!dragableObjectNumberSum && dragNumberAnswerJudge !== 0) {
    Message.error("请检查题目内容后，再预览/插入到课件哦");
    return false;
  }
  const hasAtLeastOneRelationshipAndEndCountMoreThanStartCount = (() => {
    let num = 0;
    const typeMap: Record<string, { endCount: number; count: number }> = {};

    for (const key in extraDataMap) {
      const item = extraDataMap[key];
      if (item.tag === "dragableObject" && item.type) {
        const filter = /[\u4E00-\u9FA5\uF900-\uFA2D]{1,}/;
        if (filter.test(item.type)) {
          Message.error("拖拽元素类型存在中文");
          return false;
        }

        if (!typeMap[item.type]) {
          typeMap[item.type] = {
            count: 0,
            endCount: 0,
          };
        }
        num += item.num;
        typeMap[item.type].count++;
      }
      if (item.tag === "dragArea" && item.type) {
        const filter = /[\u4E00-\u9FA5\uF900-\uFA2D]{1,}/;
        if (filter.test(item.type)) {
          Message.error("答题区类型存在中文");
          return false;
        }
        if (!typeMap[item.type]) {
          typeMap[item.type] = {
            count: 0,
            endCount: 0,
          };
        }
        typeMap[item.type].endCount += item.position.length;
      }
    }
    if (num > 50) {
      Message.error("总拖拽次数上限50次");
      return false;
    }
    // 相同类型的落点数量必须大于元素数量
    // for (const key in typeMap) {
    //   const type = typeMap[key];
    //   if (type.endCount < type.count) {
    //     Message.error(`类型【${key}】的落点数量必须大于拖拽元素数量`);
    //     return false;
    //   }
    // }

    // 有效的类别数量
    const usefulTypeCount = Object.keys(typeMap).filter(key => typeMap[key].count > 0 && typeMap[key].endCount > 0).length;
    if (usefulTypeCount <= 0) {
      Message.error("至少包含一组可分类的答题区和拖拽元素");
      return false;
    }
    return true;
  })();

  if (!hasAtLeastOneRelationshipAndEndCountMoreThanStartCount) {
    return false;
  }
  return true;
};

export default siweiDragClassifyValidate;
