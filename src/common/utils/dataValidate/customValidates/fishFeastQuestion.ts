import { Message } from "element-ui";

/**
 * 大鱼吃小鱼
 */
const fishFeastQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "fishFeast");
  const { properties: { stuLargeFishRatio, stuSmallFishRatio, stuLargeFishStartNum, stuLargeFishEndNum, stuLargeFishStartSpeed, stuLargeFishEndSpeed, stuSmallFishStartSpeed,
    stuSmallFishEndSpeed, stuNice, niceCombo, stuGreat, greatCombo, stuExcellent, excellentCombo, niceIntegralCoefficient, greatIntegralCoefficient, excellentIntegralCoefficient, stuCommentList } } = currComponent;
  let hasBadCombo = false;
  const counts: number[] = [];
  const integralCoefficientCounts: number[] = [];
  if (stuNice === "1") {
    counts.push(niceCombo)
    integralCoefficientCounts.push(niceIntegralCoefficient)
  }
  if (stuGreat === "1") {
    counts.push(greatCombo)
    integralCoefficientCounts.push(greatIntegralCoefficient)
  }
  if (stuExcellent === "1") {
    counts.push(excellentCombo)
    integralCoefficientCounts.push(excellentIntegralCoefficient)
  }
  // counts 是否是递增的
  const isIncrease = counts.every((count, index) => {
    if (index === 0) return true;
    return count > counts[index - 1];
  });
  console.log('integralCoefficientCounts', integralCoefficientCounts);
  const isICIncrease = integralCoefficientCounts.every((count, index) => {
    if (index === 0) return true;
    return count > integralCoefficientCounts[index - 1];
  });
  // niceIntegralCoefficient
  if (!isIncrease || !isICIncrease) {
    hasBadCombo = true;
  }
  if (hasBadCombo) {
    Message.error(`Combo机制的连击次数/积分系数必须是递增逻辑`);
    return false;
  }
  if (Number(stuLargeFishRatio) >= Number(stuSmallFishRatio)) {
    Message.error(`小型鱼出现比例必须大于大型鱼出现比例`);
    return false;
  }
  if (Number(stuLargeFishStartNum) >= Number(stuLargeFishEndNum)) {
    Message.error(`画面中大型鱼存在数量的配置必须是递增的`);
    return false;
  }
  if (Number(stuLargeFishStartSpeed) >= Number(stuLargeFishEndSpeed)) {
    Message.error(`大型鱼速度的配置必须是递增的`);
    return false;
  }
  if (Number(stuSmallFishStartSpeed) >= Number(stuSmallFishEndSpeed)) {
    Message.error(`小型鱼速度的配置必须是递增的`);
    return false;
  }
  let errorIndex = -1;
  const commentScores = stuCommentList.map((item: { stuScore: any; }) => item.stuScore)
  const isCommentIncrease = commentScores.every((item: number, index: number) => {
    if (index === 0) return true;
    if (item <= commentScores[index - 1]) {
      errorIndex = index;
    }
    return item > commentScores[index - 1];
  });
  if (!isCommentIncrease) {
    Message.error(`分段${errorIndex + 1}中学生作答分数配置有误，请检查`);
    return false;
  }
  return true;
};

export default fishFeastQuestion;
