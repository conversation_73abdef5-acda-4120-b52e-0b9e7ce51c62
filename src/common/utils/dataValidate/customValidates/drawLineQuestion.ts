import { Message } from "element-ui";

/**
 * 连点成画
 */
const drawLineQuestion = (data: any) => {
  // 1. 终点不能包含连接点重复
  // 2. 一笔画-所有连接点必须配置
  const { components = [] } = data;
  const drawLineQuestionComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "drawLineQuestion");
  if (drawLineQuestionComponent) {
    const {
      properties: { stuMode, stuConnectPointConfigs, stuConnectPointCount },
    } = drawLineQuestionComponent;
    const configedPoints: string[] = [];
    let hasEndReapeatWithStart = false;
    stuConnectPointConfigs.forEach((item: { start: string; end: string[]; }) => {
      const { start, end } = item;
      if (end.includes(start)) {
        hasEndReapeatWithStart = true;
      }
      stuMode === 0 && configedPoints.push(start, ...end);
    })
    if (hasEndReapeatWithStart) {
      Message.error(`连接点起点与终点不可相同`);
      return false;
    }
    // configedPoints去重后的长度与stuConnectPointCount是否相等
    if (stuMode === 0 && stuConnectPointCount !== Array.from(new Set(configedPoints)).length) {
      Message.error(`连接点关系配置未正确建立，请检查`);
      return false;
    }
  }
  return true;
};

export default drawLineQuestion;
