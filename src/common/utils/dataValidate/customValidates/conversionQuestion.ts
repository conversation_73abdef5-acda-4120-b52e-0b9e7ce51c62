import { Message } from "element-ui";

/**
 * 换算关系
 */
const conversionQuestion = (data: any) => {
  const { components = [] } = data;
  const conversionQuestionComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "conversion");
  if (conversionQuestionComponent) {
    const {
      properties: { stuItems, stuPreDatas },
    } = conversionQuestionComponent;
    const itemValueArray = stuItems.map((item: { itemValue: any; }) => item.itemValue);
    const itemValueSet = new Set(itemValueArray);
    // 相对目标值不能重复
    if (itemValueSet.size !== itemValueArray.length) {
      Message.error("元素的相对数值不可以重复");
      return false;
    }
    // 题面的数值不能不对
    if (!stuPreDatas || stuPreDatas.length === 0) {
      Message.error("请先生成题面");
      return false;
    }
  }
  return true;
};

export default conversionQuestion;
