/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2025-01-10 17:50:02
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-01-10 18:54:06
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/stepUpQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";

/**
 * 爬台阶
 */
const stepUpQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "stepUp");
  const { properties: { lastLifeFive,lastLifeThree,lastLifeOne} } = currComponent;
  let isIncrease = false;
  if(lastLifeFive>lastLifeThree && lastLifeThree>lastLifeOne)
    isIncrease = true;
  if (!isIncrease) {
    Message.error(`【一颗星】等级配置要小于【三颗星】小于【五颗星】`);
    return false;
  }
  return true;
};

export default stepUpQuestion;
