import { Message } from "element-ui";

/**
 * @description 火柴题——all有答案
 */
const matchPatternQuestionValidate = (data: any) => {
  const { components = [] } = data;
  let state = true;
  components.forEach((cmpt: any) => {
    if (cmpt.type === "specialComponent" && cmpt.subType === "matchboard") {
      const properties = cmpt.properties;
      const { rightAnswer, pattern, matchType, showTargetArea, selectTargetArea, targetAreaX, targetAreaY, targetAreaScale, targetAreaAngle } = properties;
      console.log(properties.pattern, "pattern", "matchType", matchType);
      if (!matchType) {
        if (
          pattern.find((item: number[]) => {
            return !Number(item.join(""));
          }) ||
          rightAnswer.find((item: number[][]) => {
            return item.find((subItem: number[]) => !Number(subItem.join("")));
          })
        ) {
          Message.error("请配置正确答案");
          state = false;
          return;
        }
      }
      if (!rightAnswer || !rightAnswer.length) {
        Message.error("请配置正确答案");
        state = false;
        return;
      }
      // 若选择目标区域为空则提示：“选择目标区”配置项不可为空
      // 位置：必填项  缩放倍数 不可为空即可
      if (showTargetArea) {
        if (selectTargetArea === undefined) {
          Message.error("选择目标区配置项不可为空");
          state = false;
        }
        if (targetAreaX === undefined) {
          Message.error("位置 X 配置项不可为空");
          state = false;
        }
        if (targetAreaY === undefined) {
          Message.error("位置 Y 配置项不可为空");
          state = false;
        }
      }
    }
  });

  return state;
};

export default matchPatternQuestionValidate;
