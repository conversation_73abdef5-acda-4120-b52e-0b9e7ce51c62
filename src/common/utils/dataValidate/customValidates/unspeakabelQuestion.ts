import { Message } from "element-ui";

/**
 * 旋转密码板
 */
const unspeakabelQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "Unspeakabel");
  if (currComponent) {
    const {
      properties: { stuMask, stuAnswer, stuQuestion },
    } = currComponent;
    // stuMask[0]至少存在一个不为0的
    if (!Object.values(stuMask[0]).some((item) => item === 0)) {
      Message.error(`右侧遮盖板镂空单元格的数量不可为0`);
      return false;
    }
    // 底板文字不可全部为空
    if (!Object.keys(stuQuestion[0]).some((item) => item !== '_id' && stuQuestion[0][item])) {
      Message.error(`左侧底板文字不可为空`);
      return false;
    }
    // 正确答案不可全部为空
    if (!Object.keys(stuAnswer[0]).some((item) => item !== '_id' && stuAnswer[0][item])) {
      Message.error(`正确答案不可为空`);
      return false;
    }
  }
  return true;
};

export default unspeakabelQuestion;
