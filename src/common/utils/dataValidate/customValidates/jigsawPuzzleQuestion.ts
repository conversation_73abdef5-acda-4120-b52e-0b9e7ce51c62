import { Message } from "element-ui";


/**
 * 拖拽拼图题
 */
const jigsawPuzzleQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "jigsawPuzzle");
  const { properties: { stuPieces } } = currComponent;
  // 初始区域是否全部设置在拼图区域
  const isAllinPuzzleArea = !stuPieces.find(({ initPosFlag }: { initPosFlag: number; }) => initPosFlag === 2);
  if (isAllinPuzzleArea) {
    Message.error(`碎片初始位置不可都在拼图区`);
    return false;
  }
  return true;
};

export default jigsawPuzzleQuestion;
