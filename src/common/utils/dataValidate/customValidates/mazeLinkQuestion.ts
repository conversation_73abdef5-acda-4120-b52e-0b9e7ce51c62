import { Message } from "element-ui";

/**
 * 迷宫游戏
 */
const mazeLinkQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "mazeLink");
  const { properties: { stuQuestionConfigs } } = currComponent;
  const startTimes = stuQuestionConfigs.outerTables.flat().filter((item: number) => item === 8).length;
  const endTimes = stuQuestionConfigs.outerTables.flat().filter((item: number) => item === 9).length;
  if (startTimes !== 1 || endTimes !== 1) {
    Message.error(`题板内容配置必须且只能包括一个起点和一个终点，请检查`);
    return false;
  }
  return true;
};

export default mazeLinkQuestion;
