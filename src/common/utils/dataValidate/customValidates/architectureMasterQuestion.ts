import { Message } from "element-ui";

/**
 * 帮帮建筑大赛呢
 */
const architectureMasterQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "architectureMaster");
  const { properties: { stuGenQuestion, stuFeedList } } = currComponent;
  if (!stuGenQuestion || !stuGenQuestion.length) {
    Message.error(`未生成题面`);
    return false;
  }
  let errorIndex = -1;
  const ratioList = stuFeedList.map((item: {
    stuScoreList
    : number
  }) => item.stuScoreList
  );
  const isIncrease = ratioList.every((item: number, index: number) => {
    if (index === 0) return true;
    if (item <= ratioList[index - 1]) {
      errorIndex = index;
    }
    return item > ratioList[index - 1];
  });
  if (!isIncrease) {
    Message.error(`分段${errorIndex + 1}中学生作答时间配置有误，请检查`);
    return false;
  }
  return true;
};

export default architectureMasterQuestion;
