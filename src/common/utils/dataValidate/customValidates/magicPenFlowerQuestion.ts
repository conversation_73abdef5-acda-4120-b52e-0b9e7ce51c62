import { Message } from "element-ui";

/**
 * 妙笔生花
 */
const magicPenFlowerQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "magicPenFlowerQuestion");
  const { properties: { stuBlankOptions, stuAltOptions, stuImageList } } = currComponent;
  // 空格的个数 至少为1
  if (!stuBlankOptions.length) {
    Message.error(`请至少设置一个空格`);
    return false;
  }
  // 合成关系全部配置了
  let configedOptionsLen = 0;
  stuImageList.forEach((item: { blankValues: string[]; }) => {
    configedOptionsLen += item.blankValues.length;
  });
  if (configedOptionsLen !== stuAltOptions.length) {
    Message.error(`请检查合成关系是否全部配置`);
    return false;
  }
  return true;
};

export default magicPenFlowerQuestion;
