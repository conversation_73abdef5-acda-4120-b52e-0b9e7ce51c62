import { Message } from "element-ui";

/**
 * 编程启蒙
 */
const programQuestion = (data: any) => {
  const { components = [] } = data;
  // special optionComponent todo
  const programQuestionComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "programQuestion");
  if (programQuestionComponent) {
    const {
      properties: { grid, cmds },
    } = programQuestionComponent;

    let hasCar = false,
      hasFish = false,
      hasBox = false,
      hasEmptyBox = false;

    grid.forEach((row: number[]) => {
      row.forEach(cell => {
        if (cell === 0) {
          hasCar = true;
        }
        if (cell === 2) {
          hasFish = true;
        }
        if (cell === 3) {
          hasEmptyBox = true;
        }
        if (cell === 4) {
          hasBox = true;
        }
      });
    });

    // 必须有车， 鱼干或者宝箱
    if (!hasCar || (!hasFish && !hasBox && !hasEmptyBox)) {
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    }

    // 指令区不能为空
    if (cmds.length === 0) {
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    }
  }
  return true;
};

export default programQuestion;
