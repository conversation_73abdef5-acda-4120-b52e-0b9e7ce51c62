import { Message } from "element-ui";

/**
 * 立体拼搭题
 */
const puzzleConstructsQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "puzzleConstructs");
  const { properties: { basementOptionCount, foundationOptionCount, houseOptionCount } } = currComponent;
  if (!(basementOptionCount + foundationOptionCount + houseOptionCount)) {
    Message.error('小房子/地下室/地基的数量不可都为0');
    return false;
  }

  return true;
};

export default puzzleConstructsQuestion;
