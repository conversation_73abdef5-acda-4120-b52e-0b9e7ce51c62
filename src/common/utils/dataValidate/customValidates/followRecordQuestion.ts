import { Message } from "element-ui";
import { checkWordIsInASR } from "../../asrRequest";

/**
 * @description 跟读题
 */
const voiceQuestionValidate = async (data: any) => {
  const { components = [] } = data;
  let state = true;
  let evaluatingText = "";
  let cmptId = ""; // 组件id
  let isUsePhonetics = false;
  components.forEach((cmpt: any) => {
    if (cmpt.type === "specialComponent" && cmpt.subType === "voice") {
      const properties = cmpt.properties;
      if (!properties.evaluatingText) {
        Message.error(`请填写组件ID【${cmpt.id}】评测内容`);
        state = false;
        return;
      } else {
        evaluatingText = properties.evaluatingText;
        cmptId = cmpt.id;
      }
      if (!properties.answerDuration) {
        Message.error(`请填写组件ID【${cmpt.id}】答题时长`);
        state = false;
        return;
      } else {
        const number = properties.answerDuration;
        if (number <= 0 || number > 100) {
          Message.error(`组件ID【${cmpt.id}】答题时长需为需为0-100的数字`);
          state = false;
          return;
        }
      }
      if (!properties.accuracy) {
        Message.error(`请填写组件ID【${cmpt.id}】及格线`);
        state = false;
        return;
      } else {
        const number = properties.accuracy;
        if (number <= 0 || number > 100) {
          Message.error(`组件ID【${cmpt.id}】及格线需为0-100的数字`);
          state = false;
          return;
        }
      }
      isUsePhonetics = properties.isUsePhonetics;
      if (properties.isUsePhonetics && !properties.evaluatePhonetics) {
        Message.error(`请选择组件ID【${cmpt.id}】评测音标`);
        state = false;
        return;
      }
      if (properties.isUseStarSpine) {
        // scoreLevels 的score 是否递增
        const scoreLevels = properties.scoreLevels;
        if (!properties.starSpine || !properties.starSpine.skeleton) {
          Message.error(`请选择组件ID【${cmpt.id}】星星反馈动画`);
          state = false;
          return;
        }
        if (scoreLevels) {
          const scoreList = scoreLevels.map((item: any) => item.score);
          const isAsc = scoreList.every((item: any, index: number) => index === 0 || item > scoreList[index - 1]);
          if (!isAsc) {
            Message.error(`组件ID【${cmpt.id}】分数等级需递增`);
            state = false;
            return;
          }
          // animation 必填
          const isAniEmpty = scoreLevels.some((item: any) => !item.animation);
          const isAudioEmpty = scoreLevels.some((item: any) => !item.audio);
          if (isAniEmpty) {
            Message.error(`请选择组件ID【${cmpt.id}】动画队列`);
            state = false;
          }
          if (isAudioEmpty) {
            Message.error(`请上传组件ID【${cmpt.id}】音效`);
            state = false;
          }
        }
      }
    }
  });
  const englishReg = /[^a-zA-Z0-9\s-\\.!@#\\$%\\\\^&\\*\\)\\(\\+=\\{\\}\\[\]\\/",'<>~\\·`\\?:;|]+/g;
  if (evaluatingText.match(englishReg)) {
    state = false;
    Message.error(`组件ID【${cmptId}】评测内容不能包含中文！`);
  }
  // 如果开启音标评测，则不进行单词校验
  if(isUsePhonetics){
    return state;
  }
  const words = evaluatingText.replace(/[^a-zA-Z0-9'\s-]+/g, " ");
  
  const res = await checkWordIsInASR(words);

  const errWordsList = [];
  const msg = res.data.data;
  const WordsList: any[] = msg.WordsList || [];
  WordsList.forEach((item: any) => {
    // in_dict 0（在词表中） 、1（不在词表中）
    if (item.in_dict !== 0) {
      errWordsList.push(item);
    }
  });
  if (errWordsList.length > 0) {
    state = false;
    Message.error(`组件ID【${cmptId}】单词校验错误，请检查！`);
  }

  return state;
};

export default voiceQuestionValidate;
