import { Message } from "element-ui";

/**
 * @description 华容道
 */
const klotskiQuestion = (data: any) => {
  const { components = [] } = data;
  // special optionComponent todo
  const klotskiQuestionComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "klotskiQuestion");
  if (klotskiQuestionComponent) {
    const {
      properties: { questionList, targetList },
    } = klotskiQuestionComponent;
    const qstNumberValuesSort = Object.values(questionList)
      .filter(item => item && !isNaN(Number(item)))
      .sort()
      .join("");
    const qstHasBlank = Object.values(questionList).find(item => !item);
    const tHasBlank = Object.values(targetList).find(item => !item);
    const tNumberValuesSort = Object.values(targetList)
      .filter(item => item && !isNaN(Number(item)))
      .sort()
      .join("");
    console.log("xu-qstNumberValuesSort", qstNumberValuesSort, tNumberValuesSort, qstHasBlank, tHasBlank);
    const qstBlanks = Object.values(questionList).filter(item => item === "");
    const tBlanks = Object.values(targetList).filter(item => item === "");
    const qstNumbers = [...new Set(Object.values(questionList).filter(item => item && !isNaN(Number(item))))];
    const qstKongNumbers = [
      ...new Set(
        Object.values(questionList)
          .filter(item => (item as string).includes("空") && (item as string).length > 1)
          .map(item => (item as string).slice(-1)[0]),
      ),
    ];
    const tNumbers = [...new Set(Object.values(targetList).filter(item => item && !isNaN(Number(item))))];
    const tKongNumbers = [
      ...new Set(
        Object.values(targetList)
          .filter(item => (item as string).includes("空") && (item as string).length > 1)
          .map(item => (item as string).slice(-1)[0]),
      ),
    ];
    console.log("qstNumbers", qstNumbers);
    console.log("qstKongNumbers", qstKongNumbers);

    if (qstBlanks.length > 1 || tBlanks.length > 1) {
      // 只能有一个空格子
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    } else if (!qstNumberValuesSort || !tNumberValuesSort) {
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    } else if (typeof qstHasBlank === "undefined" || typeof tHasBlank === "undefined") {
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    } else if (qstNumberValuesSort !== tNumberValuesSort) {
      Message.error(`请检查题目内容后，再预览或创建哦`);
      return false;
    } else if (qstKongNumbers.find(item => qstNumbers.includes(item)) || tKongNumbers.find(item => tNumbers.includes(item))) {
      // 数字和干扰格子不能重复
      Message.error(`干扰格子和数字格子的数字，不能相同哦，请重新检查`);
      return false;
    }
  } else {
    Message.error(`必须存在一个华容道元素`);
    return false;
  }
  return true;
};

export default klotskiQuestion;
