/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-09-02 16:02:37
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-09-03 17:48:21
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/pathPuzzleQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";

/**
 * 路线规律
 */
const pathPuzzleQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "pathPuzzle");
  const { properties: { stuQuestionConfigs, stuAnswerList, stuElementList } } = currComponent;
  const startIsMore = stuQuestionConfigs.outerTables.flat().filter((item: string) => item === 'start').length > 0;
  if (!startIsMore) {
    Message.error(`未配置起点`);
    return false;
  }
  const isInnerEmpty = stuQuestionConfigs.innerTables.flat().filter((item: string) => item === '').length > 0;
  if (isInnerEmpty) {
    Message.error(`宫格未填满，请检查`);
    return false;
  }
  let isRepeat = false;
  for (let i = 0; i < stuElementList.length; i++) {
    for (let j = i + 1; j < stuElementList.length; j++) {
      if (stuElementList[i].stuName === stuElementList[j].stuName && stuElementList[i].stuName !== "") {
        isRepeat = true;
      }
    }
  }
  if (isRepeat) {
    Message.error('题板元素名称重复，请重新输入');
    return false;
  }
  let isAnswerEmpty = false;
  for (let i = 0; i < stuAnswerList.length; i++) {
    isAnswerEmpty = stuAnswerList[i].answer.innerTables.flat().filter((item: string) => item === '').length > 0;
    if (isAnswerEmpty)
      break;

  }
  if (isAnswerEmpty) {
    Message.error(`答案设置不正确，请检查`);
  }
  return !isAnswerEmpty;
};

export default pathPuzzleQuestion;
