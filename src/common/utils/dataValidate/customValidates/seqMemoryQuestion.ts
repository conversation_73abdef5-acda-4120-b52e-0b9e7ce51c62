import { Message } from "element-ui";

/**
 * 有序记忆
 */
const seqMemoryQuestion = (data: any) => {
  const { components = [] } = data;
  console.log(components);
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "seqMemory");
  const { properties: { stuQuestionMax, stuRoundList, stuScoreList, stuGameType
  } } = currComponent;
  const scoreList = stuScoreList.map((item: { score: number }) => item.score);
  // 判断scoreList 是否是递增的
  const isIncrease = scoreList.every((item: number, index: number) => {
    if (index === 0) return true;
    return item > scoreList[index - 1];
  });
  if (!isIncrease) {
    Message.error(`等级分数配置不正确，请检查`);
    return false;
  }
  const imageCountIsRight: any[] = [];
  if (stuGameType === 1) {
    stuRoundList.forEach((item: {
      imageConfigs: Array<{ count: number }>;
    }, index: number) => {
      // item.imageConfigs count相加
      const totalCount = item.imageConfigs.reduce((prev: number, curr: { count: number; }) => {
        return prev + curr.count || 0;
      }, 0);
      if (totalCount > stuQuestionMax) {
        imageCountIsRight.push(index + 1);
      }
    })
  }
  if (imageCountIsRight.length) {
    Message.error(`第「${imageCountIsRight.join('、')}」小题的图片的数量设置总和不可超过${stuQuestionMax}，请检查`);
    return false;
  }
  return true;
};

export default seqMemoryQuestion;
