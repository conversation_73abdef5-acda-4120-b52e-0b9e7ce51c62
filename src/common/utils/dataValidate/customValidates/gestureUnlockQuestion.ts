/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-24 17:52:03
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-06-24 18:42:19
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/gestureUnlockQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";

/**
 * @description 连续切水果
 */
const gestureUnlockQuestion = (data: any) => {
  const { components = [] } = data;
  const gestureUnlockComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "gestureUnlock");
  if (gestureUnlockComponent) {
    const {
      properties: { stuUnlockTime, stuAnswerTime, stuAnswerList, stuAnswerMode }
    } = gestureUnlockComponent;
    let isCanAnswer = true
    if (stuAnswerMode === '1') {
      if (stuUnlockTime < stuAnswerTime)
        isCanAnswer = false;
    }
    else {
      if (stuUnlockTime < stuAnswerList.length)
        isCanAnswer = false;
    }

    if (!isCanAnswer) {
      Message.error(`解密次数需大于答案个数`);
      return false;
    }
    return true;
  }
};

export default gestureUnlockQuestion
