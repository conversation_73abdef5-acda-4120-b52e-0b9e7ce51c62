/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-06-20 11:34:47
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-06-20 11:36:55
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/followRecordManyTimesQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";
import { checkWordIsInASR } from "../../asrRequest";

/**
 * @description 跟读题
 */
const voiceQuestionValidate = async (data: any) => {
  const { components = [] } = data;
  let state = true;
  let evaluatingText = "";
  let cmptId = ""; // 组件id
  components.forEach((cmpt: any) => {
    if (cmpt.type === "specialComponent" && cmpt.subType === "voice") {
      const properties = cmpt.properties;
      if (!properties.evaluatingText) {
        Message.error(`请填写组件ID【${cmpt.id}】评测内容`);
        state = false;
        return;
      } else {
        evaluatingText = properties.evaluatingText;
        cmptId = cmpt.id;
      }
      if (!properties.answerDuration) {
        Message.error(`请填写组件ID【${cmpt.id}】答题时长`);
        state = false;
        return;
      } else {
        const number = properties.answerDuration;
        if (number <= 0 || number > 100) {
          Message.error(`组件ID【${cmpt.id}】答题时长需为需为0-100的数字`);
          state = false;
          return;
        }
      }
      if (!properties.accuracy) {
        Message.error(`请填写组件ID【${cmpt.id}】及格线`);
        state = false;
        return;
      } else {
        const number = properties.accuracy;
        if (number <= 0 || number > 100) {
          Message.error(`组件ID【${cmpt.id}】及格线需为0-100的数字`);
          state = false;
          return;
        }
      }
    }
  });
  const englishReg = /[^a-zA-Z0-9\s-\\.!@#\\$%\\\\^&\\*\\)\\(\\+=\\{\\}\\[\]\\/",'<>~\\·`\\?:;|]+/g;
  if (evaluatingText.match(englishReg)) {
    state = false;
    Message.error(`组件ID【${cmptId}】评测内容不能包含中文！`);
  }
  const words = evaluatingText.replace(/[^a-zA-Z0-9'\s-]+/g, " ");
  const res = await checkWordIsInASR(words);

  const errWordsList = [];
  const msg = res.data.data;
  const WordsList: any[] = msg.WordsList || [];
  WordsList.forEach((item: any) => {
    // in_dict 0（在词表中） 、1（不在词表中）
    if (item.in_dict !== 0) {
      errWordsList.push(item);
    }
  });
  if (errWordsList.length > 0) {
    state = false;
    Message.error(`组件ID【${cmptId}】单词校验错误，请检查！`);
  }

  return state;
};

export default voiceQuestionValidate;
