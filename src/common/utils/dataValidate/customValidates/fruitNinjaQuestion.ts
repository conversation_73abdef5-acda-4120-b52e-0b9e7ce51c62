import { Message } from "element-ui";

/**
 * @description 连续切水果
 */
const fruitNinjaQuestion = (data: any) => {
  const { components = [] } = data;
  const fruitNinjaComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "fruitNinja");
  if (fruitNinjaComponent) {
    const {
      properties: { stuQuestionList, stuInterferingSkinList },
    } = fruitNinjaComponent;
    const isHasList = [];
    for (let i = 0; i < stuQuestionList.length; i++) {
      const { stuInterference } = stuQuestionList[i];
      if (!stuInterference)
        isHasList.push(true);
      else {
        isHasList.push(false);
        if (stuInterference > 0) {
          for (let j = 0; j < stuInterferingSkinList.length; j++)
            if (stuInterferingSkinList[j].img !== "")
              isHasList[i] = true;
        }
      }
    }
    if (isHasList.includes(false)) {
      Message.error(`请至少上传1个干扰项皮肤`);
      return false;
    }
    return true;
  }
};

export default fruitNinjaQuestion
