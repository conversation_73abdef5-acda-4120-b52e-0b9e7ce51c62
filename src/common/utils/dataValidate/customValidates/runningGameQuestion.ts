/*
 * @Author: gedengyang_v <EMAIL>
 * @Date: 2024-08-14 18:43:31
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2024-08-16 11:04:45
 * @FilePath: /interactive-question-editor/src/common/utils/dataValidate/customValidates/RunningGameQuestion.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { Message } from "element-ui";

/**
 * 拖拽交换位置
 */
const swapPositions = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "runningGame");
  if (currComponent) {
    const { properties: { stuLevelsList } } = currComponent;
    const orderList: any = [];
    for (let i = 0; i < stuLevelsList.length; i++) {
      for (let j = 0; j < stuLevelsList[i].stuOrder.length; j++) {
        orderList[stuLevelsList[i].stuOrder[j] - 1] = stuLevelsList[i].stuOrder[j];
      }
    }

    for (let i = 0; i < orderList.length; i++) {
      // 检查当前元素是否等于索引+1
      if (parseInt(orderList[i]) !== i + 1) {
        // 如果不满足条件，则返回false
        Message.error(`请选择相邻的播放顺序`);
        return false;
      }
    }
    return true;
  }
};

export default swapPositions;
