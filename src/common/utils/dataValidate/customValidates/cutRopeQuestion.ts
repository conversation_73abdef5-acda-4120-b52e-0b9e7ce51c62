import { Message } from "element-ui";

/**
 * 割绳子
 */
const cutRopeQuestion = (data: any) => {
  const { components = [] } = data;
  const currComponent = components.find((comp: any) => comp.type === "optionComponent" && comp.subType === "cutRope");
  const { properties: { stuStemType, stuConnectCount, stuConnectConfigs } } = currComponent;
  // 判断是否有连接物没有配置切割物
  if (stuStemType === "2") {
    const configedIndex = [...new Set(stuConnectConfigs.map((item: { connectPos: string; }) => item.connectPos.split('-')[0]).filter((item: any) => item))]
    // 去掉数组中的空字符串
    if (configedIndex.length !== stuConnectCount) {
      Message.error(`存在没有配置连接关系的连接物`);
      return false;
    }
  }
  return true;
};

export default cutRopeQuestion;
