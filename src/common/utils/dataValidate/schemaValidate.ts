import { Message } from "element-ui";

const validateEmpty = ["", null, undefined];

const isRenderRequiredForm = (requiredForm: FormItemConfigs, props: Record<string, any>) => {
  let val = true;
  const { renderDependOn } = requiredForm as any;
  if (renderDependOn) {
    renderDependOn.forEach((item: any) => {
      const propVal = props[item.key];
      if (!item.value.includes(propVal)) {
        val = false;
      }
    });
  }
  return val;
}

const formPropertiesValidate = (formItemConfigs: FormItemConfigs[], props: Record<string, any>) => {
  const requiredForms = formItemConfigs.filter((item: any) => item?.rule?.[0].required && isRenderRequiredForm(item, props));
  for (let i = 0, len = requiredForms.length; i < len; i++) {
    const requiredForm = requiredForms[i];
    const val = props[requiredForm.key];
    if (typeof val === "object" && val) {
      if (Array.isArray(val)) {
        if (val.length === 0) {
          return requiredForm;
        }
        if (['SpecialDynamicList', 'special-dynamic-list'].includes(requiredForm.formItemType)) {
          // 答案配置
          if (requiredForm.answerConfig && requiredForm.answerConfig.show) {
            const answerRes: any = formPropertiesValidate([requiredForm.answerConfig], props);
            if (answerRes) return answerRes;
          }
          // 动态列表
          for (let j = 0; j < val.length; j++) {
            const res: any = formPropertiesValidate(requiredForm.subFormConfigs, val[j]);
            if (res) return res;
          }
        }
      } else if (['BaseSpineSelect', 'base-spine-select'].includes(requiredForm.formItemType)) {
        if (!val.atlas) {
          return requiredForm;
        }
        if (requiredForm.options?.length) {
          const options: any = requiredForm.options;
          for (let j = 0; j < options.length; j++) {
            if (typeof val[options[j].value] === 'undefined') {
              return {
                ...requiredForm,
                label: `${requiredForm.label || requiredForm.validateLabel}-${options[j].label}`
              };
            }
          }
        } else {
          if (typeof val["animation"] === 'undefined') {
            return {
              ...requiredForm,
              label: `${requiredForm.label || requiredForm.validateLabel}-播放队列`
            };
          }
        }
      } else if (['SpecialOptionConfig', 'special-option-config'].includes(requiredForm.formItemType)) {
        console.log('SpecialOptionConfig', requiredForm);
        // 选项配置组件
        if (val.options.length === 0) {
          return requiredForm;
        }
        if (requiredForm.answerConfig && requiredForm.answerConfig.show) {
          if (!requiredForm.answerConfig.multiple && typeof val.answer !== "number") {
            // 单选
            return {
              ...requiredForm,
              label: `正确答案`
            };
          }
          if (requiredForm.answerConfig.multiple && !val.answer.length) {
            // 多选
            return {
              ...requiredForm,
              label: `正确答案`
            };
          }
        }

        const options = val.options;
        // 先判断类型 再判断具体的字段 img text
        const typeMap = new Map([
          ["img", "imgUrl"],
          ["text", "text"],
        ]);
        const requiredKey = typeMap.get(val.type) || typeMap.get("text");
        for (const element of options) {
          // validateLabel 预览校验时显示的label
          if (requiredKey && !element[requiredKey]) return {
            ...requiredForm,
            label: requiredForm.validateLabel || requiredForm.label || `选项`
          };
        }
      } else if (['CustomOptionList', 'custom-option-list'].includes(requiredForm.formItemType)) {
        // 自定义选项组件
        if (val.options.length === 0) {
          return requiredForm;
        }
        const options = val.options;
        // 先判断类型 再判断具体的字段 img text
        const typeMap = new Map([
          ["img", "imgUrl"],
          ["text", "text"],
        ]);
        const requiredKey = typeMap.get(val.type) || typeMap.get("text");
        for (const item of options) {
          for (const element of item) {
            if (requiredKey && !element[requiredKey]) return {
              ...requiredForm,
              label: `选项`
            };
          }
        }
      } else if (['CustomDynamicOptionList', 'custom-dynamic-option-list'].includes(requiredForm.formItemType)) {
        // 自定义选项组件
        if (val.options.length === 0) {
          return requiredForm;
        }
        // 表单
        const normalConfigs: FormItemConfigs[] = [];
        const relativeTypeConfigs: FormItemConfigs[] = [];
        requiredForm.optionConfigs.forEach((item: FormItemConfigs) => {
          if (item.relativeTypeKey === 'type') {
            relativeTypeConfigs.push(item)
          } else {
            normalConfigs.push(item)
          }
        })
        for (const element of val.options) {
          const res: any = formPropertiesValidate(normalConfigs, element);
          for (const relativeTypeConfig of relativeTypeConfigs) {
            // 默认检测 如果rule中有了required: false 不检测
            const key = `${val.type}Key`;
            if (relativeTypeConfig.rule && relativeTypeConfig.rule.find((item: any) => item.required === false)) {
              console.log('设置required: false 绕过校验');
            } else if (relativeTypeConfig[key] && !element[relativeTypeConfig[key]]) {
              return relativeTypeConfig
            }
          }
          if (res) return res;
        }
        // 答案
        if (requiredForm.answerConfig && requiredForm.answerConfig.show) {
          // checkbox
          if (requiredForm.answerConfig.formItemType === 'checkbox') {
            const key = requiredForm.answerConfig.key;
            let hasAnswer = false;
            for (const element of val.options) {
              if (element[key]) {
                hasAnswer = true
              }
            }
            if (!hasAnswer) {
              console.log('requiredForm正确答案', requiredForm);
              return {
                ...requiredForm,
                label: "正确答案"
              }
            }
          }
        }
      }
    } else {
      if (validateEmpty.includes(val)) {
        return requiredForm;
      }
      // tabs操作
      if (requiredForm.options?.length) {
        const option = requiredForm.options.find(item => item.value === val);
        if (option?.associatedForm?.length) {
          const res: any = formPropertiesValidate(option.associatedForm, props);
          if (res) return res;
        }
      }
    }
  }
  return null;
};

export const schemaValidate = (formSchema: FormSchema, props: Record<string, any>) => {
  formSchema.filter(item => item.formItemType !== "collapse");
  let formItemConfigs: FormItemConfigs[] = [];
  for (let i = 0; i < formSchema.length; i++) {
    const item = formSchema[i];
    if (item.formItemType === "collapse") {
      let res = formPropertiesValidate(formItemConfigs, props);
      if (res) return res;
      formItemConfigs = [];
      res = formPropertiesValidate(item.formList, props);
      if (res) return res;
    } else {
      formItemConfigs.push(item as FormItemConfigs);
    }
  }
  const res = formPropertiesValidate(formItemConfigs, props);
  if (res) {
    console.log('schemaValidate', formItemConfigs, props, res);
  }

  return res ? res : null;
};

// schema生成的表单进行必填校验
export const checkSchemaFormValidate = (components: any[], extData: string) => {
  if (!extData) return true;
  const { formConfig } = JSON.parse(extData);
  if (!formConfig || Object.keys(formConfig).length === 0) return true;
  const optionComponents = components.filter(item => item.type === "optionComponent" && formConfig[item.subType]);
  if (optionComponents.length === 0) return true;
  for (let i = 0; i < optionComponents.length; i++) {
    const item = optionComponents[i];
    const res = schemaValidate(formConfig[item.subType], item.properties);
    if (res) {
      if (!res.label && !res.validateLabel) {
        console.warn('请在表单配置中添加validateLabel字段来解决验证显示存在的问题！！！');
      }
      Message({
        message: `<span style="padding-right: 30px;">"${res.label || res.validateLabel || "存在"}" 表单为必填项</span>`,
        dangerouslyUseHTMLString: true,
        type: "error",
        center: true,
      });
      return false;
    }
  }
  return true;
};
