import isTemplateEdit from '@/pages/index/common/utils/isTemplateEdit';
import { Message } from 'element-ui';
import { checkSchemaFormValidate } from './schemaValidate';
import { registerValidatorByBundleName } from './registerValidatorByBundleName';
import { Moment } from '@/pages/index/store/modules/animations';
import { AnimType } from '@/pages/index/components/AnimationEditor/AddAction/options';
import { categoryTotalSpineMax, categoryTotalAudioMax } from '../getImgsTotalPixel';

const questionNameValidate = (name: string) => {
  if (!name) {
    Message.error("请填写题目名称");
    return false;
  }
  if (name.length > 25) {
    Message.error("题目名称不能超过25个字符");
    return false;
  }
  return true;
};

const requiredAttrValidate = (data: any, name?: string) => {
  const {
    template: { extraConfig, questionType },
    extraDataMap,
    extraStageData,
    template: { tags = [] },
  } = data;

  if (!questionType) {
    // Message.error("缺少questionType属性，请联系研发！");
    Message.error(`${name ? name + ":" : ""}缺少questionType属性，请联系研发！`);
    return false;
  }
  // 校验全局业务属性必填项
  for (const key in extraConfig) {
    const config = extraConfig[key];
    if (config.required) {
      console.log("config.key", config.key);
      const value = extraStageData[config.key];
      if (value === undefined || value === "") {
        // Message.error(`请填写全局属性:${config.label}`);
        Message.error(`${name ? name + ":" : ""}请填写全局属性:${config.label}`);
        return false;
      }
    }
  }

  // 校验组件业务属性必填项
  for (const compId in extraDataMap) {
    const extraData = extraDataMap[compId];
    if (!extraData || !extraData.tag) {
      continue;
    }
    const tag = (tags as Tag[]).find(tag => tag.name === extraData.tag);
    if (!tag) {
      continue;
    }
    const requiredConfigs = tag.editorConfig ? tag.editorConfig.filter(config => config.required) : [];
    for (const i of requiredConfigs) {
      const value = extraData[i.key];

      if (i.type === "group" && value instanceof Array) {
        for (const v of value) {
          for (const config of i.params.children) {
            // undefined "" null 都是不合法的 0是合法的
            if (config.required && (v[config.key] === undefined || v[config.key] === "" || v[config.key] === null)) {
              // Message.error(`请填写组件【 ${compId} 】的业务属性【${i.label}】`);
              Message.error(`${name ? name + ":" : ""}请填写组件【 ${compId} 】的业务属性【${i.label}】`);
              return false;
            }
          }
        }
      }

      if (value === undefined || value === "" || value === null || (Array.isArray(value) && value.length === 0)) {
        // Message.error(`请填写组件【 ${compId} 】的业务属性【${i.label}】`);
        Message.error(`${name ? name + ":" : ""}请填写组件【 ${compId} 】的业务属性【${i.label}】`);
        return false;
      }
    }
  }
  return true;
};

const tagValidate = (data: any, name?: string) => {
  let haveCmbSubCompoenthNum = 0;
  const { components = [] } = data;
  components.forEach((cmpt: any) => {
    if (cmpt["type"] === "group" && cmpt.subComponents) {
      cmpt.subComponents.forEach((subcmpt: any) => {
        if (subcmpt.tag) {
          haveCmbSubCompoenthNum++;
        }
      });
    }
  });

  if (haveCmbSubCompoenthNum > 0) {
    // Message.error("子组件业务属性只能设置普通元素");
    Message.error(`${name ? name + ":" : ""}子组件业务属性只能设置普通元素`);
    return false;
  }
  return true;
};

/**
 * h5转cocos和复制h5组件时，没有过滤gif图，导致线上存在很多含有gif图的题目，故加此校验，再次编辑时给出提示，否则改题目无法创建和预览
 */
const gifBugsValidate = (data: any) => {
  const { resourceList = [], components = [] } = data;
  const gifs = resourceList.filter((item: string) => item.includes(".gif"));
  const gifCompIds: string[] = [];
  components.forEach((comp: any) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _stringifyComp = JSON.stringify(comp, function (k: any, v: any) {
      // 忽略文本组件的内容
      if (gifs.includes(v)) {
        gifCompIds.push(comp.id);
      }
      return v;
    });
  });
  if (gifCompIds.length) {
    Message.error(`组件管理：${gifCompIds.map(id => "组件" + id).join("、")}有gif图，请删除后再创建/预览～`);
    return true;
  } else if (gifs.length) {
    Message.error(`试题中存在gif图，删除后才能预览/创建哦～`);
    return true;
  }
  return false;
};

/**
 * resourcesList资源上限限制
 * 动效资源不可超过10个-动效资源不允许超过10个
 * 音频资源不可超过10个-音效资源不允许超过10个
 */
const resourcesTypeMaxValidate = (data: any) => {

  const spineMax = (window as any).pageTools.spineMax || categoryTotalSpineMax(data);
  const audioMax = (window as any).pageTools.audioMax || categoryTotalAudioMax(data);
  const { resourceList = [] } = data;
  const spineList = (Array.from(new Set(resourceList)) as string[]).filter((item: string) => item.endsWith(".atlas"));
  const audioList = (Array.from(new Set(resourceList)) as string[]).filter((item: string) => item.endsWith(".mp3"));
  let isValid = false;
  let errorMessage = '';
  if (spineList.length > spineMax) {
    errorMessage += `<div>动效资源不允许超过${spineMax}个<div>`;
    isValid = true;
  }
  if (audioList.length > audioMax) {
    errorMessage += `<div>音效资源不允许超过${audioMax}个<div>`;
    isValid = true;
  }
  if (isValid) {
    Message({
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      type: 'error'
    })
  }
  return isValid;
};

const getActionList = (fragment: any[]) => {
  let queueNum = 0;
  const actionListWithQueueData = fragment.map((action, index) => {
    const isQueueHeader = index === 0 || action.moment === Moment.AFTER;
    if (isQueueHeader) {
      queueNum++;
    }
    return { ...action, queueNum, isQueueHeader };
  });
  console.log('actionListWithQueueData', actionListWithQueueData);
  return actionListWithQueueData.filter(item => item.value.animType === 'spine');
}

const actionListHasConflict = (actionList: any[]) => {
  const animUsePropsMap: Map<
    AnimType,
    string[]
  > = (window as MyWindow).cocos.getAnimaPropMap();
  const queueAllPropsList = actionList.reduce(
    (prev: Record<string, Record<string, number>>[], curr) => {
      const { isQueueHeader, queueNum, componentId } = curr;
      const animType = curr.value.anim.type;
      const useProps = animUsePropsMap.get(animType) || [];
      if (!prev[queueNum - 1]) {
        prev[queueNum - 1] = {};
      }
      if (isQueueHeader) {
        prev[queueNum - 1] = {};
      }
      if (!prev[queueNum - 1]?.[componentId]) {
        prev[queueNum - 1][componentId] = {};
      }
      const queueAllPropsMap = prev[queueNum - 1][componentId];
      useProps.forEach(prop => {
        if (!queueAllPropsMap[prop]) {
          queueAllPropsMap[prop] = 1;
        } else {
          queueAllPropsMap[prop] += 1;
        }
      });

      return prev;
    },
    [],
  );
  const actionConflictList = actionList.map(action => {
    const { queueNum, componentId } = action;
    const queueAllProps = queueAllPropsList[queueNum - 1];
    if (!queueAllProps) return false;
    const animType = action.value.anim.type;
    const useProps = animUsePropsMap.get(animType) || [];
    const hasConflict = useProps.some(
      prop => queueAllProps[componentId] && queueAllProps[componentId][prop] > 1,
    );
    return hasConflict;
  });
  return !!actionConflictList.filter(item => item).length;
}

/**
 * 
 * @param data 
 * @param name 
 * @returns 
 */
const animationsValidate = (data: any) => {
  console.log('animationsValidate-data', data);
  let isValid = true;
  const { animations, components, template: { animationConfig, tags } } = data;
  // 全局动画
  Object.keys(animations).forEach((animationKey) => {
    const animation = animations[animationKey];
    const animationLabel = (animationConfig.find((item: { value: string; }) => item.value === animationKey) || { label: '未知' }).label;
    Object.values(animation.fragments).forEach((fragment, index) => {
      const actionList = getActionList(fragment as any[]);
      const hasConflict = actionListHasConflict(actionList);
      if (hasConflict) {
        isValid = false;
        Message.error(`动画模式-${animationLabel}-片段${index + 1}动画存在冲突，请检查后重试`);
      };
    })
  })
  // 组件动画
  components.forEach((comp: any) => {
    if (comp.extra.animations && Object.values(comp.extra.animations).length) {
      Object.keys(comp.extra.animations).forEach((animationKey) => {
        const animation = comp.extra.animations[animationKey] as any;
        let animationLabel = '';
        console.log('1024-tags', tags);
        tags.forEach((tag: { editorConfig: any[]; }) => {
          tag.editorConfig.forEach((item) => {
            if (item.key === 'animations') {
              if (item.params.options) {
                item.params.options.forEach((opt: { value: string; label: string; }) => {
                  if (opt.value === animationKey) {
                    animationLabel = opt.label;
                  }
                })
              }
            }
          })
        });
        Object.values(animation.fragments).forEach((fragment, index) => {
          const actionList = getActionList(fragment as any[]);
          const hasConflict = actionListHasConflict(actionList);
          if (hasConflict) {
            isValid = false;
            Message.error(`组件${comp.id}-${animationLabel}-片段${index + 1}动画存在冲突，请检查后重试`);
          };
        })
      })
    }
  })
  return isValid;
}

// 通用组件的教研逻辑
const componentValidate = (data: any, name?: string) => {
  const { components = [] } = data;
  let cid = -1;
  components.forEach((cmpt: any) => {
    if (cmpt["type"] === "specialComponent" && cmpt.subType === "speaker") {
      const properties = cmpt.properties;
      if (!properties.audioUrl) {
        // Message.error("音频组件" + cmpt["id"] + "未设置音频");
        cid = cmpt["id"];
        return;
      }
      if (properties.audioUrl === "") {
        // Message.error("音频组件" + cmpt["id"] + "未设置音频");
        cid = cmpt["id"];
        return;
      }
    }
  });
  if (cid !== -1) {
    // Message.error("音频组件ID【" + cid + "】未设置音频，请进行设置");
    Message.error(`${name ? name + ":" : ""}音频组件ID【${cid}】未设置音频，请进行设置`);
    return false;
  }
  // 动效/音频
  return true;
};

export const validate = async (data: any, mode: 'save' | 'setAnswer' = 'save') => {
  const { name, content, extData } = data;
  const jsonData = JSON.parse(content);
  const { bundleName, supportSetReferenceAnswer, referenceAnswer } = jsonData.template;
  console.log('validate-jsonData', jsonData);

  // 模板编辑模式
  if (isTemplateEdit) {
    return true;
  }

  // schema生成表单场景,表单必填校验
  if (!checkSchemaFormValidate(jsonData.components, extData)) {
    return false;
  }

  if (!questionNameValidate(name)) {
    return false;
  }
  // 必填项校验
  if (!requiredAttrValidate(jsonData)) {
    return false;
  }
  // 必填项校验
  if (!tagValidate(jsonData)) {
    return false;
  }

  if (!componentValidate(jsonData)) {
    return false;
  }

  if (gifBugsValidate(jsonData)) {
    return false;
  }
  if (resourcesTypeMaxValidate(jsonData)) {
    return false;
  }
  // 校验动画
  if (!animationsValidate(jsonData)) {
    return false;
  }
  // 参考答案的校验
  if (mode === 'save' && supportSetReferenceAnswer && (!referenceAnswer || !Object.keys(referenceAnswer).length)) {
    Message({
      message: `<span style="padding-right: 30px;">"答案" 为必填项</span>`,
      dangerouslyUseHTMLString: true,
      type: "error",
      center: true,
    });
    return false;
  }
  // 针对模板的自定义校验
  // 动态加载
  // const customValidate = customValidates[bundleName];
  const customValidate = registerValidatorByBundleName(bundleName);

  if (customValidate) {
    return await customValidate(jsonData);
  }
  return true;
};

export const validateTizu = async (questions: any) => {
  let valid = true;
  const spineMax = (window as any).pageTools.spineMax || 10;
  const audioMax = (window as any).pageTools.audioMax || 10;
  let spineLen = 0;
  let audioLen = 0;
  await questions.forEach(async (item: any) => {
    const jsonData = JSON.parse(item.content);
    const { bundleName } = jsonData.template;
    if (!requiredAttrValidate(jsonData, item.name)) {
      if (valid) valid = false;
      return valid;
    }
    // // 必填项校验
    if (!tagValidate(jsonData, item.name)) {
      if (valid) valid = false;
      return valid;
    }

    if (!componentValidate(jsonData, item.name)) {
      if (valid) valid = false;
      return valid;
    }
    if (gifBugsValidate(jsonData)) {
      return false;
    }
    const { resourceList = [] } = jsonData;
    const spineList = (Array.from(new Set(resourceList)) as string[]).filter((item: string) => item.endsWith(".atlas"));
    const audioList = (Array.from(new Set(resourceList)) as string[]).filter((item: string) => item.endsWith(".mp3"));
    spineLen += spineList.length;
    audioLen += audioList.length;
    // 针对模板的自定义校验
    // const customValidate = customValidates[bundleName];
    const customValidate = registerValidatorByBundleName(bundleName);
    if (customValidate) {
      const customValid = await customValidate(jsonData, item.name);
      if (valid && !customValid) valid = customValid;
      return valid;
    }
  });

  let errorMessage = '';

  if (spineLen > spineMax) {
    errorMessage += `<div>动效资源不允许超过${spineMax}个<div>`;
    valid = false;
  }
  if (audioLen > audioMax) {
    errorMessage += `<div>音效资源不允许超过${audioMax}个<div>`;
    valid = false;
  }
  if (!valid) {
    Message({
      dangerouslyUseHTMLString: true,
      message: errorMessage,
      type: 'error'
    })
  }
  return valid;
};

export default validate;
