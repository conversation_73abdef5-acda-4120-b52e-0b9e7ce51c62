import { Message } from "element-ui";
import { SpecialComponentSubTypes } from "@/common/constants/specialComponetSubTypes";

/**
 * @description 听后转述校验
 */
export const validateCustomH5 = (subType: SpecialComponentSubTypes, data: any) => {
  const { components = [] } = data;
  const listenRetellComponent = components.find((comp: any) => comp.type === "specialComponent" && comp.subType === subType);
  if (listenRetellComponent) {
    const {
      properties: {
        question: { customH5 },
      },
    } = listenRetellComponent;
    if (!customH5) {
      Message.error(`请先编辑题目再进行预览或创建`);
      return false;
    }
  }
  return true;
};
