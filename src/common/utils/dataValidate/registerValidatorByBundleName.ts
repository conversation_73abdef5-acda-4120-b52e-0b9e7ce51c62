/**
 * 
 * @param filePath 文件路径  './xxx.ts'
 * @returns fileName
 */
const getFileName = (filePath: string) => {
  // eslint-disable-next-line no-useless-escape
  const regex = /\/([^.\/]+)\.ts$/;
  const match = filePath.match(regex);

  if (match) {
    // console.log('匹配成功:', match[1]);
    return match[1];
  } else {
    console.error('匹配失败');
    return '';
  }
}

/**
 * 
 * @param bundleName  
 * @description 根据bundleName注册自定义验证函数;customValidates中的文件夹必须使用bundleName命名
 * @returns validator Function
 */
// 
export const registerValidatorByBundleName = (bundleName: string) => {
  // step1. 获取文件
  const files = require.context("./customValidates", true, /^(?!.*index).*\.ts$/);
  // step2. 读取modules并根据category进行注册
  const modules: any = {};
  files.keys().forEach((key: any) => {
    // console.log('files item', key,);
    const fileName = getFileName(key);
    if (fileName && fileName === bundleName) {
      const m = files(key).default;
      if (!m) {
        console.error('没有导出默认模块，请导出默认模块后重试');
      } else {
        modules[bundleName] = m;
      }
    }
  })
  return modules[bundleName];
}