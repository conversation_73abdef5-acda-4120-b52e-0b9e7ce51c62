import { CATEGORY } from "@/common/constants";

/**
 * @param {CATEGORY} category
 * @param {Component[]} components
 * @description signalId是因为【数据结构统一需求】中需要一个有序的组件(例如选项/填空)标识而新增的，之前的题目数据没有这个字段，所以新增该方法用于在老数据中插入该字段。本次只对选择题和填空题做处理，因为本次的需求中只有选择题和填空题的场景有这个字段。
 */
export const insertSignalId = (category: CATEGORY, components: Component[]) => {
  if(![CATEGORY.CHOICE, CATEGORY.BLANK].includes(category)) return;
  let tempSignalId = 1;
  components.forEach((comp: Component) => {
    // 选项
    if(CATEGORY.CHOICE && comp.tag === 'answer') {
      comp.extra.signalId = tempSignalId;
      tempSignalId++
    }
    // 填空
    if(CATEGORY.BLANK && comp.tag === 'blankModule') {
      comp.extra.signalId = tempSignalId;
      tempSignalId++
    }
  })
};
