import Vue from "vue";
import store from '@/pages/index/store';
import { initMonitor, reportApmEvent, reportApmLog, reportZs, setUserInfo, startApm } from 'common-monitor';
import { getUserInfo } from '../api/auth';
import { getResourcesListMemoryMbSize } from './getImgsTotalPixel';
import { getCategoryName } from "./standardDataManager/getStandardData";

export enum TimeMonitorType {
  FP = 'FP', // 首次绘制 白屏时间 appdom 渲染了子节点
  TTI = 'TTI', // 可交互时间 cocosInitFinished - 页面打开
  PureTTI = 'pure-TTI', // 可交互时间 cocosInitFinished - 页面打开 - 恢复题目弹窗显示时长
  DataInit = 'dataInit', // 成功获取数据 - 开始获取数据
  CocosInit = 'cocosInit', // cocos渲染完成 - 成功获取数据
  CocosScriptInit = 'cocosScriptInit', // cocos静态资源加载完毕 - 开始加载cocos静态资源 废弃 加载资源的方式改为插件注入的方式了，一打开页面就夹在，可能监控不到
  GetBundleUrl = 'getBundleUrl', // 成功获取bundleUrl -   开始获取bundle包 - 
  CocosBoot = 'cocosBoot', // cocos渲染完成 - 开始boot
  PostFile = 'postFile', // 开始调用PostFile接口 - 开始调用PostFile接口
  SetTag = 'setTag', // 点击tag弹窗确定的时间 - 打开tag的时间（值有变化） 
  CreateQuestion = 'createQuestion', // 完成创建题目的时间 - 页面加载时间
  CreateAction = 'createAction', // 完成创建题目的时间 - 点击创建按钮的时间
  SetAnswer = 'setAnswer', // 完成答案设置的时间 - 点击设置答案按钮的时间
  RecoverPreQuestion = 'reoverPreviousQuestion', // 恢复上次保存的题目缓存的弹窗停留时间
}
// 卡顿上报 todo
// 添加进入页面 离开页面的日志上报
// 每5s上报一次页面的内存占用
// 每5s上报一次页面的内存占用峰值
// 每5s上报一次页面的FPS
// 每5s上报一次页面的CPU
// 获取页面的内存占用

interface TimeManager {
  [key: string]: {
    start: number,
    end: number,
    duration?: number,
    tabIsHidden?: boolean, // tabs是否隐藏
  }
}
/**
 * @description 获取页面开始加载的时间戳
 * @returns 时间戳
 */
export const getNavigationStartTime = () => {
  // 如果是题组 需要修改时间的获取方式
  const performance = window.performance;
  if (!performance) {
    // 当前浏览器不支持
    return 0;
  }
  // const t = performance.getEntriesByType("navigation");
  return performance.timing.navigationStart;
}


export class Monitor {
  // private appId = 119522954;
  private appId = '75aZeiQnEjLwab3gZz';
  private appName = 'interactive-question-editor';
  // private appToken = '475a0741f04a4a4196411ad672761644';
  private env = process.env.VUE_APP_APM;
  private uid = 0;
  private uname = '';
  private isStopLog = (['production', 'test'].includes(process.env.VUE_APP_APM || '') || localStorage.getItem('startApmLog')) ? false : true;
  private timeManager: TimeManager = {
    [TimeMonitorType.DataInit]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.CocosScriptInit]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.GetBundleUrl]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.CocosBoot]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.SetTag]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.CreateQuestion]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.CreateAction]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.SetAnswer]: {
      start: 0,
      end: 0
    },
    [TimeMonitorType.RecoverPreQuestion]: {
      start: 0,
      end: 0
    },
  }
  /**
   * 浏览器tab显示和隐藏的数据
   * start: 触发tab显示/隐藏事件距离页面加载的时间
   * timestamp: 触发tab显示/隐藏事件的时间戳
   * state: visible | hidden tab状态
   * @description 如果第一个数据是visible 则表示页面正在加载时隐藏了tab 但是页面还没有执行此处的代码 没有监听到；那么如果FP事件很长很有可能就是tab隐藏的原因导致的；如果某一个日志的耗时长可以分析start的时间用来判断是否与tab隐藏有关
   */

  public tabsHiddenDurations: Array<{ start: number, timestamp: number, state: string }> = [];
  public tabIsHidden = false;
  constructor() {
    console.log('Monitor constructor');
    this.init();
  }

  public init() {
    try {
      if (!this.isStopLog) {
        console.log('initMonitor..env', this.env || 'development');
        initMonitor({
          // apm: {
          //   env: this.env,
          //   aid: this.appId,
          //   token: this.appToken, // AppToken
          //   appName: this.appName, // 站点标识
          //   routeMode: 'hash', // history | hash；该字段影响pv数据是否准确，确保正确性
          // },
          rum: {
            id: this.appId, // 上报 id
            reportApiSpeed: true, // 接口测速
            reportAssetSpeed: true, // 静态资源测速
            spa: true, // spa 应用页面跳转的时候开启 pv 计算
            hostUrl: 'https://rumt-zh.com',
            env: this.env,
            vue: Vue,
            appName: this.appName,
            reportToZs: false // rum性能相关数据是否同步到zs
          },
          zs: {   // 该字段是可选项，需要上报全埋点时需要配置该字段
            businessLine: this.appName // 站点标识
          },
          rlog: { // 该字段是可选项，需要使用rlog全链路排查问题时需要配置该字段
            authKey: this.appName // 站点标识
          }
        });
      };
      if (!this.isStopLog) {
        // 获取用户信息
        getUserInfo().then((res) => {
          // console.log('getUserInfo...res', res);
          store.commit("setUserInfo", res);
          this.setUserInfo(res.userId, res.userAccount);
        });
        // 白屏时间
        this.trackFPTime();
        this.logTabsHidden();
        // TTI
        this.setStartByType(TimeMonitorType.TTI, getNavigationStartTime());
        this.setStartByType(TimeMonitorType.PureTTI, getNavigationStartTime());
        // createQuestion时间
        this.setStartByType(TimeMonitorType.CreateQuestion, getNavigationStartTime());
      }
    }
    catch (error) {
      console.error(error);
    }
  }

  public logTabsHidden() {
    if (document.visibilityState !== 'visible') {
      this.tabsHiddenDurations.push({
        start: Date.now() - getNavigationStartTime(),
        timestamp: Date.now(),
        state: document.visibilityState,
      });
      this.tabIsHidden = true;
      // 上报tabs的状态
      this.reportLog('tabsIsHidden', {
        state: document.visibilityState
      });
    }
    const fn = () => {
      this.tabsHiddenDurations.push({
        start: Date.now() - getNavigationStartTime(),
        timestamp: Date.now(),
        state: document.visibilityState,
      });
      this.tabIsHidden = document.visibilityState !== 'visible';
      // 上报tabs的状态
      this.reportLog('tabsIsHidden', {
        state: document.visibilityState
      });
    };
    document.addEventListener('visibilitychange', fn);
    // 页面卸载后取消监听
    window.addEventListener('beforeunload', () => {
      console.log('beforeunload. removeEventListener');
      document.removeEventListener('visibilitychange', fn);
    });
  }

  public setUserInfo(uid: number, uname: string) {
    try {
      setUserInfo({
        uid,
        uname
      });
      this.uid = uid;
      this.uname = uname;
      startApm();
    }
    catch (error) {
      console.error(error);
    }
  }

  public getLiveLogCommonParams() {
    return {
      _category: store.state.template.category, // 题型类型
      _categoryName: getCategoryName(store.state.template.category), // 题型类型
      _questionName: store.state.name, // 题目名称
      _templateName: (store.state.template as any).name // 模板名称
    }
  }

  public setStartByType(type: TimeMonitorType, time?: number) {
    if (!this.timeManager[type]) {
      this.timeManager[type] = {
        start: 0,
        end: 0,
        tabIsHidden: this.tabIsHidden,
      }
    }
    this.timeManager[type].start = time || Date.now();
    this.timeManager[type].end = 0;
  }

  public setEndByType(type: TimeMonitorType, time?: number) {
    if (!this.timeManager[type]) return;
    this.timeManager[type].end = time || Date.now();
    this.timeManager[type].tabIsHidden = this.tabIsHidden;
    this.timeManager[type].duration = this.timeManager[type].end - this.timeManager[type].start - ((window as any).subQuestionChangeStart || 0);
    this.setTimeMonitor(type);
  }

  public async setTimeMonitor(type: TimeMonitorType) {
    const params = {
      name: type, // 事件名
      metrics: {  // 指标相关
        duration: this.timeManager[type].end - this.timeManager[type].start, // 毫秒
      }
    }
    // RecoverPreQuestion
    if (type === TimeMonitorType.TTI) {
      const recoverModalStayDuration = this.timeManager[TimeMonitorType.RecoverPreQuestion].duration || 0;
      (params.metrics as any).pureDuration = params.metrics.duration - recoverModalStayDuration;
    }
    console.log('setTimeMonitor', params);

    if (this.isStopLog) return;
    reportApmEvent(params);
  }

  public reportLongTTILog = async (logData: any = {}) => {
    // 将this.timeManager拍平成
    const data: any = {};
    Object.keys(this.timeManager).forEach((key) => {
      // data[`${key}_start`] = this.timeManager[key].start;
      // data[`${key}_end`] = this.timeManager[key].end;
      data[`${key}_duration`] = this.timeManager[key].duration;
    });
    data['pure-TTI_duration'] = data['TTI_duration'] - (data['reoverPreviousQuestion_duration'] || 0);
    const params = {
      content: 'longTTIDetail',
      extra: {
        ...logData,
        ...data,
        tabIsHidden: this.tabIsHidden,
        tabHiddenDurations: this.tabsHiddenDurations, // tab隐藏时间
        timeManager: this.timeManager,
        category: store.state.template.category, // 题型
        compLen: Object.keys(store.state.componentMap).length, // 组件数量
        resourceMemoryMbSize: await getResourcesListMemoryMbSize(store.state), // 题目资源大小
      }
    }
    console.log('reportLongTTILog', params);
    if (this.isStopLog) return;
    console.log('reportLongTTILog-isStopLog', this.isStopLog);
    reportApmLog(params);
  }

  public liveLog(eventName: string, data: any) {
    try {
      const mergeData = {
        ...data,
        ...this.getLiveLogCommonParams(),
        __uid: this.uid,
        __uname: this.uname,
        __timeStamp: Date.now(),
      }
      console.log('liveLog', eventName, mergeData);
      if (this.isStopLog) return;
      reportZs(eventName, mergeData);
    } catch (error) {
      console.error(error);
    }
  }

  public reportLog(logContent: string, data: any) {
    // console.log('reportLog', logContent, data);
    if (this.isStopLog) return;
    const params = {
      content: logContent,
      extra: data
    };
    reportApmLog(params);
  }

  // 获取页面白屏时间
  public trackFPTime = () => {
    // 页面白屏时间 直接打点
    let FPdestructor = -1;
    const loop = () => {
      console.log('has children loop');
      FPdestructor = requestAnimationFrame(loop);
      const app = document.getElementById('app');
      // 判断app是否有子元素
      if (app && app.children.length > 0) {
        console.log('has children');
        this.setStartByType(TimeMonitorType.FP, getNavigationStartTime());
        this.setEndByType(TimeMonitorType.FP, Date.now());
        console.log('FP', this.timeManager[TimeMonitorType.FP].duration);
        // 根据app里是否有内容来定义白屏时间
        cancelAnimationFrame(FPdestructor);
      } else {
        console.log('no children');
      }
    };
    loop();
  }
}
