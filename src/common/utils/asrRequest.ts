/*
 * @Date: 2021-09-03 16:16:29
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-07 17:36:54
 * @FilePath: /interactive-question-editor/src/common/utils/request.ts
 * @Author: chxu
 */
import axios from "axios";
import { isDevelopmentEnv } from "./env";
interface WordErrorItem {
  in_dict: 0 | 1, // in_dict 0（在词表中） 、1（不在词表中）
  word: string
}

export const requestAsrServer = axios.create({
  withCredentials: true,
  // baseURL: '//asr.zuoyebang.cc/',
  baseURL: '',
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

requestAsrServer.interceptors.response.use(
  res => {
    if (res.data.errNo) {
      throw res;
    }
    return res;
  },
  (error: Error) => {
    console.error(error);
    throw error;
  },
);

export const checkWordIsInASR = async (text: string) => {
  const url = isDevelopmentEnv ? "/asr/check_word" : window.location.protocol + "//asr.zuoyebang.cc/check_word";
  const checkReg = /[^a-zA-Z0-9'\s-]+/g;
  // console.log('checkWordIsInASR', text, text.replace(checkReg, " "));
  const words = text.replace(checkReg, " ").split(/\s+/);
  // console.log('words...', words);
  // words 去重
  const uniqueWords = [...new Set(words)];
  return requestAsrServer
    .get<Response<{ WordsList: WordErrorItem[] }>>(url, {
      params: {
        rt: uniqueWords.join(" "),
      },
    })
}

