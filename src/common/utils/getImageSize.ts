interface Size {
  width: number;
  height: number;
}

/**
 * @description 获取远程图片尺寸信息
 * @param {string} src 图片 src
 * @returns {Size} 图片尺寸信息
 */
export default (src: string): Promise<Size> => {
  return new Promise(resolve => {
    const img = new Image();
    img.onload = async function() {
      resolve({
        width: img.width,
        height: img.height,
      });
    };
    img.src = src;
  });
};
