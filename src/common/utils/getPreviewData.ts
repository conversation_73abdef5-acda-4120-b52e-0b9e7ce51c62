import { parse } from "query-string";
import { requestPhpServer } from "./request";
import APIS from "../api/constants";

export const getPreviewData = () => {
  return new Promise((resolve, reject) => {
    try {
      const { previewId, snapId, qid, tId } = parse(location.search);
      if (!previewId && !snapId && !qid && !tId) return;
      const requestInstance = (() => {
        if (tId) {
          return requestPhpServer.post(APIS.PHP_GET_TID_INFO, {
            tid: tId,
          });
        }
        if (qid) {
          return requestPhpServer.post(APIS.PHP_GET_QUESTION_INFO, {
            qid,
          });
        }
        if (snapId) {
          return requestPhpServer.post(APIS.PHP_GET_VERSION_INFO, {
            snapId,
          });
        }
        return requestPhpServer.get(APIS.PHP_GET_PREVIEW_DATA, {
          params: { vid: previewId },
        });
      })();
      requestInstance
        .then(({ data: { data } }) => {
          resolve(data);
        })
    } catch (error) {
      reject(error);
    }
  });
}