import { widgetToComponent } from "./widgetToComponent";
import store from "@/pages/index/store";
const chooseTemplateData = {
  animationsForClient: {},
  components: [],
  extraDataMap: {},
  extraStageData: {
    isAutoSubmit: false,
    hasRecover: true,
    answerWrongCount: 0,
    errorType: 1,
  },
  resourceList: [],
  stageData: {
    width: 1280,
    height: 720,
    safeWidth: 1280,
    safeHeight: 960,
    backgroundColor: "#ffffff",
    texture: "",
    textureType: 0,
  },
  template: {
    questionType: 4,
    bundleUrl: "https://yaya.cdnjtzy.com/cchd_bundle/selectQuestion.zip",
    bundleName: "sharkSelectQuestion",
    tags: [
      {
        name: "answer",
        label: "选项",
        editorConfig: [
          {
            key: "isCorrect",
            label: "正确答案",
            type: "switch",
            params: {},
          },
          {
            key: "selectEffect",
            label: "选中效果",
            type: "effect",
            params: {},
          },
          {
            key: "animations",
            label: "组件动画",
            type: "animations",
            params: {
              options: [
                {
                  value: "afterClick",
                  label: "点击后",
                },
              ],
            },
          },
        ],
      },
    ],
    stage: {
      width: 1280,
      height: 720,
      safeWidth: 1280,
      safeHeight: 960,
      backgroundColor: "#ffffff",
      texture: "",
      textureType: 0,
    },
    extraConfig: [
      {
        key: "hasRecover",
        label: "学生重置",
        required: true,
        type: "radio",
        params: {
          options: [
            {
              label: "有重置按钮",
              value: true,
            },
            {
              label: "无重置按钮",
              value: false,
            },
          ],
        },
      },
      {
        key: "isAutoSubmit",
        label: "校验方式",
        required: true,
        type: "radio",
        params: {
          options: [
            {
              label: "提交判定",
              value: false,
            },
            {
              label: "自动判定",
              value: true,
            },
          ],
        },
      },
      {
        key: "guide",
        label: "题干音频",
        type: "audioSelect",
        params: {},
      },
    ],
    animationConfig: [
      {
        label: "读题后",
        value: "afterReadingQuestion",
      },
      {
        label: "正确后",
        value: "afterSubmitCorrect",
      },
      {
        label: "答错后",
        value: "afterSubmitWrong",
      },
    ],
    features: {
      newCocos: 1,
      isQuestion: 1,
      hasMys: 0,
      canInteract: 1,
      isOral: 0,
      isGroup: 0,
      demoPage: 0,
      hasVideo: 0,
      liveResources: 1,
      groupData: {
        questionLength: 0,
      },
    },
    name: "选择题",
    type: 1000,
    category: 1010,
    tempType: 1,
  },
  thumbnail: "https://img.zuoyebang.cc/cw_7d81fa12075e95fd6b1b6c016e145131.png",
};

export const h5ChooseQuestionT = (data: any): any => {
  if (data.artTempId && (data.artTempId == 4 || data.artTempId == 9)) {
    const chooseData = JSON.parse(JSON.stringify(chooseTemplateData));
    chooseData.stageData.texture = chooseData.background;
    return chooseData;
  }
  return null;
};
export const h5WidgetT = async (data: any) => {
  console.error("h5WidgetT", data);
  for (const widget of data) {
    if (widget.type === "group") {
      console.error(" widget.widgets:", widget.widgets);
      // const groupWidgets = widget.widgets;
      // const subComponents: (
      //   | Omit<LabelComponent, "id">
      //   | Omit<SpriteComponent, "id">
      //   | null
      //   | undefined
      // )[] = [];
      // for (const groupWidget of groupWidgets) {
      //   const subComponent =  await widgetToComponent(groupWidget);
      //   subComponent && subComponents.push( subComponent);
      // }
      // // store.dispatch("addGroupComponent", subComponents);
      // console.error("subComponents",subComponents)
    } else {
      console.error("widget:", widget);
      const component = await widgetToComponent(widget);
      console.error("component", component);
      component && store.dispatch("addComponentNoFocus", component);
      //category:0  干扰项， 1 正确答案
    }
  }
};
