/* eslint-disable @typescript-eslint/no-explicit-any */
import Vue from "vue";
const nodeList = [] as any;
const ctx = "@@clickoutsideContext";
const isServer = Vue.prototype.$isServer;
const UserAgent = navigator.userAgent.toLowerCase();

let startClick: any;
let seed = 0;

const on = (() => {
  if (!isServer && document.addEventListener) {
    return (element: any, event: string, handler: any) => {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return (element: any, event: string, handler: any) => {
      if (element && event && handler) {
        element.attachEvent("on" + event, handler);
      }
    };
  }
})();

!Vue.prototype.$isServer && on(document, "mousedown", (e: any) => (startClick = e));

!Vue.prototype.$isServer && on(document, "mouseup", (e: any) => {
  if (e.button === 0 || /windows/.test(UserAgent)) {
    nodeList.forEach((node: any) => node[ctx].documentHandler(e, startClick));
  }
});

if (/mac os/.test(UserAgent)) {
  !Vue.prototype.$isServer && on(document, "contextmenu", (e: any) => {
    nodeList.forEach((node: any) => node[ctx].documentHandler(e, startClick));
  });
}

function createDocumentHandler(el: Element, binding: any, vnode: any) {
  return (mouseup = {} as any, mousedown = {} as any) => {
    if (!vnode ||
      !vnode.context ||
      !mouseup.target ||
      !mousedown.target ||
      el.contains(mouseup.target) ||
      el.contains(mousedown.target) ||
      el === mouseup.target ||
      (vnode.context.popperElm &&
      (vnode.context.popperElm.contains(mouseup.target) ||
      vnode.context.popperElm.contains(mousedown.target)))) { return; }

    if (binding.expression &&
      el[ctx].methodName &&
      vnode.context[el[ctx].methodName]) {
      vnode.context[el[ctx].methodName]();
    } else {
      el[ctx].bindingFn && el[ctx].bindingFn();
    }
  };
}

/**
 * v-clickoutside
 * @desc 点击元素外面才会触发的事件
 * @example
 * ```vue
 * <div v-element-clickoutside="handleClose">
 * ```
 */
export default {
  bind(el: Element, binding: any, vnode: any) {
    nodeList.push(el);
    const id = seed++;
    el[ctx] = {
      id,
      documentHandler: createDocumentHandler(el, binding, vnode),
      methodName: binding.expression,
      bindingFn: binding.value
    };
  },

  update(el: Element, binding: any, vnode: any) {
    el[ctx].documentHandler = createDocumentHandler(el, binding, vnode);
    el[ctx].methodName = binding.expression;
    el[ctx].bindingFn = binding.value;
  },

  unbind(el: any) {
    const len = nodeList.length;

    for (let i = 0; i < len; i++) {
      if (nodeList[i][ctx].id === el[ctx].id) {
        nodeList.splice(i, 1);
        break;
      }
    }
    delete el[ctx];
  }
};
