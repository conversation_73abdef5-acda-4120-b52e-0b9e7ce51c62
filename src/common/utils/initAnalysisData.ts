import { CATEGORY } from "@/common/constants";
import { cloneDeep } from "lodash-es";
import getCombineQueryParams from "./getCombineQueryParams";
import store from "@/pages/index/store";

export const isSupportAnalysis = () => {
  const { fromGroup } = getCombineQueryParams();
  // 是否是题组中的小题
  if (fromGroup) return false;
  // 12/13题型不支持解析
  if([12, 13].includes(store.state.template.questionType as number)) return false;
  // 1147(妙笔生花) 1109（小英上台）1192（小英全员pk）1196(轮流发言)
  if ([1109, 1147, 1192, 1196].includes(store.state.template.category as number)) return false;
  return true;
}

/**
 * @param {CATEGORY} category
 * @param {Object} extraStageData
 * @description extraStageData没有解析字段，通过该函数实现数据初始化
 */
export const initAnalysisData = (category: CATEGORY, extraStageData: any) => {
  if(!isSupportAnalysis()) return;
  extraStageData.analysis = {
    text: '',
    audio: {
      url: '',
      fileName: '',
      duration: 0
    },
    imageUrl: '',
    ...extraStageData.analysis
  }
};

export const getSaveAnalysisData = (extraStageData: any) => {
  const temp = cloneDeep(extraStageData);
  console.log('getSaveAnalysisData', temp);
  if(!temp.analysis) return temp;
  if (!temp.analysis.audio.url) temp.analysis.audio = undefined;
  if (!temp.analysis.text) temp.analysis.text = undefined;
  if (!temp.analysis.imageUrl) temp.analysis.imageUrl = undefined;
  if (!temp.analysis.text && !temp.analysis.audio && !temp.analysis.imageUrl) temp.analysis = undefined;
  return temp;
};


export const initIntroductionData = (extraStageData: any) => {
  if(extraStageData.introduction) return;
  extraStageData.introduction = {
    isSet: false,
    textUrls: [],
  };
};
