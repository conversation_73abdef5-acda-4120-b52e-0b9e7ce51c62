import { stringify } from "query-string";
import { requestPhpServer } from "./request";
import APIS from "../api/constants";

export const goPreview = (questionData: any) => {
  return new Promise((resolve, reject) => {
    try {
      requestPhpServer
      .post(APIS.PHP_CREATE_PREVIEW, {
            content: questionData.content,
            features: JSON.stringify(JSON.parse(questionData.content).template?.features),
            parentVersion: questionData.parentVersion,
            extData: questionData.extData,
          })
      .then(({ data }) => {
        if (data.errNo === 0) {
          const {
            data: { vid },
          } = data;
          const previewUrl = `./preview.html?${stringify({
            previewId: vid,
          })}`;
          window.open(previewUrl);
          resolve(data);
        } else {
          reject(data);
        }
      })
      .catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}