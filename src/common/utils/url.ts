/*
 * @Author: your name
 * @Date: 2021-06-15 11:35:07
 * @LastEditTime: 2022-01-06 19:23:28
 * @LastEditors: chxu
 * @Description: In User Settings Edit
 * @FilePath: /interactive-question-editor/src/common/utils/url.ts
 */
import { MessageBox, Message } from "element-ui";
import { getShipName, isOnline } from "./devHelper";

// eslint-disable-next-line @typescript-eslint/no-use-before-define

export const getPhpHost = async () => {
  if (isOnline) return;
  // 从host中获取 并种入myHost
  const shipName = getShipName();
  // 默认从host取
  // 如果想改 setName
  console.log("shipName", shipName);
  shipName && localStorage.setItem("myHost", `https://jiaoxue-tihu-${shipName}-cc.suanshubang.cc/`);
  if (!localStorage.getItem("myHost")) {
    MessageBox.prompt("", "请输入ship环境", {
      confirmButtonText: "确定",
      showCancelButton: false,
      inputValue: shipName || "",
      closeOnClickModal: false,
    }).then(res => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const value = (res as any).value;
      localStorage.setItem("myHost", `https://jiaoxue-tihu-${value}-cc.suanshubang.cc/`);
      Message({
        type: "success",
        message: "你的ship环境是: " + value,
      });
      window.location.reload();
    });
  }
};

/**
 * @description 获取cocos管理后台列表页面
 * @returns url
 */
export const getQuestionListUrl = (): string => {
  const shipName = getShipName();
  return `https://qu-${shipName}-cc.suanshubang.cc/static/cocos-tiku-manager/#/questions`;
  // 之前的管理后台题目列表页
  // return "/interactive-question-platform/index.html";
};
