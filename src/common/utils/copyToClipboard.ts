export default function copyToClipboard(text: string): boolean {
  const textArea = document.createElement("textarea");
  textArea.style.position = "fixed";
  textArea.style.top = "-100px";
  textArea.style.right = "-100px";
  textArea.style.zIndex = "99999";
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  try {
    const successCopy = document.execCommand("copy");
    return successCopy;
  } catch (err) {
    return false;
  } finally {
    document.body.removeChild(textArea);
  }
}
