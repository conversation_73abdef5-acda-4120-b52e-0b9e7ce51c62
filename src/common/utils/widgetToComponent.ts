import { checkPicturesSize } from "./resizePic";
import { showErrorMessage } from "@/common/utils/showErrorMessage";

export interface WidgetProps {
  top: number;
  left: number;
  width: number | string;
  height: number | string;
  opacity: number;
  rotate: string;
  ratio: number;
  lockStatus: boolean;
  autoWidth?: boolean;
  autoHeight?: boolean;
  clientWidth?: number;
  clientHeight?: number;
  operateLimit: string;
  lineHeight?: number;
  fontSize?: number;
}
export interface Widget {
  id?: string;
  type: string;
  name?: string;
  props: WidgetProps;
  content: string;
  masterWidget?: boolean;
  contentType?: string;
  masterType?: string;
  role?: number;
}


const hexToRgba = (t: string) => {
  if (/^#[0-9A-F]{3}$/.test(t)) {
    const e = t.match(/[0-9A-F]/g);
    if (e) {
      const r = e.slice(0, 3),
        n = r[0],
        g = r[1],
        b = r[2];
      return ""
        .concat(parseInt(n + n, 16).toString(), ",")
        .concat(parseInt(g + g, 16).toString(), ",")
        .concat(parseInt(b + b, 16).toString(), ",1");
    }

  }
  if (/^#[0-9A-F]{6}$/.test(t)) {
    const c = t.match(/[0-9A-F]{2}/g);
    if (c) {
      const l = c.slice(0, 3),
        f = l[0],
        v = l[1],
        d = l[2];
      return ""
        .concat(parseInt(f, 16).toString(), ",")
        .concat(parseInt(v, 16).toString(), ",")
        .concat(parseInt(d, 16).toString(), ",1");
    }

  }
  return "";
}

const rgbToRgba = (t: string) => {
  const data = t.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])/)
  if (data) {
    return data[0] + ",1";
  }

  return "";

}
const argbToRgba = (t: string) => {
  const e = t.match(/[0-9A-F]{2}/g) || "";
  const r = e.slice(0, 4);
  let a = r[0];
  const n = r[1];
  const g = r[2];
  const b = r[3];
  return (
    (a = (parseInt(a, 16) / 255).toFixed(3)),
    ""
      .concat(parseInt(n, 16).toString(), ",")
      .concat(parseInt(g, 16).toString(), ",")
      .concat(parseInt(b, 16).toString(), ",")
      .concat(a)
  );
}

const rgbaToHex = (t: string) => {
  const e = t.split(","),
    r = e.slice(0, 4);
  let n = Number(r[0]),
    g = Number(r[1]),
    b = Number(r[2]),
    a = Number(r[3]);
  return (
    (a = parseFloat(a + "")),
    (n = Math.floor(a * parseInt(n + "") + 255 * (1 - a))),
    (g = Math.floor(a * parseInt(g + "") + 255 * (1 - a))),
    (b = Math.floor(a * parseInt(b + "") + 255 * (1 - a))),
    "#"
      .concat(("0" + n.toString(16).toUpperCase()).slice(-2))
      .concat(("0" + g.toString(16).toUpperCase()).slice(-2))
      .concat(("0" + b.toString(16).toUpperCase()).slice(-2))
  );
}

export const setDefaultColor = (t: string) => {
  let source = t.toUpperCase();
  source = (source = source.replace(/\s*/g, "")).replace(/，/g, ",");
  const e = {
    hex1: /^#[0-9A-F]{3}$/,
    hex2: /^#[0-9A-F]{6}$/,
    rgb: /^(RGB\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9])\)?$/,
    argb: /^#[0-9A-F]{8}$/,
    rgba: /^(RGBA\()?(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),([01]|0?\.\d+|1\.0+)\)?$/,
  };
  let r = "";
  for (const o in e) {
    if (e[o].test(source)) {
      r = o;
      break;
    }
  }
  if (r) {
    let n = "";
    switch (r) {
      case "hex1":
      case "hex2":
        n = hexToRgba(source);
        break;
      case "rgb":
        n = rgbToRgba(source);
        break;
      case "argb":
        n = argbToRgba(source);
        break;
      case "rgba":
        {
          const data = source.match(/(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),(25[0-5]|2[0-4][0-9]|[0-1]?[0-9]?[0-9]),((0?\.\d+)|(1\.0+)|[01])/);
          if (data) {
            n = data[0];
          }
          break;
        }


    }
    console.log(n);

    return rgbaToHex(n);
  }
}



const colorHex = (rgb: string) => {
  rgb = rgb.trim();
  const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;
  if (/^(rgb|RGB)/.test(rgb)) {
    const aColor = rgb.replace(/(?:\(|\)|rgb|RGB)*/g, "").split(",");
    let strHex = "#";
    for (let i = 0; i < aColor.length; i++) {
      let hex = Number(aColor[i]).toString(16);
      if (hex.length === 1) {
        hex = "0" + hex;
      }
      strHex += hex;
    }
    if (strHex.length !== 7) {
      strHex = rgb;
    }
    return strHex;
  } else if (reg.test(rgb)) {
    const aNum = rgb.replace(/#/, "").split("");
    if (aNum.length === 6) {
      return rgb;
    } else if (aNum.length === 3) {
      let numHex = "#";
      for (let i = 0; i < aNum.length; i += 1) {
        numHex += aNum[i] + aNum[i];
      }
      return numHex;
    }
  } else {
    return rgb;
  }
};

const matchStrDel = (str: string, re: RegExp) => {
  let str1 = str;
  const ff = str1.match(re);
  if (ff) {
    for (let i = 0; i < ff.length; i++) {
      str1 = str1.replace(ff[i], "");
    }
  }
  return str1;
};

const matchStrGet = (str: string, re: RegExp): any => {
  const str1 = str;
  const ff = str1.match(re);
  if (ff && ff[0]) {
    return ff[0];
  } else {
    return null;
  }
};

const unescapeHtml = function (str: string) {
  if (Object.prototype.toString.call(str) !== '[object String]') {
    return '';
  }
  const capText = (match: string | undefined): any => {
    if (match) {
      return ({
        '&lt;': '<',
        '&gt;': '>',
        '&amp;': '&'
      })[match];
    }
    return "";
  }
  return str.replace(/&lt;|&gt;|&amp;/g, capText);
};



const reColor = /rgb\(.*?\)/;
const reSize = /(?<=font-size:).*?(?=px)/;

export const parseLabel = (str: string) => {
  str = unescapeHtml(str);
  const re = /(<.+?>)|[^>]+(?=<)/gi;
  const ff = str.match(re);
  console.warn("parseLabel", ff);

  let globColor = "#000000";
  let globSize = "32";
  let targetStr = "";
  const creatRich = (richStr: string, labelStr: string) => {
    let targetRichStr = richStr;
    if (labelStr.indexOf("<strong") != -1) {
      targetRichStr = "<b>" + targetRichStr + "</b>";
    }
    if (labelStr.indexOf("<em") != -1) {
      targetRichStr = "<i>" + targetRichStr + "</i>";
    }
    if (labelStr.indexOf("<u") != -1) {
      targetRichStr = "<u>" + targetRichStr + "</u>";
    }
    return targetRichStr;
  };
  if (ff) {
    for (let i = 0; i < ff.length; i++) {
      if (ff[i].indexOf("<") != -1) {
        if (ff[i + 1] === "" || !ff[i + 1]) {
          const strColor = matchStrGet(ff[i], reColor);
          if (strColor) {
            // const cor = colorHex(strColor);
            const cor = setDefaultColor(strColor);
            if (cor) {
              globColor = cor;
            }
          }
          const strSize = matchStrGet(ff[i], reSize);
          if (strSize) {
            globSize = strSize;
          }
        }
      } else {
        if (ff[i - 1] && ff[i - 1].indexOf("<") != -1) {
          // 设置为局部大小或者颜色
          const strColor = matchStrGet(ff[i - 1], reColor);
          let needColor = globColor;
          if (strColor) {
            // const cor = colorHex(strColor);
            const cor = setDefaultColor(strColor);
            if (cor) {
              needColor = cor;
            }
          }
          let needSize = globSize;
          const strSize = matchStrGet(ff[i - 1], reSize);
          if (strSize) {
            needSize = strSize;
          }
          targetStr += creatRich(
            "<color=" +
            needColor +
            "><size=" +
            needSize +
            ">" +
            ff[i] +
            "</size></color>",
            ff[i - 1],
          );
        } else {
          // 设置为全局属性
          targetStr +=
            "<color=" +
            globColor +
            "><size=" +
            globSize +
            ">" +
            ff[i] +
            "</size></color>";
        }
      }
    }
    console.log(targetStr);
    return targetStr;
  }
  console.log(
    "<color=" +
    globColor +
    "><size=" +
    globSize +
    ">" +
    str +
    "</size></color>",
  );
  return (
    "<color=" + globColor + "><size=" + globSize + ">" + str + "</size></color>"
  );

  // return str.replace(/<[^>]+>/g,"");//去掉所有的html标记

  // str = str.replace(/<p(.*?)>/g, "");
  // str = str.replace(/<\/\p>/g, "");
  // // str = str.replace(/(^\s+)|(\s+$)|\s+/g, '')
  // let start = 0;
  // while (str.indexOf(" ", start) != -1) {
  //   const index1 = str.indexOf(" ", start);
  //   if (str.indexOf(">", index1) < str.indexOf("<", index1)) {
  //     const strArr = str.split("");
  //     strArr.splice(index1, 1);
  //     str = strArr.join("");
  //   } else {
  //     start = index1 + 1;
  //   }
  // }
  // str = str.replace(/color:/g, "");
  // str = str.replace(/<strongstyle/g, "<b><color");
  // str = str.replace(/<strong>/g, "<b><color=#ffffff>");
  // str = str.replace(/<\/strong>/g, "</color></b>");
  // str = str.replace(/<spanstyle/g, "<color");
  // str = str.replace(/<\/span>/g, "</color>");
  // str = str.replace(/<span>/g, "<color=#ffffff>");
  // str = str.replace(/<emstyle/g, "<i><color");
  // str = str.replace(/<em>/g, "<i>");
  // str = str.replace(/<\/em>/g, "</color></i>");
  // str = str.replace(/<ustyle/g, "<u><color");
  // str = str.replace(/<\/u>/g, "</color></u>");
  // str = str.replace(/<u>/g, "<u>");
  // while (str.indexOf("font-size") != -1) {
  //   const index1 = str.indexOf("font-size");
  //   const index2 = str.indexOf("px", index1);
  //   const index3 = str.indexOf(">", index2);
  //   const index4 = str.indexOf("<", index3);
  //   const size = str.slice(index1 + 10, index2);
  //   const strArr = str.split("");
  //   strArr.splice(index3, 0, "><size=" + size);
  //   strArr.splice(index4 + 1, 0, "</size>");
  //   strArr.splice(index1, index2 - index1 + 3);
  //   str = strArr.join("");
  //   console.log(size, "size");
  // }
  // while (str.indexOf("rgb(") != -1) {
  //   const index1 = str.indexOf("rgb(");
  //   const index2 = str.indexOf(")", index1);
  //   console.log(str.slice(index1, index2 + 1));
  //   const hex = colorHex(str.slice(index1, index2 + 1));
  //   const strArr = str.split("");
  //   strArr.splice(index1 - 1, index2 + 4 - index1, hex as string);
  //   str = strArr.join("");
  //   console.log(hex, "hex");
  // }
  // console.log(str);
  // return str;
};

export const widgetToComponent = async (widget: Widget) => {
  const props = widget.props;
  let newComponent = null;
  if (widget.role === 100) {
    return newComponent;
  }
  switch (widget.name) {
    case "text":
    case "textplaceholder":
      {
        const width: number = props.clientWidth
          ? Number(props.clientWidth)
          : 600;
        const height: number = props.clientHeight
          ? Number(props.clientHeight)
          : 101;
        const fontSize =
          typeof props.fontSize === "number"
            ? props.fontSize
            : ((props.fontSize as unknown) as string).slice(
              0,
              //@ts-ignore
              props.fontSize.length - 2,
            );
        const labelComponent: Omit<LabelComponent, "id"> = {
          type: "label",
          tag: "",
          dragable: true,
          properties: {
            active: true,
            fontSize: Number(fontSize),
            lineHeight: Number(fontSize) + 5,
            width: width,
            height: height,
            opacity: props.opacity * 255,
            angle: -Number(props.rotate.replace("deg", "")),
            x: props.left - 1280 / 2 + width / 2,
            y: 720 / 2 - props.top - height / 2,
            string: "请输入文本",
            str: parseLabel(widget.content),
            color: "#000000",
            cusorIndex: 1,
            selectArr: [],
            isLabelRight: true,
          },
        };
        newComponent = labelComponent;
      }

      break;
    case "pic":
    case "picPlaceholder":
      {
        if (widget.content.indexOf(".png") !== -1) {
          if (!/\.(png)$/.test(widget.content))
            widget.content =
              widget.content.slice(0, widget.content.indexOf(".png")) + ".png";
        }
        if (widget.content.indexOf(".gif") !== -1) {
          // git图拦截+提示
          showErrorMessage(new Error("cocos不支持插入gif图~"));
          return;
        }
        const urls = await checkPicturesSize([widget.content]);
        if (urls.length === 0) return;
        const spriteComponent: Omit<SpriteComponent, "id"> = {
          tag: "",
          type: "sprite",
          dragable: true,
          properties: {
            active: true,
            width: Number(props.width),
            height: Number(props.height),
            x: props.left - 1280 / 2 + Number(props.width) / 2,
            y: 720 / 2 - props.top - Number(props.height) / 2,
            texture: urls[0],
            opacity: props.opacity * 255,
            angle: -Number(props.rotate.replace("deg", "")),
          },
        };
        newComponent = spriteComponent;
      }

      break;
    case "group":
    default:
      break;
  }
  return newComponent;
};
