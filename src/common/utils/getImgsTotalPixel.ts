import { getResourceList } from "@/pages/index/components/TopBar/Operations/dataProcessor";
import { Message } from "element-ui";
import { CATEGORY } from "@/common/constants";
export const getImgResolution = (
  src: string,
): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = function () {
      resolve({
        width: img.naturalWidth || img.width,
        height: img.naturalHeight || img.height,
      });
    };
    img.onerror = () => {
      reject(new Error("图片加载失败 " + src));
    };
    img.src = src;
  });
};

export const getImgFileSize = (src: string): Promise<{ size: number }> => {
  return new Promise((resolve) => {
    fetch(src)
      .then(function (res) {
        return res.blob();
      })
      .then(function (data) {
        console.log(data.size / 1024, "kb,imgSize");
        resolve({ size: data.size });
      }).catch((error) => {
        console.log('getImgFileSize error', error);
      });
  });
};

export const getImgsTotalPixelAndSize = async (urls: string[]) => {
  const imgPattern = /(\.jpg|\.png|\.jpeg)$/;
  const imgUrls = urls.filter(url => imgPattern.test(url));
  const getImgsResolution = imgUrls.map(url => getImgResolution(url));
  const resolutions = await Promise.all(getImgsResolution);
  const totalPixel = resolutions.reduce((prev, curr) => {
    return prev + curr.width * curr.height;
  }, 0);
  const getImgsSize = imgUrls.map(url => getImgFileSize(url));
  const imgsSizeList = await Promise.all(getImgsSize);
  const totalImgsSize = imgsSizeList.reduce((prev, curr) => {
    return prev + curr.size;
  }, 0);
  return { totalPixel, totalImgsSize };
};
const loadTextureMemory = async (url: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    let totalTextureSize = 0;
    cc.assetManager.loadRemote(url, cc.Asset, (err: any, asset: cc.Asset) => {
      if (!err) {
        if (asset instanceof cc.Texture2D) {
          const textureSize = asset.width * asset.height * 4;
          totalTextureSize += textureSize;
        }
      }
      resolve(totalTextureSize);
    });
  });
};
const getTextureMemory = async (url: string) => {

  let totalTextureSize = 0;
  if (window.cc) {
    const cacheData = cc.assetManager.assets;
    cacheData.forEach((item, key) => {
      if (item['_texture'] && item.nativeUrl == url && item.nativeUrl.indexOf('http') != -1) {
        const textureSize = item['width'] * item['height'] * 4;
        totalTextureSize += textureSize;
        return totalTextureSize;
      }

    });
    const resourceSuffixArr = [".jpg", ".png", ".jpeg"];
    if (totalTextureSize == 0 &&
      (url.includes(resourceSuffixArr[0]) || url.includes(resourceSuffixArr[1])
        || url.includes(resourceSuffixArr[2])
      )) {
      totalTextureSize = await loadTextureMemory(url);
    }
  }
  return totalTextureSize;
}

let culResList: string[] = [];
export const setCulResList = (urls: string[]) => {
  culResList = urls;
};
const unUseResourceArr = [".MP3", ".mp4", ".gif", ".zip"];


const categoryTotalMemoryMbMax = (state: any): number => {
  let dataSizeLimit = 40;
  switch (state.template.category) {
    case CATEGORY.XIAOLUPRACTICE:
      dataSizeLimit = 100;
      break;
    case CATEGORY.WORDBOMB:
      dataSizeLimit = 70;
      break;
    default:
      dataSizeLimit = 40;
      break;
  }
  return dataSizeLimit;
}


/**
 * 根据题目类型获取spine动画数量上限
 * @param state 题目状态
 * @returns spine动画数量上限
 */
export const categoryTotalSpineMax = (state: any): number => {
  let spineMax = 15;
  switch (state.template.category) {
    case CATEGORY.WORDBOMB: // 单词炸弹类型题目
      spineMax = 20;
      break;
    default: // 其他类型题目
      spineMax = 15;
      break;
    }
  return spineMax;
}

/**
 * 根据题目类型获取音频数量上限
 * @param state 题目状态
 * @returns 音频数量上限
 */
export const categoryTotalAudioMax = (state: any): number => {
  let audioMax = 15;
  switch (state.template.category) {
    case CATEGORY.WORDBOMB: // 单词炸弹类型题目
      audioMax = 20;
      break;
    default: // 其他类型题目
      audioMax = 15;
      break;
    }
  return audioMax;
}

export const culResListSize = async (state: any): Promise<boolean> => {
  // const { totalPixel, totalImgsSize } = await getImgsTotalPixelAndSize(culResList);
  // const imgsTotalSizeMb = (totalImgsSize / 1024 / 1024).toFixed(2);

  const urlMap = {}
  let haveUnUseResource = "";
  const resourceList = getResourceList(state);
  let imageSize = 0;
  for (let i = 0; i < resourceList.length; i++) {
    if (!urlMap[resourceList[i]]) {
      urlMap[resourceList[i]] = 1;
      imageSize += await getTextureMemory(resourceList[i]);
      for (let j = 0; j < unUseResourceArr.length; j++) {
        if (resourceList[i].indexOf(unUseResourceArr[j]) > -1) {
          haveUnUseResource = resourceList[i];
          break;
        }

      }
    }
    if (haveUnUseResource) {
      break;
    }
  }
  if (haveUnUseResource) {
    Message.error({
      message: haveUnUseResource + "资源格式不支持请更换",
    });
    return false;
  }

  const imgsTotalMemoryMb = Number((imageSize / 1024 / 1024).toFixed(2));
  console.log("预估图片将占用内存:", imgsTotalMemoryMb);
  let checkSize = true;
  const maxMemLimt = categoryTotalMemoryMbMax(state);
  console.log("maxMemLimt", maxMemLimt)
  if (imgsTotalMemoryMb > maxMemLimt) {
    Message.error({
      message: "单题最大支持" + maxMemLimt + "MB，请删除一些试题资源~",
    });
    checkSize = false;
  } else {
    Message.info({
      message: "预估图片将占用内存:" + imgsTotalMemoryMb + "MB",
    });
    checkSize = true;
  }
  // console.log("预估图片将占用内存:", imgsTotalMemoryMb + "MB", "预估图片总体积:", imgsTotalSizeMb + "MB");
  return checkSize;
};

export const getResourcesListMemoryMbSize = async (state: any): Promise<number> => {
  const urlMap = {}
  let haveUnUseResource = "";
  const resourceList = getResourceList(state);
  let imageSize = 0;
  for (let i = 0; i < resourceList.length; i++) {
    if (!urlMap[resourceList[i]]) {
      urlMap[resourceList[i]] = 1;
      imageSize += await getTextureMemory(resourceList[i]);
      for (let j = 0; j < unUseResourceArr.length; j++) {
        if (resourceList[i].indexOf(unUseResourceArr[j]) > -1) {
          haveUnUseResource = resourceList[i];
          break;
        }

      }
    }
    if (haveUnUseResource) {
      break;
    }
  }

  const imgsTotalMemoryMb = Number((imageSize / 1024 / 1024).toFixed(2));
  return imgsTotalMemoryMb;
};
