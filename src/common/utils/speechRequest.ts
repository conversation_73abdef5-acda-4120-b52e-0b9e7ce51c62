/*
 * @Date: 2021-09-03 16:16:29
 * @LastEditors: chxu
 * @LastEditTime: 2022-01-07 17:36:54
 * @FilePath: /interactive-question-editor/src/common/utils/request.ts
 * @Author: chxu
 */
import axios from "axios";

//     url: https://speech.zuoyebang.com/v1/tts
// method: post
// content-type: application/json;
// params: {format: "mp3", result_type: "url", voice: "Aitong", text: "fdasfas"}
// res: {
//   "audio_url": "https://asraudio.cdnjtzy.com/5912815ab8fe4ee9aef8ed41e0d9cab4.mp3",
//   "id": "dc3f653b-d55c-4e4a-95fa-4f37e8fc869b"
// }

export const requestSpeechServer = axios.create({
  // withCredentials: true,
  // baseURL: '//speech.zuoyebang.com/',
  baseURL: '',
  headers: {
    "Content-Type": "application/json",
  },
});

requestSpeechServer.interceptors.response.use(
  res => {
    if (res.data.errNo) {
      throw res;
    }
    if(!res.data.data) res.data = {
      data: res.data
    };
    return res;
  },
  (error: Error) => {
    console.error(error);
    throw error;
  },
);
