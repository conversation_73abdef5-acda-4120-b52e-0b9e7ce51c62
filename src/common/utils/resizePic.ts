import getImageSize from "./getImageSize";
import { blobToUrl } from "./uploadBlob";
import { Message } from "element-ui";
import { getImgFileSize } from "./getImgsTotalPixel";
import bus from "@/pages/index/common/utils/bus";
import axios from "axios";
import store from "@/pages/index/store";

const MAX_PIXEL_W = 1280;
const MAX_PIXEL_H = 960;
const kb = 1024;
const SIZE_LIMI = 1000 * kb;
// const warning = "有图片不符合使用规范，此类图片需要压缩至合规后才可在Cocos中使用，是否压缩？";
export const image2Canvas = (image: any, maxWidth: number, maxHeight: number, scale = 1) => {
  // 最大尺寸限制
  // 需要压缩的目标尺寸
  let targetWidth = image.naturalWidth * scale,
    targetHeight = image.naturalHeight * scale;
  // 等比例计算超过最大限制时缩放后的图片尺寸
  if (targetWidth > maxWidth || targetHeight > maxHeight) {
    if (targetWidth / maxWidth > targetHeight / maxHeight) {
      // 宽图片
      targetHeight = Math.round(maxWidth * (targetHeight / targetWidth));
      targetWidth = maxWidth;
    } else {
      // 高图片
      targetWidth = Math.round(maxHeight * (targetWidth / targetHeight));
      targetHeight = maxHeight;
    }
  }
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  canvas.width = targetWidth;
  canvas.height = targetHeight;
  (ctx as any).drawImage(image, 0, 0, canvas.width, canvas.height);
  return canvas;
};



export const compressImg = (src: string, type: any, mw: number, mh: number) => {
  return new Promise((resolve) => {
    const img = new Image();
    img.crossOrigin = "Anonymous";
    img.onload = async function () {
      const canvas = image2Canvas(img, mw, mh);
      canvas.toBlob(
        function (blob) {
          resolve(blob);
        }, type || "image/png", 0.92);
    };
    img.src = src;
  });
};

export const resizePicture = async (src: string, type: any, mw: number, mh: number) => {
  const blob = await compressImg(src, type, mw, mh);
  const url: string = await blobToUrl(blob, "img.png");
  return url;
};

const checkPicturesFileSize = async (urls: string[]) => {
  const newUrls: string[] = [];
  for (const url of urls) {
    const imgData = await getImgFileSize(url);
    if (imgData.size > SIZE_LIMI) {
      Message.error("图片文件大小超过1000kb,无法插入到编辑器");
    } else {
      newUrls.push(url);
    }
  }
  console.log("checkPicturesFileSize", newUrls);
  return newUrls;
};

/**
 * 检查URL是否是带有图片处理参数的CDN图片URL
 * @param url 图片URL
 * @returns 是否是CDN图片URL
 */
export const isCdnImageUrl = (url: string): boolean => {
  // 匹配图片扩展名后带有@参数的URL
  // const cdnImageRegex = /\.(jpg|jpeg|png|webp)@[^/]+$/i;
  const cdnImageRegex = /^https?:\/\/[^\s]+?\.(jpg|jpeg|png|webp)(\?|@).+$/i;
  // /^https?:\/\/[^\s]+?\.(jpg|jpeg|png|webp)(\?|@).+$/i
  return cdnImageRegex.test(url);
};

/**
 * 下载图片并转换为Blob
 * @param url 图片URL
 * @returns Promise<Blob>
 */
const downloadImage = async (url: string): Promise<Blob> => {
  try {
    const response = await axios({
      url, // 使用完整的URL，包含处理参数
      method: 'GET',
      responseType: 'blob'
    });
    return response.data;
  } catch (error) {
    console.error('下载图片失败:', error);
    throw error;
  }
};

export const processAndReuploadImage = async (url: string): Promise<string> => {
  try {
    if (isCdnImageUrl(url)) {
      console.log('xu-cdn-params-url', url);
      // 如果包含处理参数，重新上传
      const imageBlob = await downloadImage(url);
      const newUrl = await blobToUrl(imageBlob, "img.png");
      console.log('xu-cdn-params-newUrl', newUrl);
      return newUrl;
    }
    // 如果不包含处理参数，直接返回原始URL
    return url;
  } catch (error) {
    console.error('处理图片失败:', error);
    // 如果处理失败，返回原始URL
    return url;
  }
}

export const refreshCdnUrl = async () => {
  // 寻找有cdn规则的组件 重新上传后更新属性
const { componentMap } = store.state;

const findImageParams = (obj: any): any[] => {
  const result: any[] = [];

  for (const [id, comp] of Object.entries(obj)) {
    for (const [key, value] of Object.entries((comp as any).properties)) {
      const path = key;
      if (typeof value === 'string' && isCdnImageUrl(value)) {
        result.push({ id, key: path, value });
      }
    }
  }

  return result;
};
const result = findImageParams(componentMap);
// console.log('result...', result);
result.forEach(async (item: any) => {
  const newUrl = await processAndReuploadImage(item.value);
  if (newUrl) {
    store.commit("updateComponentProperties", {
      id: item.id,
      newProperties: {
        [item.key]: newUrl,
      },
      // ignoreHistory: true, // 忽略历史记录 设置这个属性 组件消失但是画布中图片还在
    });
  }
})
}

export const checkPicturesSize = async (urls: string[]) => {
  const overSizeUrls: string[] = [];
  const normalSizeUrls: string[] = [];
  for (const url of urls) {
    let tempUrl = url;
    // cdn图片带有参数的 @1000w_1000h_1e_1c_1x 这种格式的图片 需要重新上传
    if (isCdnImageUrl(url)) {

      tempUrl = await processAndReuploadImage(url); // 处理图片并重新上传
    }
    const { width, height } = await getImageSize(tempUrl);
    if (width > MAX_PIXEL_W || height > MAX_PIXEL_H) {
      overSizeUrls.push(tempUrl);
    } else {
      normalSizeUrls.push(tempUrl);
    }
  }

  bus.$emit("showPageLoading", true);

  return new Promise<string[]>((resolve) => {
    let tempUrls = [];
    if (overSizeUrls.length > 0) {
      Message({
        type: "info",
        dangerouslyUseHTMLString: true,
        message: `图片已自适应画布尺寸`,
        duration: 3000,
      });
      // 将overSizeUrls进行裁剪
      const urlsHandler: Promise<string>[] = [];
      overSizeUrls.forEach(url => {
        const imgType = url.indexOf(".png") !== -1 ? "image/png" : "image/jpeg";
        urlsHandler.push(resizePicture(url, imgType, MAX_PIXEL_W, MAX_PIXEL_H));
      });
      Promise.all(urlsHandler).then(async urls => {
        tempUrls = await checkPicturesFileSize([...normalSizeUrls, ...urls]);
        bus.$emit("showPageLoading", false);
        resolve(tempUrls);
      });
    } else {
      checkPicturesFileSize(normalSizeUrls).then(urls => {
        bus.$emit("showPageLoading", false);
        resolve(urls);
      });
    }
  });
};

