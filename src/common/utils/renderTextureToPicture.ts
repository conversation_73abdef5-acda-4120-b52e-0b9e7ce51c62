import { Message } from "element-ui";
import { showErrorMessage } from "./showErrorMessage";
import { read } from "@/pages/index/common/utils/getQueryParams";

export const postFile = async (formData: any, cb: Function) => {
  try {
    const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
    cb(url);
  } catch (error) {
    setTimeout(() => {
      console.log("正在重新上传");
      postFile(formData, cb);
    }, 1000);
  }
}

let IsUploadData = false;
let IsUploadList = false;
export const getIsUploadData = (): boolean => {
  return IsUploadData || IsUploadList;
};

export const getIsUploadEnable = (bo: boolean) => {
  IsUploadData = bo;
};



export class PixelToPicUrlQuence {
  private static _instance: PixelToPicUrlQuence;
  private _needUploadData: any[] = [];
  private _maxQuence = 5;
  private _countQuence = 0;

  private _needUploadupFileList: any[] = [];
  private _maxFileQuence = 4;
  private _countFileQuence = 0;
  private _callBackResultList = new Map();
  private _picNum = 0;
  private _callBack: any;
  public static get Instance(): PixelToPicUrlQuence {
    if (!PixelToPicUrlQuence._instance) {
      PixelToPicUrlQuence._instance = new PixelToPicUrlQuence();
    }
    return PixelToPicUrlQuence._instance;
  }
  private async picListUpload(resolve: any, currData: any) {
    const formData = new FormData();
    formData.append("file", currData.file);

    const callBack = (url: string) => {
      if (url) {
        PixelToPicUrlQuence._instance._callBackResultList.set(currData.index, url);
        resolve(url);
      }
    }

    postFile(formData, callBack);

  }

  private async picListManger() {
    const currData = PixelToPicUrlQuence._instance._needUploadupFileList.shift();
    if (!currData) {
      PixelToPicUrlQuence._instance._countFileQuence -= 1;
      if (PixelToPicUrlQuence._instance._needUploadupFileList.length < 1 && PixelToPicUrlQuence._instance._countFileQuence < 1) {
        IsUploadList = false;
        const resultUrl = [];
        for (let i = 0; i < PixelToPicUrlQuence._instance._picNum; i++) {
          resultUrl.push(PixelToPicUrlQuence._instance._callBackResultList.get(i));
        }
        PixelToPicUrlQuence._instance._callBack(resultUrl);
      }
      return;
    }

    await new Promise((resolve, reject) => {
      PixelToPicUrlQuence._instance.picListUpload(resolve, currData);
    });
    PixelToPicUrlQuence._instance.picListManger();
  }

  public starUploadPicList(fileList: any, callBack: Function) {
    if (fileList.length < 1) {
      Message.error("上传数据为空");
      return;
    }
    if (IsUploadData) {
      Message.error("上传接口正在占用");
      return;
    }
    if (PixelToPicUrlQuence._instance._countFileQuence >= PixelToPicUrlQuence._instance._maxFileQuence) {
      console.log("文件线程满了");
      return;
    }
    IsUploadList = true;
    PixelToPicUrlQuence._instance._needUploadupFileList = [];
    for (let i = 0; i < fileList.length; i++) {
      PixelToPicUrlQuence._instance._needUploadupFileList.push({ index: i, file: fileList[i] });
    }
    PixelToPicUrlQuence._instance._callBack = callBack;
    PixelToPicUrlQuence._instance._picNum = PixelToPicUrlQuence._instance._needUploadupFileList.length;
    PixelToPicUrlQuence._instance._callBackResultList.clear();

    for (let i = 0; i < PixelToPicUrlQuence._instance._needUploadupFileList.length; i++) {
      if (PixelToPicUrlQuence._instance._countFileQuence < PixelToPicUrlQuence._instance._maxFileQuence) {
        PixelToPicUrlQuence._instance._countFileQuence += 1;
        setTimeout(() => {
          PixelToPicUrlQuence._instance.picListManger();
        }, i);
      } else {
        break;
      }
    }
  }

  async pixelsPicUpload(resolve: any, currData: any, blob: any) {
    const file = new File([blob], currData.fileName, { type: blob.type });
    const formData = new FormData();
    formData.append("file", file);
    // 梳理minify的使用
    if (currData.minify) {
      formData.append("minify", currData.minify)
    }
    const callBack = (url: string) => {
      if (url) {
        resolve(url);
      }
    }
    postFile(formData, callBack);
  }
  private pixelsToPic(currData: any, h1: number, h2: number): Promise<string> {
    return new Promise((resolve) => {
      const canvas = document.createElement("canvas");
      const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
      const width = currData.imageData.width;
      canvas.width = width;
      const height = currData.imageData.height;
      canvas.height = h2 - h1;
      const rowBytes = width * 4;
      for (let row = h1; row < h2; row++) {
        const imageData = ctx.createImageData(width, 1);
        const srow = height - 1 - row;
        const start = srow * width * 4;
        for (let i = 0; i < rowBytes; i++) {
          imageData.data[i] = currData.imageData.pixels[start + i];
        }
        ctx.putImageData(imageData, 0, row - h1);
      }
      canvas.toBlob(
        async blob => {
          if (!blob) {
            console.error("生成缩略图失败");
            return;
          }
          PixelToPicUrlQuence._instance.pixelsPicUpload(resolve, currData, blob);
        },
        currData.format,
        currData.quality,
      );
    });
  }
  private async pixelsToPictureArray(currData: any) {
    // console.log("currData", currData);

    const height = currData.imageData.height;
    const picPackArrayHeight = [0];
    let currIndex = 0;
    while (currIndex < height) {
      if (currIndex + 720 < height) {
        currIndex += 720;
        picPackArrayHeight.push(currIndex);
      } else {
        currIndex = height;
        picPackArrayHeight.push(height);
        break;
      }
    }

    console.log("picPackArrayHeight", picPackArrayHeight);
    const resultUrl: string[] = [];
    for (
      let indexI = 0;
      indexI < picPackArrayHeight.length;
      indexI++ // 翻着截图
    ) {
      const h1 = picPackArrayHeight[indexI];
      const h2 = picPackArrayHeight[indexI + 1];
      if (h2) {
        const url = await PixelToPicUrlQuence._instance.pixelsToPic(currData, h1, h2);
        resultUrl.push(url);
      }
    }
    if (currData.component) {
      // currData.component['properties'][currData.setKey] = resultUrl;
      (window as any)._$store.commit("updateComponentProperties", {
        id: currData.component.id,
        newProperties: {
          [currData.setKey]: resultUrl,
        },
      });
    }
    PixelToPicUrlQuence._instance.pixelsToPictureArrayManger();
  }

  private pixelsToPictureArrayManger() {
    const currData = PixelToPicUrlQuence._instance._needUploadData.shift();
    if (!currData) {
      PixelToPicUrlQuence._instance._countQuence -= 1;
      if (PixelToPicUrlQuence._instance._needUploadData.length < 1 && PixelToPicUrlQuence._instance._countQuence < 1) {
        IsUploadData = false;
      }
      return;
    }
    PixelToPicUrlQuence._instance.pixelsToPictureArray(currData);
  }

  public starUploadPic() {
    IsUploadData = true;
    if (PixelToPicUrlQuence._instance._countQuence >= PixelToPicUrlQuence._instance._maxQuence) {
      console.log("线程满了");
      return;
    }
    if (PixelToPicUrlQuence._instance._needUploadData.length < 1) {
      IsUploadData = false;
    }
    for (let i = 0; i < PixelToPicUrlQuence._instance._needUploadData.length; i++) {
      if (PixelToPicUrlQuence._instance._countQuence < PixelToPicUrlQuence._instance._maxQuence) {
        PixelToPicUrlQuence._instance._countQuence += 1;
        setTimeout(() => {
          PixelToPicUrlQuence._instance.pixelsToPictureArrayManger();
        }, i);
      } else {
        break;
      }
    }
  }
  public addQueuePicData(component: any, setKey: string, texture: cc.RenderTexture, format = "image/jpeg", quality = 0.5, fileName = "thumbnail.jpeg") {
    console.log('addQueuePicData');
    const width = texture.width;
    const height = texture.height;
    const isReadTextComp = (component.type === 'label' && component.subType
      === "readText"); // 旧的阅读题文本组件
    if (!isReadTextComp && (width > 1280 || height > 960)) {
      // 提示错误 清空key
      (window as any)._$store.commit("updateComponentProperties", {
        id: component.id,
        newProperties: {
          [setKey]: [],
        },
      });
      showErrorMessage(new Error('文本组件的宽不可超过1280,高不可超过960'))
      return;
    }
    const pixels = new Uint8Array(texture.readPixels());
    PixelToPicUrlQuence._instance._needUploadData.push({
      component,
      setKey,
      imageData: {
        pixels,
        width,
        height,
      },
      format,
      quality,
      fileName,
      minify:"0"
    });
    // textToImg 调用时， 调用这个函数。 此时，上传图片时，会传 minify:"0"
    if (!(window as any)._$store.state.cocosInitFinished) {
      return;
    }
    if (read === '1') return;
    PixelToPicUrlQuence._instance.starUploadPic();
  }
}

// const url1 = window.URL.createObjectURL(blob);
// const aLink = document.createElement("a");
// aLink.style.display = "none";
// aLink.href = url1;
// aLink.setAttribute("download", "xxx");
// document.body.appendChild(aLink);
// aLink.click();
// document.body.removeChild(aLink); //下载完成移除元素
// window.URL.revokeObjectURL(url1); //释放掉blob对象
