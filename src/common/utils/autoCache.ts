import { fromGroup } from "@/pages/index/common/utils/getQueryParams";
import { MessageBox } from "element-ui";
import { TimeMonitorType } from "./monitorUtil";

export const AUTO_CACHE = 'autoCache';

export const getCacheData = () => {
  const cacheData = window.localStorage.getItem(AUTO_CACHE);
  return cacheData ? JSON.parse(cacheData) : {};
}

export const clearCacheData = () => {
  window.localStorage.removeItem(AUTO_CACHE)
}

export const setCacheData = (data:any) => {
  // 屏蔽cacheData
  if (localStorage.getItem('notCache')) return;
  window.localStorage.setItem(AUTO_CACHE, data)
}


export const reCacheConfirm = (data: any) => {
  return new Promise<void>((resolve) => {
    if (!data || fromGroup) {
      resolve(data);
      return;
    }
    const cacheData: any = getCacheData();
    if (cacheData && cacheData.category === data.category) {
      (window as MyWindow).monitorManager.setStartByType(TimeMonitorType.RecoverPreQuestion);
      MessageBox.confirm("是否恢复上次保存的试题？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.RecoverPreQuestion);
          (window as MyWindow).monitorManager.reportLog('recoverPreQuestion', { val: 1 });
          resolve(cacheData);
        })
        .catch(() => {
          (window as MyWindow).monitorManager.setEndByType(TimeMonitorType.RecoverPreQuestion);
          (window as MyWindow).monitorManager.reportLog('recoverPreQuestion', { val: 0 });
          resolve(data);
        });
    } else {
      resolve(data);
    }
  });
}
