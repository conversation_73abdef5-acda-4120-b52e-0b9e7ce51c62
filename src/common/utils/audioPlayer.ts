class AudioPlayer {
  _audioElMap: {
    [key: string]: HTMLAudioElement;
  } = {};

  _getAudioEl(src: string): HTMLAudioElement {
    if (this._audioElMap[src]) {
      return this._audioElMap[src];
    } else {
      const audioEl = document.createElement("audio");
      audioEl.src = src;
      document.body.appendChild(audioEl);
      this._audioElMap[src] = audioEl;
      return audioEl;
    }
  }

  play(src: string, errCb?: () => void, endCb?: () => void): void {
    const audioEl = this._getAudioEl(src);
    audioEl.currentTime = 0;
    audioEl.onerror = function() {
      errCb && errCb();
    };
    audioEl.onended = function() {
      endCb && endCb();
    };
    audioEl
      .play()
      .then()
      .catch(() => {
        errCb && errCb();
      });
  }

  pause(src: string): void {
    const audioEl = this._getAudioEl(src);
    audioEl.pause();
  }

  stop(src: string): void {
    const audioEl = this._getAudioEl(src);
    audioEl.pause();
  }
}

const audioPlayer = new AudioPlayer();

export default audioPlayer;
