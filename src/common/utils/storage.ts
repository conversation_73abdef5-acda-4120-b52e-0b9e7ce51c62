/* eslint-disable @typescript-eslint/no-use-before-define */
import Vue from "vue";
const config = {
  type: 'localStorage', // 本地存储类型 localStorage sessionStorage
  prefix: 'tihu', // 名称前缀 建议：项目名 + 项目版本
  expire: 0, //过期时间 单位：秒
};

// 判断是否支持 Storage
export const isSupStorage = () => {
  if (!window) {
    throw new Error('当前环境非浏览器，无法消费全局window实例。');
  }
  if (!window.localStorage) {
    throw new Error('当前环境非无法使用localStorage');
  }
  if (!window.sessionStorage) {
    throw new Error('当前环境非无法使用sessionStorage');
  }

  return typeof Storage !== 'undefined' ? true : false;
};

// 设置 setStorage
export const setStorage = (key: string, value: any, expire = 0) => {
  if (value === '' || value === null || value === undefined) {
    value = null;
  }

  if (isNaN(expire) || expire < 0) throw new Error('Expire must be a number');

  expire = (expire ? expire : config.expire) * 1000;
  const data = {
    value: value, // 存储值
    time: Date.now(), //存值时间戳
    expire: expire // 过期时间
  };
  console.log('setStorage', config.type, autoAddPrefix(key), JSON.stringify(data));
    window[config.type].setItem(autoAddPrefix(key), JSON.stringify(data));
};

// 获取 getStorage
export const getStorage = (key: string) => {
  let value = null;
  key = autoAddPrefix(key);
  // key 不存在判断
  if (
    !window[config.type].getItem(key) ||
    JSON.stringify(window[config.type].getItem(key)) === 'null'
  ) {
    return null;
  }

  // 优化 持续使用中续期
  const storage = JSON.parse(window[config.type].getItem(key));
  const nowTime = Date.now();
  // 过期删除
  if (storage.expire && storage.expire < nowTime - storage.time) {
    removeStorage(key);
    return null;
  } else {
    // // 未过期期间被调用 则自动续期 进行保活
    // setStorage(autoRemovePrefix(key), storage.value);
    if (isJson(storage.value)) {
      value = JSON.parse(storage.value);
    } else {
      value = storage.value;
    }
    return value;
  }
};

// 是否存在 hasStorage
export const hasStorage = (key: string) => {
  key = autoAddPrefix(key);
  const arr = getStorageAll().filter(item => {
    return item.key === key;
  });
  return arr.length ? true : false;
};

// 获取所有key
export const getStorageKeys = () => {
  const items = getStorageAll();
  const keys = [];
  for (let index = 0; index < items.length; index++) {
    keys.push(items[index].key);
  }
  return keys;
};

// 根据索引获取key
export const getStorageForIndex = (index: any) => {
  return window[config.type].key(index);
};

// 获取localStorage长度
export const getStorageLength = () => {
  return window[config.type].length;
};

// 获取全部 getAllStorage
export const getStorageAll = () => {
  const len = getStorageLength(); // 获取长度
  const arr = []; // 定义数据集
  for (let i = 0; i < len; i++) {
    const key = window[config.type].key(i);
    // 获取key 索引从0开始
    const getKey = autoRemovePrefix(key);
    // 获取key对应的值
    const storage = JSON.parse(window[config.type].getItem(key));

    const nowTime = Date.now();
    if (storage.expire && nowTime - storage.time > storage.expire) {
      removeStorage(getKey);
    } else {
      let getVal = storage.value;
      // console.log(Object.prototype.toString.call(value));
      if (isJson(getVal)) {
        getVal = JSON.parse(getVal);
      }
      // 放进数组
      arr.push({ key: getKey, val: getVal });
    }
  }
  return arr;
};

// 删除 removeStorage
export const removeStorage = (key: string) => {
  window[config.type].removeItem(autoAddPrefix(key));
};

// 清空 clearStorage
export const clearStorage = () => {
  window[config.type].clear();
};

// 判断是否可用 JSON.parse
export const isJson = (value: string) => {
  if (Object.prototype.toString.call(value) === '[object String]') {
    try {
      const obj = JSON.parse(value);
      const objType = Object.prototype.toString.call(obj);
      return objType === '[object Object]' || objType === '[object Array]';
    } catch (e) {
      // console.log('error：' + value + '!!!' + e);
      return false;
    }
  }
  return false;
};

// 名称前自动添加前缀
const autoAddPrefix = (key: string) => {
  const prefix = config.prefix ? config.prefix + '_' : '';
  return prefix + key;
};

// 移除已添加的前缀
const autoRemovePrefix = (key: { substr: (arg0: string | number) => any; }) => {
  const len = config.prefix ? config.prefix.length + 1 : '';
  return key.substr(len);
};
// eslint-disable-next-line @typescript-eslint/interface-name-prefix
export interface IStorage {
  set: (key: string, value: any, expire?: number) => void;
  get: (key: string) => any;
  getAll: () => any;
  getLen: () => number;
  isSub: () => boolean;
  isJson: (value: string) => boolean;
  has: (key: string) => boolean;
  del: (key: string) => void;
  clear: () => void;
}

export default {
  set: setStorage,
  get: getStorage,
  getAll: getStorageAll,
  getLen: getStorageLength,
  isSub: isSupStorage,
  isJson: isJson,
  has: hasStorage,
  del: removeStorage,
  clear: clearStorage
};

// // main.js

// 使用
// this.$storage.set('token', 'xxx', 10);
