import axios from 'axios';
import { stringify } from 'query-string';
import { TimeMonitorType } from './monitorUtil';
import { EDITORSERVERHOST, REQUESTBASEURLPHP, REQUESTBASEURLPHPPRE } from './devHelper';
import APIS from '../api/constants';
import bus from "@/pages/index/common/utils/bus";

export const request = axios.create({
  withCredentials: true,
  baseURL: `${EDITORSERVERHOST}interactive-question-server`,
});

request.interceptors.request.use(
  config => {
    if (config.url === 'upload/uploadFile') {
      (window as MyWindow).monitorManager?.setStartByType(TimeMonitorType.PostFile);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

request.interceptors.response.use(res => {
  if (res.config.url?.includes('upload/uploadFile')) {
    (window as MyWindow).monitorManager?.setEndByType(TimeMonitorType.PostFile);
    (window as MyWindow).monitorManager?.setTimeMonitor(TimeMonitorType.PostFile);
  }
  // (window as MyWindow).monitorManager?.ajaxMonitor(res);
  if (res.data.errNo !== 0) {
    throw res;
  }
  return res;
});

export default request;

export const requestPhpServer = axios.create({
  withCredentials: true,
  baseURL: REQUESTBASEURLPHP,
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

export const requestPhpServerPre = axios.create({
  withCredentials: true,
  baseURL: REQUESTBASEURLPHPPRE,
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

requestPhpServer.interceptors.request.use(
  config => {
    if (config.method === "post" && config.headers["content-type"] !== "multipart/form-data") {
      config.data = stringify(config.data);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

requestPhpServer.interceptors.response.use(
  res => {
    // (window as MyWindow).monitorManager?.ajaxMonitor(res);
    if (res.data.errNo) {
      if (res.data?.data?.loginUrl || (typeof res.data?.data === 'string' && res.data?.data?.includes('https://'))) {
        if(top != self) {
          // console.log('刷新父级页面2', window.parent);
          bus.$emit('logout')
          // window.parent && window.parent.location.reload();
        }
      }
      throw res;
    }
    return res;
  },
  (error: any) => {
    console.error(error);
    throw error;
  },
);

// 文本组件截图-长文本和端文本对比，清晰度区别不大 迁移后size更小
// 拖拽题-拼图题-切割后的图片，尺寸10kb以内，清晰度和size 迁移前后无差别无差别
// 题目缩略图，清晰度没有差别。 size差别不大。 迁移后size更小（155kb/157kb）
// q-editor  55kb的jpeg 没有压缩
// 图片裁剪 清晰度区别不大 迁移后size更小（250kb/258kb）
// export const uploadFile = (formData: FormData): Promise<string> => {
//   // 图片上传参数 必填
//   formData.append("pd", 'zyb_cocos');
//   formData.append("type", "2"); // 最近使用
//   formData.append("categoryId", "0");
//   return new Promise((resolve, reject) => {
//     requestPhpServer.post(APIS.PHP_UPLOAD_FILE, formData, {
//       headers: {
//         "content-type": "multipart/form-data"
//       }
//     }).then((res) => {
//       if (!res.data.errNo) {
//         const url: string = res.data.data.upload.url;
//         resolve(url);
//       }
//     }).catch((error) => {
//       reject(error)
//     })
//   })
// }

export const uploadFile = (formData: FormData): Promise<string> => {
  return new Promise((resolve, reject) => {
    request.post(APIS.UPLOAD_FILE, formData).then((res) => {
      if (res && res['data'] && res.data.data) {
        const url: string = res.data.data;
        resolve(url);
      } else {
        reject(res)
      }
    }).catch((error) => {
      reject(error)
    })
  })
}
