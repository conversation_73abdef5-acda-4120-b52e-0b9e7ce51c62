import { CATEGORY } from "@/common/constants";
import { insertSignalId } from "./insertSignalId";
import { initAnalysisData, initIntroductionData } from "./initAnalysisData";
import { genFormTemplateData } from "./FormTemplateManager";
import { getComponentTagConfig } from "@/pages/index/common/tagEdtiorConfig/componentTagConfig";
import { refreshSeqMemoryExtData } from "./refreshSeqMemoryExtData";

/**
 * 1. 拖拽题分类旧数据洗为新数据
 * 2. 总数值判定修改
 */
export const refreshDragData = (category: number, data: any) => {
  // 只处理拖拽题
  if (category !== CATEGORY.DRAG) return;
  const { template, extraStageData } = data;
  // 总数值判定升级 处理就数据的值
  console.log('refreshDragData.template', template);
  if (!extraStageData.dragNumberAnswerJudge) {
    extraStageData.dragNumberAnswerJudge = 0;
  }
  if (extraStageData.dragNumberAnswerJudge === true) {
    extraStageData.dragNumberAnswerJudge = 1;
    extraStageData.dragSign = 0;
  }

  // 总数值判定升级 处理配置
  if (!template.extraConfig.find((item: any) => item.key === 'dragNumberAnswerJudge')) {
    const dragableObjectIndex = template.extraConfig.findIndex((item: any) => item.key === 'dragableObject');
    template.extraConfig.splice(dragableObjectIndex, 0, {
      "key": "dragNumberAnswerJudge",
      "label": "数值判定方式",
      "required": false,
      "type": "switchInput",
      "params": {
        "relative": {
          "key": "dragSum",
          "label": "",
          "type": "input",
          "params": {
            "min": 1,
            "max": 10000
          }
        }
      }
    });
  } else {
    const theConfig = template.extraConfig.find((item: any) => item.key === 'dragNumberAnswerJudge');
    theConfig.label = "数值判定方式";
  }
  // 总数值判定升级 处理业务属性的配置
  template.tags && template.tags.forEach((tag: any) => {
    if (tag.name === "dragableObject") {
      const dragNumberConfig = tag.editorConfig.find((config: any) => config.key === "dragNumber");
      if (!dragNumberConfig) {
        tag.editorConfig.push({
          "key": "dragNumber",
          "label": "数值",
          "type": "inputNumber",
          "description": "在开启「数值判定方式」后，可设置当前元素的数值。范围下限为0，上限为1000",
          "params": {
            "min": 0,
            "max": 1000
          }
        })
      }
    }
  })

  // 判断是否是新数据
  const isOld = !template.extraConfig.find((con: any) => con.key === "dragableObject");
  if (!isOld) return;
  const newExtraConfig = {
    key: "dragableObject",
    label: "拖拽分类",
    type: "custom",
    relatedComponentProperties: "type",
    required: false,
    params: {},
  };
  const tags = template.tags;
  tags.forEach((tag: any) => {
    if (["dragArea", "dragableObject"].includes(tag.name)) {
      tag?.editorConfig?.forEach((config: any) => {
        if (config.key === "type" && !config.paramsfromGlobal) {
          config.paramsfromGlobal = true;
        }
        if (!Object.keys(newExtraConfig.params).length) {
          newExtraConfig.params = JSON.parse(JSON.stringify(config.params));
        }
      });
      //
    }
  });
  template.extraConfig = [...template.extraConfig, newExtraConfig];

};


// 修改连线题
export const refreshLineData = (category: number, data: { components: { tag: string; childComponents?: any[] }[] }) => {
  if (category !== CATEGORY.LINE) return;
  data.components.forEach(cmpt => {
    if ((cmpt.tag == "dragArea" || cmpt.tag == "ligature") && cmpt.childComponents) {
      cmpt.childComponents.forEach(child => {
        child.cName = "linePoint";
      });
    }
  });

}
//修改填空题键盘

export const refreshBlankComponentsData = (category: number, components: any[]) => {
  if (category !== CATEGORY.BLANK) return;
  components.forEach(cmpt => {
    if (cmpt.type === "specialComponent" && cmpt.subType === "keyboard") {
      if (!cmpt.editable || (cmpt.editable && !cmpt.editable.properties)) {
        cmpt.editable = {
          properties: {
            x: false,
            y: false,
            width: false,
            height: false,
            angle: false,
            rotation: false,
            opacity: false,
            scaleX: false,
            scaleY: false,
            color: false,
          },
        };
      }
      cmpt.properties.width = 1015;
      cmpt.properties.height = 80;
      cmpt.properties.x = -50;
      cmpt.properties.y = -300;
    } else if (cmpt.tag === "blankModule") {
      if (typeof (cmpt.extra.correctArray) == "undefined" && typeof (cmpt.extra.correct) == "string") {
        cmpt.extra.correctArray = cmpt.extra.correct.split(",");
      }
    }
  });
};


export const refreshBlankData = (category: number, data: any) => {
  if (category !== CATEGORY.BLANK) return;
  refreshBlankComponentsData(category, data.components);
  let index = -1;
  for (let i = 0; i < data.template.extraConfig.length; i++) {
    if (data.template.extraConfig[i].key === "swKeyboard") {
      index = i;
    }
  }

  const newExtraConfig = {
    "key": "swKeyboard",
    "label": "切换键盘",
    "type": "keyboardSelect",
    "params": {
      "options": [
        {
          "label": "英文键盘",
          "value": "keyboardEnglish",
          "editorConfig": [
            {
              "key": "characterLimit",
              "label": "字符限制",
              "type": "select",
              "defaultValue": 1,
              "required": true,
              "params": {
                "min": 1,
                "max": 20,
                "step": 1
              }
            }
          ]
        },
        {
          "label": "数学键盘",
          "value": "keyboard",
          "editorConfig": [
            {
              "key": "characterLimit",
              "label": "字符限制",
              "type": "select",
              "defaultValue": 1,
              "required": true,
              "params": {
                "min": 1,
                "max": 10,
                "step": 1
              }
            }
          ]
        }
      ]
    }
  };
  if (index !== -1) {
    data.template.extraConfig[index] = newExtraConfig
  } else {
    data.template.extraConfig.push(newExtraConfig);
  }
  let isfindOneDragableObject = false;
  data.template.tags.forEach((tag: any) => {
    if (["blankModule"].includes(tag.name)) {
      tag?.editorConfig?.forEach((config: any) => {
        if (config.key === "characterLimit") {
          config["type"] = "selectFromStep";
          config["editorConfigfromGlobalSwKeyboard"] = true;
        }
        if (config.type === "blank") {
          config.key = "correctArray";
        }
        if (config.key === "fontSize") {
          config.default = 32;
        }
      });
    } else if (tag.name === "oneDragableObject") {
      isfindOneDragableObject = true;
    }
  });
  if (!isfindOneDragableObject) {
    data.template.tags.push({
      "name": "oneDragableObject",
      "label": "单个拖拽元素",
      "editorConfig": []
    })
  }

}

export const refreshSelectData = (category: number, data: any) => {
  const { template: { extraConfig }, extraStageData } = data;
  extraConfig.forEach((config: any) => {
    if (config.key === 'isAutoSubmit') {
      config.params.options = [
        {
          "label": "手动提交",
          "value": false
        },
        {
          "label": "自动提交",
          "value": true
        }
      ]
      if (category === CATEGORY.CHOICE) {
        if (!config.child || !config.child.length) {
          config.child = [
            {
              "fathLabel": true,
              "key": "autoSubmitMode",
              "label": "自动提交",
              "required": true,
              "type": "radio",
              "params": {
                "options": [
                  {
                    "label": "答对才提交",
                    "value": 0
                  },
                  {
                    "label": "选中即提交",
                    "value": 1
                  }
                ]
              }
            }
          ]
        }
        if (typeof extraStageData.autoSubmitMode === "undefined") {
          data.extraStageData.autoSubmitMode = 0;
        }
      }
    }
  });
}


export const refreshExtData = (data: any, extData: ExtData): ExtData => {
  // 当extData不存在时 将模版数据中的formConfig提取到extData
  console.log('refreshExtData-start');
  if (!extData || Array.isArray(extData)) {
    const { template } = data;
    const temp: ExtData = {
      formConfig: {},
    };
    if (template.formConfig) {
      temp['formConfig'] = template['formConfig'];
      template['formConfig'] = undefined;
    }
    return temp;
  } else {
    // 处理extData被多次JSON.stringify的问题 snapId=285260
    try {
      while (typeof extData === 'string') {
        extData = JSON.parse(extData);
      }
      // 删除extData中key为number的内容，因为反复转译外加slimExtData导致的错误数据 参考 snapId=663644
      for (const key in extData) {
        // console.log('extData...4-key', key, isNaN(key as any), extData[key]);
        if (!isNaN(key as any) && ((extData as any)[key].length === 1 || (extData as any)[key] === "\\")) {
          delete (extData as any)[key];
        }
      }
    } catch (error) {
      console.log('refreshExtData捕获错误', error);
    }
    if (!extData.formConfig) {
      extData.formConfig = {};
    }
    return extData;
  }
}

export const refreshPipeLineExtData = (category: number, extData: any, components: Components): void => {
  // 修复传送带的数据
  if (category !== CATEGORY.PIPELINE) return;

  const theComp = components.find((comp: Component) => comp.subType === 'pipeLine');
  const { stuOptionConfig, stuAnswerConfigs } = theComp.properties;
  const optionLen = stuOptionConfig.options.length;
  const answerLen = stuAnswerConfigs.length;
  const theFormConfig = extData.formConfig.pipeLine.find((formConfig: any) => formConfig.key === 'stuAnswerConfigs');
  if (theFormConfig.min === answerLen) return;
  console.log('need refresh');
  theFormConfig.orderConfig.args.optionLen = optionLen;
  theFormConfig.min = answerLen;
  theFormConfig.max = answerLen;
}

export const refreshMoleQuestionExtData = (category: number, extData: any, components: Components): void => {
  // 迭代打地鼠的数据
  if (category !== CATEGORY.MOLE) return;

  const theComp = components.find((comp: Component) => comp.subType === 'moleQuestion');
  const { stuQuestionList } = theComp.properties;
  if (!theComp.properties.stuMoleNormalScore) theComp.properties.stuMoleNormalScore = 0;
  if (!theComp.properties.stuMoleLightScore) theComp.properties.stuMoleLightScore = 0;
  if (!theComp.properties.stuMoleHelmetScore) theComp.properties.stuMoleHelmetScore = 0;
  stuQuestionList.forEach((question: any) => {
    question.stuOptionList.options.forEach((option: any) => {
      if (!option.isShowOptionContent) option.isShowOptionContent = 1;
    })
  })
  // 配置 积分设置-新增
  const scoreConfig = [
    {
      "formItemType": "BaseStaticText",
      "key": "stuMouseScoreTitle",
      "label": "积分设置",
      "offset": 2,
      "text": ""
    },
    {
      "formItemType": "BaseInputNumber",
      "key": "stuMoleNormalScore",
      "label": "普通鼠",
      "placeholder": "请设置普通鼠分数",
      "min": 0,
      "max": 9,
      "offset": 4,
      "step": 1,
      "span": 10,
      "value": 0,
      "rule": [
        {
          "required": false,
          "trigger": "blur",
          "message": "请设置普通鼠分数"
        }
      ]
    },
    {
      "formItemType": "BaseInputNumber",
      "key": "stuMoleLightScore",
      "label": "闪电鼠",
      "placeholder": "请设置闪电鼠分数",
      "min": 0,
      "max": 9,
      "value": 0,
      "offset": 4,
      "step": 1,
      "span": 10,
      "rule": [
        {
          "required": false,
          "trigger": "blur",
          "message": "请设置闪电鼠分数"
        }
      ]
    },
    {
      "formItemType": "BaseInputNumber",
      "key": "stuMoleHelmetScore",
      "label": "头盔鼠",
      "placeholder": "请设置头盔鼠分数",
      "min": 0,
      "max": 9,
      "value": 0,
      "offset": 4,
      "step": 1,
      "span": 10,
      "rule": [
        {
          "required": false,
          "trigger": "blur",
          "message": "请设置头盔鼠分数"
        }
      ]
    }
  ]
  // stuMoleHelmet
  const stuMoleHelmetIndex = extData.formConfig.moleQuestion.findIndex((formConfig: any) => formConfig.key === 'stuMoleHelmet');
  const stuMouseScoreTitleIndex = extData.formConfig.moleQuestion.findIndex((formConfig: any) => formConfig.key === 'stuMouseScoreTitle');
  if (stuMouseScoreTitleIndex === -1) {
    extData.formConfig.moleQuestion.splice(stuMoleHelmetIndex + 1, 0, ...scoreConfig);
  }
  // 配置 显示选项
  const theFormConfig = extData.formConfig.moleQuestion.find((formConfig: any) => formConfig.key === 'stuQuestionList');
  const theOptionConfig = theFormConfig.subFormConfigs.find((subSubFormConfig: any) => subSubFormConfig.key === 'stuOptionList');
  const mouseTypeIndex = theOptionConfig.optionConfigs.findIndex((optionConfig: any) => optionConfig.key === 'mouseType');
  const isShowOptionContentIndex = theOptionConfig.optionConfigs.findIndex((optionConfig: any) => optionConfig.key === 'isShowOptionContent');
  const inertConfig = {
    "formItemType": "BaseRadioGroup",
    "key": "isShowOptionContent",
    "label": "显示选项",
    "labelPosition": "top",
    "rule": [
      {
        "required": false,
        "trigger": "blur"
      }
    ],
    "value": 1,
    "options": [
      {
        "label": "是",
        "value": 1,
        "span": 7
      },
      {
        "label": "否",
        "value": 2,
        "offset": 10,
        "span": 7
      }
    ]
  };
  if (isShowOptionContentIndex === -1) {
    theOptionConfig.optionConfigs.splice(mouseTypeIndex + 1, 0, inertConfig);
  }
  // 头盔鼠新增动效 stuSpineMole
  const stuSpineMoleConfig = extData.formConfig.moleQuestion.find((formConfig: any) => formConfig.key === 'stuSpineMole');
  if (stuSpineMoleConfig.options.findIndex((option: any) => option.value === 'helmetIdleNoCard') === -1) {
    theComp.properties.stuSpineMole = {
      "atlas": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/laoshu_5b711bbcf0073848f5835e83edc3f7a0/laoshu.atlas",
      "images": [
        "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/laoshu_5b711bbcf0073848f5835e83edc3f7a0/laoshu.png"
      ],
      "skeleton": "https://yaya.cdnjtzy.com/cchd_dev/cchd_spine_dev/laoshu_5b711bbcf0073848f5835e83edc3f7a0/laoshu.json",
      "cover": "https://yaya.cdnjtzy.com/cchd_dev/tihu-67872c44a1c1a88d32e07ee9660fb2eb.png",
      "normalOut": "ZZS_chudong",
      "normalRaise": "ZZS_jupai",
      "normalIdle": "ZZS_jupaidaiji",
      "normalRight": "ZZS_zhengfankui",
      "normalWrong": "ZZS_fufankui",
      "normalBack": "ZZS_huidong",
      "normalIdleNoCard": "ZZS_daiji_bujupai",
      "normalRightNoCard": "ZZS_zhengfankui_bujupai",
      "normalWrongNoCard": "ZZS_fufankui_bujupai",
      "normalBackNoCard": "ZZS_huidong_bujupai",
      "flashOut": "SDS_chudong",
      "flashRaise": "SDS_jupai",
      "flashIdle": "SDS_jupaidaiji",
      "flashRight": "SDS_zhengfankui",
      "flashWrong": "SDS_fufankui",
      "flashBack": "SDS_huidong",
      "flashIdleNoCard": "SDS_daiji_bujupai",
      "flashRightNoCard": "SDS_zhengfankui_bujupai",
      "flashWrongNoCard": "SDS_fufankui_bujupai",
      "flashBackNoCard": "SDS_huidong_bujupai",
      "helmetOut": "TKS_chudong",
      "helmetRaise": "TKS_jupai",
      "helmetIdle": "TKS_daiji",
      "helmetIdle1": "TKS_daiji2",
      "helmetRight": "TKS_zhengfankui_1",
      "helmetRight1": "TKS_zhengfankui_2",
      "helmetWrong": "TKS_fufankui",
      "helmetBack": "TKS_huidong",
      "helmetBack1": "TKS_huidong_2",
      "helmetIdleNoCard": "TKS_daiji_bujupai",
      "helmetIdle1NoCard": "TKS_daiji2_bujupai",
      "helmetRightNoCard": "TKS_zhengfankui_1_bujupai",
      "helmetRight1NoCard": "TKS_zhengfankui_2_bujupai",
      "helmetWrongNoCard": "TKS_fufankui_bujupai",
      "helmetBackNoCard": "TKS_huidong_bujupai",
      "helmetBack1NoCard": "TKS_huidong_2_bujupai"
    }
    // 普通鼠动效
    const addSpineNormalIdleConfig = [
      {
        "label": "普通鼠不举牌待机",
        "value": "normalIdleNoCard"
      },
      {
        "label": "普通鼠不举牌正确",
        "value": "normalRightNoCard"
      },
      {
        "label": "普通鼠不举牌错误",
        "value": "normalWrongNoCard"
      },
      {
        "label": "普通鼠不举牌退回",
        "value": "normalBackNoCard"
      }
    ]
    const normalIdleIndex = stuSpineMoleConfig.options.findIndex((option: any) => option.value === 'normalBack');
    stuSpineMoleConfig.options.splice(normalIdleIndex + 1, 0, ...addSpineNormalIdleConfig);
    // 闪电鼠动效
    const addSpineLightIdleConfig = [
      {
        "label": "闪电鼠不举牌待机",
        "value": "flashIdleNoCard"
      },
      {
        "label": "闪电鼠不举牌正确",
        "value": "flashRightNoCard"
      },
      {
        "label": "闪电鼠不举牌错误",
        "value": "flashWrongNoCard"
      },
      {
        "label": "闪电鼠不举牌退回",
        "value": "flashBackNoCard"
      }
    ]
    const flashIdleIndex = stuSpineMoleConfig.options.findIndex((option: any) => option.value === 'flashBack');
    stuSpineMoleConfig.options.splice(flashIdleIndex + 1, 0, ...addSpineLightIdleConfig);
    // 头盔鼠动效
    const addSpineHelmetIdleConfig = [
      {
        "label": "头盔鼠不举牌待机",
        "value": "helmetIdleNoCard"
      },
      {
        "label": "头盔鼠不举牌待机(无头盔)",
        "value": "helmetIdle1NoCard"
      },
      {
        "label": "头盔鼠不举牌正确",
        "value": "helmetRightNoCard"
      },
      {
        "label": "头盔鼠不举牌正确(无头盔)",
        "value": "helmetRight1NoCard"
      },
      {
        "label": "头盔鼠不举牌错误",
        "value": "helmetWrongNoCard"
      },
      {
        "label": "头盔鼠不举牌退回",
        "value": "helmetBackNoCard"
      },
      {
        "label": "头盔鼠不举牌退回(无头盔)",
        "value": "helmetBack1NoCard"
      }
    ]
    stuSpineMoleConfig.options.push(...addSpineHelmetIdleConfig);
  }
  // 转场动效后新增小题反馈的配置 
  const stuSpineTranslateIndex = extData.formConfig.moleQuestion.findIndex((formConfig: any) => formConfig.key === 'stuSpineTranslate');
  const stuSpineRoundIndex = extData.formConfig.moleQuestion.findIndex((formConfig: any) => formConfig.key === 'stuSpineRound');
  if (stuSpineRoundIndex === -1) {
    const addTranslateConfig = [
      {
        "formItemType": "BaseSpineSelect",
        "key": "stuSpineRound",
        "label": "小题反馈",
        "value": {
          "atlas": "",
          "images": [],
          "skeleton": "",
          "cover": ""
        },
        "options": [
          {
            "label": "有积分反馈动画",
            "value": "roundAnimate"
          },
          {
            "label": "无积分反馈动画",
            "value": "roundAnimate1"
          },
          {
            "label": "无积分游戏结束",
            "value": "roundAnimate2"
          }
        ]
      },
      {
        "formItemType": "BaseAudioSelect",
        "key": "stuAudioRound",
        "label": "小题反馈音频",
        "value": ""
      }
    ]
    extData.formConfig.moleQuestion.splice(stuSpineTranslateIndex + 1, 0, ...addTranslateConfig);
  }
}

export const refreshDoubleRacingExtData = (category: number, extData: any, components: Components): void => {
  // layoutType
  // 迭代双人竞速的数据
  if (category !== CATEGORY.DOUBLERACING) return;

  const theComp = components.find((comp: Component) => comp.subType === 'doubleRacing');
  const { stuQuestions } = theComp.properties;
  stuQuestions.forEach((question: any) => {
    if (!question.layoutType) question.layoutType = '1';
  });
  const theFormConfig = extData.formConfig.doubleRacing.find((formConfig: any) => formConfig.key === 'stuQuestions');

  const questionAudioIndex = theFormConfig.subFormConfigs.findIndex((optionConfig: any) => optionConfig.key === 'questionAudio');
  const layoutTypeIndex = theFormConfig.subFormConfigs.findIndex((optionConfig: any) => optionConfig.key === 'layoutType');
  if (layoutTypeIndex === -1) {
    // 配置 新增选项布局
    const insertConfig = {
      "formItemType": "BaseRadioGroup",
      "key": "layoutType",
      "label": "选项布局",
      "value": "1",
      "paddingLeft": 15,
      "options": [
        {
          "label": "1*4",
          "value": "1",
          "span": 6
        },
        {
          "label": "2*2",
          "value": "2",
          "span": 6
        }
      ]
    }
    theFormConfig.subFormConfigs.splice(questionAudioIndex + 1, 0, insertConfig);
    extData.formConfig.doubleRacing.forEach((formConfig: any) => {
      if (formConfig.label === '机器人配置') {
        formConfig.key = 'textRobotConfig';
      }
    })
  }
}

export const refreshPrizeClawExtData = (category: number, extData: any, components: Components): void => {
  // layoutType
  // 迭代双人竞速的数据
  if (category !== CATEGORY.PRIZECLAW) return;

  const theComp = components.find((comp: Component) => comp.subType === 'prizeClaw');
  const { stuQuestions } = theComp.properties;
  stuQuestions.forEach((question: any) => {
    if (!question.stuOptionSkin) question.stuOptionSkin = [];
    if (!question.stuOtherNum) question.stuOtherNum = 0;
    if (!question.stuPawCount) question.stuPawCount = 0;
  });
  // 配置 新增选项布局
  const insertConfig = [
    {
      "formItemType": "BaseMultipleSelect",
      "key": "stuOptionSkin",
      "label": "选项娃娃皮肤",
      "value": [],
      "clearable": true,
      "optionsConfig": {
        "relativePropertiesKey": "stuOptionBodyList",
        "labelPrefix": "选项娃娃",
        "valueKey": "index"
      },
      "options": [],
      "rule": [
        {
          "required": true,
          "trigger": "change",
          "message": "选择选项娃娃皮肤"
        }
      ],
      "paddingLeft": 15
    },
    {
      "formItemType": "BaseInputNumber",
      "key": "stuOtherNum",
      "label": "干扰娃娃数量",
      "value": 0,
      "min": 0,
      "max": 6,
      "rule": [
        {
          "required": "true",
          "message": "请填写配置",
          "trigger": "blur"
        }
      ],
      "paddingLeft": 15
    },
    {
      "formItemType": "BaseInputNumber",
      "key": "stuPawCount",
      "label": "抓取次数",
      "value": 0,
      "min": 1,
      "max": 30,
      "rule": [
        {
          "required": "true",
          "message": "请填写配置",
          "trigger": "blur"
        }
      ],
      "paddingLeft": 15
    }
  ]
  const theFormConfig = extData.formConfig.prizeClaw.find((formConfig: any) => formConfig.key === 'stuQuestions');
  // 如果没有的话 push
  if (theFormConfig.subFormConfigs.findIndex((optionConfig: any) => optionConfig.key === 'stuOptionSkin') === -1) {
    theFormConfig.subFormConfigs.push(...insertConfig);
  }
  extData.formConfig.prizeClaw.forEach((formConfig: any) => {
    if (formConfig.key === 'stuIPSpine') {
      if (formConfig.hasDefaultOption) formConfig.hasDefaultOption = undefined;
    }
    if (formConfig.key === 'stuFankuiSpine') {
      if (formConfig.hasDefaultOption) formConfig.hasDefaultOption = undefined;
    }
  })
  const stuOptionSkinIndex = extData.formConfig.prizeClaw.findIndex((formConfig: any) => formConfig.key === 'stuOptionSkin');
  stuOptionSkinIndex > -1 && extData.formConfig.prizeClaw.splice(stuOptionSkinIndex, 1);
  const stuOtherNumIndex = extData.formConfig.prizeClaw.findIndex((formConfig: any) => formConfig.key === 'stuOtherNum');
  stuOtherNumIndex > -1 && extData.formConfig.prizeClaw.splice(stuOtherNumIndex, 1);
  const stuPawCountIndex = extData.formConfig.prizeClaw.findIndex((formConfig: any) => formConfig.key === 'stuPawCount');
  stuPawCountIndex > -1 && extData.formConfig.prizeClaw.splice(stuPawCountIndex, 1);
}

export const refreshDynamicOptionsExtData = (category: number, extData: any): void => {
  // 迭代选项运动的数据
  if (category !== CATEGORY.DYNAMICOPTIONS) return;
  extData.formConfig.dynamicOptions.forEach((item: {
    collapseName: string; formList: any[];
  }) => {
    item.formList.forEach((sItem) => {
      if (sItem.key === 'stuRoundList' && !sItem.subFormConfigs.find((ssItem: { key: string; }) => ssItem.key === 'stuReduceTime')) {
        sItem.subFormConfigs.push({
          "formItemType": "BaseInputNumber",
          "key": "stuReduceTime",
          "label": "答错扣时（秒）",
          "placeholder": "请输入答题扣时时间",
          "value": 0,
          "min": 0,
          "max": 100,
          "stepStrictly": true,
          "step": 1
        })
      }
    })
    if (item.collapseName === "业务属性" && item.formList.findIndex((formConfig: any) => formConfig.key === 'stuRoundAnimation') === -1) {
      // 配置 小题反馈-新增
      const insertConfig = [

        {
          "formItemType": "BaseSpineSelect",
          "key": "stuRoundAnimation",
          "label": "小题反馈",
          "value": {
            "atlas": "",
            "images": [],
            "skeleton": "",
            "cover": ""
          },
          "options": [
            {
              "label": "反馈动画",
              "value": "right"
            }
          ]
        },
        {
          "formItemType": "BaseAudioSelect",
          "key": "stuAudioRound",
          "label": "小题反馈音频",
          "value": ""
        }
      ]
      item.formList.push(...insertConfig);
    }
  })
}

export const refreshRobotQuestionExtData = (category: number, extData: any): void => {
  // 迭代人机pk的数据
  if (category !== CATEGORY.ROBOT) return;

  // 配置 小题反馈-新增
  const insertConfig = [
    {
      "formItemType": "BaseSpineSelect",
      "key": "stuQuestionResultAni",
      "label": "小题反馈",
      "rule": [
        {
          "required": false,
          "message": "请上传小题反馈动画",
          "trigger": "blur"
        }
      ],
      "options": [
        {
          "label": "正反馈",
          "value": "win"
        },
        {
          "label": "负反馈",
          "value": "fail"
        }
      ]
    },
    {
      "formItemType": "BaseAudioSelect",
      "key": "stuWinAudio",
      "label": "正反馈音效",
      "value": "",
      "rule": [
        {
          "trigger": "change",
          "message": "请上传正反馈音效"
        }
      ]
    },
    {
      "formItemType": "BaseAudioSelect",
      "key": "stuFailAudio",
      "label": "负反馈音效",
      "value": "",
      "rule": [
        {
          "trigger": "change",
          "message": "请上传负反馈音效"
        }
      ]
    }
  ]
  if (extData.formConfig.robotQuestion.findIndex((formConfig: any) => formConfig.key === 'stuQuestionResultAni') === -1) {
    extData.formConfig.robotQuestion.push(...insertConfig);
  }
}
export const refreshComputeChallengeExtData = (category: number, extData: any): void => {
  // 迭代人机pk的数据
  if (category !== CATEGORY.COMPUTECHALLENGE) return;

  // 配置 转场动画
  const insertConfig = [
    {
      "formItemType": "BaseSpineSelect",
      "key": "stuSceneChangeAni",
      "label": "转场动画",
      "rule": [
        {
          "required": false,
          "message": "请上传转场动画",
          "trigger": "blur"
        }
      ],
      "options": [
        {
          "label": "播放列表",
          "value": "standState"
        }
      ]
    },
    {
      "formItemType": "BaseAudioSelect",
      "key": "stuSceneChangeAudio",
      "label": "转场音效",
      "value": "",
      "rule": [
        {
          "trigger": "change",
          "message": "请上传转场音效"
        }
      ]
    }
  ]
  if (extData.formConfig.computeChallenge.findIndex((formConfig: any) => formConfig.key === 'stuSceneChangeAni') === -1) {
    extData.formConfig.computeChallenge.push(...insertConfig);
  }
}

export const refreshMonsterRestExtData = (category: number, extData: any): void => {
  // MonsterRestQuestion
  // 迭代怪兽餐厅的数据
  if (category !== CATEGORY.MONSTERREST) return;

  // 新增盘中碎屑
  const insertConfig = {
    "formItemType": "BaseImageSelect",
    "key": "stuSuiXieImg",
    "label": "盘中碎屑",
    "value": "",
    "rule": [
      {
        "trigger": "blur"
      }
    ]
  }
  extData.formConfig.monsterRest.forEach((item: any) => {
    item.formList.forEach((sItem: { key: string; }, sIndex: number) => {
      if (sItem.key === "stuPlateImg" && !item.formList.find((formConfig: any) => formConfig.key === 'stuSuiXieImg')) {
        item.formList.splice(sIndex + 1, 0, insertConfig)
      }
    })
  })
}

/**
 * 
 * @param category 
 * @param data 
 * @description 支持参考答案的补丁，老的配置中没有这个字段，需要补上，否则会导致参考答案不显示
 * @tips 该补丁只针对部分题型，具体参考whiteList。如果需要新增题型，需要在whiteList中添加题型的category
 * @returns 
 */
export const refreshReferenceAnswerConfig = (category: number, data: any): void => {
  const whiteList: CATEGORY[] = [CATEGORY.PROGRAM, CATEGORY.CUTPICTURE, CATEGORY.GEARASSEMBLY, CATEGORY.BALANCE, CATEGORY.COMPUTECHALLENGE];
  const { template } = data;
  if (template.supportSetReferenceAnswer) return;
  if (whiteList.includes(category)) {
    template.supportSetReferenceAnswer = true;
    template.referenceAnswer = {};
  }
}

/**
 * 
 * @description 编程操控升级-新增列数/X/Y等字段 
 * @param extData 
 * @param components 
 * @returns 
 */
export const refreshProgramData = (category: number, extData: any, components: Components): void => {
  // 更新编程操控数据
  if (category !== CATEGORY.PROGRAM) return;

  const theComp = components.find((comp: Component) => comp.subType === 'programQuestion');
  const { properties } = theComp;
  if (!properties.mainCmdPosX) {
    properties.mainCmdCols = 5;
    properties.mainCmdPosX = 740;
    properties.mainCmdPosY = 210;
    properties.loopCmdCols = 5;
    properties.loopCmdPosX = 740;
    properties.loopCmdPosY = 438;
    properties.ifCmdCols = 5;
    properties.ifCmdPosX = 740;
    properties.ifCmdPosY = 600;
    properties.cmdListPosX = 709;
    properties.cmdListPosY = 765;
    properties.showCmdPosX = 338;
    properties.showCmdPosY = 451;
  }
}

/**
 * 
 * @description 榫卯结构模版配置中遗漏了tag的配置-在这里进行一次清洗
 * @param extData 
 * @param components 
 * @returns 
 */
export const refreshMtConbnationData = (category: number, template: any): void => {
  // 更新编程操控数据
  if (category !== 1163) return;
  if (template.tags && !template.tags.length) {
    template.tags = [
      {
        "name": "hotBgImage",
        "label": "热区图背景",
        "selectable": false,
        "editorConfig": []
      },
      {
        "name": "hotImage",
        "label": "热区图",
        "selectable": false,
        "editorConfig": []
      }
    ]
  }
}

//反应力训练数据迭代
export const refreshReactivityTrainingExtData = (category: number, extData: any): void => {
  if (category !== CATEGORY.REACTIVITUTRAINING) return;
  extData.formConfig.reactivityTraining.find(
    (formConfig: any) => {
      if (formConfig.key === 'obstacleList') {
        formConfig.label = "障碍物配置";
        formConfig.labelTips = "方块72*72，竖长72*96，中长144*72，长方形216*72";
      }
      if (formConfig.key === 'stuGuideAnimation' || formConfig.key === 'stuGuideAudio')
        formConfig.rule = undefined;
      if (formConfig.key === "stuPlayerTexture") {
        formConfig.label = "拖拽物图片（图片推荐：130*131）";
        formConfig.labelPosition = "top";
      }
    });
}

// 迭代换算关系的数据
export const refreshConversionExtData = (category: number, extData: any): void => {
  if (category !== CATEGORY.CONVERSION) return;
  extData.formConfig.conversion[1].formList.find(
    (formConfig: any) => {
      if (formConfig.key === 'stuDifficulty') {
        formConfig.options = [
          {
            "label": "一级",
            "value": 3
          },
          {
            "label": "二级",
            "value": 4
          },
          {
            "label": "三级",
            "value": 0
          },
          {
            "label": "四级",
            "value": 1
          },
          {
            "label": "五级",
            "value": 2
          }
        ]
      }
    });
}

// 迭代路线规律的数据
export const refreshPathPuzzleExtData = (category: number, extData: any): void => {
  if (category !== CATEGORY.PATHPUZZLE) return;
  const questionConfig = {
    "formItemType": "BaseRadioGroup",
    "key": "stuQuestionType",
    "label": "题目类型",
    "value": "0",
    "rule": [
      {
        "required": true,
        "message": "请设置题目类型",
        "trigger": "change"
      }
    ],
    "options": [
      {
        "label": "线路题",
        "value": "0",
        "span": 6,
        "associatedForm": [
          {
            "formItemType": "BaseSwitch",
            "key": "stuIsFeedback",
            "label": "即时反馈开关",
            "value": false
          },
          {
            "formItemType": "BaseRadioGroup",
            "key": "stuTypographyType",
            "label": "排版类型",
            "value": "0",
            "rule": [
              {
                "required": true,
                "message": "请设置排版类型",
                "trigger": "change"
              }
            ],
            "options": [
              {
                "label": "4x4",
                "value": "0",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        }
                      ]
                    }
                  },
                  {
                    "targetKey": "stuQuestionConfigs",
                    "props": {
                      "value": {
                        "outerTables": [
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ]
                        ],
                        "innerTables": [
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ]
                        ]
                      }
                    }
                  }
                ]
              },
              {
                "label": "4x5",
                "value": "1",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  },
                  {
                    "targetKey": "stuQuestionConfigs",
                    "props": {
                      "value": {
                        "outerTables": [
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ]
                        ],
                        "innerTables": [
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            ""
                          ]
                        ]
                      }
                    }
                  }
                ]
              },
              {
                "label": "5x4",
                "value": "2",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  },
                  {
                    "targetKey": "stuQuestionConfigs",
                    "props": {
                      "value": {
                        "outerTables": [
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ]
                        ],
                        "innerTables": [
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ]
                        ]
                      }
                    }
                  }
                ]
              },
              {
                "label": "5x5",
                "value": "3",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        },
                        {
                          "label": "21",
                          "value": "21"
                        },
                        {
                          "label": "22",
                          "value": "22"
                        },
                        {
                          "label": "23",
                          "value": "23"
                        },
                        {
                          "label": "24",
                          "value": "24"
                        },
                        {
                          "label": "25",
                          "value": "25"
                        }
                      ]
                    }
                  },
                  {
                    "targetKey": "stuQuestionConfigs",
                    "props": {
                      "value": {
                        "outerTables": [
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ]
                        ],
                        "innerTables": [
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ],
                          [
                            "",
                            "",
                            "",
                            "",
                            ""
                          ]
                        ]
                      }
                    }
                  }
                ]
              }
            ]
          },
          {
            "formItemType": "BaseExtTable",
            "key": "stuQuestionConfigs",
            "label": "题板内容配置",
            "labelPosition": "top",
            "text": "点击生成",
            "actions": {
              "change": true
            },
            "value": {
              "outerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ],
              "innerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ]
            },
            "hasOutsideGrid": true,
            "cellConfigs": {
              "left": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "top": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "right": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "bottom": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "center": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuElementList",
                  "labelKey": "stuName",
                  "valueKey": "stuName",
                  "config": {
                    "placeholder": ""
                  }
                }
              }
            },
            "cellDefaultValue": "",
            "rowKey": "stuRow",
            "colKey": "stuCol",
            "rule": [
              {
                "required": true,
                "message": "请设置题板内容配置",
                "trigger": "blur"
              }
            ]
          }
        ]
      },
      {
        "label": "选择题",
        "value": "1",
        "span": 6,
        "associatedForm": [
          {
            "formItemType": "BaseSwitch",
            "key": "stuIsFeedback",
            "label": "即时反馈开关",
            "value": false
          },
          {
            "formItemType": "BaseRadioGroup",
            "key": "stuTypographyType",
            "label": "排版类型",
            "value": "0",
            "rule": [
              {
                "required": true,
                "message": "请设置排版类型",
                "trigger": "change"
              }
            ],
            "options": [
              {
                "label": "4x4",
                "value": "0",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "4x5",
                "value": "1",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "5x4",
                "value": "2",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "5x5",
                "value": "3",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        },
                        {
                          "label": "21",
                          "value": "21"
                        },
                        {
                          "label": "22",
                          "value": "22"
                        },
                        {
                          "label": "23",
                          "value": "23"
                        },
                        {
                          "label": "24",
                          "value": "24"
                        },
                        {
                          "label": "25",
                          "value": "25"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          },
          {
            "formItemType": "SpecialOptionConfig",
            "key": "stuOption",
            "label": "",
            "optionTextConfig": {
              "label": "选项类型",
              "listTitle": "选项列表"
            },
            "actions": {
              "change": true
            },
            "min": 2,
            "max": 5,
            "value": {
              "type": "text",
              "options": [
                {
                  "text": "",
                  "imgUrl": ""
                },
                {
                  "text": "",
                  "imgUrl": ""
                }
              ],
              "answer": ""
            },
            "answerConfig": {
              "multiple": true,
              "show": true
            },
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置选项类型"
              }
            ]
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuOptionImgUrl",
            "label": "选项框",
            "labelPosition": "top",
            "paddingLeft": 18,
            "value": ""
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuOptionSelectedImgUrl",
            "label": "选项框选中",
            "labelPosition": "top",
            "paddingLeft": 18,
            "value": ""
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuOptionWarningImgUrl",
            "label": "选项框闪红",
            "labelPosition": "top",
            "paddingLeft": 18,
            "value": ""
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuOptionCorrectImgUrl",
            "label": "选项框变绿",
            "labelPosition": "top",
            "paddingLeft": 18,
            "value": ""
          },
          {
            "formItemType": "BaseExtTable",
            "key": "stuQuestionConfigs",
            "label": "题板内容配置",
            "labelPosition": "top",
            "text": "点击生成",
            "actions": {
              "change": true
            },
            "value": {
              "outerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ],
              "innerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ]
            },
            "hasOutsideGrid": true,
            "cellConfigs": {
              "left": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "top": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "right": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "bottom": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "center": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuElementList",
                  "labelKey": "stuName",
                  "valueKey": "stuName",
                  "config": {
                    "placeholder": ""
                  }
                }
              }
            },
            "cellDefaultValue": "",
            "rowKey": "stuRow",
            "colKey": "stuCol",
            "rule": [
              {
                "required": true,
                "message": "请设置题板内容配置",
                "trigger": "blur"
              }
            ]
          }
        ]
      },
      {
        "label": "拖拽题",
        "value": "2",
        "span": 6,
        "changeProps": [
          {
            "targetKey": "stuDragOptions",
            "props": {
              "value": [
                {
                  "label": "选项A",
                  "value": "0"
                },
                {
                  "label": "选项B",
                  "value": "1"
                }
              ]
            }
          }
        ],
        "associatedForm": [
          {
            "formItemType": "BaseSwitch",
            "key": "stuIsFeedback",
            "label": "即时反馈开关",
            "value": false
          },
          {
            "formItemType": "BaseRadioGroup",
            "key": "stuTypographyType",
            "label": "排版类型",
            "value": "0",
            "rule": [
              {
                "required": true,
                "message": "请设置排版类型",
                "trigger": "change"
              }
            ],
            "options": [
              {
                "label": "4x4",
                "value": "0",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "4x5",
                "value": "1",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "5x4",
                "value": "2",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 4
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        }
                      ]
                    }
                  }
                ]
              },
              {
                "label": "5x5",
                "value": "3",
                "span": 5,
                "changeProps": [
                  {
                    "targetKey": "stuRow",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuCol",
                    "props": {
                      "value": 5
                    }
                  },
                  {
                    "targetKey": "stuAnswerOptions",
                    "props": {
                      "value": [
                        {
                          "label": "1",
                          "value": "1"
                        },
                        {
                          "label": "2",
                          "value": "2"
                        },
                        {
                          "label": "3",
                          "value": "3"
                        },
                        {
                          "label": "4",
                          "value": "4"
                        },
                        {
                          "label": "5",
                          "value": "5"
                        },
                        {
                          "label": "6",
                          "value": "6"
                        },
                        {
                          "label": "7",
                          "value": "7"
                        },
                        {
                          "label": "8",
                          "value": "8"
                        },
                        {
                          "label": "9",
                          "value": "9"
                        },
                        {
                          "label": "10",
                          "value": "10"
                        },
                        {
                          "label": "11",
                          "value": "11"
                        },
                        {
                          "label": "12",
                          "value": "12"
                        },
                        {
                          "label": "13",
                          "value": "13"
                        },
                        {
                          "label": "14",
                          "value": "14"
                        },
                        {
                          "label": "15",
                          "value": "15"
                        },
                        {
                          "label": "16",
                          "value": "16"
                        },
                        {
                          "label": "17",
                          "value": "17"
                        },
                        {
                          "label": "18",
                          "value": "18"
                        },
                        {
                          "label": "19",
                          "value": "19"
                        },
                        {
                          "label": "20",
                          "value": "20"
                        },
                        {
                          "label": "21",
                          "value": "21"
                        },
                        {
                          "label": "22",
                          "value": "22"
                        },
                        {
                          "label": "23",
                          "value": "23"
                        },
                        {
                          "label": "24",
                          "value": "24"
                        },
                        {
                          "label": "25",
                          "value": "25"
                        }
                      ]
                    }
                  }
                ]
              }
            ]
          },
          {
            "formItemType": "SpecialOptionConfig",
            "key": "stuOption",
            "label": "",
            "labelPosition": "top",
            "optionTextConfig": {
              "label": "",
              "listTitle": "拖拽物"
            },
            "min": 2,
            "max": 5,
            "value": {
              "type": "text",
              "options": [
                {
                  "text": "",
                  "imgUrl": ""
                },
                {
                  "text": "",
                  "imgUrl": ""
                }
              ],
              "answer": ""
            },
            "typeConfig": {
              "show": false,
              "type": "img"
            },
            "answerConfig": {
              "show": false
            },
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置选项类型"
              }
            ]
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuDragAreaImgUrl",
            "label": "拖拽区底框",
            "labelPosition": "top",
            "value": ""
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuAdsorptionZoneImgUrl",
            "label": "吸附区底图",
            "labelPosition": "top",
            "value": "",
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区底图"
              }
            ]
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuAdsorptionZoneGuidanceImgUrl",
            "label": "吸附区吸附引导",
            "labelPosition": "top",
            "value": "",
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区吸附引导"
              }
            ]
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuAdsorptionZoneWarningImgUrl",
            "label": "吸附区边缘闪红",
            "labelPosition": "top",
            "value": "",
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区边缘闪红"
              }
            ]
          },
          {
            "formItemType": "BaseImageSelect",
            "key": "stuAdsorptionZoneCorrectImgUrl",
            "label": "吸附区边缘变绿",
            "labelPosition": "top",
            "value": "",
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区边缘变绿"
              }
            ]
          },
          {
            "formItemType": "BaseExtTable",
            "key": "stuQuestionConfigs",
            "label": "题板内容配置",
            "labelPosition": "top",
            "text": "点击生成",
            "actions": {
              "change": true
            },
            "value": {
              "outerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ],
              "innerTables": [
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ],
                [
                  "",
                  "",
                  "",
                  ""
                ]
              ]
            },
            "hasOutsideGrid": true,
            "cellConfigs": {
              "left": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "top": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "right": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "bottom": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuOuterTableList",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              },
              "center": {
                "type": "select",
                "optionsConfig": {
                  "relativePropertiesKey": "stuDragQuestionConfigs",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              }
            },
            "cellDefaultValue": "",
            "rowKey": "stuRow",
            "colKey": "stuCol",
            "rule": [
              {
                "required": true,
                "message": "请设置题板内容配置",
                "trigger": "blur"
              }
            ]
          },
          {
            "formItemType": "SpecialDynamicList",
            "key": "stuAdsorptionZoneCorrectList",
            "label": "",
            "labelPosition": "top",
            "orderConfig": {
              "show": true,
              "decorate": "吸附区{{$}}正确选项"
            },
            "min": 1,
            "max": 1,
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区正确选项"
              }
            ],
            "listConfig": {
              "label": "",
              "labelTips": ""
            },
            "focusConfig": {
              "show": false
            },
            "value": [
              {
                "answer": ""
              }
            ],
            "subFormConfigs": [
              {
                "formItemType": "BaseSingleSelect",
                "key": "answer",
                "label": "",
                "value": "",
                "span": 24,
                "rule": [
                  {
                    "required": true,
                    "trigger": "blur",
                    "message": "请选择吸附区正确答案"
                  }
                ],
                "optionsConfig": {
                  "relativePropertiesKey": "stuDragOptions",
                  "labelKey": "label",
                  "valueKey": "value",
                  "config": {
                    "placeholder": ""
                  }
                }
              }
            ]
          },
          {
            "formItemType": "BaseInputNumber",
            "key": "stuDragNum",
            "label": "吸附区数量",
            "value": 1,
            "span": 10,
            "actions": {
              "change": true
            },
            "rule": [
              {
                "required": true,
                "trigger": "blur",
                "message": "请设置吸附区数量"
              }
            ]
          }
        ]
      }
    ]
  };
  const stuSelectAudio = {
    "formItemType": "BaseAudioSelect",
    "key": "stuSelectAudio",
    "label": "选择提示音效",
    "value": ""
  }
  const stuEmptyAudio = {
    "formItemType": "BaseAudioSelect",
    "key": "stuEmptyAudio",
    "label": "空缺提示音效",
    "value": ""
  }
  const stuOptionSelectedAudio = {
    "formItemType": "BaseAudioSelect",
    "key": "stuOptionSelectedAudio",
    "label": "选项选中音效",
    "value": ""
  }
  const stuDragClickAudio = {
    "formItemType": "BaseAudioSelect",
    "key": "stuDragClickAudio",
    "label": "拖拽物按下音效",
    "value": ""
  }
  const stuDragAdsorbentAudio = {
    "formItemType": "BaseAudioSelect",
    "key": "stuDragAdsorbentAudio",
    "label": "拖拽物吸附音效",
    "value": ""
  }
  extData.formConfig.pathPuzzle.forEach((item: {
    collapseName: string; formList: any[];
  }) => {
    const stuIsFeedbackIndex = item.formList.findIndex((formConfig: any) => formConfig.key === 'stuIsFeedback');
    stuIsFeedbackIndex > -1 && item.formList.splice(stuIsFeedbackIndex, 1);
    const stuTypographyTypeIndex = item.formList.findIndex((formConfig: any) => formConfig.key === 'stuTypographyType');
    stuIsFeedbackIndex > -1 && item.formList.splice(stuTypographyTypeIndex, 1);
    const stuQuestionConfigsIndex = item.formList.findIndex((formConfig: any) => formConfig.key === 'stuQuestionConfigs');
    stuIsFeedbackIndex > -1 && item.formList.splice(stuQuestionConfigsIndex, 1);
    if (item.collapseName === "题目属性" && item.formList.findIndex((formConfig: any) => formConfig.key === 'stuQuestionType') === -1)
      item.formList.unshift(questionConfig);
    if (item.collapseName === "业务属性") {
      if (item.formList.findIndex((formConfig: any) => formConfig.key === 'stuSelectAudio') === -1)
        item.formList.push(stuSelectAudio);
      if (item.formList.findIndex((formConfig: any) => formConfig.key === 'stuEmptyAudio') === -1)
        item.formList.push(stuEmptyAudio);
      if (item.formList.findIndex((formConfig: any) => formConfig.key === 'stuOptionSelectedAudio') === -1)
        item.formList.push(stuOptionSelectedAudio);
      if (item.formList.findIndex((formConfig: any) => formConfig.key === 'stuDragClickAudio') === -1)
        item.formList.push(stuDragClickAudio);
      if (item.formList.findIndex((formConfig: any) => formConfig.key === 'stuDragAdsorbentAudio') === -1)
        item.formList.push(stuDragAdsorbentAudio);
    }
  });
}

export const refreshSudokuExtData = ((category: number, extData: any) => {
  if (category !== CATEGORY.SUDOKU) return;
  extData.formConfig.sudoku[0].formList.find(
    (formConfig: any) => {
      if (formConfig.key === 'stuHelp2') {
        formConfig.labelTips = "点击任意格子，该格子所在行、列、宫/区域、对角线上格子均会高亮，再次点击取消高亮";
      }
    });
})


export const initTags = (category: number, template: any): void => {
  const tags = getComponentTagConfig(category);
  if (tags.length > 0) {
    template.tags = tags;
  }
}

export const refreshLabelComp = ((components: Components) => {
  components.forEach((comp: Component) => {
    const { type, properties } = comp;
    if (type === 'label' && properties.texture) {
      properties.texture = undefined;
    }
    if (type === 'group' && comp.subComponents) {
      refreshLabelComp(comp.subComponents)
    }
  });
})


// formTemplateData
const refreshFormTemplateData = ((category: CATEGORY, questionData: any, extData: any) => {
  // if (!questionData.formTemplateData) {
  //   // 初始化 如果没有 formTemplateData 则进行初始化
  //   questionData.formTemplateData = genFormTemplateData(category, questionData);
  // } else {
  //   // 处理数据
  //   const {
  //     formTemplateData: { components },
  //   } = questionData;
  //   components.forEach((comp: any) => {
  //     if (typeof comp.qeustionEN === "string") {
  //       comp.qeustionEN = [
  //         {
  //           value: comp.qeustionEN,
  //           error: "",
  //         },
  //       ];
  //     }
  //   });
  // }
  const formTemplateData = extData.formTemplateData || questionData.formTemplateData || genFormTemplateData(category, questionData);
  extData.formTemplateData = formTemplateData;
  questionData.formTemplateData = undefined; // 删除questionData中的formTemplateData
})
const refreshMathLinkUpExteData = ((category: CATEGORY, extraStageData: any) => {
  if (category !== CATEGORY.MATHLINKUP) return;
  extraStageData.hasRecover = true;
});


export const refreshQuestionData = (category: CATEGORY, extData: any, questionData: any) => {
  console.time("refreshData");
  // 获取tag
  initTags(category, questionData.template);
  // 数据结构统一添加标签
  insertSignalId(category, questionData.components);
  // 解析
  initAnalysisData(category, questionData.extraStageData);
  // 说明
  initIntroductionData(questionData.extraStageData);
  // 洗拖拽题数据
  refreshDragData(category, questionData);
  // 洗填空题数据
  refreshBlankData(category, questionData);
  // 洗连线题数据
  refreshLineData(category, questionData);
  // 洗是否显示参考答案的配置数据
  refreshReferenceAnswerConfig(category, questionData);
  // 洗选择题的模版数据（2023年4-5月新增自动判定的模式选择）
  refreshSelectData(category, questionData);
  console.timeEnd("refreshData");
  // 洗传送带bug数据（表单配置没有跟随题目更新）
  refreshPipeLineExtData(category, extData, questionData.components);
  // 打地鼠升级
  refreshMoleQuestionExtData(category, extData, questionData.components);

  // 选项动态题板升级
  refreshDynamicOptionsExtData(category, extData);

  // 人机pk
  refreshRobotQuestionExtData(category, extData);

  // 计算挑战
  refreshComputeChallengeExtData(category, extData);

  // 怪兽餐厅
  refreshMonsterRestExtData(category, extData);

  // 双人PK
  refreshDoubleRacingExtData(category, extData, questionData.components);

  // 欢乐抓娃娃
  refreshPrizeClawExtData(category, extData, questionData.components);

  refreshProgramData(category, extData, questionData.components);
  refreshMtConbnationData(category, questionData.template);

  //反应力
  refreshReactivityTrainingExtData(category, extData);

  //换算关系
  refreshConversionExtData(category, extData);

  // 有序记忆
  refreshSeqMemoryExtData(category, extData, questionData.components);

  //路线规律
  refreshPathPuzzleExtData(category, extData);

  // 文本组件中 老的截图及字段进行处理
  refreshLabelComp(questionData.components);

  refreshFormTemplateData(category, questionData, extData);
  //算式连连看升级
  refreshMathLinkUpExteData(category, questionData.extraStageData)
  //数独
  refreshSudokuExtData(category, extData);
}
