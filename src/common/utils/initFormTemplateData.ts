import { CATEGORY } from "@/common/constants";

import { enPKQuestion } from "@/components/form-question/config/enPKQuestion";

// 在schema数据中新增两个字段
// template.hasFormData true 需要初始化题目表单
// 组件中，新增formDataType 和subType或type的值保持一致 根据该字段来初始化组件的数据 form配置和数据结构

enum FORMDATATYPE {
  ENPK = 'enPK'
}

// 题目数据 // 组件数据
export const initENPKQuestionData = () => {
  console.log('initENPKFormData-enPKQuestion', enPKQuestion);
  const { tabs } = enPKQuestion;
  return tabs.find(item => item.key === 'question')?.form;
}

export const initENPKComponentData = () => {
  console.log('initENPKFormData-enPKQuestion', enPKQuestion);
  // 找到含有key = properties的tab 返回form
  const { tabs } = enPKQuestion;
  return tabs.find(item => item.key === 'properties')?.form;
}

const questionFunctionMap = {
  [CATEGORY.ENPK]: initENPKQuestionData,
}

const componentFunctionMap = {
  [FORMDATATYPE.ENPK]: initENPKComponentData,
}

export const initQuestionData = (category: CATEGORY) => {
  console.log('initENPKFormData-enPKQuestion', enPKQuestion);
  return questionFunctionMap[category] && questionFunctionMap[category]();
}

export const initComponentsData = (components: Component[]) => {
  console.log('initENPKFormData-enPKQuestion', enPKQuestion);
  return components.filter((comp) => {
    return componentFunctionMap[comp.formDataType]
  }).map((comp) => {
    return componentFunctionMap[comp.formDataType]();
  })
}

export const genComponentData = (formDataType: FORMDATATYPE) => {
  console.log('genComponentData-enPKQuestion', formDataType);
  return componentFunctionMap[formDataType] && componentFunctionMap[formDataType]();
}


export const initFormTemplateData = (category: CATEGORY, data: any): FormTemplateData => {
  const { template: { hasFormData }, components } = data;
  console.log('initENPKFormData-category', category);
  console.log('initENPKFormData-data', data);
  console.log('initENPKFormData-res', {
    question: hasFormData ? initQuestionData(category) : undefined,
    components: initComponentsData(components)
  });
  return {
    question: hasFormData ? initQuestionData(category) : undefined,
    components: initComponentsData(components),
  }
}