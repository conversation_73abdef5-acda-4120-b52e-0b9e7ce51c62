/**
 * 从clipboard获取图片和文本内容
 */

export interface ClipboardContent {
  // 文本
  texts: string[];
  // 图片
  pictures: File[];
  // 剪贴板是否有内容
  hasContent: boolean;
}

export default async (e: ClipboardEvent) => {
  return new Promise<ClipboardContent>(resolve => {
    const data: ClipboardContent = {
      texts: [],
      pictures: [],
      hasContent: false,
    };

    const clipboardData: any = e.clipboardData;

    if (!clipboardData) {
      resolve(data);
    }

    const { items = [] } = clipboardData;
    const len = items.length;
    const textPromise: Array<Promise<any>> = [];

    data.hasContent = !!len;

    for (let i = 0; i < len; i++) {
      const item = items[i];
      // 纯文本
      if (item.kind === "string" && item.type === "text/plain") {
        textPromise.push(
          new Promise(resolve => {
            item.getAsString((text: any) => {
              data.texts.push(text);
              resolve(null);
            });
          }),
        );
        // 图片
      } else if (item.kind === "file" && item.type.match(/^image\//i)) {
        data.pictures.push(item.getAsFile()!);
      }
    }

    Promise.all(textPromise).then(() => {
      resolve(data);
    });
  });
};
