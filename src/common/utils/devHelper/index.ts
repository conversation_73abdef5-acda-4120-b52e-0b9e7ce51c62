export const getShipName = function (str?: string) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  if ((window as any).shipName) return (window as any).shipName;
  const reg = /^(jiaoxue-tihu|qu)-([A-Za-z0-9]{3,16})(-*)/;
  let temp = str;
  if (!temp && localStorage.getItem('myHost')) {
    temp = String(localStorage.getItem('myHost')).slice(8)
  }
  const res = reg.exec(temp ?? location.host);
  let shipName = "";
  if (res && res[2]) {
    shipName = res[2];
  }
  return shipName;
}

export const isOnline = window.location.href.includes("zuoyebang") || String(localStorage.getItem('myHost')).includes('zuoyebang');

const isDev = process.env.NODE_ENV === 'development';

// ts声明一个常量
export const LINEHOST = 'https://jiaoxue-tihu.zuoyebang.cc/';

export const EDITORSERVERHOST = isOnline ? LINEHOST : `//jiaoxue-tihu-${getShipName()}-cc.suanshubang.cc/`;

export const REQUESTBASEURLPHP = isDev ? '' : isOnline ? `//kejian.zuoyebang.cc/` : `//qu-${getShipName()}-cc.suanshubang.cc`;
// eslint-disable-next-line @typescript-eslint/no-use-before-define
export const REQUESTBASEURLPHPPRE = isDev ? '' : isOnline ? `//qu.zuoyebang.cc/` : `//qu-${getShipName()}-cc.suanshubang.cc`;

class DevHelper {
  mockOnline = false;
  onlineHost = LINEHOST;
  shipHost = 'https://jiaoxue-tihu-base-cc.suanshubang.cc/';
  desc = {
    name: "编辑器页面小工具",
    setMyHost: '设置myHost，参数为env，可选值为online、test、dev',
    setMockOnline: '设置mockOnline为true，即可使用线上环境的数据',
    allowDeleteSpine: '打开删除spine的按钮',
    closeDeleteSpine: '关闭删除spine的功能',
    clearTagsData: '清除tagsData的缓存数据',
    setSpineMax: '设置题目允许的动效资源的最大数量',
    setAudioMax: '设置题目允许的音频资源的最大数量',
    setMockCreateError: '模拟服务端返回错误数据',
    clearMockCreateError: '取消模拟服务端返回错误数据',
    mockCocosInitFinished: '模拟cocos渲染完成', // 题板不ready时，调试表单用
    delayLoadedMaterialLibrary: "延迟加载素材库，参数为时间，单位为毫秒或者不传参数（不延迟）",
  }
  spineMax: number | undefined;
  audioMax: number | undefined;
  constructor() {
    localStorage.setItem("pageTools", "编辑器工具箱, 在控制台输入pageTools即可使用");
    this.isDev = process.env.NODE_ENV === 'development';
  }

  isDev: boolean;

  log(...params: any) {
    const style = "background: #e6faef;border: 1px solid #e6faef;color: #42c57a;font-size: 16px;margin: 3px;padding: 3px;"
    console.log('%cFE-Editor', style, ...params);
  }

  getHost(env: string) {
    this.log(this.desc['getHost']);
    if (env === 'online') {
      return this.onlineHost;
    } else if (env) {
      return `https://jiaoxue-tihu-${env}-cc.suanshubang.cc/`;
    }
    if (location.host.includes('suanshubang')) {
      return location.host;
    }
    return this.shipHost;
  }

  setMyHost(env: string) {
    this.log(this.desc['setMyHost']);
    localStorage.setItem('myHost', this.getHost(env));
  }

  setMockOnline() {
    this.log(this.desc['setMockOnline']);
    this.setMyHost('online');
  }

  allowDeleteSpine() {
    this.log(this.desc['allowDeleteSpine']);
    localStorage.setItem('allowDeleteSpine', '1');
  }

  closeDeleteSpine() {
    this.log(this.desc['allowDeleteSpine']);
    localStorage.removeItem('allowDeleteSpine');
  }

  clearTagsData() {
    this.log(this.desc['clearTagsData']);
    localStorage.removeItem('tagsData');
  }

  setSpineMax(num: number) {
    this.log(this.desc['setSpineMax']);
    this.spineMax = num;
  }

  setAudioMax(num: number) {
    this.log(this.desc['setAudioMax']);
    this.audioMax = num;
  }

  setMockCreateError() {
    this.log(this.desc['setMockCreateError']);
    localStorage.setItem('setMockCreateError', '1');
  }
  clearMockCreateError() {
    this.log(this.desc['clearMockCreateError']);
    localStorage.removeItem('setMockCreateError');
  }
  delayLoadedMaterialLibrary(num: string) {
    this.log((window as any).pageTools.desc.delayLoadedMaterialLibrary);
    localStorage.setItem("delayLoadedMaterialLibrary", num);
  }
}

const pageTools = new DevHelper();
(window as MyWindow).pageTools = pageTools;

export default pageTools;
