import { CATEGORY } from "@/common/constants";

/**
 * @description 是否渲染编辑题目的按钮
 * @param {number} category
 * @returns {Boolean}
 */

export const isRenderFormEdit = (category: number) => {
  return [CATEGORY.LISTENANSWER, CATEGORY.LISTENRETELL, CATEGORY.LOUDREADING].includes(category)
}

export const isRenderFormEditButton = (category: number) => {
  // 听后回答 听后转述  朗读短文 跟读单词 小英pk 情景对话
  return [CATEGORY.LISTENANSWER, CATEGORY.LISTENRETELL, CATEGORY.LOUDREADING, CATEGORY.FOLLOWWORDS, CATEGORY.ENPK, CATEGORY.ENGROUPPK, CATEGORY.CONTEXTUALANSWERQUESTION].includes(category)
}
