import { parse } from "query-string";
import { nativeRequestServer } from "./index";
import { showErrorMessage } from "@/common/utils/showErrorMessage";

export const fetchQuestionData = async () => {
   // 根据id查找题板数据
    const { id } = parse(window.location.search);

    if (!id) {
      // 如果没有id，从template.json中获取模版数据
      try {
        const resTemp = await nativeRequestServer.get(`/template.json`);
        console.log('resTemp', resTemp);
        const { data } = resTemp;
        const tempData = {
          content: JSON.stringify(data),
          name: data.template.name,
          tempId: 0,
          parentVersion: 1
        }
        return tempData;
      } catch (error) {
        console.log('error');
        showErrorMessage(new Error('请在编辑器程序包中的template.json文件添加模版数据'))
      }
    } else {
      try {
        const res = await nativeRequestServer.get(`/editorApi/getSubjectDetails?id=${id}`);
        const { data: { data } } = res;
        const tempData = JSON.parse(data)
        return tempData;
      } catch (error) {
        showErrorMessage(new Error('id不存在，请删除地址栏中的id参数后重试'))
      }
    }
}