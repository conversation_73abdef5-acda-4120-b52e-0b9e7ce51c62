import axios from "axios";
import { stringify } from "query-string";

export const nativeRequestServer = axios.create({
  withCredentials: true,
  baseURL: '',
  headers: {
    "Content-Type": "application/x-www-form-urlencoded",
  },
});

nativeRequestServer.interceptors.request.use(
  config => {
    if (config.method === "post" && config.headers["content-type"] !== "multipart/form-data") {
      config.data = stringify(config.data);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

nativeRequestServer.interceptors.response.use(
  res => {
    if (res.data.errNo) {
      throw res;
    }
    return res;
  },
  (error: Error) => {
    console.error(error);
    throw error;
  },
);

export const uploadFile = (formData: FormData): Promise<string> => {
  return new Promise((resolve, reject) => {
    nativeRequestServer.post('/editorApi/uploadImg', formData, {
      headers: {
        "content-type": "multipart/form-data",
      },
    }).then((res) => {
      console.log('res...', res);
      if (res && res['data'] && res.data.path) {
        const url = `${origin}${res.data.path}`;
        resolve(url);
      } else {
        reject(res)
      }
    }).catch((error) => {
      reject(error)
    })
  })
}