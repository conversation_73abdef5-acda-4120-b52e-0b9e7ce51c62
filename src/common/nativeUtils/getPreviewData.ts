import { parse } from "query-string";
import { nativeRequestServer } from "./request";

export const getNativePreviewData = () => {
  return new Promise((resolve, reject) => {
    try {
      const { previewId } = parse(window.location.search);
      nativeRequestServer.get(`/editorApi/getSubjectDetails?id=${previewId}`).then(({ data: { data } }) => {
        resolve(JSON.parse(data));
      })
    } catch (error) {
      reject(error);
    }
  });
}