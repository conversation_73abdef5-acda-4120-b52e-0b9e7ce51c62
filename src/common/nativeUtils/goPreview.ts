import { stringify } from "query-string";
import { nativeRequestServer } from "./request";

export const nativeGoPreview = (questionData: { name: any; content: string; }) => {
  return new Promise((resolve, reject) => {
    console.log('questionData', questionData);
    try {
      nativeRequestServer.post('/editorApi/updateAndCreatedSubject', {
        data: JSON.stringify(questionData),
        name: questionData.name,
      })
      .then(({ data }) => {
        console.log('data...res', data);
        const {
         id
        } = data;
        const bundleName = JSON.parse(questionData.content).template.bundleName;
        const bundlePath = `${origin}/bundle`
        const previewUrl = `./preview.html?${stringify({
          previewId: id,
          bundleName: bundleName,
          bundlePath
        })}`;
        window.open(previewUrl);
        resolve(data);
      })
      .catch(reject);
    } catch (error) {
      reject(error);
    }
  });
}