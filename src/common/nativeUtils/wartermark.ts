/*水印配置*/
//水印配置 
const setWatermark = (str: string) => {
      const id = '1.23452384164.123412415'
    // 移除水印 判断
    if (document.getElementById(id) !== null) {
          document.body.removeChild((document.getElementById(id) as HTMLElement))
    }

    //创建画布
    const createCanvas = document.createElement('canvas')
    // 设置canvas画布大小
    createCanvas.width = 200 //宽度
    createCanvas.height = 200 //高度

    //创建Context2D对象作为2D渲染的上下文。
    const Context2D = createCanvas.getContext('2d')
    if (Context2D) {
      /*水印样式调整配置*/
      Context2D.rotate(-20 * Math.PI / 100) // 水印旋转角度
      Context2D.font = '14px Vedana' //水印文字大小
      Context2D.fillStyle = 'rgba(0, 0, 0, 1)'; //水印颜色 HEX格式,可使用red 或者rgb格式
      Context2D.textAlign = 'center' //水印水平位置
      Context2D.textBaseline = 'middle' //水印垂直位置
      Context2D.fillText(str, 0, 120)
    }
    //创建元素
    const createDiv = document.createElement('div')
    createDiv.id = id
    //水印默认设置
    createDiv.style.pointerEvents = 'none' //水印层事件穿透 让水印不阻止交互事件
    createDiv.style.top = '70px' //水印顶部距离
    createDiv.style.left = '0px' //水印左侧距离
    createDiv.style.opacity = '0.1' //水印透明度
    createDiv.style.position = 'fixed' //水印定位
    createDiv.style.zIndex = '100000' //水印样式优先显示
    createDiv.style.width = document.documentElement.clientWidth - 100 + 'px' //水印宽度
    createDiv.style.height = document.documentElement.clientHeight - 100 + 'px' //水印高度
    createDiv.style.background = 'url(' + createCanvas.toDataURL('image/png') + ') left top repeat' //水印显示(关键代码)
    document.body.appendChild(createDiv) //在指定元素节点的最后一个子节点之后添加节点
    return id
}

//声明 
const watermark = {
  set: (str: string) => {
    const time = new Date();
    const timeStr = `${time.getFullYear()}/${(time.getMonth() + 1)}/${time.getDate()}`
    const text = `${str}${timeStr}`
    let id = setWatermark(text)
    //设置间隔
    setInterval(() => {
      if (document.getElementById(id) === null) {
        id = setWatermark(text)
      }
    }, 500)
    //改变大小时执行
    window.onresize = () => {
      setWatermark(text)
    }
  }
}

export default watermark
