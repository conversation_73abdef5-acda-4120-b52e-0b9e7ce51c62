import { isDevelopmentEnv } from "@/common/utils/env";
import store from "@/pages/index/store";

const getCocosPath = () => {
  if (isDevelopmentEnv) return location.origin;
  return `${location.origin}/interactive-question-editor`;
}

export const getAnswerPreviewUrl = () => {
  const bundlePath = `${origin}/bundle`;
  const bundleName = store.state.template.bundleName;
  if (isDevelopmentEnv) {
    return `${getCocosPath()}/preview.html?answer=1&mode=mobile&bundlePath=${bundlePath}&bundleName=${bundleName}`;
  } else {
    return `/interactive-question-editor/preview.html?answer=1&mode=mobile&bundlePath=${bundlePath}&bundleName=${bundleName}`;
  }
}