/*
 * @Date: 2021-09-18 13:11:50
 * @LastEditors: chxu
 * @LastEditTime: 2021-09-18 13:30:12
 * @FilePath: /interactive-question-editor/src/common/constants/specialComponetSubTypes.ts
 * @Author: chxu
 */
export enum SpecialComponentSubTypes {
  VOICE = "voice", // 音频话筒组件
  AUDIO_HISTORY_ANSWER = "audioHistoryAnswer", // 音频历史作答组件
  GRAPHICS = "graphics",
  KEYBOARD = "keyboard",
  MATCHBOARD = "matchboard",
  COUNTER = "counter",
  CLOCK = "clock",
  SPEAKER = "speaker",    // 音频组件
  KEYBOARD_ENGLISH = "keyboardEnglish",
  BRUSH = "brush",      // 画笔组件
  READCOM = "readcom",
  VOICESPEAK = "voiceSpeak", // 看图说话组件
  LISTENRETELL = "listenRetell",
  LISTENANSWER = "listenAnswer",
  LOUDREADING = "readPage", // 朗读短文
  MICROP = "microp",
  FOLLOWWORDS = "followWords", // 跟读单词
  H5LABEl = "h5Label", // 富文本
  ENPK = "enPK", // 小英pk
  ENGROUPPK = "enGroupPK" // 小英全员pk
}
