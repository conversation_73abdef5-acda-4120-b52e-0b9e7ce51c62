/*
 * @Date: 2021-11-30 09:31:30
 * @LastEditors: gedengyang_v <EMAIL>
 * @LastEditTime: 2025-01-10 15:58:47
 * @FilePath: /interactive-question-editor/src/common/constants/category.ts
 * @Author: chxu
 */
export enum CATEGORY {
  DRAG = 1011,
  CHOICE = 1010,
  BLANK = 1012,
  LINE = 1013,
  QTE = 2001,
  MATCHPATTERN = 1019,
  FOLLOWRECORD = 1020,
  COUNT = 1021,
  READ = 1022,
  CLOCK = 1023,
  WRITE = 1024,
  TANGRAM = 1025,
  GROUP = 1027, // 题组
  READCOMQUESTION = 1100, // 阅读理解
  VIDEOGROUP = 1102, // 听后选择
  LISTENANSWER = 1104, // 听后回答
  LISTENRETELL = 1103, // 听后转述
  LOUDREADING = 1105, // 朗读短文
  FOLLOWWORDSGROUP = 1107, // 初高跟读单词
  FOLLOWWORDS = 1106, // 跟读单词小题
  ENPK = 1109, // 小英pk
  KLOTSKI = 1110, // 华容道
  CUTPICTURE = 1111, // 切割题
  PROGRAM = 1112, // 编程题
  HANOITOWER = 1113, // 汉诺塔
  FISH = 1114, // 捕鱼
  GOLDENMINER = 1115, // 黄金矿工
  RACINGCAR = 1116, // 赛车连续题板 
  GEARASSEMBLY = 1117, // 齿轮题
  ZUMA = 1118, // 弹珠游戏 
  PIPELINE = 1119, // 传送带组合
  IMWINNER = 1120, // 我是大赢家
  ROBOT = 1121, // 人机PK	robot 
  POURWATER = 1122, // 倒油取水
  COMPUTECHALLENGE = 1124, // 计算挑战
  BALANCE = 1129, // 平衡大师
  // MOLE = 1125, // 标枪 未上线 未确定bundlename
  MOLE = 1126, // 打地鼠
  CONVERSION = 1127, //	换算关系 
  PWTABLE = 1128, //	密码盘
  MONSTERREST = 1130, //	怪兽餐厅
  DOUBLERACING = 1132, // 双人PK竞速
  MEMORY = 1133, // 过目不忘 
  CLUES = 1134, // 见微知著
  DYNAMICOPTIONS = 1136, // 运动选项题板
  SERIALJUMP = 1137, // 连续选择过山崖题
  FORESTADVENTURE = 1139, // 森林冒险
  JAVELIN = 1125, // 标枪
  MIRACLEGEM = 1140, // 奇迹宝石
  PRIZECLAW = 1142, // 抓娃娃
  REALFAKEHOSTAGE = 1141, // 真假人质
  ROLLERCOASTER = 1138, // 小火车连续题板
  AIRCONTROLLER = 1148, // 飞机操控
  SEQMEMORY = 1151, // 飞机操控
  REACTIVITUTRAINING = 1152, // 反应力训练
  KNOCKICE = 1155, //小鹿练字
  XIAOLUPRACTICE = 1159, //小鹿练字
  MULTIPLEEMPTY = 1164, // 多空连答
  CONTEXTUALANSWERQUESTION = 1167, //  情景回答
  KEENOBSERVER = 1160, //  观察力：最强眼力
  IMAGINTRAIN = 1165, //  观察力：最强眼力
  MATHLINKUP=1166,//算式连连看
  FUNCUTROPE = 1174, //  趣味割绳子
  PARKOUR = 1175, //  跑酷 parkour
  THEWALLS = 1176,//墙来了
  TICTACTOE = 1177, //圈叉游戏
  MAZELINK = 1179, //  迷宫连线
  FRUITNINJA = 1180, //连续切水果
  QUEST = 1181, //大招收集
  TAPFRENZY = 1182,//连续点击
  SUDOKU = 1184, //数独
  GESTUREUNLOCK = 1185, //手势解锁
  FOLLOWRECORDMANYTIMES = 1186, //单词读三遍
  PATHPUZZLE = 1189, //路径规律
  AREASHIFTER = 1191, //等积变形
  ENGROUPPK = 1192, // 小英全员上台
  RAINRUSH = 1194,  //抢伞游戏
  DETECTIVE=1195, //小侦探考核
  AROUNDSPEAK = 1196, // 轮流发言
  STEPUP = 1198, //爬台阶
  WORDBOMB = 1202, // 单词大爆炸
}

export const cocosTemplates: any[] = [
  { id: 1, name: '选择题', category: 1010, type: '1' },
  { id: 2, name: '拖拽题', category: 1011, type: '2' },
  { id: 5, name: '填空题', category: 1012, type: '5' },
  { id: 6, name: '连线题', category: 1013, type: '6' },
  { id: 7, name: '火柴题', category: 1019, type: '7' },
  { id: 8, name: '跟读题', category: 1020, type: '8' },
  { id: 9, name: '计数题', category: 1021, type: '9' }
];
