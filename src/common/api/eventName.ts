export enum EventName {
  /**
   组件库tab点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   * @param {string} name 题目名称
   * 
   */
  CLICK_COMPONENT_LIBRARY_TAB = 'I5C_051',  // done

  /** 组件管理tab点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   * @param {string} name 题目名称
   * 
  */
  CLICK_COMPONENT_MANAGE_TAB = 'I5C_052',   // done
  /**
   * 收起的点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   * @param {string} name 题目名称
   * @param {boolean} visible 是否收起 true 是 false 不是
   */
  CLICK_COMPONENT_COLLAPSE = 'I5C_053', // done
  /**
   * 组件管理功能下具体组件点击
   * @param {string} category 组件类型
   * @param {string} id 组件id
      @param {string} isGroup 是否是组合 true 是 false 不是
      @param {string} isSub 是否是子组件 true 是 false 不是
      @param {string} type 组件类型
   */
  CLICK_COMPONENT_ITEM_CLICK = 'I5C_054', // done

  /**
   * 组件管理功能下删除组件点击
   * @param {string} category 组件类型
   * @param {string} name 组件名称
   * @param {string} type 组件id
   * @param {string} type 组件类型
   */
  CLICK_COMPONENT_ITEM_DELETE = 'I5C_055', // done

  /** 普通模式点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   */
  CLICK_COMPONENT_NORMAL_MODE = 'I5C_056', // done
  /**
   * 动画模式点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   */
  CLICK_COMPONENT_ANIMATION_MODE = 'I5C_057', // done
  /**
   * 撤销点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   */
  CLICK_REVOKE_BUTTON = 'I5C_058',  // done
  /**
   * 重做点击
   * @param {string} category 题型
   * @param {string} categoryName 题型
   */
  CLICK_REDO_BUTTON = 'I5C_059',  // done

  /**
   * 预览button点击
   */
  CLICK_PREVIEW_BUTTON = 'I5C_060',  // done
  /**
   * 保存button点击
   *  @param {string} category 题型
   * @param {string} categoryName 题型
   */
  CLICK_SAVE_BUTTON = 'I5C_061',  // done
  /**
   * 创建button点击
   * 时间戳
    用户UID
    用户姓名
    Cocos模板名称
    Cocos模板id
    Cocos题型
    Cocos题目名称
    Cocos题目id
    Cocos题目本次编辑时间（进入本Cocos题目到点击创建button的时间差）
    操作总次数
    每一个组件id的改动次数
    初始组件id数量
    创建题目时组件id数量
    这道题创建时组件类型的分布
   * @param {string} category 题型
   * @param {string} categoryName 题型
   * @param {string} name 题目名称
   * @param {string} type 题目id
   * @param {string} time 题目创建时间
   * @param {string} count 操作总次数
   * @param {string} componentId 组件id
   * @param {string} componentCount 组件id数量
   * @param {string} componentType 组件类型
   */
  CLICK_CREATE_BUTTON = 'I5C_062',  // done
}