import request, { requestPhpServer } from "../utils/request";
import APIS from "./constants";

export const getQuestionTemporaryStorage = (id: string) =>
  requestPhpServer
    .get<
      Response<{
        name: string;
        tempName: string;
        tempId: number;
        id: number;
        tempType: number;
        category: number;
        content: string;
        parentVersion: number;
      }>
    >(APIS.PHP_GET_PREVIEW_DATA, { params: { vid: id } })
    .then(res => {
      try {
        if (res.data.data && !Array.isArray(res.data.data)) {
          const jsonData = JSON.parse(res.data.data.content);
          res.data.data.name = jsonData.name;
          res.data.data.tempName = jsonData.tempName;
          res.data.data.content = jsonData.content;
          res.data.data.tempId = jsonData.tempId;
          res.data.data.category = jsonData.category;
          res.data.data.tempType = jsonData.tempType;
          res.data.data.parentVersion = jsonData.parentVersion ?? 1;
          return res;
        } else {
          throw new Error('数据返回错误，请检查')
        }
      } catch (error) {
        throw new Error('数据返回错误，请检查')
      }
      
    });

export const addQuestion = (data: Omit<QuestionItem, "id">) =>
  requestPhpServer.post<Response<{ qid: number }>>(
    APIS.PHP_CREATE_QUESTION,
    data,
  );


export const updateQuestion = (
  data: Omit<QuestionItem, "id"> & { qid: number },
) =>
  requestPhpServer.post<Response<{ qid: number }>>(
    APIS.PHP_EDIT_QUESTION,
    data,
  );


export const getQuestionTypeList = () =>  requestPhpServer.get(APIS.PHP_QUESTIONTYPE_LIST);

export interface SaveData {
  name?: string;
  id: number;
  thumbnail: string;
  type?: number;
  tempType?: number;
  tempId?: number;
  content?: string;
}

type CreateVersionParams = {
  qid: number;
  tags: string;
  content: string;
  gradeId: number;
  subjectId: number;
  parentVersion: number;
  thumbnail: string;
  features: string;
  tagIds?: string;
  standardData: string;
  extData: string;
  demoId?: number;
  templateId?: number;
  source: 0 | 1;
};

export const createVersion = (params: CreateVersionParams) => {
  if (localStorage.getItem('setMockCreateError')) {
    return Promise.resolve({
      data: { "errNo": 0, "errStr": "success", "data": false }
    })
  }
  return requestPhpServer.post<
    Response<{
      qid: number;
      snapId: number;
      tId: number;
      tempId: number;
      tempType: number;
      category: number;
      content: string;
      thumbnail: string;
      features: string;
    }>
  >(APIS.PHP_CREATE_VERSION, params);
};

export const getVersionInfo = (snapId: number) => {
  return requestPhpServer.get(APIS.PHP_GET_VERSION_INFO, {
    params: { snapId },
  });
};

export const createPicTask = (snapId: number) => {
  return requestPhpServer.get(APIS.PHP_CREATE_PIC_TASK, {
    params: { snapId },
  });
};
