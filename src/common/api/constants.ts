/*
 * @Date: 2021-11-08 20:11:57
 * @LastEditors: chxu
 * @LastEditTime: 2021-11-22 15:04:05
 * @FilePath: /interactive-question-editor/src/common/api/constants.ts
 * @Author: chxu
 */
export const APIS = {
  UPDATE_QUESTION: "/question/updateQuestion",
  GET_QUESTION_INFO: "/question/getQuestionInfo",
  PUBLISH_QUESTION: "/question/publishQuestion",
  QUESTION_PREVIEW: "/question/preview",

  GET_QUESTION_SNAPSHOT_INFO: "/question/getSnapshotInfo",

  GET_TEMPLATE_BY_ID: "/questionTemplate/getQuestionTemplateById",
  UPDATE_TEMPLATE: "/questionTemplate/updateQuestionTemplate",
  QUESTION_TEMPLATE_TYPES: "/questionTemplate/types",

  GET_STATIC_RESOURCE_LIST_BY_COND: "/staticResource/getStaticResourceListByCond",
  DELETE_STATIC_RESOURCE: "/staticResource/deleteStaticResource",
  ADD_STATIC_RESOURCE: "/staticResource/addStaticResource",

  UPLOAD_FILE: "upload/uploadFile",
  UPLOAD_FILES: "upload/uploadFiles",
  uploadFiles: "/materiallib/api/uploadmult",
  GET_BUNDLE_LIST: "/bundle",
  GET_BUNDLE_URL: "/bundle/active-url",
  // spine
  SPINE_SEARCH_SPINE: "spine/list",
  SPINE_DELETE: "spine/delete",
  SPINE_EDIT_SPINE: "spine",
  SPINE_ADD_SPINE: "spine",
  SPINE_GET_TAG_LIST: "spine-tag",

  // 题目类型
  QUESTIONTYPE_LIST: "/questionType/list",

  // cocosAni
  PHP_COCOSANI_ADD_COCOSANI: "/artcw/resource/createeffect",
  PHP_COCOSANI_GET_LIST: "/artcw/resource/geteffects",

  // php接口地址
  PHP_GET_QUESTION_LIST: "/artcw/sharks/sharklist",
  PHP_CREATE_PREVIEW: "/artcw/sharks/createpreview",
  PHP_CREATE_QUESTION: "/artcw/sharks/createshark",
  PHP_EDIT_QUESTION: "/artcw/sharks/editshark",
  PHP_DELETE_QUESTION: "/artcw/sharks/deleteshark",
  PHP_GET_QUESTION_INFO: "/artcw/sharks/sharkdetail",
  PHP_CREATE_VERSION: "/artcw/sharks/createversion",
  PHP_GET_VERSION_INFO: "/artcw/sharks/getversion",
  PHP_GET_TID_INFO: "/artcw/api/getbytid",
  PHP_GET_PREVIEW_DATA: "/artcw/sharks/getpreview",
  PHP_UPDATE_QUESTION_TEMPLATE: "/artcw/sharktemplates/updatesharktemplate",
  PHP_CREATE_QUESTION_TEMPLATE: "/artcw/sharktemplates/createsharktemplate",
  PHP_GET_QUESTION_TEMPLATE_INFO: "/artcw/sharktemplates/getsharktemplateinfo",
  PHP_GET_QUESTION_TEMPLATE_LIST: "/artcw/sharktemplates/getsharktemplatelist",
  PHP_QUESTIONTYPE_LIST: "artcw/questiontype/getall",
  PHP_SPINE_SEARCH: "artcw/spine/getspines", // ?name=2&tag[]=1
  PHP_SPINE_ADD: "artcw/spine/createspine", // ?name=test&tags[]=name
  PHP_SPINE_EDIT: "artcw/spine/updatespine", // ?name=test&tags[]=name
  PHP_SPINE_GET_TAG_LIST: "artcw/spine/gettags", // ?name=t get
  PHP_SPINE_ADD_TAG: "artcw/spine/createtag", // ?name=t get
  PHP_SPINE_DEL: "artcw/spine/delspine", // ?name=t get
  PHP_GET_STATIC_RESOURCE_LIST: "artcw/staticresource/list", // ?name=
  PHP_DELETE_STATIC_RESOURCE: "artcw/staticresource/del", // ?id=2
  PHP_ADD_STATIC_RESOURCE: "artcw/staticresource/create", // ?name=1111&urlContent=test
  PHP_UPLOAD_AUDIO: "artcw/api/uploadaudio",
  PHP_UPLOAD_FILE: "materiallib/api/upload?v=3.2", // node-api
  // PHP_UPLOAD_FILE: "/miscourseware/api/upload",
  PHP_GET_POINT_LIST: "/artcw/platmis/point",
  PHP_GET_LABEL_CONFIG: "/artcw/platmis/labelconf", // ?names[]=test
  PHP_GET_PKGLIST: "/artcw/pkgmanage/pkglist",
  PHP_GET_PKGLAST: "/hera/pkg-manage/last",
  PHP_GET_PKGS: "/hera/api/getcocospkgpreviewurl",
  PHP_GET_TAG_OPTIONS: "/artcw/platmis/getsearchtagoptions",
  PHP_GET_TAGS_BY_ID: "/artcw/sharks/gettags",
  PHP_GET_TAG_TOKEN: "/artcw/api/gettokenbypic",
  PHP_GET_TAGS_BY_TOKEN: "/artcw/api/getsearchtagsbytoken",
  PHP_GET_USER_INFO: "artcw/misauth/getuserinfo",
  PHP_CREATE_PIC_TASK: "artcw/pictask/create",
};

export default APIS;

export enum StaticResourceTypes {
  IMAGE = 0,
  AUDIO = 1,
}

export enum Partitions {
  PRIVATE = 0,
  PUBLIC = 1,
}

export enum ContentTags {
  KNOWLEDGE = 1,
  NONKNOWLEDGE = 2,
}

export const contentTagOps = [
  {
    label: '知识类',
    value: ContentTags.KNOWLEDGE,
  },
  {
    label: '非知识类',
    value: ContentTags.NONKNOWLEDGE,
  }
]
