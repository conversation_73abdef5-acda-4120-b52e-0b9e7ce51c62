import request, { requestPhpServer } from "../utils/request";
import APIS from "./constants";
import { searchSpineData, getTagListData } from "@/pages/index/components/EditArea/SchemaToForm/mockData";
import { AxiosPromise } from 'axios';

interface SpineList {
  total: number;
  list: SpineItem[];
}

interface SearchSpineParams {
  pageSize: number;
  pageNum: number;
  name: string;
  tags: string[];
}

type AddSpineParams = FormData;
type EditSpineParams = FormData;

interface Tag {
  name: string;
  id: number;
}

export const searchSpine = (data: SearchSpineParams) => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return searchSpineData as unknown as AxiosPromise<Response<SpineList>>;
  }
  return requestPhpServer.get(APIS.PHP_SPINE_SEARCH, {
    params: data,
  });
  // return request.post<Response<SpineList>>(APIS.SPINE_SEARCH_SPINE, data);
};

export const deleteSpine = (spineId: number) => {
  return requestPhpServer.post<Response<SpineList>>(APIS.PHP_SPINE_DEL, { id: spineId });
};


export const addSpine = (data: AddSpineParams) => {
  return requestPhpServer.post<Response<{ id: number }>>(APIS.PHP_SPINE_ADD, data, {
    headers: {
      "content-type": "multipart/form-data"
    }
  });
}

export const editSpine = (data: EditSpineParams) => {
return requestPhpServer.post<Response<{ id: number }>>(APIS.PHP_SPINE_EDIT, data, {
  headers: {
    "content-type": "multipart/form-data"
  }
});
}
  // requestPhpServer.post<Response<{ id: number }>>(APIS.PHP_SPINE_EDIT, data);

export const getTagList = () => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return getTagListData as unknown as AxiosPromise<Response<Tag[]>>;
  }
  // PHP_SPINE_GET_TAG_LIST
  return requestPhpServer.get(APIS.PHP_SPINE_GET_TAG_LIST);
};

export const addTag = (names: string[]) => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return getTagListData as unknown as AxiosPromise<Response<Tag[]>>;
  }
  // PHP_SPINE_GET_TAG_LIST
  return requestPhpServer.get(APIS.PHP_SPINE_ADD_TAG, {
    params: {
      names
    },
  });
  // return request.get<Response<Tag[]>>(APIS.SPINE_GET_TAG_LIST, { params: { client } })
};
