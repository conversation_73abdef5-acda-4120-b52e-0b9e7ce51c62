import request, { requestPhpServer } from "@/common/utils/request";
import APIS from "./constants";

/**
 * @description 登录验证接口
 */
export const authLogin = () => {
  return request.get("/auth/login");
};

interface UserInfo {
  userId: number,
  userName: string,
  userAccount: string
}

/**
 * 获取用户登陆信息
 */
export function getUserInfo(): Promise<UserInfo> {
  return requestPhpServer
    .get(APIS.PHP_GET_USER_INFO, { params: { t: Date.now() } })
    .then(res => {
      return res.data.data;
    });
}


