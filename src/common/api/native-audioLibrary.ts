import { nativeRequestServer } from "../nativeUtils";
import { Partitions } from "./constants";

/**
 * @description 获取用户自己上传的音频列表
 */
export const getMyAudios = () => {
  return nativeRequestServer.get('/editorApi/getAudioList')
};

/**
 * @description 删除我的音频
 */
export const deleteMyAudio = (id: number) => {
  return nativeRequestServer.get(`/editorApi/delAudio?id=${id}`)
};

/**
 * @description 上传音频
 */
export const uploadAudio = (params: { urlContent: FormData; name: string; partition: Partitions; opUser: string; client: number }) => {
  return nativeRequestServer.post('/editorApi/uploadAudio', params.urlContent, {
    headers: {
      "content-type": "multipart/form-data",
    },
  });
};
