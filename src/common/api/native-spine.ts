import { getTagListData } from "@/pages/index/components/EditArea/SchemaToForm/mockData";
import { AxiosPromise } from 'axios';
import { nativeRequestServer } from "../nativeUtils";


interface SearchSpineParams {
  pageSize: number;
  pageNum: number;
  name: string;
  tags: string[];
  client: number;
}

type AddSpineParams = FormData;

interface Tag {
  name: string;
  id: number;
}

export const searchSpine = () => {
  return nativeRequestServer.get('/editorApi/getSpineList')
};

export const addSpine = (data: AddSpineParams) => nativeRequestServer.post('/editorApi/spineUpload', data, {
  headers: {
    "content-type": "multipart/form-data",
  }
})

export const editSpine = (data: AddSpineParams) => nativeRequestServer.post('/editorApi/spineUpload', data, {
  headers: {
    "content-type": "multipart/form-data",
  }
})

export const getTagList = () => {
  return getTagListData as unknown as AxiosPromise<Response<Tag[]>>;
};
