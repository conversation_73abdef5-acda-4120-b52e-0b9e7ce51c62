import { requestPhpServer } from "../utils/request";
import APIS from "./constants";

interface CocosAniList {
  total: number;
  list: CocosAniItem[];
}

type AddCocosAniParams = FormData;

// php 请求content-type 必须小写
export const addCocosAni = (data: AddCocosAniParams) =>
  requestPhpServer.post<Response<{ effect_id: number }>>(
    APIS.PHP_COCOSANI_ADD_COCOSANI,
    data,
    {
      headers: {
        "content-type": "multipart/form-data",
      },
    },
  );

export const getCocosAniList = (data: any) =>
  requestPhpServer.get<Response<CocosAniList>>(
    APIS.PHP_COCOSANI_GET_LIST,
    data,
  );
