import { requestPhpServer } from "@/common/utils/request";
import APIS from "./constants";

interface LabelConfResData {
  gradeAndSubject: any;
  defaltPrivate: any;
  gradeStage: any;
  tikuType: any;
  difficulty: any;
  sourceMap: any;
  zbPower: any;
}

interface Point {
  pointId: number;
  parentId: number;
  title: string;
  courseId: string;
  gradeId: string;
  level: number;
  children: Point[];
}
interface PointResData {
  tree: Point[];
}

/**
 * 查询标签配置项
 * @param data
 */
export function getTagConditions(): Promise<LabelConfResData> {
  return requestPhpServer.get(APIS.PHP_GET_LABEL_CONFIG).then(res => {
    return res.data.data;
  });
}

/**
 * 查询考点配置项
 * @param data
 */
export function getPoint(params: {
  grade: number;
  subject: number;
}): Promise<PointResData> {
  return requestPhpServer
    .get(APIS.PHP_GET_POINT_LIST, {
      params,
    })
    .then(res => {
      return res.data.data;
    });
}

interface TagOptionsResData {
  aiTags: TagsItem;
  sharkTags: TagsItem;
}
export interface TagsItem {
  tagId: number;
  parentTagId: number;
  tagName: string;
  children: TagsItem[]
}

interface AITags {
  status: 0 | 1; // 0 生成中 1 已生成
  list: number[];
}

/**
 * 获取业务属性和智能分类, 此处的数据都是通过代码上线添加的，所以变动频繁，单独拎出来获取
 * @param data
 */
export function getTagOptions(): Promise<TagOptionsResData> {
  return requestPhpServer
    .get(APIS.PHP_GET_TAG_OPTIONS)
    .then(res => {
      return res.data.data;
    });
}

/**
 * 获取业务属性和智能分类, 此处的数据都是通过代码上线添加的，所以变动频繁，单独拎出来获取
 * @param data
 */
export function getTags(id: number): Promise<TagsItem[]> {
  return requestPhpServer
    .get(APIS.PHP_GET_TAGS_BY_ID, { params: { snapId: id } })
    .then(res => {
      return res.data.data;
    });
}

/**
 * 根据题目截图获取ai识别任务的token
 * @param pic string
 */
export function getTagToken(pic: string): Promise<string> {
  return requestPhpServer
    .post(APIS.PHP_GET_TAG_TOKEN, { pic })
    .then(res => {
      if (res.data.errNo === 0)  {
        return res.data.data;
      } else {
        return '';
      }
    });
}

/**
 * 根据ai识别任务的token获取智能分类
 * @param picUrl string
 */
export function getTagsByToken(token: string): Promise<AITags> {
  return requestPhpServer
    .get(APIS.PHP_GET_TAGS_BY_TOKEN, { params: { token } })
    .then(res => {
      return res.data.data;
    });
}
