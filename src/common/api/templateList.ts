/*
 * @Date: 2021-12-16 11:25:15
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-23 16:48:10
 * @FilePath: /interactive-question-editor/src/common/api/templateList.ts
 * @Author: chxu
 */
import request, { requestPhpServer } from "@/common/utils/request";

export interface GetQuestionTemplateListParam extends PageQueryParam {
  tempType?: number;
  name?: string;
  client?: number;
  parentVersion?: number,
}

export interface GetQuestionListParam extends PageQueryParam {
  name?: string;
  tempType?: number;
}
/**
 * @description 根据 id 获取模板
 */
export const getQuestionTemplateById = (id: number) => {
  return requestPhpServer.get<ResponseWrapper<QuestionTemplateItem>>(
    "/artcw/sharktemplates/getsharktemplateinfo",
    {
      params: { id },
    },
  );
};
export const addQuestionTemporaryStorage = (data: any) =>
  requestPhpServer.post<ResponseWrapper<{ vid: string }>>(
    "/artcw/sharks/createpreview",
    data,
  );
/**
 * @description 获取模板列表
 */
export const getQuestionTemplateList = (
  params: GetQuestionTemplateListParam,
) => {
  return requestPhpServer.get<
    ResponseWrapper<PageQueryRes<QuestionTemplateItem>>
  >("/artcw/sharktemplates/getsharktemplatelist", {
    params: {
      tempType: params.tempType,
      name: params.name,
      pn: 1,
      rn: params.rn,
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      parentVersion: params.parentVersion
    },
  });
};

// /**
//  * @description 分页获取互动题模板列表
//  */
// export const getQuestionTemplateList = (
//   params: GetQuestionTemplateListParam,
// ) => {
//   return request.get<
//     Response<{ tempList: QuestionTemplateItem[]; total: number }>
//   >("/questionTemplate/getQuestionTemplateListByCond", {
//     params,
//   });
// };

/**
 * @description 分页获取互动题列表
 */
export const getQuestionList = (params: GetQuestionListParam) => {
  return request.get<Response<{ questionList: QuestionItem[]; total: number }>>(
    "/question/getQuestionList",
    {
      params,
    },
  );
};

/**
 * @description 获取互动题模板分类列表
 */
export const getQuestionTemplateTypeList = () => {
  return request.get<Response<QuestionTemplateTypeItem[]>>(
    "/template-type-list",
  );
};
