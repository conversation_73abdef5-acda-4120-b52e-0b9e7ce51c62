import { requestPhpServer } from "@/common/utils/request";
import APIS from "./constants";
import { getMyAudiosData } from "@/pages/index/components/EditArea/SchemaToForm/mockData";

const defaultPageQuery: PageQueryParam = {
  pageNum: 1,
  pageSize: 10,
  pn: 0,
  rn: 0
};
type resData = PageQueryRes<Omit<AudioItem, "url"> & { urlContent: string }>;

export interface GetMyAudiosParams {
  pageNum: number,
  pageSize: number,
  name?: string,
  creatorName?: string,
}

/**
 * @description 获取用户自己上传的音频列表
 */
export const getMyAudios = (params: GetMyAudiosParams) => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return getMyAudiosData;
  }
  // PHP_GET_STATIC_RESOURCE_LIST
  return requestPhpServer.get(APIS.PHP_GET_STATIC_RESOURCE_LIST, {
    params: {
      ...params
    },
  });
};

/**
 * @description 获取图音频库列表
 */
export const getLibraryAudios = (params: PageQueryParam = defaultPageQuery) => {
  return requestPhpServer.get(APIS.PHP_GET_STATIC_RESOURCE_LIST, {
    params: {
      ...params
    },
  });
};

/**
 * @description 删除我的音频
 */

export const deleteMyAudio = (id: number) => {
  return requestPhpServer.post<Response<{ id: number }>>(APIS.PHP_DELETE_STATIC_RESOURCE, { id });
};

/**
 * @description 上传音频
 */
export const uploadAudio = (params: { urlContent: FormData; name: string; }) => {
  return requestPhpServer.post<Response<{ id: number }>>(APIS.PHP_ADD_STATIC_RESOURCE, params);
};
