<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="28px" viewBox="0 0 40 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.6 (67491) - http://www.bohemiancoding.com/sketch -->
    <title>下右</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="8" y="7" width="24" height="14" rx="1"></rect>
        <filter x="-16.7%" y="-85.7%" width="166.7%" height="214.3%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="4" dy="-4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="易课堂3.2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="易课堂v3.2组件" transform="translate(-860.000000, -1618.000000)" fill-rule="nonzero">
            <g id="阴影" transform="translate(756.000000, 1399.000000)">
                <g id="投影-copy-2" transform="translate(0.000000, 76.000000)">
                    <g id="箭头" transform="translate(10.000000, 31.000000)">
                        <g id="矩形-6" transform="translate(94.000000, 112.000000)">
                            <g id="矩形">
                                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                                <use fill="#4B4B52" xlink:href="#path-1"></use>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>