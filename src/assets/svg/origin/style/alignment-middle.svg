<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.6 (67491) - http://www.bohemiancoding.com/sketch -->
    <title>中间对齐</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <filter x="-0.9%" y="-0.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.882352941   0 0 0 0 0.894117647   0 0 0 0 0.921568627  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="易课堂3.2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="易课堂v3.2属性区" transform="translate(-867.000000, -1278.000000)" fill="#525354" fill-rule="nonzero">
            <g id="文本-copy-2" filter="url(#filter-1)" transform="translate(740.000000, 1016.000000)">
                <g id="模块/样式/对齐" transform="translate(16.000000, 218.000000)">
                    <g id="对齐-copy" transform="translate(0.000000, 38.000000)">
                        <g id="上下向中央收缩" transform="translate(111.000000, 6.000000)">
                            <path d="M8.482,4.736 L10.565,2.647 C10.7609183,2.45721749 11.0727905,2.45947744 11.2659379,2.65207927 C11.4590853,2.8446811 11.4622275,3.15654557 11.273,3.353 L8.283,6.353 C8.18915592,6.44734863 8.06157282,6.50039576 7.9285,6.50039576 C7.79542718,6.50039576 7.66784408,6.44734863 7.574,6.353 L4.584,3.353 C4.39477247,3.15654557 4.39791465,2.8446811 4.59106209,2.65207927 C4.78420954,2.45947744 5.09608165,2.45721749 5.292,2.647 L7.482,4.845 L7.482,0.5 C7.482,0.223857625 7.70585763,5.07265313e-17 7.982,0 C8.25814237,-5.07265313e-17 8.482,0.223857625 8.482,0.5 L8.482,4.736 Z M7.482,11.156 L5.292,13.353 C5.16588525,13.4796507 4.98178309,13.5293993 4.80904303,13.483506 C4.63630297,13.4376128 4.50116834,13.3030499 4.45454303,13.130506 C4.40791773,12.9579621 4.45688525,12.7736507 4.583,12.647 L7.574,9.647 C7.66780426,9.5529583 7.79517274,9.50010622 7.928,9.50010622 C8.06082726,9.50010622 8.18819574,9.5529583 8.282,9.647 L11.273,12.647 C11.4027884,12.7727237 11.4546441,12.958693 11.4086375,13.1334353 C11.362631,13.3081776 11.2259214,13.444501 11.0510496,13.490013 C10.8761779,13.5355251 10.690356,13.4831435 10.565,13.353 L8.482,11.264 L8.482,15.444 C8.482,15.7201424 8.25814237,15.944 7.982,15.944 C7.70585763,15.944 7.482,15.7201424 7.482,15.444 L7.482,11.155 L7.482,11.156 Z M5.497,8.53 C5.22085763,8.53 4.997,8.30614237 4.997,8.03 C4.997,7.75385763 5.22085763,7.53 5.497,7.53 L10.497,7.53 C10.7731424,7.53 10.997,7.75385763 10.997,8.03 C10.997,8.30614237 10.7731424,8.53 10.497,8.53 L5.497,8.53 Z" id="形状"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>