<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.6 (67491) - http://www.bohemiancoding.com/sketch -->
    <title>plus-o</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <filter x="-0.9%" y="-0.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.882352941   0 0 0 0 0.894117647   0 0 0 0 0.921568627  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="易课堂3.2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="易课堂v3.2属性区" transform="translate(-356.000000, -1105.000000)" fill="#555555">
            <g id="文本-copy-3" filter="url(#filter-1)" transform="translate(120.000000, 1018.000000)">
                <g id="字体" transform="translate(16.000000, 81.000000)">
                    <g id="分组-5" transform="translate(197.000000, 0.000000)">
                        <g id="icon/format/font-bold" transform="translate(2.000000, 1.000000)">
                            <g id="plus-o" transform="translate(21.000000, 5.000000)">
                                <path d="M7.4,7.4 L7.4,4 C7.4,3.66862915 7.66862915,3.4 8,3.4 C8.33137085,3.4 8.6,3.66862915 8.6,4 L8.6,7.4 L12,7.4 C12.2143594,7.39999999 12.4124356,7.51435934 12.5196153,7.69999999 C12.6267949,7.88564064 12.6267949,8.11435936 12.5196153,8.30000001 C12.4124356,8.48564066 12.2143594,8.60000001 12,8.6 L8.6,8.6 L8.6,12 C8.60000001,12.2143594 8.48564066,12.4124356 8.30000001,12.5196153 C8.11435936,12.6267949 7.88564064,12.6267949 7.69999999,12.5196153 C7.51435934,12.4124356 7.39999999,12.2143594 7.4,12 L7.4,8.6 L4,8.6 C3.78564064,8.60000001 3.58756442,8.48564066 3.48038473,8.30000001 C3.37320505,8.11435936 3.37320505,7.88564064 3.48038473,7.69999999 C3.58756442,7.51435934 3.78564064,7.39999999 4,7.4 L7.4,7.4 Z" id="路径"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>