<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 52.6 (67491) - http://www.bohemiancoding.com/sketch -->
    <title>minus-o</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <filter x="-0.9%" y="-0.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feColorMatrix values="0 0 0 0 0.882352941   0 0 0 0 0.894117647   0 0 0 0 0.921568627  0 0 0 1 0" type="matrix" in="shadowOffsetOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
    </defs>
    <g id="易课堂3.2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="易课堂v3.2属性区" transform="translate(-335.000000, -1105.000000)" fill="#555555">
            <g id="文本-copy-3" filter="url(#filter-1)" transform="translate(120.000000, 1018.000000)">
                <g id="字体" transform="translate(16.000000, 81.000000)">
                    <g id="分组-5" transform="translate(197.000000, 0.000000)">
                        <g id="icon/format/font-bold" transform="translate(2.000000, 1.000000)">
                            <g id="minus-o" transform="translate(0.000000, 5.000000)">
                                <path d="M4,8.85 C3.66862915,8.85 3.4,8.58137085 3.4,8.25 C3.4,7.91862915 3.66862915,7.65 4,7.65 L12,7.65 C12.3313709,7.65 12.6,7.91862915 12.6,8.25 C12.6,8.58137085 12.3313709,8.85 12,8.85 L4,8.85 Z" id="路径"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>