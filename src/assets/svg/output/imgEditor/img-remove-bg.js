/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'editor-area/img-remove-bg': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_editor-prop_img-remove-bg_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_editor-prop_img-remove-bg_a)" transform="translate(-45 -297)" _fill="#555" fill-rule="nonzero"><path pid="0" d="M52.619 306.619l2.075-2.075a.833.833 0 00-1.179-1.179l-2.075 2.075 1.179 1.179zm-5.468 1.696l5.657-5.657a1.833 1.833 0 012.593 2.593l-5.657 5.657a1.833 1.833 0 11-2.593-2.593zm3.546-8.148v-.531a.5.5 0 111 0v.53h.531a.5.5 0 110 1h-.53v.531a.5.5 0 11-1 0v-.53h-.531a.5.5 0 010-1h.53zm6.667 0v-.531a.5.5 0 111 0v.53h.53a.5.5 0 110 1h-.53v.531a.5.5 0 01-1 0v-.53h-.53a.5.5 0 110-1h.53zm0 6.53v-.53a.5.5 0 111 0v.53h.53a.5.5 0 110 1h-.53v.531a.5.5 0 01-1 0v-.53h-.53a.5.5 0 110-1h.53z"/></g>'
  }
})
