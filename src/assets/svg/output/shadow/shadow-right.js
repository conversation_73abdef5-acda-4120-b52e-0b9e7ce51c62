/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'shadow/shadow-right': {
    width: 40,
    height: 28,
    viewBox: '0 0 40 28',
    data: '<defs><filter x="-22.9%" y="-67.9%" width="179.2%" height="235.7%" filterUnits="objectBoundingBox" id="svgicon_shadow_shadow-right_a"><feMorphology radius=".5" operator="dilate" in="SourceAlpha" result="shadowSpreadOuter1"/><feOffset dx="4" in="shadowSpreadOuter1" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" in="shadowBlurOuter1"/></filter><rect pid="0" id="svgicon_shadow_shadow-right_b" x="8" y="7" width="24" height="14" rx="1"/></defs><g fill-rule="nonzero" _fill="none"><use _fill="#000" filter="url(#svgicon_shadow_shadow-right_a)" xlink:href="#svgicon_shadow_shadow-right_b"/><use _fill="#4B4B52" xlink:href="#svgicon_shadow_shadow-right_b"/></g>'
  }
})
