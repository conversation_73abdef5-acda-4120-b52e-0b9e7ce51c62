/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'shadow/shadow-left': {
    width: 40,
    height: 28,
    viewBox: '0 0 40 28',
    data: '<defs><filter x="-50%" y="-57.1%" width="166.7%" height="214.3%" filterUnits="objectBoundingBox" id="svgicon_shadow_shadow-left_a"><feOffset dx="-4" in="SourceAlpha" result="shadowOffsetOuter1"/><feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"/><feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" in="shadowBlurOuter1"/></filter><rect pid="0" id="svgicon_shadow_shadow-left_b" x="8" y="7" width="24" height="14" rx="1"/></defs><g fill-rule="nonzero" _fill="none"><use _fill="#000" filter="url(#svgicon_shadow_shadow-left_a)" xlink:href="#svgicon_shadow_shadow-left_b"/><use _fill="#4B4B52" xlink:href="#svgicon_shadow_shadow-left_b"/></g>'
  }
})
