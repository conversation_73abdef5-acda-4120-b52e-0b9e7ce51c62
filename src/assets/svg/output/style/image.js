/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/image': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<g transform="translate(2 2)" _stroke="#525354" stroke-width="1.2" _fill="none" fill-rule="evenodd"><rect pid="0" x=".6" y=".6" width="10.8" height="10.8" rx="2"/><path pid="1" d="M.892 11.003l.827-.71L6.384 6.27a2 2 0 012.72.1l2.192 2.192h0"/><circle pid="2" cx="4" cy="4" r="1"/></g>'
  }
})
