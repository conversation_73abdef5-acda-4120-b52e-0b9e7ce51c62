/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/text-font-minus': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_style_text-font-minus_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_style_text-font-minus_a)" transform="translate(-215 -87)" _fill="#555" fill-rule="evenodd"><path pid="0" d="M219 95.85a.6.6 0 110-1.2h8a.6.6 0 110 1.2h-8z"/></g>'
  }
})
