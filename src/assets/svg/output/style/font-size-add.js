/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/font-size-add': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<g _fill="none" fill-rule="evenodd"><path pid="0" d="M0 0h16v16H0z"/><path pid="1" xmlns="http://www.w3.org/2000/svg" d="M7.016 2.576L11.512 14h-1.424l-1.216-3.2H3.624L2.408 14H1L5.496 2.576h1.52zm-.72 1.408h-.064L4.04 9.696h4.416l-2.16-5.712zM13 1v2h2v1h-2v2h-1V4h-2V3h2V1h1z" _fill="#525354"/></g>'
  }
})
