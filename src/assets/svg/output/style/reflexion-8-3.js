/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/reflexion-8-3': {
    width: 40,
    height: 28,
    viewBox: '0 0 40 28',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_style_reflexion-8-3_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_style_reflexion-8-3_a)" transform="translate(-48 -306)" fill-rule="nonzero" _fill="none"><g transform="translate(48 306)"><g opacity=".4"><path pid="0" d="M9 18h22a1 1 0 011 1v9H8v-9a1 1 0 011-1z" _fill="#72D0E2"/><path pid="1" d="M10 19h20a1 1 0 011 1v8H9v-8a1 1 0 011-1z" _fill="#D5F1F7"/></g><rect pid="2" _stroke="#4FC4DB" _fill="#83D5E5" x="8.5" y=".5" width="23" height="13" rx="1"/></g></g>'
  }
})
