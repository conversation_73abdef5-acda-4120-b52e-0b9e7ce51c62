/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/alignment-middle': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_style_alignment-middle_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_style_alignment-middle_a)" transform="translate(-127 -262)" _fill="#525354" fill-rule="nonzero"><path pid="0" d="M135.482 266.736l2.083-2.089a.5.5 0 01.708.706l-2.99 3a.5.5 0 01-.709 0l-2.99-3a.5.5 0 01.708-.706l2.19 2.198V262.5a.5.5 0 011 0v4.236zm-1 6.42l-2.19 2.197a.5.5 0 11-.709-.706l2.991-3a.5.5 0 01.708 0l2.991 3a.5.5 0 11-.708.706l-2.083-2.089v4.18a.5.5 0 11-1 0v-4.289.001zm-1.985-2.626a.5.5 0 010-1h5a.5.5 0 110 1h-5z"/></g>'
  }
})
