/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/circlenumber': {
    width: 24,
    height: 24,
    viewBox: '0 0 24 24',
    data: '<g _fill="none" fill-rule="evenodd"><text font-family="PingFangSC-Medium, PingFang SC" font-size="6" font-weight="400" _fill="#323233"><tspan x="1" y="6">①</tspan> <tspan x="1" y="14">②</tspan> <tspan x="1" y="22">③</tspan></text><path pid="0" _fill="#525354" fill-rule="nonzero" d="M9 4h14v1.5H9zM9 11h14v1.5H9zM9 18h14v1.5H9z"/></g>'
  }
})
