/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/underline-dot': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.6%" filterUnits="objectBoundingBox" id="svgicon_style_underline-dot_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_style_underline-dot_a)" transform="translate(-38 -199)" _fill="#525354" fill-rule="nonzero"><path pid="0" d="M46.25 213.5a1.25 1.25 0 100-2.5 1.25 1.25 0 000 2.5zm-.25-3.375a4.04 4.04 0 002.872-1.19 4.03 4.03 0 001.19-2.873v-4.875a.188.188 0 00-.187-.187h-.938a.188.188 0 00-.187.188v4.875a2.754 2.754 0 01-2.75 2.75 2.754 2.754 0 01-2.75-2.75v-4.875a.188.188 0 00-.188-.188h-.937a.188.188 0 00-.188.188v4.875a4.04 4.04 0 001.191 2.871A4.03 4.03 0 0046 210.125z"/></g>'
  }
})
