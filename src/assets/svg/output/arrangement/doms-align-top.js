/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/doms-align-top': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_doms-align-top_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_doms-align-top_a)" transform="translate(-46 -147)" _fill="none" fill-rule="evenodd"><path pid="0" _fill="#525354" d="M47 149h14v1H47z"/><g fill-rule="nonzero"><path pid="1" d="M56 152v5h2v-5h-2zm0-1h2a1 1 0 011 1v5a1 1 0 01-1 1h-2a1 1 0 01-1-1v-5a1 1 0 011-1z" _fill="#525354"/><path pid="2" _fill="#B9BABD" d="M50 152h2v8h-2z"/><path pid="3" d="M50 151h2a1 1 0 011 1v8a1 1 0 01-1 1h-2a1 1 0 01-1-1v-8a1 1 0 011-1zm0 1v8h2v-8h-2z" _fill="#525354"/></g></g>'
  }
})
