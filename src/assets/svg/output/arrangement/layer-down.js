/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/layer-down': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_layer-down_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_layer-down_a)" transform="translate(-210 -41)" _fill="none" fill-rule="evenodd"><g fill-rule="nonzero"><path pid="0" _fill="#FFF" d="M210.907 51.747l4.405-3.537 5.278 3.537-4.841 3.362z"/><path pid="1" d="M215.694 48.798l-4.167 2.896 4.167 2.897 4.168-2.897-4.168-2.896zm.38-.953l4.752 3.302a.667.667 0 010 1.095l-4.751 3.302a.667.667 0 01-.761 0l-4.752-3.302a.667.667 0 010-1.095l4.752-3.302a.667.667 0 01.76 0z" _fill="#525354"/><path pid="2" _fill="#BCBCBC" d="M211.058 46.819L218 42.224 224.843 47 218 51.23z"/><path pid="3" d="M217.968 42.612l-6.404 4.338 6.404 3.822 6.404-3.822-6.404-4.338zm.374-.955l7.025 4.759a.667.667 0 01-.032 1.124l-7.025 4.193a.667.667 0 01-.684 0l-7.025-4.193a.667.667 0 01-.032-1.124l7.025-4.759a.667.667 0 01.748 0z" _fill="#525354"/><g _stroke="#00B266" stroke-linecap="round" stroke-linejoin="round"><path pid="4" d="M222.942 50.515v4.152M221.079 53.776l1.882 1.333 1.882-1.333"/></g></g></g>'
  }
})
