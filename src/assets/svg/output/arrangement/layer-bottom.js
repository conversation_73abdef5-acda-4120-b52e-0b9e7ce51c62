/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/layer-bottom': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_layer-bottom_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_layer-bottom_a)" transform="translate(-81 -41)" _fill="none" fill-rule="evenodd"><g fill-rule="nonzero"><path pid="0" d="M95.816 46.22a.5.5 0 10.356-.934l-6.613-2.524a1.149 1.149 0 00-.93-.011l-6.64 2.535a.5.5 0 00.356.934l6.667-2.546c.043-.02.093-.02.163.01l6.64 2.536z" _fill="#525354"/><path pid="1" d="M95.816 49.22a.5.5 0 10.356-.934l-6.613-2.524a1.149 1.149 0 00-.93-.011l-6.64 2.535a.5.5 0 00.356.934l6.667-2.546c.043-.02.093-.02.163.01l6.64 2.536z" _fill="#525354"/><path pid="2" _fill="#BCBCBC" d="M82.092 52.667l6.561 2.775 7.206-2.775-6.883-2.81z"/><path pid="3" d="M83.065 52.485l5.566-2.2 6.238 2.2-6.238 2.199-5.566-2.2zm5.78 3.183l7.248-2.554a.667.667 0 000-1.258l-7.247-2.555a.667.667 0 00-.467.009l-6.466 2.555a.667.667 0 000 1.24l6.466 2.555c.15.059.315.062.467.008z" _fill="#525354"/></g></g>'
  }
})
