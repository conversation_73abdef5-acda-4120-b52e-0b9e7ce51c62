/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/doms-align-left': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_doms-align-left_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_doms-align-left_a)" transform="translate(-47 -109)" _fill="none" fill-rule="evenodd"><path pid="0" _fill="#525354" d="M49 124v-14h1v14z"/><g fill-rule="nonzero"><path pid="1" d="M52 115h5v-2h-5v2zm-1 0v-2a1 1 0 011-1h5a1 1 0 011 1v2a1 1 0 01-1 1h-5a1 1 0 01-1-1z" _fill="#525354"/><path pid="2" _fill="#B9BABD" d="M52 121v-2h8v2z"/><path pid="3" d="M51 121v-2a1 1 0 011-1h8a1 1 0 011 1v2a1 1 0 01-1 1h-8a1 1 0 01-1-1zm1 0h8v-2h-8v2z" _fill="#525354"/></g></g>'
  }
})
