/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/doms-align-center': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_doms-align-center_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_doms-align-center_a)" transform="translate(-126 -109)" _fill="none" fill-rule="evenodd"><path pid="0" _fill="#525354" d="M134.5 110v14h-1v-14z"/><g fill-rule="nonzero"><path pid="1" d="M136.857 113h-5.714v2h5.714v-2zm1.143 0v2c0 .552-.512 1-1.143 1h-5.714c-.631 0-1.143-.448-1.143-1v-2c0-.552.512-1 1.143-1h5.714c.631 0 1.143.448 1.143 1z" _fill="#525354"/><path pid="2" _fill="#FFF" d="M137 113v2h-6v-2z"/><g><path pid="3" _fill="#B9BABD" d="M139 119v2h-10v-2z"/><path pid="4" d="M140 119v2a1 1 0 01-1 1h-10a1 1 0 01-1-1v-2a1 1 0 011-1h10a1 1 0 011 1zm-1 0h-10v2h10v-2z" _fill="#525354"/></g></g></g>'
  }
})
