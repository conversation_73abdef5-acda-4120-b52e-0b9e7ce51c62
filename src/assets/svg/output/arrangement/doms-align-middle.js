/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/doms-align-middle': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.4%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_doms-align-middle_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_doms-align-middle_a)" transform="translate(-126 -147)" _fill="none" fill-rule="evenodd"><path pid="0" _fill="#525354" d="M141 154.5h-14v1h14z"/><g fill-rule="nonzero"><path pid="1" d="M138 152.143v5.714h-2v-5.714h2zm0-1.143h-2c-.552 0-1 .512-1 1.143v5.714c0 .631.448 1.143 1 1.143h2c.552 0 1-.512 1-1.143v-5.714c0-.631-.448-1.143-1-1.143z" _fill="#525354"/><path pid="2" _fill="#FFF" d="M138 152h-2v6h2z"/><g><path pid="3" _fill="#B9BABD" d="M132 149h-2v12h2z"/><path pid="4" d="M132 148h-2a1 1 0 00-1 1v12a1 1 0 001 1h2a1 1 0 001-1v-12a1 1 0 00-1-1zm0 1v12h-2v-12h2z" _fill="#525354"/></g></g></g>'
  }
})
