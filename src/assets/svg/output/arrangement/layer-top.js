/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/layer-top': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_layer-top_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_layer-top_a)" transform="translate(-16 -41)" fill-rule="nonzero" _fill="none"><path pid="0" d="M30.716 51.78a.5.5 0 11.356.934l-6.613 2.524a1.149 1.149 0 01-.93.011l-6.64-2.535a.5.5 0 01.356-.934l6.667 2.546c.043.02.093.02.163-.01l6.64-2.536z" _fill="#525354"/><path pid="1" d="M30.716 48.78a.5.5 0 11.356.934l-6.613 2.524a1.149 1.149 0 01-.93.011l-6.64-2.535a.5.5 0 01.356-.934l6.667 2.546c.043.02.093.02.163-.01l6.64-2.536z" _fill="#525354"/><path pid="2" _fill="#BCBCBC" d="M16.992 45.333l6.561-2.775 7.206 2.775-6.883 2.81z"/><path pid="3" d="M17.965 45.515l5.566 2.2 6.238-2.2-6.238-2.199-5.566 2.2zm5.78-3.183l7.248 2.554a.667.667 0 010 1.258l-7.247 2.555a.667.667 0 01-.467-.009l-6.466-2.555a.667.667 0 010-1.24l6.466-2.555a.667.667 0 01.467-.008z" _fill="#525354"/></g>'
  }
})
