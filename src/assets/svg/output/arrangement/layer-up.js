/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'arrangement/layer-up': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_arrangement_layer-up_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_arrangement_layer-up_a)" transform="translate(-145 -41)" _fill="none" fill-rule="evenodd"><g fill-rule="nonzero"><path pid="0" _fill="#BCBCBC" d="M145.881 50.989l7.119 4.12 7-4.12-7-4.736z"/><path pid="1" d="M152.968 46.612l-6.404 4.338 6.404 3.822 6.404-3.822-6.404-4.338zm.374-.955l7.025 4.759a.667.667 0 01-.032 1.124l-7.025 4.193a.667.667 0 01-.684 0l-7.025-4.193a.667.667 0 01-.032-1.124l7.025-4.759a.667.667 0 01.748 0z" _fill="#525354"/><path pid="2" _fill="#FFF" d="M145.978 45.658l4.788-3 4.698 3-4.698 3.089z"/><path pid="3" d="M150.657 43.103l-4.034 2.554 4.034 2.553 4.034-2.553-4.034-2.554zm.428-.912l4.407 2.79a.8.8 0 010 1.352l-4.407 2.79a.8.8 0 01-.856 0l-4.408-2.79a.8.8 0 010-1.352l4.408-2.79a.8.8 0 01.856 0z" _fill="#525354"/><g _stroke="#00B266" stroke-linecap="round" stroke-linejoin="round"><path pid="4" d="M157.942 46.819v-4.152M156.079 43.558l1.882-1.334 1.882 1.334"/></g></g></g>'
  }
})
