/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'border/border-groove': {
    width: 24,
    height: 24,
    viewBox: '0 0 24 24',
    data: '<g transform="translate(-20 -8)" fill-rule="nonzero" _fill="none"><rect pid="0" _stroke="#F5F5F5" _fill="#FFF" x=".5" y=".5" width="63" height="39" rx="2"/><path pid="1" _fill="#F3F3F7" d="M21 13h22v14H21z"/><path pid="2" _fill="#D7D8DB" d="M21 25h22v2H21z"/><path pid="3" _fill="#525354" d="M21 23h22v2H21z"/><path pid="4" _fill="#D7D8DB" d="M41 12h2v15h-2z"/><path pid="5" _fill="#525354" d="M39 12h2v12h-2z"/><path pid="6" _fill="#525354" d="M21 12h22l-2 2H21z"/><path pid="7" _fill="#525354" d="M21 12h2v13l-2 2z"/></g>'
  }
})
