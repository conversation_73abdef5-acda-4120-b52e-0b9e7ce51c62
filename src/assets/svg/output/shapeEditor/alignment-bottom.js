/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'shapeEditor/alignment-bottom': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<defs><filter x="-.9%" y="-.3%" width="101.9%" height="100.7%" filterUnits="objectBoundingBox" id="svgicon_shapeEditor_alignment-bottom_a"><feOffset dx="-1" in="SourceAlpha" result="shadowOffsetOuter1"/><feColorMatrix values="0 0 0 0 0.882352941 0 0 0 0 0.894117647 0 0 0 0 0.921568627 0 0 0 1 0" in="shadowOffsetOuter1" result="shadowMatrixOuter1"/><feMerge><feMergeNode in="shadowMatrixOuter1"/><feMergeNode in="SourceGraphic"/></feMerge></filter></defs><g filter="url(#svgicon_shapeEditor_alignment-bottom_a)" transform="translate(-207 -262)" _fill="none" fill-rule="evenodd"><path pid="0" d="M215.538 271.736l2.083-2.089a.5.5 0 01.708.706l-2.99 3a.5.5 0 01-.71 0l-2.99-3a.5.5 0 01.709-.706l2.19 2.198V264.5a.5.5 0 111 0v7.236zm-4.985 3.794a.5.5 0 110-1h9a.5.5 0 110 1h-9z" _fill="#525354" fill-rule="nonzero"/></g>'
  }
})
