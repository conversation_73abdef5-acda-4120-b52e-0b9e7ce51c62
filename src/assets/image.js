/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'style/image': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<g id="控件" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">< g id="切图/ic-editor-picture" stroke="#525354" stroke- width="1.2"><g id="编组" transform="translate(2.000000, 2.000000)"><rect id="矩形" x="0.6" y="0.6" width="10.8" height="10.8" rx="2"></rect><path d="M0.891658344,11.0025121 L1.71869666,10.2935118 L6.38396895,6.26904569 C7.17713458,5.58482664 8.36386256,5.62852367 9.10456068,6.3692218 L11.2963151,8.56097622 L11.2963151,8.56097622" id="路径"></path><circle id="椭圆形" cx="4" cy="4" r="1"></circle></g></></g > '
  }
})
