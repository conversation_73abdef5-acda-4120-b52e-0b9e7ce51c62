<template>
  <el-form-item
    :label="config.label"
    :required="config.props ? config.props.required : false"
    class="q-form-item"
    :prop="config.key"
    :rules="rules">
    <el-input
      class="q-pure-input"
      :type="config.type"
      autocomplete="off"
      v-model="form[config.key]"
      :placeholder="config.props.placeholder"
      :maxlength="config.type === 'textarea' ? config.props.max : undefined"
      :show-word-limit="config.type === 'textarea' ? true : false"
    >
      <span class="el-input__count" slot="suffix">
        <span class="el-input__count-inner">
          {{(form[config.key] || '').length}}/{{getMax}}
        </span>
      </span>
    </el-input>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ConfigItem, Form } from '../index';
@Component
export default class QInput extends Vue {
  @Prop({
    required: true
  })
  config!: ConfigItem;

  @Prop({
    required: true
  })
  form!: Form;

  get getMax() {
    return this.config.props ? this.config.props.max : 0
  }

  rules = [
    { required: true, message: `请输入${this.config.label}`, trigger: 'change'},
    { required: true, message: `请输入${this.config.label}`, trigger: 'blur'},
    { type: 'string', message: `请输入${this.config.label}`, trigger: 'change'},
    { type: 'string', message: `请输入${this.config.label}`, trigger: 'blur'},
    { max: this.getMax, message: `${this.config.label}不得超过${this.getMax}字符`, trigger: 'blur'},
    { max: this.getMax, message: `${this.config.label}不得超过${this.getMax}字符`, trigger: 'change'}
  ]
}
</script>
<style lang="less">
.q-pure-input {
  input {
    padding-right: 66px !important;
  }
}
</style>
