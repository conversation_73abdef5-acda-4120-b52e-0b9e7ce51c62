<template>
  <el-form-item :label="config.label" :required="config.props ? config.props.required : false" class="q-form-item q-image-select-array" :prop="config.key" :rules="rules">
    <div class="q-image-select-wrapper">
      <div class="image-select-wrapper" :class="{ hasSrc: !!src }" v-for="(src, index) in srcArray" :key="`${index}-${src}`">
        <div class="image-select">
          <div class="image-container">
            <div class="edit-btn-container">
              <el-tooltip class="item" effect="dark" content="替换图片" placement="top-start">
                <em @click="onClickReplace(index)">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-icontihuantupian"></use>
                  </svg>
                </em>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="裁剪" placement="top-start">
                <em @click="onClickCrop(index)">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-iconcaijian"></use>
                  </svg>
                </em>
              </el-tooltip>
              <!-- <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
              <em @click="onClickDelete(index)">
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-icondelete-o"></use>
                </svg>
              </em>
            </el-tooltip> -->
              <el-tooltip class="item" effect="dark" content="下载" placement="top-start">
                <em>
                  <svg class="cocosicon" aria-hidden="true" @click="onClickDownload(index)">
                    <use xlink:href="#cocos-icondownload3"></use>
                  </svg>
                </em>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="去背景" placement="top-start">
                <em @click="onClickRemove(index)">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-iconeliminate"></use>
                  </svg>
                </em>
              </el-tooltip>
            </div>
            <el-button type="text" class="image-del">
              <em @click="onClickDelete">
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-icona-shanchuyoushang"></use>
                </svg>
              </em>
            </el-button>
            <img class="image" :src="src" crossOrigin="Anonymous" />
          </div>
          <!-- <i data-v-d9b6e6a8="" class="el-icon-error" @click="onClickDelete(index)"></i> -->
        </div>
      </div>
    </div>
    <div v-if="srcArray.length < config.props.min" class="add-btn-wrapper">
      <div class="add-btn" @click="onClickReplace(srcArray.length)">
        <i class="el-icon-plus"></i>
      </div>
    </div>
    <div class="image-tips">小提示：倒计时图片请按照3 2 1顺序上传</div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import ImageCropper from "@/components/ImageCropper/index";
import bus from "@/pages/index/common/utils/bus";
import { ConfigItem, Form } from "../index";
import { image2Canvas } from "@/common/utils/resizePic";
import ImgBgRemover from "@/components/ImgBgRemoveView";

@Component
export default class QImageSelectArray extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  @Prop({})
  maxWidth!: number;

  @Prop({})
  maxHeight!: number;

  @Prop({
    required: false,
    default: true,
  })
  isShowDeleteBtn!: boolean;

  @Prop()
  componentId!: number;

  // 是否从编辑器插入
  @Prop({
    required: false,
  })
  isEditorAdd!: boolean;

  isRemovingBg = false;

  get srcArray() {
    return this.form[this.config.key];
  }

  get getMax() {
    return this.config.itemProps ? this.config.itemProps.max : 0;
  }

  get maxLimit() {
    return this.config.props.max;
  }

  get minLimit() {
    return this.config.props.min;
  }

  checkSrcs = async (...params: any) => {
    const value = params[1];
    const callback = params[2];
    console.log("value", params, this.maxLimit, this.minLimit);
    if (value && value.length < this.config.props.min && this.config.props.required) {
      // pm 文案
      return callback(new Error(`请添加不少于${this.config.props.min}张${this.config.label}图片`));
    }
    return callback();
  };

  rules = [{ validator: this.checkSrcs, trigger: "change" }];

  onClickReplace(index?: number) {
    if (!(window as MyWindow).materialLibraryReady) {
      this.$message.info("素材库正在加载...，请稍后再试");
      return;
    }
    console.log("onClickReplace");
    bus.$emit("resourceLibraryToggle", true);
    bus.$emit("material-show");
    ImageLibrary({
      onClose: () => {
        bus.$emit("material-close");
        bus.$emit("resourceLibraryToggle", false);
      },
      onConfirm: images => {
        console.log("ImageLibrary-images", images);
        this.$set(this.srcArray, typeof index === "undefined" ? this.srcArray.length : index, images[0].url);
        bus.$emit("resourceLibraryToggle", false);
        this.$emit("itemValidate", this.config.key);
      },
      maxWidth: this.maxWidth,
      maxHeight: this.maxHeight,
    });
  }

  onClickCrop(index: number) {
    bus.$emit("material-show");
    ImageCropper({
      src: this.srcArray[index],
      onClose: () => {
        bus.$emit("material-close");
        bus.$emit("resourceLibraryToggle", false);
      },
      onConfirm: url => {
        this.$set(this.srcArray, index, url);
        bus.$emit("resourceLibraryToggle", false);
      },
    });
  }

  onClickDelete(index: number) {
    this.srcArray.splice(index, 1);
    this.$emit("itemValidate", this.config.key);
  }
  onClickDownload(index: number) {
    // 根据图片地址下载图片到本地
    const img = this.$el.querySelectorAll("img.image")[index];
    const canvas = image2Canvas(img, 1280, 960, 2);
    console.log("img", img, canvas);
    canvas.toBlob(blob => {
      const url = URL.createObjectURL(blob);
      const Link = document.createElement("a");
      Link.download = `tihu-${new Date().getTime()}.png`;
      Link.href = url;
      Link.click();
      Link.remove();
      // 用完释放URL对象
      URL.revokeObjectURL(url);
    });
  }
  get containerWidth() {
    return this.$store.state.containerWidth;
  }

  onClickRemove(index: number) {
    if (this.isRemovingBg) return;
    this.isRemovingBg = true;
    bus.$emit("material-show");
    ImgBgRemover({
      onSuccess: (url: string) => {
        this.$set(this.srcArray, index, url);
        this.isRemovingBg = false;
        bus.$emit("material-close");
      },
      onCancel: () => {
        this.isRemovingBg = false;
        bus.$emit("material-close");
      },
      props: {
        rotate: 0,
        opacity: 1,
        left: 0,
        top: 0,
        scale: Math.round((this.containerWidth / 1280) * 10000) / 10000,
        src: this.srcArray[index],
        isComponent: false,
      },
      parent: ".canvas-container",
    });
  }
}
</script>

<style lang="less" scoped>
.q-image-select-array {
  margin-bottom: 30px;
  /deep/ .el-form-item__error {
    padding-top: 14px;
  }
}
.q-image-select-wrapper {
  display: flex;
  margin-bottom: 20px;
  height: 120px;
}
.image-tips {
  left: 0px;
  position: absolute;
  bottom: -20px;
}
.is-error .image-tips {
  // display: none;
}
.is-error .q-image-select-wrapper {
  // margin-bottom: 0;
}

.image-select-wrapper.hasSrc {
  padding: 10px;
  width: 100px;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: center;
  border-radius: 5px;
  margin-right: 10px;
}
.image-select {
  position: relative;
  width: 100px;
  display: flex;
  justify-content: center;
  background: url("~@/assets/img/transparent.png") repeat;
  background-size: 10px;
  &:hover {
    .edit-btn-container {
      bottom: 0;
      transform: translate(0, 0);
    }
    .image-del {
      display: block;
    }
  }
}

.image-select {
  height: 100px;
  .image-container {
    user-select: none;
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;
    &:hover {
      .edit-btn-container {
        bottom: 0;
        transform: translate(0, 0);
      }
      .image-del {
        display: block;
      }
    }

    .delete-btn {
      position: absolute;
    }

    .edit-btn-container {
      box-sizing: border-box;
      line-height: 14px;
      font-size: 14px;
      width: 100%;
      position: absolute;
      bottom: 0;
      display: flex;
      transition: all 0.2s linear;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
      justify-content: space-around;
      max-width: 100%;
      flex-wrap: wrap;
      transform: translate(0, 100%);
      em {
        font-size: 16px;
        color: #fff;
        padding: 2px;
        cursor: pointer;
      }
      &:hover {
        bottom: 0;
      }
    }
    .image-del {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 0;
      height: 20px;
      display: none;
      transition: all 0.2s linear;
      opacity: 0.6;
      span {
        font-size: 20px;
      }
      svg {
        font-size: 24px;
      }
    }
    .image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

.add-btn {
  border: 1px dashed #dcdfe6;
  display: flex;
  font-size: 16px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 120px;
  height: 120px;
  border-radius: 5px;
  transition: all 0.1s linear;

  &:hover {
    border-color: #409eff;
  }
}
</style>
