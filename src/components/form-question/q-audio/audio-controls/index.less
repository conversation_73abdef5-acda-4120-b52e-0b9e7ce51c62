.audio-controls-container {
  width: 329px;
  height: 28px;
  background: rgba(242,244,247,1);
  border-radius: 2px;
  padding: 6px 8px;
  box-sizing: border-box;
  font-size: 0;
  .control-play-button {
    border: 0px;
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: top;
    margin-right: 14px;
    background-image: url('./resource/to-play.png');
    background-size: 11px 12px;
    background-position: 2.5px 2px;
    background-repeat: no-repeat;
    background-color: transparent;
    &.state-playing {
      background-image: url('./resource/to-pause.png');
      background-size: 16px 16px;
      background-position: 0 0;
    }
  }
  .control-progress {
    display: inline-block;
    vertical-align: top;
    width: 157px;
    height: 10px;
    margin: 3px 10px 3px 0;
    /deep/ .el-slider__runway {
      margin: 3px 0 !important;
      height: 4px;
    }
    /deep/ .el-slider__bar {
      height: 4px;
    }
    /deep/ .el-slider__button-wrapper {
      top: -3px;
    }
  }
  .control-time-container {
    display: inline-block;
    vertical-align: top;
    height: 16px;
    line-height: 16px;
    font-size: 12px;
    margin-right: 16px;
  }
  .control-volume-container {
    display: inline-block;
    vertical-align: top;
    width: 16px;
    height: 16px;
    position: relative;
    background-image: url('./resource/volume-static.png');
    &:hover {
      background-image: url('./resource/volume-active.png');
    }
    .volume-slider-container {
      box-sizing: border-box;
      position: absolute;
      bottom: 26px;
      left: -6px;
      width: 28px;
      height: 80px;
      padding: 10px 9px;
      box-shadow: 1px 1px 8px 0px rgba(131,134,143,0.2);
      border-radius: 4px;
      border: 1px solid rgba(230,235,245,1);
      background: #fff;
      .control-volume {
        width: 10px;
        height: 60px;
        /deep/ .el-slider__runway {
          width: 4px;
          margin: 0 3px;
        }
        /deep/ .el-slider__bar {
          width: 4px;
        }
        /deep/ .el-slider__button-wrapper {
          left: -3px;
        }
      }
      &::after {
        content: "";
        position: absolute;
        bottom: -26px;
        left: 0;
        top: 0;
        right: 0;
      }
    }
  }
  .el-slider {
    /deep/ .el-slider__runway {
      border-radius: 4px;
      background-color: #E1E2E6;
      margin: 3px 0 !important;
    }
    /deep/ .el-slider__bar {
      border-radius: 4px;
      background-color: #42C56A;
    }
    /deep/ .el-slider__button-wrapper {
      width: 10px;
      height: 10px;
      .el-slider__button {
        width: 10px;
        height: 10px;
        border: none;
        background-color: #42C56A;
      }
    }
  }
}