import { Vue, Component, Prop } from 'vue-property-decorator';
import './index.less';

@Component
export default class AudioControls extends Vue {
  @Prop() public audioUrl!: string;
  public progress = 0;
  public playing = false;
  public currentTime = 0;
  public duration = 0;
  public showVolumeControl = false;
  public volume = 100;
  public togglePlaying(e: MouseEvent) {
    e.preventDefault();
    e.cancelBubble = true;
    this.playing ? this.pause() : this.play();
    this.playing = !this.playing;
  }
  public play() {
    (this.$refs.audio as any).play();
  }
  public pause() {
    (this.$refs.audio as any).pause();
  }
  public handleLoadedmetadata(res: any) {
    this.duration = res.target.duration;
    this.$emit('loaded', this.duration)
    // 默认音量最高
    this.handleVolumeSliderChange(100);
  }
  public handleTimeupdate(res: any) {
    this.currentTime = res.target.currentTime;
    this.progress = this.currentTime / this.duration * 100;
  }
  public formatTime(seconds: number) {
    const hours = Math.floor(seconds / 3600);
    let restSeconds = seconds - hours * 3600;
    const minutes = Math.floor(restSeconds / 60);
    restSeconds = Math.floor(restSeconds - minutes * 60);
    const formattedTime = `${(minutes + '').padStart(2, '0')}:${(restSeconds + '').padStart(2, '0')}`;
    if (hours) {
      return `${hours}:${formattedTime}`;
    }
    return formattedTime;
  }
  public handleProgressSliderChange(newProgress: number) {
    (this.$refs.audio as any).currentTime = this.duration * newProgress / 100;
  }
  public handleVolumeSliderChange(newVolume: number) {
    (this.$refs.audio as any).volume = newVolume / 100;
  }
  // 播放完改变播放状态
  public handleAudioPlayEnd() {
    this.playing = false;
  }
  public render() {
    return (
      <div class="audio-controls-container">
        <div
          class={`control-play-button ${this.playing ? 'state-playing' : ''}`}
          onClick={this.togglePlaying}
        ></div>
        <el-slider
          class="control-progress"
          v-model={this.progress}
          show-tooltip={false}
          onChange={this.handleProgressSliderChange}
        ></el-slider>
        <div class="control-time-container">
          <span>{this.formatTime(this.currentTime)} </span>/<span> {this.formatTime(this.duration)}</span>
        </div>
        <div
          class="control-volume-container"
          onMouseover={() => { this.showVolumeControl = true; }}
          onMouseout={() => { this.showVolumeControl = false; }}
        >
          <div class="volume-slider-container" v-show={this.showVolumeControl}>
            <el-slider
              class="control-volume"
              vertical
              height="60px"
              v-model={this.volume}
              show-tooltip={false}
              onInput={this.handleVolumeSliderChange}
            ></el-slider>
          </div>
        </div>
        <audio
          ref="audio"
          src={this.audioUrl}
          onLoadedmetadata={this.handleLoadedmetadata}
          onTimeupdate={this.handleTimeupdate}
          onEnded={this.handleAudioPlayEnd}
        ></audio>
      </div>
    );
  }
}
