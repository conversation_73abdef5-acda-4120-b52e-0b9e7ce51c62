<template>
  <el-form-item class="audio-container" :label="config.label" :prop="config.key" :required="config.props && typeof config.props.required !== 'undefined' ? config.props.required : true" :rules="rules">
    <div class="no-mp3" v-if="!form[config.key].url">
      <el-upload :show-file-list="false" :http-request="request" action="" accept=".mp3" :before-upload="beforeUpload" :on-success="onSuccess" :on-error="handleUploadError" class="audio-uploader">
        <el-button size="small" type="primary" class="upload-handler"><img src="~@/assets/img/music-outline.png" />上传音频</el-button>
      </el-upload>
      <span class="tip">{{ config.props.afterfix || "不超过600kb的mp3文件" }}</span>
    </div>
    <div>
      <div class="has-mp3" v-if="form[config.key].url">
        <span class="mp3-name">{{ form[config.key].name }}</span>
        <i class="el-icon-success"></i>
        <i class="el-icon-error" @click="handleDelete()"></i>
      </div>

      <AudioControls v-if="form[config.key].url" class="audio-control" :audioUrl="form[config.key].url" @loaded="duration => (form[config.key].duration = duration)"></AudioControls>
    </div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import APIS from "@/common/api/constants";
import AudioControls from "./audio-controls";
import { requestPhpServer } from "@/common/utils/request";
import { ConfigItem, Form } from "../index";

@Component({
  components: {
    AudioControls,
  },
})
export default class QAudio extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  fileName = "";

  async request(params: { file: File }) {
    const { file } = params;
    const formData = new FormData();
    formData.append("file", file);
    try {
      const uploadFileRes = await requestPhpServer.post(APIS.PHP_UPLOAD_AUDIO, formData, {
        headers: {
          "content-type": "multipart/form-data",
        },
      });
      return uploadFileRes;
    } catch (err) {
      this.$message.error((err as Error).message);
      return Promise.reject();
    }
  }

  checkAudio = (...params: any) => {
    const value = params[1];
    const callback = params[2];
    // 判断是否非必填
    if (this.config.props && this.config.props.required === false) {
      return callback();
    }
    if (!value.url) {
      return callback(new Error(`请上传${this.config.label}`));
    }
    return callback();
  };

  rules = [{ validator: this.checkAudio, trigger: "change" }];

  beforeUpload(file: File) {
    const isType = file.type === "audio/mp3" || file.type === "audio/mpeg";
    const { size } = file;
    this.fileName = file.name;
    if (file.name.endsWith(".MP3")) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    // 获取文件的后缀名 并判断是否是小写
    const isLower = file.name.split(".")[1].toLowerCase() === file.name.split(".")[1];
    if (!isLower) {
      this.$message.error("文件后缀名必须小写，请修改后再上传");
      return false;
    }
    const maxSize = this.config.props.maxSize || "600KB";
    if (typeof maxSize !== "string") {
      console.error("maxSize 参数不对，请检查配置");
    }
    const base = maxSize.toUpperCase().indexOf("KB") > -1 ? 1024 : maxSize.toUpperCase().indexOf("MB") > -1 ? 1024 * 1024 : 1;

    // console.log('maxSize....', maxSize, size, size / base);

    const isOutOfSize = size / base > parseInt(maxSize, 10);
    if (isOutOfSize) {
      console.log(size);
      this.$message.error(`音频不能超过${maxSize} `);
      return false;
    }
    if (!isType) {
      this.$message.error("上传视频只支持mp3格式!");
    }
  }

  handleUploadError() {
    this.$message.error("该MP3音频存在问题，请使用其他音频");
  }
  onSuccess(res: any) {
    if (res.data.errNo === 0) {
      const url = res.data.data;
      // const { url } = res.data.data;
      this.form[this.config.key].url = url;
      this.form[this.config.key].name = this.fileName;
      this.fileName = "";
      this.$emit("itemValidate", this.config.key);
    } else {
      this.$message.error("该MP3音频存在问题，请使用其他音频");
    }
  }
  handleDelete() {
    this.form[this.config.key] = {
      name: "",
      url: "",
      duration: 0,
    };
    this.$emit("itemValidate", this.config.key);
  }
}
</script>

<style lang="less">
.audio-container {
  .no-mp3 {
    display: flex;
    .media-upload {
      display: inline-block;
      line-height: 28px;
    }
    .tip {
      margin-left: 16px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .has-mp3 {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    width: 326px;
    height: 28px;
    padding: 0 8px;
    vertical-align: middle;
    .mp3-name {
      display: inline-block;
      vertical-align: top;
      font-size: 12px;
      line-height: 28px;
      color: #555;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 280px;
    }
    .el-icon-success {
      color: #42c57a;
    }
    .el-icon-error {
      display: none;
      color: #95a1ad;
      cursor: pointer;
    }
    .el-input__count {
      line-height: 15px;
    }
    .el-icon-success,
    .el-icon-error {
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
    &:hover {
      border-radius: 2px;
      background: #f0f1f5;
      .el-icon-error {
        display: inline-block;
      }
      .el-icon-success {
        display: none;
      }
    }
  }
  .audio-control {
    margin-bottom: 10px;
  }
  .el-textarea {
    margin-bottom: 6px;
    /deep/ .el-input__count {
      line-height: 15px;
    }
  }
  .upload-handler {
    > span {
      display: flex;
      align-items: center;
    }
  }
}
</style>
