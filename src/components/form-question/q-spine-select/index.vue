<template>
  <el-form-item class="audio-container" :class="{ 'opacity-label': config.hideLabel }" :label="config.label" :prop="config.key" :required="true" :rules="rules">
    <div class="spine-wrapper">
      <div class="no-spine" v-if="!form[config.key].skeleton">
        <el-button size="small" type="primary" class="upload-handler" @click="handleSelect">添加动效</el-button>
      </div>
      <div class="has-spine" v-if="form[config.key].cover">
        <!-- 替换 删除 pm 1205.todo -->
        <div class="spine-cover"><img :src="form[config.key].cover" /></div>
        <i class="el-icon-error" @click="handleDelete()"></i>
      </div>
      <div class="is-required" style="margin-left: -110px;" v-if="form[config.key].skeleton">
        <label class="el-form-item__label" style="width: 110px;">播放队列</label>
        <el-select size="small" placeholder="" v-model="form[config.key].animation">
          <el-option v-for="item in animationOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ConfigItem, Form } from "../index";

@Component({
  components: {
    // AudioControls
  },
})
export default class QAudioSelect extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  animationOptions: Array<{ label: string; value: string }> = [];

  isFetching = false;

  handleSelect() {
    // bus.$emit("resourceLibraryToggle", true);
    this.$getPageConfigByKey("SpineLibrary")().then((comp: { SpineLibrary: (arg0: { isMultiple: boolean; classify: string; onClose: () => void; onConfirm: (spines: any) => void }) => void }) => {
      comp.SpineLibrary({
        isMultiple: true,
        classify: "spine",
        onClose: () => {
          // bus.$emit("resourceLibraryToggle", false);
        },
        onConfirm: spines => {
          console.log("xu-spines", spines);
          const [{ atlas, images, skeleton, cover }] = spines;
          this.form[this.config.key] = {
            atlas,
            images,
            skeleton,
            cover,
            animation: "", // 一个
          };
          this.fetchAnimation(skeleton);
          // this.$emit('itemValidate', this.config.key);
        },
      });
    });
  }

  checkAudio = (...params: any) => {
    const value = params[1];
    const callback = params[2];
    if (!value.skeleton) {
      return callback(new Error(`请添加${this.config.label}动效`));
    }
    if (!value.animation) {
      return callback(new Error(`请选择${this.config.label}播放队列`));
    }
    return callback();
  };

  rules = [{ validator: this.checkAudio, trigger: "change" }];

  handleDelete() {
    this.form[this.config.key] = {
      atlas: "",
      images: [],
      skeleton: "",
      cover: "",
      animation: "",
    };
    this.$emit("itemValidate", this.config.key);
  }

  fetchAnimation(url: string) {
    this.isFetching = true;
    return fetch(url)
      .then(res => res.json())
      .then(({ animations }) => {
        const animationsKeys = Object.keys(animations).map(ani => {
          return {
            label: ani,
            value: ani,
          };
        });
        this.animationOptions = animationsKeys;
      })
      .finally(() => {
        this.isFetching = false;
      });
  }
}
</script>

<style lang="less">
.audio-container {
  .no-spine {
    display: flex;
    .media-upload {
      display: inline-block;
      line-height: 28px;
    }
    .tip {
      margin-left: 16px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .has-spine {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    padding: 0 8px;
    vertical-align: middle;
    .spine-cover {
      img {
        width: 100px;
      }
    }
    .el-icon-success {
      color: #42c57a;
    }
    .el-icon-error {
      display: none;
      color: #95a1ad;
      cursor: pointer;
    }
    .el-input__count {
      line-height: 15px;
    }
    .el-icon-success,
    .el-icon-error {
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
    &:hover {
      border-radius: 2px;
      background: #f0f1f5;
      .el-icon-error {
        display: inline-block;
      }
      .el-icon-success {
        display: none;
      }
    }
  }
  .audio-control {
    margin-bottom: 10px;
  }
  .el-textarea {
    margin-bottom: 6px;
    /deep/ .el-input__count {
      line-height: 15px;
    }
  }
  .upload-handler {
    > span {
      display: flex;
      align-items: center;
    }
  }
}
.opacity-label label {
  opacity: 0 !important;
}
</style>
