<template>
  <div class="form-array">
    <el-form-item
      :label="`${config.label}${supportAdd ? index + 1 : ''}`"
      :required="true"
      class="q-form-item"
      :prop="`${config.key}.${index}`"
      :rules="rules"
      :key="index"
      v-for="(item, index) in form[config.key]">
       <div class="input-check-wapper">
          <div class="input-check-main">
            <el-input
              :type="config.itemProps && config.itemProps.type ? config.itemProps.type : 'text'"
              :rows="config.itemProps && config.itemProps.rows ? config.itemProps.rows : 2"
              autocomplete="off"
              v-model="item.value"
              :validate-event="true"
              :placeholder="config.itemProps && config.itemProps.placeholder ? config.itemProps.placeholder : ''"
            >
              <span class="el-input__count" slot="suffix">
                <span class="el-input__count-inner">
                  {{item.value.length}}/{{getMax}}
                </span>
              </span>
            </el-input>
            <span class="el-input__count textarea-count" v-if="config.itemProps && config.itemProps.type === 'textarea'">
              <span class="el-input__count-inner">
                {{item.value.length}}/{{getMax}}
              </span>
            </span>
            <span class="answer-all-icon" v-if="supportAdd">
              <em
                class="el-icon-plus"
                :class="{ disabled: form[config.key].length > maxLimit - 1 }"
                @click.prevent="changeLength('add', index)"
              ></em>
              <em
                class="el-icon-minus"
                :class="{ disabled: form[config.key].length < 2 }"
                @click.prevent="changeLength('delete', index)"
              ></em>
            </span>
          </div>
       </div>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { Form, ConfigItem } from '../index'

@Component
export default class QInputs extends Vue {
  @Prop({
    required: true
  })
  config!: ConfigItem;

  @Prop({
    required: true
  })
  form!: Form;

  get getMax() {
    return this.config.itemProps ? this.config.itemProps.max : 0
  }

  get supportAdd() {
    return this.config.props.max > 1
  }

  get maxLimit() {
    return this.config.props.max
  }

  get content() {
    return this.form[this.config.key];
  }

  changeWordsCheck = async (...params: any) => {
    const value = params[1];
    const callback = params[2];
    const index = Number(params[0].field.slice(-1));
    if (!value.value.trim().length) {
      this.content[index].error = [];
      return callback(new Error(`请输入${this.config.label}`));
    }
    return callback();
  };

  checkWords = async (...params: any) => {
    const value = params[1];
    const callback = params[2];
    const index = Number(params[0].field.slice(-1));
    const checkValue = value.value.replace(/(\s*$)/g,""); // 去除右侧空格
    if (!checkValue.length) {
      this.content[index].error = [];
      return callback(new Error(`请输入${this.config.label}`));
    }
    if (checkValue.length > this.getMax) {
      this.content[index].error = [];
      return callback(new Error(`${this.config.label}不得超过${this.getMax}个字符`));
    }
    return callback();
  };

  rules = [
    { validator: this.changeWordsCheck, trigger: 'change'  },
    { validator: this.checkWords, trigger: 'blur'  }
  ]

  changeLength(type: 'add' | 'delete', index: number) {
    if(type === 'delete' && this.content.length > 1) {
      this.content.splice(index, 1);
    }
    if(type === 'add' && this.content.length < 4) {
      this.content.splice(index + 1, 0, { value: '', error: '' });
    }    
  }

  created() {
    this.content.forEach((item: any) => {
      item.error = [];
    });
  }
}
</script>
<style lang="less">
.form-array {
  .el-form-item {
    display: flex;
  }
  .q-form-item.el-form-item.is-success .el-input__inner {
    border-color: #e1e2e6;
  }
  .q-form-item.el-form-item.is-success .el-textarea__inner {
    border-color: #e1e2e6;
  }
  .input-check-wapper {
    width: 100%;
  }
  .input-check-main {
    display: flex;
    position: relative;
    .textarea-count {
      position: absolute;
      bottom: -12px;
      right: 20px;
      color: #999;
      font-size: 12px;
    }
    input {
      padding-right: 66px !important;
    }
  }
  .el-form-item__content {
    flex: 1;
  }
  .error-list-wrapper {
    position: relative;
  }
  .words-error-wrapper {
    position: static;
  }
  .tips-wrapper {
    padding-left: 20px;
    background: url("~@/assets/img/tips_bg.png") no-repeat;
    background-size: 16px;
    background-position: 0px 11px;
    text-align: left;
  }
}
</style>
