<template>
  <div class="question-form-panel">
    <el-form
      :model="formData"
      label-position="right"
      label-width="110px"
      ref="form"
      
      class="form-question-wrapper">
      <div :key="item.key" v-for="(item) in formItemConfigs">
          <template v-if="['text', 'textarea'].includes(item.type) && item.props.hide !== true">
            <QInput :config="item" :form="formData" :class="{ hide: item.props.hide }"></QInput>
          </template>
          <template v-if="item.type === 'select'">
            <QSelect :config="item" :form="formData"></QSelect>
          </template>
          <template v-if="item.type === 'audio'">
            <QAudioSelect
              :config="item"
              :form="formData"
              @itemValidate="itemValidate"></QAudioSelect>
          </template>
          <template v-if="item.type === 'img'">
            <QImageSelect
              :config="item"
              :form="formData"
              :isShowDeleteBtn="false"
              @itemValidate="itemValidate"
            />
          </template>
          <template v-if="item.type === 'img-array'">
            <QImageSelectArray
              :config="item"
              :form="formData"
              :isShowDeleteBtn="false"
              @itemValidate="itemValidate"
            />
          </template>
          <template v-if="item.type === 'spine'">
            <QSpineSelect
              :config="item"
              :form="formData"
              @itemValidate="itemValidate"
            />
          </template>
          <template v-if="item.type === 'text-array' && item.props.hide !== true">
            <QInputCheck :config="item" :form="formData" :class="{ hide: item.props.hide }"></QInputCheck>
          </template>
        </div>
    </el-form>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { CATEGORY } from '@/common/constants';
import QInput from '@/components/form-question/q-input/index.vue';
import QSelect from '@/components/form-question/q-select/index.vue';
import QAudioSelect from '@/components/form-question/q-audio-select/index.vue';
import QSpineSelect from '@/components/form-question/q-spine-select/index.vue';
import QTags from '@/components/form-question/q-tags/index.vue';
import QImageSelect from "@/components/form-question/q-image-select/index.vue";
import QImageSelectArray from "@/components/form-question/q-image-select-array/index.vue";
import QInputCheck from '@/components/form-question/q-input-check/index.vue';
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: {
    QInput,
    QSelect,
    QAudioSelect,
    QTags,
    QImageSelect,
    QImageSelectArray,
    QSpineSelect,
    QInputCheck
  }
})

export default class QuestionForm extends Vue {
  @Prop({
    required: true,
  })
  category!: CATEGORY;

  @Prop({
    required: true,
  })
  type!: 'question' | 'components';

  @Prop({
    required: true,
  })
  formData!: { [key: string]: any };

  @Prop({
    required: true,
  })
  formItemConfigs!: Array<{ [key: string]: any }>;

  handleAdd(index: number) {
    console.log('handleAdd', index);
  }

  itemValidate(key: string, cb?: any) {
    (this.$refs.form as any).validateField(key, (...params: any[]) =>  {
      console.log('validateField.params', params);
      cb && cb(params);
    });
  }

  validate(cb?: (valid: boolean, data: any) => any) {
    (this.$refs.form as any).validate((valid: boolean, data: any) => cb && cb(valid, data))
  }
  clearValidate() {
    (this.$refs.form as any).clearValidate()
  }

  mounted() {
    bus.$on("material-show", (val: boolean) => {
      console.log("xu-post-material-show", val);
    });
    // this.titleTags = this.formData.
  }
}
</script>

<style lang="less" scoped>
.question-form-panel {
  .tips {
    text-align: left;
  }
  .question-tag {
    margin: 16px 0px;
    display: flex;
    align-items: center;
  }
  .hide {
    display: none !important;
  }
}
</style>