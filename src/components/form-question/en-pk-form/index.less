.form-question-wrapper {
  box-sizing: border-box;
  padding: 0 40px;

  .el-form-item__content {
    display: flex;
  }

  .el-textarea__inner {
    resize: vertical !important;
    min-height: 50px !important;
  }

  .tox-tinymce {
    width: 100%;
  }

  .el-slider.is-vertical .el-slider__runway {
    margin: 0 2px !important;
  }

  .el-slider .el-slider__runway {
    margin: 3px 0 !important;
  }

  .answer-all-icon {
    width: 80px;

    .el-icon-plus {
      &:before {
        background: rgba(66, 197, 122, 1);
        color: #fff;
        border-radius: 50%;
        border: 2px solid rgba(66, 197, 122, 1);
        cursor: pointer;
      }
    }

    .el-icon-minus {
      &:before {
        background: rgba(66, 197, 122, 1);
        color: #fff;
        border-radius: 50%;
        border: 2px solid rgba(66, 197, 122, 1);
        cursor: pointer;
      }
    }

    em {
      width: 16px;
      height: 16px;
      margin-left: 16px;
    }

    .disabled {
      &:before {
        border: 2px solid rgba(175, 176, 179, 1) !important;
        background: rgba(175, 176, 179, 1) !important;
        cursor: not-allowed !important;
      }
    }
  }

  .words-error-wrapper.el-form-item__error {
    width: 450px;

    >div {
      text-align: left;
      line-height: 1.5;
    }

    .add-words {
      float: right;
      color: #42c57a;
      text-decoration: underline;
      cursor: pointer;
      position: absolute;
      right: 72px;
      bottom: 0;
    }
  }

  .q-form-item.is-error {
    .tox-tinymce {
      border-color: #fa574b !important;
    }
  }
}

.en-question-dialog {
  height: 80vh;

  .dialog-footer {
    margin-top: 24px;
  }

  &.gray::before {
    content: '';
    left: 0;
    bottom: 0;
    top: 0;
    right: 0;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1002;
  }
}

.tox-statusbar__text-container {
  display: none !important;
}

.mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
  font-size: 12px;
  color: #999;
}

#tinymce::before {
  font-size: 12px;
  color: #999;
}

.question-tag {
  margin: 16px 24px;
  padding-left: 16px;
  height: 56px;
  line-height: 56px;
  background: rgba(247, 248, 251, 1);

  .el-tag {
    width: 72px;
    height: 32px;
    margin-right: 8px;
    text-align: center;
    border-radius: 2px;
    font-weight: 400;
    color: rgba(85, 85, 85, 1);
    font-size: 12px;
    border: 1px solid rgba(235, 236, 240, 1);
    background: rgba(255, 255, 255, 1);
    line-height: 32px;

    &.active {
      background: rgba(66, 197, 122, 1);
      color: rgba(255, 255, 255, 1);
      // border: none;
    }
  }

  .button-new-tag {
    width: 96px;
    height: 32px;
    margin-left: 8px;
    background: rgba(255, 255, 255, 1);
    border-radius: 2px;
    color: rgba(66, 197, 122, 1);
    border: 1px solid rgba(66, 197, 122, 1);

    .el-tag__close {
      color: rgba(66, 197, 122, 1);
    }

    .el-tag__close:hover {
      color: #FFF;
      background-color: rgba(66, 197, 122, 1);
    }
  }
}

.question-tag {
  display: flex;
  align-items: center;
  margin: 16px 0px;
}

.tips {
  text-align: left;
}
