<template>
  <el-dialog
    title="PK题"
    :visible.sync="visible"
    width="880px"
    height="80vh"
    top="0vh"
    :custom-class="`en-question-dialog ${showGray ? 'gray' : ''}`"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-tabs v-model="activeName">
      <el-tab-pane v-for="tab in tabs" :key="tab.key" :label="tab.label" :name="tab.key">
        <!--  // ts类型 12.1todo  -->
        <div v-if="tab.key === 'components'">
          <div class="tips">注意：长按下方“小题1”等，可前后拖拽调整题目顺序</div>
          <QTags :titleTags="titleTags" :currentIndex="currentIndex" @select="handleSelect" @add="handleAdd" @delete="handleDelete" @drag="handleDrag"></QTags>
        </div>
        <QuestionForm
          v-if="(tab.key === 'components' && tab.form[currentIndex]) || (tab.form && Object.keys(tab.form).length)"
          :category="category"
          :formData="tab.key === 'components' ? tab.form[currentIndex] : tab.form"
          :type="tab.key"
          :formItemConfigs="tab.configs"
          :ref="`${tab.key}Form`"
        ></QuestionForm>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onDialogClose">取 消</el-button>
      <el-button type="primary" @click="onDialogConfirm" :loading="loading">
        插入题目
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { CATEGORY } from "@/common/constants";
import QInput from "@/components/form-question/q-input/index.vue";
import QSelect from "@/components/form-question/q-select/index.vue";
import QAudio from "@/components/form-question/q-audio/index.vue";
import QTags from "@/components/form-question/q-tags/index.vue";
import { enPKQuestion } from "@/components/form-question/config/enPKQuestion";
import { cloneDeep } from "lodash-es";
import { Config } from "./index";
import QuestionForm from "./questionForm.vue";
import { getComponentFormItemConfigs, getQuestionFormItemConfigs, getComponentModelData } from "@/common/utils/FormTemplateManager";
import { FORMDATATYPE } from "@/common/utils/FormTemplateManager/config";
import bus from "@/pages/index/common/utils/bus";
import { showErrorMessage } from "@/common/utils/showErrorMessage";

enum ActiveNames {
  QUESTION = "question",
  COMPONENTS = "components",
}
@Component({
  components: {
    QInput,
    QSelect,
    QAudio,
    QTags,
    QuestionForm,
  },
})
export default class ENPKQuestionForm extends Vue {
  @Prop({
    required: true,
  })
  category!: CATEGORY;

  config = (cloneDeep(enPKQuestion) as unknown) as Config;

  visible = false;

  loading = false;

  showGray = false;

  formDataType = this.category === CATEGORY.ENPK ? FORMDATATYPE.ENPK : FORMDATATYPE.ENGROUPPK;

  activeName = ActiveNames.COMPONENTS;

  tabs = [
    {
      label: "题组",
      key: "components",
      formDataType: this.formDataType,
      configs: getComponentFormItemConfigs(this.formDataType),
      form: [],
    },
    {
      label: "属性",
      key: "question",
      configs: getQuestionFormItemConfigs(this.category),
      form: {},
    },
  ];

  get ActiveNames() {
    return ActiveNames;
  }

  get formTemplateData() {
    return this.$store.state.extData.formTemplateData;
  }

  get components() {
    return this.tabs[0].form as any[];
  }

  get currSubQst() {
    return (this.tabs[0].form as any[])[this.currentIndex];
  }

  get question() {
    return this.tabs[1].form;
  }

  get titleTags() {
    return (this.tabs[0].form as any).map((_item: any, index: number) => {
      return {
        name: `小题${index + 1}`,
        id: index + 1, // 应该是componentId
      };
    });
  }

  currentIndex = 0;

  justChange = false;

  @Watch("currentIndex")
  currentIndexWacher() {
    this.justChange = true;
  }
  @Watch("visible")
  visibleWacher(val: boolean) {
    if (val) {
      // 点击编辑按钮进入时 需要重新赋值
      this.tabs[1].form = cloneDeep(this.formTemplateData.question);
      this.tabs[0].form = cloneDeep(this.formTemplateData.components);
      this.currentIndex = 0;
      this.activeName = ActiveNames.COMPONENTS;
    }
  }

  created() {
    this.tabs[1].configs = getQuestionFormItemConfigs(this.category);
  }

  async mounted() {
    bus.$on("resourceLibraryToggle", (val: boolean) => {
      this.showGray = val;
    });
    bus.$on("material-close", () => {
      this.showGray = false;
    });
  }

  onDialogClose() {
    this.visible = false;
    this.loading = false;
    this.currentIndex = 0;
    this.tabs[1].form = cloneDeep(this.formTemplateData.question);
    this.tabs[0].form = cloneDeep(this.formTemplateData.components);
    this.$nextTick(() => {
      this.$refs.componentsForm && (this.$refs.componentsForm as any)[0].clearValidate();
      this.$refs.questionForm && (this.$refs.questionForm as any)[0].clearValidate();
    });
    this.$emit("cancel", { category: this.category });
  }

  getCocosData() {
    const data: any = {
      question: [],
    };
    this.tabs[1].configs.forEach((config: { [x: string]: string }) => {
      if (config.type === "audio") {
        data[config.cocosKey] = (this.tabs[1].form as any)[config.key].url;
      } else {
        data[config.cocosKey] = (this.tabs[1].form as any)[config.key];
      }
    });
    data.question = (this.tabs[0].form as any).map((formItem: any) => {
      const tempItem: any = {};
      this.tabs[0].configs.forEach((config: any) => {
        if (config.key === "qeustionEN") {
          tempItem[config.cocosKey] = formItem[config.key][0].value;
        } else {
          tempItem[config.cocosKey] = formItem[config.key];
        }
      });
      return tempItem;
    });
    return data;
  }

  async onDialogConfirm() {
    // 校验数据
    this.$refs.componentsForm &&
      (this.$refs.componentsForm as any)[0].validate((valid: boolean) => {
        console.log("onDialogConfirm", valid);
        if (!valid) {
          showErrorMessage(new Error("题目信息有误，请修改后再插入"));
        } else {
          const validArrays: number[] = [];
          // 数据验证
          (this.components as any).forEach((comp: { [x: string]: any }, index: number) => {
            const configs = this.tabs[0].configs;
            configs.forEach((config: { key: any; type: string; props: { required: any; max?: number } }) => {
              const {
                key,
                type,
                props: { required, max },
              } = config;
              if (key === "qeustionEN") {
                if (required && type === "text-array" && comp[key][0].error.length) {
                  !validArrays.includes(index) && validArrays.push(index);
                }
              }
              if (required && !comp[key]) {
                !validArrays.includes(index) && validArrays.push(index);
              } else if (type === "text" && max && comp[key].length > max) {
                !validArrays.includes(index) && validArrays.push(index);
              }
            });
          });
          if (validArrays.length) {
            console.log("validArrays", validArrays);
            this.currentIndex = validArrays[0];
            // 切换tab
            this.activeName = ActiveNames.COMPONENTS;
            // 先判断当前的tab内容是否有错误 有的话 不切换 无的话切换到有问题的tab
            this.$nextTick(() => {
              this.$refs.componentsForm && (this.$refs.componentsForm as any)[0].validate();
            });
          }
          (this.$refs.questionForm as any)[0].validate((valid: boolean) => {
            if (!valid) {
              if (!validArrays.length) {
                this.activeName = ActiveNames.QUESTION;
              }
            }
            if (!valid || validArrays.length) {
              showErrorMessage(new Error("题目信息有误，请修改后再插入"));
            } else {
              // 返回内容
              this.visible = false;
              // 更新
              this.$store.commit("initFormTemplateData", {
                formTemplateData: {
                  components: this.components,
                  question: this.question,
                },
              });
              this.$emit("done", { category: this.category, data: this.getCocosData() });
            }
          });
        }
      });
  }

  handleSelect(index: number) {
    // 验证 切换
    const curQstform = [this.tabs[0].form as any][this.currentIndex];
    if (curQstform) {
      this.$refs.componentsForm &&
        (this.$refs.componentsForm as any)[0].validate((valid: any) => {
          console.log("handleSelect", valid);
          this.currentIndex = index;
          this.$nextTick(() => {
            this.$refs.componentsForm && (this.$refs.componentsForm as any)[0].clearValidate();
          });
        });
    } else {
      this.currentIndex = index;
      this.$nextTick(() => {
        this.$refs.componentsForm && (this.$refs.componentsForm as any)[0].clearValidate();
      });
    }
  }
  handleAdd(index: number) {
    console.log("handleAdd", index);
    // 新增
    (this.components as any).splice(index, 0, getComponentModelData(this.formDataType));
    this.handleSelect(index);
  }
  handleDelete(index: number) {
    (this.components as any).splice(index, 1);
    let selectIndex = this.currentIndex;
    if (this.currentIndex > index) {
      selectIndex = Math.max(Math.min(this.currentIndex - 1, this.titleTags.length - 1), 0);
    }
    if (selectIndex > this.titleTags.length - 1) {
      selectIndex = this.titleTags.length - 1;
    }
    this.handleSelect(selectIndex);
  }
  handleDrag(index: number, dropIndex: number) {
    [this.components[index], this.components[dropIndex]] = [this.components[dropIndex], this.components[index]];
    if (this.currentIndex === index) {
      this.currentIndex = dropIndex;
    } else if (this.currentIndex === dropIndex) {
      this.currentIndex = index;
    }
    this.handleSelect(this.currentIndex);
  }
}
</script>

<style lang="less">
.en-question-dialog {
  .form-array .el-form-item__content {
    margin-left: unset !important;
  }
}
</style>
