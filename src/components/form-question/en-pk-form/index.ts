export interface ConfigItemProps {
  tips?: string,
  max: number,
  type?: string,
  required?: boolean,
  afterfix?: string,
  options?: number[] | { label: string, value: string | number }[],
  optionType?: string,
  rows?: number,
  placeholder?: string
}

export interface ConfigItem {
  key: string,
  label: string,
  hideLabel?: boolean,
  type: string,
  cocosKey: string,
  props: ConfigItemProps,
  itemProps?: ConfigItemProps
}

export interface Form {
  questionStem: string,
  answerStart?: string,
  readTime: number,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  playCount: number,
  readyTime: number,
  answerTime: number,
  answers: Array<{ value: string, error: string }>,
  keywords: Array<{ value: string, error: string }>
}

export interface FollowWordsForm {
  uniqueId: number,
  words: Array<{ value: string, error: string }>,
  explains: Array<{ value: string, error: string }>,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  answerTime: number
}

export interface Config {
  title: string,
  configs: Array<ConfigItem>,
  form: FollowWordsForm
}

export interface ErrorItem {
  in_dict: number,
  word: string
}
import './index.less'
import FormQuestion from './index.vue'
import Vue from "vue";
import { CATEGORY } from '@/common/constants';
import store from "@/pages/index/store";

export const cache = new Set<string>();

export function open(component: Component) {
  const div = document.createElement("div");
  const el = document.createElement("div");

  const id = 'form-modal' + Math.random();
  div.id = id;
  div.appendChild(el);
  document.body.appendChild(div);

  const ComponentConstructor = Vue.extend(component);

  return (propsData = {}, parent = undefined) => {
    let instance = new ComponentConstructor({
      propsData,
      parent,
      store,
    }).$mount(el);

    const destroyFormModal = () => {
      if (cache.has(id)) return;
      if (instance && div.parentNode) {
        cache.add(id);
        (instance as any).visible = false;
        // 延时是为了在关闭动画执行完毕后卸载组件
        setTimeout(() => {
          cache.delete(id);
          instance.$destroy();
          // @ts-ignore
          instance = null;
          div.parentNode && div.parentNode.removeChild(div);
        }, 1000);
      }
    };

    // visible控制
    if ((instance as any).visible !== undefined) {
      // 支持sync/v-model
      instance.$watch("visible", (val) => {
        !val && destroyFormModal();
      });
      Vue.nextTick(() => ((instance as any).visible = true));
    }

    return new Promise((resolve, reject) => {
      // emit 一个 done 事件关闭
      instance.$once("done", (data: any) => {
        destroyFormModal();
        resolve(data);
      })
      // emit 一个 cancel 事件取消
      instance.$once("cancel", (data: any) => {
        destroyFormModal();
        reject(data);
      });
    });
  };
}

export default function enPkFormDialog(category: CATEGORY) {
  return open(FormQuestion)({ category });
}