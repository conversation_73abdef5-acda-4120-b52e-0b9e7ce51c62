<template>
  <el-form-item class="audio-select-container" :label="config.label" :class="{ 'opacity-label': config.hideLabel }" :prop="config.key" :required="true" :rules="rules">
    <div class="no-mp3" v-if="!form[config.key].url">
      <el-button size="small" type="primary" class="upload-handler" @click="handleSelect">添加音频</el-button>
      <!-- <span class="tip">不超过600kb的mp3文件</span> -->
    </div>
    <div>
      <div class="has-mp3" v-if="form[config.key].url">
        <span class="mp3-name">{{ form[config.key].name }}</span>
        <i class="el-icon-success"></i>
        <i class="el-icon-error" @click="handleDelete()"></i>
      </div>

      <AudioControls v-if="form[config.key].url" class="audio-control" :audioUrl="form[config.key].url" @loaded="duration => (form[config.key].duration = duration)"></AudioControls>
    </div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import AudioControls from "./audio-controls";
import { ConfigItem, Form } from "../index";
import bus from "@/pages/index/common/utils/bus";

@Component({
  components: {
    AudioControls,
  },
})
export default class QAudioSelect extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  fileName = "";

  async dynamicImport(modulePath: string) {
    const module = await import(modulePath);
    return module;
  }

  async handleSelect() {
    this.$getPageConfigByKey("AudioLibrary")().then((component: { AudioLibrary: any; }) => {
      const AudioLibrary = component.AudioLibrary;
      AudioLibrary({
        onClose: () => {
          bus.$emit("resourceLibraryToggle", false);
        },
        onConfirm: (audios: { name: string; url: string; duration: number}[]) => {
          this.form[this.config.key].url = audios[0].url;
          this.form[this.config.key].name = audios[0].name;
          bus.$emit("resourceLibraryToggle", false);
          this.$emit("itemValidate", this.config.key);
        },
      });
    });
  }

  checkAudio = (...params: any) => {
    const value = params[1];
    const callback = params[2];
    if (!value.url) {
      return callback(new Error(`请添加${this.config.label}音频`));
    }
    return callback();
  };

  rules = [{ validator: this.checkAudio, trigger: "change" }];

  handleDelete() {
    this.form[this.config.key] = {
      name: "",
      url: "",
      duration: 0,
    };
    this.$emit("itemValidate", this.config.key);
  }
}
</script>

<style lang="less">
.audio-select-container {
  .no-mp3 {
    display: flex;
    .media-upload {
      display: inline-block;
      line-height: 28px;
    }
    .tip {
      margin-left: 16px;
      color: rgba(153, 153, 153, 1);
    }
  }
  .has-mp3 {
    display: inline-block;
    position: relative;
    box-sizing: border-box;
    width: 326px;
    height: 28px;
    padding: 0px 8px;
    vertical-align: middle;
    .mp3-name {
      text-align: left;
      display: block;
      vertical-align: top;
      font-size: 12px;
      line-height: 28px;
      color: #555;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      width: 280px;
    }
    .el-icon-success {
      color: #42c57a;
    }
    .el-icon-error {
      display: none;
      color: #95a1ad;
      cursor: pointer;
    }
    .el-input__count {
      line-height: 15px;
    }
    .el-icon-success,
    .el-icon-error {
      position: absolute;
      right: 8px;
      top: 6px;
      font-size: 16px;
    }
    &:hover {
      border-radius: 2px;
      background: #f0f1f5;
      .el-icon-error {
        display: inline-block;
      }
      .el-icon-success {
        display: none;
      }
    }
  }
  .audio-control {
    margin-bottom: 10px;
  }
  .el-textarea {
    margin-bottom: 6px;
    /deep/ .el-input__count {
      line-height: 15px;
    }
  }
  .upload-handler {
    > span {
      display: flex;
      align-items: center;
    }
  }
}
.opacity-label label {
  opacity: 0 !important;
}
</style>
