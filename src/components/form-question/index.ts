export interface ConfigItemProps {
[x: string]: any
  tips?: string,
  max: number,
  type?: string,
  required?: boolean,
  afterfix?: string,
  options?: number[] | { label: string, value: string | number }[],
  optionType?: string,
  rows?: number,
  placeholder?: string
}

export interface ConfigItem {
   key: string,
   label: string,
   hideLabel?: boolean,
   type: string,
   cocosKey: string,
   props: ConfigItemProps,
   itemProps?: ConfigItemProps
}

export interface Form {
  questionStem: string,
  answerStart?: string,
  readTime: number,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  playCount: number,
  readyTime: number,
  answerTime: number,
  answers: Array<{ value: string, error: string }>,
  keywords: Array<{ value: string, error: string }>
}

export interface Config {
  title: string,
  configs: Array<ConfigItem>,
  form: Form
}

export interface ErrorItem {
  in_dict: 0 | 1, // in_dict 0（在词表中） 、1（不在词表中）
  word: string
}
import './index.less'
import FormQuestion from './index.vue'

export default FormQuestion
