export const loudReadingQuestion = {
  title: '朗读短文',
  configs: [
    {
      key: 'readyTime',
      label: '准备时间',
      type: 'select',
      cocosKey: 'readyTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (_v, i) => (i+1)),
        max: 120,
        required: true,
      }
    },
    {
      key: 'answers',
      type: 'text-array',
      label: '朗读文本',
      cocosKey: 'questionTitle',
      props: {
        min: 1,
        max: 1,
      },
      itemProps: {
        type: 'textarea',
        required: true,
        max: 1000,
        rows: 12,
        placeholder: '请输入朗读文本内容，最多不超过1000个字符',
      }
    },
    {
      key: 'answerTime',
      label: '答题时间',
      type: 'select',
      cocosKey: 'answerTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (_v, i) => (i+1)),
        required: true,
        max: 120,
      }
    },
  ],
  form: {
    readyTime: 60,
    answerTime: 120,
    answers: [{
      value: '',
      error: ''
    }]
  }
}
