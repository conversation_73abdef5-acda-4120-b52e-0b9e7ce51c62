export const ContextualAnswerQuestion = {
  title: '情景回答',
  configs: [
    {
      key: 'questionTitle',
      label: '题干',
      type: 'textarea',
      cocosKey: 'questionTitle',
      props: {
        type: 'textarea',
        max: 1000,
        required: true,
        placeholder: '题干最多不超过1000个字符',
      }
    },
    {
      key: 'lookTime',
      label: '看题时间',
      type: 'select',
      cocosKey: 'lookTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i + 1)),
        max: 120,
        required: true,
      }
    },
    {
      key: 'audio',
      label: '听力音频',
      type: 'audio',
      cocosKey: 'audio',
      props: {
        max: 1,
        afterfix: '不超过5MB的mp3文件',
        required: true,
        maxSize: '5MB'
      }
    },
    {
      key: 'playCount',
      label: '播放次数',
      type: 'select',
      cocosKey: 'playCount',
      props: {
        afterfix: '',
        options: Array.from({ length: 5 }, (v, i) => (i + 1)),
        required: true,
        max: 1,
      }
    },
    {
      key: 'answerTime',
      label: '答题时长',
      type: 'select',
      cocosKey: 'answerTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        max: 120,
        required: true,
      }
    },
    {
      key: 'answerSetting',
      type: 'text-array',
      label: '答案设置',
      cocosKey: 'answerSetting',
      props: {
        min: 1,
        max: 5,
        tips: '您设置的答案将作为学生的评分依据，并会作为标准答案展示给学生，请认真填写'
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 200,
        placeholder: '如果有多个答案，请填写多行，最多支持5个答案（每个答案不超过200字符）',
      }
    },
    {
      key: 'keywordList',
      type: 'text-array',
      label: '关键词',
      cocosKey: 'keywordList',
      props: {
        min: 1,
        max: 5,
        required: true,
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 200,
        placeholder: '如果有多个关键词，请填写多行，最多支持5个关键词（每个关键词不超过200字符）',
      }
    }
  ],
  form: {
    uniqueId: Date.now(),
    audio: {
      url: '',
      name: '',
      duration: 0,
    },
    playCount: 1,
    answerTime: 15,
    lookTime: 5,
    questionTitle: '',
    answerSetting: [{
      value: '',
      error: ''
    }],
    keywordList: [{
      value: '',
      error: ''
    }]
  }
}
