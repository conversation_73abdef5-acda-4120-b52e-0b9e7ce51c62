export const enGroupPKQuestion = {
  title: '朗读短文',
  tabs: [
    {
      label: '题组',
      key: 'question',
      configs: [
        {
          key: 'description',
          type: 'text',
          label: '题目说明',
          cocosKey: 'name',
          props: {
            required: true,
          }
        },
        {
          key: 'image',
          label: '题干图片',
          type: 'img',
          cocosKey: 'countDownEndUrl',
          props: {
            required: false,
          }
        },
        {
          key: 'qeustionEN',
          type: 'text-array',
          label: '题干',
          cocosKey: 'text0',
          props: {
            min: 1,
            max: 1,
            required: true,
            tips: ''
          },
          itemProps: {
            type: 'text',
            required: true,
            max: 100,
            placeholder: '题干最多不超过100个字符',
          }
        },
        {
          key: 'qeustionCH',
          type: 'text',
          label: '翻译',
          cocosKey: 'label1',
          props: {
            required: true,
          }
        },
        {
          key: 'type',
          label: '类型',
          type: 'select',
          cocosKey: 'type',
          props: {
            options: [
              { label: '单词', value: 1 },
              { label: '句子', value: 2 },
              { label: '段落', value: 3 },
            ],
            required: true,
          }
        },
        {
          key: 'answerTime',
          label: '答题时长',
          type: 'select',
          cocosKey: 'answerTime',
          props: {
            afterfix: '秒',
            options: Array.from({ length: 120 }, (v, i) => (i+1)),
            max: 120,
            required: true,
          }
        },
      ],
      form: {
        description: '',
        image: '',
        qeustionEN: [{
          value: '',
          error: ''
        }],
        qeustionCH: '',
        type: 2,
        answerTime: 4,
      },
    },
    {
      label: '属性',
      key: 'properties',
      configs: [
        {
          key: 'countDownImages',
          label: '倒计时',
          type: 'img-array',
          cocosKey: 'countDownUrl',
          props: {
            max: 3,
            min: 3,
            required: true,
          }
        },
        {
          key: 'countDownSound',
          label: '',
          type: 'audio',
          cocosKey: 'countDownSound',
          props: {
            required: true,
          }
        },
        {
          key: 'answerRaceImage',
          label: '抢答按钮',
          type: 'img',
          cocosKey: 'countDownEndUrl',
          props: {
            required: true,
          }
        },
        {
          key: 'answerRaceSound',
          label: '', // 
          type: 'audio',
          cocosKey: 'countDownEndSound',
          props: {
            required: true,
          }
        },
        {
          key: 'randomSound',
          label: '随机', // 
          type: 'audio',
          cocosKey: 'randomSound',
          props: {
            required: true,
          }
        },
        {
          key: 'pkImage',
          label: 'PK',
          type: 'img',
          cocosKey: 'pkUrl',
          props: {
            required: true,
          }
        },
        {
          key: 'pkSound',
          label: '', // 
          type: 'audio',
          cocosKey: 'pkSound',
          props: {
            required: true,
          }
        },
        {
          key: 'pkLeftImage',
          label: 'PK背景图-左',
          type: 'img',
          cocosKey: 'pkBgUrl0',
          props: {
            required: true,
          }
        },
        {
          key: 'pkRightImage',
          label: 'PK背景图-右',
          type: 'img',
          cocosKey: 'pkBgUrl1',
          props: {
            required: true,
          }
        },
        {
          key: 'questionItemImage', // 单个题目未作答的占位图
          label: '未作答图',
          type: 'img',
          cocosKey: 'starUrl0',
          props: {
            required: true,
          }
        },
        {
          key: 'questionItemDoneImage', // 单个题目已作答的占位图
          label: '未作答图',
          type: 'img',
          cocosKey: 'starUrl1',
          props: {
            required: true,
          }
        },
        {
          key: 'winSpine',
          label: '作答结果-胜利',
          type: 'spine',
          cocosKey: 'succSpine',
          props: {
            required: true,
          }
        },
        {
          key: 'winAudio',
          label: '',
          type: 'audio',
          cocosKey: 'succSound',
          props: {
            required: true,
          }
        },
        {
          key: 'drawSpine',
          label: '作答结果-平局',
          type: 'spine',
          cocosKey: 'succSpine',
          props: {
            required: true,
          }
        },
        {
          key: 'drawAudio',
          label: '',
          type: 'audio',
          cocosKey: 'succSound',
          props: {
            required: true,
          }
        },
      ],
      form: {
        countDownImages: [],
        countDownSound: '',
        answerRaceImage: '',
        answerRaceSound: '',
        randomSound: '',
        pkImage: '',
        pkSound: '',
        pkLeftImage: '',
        pkRightImage: '',
        questionItemImage: '',
        questionItemDoneImage: '',
        questionItemDoneAudio: {
          url: '',
          name: '',
          duration: 0,
        },
        winSpine: {
          atlas: '',
          images: [],
          skeleton: '',
          animation: ''
        },
        winAudio: '',
        drawSpine: {
          atlas: '',
          images: [],
          skeleton: '',
          animation: ''
        },
        drawAudio: '',
      }
    },
  ],
}
