export const followWordQuestion = {
  title: '跟读单词',
  configs: [
    {
      key: 'words',
      type: 'text-array',
      label: '单词',
      cocosKey: 'words',
      props: {
        min: 1,
        max: 1,
        required: true,
        tips: ''
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 30,
        placeholder: '单词最多不超过30个字符',
      }
    },
    {
      key: 'explains',
      type: 'input-array',
      label: '释义',
      cocosKey: 'explains',
      props: {
        min: 1,
        max: 4,
        required: true,
        isCheckWord: false,
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 30,
        placeholder: '释义最多不超过30个字符',
      }
    },
    {
      key: 'audio',
      label: '音频',
      type: 'audio',
      props: {
        max: 1,
        afterfix: '不超过600kb的mp3文件',
        required: true,
      }
    },
    {
      key: 'answerTime',
      label: '答题时长',
      type: 'select',
      cocosKey: 'answerTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        max: 120,
        required: true,
      }
    },
  ],
  form: {
    uniqueId: Date.now(),
    words: [{
      value: '',
      error: ''
    }],
    explains: [{
      value: '',
      error: ''
    }],
    audio: {
      url: '',
      name: '',
      duration: 0,
    },
    answerTime: 4,
  }
}
