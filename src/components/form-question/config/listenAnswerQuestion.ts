export const listenAnswerQuestionFormData = {
  title: '听后回答',
  configs: [
    {
      key: 'questionStem',
      label: '题干',
      type: 'text',
      cocosKey: 'questionTitle',
      props: {
        type: 'text',
        required: true,
        max: 100,
        placeholder: '题干最多不超过100个字符',
      }
    },
    {
      key: 'readTime',
      label: '看题时间',
      type: 'select',
      cocosKey: 'lookTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        max: 120,
        required: true,
      }
    },
    {
      key: 'audio',
      label: '听力音频',
      type: 'audio',
      props: {
        max: 1,
        afterfix: '不超过600kb的mp3文件',
        required: true,
      }
    },
    {
      key: 'playCount',
      label: '播放次数',
      type: 'select',
      cocosKey: 'playCount',
      props: {
        afterfix: '',
        options: Array.from({ length: 5 }, (v, i) => (i+1)),
        required: true,
        max: 1,
      }
    },
    {
      key: 'answerTime',
      label: '答题时长',
      type: 'select',
      cocosKey: 'answerTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        required: true,
        max: 120,
      }
    },
    {
      key: 'answers',
      type: 'text-array',
      label: '答案设置',
      cocosKey: 'answerSetting',
      props: {
        min: 1,
        max: 5,
        tips: '您设置的答案将作为学生的评分依据，并会作为标准答案展示给学生，请认真填写'
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 200,
        placeholder: '如果有多个答案，请填写多行，最多支持5个答案（每个答案不超过200字符）',
      }
    },
    {
      key: 'keywords',
      type: 'text-array',
      label: '关键词',
      cocosKey: 'keywordList',
      props: {
        min: 1,
        max: 5,
        required: true,
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 200,
        placeholder: '如果有多个关键词，请填写多行，最多支持5个关键词（每个关键词不超过200字符）',
      }
    },
  ],
  form: {
    questionStem: ``,
    answerStart: '',
    readTime: 5,
    audio: {
      url: '',
      name: '',
      duration: 0
    },
    playCount: 1,
    readyTime: 120,
    answerTime: 15,
    answers: [{
      value: '',
      error: ''
    }],
    keywords: [{
      value: '',
      error: ''
    }]
  }
}
