export const listenRetellQuestionFormData = {
  title: '听后转述',
  configs: [
    {
      key: 'title',
      type: 'text',
      label: '标题',
      cocosKey: 'title',
      props: {
        customClass: 'retell-title',
        type: 'text',
        required: true,
        max: 10,
        placeholder: '标题最多不超过10个字符',
      }
    },
    {
      key: 'questionStem',
      label: '题干',
      type: 'editor',
      cocosKey: 'questionTitlePicList',
      props: {
        type: 'text',
        max: 1000,
        required: true,
        placeholder: '题干最多不超过1000个字符',
      }
    },
    {
      key: 'answerStart',
      type: 'text',
      label: '转述开头',
      cocosKey: 'tipsStart',
      props: {
        type: 'text',
        required: true,
        max: 1000,
        placeholder: '转述开头最多不超过1000个字符',
      }
    },
    {
      key: 'readTime',
      label: '看题时间',
      type: 'select',
      cocosKey: 'lookTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        max: 120,
        required: true,
      }
    },
    {
      key: 'audio',
      label: '听力音频',
      type: 'audio',
      props: {
        max: 1,
        afterfix: '不超过600kb的mp3文件',
        required: false,
      }
    },
    {
      key: 'playCount',
      label: '播放次数',
      type: 'select',
      cocosKey: 'playCount',
      props: {
        afterfix: '',
        options: Array.from({ length: 5 }, (v, i) => (i+1)),
        required: true,
        max: 1,
      }
    },
    {
      key: 'readyTime',
      label: '答题准备',
      type: 'select',
      cocosKey: 'readyTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        required: true,
        max: 120,
      }
    },
    {
      key: 'answerTime',
      label: '答题时长',
      type: 'select',
      cocosKey: 'answerTime',
      props: {
        afterfix: '秒',
        options: Array.from({ length: 120 }, (v, i) => (i+1)),
        required: true,
        max: 120,
      }
    },
    {
      key: 'answers',
      type: 'text-array',
      label: '答案设置',
      cocosKey: 'answerSetting',
      props: {
        min: 1,
        max: 1,
        required: true,
        tips: '您设置的答案将作为学生的评分依据，并会作为标准答案展示给学生，请认真填写'
      },
      itemProps: {
        type: 'textarea',
        required: true,
        max: 1000,
        placeholder: '答案最多不超过1000个字符',
      }
    },
    {
      key: 'keywords',
      type: 'text-array',
      label: '关键词',
      cocosKey: 'keywordList',
      props: {
        min: 1,
        max: 5,
        required: true,
      },
      itemProps: {
        type: 'text',
        required: true,
        max: 200,
        placeholder: '如果有多个关键词，请填写多行，最多支持5个答案（每个答案不超过200字符）',
      }
    },
  ],
  form: {
    questionStem: ``,
    title: '',
    answerStart: '',
    readTime: 5,
    audio: {
      url: '',
      name: '',
      duration: 0
    },
    playCount: 1,
    readyTime: 120,
    answerTime: 15,
    answers: [{
      value: '',
      error: ''
    }],
    keywords: [{
      value: '',
      error: ''
    }]
  }
}
