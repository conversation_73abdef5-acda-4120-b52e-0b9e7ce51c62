<template>
  <div class="form-array">
    <el-form-item
      :label="`${config.label}${supportAdd ? index + 1 : ''}`"
      :required="true"
      class="q-form-item"
      :prop="`${config.key}.${index}`"
      :rules="rules"
      :key="index"
      v-for="(item, index) in form[config.key]"
    >
      <div class="input-check-wapper">
        <div class="input-check-main">
          <el-input
            :type="config.itemProps && config.itemProps.type ? config.itemProps.type : 'text'"
            :rows="config.itemProps && config.itemProps.rows ? config.itemProps.rows : 2"
            autocomplete="off"
            v-model="item.value"
            :validate-event="true"
            :placeholder="config.itemProps && config.itemProps.placeholder ? config.itemProps.placeholder : ''"
          >
            <span class="el-input__count" slot="suffix">
              <span class="el-input__count-inner"> {{ item.value.length }}/{{ getMax }} </span>
            </span>
          </el-input>
          <span class="el-input__count textarea-count" v-if="config.itemProps && config.itemProps.type === 'textarea'">
            <span class="el-input__count-inner"> {{ item.value.length }}/{{ getMax }} </span>
          </span>
          <span class="answer-all-icon" v-if="supportAdd">
            <i class="el-icon-plus" :class="{ disabled: form[config.key].length > 4 }" @click.prevent="changeLength('add', index)"></i>
            <i class="el-icon-minus" :class="{ disabled: form[config.key].length < 2 }" @click.prevent="changeLength('delete', index)"></i>
          </span>
        </div>
        <!-- <div class="el-form-item__error words-error-wrapper">
          <div class="error-list-wrapper">
            <div>{{item.error.map(eitem => eitem.word).join('，')}} 无法测评，请联系产运老师处理</div>
          </div>
        </div> -->
        <div class="tips-wrapper">{{ config.props.tips }}</div>
      </div>
    </el-form-item>
    
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { Form, ConfigItem, ErrorItem } from "../index";
import { checkWordIsInASR } from "@/common/utils/asrRequest";

@Component
export default class QInputCheck extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  addModal: {
    show: boolean;
    list: ErrorItem[];
  } = {
    show: false,
    list: [],
  };

  get getMax() {
    return this.config.itemProps ? this.config.itemProps.max : 0;
  }

  get supportAdd() {
    return this.config.props.max > 1;
  }

  get content() {
    return this.form[this.config.key];
  }

  changeWordsCheck = async (...params: any) => {
    const value = params[1];
    const callback = params[2];
    const index = Number(params[0].field.slice(-1));
    this.content[index].validateError = [];
    if (!value.value.trim().length) {
      this.content[index].error = [];
      this.content[index].validateError = [`请输入${this.config.label}`];
      return callback(new Error(`请输入${this.config.label}`));
    }
    const wordReplace = /[^a-zA-Z0-9 '",\n.!?;:~\\\\|+=@#$%^&*/_-]+/g;

    value.value = value.value.replace(wordReplace, "");

    if (this.content[index].error.length) {
      return callback(new Error(`${this.content[index].error.map((eitem: any) => eitem.word).join('，')} 无法测评，请联系产运老师处理`));
    }
    if (value.value.length > this.getMax) {
      this.content[index].error = [];
      this.content[index].validateError = [`${this.config.label}不得超过${this.getMax}个字符`];
      return callback(`${this.config.label}不得超过${this.getMax}个字符`);
    }
    return callback();
  };

  checkWords = async (...params: any) => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<void>(async (resolve, reject) => {
      const value = params[1];
      // const callback = params[2];
      const index = Number(params[0].field.slice(-1));
      this.content[index].validateError = [];
      const checkValue = value.value.replace(/(\s*$)/g, ""); // 去除右侧空格
      if (!checkValue.length) {
        this.content[index].error = [];
        this.content[index].validateError = [`请输入${this.config.label}`];
        reject(`请输入${this.config.label}`);
        // return callback(new Error(`请输入${this.config.label}`));
      }
      if (checkValue.length > this.getMax) {
        this.content[index].error = [];
        this.content[index].validateError = [`${this.config.label}不得超过${this.getMax}个字符`];
        reject(`${this.config.label}不得超过${this.getMax}个字符`);
        // return callback(new Error(`${this.config.label}不得超过${this.getMax}个字符`));
      }
      const wordReplace = /[^a-zA-Z0-9 '",\n.!?;:~\\\\|+=@#$%^&*/_-]+/g;
      value.value = checkValue.replace(wordReplace, "");
      const res = await checkWordIsInASR(value.value);
      try {
        const data = (res as any).data.data || {};
        const wordsList: any[] = data.WordsList || [];
        const currentItem = this.content[index];
        if (currentItem) {
          currentItem.error = wordsList.filter(item => item.in_dict !== 0);
        }
        if (currentItem.error.length) {
          console.log("changeWordsCheck-end");
          reject(`${currentItem.error.map((eitem: any) => eitem.word).join('，')} 无法测评，请联系产运老师处理`);
          // reject("以上单词系统校验错误，请检查！");
          // return callback(new Error('以上单词系统校验错误，请检查！'));
        }
        console.log("changeWordsCheck-blu-end");
        resolve();
      } catch (error) {
        reject("以上单词系统校验错误，请检查！");
      }
    });
  };

  rules = [
    { validator: this.checkWords, trigger: "blur" },
    { validator: this.changeWordsCheck, trigger: "change" },
  ];

  changeLength(type: "add" | "delete", index: number) {
    if (type === "delete" && this.content.length > 1) {
      this.content.splice(index, 1);
    }
    if (type === "add" && this.content.length < 5) {
      this.content.splice(index + 1, 0, { value: "", error: "" });
    }
  }

  created() {
    this.content.forEach((item: any) => {
      item.error = [];
    });
  }
}
</script>
<style lang="less">
.form-array {
  .el-form-item {
    display: flex;
  }
  .q-form-item.el-form-item.is-success .el-input__inner {
    border-color: #e1e2e6;
  }
  .q-form-item.el-form-item.is-success .el-textarea__inner {
    border-color: #e1e2e6;
  }
  .input-check-wapper {
    width: 100%;
  }
  .input-check-main {
    display: flex;
    position: relative;
    .textarea-count {
      position: absolute;
      bottom: 2px;
      right: 4px;
      color: #999;
      font-size: 12px;
    }
    input {
      padding-right: 66px !important;
    }
  }
  .el-form-item__content {
    flex: 1;
  }
  .error-list-wrapper {
    position: relative;
  }
  .words-error-wrapper {
    position: static;
    display: none;
  }
  .tips-wrapper {
    padding-left: 20px;
    background: url("~@/assets/img/tips_bg.png") no-repeat;
    background-size: 16px;
    background-position: 0px 11px;
    text-align: left;
  }
  .is-error .words-error-wrapper {
    display: block;
  }
}
</style>
