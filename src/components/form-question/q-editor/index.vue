<!-- eslint-disable @typescript-eslint/camelcase -->
<template>
  <el-form-item :label="config.label" :required="config.props ? config.props.required : true" class="q-form-item" label-width="75px" :prop="config.key" :rules="rules">
    <vue-tinymce ref="tinymce" v-model="form[config.key]" :setting="setting" :setup="setup"></vue-tinymce>
    <span class="words-count">{{ `${valueLen}/${getMax}` }}</span>
    <el-input type="age" v-model.number="valueLen" v-show="false"></el-input>
  </el-form-item>
</template>

<script lang="ts">
import domtoimage from "@/components/zyb-color-picker/colorPipette/dom-to-image.js";
import FormulaModal, { OutputType } from "@/pages/index/components/FormulaModal";
import { Component, Prop, Vue } from "vue-property-decorator";
import { ConfigItem, Form } from "../index";
//安装组件
import "@/pages/index/tinymce";

// 样式需要这样引入，否则可能会覆盖全局样式
import resetCss from "!!raw-loader!./reset.css";
import wavyUnderlineCss from "!!raw-loader!./wavyUnderline.css";
import contentCss from "!!raw-loader!tinymce/skins/content/default/content.min.css";
import contentUiCss from "!!raw-loader!tinymce/skins/ui/oxide/content.min.css";
import { tinymcemath2svg } from "./chtmlTransform2svg";

import myicons from "../../../../public/tinymce/myicons.js";

@Component
export default class QEditor extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  editor: any;

  get getMax() {
    return this.config.props && this.config.props.max ? this.config.props.max : 0;
  }

  valueLen = 0;

  checkAge = (...params: any) => {
    const wordcount = (this as any).$tinymce.activeEditor.plugins.wordcount;
    const valueLen = wordcount.body.getCharacterCount();
    const callback = params[2];
    if (!valueLen) {
      return callback(new Error(`请输入${this.config.label}`));
    }
    if (valueLen > this.getMax) {
      return callback(new Error(`${this.config.label}不得超过${this.getMax}个字符`));
    }
    return callback();
  };

  rules = [{ validator: this.checkAge, trigger: "change" }];

  public setting: any = {
    selector: "textarea", // change this value according to your HTML
    editable_root: false,
    block_unsupported_drop: true,
    newdocument_content: '<div contenteditable="true">Editable content</div>',
    menubar: false,
    skin: false,
    content_css: false,
    // toolbar: [
    //   "undo redo | fontsizeselect forecolor backcolor | bold italic  strikethrough underline underlinedot wavyunderline superscript subscript | alignleft aligncenter alignright alignjustify firstlineindent lineheight | myimage table mathjax | wordcount",
    //   // "",
    // ],
    // 没有波浪线
    toolbar: [
       "undo redo | fontsizeselect forecolor backcolor | bold italic strikethrough underline wavyunderline underlinedot subscript superscript | alignleft aligncenter alignright alignjustify firstlineindent lineheight | myimage table mathjax | wordcount"
      // "",
    ],
    tooltip_delay: 0, // 鼠标悬停提示延迟
    mathjax: {
      lib: "./tinymce/plugins/mathjax/tex-mml-chtml.js", //required path to mathjax
      symbols: { start: "\\(", end: "\\)" }, //optional: mathjax symbols
      className: "math-tex", //optional: mathjax element class
      configUrl: "./tinymce/plugins/mathjax/config.js", //optional: mathjax config js
    },
    // wavyunderline
    plugins: "table wordcount paste mathjax underlinedot firstlineindent wavyunderline superscript subscript",
    fontsize_formats: "14px 16px 18px 20px 24px 26px 30px 34px 36px 40px",
    branding: false,
    elementpath: false,
    language: "zh_CN",
    height: 200,
    min_height: 200,
    max_height: 450,
    draggable_invalid_targets: ["math-tex", "mce-container", "mce-content-body"],
    content_style:
      [contentCss, contentUiCss, resetCss, wavyUnderlineCss].join("\n") +
      "body {font-size: 24px} p,table {margin:12px 0} table{word-break: break-all; .math-tex{position: relative;};.math-tex::before{content:'';left:0;right:0;top:0;bottom:0;position:absolute;z-index:10;}} sub, sup {position: relative; display: inline-block; line-height: normal; text-indent: 0 !important;} sub {bottom: -0.3em; vertical-align: baseline;} sup {top: -0.3em; vertical-align: baseline;} /* 防止上标和下标在首行缩进段落中继承text-indent */ p[data-firstlineindent='1'] sub, p[data-firstlineindent='1'] sup {text-indent: 0 !important;} /* 针对下划线的特定处理 */ span[style*='text-decoration: underline'] {text-decoration: underline !important;} /* 针对删除线的特定处理 */ span[style*='text-decoration: line-through'] {text-decoration: line-through !important;} /* 针对同时有下划线和删除线的特定处理 */ span[style*='text-decoration: underline line-through'], span[style*='text-decoration: line-through underline'] {text-decoration: underline line-through !important;} /* 确保字体大小变化不影响文本装饰 */ span[style*='font-size'] span[style*='text-decoration'], span[style*='text-decoration'] span[style*='font-size'], span[style*='text-decoration: line-through'] span, span[style*='text-decoration: line-through'] * {text-decoration: inherit !important;} /* 解决双重着重号问题 */ .underlinedot .underlinedot { text-emphasis: none !important; } /* 确保着重号不会被嵌套应用 */ span.underlinedot > span.underlinedot, span.underlinedot span.underlinedot { text-emphasis: none !important; } /* 处理零宽度空格 */ .underlinedot:empty, .underlinedot:first-child:last-child:empty { text-emphasis: none !important; }", // 初始化赋值
    paste_data_images: false,
    placeholder: `${this.config.label}最多不超过${this.getMax}个字符`,
    paste_postprocess: function(plugin: any, args: any) {
      const tableNodes = args.node.querySelectorAll("table");
      if (tableNodes) {
        [...tableNodes].forEach(node => {
          node.setAttribute("border", 1);
          node.setAttribute("cellspacing", 0);
          node.setAttribute("cellpadding", 0);
          // word-break: break-all;
          node.setAttribute("style", "border-collapse: collapse;word-break: break-all;");
        });
      }
    },
    init_instance_callback: (editor: any) => {
      const iframeDocument = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument;
      // console.log("is null?", iframeDocument, document.querySelector(".tox-edit-area__iframe"));
      if (iframeDocument) {
        this.resetPlaceholderStyle(iframeDocument.head);
      }
      if (this.form[this.config.key]) {
        editor.setContent(this.form[this.config.key]);
        this.valueLen = editor.getContent({ format: "text" }).length;
      }

      // // 覆盖TinyMCE的strikethrough格式定义，使用自定义类而不是内联样式
      editor.formatter.register('strikethrough', {
        inline: 'span',
        classes: 'mce-strikethrough',
        merge: false, // 不允许合并，避免嵌套
        deep: false, // 防止深度嵌套
        split: true,  // 允许分割，避免嵌套
        remove_similar: true, // 移除相似的格式
        onformat: function(node: any, format: any, vars: any) {
          // 在应用删除线时，确保不创建嵌套结构
          var parent = node.parentNode;
          if (parent && parent.nodeName === 'SPAN' && editor.dom.hasClass(parent, 'mce-strikethrough')) {
            // 如果父元素已经是删除线span，则将删除线类应用到父元素
            editor.dom.addClass(parent, 'mce-strikethrough');
            return false; // 阻止创建新的span
          }
          return true; // 允许创建新的span
        }
      });
      
      // 添加自定义样式
      var styleTag = editor.dom.create('style', {
        type: 'text/css'
      });
      styleTag.innerHTML = '.mce-strikethrough { text-decoration: line-through !important; }';
      editor.getDoc().head.appendChild(styleTag);
      
      // 确保已有的删除线内容也能正确显示
      setTimeout(function() {
        // 查找所有使用内联样式的删除线span
        var oldStrikeThroughSpans = editor.dom.select("span[style*='text-decoration: line-through']");
        oldStrikeThroughSpans.forEach(function(span: any) {
          // 移除内联样式并添加自定义类
          editor.dom.setStyle(span, 'text-decoration', '');
          editor.dom.addClass(span, 'mce-strikethrough');
        });
      }, 100);
      
      // 实现上标和下标互斥
      // 覆盖上标命令
      editor.addCommand('mceToggleSuperscript', function() {
        // 先检查是否有下标
        if (editor.formatter.match('subscript')) {
          // 如果有下标，先移除下标
          editor.formatter.remove('subscript');
        }
        // 然后切换上标
        editor.formatter.toggle('superscript');
      });
      
      // 覆盖下标命令
      editor.addCommand('mceToggleSubscript', function() {
        // 先检查是否有上标
        if (editor.formatter.match('superscript')) {
          // 如果有上标，先移除上标
          editor.formatter.remove('superscript');
        }
        // 然后切换下标
        editor.formatter.toggle('subscript');
      });

      editor.on("Change SetContent KeyUp", () => {
        this.contentChange();
      });
      editor.on("ExecCommand", function(e: any) {
        var cmd = e.command.toLowerCase();
        console.log("on ExecCommand", cmd, e);
        
        // 处理字体大小变化时的删除线结构优化
        if (cmd === 'fontsize') {
          setTimeout(function() {
            // 递归处理所有删除线span，确保完全拆开嵌套
            function processStrikethroughSpans() {
              var strikethroughSpans = editor.dom.select('span.mce-strikethrough');
              var hasChanges = false;
              
              strikethroughSpans.forEach(function(span: any) {
                // 检查是否有嵌套的字体大小span
                var fontSizeSpans = editor.dom.select('span[style*="font-size"]', span);
                if (fontSizeSpans.length > 0) {
                  hasChanges = true;
                  var parent = span.parentNode;
                  if (parent) {
                    // 递归收集所有文本节点和span节点，保持原有顺序
                    function collectNodes(node: any, result: any[]) {
                      if (node.nodeType === 3) { // 文本节点
                        if (node.textContent.trim()) {
                          result.push({
                            type: 'text',
                            content: node.textContent,
                            node: node
                          });
                        }
                      } else if (node.nodeType === 1) { // 元素节点
                        if (node.style && node.style.fontSize) {
                          // 字体大小span，递归处理其内容
                          var childNodes: any[] = [];
                          for (let i = 0; i < node.childNodes.length; i++) {
                            collectNodes(node.childNodes[i], childNodes);
                          }
                          // 为每个子节点创建独立的字体大小span
                          childNodes.forEach(function(childItem: any) {
                            result.push({
                              type: 'fontspan',
                              content: childItem.content || childItem.node.innerHTML,
                              fontSize: node.style.fontSize,
                              isText: childItem.type === 'text'
                            });
                          });
                        } else {
                          // 其他元素，递归处理其子节点
                          for (let i = 0; i < node.childNodes.length; i++) {
                            collectNodes(node.childNodes[i], result);
                          }
                        }
                      }
                    }
                    
                    var allNodes: any[] = [];
                    collectNodes(span, allNodes);
                    
                    // 创建新的同级span元素
                    allNodes.forEach(function(item: any) {
                      if (item.type === 'text') {
                        // 创建文本span
                        var textSpan = editor.dom.create('span', {
                          'class': 'mce-strikethrough'
                        });
                        textSpan.textContent = item.content;
                        parent.insertBefore(textSpan, span);
                      } else if (item.type === 'fontspan') {
                        // 创建字体大小span
                        var fontSpan = editor.dom.create('span', {
                          'style': 'font-size: ' + item.fontSize,
                          'class': 'mce-strikethrough'
                        });
                        if (item.isText) {
                          fontSpan.textContent = item.content;
                        } else {
                          fontSpan.innerHTML = item.content;
                        }
                        parent.insertBefore(fontSpan, span);
                      }
                    });
                    
                    // 移除原来的删除线span
                    editor.dom.remove(span);
                  }
                }
              });
              
              // 如果还有变化，继续处理
              if (hasChanges) {
                setTimeout(processStrikethroughSpans, 0);
              }
            }
            
            // 开始处理
            processStrikethroughSpans();
          }, 10);
        }
      });
      const handleMathjaxOpen = (e: { target: any; data: { latex: any } }) => {
        console.log("myMathjaxOpen...e...", e);
        FormulaModal({
          onClose: () => {
            console.log("FormulaModal...onClose");
            editor.fire("myMathjaxInsertClose");
          },
          onConfirm: (...params) => {
            console.log("FormulaModal...onConfirm", params);
          },
          onGetEquationInfo: (latexStr: string) => {
            const latex = latexStr.replace(/^\$|\$$/g, "");
            const latex$ = latex.replace(/[boldsymbol]{10}/g, "mathbf");
            console.log("onGetEquationInfo", latexStr, "latex$", latex$);
            editor.fire("myMathjaxInsert", { data: { latex: latex$, target: e.target } });
          },
          latex: e.data.latex,
          type: OutputType.HTML,
        });
      };
      editor.on("myMathjaxOpen", handleMathjaxOpen);
      // 为编辑器容器添加拖拽事件
      editor.on("dragstart", function(event: { preventDefault: () => void }) {
        event.preventDefault(); // 阻止拖拽开始
      });
      editor.on("dragover", function(event: { preventDefault: () => void }) {
        event.preventDefault(); // 阻止拖拽开始
      });
      editor.on("drop", function(event: { preventDefault: () => void }) {
        event.preventDefault(); // 阻止拖拽开始
      });

      editor.on("drag", function(event: { preventDefault: () => void }) {
        event.preventDefault(); // 阻止拖拽过程中的默认行为
      });

      editor.on("dragend", function(event: { preventDefault: () => void }) {
        event.preventDefault(); // 阻止拖拽结束
      });

      this.setToolTip();
    },
  };

  public styleStr = `
    #tinymce {
      font-size: 24px;
      line-height: 1.5;
      margin: 0 !important;
      padding: 0px;
    }
    #tinymce p{
      margin:0 !important;
    }
    #tinymce::before {
      font-size: 14px;
      color: #C0C4CC;
      font-family: Arial,Avenir, Helvetica, sans-serif;
      font-weight: 300;
    }
    /* 防止上标和下标在首行缩进段落中继承text-indent */
    #tinymce sub, #tinymce sup {
      text-indent: 0 !important;
    }
    #tinymce p[data-firstlineindent='1'] sub, 
    #tinymce p[data-firstlineindent='1'] sup {
      text-indent: 0 !important;
    }
    
    /* 确保字体大小变化不影响文本装饰，防止双重删除线 */
    #tinymce span[style*='font-size'] span[style*='text-decoration'],
    #tinymce span[style*='text-decoration'] span[style*='font-size'],
    #tinymce span[style*='text-decoration: line-through'] span,
    #tinymce span[style*='text-decoration: line-through'] * {
      text-decoration: inherit !important;
    }
    table {
      max-width: '886px'
    }
    ::-webkit-scrollbar-track-piece {
      background-color :#fff;
      border-radius: 2em;
    }
    ::-webkit-scrollbar {
      width: 0px;
    }
    ::-webkit-scrollbar-thumb {
      background-color:rgba(95,101,113,1);
      background-clip:padding-box;
      border-radius: 2em;
      border: 4px solid rgba(255,255,255,1);
      width:0px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background-color: red;
    }
    #tinymce:hover ::-webkit-scrollbar {
      width: 6px;
    }

    .underlinedot {
      text-emphasis-position: under left;
      text-emphasis: dot;
    }
${wavyUnderlineCss}
    `;

  resetPlaceholderStyle(dom: HTMLElement) {
    const style = document.createElement("style");
    style.type = "text/css";
    style.innerText = this.styleStr;
    dom.appendChild(style);
  }

  setToolTip() {
    // 创建自定义 tooltip 容器（只创建一次）
    let tooltip = document.createElement("div");
    tooltip.className = "my-tooltip";
    tooltip.style.position = "fixed";
    tooltip.style.display = "none";
    tooltip.style.zIndex = "9999";
    document.body.appendChild(tooltip);
    let timer: any = null;
    let debounceTimer: any = null; // 新增防抖定时器

    const onMouseOver = (e: MouseEvent) => {
      clearTimeout(debounceTimer);
      debounceTimer = setTimeout(() => {
        const btn = (e.target as any).closest(".tox-split-button") || (e.target && (e.target as any).closest(".tox-tbtn"));
        if (btn) {
          console.log("onMouseOver.e.target", e.target, btn);
          // 获取 title 内容
          const title = btn.getAttribute("title") || btn.getAttribute("data-title");
          if (title) {
            tooltip.innerText = title;
            // 定位到按钮下方
            const rect = btn.getBoundingClientRect();
            console.log("rect", rect, rect.left, tooltip.offsetWidth);
            tooltip.style.left = `${rect.left + rect.width / 2}px`;
            tooltip.style.top = rect.bottom + 6 + "px";
            tooltip.style.display = "block";
            // 移除原生 title，防止原生tooltip闪现
            // btn._oldTitle = title;
            btn.removeAttribute("title");
            btn.setAttribute("data-title", title);
            document.body.addEventListener("mouseout", onMouseOut);
            // 如果2s之后鼠标没有移出，则隐藏tooltip
            if (timer) {
              clearTimeout(timer);
            }
            timer = setTimeout(() => {
              clearTimeout(timer);
              tooltip.style.display = "none";
              // 恢复 title 属性
              // if (btn._oldTitle) {
              //   // btn.setAttribute("title", btn._oldTitle);
              //   btn._oldTitle = null;
              // }
            }, 500);
          }
        }
      }, 100); // 100ms 防抖
    };
    const onMouseOut = (e: MouseEvent) => {
      const btn = (e.target as any).closest(".tox-split-button") || (e.target && (e.target as any).closest(".tox-tbtn"));
      // const btn = e.target.closest('.tox-tbtn') || e.target.closest('.tox-split-button');
      if (btn) {
        clearTimeout(timer);
        timer = setTimeout(() => {
          clearTimeout(timer);
          tooltip.style.display = "none";
        }, 500);
        document.body.removeEventListener("mouseout", onMouseOut);
      }
    };

    // 事件委托：监听整个 body，捕获所有 .tox-tbtn
    document.body.addEventListener("mouseover", onMouseOver);
  }

  contentChange() {
    const wordcount = (this as any).$tinymce.activeEditor.plugins.wordcount;
    if (this.valueLen != wordcount.body.getCharacterCount()) {
      this.valueLen = wordcount.body.getCharacterCount();
      this.$emit("itemValidate", this.config.key);
    }
  }

  public setup(editor: any) {
    this.editor = editor;
    // 注册 myicons 图标，优先覆盖
    if (myicons && myicons.forEach) {
      myicons.forEach((svg, name) => {
        console.log("name....", name);
        editor.ui.registry.addIcon(name, svg);
      });
    }

    editor.ui.registry.addToggleButton("myimage", {
      icon: "myimage",
      tooltip: "Insert image",
      onAction: () => {
        this.insertImg();
      },
    });
    editor.ui.registry.addButton("myindent", {
      icon: "indent",
      tooltip: "增加缩进",
      onAction: () => {
        editor.execCommand("indent");
      },
    });
    editor.ui.registry.addButton("myoutdent", {
      icon: "outdent",
      tooltip: "减少缩进",
      onAction: () => {
        editor.execCommand("outdent");
      },
    });


    
    // 添加处理着重号元素的监听器
    const processUnderlinedotElements = () => {
      // 获取编辑器内容区域
      const body = editor.getBody();
      if (!body) return;

      // 保存当前的选区和光标位置
      const bookmark = editor.selection.getBookmark(2, true);
      let hasProcessed = false; // 标记是否进行了处理

      // 查找所有着重号元素
      const underlinedotElements = body.querySelectorAll('.underlinedot');

      // 处理每个着重号元素
      underlinedotElements.forEach((element: HTMLElement) => {
        // 获取元素的所有子节点
        const childNodes = Array.from(element.childNodes);

        // 处理每个子节点
        childNodes.forEach((node) => {
          // 只处理文本节点
          if (node.nodeType === 3 && node.nodeValue) {
            const text = node.nodeValue;

            // 检查是否包含零宽度字符
            if (text.indexOf('\uFEFF') !== -1) {
              // 检查是否仅包含零宽度字符
              const textWithoutZWS = text.replace(/\uFEFF/g, '');

              // 如果移除零宽度字符后文本不为空，说明包含其他字符，需要移除零宽度字符
              if (textWithoutZWS.trim().length > 0) {
                node.nodeValue = textWithoutZWS;
                hasProcessed = true; // 标记已进行处理
              }
              // 如果仅包含零宽度字符，则保留不处理
            }
          }
        });
      });



      // 如果进行了处理，恢复光标位置
      if (hasProcessed) {
        try {
          // 恢复光标位置
          editor.selection.moveToBookmark(bookmark);
        } catch (e) {
          console.error('恢复光标位置失败:', e);
        }
      }
    };
    
    // 在编辑器内容变化时处理着重号元素
    editor.on('Change SetContent KeyUp NodeChange', () => {
      // 使用setTimeout确保在DOM更新后处理
      setTimeout(processUnderlinedotElements, 0);
    });

    // 在编辑器初始化完成后处理一次
    editor.on('init', () => {
      setTimeout(processUnderlinedotElements, 100);
    });
  }

  public genBase64(file: any) {
    return new Promise(resolve => {
      const fileReader = new FileReader();
      fileReader.readAsDataURL(file);
      fileReader.onload = e => {
        resolve((e.target as any).result);
      };
    });
  }

  public cropperImage(file: any): Promise<Blob> {
    return new Promise(resolve => {
      const fileReader = new FileReader();
      const img = new Image();
      // 缩放图片需要用到的canvas
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d") as CanvasRenderingContext2D;

      // 文件base64化，以便获知图片原始尺寸
      fileReader.readAsDataURL(file);
      fileReader.onload = e => {
        // resolve((e.target as any).result)
        img.src = e.target?.result as string;
      };
      // base64地址图片加载完毕后
      img.onload = function() {
        const originalSize = {
          width: img.width,
          height: img.height,
        };
        const maxSize = {
          width: 600,
          height: Math.round((img.height / img.width) * 600),
        };
        const targetSize = {
          width: Math.min(originalSize.width, maxSize.width),
          height: originalSize.width < maxSize.width ? originalSize.height : maxSize.height,
        };
        // canvas对图片进行缩放
        canvas.width = targetSize.width;
        canvas.height = targetSize.height;
        // 清除画布
        context.clearRect(0, 0, targetSize.width, targetSize.height);
        // 图片压缩
        context.drawImage(img, 0, 0, targetSize.width, targetSize.height);
        // canvas转为blob并上传
        canvas.toBlob(function(blob) {
          //blob将base64编码的src 以二进制的形式存进了 Blob对象
          // console.log(blob);
          resolve(blob as Blob);
        }, file.type || "image/png");
      };
    });
  }

  public insertImg() {
    // 模拟出一个input用于添加本地文件
    const input = document.createElement("input");
    input.setAttribute("type", "file");
    input.setAttribute("name", "image");
    input.setAttribute("accept", ".jpg, .jpeg, .png");
    input.click();
    input.onchange = async () => {
      if (input.files === null) {
        return;
      }
      let file = (input.files && input.files[0]) || { size: 0 };
      const isOver10MB = file.size > 1024 * 1024 * 10;
      if (isOver10MB) {
        this.$message.error("图片不能超过10MB");
        return;
      }
      const isOver100KB = file.size > 1024 * 100;
      // 超过100KB进行压缩
      if (isOver100KB) {
        const fileBlob: Blob = await this.cropperImage(file);
        // 超过最大尺寸 对尺寸进行压缩后在上传
        file = new File([fileBlob as Blob], file.name, { type: fileBlob.type });
      }
      const data = new FormData();
      data.append("name", "image");
      data.append("file", file);
      try {
        const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(data);
        this.insertEditorImg(url);
      } catch (error) {
        console.log("error", error);
        if ((error as any)?.data?.errNo) {
          this.$message.error((error as any)?.data?.errStr || "图片上传失败，请联系技术人员排查");
        }
      }
    };
  }

  insertEditorImg(url: string) {
    const tinymce = this.$refs.tinymce;

    (tinymce as any).editor.selection.setContent(
      (tinymce as any).editor.dom.createHTML("img", {
        src: url,
        style: "max-width: 600px;",
      }),
    );
  }

  setContent(content: string) {
    // console.log('....setContent', content);
    if (this.editor) {
      this.editor.setContent(content || "");
      this.contentChange();
    }
  }

  mounted() {
    (window as any).domtoimage = domtoimage;
    (window as any).getBlob = async () => {
      // const dom = await tinymcemath2svg();
      // // const htmlDom = iframeDocument.querySelector("html");
      // // this.resetPlaceholderStyle(htmlDom);
      // const defaultOptions = { height: dom.offsetHeight + 88, bgcolor: "transparent", style: { margin: "0", fontSize: "24px", lineHeight: 1.5 }, styleStr: this.styleStr };
      // domtoimage.toSvg(dom as Element, defaultOptions).then((dataUrl: string) => {
      //   console.log("toSvg", dataUrl);
      // });
      try {
        const dom = await tinymcemath2svg();
        const defaultOptions = {
          height: dom.offsetHeight + 88,
          bgcolor: "transparent",
          style: { margin: "0", fontSize: "24px", lineHeight: 1.5 },
          styleStr: this.styleStr,
          beforeMakeSvg: (clonedDoc: { querySelectorAll: (arg0: string) => any[] }) => {
            console.log("onclone", clonedDoc);
            // 1. 移除所有 style 标签中的 @font-face 规则
            clonedDoc.querySelectorAll("style").forEach(styleTag => {
              styleTag.textContent = styleTag.textContent.replace(/@font-face\s*{[^}]*}/g, "");
            });
            // 2. 移除所有 link 标签（如果有字体相关的 link）
            clonedDoc.querySelectorAll('link[rel="stylesheet"]').forEach(linkTag => {
              if (linkTag.href && /font/i.test(linkTag.href)) {
                linkTag.parentNode.removeChild(linkTag);
              }
            });
          },
        };
        console.log("dom", dom, defaultOptions);
        domtoimage.toSvg(dom as Element, defaultOptions).then((dataUrl: string) => {
          console.log("toSvg", dataUrl);
        });
      } catch (error) {
        console.log("has catch????", error);
      }
    };
  }

  public getContent() {
    const iframeDocument = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument;
    const contentDom = iframeDocument.querySelector("#tinymce");
    for (var i = 0; i < contentDom.children.length; i++) {
      if (contentDom.children[i].tagName.toLowerCase() === "div") {
        // 这是一个div，可以进行操作
        console.log(contentDom.children[i]);
        contentDom.removeChild(contentDom.children[i]);
      }
    }
    // contentDom
    // console.log('editor.getContent', contentDom);
    const getContent = contentDom.outerHTML;
    const div = this.editor.dom.create("div");
    div.innerHTML = getContent;
    const elements = div.querySelectorAll(".math-tex");
    for (let i = 0; i < elements.length; i++) {
      const latex = elements[i].getAttribute("data-latex");
      elements[i].removeAttribute("contenteditable");
      elements[i].removeAttribute("style");
      elements[i].removeAttribute("data-latex");
      elements[i].innerHTML = latex;
    }
    // console.log('getContent...', div.innerHTML)
    return div.innerHTML;
  }

  public async genBlob(options?: any) {
    try {
      const dom = await tinymcemath2svg();
      const defaultOptions = {
        height: dom.offsetHeight + 88,
        bgcolor: "transparent",
        style: { margin: "0", fontSize: "24px", lineHeight: 1.5 },
        styleStr: this.styleStr,
      };
      const beforeMakeSvg = (clonedDoc: { querySelectorAll: (arg0: string) => any[] }) => {
        console.log("onclone", clonedDoc);
        // 1. 移除所有 style 标签中的 @font-face 规则
        clonedDoc.querySelectorAll("style").forEach(styleTag => {
          styleTag.textContent = styleTag.textContent.replace(/@font-face\s*{[^}]*}/g, "");
        });
        // 2. 移除所有 link 标签（如果有字体相关的 link）
        clonedDoc.querySelectorAll('link[rel="stylesheet"]').forEach(linkTag => {
          if (linkTag.href && /font/i.test(linkTag.href)) {
            linkTag.parentNode.removeChild(linkTag);
          }
        });
      };

      // console.log("dom", dom, defaultOptions);
      const res = await domtoimage.toBlob(dom as Element, {
        ...(options || defaultOptions),
        beforeMakeSvg,
        styleStr: this.styleStr,
      });
      return res;
    } catch (error) {
      console.log("has catch????", error);
    }
  }
}
</script>

<style lang="less" scoped>
.words-count {
  position: absolute;
  border: 0;
  z-index: 10000;
  bottom: -12px;
  right: 20px;
  font-size: 12px;
  color: #999;
}
</style>

<style lang="less">
.q-form-item.is-error {
  .tox-tinymce {
    border-color: #fa574b !important;
  }
}
.tox-statusbar__wordcount {
  display: none;
}
.underline-dot {
  border-bottom: 2px dotted #000;
}
.wavy-dropdown-show .tox-collection__group {
  display: flex !important;
  margin: 0 8px !important;
}
.wavy-dropdown-show .tox-collection--list .tox-collection__item {
  padding: 0 !important;
}
.wavy-dropdown-show .tox-collection--list .tox-collection__item-label {
  margin-left: 0px !important;
}
.underlinedot {
  text-emphasis-position: under left;
  text-emphasis: dot;
}
.tox .tox-tbtn {
  margin: 4px 5px 4px 5px !important;
  width: auto !important;
  height: auto !important;
}
.tox-toolbar {
  height: 39px !important;
}
.my-tooltip {
  // background: #222;
  color: #fff;
  border-radius: 4px;
  padding: 10px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  transition: opacity 0.2s;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 4px;
  transform: translate(calc(-50%), 0px);
}
/* 箭头部分 */
.my-tooltip::after {
  content: "";
  position: absolute;
  top: -5px; /* 箭头距离 tooltip 顶部的距离 */
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px 5px;
  border-style: solid;
  border-color: transparent transparent #222 transparent;
  width: 0;
  height: 0;
  pointer-events: none;
}
.tox .tox-split-button:hover {
  user-select: none;
  background: #dee0e2;
  border: 0;
  box-shadow: none;
  color: #222f3e;
}
.tox .tox-tbtn--select {
  height: 24px !important;
}
.tox .tox-split-button {
  height: 24px !important;
}
.tox .tox-tbtn--select:hover {
  user-select: none;
  background: #dee0e2;
  border: 0;
  box-shadow: none;
  color: #222f3e;
}
.tox-tinymce {
  border: 1px solid #f0f1f5;
}
#tinymce {
  padding: 12px;
}
.tox-statusbar {
  border-top: 1px solid #f0f1f5 !important;
}
.tox:not([dir="rtl"]) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid #f0f1f5;
}
.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  background: unset !important;
  box-shadow: 0 -1px 0 0 #f0f1f5 inset;
}
.tox-tbtn__select-label {
  width: unset !important;
}
// .tox-toolbar__group {
//   margin-left: 6px !important;
// }
</style>
