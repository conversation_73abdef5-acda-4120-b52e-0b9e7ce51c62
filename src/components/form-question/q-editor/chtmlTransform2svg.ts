
const transformFormula = (dom: {
  outerHTML: string; getAttribute: (arg0: string) => any; innerHTML: string; 
}, cb: { (): void; (): void; }) => {
  // todo: 公式转为图片时有耗时，需要优化
  // const latex = latexStr.replace(/^\(|\)$/g, "");
  const domStyle = window.getComputedStyle(dom as any);
  const fontSize = parseInt(domStyle.fontSize);
  const latex$ = dom.getAttribute('data-latex');
  const div = document.createElement("div");
  div.innerText = latex$;
  console.log("innerText", latex$);
  div.style.display = "none";
  div.style.lineHeight = domStyle.lineHeight;
  div.style.fontSize = domStyle.fontSize;
  document.body.appendChild(div);
  (window as any).MathJax.Hub.Typeset([
    div,
    () => {
      const svgEl = div.querySelector("svg");
      [
        ["version", 1.1],
        ["xmlns", "http://www.w3.org/2000/svg"],
      ].forEach((item: any) => {
        svgEl?.setAttribute(item[0], item[1]);
      });
      if (svgEl) {
        svgEl.style.fontSize = domStyle.fontSize;
        const textDom = svgEl.querySelectorAll('text');
        if (textDom && textDom.length) {
          [...textDom].forEach((ele) => {
            const scale = (ele.getAttribute('transform') || 'scale(52.083) matrix(1 0 0 -1 0 0)').replace('scale(', '').replace(') matrix(1 0 0 -1 0 0)', '') || 1;
            if (scale) {
              const temp = Number(scale) * (16 / fontSize);
              ele.setAttribute('transform', `scale(${temp}) matrix(1 0 0 -1 0 0)`);
              ele.style.fontSize = domStyle.fontSize;
              // console.log('scale...', temp, scale);
            }

          })
        }
      }
      // (svgEl as any).style.color = "#000000";
      const svg = svgEl ? svgEl.outerHTML : "";
      
      
      document.body.removeChild(div);
      dom.outerHTML = svg;

      // console.log('svg。。。', svg);
      cb()
    },
  ]);
}

export const tinymcemath2svg = () => {
  return new Promise<HTMLElement>((resolve) => {
    const iframeDocument = (document.querySelector(".tox-edit-area__iframe") as any).contentDocument;
    // 删除多余的干扰dom
    const offscreenDom = iframeDocument.querySelector('.mce-offscreen-selection');
    if (offscreenDom) {
      offscreenDom.parentNode.removeChild(offscreenDom);
    }
    
    const mathDoms = iframeDocument.querySelectorAll('p .math-tex');
    const bodyDom = iframeDocument.querySelector("#tinymce");
    let doneNumber = 0;
    const maxLen = [...mathDoms].length;
    if (maxLen === 0) {
      resolve(bodyDom);
    }
    [...mathDoms].forEach((dom) => {
      transformFormula(dom, () => {
        doneNumber++;
        if (doneNumber === maxLen) {
          console.log('maxLen', maxLen, 'doneNumber', doneNumber);
          resolve(bodyDom)
        }
      })
    })
  })
}
