<template>
  <el-dialog
    :title="config.title"
    :visible.sync="visible"
    width="1130px"
    height="560px"
    top="0vh"
    custom-class="form-question-dialog"
    :close-on-click-modal="false"
    @close="onDialogClose">
   <el-form
    :model="config.form"
    label-position="left"
    ref="form"
    class="form-question-wrapper">
      <div :key="item.key" v-for="(item) in config.configs" :class="[`${item.props && item.props.customClass}`]">
        <template v-if="['text', 'textarea'].includes(item.type)">
          <QInput :config="item" :form="config.form"></QInput>
        </template>
        <template v-if="item.type === 'select'">
          <QSelect :config="item" :form="config.form"></QSelect>
        </template>
        <template v-if="item.type === 'editor'">
          <QEditor
            :config="item"
            :form="config.form"
            ref="QEditor"
            @itemValidate="itemValidate"></QEditor>
        </template>
        <template v-if="item.type === 'audio'">
          <QAudio
            :config="item"
            :form="config.form"
            @itemValidate="itemValidate"></QAudio>
        </template>
        <template v-if="item.type === 'text-array'">
          <QInputCheck :config="item" :form="config.form"></QInputCheck>
        </template>
      </div>
   </el-form>
   <div slot="footer" class="dialog-footer">
    <el-button @click="onDialogClose">取 消</el-button>
    <el-button type="primary" @click="onDialogConfirm" :loading="loading">
      插入题目
    </el-button>
  </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";
import QInput from './q-input/index.vue';
import QInputCheck from './q-input-check/index.vue';
import QSelect from './q-select/index.vue';
import QEditor from './q-editor/index.vue';
import QAudio from './q-audio/index.vue';
import { Config, Form, ConfigItem } from './index';
import { CATEGORY } from '@/common/constants';
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { cloneDeep } from "lodash-es";
import { formDataConfig } from './config'

@Component({
  components: {
    QInput,
    QSelect,
    QEditor,
    QAudio,
    QInputCheck
  }
})
export default class FormQuestion extends Vue {
  @Prop({
    required: true,
  })
  category!: CATEGORY;

  config = cloneDeep(formDataConfig[this.category]) as Config;

  visible = false

  loading = false

  stopInsert = false

  itemValidate(key: string) {
    (this.$refs.form as any).validateField(key, (...params: any[]) =>  {
      console.log('validateField.params', params);
    });
  }

  onDialogClose() {
    this.visible = false;
    this.loading = false;
    (this.$refs.form as any).clearValidate();
  }

  async onDialogConfirm() {
    this.loading = true;
    (this.$refs.form as any).validate(async (valid: boolean, data: any[]) =>  {
      console.log('validate.params', valid, data);
      if(!valid) {
        this.loading = false;
        showErrorMessage(new Error('题目信息有误，请修改后再插入'))
      } else {
        const cocosData = await this.getCocosData();
        if(this.stopInsert) {
          this.stopInsert = false;
          this.loading = false;
          return;
        }
        bus.$emit('form-question-insert', { category: this.category, data: cocosData })
        this.visible = false
        this.loading = false;
      }
    });
  }
  // word, explains:['...', '...'] audioUrl: , startTime

  async getCocosData() {
    let titleBlob: any = null;
    if(this.$refs.QEditor) {
      titleBlob = await (this.$refs.QEditor as Array<QEditor>)[0].genBlob();
    }
    const { form, configs } = this.config;
    const cocosData: any = {};
    configs.forEach(async (config: ConfigItem) => {
      switch (config.type) {
        case 'editor':
          // eslint-disable-next-line no-case-declarations
          cocosData[config['cocosKey']] = [titleBlob]
          break;
        case 'audio':
          cocosData['audioUrl'] = form.audio.url;
          cocosData['audioDuration'] = form.audio.duration;
          break;
        case 'text-array':
          cocosData[config['cocosKey']] = config.props.max === 1 ? form[config.key]?.[0].value : form[config.key].map((item: any) => item.value)
        break;

        default:
          cocosData[config['cocosKey']] = form[config.key];
      }
    })
    return { ...cocosData, customH5: cloneDeep(form) }
  }

  handleEnhandledrejection(event: any) {
    if(event.reason && event.reason.message && event.reason.message.includes('dom-to-image-img-load-error')) {
      console.warn(`UNHANDLED PROMISE REJECTION:`, event.reason.message);
      const [key, value] = 'dom-to-image-img-load-error: https://yaya.cdnjtzy.com/cw_2f086ea4ca10a3d3fe9adaa65e4015e8-0224d5.png'.split(' ');
      const { form } = this.config;
      const str = form['questionStem'];
      console.log(key, value);
      if(str.includes(value)) {
        this.stopInsert = true;
        showErrorMessage(new Error('题目信息可能有丢失，请再次点击【插入题目】进行重试'))
      }
    }
    event.preventDefault(); // 增加阻止默认事件，阻止页面报错
    // 通过addEventListener绑定的事件，不能通过return false来阻止默认行为
  }

  handleRender(data: { category: CATEGORY, customH5: Form }) {
    this.visible = true;
    const { customH5 } = data;
    if(customH5) {
      this.config.form = cloneDeep(customH5);
    } else {
      this.config = cloneDeep(formDataConfig[this.category]) as Config
    }
    const questionStemConfig = this.config.configs.find((config) => config.key === 'questionStem');
    if (questionStemConfig && questionStemConfig.type === 'editor') {
      const qEditor = this.$refs.QEditor ? ((this.$refs.QEditor as Array<QEditor>)[0]) : null;
      if(qEditor) qEditor.setContent(customH5.questionStem)
    }
    this.$nextTick(() => {
      (this.$refs.form as any).clearValidate();
    })
  }

  mounted() {
    bus.$on('form-question-render', this.handleRender);
    bus.$emit('form-question-mounted');
    window.addEventListener("unhandledrejection", this.handleEnhandledrejection, false);
    this.$once("hook:beforeDestroy", () => {
      window.removeEventListener("unhandledrejection", this.handleEnhandledrejection, false);
      bus.$off('form-question-render', this.handleRender)
    });
  }
}
</script>
