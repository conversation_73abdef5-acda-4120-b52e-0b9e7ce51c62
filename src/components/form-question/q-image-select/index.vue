<template>
  <el-form-item :label="config.label" :required="config.props ? config.props.required : false" class="q-form-item" :prop="config.key" :rules="rules">
    <div class="image-select-wrapper" :class="{ hasSrc: !!src }">
      <div class="image-select" :class="{ hasSrc: !!src }">
        <div class="image-container" v-if="src">
          <div class="edit-btn-container-outer">
            <div class="edit-btn-container">
              <el-tooltip class="item" effect="dark" content="替换图片" placement="top-start">
                <em @click="onClickReplace">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-icontihuantupian"></use>
                  </svg>
                </em>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="裁剪" placement="top-start">
                <em @click="onClickCrop">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-iconcaijian"></use>
                  </svg>
                </em>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="缩放" placement="top-start">
                <em @click="onClickScale">
                  <svg class="cocosicon" aria-hidden="true" style="font-size: 14px;position: relative;top: 2px;">
                    <use xlink:href="#cocos-iconsuofang-youxiang"></use>
                  </svg>
                </em>
              </el-tooltip>
              <!-- <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
              <em @click="onClickDelete">
                <svg class="cocosicon" aria-hidden="true">
                  <use xlink:href="#cocos-icondelete-o"></use>
                </svg>
              </em>
            </el-tooltip> -->
              <el-tooltip class="item" effect="dark" content="下载" placement="top-start">
                <em>
                  <svg class="cocosicon" aria-hidden="true" @click="onClickDownload">
                    <use xlink:href="#cocos-icondownload3"></use>
                  </svg>
                </em>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="去背景" placement="top-start">
                <em @click="onClickRemove">
                  <svg class="cocosicon" aria-hidden="true">
                    <use xlink:href="#cocos-iconeliminate"></use>
                  </svg>
                </em>
              </el-tooltip>
            </div>
          </div>

          <el-button type="text" class="image-del">
            <em @click="onClickDelete">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-icona-shanchuyoushang"></use>
              </svg>
            </em>
          </el-button>
          <img class="image" :src="src" crossOrigin="Anonymous" />
          <!-- <i data-v-d9b6e6a8="" class="el-icon-error" @click="onClickDelete"></i> -->
        </div>
        <div v-else>
          <div class="add-btn" @click="onClickReplace">
            <i class="el-icon-plus"></i>
          </div>
        </div>
      </div>
    </div>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import ImageCropper from "@/components/ImageCropper/index";
import bus from "@/pages/index/common/utils/bus";
import { ConfigItem, Form } from "../index";
import { image2Canvas } from "@/common/utils/resizePic";
import { ImageScaleResult } from "@/types/imageScale";
import ImgBgRemover from "@/components/ImgBgRemoveView";

@Component
export default class QImageSelect extends Vue {
  @Prop({
    required: true,
  })
  config!: ConfigItem;

  @Prop({
    required: true,
  })
  form!: Form;

  @Prop({})
  maxWidth!: number;

  @Prop({})
  maxHeight!: number;

  @Prop({
    required: false,
    default: true,
  })
  isShowDeleteBtn!: boolean;

  @Prop()
  componentId!: number;

  // 是否从编辑器插入
  @Prop({
    required: false,
  })
  isEditorAdd!: boolean;

  isRemovingBg = false;

  check = (...params: any) => {
    const value = params[1];
    const callback = params[2];
    if (!value && this.config.props && this.config.props.required) {
      return callback(new Error(`请添加${this.config.label}`));
    }
    return callback();
  };

  rules = [{ validator: this.check, trigger: "change" }];

  get src() {
    return this.form[this.config.key];
  }

  get containerWidth() {
    return this.$store.state.containerWidth;
  }

  onClickReplace() {
    if (!(window as MyWindow).materialLibraryReady) {
      this.$message.info("素材库正在加载...，请稍后再试");
      return;
    }
    console.log("onClickReplace");
    bus.$emit("resourceLibraryToggle", true);
    bus.$emit("material-show");
    ImageLibrary({
      onConfirm: images => {
        console.log("ImageLibrary-images", images);
        // this.$emit("update:src", images[0].url);
        this.form[this.config.key] = images[0].url;
        bus.$emit("resourceLibraryToggle", false);
        this.$emit("itemValidate", this.config.key);
      },
      maxWidth: this.maxWidth,
      maxHeight: this.maxHeight,
    });
  }

  onClickCrop() {
    ImageCropper({
      src: this.form[this.config.key],
      onClose: () => {
        bus.$emit("resourceLibraryToggle", false);
      },
      onConfirm: url => {
        // this.$emit("update:src", url);
        this.form[this.config.key] = url;
        bus.$emit("resourceLibraryToggle", false);
      },
    });
  }

  onClickScale() {
    // this.isScale = true;
    bus.$emit("show-image-scale-modal", {
      url: this.form[this.config.key],
    });
    bus.$on("image-scale-confirm", this.imageScaleHandler);
  }

  async imageScaleHandler(data: ImageScaleResult) {
    const { url } = data;
    this.form[this.config.key] = url;
    bus.$off("image-scale-confirm", this.imageScaleHandler);
  }

  onClickDelete() {
    // this.$emit("update:src", "");
    this.form[this.config.key] = "";
    this.$emit("itemValidate", this.config.key);
  }

  onClickDownload() {
    // 根据图片地址下载图片到本地
    const img = this.$el.querySelector("img.image");
    const canvas = image2Canvas(img, 1280, 960, 2);
    console.log("img", img, canvas);
    canvas.toBlob(blob => {
      const url = URL.createObjectURL(blob);
      const Link = document.createElement("a");
      Link.download = `tihu-${new Date().getTime()}.png`;
      Link.href = url;
      Link.click();
      Link.remove();
      // 用完释放URL对象
      URL.revokeObjectURL(url);
    });
  }

  onClickRemove() {
    if (this.isRemovingBg) return;
    bus.$emit("material-show");
    this.isRemovingBg = true;
    ImgBgRemover({
      onSuccess: (url: string) => {
        this.form[this.config.key] = url;
        this.isRemovingBg = false;
        bus.$emit("material-close");
      },
      onCancel: () => {
        this.isRemovingBg = false;
        bus.$emit("material-close");
      },
      props: {
        rotate: 0,
        opacity: 1,
        left: 0,
        top: 0,
        scale: Math.round((this.containerWidth / 1280) * 10000) / 10000,
        src: this.src,
        isComponent: false,
      },
      parent: ".canvas-container",
    });
  }
}
</script>

<style lang="less" scoped>
.image-select.hasSrc {
  display: flex;
  justify-content: center;
  background: url("~@/assets/img/transparent.png") repeat;
  background-size: 10px;
}

.image-select-wrapper.hasSrc {
  padding: 10px;
  width: 100px;
  border: 1px solid #dcdfe6;
  display: flex;
  justify-content: center;
  border-radius: 5px;
  margin-right: 10px;
}

.image-select {
  position: relative;
  &:hover {
    .edit-btn-container {
      bottom: 0;
    }
    .image-del {
      display: block;
    }
  }
  .image-container {
    user-select: none;
    position: relative;
    width: 100px;
    height: 100px;
    overflow: hidden;
    &:hover {
      .edit-btn-container-outer {
        bottom: 0;
        transform: translate(0, 0%);
      }
      .image-del {
        display: block;
      }
    }

    .delete-btn {
      position: absolute;
    }

    .edit-btn-container-outer {
      justify-content: center;
      transform: translate(0, 100%);
      .edit-btn-container {
        max-width: 100%;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
      }
      box-sizing: border-box;
      line-height: 14px;
      font-size: 14px;
      width: 100%;
      // height: 20px;
      position: absolute;
      bottom: 0;
      display: flex;
      // opacity: 0;
      transition: all 0.2s linear;
      background: rgba(0, 0, 0, 0.5);
      z-index: 1;
      // bottom: -100%;
      max-width: 100%;
      em {
        font-size: 16px;
        color: #fff;
        padding: 2px;
        cursor: pointer;
        width: 20%;
      }
      &:hover {
        bottom: 0;
      }
    }
    .image-del {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 0;
      height: 20px;
      display: none;
      transition: all 0.2s linear;
      opacity: 0.6;
      span {
        font-size: 20px;
      }
      svg {
        font-size: 24px;
      }
    }

    .image {
      width: 100%;
      height: 100%;
      object-fit: contain;
      min-height: 60px;
    }
  }

  .add-btn {
    border: 1px dashed #dcdfe6;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 120px;
    height: 120px;
    border-radius: 5px;
    transition: all 0.1s linear;

    &:hover {
      border-color: #409eff;
    }
  }
}
</style>
