<template>
  <div class="question-tag">
    <div
      v-for="(tag, index) in titleTags"
      :key="tag.name"
      @drop.stop="draglineDrop($event, index)"
      @dragenter.stop="draglineEnter($event, index)"
      @dragleave.stop="draglineLeave($event, index)"
      @dragend.stop="draglineEnd($event, index)"
      @dragover.prevent="draglineOver($event, index)"
      :draggable="draggable"
      @dragstart="onDragStart($event, index)">
      <el-tag
        :closable="titleTags.length > 1"
        :disable-transitions="false"
        class="button-new-tag"
        size="small"
        :class="{'active' : currentIndex === index}"
        @click="changeQuestion(index)"
        @close="handleClose(index)"
        :draggable="draggable"
        @dragstart="onDragStart($event, index)"
        >
        小题{{index + 1}}
      </el-tag>
    </div>
    <el-button
      class="button-new-tag"
      :disabled="titleTags.length > 4"
      size="small"
      @click="addQuestion"
    >
      + 添加小题
    </el-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
export interface TitleTags {
  name: string,
  id: string
}
// todo 拖拽

@Component
export default class QTags extends Vue {
  @Prop({
    required: true
  })
  titleTags!: TitleTags[];

  @Prop({
    required: true
  })
  currentIndex!: number;

  draggable = true
  dropIndex = -1
  dropStartIndex = -1
  editIndex = -1

  changeQuestion(index: number) {
    console.log('changeQuestion', index);
    this.$emit('select', index);
  }

  handleClose(index: number) {
    this.$confirm('确定删除本小题吗？', '提示', {
      type: 'warning',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: true,
      callback: action => {
        console.log('action', action);
        if(action === 'confirm') {
          console.log('handleClose', index);
          this.$emit('delete', index);
        }
      }
    })
  }

  addQuestion() {
    const addIndex = this.titleTags.length;
    console.log('addQuestion', addIndex);
    this.$emit('add', addIndex);
  }

  onDragStart (_e: any, index: number) {
    console.log('onDragStart',index);
    this.dropStartIndex = index;
  }

  draglineDrop (_e: DragEvent, index: number) {
    if (this.dropIndex === index) return;
    this.dropIndex = -1;
  }

  draglineEnter (e: DragEvent, index: number) {
    this.dropIndex = index;
    e.preventDefault();
  }

  draglineLeave (e: any, index: number) {
    if (e?.fromElement?.className !== 'qst-item-content') {
      this.dropStartIndex = -1;
      return;
    };
    if(this.dropIndex === index) this.dropIndex = -1;
    console.log('draglineLeave', e, e.fromElement.className, index);
  }

  async draglineEnd (e: DragEvent, index: number) {
    console.log('draglineEnd', this.dropIndex, index);
    if (this.dropIndex === -1) return;
    this.$emit('drag', index, this.dropIndex);
    await this.$nextTick();
    this.dropIndex = -1;
    this.dropStartIndex = -1;
  }

  draglineOver (e: DragEvent, index: number) {
    console.log(index);
    e.preventDefault();
  }
}
</script>
<style lang="less">
.q-pure-input {
  input {
    padding-right: 66px !important;
  }
}
</style>
