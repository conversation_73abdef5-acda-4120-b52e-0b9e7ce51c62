<template>
  <el-form-item :label="config.label" :required="config.props && typeof config.props.required !== 'undefined' ? config.props.required: true">
    <el-select placeholder="" v-model="form[config.key]">
      <template v-if="config.props && config.props.optionType">
        <el-option
          v-for="item in config.props ? config.props.options : []"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </template>
      <template v-else>
        <el-option
          v-for="num in config.props ? config.props.options : []"
          :key="num"
          :label="num"
          :value="num"></el-option>
      </template>
    </el-select>
    <span style="margin-left: 5px;">{{ config.props && config.props.afterfix ? config.props.afterfix : '' }}</span>
  </el-form-item>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ConfigItem, Form } from '../index'


@Component
export default class QSelect extends Vue {
  @Prop({
    required: true
  })
  config!: ConfigItem;

  @Prop({
    required: true
  })
  form!: Form;
}
</script>

<style scoped lang="less">

</style>
