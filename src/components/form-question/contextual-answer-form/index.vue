<template>
  <el-dialog :title="config.title" :visible.sync="visible" width="880px" height="560px" top="0vh" custom-class="form-question-dialog" :close-on-click-modal="false" @close="onDialogClose">
    <div class="question-tag">
      <el-tag
        v-for="(tag, index) in titleTags"
        :key="tag.name"
        :closable="titleTags.length > 1"
        :disable-transitions="false"
        class="button-new-tag"
        size="small"
        :class="{ active: currentIndex === index }"
        @click="changeQuestion(index)"
        @close="handleClose(index)"
      >
        小题{{ index + 1 }}
      </el-tag>
      <el-button class="button-new-tag" :disabled="titleTags.length > 4" size="small" @click="addQuestion">
        + 添加小题
      </el-button>
    </div>
    <el-form :model="config.form" label-position="left" ref="form" class="form-question-wrapper">
      <div :key="item.key" v-for="item in config.configs">
        <template v-if="['text', 'textarea'].includes(item.type)">
          <QInput :config="item" :form="config.form"></QInput>
        </template>
        <template v-if="item.type === 'select'">
          <QSelect :config="item" :form="config.form"></QSelect>
        </template>
        <template v-if="item.type === 'audio'">
          <QAudio :config="item" :form="config.form" @itemValidate="itemValidate"></QAudio>
        </template>
        <template v-if="item.type === 'text-array'">
          <QInputCheck :config="item" :form="config.form"></QInputCheck>
        </template>
        <template v-if="item.type === 'input-array'">
          <QInputs :config="item" :form="config.form"></QInputs>
        </template>
      </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onDialogClose">取 消</el-button>
      <el-button type="primary" @click="onDialogConfirm">
        插入题目
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";
import QInput from "@/components/form-question/q-input/index.vue";
import QInputCheck from "@/components/form-question/q-input-check/index.vue";
import QInputs from "@/components/form-question/q-inputs/index.vue";
import QSelect from "@/components/form-question/q-select/index.vue";
import QAudio from "@/components/form-question/q-audio/index.vue";
import { Config, FollowWordsForm } from "./index";
import { CATEGORY } from "@/common/constants";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { cloneDeep } from "lodash-es";
import { ContextualAnswerQuestion } from "@/components/form-question/config/ContextualAnswerQuestion";
@Component({
  components: {
    QInput,
    QSelect,
    QAudio,
    QInputCheck,
    QInputs,
  },
})
export default class ContextualAnswerForm extends Vue {
  @Prop({
    required: true,
  })
  category!: CATEGORY;

  config = (cloneDeep(ContextualAnswerQuestion) as unknown) as Config;

  visible = false;

  currentIndex = 0;

  customH5: FollowWordsForm[] = [];

  // 获取情景对话组件的id
  geContextualAnswerComp() {
    return Object.values(this.$store.state.componentMap).find((item: Component) => item.type === "optionComponent" && item.subType === "contextualAnswer");
  }

  itemValidate(key: string) {
    (this.$refs.form as any).validateField(key, (...params: any[]) => {
      console.log("validateField.params", params);
    });
  }

  onDialogClose() {
    this.visible = false;
    (this.$refs.form as any) && (this.$refs.form as any).clearValidate();
  }

  async onDialogConfirm() {
    const validArrays: number[] = [];
    // 数据验证
    this.customH5.forEach((item, index: number) => {
      Object.keys(item).forEach(key => {
        const val = item[key];
        // console.log('val', key, val);
        // 超过最大输入
        if(item) {
          const theConfig = this.config.configs.find(config => config.key === key);
          // console.log('theConfig', theConfig, val);
          if(theConfig && theConfig.type === 'text' && theConfig.props.max < val.length) {
            !validArrays.includes(index) && validArrays.push(index);
          }
        }
        // 非数组校验
        if (!val || (typeof val.url !== "undefined" && !val.url)) {
          !validArrays.includes(index) && validArrays.push(index);
        }
        // 数组校验
        if ((Array.isArray(val) && val.find(valItem => !valItem.value || valItem.error.length || valItem.validateError?.length))) {
          !validArrays.includes(index) && validArrays.push(index);
        }
      });
    });
    if (validArrays.length) {
      console.log("validArrays", validArrays);
      this.currentIndex = validArrays[0];
      this.changeQuestion(this.currentIndex);
      await this.$nextTick();
    }
    (this.$refs.form as any).validate(async (valid: boolean, data: any[]) => {
      console.log("validate.params", valid, data);
      if (!valid || validArrays.length) {
        showErrorMessage(new Error("题目信息有误，请修改后再插入"));
      } else {
        // cocosData 和 cocosData 保持数据结构的一致性 不在存两份数据
        // 将数据存入组件 并关闭组件 stuQuestion stuQuestionIndex
        this.visible = false;
        const comp = this.geContextualAnswerComp();
        if (comp) {
          this.$store.dispatch("updateComponentsProperties", {
            ids: [(comp as any).id],
            newProperties: {
              ["stuQuestion"]: cloneDeep(this.customH5),
              ["stuQuestionIndex"]: 0,
            },
          });
        } else {
          showErrorMessage(new Error("没有情景对话组件，请先添加情景对话组件"));
        }
      }
    });
  }

  get titleTags() {
    return this.customH5.map((_item, index) => {
      return {
        name: `小题${index + 1}`,
        type: "success",
      };
    });
  }

  handleRender(data: { category: CATEGORY; }) {
    console.log("情景对话-handleRender");
    if (data.category !== CATEGORY.CONTEXTUALANSWERQUESTION) return;
    this.visible = true;
    // 获取数据
    const comp: Component = this.geContextualAnswerComp();
    const { stuQuestion } = comp.properties;
    this.currentIndex = 0;
    if (stuQuestion && stuQuestion.length) {
      this.customH5 = cloneDeep(stuQuestion);
      this.config.form = this.customH5[this.currentIndex];
    } else {
      this.config = (cloneDeep(ContextualAnswerQuestion) as unknown) as Config;
      this.customH5 = [this.config.form];
    }
    this.$nextTick(() => {
      (this.$refs.form as any) && (this.$refs.form as any).clearValidate();
    });
  }

  changeQuestion(index: number) {
    console.log("changeQuestion", index);
    this.currentIndex = index;
    this.$set(this.config, "form", this.customH5[this.currentIndex]);
    (this.$refs.form as any) && (this.$refs.form as any).clearValidate();
  }

  handleClose(index: number) {
    this.$confirm("确定删除本小题吗？", "提示", {
      type: "warning",
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: true,
      callback: action => {
        console.log("action", action);
        if (action === "confirm") {
          console.log("handleClose", index);
          this.customH5.splice(index, 1);
          // curr > index curr - 1
          // curr = index curr
          // curr < index curr
          // 0 1 1 0 0
          let selectIndex = this.currentIndex;
          if (this.currentIndex > index) {
            selectIndex = Math.max(Math.min(this.currentIndex - 1, this.customH5.length - 1), 0);
          }
          if (selectIndex > this.customH5.length - 1) {
            selectIndex = this.customH5.length - 1;
          }
          console.log("selectIndex", selectIndex);
          this.changeQuestion(selectIndex);
        }
      },
    });
  }

  addQuestion() {
    console.log("addQuestion", JSON.stringify(ContextualAnswerQuestion.form));
    const newQuestion = cloneDeep(ContextualAnswerQuestion.form);
    newQuestion.uniqueId = Date.now();
    const addIndex = this.customH5.length;
    this.$set(this.customH5, addIndex, newQuestion);
    this.changeQuestion(addIndex);
    this.$nextTick(() => {
      (this.$refs.form as any) && (this.$refs.form as any).clearValidate();
    });
  }

  mounted() {
    console.log('情景对话-mounted');
    bus.$on("form-dialog-toggle", this.handleRender);
    this.$once("hook:beforeDestroy", () => {
      bus.$off("form-dialog-toggle", this.handleRender);
    });
  }
}
</script>
