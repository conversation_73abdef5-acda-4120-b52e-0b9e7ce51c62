export interface ConfigItemProps {
  tips?: string,
  max: number,
  type?: string,
  required?: boolean,
  afterfix?: string,
  options?: number[] | { label: string, value: string | number }[],
  optionType?: string,
  rows?: number,
  placeholder?: string
}

export interface ConfigItem {
   key: string,
   label: string,
   hideLabel?: boolean,
   type: string,
   cocosKey: string,
   props: ConfigItemProps,
   itemProps?: ConfigItemProps
}

export interface Form {
  questionStem: string,
  answerStart?: string,
  readTime: number,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  playCount: number,
  readyTime: number,
  answerTime: number,
  answers: Array<{ value: string, error: string }>,
  keywords: Array<{ value: string, error: string }>
}

export interface FollowWordsForm {
  uniqueId: number,
  words: Array<{ value: string, error: string }>,
  explains: Array<{ value: string, error: string }>,
  audio: {
    url: string,
    name: string,
    duration: number
  },
  answerTime: number
}

export interface Config {
  title: string,
  configs: Array<ConfigItem>,
  form: FollowWordsForm
}

export interface ErrorItem {
  in_dict: number,
  word: string
}
import './index.less'
import FormQuestion from './index.vue'

export default FormQuestion
