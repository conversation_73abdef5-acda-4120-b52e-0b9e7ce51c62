<template>
  <el-dialog
    :title="config.title"
    :visible.sync="visible"
    width="880px"
    height="560px"
    top="0vh"
    custom-class="form-question-dialog"
    :close-on-click-modal="false"
    @close="onDialogClose">
    <div class="question-tag">
      <el-tag
        v-for="(tag, index) in titleTags"
        :key="tag.name"
        :closable="titleTags.length > 1"
        :disable-transitions="false"
        class="button-new-tag"
        size="small"
        :class="{'active' : currentIndex === index}"
        @click="changeQuestion(index)"
        @close="handleClose(index)">
        小题{{index + 1}}
      </el-tag>
      <el-button
        class="button-new-tag"
        :disabled="titleTags.length > 4"
        size="small"
        @click="addQuestion"
      >
        + 添加小题
      </el-button>
    </div>
    <el-form
      :model="config.form"
      label-position="left"
      ref="form"
      class="form-question-wrapper">
        <div :key="item.key" v-for="(item) in config.configs">
          <template v-if="['text', 'textarea'].includes(item.type)">
            <QInput :config="item" :form="config.form"></QInput>
          </template>
          <template v-if="item.type === 'select'">
            <QSelect :config="item" :form="config.form"></QSelect>
          </template>
          <template v-if="item.type === 'audio'">
            <QAudio
              :config="item"
              :form="config.form"
              @itemValidate="itemValidate"></QAudio>
          </template>
          <template v-if="item.type === 'text-array'">
            <QInputCheck :config="item" :form="config.form"></QInputCheck>
          </template>
          <template v-if="item.type === 'input-array'">
            <QInputs :config="item" :form="config.form"></QInputs>
          </template>
        </div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="onDialogClose">取 消</el-button>
      <el-button type="primary" @click="onDialogConfirm" :loading="loading">
        插入题目
      </el-button>
    </div>
  </el-dialog>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import bus from "@/pages/index/common/utils/bus";
import QInput from '@/components/form-question/q-input/index.vue';
import QInputCheck from '@/components/form-question/q-input-check/index.vue';
import QInputs from '@/components/form-question/q-inputs/index.vue';
import QSelect from '@/components/form-question/q-select/index.vue';
import QAudio from '@/components/form-question/q-audio/index.vue';
import { Config, FollowWordsForm } from './index';
import { CATEGORY } from '@/common/constants';
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import { cloneDeep } from "lodash-es";
import { followWordQuestion } from '@/components/form-question/config/followWordQuestion'
@Component({
  components: {
    QInput,
    QSelect,
    QAudio,
    QInputCheck,
    QInputs
  }
})
export default class FollowWordGroupForm extends Vue {
  @Prop({
    required: true,
  })
  category!: CATEGORY;

  config = cloneDeep(followWordQuestion) as unknown as Config;

  visible = false

  loading = false

  stopInsert = false

  currentIndex = 0

  customH5: FollowWordsForm[] = [];

  itemValidate(key: string) {
    (this.$refs.form as any).validateField(key, (...params: any[]) =>  {
      console.log('validateField.params', params);
    });
  }

  onDialogClose() {
    this.visible = false;
    this.loading = false;
    (this.$refs.form as any).clearValidate();
  }

  async onDialogConfirm() {
    this.loading = true;
    const validArrays: number[] = [];
    // 数据验证
    this.customH5.forEach((item, index: number) => {
      Object.values(item).forEach((val) => {
        console.log('xu-val', val);
        if(!val
        || (typeof val.url !== 'undefined' && !val.url)
        || (Array.isArray(val) && val.find((valItem) => !valItem.value || valItem.error.length))
        ){
          !validArrays.includes(index) && validArrays.push(index)
        }
      })
    });
    if(validArrays.length) {
      console.log('validArrays', validArrays);
      this.currentIndex = validArrays[0];
      this.changeQuestion(this.currentIndex);
      await this.$nextTick();
    }
    (this.$refs.form as any).validate(async (valid: boolean, data: any[]) =>  {
      console.log('validate.params', valid, data);

      if(!valid || validArrays.length) {
        this.loading = false;
        showErrorMessage(new Error('题目信息有误，请修改后再插入'))
      } else {
        const cocosData = this.customH5.map((item) => {
          return {
            uniqueId: item.uniqueId,
            word: item.words[0].value,
            explains: item.explains.map((explain) => explain.value),
            answerTime: item.answerTime,
            audioUrl: item.audio.url
          }
        });
        console.log('cocosData', cocosData);
        if(this.stopInsert) {
          this.stopInsert = false;
          this.loading = false;
          return;
        }
        bus.$emit('follow-word-group-insert', { category: this.category, flatData: cocosData, customH5: cloneDeep(this.customH5) })
        this.visible = false
        this.loading = false;
      }
    });
  }

  get titleTags() {
    return this.customH5.map((_item, index) => {
      return {
        name: `小题${index + 1}`,
        type: 'success'
      }
    })
  }

  handleRender(data: { category: CATEGORY, customH5: FollowWordsForm[], curr: number }) {
    this.visible = true;
    const { customH5, curr } = data;
    this.currentIndex = curr;
    this.customH5 = customH5;
    if(customH5) {
      this.config.form = customH5[curr];
    } else {
      this.config = cloneDeep(followWordQuestion) as unknown as Config;
    }
    this.$nextTick(() => {
      (this.$refs.form as any).clearValidate();
    })
  }

  changeQuestion(index: number) {
    console.log('changeQuestion', index);
    this.currentIndex = index;
    this.$set(this.config, 'form', this.customH5[this.currentIndex]);
    (this.$refs.form as any).clearValidate();
  }

  handleClose(index: number) {
    this.$confirm('确定删除本小题吗？', '提示', {
      type: 'warning',
      closeOnClickModal: false,
      closeOnPressEscape: false,
      showClose: true,
      callback: action => {
        console.log('action', action);
        if(action === 'confirm') {
          console.log('handleClose', index);
          this.customH5.splice(index, 1);
          // curr > index curr - 1
          // curr = index curr
          // curr < index curr
          // 0 1 1 0 0
          let selectIndex = this.currentIndex;
          if(this.currentIndex > index) {
            selectIndex = Math.max(Math.min(this.currentIndex - 1, this.customH5.length - 1), 0);
          }
          if(selectIndex > this.customH5.length - 1) {
            selectIndex = this.customH5.length - 1;
          }
          console.log('selectIndex', selectIndex);
          this.changeQuestion(selectIndex);
        }
      }
    })
  }

  addQuestion() {
    console.log('addQuestion', JSON.stringify(followWordQuestion.form));
    const newQuestion = cloneDeep(followWordQuestion.form);
    newQuestion.uniqueId = Date.now();
    const addIndex = this.customH5.length;
    this.$set(this.customH5, addIndex, newQuestion)
    this.changeQuestion(addIndex);
    this.$nextTick(() => {
      (this.$refs.form as any).clearValidate();
    })
  }

  mounted() {
    bus.$on('follow-word-group-render', this.handleRender);
    this.$once("hook:beforeDestroy", () => {
      bus.$off('follow-word-group-render', this.handleRender)
    });
  }
}
</script>
