/* eslint-disable */
import tinycolor from 'tinycolor2'
import { colorFormat } from './colorFormat'
export default {
  props: ['value'],
  data() {
    return {
      val: colorFormat(this.value)
    }
  },
  computed: {
    colors: {
      get() {
        return this.val
      },
      set(newVal) {
        this.val = newVal
        this.$emit('input', newVal)
      }
    }
  },
  watch: {
    value: {
    　　handler(newVal) {
          this.val = colorFormat(newVal)
    　　},
    　　immediate: true,
        deep: true,
    },
  },
  methods: {
    colorChange(data, oldHue) {
      this.oldHue = this.colors.hsl.h
      this.$set(this, 'colors', colorFormat(data, oldHue || this.oldHue))
    },
    isValidHex(hex) {
      return tinycolor(hex).isValid()
    },
    simpleCheckForValidColor(data) {
      var keysToCheck = ['r', 'g', 'b', 'a', 'h', 's', 'l', 'v']
      var checked = 0
      var passed = 0

      for (var i = 0; i < keysToCheck.length; i++) {
        var letter = keysToCheck[i]
        if (data[letter]) {
          checked++
          if (!isNaN(data[letter])) {
            passed++
          }
        }
      }

      if (checked === passed) {
        return data
      }
    }
  }
}
