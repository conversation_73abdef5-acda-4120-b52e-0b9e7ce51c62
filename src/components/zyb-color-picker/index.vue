<!--
 * @Date: 2021-12-01 18:30:46
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-03 17:19:56
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/index.vue
 * @Author: chxu
-->
<template>
  <div>
    <span class="color-label">{{ label }}</span>
    <div class="color-container">
      <color-picker ek-sign="bgcolor" class="color-select" :property="property" v-model="color" :clearable="clearable"></color-picker>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import color from "./ColorPicker.vue";
import { toHexString } from "@/components/zyb-color-picker/colorPipette/utils";

export default {
  components: {
    "color-picker": color,
  },
  props: {
    // 当前颜色值
    value: {
      type: String,
    },
    // 默认颜色
    defaultColor: {
      type: String,
      default: "rgba(255, 255, 255, 0)",
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否允许清空
    clearable: {
      type: Boolean,
      default: false,
    },
    content: {
      type: String,
      default: "",
    },

    property: {
      type: String,
      default: "color",
    },
    componentIds: {
      type: Array,
    },
    // 当前颜色值
    label: {
      type: String,
    },
  },
  data() {
    return {};
  },
  computed: {
    color: {
      get() {
        const firstVal = this.$store.state.componentMap[this.componentIds[0]].properties[this.property];
        const hasDifferentVal = this.componentIds.some(id => this.$store.state.componentMap[id].properties[this.property] !== firstVal);
        if (hasDifferentVal) {
          return "";
        }
        console.warn(this.property, firstVal);
        return firstVal;
      },
      set(val) {
        this.$store.dispatch("updateComponentsProperties", {
          ids: this.componentIds,
          newProperties: {
            [this.property]: toHexString(val),
          },
        });
      },
    },
  },
  mounted() {},
};
</script>

<style lang="less" scoped>
.color-select {
  display: flex;
  align-items: center;
  .color-label {
    margin-right: 10px;
  }
}
</style>
