<template>
  <el-popover placement="top" width="190" v-model="visible" @show="openPanel">
    <template v-if="visible">
      <div class="zyb-color-container">
        <div class="box" id="boxPanel" :class="{ open: openStatus }" :style="styleObj">
          <div class="bd" v-if="!simple">
            <h3>主题颜色</h3>
            <ul class="tColor">
              <li v-for="(color, index) of tColor" :key="index" :style="`background-color: ${color}`" @mouseover="hoveColor = color" @mouseout="hoveColor = null" @click="updataValue(color)"></li>
            </ul>
            <ul class="bColor">
              <li v-for="(item, index) of cColor" :key="index">
                <ul>
                  <li v-for="(color, index) of item" :key="index" :style="`background-color: ${color}`" @mouseover="hoveColor = color" @mouseout="hoveColor = null" @click="updataValue(color)"></li>
                </ul>
              </li>
            </ul>
            <h3>标准颜色</h3>
            <ul class="tColor">
              <li v-for="(color, index) of bColor" :key="index" :style="`background-color: ${color}`" @mouseover="hoveColor = color" @mouseout="hoveColor = null" @click="updataValue(color)"></li>
            </ul>
            <div class="lastestColor" v-show="latestColors.length > 0">
              <h3>最近使用</h3>
              <ul class="tColor">
                <li
                  v-for="(color, index) of latestColors"
                  :key="index"
                  :style="`background-color: ${color}`"
                  @mouseover="hoveColor = color"
                  @mouseout="hoveColor = null"
                  @click="updataValue(color)"
                ></li>
              </ul>
            </div>
            <div class="more-color-area">
              <el-tooltip effect="dark" placement="top" content="取色器" :open-delay="tooltipDelay">
                <div
                  :class="[
                    {
                      sucker: true,
                      'sucker-active': colorData.state,
                    },
                  ]"
                  @click.stop="beginGetColor"
                >
                  <span class="sucker-icon"></span>
                  <span>取色器</span>
                </div>
              </el-tooltip>
              <h3 style="flex: 1;" v-if="clearable">
                <el-button type="text" size="small" style="margin-left: 40px;cursor: pointer;" @click="updataValue('')">清空</el-button>
              </h3>
              <h3 @click="moreColorPanel" class="moreColor">
                更多颜色...
              </h3>
            </div>
          </div>
          <div class="bd" v-else>
            <ul class="tColor">
              <li
                v-for="(color, index) of simpleColors"
                :key="index"
                :style="`background-color: ${color}`"
                @mouseover="hoveColor = color"
                @mouseout="hoveColor = null"
                @click="updataValue(color)"
              ></li>
            </ul>
          </div>
        </div>
        <sketch-picker v-model="showColor" v-if="ctrl.colorPanel" @childChange="updataValue" :style="sketchStyle"></sketch-picker></div
    ></template>

    <div class="zyb-color-wrapper" slot="reference">
      <div class="zyb-color-picker">
        <span class="zyb-color-picker-color is-alpha">
          <span class="zyb-color-picker-color-inner" :style="`background-color: ${showColor}`">
            <el-button icon="el-icon-close" v-if="clearable && simple && value" size="mini" @click="updataValue('')"></el-button>
          </span>
        </span>
      </div>
    </div>
  </el-popover>
</template>

<script>
/* eslint-disable */
import sketch from "./Sketch.vue";
import clickOutside from "@/common/utils/clickoutside";
import ColorPipette from "@/components/zyb-color-picker/colorPipette/colorPipette";
import { tColor, colorConfig, bColor, cColor } from "./color-config";

let defaultProps = {
  hex: "#194d33",
  hsl: {
    h: 150,
    s: 0.5,
    l: 0.2,
    a: 1,
  },
  hsv: {
    h: 150,
    s: 0.66,
    v: 0.3,
    a: 1,
  },
  rgba: {
    r: 25,
    g: 77,
    b: 51,
    a: 1,
  },
  a: 1,
};

export default {
  components: {
    "sketch-picker": sketch,
  },
  props: {
    // 当前颜色值
    value: {
      type: String,
    },
    // 默认颜色
    defaultColor: {
      type: String,
      default: "rgba(255, 255, 255, 0)",
    },
    content: {
      type: String,
      default: "",
    },
    property: {
      type: String,
      default: "color",
    },
    simple: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    simpleColors: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      tooltipDelay: 800,
      colorData: {}, // 后期删除
      isBeginGetColor: false,
      latestColors: [],
      colors: defaultProps,
      // 面板打开状态
      openStatus: false,
      // 鼠标经过的颜色块
      hoveColor: null,
      pipette: null,
      // 主题颜色
      tColor: tColor,
      // 颜色面板
      colorConfig: colorConfig,
      // 标准颜色
      bColor: bColor,
      cColor: cColor,
      ctrl: {
        colorPanel: false,
      },
      styleObj: {
        // top: "-155px",
      },
      sketchStyle: {
        top: "",
      },
    };
  },
  computed: {
    bgc() {
      return this.colors.hex;
    },
    // 显示面板颜色
    showPanelColor() {
      if (this.hoveColor) {
        return this.hoveColor;
      } else {
        return this.showColor;
      }
    },
    // 显示颜色
    showColor: {
      get() {
        if (this.value) {
          return this.value;
        } else {
          return this.defaultColor;
        }
      },
      set(val) {},
    },
    // 颜色面板
    colorPanel() {
      return this.colorConfig.map(item => {
        return item.reverse();
      });
    },
  },
  methods: {
    async beginGetColor() {
      if (this.isBeginGetColor || this.colorData.state) {
        return false;
      }
      this.isBeginGetColor = true;
      // 开始取色
      const container = document.querySelector(".canvas-container");
      this.pipette = new ColorPipette({
        container,
        // container: document.body,
        scale: 1280 / container.getBoundingClientRect().width,
        listener: {
          onOk: color => {
            this.isBeginGetColor = false;
            this.updataValue(color);
            this.closePanel();
          },
          onESC: () => {
            this.isBeginGetColor = false;
            this.closePanel();
          },
        },
      });
      this.pipette.start();
    },
    moreColorPanel() {
      this.openStatus = false;
      this.ctrl.colorPanel = true;
    },
    openDrop(e) {
      this.openStatus = !this.openStatus;
      if (this.ctrl.colorPanel) {
        this.ctrl.colorPanel = false;
        this.openStatus = false;
      }
    },
    closePanel() {
      this.visible = false;
      this.openStatus = false;
      this.ctrl.colorPanel = false;
    },
    openPanel() {
      this.openStatus = true;
      this.ctrl.colorPanel = false;
    },
    // 更新组件的值 value
    updataValue(value) {
      this.latestColors = this.getLocalStorage();
      this.$emit("input", value);
      this.$emit("change", value);
      if (value === "transparent") return;
      let index = this.latestColors.indexOf(value);
      if (index !== -1) {
        this.latestColors.splice(index, 1);
      }
      if (this.latestColors.length >= 10) {
        this.latestColors.pop();
      }
      this.latestColors.unshift(value);
      this.setLocalStorage(this.latestColors);
    },
    // 设置默认颜色
    handleDefaultColor() {
      this.updataValue(this.defaultColor);
    },
    // 格式化 hex 颜色值
    parseColor(hexStr) {
      if (hexStr.length === 4) {
        hexStr = "#" + hexStr[1] + hexStr[1] + hexStr[2] + hexStr[2] + hexStr[3] + hexStr[3];
      } else {
        return hexStr;
      }
    },
    // RGB 颜色 转 HEX 颜色
    rgbToHex(r, g, b) {
      let hex = ((r << 16) | (g << 8) | b).toString(16);
      return "#" + new Array(Math.abs(hex.length - 7)).join("0") + hex;
    },
    // HEX 转 RGB 颜色
    hexToRgb(hex) {
      hex = this.parseColor(hex);
      let rgb = [];
      for (let i = 1; i < 7; i += 2) {
        rgb.push(parseInt("0x" + hex.slice(i, i + 2)));
      }
      return rgb;
    },
    getLocalStorage() {
      return localStorage.getItem("latestColors") ? JSON.parse(localStorage.getItem("latestColors"))["pickColor"] : [];
    },
    setLocalStorage(latestColors) {
      localStorage.setItem("latestColors", JSON.stringify({ pickColor: latestColors }));
    },
  },
  created() {
    this.latestColors = this.getLocalStorage();
  },
  directives: {
    clickOutside,
  },
};
</script>

<style lang="less">
.transparent {
  height: 20px;
  border: 1px solid #e1e3e6;
  border-radius: 4px;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #525c66;
  margin-bottom: 16px;
  cursor: pointer;
}
.colorBtn {
  width: 15px;
  height: 15px;
}
.box {
  // position: absolute;
  // width: 190px;
  background: #fff;
  // border: 1px solid #ddd;
  /* visibility: hidden; */
  // border-radius: 2px;
  margin-top: 2px;
  // padding: 12px;
  // box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);
  opacity: 1;
  transition: all 0.3s ease;
  z-index: 2007;
  box-sizing: content-box !important;
  // left: -85px;
  // top: 30px;
  display: none;
  &.open {
    display: block;
  }
}
.box h3 {
  margin: 0;
  font-size: 12px;
  font-weight: normal;
  margin-bottom: 8px;
  line-height: 1;
  color: #525c66;
  img {
    margin-right: 6px;
  }
}
.moreColor {
  cursor: pointer;
  img {
    vertical-align: -3px;
  }
}
/* .box.open{ visibility: visible; opacity: 1; } */
.hd {
  overflow: hidden;
  line-height: 29px;
}
.hd .colorView {
  width: 100px;
  height: 30px;
  float: left;
  transition: background-color 0.3s ease;
}
.hd .defaultColor {
  width: 80px;
  float: right;
  text-align: center;
  border: 1px solid #ddd;
  cursor: pointer;
}
.tColor li {
  width: 15px;
  height: 15px;
  display: inline-block;
  margin: 0 2px;
  transition: all 0.3s ease;
}
.tColor li:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  transform: scale(1.3);
}
.bColor {
  margin-bottom: 16px;
}
.bColor li {
  width: 15px;
  display: inline-block;
  margin: 0 2px;
}
.bColor li li {
  display: block;
  width: 15px;
  height: 15px;
  transition: all 0.3s ease;
  margin: 0;
}
.bColor li li:hover {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
  transform: scale(1.3);
}
.lastestColor {
  margin-top: 16px;
}
.zyb-color-wrapper {
  display: inline-block;
  position: relative;
  line-height: normal;
  box-sizing: border-box;
  width: 48px;
  height: 28px;
  border: 1px solid #e1e2e6;
  border-radius: 2px;
  &:focus {
    outline: none;
  }
}
.zyb-color-picker {
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background-color: #fff;
  font-size: 0;
  position: relative;
  cursor: pointer;
  padding: 3px;
  &:focus {
    outline: none;
  }
}
.zyb-color-picker-color.is-alpha {
  position: relative;
  display: block;
  box-sizing: border-box;
  // border: 1px solid #999;
  border-radius: 2px;
  width: 100%;
  height: 100%;
  text-align: center;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
}
.zyb-color-picker .zyb-color-picker-color-inner {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: transparent;
  button {
    display: none;
    background: rgba(255, 255, 255, 0.3);
  }
  &:hover {
    button {
      display: block;
    }
  }
}
.more-color-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  h3 {
    margin-bottom: 0;
  }
  .sucker {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 400;
    color: rgba(82, 92, 102, 1);
    .sucker-icon {
      width: 16px;
      height: 16px;
      background: url("~@/assets/img/xiguan.svg") no-repeat;
      margin-right: 6px;
    }
  }
  .sucker-active {
    .sucker-icon {
      background: url("~@/assets/img/xiguan-active.svg") no-repeat;
    }
  }
}
.color-pointer {
  position: absolute;
  width: 20px;
  height: 20px;
  left: -24px;
  top: -26px;
  // opacity: 0.1;
  // border: 1px solid red;
  background: url("~@/assets/img/xiguan.svg") no-repeat;
}
</style>
