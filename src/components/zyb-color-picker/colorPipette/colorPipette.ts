/*
 * @Date: 2021-11-30 10:49:56
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-22 17:50:38
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/colorPipette/colorPipette.ts
 * @Author: chxu
 */
/**
 * 网页颜色吸管工具【拾色器】
 * date: 2021.10.31
 * author: alanyf
 */
 import domtoimage from './dom-to-image.js';
 import { drawTooltip, getCanvas, getCanvasRectColor, loadImage, rbgaObjToHex, renderColorInfo } from './utils';
 import type { Props, Rect } from './interface';
 
 export * from './interface';
 
 /**
  * 网页拾色器【吸管工具】
  */
 class ColorPipette {
   container: any = {};
   listener: Record<string, (e: any) => void> = {};
   rect: Rect = { x: 0, y: 0, width: 0, height: 0 };
   canvas: any = {};
   ctx: any;
   scale = 1;
   magnifier: any = null;
   colorContainer: any = null;
   colors: string[][] = [];
   tooltipVisible = true;
   useMagnifier = false;
   constructor(props: Props) {
     try {
       const { container, listener, scale = 1, useMagnifier = false } = props;
       this.container = container || document.body;
       this.listener = listener || {};
       this.rect = this.container.getBoundingClientRect();
      //  this.rect.width = 811;
      //  this.rect.height = 608;
       this.scale = scale > 4 ? 4 : scale;
       this.useMagnifier = useMagnifier;
       // 去除noscript标签，可能会导致
       const noscript = document.body.querySelector('noscript');
       noscript?.parentNode?.removeChild(noscript);
       this.initCanvas();
     } catch (err) {
       console.error(err);
       this.destroy();
     }
   }
   /**
    * 初始化canvas
    */
   initCanvas() {
     const { rect, scale } = this;
     const { x, y, width, height } = rect;
     const { canvas, ctx } = getCanvas({
       width: rect.width,
       height: rect.height,
       scale,
       attrs: {
         class: 'color-pipette-canvas-container',
         style: `
           position: fixed;
           left: ${x}px;
           top: ${y}px;
           z-index: 10000;
           cursor: none;
           width: ${width}px;
           height: ${height}px;
           opacity: 1;
         `,
       },
     });
     this.canvas = canvas;
     this.ctx = ctx;
   }
   /**
    * 开始
    */
   async start() {
     try {
       await this.drawCanvas();
       document.body.appendChild(this.canvas);
       const tooltip = drawTooltip('按Esc可退出');
       document.body.appendChild(tooltip);
       setTimeout(() => tooltip?.parentNode?.removeChild(tooltip), 2000);
       // 添加监听
       this.canvas.addEventListener('mousemove', this.handleMove);
       this.canvas.addEventListener('mousedown', this.handleDown);
       document.addEventListener('keydown', this.handleKeyDown);
     } catch (err) {
       console.error(err);
       this.destroy();
     }
   }
   /**
    * 结束销毁dom，清除事件监听
    */
   destroy() {
     this.canvas.removeEventListener('mousemove', this.handleMove);
     this.canvas.removeEventListener('mousedown', this.handleDown);
     document.removeEventListener('keydown', this.handleKeyDown);
     this.canvas?.parentNode?.removeChild(this.canvas);
     this.colorContainer?.parentNode?.removeChild(this.colorContainer);
   }
 
   /**
    * 将dom节点画到canvas里
    */
   async drawCanvas() {
     const base64 = await domtoimage.toPng(this.container, {
       filter: (node: { id: string; }) => node.id !== 'GameCanvas'
     }).catch(() => '');
     if (!base64) {
       return;
     }
     const img = await loadImage(base64);
     if (!img) {
       return;
     }
     this.ctx.drawImage(img, 0, 0, this.rect.width, this.rect.height);
      const texture = await (window as MyWindow).cocos.screenshot();
      const data = texture.readPixels();
      const width = texture.width;
      const height = texture.height;
      const tempData: Uint8ClampedArray = new Uint8ClampedArray(data.length);
      const rowBytes = width * 4;
      for (let row = 0; row < height; row++) {
        const srow = height - 1 - row;
        const start = srow * width * 4;
        const tStart = row * width * 4;
        for (let i = 0; i < rowBytes; i++) {
          tempData[tStart + i] = data[start + i];
        }
      }
      const imageData = new ImageData(tempData, width, height);
      this.ctx.putImageData(imageData, 0, 0);
   }
 
   /**
    * 处理鼠标移动
    */
   handleMove = (e: any) => {
     const { rect } = this;
     let { pageX: x, pageY: y } = e;
     x -= rect.x;
     y -= rect.y;
     const color = this.getPointColor(x, y);
     const { onChange = () => '' } = this.listener;
     const point = { x: e.pageX + 15, y: e.pageY + 15 };
     const colorContainer = renderColorInfo({
       containerDom: this.colorContainer,
       color,
       point,
     });
     if (!this.colorContainer) {
       this.colorContainer = colorContainer;
       document.body.appendChild(colorContainer);
     }
     onChange(color);
   }
 
   /**
    * 处理鼠标按下
    */
   handleDown = (e: any) => {
     const { onOk = () => '' } = this.listener;
     let { pageX: x, pageY: y } = e;
     x -= this.rect.x;
     y -= this.rect.y;
     const color = this.getPointColor(x, y);
     onOk(color);
     this.destroy();
   }
 
   /**
    * 处理键盘按下Esc退出拾色
    */
   handleKeyDown = (e: KeyboardEvent) => {
     if (e.code === 'Escape') {
      const { onESC = () => '' } = this.listener;
       this.destroy();
       onESC(e);
     }
   };
 
   /**
    * 获取鼠标点周围的颜色整列
    */
   getPointColors(e: any) {
     const { ctx, rect, scale } = this;
     let { pageX: x, pageY: y } = e;
     x -= rect.x;
     y -= rect.y;
     const color = this.getPointColor(x, y);
     const size = 19;
     const half = Math.floor(size / 2);
     const info = { x: x - half, y: y - half, width: size, height: size };
     const colors = getCanvasRectColor(ctx, info, scale);
     return { color, colors };
   }
 
   /**
    * 获取鼠标点的颜色
    */
   getPointColor(x: number, y: number) {
     const { scale } = this;
     const { data } = this.ctx.getImageData(Math.round(x * scale), Math.round(y * scale), 1, 1);
     const r = data[0];
     const g = data[1];
     const b = data[2];
     const a = data[3] / 255;
     const rgba = { r, g, b, a };
     return rbgaObjToHex(rgba);
   }
 }
 
 export default ColorPipette;
 
 