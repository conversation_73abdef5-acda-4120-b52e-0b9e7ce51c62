/*
 * @Date: 2021-11-30 10:54:05
 * @LastEditors: chxu
 * @LastEditTime: 2021-11-30 11:01:15
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/colorPipette/interface.ts
 * @Author: chxu
 */
export interface Point {
  x: number;
  y: number;
}

export interface Rect {
  x: number;
  y: number;
  width: number;
  height: number;
}

export type Colors = string[][];

export interface Rgba {
  r: number;
  g: number;
  b: number;
  a: number;
}

export interface Props {
  container: any;
  listener?: Record<string, (e: any) => void>;
  scale?: number;
  useMagnifier?: boolean;
}
