/*
 * @Date: 2021-09-02 19:11:32
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-03 17:22:42
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/colorFormat.js
 * @Author: chxu
 */
import tinycolor from "tinycolor2";

export const colorFormat = (data, oldHue) => {
  let alpha = data && data.a;
  let color;

  // hsl is better than hex between conversions
  if (data && data.hsl) {
    color = tinycolor(data.hsl);
  } else if (data && data.hex && data.hex.length > 0) {
    color = tinycolor(data.hex);
  } else if (typeof data === 'string') {
    color = tinycolor(data.slice(0, 7));
    // 透明度
    if (data.length === 9) {
      color.setAlpha(Math.round(parseInt(data.slice(7), 16) * 100 / 255) / 100);
      alpha = Math.round(parseInt(data.slice(7), 16) * 100 / 255) / 100;
    }
  } else {
    color = tinycolor(data);
  }
  if (color && (color._a === undefined || color._a === null || color._a === 1)) {
    color.setAlpha(alpha || 1);
  }

  const hsl = color.toHsl();
  const hsv = color.toHsv();

  if (hsl.s === 0) {
    hsv.h = hsl.h = data.h || (data.hsl && data.hsl.h) || oldHue || 0;
  }
  return {
    hsl: hsl,
    hex: color.toHexString().toUpperCase(),
    rgba: color.toRgb(),
    hsv: hsv,
    oldHue: data.h || oldHue || hsl.h,
    source: data.source,
    a: data.a || color.getAlpha(),
  };
};
