<!--
 * @Date: 2021-12-03 14:19:43
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-03 17:23:21
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/comprise/EditableInput.vue
 * @Author: chxu
-->
<template>
  <div class="vc-editable-input">
    <span class="vc-input__label">{{ label }}</span>
    <input
      class="vc-input__input"
      :aria-label="desc ? label + '(' + desc + ')' : label"
      v-model="val"
      @focus="$event.target.select()"
      @input="update"
      :disabled="disabled"
      ref="input"
    />
    <span class="vc-input__desc">{{ desc }}</span>
  </div>
</template>

<script>
/* eslint-disable */
export default {
  name: "editableInput",
  props: {
    label: String,
    desc: String,
    value: [String, Number],
    max: Number,
    min: Number,
    arrowOffset: {
      type: Number,
      default: 1,
    },
    disabled: false,
  },
  computed: {
    val: {
      get() {
        return this.value;
      },
      set(v) {
        if (!(this.max === undefined) && +v > this.max) {
          this.$refs.input.value = this.max;
        } else {
          return v;
        }
      },
    },
  },
  methods: {
    update(e) {
      this.handleChange(e.target.value);
    },
    handleChange(newVal) {
      let data = {};
      data[this.label] = newVal;
      if (data.hex === undefined && data["#"] === undefined) {
        this.$emit("change", data);
      } else if (newVal.length > 5) {
        this.$emit("change", data);
      }
    }
  },
};
</script>

<style lang="less">
.vc-editable-input {
  position: relative;
}
.vc-input__input {
  padding: 0;
  border: 0;
  outline: none;
  &:disabled {
    cursor: not-allowed;
  }
}
.vc-input__label {
  text-transform: capitalize;
}
</style>
