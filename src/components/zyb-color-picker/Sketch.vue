<!--
 * @Date: 2021-09-02 19:11:32
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-07 11:05:59
 * @FilePath: /interactive-question-editor/src/components/zyb-color-picker/Sketch.vue
 * @Author: chxu
-->
<template>
  <div :class="['vc-sketch', disableAlpha ? 'vc-sketch__disable-alpha' : '']">
    <div class="vc-sketch-saturation-wrap">
      <saturation v-model="colors" @change="childChange"></saturation>
    </div>
    <div class="vc-sketch-controls">
      <div class="vc-sketch-sliders">
        <div class="vc-sketch-hue-wrap">
          <hue v-model="colors" @change="childChange"></hue>
        </div>
        <div class="vc-sketch-alpha-wrap" v-if="!disableAlpha">
          <alpha v-model="colors" @change="childChange"></alpha>
        </div>
      </div>
      <div class="vc-sketch-color-wrap">
        <div
          class="vc-sketch-active-color"
          :style="{ background: activeColor }"
        ></div>
        <checkboard></checkboard>
      </div>
    </div>
    <div class="vc-sketch-field" v-if="!disableFields">
      <!-- rgba -->
      <div class="vc-sketch-field--double">
        <ed-in label="hex" :value="hex" @change="inputChange"></ed-in>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import colorMixin from "./color";
import editableInput from "./comprise/EditableInput.vue";
import saturation from "./comprise/Saturation.vue";
import hue from "./comprise/Hue.vue";
import alpha from "./comprise/Alpha.vue";
import checkboard from "./comprise/Checkboard.vue";
import { colorFormat } from "./colorFormat";
import { presetColors } from "./color-config";

export default {
  name: "Sketch",
  mixins: [colorMixin],
  components: {
    saturation,
    hue,
    alpha,
    "ed-in": editableInput,
    checkboard,
  },
  props: {
    presetColors: {
      type: Array,
      default() {
        return presetColors;
      },
    },
    disableAlpha: {
      type: Boolean,
      default: true,
    },
    disableFields: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    hex() {
      return this.colors.hex.replace("#", "");
    },
    activeColor() {
      var rgba = this.colors.rgba;
      return "rgba(" + [rgba.r, rgba.g, rgba.b, rgba.a].join(",") + ")";
    },
  },
  methods: {
    handlePreset(c) {
      this.colorChange({
        hex: c,
        source: "hex",
      });
    },
    childChange(data) {
      this.colorChange(data);
      let color = colorFormat(data).rgba;
      this.$emit(
        "childChange",
        `rgba(${color.r}, ${color.g}, ${color.b}, ${color.a})`,
      );
    },
    inputChange(data) {
      if (!data) {
        return;
      }
      if (data.hex) {
        this.isValidHex(data.hex) &&
          this.colorChange({
            hex: data.hex,
            source: "hex",
          });
        this.$emit("childChange", `#${data.hex}`);
      } else if (data.r || data.g || data.b || data.a) {
        this.colorChange({
          r: data.r || this.colors.rgba.r,
          g: data.g || this.colors.rgba.g,
          b: data.b || this.colors.rgba.b,
          a: data.a || this.colors.rgba.a,
          source: "rgba",
        });
        this.$emit("childChange", colorFormat(this.colors.rgba).hex);
      }
    },
  },
};
</script>

<style>
.vc-sketch {
  z-index: 2007;
  width: 190px;
  box-sizing: initial;
  background: #fff;
}
.vc-sketch-saturation-wrap {
  width: 100%;
  padding-bottom: 75%;
  position: relative;
  overflow: hidden;
}
.vc-sketch-controls {
  display: flex;
}
.vc-sketch-sliders {
  padding: 4px 0;
  flex: 1;
}
.vc-sketch-sliders .vc-hue,
.vc-sketch-sliders .vc-alpha-gradient {
  border-radius: 2px;
}
.vc-sketch-hue-wrap {
  position: relative;
  height: 10px;
}
.vc-sketch-alpha-wrap {
  position: relative;
  height: 10px;
  margin-top: 4px;
  overflow: hidden;
}
.vc-sketch-color-wrap {
  width: 24px;
  height: 24px;
  position: relative;
  margin-top: 4px;
  margin-left: 4px;
  border-radius: 3px;
}
.vc-sketch-active-color {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 2px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15),
    inset 0 0 4px rgba(0, 0, 0, 0.25);
  z-index: 2;
}
.vc-sketch-color-wrap .vc-checkerboard {
  background-size: auto;
}

.vc-sketch-field {
  display: flex;
  padding-top: 4px;
}
.vc-sketch-field .vc-editable-input {
  width: 190px;
  margin: auto;
}
.vc-sketch-field .vc-input__input {
  /* display: inline-block;
  width: calc(200px - 22px);  */
  padding: 4px 10% 3px;
  width: calc(190px - 28px);
  border: none;
  box-sizing: border-box;
  text-align: center;
  box-shadow: inset 0 0 0 1px #ccc;
  font-size: 11px;
}
.vc-sketch-field .vc-input__label {
  /* display: inline-block;
  width: 22px; */
  text-align: center;
  font-size: 11px;
  color: #222;
  padding-top: 3px;
  padding-bottom: 4px;
  text-transform: capitalize;
}
.vc-sketch-field--single {
  flex: 1;
  padding-left: 6px;
}
.vc-sketch-field--double {
  flex: 2;
}
.vc-sketch-presets {
  margin-right: -10px;
  margin-left: -10px;
  padding-left: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.vc-sketch-presets-color {
  border-radius: 3px;
  overflow: hidden;
  position: relative;
  display: inline-block;
  margin: 0 10px 10px 0;
  vertical-align: top;
  cursor: pointer;
  width: 16px;
  height: 16px;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.15);
}

.vc-sketch__disable-alpha .vc-sketch-color-wrap {
  height: 10px;
}
</style>
