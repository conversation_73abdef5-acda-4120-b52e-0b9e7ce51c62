<template>
  <div class="image-select" :class="{ hasSrc: !!src }">
    <div class="image-container" v-if="src">
      <div class="edit-btn-container-outer">
        <div class="edit-btn-container">
          <el-tooltip class="item" effect="dark" content="替换图片" placement="top-start">
            <em @click="onClickReplace" v-if="!disableReplace">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-icontihuantupian"></use>
              </svg>
            </em>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="裁剪" placement="top-start">
            <em @click="onClickCrop" v-if="!disableCrop">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-iconcaijian"></use>
              </svg>
            </em>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="缩放" placement="top-start">
            <em @click="onClickScale" v-if="!disableCrop">
              <svg class="cocosicon" aria-hidden="true" style="font-size: 14px;position: relative;top: 2px;">
                <use xlink:href="#cocos-iconsuofang-youxiang"></use>
              </svg>
            </em>
          </el-tooltip>
          <!-- v-if="isShowDeleteBtn" -->
          <!-- <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
          
        </el-tooltip> -->
          <el-tooltip class="item" effect="dark" content="下载" placement="top-start">
            <em>
              <svg class="cocosicon" aria-hidden="true" @click="onClickDownload">
                <use xlink:href="#cocos-icondownload3"></use>
              </svg>
            </em>
          </el-tooltip>

          <el-tooltip class="item img-remove-bg" effect="dark" content="去背景" placement="top-start">
            <!-- <svgicon @click="onClickRemove" class="redo" name="editor-area/img-remove-bg" width="24" height="24" original /> -->
            <em @click="onClickRemove">
              <svg class="cocosicon" aria-hidden="true">
                <use xlink:href="#cocos-iconeliminate"></use>
              </svg>
            </em>
          </el-tooltip>
        </div>
      </div>
      <el-button type="text" class="image-del" v-if="isShowDeleteBtn">
        <em @click="onClickDelete">
          <svg class="cocosicon" aria-hidden="true">
            <use xlink:href="#cocos-icona-shanchuyoushang"></use>
          </svg>
        </em>
      </el-button>
      <img class="image" :src="src" crossOrigin="Anonymous" />
    </div>
    <div v-else>
      <div class="add-btn" @click="onClickReplace">
        <i class="el-icon-plus"></i>
      </div>
    </div>
    <MockImgageLibrary ref="mockImgageLibrary"></MockImgageLibrary>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from "vue-property-decorator";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import MockImgageLibrary from "@/pages/index/components/EditArea/SchemaToForm/mockImgageLibrary.vue";
import ImageCropper from "@/components/ImageCropper/index";
import bus from "@/pages/index/common/utils/bus";
import getImageSize from "@/common/utils/getImageSize";
import { image2Canvas } from "@/common/utils/resizePic";
import { ImageScaleResult } from "@/types/imageScale";
import ImgBgRemover from "../ImgBgRemoveView";

@Component({
  components: {
    MockImgageLibrary,
  },
})
export default class ImageSelect extends Vue {
  @Prop({
    required: true,
  })
  src!: string;

  @Prop({})
  maxWidth!: number;

  @Prop({})
  maxHeight!: number;

  @Prop({
    default: false,
  })
  isMultiple!: boolean;

  @Prop({
    required: false,
    default: false,
  })
  disableReplace!: boolean;

  @Prop({
    required: false,
    default: false,
  })
  disableCrop!: boolean;

  @Prop({
    required: false,
    default: true,
  })
  isShowDeleteBtn!: boolean;

  @Prop({
    required: false,
    default: false,
  })
  checkSize!: boolean;

  @Prop({
    required: false,
    default: false,
  })
  isChangePropertiesSize!: boolean;

  @Prop()
  componentId!: string;

  @Prop({
    required: false,
    default: "",
  })
  property!: string; // 判断是否是背景图的时候使用

  // 是否从编辑器插入
  @Prop({
    required: false,
  })
  isEditorAdd!: boolean;

  isScale = false;

  isRemovingBg = false;

  get containerWidth() {
    if (!this.$store.state) {
      return 0;
    }
    return this.$store.state.containerWidth;
  }

  async imageScaleHandler(data: ImageScaleResult) {
    const { url, width, height, x, y } = data;
    this.$emit("update:src", url);
    if (this.isChangePropertiesSize && this.componentId) {
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          width,
          height,
        },
      });
      this.$store.commit("updateComponentProperties", {
        id: this.componentId,
        newProperties: {
          x,
          y,
        },
      });
    }
    bus.$off("image-scale-confirm", this.imageScaleHandler);
  }

  async onClickReplace() {
    if (process.env.VUE_APP_IS_OVERVIEW || window.location.pathname.endsWith("overview.html")) {
      const res = await (this.$refs.mockImgageLibrary as any).init();
      if (res) {
        this.$emit("update:src", res.url);
      }
      return;
    }
    bus.$emit("material-show");
    ImageLibrary({
      onConfirm: async images => {
        this.$emit("update:src", images[0].url);
        if (this.isChangePropertiesSize && this.componentId) {
          const componentWidth = this.$store.state.componentMap[this.componentId].properties.width;
          const imageSize = await getImageSize(images[0].url);
          // 保持图片原始比例
          this.$store.commit("updateComponentProperties", {
            id: this.componentId,
            newProperties: {
              width: componentWidth,
              height: (imageSize.height / imageSize.width) * componentWidth,
            },
          });
        }
      },
      maxWidth: this.maxWidth,
      maxHeight: this.maxHeight,
      isMultiple: this.isMultiple,
      checkSize: this.checkSize,
    });
  }

  onClickCrop() {
    ImageCropper({
      maxWidth: this.maxWidth,
      maxHeight: this.maxHeight,
      checkSize: this.checkSize,
      src: this.src,
      onConfirm: async url => {
        this.$emit("update:src", url);
        if (this.isChangePropertiesSize && this.componentId) {
          const componentWidth = this.$store.state.componentMap[this.componentId].properties.width;
          const imageSize = await getImageSize(url);
          // 保持图片原始比例
          this.$store.commit("updateComponentProperties", {
            id: this.componentId,
            newProperties: {
              width: componentWidth,
              height: (imageSize.height / imageSize.width) * componentWidth,
            },
          });
        }
      },
    });
  }

  handleRemoveCancel(componentId: string) {
    // 获取组件的数据
    this.hideCompInScene(componentId, true);
    this.isRemovingBg = false;
    bus.$emit("material-close");
  }
  handleRemoveSuccess(url: string, componentId: string) {
    this.$emit("update:src", url);
    // 还原组件的状态
    this.hideCompInScene(componentId, true);
    this.isRemovingBg = false;
    bus.$emit("material-close");
  }

  hideCompInScene(componentId: string, toggle: boolean) {
    console.log("hideCompInScene...", componentId, toggle);
    if (!componentId) return;
    // 获取组件的数据
    const component = this.$store.state.componentMap[componentId];
    const { active } = component.properties;
    if (!active) return;
    if (!component) return;
    try {
      const theNode = (window as any).qte.displayObjectManager.getDisplayObjectById(componentId).node;
      const selectNode = theNode.getChildByName("_nodeSelect");
      const lineNode = theNode.getChildByName("edge_node");
      if (selectNode) {
        selectNode.active = toggle;
      }
      if (lineNode) {
        lineNode.active = toggle;
      }
      theNode.active = toggle;
    } catch (error) {
      console.log("hideCompInScene error...", error);
    }

    // (window as any).qte.displayObjectManager.getDisplayObjectById(componentId).node.selected = toggle;
  }

  getImageRemoveConfig() {
    if (this.componentId) {
      // 获取组件的数据
      const component = this.$store.state.componentMap[this.componentId];
      // 计算图片的宽高
      const { width, height, angle, opacity = 255 } = component.properties;
      const { x, y } = (window as any).cocos.getWorldRect(this.componentId);
      return {
        compId: String(this.componentId),
        displayWidth: width,
        displayHeight: height,
        rotate: angle,
        opacity: Math.round((opacity / 255) * 100) / 100,
        left: x,
        top: y,
        scale: Math.round((this.containerWidth / 1280) * 10000) / 10000,
        src: this.src,
        isComponent: true,
        handler: ".img-remove-bg",
      };
    } else {
      return {
        rotate: 0,
        opacity: 1,
        left: 0,
        top: 0,
        scale: Math.round((this.containerWidth / 1280) * 10000) / 10000,
        src: this.src,
        isComponent: false,
        handler: ".img-remove-bg",
      };
    }
  }

  onClickRemove() {
    console.log("onClickRemove", this.componentId);
    const componentId = this.componentId;
    if (this.isRemovingBg) return;
    this.isRemovingBg = true;
    bus.$emit("material-show");
    console.log("onClickRemove", this.componentId);

    console.log("onClickRemove.x, y", this.property, this.getImageRemoveConfig());

    const handleChangeCurrentComponent = () => {
      this.hideCompInScene(componentId, true);
      bus.$off("changeCurrentComponent", handleChangeCurrentComponent);
    };
    ImgBgRemover({
      onSuccess: (url: string) => {
        this.handleRemoveSuccess(url, componentId);
        bus.$once("changeCurrentComponent", handleChangeCurrentComponent);
      },
      onCancel: () => {
        this.handleRemoveCancel(componentId);
        bus.$once("changeCurrentComponent", handleChangeCurrentComponent);
      },
      imageLoaded: () => {
        this.hideCompInScene(componentId, false);
        if (componentId) {
          bus.$once("changeCurrentComponent", handleChangeCurrentComponent);
        }
      },
      props: this.getImageRemoveConfig(),
      parent: ".canvas-container",
    });
  }

  onClickScale() {
    // this.isScale = true;
    bus.$emit("show-image-scale-modal", {
      url: this.src,
    });
    bus.$on("image-scale-confirm", this.imageScaleHandler);
  }

  onClickDelete() {
    this.$emit("update:src", "");
  }
  onClickDownload() {
    // 根据图片地址下载图片到本地
    const img = this.$el.querySelector("img.image");
    const canvas = image2Canvas(img, 1280, 960, 2);
    canvas.toBlob(blob => {
      const url = URL.createObjectURL(blob);
      const Link = document.createElement("a");
      Link.download = `tihu-${new Date().getTime()}.png`;
      Link.href = url;
      Link.click();
      Link.remove();
      // 用完释放URL对象
      URL.revokeObjectURL(url);
    });
  }
  beforeDestroy() {
    if (this.isRemovingBg) {
      this.handleRemoveCancel(this.componentId);
    }
  }
}
</script>

<style lang="less" scoped>
.image-select.hasSrc {
  display: flex;
  justify-content: center;
  background: url("~@/assets/img/transparent.png") repeat;
  background-size: 10px;
}

.image-select {
  position: relative;
  overflow: hidden;
  min-height: 32px;
  max-height: 200px;
  &:hover {
    .edit-btn-container-outer {
      bottom: 0;
      transform: translate(0, 0%);
    }
    .image-del {
      display: block;
    }
  }
  // el-icon-edit-outline  el-icon-delete  el-icon-download el-icon-full-screen
  .edit-btn-container-outer {
    justify-content: space-around;
    transform: translate(0, 100%);
    .edit-btn-container {
      max-width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
    box-sizing: border-box;
    line-height: 14px;
    font-size: 14px;
    width: 100%;
    position: absolute;
    bottom: 0;
    display: flex;
    transition: all 0.1s linear;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
    max-width: 100%;
    justify-content: space-around;
    transform: translate(0, 100%);
    .edit-btn-container {
      max-width: 100%;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
    em {
      color: #fff;
      padding: 2px;
      cursor: pointer;
    }
    svg {
      font-size: 20px;
    }
    &:hover {
      bottom: 0;
    }
  }
  .image-del {
    position: absolute;
    right: 0;
    top: 0;
    font-size: 0;
    height: 20px;
    display: none;
    transition: all 0.2s linear;
    opacity: 0.6;
    span {
      font-size: 20px;
    }
    svg {
      font-size: 24px;
    }
  }
  .image-container {
    user-select: none;
    position: relative;
    width: 180px;
    line-height: 0;
    font-size: 0;
    display: flex;
    justify-content: center;

    .delete-btn {
      position: absolute;
    }

    .image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      min-height: 60px;
    }
  }

  .add-btn {
    border: 1px dashed #dcdfe6;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    transition: all 0.1s linear;

    &:hover {
      border-color: #409eff;
    }
  }
}
</style>
