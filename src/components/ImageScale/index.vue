<template>
  <div class="image-scale-container">
    <section class="canvas-wrapper" :style="containerStyle">
      <vue-cropper
        :img="src"
        outputType="png"
        :canScale="true"
        :canMove="true"
        @imgLoad="imgLoad"
        @realTime="realTime"
        @imgMoving="imgMoving"
        ref="cropper"
      ></vue-cropper>
    </section>
    <section class="operation-wrapper" :style="operationContainerStyle">
      <div class="operation-content"> 
        <div class="scale-wrapper">
          <span style="margin-right: 10px; font-size: 20px">缩放比例</span>
          <el-slider
            style="flex: 1"
            v-model="slider"
            :min="minScale"
            :max="maxScale"
            :step="step">
          </el-slider>
          <el-input
            :value="slider"
            size="normal"
            type="number"
            :step="step"
            style="width: 116px; margin-left: 10px"
            @input="sliderChange">
            <template slot="append">%</template>
          </el-input>
        </div>
        <div class="btn-wrapper">
          <i class="el-icon-circle-close cancel-btn" @click="close"></i>
          <i class="el-icon-circle-check confirm-btn" @click="confirm"></i>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts">
import { Loading } from 'element-ui';
import { Component, Vue, Watch, Prop } from "vue-property-decorator";
import { VueCropper } from "vue-cropper";
import bus from "@/pages/index/common/utils/bus";
import { showErrorMessage } from "@/common/utils/showErrorMessage";

@Component({
  components: {
    VueCropper,
  },
})
export default class ImageCropper extends Vue {
  // 需要缩放的图片地址
  @Prop({
    default: '',
  })
  src!: string;
  // 容器缩放比例
  @Prop({
    default: 1
  })
  containerScale!: number;
  // x轴坐标
  @Prop({
    default: undefined
  })
  x!: number;
  // y轴坐标
  @Prop({
    default: undefined
  })
  y!: number;
  slider = 100;
  step = 1;
  maxScale = 300;
  minScale = 20;
  $mask!: HTMLDivElement;
  
  get containerStyle() {
    return [
      {
        transform: `scale(${this.containerScale})`
      },
    ];
  }

  get operationContainerStyle() {
    return [
      {
        width: `calc(50% * ${this.containerScale})`,
      },
    ];
  }

  created() {
    this.addMask();
  }

  beforeDestroy() {
    document.body.removeChild(this.$mask);
  }

  addMask() {
    this.$mask = document.createElement('div');
    this.$mask.className = 'image-scale-mask';
    document.body.appendChild(this.$mask);
  }

  sliderChange(num: number) {
    this.slider = +Number(num).toFixed(2);
  }

  imgLoad() {
    this.slider = +((this.$refs.cropper as any).scale * 100).toFixed(2);
    if (typeof this.x !== 'undefined') {
      try {
        (this.$refs.cropper as any).x = this.x;
      } catch (err) {
        console.log('cropper set x error:', err);
      }
    }
    if (typeof this.y !== 'undefined') {
      try {
        (this.$refs.cropper as any).y = this.y;
      } catch (err) {
        console.log('cropper set y error:', err);
      }
    }
  }

  realTime() {
    if ((this.$refs as any).cropper.scale * 100 <= this.maxScale) {
      this.slider = +((this.$refs as any).cropper.scale * 100).toFixed(2);
    } else {
      this.slider = this.maxScale;
      (this.$refs as any).cropper.scale = this.maxScale / 100;
    }
  }

  imgMoving(data: any) {
    const { axis } = data;
    const { x1, y1, x2, y2 } = axis;
    const { scale, trueWidth, trueHeight } = (this.$refs.cropper as any);
    // 下面逻辑是对图片移动的边界限制
    if (x1 < 0) {
      /**
       * vue-cropper 内部 x1 计算公式 x1 = x + (trueWidth * (1 - scale)) / 2
       * 得到：x = x1 - (trueWidth * (1 - scale)) / 2
       */
      (this.$refs.cropper as any).x = 0 - (trueWidth * (1 - scale)) / 2;
    }
    if (y1 < 0) {
      /**
       * vue-cropper 内部 x1 计算公式 y1 = x + (trueHeight * (1 - scale)) / 2
       * 得到：y = y1 - (trueHeight * (1 - scale)) / 2
       */
      (this.$refs.cropper as any).y = 0 - (trueHeight * (1 - scale)) / 2;
    }
    const scaleWidth = trueWidth * scale;
    const scaleHeight = trueHeight * scale;
    if (x2 > 1280) {
      /**
       * vue-cropper 内部 x2 计算公式 x2 = x1 + trueWidth * scale 并根据 x1 公式 x1 = x + (trueWidth * (1 - scale)) / 2
       * 得到 x2 = x + (trueWidth * (1 - scale)) / 2 + trueWidth * scale
       * 并由以上得到 x 的计算公式得到：x = x2 - (trueWidth * (1 - scale)) / 2 - trueWidth * scale
       */
      (this.$refs.cropper as any).x = 1280 - (trueWidth * (1 - scale)) / 2 - scaleWidth;
    }
    if (y2 > 960) {
      /**
       * vue-cropper 内部 y2 计算公式 y2 = y1 + trueHeight * scale 并根据 y1 公式 y1 = y + (trueHeight * (1 - scale)) / 2
       * 得到 y2 = y + (trueHeight * (1 - scale)) / 2 + trueHeight * scale
       * 并由以上得到 y 的计算公式得到：y = y2 - (trueHeight * (1 - scale)) / 2 - trueHeight * scale
       */
      (this.$refs.cropper as any).y = 960 - (trueHeight * (1 - scale)) / 2 - scaleHeight;
    }
  }

  @Watch("slider")
  changeScale(val: number) {
    if (val >= this.minScale && val <= this.maxScale) {
      (this.$refs as any).cropper.scale = val / 100;
    }
  }

  close() {
    this.$emit('close');
  }

  getImageSize(file: Blob): Promise<{ width: number, height: number }> {
    return new Promise(resolve => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = (e) => {
        const img = new Image();
        img.crossOrigin = "Anonymous";
        if (e.target && e.target.result) {
          img.src = e.target.result.toString();
        }
        img.onload = async () => {
          resolve({
            width: img.width,
            height: img.height,
          });
        }
      }
    });
  }

  confirm() {
    (this.$refs.cropper as any).getCropBlob(async (blob: Blob) => {
      try {
        const loadingInstance = Loading.service({ fullscreen: true });
        const srcFileName = this.src.split("/").pop();
        const name = (srcFileName as string).replace(/-.*(\.)/, "$1");
        const file = new File([blob], name, { type: blob.type });
        const imgAxis = (this.$refs.cropper as any).getImgAxis()
        const formData = new FormData();
        formData.append("file", file);
        const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
        const { x1, y1, x2, y2 } = imgAxis;
        loadingInstance.close();
        bus.$emit('image-scale-confirm', {
          url: url,
          x: x1,
          y: y1,
          width: (x2 - x1),
          height: (y2 - y1)
        });
        this.close();
      } catch (err) {
        showErrorMessage(err as any);
      }
    });
  }
}
</script>

<style lang="less" scoped>
.image-scale-container {
  position: absolute;
  width: 1280px;
  height: 960px;
  left: 0;
  top: 0;
  z-index: 9999;

  .vue-cropper {
    background-image: none;
  }
  .canvas-wrapper {
    width: 100%;
    height: 100%;
    transform-origin: left top;
    border: 1px solid red;
  }
  .operation-wrapper {
    position: absolute;
    display: flex;
    justify-content: center;
    left: 0;
    top: 20px;
    transform: translate(50%, 0px);
    .operation-content {
      display: flex;
      width: 100%;
      min-width: 400px;
      
      color: #fff;
      box-sizing: border-box;
      justify-content: space-between;
      align-items: center;
      border: 1px solid rgba(82,88,102,.2);
      border-radius: 10px;
      box-shadow: 1px 1px 8px 0 rgba(131,134,143,.2);
      padding: 10px;
      background: rgba(0,0,0,.35);
    }
    .scale-wrapper {
      display: inline-flex;
      width: 500px;
      align-items: center;
    }

    .btn-wrapper {
      display: flex;
      margin-left: 20px;
      align-items: center;
      font-size: 35px;
      .cancel-btn {
        margin-right: 5px;
        color: rgb(250, 87, 75);
      }
      .confirm-btn {
        color: rgb(65, 196, 121);
      }
      
    }
  }
}
</style>

<style lang="less">
.image-scale-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  opacity: .5;
  background: #000;
  z-index: 9998;
}
</style>