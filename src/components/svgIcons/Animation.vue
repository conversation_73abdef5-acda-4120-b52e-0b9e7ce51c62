<template>
  <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M170.666667 85.333333c-47.36 0-85.333333 37.973333-85.333334 85.333334v426.666666h85.333334V170.666667h426.666666V85.333333H170.666667m170.666666 170.666667c-47.36 0-85.333333 37.973333-85.333333 85.333333v426.666667h85.333333V341.333333h426.666667V256H341.333333m170.666667 170.666667c-47.36 0-85.333333 37.973333-85.333333 85.333333v341.333333c0 47.36 37.973333 85.333333 85.333333 85.333334h341.333333c47.36 0 85.333333-37.973333 85.333334-85.333334v-341.333333c0-47.36-37.973333-85.333333-85.333334-85.333333h-341.333333z"
    ></path>
  </svg>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({})
export default class Animation extends Vue {}
</script>
