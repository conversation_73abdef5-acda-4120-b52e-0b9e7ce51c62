<template>
  <svg viewBox="0 0 18 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
      <clipPath id="clipPath4">
        <rect x="0" y="6.2" width="18" height="10"></rect>
      </clipPath>
    </defs>
    <g id="svg-line" fill-rule="nonzero">
      <g id="cloud">
        <path
          d="M12.9 11.2C12.9 10.8 13.3 10.4 13.8 10.4 15.2 10.4 16.2 9.3 16.2 7.9 16.2 7.2 16 6.6 15.5 6.1 15.4 6.1 15.3 6 15.3 6 15.2 5.9 15.2 5.9 15.1 5.9 15 5.8 15 5.8 14.9 5.7 14.9 5.7 14.9 5.7 14.9 5.7 14.8 5.7 14.8 5.7 14.7 5.7 14.7 5.6 14.7 5.6 14.6 5.6 14.6 5.6 14.5 5.6 14.5 5.6 14.5 5.6 14.4 5.6 14.4 5.6 14.4 5.5 14.3 5.5 14.3 5.5 14.2 5.5 14.2 5.5 14.1 5.5 14.1 5.5 14.1 5.5 14 5.5 13.9 5.5 13.9 5.5 13.8 5.5 13.8 5.5 13.7 5.5 13.7 5.5 13.7 5.3 13.6 5.2 13.6 5.1 13.6 5 13.6 5 13.6 5 13.5 4.9 13.5 4.8 13.5 4.7 13.5 4.6 13.4 4.6 13.4 4.6 13.4 4.5 13.3 4.4 13.3 4.3 13.3 4.3 13.3 4.2 13.3 4.2 13.2 4.1 13.2 4 13.1 3.9 12.3 2.6 10.7 1.8 9 1.8 7.3 1.8 5.7 2.6 4.9 3.9 4.8 4 4.8 4.1 4.7 4.2 4.7 4.2 4.7 4.3 4.7 4.3 4.7 4.4 4.6 4.5 4.6 4.6 4.6 4.6 4.5 4.6 4.5 4.7 4.5 4.8 4.5 4.9 4.4 5 4.4 5 4.4 5 4.4 5.1 4.4 5.2 4.3 5.3 4.3 5.5 4.3 5.5 4.2 5.5 4.2 5.5 4.1 5.5 4.1 5.5 4 5.5 3.9 5.5 3.9 5.5 3.9 5.5 3.8 5.5 3.8 5.5 3.7 5.5 3.7 5.5 3.6 5.5 3.6 5.6 3.6 5.6 3.5 5.6 3.5 5.6 3.5 5.6 3.4 5.6 3.4 5.6 3.3 5.6 3.3 5.6 3.3 5.7 3.2 5.7 3.2 5.7 3.1 5.7 3.1 5.7 3.1 5.7 3.1 5.7 3 5.8 3 5.8 2.9 5.8 2.8 5.9 2.8 5.9 2.7 6 2.7 6 2.6 6.1 2.5 6.1 2 6.6 1.8 7.2 1.8 7.9 1.8 9.3 2.8 10.4 4.2 10.4 4.7 10.4 5.1 10.8 5.1 11.2 5.1 11.7 4.7 12 4.2 12 1.9 12 0 10.2 0 7.9 0 6.1 1.3 4.5 3 4 3.9 1.7 6.3 0 9 0 11.7 0 14.1 1.7 15 4 16.7 4.5 18 6.1 18 7.9 18 10.2 16.1 12 13.8 12 13.3 12 12.9 11.7 12.9 11.2Z"
          id="Shape"
        ></path>
      </g>
      <g style='clip-path: url("#clipPath4");'>
        <g id="arrow" transform="translate(0, -4.8)">
          <path
            d="M8.4 10.5C8.5 10.3 8.8 10.2 9 10.2 9.2 10.2 9.5 10.3 9.6 10.5L12.2 13.2C12.3 13.4 12.4 13.6 12.4 13.8 12.4 13.8 12.4 13.8 12.4 14 12.4 14.3 12 14.6 11.7 14.6 11.7 14.6 11.1 14.6 9.9 14.6L9.9 19.1C9.9 19.6 9.5 20 9 20L9 20C8.5 20 8.1 19.6 8.1 19.1L8.1 14.6C6.9 14.6 6.3 14.6 6.3 14.6 6.1 14.6 5.6 14.3 5.6 14 5.6 13.9 5.6 13.8 5.6 13.8 5.6 13.5 5.7 13.4 5.8 13.2 5.8 13.2 6.7 12.3 8.4 10.5Z"
          ></path>
          <!-- <path d="M8.4 20.5C8.5 20.3 8.8 20.2 9 20.2 9.2 20.2 9.5 20.3 9.6 20.5L12.2 23.2C12.3 23.4 12.4 23.6 12.4 23.8 12.4 23.8 12.4 23.8 12.4 24 12.4 24.3 12 24.6 11.7 24.6 11.7 24.6 11.1 24.6 9.9 24.6L9.9 29.1C9.9 29.6 9.5 30 9 30L9 30C8.5 30 8.1 29.6 8.1 29.1L8.1 24.6C6.9 24.6 6.3 24.6 6.3 24.6 6.1 24.6 5.6 24.3 5.6 24 5.6 23.9 5.6 23.8 5.6 23.8 5.6 23.5 5.7 23.4 5.8 23.2 5.8 23.2 6.7 22.3 8.4 20.5Z"></path> -->
          <!-- <path d="M8.4 0.5C8.5 0.3 8.8 0.2 9 0.2 9.2 0.2 9.5 0.3 9.6 0.5L12.2 3.2C12.3 3.4 12.4 3.6 12.4 3.8 12.4 3.8 12.4 3.8 12.4 4 12.4 4.3 12 4.6 11.7 4.6 11.7 4.6 11.1 4.6 9.9 4.6L9.9 9.1C9.9 9.6 9.5 10 9 10L9 10C8.5 10 8.1 9.6 8.1 9.1L8.1 4.6C6.9 4.6 6.3 4.6 6.3 4.6 6.1 4.6 5.6 4.3 5.6 4 5.6 3.9 5.6 3.8 5.6 3.8 5.6 3.5 5.7 3.4 5.8 3.2 5.8 3.2 6.7 2.3 8.4 0.5Z"></path> -->
        </g>
      </g>
    </g>
  </svg>
</template>

<script lang="ts">
import { Component, Vue } from "vue-property-decorator";

@Component({})
export default class CloudUploadOutlined extends Vue {}
</script>
