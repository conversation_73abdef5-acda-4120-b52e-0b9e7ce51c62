<template>
  <div class="image-select" :class="{ hasSrc: !!src }">
    <div class="image-container" v-if="src">
      <div class="edit-btn-container">
        <div class="btn" @click="onClickReplace">替换</div>
        <div class="btn" @click="onClickCrop">裁剪</div>
        <div class="btn" @click="onClickDelete">
          删除
        </div>
      </div>
      <img class="image" :src="src" />
    </div>
    <div v-else>
      <div class="add-btn" @click="onClickAdd">
        <i class="el-icon-plus"></i>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";
import { ImageLibrary } from "@/pages/index/components/MaterialLibrary/ImageLibrary/index";
import ImageCropper from "@/components/ImageCropper/index";
import getImageSize from "@/common/utils/getImageSize";
import bus from "@/pages/index/common/utils/bus";
// 没有发现使用到该组件，按钮样式没有优化
@Component
export default class EdtiorImageSelect extends Vue {
  @Prop({
    required: true,
  })
  src!: string;

  @Prop({})
  maxWidth!: number;

  @Prop({})
  maxHeight!: number;

  @Prop()
  componentId!: number;

  @Prop({
    default: "",
  })
  property!: string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  componentParams: any = {};

  onClickAdd() {
    bus.$emit("material-show");
    ImageLibrary({
      isMultiple: true,
      onConfirm: images => {
        images.forEach((image, index) => {
          const { url } = image;
          getImageSize(url).then(({ width, height }) => {
            this.componentParams = {
              tag: "point",
              isShowProperty: "hide",
              isFocus: true,
              type: "sprite",
              editable: true,
              deletable: false,
              tagName: "subTagName",
              properties: {
                active: true,
                width,
                height,
                x: 0 + index * 20,
                y: 0 - index * 20,
                texture: url,
              },
            };
            this.$store.dispatch("editorAddComponentAndFocus", this.componentParams);
            // getParams 扩展字段
            this.$store.commit("updateComponentExtra", {
              id: this.componentId,
              newExtra: {
                getParams: this.params.extraParams,
                property: this.property,
                [this.property]: url,
                [this.params.extraParams]: this.componentParams,
              },
            });
          });
        });
      },
    });
  }

  onClickReplace() {
    bus.$emit("material-show");
    ImageLibrary({
      onConfirm: images => {
        this.$emit("update:src", images[0].url);
        const componentId = this.$store.state.componentMap[this.componentId].extra;
        const params = componentId.getParams;
        this.$store.commit("updateComponentProperties", {
          id: componentId[params].id,
          newProperties: {
            texture: images[0].url,
          },
        });
        this.$store.commit("updateComponentExtra", {
          id: this.componentId,
          newExtra: {
            getParams: this.params.extraParams,
            property: this.property,
            [this.property]: images[0].url,
            [this.params.extraParams]: this.componentParams,
          },
        });
      },
      maxWidth: this.maxWidth,
      maxHeight: this.maxHeight,
    });
  }

  onClickCrop() {
    ImageCropper({
      src: this.src,
      onConfirm: url => {
        this.$emit("update:src", url);
        const componentId = this.$store.state.componentMap[this.componentId].extra;
        const params = componentId.getParams;
        this.$store.commit("updateComponentProperties", {
          id: componentId[params].id,
          newProperties: {
            texture: "",
          },
        });
        this.$store.commit("updateComponentExtra", {
          id: this.componentId,
          newExtra: {
            getParams: this.params.extraParams,
            property: this.property,
            [this.property]: url,
            [this.params.extraParams]: this.componentParams,
          },
        });
      },
    });
  }

  onClickDelete() {
    this.$emit("update:src", "");
    const componentId = this.$store.state.componentMap[this.componentId].extra;
    const params = componentId.getParams;
    console.error("tagid", componentId[params].id);
    this.$store.dispatch("removeComponent", componentId[params].id);
    this.$store.commit("updateComponentExtra", {
      id: this.componentId,
      newExtra: {
        getParams: this.params.extraParams,
        [this.property]: "",
        [this.params.extraParams]: this.componentParams,
      },
    });
  }
}
</script>

<style lang="less" scoped>
.image-select.hasSrc {
  display: flex;
  justify-content: center;
  background: url("~@/assets/img/transparent.png") repeat;
  background-size: 10px;
}

.image-select {
  .image-container {
    user-select: none;
    position: relative;
    width: 180px;
    line-height: 0;
    font-size: 0;

    &:hover {
      .edit-btn-container {
        opacity: 1;
      }
    }

    .delete-btn {
      position: absolute;
    }

    .edit-btn-container {
      box-sizing: border-box;
      line-height: 14px;
      font-size: 14px;
      width: 180px;
      position: absolute;
      bottom: 0;
      display: flex;
      justify-content: space-between;
      opacity: 0;
      transition: all 0.1s linear;

      .btn {
        cursor: pointer;
        box-sizing: border-box;
        flex-grow: 1;
        padding: 6px 10px;
        color: white;
        background: #409eff;
        text-align: center;

        &:first-of-type {
          background: #67c23a;
        }

        &:last-of-type {
          background: #f56c6c;
        }
      }
    }

    .image {
      width: 180px;
    }
  }

  .add-btn {
    border: 1px dashed #dcdfe6;
    display: flex;
    font-size: 16px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    width: 50px;
    height: 50px;
    border-radius: 5px;
    transition: all 0.1s linear;

    &:hover {
      border-color: #409eff;
    }
  }
}
</style>
