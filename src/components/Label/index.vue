<template>
  <span :class="['label', { required: required }]"
    >{{ label }}
    <el-tooltip v-if="description" :content="description" placement="bottom">
      <i class="el-icon-info"
    /></el-tooltip>
  </span>
</template>

<script lang="ts">
import { Component, Vue, Prop } from "vue-property-decorator";

@Component
export default class Label extends Vue {
  @Prop({
    required: true,
  })
  label!: string;

  @Prop({
    default: "",
  })
  description!: string;

  @Prop({
    default: false,
  })
  required!: boolean;
}
</script>

<style scoped lang="less">
.label {
  display: inline-block;
  text-align: left;
  min-width: 40px;
  flex-shrink: 0;

  .el-icon-info {
    cursor: pointer;
  }
}

.required {
  &::before {
    content: "*";
    color: red;
  }
}
</style>
