<template>
  <div class="audio-select-container">
    <span v-if="label" class="label">{{ label }}</span>
    <template v-if="v">
      <audio-player :src="v" />
      <el-tooltip content="替换">
        <i class="el-icon-upload2" @click="handleSelect" />
      </el-tooltip>
      <el-tooltip content="下载">
        <i class="el-icon-download" @click="handleDownload" />
      </el-tooltip>
      <el-tooltip content="删除">
        <i class="el-icon-close" @click="v = ''" />
      </el-tooltip>
    </template>
    <el-button v-else size="mini" @click="handleSelect">添加音频</el-button>
  </div>
</template>

<script lang="ts">
import audioPlayer from "@/common/utils/audioPlayer";
import downloadByUrl from "@/common/utils/downloadByUrl";
import { audioRegExp } from "@/common/utils/regExp";
import AudioPlayer from "@/components/AudioPlayer/index.vue";
import { Component, Prop, Vue } from "vue-property-decorator";

@Component({
  components: { AudioPlayer },
})
export default class AudioSelect extends Vue {
  @Prop({
    required: false,
  })
  label!: string;

  @Prop({
    required: false,
  })
  defaultValue!: number | string;

  @Prop({
    default: () => ({}),
  })
  params!: any;

  @Prop({
    required: true,
  })
  value!: string;

  isPlaying = false;

  handleClick() {
    const { isPlaying, v } = this;

    if (isPlaying) {
      audioPlayer.pause(v);
    } else {
      audioPlayer.play(
        v,
        () => {
          this.isPlaying = false;
        },
        () => {
          this.isPlaying = false;
        },
      );
    }
    this.isPlaying = !isPlaying;
  }

  handleDownload() {
    downloadByUrl(this.v);
  }

  handleSelect() {
    this.$getPageConfigByKey("AudioLibrary")().then((component: { AudioLibrary: any; }) => {
      const AudioLibrary = component.AudioLibrary;
      AudioLibrary({
        onClose: () => {
          //
        },
        onConfirm: (audios: { url: string; }[]) => {
          this.v = audios[0].url;
        },
      });
    });

  }

  get isDisabled() {
    return !audioRegExp.test(this.v);
  }

  get v() {
    return this.value ?? this.defaultValue;
  }

  set v(val) {
    if (val === this.v) return;
    this.$emit("update:value", val);
    this.$emit("change", val);
  }
}
</script>

<style lang="less" scoped>
.audio-select-container {
  display: flex;
  align-items: center;
  padding-bottom: 10px;

  .el-input {
    width: 150px;
  }

  .label {
    display: inline-block;
    text-align: left;
    min-width: 40px;
    flex-shrink: 0;
    margin-right: 5px;
  }

  .el-icon-upload2,
  .el-icon-download,
  .el-icon-close {
    cursor: pointer;
    font-size: 16px;
  }
}
</style>
