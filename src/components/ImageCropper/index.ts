import Vue from "vue";
import ImageCropperVue from "./index.vue";
import { CombinedVueInstance } from "vue/types/vue";
import store from "@/pages/index/store";

const ImageCropperConstructor = Vue.extend(ImageCropperVue);

type InstanceMethods = {
  onClose?: () => void;
  onConfirm: (url: string) => void;
};

type InstanceData = {
  visible: boolean;
  isAutoUpload: boolean;
  src: string;
  maxWidth?: number;
  maxHeight?: number;
  checkSize?: boolean;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceData,
  InstanceMethods,
  object,
  Record<never, any>
>;

export const createInstance = () => {
  if (!instance) {
    instance = new ImageCropperConstructor({
      store,
      el: document.createElement("div")
    });
  }
  return instance;
};

export type ImageCropperOptions = {
  isAutoUpload?: boolean;
  src: string;
  maxWidth?: number;
  maxHeight?: number;
  checkSize?: boolean;
} & InstanceMethods;

const ImageCropper = ({
  onClose,
  onConfirm,
  isAutoUpload,
  maxWidth,
  maxHeight,
  checkSize,
  src
}: ImageCropperOptions) => {
  createInstance();
  document.body.appendChild(instance.$el);

  instance.visible = true;
  instance.src = src;
  instance.maxWidth = maxWidth;
  instance.maxHeight = maxHeight;
  instance.checkSize = checkSize;

  if (isAutoUpload === false) {
    instance.isAutoUpload = false;
  }

  if (onClose) {
    instance.onClose = onClose;
  }

  instance.onConfirm = onConfirm;
};

export default ImageCropper;
