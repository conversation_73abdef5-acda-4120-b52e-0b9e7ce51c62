<template>
  <div class="image-cropper">
    <el-dialog :visible.sync="visible" top="0vh" :close-on-click-modal="false">
      <div class="cropper-wrapper">
        <vue-cropper
          :img="src"
          outputType="png"
          :autoCrop="false"
          :canMove="false"
          :info="true"
          ref="cropper"
          @mouseenter.native="enter"
          :full="false"
          :fixed="fixed"
          :fixedNumber="fixedNumber"
          :enlarge="scale"
          :infoTrue="true"
          @imgLoad="imgLoad"
          @realTime="realTime"
        ></vue-cropper>
      </div>
      <div class="scale-wrapper">
        <span style="margin-right: 10px;">缩放比例</span>
        <el-slider style="flex: 1" v-model="slider" :min="minScale" :max="maxScale" :step="step"> </el-slider>
        <el-input :value="slider" size="mini" type="number" :step="step" style="width: 100px; margin-left: 10px" @input="sliderChange">
          <template slot="append">%</template>
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="onClickClose">取 消</el-button>
        <el-button type="primary" @click="onClickConfirm">
          确 定
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import { VueCropper } from "vue-cropper";
import { showErrorMessage } from "@/common/utils/showErrorMessage";
import bus from "@/pages/index/common/utils/bus";
import { Message } from "element-ui";

@Component({
  components: {
    VueCropper,
  },
})
export default class ImageCropper extends Vue {
  visible = false;
  src = "";
  // fixed 和 fixedNumber目前没有在业务中使用
  fixed = false; // 是否开启截图框宽高固定比例
  fixedNumber = [1, 1]; // 截图框的宽高比例, 开启fixed生效
  isAutoUpload = true;
  scale = Math.round((1280 / ((document.body.clientWidth * 66.7) / 100)) * 100) / 100;
  maxWidth = 0;
  maxHeight = 0;
  checkSize = false;
  slider = 100;
  step = 1;
  maxScale = 300;
  minScale = 20;

  imgLoad() {
    this.slider = +((this.$refs.cropper as any).scale * 100).toFixed(2);
  }

  realTime() {
    if ((this.$refs as any).cropper.scale * 100 <= this.maxScale) {
      this.slider = +((this.$refs as any).cropper.scale * 100).toFixed(2);
    } else {
      this.slider = this.maxScale;
      (this.$refs as any).cropper.scale = this.maxScale / 100;
    }
  }

  @Watch("visible")
  dialogVisibleChanged(val: boolean) {
    if (this.$refs.cropper as any) {
      (this.$refs.cropper as any).clearCrop(); //清除裁剪
    }
    bus.$emit("dialogVisible", val);
  }

  onClickClose() {
    this.visible = false;
  }

  enter() {
    if (!this.src) {
      return;
    }
    const w = (this.$refs.cropper as any).w;
    if (w) {
      this.scale = Math.round((1280 / w) * 100) / 100;
    }
    (this.$refs.cropper as any).startCrop(); //开始裁剪
  }

  getImageSize(blob: Blob): Promise<{ width: number; height: number }> {
    return new Promise(resolve => {
      const reader = new FileReader();
      reader.readAsDataURL(blob);
      reader.onload = e => {
        const img = new Image();
        img.crossOrigin = "Anonymous";
        if (e.target && e.target.result) {
          img.src = e.target.result.toString();
          // console.error(e.target.result)
        }
        img.onload = async () => {
          resolve({
            width: img.width,
            height: img.height,
          });
        };
      };
    });
  }

  sliderChange(num: number) {
    this.slider = +Number(num).toFixed(2);
  }

  @Watch("slider")
  changeScale(val: number) {
    if (val >= this.minScale && val <= this.maxScale) {
      (this.$refs as any).cropper.scale = val / 100;
    }
  }

  onClickConfirm() {
    // 如果没有裁剪区域 不处理
    if (!(this.$refs.cropper as any).cropping) {
      this.visible = false;
      return false;
    }

    (this.$refs.cropper as any).getCropBlob(async (blob: Blob) => {
      try {
        const srcFileName = this.src.split("/").pop();
        const name = (srcFileName as string).replace(/-.*(\.)/, "$1");
        const file = new File([blob], name, { type: blob.type });
        const { width, height } = await this.getImageSize(blob);
        if (this.checkSize && (width > Number(this.maxWidth) || height > Number(this.maxHeight))) {
          Message.error("图片支持最大尺寸为" + this.maxWidth + "*" + this.maxHeight + "");
          return;
        }
        const formData = new FormData();
        formData.append("file", file);
        const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
        this.onConfirm && this.onConfirm(url);
        this.visible = false;
      } catch (err) {
        showErrorMessage(err as any);
      }
    });
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function
  onConfirm(_url: string) {}
}
</script>

<style scoped lang="less">
.image-cropper {
  /deep/ .el-dialog {
    width: calc(66.7vw + 32px);
  }
  .cropper-wrapper {
    width: 66.7vw;
    height: 37.52vw;
  }

  .scale-wrapper {
    display: flex;
    align-items: center;
  }
}
</style>
