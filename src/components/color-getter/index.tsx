import { Component, Prop, Vue, Watch } from "vue-property-decorator";
import "./index.less";
import html2canvas from "html2canvas";
import bus from "@/pages/index/common/utils/bus";

interface ColorData {
  imgUrl: string;
  width: number;
  height: number;
  state: boolean;
}

@Component
export default class ColorGetter extends Vue {
  @Prop(Number) public scale!: number;

  public colorData: ColorData = {
    imgUrl: "",
    width: 0,
    height: 0,
    state: false,
  };
  public colorLeft = "";
  public colorTop = "";
  public colorValue = "";
  public isMoveBlank = false;
  public isBeginGetColor = false;

  public mounted() {
    bus.$on("changeGetColorState", this.changeGetColorState);
  }

  public beforeDestroy() {
    bus.$off("changeGetColorState", this.changeGetColorState);
  }

  public get colorBoxStyle() {
    return {
      backgroundColor: this.colorValue,
      transform: `translate(${this.colorLeft}, ${this.colorTop})`,
    };
  }

  @Watch("isBeginGetColor")
  public watchIsBeginGetColor(val: boolean) {
    if (val) {
      this.beginGetColor();
    }
  }

  public changeGetColorState(state: boolean) {
    this.isBeginGetColor = state;
  }

  public async beginGetColor() {
    const container = document.querySelector(".oc-editor") as HTMLElement;
    if (container) {
      container.style.overflow = "hidden";
      const shareContent = container.cloneNode(true) as HTMLElement;
      shareContent.style.position = "absolute";
      shareContent.style.width = "100%";
      shareContent.style.height = "100%";
      shareContent.style.top = "100%";
      // 网格线
      const grid = shareContent.querySelector(".showGrid") as HTMLElement;
      if (grid) {
        grid.style.background = "unset";
      }
      const zoomContent = shareContent.querySelector(".oc-editor-zoom-transition") as HTMLElement;
      if (zoomContent) {
        zoomContent.style.transform = "scale(1)";
      }
      const editorPage = shareContent.querySelector(".oc-editor-page") as HTMLElement;
      if (editorPage) {
        editorPage.style.boxShadow = "none";
      }
      container.appendChild(shareContent);
      // 处理svg
      const svgs = [...shareContent.getElementsByTagName("svg")];
      await Promise.all(svgs.map((svg: SVGSVGElement) => {
        // 清除svg的fitler，如果有filter的话转canvas之后svg会消失
        const filter = svg.style.filter;
        svg.style.filter = "";
        filter && svg.setAttribute("backup-filter", filter);
      }));

      const width = shareContent.offsetWidth; // 获取dom 宽度
      const height = shareContent.offsetHeight; // 获取dom 高度
      const opts = {
        scale: 1,
        dpi: window.devicePixelRatio * 2,
        width,
        height,
        useCORS: true // 【重要】开启跨域配置
      };
      html2canvas(shareContent, opts).then((canvas) => {
        container.removeChild(shareContent);
        container.style.overflow = "unset";
        this.colorData = {
          imgUrl: canvas.toDataURL(),
          width,
          height,
          state: true
        };
      });
    }
  }
  // 十六进制转换器
  public hexadecimal(num: string) {
    const r = parseInt(num, 10).toString(16);
    if (r.length === 1) {
      return "0" + r;
    }
    return r.toUpperCase();
  }
  public showColor($event: MouseEvent) {
    $event.stopPropagation();
    if (!this.colorData.state) {
      return false;
    }
    this.isMoveBlank = false;
    // 加上距离吸管顶端的距离
    const x = $event.offsetX;
    const y = $event.offsetY;
    this.colorLeft = x + 30 + "px";
    this.colorTop = y + "px";
    // 创建canvas
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d") as CanvasRenderingContext2D;
    const newImg = (this.$refs.imgDom) as HTMLImageElement;
    // 当图片加载完的时候
    canvas.width = newImg.width;
    canvas.height = newImg.height;
    ctx.drawImage(newImg, 0, 0);
    // 获取(x,y,1,1)的像素数据对象，加上距离吸管顶端的距离
    const imgData = ctx.getImageData(x + 3, y + 13, 1, 1) as any;
    if (imgData.data[3] === 0) {
      this.colorValue = "#FFFFFF";
    } else {
      const color16 = "#" + this.hexadecimal(imgData.data[0]) + this.hexadecimal(imgData.data[1]) + this.hexadecimal(imgData.data[2]);
      this.colorValue = color16;
    }
  }
  public showBlankColor(e: MouseEvent) {
    if (!this.colorData.state) {
      return false;
    }
    this.isMoveBlank = true;
    const x = e.offsetX;
    const y = e.offsetY;
    this.colorLeft = x + 30 + "px";
    this.colorTop = y + "px";
    this.colorValue = "#ffffff";
  }
  public finished() {
    this.isBeginGetColor = false;
    // config.bus.$emit("updateValue", this.colorValue);
  }

  public render() {
    const {colorData, colorBoxStyle, isBeginGetColor} = this;
    return (
      isBeginGetColor ?
        <div
          class="get-color-box"
          style={{
            width: colorData.width + "px",
            height: colorData.height + "px"
          }}
          onMousemove={this.showBlankColor}
          onClick={this.finished}>
          <div
            class="img-box"
            style={{
              position: "relative",
              transform: `scale(${this.scale})`
            }}
            onMousemove={this.showColor}>
            <img
              style="opacity: 0"
              ref="imgDom"
              src={colorData.imgUrl}
              width={colorData.width}
              height={colorData.height}
              alt=""/>
            <div
              class={["color-box", {
                "can-show": this.colorData.state
              }]}
              style={colorBoxStyle}/>
          </div>
          {
            this.isMoveBlank ?
              <div
                class={["color-box", {
                  "can-show-blank": this.colorData.state
                }]}
                style={{
                  ...colorBoxStyle,
                  transform: `scale(${this.scale})`
                }}/> : null
          }
        </div> : null
    );
  }
}
