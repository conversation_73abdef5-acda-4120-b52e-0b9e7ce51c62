/**
 * 将rotateX、rotateY 转换 scale
 * <AUTHOR>
 */

function transRotate (rotateX: number|string, rotateY: number|string) {
  return `scale(${rotateY.toString().replace('-', '').replace(/deg/ig, '') === '180' ? -1 : 1}, ${rotateX.toString().replace('-', '').replace(/deg/ig, '') === '180' ? -1 : 1})`;
}

function checkTrans ({originVal, rotateX, rotateY}: {originVal: string, rotateX: number|string, rotateY: number|string} , preview: boolean) {
  return preview ? originVal : transRotate(rotateX, rotateY);
}

export {
  transRotate,
  checkTrans
};
