import Vue from "vue";
import ImgBgRemoveView from './ImgBgRemoveView.vue';
import { CombinedVueInstance } from "vue/types/vue";
import store from "@/pages/index/store";
import bus from "@/pages/index/common/utils/bus";

const ImgBgRemoveViewConstructor = Vue.extend(ImgBgRemoveView);

type InstanceMethods = {
  onCancel?: () => void;
  onSuccess: (url: string) => void;
  imageLoaded?: () => void;
};

type InstanceProps = {
  compId?: string;
  scale?: number;
  left?: number;
  top?: number;
  displayWidth?: number;
  displayHeight?: number;
  opacity?: number;
  rotate?: number;
  src: string;
  isComponent?: boolean;
  onCancel?: () => void;
  onSuccess?: (url: string) => void;
  imageLoaded?: () => void;
};
type InstancePublicProp = {
  config: InstanceProps,
  visible: boolean;
  resetProperties: (isResetCanvas: boolean) => void;
  init: (config: InstanceProps) => void;
};

export let instance: CombinedVueInstance<
  Record<never, any> & Vue,
  InstanceProps & InstancePublicProp,
  InstanceMethods,
  object,
  Record<never, any>
>;

export const createInstance = (props: InstanceProps) => {
  if (!instance) {
    instance = new ImgBgRemoveViewConstructor({
      // data: { config: props },
      store,
      el: document.createElement("div"),
    });
    instance.config = props;
    instance.visible = true;
  } else {
    instance.resetProperties(true);
    instance.visible = true;
    instance.init(props);
  }
  return instance;
};

export type ImgBgRemoveViewOptions = {
  props: InstanceProps;
  parent?: string;
} & InstanceMethods;

const ImgBgRemover = ({
  onCancel,
  onSuccess,
  imageLoaded,
  props,
  parent
}: ImgBgRemoveViewOptions) => {
  createInstance({ ...props, onCancel, onSuccess, imageLoaded });
  instance.visible = true;
  if (props.isComponent) {
    const handleChangeCurrentComponent = () => {
      instance.visible = false;
      bus.$off("changeCurrentComponent", handleChangeCurrentComponent);
    };
    bus.$once("changeCurrentComponent", handleChangeCurrentComponent);
    instance.$watch("visible", (visible) => {
      if (!visible) {
        bus.$once("changeCurrentComponent", handleChangeCurrentComponent);
      }
    });
  }


  if (parent) {
    const parentEl = document.querySelector(parent);
    parentEl && parentEl.appendChild(instance.$el);
  } else {
    document.body.appendChild(instance.$el);
  }
};

export default ImgBgRemover;