class Point {
  public x: number;
  public y: number;
  constructor(x: number, y: number) {
    this.x = x;
    this.y = y;
  }
}

// tslint:disable-next-line:interface-name
interface ItPoint {
  x: number;
  y: number
}

export default class Rect {
  public top: any;
  public right: any;
  public left: any;
  public bottom: any;
  public width: any;
  public height: any;

  constructor(src: HTMLElement | any) {
    let rect: any;
    if (src instanceof HTMLElement) {
      rect = src.getBoundingClientRect();
    } else {
      rect = { ...src };
      if (!('left' in rect) && 'x' in rect) {
        rect.left = rect.x;
      }

      if (!('top' in rect) && 'y' in rect) {
        rect.top = rect.y;
      }

      if (!('right' in rect) && 'width' in rect) {
        rect.right = rect.left + rect.width;
      }

      if (!('bottom' in rect) && 'height' in rect) {
        rect.bottom = rect.top + rect.height;
      }
    }

    ['top', 'right', 'bottom', 'left'].forEach((prop) => (this[prop] = rect[prop]));

    Object.defineProperty(this, 'x', {
      enumerable: true,
      get() {
        return this.left;
      },
      set(val) {
        this.left = val;
      }
    });

    Object.defineProperty(this, 'y', {
      enumerable: true,
      get() {
        return this.top;
      },
      set(val) {
        this.top = val;
      }
    });

    Object.defineProperty(this, 'width', {
      enumerable: true,
      get() {
        return this.right - this.left;
      },
      set(val) {
        this.right = this.left + val;
      }
    });

    Object.defineProperty(this, 'height', {
      enumerable: true,
      get() {
        return this.bottom - this.top;
      },
      set(val) {
        this.bottom = this.top + val;
      }
    });
  }

  public round() {
    const src = {
      top: Math.round(this.top as any),
      right: Math.round(this.right as any),
      bottom: Math.round(this.bottom as any),
      left: Math.round(this.left as any)
    };
    return new Rect(src as any);
  }

  public rotatedPoint(rotate: number, point?: ItPoint) {
    const { top, left, width, height } = this;

    const rad = Math.PI / 180 * rotate;
    const cos = Math.cos(rad);
    const sin = Math.sin(rad);

    const centerX = width / 2 + left;
    const centerY = height / 2 + top;

    const getRotatedPoint = (point: ItPoint): any => {
      const x = point.x - centerX;
      const y = point.y - centerY;

      return new Point(x * cos - y * sin + centerX, x * sin + y * cos + centerY);
    };
    return point ? getRotatedPoint(point) : getRotatedPoint;
  }

  public rotatedPoints(rotate: number) {
    const getRotatedPoint = this.rotatedPoint(rotate);
    const { top, right, bottom, left } = this;

    return {
      topLeft: getRotatedPoint(new Point(left, top)),
      topRight: getRotatedPoint(new Point(right, top)),
      bottomRight: getRotatedPoint(new Point(right, bottom)),
      bottomLeft: getRotatedPoint(new Point(left, bottom))
    };
  }

  public rotatedRect(rotate: number) {
    const [x, y] = Object.values(this.rotatedPoints(rotate)).reduce((result, point) => {
      result[0].push(point.x);
      result[1].push(point.y);
      return result;
    }, [[], []]);

    return new Rect({
      top: Math.min(...y),
      bottom: Math.max(...y),
      right: Math.max(...x),
      left: Math.min(...x)
    });
  }

  /**
   * 判断当前矩形是否包含传进的矩形
   *
   * @param {Rect} rect 要判断是否被包含的矩形
   * @param {boolean} [overlap=false] 是否完全包含
   * @returns
   * @memberof Rect
   */
  public isContain(rect: Rect, overlap = false) {
    const { top, right, bottom, left } = rect;
    const { top: top1, right: right1, bottom: bottom1, left: left1 } = this;
    let result: boolean;

    if (overlap) {
      // 交集
      result = !(left > right1 || top > bottom1 || left > right1 || top > bottom1);
    } else {
      // 包含
      result = left >= left1 && right <= right1 && top >= top1 && bottom <= bottom1;
    }
    return result;
  }
}
