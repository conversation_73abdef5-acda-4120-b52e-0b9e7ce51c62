import { cloneDeep, isEqual, isNumber } from "lodash-es";

// tslint:disable-next-line:interface-name
interface Ioptions {
  [key: string]: any
}

export class HistoricalRecord {

  private arr: any[] = []; // 操作记录数组
  private index = -1; // 当前记录位置
  private maxStack = 16; // 最多存多少步 // 暂时改为存储15次记录
  private delay = 500; // 多少毫秒内是一步操作
  private updateTime = 0;

  constructor(options?: Ioptions) {
    if (options) {
      this.setOptions(options);
    }
  }

  public setOptions(options: Ioptions) {
    const { maxStack, delay } = options;
    if (maxStack && isNumber(maxStack) && !isNaN(maxStack)) {
      this.maxStack = maxStack;
    }
    if (delay && isNumber(delay) && !isNaN(delay)) {
      this.delay = delay;
    }
  }
  public push(data: any, clone = false) {
    let newData = data;
    if (clone) {
      newData = cloneDeep(data);
    }
    if (this.index !== -1 && isEqual(newData, this.arr[this.index])) {
      return;
    }
    const newUpdateTime = +new Date();
    const oldUpdateTime = this.updateTime;
    this.updateTime = newUpdateTime;
    // delay（默认300）ms以内算一步操作，更新arr当前索引下的值
    if (newUpdateTime - oldUpdateTime < this.delay) {
      this.arr[this.index] = newData;
      return;
    }
    // 数组长度大于当前所在索引，说明撤销过，删除当前索引后的数组项
    if (this.arr.length - 1 > this.index) {
      this.arr.splice(this.index + 1);
    }
    // 添加新纪录
    this.arr.push(newData);
    this.index++;
    // 删除超出的记录
    if (this.arr.length > this.maxStack) {
      this.arr.shift();
      this.index--;
    }
  }
  public undo() {
    if (this.index === 0) {
      return;
    }
    this.index--;
    const data = this.arr[this.index];
    return data;
  }
  public redo() {
    if (this.index === this.arr.length - 1) {
      return;
    }
    this.index++;
    const data = this.arr[this.index];
    return data;
  }
  public clear() {
    this.arr = [];
    this.index = -1;
  }
  public getLength() {
    return this.arr.length;
  }
  public getIndex() {
    return this.index;
  }
  public get length() {
    return this.arr.length;
  }
  public get canUndo() {
    return this.index > 0;
  }
  public get canRedo() {
    return this.index !== this.length - 1;
  }
}

const historicalRecord = new HistoricalRecord();

export default historicalRecord;
