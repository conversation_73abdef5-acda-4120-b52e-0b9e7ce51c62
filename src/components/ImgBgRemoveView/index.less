@defaultcolor: #0092ED;

.image-clipper {
  pointer-events: all;
  position: absolute;
  z-index: 20;

  .mask {
    position: absolute;
    left: 0;
    top: 0;
  }

  .image-clipper-size {
    position: absolute;
    top: -30px;
    left: 10px;
    padding: 0 7px;
    background: #292C33;
    border-radius: 2px;
    opacity: .8;
    color: #fff;
    line-height: 20px;
    font-size: 12px;
    z-index: 21;
  }

  .image-tools {
    position: absolute;
    top: 100%;
    margin-top: 2px;
    display: flex;
    align-items: center;
    bottom: -52px;
    left: 11px;
    box-sizing: border-box;
    width: 296px;
    height: 48px;
    background: #fff;
    box-shadow: 1px 1px 8px 0px rgba(131, 134, 143, 0.2);
    border-radius: 2px;
    border: 1px solid rgba(82, 88, 102, 0.2);
    z-index: 21;
  }

  .el-icon-arrow-down {
    display: block;
    padding-left: 9px;
  }

  .image-clipper-type-button {
    padding: 8px 16px;
    width: 45px;
  }

  .image-clipper-tips {
    flex-grow: 1;
    width: 96px;
    height: 17px;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
  }

  .image-clipper-actions {
    margin-right: 16px;
  }

  .image-clipper-action {
    display: inline-block;
    cursor: pointer;
    margin-left: 16px;
    border: 0;
    padding: 0;
    height: 24px;
    width: 24px;
    min-width: 0;
    font-size: 16px;
    font-weight: bold;

    &:hover {
      svg {
        path {
          fill: #51CC86;
        }
      }
    }
  }

  .image-clipper-action-cancel {
    &:hover {
      svg {
        path {
          fill: #F9685D;
        }
      }
    }
  }
}

.image-clipper-type-button {
  display: flex;
  align-items: center;
  padding: 11px 4px;
  cursor: pointer;
}

.image-clipper-type {
  display: block;
  border: 2px solid #525866;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
}

.image-clipper-type-circle {
  border-radius: 50%;
}

.el-popover.image-clipper-popover {
  padding: 0 11px;
  min-width: 0;
}

.image-clipper .vdrr.dragging .vdrr-handles {
  display: block;
}

// .widget.widget-pic {}
.img-right-menu {
  position: absolute;
  z-index: 120;
  left: 100%;
  top: 0;
  width: auto;
  height: auto;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  padding: 4px;
  border: 1px solid rgba(230, 235, 245, 1);
  box-shadow: 1px 1px 8px 0px rgba(131, 134, 143, 0.2);
  box-sizing: border-box;
  transform: translateX(12px);
  background: #fff;
  cursor: default;

  .img-right-menu-item {
    display: block;
    transition: all 0.3s;
  }

  .img-right-menu-item:not(.is-gif-disable):hover {
    background: rgba(243, 243, 247, 1);

    svg path {
      fill: #41C479;
    }
  }

  .adjustPic {
    cursor: pointer;
  }

  .replacePic {
    margin-top: 4px;
    cursor: pointer;
  }

  .is-gif-disable {
    cursor: not-allowed;

    svg path {
      fill: #DDDDDD;
    }
  }
}

.active-show {
  display: none;
}

&.active {
  .active-show {
    display: block;
  }
}

.adjusting {
  display: block;
}

.image-remove-bg {
  pointer-events: all;
  position: absolute;
  z-index: 9998;

  .image-remove-bg-content {
    width: 100%;
    height: 100%;
    transform-origin: left top;

    &:before {
      content: '';
      position: absolute;
      top: -1px;
      right: -1px;
      bottom: -1px;
      left: -1px;
      border: 1px solid @defaultcolor;
      pointer-events: none;
    }
  }

  .image-clipper {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .image-clipper-content {
      position: absolute;
      width: 100%;
      height: 100%;

      img,
      .image-remove-bg-canvas {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        touch-action: none;
      }

      .eraser-cursor {
        position: absolute;
        border: 1px solid #000;
        border-radius: 50%;
        pointer-events: none;
      }
    }
  }

  .image-tools {
    font-size: 12px;
    position: absolute;
    display: flex;
    flex-direction: column;
    left: 11px;
    width: 306px;
    border-radius: 2px;
    z-index: 21;

    .tools__operation {
      background: #FFFFFF;
      border-radius: 2px;
      border: 1px solid rgba(230, 235, 245, 1);
      box-shadow: 1px 1px 8px 0px rgba(131, 134, 143, 0.2);
    }

    .tools__erase {
      display: flex;
      align-items: center;
      margin-top: 4px;

      .erase__wrapper {
        box-sizing: border-box;
        width: 240px;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        box-shadow: 1px 1px 8px 0px rgba(131, 134, 143, 0.2);
        border-radius: 2px;
        border: 1px solid rgba(230, 235, 245, 1);
        background: #fff;

        .erase__title {
          font-size: 12px;
          color: #555555;
        }

        .erase__content {
          width: 104px;
          margin-left: 16px;
          margin-right: 16px;

          .el-slider {
            .el-slider__runway {
              height: 4px;
              margin: 13px 0;
              background-color: #E1E3E6;
            }

            .el-slider__bar {
              height: 4px;
              background-color: #41c479;
            }

            .el-slider__button-wrapper {
              width: 12px;
              height: 12px;
              margin-top: 5px;
            }

            .el-slider__button {
              width: 10px;
              height: 10px;
              background-color: #fff;
              box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
              border: 1px solid rgba(190, 197, 204, 1);
            }
          }
        }
      }
    }

    .image-tools-item {
      box-sizing: border-box;
      display: flex;
      height: 32px;
      align-items: center;

      &.tools__basic-operation {
        height: 48px;
        padding: 12px 16px;
        background: #FFFFFF;
        border-radius: 2px;
      }

      &.tools__edge-adjust {
        height: 32px;
        background: #FFFFFF;
        border-radius: 2px;
        padding-left: 16px;
        padding-bottom: 10px;

        .edge-adjust__title {
          margin-right: 16px;
          color: #555;
        }

        .edge-adjust__content {
          width: 104px;
          margin-right: 16px;
        }
      }

      .image-tools-item-title {
        color: #999999;
        text-align: center;
        cursor: pointer;
      }

      .image-tools-item-content {
        flex: 1;
      }

      .common-operation {
        display: flex;
        align-items: center;

        .svg-box {
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;

          &.svg-undo,
          &.svg-redo,
          &.erase,
          &.sure {
            margin-left: 16px;
          }

          &.close {
            margin-left: 40px;
          }

          &.sure {
            .completeLoad {
              width: 24px;
              height: 24px;
              border-radius: 50%;
              background-color: #41C479;
              line-height: 24px;
              text-align: center;
              color: #fff;
            }
          }
        }

        svg {
          width: 24px;
          height: 24px;

          &.undo,
          &.redo {
            opacity: .5;
            cursor: no-drop;
          }

          &.disabled {
            opacity: 1;
            cursor: pointer;
          }

          &.erase-active {
            color: '#41C479';

            /deep/ g {
              path {
                fill: #41C479;
              }
            }
          }
        }

        .svg-undo,
        .svg-redo {
          &.active:hover {
            background: #F3F3F7;
            border-radius: 50%;
          }
        }

        .erase:hover {
          background: #F3F3F7;
          border-radius: 50%;
        }

        .close:hover {
          svg {
            path {
              fill: #F9685D;
            }
          }
        }

        .sure:hover {
          svg {
            path {
              fill: #51CC86;
            }
          }
        }

        .line {
          width: 1px;
          height: 16px;
          background: rgba(255, 255, 255, .5);
        }
      }

      .el-slider {
        .el-slider__runway {
          height: 4px;
          margin: 13px 0;
          background-color: #E1E3E6;
        }

        .el-slider__bar {
          height: 4px;
          background-color: #41c479;
        }

        .el-slider__button-wrapper {
          width: 12px;
          height: 12px;
          margin-top: 5px;
        }

        .el-slider__button {
          width: 10px;
          height: 10px;
          box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.08);
          border: 1px solid rgba(190, 197, 204, 1);
        }
      }
    }
  }
}

.adjustmenu-tooltip {
  margin-left: 22px !important;
}

.control-img {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 200;
  pointer-events: none;
  overflow: visible;

  circle,
  path {
    pointer-events: auto;
    stroke-opacity: 1;
    stroke-width: 1;
    cursor: pointer;
  }
}

.fullscreen-modal {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: -1;
}