<!-- eslint-disable @typescript-eslint/no-empty-function -->
<template>
  <div class="image-remove-bg" @mousedown="e => e.stopPropagation()" :style="outerStyles" v-show="visible && config.displayWidth">
    <div class="image-remove-bg-content" :style="{ transform: `rotate(${-config.rotate}deg)` }">
      <div class="image-clipper" id="image-clipper" :style="[clipStyles]">
        <div class="image-clipper-content" :style="imgStyles">
          <!-- <img class="image-remove-bg-canvas" :src="src" /> -->
          <canvas
            class="image-remove-bg-canvas"
            ref="canvas"
            @mousedown="mousedown"
            @mousemove="eraseClear"
            @mouseenter="mouseenter"
            @mouseleave="mouseleave"
            :width="originalWidth"
            :height="originalHeight"
            :style="{ cursor: cleanable ? 'none' : 'crosshair', transform: 'translateZ(0)' }"
            @click="handleRemoveBg"
          ></canvas>
          <div
            class="eraser-cursor"
            v-show="cleanable"
            :style="{
              top: eraseCursor.top + 'px',
              left: eraseCursor.left + 'px',
              width: eraseSize / ratiox + 'px',
              height: eraseSize / ratioy + 'px',
              background: isOutCanvas ? '' : '#fff',
              borderColor: isOutCanvas ? 'transparent' : '#000',
            }"
          ></div>
        </div>
      </div>
    </div>
    <div :style="toolsStyle" class="image-tools">
      <div class="tools__operation">
        <div class="image-tools-item tools__basic-operation">
          <span class="image-tools-item-title">点击图片</span>
          <div class="image-tools-item-content common-operation">
            <div class="svg-box svg-undo" :class="{ active: canUndo, disabled: canUndo }" @click="handleUndo">
              <svgicon class="undo" name="editor-area/undo" width="24" height="24" original :class="{ active: canUndo, disabled: canUndo }" />
            </div>
            <div class="svg-box svg-redo" :class="{ active: canRedo, disabled: canRedo }" @click="handleRedo">
              <svgicon class="redo" name="editor-area/redo" width="24" height="24" original :class="{ active: canRedo, disabled: canRedo }" />
            </div>
            <div class="svg-box erase" :class="{ 'erase-active': cleanable }" @click="handleErase">
              <svgicon name="editor-area/erase" width="24" height="24" original />
            </div>
            <div class="svg-box close" @click="handleCancel">
              <svgicon name="editor-area/close" width="24" height="24" original />
            </div>
            <div class="svg-box sure" @click="handleComplete">
              <span v-if="isCompleteLoading" class="completeLoad"><i class="el-icon-loading"></i></span>
              <svgicon v-else name="editor-area/removebg-sure" width="24" height="24" original />
            </div>
          </div>
        </div>
        <div v-if="isEdgeAdjust" class="image-tools-item tools__edge-adjust">
          <span class="image-tools-item-title edge-adjust__title">边缘调整</span>
          <div class="edge-adjust__content" @click="handleClickColorDiff">
            <el-slider v-model="colorDiff" :show-tooltip="false"> </el-slider>
          </div>
          {{ colorDiff }}
        </div>
      </div>
      <div v-if="cleanable" class="tools__erase">
        <div class="erase__wrapper">
          <span class="erase__title">橡皮大小</span>
          <div class="erase__content" @click="handleClickEraseSize">
            <el-slider :min="1" v-model="eraseSize" :show-tooltip="false"> </el-slider>
          </div>
          {{ eraseSize }}
        </div>
      </div>
    </div>
    <div class="fullscreen-modal" v-if="!config.isComponent"></div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from "vue-property-decorator";
import "./index.less";
import { HistoricalRecord } from "./history/index";
import Rect from "./Rect";
import clickOutside from "vue-click-outside";
export interface Point {
  x: number;
  y: number;
}
@Component({
  directives: {
    "click-outside": clickOutside,
  },
})
export default class ImgBgRemoveView extends Vue {
  config = {
    compId: "",
    displayWidth: 0,
    displayHeight: 0,
    left: 0,
    top: 0,
    scale: 1,
    isComponent: true,
    src: "",
    rotate: 0,
    opacity: 1,
    handler: ".img-remove-bg",
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    imageLoaded: () => {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onCancel: () => {},
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    onSuccess: (url: string) => {
      console.log("url", url);
    },
  };

  visible = true;
  // 定义组件的属性
  // rotate = "0deg";
  // top = 10;
  // left = 18;
  // width = parseInt(167 + "", 10);
  // height = parseInt(208 + "", 10);
  // displayWidth = 460;
  // displayHeight = 574;
  originalWidth = 460;
  originalHeight = 574;

  // 定义组件的内部变量
  ratiox = 1;
  ratioy = 1;

  ctx: null | CanvasRenderingContext2D = null;
  origImageDataArray: null | Uint8ClampedArray = null;
  lastImageDataArray: null | Uint8ClampedArray = null;
  // 边缘调整
  isEdgeAdjust = false; // 是否显示边缘调整
  colorDiff = 30;
  clipType = "rect"; // circle
  startX = -1;
  startY = -1;
  historicalRecord = new HistoricalRecord();

  // 橡皮擦模式相关的字段
  cleanable = false;
  clearing = false;
  beginPoint: null | Point = null;
  points: Point[] = [];
  eraseSize = 20;
  eraseCursor = {
    top: 0,
    left: 0,
  };
  isOutCanvas = true; // 鼠标是否在画布之外

  // 用来判断图片是否发生了变化
  initBase64 = "";

  // 上传中
  isCompleteLoading = false;

  wheelEle: any = {};

  public get canvas() {
    return this.$refs.canvas as HTMLCanvasElement;
  }

  get containerWidth() {
    if (!this.$store.state) {
      return document.querySelector(".canvas-container")?.clientWidth || 0;
    }
    return this.$store.state.containerWidth;
  }

  get scale() {
    return Math.round((this.containerWidth / 1280) * 10000) / 10000;
  }

  handleClickEraseSize(e: MouseEvent) {
    // 如果点击的对象是el-slider__button 不处理
    if (e.target instanceof HTMLDivElement && e.target.className.includes("el-slider__button")) {
      return;
    }
    // console.log("e.target...", e.target);
    const dom = document.querySelector(".erase__content") as HTMLDivElement;
    // 获取点击位置
    const { offsetX } = e;
    // 获取点击位置在滑块上的百分比
    const percent = offsetX / dom!.clientWidth;
    // 根据百分比计算橡皮擦的大小
    this.eraseSize = Math.round(percent * 100);
    //
    // console.log("handleClickEraseSize..percent", percent, this.eraseSize);
  }

  handleClickColorDiff(e: MouseEvent) {
    // 如果点击的对象是el-slider__button 不处理
    if (e.target instanceof HTMLDivElement && e.target.className.includes("el-slider__button")) {
      return;
    }
    const dom = document.querySelector(".edge-adjust__content") as HTMLDivElement;
    // 获取点击位置
    const { offsetX } = e;
    // 获取点击位置在滑块上的百分比
    const percent = offsetX / dom!.clientWidth;
    // 根据百分比计算橡皮擦的大小
    this.colorDiff = Math.round(percent * 100);
  }

  public get outerStyles() {
    const {
      config: { displayWidth, displayHeight, top, left },
      getScaleSize,
    } = this;

    if (this.$el.parentElement) {
      // 获取父节点的width height x y
      const { height, x, bottom } = this.$el.parentElement!.getBoundingClientRect();
      // 获取父节点距离页面底部的距离
      const parentBottom = document.body.clientHeight - bottom;

      const maxTop = height + parentBottom - getScaleSize(displayHeight + 126) - 40;
      // console.log("maxTop...", height, bottom, maxTop, getScaleSize(top), getScaleSize(displayHeight + 126));
      return {
        width: `${getScaleSize(displayWidth)}px`,
        height: `${getScaleSize(displayHeight)}px`,
        top: `${Math.min(maxTop, getScaleSize(top))}px`,
        left: `${Math.max(-x + 40, getScaleSize(left))}px`,
      };
    }
    return {
      width: `${getScaleSize(displayWidth)}px`,
      height: `${getScaleSize(displayHeight)}px`,
      top: `${getScaleSize(top)}px`,
      left: `${getScaleSize(left)}px`,
    };
  }

  getScaleSize = (size: number, scale = this.scale) => {
    // console.log("getScaleSize", size, scale, Math.round(size * scale * 100) / 100);
    // 保留两位小数
    return Math.round(size * scale * 100) / 100;
  };

  public get clipStyles() {
    return {
      // transform: `${transRotate(rotateX, rotateY)}`,
      borderRadius: this.clipType === "circle" ? "50%" : "",
    };
  }

  public get imgStyles() {
    const {
      config: { opacity, displayWidth, displayHeight },
      getScaleSize,
    } = this;
    return {
      opacity,
      width: `${getScaleSize(displayWidth)}px`,
      height: `${getScaleSize(displayHeight)}px`,
      // transform: `scale(${this.scale})`,
      transformOrigin: "left top",
    };
  }

  public get tolerrance() {
    return this.colorDiff * this.colorDiff;
  }

  // 监听tolerrance属性的变化
  @Watch("tolerrance")
  public watchTolerrance() {
    this.changeTolerrance();
  }

  // 改变容差值
  public changeTolerrance() {
    const { startX, startY, lastImageDataArray } = this;
    if (startX !== -1 && startY !== -1 && lastImageDataArray) {
      this.restoreLast();
      this.fill(startX, startY);
    }
  }

  // 填充
  public fill(x: number, y: number, canRecord = false) {
    const { ctx, originalWidth, originalHeight, lastImageDataArray } = this;
    if (!ctx) return;
    // console.log("fill2", ctx!.getImageData(0, 0, originalWidth, originalHeight), x, y, originalWidth, originalHeight);
    const imageData = this.floodFill(ctx!.getImageData(0, 0, originalWidth, originalHeight), x, y, originalWidth, originalHeight);
    // console.log("fill3", imageData);
    ctx!.putImageData(imageData, 0, 0);
    // console.log("fill4");
    if (canRecord) {
      this.historicalRecord.push({
        data: new Uint8ClampedArray(imageData.data),
        lastImageDataArray,
        x,
        y,
      });
    }
  }
  // 计算两个颜色的距离
  public colorDist(c1: number, c2: number) {
    /* tslint:disable */
    // 将c1和c2转换为RGB格式
    const r1 = c1 & 0xff;
    const g1 = (c1 >> 8) & 0xff;
    const b1 = (c1 >> 16) & 0xff;

    const r2 = c2 & 0xff;
    const g2 = (c2 >> 8) & 0xff;
    const b2 = (c2 >> 16) & 0xff;

    // 计算两个颜色的R、G、B通道的差值
    const dr = Math.abs(r1 - r2);
    const dg = Math.abs(g1 - g2);
    const db = Math.abs(b1 - b2);
    // 返回两个颜色的距离
    return dr * dr + dg * dg + db * db;
  }
  // 定义一个floodFill函数，用于对图像进行填充
  public floodFill(imageData: ImageData, x: number, y: number, w: number, h: number) {
    // 获取容差值
    const { tolerrance } = this;
    // const start = window.performance.now();

    // 将图像数据转换为Uint32Array类型
    const u32 = new Uint32Array(imageData.data.buffer);
    // 创建一个Uint8Array类型的数组，用于记录已经访问过的像素点
    const visited = new Uint8Array(u32.length);

    // 创建一个队列，用于存储待处理的像素点
    const queue: number[] = [];
    // 将起始点加入队列，并标记为已访问
    queue.push(x + y * w);
    visited[x + y * w] = 1;
    // 获取起始点的颜色值
    const c = u32[x + y * w];

    // 当队列不为空时，继续处理
    while (queue.length) {
      // let index = queue.shift()
      // 从队列中取出一个像素点
      const index = queue.pop()!;
      // 计算像素点的坐标
      const x = index % w;
      const y = (index - x) / w;
      // 将像素点颜色设置为0
      u32[index] = 0;

      // 定义一个数组，用于表示像素点的8个方向
      const delta = [-1, 0, 0, -1, 1, 0, 0, 1];

      // 遍历像素点的8个方向
      for (let i = 0; i + 1 < delta.length; i += 2) {
        // 计算像素点的坐标
        const x1 = x + delta[i];
        const y1 = y + delta[i + 1];

        // 如果像素点超出图像范围，则跳过
        if (x1 < 0 || x1 >= w || y1 < 0 || y1 >= h) {
          continue;
        }

        // 计算像素点的索引
        const index1 = x1 + y1 * w;

        // 如果像素点未被访问过，则将其加入队列，并标记为已访问
        if (!visited[index1]) {
          visited[index1] = 1;
          // 获取像素点的颜色值
          const c1 = u32[index1];
          // 计算颜色值之间的距离
          const d = this.colorDist(c, c1);
          // 如果颜色值之间的距离小于容差值，则将其加入队列
          if (d < tolerrance) {
            queue.push(index1);
          }
        }
      }
    }

    // console.log('floodFill took', window.performance.now() - start, visited.filter(v => v).length, 'visited');

    // 返回处理后的图像数据
    return imageData;
  }

  public get toolsStyle() {
    let left = "0";
    let top = "100%";
    if (this.config.compId) {
      const { xMin, yMax } = ((window as MyWindow).cocos as any).getRotateEWordRect(this.config.compId);
      top = this.getScaleSize(yMax - this.config.top) + "px";
      left = this.getScaleSize(xMin - this.config.left) + "px";
    }
    return {
      // top: bottom - this.top + 10 + "px",
      // left: left - this.left + "px",
      transform: `scale(${this.config.scale}) translate(0, 6px)`,
      transformOrigin: "left top",
      // top: "100%",
      // left: "0",
      // top: this.getScaleSize(bottom - this.config.top + 10) + "px",
      // left: this.getScaleSize(left - this.config.left) + "px",
      top,
      left,
    };
  }

  public get rotatedPoint() {
    const { left, top, displayWidth: width, displayHeight: height, rotate } = this.config;
    // console.log(
    //   "rotatedPoint...",
    //   new Rect({
    //     top,
    //     left,
    //     width,
    //     height,
    //   }).rotatedRect(rotate),
    // );
    return new Rect({
      top,
      left,
      width,
      height,
    }).rotatedRect(-rotate);
  }

  // 初始化组件
  public init(config?: any) {
    if (config) {
      this.config = config;
    }
    const {
      config: { src },
      canvas,
    } = this;
    this.$nextTick(() => {
      const img = new Image();
      img.setAttribute("crossOrigin", location.origin);
      // this.$el.appendChild(img);
      img.addEventListener("load", () => {
        this.originalWidth = parseInt(img.width + "", 10);
        this.originalHeight = parseInt(img.height + "", 10);
        if (!this.config.isComponent) {
          this.$set(this, "config", {
            ...this.config,
            displayWidth: this.originalWidth,
            displayHeight: this.originalHeight,
            left: 1280 / 2 - this.originalWidth / 2,
            top: 960 / 2 - this.originalHeight / 2,
          });
        }
        this.$nextTick(() => {
          this.ratiox = this.originalWidth / this.getScaleSize(this.config.displayWidth);
          this.ratioy = this.originalHeight / this.getScaleSize(this.config.displayHeight);
          // this.ratiox = 1;
          // this.ratioy = 1;
          this.historicalRecord = new HistoricalRecord();
          this.historicalRecord.setOptions({ maxStack: 100 });
          // this.originalWidth改变后需要nexttick后canvas才能使用上新的宽高
          const { originalWidth, originalHeight } = this;
          const ctx = canvas.getContext("2d")!;
          // ctx.drawImage(img, 0, 0, originalWidth, originalHeight);
          ctx.drawImage(img, 0, 0);
          this.initBase64 = canvas.toDataURL("image/png");
          const imageData = ctx.getImageData(0, 0, originalWidth, originalHeight);
          const origImageDataArray = new Uint8ClampedArray(imageData.data);
          this.historicalRecord.push({
            data: origImageDataArray,
            lastImageDataArray: null,
            x: -1,
            y: -1,
          });
          this.origImageDataArray = origImageDataArray;
          this.ctx = ctx;
          this.$nextTick(() => {
            this.config.imageLoaded && this.config.imageLoaded();
            this.visible = true;
          });
        });
      });
      img.src = `${src}?time=${Date.now()}`;
    });
  }
  // 点击图片去背景
  public handleRemoveBg(e: MouseEvent) {
    if (this.cleanable) {
      return;
    }
    this.isEdgeAdjust = true;
    const x = Math.round(e.offsetX * this.ratiox);
    const y = Math.round(e.offsetY * this.ratioy);
    this.startX = x;
    this.startY = y;

    this.rememberLast();
    this.fill(x, y, true);
  }

  // 记录上一次的图像数据
  public rememberLast() {
    const { ctx, originalWidth, originalHeight } = this;
    const imageData = ctx!.getImageData(0, 0, originalWidth, originalHeight);
    this.lastImageDataArray = new Uint8ClampedArray(imageData.data);
  }
  // 恢复上一次的图像数据
  public restoreLast() {
    const { ctx, lastImageDataArray, originalWidth, originalHeight } = this;
    if (!lastImageDataArray) {
      return;
    }

    const imageData = ctx!.createImageData(originalWidth, originalHeight);
    imageData.data.set(lastImageDataArray);
    ctx!.putImageData(imageData, 0, 0);
  }

  public get canUndo() {
    const canUndo = this.historicalRecord ? this.historicalRecord.canUndo : false;
    return canUndo;
  }
  public get canRedo() {
    const canRedo = this.historicalRecord ? this.historicalRecord.canRedo : false;
    return canRedo;
  }
  public handleUndo() {
    const data = this.historicalRecord.undo();
    this.useHistoryData(data);
  }
  public handleRedo() {
    const data = this.historicalRecord.redo();
    this.useHistoryData(data);
  }
  public useHistoryData(historyData: any) {
    if (historyData) {
      const { ctx, originalWidth, originalHeight } = this;
      const { data, lastImageDataArray, x, y } = historyData;
      const imageData = ctx!.createImageData(originalWidth, originalHeight);
      imageData.data.set(data);
      ctx!.putImageData(imageData, 0, 0);
      this.startX = x;
      this.startY = y;
      this.lastImageDataArray = lastImageDataArray;
    }
  }

  public handleErase() {
    this.cleanable = !this.cleanable;
  }
  public mousedown(e: MouseEvent) {
    if (!this.cleanable) {
      return;
    }
    let { offsetX: x, offsetY: y } = e;
    x = Math.round(x * this.ratiox);
    y = Math.round(y * this.ratioy);
    this.beginPoint = { x, y };
    this.points.push({ x, y });
    const ctx = this.ctx!;
    ctx.lineWidth = this.eraseSize;
    ctx.lineCap = "round"; // 线条起始和结尾样式
    ctx.lineJoin = "round"; // 线条转弯样式
    ctx.strokeStyle = "#000";
    this.clearing = true;
  }
  public eraseClear(e: MouseEvent) {
    if (!this.cleanable) {
      return;
    }
    const ctx = this.ctx!;
    const beginPoint = this.beginPoint!;
    let { offsetX: x, offsetY: y } = e;
    this.initEraseCursor(x, y);
    x = Math.round(x * this.ratiox);
    y = Math.round(y * this.ratioy);
    if (!this.clearing) {
      return;
    }
    this.points.push({ x, y });
    if (this.points.length > 3) {
      const lastTwoPoints = this.points.slice(-2);
      const controlPoint = lastTwoPoints[0];
      const endPoint = {
        x: (lastTwoPoints[0].x + lastTwoPoints[1].x) / 2,
        y: (lastTwoPoints[0].y + lastTwoPoints[1].y) / 2,
      };

      ctx.beginPath();
      ctx.globalCompositeOperation = "destination-out";
      ctx.moveTo(beginPoint.x, beginPoint.y);
      ctx.quadraticCurveTo(controlPoint.x, controlPoint.y, endPoint.x, endPoint.y);
      ctx.stroke();
      ctx.globalCompositeOperation = "source-over";
      ctx.closePath();
      this.beginPoint = endPoint;
    } else {
      ctx.beginPath();
      ctx.globalCompositeOperation = "destination-out";
      ctx.moveTo(beginPoint.x, beginPoint.y);
      ctx.lineTo(x, y);
      ctx.stroke();
      ctx.globalCompositeOperation = "source-over";
      ctx.closePath();
      this.beginPoint = { x, y };
    }
  }
  public mouseup(e: MouseEvent) {
    if (!this.cleanable || !this.clearing) {
      return;
    }
    this.eraseClear(e);
    this.clearing = false;
    const { ctx, originalWidth, originalHeight } = this;
    const imageData = ctx!.getImageData(0, 0, originalWidth, originalHeight);
    this.historicalRecord.push({
      data: new Uint8ClampedArray(imageData.data),
      lastImageDataArray: null,
      x: -1,
      y: -1,
    });
  }
  public initEraseCursor(x: number, y: number) {
    this.eraseCursor.left = x - this.eraseSize / this.ratiox / 2;
    this.eraseCursor.top = y - this.eraseSize / this.ratioy / 2;
  }
  mousewheel(e: any) {
    const { eraseSize, cleanable } = this;
    if (!cleanable) {
      return;
    }
    const min = 1;
    const max = 2400;
    const wheelDelta = e.wheelDelta || e.detail || 0;
    const upOrDown = Math.abs(wheelDelta) / wheelDelta; // 1 或-1
    const multi = 2 * upOrDown; // 滚一次滚轮 增加/减少大小值
    if (eraseSize + multi <= min) {
      this.eraseSize = min;
    } else if (eraseSize + multi >= max) {
      this.eraseSize = max;
    } else {
      this.eraseSize += multi;
    }
    const { offsetX: x, offsetY: y } = e;
    this.initEraseCursor(x, y);
  }

  public mouseenter() {
    this.isOutCanvas = false;
  }
  public mouseleave(e: any) {
    this.isOutCanvas = true;

    // 判断是否是在使用橡皮擦
    if (this.clearing) {
      this.mouseup(e);
      return;
    }
  }
  public handleCancel() {
    this.visible = false;
    if (this.config.onCancel) {
      this.config.onCancel();
    }
    this.resetProperties(true);
  }
  public async handleComplete() {
    const base64 = this.canvas.toDataURL("image/png");
    if (base64 === this.initBase64) {
      this.handleCancel();
    } else {
      this.isCompleteLoading = true;
      this.canvas.toBlob(async blob => {
        if (!blob) {
          console.log("图片生成失败，请重试");
          this.isCompleteLoading = false;
        }
        const file = new File([blob as Blob], "bgRemove.png", { type: (blob as Blob).type });
        const formData = new FormData();
        formData.append("file", file);
        formData.append("minify", "0");
        const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);
        // 加载url完毕后再执行onSuccess 否则cocos加载url时间长的话图片更替会有延迟
        const newImg = new Image();
        newImg.src = url;
        newImg.onload = () => {
          this.$emit("close");
          const timer = setTimeout(() => {
            clearTimeout(timer);
            this.visible = false;
            this.isCompleteLoading = false;
            this.resetProperties(true);
            if (this.config.onSuccess) {
              this.config.onSuccess(url);
            }
          }, 300);
        };
      }, "image/png");
      // const url = await (window as MyWindow).$getPageConfigByKey("uploadFile")(formData);

      // this.uploadImg(file);
    }
  }

  onGlobalClick(e: MouseEvent) {
    // const EditAreaDom = document.querySelector(".edit-area");
    //  && !EditAreaDom?.contains(e.target as Node)
    const handlerDom = document.querySelector(this.config.handler);
    if (!this.$el.contains(e.target as Node) && !handlerDom?.contains(e.target as Node)) {
      this.handleCancel();
    }
  }

  public resetProperties(resetCanvas = false) {
    if (resetCanvas && this.ctx) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx = null;
      this.origImageDataArray = null;
      this.lastImageDataArray = null;
    }
    this.isEdgeAdjust = false;
    this.colorDiff = 30;
    this.clipType = "rect";
    this.startX = -1;
    this.startY = -1;
    this.historicalRecord = new HistoricalRecord();
    this.cleanable = false;
    this.clearing = false;
    this.beginPoint = null;
    this.points = [];
    this.eraseSize = 20;
    this.eraseCursor = {
      top: 0,
      left: 0,
    };
    this.isOutCanvas = true;
    this.initBase64 = "";
    this.isCompleteLoading = false;
  }

  // 组件挂载时执行
  public mounted() {
    this.$nextTick(() => {
      const wheelEle = document.getElementById("image-clipper") as HTMLElement;
      this.wheelEle = wheelEle;
      this.init();
      document.addEventListener("mouseup", this.mouseup);
      wheelEle.addEventListener("mousewheel", this.mousewheel);
      wheelEle.addEventListener("DOMMouseScroll", this.mousewheel);
      // 监听全局的点击事件 当点击其他区域时 执行取消逻辑
      window.addEventListener("mousedown", this.onGlobalClick, true);
      this.$once("hook:beforeDestroy", () => {
        document.removeEventListener("mouseup", this.mouseup);
        wheelEle.removeEventListener("mousewheel", this.mousewheel);
        wheelEle.removeEventListener("DOMMouseScroll", this.mousewheel);
        window.removeEventListener("mousedown", this.onGlobalClick, true);
      });
    });
  }
}
</script>
