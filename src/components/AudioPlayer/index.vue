<template>
  <div class="audio-player">
    <span class="audio-player-btn">
      <i :class="playing ? 'el-icon-video-pause' : 'el-icon-video-play'" @click="onClickBtn" />
    </span>
    <el-slider v-model="currentTime" :max="duration" :step="0.1" :format-tooltip="formatTime" @change="seek"></el-slider>
    <span class="audio-player-duration">
      {{ formatTime(duration) }}
    </span>
  </div>
</template>

<script lang="ts">
import bus from "@/pages/index/common/utils/bus";
import { Howl } from "howler";
import { Component, Prop, Vue, Watch } from "vue-property-decorator";

@Component
export default class AudioPlayer extends Vue {
  @Prop({
    required: true,
  })
  src!: string;

  sound!: Howl;
  loaded = false;
  playing = false;
  duration = 0;
  currentTime = 0;

  @Watch("src", {
    immediate: true,
  })
  srcWacher(src: string) {
    this.init(src);
  }

  init(src: string) {
    if (this.sound) {
      this.sound.stop();
    }
    this.duration = 0;
    this.currentTime = 0;
    this.playing = false;
    this.loaded = false;
    this.sound = new Howl({
      src: [src],
      onplay: () => {
        this.playing = true;
        this.flashCurrentTime();
      },
      onpause: () => {
        this.playing = false;
      },
      onend: () => {
        this.currentTime = 0;
        this.playing = false;
      },
      onload: () => {
        this.loaded = true;
        this.duration = Math.round(this.sound.duration());
        bus.$emit("audioLoaded", { src, formatTime: this.formatTime(this.duration), duration: this.duration });
      },
    });
  }

  destroyed() {
    if (this.sound) {
      this.sound.stop();
    }
  }

  seek(s: number) {
    this.sound.seek(s);
  }

  pause() {
    this.sound.pause();
  }

  play() {
    this.sound.play();
  }

  onClickBtn() {
    this.playing ? this.pause() : this.play();
  }

  getCurrentTime() {
    const seek = (this.sound.seek() as number) || 0;
    return seek;
  }

  flashCurrentTime() {
    if (this.sound.playing()) {
      this.currentTime = this.getCurrentTime();
      requestAnimationFrame(this.flashCurrentTime);
    }
  }

  formatTime(s: number) {
    return [Math.floor(s / 60), Math.floor(s % 60)].join(":").replace(/\b(\d)\b/g, "0$1");
  }
}
</script>

<style scoped lang="less">
.audio-player {
  display: flex;
  align-items: center;
  margin-right: 6px;
  flex-grow: 1;

  &-btn {
    line-height: unset;
    color: #409eff;
    font-size: 20px;
    cursor: pointer;
    margin-right: 7px;
  }

  .el-slider {
    flex-grow: 1;
    width: 50px;
    margin-right: 10px;
  }

  /deep/ .el-slider__button {
    width: 8px;
    height: 8px;
  }

  /deep/ .el-slider__button-wrapper {
    width: 12px;
    height: 12px;
    left: 0px;
    top: 50% !important;
    transform: translate(-50%, -50%);
    &::after {
      content: unset;
      display: block;
    }
  }
  /deep/ .el-slider__button-wrapper::after {
    display: block;
  }
  /deep/ .el-slider__button-wrapper .el-tooltip {
    display: block;
  }
}
</style>
