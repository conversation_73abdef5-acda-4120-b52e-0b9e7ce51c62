{"animations": {"afterSubmitCorrect": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}, "afterSubmitWrong": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}}, "animationsForClient": {}, "components": [{"type": "specialComponent", "subType": "keyboardEnglish", "tag": "", "dragable": true, "deletable": false, "canCombine": false, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false}}, "properties": {"active": true, "width": 1280, "height": 326, "x": 0, "y": -200, "isPackUp": false, "keyboardType": 1}, "id": "2", "extra": {"tag": ""}}, {"tag": "blankModule", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 106.1, "height": 106.1, "x": -302.3, "y": 211.7, "texture": "https://jiaoyanbos.cdnjtzy.com/cw_1a2abbc85cd5d026ca692e866ce8dd88.png", "angle": 0}, "id": "3", "extra": {"characterLimit": "20", "labelColor": "#000", "fontSize": 32, "correctArray": ["you", "me"], "hasAllCorrect": true, "tag": "blankModule"}}, {"tag": "blankModule", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 106.1, "height": 106.1, "x": -442, "y": 211.7, "texture": "https://jiaoyanbos.cdnjtzy.com/cw_1a2abbc85cd5d026ca692e866ce8dd88.png", "angle": 0}, "extra": {"characterLimit": "4", "labelColor": "#000", "fontSize": 24, "correctArray": ["Do"], "hasAllCorrect": true, "tag": "blankModule"}, "id": "4"}, {"type": "label", "tag": "", "dragable": true, "properties": {"active": true, "fontSize": 32, "width": 425, "height": 41, "x": 5, "y": 219.1, "string": "单击添加文本", "str": "<size=32><color=#000000>know his telephone number?</c></s>", "color": "#000000", "cusorIndex": 25, "selectArr": [], "isLabelRight": true, "isFixed": false, "rowSpacing": 1, "angle": 0, "enableBold": false, "enableItalic": false, "enableUnderline": false, "lineHeight": 32, "horizontalAlign": 0, "textureArray": ["https://yaya.cdnjtzy.com/textImg-5ea3fa.png"]}, "id": "5", "extra": {"tag": ""}}], "extraDataMap": {"2": {"tag": ""}, "3": {"characterLimit": "20", "labelColor": "#000", "fontSize": 32, "correctArray": ["you", "me"], "hasAllCorrect": true, "tag": "blankModule"}, "4": {"characterLimit": "4", "labelColor": "#000", "fontSize": 24, "correctArray": ["Do"], "hasAllCorrect": true, "tag": "blankModule"}, "5": {"tag": ""}}, "extraStageData": {"isAutoSubmit": false, "keyboardType": 1, "hasRecover": true}, "resourceList": ["https://jiaoyanbos.cdnjtzy.com/cw_1a2abbc85cd5d026ca692e866ce8dd88.png", "https://yaya.cdnjtzy.com/textImg-5ea3fa.png"], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "template": {"questionType": 5, "bundleUrl": "https://yaya.cdnjtzy.com/cchd_bundle/blankQuestion.zip", "bundleName": "sharkBlankQuestion", "category": 1012, "type": 1002, "tempType": 5, "name": "填空题", "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}, "animationConfig": [{"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "tags": [{"name": "blankModule", "label": "答题区", "editorConfig": [{"key": "selectEffect", "label": "选中效果", "type": "effect", "params": {}}, {"key": "characterLimit", "label": "字符限制", "type": "selectFromStep", "defaultValue": 1, "required": true, "editorConfigfromGlobalSwKeyboard": true, "params": {"options": []}}, {"key": "labelColor", "label": "文本颜色", "type": "text", "required": true, "default": "#000", "params": {}}, {"key": "fontSize", "label": "文本字号", "type": "text", "required": true, "default": 24, "params": {}}, {"key": "<PERSON><PERSON><PERSON>y", "label": "答案", "type": "blank", "required": true, "params": {}}]}, {"name": "oneDragableObject", "label": "单个拖拽元素", "editorConfig": []}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}, {"key": "swKeyboard", "label": "切换键盘", "type": "keyboardSelect", "params": {"options": [{"label": "英文键盘", "value": "keyboardEnglish", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 20, "step": 1}}]}, {"label": "数学键盘", "value": "keyboard", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 10, "step": 1}}]}]}}]}, "questionType": 5, "versionInfo": {"v": 1, "timeStamp": 1658386236}, "thumbnail": "https://yaya.cdnjtzy.com/thumbnail-a68bd6.jpeg"}