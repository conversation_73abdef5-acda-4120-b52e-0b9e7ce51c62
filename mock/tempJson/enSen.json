{"animations": {}, "animationsForClient": {}, "components": [{"tag": "", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 1280, "height": 720, "x": 0, "y": 0, "texture": "https://testimg.zuoyebang.cc/cw_bfbda25ef9690338e346195f4194cc5e.png", "opacity": 255, "angle": 0}, "id": "6", "extra": {"tag": ""}}, {"type": "specialComponent", "subType": "keyboard", "tag": "", "dragable": true, "deletable": false, "canCombine": false, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false}}, "properties": {"active": true, "width": 1015, "height": 80, "x": -50, "y": -300, "keyboardType": 1}, "id": "1", "extra": {"tag": ""}}, {"tag": "", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 156, "height": 31, "x": 532, "y": 311.5, "texture": "https://testimg.zuoyebang.cc/cw_f0d5cb7965e06c6e43f4690caa5cc8af.png", "opacity": 255, "angle": 0, "flipType": 0}, "id": "2", "extra": {"tag": ""}}, {"type": "label", "tag": "", "dragable": true, "properties": {"active": true, "fontSize": 36, "lineHeight": 41, "width": 496, "height": 64, "x": 8, "y": 308, "string": "请输入文本", "str": "<b><color=#833500><size=36>看图/听音补全单词、写单词。</size></color></b>", "color": "#833500", "cusorIndex": 1, "selectArr": [], "isLabelRight": true, "isFixed": false, "opacity": 255, "angle": 0}, "id": "3", "extra": {"tag": ""}}, {"type": "label", "tag": "", "dragable": true, "properties": {"active": true, "fontSize": 32, "lineHeight": 37, "width": 714, "height": 58, "x": -82, "y": 191, "string": "请输入文本", "str": "<color=#FFFFFF><size=32>I know his telephone number。(改为一般疑问句)</size></color>", "color": "#FFFFFF", "cusorIndex": 1, "selectArr": [], "isLabelRight": true, "isFixed": false, "opacity": 255, "angle": 0}, "id": "4", "extra": {"tag": ""}}, {"type": "label", "tag": "", "dragable": true, "properties": {"active": true, "fontSize": 32, "lineHeight": 37, "width": 749, "height": 68, "x": -74.5, "y": 78, "string": "请输入文本", "str": "<color=#FFFFFF><size=32>Do</size></color><color=#FFFFFF><size=32> </size></color><color=#FFFFFF><size=32>you</size></color><color=#FFFFFF><size=32> know his telephone number? </size></color>", "color": "#FFFFFF", "cusorIndex": 1, "selectArr": [], "isLabelRight": true, "isFixed": false, "opacity": 255, "angle": 0}, "id": "5", "extra": {"tag": ""}}], "extraDataMap": {}, "extraStageData": {"isAutoSubmit": false, "keyboardType": 1, "hasRecover": true}, "resourceList": [], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "template": {"questionType": 5, "bundleUrl": "https://yaya.cdnjtzy.com/cchd_bundle/blankQuestion.zip", "bundleName": "sharkBlankQuestion", "category": 1012, "type": 1002, "tempType": 5, "name": "填空题", "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}, "animationConfig": [{"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "tags": [{"name": "blankModule", "label": "答题区", "editorConfig": [{"key": "selectEffect", "label": "选中效果", "type": "effect", "params": {}}, {"key": "characterLimit", "label": "字符限制", "type": "selectFromStep", "defaultValue": 1, "required": true, "editorConfigfromGlobalSwKeyboard": true, "params": {"options": []}}, {"key": "labelColor", "label": "文本颜色", "type": "text", "required": true, "default": "#000", "params": {}}, {"key": "fontSize", "label": "文本字号", "type": "text", "required": true, "default": 24, "params": {}}, {"key": "<PERSON><PERSON><PERSON>y", "label": "答案", "type": "blank", "required": true, "params": {}}]}, {"name": "oneDragableObject", "label": "单个拖拽元素", "editorConfig": []}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}, {"key": "swKeyboard", "label": "切换键盘", "type": "keyboardSelect", "params": {"options": [{"label": "英文键盘", "value": "keyboardEnglish", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 20, "step": 1}}]}, {"label": "数学键盘", "value": "keyboard", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 10, "step": 1}}]}]}}]}, "thumbnail": "https://zbkcocos.cdnjtzy.com/977xahpe3p92tbl8.png"}