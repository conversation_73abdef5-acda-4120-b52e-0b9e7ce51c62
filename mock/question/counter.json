{"components": [{"type": "specialComponent", "subType": "voice", "tag": "", "dragable": true, "deletable": false, "canCombine": false, "properties": {"active": true, "width": 600, "height": 120, "x": 0, "y": -200, "answerDuration": 15, "evaluatingText": "", "overScore": [60, 90], "overTimeNum": 4, "accuracy": 60, "wordType": 1, "autoBegin": false, "angle": 0}, "id": "1", "extra": {"tag": ""}}], "extraDataMap": {}, "resourceList": [], "template": {"name": "跟读题", "tempType": 8, "category": 1020, "bundleUrl": "", "questionType": 3, "bundleName": "followRecordQuestion", "tags": [{"name": "tone", "label": "升降调", "editorConfig": [{"key": "type", "label": "类型", "type": "select", "defaultValue": 1, "required": true, "params": {"options": [{"value": 1, "label": "降调"}, {"value": 2, "label": "升调"}]}}]}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff"}, "extraConfig": [], "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "animations": {}, "stageData": {}, "extraStageData": {"isAutoSubmit": true, "hasRecover": false}}