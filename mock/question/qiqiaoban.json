{"components": [{"type": "specialComponent", "subType": "tangram", "tag": "", "deletable": false, "dragable": true, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false, "active": false}}, "properties": {"active": true, "rightAnswer": [{"type": 1, "angle": -90, "scale": 77.78, "position": {"x": 140.0047923400882, "y": -132.23006961851203, "z": 0}, "color": {"_val": 4284962796}, "flip": false, "index": 1}, {"type": 0, "angle": -180, "scale": 109.99753088137935, "position": {"x": 178.89404642631152, "y": -210.0096966616237, "z": 0}, "color": {"_val": 4285778543}, "flip": false, "index": 0}, {"type": 0, "angle": -135, "scale": 77.78, "position": {"x": 237.22848699097904, "y": -229.45451018317948, "z": 0}, "color": {"_val": 4281833713}, "flip": false, "index": 0}, {"type": 0, "angle": -90, "scale": 155.56, "position": {"x": 311.6715385125348, "y": -138.9028477047353, "z": 0}, "color": {"_val": 4282763768}, "flip": false, "index": 0}, {"type": 0, "angle": -45, "scale": 77.78, "position": {"x": 237.2277410772023, "y": -48.351185226291136, "z": 0}, "color": {"_val": 4294342000}, "flip": false, "index": 0}, {"type": 2, "angle": 0, "scale": 77.78, "position": {"x": 256.6733005125348, "y": 9.983628295264687, "z": 0}, "color": {"_val": 4294329991}, "flip": false, "index": 0}, {"type": 0, "angle": 0, "scale": 155.56, "position": {"x": 224.4568245125348, "y": 103.87186629526468, "z": 0}, "color": {"_val": 4280170751}, "flip": false, "index": 0}], "width": 1280, "height": 720, "opacity": 255, "angle": 0, "x": 0, "y": 0}, "id": "1", "extra": {}}], "extraDataMap": {}, "resourceList": [], "template": {"name": "七巧板题", "tempType": 13, "category": 1025, "bundleUrl": "", "questionType": 3, "bundleName": "tangramQuestion", "tags": [], "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff"}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}], "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "animations": {}, "stageData": {}, "extraStageData": {"hasRecover": true, "isAutoSubmit": false}}