{"animations": {}, "animationsForClient": {}, "components": [{"type": "specialComponent", "subType": "keyboardEnglish", "tag": "", "dragable": true, "deletable": false, "canCombine": false, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false}}, "properties": {"active": true, "width": 1070, "height": 200, "x": -60, "y": -287, "keyboardType": 1, "isPackUp": false}, "id": "1", "extra": {"tag": ""}}], "extraDataMap": {}, "extraStageData": {"keyboardType": 1, "isAutoSubmit": true, "hasRecover": false}, "resourceList": [], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "template": {"questionType": 5, "bundleUrl": "https://yaya.cdnjtzy.com/cchd_bundle/blankQuestion.zip", "bundleName": "sharkBlankQuestion", "category": 1012, "type": 1002, "tempType": 5, "name": "填空题", "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}, "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "tags": [{"name": "blankModule", "label": "答题区", "editorConfig": [{"key": "selectEffect", "label": "选中效果", "type": "effect", "params": {}}, {"key": "characterLimit", "label": "字符限制", "type": "selectFromStep", "defaultValue": 1, "required": true, "editorConfigfromGlobalSwKeyboard": true, "params": {"options": [{"value": 1, "label": "1"}, {"value": 2, "label": "2"}, {"value": 3, "label": "3"}, {"value": 4, "label": "4"}, {"value": 5, "label": "5"}, {"value": 6, "label": "6"}, {"value": 7, "label": "7"}, {"value": 8, "label": "8"}, {"value": 9, "label": "9"}, {"value": 10, "label": "10"}, {"value": 20, "label": "20"}]}}, {"key": "labelColor", "label": "文本颜色", "type": "text", "required": true, "default": "#000", "params": {}}, {"key": "fontSize", "label": "文本字号", "type": "text", "required": true, "default": 24, "params": {}}, {"key": "correct", "label": "答案", "type": "blank", "required": true, "params": {}}]}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "extraConfig": [{"key": "guide", "label": "题干音频", "type": "audioSelect", "params": {}}, {"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}, {"key": "swKeyboard", "label": "切换键盘", "type": "keyboardSelect", "params": {"options": [{"label": "英文键盘", "value": "keyboardEnglish", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 20, "step": 1}}]}, {"label": "数学键盘", "value": "keyboard", "editorConfig": [{"key": "characterLimit", "label": "字符限制", "type": "select", "defaultValue": 1, "required": true, "params": {"min": 1, "max": 10, "step": 1}}]}]}}]}, "thumbnail": "https://zbkcocos.cdnjtzy.com/977xahpe3p92tbl8.png"}