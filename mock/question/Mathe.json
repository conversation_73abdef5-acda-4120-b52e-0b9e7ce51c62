{"components": [{"type": "specialComponent", "subType": "matchboard", "tag": "matchboard", "deletable": false, "properties": {"active": true, "matchType": 0, "mathType": 2, "grid": {"row": 7, "col": 3}, "width": 1140, "height": 180, "opacity": 255, "angle": 0, "x": 0, "y": 0, "operateLimit": "100001", "matchWidth": 67, "matchHeight": 11, "pattern": [[0, 0, 1, 0, 0, 1, 0], [1, 1, 1, 1, 1, 1, 1], [0, 1, 0, 1], [0, 0, 1, 0, 0, 1, 0], [0, 0, 1, 0, 0, 1, 0], [0, 1, 0, 1], [0, 0, 1, 0, 0, 1, 0], [1, 1, 1, 0, 1, 1, 1], [1, 0, 1, 0], [1, 0, 1, 1, 1, 0, 1], [1, 1, 0, 1, 0, 1, 1]], "rightAnswer": [], "actionNumbers": 1, "actionType": 0}, "id": "1", "extra": {"tag": "matchboard"}}], "extraDataMap": {}, "resourceList": [], "template": {"name": "火柴题", "tempType": 7, "category": 1019, "bundleUrl": "", "bundleName": "matchPatternQuestion", "tags": [{"name": "matchboard", "label": "火柴面板", "editorConfig": []}], "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}, {"label": "错误提示1", "value": "wrongHint1"}, {"label": "错误提示2", "value": "wrongHint2"}, {"label": "错误提示3", "value": "wrongHint3"}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff"}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}], "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "animations": {}, "stageData": {}, "extraStageData": {"hasRecover": true, "isAutoSubmit": false}}