{"errNo": 0, "errStr": "", "data": {"artType": 0, "artTempId": "30", "tempId": 58, "artTitle": "未命名", "artInfo": {"background": "https://img.zuoyebang.cc/cw_a4298fa2dc5e5d3a618ef39cfaca101b.png", "detail": [{"id": "widget-1580805547901-1", "type": "pic", "name": "pic", "masterWidget": false, "contentType": "", "props": {"top": 27.************, "left": 62, "width": 1160, "height": 506, "opacity": 1, "rotate": "0deg", "ratio": 2.2924901185771, "lockStatus": false, "operateLimit": "111111", "parentId": "lately", "childId": 0, "menuId": "lately", "rotateX": "0deg", "rotateY": "0deg", "clipType": "rect", "imageWidth": 887, "imageHeight": 387, "clipTop": 0, "clipLeft": 0, "clipWidth": 1160, "clipHeight": 506, "containerTop": 27.************, "containerLeft": 62, "containerWidth": 1160, "containerHeight": 506, "shadowObj": {"position": "", "color": "#666"}, "typeFlag": 0}, "content": "https://img.zuoyebang.cc/cw_87017fe1404ad0f9492f4f7c37907153.png", "category": 0, "role": 3, "extendData": []}, {"id": "widget-1580805547901-2", "type": "pic", "name": "pic", "masterWidget": false, "contentType": "", "props": {"top": 147.64705882353, "left": 112, "width": 1068, "height": 266, "opacity": 1, "rotate": "0deg", "ratio": 4.015037593985, "lockStatus": false, "operateLimit": "111111", "parentId": "lately", "childId": 0, "menuId": "lately", "rotateX": "0deg", "rotateY": "0deg", "clipType": "rect", "imageWidth": 816, "imageHeight": 204, "clipTop": 0, "clipLeft": 0, "clipWidth": 1068, "clipHeight": 266, "containerTop": 147.64705882353, "containerLeft": 112, "containerWidth": 1068, "containerHeight": 266, "shadowObj": {"position": "", "color": "#666"}, "typeFlag": 0}, "content": "https://img.zuoyebang.cc/cw_46c745f4fd401eb34fa88ddd38db7eb9.png", "category": 0, "role": 3, "extendData": []}, {"id": "widget-1580805547901-3", "type": "pic", "name": "pic", "masterWidget": false, "contentType": "", "props": {"top": 188.5, "left": 143.5, "width": 279, "height": 187, "opacity": 1, "rotate": "0deg", "ratio": 1.4919786096257, "lockStatus": false, "operateLimit": "111111", "parentId": "lately", "childId": 0, "menuId": "lately", "rotateX": "0deg", "rotateY": "0deg", "clipType": "rect", "imageWidth": 279, "imageHeight": 187, "clipTop": 0, "clipLeft": 0, "clipWidth": 279, "clipHeight": 187, "containerTop": 188.5, "containerLeft": 143.5, "containerWidth": 279, "containerHeight": 187, "shadowObj": {"position": "", "color": "#666"}, "typeFlag": 0}, "content": "https://img.zuoyebang.cc/cw_50aa1282f2ddd5b0c7fef0da62b2eba0.png", "category": 2, "role": 1, "extendData": []}, {"id": "widget-1580805547901-6", "type": "text", "name": "text", "masterWidget": false, "contentType": "", "props": {"top": 83, "left": 499, "width": "auto", "height": "auto", "clientWidth": 290, "clientHeight": 52, "opacity": 1, "rotate": "0deg", "ratio": 1, "lockStatus": false, "operateLimit": "111111", "bold": true, "italic": "", "underline": "", "fontSize": 28, "color": "#6E4318", "listObj": {"label": "无", "value": ""}, "lineHeight": 1.5, "textFormat": "", "fontFamily": "font_cn_syht20190726", "deleteline": "", "textAlign": "left", "borderColor": "", "borderStyle": "", "borderWidth": "1px", "backgroundColor": "", "tbAlign": "flex-start", "shadowObj": {"position": "", "color": "#6E4318"}, "hanzi": "<p><strong style=\"font-size: 28px; color: rgb(110, 67, 24); font-family: font_cn_wryh20190321;\">读出图片所对应的句子</strong></p>"}, "editable": true, "content": "<p><strong style=\"font-size: 28px; color: rgb(110, 67, 24); font-family: font_cn_wryh20190321;\">读出图片所对应的句子</strong></p>", "role": 1, "category": 1, "fontSize": 28, "textAlign": "left", "extendData": []}, {"id": "widget-1580805547901-7", "type": "hx-read-english-text", "name": "hx-read-english-text", "masterWidget": false, "contentType": "", "props": {"top": 195, "left": 455, "width": 706, "height": "auto", "clientHeight": 118, "opacity": 1, "rotate": "0deg", "ratio": 5.9830508474576, "lockStatus": false, "operateLimit": "111001", "bold": true, "italic": "", "underline": "", "fontSize": 36, "color": "#333333", "listObj": {"label": "无", "value": ""}, "lineHeight": 1.5, "textFormat": "", "fontFamily": "font_cn_syht20190726", "deleteline": "", "textAlign": "left", "borderColor": "", "borderStyle": "", "borderWidth": "1px", "backgroundColor": "", "tbAlign": "flex-start", "shadowObj": {"position": "", "color": "#333333"}, "time": 6, "intonation": "0", "readType": 2}, "editable": true, "content": "<p><strong style=\"font-size: 36px; font-family: font_cn_wryh20190321; color: rgb(51, 51, 51);\">Wow! Are there any forks,cups,spoons and chopsticks in the cupboard?</strong></p>", "role": 1, "category": 4}, {"id": "widget-1580805547901-8", "type": "text", "name": "text", "masterWidget": false, "contentType": "", "props": {"top": 313, "left": 455, "width": 706, "height": "auto", "clientWidth": 706, "clientHeight": 52, "opacity": 1, "rotate": "0deg", "ratio": 13.576923076923, "lockStatus": false, "operateLimit": "111111", "bold": "", "italic": "", "underline": "", "fontSize": 28, "color": "#999999", "listObj": {"label": "无", "value": ""}, "lineHeight": 1.5, "textFormat": "", "fontFamily": "font_cn_syht20190726", "deleteline": "", "textAlign": "left", "borderColor": "", "borderStyle": "", "borderWidth": "1px", "backgroundColor": "", "tbAlign": "flex-start", "shadowObj": {"position": "", "color": "#999999"}, "hanzi": "<p><span style=\"font-size: 28px; color: rgb(153, 153, 153);\">真的！碗橱里有叉子、杯子、勺子和筷子吗？</span></p>"}, "editable": true, "content": "<p><span style=\"font-size: 28px; color: rgb(153, 153, 153);\">真的！碗橱里有叉子、杯子、勺子和筷子吗？</span></p>", "role": 1, "category": 3, "fontSize": 28, "textAlign": "left", "extendData": []}, {"id": "widget-1541577725889-0", "type": "pic", "name": "pic", "masterWidget": false, "props": {"top": 33, "left": 1094, "width": 156, "height": 31, "opacity": 1, "rotate": "0deg", "ratio": 5.0322580645161, "lockStatus": false, "operateLimit": "000000", "parentId": 0, "childId": 0, "menuId": 0, "rotateX": "0deg", "rotateY": "0deg", "clipType": "rect", "imageWidth": 156, "imageHeight": 31, "clipTop": 0, "clipLeft": 0, "clipWidth": 156, "clipHeight": 31, "containerTop": 33, "containerLeft": 1094, "containerWidth": 156, "containerHeight": 31, "shadowObj": {"position": "", "color": "#666"}, "isLogo": 1, "typeFlag": 0}, "content": "https://testimg.zuoyebang.cc/cw_f0d5cb7965e06c6e43f4690caa5cc8af.png@q_40,f_png", "category": 0, "role": 3, "isLogo": 1, "extendData": []}], "width": 1280, "height": 720, "others": [], "rightAnswers": [], "hdktOthers": {"rightAnswers": []}}, "md5Hash": "539e0809f13382d40fdbc68ec2f7da12", "thumbnail": "https://zyb-image.bj.bcebos.com/465d261d9a82f8dd9bc0b7730c1377db.png", "gradeId": 11, "subjectId": 2, "status": 2, "artSource": 0, "deleted": 0, "operatorUid": 2653, "operatorName": "邓超", "updateTime": 1637302841, "createTime": 1637302841, "artCategory": 5030, "canvasRatioType": 1, "hdktOthers": {"rightAnswers": []}, "tags": ""}, "type": "hdkt"}