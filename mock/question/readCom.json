{"animations": {"afterSubmitCorrect": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}, "afterSubmitWrong": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}}, "animationsForClient": {}, "components": [{"type": "specialComponent", "subType": "readcom", "tag": "", "deletable": false, "dragable": true, "canCombine": false, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false, "active": false, "zIndex": false}}, "properties": {"active": true, "width": 1280, "height": 720, "x": 0, "y": 0, "question": {"questionList": ["https://yaya.cdnjtzy.com/cocosPic-21a6c7.png", "https://yaya.cdnjtzy.com/cocosPic-b80b34.png", "https://yaya.cdnjtzy.com/cocosPic-d2cf2d.png", "https://yaya.cdnjtzy.com/cocosPic-aff1e8.png", "https://yaya.cdnjtzy.com/cocosPic-1a4484.png", "https://yaya.cdnjtzy.com/cocosPic-f121a3.png"], "optionsList": [{"optionsQuestion": ["https://yaya.cdnjtzy.com/cocosPic-d35ec8.png"], "options": [{"picList": ["https://yaya.cdnjtzy.com/cocosPic-a4c75a.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-ca5ef7.png"], "isCorrect": true}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-dc8b3e.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-27ef7b.png"], "isCorrect": false}]}, {"optionsQuestion": ["https://yaya.cdnjtzy.com/cocosPic-0479cf.png"], "options": [{"picList": ["https://yaya.cdnjtzy.com/cocosPic-92c1b8.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-e5670c.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-d47839.png"], "isCorrect": true}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-95e384.png"], "isCorrect": false}]}, {"optionsQuestion": ["https://yaya.cdnjtzy.com/cocosPic-39db85.png"], "options": [{"picList": ["https://yaya.cdnjtzy.com/cocosPic-50f793.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-869625.png"], "isCorrect": true}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-e1a9b2.png"], "isCorrect": false}, {"picList": ["https://yaya.cdnjtzy.com/cocosPic-d312b9.png"], "isCorrect": false}]}]}, "customH5": "{\"name\":\"newTaskPage\",\"id\":115491108,\"width\":1280,\"height\":720,\"background\":\"https://yy-s.zuoyebang.cc/static/activity_template/resource/bj2.png\",\"remark\":\"\",\"animation\":\"{\\\"id\\\":0,\\\"direction\\\":\\\"\\\"}\",\"hashId\":18840151,\"others\":{\"goToPageId\":\"goToPageId-1651212680884-40\",\"originCreateTimes\":{}},\"themeDetailId\":0,\"lockPage\":0,\"detail\":[{\"id\":\"widget-1651212680884-10\",\"type\":\"englishTask\",\"name\":\"englishTask\",\"isThemeWidget\":false,\"props\":{\"top\":0,\"left\":0,\"opacity\":1,\"ratio\":1.7777777777777777,\"writingMode\":\"\",\"width\":1280,\"height\":720,\"rotate\":\"0deg\",\"originTid\":734654944,\"originZbTid\":6903804,\"artId\":1297141,\"questionInfo\":{\"content\":\"<p class=\\\"tkspec-text-indent-normal\\\">If you ever get the impression that your dog can \\\"tell\\\" whether you look content or annoyed, you may be onto something. Dogs may indeed be able to distinguish between happy and angry human faces, according to a new study.</p><p class=\\\"tkspec-text-indent-normal\\\">Researchers trained a group of 11 dogs to distinguish between images (图像) of the same person making either a happy or an angry face. During the training stage, each dog was shown only the upper half or the lower half of the person's face. The researchers then tested the dogs' ability to distinguish between human facial expressions by showing them the other half of the person's face or images totally different from the ones used in training. There searchers found that the dogs were able to pick the angry or happy face by touching a picture of it with their noses more often than one would expect by random chance.</p><p class=\\\"tkspec-text-indent-normal\\\">The study showed the animals had figured out how to apply what they learned about human faces during training to new faces in the testing stage. \\\"We can rule out that the dogs simply distinguish between the pictures based on a simple cue, such as the sight of teeth,\\\" said study author Corsin Muller. \\\"Instead, our results suggest that the successful dogs realized that a smiling mouth means the same thing as smiling eyes, and the same rule applies to an angry mouth having the same meaning as angry eyes.\\\"</p><p class=\\\"tkspec-text-indent-normal\\\">\\\"With our study, we think we can now confidently conclude that at least some dogs can distinguish human facial expressions,\\\" Muller told&nbsp;<span class=\\\"tkspec-italic-normal\\\">Live Science</span>.</p><p class=\\\"tkspec-text-indent-normal\\\">At this point, it is not clear why dogs seem to be equipped with the ability to recognize different facial expressions in humans. \\\"To us, the most likely explanation appears to be that the basis lies in their living with humans, which gives them a lot of exposure to human facial expressions,\\\" and this exposure has provided them with many chances to learn to distinguish between them, Muller said.</p>\",\"questionList\":[{\"mark\":\"734654944-1\",\"choose\":\"B\",\"options\":{\"A\":\"<p>distinguish shapes</p>\",\"B\":\"<p>make sense of human faces</p>\",\"C\":\"<p>feel happy or angry</p>\",\"D\":\"<p>communicate with each other</p>\"},\"title\":\"<p>The new study focused on whether dogs can <span class=\\\"tkspec-embed-text tkspec-fill-blank-underline\\\">﻿<span contenteditable=\\\"false\\\">&nbsp;&nbsp;&nbsp;&nbsp;</span>﻿</span><span class=\\\"tkspec-embed-text tkspec-fill-blank-underline\\\">﻿<span contenteditable=\\\"false\\\">&nbsp;&nbsp;&nbsp;&nbsp;</span>﻿</span><span class=\\\"tkspec-embed-text tkspec-fill-blank-underline\\\">﻿<span contenteditable=\\\"false\\\">&nbsp;&nbsp;&nbsp;&nbsp;</span>﻿</span><span class=\\\"tkspec-embed-text tkspec-fill-blank-underline\\\">﻿<span contenteditable=\\\"false\\\">&nbsp;&nbsp;&nbsp;&nbsp;</span>﻿</span>.</p>\",\"index\":1},{\"mark\":\"734654944-2\",\"choose\":\"C\",\"options\":{\"A\":\"<p>Researechers tested the dogs in random order.</p>\",\"B\":\"<p>Diverse methods were adopted during training.</p>\",\"C\":\"<p>Pictures used in the two stages were different.</p>\",\"D\":\"<p>The dogs were photographed before the test.</p>\"},\"title\":\"<p>What can we learn about the study from paragraph 2?</p>\",\"index\":2},{\"mark\":\"734654944-3\",\"choose\":\"B\",\"options\":{\"A\":\"<p>A suggestion for future studies.</p>\",\"B\":\"<p>A possible reason for the study findings.</p>\",\"C\":\"<p>A major limitation of the study.</p>\",\"D\":\"<p>An explanation of the research method.</p>\"},\"title\":\"<p>What is the last paragraph mainly about?</p>\",\"index\":3}],\"title\":\"\"},\"category\":34,\"answer\":\"\",\"analysis\":\"\",\"zbDifficulty\":{\"id\":4,\"name\":\"4星\"},\"zbPointId\":[{\"id\":121539,\"name\":\"说明文\"},{\"id\":121542,\"name\":\"科技发明\"},{\"id\":121556,\"name\":\"阅读选择（全国卷形式）\"}],\"zbPower\":[],\"totalOptionsCount\":3,\"zbGradeId\":7,\"subjectId\":3,\"thumbnail\":\"https://zyb-image.bj.bcebos.com/4f03a5a91b9b83652e1a3ab391a6fbc7.png?50?50?50?50?19\"},\"displayName\":null,\"editorLocked\":false}],\"playTag\":\"\",\"playTagType\":11,\"animations\":[],\"source\":\"newquestion\",\"tid\":0,\"zbTid\":0,\"grade\":0,\"subject\":0,\"canSend\":1,\"copyId\":0,\"courseProp\":\"\",\"themeAttr\":\"\",\"tabs\":[\"zbtiku\"],\"logoId\":1,\"logoPositionType\":1,\"coursewareId\":11437461,\"capture\":\"\",\"captureHash\":\"67758653b90ff9f6a988546a1d313fc4b0b209d2a5f8b4d8db45f3d29d4735fd\",\"renderType\":0,\"othersExt\":{},\"uniqId\":\"\",\"sourceVersion\":\"\",\"artId\":1297141,\"tagsData\":{\"pointList\":[{\"id\":\"121539\",\"name\":\"说明文\"},{\"id\":\"121542\",\"name\":\"科技发明\"},{\"id\":\"121556\",\"name\":\"阅读选择（全国卷形式）\"}],\"ability\":[],\"difficulty\":{\"id\":\"4\",\"name\":\"4星\"},\"gradeId\":\"7\",\"subject\":\"3\"},\"tagsReady\":true}"}, "id": "1", "extra": {"tag": ""}}, {"type": "specialComponent", "subType": "brush", "tag": "", "deletable": false, "dragable": true, "canCombine": false, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false, "active": false, "zIndex": false}}, "properties": {"active": true, "width": 95, "height": 182, "x": 576, "y": -90, "color": "#000000", "style": 1, "mountList": ["1"]}, "id": "2", "extra": {"tag": ""}}], "extraDataMap": {"1": {"tag": ""}, "2": {"tag": ""}}, "extraStageData": {"isAutoSubmit": true, "hasRecover": false}, "resourceList": ["https://yaya.cdnjtzy.com/cocosPic-21a6c7.png", "https://yaya.cdnjtzy.com/cocosPic-b80b34.png", "https://yaya.cdnjtzy.com/cocosPic-d2cf2d.png", "https://yaya.cdnjtzy.com/cocosPic-aff1e8.png", "https://yaya.cdnjtzy.com/cocosPic-1a4484.png", "https://yaya.cdnjtzy.com/cocosPic-f121a3.png", "https://yaya.cdnjtzy.com/cocosPic-d35ec8.png", "https://yaya.cdnjtzy.com/cocosPic-a4c75a.png", "https://yaya.cdnjtzy.com/cocosPic-ca5ef7.png", "https://yaya.cdnjtzy.com/cocosPic-dc8b3e.png", "https://yaya.cdnjtzy.com/cocosPic-27ef7b.png", "https://yaya.cdnjtzy.com/cocosPic-0479cf.png", "https://yaya.cdnjtzy.com/cocosPic-92c1b8.png", "https://yaya.cdnjtzy.com/cocosPic-e5670c.png", "https://yaya.cdnjtzy.com/cocosPic-d47839.png", "https://yaya.cdnjtzy.com/cocosPic-95e384.png", "https://yaya.cdnjtzy.com/cocosPic-39db85.png", "https://yaya.cdnjtzy.com/cocosPic-50f793.png", "https://yaya.cdnjtzy.com/cocosPic-869625.png", "https://yaya.cdnjtzy.com/cocosPic-e1a9b2.png", "https://yaya.cdnjtzy.com/cocosPic-d312b9.png", "https://yy-s.zuoyebang.cc/static/activity_template/resource/bj2.png", "https://zyb-image.bj.bcebos.com/4f03a5a91b9b83652e1a3ab391a6fbc7.png"], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "template": {"name": "阅读理解", "tempType": 16, "category": 1100, "bundleUrl": "", "questionType": 13, "bundleName": "readcomQuestion", "tags": [], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960}, "extraConfig": [{"key": "guide", "label": "题干音频", "type": "audioSelect", "params": {}}], "animationConfig": [{"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "questionType": 13, "versionInfo": {"v": 1, "timeStamp": 1651733456}, "thumbnail": ""}