{"components": [{"type": "specialComponent", "subType": "write", "tag": "", "deletable": false, "dragable": true, "editable": {"properties": {"x": false, "y": false, "width": false, "height": false, "angle": false, "rotation": false, "opacity": false, "scaleX": false, "scaleY": false, "color": false, "active": false}}, "properties": {"active": true, "writeType": 1, "writeArray": [{"hanzi": "生", "pinyin": "sh<PERSON>ng", "hasAnswer": false}], "width": 1280, "height": 720, "opacity": 255, "angle": 0, "x": 0, "y": 0}, "id": "1", "extra": {}}], "extraDataMap": {}, "resourceList": [], "template": {"name": "手写题", "tempType": 12, "category": 1024, "bundleUrl": "", "questionType": 2, "bundleName": "sharkWriteQuestion", "tags": [], "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff"}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}], "features": {"newCocos": 1, "isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "animations": {}, "stageData": {}, "extraStageData": {"hasRecover": true, "isAutoSubmit": false}}