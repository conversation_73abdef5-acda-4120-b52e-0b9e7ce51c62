/* eslint-disable @typescript-eslint/no-var-requires */
const fs = require("fs");
const path = require("path");

module.exports = async req => {
  const { body } = req;
  console.log(body);
  fs.writeFileSync(
    path.resolve(__dirname, "preview/1.json"),
    JSON.stringify({
      errNo: 0,
      errStr: "",
      data: body
    }),
    "utf-8"
  );
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });
  return {
    errNo: 0,
    errStr: "",
    data: {
      previewId: 1
    }
  };
};
