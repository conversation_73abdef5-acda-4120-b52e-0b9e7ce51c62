{"animations": {"afterReadingQuestion": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}, "afterSubmitCorrect": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}, "afterSubmitWrong": {"audio": {"url": "", "delay": 0}, "fragments": {}, "points": {}}}, "animationsForClient": {}, "components": [{"tag": "ligature", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 80, "height": 80, "x": -273, "y": -45, "texture": "https://yaya.cdnjtzy.com/jianpan_hong_bg_click-1f492e.png", "angle": 0}, "id": "1", "extra": {"linkPoint": {"texture": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "linkPointType": "down"}, "tag": "ligature"}, "childComponents": [{"tag": "", "isFocus": false, "type": "sprite", "editable": false, "deletable": false, "tagName": "", "properties": {"active": true, "width": 30, "height": 30, "x": 0, "y": -60, "texture": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "angle": 0}, "id": "2", "extra": {}}]}, {"tag": "dragArea", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 80, "height": 80, "x": 165, "y": 160.5, "texture": "https://yaya.cdnjtzy.com/jianpan_lan_bg_click-ed42d9.png", "angle": 0}, "id": "3", "extra": {"pairingObject": ["1"], "linkPoint": {"texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "linkPointType": "left"}, "tag": "dragArea"}, "childComponents": [{"tag": "", "isFocus": false, "type": "sprite", "editable": false, "deletable": false, "tagName": "", "properties": {"active": true, "width": 175, "height": 106, "x": -60, "y": 0, "texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "angle": 0}, "id": "4", "extra": {}}]}, {"tag": "dragArea", "type": "sprite", "dragable": true, "properties": {"active": true, "width": 80, "height": 80, "x": 200.3, "y": -86.5, "texture": "https://yaya.cdnjtzy.com/jianpan_lan_bg_click-ed42d9.png", "angle": 0}, "id": "5", "extra": {"pairingObject": ["1"], "linkPoint": {"texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "linkPointType": "left"}, "tag": "dragArea"}, "childComponents": [{"tag": "", "isFocus": false, "type": "sprite", "editable": false, "deletable": false, "tagName": "", "properties": {"active": true, "width": 175, "height": 106, "x": -60, "y": 0, "texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "angle": 0}, "id": "6", "extra": {}}]}], "extraDataMap": {"1": {"linkPoint": {"texture": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "linkPointType": "down"}, "tag": "ligature"}, "3": {"pairingObject": ["1"], "linkPoint": {"texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "linkPointType": "left"}, "tag": "dragArea"}, "5": {"pairingObject": ["1"], "linkPoint": {"texture": "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "linkPointType": "left"}, "tag": "dragArea"}}, "extraStageData": {"isAutoSubmit": false, "hasRecover": true}, "resourceList": ["https://yaya.cdnjtzy.com/jianpan_hong_bg_click-1f492e.png", "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "https://yaya.cdnjtzy.com/jianpan_lan_bg_click-ed42d9.png", "https://yaya.cdnjtzy.com/alice_asset_1-f426a9.png", "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_0c5921e1c3fd9e9bda977ecf4d43bc95_1624_1218.jpg"], "stageData": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_0c5921e1c3fd9e9bda977ecf4d43bc95_1624_1218.jpg", "textureType": 0}, "template": {"questionType": 3, "bundleUrl": "https://yaya.cdnjtzy.com/cchd_bundle/connectionQuestion.zip", "bundleName": "sharkLineQuestion", "category": 1013, "type": 1003, "tempType": 6, "name": "连线", "animationConfig": [{"label": "读题后", "value": "afterReadingQuestion"}, {"label": "正确后", "value": "afterSubmitCorrect"}, {"label": "答错后", "value": "afterSubmitWrong"}], "tags": [{"name": "ligature", "label": "连线元素", "editorConfig": [{"key": "linkPoint", "label": "连接点", "type": "childComponent", "params": {"options": [{}]}, "subParams": [{"key": "texture", "label": "图片", "type": "effect", "main": true, "defaultValue": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "params": {"options": [{}]}}, {"key": "linkPointType", "label": "位置", "type": "select", "defaultValue": "right", "params": {"options": [{"value": "up", "label": "上"}, {"value": "down", "label": "下"}, {"value": "left", "label": "左"}, {"value": "right", "label": "右"}]}}]}, {"key": "selectEffect", "label": "选中效果", "type": "effect", "params": {"options": [{}]}}, {"key": "closeToEffect", "label": "靠近效果", "type": "effect", "params": {"options": [{}]}}, {"key": "animations", "label": "组件动画", "type": "animations", "params": {"options": [{"value": "afterCorrectQuestionGuide", "label": "正确后"}, {"value": "afterWrong", "label": "错误后"}]}}]}, {"name": "dragArea", "label": "答题区", "editorConfig": [{"key": "pairingObject", "label": "配对元素", "type": "select", "params": {"multiple": true, "options": "$tag=ligature"}}, {"key": "linkPoint", "label": "连接点", "type": "childComponent", "params": {"options": [{}]}, "subParams": [{"key": "texture", "label": "图片", "type": "effect", "main": true, "defaultValue": "https://zyb-yaya-math-1253445850.file.myqcloud.com/test/miqa_d9b51389d77681fce6e88adf002101e8_149_100.jpg", "params": {"options": [{}]}}, {"key": "linkPointType", "label": "位置", "type": "select", "defaultValue": "right", "params": {"options": [{"value": "up", "label": "上"}, {"value": "down", "label": "下"}, {"value": "left", "label": "左"}, {"value": "right", "label": "右"}]}}]}, {"key": "selectEffect", "label": "选中效果", "type": "effect", "params": {"options": [{}]}}, {"key": "closeToEffect", "label": "靠近效果", "type": "effect", "params": {"options": [{}]}}, {"key": "animations", "label": "组件动画", "type": "animations", "params": {"options": [{"value": "afterCorrectQuestionGuide", "label": "正确后"}, {"value": "afterWrong", "label": "错误后"}]}}]}], "stage": {"width": 1280, "height": 720, "safeWidth": 1280, "safeHeight": 960, "backgroundColor": "#ffffff", "texture": "", "textureType": 0}, "extraConfig": [{"key": "hasRecover", "label": "学生重置", "required": true, "type": "radio", "params": {"options": [{"label": "有重置按钮", "value": true}, {"label": "无重置按钮", "value": false}]}}, {"key": "guide", "label": "题干音频", "type": "audioSelect", "params": {}}], "features": {"isQuestion": 1, "hasMys": 0, "canInteract": 1, "isOral": 0, "isGroup": 0, "demoPage": 0, "hasVideo": 0, "liveResources": 1, "groupData": {"questionLength": 0}}}, "questionType": 3, "thumbnail": "https://yaya.cdnjtzy.com/thumbnail-b94ff9.jpeg"}