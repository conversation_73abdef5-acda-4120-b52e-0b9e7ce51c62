{"name": "interactive-question-editor", "version": "3.1.0-alpha.3", "private": true, "scripts": {"serve": "node copy.js & vue-cli-service serve", "analyzer": "cross-env use_analyzer=true yarn build:test", "build": "node copy.js & vue-cli-service build --mode=production", "lint": "vue-cli-service lint", "build:test": "node copy.js & vue-cli-service build --mode=test", "build:online": "node copy.js & vue-cli-service build --mode=production", "build:overview": "cross-env VUE_APP_IS_OVERVIEW=true vue-cli-service build --mode=test", "build:baixiong": "vue-cli-service build --mode=baixiong", "serve:baixiong": "vue-cli-service serve --mode=baixiong", "analyzer:baixiong": "cross-env use_analyzer=true vue-cli-service serve --mode=baixiong", "commit": "cz", "svg": "vsvg -s src/assets/svg/origin -t src/assets/svg/output "}, "dependencies": {"@packy-tang/vue-tinymce": "^1.1.2", "@types/pinyin": "^2.10.0", "axios": "0.18.0", "cheerio": "0.20.0", "clipboard-polyfill": "^3.0.3", "common-monitor": "^4.0.9", "compression-webpack-plugin": "^5.0.2", "core-js": "^3.6.5", "domhandler": "2.3.0", "element-ui": "^2.14.1", "howler": "^2.2.0", "ismobilejs": "^1.1.1", "jszip": "^3.6.0", "lodash-es": "^4.17.21", "material-library": "^1.1.12", "pinyin": "^2.10.2", "qrcode": "^1.5.3", "qte-render": "^3.1.55-alpha.4", "query-string": "^5.1.1", "terser-webpack-plugin": "^1.3.0", "tinycolor2": "^1.4.2", "tinymce": "^5.10.9", "uuid": "^8.3.0", "vue": "^2.6.10", "vue-click-outside": "^1.1.0", "vue-content-placeholders": "^0.2.1", "vue-cropper": "^0.5.5", "vue-draggable-resizable": "^2.3.0", "vue-router": "3.0.2", "vue-svgicon": "3.2.9", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "^2.24.1", "vuex": "^3.4.0", "vuex-class": "^0.3.2", "zyb-js-sdk": "0.0.21-beta.1", "zyb-pc-ui": "^1.2.3", "zyb-shape-editor": "0.0.11-test.50", "md5": "^2.3.0"}, "devDependencies": {"@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@types/howler": "^2.2.1", "@types/lodash-es": "^4.17.6", "@types/md5": "^2.3.5", "@types/qrcode": "^1.3.5", "@types/query-string": "^5.1.0", "@types/uuid": "^8.3.0", "@typescript-eslint/eslint-plugin": "^2.33.0", "@typescript-eslint/parser": "^2.33.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "@zbk/vue-cli-plugin-cdn-retry": "0.0.20-beta.3", "autoprefixer": "^9.6.0", "babel-plugin-component": "^1.1.1", "commitizen": "^4.2.2", "commitlint": "^11.0.0", "cross-env": "^7.0.3", "cz-conventional-changelog": "3.3.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "husky": "^4.3.0", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^10.5.2", "prettier": "^1.19.1", "raw-loader": "^4.0.2", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "typescript": "~3.9.3", "vue-cli-plugin-element": "~1.0.1", "vue-property-decorator": "^7.0.0", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.7.0"}, "engines": {"node": ">= 8.0.0", "npm": ">= 5.0.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"src/**/*.{vue,ts,js,css}": ["eslint --fix --ext .ts --ext .vue", "git add"]}}