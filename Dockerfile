FROM image-docker.zuoyebang.cc/base/node-builder:14.17.4-slim as builder
 
WORKDIR /output
WORKDIR /home/<USER>/
ARG CI_FE_DEBUG
ARG REPO_GIT_REMOTE_ADDRESS

COPY package.json /home/<USER>/package.json
RUN npm install --registry=http://ued.zuoyebang.cc/npm/ --ignore-scripts

COPY . /home/<USER>/
RUN npm install --registry=http://ued.zuoyebang.cc/npm/ --ignore-scripts
RUN if [ "$CI_FE_DEBUG" = "true" ] ; then npm run build:test ; else npm run build:online ; fi
 
# 运行
# FROM image-docker.zuoyebang.cc/privbase/fe-nginx:1.2.5-cos 
FROM image-docker.zuoyebang.cc/privbase/fe-nginx:1.2.8-cos-custom-edu
 
ARG APP_NAME
ENV APP_NAME $APP_NAME
ARG REPO_NAME
ENV REPO_NAME $REPO_NAME
# 仅用于通用的前端，部分前端视情况来组织目录结构
COPY --from=builder /home/<USER>/dist/ /home/<USER>/www/$APP_NAME/