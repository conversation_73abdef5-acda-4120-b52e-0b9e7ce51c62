/* eslint-disable @typescript-eslint/no-var-requires */
// eslint-disable-next-line @typescript-eslint/no-var-requires
const OverviewBuildCleanPlugin = require("./OverviewBuildCleanPlugin.js");
const InsertCocosScripts = require('qte-render/public/plugins/insert-cocos-scripts');
const LoadGameScripts = require('./plugins/load-game-scripts.js')
const InsertScriptPlugin = require('./plugins/insert-script-plugin')
const cocosPublicPath = process.env.VUE_APP_CDN_PATH ? `${process.env.VUE_APP_CDN_PATH}/interactive-question-editor/cocos/` : `cocos/`;
const jsNames = require("./public/cocos/md5.json");
const jsList = jsNames.map(name => cocosPublicPath + name);
const overview = {
  title: "表单组件总览",
  entry: "src/pages/overview/index.native.ts",
  template: "public/index.html",
  chunks: ['chunk-element-ui', 'chunk-vue', 'overview', 'chunk-common', 'chunk-vendors']
};
console.log('index.native.ts');
let pages = {
  index: {
    title: "cocos编辑器",
    entry: "src/pages/index/index.native.ts",
    needLoading: true,
    template: "public/preiview.html",
    chunks: ['chunk-element-ui', 'chunk-vue', 'index', 'chunk-common', 'chunk-vendors']
  },
  preview: {
    title: "预览",
    entry: "src/pages/preview/index.ts",
    template: "public/index.html",
    chunks: ['chunk-element-ui', 'chunk-vue', 'preview', 'chunk-common', 'chunk-vendors']
  },
  overview
};
const getPublicPath = () => {
  if (process.env.VUE_APP_IS_OVERVIEW) {
    return "./";
  } else if (process.env.NODE_ENV === "production") {
    return `${process.env.VUE_APP_CDN_PATH}`;
  } else {
    return undefined;
  }
};

module.exports = {
  pages,
  publicPath: getPublicPath(),
  runtimeCompiler: true,
  productionSourceMap: true,
  // lintOnSave: false,
  configureWebpack: config => {
    if (process.env.use_analyzer) {
      console.log("analyzer");
      const analyzer = require("webpack-bundle-analyzer").BundleAnalyzerPlugin;
      config.plugins.push(new analyzer());
    }
    if (process.env.VUE_APP_IS_OVERVIEW) {
      config.plugins.push(new OverviewBuildCleanPlugin());
    }
    config.plugins.push(new InsertScriptPlugin({
      dirname: __dirname,
    }));
    config.plugins.push(new LoadGameScripts({
      jsList,
      excludeHtmlNames: ['overview.html', 'preview.html', 'group.html'],
    }));
    config.plugins.push(new InsertCocosScripts({
      dirname: __dirname,
      simple: true,
      excludeHtmlNames: ['overview.html', 'index.html', 'group.html'],
    }));
  },
  chainWebpack: config => {
    // 移除 prefetch 插件
    config.plugins.delete("prefetch-index");
    config.optimization.merge({
      splitChunks: {
        automaticNameDelimiter: '-',
        chunks: 'async',
        cacheGroups: {
          vendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true,
            minChunks: 2,
            chunks: 'initial',
            enforce: true,
          },
          common: {
            name: `chunk-common`,
            minChunks: 2,
            priority: -20,
            chunks: 'initial',
            reuseExistingChunk: true,
            enforce: true
          },
          elementUI: {
            chunks: 'initial',
            name: 'chunk-element-ui',
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
            // test: /[\\/]element-ui[\\/]/,
            priority: 10,
            minChunks: 1,
            reuseExistingChunk: true,
            enforce: true
          },
          schemaForm: {
            chunks: 'async',
            name: 'chunk-schema-form',
            test: /[\\/]EditArea[\\/]SchemaToForm[\\/](.*)/,
            // test: /[\\/]element-ui[\\/]/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          },
          tinymce: {
            chunks: 'async',
            name: 'chunk-tinymce',
            test: /[\\/]node_modules[\\/]_?tinymce(.*)/,
            // test: /[\\/]element-ui[\\/]/,
            priority: 10,
            reuseExistingChunk: true,
            enforce: true
          }
        }
      }
    })
  },
  devServer: {
    disableHostCheck: true,
    historyApiFallback: true,
    proxy: {
      "^/asr": {
        target: "https://asr.zuoyebang.cc/",
        ws: true,
        pathRewrite: {
          "^/asr": "",
        },
        changeOrigin: true,
      },
      "^/localApi": {
        target: "http://localhost:3000/",
        ws: true,
        pathRewrite: {
          "^/localApi": "/localApi",
        },
        changeOrigin: true,
      },
      "^/localAssets": {
        target: "http://localhost:3000/",
        ws: true,
        pathRewrite: {
          "^/localAssets": "",
        },
        changeOrigin: true,
      },
    },
  },
};
