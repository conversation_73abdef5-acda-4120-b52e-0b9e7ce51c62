/*
 * @Date: 2021-09-18 13:11:04
 * @LastEditors: chxu
 * @LastEditTime: 2021-12-01 20:18:10
 * @FilePath: /interactive-question-editor/.eslintrc.js
 * @Author: chxu
 */
module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    "plugin:vue/essential",
    "eslint:recommended",
    "@vue/typescript/recommended",
    "@vue/prettier",
    "@vue/prettier/@typescript-eslint",
  ],
  parserOptions: {
    ecmaVersion: 2020,
  },
  rules: {
    "prettier/prettier": "off",
    "no-console": process.env.NODE_ENV === "production" ? "warn" : "off",
    "no-debugger": process.env.NODE_ENV === "production" ? "warn" : "off",
    "@typescript-eslint/no-explicit-any": ["off"],
    "@typescript-eslint/ban-ts-ignore": ["off"]
  },
};