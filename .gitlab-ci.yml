stages:
  - build

variables:
  GIT_SUBMODULE_STRATEGY: recursive

before_script:
  - before_ci_script

docker-build:
  stage: build
  before_script:
    - echo 'test before script...'
  script:
    - docker_build_fe_script
  after_script:
    - after_ci_fe_script
  tags:
    - global-runner

  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /^ci /
      - $CI_COMMIT_REF_NAME == "master"
      - $CI_COMMIT_REF_NAME =~ /^release/
      - $CI_COMMIT_REF_NAME =~ /^dev/
      - $CI_COMMIT_REF_NAME =~ /^feat/
      - $CI_PIPELINE_SOURCE == "web"
      - $CI_PIPELINE_SOURCE == "api"

# build-cocos:
#   stage: build-cocos
#   before_script:
#     - ''
#   script:
#     - echo "test build cocos"
#     - pwd
#     - cd /Users/<USER>/test/
#     - node test.js
#   tags:
#     - build-cocos
